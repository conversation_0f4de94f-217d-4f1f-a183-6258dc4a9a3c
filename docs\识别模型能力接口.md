
# OCR与版面分析接口文档

## 1. OCR文字识别接口

### 1.1 通用文字识别

**接口说明**: 识别图片中的文字内容

**请求方式**: POST

**请求路径**: `/api/v1/ocr/general`

**请求参数**:
```json
{
    "image": "base64编码的图片",
    "language": "识别语言(可选，默认中文)",
    "detect_direction": false  // 是否检测文字方向
}
```

**响应结果**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "text_list": [
            {
                "text": "识别到的文字",
                "confidence": 0.95,
                "location": {
                    "top": 100,
                    "left": 100,
                    "width": 200,
                    "height": 50
                }
            }
        ],
        "direction": 0  // 文字方向(0:正向, 1:逆时针90度, 2:180度, 3:顺时针90度)
    }
}
```

## 2. 版面分析接口

### 2.1 文档版面分析

**接口说明**: 识别文档的版面布局结构

**请求方式**: POST

**请求路径**: `/api/v1/layout/analyze`

**请求参数**:
```json
{
    "image": "base64编码的图片",
    "return_text": false  // 是否返回区域文字内容
}
```

**响应结果**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "regions": [
            {
                "type": "text",  // 区域类型：text(文本)、table(表格)、image(图片)、title(标题)等
                "confidence": 0.98,
                "location": {
                    "top": 100,
                    "left": 100,
                    "width": 200,
                    "height": 50
                },
                "text": "区域文字内容(当return_text=true时返回)"
            }
        ],
        "page_type": "normal"  // 页面类型：normal(普通文档)、table(表格为主)、picture(图片为主)
    }
}
```

## 3. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1001 | 参数错误 |
| 1002 | 图片解析失败 |
| 2001 | OCR识别失败 |
| 2002 | 版面分析失败 |
| 5000 | 系统内部错误 |

## 4. 注意事项

1. 图片格式支持：PNG、JPG、JPEG、BMP
2. 图片base64大小不超过10MB
3. 接口调用频率限制：10次/秒
4. 建议图片分辨率不低于300DPI
```

以上接口设计包含了：

1. OCR识别接口：
   - 支持多语言识别
   - 返回文字位置信息
   - 支持文字方向检测

2. 版面分析接口：
   - 支持多种版面元素识别
   - 可选是否返回区域文字内容
   - 返回页面整体类型判断

3. 统一的错误码处理和注意事项说明

你可以根据具体需求对接口参数和返回值进行调整。如果需要添加其他特定功能（如表格识别、身份证识别等），我可以继续补充相关接口设计。
