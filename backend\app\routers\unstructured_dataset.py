from fastapi import APIRouter, HTTPException, Depends
from typing import List
from ..models.unstructured_dataset import UnstructuredDataset, UnstructuredDatasetCreate, UnstructuredDatasetUpdate, UnstructuredDatasetResponse
from ..db.mongodb import db
from ..utils.auth import verify_token
from bson import ObjectId
from datetime import datetime

router = APIRouter(
    prefix="/api/unstructured-datasets",
    tags=["unstructured_datasets"]
)

# 创建新的非结构化数据集
@router.post("/", response_model=UnstructuredDatasetResponse)
async def create_unstructured_dataset(dataset: UnstructuredDatasetCreate, current_user: dict = Depends(verify_token)):
    new_dataset = dataset.dict()
    new_dataset["created_at"] = datetime.now()
    new_dataset["last_updated"] = datetime.now()
    result = await db["unstructured_datasets"].insert_one(new_dataset)
    created_dataset = await db["unstructured_datasets"].find_one({"_id": result.inserted_id})
    return UnstructuredDatasetResponse(**created_dataset)

# 获取非结构化数据集列表
@router.get("/", response_model=List[UnstructuredDatasetResponse])
async def read_unstructured_datasets(skip: int = 0, limit: int = 10, current_user: dict = Depends(verify_token)):
    datasets = await db["unstructured_datasets"].find().skip(skip).limit(limit).to_list(length=limit)
    return [UnstructuredDatasetResponse(**dataset) for dataset in datasets]

# 获取单个非结构化数据集
@router.get("/{dataset_id}", response_model=UnstructuredDatasetResponse)
async def read_unstructured_dataset(dataset_id: str, current_user: dict = Depends(verify_token)):
    dataset = await db["unstructured_datasets"].find_one({"_id": ObjectId(dataset_id)})
    if dataset is None:
        raise HTTPException(status_code=404, detail="Unstructured dataset not found")
    return UnstructuredDatasetResponse(**dataset)

# 更新非结构化数据集
@router.put("/{dataset_id}", response_model=UnstructuredDatasetResponse)
async def update_unstructured_dataset(dataset_id: str, dataset: UnstructuredDatasetUpdate, current_user: dict = Depends(verify_token)):
    update_data = dataset.dict(exclude_unset=True)
    update_data["last_updated"] = datetime.now()
    result = await db["unstructured_datasets"].update_one(
        {"_id": ObjectId(dataset_id)},
        {"$set": update_data}
    )
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Unstructured dataset not found")
    updated_dataset = await db["unstructured_datasets"].find_one({"_id": ObjectId(dataset_id)})
    return UnstructuredDatasetResponse(**updated_dataset)

# 删除非结构化数据集
@router.delete("/{dataset_id}", response_model=dict)
async def delete_unstructured_dataset(dataset_id: str, current_user: dict = Depends(verify_token)):
    result = await db["unstructured_datasets"].delete_one({"_id": ObjectId(dataset_id)})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Unstructured dataset not found")
    return {"message": "Unstructured dataset deleted successfully"}
