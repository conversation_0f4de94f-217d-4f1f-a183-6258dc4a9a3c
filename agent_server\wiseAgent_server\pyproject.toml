[project]
name = "agent"
version = "0.0.1"
description = "Starter template for making a new agent LangGraph."
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
]
readme = "README.md"
license = { text = "MIT" }
requires-python = ">=3.9"
dependencies = [
    "langgraph>=0.2.6",
    "python-dotenv>=1.0.1",
]


[project.optional-dependencies]
dev = ["mypy>=1.11.1", "ruff>=0.6.1"]

[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["langgraph.templates.agent", "agent"]
[tool.setuptools.package-dir]
"langgraph.templates.agent" = "src/agent"
"agent" = "src/agent"


[tool.setuptools.package-data]
"*" = ["py.typed"]

[tool.ruff]
lint.select = [
    "E",    # pycodestyle
    "F",    # pyflakes
    "I",    # isort
    "D",    # pydocstyle
    "D401", # First line should be in imperative mood
    "T201",
    "UP",
]
lint.ignore = [
    "UP006",
    "UP007",
    # We actually do want to import from typing_extensions
    "UP035",
    # Relax the convention by _not_ requiring documentation for every function parameter.
    "D417",
    "E501",
]
[tool.ruff.lint.per-file-ignores]
"tests/*" = ["D", "UP"]
[tool.ruff.lint.pydocstyle]
convention = "google"

[dependency-groups]
dev = [
    "anyio>=4.7.0",
    "langgraph-cli[inmem]>=0.2.8",
    "mypy>=1.13.0",
    "pytest>=8.3.5",
    "ruff>=0.8.2",
]
