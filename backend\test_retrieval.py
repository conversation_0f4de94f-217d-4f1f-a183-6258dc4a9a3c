"""
检索测试脚本
用于测试检索接口的功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from app.engines.retrieval.knowledge_retriever import (
    RetrievalTestRequest,
    test_knowledge_retrieval
)

async def test_semantic_search():
    """测试语义检索"""
    print("=== 测试语义检索 ===")
    
    request = RetrievalTestRequest(
        knowledge_base_id="6749c521f857825b11f1ce8f",  # 替换为实际的知识库ID
        query="支付机构的监管要求",
        search_mode="semantic",
        topk=5,
        min_similarity=0.7
    )
    
    result = await test_knowledge_retrieval(request)
    
    print(f"检索成功: {result.success}")
    print(f"检索模式: {result.search_mode}")
    print(f"结果数量: {result.total_results}")
    print(f"执行时间: {result.execution_time:.2f}秒")
    
    if result.success and result.results:
        print("\n前3个结果:")
        for i, res in enumerate(result.results[:3]):
            print(f"{i+1}. 分数: {res.get('score', 'N/A')}")
            print(f"   内容: {res.get('content', '')[:100]}...")
            print()
    else:
        print(f"检索失败: {result.error_message}")

async def test_fulltext_search():
    """测试全文检索"""
    print("=== 测试全文检索 ===")
    
    request = RetrievalTestRequest(
        knowledge_base_id="6749c521f857825b11f1ce8f",  # 替换为实际的知识库ID
        query="支付机构",
        search_mode="fulltext",
        topk=5,
        min_similarity=0.5
    )
    
    result = await test_knowledge_retrieval(request)
    
    print(f"检索成功: {result.success}")
    print(f"检索模式: {result.search_mode}")
    print(f"结果数量: {result.total_results}")
    print(f"执行时间: {result.execution_time:.2f}秒")
    
    if result.success and result.results:
        print("\n前3个结果:")
        for i, res in enumerate(result.results[:3]):
            print(f"{i+1}. 分数: {res.get('score', 'N/A')}")
            print(f"   内容: {res.get('content', '')[:100]}...")
            print()
    else:
        print(f"检索失败: {result.error_message}")

async def test_hybrid_search():
    """测试混合检索"""
    print("=== 测试混合检索 ===")
    
    request = RetrievalTestRequest(
        knowledge_base_id="6749c521f857825b11f1ce8f",  # 替换为实际的知识库ID
        query="支付机构监管",
        search_mode="hybrid",
        topk=5,
        min_similarity=0.6
    )
    
    result = await test_knowledge_retrieval(request)
    
    print(f"检索成功: {result.success}")
    print(f"检索模式: {result.search_mode}")
    print(f"结果数量: {result.total_results}")
    print(f"执行时间: {result.execution_time:.2f}秒")
    
    if result.success and result.results:
        print("\n前3个结果:")
        for i, res in enumerate(result.results[:3]):
            print(f"{i+1}. RRF分数: {res.get('rrf_score', 'N/A')}")
            print(f"   语义排名: {res.get('semantic_rank', 'N/A')}")
            print(f"   全文排名: {res.get('fulltext_rank', 'N/A')}")
            print(f"   内容: {res.get('content', '')[:100]}...")
            print()
    else:
        print(f"检索失败: {result.error_message}")

async def main():
    """主测试函数"""
    print("开始检索功能测试...\n")
    
    try:
        # 测试语义检索
        await test_semantic_search()
        print("\n" + "="*50 + "\n")
        
        # 测试全文检索
        await test_fulltext_search()
        print("\n" + "="*50 + "\n")
        
        # 测试混合检索
        await test_hybrid_search()
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
