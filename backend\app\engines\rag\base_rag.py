from typing import List, Dict, Optional
from dataclasses import dataclass
from abc import ABC, abstractmethod

# 基础数据结构
@dataclass
class Document:
    content: str
    metadata: Dict = None
    score: Optional[float] = None

# 抽象基类定义接口
class BaseVectorStore(ABC):
    @abstractmethod
    def add_documents(self, documents: List[Document]):
        pass
    
    @abstractmethod
    def search(self, query: str, top_k: int = 3) -> List[Document]:
        pass

class BaseEmbedding(ABC):
    @abstractmethod
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        pass
    
    @abstractmethod
    def embed_query(self, query: str) -> List[float]:
        pass

class BaseLLM(ABC):
    @abstractmethod
    def generate(self, prompt: str) -> str:
        pass

# 添加基础分词器接口
class BaseTokenizer(ABC):
    @abstractmethod
    def split_text(self, text: str) -> List[str]:
        """将文本分割成块"""
        pass
    
    @abstractmethod
    def merge_texts(self, texts: List[str]) -> str:
        """将文本块合并"""
        pass

# 添加提示词模板接口
class BasePromptTemplate(ABC):
    @abstractmethod
    def format(self, **kwargs) -> str:
        """格式化提示词模板"""
        pass

class BaseRetriever(ABC):
    @abstractmethod
    def retrieve(self, query: str, top_k: int = 3) -> List[Document]:
        """检索相关文档"""
        pass

# RAG 核心工作流
class RAGWorkflow:
    def __init__(
        self,
        vector_store: BaseVectorStore,
        embedding_model: BaseEmbedding,
        llm: BaseLLM,
        tokenizer: BaseTokenizer = None,
        prompt_template: BasePromptTemplate = None,
        retriever: BaseRetriever = None
    ):
        self.vector_store = vector_store
        self.embedding_model = embedding_model
        self.llm = llm
        self.tokenizer = tokenizer
        self.prompt_template = prompt_template
        self.retriever = retriever
    
    def add_documents(self, documents: List[Document]):
        """添加文档到知识库"""
        if self.tokenizer:
            # 如果有分词器，先进行文档分块
            processed_docs = []
            for doc in documents:
                chunks = self.tokenizer.split_text(doc.content)
                for chunk in chunks:
                    processed_docs.append(Document(
                        content=chunk,
                        metadata=doc.metadata
                    ))
            documents = processed_docs
            
        self.vector_store.add_documents(documents)
    
    def query(self, user_query: str, top_k: int = 3) -> str:
        """处理用户查询"""
        # 1. 使用检索器或向量存储检索文档
        if self.retriever:
            relevant_docs = self.retriever.retrieve(user_query, top_k=top_k)
        else:
            relevant_docs = self.vector_store.search(user_query, top_k=top_k)
        
        # 2. 使用提示词模板或默认模板
        if self.prompt_template:
            prompt = self.prompt_template.format(
                context="\n".join([doc.content for doc in relevant_docs]),
                query=user_query
            )
        else:
            context = "\n".join([doc.content for doc in relevant_docs])
            prompt = f"""基于以下上下文回答问题:
            
            上下文: {context}
            
            问题: {user_query}
            """
        
        # 3. 生成回答
        response = self.llm.generate(prompt)
        return response
