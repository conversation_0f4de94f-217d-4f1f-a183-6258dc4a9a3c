"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8703],{38703:function(e,t,r){r.d(t,{Z:function(){return oe}});var o=r(67294),n=r(15063),i=r(19735),s=r(64894),a=r(17012),c=r(62208),l=r(93967),u=r.n(l),d=r(98423),p=r(53124),g=r(87462),f=r(1413),m=r(91),h={percent:0,prefixCls:"rc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1,gapPosition:"bottom"},b=function(){var e=(0,o.useRef)([]),t=(0,o.useRef)(null);return(0,o.useEffect)((function(){var r=Date.now(),o=!1;e.current.forEach((function(e){if(e){o=!0;var n=e.style;n.transitionDuration=".3s, .3s, .3s, .06s",t.current&&r-t.current<100&&(n.transitionDuration="0s, 0s")}})),o&&(t.current=Date.now())})),e.current};var y=r(71002),$=r(97685),v=r(98924),k=0,C=(0,v.Z)();var x=function(e){var t=o.useState(),r=(0,$.Z)(t,2),n=r[0],i=r[1];return o.useEffect((function(){var e;i("rc_progress_".concat((C?(e=k,k+=1):e="TEST_OR_SSR",e)))}),[]),e||n},S=function(e){var t=e.bg,r=e.children;return o.createElement("div",{style:{width:"100%",height:"100%",background:t}},r)};function w(e,t){return Object.keys(e).map((function(r){var o=parseFloat(r),n="".concat(Math.floor(o*t),"%");return"".concat(e[r]," ").concat(n)}))}var E=o.forwardRef((function(e,t){var r=e.prefixCls,n=e.color,i=e.gradientId,s=e.radius,a=e.style,c=e.ptg,l=e.strokeLinecap,u=e.strokeWidth,d=e.size,p=e.gapDegree,g=n&&"object"===(0,y.Z)(n),f=g?"#FFF":void 0,m=d/2,h=o.createElement("circle",{className:"".concat(r,"-circle-path"),r:s,cx:m,cy:m,stroke:f,strokeLinecap:l,strokeWidth:u,opacity:0===c?0:1,style:a,ref:t});if(!g)return h;var b="".concat(i,"-conic"),$=p?"".concat(180+p/2,"deg"):"0deg",v=w(n,(360-p)/360),k=w(n,1),C="conic-gradient(from ".concat($,", ").concat(v.join(", "),")"),x="linear-gradient(to ".concat(p?"bottom":"top",", ").concat(k.join(", "),")");return o.createElement(o.Fragment,null,o.createElement("mask",{id:b},h),o.createElement("foreignObject",{x:0,y:0,width:d,height:d,mask:"url(#".concat(b,")")},o.createElement(S,{bg:x},o.createElement(S,{bg:C}))))})),O=100,j=function(e,t,r,o,n,i,s,a,c,l){var u=arguments.length>10&&void 0!==arguments[10]?arguments[10]:0,d=r/100*360*((360-i)/360),p=0===i?0:{bottom:0,top:180,left:90,right:-90}[s],g=(100-o)/100*t;"round"===c&&100!==o&&(g+=l/2)>=t&&(g=t-.01);var f=50;return{stroke:"string"==typeof a?a:void 0,strokeDasharray:"".concat(t,"px ").concat(e),strokeDashoffset:g+u,transform:"rotate(".concat(n+d+p,"deg)"),transformOrigin:"".concat(f,"px ").concat(f,"px"),transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s",fillOpacity:0}},I=["id","prefixCls","steps","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","style","className","strokeColor","percent"];function W(e){var t=null!=e?e:[];return Array.isArray(t)?t:[t]}var N=function(e){var t,r,n,i=(0,f.Z)((0,f.Z)({},h),e),s=i.id,a=i.prefixCls,c=i.steps,l=i.strokeWidth,d=i.trailWidth,p=i.gapDegree,$=void 0===p?0:p,v=i.gapPosition,k=i.trailColor,C=i.strokeLinecap,S=i.style,w=i.className,N=i.strokeColor,z=i.percent,P=(0,m.Z)(i,I),A=x(s),D="".concat(A,"-gradient"),M=50-l/2,R=2*Math.PI*M,Z=$>0?90+$/2:-90,X=R*((360-$)/360),L="object"===(0,y.Z)(c)?c:{count:c,gap:2},F=L.count,T=L.gap,_=W(z),B=W(N),H=B.find((function(e){return e&&"object"===(0,y.Z)(e)})),q=H&&"object"===(0,y.Z)(H)?"butt":C,Q=j(R,X,0,100,Z,$,v,k,q,l),Y=b();return o.createElement("svg",(0,g.Z)({className:u()("".concat(a,"-circle"),w),viewBox:"0 0 ".concat(O," ").concat(O),style:S,id:s,role:"presentation"},P),!F&&o.createElement("circle",{className:"".concat(a,"-circle-trail"),r:M,cx:50,cy:50,stroke:k,strokeLinecap:q,strokeWidth:d||l,style:Q}),F?(t=Math.round(F*(_[0]/100)),r=100/F,n=0,new Array(F).fill(null).map((function(e,i){var s=i<=t-1?B[0]:k,c=s&&"object"===(0,y.Z)(s)?"url(#".concat(D,")"):void 0,u=j(R,X,n,r,Z,$,v,s,"butt",l,T);return n+=100*(X-u.strokeDashoffset+T)/X,o.createElement("circle",{key:i,className:"".concat(a,"-circle-path"),r:M,cx:50,cy:50,stroke:c,strokeWidth:l,opacity:1,style:u,ref:function(e){Y[i]=e}})}))):function(){var e=0;return _.map((function(t,r){var n=B[r]||B[B.length-1],i=j(R,X,e,t,Z,$,v,n,q,l);return e+=t,o.createElement(E,{key:r,color:n,ptg:t,radius:M,prefixCls:a,gradientId:D,style:i,strokeLinecap:q,strokeWidth:l,gapDegree:$,ref:function(e){Y[r]=e},size:O})})).reverse()}())},z=r(83062),P=r(65409);function A(e){return!e||e<0?0:e>100?100:e}function D(e){let{success:t,successPercent:r}=e,o=r;return t&&"progress"in t&&(o=t.progress),t&&"percent"in t&&(o=t.percent),o}const M=(e,t,r)=>{var o,n,i,s;let a=-1,c=-1;if("step"===t){const t=r.steps,o=r.strokeWidth;"string"==typeof e||void 0===e?(a="small"===e?2:14,c=null!=o?o:8):"number"==typeof e?[a,c]=[e,e]:[a=14,c=8]=Array.isArray(e)?e:[e.width,e.height],a*=t}else if("line"===t){const t=null==r?void 0:r.strokeWidth;"string"==typeof e||void 0===e?c=t||("small"===e?6:8):"number"==typeof e?[a,c]=[e,e]:[a=-1,c=8]=Array.isArray(e)?e:[e.width,e.height]}else"circle"!==t&&"dashboard"!==t||("string"==typeof e||void 0===e?[a,c]="small"===e?[60,60]:[120,120]:"number"==typeof e?[a,c]=[e,e]:Array.isArray(e)&&(a=null!==(n=null!==(o=e[0])&&void 0!==o?o:e[1])&&void 0!==n?n:120,c=null!==(s=null!==(i=e[0])&&void 0!==i?i:e[1])&&void 0!==s?s:120));return[a,c]};var R=e=>{const{prefixCls:t,trailColor:r=null,strokeLinecap:n="round",gapPosition:i,gapDegree:s,width:a=120,type:c,children:l,success:d,size:p=a,steps:g}=e,[f,m]=M(p,"circle");let{strokeWidth:h}=e;void 0===h&&(h=Math.max((e=>3/e*100)(f),6));const b={width:f,height:m,fontSize:.15*f+6},y=o.useMemo((()=>s||0===s?s:"dashboard"===c?75:void 0),[s,c]),$=(e=>{let{percent:t,success:r,successPercent:o}=e;const n=A(D({success:r,successPercent:o}));return[n,A(A(t)-n)]})(e),v=i||"dashboard"===c&&"bottom"||void 0,k="[object Object]"===Object.prototype.toString.call(e.strokeColor),C=(e=>{let{success:t={},strokeColor:r}=e;const{strokeColor:o}=t;return[o||P.ez.green,r||null]})({success:d,strokeColor:e.strokeColor}),x=u()(`${t}-inner`,{[`${t}-circle-gradient`]:k}),S=o.createElement(N,{steps:g,percent:g?$[1]:$,strokeWidth:h,trailWidth:h,strokeColor:g?C[1]:C,strokeLinecap:n,trailColor:r,prefixCls:t,gapDegree:y,gapPosition:v}),w=f<=20,E=o.createElement("div",{className:x,style:b},S,!w&&l);return w?o.createElement(z.Z,{title:l},E):E},Z=r(11568),X=r(14747),L=r(83559),F=r(83262);const T="--progress-line-stroke-color",_="--progress-percent",B=e=>{const t=e?"100%":"-100%";return new Z.E4(`antProgress${e?"RTL":"LTR"}Active`,{"0%":{transform:`translateX(${t}) scaleX(0)`,opacity:.1},"20%":{transform:`translateX(${t}) scaleX(0)`,opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}})},H=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:Object.assign(Object.assign({},(0,X.Wf)(e)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:e.fontSize},[`${t}-outer`]:{display:"inline-flex",alignItems:"center",width:"100%"},[`${t}-inner`]:{position:"relative",display:"inline-block",width:"100%",flex:1,overflow:"hidden",verticalAlign:"middle",backgroundColor:e.remainingColor,borderRadius:e.lineBorderRadius},[`${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.defaultColor}},[`${t}-success-bg, ${t}-bg`]:{position:"relative",background:e.defaultColor,borderRadius:e.lineBorderRadius,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`},[`${t}-layout-bottom`]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",[`${t}-text`]:{width:"max-content",marginInlineStart:0,marginTop:e.marginXXS}},[`${t}-bg`]:{overflow:"hidden","&::after":{content:'""',background:{_multi_value_:!0,value:["inherit",`var(${T})`]},height:"100%",width:`calc(1 / var(${_}) * 100%)`,display:"block"},[`&${t}-bg-inner`]:{minWidth:"max-content","&::after":{content:"none"},[`${t}-text-inner`]:{color:e.colorWhite,[`&${t}-text-bright`]:{color:"rgba(0, 0, 0, 0.45)"}}}},[`${t}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:e.colorSuccess},[`${t}-text`]:{display:"inline-block",marginInlineStart:e.marginXS,color:e.colorText,lineHeight:1,width:"2em",whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[r]:{fontSize:e.fontSize},[`&${t}-text-outer`]:{width:"max-content"},[`&${t}-text-outer${t}-text-start`]:{width:"max-content",marginInlineStart:0,marginInlineEnd:e.marginXS}},[`${t}-text-inner`]:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",marginInlineStart:0,padding:`0 ${(0,Z.bf)(e.paddingXXS)}`,[`&${t}-text-start`]:{justifyContent:"start"},[`&${t}-text-end`]:{justifyContent:"end"}},[`&${t}-status-active`]:{[`${t}-bg::before`]:{position:"absolute",inset:0,backgroundColor:e.colorBgContainer,borderRadius:e.lineBorderRadius,opacity:0,animationName:B(),animationDuration:e.progressActiveMotionDuration,animationTimingFunction:e.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${t}-rtl${t}-status-active`]:{[`${t}-bg::before`]:{animationName:B(!0)}},[`&${t}-status-exception`]:{[`${t}-bg`]:{backgroundColor:e.colorError},[`${t}-text`]:{color:e.colorError}},[`&${t}-status-exception ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorError}},[`&${t}-status-success`]:{[`${t}-bg`]:{backgroundColor:e.colorSuccess},[`${t}-text`]:{color:e.colorSuccess}},[`&${t}-status-success ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorSuccess}}})}},q=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-circle-trail`]:{stroke:e.remainingColor},[`&${t}-circle ${t}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${t}-circle ${t}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:e.circleTextColor,fontSize:e.circleTextFontSize,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[r]:{fontSize:e.circleIconFontSize}},[`${t}-circle&-status-exception`]:{[`${t}-text`]:{color:e.colorError}},[`${t}-circle&-status-success`]:{[`${t}-text`]:{color:e.colorSuccess}}},[`${t}-inline-circle`]:{lineHeight:1,[`${t}-inner`]:{verticalAlign:"bottom"}}}},Q=e=>{const{componentCls:t}=e;return{[t]:{[`${t}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:e.progressStepMinWidth,marginInlineEnd:e.progressStepMarginInlineEnd,backgroundColor:e.remainingColor,transition:`all ${e.motionDurationSlow}`,"&-active":{backgroundColor:e.defaultColor}}}}}},Y=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-small&-line, ${t}-small&-line ${t}-text ${r}`]:{fontSize:e.fontSizeSM}}}};var G=(0,L.I$)("Progress",(e=>{const t=e.calc(e.marginXXS).div(2).equal(),r=(0,F.IX)(e,{progressStepMarginInlineEnd:t,progressStepMinWidth:t,progressActiveMotionDuration:"2.4s"});return[H(r),q(r),Q(r),Y(r)]}),(e=>({circleTextColor:e.colorText,defaultColor:e.colorInfo,remainingColor:e.colorFillSecondary,lineBorderRadius:100,circleTextFontSize:"1em",circleIconFontSize:e.fontSize/e.fontSizeSM+"em"}))),J=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]])}return r};const K=(e,t)=>{const{from:r=P.ez.blue,to:o=P.ez.blue,direction:n=("rtl"===t?"to left":"to right")}=e,i=J(e,["from","to","direction"]);if(0!==Object.keys(i).length){const e=`linear-gradient(${n}, ${(e=>{let t=[];return Object.keys(e).forEach((r=>{const o=parseFloat(r.replace(/%/g,""));Number.isNaN(o)||t.push({key:o,value:e[r]})})),t=t.sort(((e,t)=>e.key-t.key)),t.map((e=>{let{key:t,value:r}=e;return`${r} ${t}%`})).join(", ")})(i)})`;return{background:e,[T]:e}}const s=`linear-gradient(${n}, ${r}, ${o})`;return{background:s,[T]:s}};var U=e=>{const{prefixCls:t,direction:r,percent:n,size:i,strokeWidth:s,strokeColor:a,strokeLinecap:c="round",children:l,trailColor:d=null,percentPosition:p,success:g}=e,{align:f,type:m}=p,h=a&&"string"!=typeof a?K(a,r):{[T]:a,background:a},b="square"===c||"butt"===c?0:void 0,y=null!=i?i:[-1,s||("small"===i?6:8)],[$,v]=M(y,"line",{strokeWidth:s});const k={backgroundColor:d||void 0,borderRadius:b},C=Object.assign(Object.assign({width:`${A(n)}%`,height:v,borderRadius:b},h),{[_]:A(n)/100}),x=D(e),S={width:`${A(x)}%`,height:v,borderRadius:b,backgroundColor:null==g?void 0:g.strokeColor},w={width:$<0?"100%":$},E=o.createElement("div",{className:`${t}-inner`,style:k},o.createElement("div",{className:u()(`${t}-bg`,`${t}-bg-${m}`),style:C},"inner"===m&&l),void 0!==x&&o.createElement("div",{className:`${t}-success-bg`,style:S})),O="outer"===m&&"start"===f,j="outer"===m&&"end"===f;return"outer"===m&&"center"===f?o.createElement("div",{className:`${t}-layout-bottom`},E,l):o.createElement("div",{className:`${t}-outer`,style:w},O&&l,E,j&&l)};var V=e=>{const{size:t,steps:r,rounding:n=Math.round,percent:i=0,strokeWidth:s=8,strokeColor:a,trailColor:c=null,prefixCls:l,children:d}=e,p=n(r*(i/100)),g=null!=t?t:["small"===t?2:14,s],[f,m]=M(g,"step",{steps:r,strokeWidth:s}),h=f/r,b=Array.from({length:r});for(let e=0;e<r;e++){const t=Array.isArray(a)?a[e]:a;b[e]=o.createElement("div",{key:e,className:u()(`${l}-steps-item`,{[`${l}-steps-item-active`]:e<=p-1}),style:{backgroundColor:e<=p-1?t:c,width:h,height:m}})}return o.createElement("div",{className:`${l}-steps-outer`},b,d)},ee=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]])}return r};const te=["normal","exception","active","success"],re=o.forwardRef(((e,t)=>{const{prefixCls:r,className:l,rootClassName:g,steps:f,strokeColor:m,percent:h=0,size:b="default",showInfo:y=!0,type:$="line",status:v,format:k,style:C,percentPosition:x={}}=e,S=ee(e,["prefixCls","className","rootClassName","steps","strokeColor","percent","size","showInfo","type","status","format","style","percentPosition"]),{align:w="end",type:E="outer"}=x,O=Array.isArray(m)?m[0]:m,j="string"==typeof m||Array.isArray(m)?m:void 0,I=o.useMemo((()=>{if(O){const e="string"==typeof O?O:Object.values(O)[0];return new n.t(e).isLight()}return!1}),[m]),W=o.useMemo((()=>{var t,r;const o=D(e);return parseInt(void 0!==o?null===(t=null!=o?o:0)||void 0===t?void 0:t.toString():null===(r=null!=h?h:0)||void 0===r?void 0:r.toString(),10)}),[h,e.success,e.successPercent]),N=o.useMemo((()=>!te.includes(v)&&W>=100?"success":v||"normal"),[v,W]),{getPrefixCls:z,direction:P,progress:Z}=o.useContext(p.E_),X=z("progress",r),[L,F,T]=G(X),_="line"===$,B=_&&!f,H=o.useMemo((()=>{if(!y)return null;const t=D(e);let r;const n=_&&I&&"inner"===E;return"inner"===E||k||"exception"!==N&&"success"!==N?r=(k||(e=>`${e}%`))(A(h),A(t)):"exception"===N?r=_?o.createElement(a.Z,null):o.createElement(c.Z,null):"success"===N&&(r=_?o.createElement(i.Z,null):o.createElement(s.Z,null)),o.createElement("span",{className:u()(`${X}-text`,{[`${X}-text-bright`]:n,[`${X}-text-${w}`]:B,[`${X}-text-${E}`]:B}),title:"string"==typeof r?r:void 0},r)}),[y,h,W,N,$,X,k]);let q;"line"===$?q=f?o.createElement(V,Object.assign({},e,{strokeColor:j,prefixCls:X,steps:"object"==typeof f?f.count:f}),H):o.createElement(U,Object.assign({},e,{strokeColor:O,prefixCls:X,direction:P,percentPosition:{align:w,type:E}}),H):"circle"!==$&&"dashboard"!==$||(q=o.createElement(R,Object.assign({},e,{strokeColor:O,prefixCls:X,progressStatus:N}),H));const Q=u()(X,`${X}-status-${N}`,{[`${X}-${"dashboard"===$?"circle":$}`]:"line"!==$,[`${X}-inline-circle`]:"circle"===$&&M(b,"circle")[0]<=20,[`${X}-line`]:B,[`${X}-line-align-${w}`]:B,[`${X}-line-position-${E}`]:B,[`${X}-steps`]:f,[`${X}-show-info`]:y,[`${X}-${b}`]:"string"==typeof b,[`${X}-rtl`]:"rtl"===P},null==Z?void 0:Z.className,l,g,F,T);return L(o.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},null==Z?void 0:Z.style),C),className:Q,role:"progressbar","aria-valuenow":W,"aria-valuemin":0,"aria-valuemax":100},(0,d.Z)(S,["trailColor","strokeWidth","width","gapDegree","gapPosition","strokeLinecap","success","successPercent"])),q))}));var oe=re}}]);