# 贷前助手 API 文档

## 贷款分析接口

### 基本信息

- **接口URL**: `/api/v1/loan-analysis`
- **请求方法**: POST
- **接口描述**: 对企业进行贷前分析，支持流式和非流式响应

### 请求参数

#### Headers

| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| Authorization | 是 | string | Bearer Token 认证 |

#### Body 参数

```json
{
    "enterpriseId": "string",     // 企业ID，必填
    "dimensions": ["string"],     // 分析维度列表，可选，默认为空数组
    "executionMode": "SYNC",      // 执行模式，可选，默认为 "SYNC"
    "outputFormat": "MARKDOWN",   // 输出格式，可选，默认为 "MARKDOWN"
    "stream": false,              // 是否使用流式响应，可选，默认为 false
    "context": {}                 // 上下文信息，可选，默认为空对象
}
```

### 响应参数

#### 流式响应 (stream=true)

- **Content-Type**: `text/event-stream`
- **响应格式**: Server-Sent Events (SSE)
- **示例**:
```
data: 分析结果片段1
data: 分析结果片段2
...
event: end
data: Stream has ended
```

#### 非流式响应 (stream=false)

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "dimension1": "分析结果1",
        "dimension2": "分析结果2"
    }
}
```

### 错误码

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误，目前仅支持同步模式和 MARKDOWN 格式 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

### 使用示例

```python
import requests

url = "http://your-domain/api/v1/loan-analysis"
headers = {
    "Authorization": "Bearer your-token",
    "Content-Type": "application/json"
}
data = {
    "enterpriseId": "123456",
    "dimensions": ["financial", "credit"],
    "stream": True
}

response = requests.post(url, headers=headers, json=data, stream=True)
for line in response.iter_lines():
    if line:
        print(line.decode())
```

### 注意事项

1. 目前仅支持同步执行模式（SYNC）
2. 输出格式仅支持 MARKDOWN
3. 流式响应会实时返回分析结果
4. 所有请求都会被记录到数据库中用于审计 