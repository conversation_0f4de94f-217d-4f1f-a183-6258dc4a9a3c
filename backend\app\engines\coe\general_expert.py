from typing import Dict, Optional
from .base_expert import BaseExpert, ExpertResponse
from app.utils.logging_config import setup_logging, get_logger
from app.utils.llmClient import stream_openai_api

# 设置日志
setup_logging()
logger = get_logger(__name__)

class GeneralExpert(BaseExpert):
    """通用专家，处理其他专家无法处理的查询"""
    
    def __init__(
        self,
        model_config: Dict[str, str],
        name: str = "GeneralExpert",
        description: str = "通用问答专家，处理一般性查询"
    ):
        """
        初始化通用专家
        
        Args:
            model_config: LLM模型配置，格式如下：
                {
                    "api_key": "your-api-key",
                    "model": "gpt-3.5-turbo",
                    "service_url": "https://api.openai.com/v1/chat/completions"
                }
            name: 专家名称
            description: 专家描述
        """
        super().__init__(name, description)
        self.model_config = model_config
        
    async def can_handle(self, query: str) -> float:
        """
        通用专家的处理置信度较低，让其他专家有优先处理的机会
        
        Args:
            query: 用户查询
            
        Returns:
            float: 固定返回 0.1 的置信度
        """
        return 0.1
    
    async def handle(self, query: str, context: Optional[Dict] = None) -> ExpertResponse:
        """
        使用 LLM 处理查询
        
        Args:
            query: 用户查询
            context: 上下文信息，可能包含：
                - history: 历史对话记录
                - system_prompt: 系统提示词
                - temperature: 温度参数
                
        Returns:
            ExpertResponse: 专家响应结果
        """
        try:
            # 准备消息列表
            messages = []
            
            # 添加系统提示词
            if context and "system_prompt" in context:
                messages.append({
                    "role": "system",
                    "content": context["system_prompt"]
                })
            else:
                messages.append({
                    "role": "system",
                    "content": "你是一个专业、友好的AI助手，请尽可能准确地回答用户的问题。"
                })
            
            # 添加历史对话记录
            if context and "history" in context:
                messages.extend(context["history"])
            
            # 添加当前查询
            messages.append({
                "role": "user",
                "content": query
            })
            
            # 准备API调用参数
            api_key = self.model_config.get("api_key")
            model = self.model_config.get("model", "gpt-3.5-turbo")
            url = self.model_config.get("service_url", "https://api.openai.com/v1/chat/completions")
            
            extra = {
                "temperature": context.get("temperature", 0.7) if context else 0.7,
                "stream": True
            }
            
            # 调用LLM生成回答
            response_chunks = []
            async for chunk in stream_openai_api(
                api_key=api_key,
                model=model,
                messages=messages,
                url=url,
                extra=extra
            ):
                if isinstance(chunk, str):
                    response_chunks.append(chunk)
            
            # 合并响应内容
            full_response = "".join(response_chunks)
            
            return ExpertResponse(
                content=full_response,
                confidence=0.1,  # 保持较低的置信度
                metadata={
                    "model": model,
                    "temperature": extra["temperature"]
                }
            )
            
        except Exception as e:
            logger.error(f"通用专家处理失败: {str(e)}")
            return ExpertResponse(
                content="抱歉，我现在无法正常回答您的问题。",
                confidence=0.0,
                metadata={"error": str(e)}
            ) 