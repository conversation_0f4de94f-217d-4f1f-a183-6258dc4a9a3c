{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# !pip install llama-index llama-parse "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import nest_asyncio\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from markitdown import MarkItDown\n", "md = MarkItDown()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["索索 引引 号号:\t717804719/2025-64\n", "\n", "主题分类:\t政策法规\n", "\t主题分类\n", "政策法规\n", "\n", "办文部门:\t非银机构监管司\n", "非银机构监管司\n", "办文部门\n", "\n", "发文日期:\t2025-01-23\n", "\t发文日期\n", "\n", "公文名称:\t国家金融监督管理总局关于印发金融租赁公司监管评级办法的通知\n", "国家金融监督管理总局关于印发金融租赁公司监管评级办法的通知\n", "公文名称\n", "\n", "文文  号号:\t金规〔金规〔2025〕〕3号号\n", "\n", "国家金融监督管理总局\n", "国家金融监督管理总局\n", "\n", "金规〔金规〔2025〕〕3号号\n", "\n", "国家金融监督管理总局关于印发金融租赁公司监管评级办法的通知\n", "\n", "各金融监管局:\n", "\n", "现将《金融租赁公司监管评级办法》印发给你们,请遵照执行。\n", "\n", "国家金融监督管理总局\n", "2025年1月23日\n", "\n", "(此件发至金融监管分局和金融租赁公司)\n", "\n", "金融租赁公司监管评级办法\n", "第一章\t总\t则\n", "\n", "第一条\t 为全面评估金融租赁公司的经营管理和风险状况,合理配置监管资源,有效实施分类监管,促进金融租赁公司稳健经营和规范发展,持续提升服务实\n", "体经济能力,根据《中华人民共和国银行业监督管理法》和《金融租赁公司管理办法》(国家金融监督管理总局令2024年第6号)等有关法律、部门规章的规定,\n", "制定本办法。\n", "\n", "第二条\t 本办法适用于在中华人民共和国境内依法设立的开业满一个完整会计年度以上的金融租赁公司法人机构的监管评级。对当年新设立的金融租赁\n", "\n", "公司,可以依据本办法进行试评级。\n", "\n", "金融租赁公司专业子公司纳入母公司评级考量,不单独开展监管评级。\n", "第三条\t 金融租赁公司监管评级是指国家金融监督管理总局及其派出机构(以下简称监管机构)根据日常监管掌握情况以及其他相关信息,按照本办法对金\n", "\n", "融租赁公司的整体状况作出评价判断的监管过程,是实施分类监管的基础。\n", "\n", "分类监管是指监管机构根据金融租赁公司监管评级结果,参考金融租赁公司风险分级等情况,对不同评级级别的金融租赁公司在市场准入、监管措施以及\n", "\n", "监管资源配置等方面实施有所区别的监管政策。\n", "\n", "第四条\t金融租赁公司的监管评级工作由监管机构按照依法合规、客观公正、全面审慎的原则组织实施。\n", "\n", "第二章\t评级要素及评级结果\n", "第五条\t 金融租赁公司监管评级要素包括公司治理、资本管理、风险管理、专业能力、信息科技管理等五个部分。金融租赁公司监管评级指标由定量和\n", "\n", "定性两类评级指标组成。\n", "\n", "第六条\t 金融租赁公司监管评级综合得分的满分为100分,各部分的分值权重分别为公司治理(20%)、资本管理(15%)、风险管理(30%)、专业能力(25%)、信\n", "\n", "息科技管理(10%)。\n", "\n", "每一项评级要素满分均为100分,各评级要素得分按照要素分值权重加权汇总后形成评级综合得分。\n", "第七条\t金融租赁公司的监管评级结果从优到劣分为1—5级和S级,其中2级和3级进一步细分为A、B两个档次。金融租赁公司出现重大风险的,直接划分为5\n", "\n", "级。处于重组、被接管、实施市场退出等情况的金融租赁公司经监管机构认定后直接列为S级,不参加当年监管评级。\n", "\n", "评级综合得分在90分(含)以上为1级;70分(含)至90分为2级,其中,80分(含)至90分为2A,70分(含)至80分为2B;50分(含)至70分为3级,其中,60分(含)至70分为\n", "\n", "3A,50分(含)至60分为3B;50分以下的为4级。\n", "\n", "第三章\t组织实施\n", "\n", "第八条\t金融租赁公司的监管评级周期为一年,评价期间为上一年1月1日至12月31日。监管评级工作原则上应当于每年4月底前完成。\n", "第九条\t金融租赁公司监管评级包括信息收集、初评、复评、审核、结果反馈等环节。\n", "第十条\t国家金融监督管理总局根据金融租赁公司风险特征和监管重点,可以于每年开展监管评级工作前对相关评级指标和评价要点进行适当调整。\n", "第十一条\t 国家金融监督管理总局各级派出机构应当持续、全面、深入收集与金融租赁公司评级相关的内外部信息,充分反映金融租赁公司的公司治理、\n", "\n", "风险管理、业务经营等情况。\n", "\n", "金融租赁公司应当确保其提供的数据及其他资料真实、准确、完整,不得隐瞒重大事项,不得提供有虚假记载、误导性陈述或重大遗漏的信息和资料。\n", "监管机构发现数据和信息失真或存在重大遗漏时,应当及时与金融租赁公司核实,采用修正后的数据和信息进行监管评级,并视情节轻重依法依规采取相应\n", "\n", "监管措施。\n", "\n", "第十二条\t 国家金融监督管理总局各级派出机构根据日常监管掌握情况,对金融租赁公司进行监管评级初评和复评。初评由直接监管金融租赁公司的各级\n", "\n", "派出机构负责组织实施,对每一项评级指标的分析判断应当理由充分、分析深入、判断合理。复评由相关省级派出机构组织实施。初评与复评应当遵循评级\n", "尺度统一、客观准确和公平公正等原则,复评结果对初评结果有调整的,应当说明调整理由。\n", "\n", "第十三条\t 国家金融监督管理总局对监管评级复评结果进行审核,确定参评金融租赁公司的监管评级最终结果,并将监管评级最终结果反馈各省级派出机\n", "\n", "构。\n", "\n", "第十四条\t 年度监管评级工作结束后,参评金融租赁公司经营管理或风险状况出现重大情势变化,或者发现在监管评级期间未能掌握的重大情况,国家金融监\n", "\n", "督管理总局相关省级派出机构可以申请对监管评级结果进行动态调整。\n", "\n", "第十五条\t 国家金融监督管理总局各级派出机构应当将金融租赁公司的监管评级最终结果以及存在的主要风险和问题向金融租赁公司通报,并提出相应整\n", "\n", "改要求。\n", "\n", "金融租赁公司在收到监管机构的评级结果通报后,应当及时将有关情况通报金融租赁公司董事会、监事会(监事)和高级管理层,并通报金融租赁公司主要\n", "\n", "股东,通报内容包括但不限于:评级结果、监管机构反馈的主要问题、整改要求等。\n", "\n", "金融租赁公司应当严格限定评级结果使用用途,不得用于广告、宣传、营销等商业目的。\n", "\n", "第四章\t评级结果运用\n", "\n", "第十六条\t监管评级结果应当作为全面衡量金融租赁公司经营状况、专业能力、风险管理能力和风险程度的主要依据。\n", "监管评级结果为1级,表示金融租赁公司在各方面都是健全的,具有较强的风险抵御能力,可能存在一些轻微问题,但能够通过公司内部“三道防线”及时发现并\n", "\n", "解决。\n", "\n", "监管评级结果为2级,表示金融租赁公司基本是健全的,风险抵御能力良好,存在一些较轻问题,但能够在监管提示后在日常经营中予以纠偏解决。\n", "监管评级结果为3级,表示金融租赁公司存在一些明显问题,具备一定的风险抵御能力,但存在的问题如不及时纠正,很容易导致经营状况劣变,应当给予监管\n", "\n", "关注。\n", "\n", "监管评级结果为4级,表示金融租赁公司存在的问题较多或较为严重,并且未得到有效处理或解决,需及时采取监管措施、实施早期干预,改善经营状况、降\n", "\n", "低风险水平,否则可能引发重大风险。\n", "\n", "监管评级结果为5级,表示金融租赁公司为高风险机构,需要采取措施进行风险处置或救助。\n", "第十七条\t金融租赁公司的监管评级结果应当作为监管机构制定及调整监管规划、配置监管资源、采取监管措施和行动的主要依据。\n", "国家金融监督管理总局各级派出机构应当根据金融租赁公司的监管评级结果,深入分析风险及其成因,制定金融租赁公司的综合监管计划,明确监管重点,确\n", "\n", "\f定非现场监测和现场检查的频率、范围,并督促金融租赁公司对发现的问题及时整改并上报整改落实情况。\n", "\n", "对监管评级为1级的金融租赁公司,以非现场监管为主,定期监测各项监管指标,通过现场走访、监管会谈和调研等方式,掌握最新经营状况,适当放宽监管周\n", "\n", "期、降低现场检查频率。\n", "\n", "对监管评级为2级的金融租赁公司,应当适当提高非现场监管分析与现场检查的频率、力度,增加与董事会和高级管理层的监管会谈频度,及时发现金融租\n", "\n", "赁公司经营管理中存在的问题,督促其持续改善公司治理、内部控制和风险管理。\n", "\n", "对监管评级为3级的金融租赁公司,应当提高现场检查频率,加大现场检查力度,督促改善公司治理机制,加强对董事和高级管理人员履职行为的监管,密切跟\n", "\n", "踪研判金融租赁公司及主要股东经营和风险状况,加大对其高风险业务的监管指导,防范风险外溢,视情况采取相应监管措施。\n", "\n", "对监管评级为4级的金融租赁公司,应当给予持续的监管关注并实施早期干预,限制其高风险业务活动,要求其立即采取措施改善经营状况、降低风险水平,\n", "并区分问题性质依法依规要求主要股东履行补充资本、提供流动性支持等相关承诺,采取限制股东权利、限制分配红利和其他收入、责令调整董事或高级管\n", "理人员等措施,必要时暂停部分业务,制定恢复与处置计划。\n", "\n", "对监管评级为5级的金融租赁公司,按照国家金融监督管理总局关于高风险金融机构认定和处置相关规定执行。\n", "第十八条\t 国家金融监督管理总局各级派出机构应当加强对金融租赁公司单项要素得分情况的监管关注,结合评级反映的问题,针对该单项要素依法依规采\n", "\n", "取相应监管措施。\n", "\n", "第十九条\t监管评级结果应当作为监管机构对金融租赁公司业务范围、机构设立等市场准入事项实施分级分类监管的审慎性条件。\n", "对于达到监管评级良好条件的金融租赁公司,可以按照法定程序申请《金融租赁公司管理办法》第二十九条专项业务、发行资本工具、设立专业子公司\n", "\n", "等市场准入事项,可以优先试点创新类业务。\n", "\n", "国家金融监督管理总局可以根据行业发展情况和风险监管要求对分级分类监管条件进行适当调整。\n", "第二十条\t监管评级结果下降不改变金融租赁公司已获批的业务资格。监管评级结果上升需要恢复开展业务的,无需重新申请业务资格。\n", "金融租赁公司因监管评级结果下降不再符合申请开展《金融租赁公司管理办法》第二十九条相应业务的条件时,监管机构可以依照法定程序暂停其开展\n", "\n", "一项或多项专项业务,也可以根据实际情况,对相关金融租赁公司设置一年观察期,如下一年度监管评级结果仍不符合的,不得新增开展相关业务。\n", "\n", "第二十一条\t本办法由国家金融监督管理总局负责解释。\n", "第二十二条\t 本办法自印发之日起施行。《中国银保监会办公厅关于印发金融租赁公司监管评级办法(试行)的通知》(银保监办发〔2020〕60号)同时废\n", "\n", "第五章\t附\t则\n", "\n", "止。\n", "\n", "国家金融监督管理总局修订发布《金融租赁公司监管评级办法》\n", "https://www.nfra.gov.cn/cn/view/pages/ItemDetail.html?docId=1196824&itemId=915\n", "国家金融监督管理总局有关司局负责人就《金融租赁公司监管评级办法》答记者问\n", "https://www.nfra.gov.cn/cn/view/pages/ItemDetail.html?docId=1196811&itemId=915\n", "\n", "手机扫一扫打开此页\n", "\n", "\n"]}], "source": ["result = md.convert(\"./data/1196832.pdf\")\n", "print(result.text_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "# pdfminer.six"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from pdfminer.high_level import extract_text\n", "import re"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["text = extract_text(\"./data/1196832.pdf\")\n", "markdown_text = text.replace('\\n', '  \\n')  # 将换行符替换为 Markdown 换行"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["'索索\\u2002引引\\u2002号号:\\t717804719/2025-64  \\n  \\n主题分类:\\t政策法规  \\n\\t主题分类  \\n政策法规  \\n  \\n办文部门:\\t非银机构监管司  \\n非银机构监管司  \\n办文部门  \\n  \\n发文日期:\\t2025-01-23  \\n\\t发文日期  \\n  \\n公文名称:\\t国家金融监督管理总局关于印发金融租赁公司监管评级办法的通知  \\n国家金融监督管理总局关于印发金融租赁公司监管评级办法的通知  \\n公文名称  \\n  \\n文文\\u2003\\u2003号号:\\t金规〔金规〔2025〕〕3号号  \\n  \\n国家金融监督管理总局  \\n国家金融监督管理总局  \\n  \\n金规〔金规〔2025〕〕3号号  \\n  \\n国家金融监督管理总局关于印发金融租赁公司监管评级办法的通知  \\n  \\n各金融监管局:  \\n  \\n现将《金融租赁公司监管评级办法》印发给你们,请遵照执行。  \\n  \\n国家金融监督管理总局  \\n2025年1月23日  \\n  \\n(此件发至金融监管分局和金融租赁公司)  \\n  \\n金融租赁公司监管评级办法  \\n第一章\\t总\\t则  \\n  \\n第一条\\t 为全面评估金融租赁公司的经营管理和风险状况,合理配置监管资源,有效实施分类监管,促进金融租赁公司稳健经营和规范发展,持续提升服务实  \\n体经济能力,根据《中华人民共和国银行业监督管理法》和《金融租赁公司管理办法》(国家金融监督管理总局令2024年第6号)等有关法律、部门规章的规定,  \\n制定本办法。  \\n  \\n第二条\\t 本办法适用于在中华人民共和国境内依法设立的开业满一个完整会计年度以上的金融租赁公司法人机构的监管评级。对当年新设立的金融租赁  \\n  \\n公司,可以依据本办法进行试评级。  \\n  \\n金融租赁公司专业子公司纳入母公司评级考量,不单独开展监管评级。  \\n第三条\\t 金融租赁公司监管评级是指国家金融监督管理总局及其派出机构(以下简称监管机构)根据日常监管掌握情况以及其他相关信息,按照本办法对金  \\n  \\n融租赁公司的整体状况作出评价判断的监管过程,是实施分类监管的基础。  \\n  \\n分类监管是指监管机构根据金融租赁公司监管评级结果,参考金融租赁公司风险分级等情况,对不同评级级别的金融租赁公司在市场准入、监管措施以及  \\n  \\n监管资源配置等方面实施有所区别的监管政策。  \\n  \\n第四条\\t金融租赁公司的监管评级工作由监管机构按照依法合规、客观公正、全面审慎的原则组织实施。  \\n  \\n第二章\\t评级要素及评级结果  \\n第五条\\t 金融租赁公司监管评级要素包括公司治理、资本管理、风险管理、专业能力、信息科技管理等五个部分。金融租赁公司监管评级指标由定量和  \\n  \\n定性两类评级指标组成。  \\n  \\n第六条\\t 金融租赁公司监管评级综合得分的满分为100分,各部分的分值权重分别为公司治理(20%)、资本管理(15%)、风险管理(30%)、专业能力(25%)、信  \\n  \\n息科技管理(10%)。  \\n  \\n每一项评级要素满分均为100分,各评级要素得分按照要素分值权重加权汇总后形成评级综合得分。  \\n第七条\\t金融租赁公司的监管评级结果从优到劣分为1—5级和S级,其中2级和3级进一步细分为A、B两个档次。金融租赁公司出现重大风险的,直接划分为5  \\n  \\n级。处于重组、被接管、实施市场退出等情况的金融租赁公司经监管机构认定后直接列为S级,不参加当年监管评级。  \\n  \\n评级综合得分在90分(含)以上为1级;70分(含)至90分为2级,其中,80分(含)至90分为2A,70分(含)至80分为2B;50分(含)至70分为3级,其中,60分(含)至70分为  \\n  \\n3A,50分(含)至60分为3B;50分以下的为4级。  \\n  \\n第三章\\t组织实施  \\n  \\n第八条\\t金融租赁公司的监管评级周期为一年,评价期间为上一年1月1日至12月31日。监管评级工作原则上应当于每年4月底前完成。  \\n第九条\\t金融租赁公司监管评级包括信息收集、初评、复评、审核、结果反馈等环节。  \\n第十条\\t国家金融监督管理总局根据金融租赁公司风险特征和监管重点,可以于每年开展监管评级工作前对相关评级指标和评价要点进行适当调整。  \\n第十一条\\t 国家金融监督管理总局各级派出机构应当持续、全面、深入收集与金融租赁公司评级相关的内外部信息,充分反映金融租赁公司的公司治理、  \\n  \\n风险管理、业务经营等情况。  \\n  \\n金融租赁公司应当确保其提供的数据及其他资料真实、准确、完整,不得隐瞒重大事项,不得提供有虚假记载、误导性陈述或重大遗漏的信息和资料。  \\n监管机构发现数据和信息失真或存在重大遗漏时,应当及时与金融租赁公司核实,采用修正后的数据和信息进行监管评级,并视情节轻重依法依规采取相应  \\n  \\n监管措施。  \\n  \\n第十二条\\t 国家金融监督管理总局各级派出机构根据日常监管掌握情况,对金融租赁公司进行监管评级初评和复评。初评由直接监管金融租赁公司的各级  \\n  \\n派出机构负责组织实施,对每一项评级指标的分析判断应当理由充分、分析深入、判断合理。复评由相关省级派出机构组织实施。初评与复评应当遵循评级  \\n尺度统一、客观准确和公平公正等原则,复评结果对初评结果有调整的,应当说明调整理由。  \\n  \\n第十三条\\t 国家金融监督管理总局对监管评级复评结果进行审核,确定参评金融租赁公司的监管评级最终结果,并将监管评级最终结果反馈各省级派出机  \\n  \\n构。  \\n  \\n第十四条\\t 年度监管评级工作结束后,参评金融租赁公司经营管理或风险状况出现重大情势变化,或者发现在监管评级期间未能掌握的重大情况,国家金融监  \\n  \\n督管理总局相关省级派出机构可以申请对监管评级结果进行动态调整。  \\n  \\n第十五条\\t 国家金融监督管理总局各级派出机构应当将金融租赁公司的监管评级最终结果以及存在的主要风险和问题向金融租赁公司通报,并提出相应整  \\n  \\n改要求。  \\n  \\n金融租赁公司在收到监管机构的评级结果通报后,应当及时将有关情况通报金融租赁公司董事会、监事会(监事)和高级管理层,并通报金融租赁公司主要  \\n  \\n股东,通报内容包括但不限于:评级结果、监管机构反馈的主要问题、整改要求等。  \\n  \\n金融租赁公司应当严格限定评级结果使用用途,不得用于广告、宣传、营销等商业目的。  \\n  \\n第四章\\t评级结果运用  \\n  \\n第十六条\\t监管评级结果应当作为全面衡量金融租赁公司经营状况、专业能力、风险管理能力和风险程度的主要依据。  \\n监管评级结果为1级,表示金融租赁公司在各方面都是健全的,具有较强的风险抵御能力,可能存在一些轻微问题,但能够通过公司内部“三道防线”及时发现并  \\n  \\n解决。  \\n  \\n监管评级结果为2级,表示金融租赁公司基本是健全的,风险抵御能力良好,存在一些较轻问题,但能够在监管提示后在日常经营中予以纠偏解决。  \\n监管评级结果为3级,表示金融租赁公司存在一些明显问题,具备一定的风险抵御能力,但存在的问题如不及时纠正,很容易导致经营状况劣变,应当给予监管  \\n  \\n关注。  \\n  \\n监管评级结果为4级,表示金融租赁公司存在的问题较多或较为严重,并且未得到有效处理或解决,需及时采取监管措施、实施早期干预,改善经营状况、降  \\n  \\n低风险水平,否则可能引发重大风险。  \\n  \\n监管评级结果为5级,表示金融租赁公司为高风险机构,需要采取措施进行风险处置或救助。  \\n第十七条\\t金融租赁公司的监管评级结果应当作为监管机构制定及调整监管规划、配置监管资源、采取监管措施和行动的主要依据。  \\n国家金融监督管理总局各级派出机构应当根据金融租赁公司的监管评级结果,深入分析风险及其成因,制定金融租赁公司的综合监管计划,明确监管重点,确  \\n  \\n\\x0c定非现场监测和现场检查的频率、范围,并督促金融租赁公司对发现的问题及时整改并上报整改落实情况。  \\n  \\n对监管评级为1级的金融租赁公司,以非现场监管为主,定期监测各项监管指标,通过现场走访、监管会谈和调研等方式,掌握最新经营状况,适当放宽监管周  \\n  \\n期、降低现场检查频率。  \\n  \\n对监管评级为2级的金融租赁公司,应当适当提高非现场监管分析与现场检查的频率、力度,增加与董事会和高级管理层的监管会谈频度,及时发现金融租  \\n  \\n赁公司经营管理中存在的问题,督促其持续改善公司治理、内部控制和风险管理。  \\n  \\n对监管评级为3级的金融租赁公司,应当提高现场检查频率,加大现场检查力度,督促改善公司治理机制,加强对董事和高级管理人员履职行为的监管,密切跟  \\n  \\n踪研判金融租赁公司及主要股东经营和风险状况,加大对其高风险业务的监管指导,防范风险外溢,视情况采取相应监管措施。  \\n  \\n对监管评级为4级的金融租赁公司,应当给予持续的监管关注并实施早期干预,限制其高风险业务活动,要求其立即采取措施改善经营状况、降低风险水平,  \\n并区分问题性质依法依规要求主要股东履行补充资本、提供流动性支持等相关承诺,采取限制股东权利、限制分配红利和其他收入、责令调整董事或高级管  \\n理人员等措施,必要时暂停部分业务,制定恢复与处置计划。  \\n  \\n对监管评级为5级的金融租赁公司,按照国家金融监督管理总局关于高风险金融机构认定和处置相关规定执行。  \\n第十八条\\t 国家金融监督管理总局各级派出机构应当加强对金融租赁公司单项要素得分情况的监管关注,结合评级反映的问题,针对该单项要素依法依规采  \\n  \\n取相应监管措施。  \\n  \\n第十九条\\t监管评级结果应当作为监管机构对金融租赁公司业务范围、机构设立等市场准入事项实施分级分类监管的审慎性条件。  \\n对于达到监管评级良好条件的金融租赁公司,可以按照法定程序申请《金融租赁公司管理办法》第二十九条专项业务、发行资本工具、设立专业子公司  \\n  \\n等市场准入事项,可以优先试点创新类业务。  \\n  \\n国家金融监督管理总局可以根据行业发展情况和风险监管要求对分级分类监管条件进行适当调整。  \\n第二十条\\t监管评级结果下降不改变金融租赁公司已获批的业务资格。监管评级结果上升需要恢复开展业务的,无需重新申请业务资格。  \\n金融租赁公司因监管评级结果下降不再符合申请开展《金融租赁公司管理办法》第二十九条相应业务的条件时,监管机构可以依照法定程序暂停其开展  \\n  \\n一项或多项专项业务,也可以根据实际情况,对相关金融租赁公司设置一年观察期,如下一年度监管评级结果仍不符合的,不得新增开展相关业务。  \\n  \\n第二十一条\\t本办法由国家金融监督管理总局负责解释。  \\n第二十二条\\t 本办法自印发之日起施行。《中国银保监会办公厅关于印发金融租赁公司监管评级办法(试行)的通知》(银保监办发〔2020〕60号)同时废  \\n  \\n第五章\\t附\\t则  \\n  \\n止。  \\n  \\n国家金融监督管理总局修订发布《金融租赁公司监管评级办法》  \\nhttps://www.nfra.gov.cn/cn/view/pages/ItemDetail.html?docId=1196824&itemId=915  \\n国家金融监督管理总局有关司局负责人就《金融租赁公司监管评级办法》答记者问  \\nhttps://www.nfra.gov.cn/cn/view/pages/ItemDetail.html?docId=1196811&itemId=915  \\n  \\n手机扫一扫打开此页  \\n  \\n\\x0c'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["markdown_text"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import requests\n", "M3_LARGE_EMBDDING_SERVICE = 'http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/bge_m3_finetune_20240710/v1/embeddings'  # 最新（20240710）阿里派平台\n", "access_token = 'ZWY0YzJkZTM4OGI0YmI3YjljOTI4NGE5ZDMxMDVhMzUzZjA3YmYxMw=='  # 派平台调此接口需要先做口令验证\n", "\n", "def get_embedding(text):\n", "    headers = {'Content-Type': 'application/json', 'Authorization': f'{access_token}'}\n", "    req = {\n", "        \"input\": [text],\n", "        \"model\": \"bge-m3\"\n", "    }\n", "    embdd_response = requests.post(url=M3_LARGE_EMBDDING_SERVICE, json=req, headers=headers)\n", "    if embdd_response.status_code == 200:\n", "        return embdd_response.json()['data'][0]['embedding']\n", "    else:\n", "        return None\n", "    "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["r = get_embedding(\"总结一下，映射结构应该包括各个字段的类型，特别是embedding的正确类型和维度，以及index_content的分词器设置\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["[0.70654296875,\n", " -0.0286407470703125,\n", " -0.033599853515625,\n", " 0.0227508544921875,\n", " 0.0185546875,\n", " -0.003509521484375,\n", " -0.00873565673828125,\n", " -0.004451751708984375,\n", " 0.017364501953125,\n", " -0.0125274658203125,\n", " -0.005168914794921875,\n", " -0.0011949539184570312,\n", " -0.01203155517578125,\n", " 0.0274810791015625,\n", " 0.01885986328125,\n", " -0.035369873046875,\n", " 0.0034694671630859375,\n", " -0.0162353515625,\n", " -0.0045623779296875,\n", " 0.0458984375,\n", " -0.0285491943359375,\n", " -0.01116943359375,\n", " 0.0092926025390625,\n", " -0.018035888671875,\n", " -0.0257110595703125,\n", " 0.01213836669921875,\n", " 0.026885986328125,\n", " -4.744529724121094e-05,\n", " -0.00981903076171875,\n", " -0.0034160614013671875,\n", " -0.01171875,\n", " 0.01479339599609375,\n", " -0.030609130859375,\n", " 0.048736572265625,\n", " -0.0168609619140625,\n", " 0.009735107421875,\n", " -0.04901123046875,\n", " -0.0002639293670654297,\n", " -0.028167724609375,\n", " -0.02008056640625,\n", " 0.043182373046875,\n", " -0.02471923828125,\n", " 0.0070953369140625,\n", " -0.00310516357421875,\n", " 0.0020923614501953125,\n", " 0.0002682209014892578,\n", " -0.01401519775390625,\n", " 0.03753662109375,\n", " -0.01085662841796875,\n", " 0.0025997161865234375,\n", " 0.01120758056640625,\n", " -0.031890869140625,\n", " -0.0272216796875,\n", " -0.01512908935546875,\n", " 0.002490997314453125,\n", " -0.028594970703125,\n", " -0.015625,\n", " -0.0172576904296875,\n", " 0.00499725341796875,\n", " -0.010650634765625,\n", " -0.0093994140625,\n", " -0.031524658203125,\n", " 0.0014801025390625,\n", " -0.017669677734375,\n", " 0.01329803466796875,\n", " 0.024566650390625,\n", " -0.00543212890625,\n", " -0.0149688720703125,\n", " -0.0012102127075195312,\n", " -0.0126953125,\n", " -0.01078033447265625,\n", " -0.01806640625,\n", " 0.0035400390625,\n", " -0.00768280029296875,\n", " -0.016510009765625,\n", " -0.02410888671875,\n", " 0.0032978057861328125,\n", " -0.0185089111328125,\n", " -0.0205230712890625,\n", " -0.042083740234375,\n", " 0.01096343994140625,\n", " -0.022216796875,\n", " 0.00540924072265625,\n", " 0.054229736328125,\n", " -0.005596160888671875,\n", " -0.0116119384765625,\n", " 0.0251007080078125,\n", " -0.0174560546875,\n", " 0.0307159423828125,\n", " 0.041290283203125,\n", " 0.006534576416015625,\n", " -0.0174407958984375,\n", " -0.0357666015625,\n", " 0.021759033203125,\n", " 0.0109100341796875,\n", " -0.037109375,\n", " -0.011077880859375,\n", " 0.0268707275390625,\n", " 0.019256591796875,\n", " 0.03717041015625,\n", " 0.0012693405151367188,\n", " -0.0217132568359375,\n", " 0.01148223876953125,\n", " 0.01549530029296875,\n", " 0.0160064697265625,\n", " 0.036651611328125,\n", " -0.01462554931640625,\n", " 0.01507568359375,\n", " -0.007236480712890625,\n", " 0.0206146240234375,\n", " -0.0134124755859375,\n", " -0.0031223297119140625,\n", " 0.004451751708984375,\n", " 0.02484130859375,\n", " 0.0102996826171875,\n", " 0.027862548828125,\n", " -0.0091094970703125,\n", " 0.003078460693359375,\n", " -0.0199127197265625,\n", " 0.0006818771362304688,\n", " -0.001995086669921875,\n", " 0.07830810546875,\n", " -0.01303863525390625,\n", " 0.0197296142578125,\n", " -0.00473785400390625,\n", " 0.01116943359375,\n", " 0.01910400390625,\n", " -0.0013637542724609375,\n", " 0.039398193359375,\n", " -0.0167083740234375,\n", " -0.004413604736328125,\n", " 0.013946533203125,\n", " -0.0011014938354492188,\n", " 0.005313873291015625,\n", " -0.017364501953125,\n", " -0.0235748291015625,\n", " -0.0020542144775390625,\n", " 0.0214385986328125,\n", " 0.0124359130859375,\n", " 0.00661468505859375,\n", " -0.04840087890625,\n", " 0.04168701171875,\n", " 0.004360198974609375,\n", " 0.00765228271484375,\n", " -0.0179595947265625,\n", " -0.005767822265625,\n", " -0.0190277099609375,\n", " 0.004383087158203125,\n", " 0.02410888671875,\n", " 0.0021533966064453125,\n", " -0.0230255126953125,\n", " -0.00899505615234375,\n", " 0.01300811767578125,\n", " 0.01355743408203125,\n", " 0.061981201171875,\n", " 0.0290679931640625,\n", " -0.03460693359375,\n", " -0.0015497207641601562,\n", " 0.000640869140625,\n", " 0.01105499267578125,\n", " 0.00811767578125,\n", " -0.047271728515625,\n", " 0.045257568359375,\n", " 0.0037174224853515625,\n", " -0.0017375946044921875,\n", " 0.041900634765625,\n", " -0.01218414306640625,\n", " 0.010498046875,\n", " 0.014312744140625,\n", " -0.010894775390625,\n", " -0.01163482666015625,\n", " 0.0309906005859375,\n", " 0.01288604736328125,\n", " -0.0021419525146484375,\n", " 0.0265655517578125,\n", " -0.004238128662109375,\n", " 0.0134429931640625,\n", " 0.050689697265625,\n", " 0.00389862060546875,\n", " -0.02734375,\n", " -0.0127105712890625,\n", " -0.004810333251953125,\n", " 0.0175018310546875,\n", " -0.01023101806640625,\n", " 0.035369873046875,\n", " -0.0010309219360351562,\n", " -0.0199127197265625,\n", " -0.0149383544921875,\n", " -0.01654052734375,\n", " -0.01018524169921875,\n", " 0.02374267578125,\n", " -0.01073455810546875,\n", " -0.0211029052734375,\n", " 0.0164947509765625,\n", " -0.0118865966796875,\n", " -0.015045166015625,\n", " 0.0254669189453125,\n", " 0.046722412109375,\n", " -0.0035495758056640625,\n", " 0.0183563232421875,\n", " -0.018798828125,\n", " 0.03466796875,\n", " 0.01320648193359375,\n", " -0.004848480224609375,\n", " -0.01546478271484375,\n", " -0.0161285400390625,\n", " 0.013519287109375,\n", " -0.042083740234375,\n", " -0.031585693359375,\n", " -0.006404876708984375,\n", " -0.010833740234375,\n", " -0.00441741943359375,\n", " -0.0223388671875,\n", " -0.0283355712890625,\n", " -0.01284027099609375,\n", " -0.036773681640625,\n", " -0.0106658935546875,\n", " -0.0086669921875,\n", " 0.0182342529296875,\n", " -0.023834228515625,\n", " -0.006786346435546875,\n", " -0.0185089111328125,\n", " -0.01800537109375,\n", " 0.0389404296875,\n", " -0.00260162353515625,\n", " 0.00482177734375,\n", " -0.0016918182373046875,\n", " 0.036224365234375,\n", " -0.0214996337890625,\n", " -0.0243072509765625,\n", " -0.004177093505859375,\n", " -0.0112152099609375,\n", " 0.00888824462890625,\n", " 0.0147857666015625,\n", " -0.005126953125,\n", " 0.023162841796875,\n", " -0.03466796875,\n", " 0.0164947509765625,\n", " 0.0137786865234375,\n", " -0.00806427001953125,\n", " -0.040130615234375,\n", " 0.0110626220703125,\n", " -0.040191650390625,\n", " -0.008758544921875,\n", " -0.00342559814453125,\n", " -0.0238494873046875,\n", " -0.0272979736328125,\n", " -0.0087738037109375,\n", " 0.043212890625,\n", " -0.0208892822265625,\n", " -0.0111846923828125,\n", " 0.0032405853271484375,\n", " 0.01824951171875,\n", " -0.005924224853515625,\n", " 0.0278472900390625,\n", " -0.0020427703857421875,\n", " -0.02142333984375,\n", " 0.003173828125,\n", " 0.00705718994140625,\n", " -0.007709503173828125,\n", " -0.0033588409423828125,\n", " 0.01479339599609375,\n", " 0.00353240966796875,\n", " -0.0023441314697265625,\n", " 0.01560211181640625,\n", " -0.0115509033203125,\n", " -0.01898193359375,\n", " 0.01120758056640625,\n", " 0.0081329345703125,\n", " -0.0298919677734375,\n", " 0.01248931884765625,\n", " 0.0214080810546875,\n", " 0.01149749755859375,\n", " 0.02813720703125,\n", " -0.04901123046875,\n", " 0.01059722900390625,\n", " 0.048858642578125,\n", " -0.047149658203125,\n", " -0.0037822723388671875,\n", " 0.038421630859375,\n", " 0.022796630859375,\n", " 0.00644683837890625,\n", " -0.0162506103515625,\n", " 0.0184173583984375,\n", " -0.0104217529296875,\n", " 0.0172576904296875,\n", " -0.02142333984375,\n", " 0.0276336669921875,\n", " -0.0239105224609375,\n", " 0.0290069580078125,\n", " -0.022369384765625,\n", " -0.01151275634765625,\n", " 0.0128631591796875,\n", " 0.038360595703125,\n", " 0.0157928466796875,\n", " -0.00653839111328125,\n", " -0.00292205810546875,\n", " -0.01262664794921875,\n", " -0.07305908203125,\n", " -0.0174560546875,\n", " 0.00585174560546875,\n", " 0.00493621826171875,\n", " 0.01203155517578125,\n", " 0.0001728534698486328,\n", " -0.0257110595703125,\n", " 0.00046896934509277344,\n", " -0.006439208984375,\n", " 0.0269775390625,\n", " -0.0295867919921875,\n", " -0.0394287109375,\n", " 0.0162506103515625,\n", " -0.050506591796875,\n", " 0.06256103515625,\n", " 0.006168365478515625,\n", " 0.01462554931640625,\n", " 0.01654052734375,\n", " -0.01401519775390625,\n", " -0.0038585662841796875,\n", " -0.00228118896484375,\n", " -0.0142364501953125,\n", " -0.0026950836181640625,\n", " 0.028167724609375,\n", " 0.0018529891967773438,\n", " -0.0012540817260742188,\n", " 0.04241943359375,\n", " 0.031341552734375,\n", " -0.005466461181640625,\n", " -0.0204925537109375,\n", " -0.032073974609375,\n", " -0.00531768798828125,\n", " 0.007343292236328125,\n", " 0.0162506103515625,\n", " -0.005657196044921875,\n", " 0.00891876220703125,\n", " -0.02777099609375,\n", " 0.02301025390625,\n", " -0.01140594482421875,\n", " 0.011505126953125,\n", " 0.025390625,\n", " -0.0239105224609375,\n", " 0.01537322998046875,\n", " 0.01059722900390625,\n", " -0.0153961181640625,\n", " 0.01087188720703125,\n", " 0.007266998291015625,\n", " -0.005840301513671875,\n", " -0.024322509765625,\n", " 0.0027446746826171875,\n", " -0.0214080810546875,\n", " -0.035858154296875,\n", " -0.0022411346435546875,\n", " -0.017852783203125,\n", " -0.052703857421875,\n", " -0.0226898193359375,\n", " 0.00946044921875,\n", " 0.03759765625,\n", " 0.0011835098266601562,\n", " 0.016845703125,\n", " 0.006740570068359375,\n", " -0.017608642578125,\n", " 0.0012807846069335938,\n", " 0.0092926025390625,\n", " -0.045623779296875,\n", " -0.005260467529296875,\n", " 0.0288543701171875,\n", " 0.01531982421875,\n", " 0.0118865966796875,\n", " -0.01702880859375,\n", " 0.006015777587890625,\n", " 0.0020732879638671875,\n", " -0.001995086669921875,\n", " -0.027557373046875,\n", " -0.03387451171875,\n", " 0.018798828125,\n", " -0.02630615234375,\n", " 0.0004611015319824219,\n", " 0.001682281494140625,\n", " -0.02880859375,\n", " -0.00030803680419921875,\n", " -0.00547027587890625,\n", " 0.0308990478515625,\n", " 0.01387786865234375,\n", " -0.0278472900390625,\n", " 0.038665771484375,\n", " 0.021759033203125,\n", " 0.0044708251953125,\n", " -0.01470947265625,\n", " 0.10919189453125,\n", " -0.00409698486328125,\n", " 0.023834228515625,\n", " 0.006687164306640625,\n", " 0.03326416015625,\n", " 0.0196380615234375,\n", " 0.02215576171875,\n", " 0.0379638671875,\n", " -0.0172576904296875,\n", " -0.0157928466796875,\n", " -0.0240936279296875,\n", " 0.02203369140625,\n", " -0.00778961181640625,\n", " -0.005970001220703125,\n", " 0.022735595703125,\n", " 0.006015777587890625,\n", " 0.01202392578125,\n", " -0.0065765380859375,\n", " 0.0389404296875,\n", " 0.0012350082397460938,\n", " -0.006923675537109375,\n", " -0.009185791015625,\n", " 0.0255584716796875,\n", " 0.0341796875,\n", " -0.016510009765625,\n", " -0.0333251953125,\n", " 0.0266571044921875,\n", " 0.00559234619140625,\n", " -0.0092926025390625,\n", " 0.0244598388671875,\n", " 0.0015125274658203125,\n", " 0.04473876953125,\n", " 0.0210723876953125,\n", " 0.035247802734375,\n", " -0.021759033203125,\n", " -0.050811767578125,\n", " 0.00446319580078125,\n", " 0.00225830078125,\n", " 0.00392913818359375,\n", " 0.0197601318359375,\n", " -0.01343536376953125,\n", " 0.00390625,\n", " -0.0274505615234375,\n", " 0.007511138916015625,\n", " 0.013641357421875,\n", " -0.0177764892578125,\n", " 0.007720947265625,\n", " -0.04443359375,\n", " -0.003299713134765625,\n", " 0.01293182373046875,\n", " 5.14984130859375e-05,\n", " -0.034088134765625,\n", " 0.0017242431640625,\n", " 0.01302337646484375,\n", " 0.011627197265625,\n", " -0.0251922607421875,\n", " -0.005153656005859375,\n", " 0.01800537109375,\n", " -0.0263519287109375,\n", " 0.0251617431640625,\n", " 0.0237274169921875,\n", " -0.00525665283203125,\n", " -0.026092529296875,\n", " -0.027801513671875,\n", " 0.0103607177734375,\n", " 0.0159759521484375,\n", " -0.0004818439483642578,\n", " -0.0311431884765625,\n", " 0.017333984375,\n", " -0.005199432373046875,\n", " 0.038970947265625,\n", " 0.00109100341796875,\n", " -0.031982421875,\n", " -0.034088134765625,\n", " -0.00571441650390625,\n", " 0.01348876953125,\n", " 0.0008330345153808594,\n", " 0.0177001953125,\n", " -0.00930023193359375,\n", " 0.014129638671875,\n", " -0.050689697265625,\n", " 0.0020122528076171875,\n", " -0.0205841064453125,\n", " -0.013275146484375,\n", " 0.0192413330078125,\n", " -0.036163330078125,\n", " -0.04071044921875,\n", " 0.0107879638671875,\n", " -0.0166778564453125,\n", " -0.0161895751953125,\n", " -0.0052947998046875,\n", " -0.01284027099609375,\n", " 0.0169677734375,\n", " 0.00914764404296875,\n", " -0.0222320556640625,\n", " 0.0009012222290039062,\n", " -0.0098724365234375,\n", " 0.01038360595703125,\n", " -0.038299560546875,\n", " -0.0025920867919921875,\n", " -0.0188751220703125,\n", " 0.0011434555053710938,\n", " -0.0207977294921875,\n", " -0.04388427734375,\n", " 0.005298614501953125,\n", " -0.0287017822265625,\n", " 0.0089111328125,\n", " -0.0016984939575195312,\n", " 0.00218963623046875,\n", " 0.00931549072265625,\n", " -0.01557159423828125,\n", " 0.0308837890625,\n", " 0.016937255859375,\n", " 0.01019287109375,\n", " -0.0178070068359375,\n", " 0.01213836669921875,\n", " -0.00783538818359375,\n", " -0.0023021697998046875,\n", " 0.005756378173828125,\n", " -0.0283966064453125,\n", " -0.0133819580078125,\n", " 0.005962371826171875,\n", " 0.00888824462890625,\n", " -0.01497650146484375,\n", " -0.0163116455078125,\n", " -0.0025463104248046875,\n", " -0.033172607421875,\n", " 0.0011606216430664062,\n", " -0.03802490234375,\n", " 0.01453399658203125,\n", " 0.01849365234375,\n", " -0.0236358642578125,\n", " 0.0018301010131835938,\n", " 0.005466461181640625,\n", " 0.033721923828125,\n", " -0.019622802734375,\n", " -0.00806427001953125,\n", " 0.018157958984375,\n", " 0.018341064453125,\n", " -0.0102691650390625,\n", " 0.0167083740234375,\n", " 0.0027904510498046875,\n", " -0.0273284912109375,\n", " -0.0286865234375,\n", " 0.00447845458984375,\n", " 0.049652099609375,\n", " 0.010711669921875,\n", " -0.004791259765625,\n", " -0.00505828857421875,\n", " 0.00612640380859375,\n", " -0.01276397705078125,\n", " 0.03240966796875,\n", " 0.02020263671875,\n", " -0.051513671875,\n", " -0.0123291015625,\n", " -0.0003840923309326172,\n", " -0.004047393798828125,\n", " 0.0307769775390625,\n", " 0.014923095703125,\n", " 0.00653076171875,\n", " 0.00040459632873535156,\n", " -0.00652313232421875,\n", " -0.0020427703857421875,\n", " 0.0181427001953125,\n", " 0.005962371826171875,\n", " 0.0277557373046875,\n", " 0.0010547637939453125,\n", " 0.0195465087890625,\n", " -0.029937744140625,\n", " -0.0093841552734375,\n", " 0.01113128662109375,\n", " -0.018707275390625,\n", " -0.0309906005859375,\n", " 0.0382080078125,\n", " 0.0213775634765625,\n", " -0.01288604736328125,\n", " -0.04412841796875,\n", " -0.024688720703125,\n", " 0.01384735107421875,\n", " 0.01285552978515625,\n", " 0.003173828125,\n", " -0.0240478515625,\n", " -0.01151275634765625,\n", " -0.0227813720703125,\n", " 0.03387451171875,\n", " -0.006805419921875,\n", " -0.01033782958984375,\n", " -0.022491455078125,\n", " 0.056121826171875,\n", " -0.0159759521484375,\n", " 0.0014514923095703125,\n", " 0.052276611328125,\n", " -0.0260467529296875,\n", " 2.86102294921875e-06,\n", " 0.0090179443359375,\n", " -0.0018157958984375,\n", " 0.0012969970703125,\n", " 0.006130218505859375,\n", " -0.0445556640625,\n", " -0.025390625,\n", " 0.030303955078125,\n", " -0.0247802734375,\n", " 0.0260162353515625,\n", " -0.026824951171875,\n", " 0.0096588134765625,\n", " 0.0209503173828125,\n", " -0.045318603515625,\n", " 0.006168365478515625,\n", " -0.027557373046875,\n", " -0.021942138671875,\n", " 0.02740478515625,\n", " 0.01189422607421875,\n", " -0.0428466796875,\n", " -0.004322052001953125,\n", " 0.020233154296875,\n", " 0.00925445556640625,\n", " 0.041656494140625,\n", " -0.006256103515625,\n", " 0.006076812744140625,\n", " -0.020721435546875,\n", " 0.01364898681640625,\n", " 0.00542449951171875,\n", " -0.004535675048828125,\n", " -0.016998291015625,\n", " 0.01409149169921875,\n", " 0.0081634521484375,\n", " 0.02264404296875,\n", " -0.0438232421875,\n", " -0.01116180419921875,\n", " 0.0270233154296875,\n", " -0.0006103515625,\n", " 0.058319091796875,\n", " -0.006107330322265625,\n", " -0.028106689453125,\n", " 0.0209197998046875,\n", " -0.004901885986328125,\n", " 0.0009083747863769531,\n", " 0.0261383056640625,\n", " 0.03265380859375,\n", " -0.01544952392578125,\n", " -0.0085601806640625,\n", " -0.02691650390625,\n", " 0.0275115966796875,\n", " 0.033416748046875,\n", " -0.0227813720703125,\n", " -0.00027060508728027344,\n", " -0.04254150390625,\n", " -0.0046234130859375,\n", " 0.0164337158203125,\n", " -0.0130462646484375,\n", " 0.0259246826171875,\n", " -0.03302001953125,\n", " 0.005924224853515625,\n", " 0.0016984939575195312,\n", " 0.000606536865234375,\n", " -0.0078582763671875,\n", " -0.01375579833984375,\n", " 0.00841522216796875,\n", " 0.012359619140625,\n", " -0.0460205078125,\n", " -0.0208282470703125,\n", " 0.0266571044921875,\n", " -0.0193023681640625,\n", " 0.0033931732177734375,\n", " -0.0110931396484375,\n", " -0.0191192626953125,\n", " -0.0026836395263671875,\n", " -0.025146484375,\n", " 0.001750946044921875,\n", " -0.022125244140625,\n", " -0.0020885467529296875,\n", " -0.0126495361328125,\n", " -0.0128936767578125,\n", " -0.006626129150390625,\n", " -0.01325225830078125,\n", " -0.0011081695556640625,\n", " -0.0220794677734375,\n", " -0.051361083984375,\n", " 0.01299285888671875,\n", " -0.026275634765625,\n", " -0.03668212890625,\n", " 0.025390625,\n", " -0.0107269287109375,\n", " 0.0234375,\n", " -0.0026226043701171875,\n", " -0.0196685791015625,\n", " 0.0012807846069335938,\n", " 0.0100250244140625,\n", " 0.0037937164306640625,\n", " -0.048431396484375,\n", " 0.03515625,\n", " 0.0067596435546875,\n", " -0.020263671875,\n", " -0.003940582275390625,\n", " 0.028961181640625,\n", " 0.029541015625,\n", " -0.036651611328125,\n", " 0.0007905960083007812,\n", " -0.0011701583862304688,\n", " -0.028228759765625,\n", " -0.00917816162109375,\n", " -0.040191650390625,\n", " -0.0022754669189453125,\n", " 0.0155487060546875,\n", " 0.0038280487060546875,\n", " -0.0021800994873046875,\n", " 0.0168304443359375,\n", " -0.018798828125,\n", " 0.0192413330078125,\n", " -0.020721435546875,\n", " -0.0260772705078125,\n", " -0.0064849853515625,\n", " -0.007640838623046875,\n", " 0.0244598388671875,\n", " 0.0239410400390625,\n", " 0.01244354248046875,\n", " 0.050537109375,\n", " -0.0305023193359375,\n", " 0.0243988037109375,\n", " -0.009124755859375,\n", " 0.0024356842041015625,\n", " 0.00011593103408813477,\n", " -0.00606536865234375,\n", " -0.0189056396484375,\n", " 0.0240325927734375,\n", " -0.01096343994140625,\n", " 0.022216796875,\n", " 0.0201263427734375,\n", " -0.0256500244140625,\n", " 0.00514984130859375,\n", " -0.01812744140625,\n", " -0.029876708984375,\n", " -0.01678466796875,\n", " -0.0184173583984375,\n", " 0.0244140625,\n", " 0.007099151611328125,\n", " 0.01221466064453125,\n", " -0.033416748046875,\n", " 0.01207733154296875,\n", " 0.035400390625,\n", " 0.01462554931640625,\n", " 0.0110321044921875,\n", " 0.027862548828125,\n", " -0.029571533203125,\n", " 0.022430419921875,\n", " -0.03179931640625,\n", " 0.0026798248291015625,\n", " -0.0068359375,\n", " -0.0028285980224609375,\n", " -0.02325439453125,\n", " -0.00888824462890625,\n", " -0.0009212493896484375,\n", " -0.0201263427734375,\n", " -0.016632080078125,\n", " -0.0030517578125,\n", " 0.03570556640625,\n", " -0.006458282470703125,\n", " 0.017547607421875,\n", " 0.01094818115234375,\n", " -0.01166534423828125,\n", " -0.0101165771484375,\n", " 0.0151519775390625,\n", " -0.0265045166015625,\n", " 0.01910400390625,\n", " 0.0212554931640625,\n", " 0.0153961181640625,\n", " 0.00588226318359375,\n", " -0.028076171875,\n", " -0.003940582275390625,\n", " -0.0191192626953125,\n", " -0.01447296142578125,\n", " -0.005764007568359375,\n", " 0.01282501220703125,\n", " 0.0013246536254882812,\n", " -0.00704193115234375,\n", " 0.0252532958984375,\n", " 0.0010309219360351562,\n", " 0.0177001953125,\n", " 0.0019464492797851562,\n", " 0.01410675048828125,\n", " -0.0015354156494140625,\n", " -0.0184783935546875,\n", " -0.003993988037109375,\n", " -0.002658843994140625,\n", " 0.0297393798828125,\n", " -0.067626953125,\n", " -0.037872314453125,\n", " 0.0135650634765625,\n", " -0.01512908935546875,\n", " 0.04473876953125,\n", " 0.0012836456298828125,\n", " 0.00891876220703125,\n", " 0.002048492431640625,\n", " 0.01375579833984375,\n", " -0.0628662109375,\n", " 0.033111572265625,\n", " -0.00397491455078125,\n", " 0.0242156982421875,\n", " -0.018035888671875,\n", " -0.0177764892578125,\n", " -0.02008056640625,\n", " -0.0204620361328125,\n", " -0.01435089111328125,\n", " -0.004901885986328125,\n", " -0.060943603515625,\n", " 0.0177459716796875,\n", " -0.0026073455810546875,\n", " -0.0219573974609375,\n", " -2.6106834411621094e-05,\n", " -0.00765228271484375,\n", " 0.01079559326171875,\n", " -0.046875,\n", " -0.0158843994140625,\n", " 0.022491455078125,\n", " -0.0026988983154296875,\n", " 0.00722503662109375,\n", " 0.040252685546875,\n", " -0.001983642578125,\n", " 0.0008444786071777344,\n", " 0.013641357421875,\n", " 0.009674072265625,\n", " 0.031494140625,\n", " -0.0021915435791015625,\n", " -0.021240234375,\n", " 0.054901123046875,\n", " 0.01372528076171875,\n", " 0.006519317626953125,\n", " 0.007678985595703125,\n", " 0.0034084320068359375,\n", " 0.0149993896484375,\n", " 0.007633209228515625,\n", " -0.01629638671875,\n", " 0.035614013671875,\n", " 0.019927978515625,\n", " 0.024627685546875,\n", " 0.0015354156494140625,\n", " 0.00957489013671875,\n", " 0.0071563720703125,\n", " -0.0011615753173828125,\n", " 0.0225830078125,\n", " 0.0007243156433105469,\n", " -0.01470947265625,\n", " -0.0404052734375,\n", " 0.0262298583984375,\n", " -0.023956298828125,\n", " -0.0182342529296875,\n", " -0.0301666259765625,\n", " -0.01056671142578125,\n", " -0.00473785400390625,\n", " -0.035308837890625,\n", " -0.011871337890625,\n", " 0.0159759521484375,\n", " -0.02685546875,\n", " -0.033172607421875,\n", " -0.0015773773193359375,\n", " 0.0280303955078125,\n", " -0.02471923828125,\n", " -0.068359375,\n", " 0.01181793212890625,\n", " 0.0005774497985839844,\n", " -0.0350341796875,\n", " 0.00977325439453125,\n", " -0.004848480224609375,\n", " 0.025390625,\n", " 0.001110076904296875,\n", " -0.0302734375,\n", " -0.04705810546875,\n", " -0.02825927734375,\n", " 0.0187225341796875,\n", " 0.055999755859375,\n", " 0.016876220703125,\n", " -0.0223388671875,\n", " 0.031646728515625,\n", " 0.023284912109375,\n", " -0.00995635986328125,\n", " -0.0221710205078125,\n", " -0.01491546630859375,\n", " 0.040679931640625,\n", " 0.0307159423828125,\n", " -0.0026226043701171875,\n", " 0.00286865234375,\n", " 0.03570556640625,\n", " -0.00777435302734375,\n", " 0.01387786865234375,\n", " -0.00044155120849609375,\n", " -0.0016431808471679688,\n", " -0.005321502685546875,\n", " 0.00827789306640625,\n", " -0.0265960693359375,\n", " -0.00824737548828125,\n", " -0.0004253387451171875,\n", " 0.0249481201171875,\n", " 0.011474609375,\n", " -0.01116943359375,\n", " -0.0031757354736328125,\n", " -0.01105499267578125,\n", " -0.00972747802734375,\n", " -0.002147674560546875,\n", " -0.0183868408203125,\n", " 0.0262451171875,\n", " 0.0128631591796875,\n", " 0.01032257080078125,\n", " 0.023681640625,\n", " -0.00670623779296875,\n", " 0.0037708282470703125,\n", " -0.0257110595703125,\n", " 0.03521728515625,\n", " 0.01085662841796875,\n", " -0.00617218017578125,\n", " -0.004528045654296875,\n", " -0.019744873046875,\n", " 0.01092529296875,\n", " -0.034149169921875,\n", " 0.036834716796875,\n", " 0.0021305084228515625,\n", " -0.0117034912109375,\n", " 0.004062652587890625,\n", " -0.00635528564453125,\n", " 0.0064849853515625,\n", " -0.017303466796875,\n", " -0.02337646484375,\n", " -0.0013952255249023438,\n", " -0.0281829833984375,\n", " -0.0267486572265625,\n", " 0.022857666015625,\n", " -0.04473876953125,\n", " -0.01363372802734375,\n", " -0.01248931884765625,\n", " 0.017852783203125,\n", " 0.033477783203125,\n", " 0.0252532958984375,\n", " 0.05474853515625,\n", " 0.0009636878967285156,\n", " 0.01189422607421875,\n", " -0.037872314453125,\n", " 0.0132293701171875,\n", " -0.01500701904296875,\n", " -0.01401519775390625,\n", " 0.015869140625,\n", " -0.0255279541015625,\n", " -0.0091400146484375,\n", " -0.00726318359375,\n", " -0.01678466796875,\n", " -0.007320404052734375,\n", " -0.0023822784423828125,\n", " 0.03375244140625,\n", " -0.00467681884765625,\n", " -0.036956787109375,\n", " -0.0119476318359375,\n", " 0.026611328125,\n", " 0.016693115234375,\n", " -0.007320404052734375,\n", " -0.038665771484375,\n", " 0.02899169921875,\n", " 0.00543975830078125,\n", " 0.0176849365234375,\n", " -0.0018072128295898438,\n", " -0.0112457275390625,\n", " 0.0172576904296875,\n", " -0.013641357421875,\n", " -0.0011882781982421875,\n", " 0.0230255126953125,\n", " 0.00913238525390625,\n", " 0.00010657310485839844,\n", " 0.002895355224609375,\n", " -0.007198333740234375,\n", " -0.00885009765625,\n", " -0.0411376953125,\n", " 0.0072021484375,\n", " -0.0064849853515625,\n", " 0.031585693359375,\n", " -0.004489898681640625,\n", " 0.00030422210693359375,\n", " 0.006183624267578125,\n", " -0.008392333984375,\n", " -0.00809478759765625,\n", " 0.038360595703125,\n", " -0.004886627197265625,\n", " -0.0005249977111816406,\n", " 0.05987548828125,\n", " 0.0198516845703125,\n", " 0.0347900390625,\n", " 0.0168609619140625,\n", " 0.0099334716796875,\n", " 0.01291656494140625,\n", " -0.0116424560546875,\n", " 0.0082244873046875,\n", " 0.00510406494140625,\n", " -0.00848388671875,\n", " -0.0213470458984375,\n", " 0.01654052734375,\n", " -0.01416778564453125,\n", " -0.01517486572265625,\n", " 0.00815582275390625,\n", " 0.019866943359375,\n", " -0.00875091552734375,\n", " -0.032745361328125,\n", " -0.01708984375,\n", " -0.01317596435546875,\n", " -0.0178070068359375,\n", " -0.0246734619140625,\n", " -0.0216827392578125,\n", " -0.021942138671875,\n", " 0.00904083251953125,\n", " -0.0011205673217773438,\n", " -0.007122039794921875,\n", " -0.01311492919921875,\n", " -0.007110595703125,\n", " -0.029541015625,\n", " -0.0295867919921875,\n", " 0.02862548828125,\n", " 0.00678253173828125,\n", " 0.0386962890625,\n", " ...]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["r"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["1536"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["len(r)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}