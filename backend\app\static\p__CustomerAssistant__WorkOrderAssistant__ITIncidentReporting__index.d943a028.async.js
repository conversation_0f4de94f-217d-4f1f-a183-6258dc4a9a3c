(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5917],{82947:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"}},52197:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"}}]},name:"star",theme:"filled"}},82826:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"},s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var i=a.forwardRef(c)},85175:function(e,t,n){"use strict";var r=n(1413),a=n(67294),o=n(48820),s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o.Z}))},i=a.forwardRef(c);t.Z=i},82061:function(e,t,n){"use strict";var r=n(1413),a=n(67294),o=n(47046),s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o.Z}))},i=a.forwardRef(c);t.Z=i},47389:function(e,t,n){"use strict";var r=n(1413),a=n(67294),o=n(27363),s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o.Z}))},i=a.forwardRef(c);t.Z=i},1832:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M834.1 469.2A347.49 347.49 0 00751.2 354l-29.1-26.7a8.09 8.09 0 00-13 3.3l-13 37.3c-8.1 23.4-23 47.3-44.1 70.8-1.4 1.5-3 1.9-4.1 2-1.1.1-2.8-.1-4.3-1.5-1.4-1.2-2.1-3-2-4.8 3.7-60.2-14.3-128.1-53.7-202C555.3 171 510 123.1 453.4 89.7l-41.3-24.3c-5.4-3.2-12.3 1-12 7.3l2.2 48c1.5 32.8-2.3 61.8-11.3 85.9-11 29.5-26.8 56.9-47 81.5a295.64 295.64 0 01-47.5 46.1 352.6 352.6 0 00-100.3 121.5A347.75 347.75 0 00160 610c0 47.2 9.3 92.9 27.7 136a349.4 349.4 0 0075.5 110.9c32.4 32 70 57.2 111.9 74.7C418.5 949.8 464.5 959 512 959s93.5-9.2 136.9-27.3A348.6 348.6 0 00760.8 857c32.4-32 57.8-69.4 75.5-110.9a344.2 344.2 0 0027.7-136c0-48.8-10-96.2-29.9-140.9zM713 808.5c-53.7 53.2-125 82.4-201 82.4s-147.3-29.2-201-82.4c-53.5-53.1-83-123.5-83-198.4 0-43.5 9.8-85.2 29.1-124 18.8-37.9 46.8-71.8 80.8-97.9a349.6 349.6 0 0058.6-56.8c25-30.5 44.6-64.5 58.2-101a240 240 0 0012.1-46.5c24.1 22.2 44.3 49 61.2 80.4 33.4 62.6 48.8 118.3 45.8 165.7a74.01 74.01 0 0024.4 59.8 73.36 73.36 0 0053.4 18.8c19.7-1 37.8-9.7 51-24.4 13.3-14.9 24.8-30.1 34.4-45.6 14 17.9 25.7 37.4 35 58.4 15.9 35.8 24 73.9 24 113.1 0 74.9-29.5 145.4-83 198.4z"}}]},name:"fire",theme:"outlined"},s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var i=a.forwardRef(c)},12906:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM512 533c-85.5 0-155.6 67.3-160 151.6a8 8 0 008 8.4h48.1c4.2 0 7.8-3.2 8.1-7.4C420 636.1 461.5 597 512 597s92.1 39.1 95.8 88.6c.3 4.2 3.9 7.4 8.1 7.4H664a8 8 0 008-8.4C667.6 600.3 597.5 533 512 533z"}}]},name:"frown",theme:"outlined"},s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var i=a.forwardRef(c)},43471:function(e,t,n){"use strict";var r=n(1413),a=n(67294),o=n(82947),s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o.Z}))},i=a.forwardRef(c);t.Z=i},25820:function(e,t,n){"use strict";var r=n(1413),a=n(67294),o=n(52197),s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o.Z}))},i=a.forwardRef(c);t.Z=i},75750:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var i=a.forwardRef(c)},87784:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"},s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var i=a.forwardRef(c)},93933:function(e,t,n){"use strict";n.d(t,{$Z:function(){return Z},$o:function(){return f},Db:function(){return x},Mw:function(){return u},SJ:function(){return v},X1:function(){return b},Xw:function(){return d},bk:function(){return C},fx:function(){return P},qP:function(){return S},tn:function(){return k},zl:function(){return I}});var r=n(15009),a=n.n(r),o=n(99289),s=n.n(o),c=n(78158),i=n(10981);function u(e){return l.apply(this,arguments)}function l(){return(l=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/myConversations",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function d(e){return p.apply(this,arguments)}function p(){return(p=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/conversations",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function f(e,t){return h.apply(this,arguments)}function h(){return(h=s()(a()().mark((function e(t,n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/conversationActive/"+t,{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function x(e){return m.apply(this,arguments)}function m(){return(m=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/clearConversation/"+t,{method:"PUT"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function v(e){return g.apply(this,arguments)}function g(){return(g=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/conversations/"+t,{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e,t){return y.apply(this,arguments)}function y(){return(y=s()(a()().mark((function e(t,n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/conversations/".concat(t),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function k(e){return w.apply(this,arguments)}function w(){return(w=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/message",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Z(e){return j.apply(this,arguments)}function j(){return(j=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/messages/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function S(e){return _.apply(this,arguments)}function _(){return(_=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/delete_messages",{method:"DELETE",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function C(e){return O.apply(this,arguments)}function O(){return(O=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/messages/collected",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function P(e){return T.apply(this,arguments)}function T(){return(T=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/app/get_knowledge_bases",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function I(e){return D.apply(this,arguments)}function D(){return(D=s()(a()().mark((function e(t){var n,r;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=(0,i.bW)(),e.next=3,fetch("/api/app/chat/chat2kb",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(n)},body:JSON.stringify(t)});case 3:if((r=e.sent).ok){e.next=6;break}throw new Error("HTTP 错误！状态码：".concat(r.status));case 6:return e.abrupt("return",r);case 7:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},13973:function(e,t,n){"use strict";n.d(t,{Z:function(){return y}});var r=n(15009),a=n.n(r),o=n(99289),s=n.n(o),c=n(5574),i=n.n(c),u=n(67294),l=n(55102),d=n(2453),p=n(17788),f=n(84567),h=n(78158);function x(e){return m.apply(this,arguments)}function m(){return(m=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,h.N)("/api/feedbacks",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var v=n(85893),g=l.Z.TextArea,b=[{label:"回答不准确",value:"inaccurate"},{label:"回答不完整",value:"incomplete"},{label:"理解错误",value:"irrelevant"},{label:"生成幻觉",value:"hallucination"},{label:"内容混乱",value:"error"},{label:"有害内容",value:"harmful"},{label:"其他问题",value:"other"}],y=function(e){var t=e.visible,n=e.messageId,r=e.conversationId,o=e.appInfo,c=e.onClose,l=u.useState(""),h=i()(l,2),m=h[0],y=h[1],k=u.useState([]),w=i()(k,2),Z=w[0],j=w[1],S=function(){var e=s()(a()().mark((function e(){var t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==Z.length){e.next=3;break}return d.ZP.error("请至少选择一个反馈类型"),e.abrupt("return");case 3:return e.prev=3,t={message_id:n,conversation_id:r,app_info:o,content:m,feedback_types:Z},console.log("feedbackData===>",t),e.next=8,x(t);case 8:e.sent.success?(d.ZP.success("感谢您的反馈！"),_()):d.ZP.error("提交反馈失败，请稍后重试"),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(3),console.error("提交反馈失败:",e.t0),d.ZP.error("提交反馈失败，请稍后重试");case 16:case"end":return e.stop()}}),e,null,[[3,12]])})));return function(){return e.apply(this,arguments)}}(),_=function(){y(""),j([]),c()};return(0,v.jsxs)(p.Z,{title:"反馈问题",open:t,onOk:S,onCancel:_,okText:"提交",cancelText:"取消",children:[(0,v.jsxs)("div",{style:{marginBottom:16},children:[(0,v.jsx)("div",{style:{marginBottom:8},children:"请选择存在的问题（可多选）："}),(0,v.jsx)(f.Z.Group,{options:b,value:Z,onChange:function(e){return j(e)}})]}),(0,v.jsxs)("div",{children:[(0,v.jsx)("div",{style:{marginBottom:8},children:"详细反馈（选填）："}),(0,v.jsx)(g,{value:m,onChange:function(e){return y(e.target.value)},placeholder:"请输入您的具体反馈意见...",rows:4})]})]})}},80365:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return ye}});var r=n(97857),a=n.n(r),o=n(15009),s=n.n(o),c=n(64599),i=n.n(c),u=n(19632),l=n.n(u),d=n(99289),p=n.n(d),f=n(5574),h=n.n(f),x=n(9783),m=n.n(x),v=n(67294),g=n(55102),b=n(8232),y=n(2453),k=n(83062),w=n(4393),Z=n(34041),j=n(98137),S=n(83622),_=n(82826),C=n(27484),O=n.n(C),P=n(24444),T=(0,P.kc)((function(e){var t=e.token;return{reference:{background:"".concat(t.colorBgLayout,"80"),maxWidth:1080,margin:"0 auto",padding:"20px",height:"100%",overflowY:"auto"}}})),I=n(85893),D=g.Z.TextArea,N=(0,v.forwardRef)((function(e,t){var n=e.messages,r=b.Z.useForm(),a=h()(r,1)[0],o=T().styles,s=(0,v.useState)([]),c=h()(s,2),i=c[0],u=c[1];(0,v.useImperativeHandle)(t,(function(){return{updateMessagesList:function(e){u(e)},getMessageId:function(e){var t;return(null===(t=i[e])||void 0===t?void 0:t.message_id)||""}}})),(0,v.useEffect)((function(){n&&n.length>0?u(n):u([])}),[n]);var l,d=function(e){if(i.length>0){var t=i[i.length-1],n=JSON.parse((r=t.content,o={},s=r.match(/Troubleshooting Suggestions:\n([\s\S]*?)\n\n/),c=r.match(/Categories:\s*([\s\S]*?)\n/),u=r.match(/Subcategories:\s*([\s\S]*?)\n\n/),l=r.match(/Comments:\n([\s\S]*)/),s&&(o["Troubleshooting Suggestions"]=String(s[1].trim().split("\n").map((function(e){return e.trim()})).filter((function(e){return e})))),c&&(o.Categories=String(c[1].trim())),u&&(o.Subcategories=String(u[1].trim())),l&&(o.Comments=String(l[1].trim().split("\n").map((function(e){return e.trim()})).filter((function(e){return e})))),JSON.stringify(o,null,4)));console.info("Last Message:",t),console.info("Parsed JSON Data:",n),console.info("Parsed JSON Data:",n["Ticket Reporting Content Suggestion"]),n&&n[e]?"Comments"===e?a.setFieldsValue(m()({},"Comments",n["Ticket Reporting Content Suggestion"]||n.Comments)):a.setFieldsValue(m()({},e,n[e])):y.ZP.error("没有对应的AI生成数据")}var r,o,s,c,u,l};return(0,I.jsx)(w.Z,{title:"IT事件报告",className:o.reference,children:(0,I.jsxs)(b.Z,{form:a,layout:"vertical",initialValues:{current_date:O()("2024-09-09"),estimated_end_date:O()("2024-09-12"),status:"initialize"},children:[(0,I.jsx)(b.Z.Item,{label:(0,I.jsx)("span",{children:"Request by"}),name:"request_by",style:{flex:1,margin:"8px"},children:(0,I.jsx)(g.Z,{placeholder:"Tom Adj"})}),(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,I.jsx)(b.Z.Item,{label:(0,I.jsxs)("span",{children:["Categories",(0,I.jsx)(k.Z,{title:"AI生成",children:(0,I.jsx)(_.Z,{style:{marginLeft:8,color:"#1890ff"},onClick:function(){d("Categories")}})})]}),name:"Categories",style:{flex:1,margin:"8px"},children:(0,I.jsxs)(Z.default,{children:[(0,I.jsx)(Z.default.Option,{value:"Hardware",children:"Hardware"}),(0,I.jsx)(Z.default.Option,{value:"Software",children:"Software"}),(0,I.jsx)(Z.default.Option,{value:"Network",children:"Network"}),(0,I.jsx)(Z.default.Option,{value:"Security",children:"Security"}),(0,I.jsx)(Z.default.Option,{value:"Security",children:"Security"})]})}),(0,I.jsx)(b.Z.Item,{name:"Subcategories",label:(0,I.jsxs)("span",{children:["Subcategories",(0,I.jsx)(k.Z,{title:"AI生成",children:(0,I.jsx)(_.Z,{style:{marginLeft:8,color:"#1890ff"},onClick:function(){d("Subcategories")}})})]}),style:{flex:1,margin:"8px"},children:(0,I.jsxs)(Z.default,{children:[(0,I.jsx)(Z.default.Option,{value:"Laptop troubleshoot and technical support",children:"Laptop troubleshoot and technical support"}),(0,I.jsx)(Z.default.Option,{value:"Printer troubleshoot and technical support",children:"Printer troubleshoot and technical support"}),(0,I.jsx)(Z.default.Option,{value:"Scanner troubleshoot and technical support",children:"Scanner troubleshoot and technical support"}),(0,I.jsx)(Z.default.Option,{value:"Other hardware troubleshoot and technical support",children:"Other hardware troubleshoot and technical support"}),(0,I.jsx)(Z.default.Option,{value:"Software installation and configuration",children:"Software installation and configuration"}),(0,I.jsx)(Z.default.Option,{value:"Software update and upgrade",children:"Software update and upgrade"}),(0,I.jsx)(Z.default.Option,{value:"Software troubleshooting and technical support",children:"Software troubleshooting and technical support"}),(0,I.jsx)(Z.default.Option,{value:"Other software troubleshoot and technical support",children:"Other software troubleshoot and technical support"}),(0,I.jsx)(Z.default.Option,{value:"Network configuration and setup",children:"Network configuration and setup"}),(0,I.jsx)(Z.default.Option,{value:"Network troubleshooting and technical support",children:"Network troubleshooting and technical support"}),(0,I.jsx)(Z.default.Option,{value:"Other network troubleshoot and technical support",children:"Other network troubleshoot and technical support"}),(0,I.jsx)(Z.default.Option,{value:"Security configuration and setup",children:"Security configuration and setup"}),(0,I.jsx)(Z.default.Option,{value:"Security troubleshooting and technical support",children:"Security troubleshooting and technical support"}),(0,I.jsx)(Z.default.Option,{value:"Other security troubleshoot and technical support",children:"Other security troubleshoot and technical support"}),(0,I.jsx)(Z.default.Option,{value:"Database configuration and setup",children:"Database configuration and setup"}),(0,I.jsx)(Z.default.Option,{value:"Database troubleshooting and technical support",children:"Database troubleshooting and technical support"}),(0,I.jsx)(Z.default.Option,{value:"Other database troubleshoot and technical support",children:"Other database troubleshoot and technical support"})]})})]}),(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,I.jsx)(b.Z.Item,{label:(0,I.jsx)("span",{children:"Branch"}),name:"branch",style:{flex:1,margin:"8px"},children:(0,I.jsx)(g.Z,{placeholder:"IT Centre"})}),(0,I.jsx)(b.Z.Item,{label:(0,I.jsx)("span",{children:"Department"}),name:"department",style:{flex:1,margin:"8px"},children:(0,I.jsx)(Z.default,{children:(0,I.jsx)(Z.default.Option,{value:"initialize",children:"Initialize"})})})]}),(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,I.jsx)(b.Z.Item,{label:(0,I.jsx)("span",{children:"Current Date"}),name:"current_date",style:{flex:1,margin:"8px"},children:(0,I.jsx)(j.default,{style:{width:"100%"}})}),(0,I.jsx)(b.Z.Item,{label:(0,I.jsx)("span",{children:"Estimated End Date"}),name:"estimated_end_date",style:{flex:1,margin:"8px"},children:(0,I.jsx)(j.default,{style:{width:"100%"}})})]}),(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,I.jsx)(b.Z.Item,{label:(0,I.jsx)("span",{children:"Contact No."}),name:"contact_no",style:{flex:1,margin:"8px"},children:(0,I.jsx)(g.Z,{placeholder:"IT Centre"})}),(0,I.jsx)(b.Z.Item,{label:(0,I.jsx)("span",{children:"Status"}),name:"status",style:{flex:1,margin:"8px"},children:(0,I.jsx)(Z.default,{children:(0,I.jsx)(Z.default.Option,{value:"initialize",children:"Initialize"})})})]}),(0,I.jsx)(b.Z.Item,{label:(l="Comments",(0,I.jsxs)("span",{children:[l,(0,I.jsx)(k.Z,{title:"AI生成",children:(0,I.jsx)(_.Z,{style:{marginLeft:8,color:"#1890ff"},onClick:function(){d(l)}})})]})),name:"Comments",style:{flex:1,margin:"8px"},children:(0,I.jsx)(D,{rows:4,placeholder:"Issue Summary: ..."})}),(0,I.jsx)(b.Z.Item,{style:{flex:1,margin:"8px 8px"},children:(0,I.jsx)(S.ZP,{type:"primary",htmlType:"submit",children:"Submit"})})]})})})),E=n(93461),z=n(34114),M=n(78205),R=n(78919),A=n(4628),L=n(9502),H=n(37864),F=n(42075),B=n(71471),Y=n(17788),J=n(74330),W=n(86250),q=n(85265),G=n(10048),U=n(10981),X=n(78404),$=n(1832),K=n(14079),V=n(51042),Q=n(82061),ee=n(47389),te=n(87784),ne=n(25820),re=n(75750),ae=n(12906),oe=n(85175),se=n(43471),ce=(0,P.kc)((function(e){var t=e.token;return{reference:{background:"".concat(t.colorBgLayout,"80"),minWidth:"400px",maxWidth:"1080px",width:"60%"},layout:{width:"100%",minWidth:"800px",borderRadius:t.borderRadius,display:"flex",background:t.colorBgContainer,fontFamily:"AlibabaPuHuiTi, ".concat(t.fontFamily,", sans-serif"),".ant-prompts":{color:t.colorText},border:"3px solid",borderImage:"linear-gradient(45deg, ".concat(t.colorPrimary,", ").concat(t.colorSplit,") 1")},menu:{height:"100%",display:"flex",flexDirection:"column"},conversations:{padding:"0 12px",flex:1,overflowY:"auto"},chat:{height:"100%",width:"40%",maxWidth:"1080px",margin:"0 auto",boxSizing:"border-box",display:"flex",flexDirection:"column",padding:t.paddingLG,gap:"16px"},messages:{flex:1},placeholder:{paddingTop:"32px"},sender:{boxShadow:t.boxShadow},logo:{display:"flex",height:"72px",alignItems:"center",justifyContent:"start",padding:"0 24px",boxSizing:"border-box",img:{width:"24px",height:"24px",display:"inline-block"},span:{display:"inline-block",margin:"0 8px",fontWeight:"bold",color:t.colorText,fontSize:"16px"}},addBtn:{background:"#1677ff0f",border:"1px solid #1677ff34",width:"calc(100% - 24px)",margin:"0 12px 24px 12px"}}})),ie=n(93933),ue=n(13973);function le(e){return e+"-"+Date.now()}var de,pe,fe=[{key:"1",label:(de=(0,I.jsx)($.Z,{style:{color:"#FF4D4F"}}),pe="IT incident reporting",(0,I.jsxs)(F.Z,{align:"start",children:[de,(0,I.jsx)("span",{children:pe})]})),description:"IT incident reporting",children:[{key:"1-1",description:"My laptop cannot connect to the Wi-Fi network."},{key:"1-2",description:'My printer is not working and shows a "paper jam" error.'},{key:"1-3",description:"My computer shows a blue screen error on startup."}]}],he=[{key:"historyConversation",description:"历史对话",icon:(0,I.jsx)(K.Z,{style:{color:"#FF4D4F"}})},{key:"newConversation",description:"新建对话",icon:(0,I.jsx)(V.Z,{style:{color:"#1890FF"}})},{key:"clearConversation",description:"清空对话",icon:(0,I.jsx)(Q.Z,{style:{color:"#1890FF"}})}],xe=(0,U.bG)(),me=(0,X.kH)(),ve=(0,G.Z)({html:!0,breaks:!0}),ge=function(e){return(0,I.jsx)(B.Z,{style:{marginBottom:0},children:(0,I.jsx)("div",{dangerouslySetInnerHTML:{__html:ve.render(e)}})})},be="ITIncidentReportReport",ye=function(){var e,t=ce().styles,n=(0,v.useState)(window.innerHeight),r=h()(n,1)[0],o=v.useRef(),c=v.useState(!1),u=h()(c,2),d=u[0],f=u[1],x=v.useState(""),m=h()(x,2),b=m[0],w=m[1],Z=v.useState([]),j=h()(Z,2),_=j[0],C=j[1],P=v.useState(),T=h()(P,2),D=T[0],B=T[1],G=(0,v.useState)(!1),X=h()(G,2),$=X[0],K=X[1],de=(0,v.useState)(!1),pe=h()(de,2),ve=pe[0],ye=pe[1],ke=(0,v.useState)(!1),we=h()(ke,2),Ze=we[0],je=we[1],Se=(0,v.useState)(""),_e=h()(Se,2),Ce=_e[0],Oe=_e[1],Pe=(0,v.useState)(""),Te=h()(Pe,2),Ie=Te[0],De=Te[1],Ne=(0,v.useRef)(null),Ee=(0,v.useState)([]),ze=h()(Ee,2),Me=ze[0],Re=ze[1],Ae=(0,v.useState)(!1),Le=h()(Ae,2),He=Le[0],Fe=Le[1],Be=(0,v.useState)(""),Ye=h()(Be,2),Je=Ye[0],We=Ye[1],qe=function(e){We(e),Fe(!0)},Ge=function(e){var t=Me.find((function(t){return t.message_id===e}));t&&navigator.clipboard.writeText(t.content).then((function(){y.ZP.success("复制成功")})).catch((function(){y.ZP.error("复制失败")}))},Ue=(0,E.Z)({request:(e=p()(s()().mark((function e(t,n){var r,a,c,u,d,p,f,h,x,m,v,g,b,k,w,Z,j,S,_,C,P,T,I,D,N,E,z,M,R,A,L,H,F,B,Y,J,W;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.messages,a=t.message,c=n.onSuccess,u=n.onUpdate,d=n.onError,e.prev=2,!ve){e.next=6;break}return y.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 6:if(ye(!0),f=(0,U.bW)(),h=a?a.id:le(o.current),a||c({content:"出现了异常:",role:"assistant",id:h,references:[],collected:!1}),x={conversation_id:o.current||"",message_id:h,meta_data:{},extra:{},role:a?a.role:"user",content:a?a.content:"",app_info:be,user_id:null==xe?void 0:xe.id,user_name:null==xe?void 0:xe.name,references:[],token_count:null,price:null,collected:!1,created_at:O()().format("YYYY-MM-DD HH:mm:ss")},Re((function(e){var t=[].concat(l()(e),[x]);return console.log("更新后的消息列表:",t),t})),o.current){e.next=15;break}throw y.ZP.error("No active conversation selected"),new Error("No active conversation selected");case 15:return console.log("activeKey===>",o.current),m={conversation_id:o.current,app_info:be,user_id:null==xe?void 0:xe.id,user_name:null==xe?void 0:xe.name,extra:{},messages:r},v={id:le(o.current),role:"user",content:"",references:[],collected:!1},g=!1,b="",k=[],u(v),e.next=24,fetch("/api/app/chat/system_app_completions",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(f)},body:JSON.stringify(m)});case 24:if((w=e.sent).ok){e.next=27;break}throw new Error("HTTP 错误！状态码：".concat(w.status));case 27:if(Z=null===(p=w.body)||void 0===p?void 0:p.getReader()){e.next=30;break}throw new Error("当前浏览器不支持 ReadableStream。");case 30:j=new TextDecoder("utf-8"),S={conversation_id:o.current||"",message_id:v.id,meta_data:{},extra:{},role:"assistant",content:"",app_info:be,user_id:null==xe?void 0:xe.id,user_name:null==xe?void 0:xe.name,references:[],token_count:null,price:null,collected:!1,created_at:O()().format("YYYY-MM-DD HH:mm:ss")};case 32:if(g){e.next=100;break}return e.next=35,Z.read();case 35:_=e.sent,C=_.value,_.done&&(g=!0),b+=j.decode(C,{stream:!0}),P=b.split("\n\n"),b=P.pop()||"",T=i()(P),e.prev=43,T.s();case 45:if((I=T.n()).done){e.next=90;break}if(""!==(D=I.value).trim()){e.next=49;break}return e.abrupt("continue",88);case 49:N=D.split("\n"),E=null,z=null,M=i()(N);try{for(M.s();!(R=M.n()).done;)(A=R.value).startsWith("event: ")?E=A.substring(7).trim():A.startsWith("data: ")&&(z=A.substring(6))}catch(e){M.e(e)}finally{M.f()}if(!z){e.next=88;break}e.t0=E,e.next="answer"===e.t0?58:"moduleStatus"===e.t0?70:"appStreamResponse"===e.t0?72:"flowResponses"===e.t0?74:"end"===e.t0?76:"error"===e.t0?78:88;break;case 58:if("[DONE]"===z){e.next=69;break}e.prev=59,H=JSON.parse(z),(F=(null===(L=H.choices[0])||void 0===L||null===(L=L.delta)||void 0===L?void 0:L.content)||"")&&(v.content+=F,u(v)),e.next=69;break;case 65:return e.prev=65,e.t1=e.catch(59),console.error("Error parsing answer data:",e.t1),e.abrupt("return",c({content:"出现了异常:"+z,role:"assistant",id:le(o.current),references:[],collected:!1}));case 69:return e.abrupt("break",88);case 70:try{B=JSON.parse(z),console.log("模块状态：",B)}catch(e){console.error("Error parsing moduleStatus data:",e)}return e.abrupt("break",88);case 72:try{Y=JSON.parse(z),console.log("appStreamData===>",Y),k=Y,v.references=k}catch(e){console.error("Error parsing appStreamResponse data:",e)}return e.abrupt("break",88);case 74:try{console.log("flowResponsesData",z)}catch(e){console.error("Error parsing flowResponses data:",e)}return e.abrupt("break",88);case 76:return g=!0,e.abrupt("break",88);case 78:e.prev=78,J=JSON.parse(z),u(J),e.next=87;break;case 83:throw e.prev=83,e.t2=e.catch(78),console.error("Error event received:",e.t2),e.t2;case 87:return e.abrupt("break",88);case 88:e.next=45;break;case 90:e.next=95;break;case 92:e.prev=92,e.t3=e.catch(43),T.e(e.t3);case 95:return e.prev=95,T.f(),e.finish(95);case 98:e.next=32;break;case 100:if(c(v),!v.content||""===v.content.trim()){e.next=108;break}return S.content=v.content,S.references=k,e.next=106,(0,ie.tn)(S);case 106:(W=e.sent).success?(S.message_id=W.data.message_id,console.log("创建消息成功，返回数据:",W.data),Re((function(e){var t=[].concat(l()(e),[W.data]);return console.log("更新后的消息列表:",t),t}))):y.ZP.error("消息上报失败");case 108:e.next=115;break;case 110:e.prev=110,e.t4=e.catch(2),console.log("error===>",e.t4),c({content:"出现了，系统正在处理其他对话。请稍后重试",role:"assistant",id:le(o.current),references:[],collected:!1}),d(e.t4 instanceof Error?e.t4:new Error("Unknown error"));case 115:return e.prev=115,ye(!1),e.finish(115);case 118:case"end":return e.stop()}}),e,null,[[2,110,115,118],[43,92,95,98],[59,65],[78,83]])}))),function(t,n){return e.apply(this,arguments)})}),Xe=h()(Ue,1)[0],$e=(0,z.Z)({agent:Xe}),Ke=$e.onRequest,Ve=$e.messages,Qe=$e.setMessages,et=function(e){B(e),console.log("activeKey 设置",e),o.current=e},tt=function(e){var t=e.filter((function(e){return e.pinned})).sort((function(e,t){return new Date(t.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return a()(a()({},e),{},{key:e.id||"",label:e.conversation_name||e.id,group:"置顶"})})),n=e.filter((function(e){return!e.pinned})).sort((function(e,t){return new Date(t.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return a()(a()({},e),{},{key:e.id||"",label:e.conversation_name||"",group:"对话"})}));C([].concat(l()(t),l()(n)))},nt=function(e){return 0===e.length?null:e.reduce((function(e,t){return new Date(t.active_at)>new Date(e.active_at)?t:e}))},rt=function(){var e=p()(s()().mark((function e(t){var n,r,a;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,ye(!0),console.info("获取对话信息",t),n=O()().format("YYYY-MM-DD HH:mm:ss"),e.next=6,(0,ie.$o)(t,{conversation_name:null,active_at:n,pinned_at:null,pinned:null});case 6:null!=(r=e.sent)&&r.messages?(console.info("设置对话信息",r.messages),a=r.messages.map((function(e){return{id:e.id||e.message_id,message:{id:e.id||e.message_id,content:e.content,role:e.role,references:e.references||[],collected:e.collected||!1},status:"assistant"===e.role?"success":"local",meta:e.meta||{avatar:"assistant"===e.role?(null==me?void 0:me.logo)||"/static/logo.png":(null==xe?void 0:xe.avatar)||"/avatar/default.jpeg"}}})),Re(r.messages),Qe(a),et(t)):y.ZP.error("获取对话信息失败"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("切换对话时出错：",e.t0);case 13:return e.prev=13,ye(!1),B(t),e.finish(13);case 17:case"end":return e.stop()}}),e,null,[[0,10,13,17]])})));return function(t){return e.apply(this,arguments)}}(),at=function(){var e=p()(s()().mark((function e(){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o.current){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,(0,ie.Db)(o.current);case 4:e.sent.success?(Re([]),Qe([])):y.ZP.error("清空对话失败");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ot=function(){var e=p()(s()().mark((function e(){var t,n,r,a;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!ve){e.next=3;break}return y.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(!(t=(0,U.bG)())){e.next=19;break}return e.prev=5,n=(new Date).toLocaleString(),r="对话-".concat(n),e.next=10,(0,ie.Xw)({user_id:t.id,user_name:t.name,conversation_name:r,app_info:be});case 10:a=e.sent,tt([].concat(l()(_),[{key:a.id||"",id:a.id||"",label:a.conversation_name||"",conversation_name:a.conversation_name||"",active_at:a.active_at||"",pinned_at:a.pinned_at,pinned:a.pinned||!1,messages:[]}])),et(a.id||""),at(),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(5),console.error("创建新对话时出错：",e.t0);case 19:case"end":return e.stop()}}),e,null,[[5,16]])})));return function(){return e.apply(this,arguments)}}(),st=function(){var e=p()(s()().mark((function e(t){var n,r,o,c,i;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=_.find((function(e){return e.key===t}))){e.next=3;break}return e.abrupt("return");case 3:return r=n.pinned,o=!r,e.prev=6,c=O()().format("YYYY-MM-DD HH:mm:ss"),e.next=10,(0,ie.X1)(t,{conversation_name:null,active_at:null,pinned:o,pinned_at:c});case 10:i=_.map((function(e){return e.key===t?a()(a()({},e),{},{pinned:o}):e})),tt(i),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),console.error("更新置顶状态时出错：",e.t0);case 17:case"end":return e.stop()}}),e,null,[[6,14]])})));return function(t){return e.apply(this,arguments)}}(),ct=function(){var e=p()(s()().mark((function e(t){var n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,ie.SJ)(t);case 3:n=_.filter((function(e){return e.key!==t})),tt(n),o.current===t&&n.length>0&&rt(n[0].key||""),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除对话时出错：",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(t){return e.apply(this,arguments)}}(),it=function(){var e=p()(s()().mark((function e(t,n){var r,o;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,_.find((function(e){return e.key===t}))){e.next=4;break}return e.abrupt("return");case 4:return r={conversation_name:n,active_at:null,pinned_at:null,pinned:null},e.next=7,(0,ie.X1)(t,r);case 7:null!=(o=e.sent)&&o.success?C((function(e){return e.map((function(e){return e.key===t?a()(a()({},e),{},{label:n}):e}))})):y.ZP.error("更新对话标题失败"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("更新对话标题时出错：",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t,n){return e.apply(this,arguments)}}(),ut=function(){var e=p()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!ve){e.next=3;break}return y.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:return e.next=5,rt(t);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();(0,v.useEffect)((function(){var e=function(){var e=p()(s()().mark((function e(){var t,n,r;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t=(0,U.bG)())){e.next=20;break}return e.prev=2,e.next=5,(0,ie.Mw)({user_id:t.id,app_info:be});case 5:if(!(n=e.sent).success||!Array.isArray(n.data)){e.next=15;break}if(0!==n.data.length){e.next=12;break}return e.next=10,ot();case 10:e.next=15;break;case 12:r=nt(n.data),tt(n.data),rt(r?r.id:n.data[0].id||"");case 15:e.next=20;break;case 17:e.prev=17,e.t0=e.catch(2),console.error("初始化对话时出错：",e.t0);case 20:case"end":return e.stop()}}),e,null,[[2,17]])})));return function(){return e.apply(this,arguments)}}();e()}),[be]);var lt=function(){var e=p()(s()().mark((function e(t){var n,r,a,o;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("重新生成消息:",t),e.prev=1,console.info(Me),n=Me.findIndex((function(e){return e.message_id===t})),console.log("currentIndex===>",n),-1!==n){e.next=8;break}return y.ZP.error("未找到指定消息"),e.abrupt("return");case 8:return r=Me[n],a=Me.slice(n),console.log("将要删除的消息:",a),e.next=13,(0,ie.qP)(a.map((function(e){return e.message_id})));case 13:e.sent.success||y.ZP.error("删除消息失败"),Re((function(e){return e.slice(0,n)})),Qe((function(e){return e.slice(0,n)})),"assistant"===r.role?(o=Me.slice(0,n).reverse().find((function(e){return"user"===e.role})))&&Ke({id:t,role:"user",content:o.content,references:[],collected:!1}):Ke({id:t,role:"user",content:r.content,references:[],collected:!1}),y.ZP.success("正在重新生成回复..."),e.next=25;break;case 21:e.prev=21,e.t0=e.catch(1),console.error("重新生成消息时出错：",e.t0),y.ZP.error("重新生成消息失败");case 25:case"end":return e.stop()}}),e,null,[[1,21]])})));return function(t){return e.apply(this,arguments)}}(),dt=function(){var e=p()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:Y.Z.confirm({title:"确认删除",content:"确定要删除这条消息吗？删除后不可恢复。",okText:"确认",cancelText:"取消",onOk:function(){return p()(s()().mark((function e(){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("delete messageId===>",t),e.next=4,(0,ie.$Z)(t);case 4:e.sent.success?(Re((function(e){return e.filter((function(e){return e.message_id!==t}))})),console.log("delete currentConversationMessages===>",Me),Qe((function(e){return e.filter((function(e){return e.message.id!==t}))})),console.log("delete messages===>",Ve),y.ZP.success("消息及相关引用已删除")):y.ZP.error("删除消息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除消息时出错：",e.t0),y.ZP.error("删除消息失败");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),pt=function(){var e=p()(s()().mark((function e(t,n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("收藏状态切换:",t,n),e.next=3,(0,ie.bk)({message_id:t,collected:!n});case 3:e.sent.success?(y.ZP.success(n?"取消收藏成功":"收藏成功"),Qe((function(e){return e.map((function(e){return e.id===t?a()(a()({},e),{},{message:a()(a()({},e.message),{},{collected:!n})}):e}))})),Re((function(e){return e.map((function(e){return e.message_id===t?a()(a()({},e),{},{collected:!n}):e}))}))):y.ZP.error(n?"取消收藏失败":"收藏失败");case 5:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),ft=function(){var e=p()(s()().mark((function e(t){var n,r,a;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!ve){e.next=3;break}return y.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(n=t.data,r=n.key,a=n.description,"historyConversation"!==r){e.next=8;break}K(!0),e.next=19;break;case 8:if("newConversation"!==r){e.next=13;break}return e.next=11,ot();case 11:e.next=19;break;case 13:if("clearConversation"!==r){e.next=18;break}return e.next=16,at();case 16:e.next=19;break;case 18:Ke({id:le(o.current),role:"user",content:a,references:[],collected:!1});case 19:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ht=(0,I.jsxs)(F.Z,{direction:"vertical",size:16,className:t.placeholder,children:[(0,I.jsx)(M.Z,{variant:"borderless",icon:(0,I.jsx)("img",{src:(null==me?void 0:me.logo)||"/static/logo.png",alt:"logo"}),title:"您好，我是智能客服助手",description:"我可以为您解答产品咨询、业务办理等相关问题，请问有什么可以帮您？"}),(0,I.jsx)(R.Z,{title:"以下是常见问题，您可以直接点击进行咨询",items:fe,styles:{list:{width:"100%"},item:{backgroundImage:"linear-gradient(137deg, #f0f7ff 0%, #fff1f0 100%)",border:0,flex:1}},onItemClick:ft})]}),xt=Ve.length>0?Ve.map((function(e){var t=e.id,n=e.message,r=e.status;return{key:o.current+"_"+t,loadingRender:function(){return(0,I.jsxs)(F.Z,{children:[(0,I.jsx)(J.Z,{size:"small"}),"模型思考中..."]})},loading:"loading"===r&&n.content.length<1,content:n.content,shape:"local"===r?"corner":"round",variant:"local"===r?"filled":"borderless",rols:n.role,messageRender:ge,avatar:"local"===r?{src:(null==xe?void 0:xe.avatar)||"/avatar/default.jpeg"}:{src:(null==me?void 0:me.logo)||"/static/logo.png"},placement:"local"!==r?"start":"end",footer:"local"!==r?(0,I.jsxs)(W.Z,{children:[(0,I.jsx)(S.ZP,{size:"small",type:"text",icon:n.collected?(0,I.jsx)(ne.Z,{style:{color:"#FFD700"}}):(0,I.jsx)(re.Z,{style:{color:"#ccc"}}),onClick:function(){return pt(n.id,n.collected)}}),(0,I.jsx)(S.ZP,{size:"small",type:"text",icon:(0,I.jsx)(ae.Z,{style:{color:"#ccc"}}),onClick:function(){return qe(n.id)}}),(0,I.jsx)(S.ZP,{size:"small",type:"text",icon:(0,I.jsx)(oe.Z,{style:{color:"#ccc"}}),onClick:function(){return Ge(n.id)}})]}):(0,I.jsxs)(W.Z,{children:[(0,I.jsx)(S.ZP,{size:"small",type:"text",icon:(0,I.jsx)(se.Z,{style:{color:"#ccc"}}),onClick:function(){return lt(n.id)}}),(0,I.jsx)(S.ZP,{size:"small",type:"text",icon:(0,I.jsx)(Q.Z,{style:{color:"#ccc"}}),onClick:function(){return dt(n.id)}}),(0,I.jsx)(S.ZP,{size:"small",type:"text",icon:n.collected?(0,I.jsx)(ne.Z,{style:{color:"#FFD700"}}):(0,I.jsx)(re.Z,{style:{color:"#ccc"}}),onClick:function(){return pt(n.id,n.collected)}}),(0,I.jsx)(S.ZP,{size:"small",type:"text",icon:(0,I.jsx)(ae.Z,{style:{color:"#ccc"}}),onClick:function(){return qe(n.id)}}),(0,I.jsx)(S.ZP,{size:"small",type:"text",icon:(0,I.jsx)(oe.Z,{style:{color:"#ccc"}}),onClick:function(){return Ge(n.id)}})]})}})):[{content:ht,variant:"borderless"}],mt=(0,I.jsx)(A.Z.Header,{title:"Attachments",open:d,onOpenChange:f,styles:{content:{padding:0}}}),vt=(0,I.jsxs)("div",{className:t.logo,children:[(0,I.jsx)("span",{children:"对话记录"}),(0,I.jsx)(k.Z,{title:"新对话",children:(0,I.jsx)(S.ZP,{type:"text",icon:(0,I.jsx)(V.Z,{}),onClick:ot,style:{fontSize:"16px"}})})]}),gt=(0,I.jsx)(Y.Z,{title:"修改对话标题",open:Ze,onOk:function(){Ie&&Ce.trim()&&(it(Ie,Ce.trim()),je(!1))},onCancel:function(){je(!1),Oe(""),De("")},children:(0,I.jsx)(g.Z,{value:Ce,onChange:function(e){return Oe(e.target.value)},placeholder:"请输入新的对话标题"})}),bt=(0,I.jsx)(q.Z,{title:"历史对话",placement:"right",width:400,onClose:function(){return K(!1)},open:$,children:(0,I.jsxs)("div",{className:t.menu,children:[vt,(0,I.jsx)(L.Z,{items:_,activeKey:D,onActiveChange:ut,menu:function(e){return{items:[{label:"重命名",key:"edit",icon:(0,I.jsx)(ee.Z,{})},{label:"置顶",key:"pin",icon:(0,I.jsx)(te.Z,{})},{label:"删除",key:"delete",icon:(0,I.jsx)(Q.Z,{}),danger:!0}],onClick:function(t){switch(console.log("menuInfo","Click ".concat(e.key," - ").concat(t.key)),t.key){case"edit":De(e.key),Oe(e.label),je(!0);break;case"pin":st(e.key);break;case"delete":if(ve)return void y.ZP.error("系统正在处理其他对话。请稍😊");ct(e.key)}}}},groupable:!0})]})});return(0,v.useEffect)((function(){console.log("currentConversationMessages 更新了:",Me)}),[Me]),(0,I.jsxs)("div",{className:t.layout,style:{height:r-56},children:[(0,I.jsx)("div",{className:t.reference,children:(0,I.jsx)(N,{ref:Ne,messages:Me})}),(0,I.jsxs)("div",{className:t.chat,children:[(0,I.jsx)(H.Z.List,{items:xt,className:t.messages}),(0,I.jsx)(R.Z,{items:he,onItemClick:ft}),(0,I.jsx)(A.Z,{value:b,header:mt,onSubmit:function(e){console.log("nextContent===>",e),e&&(Ke({id:le(o.current),role:"user",content:e,references:[],collected:!1}),w(""))},onChange:w,loading:Xe.isRequesting(),className:t.sender})]}),gt,bt,(0,I.jsx)(ue.Z,{visible:He,messageId:Je,conversationId:D,appInfo:be,onClose:function(){return Fe(!1)}})]})}},64599:function(e,t,n){var r=n(96263);e.exports=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=r(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,o=function(){};return{s:o,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,c=!0,i=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return c=e.done,e},e:function(e){i=!0,s=e},f:function(){try{c||null==n.return||n.return()}finally{if(i)throw s}}}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);