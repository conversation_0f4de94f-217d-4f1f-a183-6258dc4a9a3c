from datetime import datetime
from mongoengine import Document, ObjectIdField, StringField, DateTimeField, ReferenceField, BooleanField, IntField, ListField

class ReportTemplate(Document):
    """信贷报告模板基本信息"""
    meta = {'collection': 'report_templates'}
    
    title = StringField(required=True, description='模板标题')
    description = StringField(description='模板描述')
    template_type = StringField(description='模板类型，例如：对公贷款报告、个人贷款报告等')
    tags = ListField(StringField(), description='模板标签，便于分类和筛选')
    file_path = StringField(description='原始模板文件存储路径')
    file_type = StringField(description='模板文件类型，例如DOCX')
    content_template = StringField(description='内容模板')
    created_at = DateTimeField(default=datetime.now, description='创建时间')
    updated_at = DateTimeField(default=datetime.now, description='更新时间')
    is_active = BooleanField(default=True, description='是否启用')
    template_sections = ListField(ObjectIdField(), description='模板章节结构')
    
    def __str__(self):
        return f"{self.title}"


class TemplateSection(Document):
    """模板章节结构，包含提示词配置"""
    meta = {'collection': 'template_sections'}
    title = StringField(required=True, description='章节标题')
    section_type = StringField(description='章节类型，例如：概述、财务分析、风险评估等')
    tags = ListField(StringField(), description='章节标签，用于标识章节特性')
    prompt_text = StringField(description='章节内容生成提示词')
    data_requirements = StringField(description='章节所需数据要求描述')
    content_template = StringField(description='内容模板')
    created_at = DateTimeField(default=datetime.now, description='创建时间')
    updated_at = DateTimeField(default=datetime.now, description='更新时间')
    child_sections = ListField(ObjectIdField(), description='子章节')
    
    def __str__(self):
        return f"{self.title} (Level: {self.level})"


class ReportTask(Document):
    """报告任务"""
    meta = {'collection': 'report_tasks'}
    
    name = StringField(required=True, description='任务名称')
    template = ReferenceField(ReportTemplate, required=True, description='使用的模板')
    customer_id = StringField(required=True, description='调查对象ID（客户ID）')
    customer_name = StringField(description='调查对象名称（客户名称）')
    status = StringField(default='pending', description='任务状态：pending（待上传）、generating（生成中）、review（待审核）、completed（已完成）、failed（失败）')
    created_by = StringField(description='创建人ID')
    created_at = DateTimeField(default=datetime.now, description='创建时间')
    updated_at = DateTimeField(default=datetime.now, description='更新时间')
    completed_at = DateTimeField(description='完成时间')
    
    def __str__(self):
        return f"{self.name} ({self.status})"
