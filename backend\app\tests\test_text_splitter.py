import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import pytest
from app.engines.processing.text_splitter import TextSplitter

@pytest.fixture
def text_splitter():
    """创建文本分割器实例"""
    return TextSplitter(
        chunk_size=100,
        chunk_overlap=20,
        length_function=len
    )

@pytest.fixture
def long_text():
    """生成测试用的长文本"""
    return """
    这是第一段测试文本，用于测试文本分割功能。这段文本需要足够长，以便能够测试分割效果。
    这是第二段测试文本，包含了一些特殊符号：!@#$%^&*()。
    这是第三段测试文本，包含了一些数字：123456789。
    这是第四段测试文本，包含了一些英文：Hello World!
    这是第五段测试文本，用于测试文本重叠部分的处理。
    这是最后一段测试文本，确保分割器能够正确处理文本结尾。
    """.strip()

def test_text_splitter_initialization():
    """测试文本分割器的初始化"""
    splitter = TextSplitter(chunk_size=100, chunk_overlap=20)
    assert splitter.chunk_size == 100
    assert splitter.chunk_overlap == 20
    assert callable(splitter.length_function)

    # 测试参数验证
    with pytest.raises(ValueError):
        TextSplitter(chunk_size=-1)
    
    with pytest.raises(ValueError):
        TextSplitter(chunk_overlap=-1)
    
    with pytest.raises(ValueError):
        TextSplitter(chunk_size=50, chunk_overlap=60)  # overlap 不能大于 chunk_size

def test_split_text(text_splitter, long_text):
    """测试文本分割功能"""
    chunks = text_splitter.split_text(long_text)
    
    # 基本检查
    assert isinstance(chunks, list)
    assert len(chunks) > 0
    assert all(isinstance(chunk, str) for chunk in chunks)
    
    # 检查每个块的大小
    for chunk in chunks:
        assert len(chunk) <= text_splitter.chunk_size
    
    # 检查重叠部分
    if len(chunks) > 1:
        for i in range(len(chunks) - 1):
            current_chunk = chunks[i]
            next_chunk = chunks[i + 1]
            # 检查是否有重叠
            overlap_found = False
            for j in range(min(len(current_chunk), text_splitter.chunk_overlap)):
                if current_chunk[-j:] in next_chunk:
                    overlap_found = True
                    break
            assert overlap_found

def test_split_empty_text(text_splitter):
    """测试空文本的处理"""
    empty_text = ""
    chunks = text_splitter.split_text(empty_text)
    assert chunks == []

    whitespace_text = "   \n   \t   "
    chunks = text_splitter.split_text(whitespace_text)
    assert chunks == []

def test_split_short_text(text_splitter):
    """测试短文本的处理"""
    short_text = "这是一个短文本"
    chunks = text_splitter.split_text(short_text)
    assert len(chunks) == 1
    assert chunks[0] == short_text

def test_split_text_with_different_separators(text_splitter):
    """测试不同分隔符的处理"""
    # 测试换行符
    text_with_newlines = "第一行\n第二行\n第三行"
    chunks = text_splitter.split_text(text_with_newlines)
    assert len(chunks) > 0
    
    # 测试句号
    text_with_periods = "这是第一句。这是第二句。这是第三句。"
    chunks = text_splitter.split_text(text_with_periods)
    assert len(chunks) > 0
    
    # 测试混合分隔符
    mixed_text = "第一段。\n第二段！\n第三段？"
    chunks = text_splitter.split_text(mixed_text)
    assert len(chunks) > 0

def test_custom_length_function():
    """测试自定义长度函数"""
    def token_length(text: str) -> int:
        # 模拟分词长度计算
        return len(text.split())
    
    splitter = TextSplitter(
        chunk_size=5,  # 每块最多5个词
        chunk_overlap=2,  # 重叠2个词
        length_function=token_length
    )
    
    text = "这是 一个 测试 文本 用于 测试 分词 长度 计算"
    chunks = splitter.split_text(text)
    
    # 检查每个块的词数
    for chunk in chunks:
        assert len(chunk.split()) <= 5

def test_edge_cases(text_splitter):
    """测试边界情况"""
    # 测试单字符
    single_char = "字"
    chunks = text_splitter.split_text(single_char)
    assert len(chunks) == 1
    assert chunks[0] == single_char
    
    # 测试特殊字符
    special_chars = "!@#$%^&*()"
    chunks = text_splitter.split_text(special_chars)
    assert len(chunks) == 1
    assert chunks[0] == special_chars
    
    # 测试重复字符
    repeated_chars = "啊" * 200
    chunks = text_splitter.split_text(repeated_chars)
    assert len(chunks) > 1
    assert all(len(chunk) <= text_splitter.chunk_size for chunk in chunks)

if __name__ == "__main__":
    pytest.main(["-v", "test_text_splitter.py"]) 