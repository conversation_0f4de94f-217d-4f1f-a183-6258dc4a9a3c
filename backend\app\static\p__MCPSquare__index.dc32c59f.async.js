"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4216],{75750:function(e,o,r){r.d(o,{Z:function(){return s}});var t=r(1413),l=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},c=r(91146),n=function(e,o){return l.createElement(c.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:o,icon:a}))};var s=l.forwardRef(n)},99459:function(e,o,r){r.r(o),r.d(o,{default:function(){return F}});var t=r(5574),l=r.n(t),a=r(67294),c=r(71471),n=r(55102),s=r(71230),i=r(15746),d=r(4393),h=r(66309),p=r(40110),u=r(75750),g="container___yLDgR",v="header___jh1Es",m="logo___u855o",b="mainContent___euHkQ",C="sidebar___hX61U",f="sidebarHeader___FCn5V",x="categories___JN52A",_="categoryItem___zi2NU",y="active___F76dr",k="count___Qb3RW",j="content___uKUra",P="servicesList___gXJ6j",N="serviceCard___gmTCv",S="hostedServiceCard___P_K2o",w="localServiceCard___EvrtS",$="serviceCardHeader___TStc4",O="serviceInfo___rlYlK",T="serviceLogo___eYxCa",I="logoPlaceholder___zqakV",E="serviceTitle___trthV",M="serviceDescription___Qh8Oa",Z="serviceFooter___xrmTT",B="hostedTagColor___XsqO1",U="localTagColor___KkfpB",A="footer___jjQBt",L=r(78404),H=r(85893),z=c.Z.Title,X=c.Z.Text,F=function(){var e=(0,a.useState)("searchTool"),o=l()(e,2),r=o[0],t=o[1],c=(0,L.kH)();return(0,H.jsxs)("div",{className:g,children:[(0,H.jsx)("div",{className:v,children:(0,H.jsxs)("div",{className:m,children:[(0,H.jsx)("img",{src:null==c?void 0:c.logo,alt:"系统Logo"}),(0,H.jsxs)("div",{children:[(0,H.jsx)(z,{level:4,style:{margin:0},children:" MCP 广场"}),(0,H.jsx)(X,{type:"secondary",children:"聚合优质MCP资源，拓展模型智能边界"})]})]})}),(0,H.jsxs)("div",{className:b,children:[(0,H.jsxs)("div",{className:C,children:[(0,H.jsx)("div",{className:f,children:(0,H.jsx)(z,{level:5,children:"MCP 服务"})}),(0,H.jsx)(n.Z,{placeholder:"搜索MCP服务（共410个）",prefix:(0,H.jsx)(p.Z,{}),style:{marginBottom:16}}),(0,H.jsx)("div",{className:x,children:[{key:"browserAuto",name:"浏览器自动化",count:240},{key:"searchTool",name:"搜索工具",count:410},{key:"collab",name:"交流协作工具",count:174},{key:"devTool",name:"开发者工具",count:894},{key:"mediaInteract",name:"媒体与多媒体",count:46},{key:"fileSystem",name:"文件系统",count:150},{key:"finance",name:"金融",count:163},{key:"knowledgeBase",name:"知识管理与记忆",count:200},{key:"locationService",name:"位置服务",count:31}].map((function(e){return(0,H.jsxs)("div",{className:"".concat(_," ").concat(r===e.key?y:""),onClick:function(){return t(e.key)},children:[(0,H.jsx)("span",{style:{color:"searchTool"===e.key?"#4e54ff":"inherit"},children:e.name}),(0,H.jsx)("span",{className:k,children:e.count})]},e.key)}))})]}),(0,H.jsx)("div",{className:j,children:(0,H.jsx)("div",{className:P,children:(0,H.jsx)(s.Z,{gutter:[16,16],children:[{title:"必应搜索中文",icon:"🔍",logoUrl:"https://via.placeholder.com/40",author:"@yan5236",path:"/bing-cn-mcp-server",description:"必应搜索中文",hosted:!0,views:"5.9k"},{title:"搜狗工具",icon:"🔍",logoUrl:"https://via.placeholder.com/40",author:"@sogou",path:"/sogou-tool",description:"搜索工具",hosted:!0,views:"2.0k"},{title:"Tavily搜索",icon:"🔍",logoUrl:"https://via.placeholder.com/40",author:"@tavily-ai",path:"/tavily-mcp",description:"该服务器使用良好的搜索与Tavily的搜索和政策搜索工具集成，提供来自可靠的网络信息访问和摘要的引擎。",hosted:!0,views:"7.8k"},{title:"Elasticsearch",icon:"🔍",logoUrl:"https://via.placeholder.com/40",author:"@elastic",path:"/mcp-server-elasticsearch",description:"使用Cloudflare和MCP客户端连接到Elasticsearch数据。允许用户通过自然语言查询连接Elasticsearch索引进行交互。",hosted:!1,views:"2.3k"},{title:"Perplexity Ask",icon:"🔍",logoUrl:"https://via.placeholder.com/40",author:"@ppl-ai",path:"/modelcontextprotocol",description:"一种MCP服务完成。它集成了Sonar API，为完整搜索提供了无缝的比较实时、全网研究能力。",hosted:!0,views:"1.9k"},{title:"Exa搜索",icon:"🔍",logoUrl:"https://via.placeholder.com/40",author:"@exa-labs",path:"/exa-mcp-server",description:"集成上下文协议（MCP）服务器让您为多渠道传递的站点提供强大的搜索 Exa AI搜索API进行网络检索，这样保真使AI模型能够以安全和受控的方式搜索互联网。",hosted:!0,views:"2.3k"},{title:"Brave搜索",icon:"🔍",logoUrl:"https://via.placeholder.com/40",author:"@modelcontextprotocol",path:"/brave-search",description:"一个集成了Brave Search API的MCP服务商实现，提供网页和来源搜索功能。",hosted:!0,views:"1.7k"},{title:"GitHub",icon:"🔍",logoUrl:"https://via.placeholder.com/40",author:"@modelcontextprotocol",path:"/github",description:"用于GitHub API的MCP服务商，支持文件操作、仓库管理、搜索功能等多项功能。",hosted:!0,views:"8.3k"},{title:"MCP 中文趋势聚合",icon:"🔍",logoUrl:"https://via.placeholder.com/40",author:"@baranwang",path:"/mcp-trends-hub",description:"一个MCP服务商，聚合了多自新浪微博、知乎、B站以等平台各大网站和平台的热门趋势和排行榜。",hosted:!0,views:"4.0k"},{title:"ArXiv AI搜索服务",icon:"🔍",logoUrl:"https://via.placeholder.com/40",author:"@blackip",path:"/arxiv-mcp-server",description:"ArXiv MCP服务商通过提供一个sophisticated interface（sophisticated口译为UI）来查询arXiv写入的研究论文搜索，从而完全了解研究文献。",hosted:!0,views:"2.1k"},{title:"Google 搜索",icon:"🔍",logoUrl:"https://via.placeholder.com/40",author:"@adenot",path:"/mcp-google-search",description:"通过使用Google自定义搜索API提供简单搜索功能，使用户能够通过Model Context Protocol服务商进行搜索。",hosted:!0,views:"6.5k"},{title:"MCP股票分析",icon:"📈",logoUrl:"https://via.placeholder.com/40",author:"@gptliabs",path:"/mcp-stock-analysis",description:"通过Yahoo Finance API提供对实时和历史市场股票数据的访问，使本地LLM得以通过MCP获得财务数据（80Clouds Desktop和Cursor）",hosted:!1,views:"3.2k"}].map((function(e,o){return(0,H.jsx)(i.Z,{xs:24,sm:24,md:24,lg:12,xl:8,children:(0,H.jsxs)(d.Z,{hoverable:!0,className:"".concat(N," ").concat(e.hosted?S:w),bordered:!1,bodyStyle:{padding:0,height:"100%",display:"flex",flexDirection:"column"},children:[(0,H.jsx)("div",{className:$,children:(0,H.jsxs)("div",{className:O,children:[(0,H.jsx)("div",{className:T,children:e.logoUrl?(0,H.jsx)("img",{src:null==c?void 0:c.logo,alt:e.title}):(0,H.jsx)("div",{className:I,children:e.icon})}),(0,H.jsx)("div",{style:{flex:1,overflow:"hidden"},children:(0,H.jsxs)("div",{className:E,children:[(0,H.jsx)("span",{children:e.title}),(0,H.jsx)("br",{}),e.hosted?(0,H.jsx)(h.Z,{className:B,children:"Hosted"}):(0,H.jsx)(h.Z,{className:U,children:"Local"})]})})]})}),(0,H.jsx)("div",{className:M,children:e.description}),(0,H.jsx)("div",{className:Z,children:(0,H.jsxs)("div",{children:[(0,H.jsx)(u.Z,{})," ",e.views]})})]})},o)}))})})})]}),(0,H.jsx)("div",{className:A,children:(0,H.jsx)(X,{type:"secondary",children:"modelscope.cn/mcp/servers/@elastic/mcp-server-elasticsearch"})})]})}},15746:function(e,o,r){var t=r(21584);o.Z=t.Z},71230:function(e,o,r){var t=r(17621);o.Z=t.Z},66309:function(e,o,r){r.d(o,{Z:function(){return O}});var t=r(67294),l=r(93967),a=r.n(l),c=r(98423),n=r(98787),s=r(69760),i=r(96159),d=r(45353),h=r(53124),p=r(11568),u=r(15063),g=r(14747),v=r(83262),m=r(83559);const b=e=>{const{lineWidth:o,fontSizeIcon:r,calc:t}=e,l=e.fontSizeSM;return(0,v.IX)(e,{tagFontSize:l,tagLineHeight:(0,p.bf)(t(e.lineHeightSM).mul(l).equal()),tagIconSize:t(r).sub(t(o).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},C=e=>({defaultBg:new u.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var f=(0,m.I$)("Tag",(e=>(e=>{const{paddingXXS:o,lineWidth:r,tagPaddingHorizontal:t,componentCls:l,calc:a}=e,c=a(t).sub(r).equal(),n=a(o).sub(r).equal();return{[l]:Object.assign(Object.assign({},(0,g.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${l}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${l}-close-icon`]:{marginInlineStart:n,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${l}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${l}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:c}}),[`${l}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(b(e))),C),x=function(e,o){var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(r[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var l=0;for(t=Object.getOwnPropertySymbols(e);l<t.length;l++)o.indexOf(t[l])<0&&Object.prototype.propertyIsEnumerable.call(e,t[l])&&(r[t[l]]=e[t[l]])}return r};const _=t.forwardRef(((e,o)=>{const{prefixCls:r,style:l,className:c,checked:n,onChange:s,onClick:i}=e,d=x(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:u}=t.useContext(h.E_),g=p("tag",r),[v,m,b]=f(g),C=a()(g,`${g}-checkable`,{[`${g}-checkable-checked`]:n},null==u?void 0:u.className,c,m,b);return v(t.createElement("span",Object.assign({},d,{ref:o,style:Object.assign(Object.assign({},l),null==u?void 0:u.style),className:C,onClick:e=>{null==s||s(!n),null==i||i(e)}})))}));var y=_,k=r(98719);var j=(0,m.bk)(["Tag","preset"],(e=>(e=>(0,k.Z)(e,((o,r)=>{let{textColor:t,lightBorderColor:l,lightColor:a,darkColor:c}=r;return{[`${e.componentCls}${e.componentCls}-${o}`]:{color:t,background:a,borderColor:l,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(b(e))),C);const P=(e,o,r)=>{const t="string"!=typeof(l=r)?l:l.charAt(0).toUpperCase()+l.slice(1);var l;return{[`${e.componentCls}${e.componentCls}-${o}`]:{color:e[`color${r}`],background:e[`color${t}Bg`],borderColor:e[`color${t}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var N=(0,m.bk)(["Tag","status"],(e=>{const o=b(e);return[P(o,"success","Success"),P(o,"processing","Info"),P(o,"error","Error"),P(o,"warning","Warning")]}),C),S=function(e,o){var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(r[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var l=0;for(t=Object.getOwnPropertySymbols(e);l<t.length;l++)o.indexOf(t[l])<0&&Object.prototype.propertyIsEnumerable.call(e,t[l])&&(r[t[l]]=e[t[l]])}return r};const w=t.forwardRef(((e,o)=>{const{prefixCls:r,className:l,rootClassName:p,style:u,children:g,icon:v,color:m,onClose:b,bordered:C=!0,visible:x}=e,_=S(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:y,direction:k,tag:P}=t.useContext(h.E_),[w,$]=t.useState(!0),O=(0,c.Z)(_,["closeIcon","closable"]);t.useEffect((()=>{void 0!==x&&$(x)}),[x]);const T=(0,n.o2)(m),I=(0,n.yT)(m),E=T||I,M=Object.assign(Object.assign({backgroundColor:m&&!E?m:void 0},null==P?void 0:P.style),u),Z=y("tag",r),[B,U,A]=f(Z),L=a()(Z,null==P?void 0:P.className,{[`${Z}-${m}`]:E,[`${Z}-has-color`]:m&&!E,[`${Z}-hidden`]:!w,[`${Z}-rtl`]:"rtl"===k,[`${Z}-borderless`]:!C},l,p,U,A),H=e=>{e.stopPropagation(),null==b||b(e),e.defaultPrevented||$(!1)},[,z]=(0,s.Z)((0,s.w)(e),(0,s.w)(P),{closable:!1,closeIconRender:e=>{const o=t.createElement("span",{className:`${Z}-close-icon`,onClick:H},e);return(0,i.wm)(e,o,(e=>({onClick:o=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,o),H(o)},className:a()(null==e?void 0:e.className,`${Z}-close-icon`)})))}}),X="function"==typeof _.onClick||g&&"a"===g.type,F=v||null,R=F?t.createElement(t.Fragment,null,F,g&&t.createElement("span",null,g)):g,D=t.createElement("span",Object.assign({},O,{ref:o,className:L,style:M}),R,z,T&&t.createElement(j,{key:"preset",prefixCls:Z}),I&&t.createElement(N,{key:"status",prefixCls:Z}));return B(X?t.createElement(d.Z,{component:"Tag"},D):D)})),$=w;$.CheckableTag=y;var O=$}}]);