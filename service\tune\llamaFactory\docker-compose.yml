services:
  llamafactory:
    image: llamafactory:latest
    build:
      dockerfile: Dockerfile
      context: .
    container_name: llamafactory
    depends_on:
      - mongo  # 确保 MongoDB 先启动
    volumes:
      - /data/tune:/app/data/tune
    ports:
      - "7860:7860"
      # - "8000:8000"
    networks:
      - llamafactory_network
    ipc: host
    tty: true
    stdin_open: true
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #       - driver: nvidia
    #         count: "all"
    #         capabilities: [gpu]
    restart: unless-stopped
  mongo:
    image: mongo:latest
    volumes:
      - /data/tune/mongo:/data/db  # 将 MongoDB 数据存储到宿主机的 /data/tune/mongo 目录
    ports:
      - "27016:27017"  # 暴露 MongoDB 端口
    networks:
      - llamafactory_network
    environment:
      - MONGO_INITDB_ROOT_USERNAME=root
      - MONGO_INITDB_ROOT_PASSWORD=example

networks:
  llamafactory_network: