"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[331],{5966:function(e,t,n){var a=n(97685),r=n(1413),i=n(91),s=n(21770),o=n(8232),c=n(55241),l=n(98423),d=n(67294),u=n(62633),p=n(85893),f=["fieldProps","proFieldProps"],h=["fieldProps","proFieldProps"],x="text",v=function(e){var t=(0,s.Z)(e.open||!1,{value:e.open,onChange:e.onOpenChange}),n=(0,a.Z)(t,2),i=n[0],l=n[1];return(0,p.jsx)(o.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(t){var n,a=t.getFieldValue(e.name||[]);return(0,p.jsx)(c.Z,(0,r.Z)((0,r.Z)({getPopupContainer:function(e){return e&&e.parentNode?e.parentNode:e},onOpenChange:function(e){return l(e)},content:(0,p.jsxs)("div",{style:{padding:"4px 0"},children:[null===(n=e.statusRender)||void 0===n?void 0:n.call(e,a),e.strengthText?(0,p.jsx)("div",{style:{marginTop:10},children:(0,p.jsx)("span",{children:e.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},e.popoverProps),{},{open:i,children:e.children}))}})},m=function(e){var t=e.fieldProps,n=e.proFieldProps,a=(0,i.Z)(e,f);return(0,p.jsx)(u.Z,(0,r.Z)({valueType:x,fieldProps:t,filedConfig:{valueType:x},proFieldProps:n},a))};m.Password=function(e){var t=e.fieldProps,n=e.proFieldProps,s=(0,i.Z)(e,h),o=(0,d.useState)(!1),c=(0,a.Z)(o,2),f=c[0],m=c[1];return null!=t&&t.statusRender&&s.name?(0,p.jsx)(v,{name:s.name,statusRender:null==t?void 0:t.statusRender,popoverProps:null==t?void 0:t.popoverProps,strengthText:null==t?void 0:t.strengthText,open:f,onOpenChange:m,children:(0,p.jsx)("div",{children:(0,p.jsx)(u.Z,(0,r.Z)({valueType:"password",fieldProps:(0,r.Z)((0,r.Z)({},(0,l.Z)(t,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(e){var n;null==t||null===(n=t.onBlur)||void 0===n||n.call(t,e),m(!1)},onClick:function(e){var n;null==t||null===(n=t.onClick)||void 0===n||n.call(t,e),m(!0)}}),proFieldProps:n,filedConfig:{valueType:x}},s))})}):(0,p.jsx)(u.Z,(0,r.Z)({valueType:"password",fieldProps:t,proFieldProps:n,filedConfig:{valueType:x}},s))},m.displayName="ProFormComponent",t.Z=m},45492:function(e,t,n){n.r(t),n.d(t,{default:function(){return G}});var a=n(97857),r=n.n(a),i=n(5574),s=n.n(i),o=n(97131),c=n(88372),l=n(50136),d=n(67294),u=n(15009),p=n.n(u),f=n(99289),h=n.n(f),x=n(34994),v=n(5966),m=n(83622),g=n(17788),w=n(2453),j=n(10981),b=n(9783),Z=n.n(b),y=n(24444),P=(0,y.kc)((function(e){var t=e.token;return{baseView:Z()({display:"flex",paddingTop:"12px",".ant-legacy-form-item .ant-legacy-form-item-control-wrapper":{width:"100%"}},"@media screen and (max-width: ".concat(t.screenXL,"px)"),{flexDirection:"column-reverse"}),left:{minWidth:"224px",maxWidth:"448px"},right:Z()({flex:"1",paddingLeft:"104px"},"@media screen and (max-width: ".concat(t.screenXL,"px)"),{display:"flex",flexDirection:"column",alignItems:"center",maxWidth:"448px",padding:"20px"}),avatar_title:Z()({height:"22px",marginBottom:"8px",color:t.colorTextHeading,fontSize:t.fontSize,lineHeight:"22px"},"@media screen and (max-width: ".concat(t.screenXL,"px)"),{display:"none"}),avatar:{width:"144px",height:"144px",marginBottom:"12px",overflow:"hidden",img:{width:"100%"}},button_view:{width:"144px",textAlign:"center"},area_code:{width:"72px"},phone_number:{width:"214px"}}})),k=n(78158);function C(e,t){return S.apply(this,arguments)}function S(){return(S=h()(p()().mark((function e(t,n){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,k.N)("/api/MyUserUpdate",{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function T(e,t){return F.apply(this,arguments)}function F(){return(F=h()(p()().mark((function e(t,n){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,k.N)("/api/changePassword",{method:"PUT",data:{old_password:t,new_password:n}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var N=n(85893),z=function(){var e=P().styles,t=(0,d.useState)(!1),n=s()(t,2),a=n[0],i=n[1],o=(0,d.useState)(""),c=s()(o,2),l=c[0],u=c[1],f=["/static/avatar/default.jpeg","/static/avatar/01.png","/static/avatar/02.png","/static/avatar/03.png","/static/avatar/04.png","/static/avatar/05.png","/static/avatar/06.png","/static/avatar/07.png","/static/avatar/08.png","/static/avatar/09.png","/static/avatar/10.png","/static/avatar/11.png","/static/avatar/12.png","/static/avatar/13.png","/static/avatar/14.png","/static/avatar/15.png","/static/avatar/16.png","/static/avatar/17.png","/static/avatar/18.png","/static/avatar/19.png","/static/avatar/20.png","/static/avatar/21.png","/static/avatar/22.png","/static/avatar/23.png","/static/avatar/24.png","/static/avatar/25.png","/static/avatar/26.png"],b=function(t){var n=t.avatar;return(0,N.jsxs)(N.Fragment,{children:[(0,N.jsx)("div",{className:e.avatar_title,children:"头像"}),(0,N.jsx)("div",{className:e.avatar,children:(0,N.jsx)("img",{src:l||n,alt:"avatar"})}),(0,N.jsx)("div",{className:e.button_view,children:(0,N.jsx)(m.ZP,{onClick:function(){return i(!0)},children:"更换头像"})}),(0,N.jsx)(g.Z,{title:"选择头像",open:a,onCancel:function(){return i(!1)},footer:null,children:(0,N.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:"10px"},children:f.map((function(e,t){return(0,N.jsx)("div",{onClick:function(){u(e),i(!1)},style:{cursor:"pointer"},children:(0,N.jsx)("img",{src:e,alt:"avatar-".concat(t),style:{width:"80px",height:"80px",border:l===e?"2px solid #1890ff":"none"}})},t)}))})})]})},Z=(0,j.bG)(),y=function(){var e=h()(p()().mark((function e(t){var n,a,r;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,null!=(n=(0,j.bG)())&&n.id){e.next=5;break}return w.ZP.error("获取用户信息失败"),e.abrupt("return");case 5:return a={name:t.name,avatar:l||n.avatar},e.next=8,C(n.id,a);case 8:(r=e.sent).success?(w.ZP.success("更新基本信息成功"),(0,j.ps)(r.user)):w.ZP.error(r.message||"更新失败"),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(0),console.error("更新用户信息失败:",e.t0),w.ZP.error("更新基本信息失败，请重试");case 16:case"end":return e.stop()}}),e,null,[[0,12]])})));return function(t){return e.apply(this,arguments)}}();return(0,N.jsx)("div",{className:e.baseView,children:(0,N.jsxs)(N.Fragment,{children:[(0,N.jsx)("div",{className:e.left,children:(0,N.jsxs)(x.A,{layout:"vertical",onFinish:y,submitter:{searchConfig:{submitText:"更新基本信息"},render:function(e,t){return t[1]}},initialValues:r()(r()({},Z),{},{phone:null==Z?void 0:Z.phone.split("-")}),hideRequiredMark:!0,children:[(0,N.jsx)(v.Z,{width:"md",name:"phone",label:"手机号",readonly:!0,disabled:!0,rules:[{required:!0,message:"请输入您的手机号!"}]}),(0,N.jsx)(v.Z,{width:"md",name:"name",label:"昵称",rules:[{required:!0,message:"请输入您的昵称!"}]})]})}),(0,N.jsx)("div",{className:e.right,children:(0,N.jsx)(b,{avatar:function(){if(Z){if(Z.avatar)return Z.avatar;return"/static/avatar/default.jpeg"}return""}()})})]})})},R=n(1413),B={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168.5 273.7a68.7 68.7 0 10137.4 0 68.7 68.7 0 10-137.4 0zm730 79.2s-23.7-184.4-426.9-70.1c17.3-30 25.6-49.5 25.6-49.5L396.4 205s-40.6 132.6-113 194.4c0 0 70.1 40.6 69.4 39.4 20.1-20.1 38.2-40.6 53.7-60.4 16.1-7 31.5-13.6 46.7-19.8-18.6 33.5-48.7 83.8-78.8 115.6l42.4 37s28.8-27.7 60.4-61.2h36v61.8H372.9v49.5h140.3v118.5c-1.7 0-3.6 0-5.4-.2-15.4-.7-39.5-3.3-49-18.2-11.5-18.1-3-51.5-2.4-71.9h-97l-3.4 1.8s-35.5 159.1 102.3 155.5c129.1 3.6 203-36 238.6-63.1l14.2 52.6 79.6-33.2-53.9-131.9-64.6 20.1 12.1 45.2c-16.6 12.4-35.6 21.7-56.2 28.4V561.3h137.1v-49.5H628.1V450h137.6v-49.5H521.3c17.6-21.4 31.5-41.1 35-53.6l-42.5-11.6c182.8-65.5 284.5-54.2 283.6 53.2v282.8s10.8 97.1-100.4 90.1l-60.2-12.9-14.2 57.1S882.5 880 903.7 680.2c21.3-200-5.2-327.3-5.2-327.3zm-707.4 18.3l-45.4 69.7 83.6 52.1s56 28.5 29.4 81.9C233.8 625.5 112 736.3 112 736.3l109 68.1c75.4-163.7 70.5-142 89.5-200.7 19.5-60.1 23.7-105.9-9.4-139.1-42.4-42.6-47-46.6-110-93.4z"}}]},name:"taobao",theme:"outlined"},L=n(91146),M=function(e,t){return d.createElement(L.Z,(0,R.Z)((0,R.Z)({},e),{},{ref:t,icon:B}))};var H=d.forwardRef(M),_={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M557.2 129a6.68 6.68 0 016.72 6.65V250.2h243.8a6.74 6.74 0 016.65 6.84c.02 23.92-6.05 54.69-19.85 54.69H563.94v81.1h166.18c7.69 0 13.8 6.51 13.25 14.18l-.11 1.51c-.7 90.2-30.63 180.64-79.52 259.65l8.71 3.82c77.3 33.48 162.15 60.85 267.15 64.14a21.08 21.08 0 0120.38 22.07l-.2 3.95c-3.34 59.57-20 132.85-79.85 132.85-8.8 0-17.29-.55-25.48-1.61-78.04-9.25-156.28-57.05-236.32-110.27l-17.33-11.57-13.15-8.83a480.83 480.83 0 01-69.99 57.25 192.8 192.8 0 01-19.57 11.08c-65.51 39.18-142.21 62.6-227.42 62.62-118.2 0-204.92-77.97-206.64-175.9l-.03-2.95.03-1.7c1.66-98.12 84.77-175.18 203-176.72l3.64-.03c102.92 0 186.66 33.54 270.48 73.14l.44.38 1.7-3.47c21.27-44.14 36.44-94.95 42.74-152.06h-324.8a6.64 6.64 0 01-6.63-6.62c-.04-21.86 6-54.91 19.85-54.91h162.1v-81.1H191.92a6.71 6.71 0 01-6.64-6.85c-.01-22.61 6.06-54.68 19.86-54.68h231.4v-64.85l.02-1.99c.9-30.93 23.72-54.36 120.64-54.36M256.9 619c-74.77 0-136.53 39.93-137.88 95.6l-.02 1.26.08 3.24a92.55 92.55 0 001.58 13.64c20.26 96.5 220.16 129.71 364.34-36.59l-8.03-4.72C405.95 650.11 332.94 619 256.9 619"}}]},name:"alipay",theme:"outlined"},I=function(e,t){return d.createElement(L.Z,(0,R.Z)((0,R.Z)({},e),{},{ref:t,icon:_}))};var W=d.forwardRef(I),V={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M573.7 252.5C422.5 197.4 201.3 96.7 201.3 96.7c-15.7-4.1-17.9 11.1-17.9 11.1-5 61.1 33.6 160.5 53.6 182.8 19.9 22.3 319.1 113.7 319.1 113.7S326 357.9 270.5 341.9c-55.6-16-37.9 17.8-37.9 17.8 11.4 61.7 64.9 131.8 107.2 138.4 42.2 6.6 220.1 4 220.1 4s-35.5 4.1-93.2 11.9c-42.7 5.8-97 12.5-111.1 17.8-33.1 12.5 24 62.6 24 62.6 84.7 76.8 129.7 50.5 129.7 50.5 33.3-10.7 61.4-18.5 85.2-24.2L565 743.1h84.6L603 928l205.3-271.9H700.8l22.3-38.7c.*******.4.8S799.8 496.1 829 433.8l.6-1h-.1c5-10.8 8.6-19.7 10-25.8 17-71.3-114.5-99.4-265.8-154.5z"}}]},name:"dingding",theme:"outlined"},E=function(e,t){return d.createElement(L.Z,(0,R.Z)((0,R.Z)({},e),{},{ref:t,icon:V}))};var q=d.forwardRef(E),D=n(2487),K=function(){return(0,N.jsx)(d.Fragment,{children:(0,N.jsx)(D.Z,{itemLayout:"horizontal",dataSource:[{title:"绑定淘宝",description:"当前未绑定淘宝账号",actions:[(0,N.jsx)("a",{children:"绑定"},"Bind")],avatar:(0,N.jsx)(H,{className:"taobao"})},{title:"绑定支付宝",description:"当前未绑定支付宝账号",actions:[(0,N.jsx)("a",{children:"绑定"},"Bind")],avatar:(0,N.jsx)(W,{className:"alipay"})},{title:"绑定钉钉",description:"当前未绑定钉钉账号",actions:[(0,N.jsx)("a",{children:"绑定"},"Bind")],avatar:(0,N.jsx)(q,{className:"dingding"})}],renderItem:function(e){return(0,N.jsx)(D.Z.Item,{actions:e.actions,children:(0,N.jsx)(D.Z.Item.Meta,{avatar:e.avatar,title:e.title,description:e.description})})}})})},O=n(8232),U=n(55102),A=function(){var e=(0,d.useState)(!1),t=s()(e,2),n=t[0],a=t[1],r=(0,d.useState)(!1),i=s()(r,2),o=i[0],c=i[1],l=O.Z.useForm(),u=s()(l,1)[0],f=function(){var e=h()(p()().mark((function e(t){return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,c(!0),e.next=4,T(t.oldPassword,t.newPassword);case 4:w.ZP.success("密码修改成功"),a(!1),u.resetFields(),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(0),w.ZP.error("密码修改失败");case 12:return e.prev=12,c(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,9,12,15]])})));return function(t){return e.apply(this,arguments)}}(),x=[{title:"账户密码",description:(0,N.jsx)(N.Fragment,{}),actions:[(0,N.jsx)("a",{onClick:function(){return a(!0)},children:"修改"},"Modify")]}];return(0,N.jsxs)(N.Fragment,{children:[(0,N.jsx)(D.Z,{itemLayout:"horizontal",dataSource:x,renderItem:function(e){return(0,N.jsx)(D.Z.Item,{actions:e.actions,children:(0,N.jsx)(D.Z.Item.Meta,{title:e.title,description:e.description})})}}),(0,N.jsx)(g.Z,{title:"修改密码",open:n,onCancel:function(){return a(!1)},onOk:function(){return u.submit()},confirmLoading:o,children:(0,N.jsxs)(O.Z,{form:u,onFinish:f,layout:"vertical",children:[(0,N.jsx)(O.Z.Item,{label:"原密码",name:"oldPassword",rules:[{required:!0,message:"请输入原密码"}],children:(0,N.jsx)(U.Z.Password,{})}),(0,N.jsx)(O.Z.Item,{label:"新密码",name:"newPassword",rules:[{required:!0,message:"请输入新密码"}],children:(0,N.jsx)(U.Z.Password,{})}),(0,N.jsx)(O.Z.Item,{label:"确认新密码",name:"confirmPassword",dependencies:["newPassword"],rules:[{required:!0,message:"请确认新密码"},function(e){var t=e.getFieldValue;return{validator:function(e,n){return n&&t("newPassword")!==n?Promise.reject(new Error("两次输入的密码不一致")):Promise.resolve()}}}],children:(0,N.jsx)(U.Z.Password,{})})]})})]})},X=(0,y.kc)((function(e){var t=e.token;return{main:Z()({display:"flex",width:"100%",height:"100%",paddingTop:"16px",paddingBottom:"16px",paddingLeft:"16px",paddingRight:"16px",backgroundColor:t.colorBgContainer,".ant-list-split .ant-list-item:last-child":{borderBottom:"1px solid ".concat(t.colorSplit)},".ant-list-item":{paddingTop:"14px",paddingBottom:"14px"}},"@media screen and (max-width: ".concat(t.screenMD,"px)"),{flexDirection:"column"}),leftMenu:Z()({width:"224px",borderRight:"".concat(t.lineWidth,"px solid ").concat(t.colorSplit),".ant-menu-inline":{border:"none"},".ant-menu-horizontal":{fontWeight:"bold"}},"@media screen and (max-width: ".concat(t.screenMD,"px)"),{width:"100%",border:"none"}),right:Z()({flex:"1",padding:"8px 40px"},"@media screen and (max-width: ".concat(t.screenMD,"px)"),{padding:"40px"}),title:{marginBottom:"12px",color:t.colorTextHeading,fontWeight:"500",fontSize:"20px",lineHeight:"28px"},taobao:{display:"block",color:"#ff4000",fontSize:"48px",lineHeight:"48px",borderRadius:t.borderRadius},dingding:{margin:"2px",padding:"6px",color:"#fff",fontSize:"32px",lineHeight:"32px",backgroundColor:"#2eabff",borderRadius:t.borderRadius},alipay:{color:"#2eabff",fontSize:"48px",lineHeight:"48px",borderRadius:t.borderRadius},":global":{"font.strong":{color:t.colorSuccess},"font.medium":{color:t.colorWarning},"font.weak":{color:t.colorError}}}})),G=function(){var e=X().styles,t={base:"基本设置",security:"安全设置"},n=(0,d.useState)({mode:"inline",selectKey:"base"}),a=s()(n,2),i=a[0],u=a[1],p=(0,d.useRef)(),f=function(){requestAnimationFrame((function(){if(p.current){var e="inline",t=p.current.offsetWidth;p.current.offsetWidth<641&&t>400&&(e="horizontal"),window.innerWidth<768&&t>400&&(e="horizontal"),u(r()(r()({},i),{},{mode:e}))}}))};(0,d.useLayoutEffect)((function(){return p.current&&(window.addEventListener("resize",f),f()),function(){window.removeEventListener("resize",f)}}),[p.current]);return(0,N.jsx)(o._z,{children:(0,N.jsx)(c.f,{children:(0,N.jsxs)("div",{className:e.main,ref:function(e){e&&(p.current=e)},children:[(0,N.jsx)("div",{className:e.leftMenu,children:(0,N.jsx)(l.Z,{mode:i.mode,selectedKeys:[i.selectKey],onClick:function(e){var t=e.key;u(r()(r()({},i),{},{selectKey:t}))},items:Object.keys(t).map((function(e){return{key:e,label:t[e]}}))})}),(0,N.jsxs)("div",{className:e.right,children:[(0,N.jsx)("div",{className:e.title,children:t[i.selectKey]}),function(){switch(i.selectKey){case"base":return(0,N.jsx)(z,{});case"security":return(0,N.jsx)(A,{});case"binding":return(0,N.jsx)(K,{});case"notification":return(0,N.jsx)(NotificationView,{});default:return null}}()]})]})})})}}}]);