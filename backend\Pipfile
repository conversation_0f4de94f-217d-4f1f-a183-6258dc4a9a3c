[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
absl-py = "==1.0.0"
accelerate = "==0.18.0"
aiohttp = "==3.9.1"
aiosignal = "==1.3.1"
alembic = "==1.13.1"
aliyun-python-sdk-core = "==2.13.36"
aliyun-python-sdk-kms = "==2.15.0"
anyio = "==3.6.2"
appdirs = "==1.4.4"
appnope = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/appnope_1649077682618/work"}
argon2-cffi = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/argon2-cffi_1640817743617/work"}
argon2-cffi-bindings = {file = "../../../../../runner/miniforge3/conda-bld/argon2-cffi-bindings_1649500366499/work"}
arxiv = "==2.1.0"
asttokens = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/asttokens_1618968359944/work"}
astunparse = "==1.6.3"
async-generator = "==1.10"
async-timeout = "==4.0.2"
attrs = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/attrs_1659291887007/work"}
autogenstudio = {editable = true, ref = "744810f8f4435f36fd70245f7cb02a01a5b43a30", git = "https://github.com/microsoft/autogen.git", subdirectory = "samples/apps/autogen-studio"}
backcall = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/backcall_1592338393461/work"}
"backports.functools-lru-cache" = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/backports.functools_lru_cache_1618230623929/work"}
baidu-aip = "==4.16.6"
beautifulsoup4 = "==4.12.2"
black = "==23.3.0"
bleach = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/bleach_1656355450470/work"}
brotlipy = {file = "../../../../../runner/miniforge3/conda-bld/brotlipy_1648854242877/work"}
cached-property = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/cached_property_1615209429212/work"}
cachetools = "==5.1.0"
certifi = "==2022.6.15"
cffi = {file = "../../../../../runner/miniforge3/conda-bld/cffi_1656782892388/work"}
cfgv = "==3.3.1"
chardet = "==3.0.4"
charset-normalizer = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/charset-normalizer_1655906222726/work"}
click = "==8.1.3"
colorama = "==0.4.6"
conda = "==4.13.0"
conda-package-handling = {file = "../../../../../runner/miniforge3/conda-bld/conda-package-handling_1649385125392/work"}
coverage = "==7.2.3"
crcmod = "==1.7"
cryptography = {file = "../../../../../runner/miniforge3/conda-bld/cryptography_1657174295336/work"}
cssselect = "==1.2.0"
cycler = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/cycler_1635519461629/work"}
dataclasses-json = "==0.6.3"
datasets = "==2.16.1"
debugpy = {file = "../../../../../runner/miniforge3/conda-bld/debugpy_1649586402495/work"}
decorator = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/decorator_1641555617451/work"}
deepspeed = "==0.8.3"
defusedxml = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/defusedxml_1615232257335/work"}
deprecated = "==1.2.14"
diffusers = "==0.24.0"
dill = "==0.3.6"
diskcache = "==5.6.3"
distlib = "==0.3.6"
distro = "==1.9.0"
dnspython = "==2.3.0"
docker = "==6.0.1"
docker-pycreds = "==0.4.0"
docopt = "==0.6.2"
docstring-parser = "==0.16"
duckduckgo-search = "==2.8.6"
eas-prediction = "==0.13"
elastic-transport = "==8.1.2"
elasticsearch = "==7.10.0"
entrypoints = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/entrypoints_1643888246732/work"}
et-xmlfile = "==1.1.0"
evaluate = "==0.4.0"
exceptiongroup = "==1.1.1"
executing = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/executing_1658852325129/work"}
fastapi = "==0.95.0"
fastjsonschema = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/python-fastjsonschema_1658064924516/work/dist"}
feedparser = "==6.0.10"
filelock = "==3.10.7"
flake8 = "==6.0.0"
flaml = "==2.1.2"
flask = "==1.0.2"
flask-dropzone = "==1.5.3"
flatbuffers = "==1.12"
flit-core = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/flit-core_1645629044586/work/source/flit_core"}
fonttools = {file = "../../../../../runner/miniforge3/conda-bld/fonttools_1657249430590/work"}
frozenlist = "==1.3.3"
fsspec = "==2023.10.0"
gast = "==0.4.0"
gevent = "==21.12.0"
gitdb = "==4.0.10"
gitpython = "==3.1.31"
google-ai-generativelanguage = "==0.6.5"
google-api-core = "==2.11.0"
google-api-python-client = "==2.85.0"
google-auth = "==2.30.0"
google-auth-httplib2 = "==0.1.0"
google-auth-oauthlib = "==0.4.6"
google-cloud-aiplatform = "==1.56.0"
google-cloud-bigquery = "==3.24.0"
google-cloud-core = "==2.4.1"
google-cloud-resource-manager = "==1.12.3"
google-cloud-storage = "==2.14.0"
google-crc32c = "==1.5.0"
google-generativeai = "==0.7.0"
google-pasta = "==0.2.0"
google-resumable-media = "==2.7.1"
googleapis-common-protos = "==1.59.0"
greenlet = "==1.1.2"
grpc-google-iam-v1 = "==0.13.0"
grpcio = "==1.64.1"
grpcio-status = "==1.62.2"
gtts = "==2.3.1"
h11 = "==0.14.0"
h5py = {file = "../../../../../runner/miniforge3/conda-bld/h5py_1637964045648/work"}
hdfs = "==2.5.2"
hf-doc-builder = {ref = "5dab464779c07d87df206fecd77afc71c7e81113", git = "https://github.com/huggingface/doc-builder"}
hjson = "==3.1.0"
httpcore = "==1.0.2"
httplib2 = "==0.22.0"
httpx = "==0.26.0"
huggingface-hub = "==0.23.0"
identify = "==2.5.22"
idna = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/idna_1642433548627/work"}
importlib-metadata = {file = "../../../../../runner/miniforge3/conda-bld/importlib-metadata_1653252883118/work"}
importlib-resources = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/importlib_resources_1658604161399/work"}
ipykernel = {file = "../../../../../runner/miniforge3/conda-bld/ipykernel_1657295113967/work"}
ipython = {file = "../../../../../runner/miniforge3/conda-bld/ipython_1653754989223/work"}
ipython-genutils = "==0.2.0"
ipywidgets = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/ipywidgets_1655973868664/work"}
isort = "==5.12.0"
itsdangerous = "==0.24"
jedi = {file = "../../../../../runner/miniforge3/conda-bld/jedi_1649067387646/work"}
jieba = "==0.39"
jinja2 = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/jinja2_1654302431367/work"}
jmespath = "==0.10.0"
joblib = "==1.3.2"
jsonlines = "==3.1.0"
jsonschema = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/jsonschema-meta_1659525086692/work"}
jupyter = {file = "../../../../../runner/miniforge3/conda-bld/jupyter_1637233473259/work"}
jupyter-client = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/jupyter_client_1654730843242/work"}
jupyter-console = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/jupyter_console_1655961255101/work"}
jupyter-core = {file = "../../../../../runner/miniforge3/conda-bld/jupyter_core_1658332720484/work"}
jupyterlab-widgets = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/jupyterlab_widgets_1655961217661/work"}
kafka = "==1.3.5"
kafka-python = "==1.4.6"
keras = "==2.9.0"
keras-preprocessing = "==1.1.2"
kiwisolver = {file = "../../../../../runner/miniforge3/conda-bld/kiwisolver_1657953165042/work"}
libclang = "==14.0.1"
llama-index = "==0.9.21"
loguru = "==0.7.0"
lxml = "==4.9.2"
mako = "==1.3.5"
markdown = "==3.3.7"
markdown-it-py = "==3.0.0"
markupsafe = {file = "../../../../../runner/miniforge3/conda-bld/markupsafe_1648737598016/work"}
marshmallow = "==3.20.1"
matplotlib = {file = "../../../../../runner/miniforge3/conda-bld/matplotlib-suite_1659031634280/work"}
matplotlib-inline = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/matplotlib-inline_1631080358261/work"}
mccabe = "==0.7.0"
mdurl = "==0.1.2"
minio = "==7.1.3"
mistune = {file = "../../../../../runner/miniforge3/conda-bld/mistune_1635844817800/work"}
mpmath = "==1.3.0"
multidict = "==6.0.4"
multiprocess = "==0.70.14"
munkres = "==1.1.4"
mypy-extensions = "==1.0.0"
nbconvert = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/nbconvert_1631125953237/work"}
nbformat = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/nbformat_1651607001005/work"}
nest-asyncio = "==1.5.8"
networkx = "==3.0"
ninja = "==1.11.1"
nltk = "==3.8.1"
nodeenv = "==1.7.0"
nodelist-inflator = "==0.2.0"
notebook = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/notebook_1650363291341/work"}
numpy = {file = "../../../../../runner/miniforge3/conda-bld/numpy_1653325964689/work"}
oauthlib = "==3.2.0"
openai = "==1.6.1"
openpyxl = "==3.0.10"
opt-einsum = "==3.3.0"
orjson = "==3.8.10"
oss2 = "==2.15.0"
outcome = "==1.2.0"
packaging = "==23.1"
pandas = "==1.4.2"
pandocfilters = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/pandocfilters_1631603243851/work"}
parso = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/parso_1638334955874/work"}
pathspec = "==0.11.1"
pathtools = "==0.1.2"
peft = "==0.2.0"
pexpect = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/pexpect_1602535608087/work"}
pickleshare = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/pickleshare_1602536217715/work"}
pillow = {file = "../../../../../runner/miniforge3/conda-bld/pillow_1657007219741/work"}
pinecone-client = "==2.2.1"
pkgutil-resolve-name = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/pkgutil-resolve-name_1633981968097/work"}
platformdirs = "==3.2.0"
playsound = "==1.2.2"
pre-commit = "==3.2.2"
prometheus-client = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/prometheus_client_1649447152425/work"}
prompt-toolkit = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/prompt-toolkit_1656332401605/work"}
proto-plus = "==1.23.0"
protobuf = "==4.22.3"
psutil = {file = "../../../../../runner/miniforge3/conda-bld/psutil_1653089402647/work"}
psycopg = "==3.1.19"
ptyprocess = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/ptyprocess_1609419310487/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl"}
pure-eval = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/pure_eval_1642875951954/work"}
py-cpuinfo = "==9.0.0"
pyarrow = "==11.0.0"
pyarrow-hotfix = "==0.6"
pyasn1 = "==0.4.8"
pyasn1-modules = "==0.2.8"
pyautogen = "==0.2.29"
pycodestyle = "==2.10.0"
pycosat = {file = "../../../../../runner/miniforge3/conda-bld/pycosat_1649384941891/work"}
pycparser = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/pycparser_1636257122734/work"}
pycryptodome = "==3.14.1"
pydantic = "==1.10.16"
pyflakes = "==3.0.1"
pygments = "==2.18.0"
pymongo = "==3.7.2"
pymysql = "==0.9.3"
pyopenssl = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/pyopenssl_1643496850550/work"}
pyparsing = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/pyparsing_1652235407899/work"}
pyrouge = {ref = "dfdff03cff1b69bfb18d42dadfb79c79f1caaa18", git = "https://github.com/pltrdy/pyrouge.git"}
pyrsistent = {file = "../../../../../runner/miniforge3/conda-bld/pyrsistent_1649013408545/work"}
pysocks = {file = "../../../../../runner/miniforge3/conda-bld/pysocks_1648857374584/work"}
python-dateutil = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/python-dateutil_1626286286081/work"}
python-docx = "==1.1.2"
python-dotenv = "==1.0.0"
pytz = "==2022.1"
pyyaml = "==6.0"
pyzmq = {file = "../../../../../runner/miniforge3/conda-bld/pyzmq_1656183620094/work"}
qtconsole = "==5.4.2"
qtpy = "==2.3.1"
readability-lxml = "==0.8.1"
redis = "==3.2.1"
redis-py-cluster = "==2.1.3"
regex = "==2023.3.23"
requests = "==2.31.0"
requests-oauthlib = "==1.3.1"
responses = "==0.18.0"
rich = "==13.7.1"
rsa = "==4.8"
ruamel-yaml-conda = {file = "../../../../../runner/miniforge3/conda-bld/ruamel_yaml_1653464548430/work"}
safetensors = "==0.4.1"
selenium = "==4.8.3"
send2trash = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/send2trash_1628511208346/work"}
sentencepiece = "==0.1.97"
sentry-sdk = "==1.18.0"
setproctitle = "==1.3.2"
sgmllib3k = "==1.0.0"
shapely = "==2.0.4"
shellingham = "==1.5.4"
six = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/six_1620240208055/work"}
smmap = "==5.0.0"
sniffio = "==1.3.0"
sortedcontainers = "==2.4.0"
soupsieve = "==2.4.1"
sourcery = "==1.2.0"
sqlalchemy = "==2.0.23"
sqlmodel = "==0.0.19"
stack-data = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/stack_data_1655315839047/work"}
starlette = "==0.26.1"
sympy = "==1.11.1"
tenacity = "==8.2.3"
tensorboard = "==2.9.0"
tensorboard-data-server = "==0.6.1"
tensorboard-plugin-wit = "==1.8.1"
tensorflow-estimator = "==2.9.0"
tensorflow-macos = "==2.9.0"
tensorflow-metal = "==0.5.0"
termcolor = "==1.1.0"
terminado = {file = "../../../../../runner/miniforge3/conda-bld/terminado_1652790782651/work"}
testpath = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/testpath_1645693042223/work"}
tiktoken = "==0.3.3"
tokenizers = "==0.19.1"
tomli = "==2.0.1"
torch = "==2.0.0"
torchmetrics = "==0.11.4"
torchvision = "==0.2.2"
tornado = {file = "../../../../../runner/miniforge3/conda-bld/tornado_1656938068018/work"}
tqdm = "==4.65.0"
traitlets = {file = "../../../../../../System/Volumes/Data/home/<USER>/feedstock_root/build_artifacts/traitlets_1655411388954/work"}

[dev-packages]

[requires]
python_version = "3.8"
