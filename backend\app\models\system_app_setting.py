from mongoengine import Document, StringField, DateTimeField, EnumField, IntField, DictField
from pydantic import BaseModel,ConfigDict, Field
from typing import Optional, Dict, Any, Union, Literal, List
from enum import Enum
from datetime import datetime
from app.utils.enums import AppType



class WiseragParams(BaseModel):
    api_version: str = Field(description="API版本")
    service_url: str = Field(description="服务地址")
    max_tokens: int = Field(description="最大token数")
    token_key: str = Field(description="token密钥")
    token_price: int = Field(description="token价格")

class KnowledgeQAParams(BaseModel):
    knowledge_base_ids: List[str] = Field(description="知识库ID列表")
    rerank_id: int = Field(description="排序模型",default=None)
    llm_id: str = Field(description="大语言模型ID",default=None)
    config: Dict[str, Any] = Field(description="配置",default={})
    prompt: str = Field(description="提示词",default="")
    prompt_retrieval: str = Field(description="召回提示词模版",default="")
    system_prompt: str = Field(description="召回提示词模版",default=None)

class AgentParams(BaseModel):
    agent_type: str = Field(description="智能体类型")
    tools: List[str] = Field(description="可用工具列表")
    memory_type: str = Field(description="记忆类型")

class LLMParams(BaseModel):
   llm_id: str = Field(description="大语言模型ID")
   system_prompt: str = Field(description="系统提示词")
   prompt_template: str = Field(description="用户提示词模版")

class ExpertSystemParams(BaseModel):
    rules_path: str = Field(description="规则文件路径")
    inference_engine: str = Field(description="推理引擎类型")

ParamsType = Union[
    WiseragParams,
    KnowledgeQAParams,
    AgentParams,
    LLMParams,
    ExpertSystemParams
]

class SystemAppSettingModel(Document):
    meta = {
        'collection': 'system_app_settings'
    }
    id = IntField(primary_key=True)
    name = StringField(required=True)
    description = StringField()
    app_info = StringField(required=True)
    type = EnumField(AppType, required=True)
    params = DictField(required=True)  # 会根据 type 存储对应的参数模型数据
    extra = DictField() # 额外参数
    # service_url = StringField()
    # token_key = StringField()
    # token_price = StringField()
    created_at = DateTimeField(default=datetime.now)

    def clean(self):
        """验证 params 字段的数据格式"""
        super().clean()
        try:
            if self.type == AppType.WISERAG:
                WiseragParams.model_validate(self.params)
            elif self.type == AppType.KNOWLEDGE_QA:
                KnowledgeQAParams.model_validate(self.params)
            elif self.type == AppType.AGENT:
                AgentParams.model_validate(self.params)
            elif self.type == AppType.LLM:
                LLMParams.model_validate(self.params)
            elif self.type == AppType.EXPERT_SYSTEM:
                ExpertSystemParams.model_validate(self.params)
        except Exception as e:
            raise ValidationError(f"参数验证失败: {str(e)}")

class SystemAppSetting(BaseModel):
    id: int
    name: str
    description: str
    app_info: str
    type: str
    extra: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(
        from_attributes=True,
        protected_namespaces=()
    )

class SystemAppSetting(SystemAppSetting):
    created_at: datetime

    model_config = ConfigDict(
        from_attributes=True,
        protected_namespaces=()
    )

class SystemAppSettingCreate(BaseModel):
    name: str
    description: str
    app_info: str
    type: str
    params: ParamsType


class SystemAppSettingUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    app_info: Optional[str] = None
    type: Optional[str] = None
    params: Optional[Dict[str, Any]] = None

    # def validate_params(self):
    #     """根据 type 验证 params"""
    #     if self.type and self.params:
    #         try:
    #             if self.type == "WISERAG":
    #                 WiseragParams.model_validate(self.params)
    #             elif self.type == "KNOWLEDGE_QA":
    #                 KnowledgeQAParams.model_validate(self.params)
    #             elif self.type == "LLM":
    #                 LLMParams.model_validate(self.params)
    #             elif self.type == "AGENT":
    #                 AgentParams.model_validate(self.params)
    #             elif self.type == "EXPERT_SYSTEM":
    #                 ExpertSystemParams.model_validate(self.params)
    #         except Exception as e:
    #             raise ValueError(f"参数验证失败: {str(e)}")