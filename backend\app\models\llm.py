from mongoengine import Document, StringField, DateTimeField, IntField, ObjectIdField, ListField, DictField, FloatField, BooleanField
from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from enum import Enum

class Provider(str, Enum):
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    JIUTIAN = "jiutian"
    TELEAI = "teleai"
    DOUBAO = "doubao"
    LOCAL = "local"
    OLLAMA = "ollama"

class LLMModel(Document):
    meta = {'collection': 'llms'}
    id = IntField(required=True, primary_key=True)
    name = StringField(required=True)  # 服务名称
    shortName = StringField(required=True)  # 模型简称
    m_name = StringField(required=True)
    description = StringField()
    temperature = FloatField(default=0.7)
    price = FloatField(default=0.0)  # 价格
    logoUrl = StringField(required=True)  # 模型logo
    logoFallback = StringField(required=True)  # 模型logo fallback
    max_tokens = IntField(default=100)
    unit = StringField(default="M Tokens")  # 计费单位
    top_p = FloatField(default=1.0)
    frequency_penalty = FloatField(default=0.0)
    presence_penalty = FloatField(default=0.0)
    service_url = StringField(required=True)  # 新增字段：服务地址
    api_key = StringField(required=True)  # 新增字段：key
    provider = StringField(required=True)  # 新增字段：模型提供商
    created_at = DateTimeField(default=datetime.now)
    created_by = IntField(required=True)
    is_active = BooleanField(default=True)
    tags = ListField(StringField())  # 模型标签

    def __repr__(self):
        return f"<LLMModel(name={self.m_name}, temperature={self.temperature})>"

class LLMCreate(BaseModel):
    name: str
    m_name: str
    description:  str
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 100
    top_p: Optional[float] = 1.0
    frequency_penalty: Optional[float] = 0.0
    presence_penalty: Optional[float] = 0.0
    service_url: str  # 新增字段
    api_key: str  # 新增字段
    provider: str  # 新增字段

    class Config:
        from_attributes = True

class LLMUpdate(BaseModel):
    name: Optional[str] = None
    m_name: Optional[str] = None
    description :Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    top_p: Optional[float] = None
    frequency_penalty: Optional[float] = None
    presence_penalty: Optional[float] = None
    service_url: Optional[str] = None  # 新增字段
    api_key: Optional[str] = None  # 新增字段
    provider: Optional[str] = None  # 新增字段
    is_active: Optional[bool] = None

    class Config:
        from_attributes = True

class LLMResponse(BaseModel):
    id: int
    name: str
    m_name: str
    description: str
    temperature: float
    max_tokens: int
    top_p: float
    service_url: str  # 新增字段
    api_key: str  # 新增字段
    provider: str  # 新增字段
    created_at: datetime
    created_by: int

    class Config:
        from_attributes = True