"""
知识图谱可视化API接口
提供知识库图谱数据查询和可视化接口
"""

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from urllib.parse import unquote
from .graph_retriever import GraphRetriever
from .graph_visualizer import GraphVisualizer
from loguru import logger
import asyncio

app = FastAPI(title="知识图谱可视化API", version="1.0.0")


# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有请求头
)

# 全局实例
graph_retriever = None
graph_visualizer = GraphVisualizer()


class GraphAPIException(Exception):
    """图谱API异常"""
    def __init__(self, message: str, code: int = 400):
        self.message = message
        self.code = code


@app.exception_handler(GraphAPIException)
async def graph_api_exception_handler(request, exc: GraphAPIException):
    return {"error": exc.message, "code": exc.code}


@app.on_event("startup")
async def startup_event():
    """启动时初始化"""
    global graph_retriever
    try:
        logger.info("🚀 [系统启动] 开始初始化图谱API服务...")
        graph_retriever = GraphRetriever()
        logger.info("📊 [系统启动] GraphRetriever实例创建成功")

        await graph_retriever.initialize()
        logger.info("🔗 [系统启动] Neo4j连接初始化成功")

        logger.info("✅ [系统启动] 图谱API服务启动完成，服务已就绪")
    except Exception as e:
        logger.error(f"❌ [系统启动] 图谱API服务启动失败: {e}", exc_info=True)
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """关闭时清理资源"""
    global graph_retriever
    logger.info("🔄 [系统关闭] 开始关闭图谱API服务...")

    if graph_retriever:
        try:
            await graph_retriever.close()
            logger.info("🔗 [系统关闭] Neo4j连接已关闭")
        except Exception as e:
            logger.error(f"⚠️ [系统关闭] 关闭Neo4j连接时出错: {e}")

    logger.info("✅ [系统关闭] 图谱API服务已完全关闭")


class EntitySubgraphResponse(BaseModel):
    """实体子图响应模型"""
    center_entity: Optional[Dict[str, Any]]
    nodes: List[Dict[str, Any]]
    links: List[Dict[str, Any]]
    depth: int
    stats: Dict[str, int]
    knowledge_base_id: str


class KnowledgeBaseGraphResponse(BaseModel):
    """知识库图谱响应模型"""
    knowledge_base_id: str
    nodes: List[Dict[str, Any]]
    links: List[Dict[str, Any]]
    stats: Dict[str, int]
    summary: Dict[str, Any]


class EntitySubgraphRequest(BaseModel):
    """实体子图查询请求模型"""
    entity_name: str
    max_depth: int = 2
    format: str = "echarts"


@app.get("/api/knowledge-base/{kb_id}/entity/subgraph", 
         response_model=EntitySubgraphResponse)
async def get_entity_subgraph(
    kb_id: str,
    entity_name: str,
    max_depth: int = Query(2, ge=1, le=5, description="查询深度，范围1-5"),
    format: str = Query("echarts", regex="^(echarts|raw)$", description="返回格式")
):
    """
    获取指定实体的子图数据
    
    Args:
        kb_id: 知识库ID
        entity_name: 实体名称
        max_depth: 查询深度 (1-5)
        format: 返回格式 (echarts/raw)
    
    Returns:
        实体子图数据
    """
    try:
        if not graph_retriever:
            logger.error(f"[GET实体子图] 图谱服务未初始化 - kb_id={kb_id}, entity={entity_name}")
            raise GraphAPIException("图谱服务未初始化", 500)

        # URL解码实体名称
        decoded_entity_name = unquote(entity_name)

        logger.info(f"[GET实体子图] 开始查询 - kb_id={kb_id}, entity_raw={entity_name}, entity_decoded={decoded_entity_name}, depth={max_depth}, format={format}")

        # 获取实体子图
        result = await graph_retriever.get_entity_subgraph_by_kb(
            decoded_entity_name, kb_id, max_depth
        )

        if not result["center_entity"]:
            logger.warning(f"[GET实体子图] 未找到实体 - kb_id={kb_id}, entity={decoded_entity_name}")
            raise GraphAPIException(f"未找到实体: {decoded_entity_name}", 404)

        logger.info(f"[GET实体子图] 查询成功 - kb_id={kb_id}, entity={decoded_entity_name}, nodes_count={len(result['nodes'])}, relationships_count={len(result['relationships'])}")

        # 数据格式转换
        if format == "echarts":
            logger.debug(f"[GET实体子图] 转换为ECharts格式 - kb_id={kb_id}, entity={decoded_entity_name}")
            graph_data = graph_visualizer.to_echarts_format(
                result["nodes"], result["relationships"], kb_id
            )
            logger.info(f"[GET实体子图] ECharts转换完成 - kb_id={kb_id}, entity={decoded_entity_name}, echarts_nodes={len(graph_data['nodes'])}, echarts_links={len(graph_data['links'])}")
            return EntitySubgraphResponse(
                center_entity=result["center_entity"],
                nodes=graph_data["nodes"],
                links=graph_data["links"],
                depth=max_depth,
                stats=result["stats"],
                knowledge_base_id=kb_id
            )
        else:  # raw format
            logger.info(f"[GET实体子图] 返回原始格式 - kb_id={kb_id}, entity={decoded_entity_name}")
            return EntitySubgraphResponse(
                center_entity=result["center_entity"],
                nodes=result["nodes"],
                links=result["relationships"],
                depth=max_depth,
                stats=result["stats"],
                knowledge_base_id=kb_id
            )

    except GraphAPIException as e:
        logger.error(f"[GET实体子图] 业务异常 - kb_id={kb_id}, entity={entity_name}, error={e.message}")
        raise
    except Exception as e:
        logger.error(f"[GET实体子图] 系统异常 - kb_id={kb_id}, entity={entity_name}, error={str(e)}", exc_info=True)
        raise GraphAPIException(f"获取实体子图失败: {str(e)}", 500)


@app.post("/api/knowledge-base/{kb_id}/entity/subgraph",
          response_model=EntitySubgraphResponse)
async def get_entity_subgraph_post(
    kb_id: str,
    request: EntitySubgraphRequest
):
    """
    获取指定实体的子图数据（POST方式，避免中文URL编码问题）

    Args:
        kb_id: 知识库ID
        request: 请求体，包含entity_name, max_depth, format

    Returns:
        实体子图数据
    """
    try:
        if not graph_retriever:
            logger.error(f"[POST实体子图] 图谱服务未初始化 - kb_id={kb_id}")
            raise GraphAPIException("图谱服务未初始化", 500)

        entity_name = request.entity_name
        max_depth = request.max_depth
        format = request.format

        logger.info(f"[POST实体子图] 接收请求 - kb_id={kb_id}, entity={entity_name}, depth={max_depth}, format={format}")

        # 验证参数
        if max_depth < 1 or max_depth > 100:
            logger.warning(f"[POST实体子图] 参数验证失败 - kb_id={kb_id}, entity={entity_name}, invalid_depth={max_depth}")
            raise GraphAPIException("max_depth 必须在 1-100 之间", 400)

        if format not in ["echarts", "raw"]:
            logger.warning(f"[POST实体子图] 参数验证失败 - kb_id={kb_id}, entity={entity_name}, invalid_format={format}")
            raise GraphAPIException("format 必须是 'echarts' 或 'raw'", 400)

        logger.info(f"[POST实体子图] 参数验证通过，开始查询 - kb_id={kb_id}, entity={entity_name}, depth={max_depth}")

        # 获取实体子图
        result = await graph_retriever.get_entity_subgraph_by_kb(
            entity_name, kb_id, max_depth
        )

        if not result["center_entity"]:
            logger.warning(f"[POST实体子图] 未找到实体 - kb_id={kb_id}, entity={entity_name}")
            raise GraphAPIException(f"未找到实体: {entity_name}", 404)

        logger.info(f"[POST实体子图] 查询成功 - kb_id={kb_id}, entity={entity_name}, nodes_count={len(result['nodes'])}, relationships_count={len(result['relationships'])}")

        # 数据格式转换
        if format == "echarts":
            logger.debug(f"[POST实体子图] 转换为ECharts格式 - kb_id={kb_id}, entity={entity_name}")
            graph_data = graph_visualizer.to_echarts_format(
                result["nodes"], result["relationships"], kb_id
            )
            logger.info(f"[POST实体子图] ECharts转换完成 - kb_id={kb_id}, entity={entity_name}, echarts_nodes={len(graph_data['nodes'])}, echarts_links={len(graph_data['links'])}")
            return EntitySubgraphResponse(
                center_entity=result["center_entity"],
                nodes=graph_data["nodes"],
                links=graph_data["links"],
                depth=max_depth,
                stats=result["stats"],
                knowledge_base_id=kb_id
            )
        else:  # raw format
            logger.info(f"[POST实体子图] 返回原始格式 - kb_id={kb_id}, entity={entity_name}")
            return EntitySubgraphResponse(
                center_entity=result["center_entity"],
                nodes=result["nodes"],
                links=result["relationships"],
                depth=max_depth,
                stats=result["stats"],
                knowledge_base_id=kb_id
            )

    except GraphAPIException as e:
        logger.error(f"[POST实体子图] 业务异常 - kb_id={kb_id}, entity={request.entity_name if hasattr(request, 'entity_name') else 'unknown'}, error={e.message}")
        raise
    except Exception as e:
        logger.error(f"[POST实体子图] 系统异常 - kb_id={kb_id}, entity={request.entity_name if hasattr(request, 'entity_name') else 'unknown'}, error={str(e)}", exc_info=True)
        raise GraphAPIException(f"获取实体子图失败: {str(e)}", 500)


@app.get("/api/knowledge-base/{kb_id}/graph",
         response_model=KnowledgeBaseGraphResponse)
async def get_knowledge_base_graph(
    kb_id: str,
    max_depth: int = Query(2, ge=1, le=5, description="查询深度"),
    limit: int = Query(50, ge=1, le=100, description="最大返回节点数"),
    entity_types: Optional[List[str]] = Query(None, description="过滤的实体类型"),
    format: str = Query("echarts", regex="^(echarts|raw)$", description="返回格式")
):
    """
    获取知识库的完整图谱数据
    
    Args:
        kb_id: 知识库ID
        max_depth: 查询深度
        limit: 最大返回节点数
        entity_types: 过滤的实体类型
        format: 返回格式
    
    Returns:
        知识库图谱数据
    """
    try:
        if not graph_retriever:
            logger.error(f"[知识库图谱] 图谱服务未初始化 - kb_id={kb_id}")
            raise GraphAPIException("图谱服务未初始化", 500)

        logger.info(f"[知识库图谱] 开始查询 - kb_id={kb_id}, depth={max_depth}, limit={limit}, entity_types={entity_types}, format={format}")

        # 获取知识库图谱
        result = await graph_retriever.get_knowledge_base_graph(
            kb_id, max_depth, limit, entity_types
        )

        if not result["nodes"]:
            logger.warning(f"[知识库图谱] 未找到图谱数据 - kb_id={kb_id}, limit={limit}")
            raise GraphAPIException(f"知识库 {kb_id} 中没有找到图谱数据", 404)

        logger.info(f"[知识库图谱] 查询成功 - kb_id={kb_id}, nodes_count={len(result['nodes'])}, relationships_count={len(result['relationships'])}, stats={result['stats']}")

        # 数据格式转换
        if format == "echarts":
            logger.debug(f"[知识库图谱] 转换为ECharts格式 - kb_id={kb_id}")
            graph_data = graph_visualizer.to_echarts_format(
                result["nodes"], result["relationships"], kb_id
            )
            logger.info(f"[知识库图谱] ECharts转换完成 - kb_id={kb_id}, echarts_nodes={len(graph_data['nodes'])}, echarts_links={len(graph_data['links'])}")
            return KnowledgeBaseGraphResponse(
                knowledge_base_id=kb_id,
                nodes=graph_data["nodes"],
                links=graph_data["links"],
                stats=result["stats"],
                summary=result["summary"]
            )
        else:  # raw format
            logger.info(f"[知识库图谱] 返回原始格式 - kb_id={kb_id}")
            return KnowledgeBaseGraphResponse(
                knowledge_base_id=kb_id,
                nodes=result["nodes"],
                links=result["relationships"],
                stats=result["stats"],
                summary=result["summary"]
            )

    except GraphAPIException as e:
        logger.error(f"[知识库图谱] 业务异常 - kb_id={kb_id}, error={e.message}")
        raise
    except Exception as e:
        logger.error(f"[知识库图谱] 系统异常 - kb_id={kb_id}, error={str(e)}", exc_info=True)
        raise GraphAPIException(f"获取知识库图谱失败: {str(e)}", 500)


@app.get("/api/knowledge-base/{kb_id}/stats")
async def get_knowledge_base_stats(kb_id: str):
    """
    获取知识库统计信息
    
    Args:
        kb_id: 知识库ID
    
    Returns:
        知识库统计信息
    """
    try:
        if not graph_retriever:
            logger.error(f"[知识库统计] 图谱服务未初始化 - kb_id={kb_id}")
            raise GraphAPIException("图谱服务未初始化", 500)

        logger.info(f"[知识库统计] 开始查询统计信息 - kb_id={kb_id}")

        # 获取基本统计
        logger.debug(f"[知识库统计] 获取基本统计 - kb_id={kb_id}")
        stats = await graph_retriever.neo4j_client.get_kb_stats(kb_id)

        # 获取实体类型统计
        logger.debug(f"[知识库统计] 获取实体类型统计 - kb_id={kb_id}")
        entity_types = await graph_retriever.neo4j_client.get_kb_entity_type_stats(kb_id)

        # 获取关系类型统计
        logger.debug(f"[知识库统计] 获取关系类型统计 - kb_id={kb_id}")
        relation_types = await graph_retriever.neo4j_client.get_kb_relation_type_stats(kb_id)

        logger.info(f"[知识库统计] 统计查询完成 - kb_id={kb_id}, nodes={stats.get('nodes', 0)}, relationships={stats.get('relationships', 0)}, entity_type_count={len(entity_types)}, relation_type_count={len(relation_types)}")

        return {
            "knowledge_base_id": kb_id,
            "basic_stats": stats,
            "entity_types": entity_types,
            "relation_types": relation_types
        }

    except GraphAPIException as e:
        logger.error(f"[知识库统计] 业务异常 - kb_id={kb_id}, error={e.message}")
        raise
    except Exception as e:
        logger.error(f"[知识库统计] 系统异常 - kb_id={kb_id}, error={str(e)}", exc_info=True)
        raise GraphAPIException(f"获取知识库统计失败: {str(e)}", 500)


@app.get("/api/knowledge-base/{kb_id}/entities")
async def list_entities(
    kb_id: str,
    limit: int = Query(20, ge=1, le=100, description="返回数量限制")
):
    """
    列出知识库中的实体（用于调试）

    Args:
        kb_id: 知识库ID
        limit: 返回数量限制

    Returns:
        实体列表
    """
    try:
        if not graph_retriever:
            logger.error(f"[实体列表] 图谱服务未初始化 - kb_id={kb_id}")
            raise GraphAPIException("图谱服务未初始化", 500)

        logger.info(f"[实体列表] 开始查询实体列表 - kb_id={kb_id}, limit={limit}")

        # 直接查询Neo4j获取实体列表
        async with graph_retriever.neo4j_client.driver.session(database=graph_retriever.neo4j_client.database) as session:
            kb_label = f"KB_{kb_id}"
            query = f"""
            MATCH (n:{kb_label})
            RETURN n.entity_id as entity_id,
                   n.entity_name as entity_name,
                   n.entity_type as entity_type
            LIMIT {limit}
            """

            logger.debug(f"[实体列表] 执行查询 - kb_id={kb_id}, query_label={kb_label}")
            result = await session.run(query)
            entities = []
            async for record in result:
                entities.append({
                    "entity_id": record["entity_id"],
                    "entity_name": record["entity_name"],
                    "entity_type": record["entity_type"]
                })

        logger.info(f"[实体列表] 查询完成 - kb_id={kb_id}, found_entities={len(entities)}, limit={limit}")

        # 打印前几个实体名称用于调试
        if entities:
            sample_names = [e["entity_name"] for e in entities[:5]]
            logger.debug(f"[实体列表] 示例实体名称 - kb_id={kb_id}, samples={sample_names}")

        return {
            "knowledge_base_id": kb_id,
            "entities": entities,
            "count": len(entities)
        }

    except GraphAPIException as e:
        logger.error(f"[实体列表] 业务异常 - kb_id={kb_id}, error={e.message}")
        raise
    except Exception as e:
        logger.error(f"[实体列表] 系统异常 - kb_id={kb_id}, error={str(e)}", exc_info=True)
        raise GraphAPIException(f"获取实体列表失败: {str(e)}", 500)


@app.get("/health")
async def health_check():
    """健康检查"""
    logger.debug("[健康检查] 收到健康检查请求")

    # 检查图谱服务状态
    service_status = "healthy" if graph_retriever and graph_retriever.is_connected else "unhealthy"

    response = {
        "status": service_status,
        "service": "知识图谱可视化API",
        "version": "1.0.0",
        "graph_service_connected": graph_retriever.is_connected if graph_retriever else False
    }

    if service_status == "healthy":
        logger.debug("[健康检查] 服务状态正常")
    else:
        logger.warning("[健康检查] 服务状态异常 - 图谱服务未连接")

    return response


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8090)
