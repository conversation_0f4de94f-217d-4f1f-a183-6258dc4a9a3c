(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7131],{47356:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.default={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"}},44149:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.default={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M869 487.8L491.2 159.9c-2.9-2.5-6.6-3.9-10.5-3.9h-88.5c-7.4 0-10.8 9.2-5.2 14l350.2 304H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h585.1L386.9 854c-5.6 4.9-2.2 14 5.2 14h91.5c1.9 0 3.8-.7 5.2-2L869 536.2a32.07 32.07 0 000-48.4z"}}]},name:"arrow-right",theme:"outlined"}},88372:function(e,t,n){"use strict";n.d(t,{f:function(){return p}});var r=n(4942),o=n(21532),a=n(93967),i=n.n(a),l=n(67294),c=n(76509),s=n(1413),u=n(64847),d=function(e){return(0,r.Z)({},e.componentCls,{width:"100%","&-wide":{maxWidth:1152,margin:"0 auto"}})};var f=n(85893),p=function(e){var t=(0,l.useContext)(c.X),n=e.children,a=e.contentWidth,p=e.className,m=e.style,g=(0,l.useContext)(o.ZP.ConfigContext).getPrefixCls,v=e.prefixCls||g("pro"),h=a||t.contentWidth,y="".concat(v,"-grid-content"),b=function(e){return(0,u.Xj)("ProLayoutGridContent",(function(t){var n=(0,s.Z)((0,s.Z)({},t),{},{componentCls:".".concat(e)});return[d(n)]}))}(y),x=b.wrapSSR,C=b.hashId,S="Fixed"===h&&"top"===t.layout;return x((0,f.jsx)("div",{className:i()(y,C,p,(0,r.Z)({},"".concat(y,"-wide"),S)),style:m,children:(0,f.jsx)("div",{className:"".concat(v,"-grid-content-children ").concat(C).trim(),children:n})}))}},97131:function(e,t,n){"use strict";n.d(t,{_z:function(){return He}});var r=n(4942),o=n(91),a=n(1413),i=n(71002),l=n(10915),c=n(11941),s=n(67159),u=n(21532),d=n(67294),f=n(93967),p=n.n(f),m=n(9220),g=n(74902),v=n(75164);var h=function(e){let t;const n=n=>()=>{t=null,e.apply(void 0,(0,g.Z)(n))},r=function(){if(null==t){for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];t=(0,v.Z)(n(r))}};return r.cancel=()=>{v.Z.cancel(t),t=null},r},y=n(53124),b=n(83559);var x=(0,b.I$)("Affix",(e=>{const{componentCls:t}=e;return{[t]:{position:"fixed",zIndex:e.zIndexPopup}}}),(e=>({zIndexPopup:e.zIndexBase+10})));function C(e){return e!==window?e.getBoundingClientRect():{top:0,bottom:window.innerHeight}}function S(e,t,n){if(void 0!==n&&Math.round(t.top)>Math.round(e.top)-n)return n+t.top}function w(e,t,n){if(void 0!==n&&Math.round(t.bottom)<Math.round(e.bottom)+n){return n+(window.innerHeight-t.bottom)}}var Z=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const j=["resize","scroll","touchstart","touchmove","touchend","pageshow","load"];function k(){return"undefined"!=typeof window?window:null}const P=d.forwardRef(((e,t)=>{var n;const{style:r,offsetTop:o,offsetBottom:a,prefixCls:i,className:l,rootClassName:c,children:s,target:u,onChange:f,onTestUpdatePosition:g}=e,v=Z(e,["style","offsetTop","offsetBottom","prefixCls","className","rootClassName","children","target","onChange","onTestUpdatePosition"]),{getPrefixCls:b,getTargetContainer:P}=d.useContext(y.E_),O=b("affix",i),[_,N]=d.useState(!1),[I,E]=d.useState(),[M,T]=d.useState(),B=d.useRef(0),R=d.useRef(null),H=d.useRef(null),z=d.useRef(null),A=d.useRef(null),L=d.useRef(null),X=null!==(n=null!=u?u:P)&&void 0!==n?n:k,W=void 0===a&&void 0===o?0:o,F=()=>{B.current=1,(()=>{if(1!==B.current||!A.current||!z.current||!X)return;const e=X();if(e){const t={status:0},n=C(z.current);if(0===n.top&&0===n.left&&0===n.width&&0===n.height)return;const r=C(e),o=S(n,r,W),i=w(n,r,a);void 0!==o?(t.affixStyle={position:"fixed",top:o,width:n.width,height:n.height},t.placeholderStyle={width:n.width,height:n.height}):void 0!==i&&(t.affixStyle={position:"fixed",bottom:i,width:n.width,height:n.height},t.placeholderStyle={width:n.width,height:n.height}),t.lastAffix=!!t.affixStyle,_!==t.lastAffix&&(null==f||f(t.lastAffix)),B.current=t.status,E(t.affixStyle),T(t.placeholderStyle),N(t.lastAffix)}})()},$=h((()=>{F()})),D=h((()=>{if(X&&I){const e=X();if(e&&z.current){const t=C(e),n=C(z.current),r=S(n,t,W),o=w(n,t,a);if(void 0!==r&&I.top===r||void 0!==o&&I.bottom===o)return}}F()})),U=()=>{const e=null==X?void 0:X();e&&(j.forEach((t=>{var n;H.current&&(null===(n=R.current)||void 0===n||n.removeEventListener(t,H.current)),null==e||e.addEventListener(t,D)})),R.current=e,H.current=D)};d.useImperativeHandle(t,(()=>({updatePosition:$}))),d.useEffect((()=>(L.current=setTimeout(U),()=>(()=>{L.current&&(clearTimeout(L.current),L.current=null);const e=null==X?void 0:X();j.forEach((t=>{var n;null==e||e.removeEventListener(t,D),H.current&&(null===(n=R.current)||void 0===n||n.removeEventListener(t,H.current))})),$.cancel(),D.cancel()})())),[]),d.useEffect((()=>{U()}),[u,I]),d.useEffect((()=>{$()}),[u,o,a]);const[Q,q,G]=x(O),K=p()(c,q,O,G),V=p()({[K]:I});return Q(d.createElement(m.Z,{onResize:$},d.createElement("div",Object.assign({style:r,className:l,ref:z},v),I&&d.createElement("div",{style:M,"aria-hidden":"true"}),d.createElement("div",{className:V,ref:A,style:I},d.createElement(m.Z,{onResize:$},s)))))}));var O=P,_=n(76509),N=n(12044),I=n(98423),E=n(73935),M=n(64847),T=function(e){return(0,r.Z)({},e.componentCls,{position:"fixed",insetInlineEnd:0,bottom:0,zIndex:99,display:"flex",alignItems:"center",width:"100%",paddingInline:24,paddingBlock:0,boxSizing:"border-box",lineHeight:"64px",backgroundColor:(0,M.uK)(e.colorBgElevated,.6),borderBlockStart:"1px solid ".concat(e.colorSplit),"-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)",color:e.colorText,transition:"all 0.2s ease 0s","&-left":{flex:1,color:e.colorText},"&-right":{color:e.colorText,"> *":{marginInlineEnd:8,"&:last-child":{marginBlock:0,marginInline:0}}}})};var B=n(85893),R=["children","className","extra","portalDom","style","renderContent"],H=function(e){var t=e.children,n=e.className,i=e.extra,l=e.portalDom,c=void 0===l||l,s=e.style,f=e.renderContent,m=(0,o.Z)(e,R),g=(0,d.useContext)(u.ZP.ConfigContext),v=g.getPrefixCls,h=g.getTargetContainer,y=e.prefixCls||v("pro"),b="".concat(y,"-footer-bar"),x=function(e){return(0,M.Xj)("ProLayoutFooterToolbar",(function(t){var n=(0,a.Z)((0,a.Z)({},t),{},{componentCls:".".concat(e)});return[T(n)]}))}(b),C=x.wrapSSR,S=x.hashId,w=(0,d.useContext)(_.X),Z=(0,d.useMemo)((function(){var e=w.hasSiderMenu,t=w.isMobile,n=w.siderWidth;if(e)return n?t?"100%":"calc(100% - ".concat(n,"px)"):"100%"}),[w.collapsed,w.hasSiderMenu,w.isMobile,w.siderWidth]),j=(0,d.useMemo)((function(){return"undefined"==typeof window||"undefined"==typeof document?null:(null==h?void 0:h())||document.body}),[]),k=function(e,t){var n=t.stylish;return(0,M.Xj)("ProLayoutFooterToolbarStylish",(function(t){var o=(0,a.Z)((0,a.Z)({},t),{},{componentCls:".".concat(e)});return n?[(0,r.Z)({},"".concat(o.componentCls),null==n?void 0:n(o))]:[]}))}("".concat(b,".").concat(b,"-stylish"),{stylish:e.stylish}),P=(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)("div",{className:"".concat(b,"-left ").concat(S).trim(),children:i}),(0,B.jsx)("div",{className:"".concat(b,"-right ").concat(S).trim(),children:t})]});(0,d.useEffect)((function(){return w&&null!=w&&w.setHasFooterToolbar?(null==w||w.setHasFooterToolbar(!0),function(){var e;null==w||null===(e=w.setHasFooterToolbar)||void 0===e||e.call(w,!1)}):function(){}}),[]);var O=(0,B.jsx)("div",(0,a.Z)((0,a.Z)({className:p()(n,S,b,(0,r.Z)({},"".concat(b,"-stylish"),!!e.stylish)),style:(0,a.Z)({width:Z},s)},(0,I.Z)(m,["prefixCls"])),{},{children:f?f((0,a.Z)((0,a.Z)((0,a.Z)({},e),w),{},{leftWidth:Z}),P):P})),H=(0,N.j)()&&c&&j?(0,E.createPortal)(O,j,b):O;return k.wrapSSR(C((0,B.jsx)(d.Fragment,{children:H},b)))},z=n(88372),A=n(97685),L=n(3770),X=n.n(L),W=n(77059),F=n.n(W),$=n(50344),D=n(64217),U=n(96159),Q=n(13622),q=n(7743);const G=e=>{let{children:t}=e;const{getPrefixCls:n}=d.useContext(y.E_),r=n("breadcrumb");return d.createElement("li",{className:`${r}-separator`,"aria-hidden":"true"},""===t?t:t||"/")};G.__ANT_BREADCRUMB_SEPARATOR=!0;var K=G,V=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function Y(e,t,n,r){if(null==n)return null;const{className:o,onClick:a}=t,i=V(t,["className","onClick"]),l=Object.assign(Object.assign({},(0,D.Z)(i,{data:!0,aria:!0})),{onClick:a});return void 0!==r?d.createElement("a",Object.assign({},l,{className:p()(`${e}-link`,o),href:r}),n):d.createElement("span",Object.assign({},l,{className:p()(`${e}-link`,o)}),n)}function J(e,t){return(n,r,o,a,i)=>{if(t)return t(n,r,o,a);const l=function(e,t){if(void 0===e.title||null===e.title)return null;const n=Object.keys(t).join("|");return"object"==typeof e.title?e.title:String(e.title).replace(new RegExp(`:(${n})`,"g"),((e,n)=>t[n]||e))}(n,r);return Y(e,n,l,i)}}var ee=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const te=e=>{const{prefixCls:t,separator:n="/",children:r,menu:o,overlay:a,dropdownProps:i,href:l}=e;const c=(e=>{if(o||a){const n=Object.assign({},i);if(o){const e=o||{},{items:t}=e,r=ee(e,["items"]);n.menu=Object.assign(Object.assign({},r),{items:null==t?void 0:t.map(((e,t)=>{var{key:n,title:r,label:o,path:a}=e,i=ee(e,["key","title","label","path"]);let c=null!=o?o:r;return a&&(c=d.createElement("a",{href:`${l}${a}`},c)),Object.assign(Object.assign({},i),{key:null!=n?n:t,label:c})}))})}else a&&(n.overlay=a);return d.createElement(q.Z,Object.assign({placement:"bottom"},n),d.createElement("span",{className:`${t}-overlay-link`},e,d.createElement(Q.Z,null)))}return e})(r);return null!=c?d.createElement(d.Fragment,null,d.createElement("li",null,c),n&&d.createElement(K,null,n)):null},ne=e=>{const{prefixCls:t,children:n,href:r}=e,o=ee(e,["prefixCls","children","href"]),{getPrefixCls:a}=d.useContext(y.E_),i=a("breadcrumb",t);return d.createElement(te,Object.assign({},o,{prefixCls:i}),Y(i,o,n,r))};ne.__ANT_BREADCRUMB_ITEM=!0;var re=ne,oe=n(11568),ae=n(14747),ie=n(83262);var le=(0,b.I$)("Breadcrumb",(e=>(e=>{const{componentCls:t,iconCls:n,calc:r}=e;return{[t]:Object.assign(Object.assign({},(0,ae.Wf)(e)),{color:e.itemColor,fontSize:e.fontSize,[n]:{fontSize:e.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:e.linkColor,transition:`color ${e.motionDurationMid}`,padding:`0 ${(0,oe.bf)(e.paddingXXS)}`,borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",marginInline:r(e.marginXXS).mul(-1).equal(),"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover}},(0,ae.Qy)(e)),"li:last-child":{color:e.lastItemColor},[`${t}-separator`]:{marginInline:e.separatorMargin,color:e.separatorColor},[`${t}-link`]:{[`\n          > ${n} + span,\n          > ${n} + a\n        `]:{marginInlineStart:e.marginXXS}},[`${t}-overlay-link`]:{borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",padding:`0 ${(0,oe.bf)(e.paddingXXS)}`,marginInline:r(e.marginXXS).mul(-1).equal(),[`> ${n}`]:{marginInlineStart:e.marginXXS,fontSize:e.fontSizeIcon},"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover,a:{color:e.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},[`&${e.componentCls}-rtl`]:{direction:"rtl"}})}})((0,ie.IX)(e,{}))),(e=>({itemColor:e.colorTextDescription,lastItemColor:e.colorText,iconFontSize:e.fontSize,linkColor:e.colorTextDescription,linkHoverColor:e.colorText,separatorColor:e.colorTextDescription,separatorMargin:e.marginXS}))),ce=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function se(e){const{breadcrumbName:t,children:n}=e,r=ce(e,["breadcrumbName","children"]),o=Object.assign({title:t},r);return n&&(o.menu={items:n.map((e=>{var{breadcrumbName:t}=e,n=ce(e,["breadcrumbName"]);return Object.assign(Object.assign({},n),{title:t})}))}),o}var ue=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const de=e=>{const{prefixCls:t,separator:n="/",style:r,className:o,rootClassName:a,routes:i,items:l,children:c,itemRender:s,params:u={}}=e,f=ue(e,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:m,direction:g,breadcrumb:v}=d.useContext(y.E_);let h;const b=m("breadcrumb",t),[x,C,S]=le(b),w=function(e,t){return(0,d.useMemo)((()=>e||(t?t.map(se):null)),[e,t])}(l,i);const Z=J(b,s);if(w&&w.length>0){const e=[],t=l||i;h=w.map(((r,o)=>{const{path:a,key:i,type:l,menu:c,overlay:s,onClick:f,className:p,separator:m,dropdownProps:g}=r,v=((e,t)=>{if(void 0===t)return t;let n=(t||"").replace(/^\//,"");return Object.keys(e).forEach((t=>{n=n.replace(`:${t}`,e[t])})),n})(u,a);void 0!==v&&e.push(v);const h=null!=i?i:o;if("separator"===l)return d.createElement(K,{key:h},m);const y={},x=o===w.length-1;c?y.menu=c:s&&(y.overlay=s);let{href:C}=r;return e.length&&void 0!==v&&(C=`#/${e.join("/")}`),d.createElement(te,Object.assign({key:h},y,(0,D.Z)(r,{data:!0,aria:!0}),{className:p,dropdownProps:g,href:C,separator:x?"":n,onClick:f,prefixCls:b}),Z(r,u,t,e,C))}))}else if(c){const e=(0,$.Z)(c).length;h=(0,$.Z)(c).map(((t,r)=>{if(!t)return t;const o=r===e-1;return(0,U.Tm)(t,{separator:o?"":n,key:r})}))}const j=p()(b,null==v?void 0:v.className,{[`${b}-rtl`]:"rtl"===g},o,a,C,S),k=Object.assign(Object.assign({},null==v?void 0:v.style),r);return x(d.createElement("nav",Object.assign({className:j,style:k},f),d.createElement("ol",null,h)))};de.Item=re,de.Separator=K;var fe=de,pe=n(68997),me=n(42075),ge=n(80334),ve=function(e){var t;return(0,r.Z)({},e.componentCls,(0,a.Z)((0,a.Z)({},null===M.Wf||void 0===M.Wf?void 0:(0,M.Wf)(e)),{},(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)({position:"relative",backgroundColor:e.colorWhite,paddingBlock:e.pageHeaderPaddingVertical+2,paddingInline:e.pageHeaderPadding,"&&-ghost":{backgroundColor:e.pageHeaderBgGhost},"&-no-children":{height:null===(t=e.layout)||void 0===t||null===(t=t.pageContainer)||void 0===t?void 0:t.paddingBlockPageContainerContent},"&&-has-breadcrumb":{paddingBlockStart:e.pageHeaderPaddingBreadCrumb},"&&-has-footer":{paddingBlockEnd:0},"& &-back":(0,r.Z)({marginInlineEnd:e.margin,fontSize:16,lineHeight:1,"&-button":(0,a.Z)((0,a.Z)({fontSize:16},null===M.Nd||void 0===M.Nd?void 0:(0,M.Nd)(e)),{},{color:e.pageHeaderColorBack,cursor:"pointer"})},"".concat(e.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:0})},"& ".concat("ant","-divider-vertical"),{height:14,marginBlock:0,marginInline:e.marginSM,verticalAlign:"middle"}),"& &-breadcrumb + &-heading",{marginBlockStart:e.marginXS}),"& &-heading",{display:"flex",justifyContent:"space-between","&-left":{display:"flex",alignItems:"center",marginBlock:e.marginXS/2,marginInlineEnd:0,marginInlineStart:0,overflow:"hidden"},"&-title":(0,a.Z)((0,a.Z)({marginInlineEnd:e.marginSM,marginBlockEnd:0,color:e.colorTextHeading,fontWeight:600,fontSize:e.pageHeaderFontSizeHeaderTitle,lineHeight:e.controlHeight+"px"},{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}),{},(0,r.Z)({},"".concat(e.componentCls,"-rlt &"),{marginInlineEnd:0,marginInlineStart:e.marginSM})),"&-avatar":(0,r.Z)({marginInlineEnd:e.marginSM},"".concat(e.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:e.marginSM}),"&-tags":(0,r.Z)({},"".concat(e.componentCls,"-rlt &"),{float:"right"}),"&-sub-title":(0,a.Z)((0,a.Z)({marginInlineEnd:e.marginSM,color:e.colorTextSecondary,fontSize:e.pageHeaderFontSizeHeaderSubTitle,lineHeight:e.lineHeight},{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}),{},(0,r.Z)({},"".concat(e.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:12})),"&-extra":(0,r.Z)((0,r.Z)({marginBlock:e.marginXS/2,marginInlineEnd:0,marginInlineStart:0,whiteSpace:"nowrap","> *":(0,r.Z)({"white-space":"unset"},"".concat(e.componentCls,"-rlt &"),{marginInlineEnd:e.marginSM,marginInlineStart:0})},"".concat(e.componentCls,"-rlt &"),{float:"left"}),"*:first-child",(0,r.Z)({},"".concat(e.componentCls,"-rlt &"),{marginInlineEnd:0}))}),"&-content",{paddingBlockStart:e.pageHeaderPaddingContentPadding}),"&-footer",{marginBlockStart:e.margin}),"&-compact &-heading",{flexWrap:"wrap"}),"&-wide",{maxWidth:1152,margin:"0 auto"}),"&-rtl",{direction:"rtl"})))};var he=function(e,t,n,r){return n&&r?(0,B.jsx)("div",{className:"".concat(e,"-back ").concat(t).trim(),children:(0,B.jsx)("div",{role:"button",onClick:function(e){null==r||r(e)},className:"".concat(e,"-back-button ").concat(t).trim(),"aria-label":"back",children:n})}):null},ye=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"ltr";return void 0!==e.backIcon?e.backIcon:"rtl"===t?(0,B.jsx)(F(),{}):(0,B.jsx)(X(),{})},be=function e(t){return null==t?void 0:t.map((function(t){var n;return(0,ge.ET)(!!t.breadcrumbName,"Route.breadcrumbName is deprecated, please use Route.title instead."),(0,a.Z)((0,a.Z)({},t),{},{breadcrumbName:void 0,children:void 0,title:t.title||t.breadcrumbName},null!==(n=t.children)&&void 0!==n&&n.length?{menu:{items:e(t.children)}}:{})}))},xe=function(e){var t,n=d.useState(!1),o=(0,A.Z)(n,2),i=o[0],l=o[1],c=d.useContext(u.ZP.ConfigContext),s=c.getPrefixCls,f=c.direction,g=e.prefixCls,v=e.style,h=e.footer,y=e.children,b=e.breadcrumb,x=e.breadcrumbRender,C=e.className,S=e.contentWidth,w=e.layout,Z=e.ghost,j=void 0===Z||Z,k=s("page-header",g),P=function(e){return(0,M.Xj)("ProLayoutPageHeader",(function(t){var n=(0,a.Z)((0,a.Z)({},t),{},{componentCls:".".concat(e),pageHeaderBgGhost:"transparent",pageHeaderPadding:16,pageHeaderPaddingVertical:4,pageHeaderPaddingBreadCrumb:t.paddingSM,pageHeaderColorBack:t.colorTextHeading,pageHeaderFontSizeHeaderTitle:t.fontSizeHeading4,pageHeaderFontSizeHeaderSubTitle:14,pageHeaderPaddingContentPadding:t.paddingSM});return[ve(n)]}))}(k),O=P.wrapSSR,_=P.hashId,N=(!b||null!=b&&b.items||null==b||!b.routes||((0,ge.ET)(!1,"The routes of Breadcrumb is deprecated, please use items instead."),b.items=be(b.routes)),null!=b&&b.items?function(e,t){var n;return null!==(n=e.items)&&void 0!==n&&n.length?(0,B.jsx)(fe,(0,a.Z)((0,a.Z)({},e),{},{className:p()("".concat(t,"-breadcrumb"),e.className)})):null}(b,k):null),I=b&&"props"in b,E=null!==(t=null==x?void 0:x((0,a.Z)((0,a.Z)({},e),{},{prefixCls:k}),N))&&void 0!==t?t:N,T=I?b:E,R=p()(k,_,C,(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)({},"".concat(k,"-has-breadcrumb"),!!T),"".concat(k,"-has-footer"),!!h),"".concat(k,"-rtl"),"rtl"===f),"".concat(k,"-compact"),i),"".concat(k,"-wide"),"Fixed"===S&&"top"==w),"".concat(k,"-ghost"),j)),H=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",r=arguments.length>3?arguments[3]:void 0,o=t.title,i=t.avatar,l=t.subTitle,c=t.tags,s=t.extra,u=t.onBack,d="".concat(e,"-heading"),f=o||l||c||s;if(!f)return null;var m=ye(t,n),g=he(e,r,m,u),v=g||i||f;return(0,B.jsxs)("div",{className:d+" "+r,children:[v&&(0,B.jsxs)("div",{className:"".concat(d,"-left ").concat(r).trim(),children:[g,i&&(0,B.jsx)(pe.Z,(0,a.Z)({className:p()("".concat(d,"-avatar"),r,i.className)},i)),o&&(0,B.jsx)("span",{className:"".concat(d,"-title ").concat(r).trim(),title:"string"==typeof o?o:void 0,children:o}),l&&(0,B.jsx)("span",{className:"".concat(d,"-sub-title ").concat(r).trim(),title:"string"==typeof l?l:void 0,children:l}),c&&(0,B.jsx)("span",{className:"".concat(d,"-tags ").concat(r).trim(),children:c})]}),s&&(0,B.jsx)("span",{className:"".concat(d,"-extra ").concat(r).trim(),children:(0,B.jsx)(me.Z,{children:s})})]})}(k,e,f,_),z=y&&function(e,t,n){return(0,B.jsx)("div",{className:"".concat(e,"-content ").concat(n).trim(),children:t})}(k,y,_),L=function(e,t,n){return t?(0,B.jsx)("div",{className:"".concat(e,"-footer ").concat(n).trim(),children:t}):null}(k,h,_);return T||H||L||z?O((0,B.jsx)(m.Z,{onResize:function(e){var t=e.width;return l(t<768)},children:(0,B.jsxs)("div",{className:R,style:v,children:[T,H,z,L]})})):(0,B.jsx)("div",{className:p()(_,["".concat(k,"-no-children")])})},Ce=n(83832),Se=function(e){var t=(0,M.dQ)().token,n=e.children,r=e.style,o=e.className,i=e.markStyle,l=e.markClassName,c=e.zIndex,s=void 0===c?9:c,f=e.gapX,m=void 0===f?212:f,g=e.gapY,v=void 0===g?222:g,h=e.width,y=void 0===h?120:h,b=e.height,x=void 0===b?64:b,C=e.rotate,S=void 0===C?-22:C,w=e.image,Z=e.offsetLeft,j=e.offsetTop,k=e.fontStyle,P=void 0===k?"normal":k,O=e.fontWeight,_=void 0===O?"normal":O,N=e.fontColor,I=void 0===N?t.colorFill:N,E=e.fontSize,T=void 0===E?16:E,R=e.fontFamily,H=void 0===R?"sans-serif":R,z=e.prefixCls,L=(0,(0,d.useContext)(u.ZP.ConfigContext).getPrefixCls)("pro-layout-watermark",z),X=p()("".concat(L,"-wrapper"),o),W=p()(L,l),F=(0,d.useState)(""),$=(0,A.Z)(F,2),D=$[0],U=$[1];return(0,d.useEffect)((function(){var t=document.createElement("canvas"),n=t.getContext("2d"),r=function(e){if(!e)return 1;var t=e.backingStorePixelRatio||e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||1;return(window.devicePixelRatio||1)/t}(n),o="".concat((m+y)*r,"px"),a="".concat((v+x)*r,"px"),i=Z||m/2,l=j||v/2;if(t.setAttribute("width",o),t.setAttribute("height",a),n){n.translate(i*r,l*r),n.rotate(Math.PI/180*Number(S));var c=y*r,s=x*r,u=function(e){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=Number(T)*r;n.font="".concat(P," normal ").concat(_," ").concat(a,"px/").concat(s,"px ").concat(H),n.fillStyle=I,Array.isArray(e)?null==e||e.forEach((function(e,t){return n.fillText(e,0,t*a+o)})):n.fillText(e,0,o?o+a:0),U(t.toDataURL())};if(w){var d=new Image;return d.crossOrigin="anonymous",d.referrerPolicy="no-referrer",d.src=w,void(d.onload=function(){n.drawImage(d,0,0,c,s),U(t.toDataURL()),e.content&&u(e.content,d.height+8)})}e.content&&u(e.content)}else console.error("当前环境不支持Canvas")}),[m,v,Z,j,S,P,_,y,x,H,I,w,e.content,T]),(0,B.jsxs)("div",{style:(0,a.Z)({position:"relative"},r),className:X,children:[n,(0,B.jsx)("div",{className:W,style:(0,a.Z)((0,a.Z)({zIndex:s,position:"absolute",left:0,top:0,width:"100%",height:"100%",backgroundSize:"".concat(m+y,"px"),pointerEvents:"none",backgroundRepeat:"repeat"},D?{backgroundImage:"url('".concat(D,"')")}:{}),i)})]})},we=[576,768,992,1200].map((function(e){return"@media (max-width: ".concat(e,"px)")})),Ze=(0,A.Z)(we,4),je=Ze[0],ke=Ze[1],Pe=Ze[2],Oe=Ze[3],_e=function(e){var t,n,o,a,i,l,c,s,u,d,f,p,m,g,v,h,y,b;return(0,r.Z)({},e.componentCls,(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)({position:"relative","&-children-container":{paddingBlockStart:0,paddingBlockEnd:null===(t=e.layout)||void 0===t||null===(t=t.pageContainer)||void 0===t?void 0:t.paddingBlockPageContainerContent,paddingInline:null===(n=e.layout)||void 0===n||null===(n=n.pageContainer)||void 0===n?void 0:n.paddingInlinePageContainerContent},"&-children-container-no-header":{paddingBlockStart:null===(o=e.layout)||void 0===o||null===(o=o.pageContainer)||void 0===o?void 0:o.paddingBlockPageContainerContent},"&-affix":(0,r.Z)({},"".concat(e.antCls,"-affix"),(0,r.Z)({},"".concat(e.componentCls,"-warp"),{backgroundColor:null===(a=e.layout)||void 0===a||null===(a=a.pageContainer)||void 0===a?void 0:a.colorBgPageContainerFixed,transition:"background-color 0.3s",boxShadow:"0 2px 8px #f0f1f2"}))},"& &-warp-page-header",(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)({paddingBlockStart:(null!==(i=null===(l=e.layout)||void 0===l||null===(l=l.pageContainer)||void 0===l?void 0:l.paddingBlockPageContainerContent)&&void 0!==i?i:40)/4,paddingBlockEnd:(null!==(c=null===(s=e.layout)||void 0===s||null===(s=s.pageContainer)||void 0===s?void 0:s.paddingBlockPageContainerContent)&&void 0!==c?c:40)/2,paddingInlineStart:null===(u=e.layout)||void 0===u||null===(u=u.pageContainer)||void 0===u?void 0:u.paddingInlinePageContainerContent,paddingInlineEnd:null===(d=e.layout)||void 0===d||null===(d=d.pageContainer)||void 0===d?void 0:d.paddingInlinePageContainerContent},"& ~ ".concat(e.proComponentsCls,"-grid-content"),(0,r.Z)({},"".concat(e.proComponentsCls,"-page-container-children-content"),{paddingBlock:(null!==(f=null===(p=e.layout)||void 0===p||null===(p=p.pageContainer)||void 0===p?void 0:p.paddingBlockPageContainerContent)&&void 0!==f?f:24)/3})),"".concat(e.antCls,"-page-header-breadcrumb"),{paddingBlockStart:(null!==(m=null===(g=e.layout)||void 0===g||null===(g=g.pageContainer)||void 0===g?void 0:g.paddingBlockPageContainerContent)&&void 0!==m?m:40)/4+10}),"".concat(e.antCls,"-page-header-heading"),{paddingBlockStart:(null!==(v=null===(h=e.layout)||void 0===h||null===(h=h.pageContainer)||void 0===h?void 0:h.paddingBlockPageContainerContent)&&void 0!==v?v:40)/4}),"".concat(e.antCls,"-page-header-footer"),{marginBlockStart:(null!==(y=null===(b=e.layout)||void 0===b||null===(b=b.pageContainer)||void 0===b?void 0:b.paddingBlockPageContainerContent)&&void 0!==y?y:40)/4})),"&-detail",(0,r.Z)({display:"flex"},je,{display:"block"})),"&-main",{width:"100%"}),"&-row",(0,r.Z)({display:"flex",width:"100%"},ke,{display:"block"})),"&-content",{flex:"auto",width:"100%"}),"&-extraContent",(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)({flex:"0 1 auto",minWidth:"242px",marginInlineStart:88,textAlign:"end"},Oe,{marginInlineStart:44}),Pe,{marginInlineStart:20}),ke,{marginInlineStart:0,textAlign:"start"}),je,{marginInlineStart:0})))};var Ne=n(1977),Ie=["title","content","pageHeaderRender","header","prefixedClassName","extraContent","childrenContentStyle","style","prefixCls","hashId","value","breadcrumbRender"],Ee=["children","loading","className","style","footer","affixProps","token","fixedHeader","breadcrumbRender","footerToolBarProps","childrenContentStyle"];var Me=function(e){var t=e.tabList,n=e.tabActiveKey,r=e.onTabChange,o=e.hashId,i=e.tabBarExtraContent,l=e.tabProps,u=e.prefixedClassName;return Array.isArray(t)||i?(0,B.jsx)(c.Z,(0,a.Z)((0,a.Z)({className:"".concat(u,"-tabs ").concat(o).trim(),activeKey:n,onChange:function(e){r&&r(e)},tabBarExtraContent:i,items:null==t?void 0:t.map((function(e,t){var n;return(0,a.Z)((0,a.Z)({label:e.tab},e),{},{key:(null===(n=e.key)||void 0===n?void 0:n.toString())||(null==t?void 0:t.toString())})}))},l),{},{children:(0,Ne.n)(s.Z,"4.23.0")<0?null==t?void 0:t.map((function(e,t){return(0,B.jsx)(c.Z.TabPane,(0,a.Z)({tab:e.tab},e),e.key||t)})):null})):null},Te=function(e,t,n,r){return e||t?(0,B.jsx)("div",{className:"".concat(n,"-detail ").concat(r).trim(),children:(0,B.jsx)("div",{className:"".concat(n,"-main ").concat(r).trim(),children:(0,B.jsxs)("div",{className:"".concat(n,"-row ").concat(r).trim(),children:[e&&(0,B.jsx)("div",{className:"".concat(n,"-content ").concat(r).trim(),children:e}),t&&(0,B.jsx)("div",{className:"".concat(n,"-extraContent ").concat(r).trim(),children:t})]})})}):null},Be=function(e){var t,n=e.title,r=e.content,i=e.pageHeaderRender,l=e.header,c=e.prefixedClassName,s=e.extraContent,u=(e.childrenContentStyle,e.style,e.prefixCls),d=e.hashId,f=e.value,p=e.breadcrumbRender,m=(0,o.Z)(e,Ie);if(!1===i)return null;if(i)return(0,B.jsxs)(B.Fragment,{children:[" ",i((0,a.Z)((0,a.Z)({},e),f))]});var g=n;n||!1===n||(g=f.title);var v=(0,a.Z)((0,a.Z)((0,a.Z)({},f),{},{title:g},m),{},{footer:Me((0,a.Z)((0,a.Z)({},m),{},{hashId:d,breadcrumbRender:p,prefixedClassName:c}))},l),h=v.breadcrumb,y=!(h&&(null!=h&&h.itemRender||null!=h&&null!==(t=h.items)&&void 0!==t&&t.length)||p);return["title","subTitle","extra","tags","footer","avatar","backIcon"].every((function(e){return!v[e]}))&&y&&!r&&!s?null:(0,B.jsx)(xe,(0,a.Z)((0,a.Z)({},v),{},{className:"".concat(c,"-warp-page-header ").concat(d).trim(),breadcrumb:!1===p?void 0:(0,a.Z)((0,a.Z)({},v.breadcrumb),f.breadcrumbProps),breadcrumbRender:function(){if(p)return p}(),prefixCls:u,children:(null==l?void 0:l.children)||Te(r,s,c,d)}))},Re=function(e){var t,n,c=e.children,s=e.loading,f=void 0!==s&&s,m=e.className,g=e.style,v=e.footer,h=e.affixProps,y=e.token,b=e.fixedHeader,x=e.breadcrumbRender,C=e.footerToolBarProps,S=e.childrenContentStyle,w=(0,o.Z)(e,Ee),Z=(0,d.useContext)(_.X);(0,d.useEffect)((function(){var e;return Z&&null!=Z&&Z.setHasPageContainer?(null==Z||null===(e=Z.setHasPageContainer)||void 0===e||e.call(Z,(function(e){return e+1})),function(){var e;null==Z||null===(e=Z.setHasPageContainer)||void 0===e||e.call(Z,(function(e){return e-1}))}):function(){}}),[]);var j=(0,d.useContext)(l.L_).token,k=(0,d.useContext)(u.ZP.ConfigContext).getPrefixCls,P=e.prefixCls||k("pro"),N="".concat(P,"-page-container"),I=function(e,t){return(0,M.Xj)("ProLayoutPageContainer",(function(n){var r,o=(0,a.Z)((0,a.Z)({},n),{},{componentCls:".".concat(e),layout:(0,a.Z)((0,a.Z)({},null==n?void 0:n.layout),{},{pageContainer:(0,a.Z)((0,a.Z)({},null==n||null===(r=n.layout)||void 0===r?void 0:r.pageContainer),t)})});return[_e(o)]}))}(N,y),E=I.wrapSSR,T=I.hashId,R=function(e,t){var n=t.stylish;return(0,M.Xj)("ProLayoutPageContainerStylish",(function(t){var o=(0,a.Z)((0,a.Z)({},t),{},{componentCls:".".concat(e)});return n?[(0,r.Z)({},"div".concat(o.componentCls),null==n?void 0:n(o))]:[]}))}("".concat(N,".").concat(N,"-stylish"),{stylish:e.stylish}),A=(0,d.useMemo)((function(){var e;return 0!=x&&(x||(null==w||null===(e=w.header)||void 0===e?void 0:e.breadcrumbRender))}),[x,null==w||null===(t=w.header)||void 0===t?void 0:t.breadcrumbRender]),L=Be((0,a.Z)((0,a.Z)({},w),{},{breadcrumbRender:A,ghost:!0,hashId:T,prefixCls:void 0,prefixedClassName:N,value:Z})),X=(0,d.useMemo)((function(){if(d.isValidElement(f))return f;if("boolean"==typeof f&&!f)return null;var e=function(e){return"object"===(0,i.Z)(e)?e:{spinning:e}}(f);return e.spinning?(0,B.jsx)(Ce.S,(0,a.Z)({},e)):null}),[f]),W=(0,d.useMemo)((function(){return c?(0,B.jsx)(B.Fragment,{children:(0,B.jsx)("div",{className:p()(T,"".concat(N,"-children-container"),(0,r.Z)({},"".concat(N,"-children-container-no-header"),!L)),style:S,children:c})}):null}),[c,N,S,T]),F=(0,d.useMemo)((function(){var t=X||W;if(e.waterMarkProps||Z.waterMarkProps){var n=(0,a.Z)((0,a.Z)({},Z.waterMarkProps),e.waterMarkProps);return(0,B.jsx)(Se,(0,a.Z)((0,a.Z)({},n),{},{children:t}))}return t}),[e.waterMarkProps,Z.waterMarkProps,X,W]),$=p()(N,T,m,(0,r.Z)((0,r.Z)((0,r.Z)({},"".concat(N,"-with-footer"),v),"".concat(N,"-with-affix"),b&&L),"".concat(N,"-stylish"),!!w.stylish));return E(R.wrapSSR((0,B.jsxs)(B.Fragment,{children:[(0,B.jsxs)("div",{style:g,className:$,children:[b&&L?(0,B.jsx)(O,(0,a.Z)((0,a.Z)({offsetTop:Z.hasHeader&&Z.fixedHeader?null===(n=j.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.heightLayoutHeader:1},h),{},{className:"".concat(N,"-affix ").concat(T).trim(),children:(0,B.jsx)("div",{className:"".concat(N,"-warp ").concat(T).trim(),children:L})})):L,F&&(0,B.jsx)(z.f,{children:F})]}),v&&(0,B.jsx)(H,(0,a.Z)((0,a.Z)({stylish:w.footerStylish,prefixCls:P},C),{},{children:v}))]})))},He=function(e){return(0,B.jsx)(l._Y,{needDeps:!0,children:(0,B.jsx)(Re,(0,a.Z)({},e))})}},83832:function(e,t,n){"use strict";n.d(t,{S:function(){return c}});var r=n(1413),o=n(91),a=n(74330),i=(n(67294),n(85893)),l=["isLoading","pastDelay","timedOut","error","retry"],c=function(e){e.isLoading,e.pastDelay,e.timedOut,e.error,e.retry;var t=(0,o.Z)(e,l);return(0,i.jsx)("div",{style:{paddingBlockStart:100,textAlign:"center"},children:(0,i.jsx)(a.Z,(0,r.Z)({size:"large"},t))})}},76509:function(e,t,n){"use strict";n.d(t,{X:function(){return r}});var r=(0,n(67294).createContext)({})},3770:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r;const o=(r=n(27863))&&r.__esModule?r:{default:r};t.default=o,e.exports=o},77059:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r;const o=(r=n(21379))&&r.__esModule?r:{default:r};t.default=o,e.exports=o},33046:function(e,t,n){"use strict";var r=n(64836).default,o=n(75263).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n(10434)),i=r(n(27424)),l=r(n(38416)),c=r(n(70215)),s=o(n(67294)),u=r(n(93967)),d=n(87646),f=r(n(61711)),p=r(n(27727)),m=n(26814),g=n(72014),v=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];(0,m.setTwoToneColor)(d.blue.primary);var h=s.forwardRef((function(e,t){var n=e.className,r=e.icon,o=e.spin,d=e.rotate,m=e.tabIndex,h=e.onClick,y=e.twoToneColor,b=(0,c.default)(e,v),x=s.useContext(f.default),C=x.prefixCls,S=void 0===C?"anticon":C,w=x.rootClassName,Z=(0,u.default)(w,S,(0,l.default)((0,l.default)({},"".concat(S,"-").concat(r.name),!!r.name),"".concat(S,"-spin"),!!o||"loading"===r.name),n),j=m;void 0===j&&h&&(j=-1);var k=d?{msTransform:"rotate(".concat(d,"deg)"),transform:"rotate(".concat(d,"deg)")}:void 0,P=(0,g.normalizeTwoToneColors)(y),O=(0,i.default)(P,2),_=O[0],N=O[1];return s.createElement("span",(0,a.default)({role:"img","aria-label":r.name},b,{ref:t,tabIndex:j,onClick:h,className:Z}),s.createElement(p.default,{icon:r,primaryColor:_,secondaryColor:N,style:k}))}));h.displayName="AntdIcon",h.getTwoToneColor=m.getTwoToneColor,h.setTwoToneColor=m.setTwoToneColor;t.default=h},61711:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=(0,n(67294).createContext)({});t.default=r},27727:function(e,t,n){"use strict";var r=n(64836).default,o=n(75263).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n(70215)),i=r(n(42122)),l=o(n(67294)),c=n(72014),s=["icon","className","onClick","style","primaryColor","secondaryColor"],u={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};var d=function(e){var t=e.icon,n=e.className,r=e.onClick,o=e.style,d=e.primaryColor,f=e.secondaryColor,p=(0,a.default)(e,s),m=l.useRef(),g=u;if(d&&(g={primaryColor:d,secondaryColor:f||(0,c.getSecondaryColor)(d)}),(0,c.useInsertStyles)(m),(0,c.warning)((0,c.isIconDefinition)(t),"icon should be icon definiton, but got ".concat(t)),!(0,c.isIconDefinition)(t))return null;var v=t;return v&&"function"==typeof v.icon&&(v=(0,i.default)((0,i.default)({},v),{},{icon:v.icon(g.primaryColor,g.secondaryColor)})),(0,c.generate)(v.icon,"svg-".concat(v.name),(0,i.default)((0,i.default)({className:n,onClick:r,style:o,"data-icon":v.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},p),{},{ref:m}))};d.displayName="IconReact",d.getTwoToneColors=function(){return(0,i.default)({},u)},d.setTwoToneColors=function(e){var t=e.primaryColor,n=e.secondaryColor;u.primaryColor=t,u.secondaryColor=n||(0,c.getSecondaryColor)(t),u.calculated=!!n};t.default=d},26814:function(e,t,n){"use strict";var r=n(64836).default;Object.defineProperty(t,"__esModule",{value:!0}),t.getTwoToneColor=function(){var e=a.default.getTwoToneColors();if(!e.calculated)return e.primaryColor;return[e.primaryColor,e.secondaryColor]},t.setTwoToneColor=function(e){var t=(0,i.normalizeTwoToneColors)(e),n=(0,o.default)(t,2),r=n[0],l=n[1];return a.default.setTwoToneColors({primaryColor:r,secondaryColor:l})};var o=r(n(27424)),a=r(n(27727)),i=n(72014)},27863:function(e,t,n){"use strict";var r=n(75263).default,o=n(64836).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(10434)),i=r(n(67294)),l=o(n(47356)),c=o(n(33046)),s=function(e,t){return i.createElement(c.default,(0,a.default)({},e,{ref:t,icon:l.default}))},u=i.forwardRef(s);t.default=u},21379:function(e,t,n){"use strict";var r=n(75263).default,o=n(64836).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(10434)),i=r(n(67294)),l=o(n(44149)),c=o(n(33046)),s=function(e,t){return i.createElement(c.default,(0,a.default)({},e,{ref:t,icon:l.default}))},u=i.forwardRef(s);t.default=u},72014:function(e,t,n){"use strict";var r=n(75263).default,o=n(64836).default;Object.defineProperty(t,"__esModule",{value:!0}),t.generate=function e(t,n,r){if(!r)return d.default.createElement(t.tag,(0,a.default)({key:n},m(t.attrs)),(t.children||[]).map((function(r,o){return e(r,"".concat(n,"-").concat(t.tag,"-").concat(o))})));return d.default.createElement(t.tag,(0,a.default)((0,a.default)({key:n},m(t.attrs)),r),(t.children||[]).map((function(r,o){return e(r,"".concat(n,"-").concat(t.tag,"-").concat(o))})))},t.getSecondaryColor=function(e){return(0,l.generate)(e)[0]},t.iconStyles=void 0,t.isIconDefinition=function(e){return"object"===(0,i.default)(e)&&"string"==typeof e.name&&"string"==typeof e.theme&&("object"===(0,i.default)(e.icon)||"function"==typeof e.icon)},t.normalizeAttrs=m,t.normalizeTwoToneColors=function(e){if(!e)return[];return Array.isArray(e)?e:[e]},t.useInsertStyles=t.svgBaseProps=void 0,t.warning=function(e,t){(0,u.default)(e,"[@ant-design/icons] ".concat(t))};var a=o(n(42122)),i=o(n(18698)),l=n(87646),c=n(93399),s=n(63298),u=o(n(45520)),d=r(n(67294)),f=o(n(61711));function p(e){return e.replace(/-(.)/g,(function(e,t){return t.toUpperCase()}))}function m(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(e).reduce((function(t,n){var r=e[n];if("class"===n)t.className=r,delete t.class;else delete t[n],t[p(n)]=r;return t}),{})}t.svgBaseProps={width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",focusable:"false"};var g=t.iconStyles="\n.anticon {\n  display: inline-flex;\n  align-items: center;\n  color: inherit;\n  font-style: normal;\n  line-height: 0;\n  text-align: center;\n  text-transform: none;\n  vertical-align: -0.125em;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.anticon > * {\n  line-height: 1;\n}\n\n.anticon svg {\n  display: inline-block;\n}\n\n.anticon::before {\n  display: none;\n}\n\n.anticon .anticon-icon {\n  display: block;\n}\n\n.anticon[tabindex] {\n  cursor: pointer;\n}\n\n.anticon-spin::before,\n.anticon-spin {\n  display: inline-block;\n  -webkit-animation: loadingCircle 1s infinite linear;\n  animation: loadingCircle 1s infinite linear;\n}\n\n@-webkit-keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n";t.useInsertStyles=function(e){var t=(0,d.useContext)(f.default),n=t.csp,r=t.prefixCls,o=t.layer,a=g;r&&(a=a.replace(/anticon/g,r)),o&&(a="@layer ".concat(o," {\n").concat(a,"\n}")),(0,d.useEffect)((function(){var t=e.current,r=(0,s.getShadowRoot)(t);(0,c.updateCSS)(a,"@ant-design-icons",{prepend:!o,csp:n,attachTo:r})}),[])}},19158:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return!("undefined"==typeof window||!window.document||!window.document.createElement)}},32191:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(!e)return!1;if(e.contains)return e.contains(t);var n=t;for(;n;){if(n===e)return!0;n=n.parentNode}return!1}},93399:function(e,t,n){"use strict";var r=n(64836).default;Object.defineProperty(t,"__esModule",{value:!0}),t.clearContainerCache=function(){u.clear()},t.injectCSS=g,t.removeCSS=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=v(e,t);if(n){var r=f(t);r.removeChild(n)}},t.updateCSS=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=f(n),a=m(r),i=(0,o.default)((0,o.default)({},n),{},{styles:a});h(r,i);var l=v(t,i);if(l){var c,s,u;if(null!==(c=i.csp)&&void 0!==c&&c.nonce&&l.nonce!==(null===(s=i.csp)||void 0===s?void 0:s.nonce))l.nonce=null===(u=i.csp)||void 0===u?void 0:u.nonce;return l.innerHTML!==e&&(l.innerHTML=e),l}var p=g(e,i);return p.setAttribute(d(i),t),p};var o=r(n(42122)),a=r(n(19158)),i=r(n(32191)),l="data-rc-order",c="data-rc-priority",s="rc-util-key",u=new Map;function d(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.mark;return t?t.startsWith("data-")?t:"data-".concat(t):s}function f(e){return e.attachTo?e.attachTo:document.querySelector("head")||document.body}function p(e){return"queue"===e?"prependQueue":e?"prepend":"append"}function m(e){return Array.from((u.get(e)||e).children).filter((function(e){return"STYLE"===e.tagName}))}function g(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(0,a.default)())return null;var n=t.csp,r=t.prepend,o=t.priority,i=void 0===o?0:o,s=p(r),u="prependQueue"===s,d=document.createElement("style");d.setAttribute(l,s),u&&i&&d.setAttribute(c,"".concat(i)),null!=n&&n.nonce&&(d.nonce=null==n?void 0:n.nonce),d.innerHTML=e;var g=f(t),v=g.firstChild;if(r){if(u){var h=(t.styles||m(g)).filter((function(e){if(!["prepend","prependQueue"].includes(e.getAttribute(l)))return!1;var t=Number(e.getAttribute(c)||0);return i>=t}));if(h.length)return g.insertBefore(d,h[h.length-1].nextSibling),d}g.insertBefore(d,v)}else g.appendChild(d);return d}function v(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=f(t);return(t.styles||m(n)).find((function(n){return n.getAttribute(d(t))===e}))}function h(e,t){var n=u.get(e);if(!n||!(0,i.default)(document,n)){var r=g("",t),o=r.parentNode;u.set(e,o),e.removeChild(r)}}},63298:function(e,t){"use strict";function n(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}function r(e){return n(e)instanceof ShadowRoot}Object.defineProperty(t,"__esModule",{value:!0}),t.getShadowRoot=function(e){return r(e)?n(e):null},t.inShadow=r},45520:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.call=c,t.default=void 0,t.note=i,t.noteOnce=u,t.preMessage=void 0,t.resetWarned=l,t.warning=a,t.warningOnce=s;var n={},r=[],o=t.preMessage=function(e){r.push(e)};function a(e,t){}function i(e,t){}function l(){n={}}function c(e,t,r){t||n[r]||(e(!1,r),n[r]=!0)}function s(e,t){c(a,e,t)}function u(e,t){c(i,e,t)}s.preMessage=o,s.resetWarned=l,s.noteOnce=u;t.default=s},73897:function(e){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r},e.exports.__esModule=!0,e.exports.default=e.exports},85372:function(e){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},38416:function(e,t,n){var r=n(64062);e.exports=function(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},10434:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},64836:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},75263:function(e,t,n){var r=n(18698).default;function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(o=function(e){return e?n:t})(e)}e.exports=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=r(e)&&"function"!=typeof e)return{default:e};var n=o(t);if(n&&n.has(e))return n.get(e);var a={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var c=i?Object.getOwnPropertyDescriptor(e,l):null;c&&(c.get||c.set)?Object.defineProperty(a,l,c):a[l]=e[l]}return a.default=e,n&&n.set(e,a),a},e.exports.__esModule=!0,e.exports.default=e.exports},68872:function(e){e.exports=function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,o=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw o}}return l}},e.exports.__esModule=!0,e.exports.default=e.exports},12218:function(e){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},42122:function(e,t,n){var r=n(38416);function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}e.exports=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e},e.exports.__esModule=!0,e.exports.default=e.exports},70215:function(e,t,n){var r=n(7071);e.exports=function(e,t){if(null==e)return{};var n,o,a=r(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a},e.exports.__esModule=!0,e.exports.default=e.exports},7071:function(e){e.exports=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n},e.exports.__esModule=!0,e.exports.default=e.exports},27424:function(e,t,n){var r=n(85372),o=n(68872),a=n(86116),i=n(12218);e.exports=function(e,t){return r(e)||o(e,t)||a(e,t)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},95036:function(e,t,n){var r=n(18698).default;e.exports=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},64062:function(e,t,n){var r=n(18698).default,o=n(95036);e.exports=function(e){var t=o(e,"string");return"symbol"==r(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},18698:function(e){function t(n){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},86116:function(e,t,n){var r=n(73897);e.exports=function(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);