"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8258],{58258:function(e,n,t){t.d(n,{$V:function(){return M},Bp:function(){return _},CH:function(){return v},DY:function(){return i},E:function(){return R},E4:function(){return m},IV:function(){return N},L9:function(){return G},Pq:function(){return J},SJ:function(){return Z},SS:function(){return z},SZ:function(){return U},Vk:function(){return V},_I:function(){return k},au:function(){return C},bV:function(){return s},dx:function(){return D},e_:function(){return x},ki:function(){return P},l$:function(){return S},pj:function(){return d},yN:function(){return h}});var r=t(15009),a=t.n(r),u=t(99289),o=t.n(u),c=t(78158);function i(){return p.apply(this,arguments)}function p(){return(p=o()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/embeddingsList",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function s(e,n,t,r,a,u){return f.apply(this,arguments)}function f(){return(f=o()(a()().mark((function e(n,t,r,u,o,i){var p,s;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return p=new FormData,console.log("KnowledgeId",n),p.append("knowledgeId",n),p.append("ocr",t.toString()),p.append("parentId",u),p.append("parserType",o),p.append("files_source_type",i||""),r.forEach((function(e){p.append("files",e)})),e.prev=8,e.next=11,(0,c.N)("/api/updateKnowledgeFile",{method:"POST",data:p,requestType:"form"});case 11:return e.abrupt("return",e.sent);case 14:if(e.prev=14,e.t0=e.catch(8),404!==(null===(s=e.t0.response)||void 0===s?void 0:s.status)){e.next=19;break}throw console.error("上传端点不存在 (404):",e.t0),new Error("上传服务不可用，请联系管理员");case 19:throw console.error("文件上传失败:",e.t0),e.t0;case 21:case"end":return e.stop()}}),e,null,[[8,14]])})))).apply(this,arguments)}function d(e,n,t,r,a,u){return l.apply(this,arguments)}function l(){return(l=o()(a()().mark((function e(n,t,r,u,o,i){var p,s;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return p=new FormData,console.log("KnowledgeId",n),p.append("knowledgeId",n),p.append("ocr",t.toString()),p.append("parentId",u),p.append("parserType",o),p.append("files_source_type",i||""),r.forEach((function(e){p.append("files",e)})),e.prev=8,e.next=11,(0,c.N)("/api/updateKnowledgeFileFromChat",{method:"POST",data:p,requestType:"form"});case 11:return e.abrupt("return",e.sent);case 14:if(e.prev=14,e.t0=e.catch(8),404!==(null===(s=e.t0.response)||void 0===s?void 0:s.status)){e.next=19;break}throw console.error("上传端点不存在 (404):",e.t0),new Error("上传服务不可用，请联系管理员");case 19:throw console.error("文件上传失败:",e.t0),e.t0;case 21:case"end":return e.stop()}}),e,null,[[8,14]])})))).apply(this,arguments)}function h(e){return w.apply(this,arguments)}function w(){return w=o()(a()().mark((function e(n){var t,r,u=arguments;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=!(u.length>1&&void 0!==u[1])||u[1],r=u.length>2?u[2]:void 0,e.abrupt("return",(0,c.N)("/api/knowledge_file_preview/".concat(n),{method:"GET",params:{download:t},responseType:"blob"}).then((function(e){var n,a=null===(n=e.headers)||void 0===n?void 0:n.get("content-disposition"),u=r||"download";if(!r&&a){var o=a.match(/filename\*=UTF-8''([^;]+)/i);if(o)u=decodeURIComponent(o[1]);else{var c=a.match(/filename="([^"]+)"/i);c&&(u=c[1])}}if(t){var i=new Blob([e],{type:e.type}),p=window.URL.createObjectURL(i),s=document.createElement("a");s.href=p,s.download=u,document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(p),document.body.removeChild(s)}else{var f=new Blob([e],{type:e.type}),d=window.URL.createObjectURL(f),l=document.createElement("iframe");l.src=d,l.style.display="none",document.body.appendChild(l),window.URL.revokeObjectURL(d),document.body.removeChild(l)}return e})));case 3:case"end":return e.stop()}}),e)}))),w.apply(this,arguments)}function m(e){return y.apply(this,arguments)}function y(){return(y=o()(a()().mark((function e(n){var t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t="/api/knowledge_file_preview/".concat(n),e.abrupt("return",fetch(t));case 2:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function v(e,n){return b.apply(this,arguments)}function b(){return(b=o()(a()().mark((function e(n,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_base/".concat(n),{method:"PUT",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function k(e){return g.apply(this,arguments)}function g(){return(g=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/source_files/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var x=function(){var e=o()(a()().mark((function e(n,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,fetch("/api/renameFile/".concat(n),{method:"POST",body:JSON.stringify({newName:t}),headers:{"Content-Type":"application/json"}});case 2:if(e.sent.ok){e.next=5;break}throw new Error("重命名失败");case 5:case"end":return e.stop()}}),e)})));return function(n,t){return e.apply(this,arguments)}}();function _(e){return T.apply(this,arguments)}function T(){return(T=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_bases/".concat(n),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function N(e,n){return E.apply(this,arguments)}function E(){return(E=o()(a()().mark((function e(n,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_base/".concat(n),{method:"PUT",data:{tags:t}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function S(e,n,t,r,a){return O.apply(this,arguments)}function O(){return(O=o()(a()().mark((function e(n,t,r,u,o){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,c.N)("/api/updateKnowledgeFolder",{method:"POST",data:{action:n,folder_name:t,knowledgeId:r,parentId:u,folder_id:o}});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function P(e,n){return L.apply(this,arguments)}function L(){return(L=o()(a()().mark((function e(n,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,c.N)("/api/updateKnowledgeTags",{method:"POST",data:{file_id:n,tags:t}});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function U(e,n){return I.apply(this,arguments)}function I(){return(I=o()(a()().mark((function e(n,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,c.N)("/api/updateKnowledgeMove",{method:"POST",data:{file_id:n,parent_id:t}});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function C(e,n){return F.apply(this,arguments)}function F(){return(F=o()(a()().mark((function e(n,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,c.N)("/api/updateKnowledgeForbid",{method:"POST",data:{file_id:n,forbidden:t}});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function R(e,n,t,r,a,u,o){return K.apply(this,arguments)}function K(){return(K=o()(a()().mark((function e(n,t,r,u,o,i,p){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,c.N)("/api/knowledge_folder_tree/".concat(n),{method:"POST",data:{name:t,tags:r,only_folder:u,forbidden:o,data_type:i,flg:p}});case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function G(e){return j.apply(this,arguments)}function j(){return(j=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/source_files/reset/".concat(n),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function D(e,n,t,r,a,u){return q.apply(this,arguments)}function q(){return(q=o()(a()().mark((function e(n,t,r,u,o,i){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge-base/chunk/".concat(n),{method:"GET",params:{page:t,pageSize:r,file_name:u,answer:o,question:i}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function V(e){return B.apply(this,arguments)}function B(){return(B=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge-base/chunk/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function J(e){return $.apply(this,arguments)}function $(){return($=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge-base/chunk/batchDelete",{method:"POST",data:{ids:n}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function z(e){return H.apply(this,arguments)}function H(){return(H=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_base/search",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function M(e,n){return Y.apply(this,arguments)}function Y(){return(Y=o()(a()().mark((function e(n,t){var r;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r={max_depth:(null==t?void 0:t.max_depth)||50,limit:(null==t?void 0:t.limit)||25,format:(null==t?void 0:t.format)||"echarts"},e.abrupt("return",(0,c.N)("/api/wisegraph/knowledge-base/".concat(n,"/graph"),{method:"GET",params:r}));case 2:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Z(e){return A.apply(this,arguments)}function A(){return(A=o()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/knowledge_bases/".concat(n),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}}]);