"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[523],{47046:function(e,o){o.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},82061:function(e,o,r){var t=r(1413),n=r(67294),a=r(47046),l=r(91146),c=function(e,o){return n.createElement(l.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:o,icon:a.Z}))},s=n.forwardRef(c);o.Z=s},47389:function(e,o,r){var t=r(1413),n=r(67294),a=r(27363),l=r(91146),c=function(e,o){return n.createElement(l.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:o,icon:a.Z}))},s=n.forwardRef(c);o.Z=s},49354:function(e,o,r){r.d(o,{Z:function(){return s}});var t=r(1413),n=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M531.3 574.4l.3-1.4c5.8-23.9 13.1-53.7 7.4-80.7-3.8-21.3-19.5-29.6-32.9-30.2-15.8-.7-29.9 8.3-33.4 21.4-6.6 24-.7 56.8 10.1 98.6-13.6 32.4-35.3 79.5-51.2 107.5-29.6 15.3-69.3 38.9-75.2 68.7-1.2 5.5.2 12.5 3.5 18.8 3.7 7 9.6 12.4 16.5 15 3 1.1 6.6 2 10.8 2 17.6 0 46.1-14.2 84.1-79.4 5.8-1.9 11.8-3.9 17.6-5.9 27.2-9.2 55.4-18.8 80.9-23.1 28.2 15.1 60.3 24.8 82.1 24.8 21.6 0 30.1-12.8 33.3-20.5 5.6-13.5 2.9-30.5-6.2-39.6-13.2-13-45.3-16.4-95.3-10.2-24.6-15-40.7-35.4-52.4-65.8zM421.6 726.3c-13.9 20.2-24.4 30.3-30.1 34.7 6.7-12.3 19.8-25.3 30.1-34.7zm87.6-235.5c5.2 8.9 4.5 35.8.5 49.4-4.9-19.9-5.6-48.1-2.7-51.4.8.1 1.5.7 2.2 2zm-1.6 120.5c10.7 18.5 24.2 34.4 39.1 46.2-21.6 4.9-41.3 13-58.9 20.2-4.2 1.7-8.3 3.4-12.3 5 13.3-24.1 24.4-51.4 32.1-71.4zm155.6 65.5c.1.2.2.5-.4.9h-.2l-.2.3c-.8.5-9 5.3-44.3-8.6 40.6-1.9 45 7.3 45.1 7.4zm191.4-388.2L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file-pdf",theme:"outlined"},l=r(91146),c=function(e,o){return n.createElement(l.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:o,icon:a}))};var s=n.forwardRef(c)},26058:function(e,o,r){r.d(o,{Z:function(){return k}});var t=r(74902),n=r(67294),a=r(93967),l=r.n(a),c=r(98423),s=r(53124),i=r(82401),d=r(50344),u=r(70985);var f=r(24793),g=function(e,o){var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(r[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(t=Object.getOwnPropertySymbols(e);n<t.length;n++)o.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(e,t[n])&&(r[t[n]]=e[t[n]])}return r};function m(e){let{suffixCls:o,tagName:r,displayName:t}=e;return e=>n.forwardRef(((t,a)=>n.createElement(e,Object.assign({ref:a,suffixCls:o,tagName:r},t))))}const p=n.forwardRef(((e,o)=>{const{prefixCls:r,suffixCls:t,className:a,tagName:c}=e,i=g(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:d}=n.useContext(s.E_),u=d("layout",r),[m,p,b]=(0,f.ZP)(u),C=t?`${u}-${t}`:u;return m(n.createElement(c,Object.assign({className:l()(r||C,a,p,b),ref:o},i)))})),b=n.forwardRef(((e,o)=>{const{direction:r}=n.useContext(s.E_),[a,m]=n.useState([]),{prefixCls:p,className:b,rootClassName:C,children:h,hasSider:v,tagName:y,style:x}=e,k=g(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),O=(0,c.Z)(k,["suffixCls"]),{getPrefixCls:$,className:N,style:S}=(0,s.dj)("layout"),w=$("layout",p),Z=function(e,o,r){return"boolean"==typeof r?r:!!e.length||(0,d.Z)(o).some((e=>e.type===u.Z))}(a,h,v),[j,E,P]=(0,f.ZP)(w),z=l()(w,{[`${w}-has-sider`]:Z,[`${w}-rtl`]:"rtl"===r},N,b,C,E,P),H=n.useMemo((()=>({siderHook:{addSider:e=>{m((o=>[].concat((0,t.Z)(o),[e])))},removeSider:e=>{m((o=>o.filter((o=>o!==e))))}}})),[]);return j(n.createElement(i.V.Provider,{value:H},n.createElement(y,Object.assign({ref:o,className:z,style:Object.assign(Object.assign({},S),x)},O),h)))})),C=m({tagName:"div",displayName:"Layout"})(b),h=m({suffixCls:"header",tagName:"header",displayName:"Header"})(p),v=m({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(p),y=m({suffixCls:"content",tagName:"main",displayName:"Content"})(p);const x=C;x.Header=h,x.Footer=v,x.Content=y,x.Sider=u.Z,x._InternalSiderContext=u.D;var k=x},66309:function(e,o,r){r.d(o,{Z:function(){return E}});var t=r(67294),n=r(93967),a=r.n(n),l=r(98423),c=r(98787),s=r(69760),i=r(96159),d=r(45353),u=r(53124),f=r(11568),g=r(15063),m=r(14747),p=r(83262),b=r(83559);const C=e=>{const{lineWidth:o,fontSizeIcon:r,calc:t}=e,n=e.fontSizeSM;return(0,p.IX)(e,{tagFontSize:n,tagLineHeight:(0,f.bf)(t(e.lineHeightSM).mul(n).equal()),tagIconSize:t(r).sub(t(o).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},h=e=>({defaultBg:new g.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var v=(0,b.I$)("Tag",(e=>(e=>{const{paddingXXS:o,lineWidth:r,tagPaddingHorizontal:t,componentCls:n,calc:a}=e,l=a(t).sub(r).equal(),c=a(o).sub(r).equal();return{[n]:Object.assign(Object.assign({},(0,m.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,f.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${n}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${n}-close-icon`]:{marginInlineStart:c,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${n}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${n}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${n}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(C(e))),h),y=function(e,o){var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(r[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(t=Object.getOwnPropertySymbols(e);n<t.length;n++)o.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(e,t[n])&&(r[t[n]]=e[t[n]])}return r};const x=t.forwardRef(((e,o)=>{const{prefixCls:r,style:n,className:l,checked:c,onChange:s,onClick:i}=e,d=y(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:f,tag:g}=t.useContext(u.E_),m=f("tag",r),[p,b,C]=v(m),h=a()(m,`${m}-checkable`,{[`${m}-checkable-checked`]:c},null==g?void 0:g.className,l,b,C);return p(t.createElement("span",Object.assign({},d,{ref:o,style:Object.assign(Object.assign({},n),null==g?void 0:g.style),className:h,onClick:e=>{null==s||s(!c),null==i||i(e)}})))}));var k=x,O=r(98719);var $=(0,b.bk)(["Tag","preset"],(e=>(e=>(0,O.Z)(e,((o,r)=>{let{textColor:t,lightBorderColor:n,lightColor:a,darkColor:l}=r;return{[`${e.componentCls}${e.componentCls}-${o}`]:{color:t,background:a,borderColor:n,"&-inverse":{color:e.colorTextLightSolid,background:l,borderColor:l},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(C(e))),h);const N=(e,o,r)=>{const t="string"!=typeof(n=r)?n:n.charAt(0).toUpperCase()+n.slice(1);var n;return{[`${e.componentCls}${e.componentCls}-${o}`]:{color:e[`color${r}`],background:e[`color${t}Bg`],borderColor:e[`color${t}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,b.bk)(["Tag","status"],(e=>{const o=C(e);return[N(o,"success","Success"),N(o,"processing","Info"),N(o,"error","Error"),N(o,"warning","Warning")]}),h),w=function(e,o){var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(r[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(t=Object.getOwnPropertySymbols(e);n<t.length;n++)o.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(e,t[n])&&(r[t[n]]=e[t[n]])}return r};const Z=t.forwardRef(((e,o)=>{const{prefixCls:r,className:n,rootClassName:f,style:g,children:m,icon:p,color:b,onClose:C,bordered:h=!0,visible:y}=e,x=w(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:k,direction:O,tag:N}=t.useContext(u.E_),[Z,j]=t.useState(!0),E=(0,l.Z)(x,["closeIcon","closable"]);t.useEffect((()=>{void 0!==y&&j(y)}),[y]);const P=(0,c.o2)(b),z=(0,c.yT)(b),H=P||z,I=Object.assign(Object.assign({backgroundColor:b&&!H?b:void 0},null==N?void 0:N.style),g),B=k("tag",r),[T,M,R]=v(B),_=a()(B,null==N?void 0:N.className,{[`${B}-${b}`]:H,[`${B}-has-color`]:b&&!H,[`${B}-hidden`]:!Z,[`${B}-rtl`]:"rtl"===O,[`${B}-borderless`]:!h},n,f,M,R),L=e=>{e.stopPropagation(),null==C||C(e),e.defaultPrevented||j(!1)},[,F]=(0,s.Z)((0,s.w)(e),(0,s.w)(N),{closable:!1,closeIconRender:e=>{const o=t.createElement("span",{className:`${B}-close-icon`,onClick:L},e);return(0,i.wm)(e,o,(e=>({onClick:o=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,o),L(o)},className:a()(null==e?void 0:e.className,`${B}-close-icon`)})))}}),W="function"==typeof x.onClick||m&&"a"===m.type,q=p||null,D=q?t.createElement(t.Fragment,null,q,m&&t.createElement("span",null,m)):m,V=t.createElement("span",Object.assign({},E,{ref:o,className:_,style:I}),D,F,P&&t.createElement($,{key:"preset",prefixCls:B}),z&&t.createElement(S,{key:"status",prefixCls:B}));return T(W?t.createElement(d.Z,{component:"Tag"},V):V)})),j=Z;j.CheckableTag=k;var E=j}}]);