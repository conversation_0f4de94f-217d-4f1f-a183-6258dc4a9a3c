from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from datetime import datetime
from ..models.permission import Permission, PermissionCreate, PermissionUpdate
from ..db.mongodb import db
from ..utils.auth import verify_token

router = APIRouter()

# 获取所有权限，支持分页
@router.get("/api/permissions", response_model=Dict[str, Any])
async def get_permissions(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    name: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    query = {}
    if name:
        query["name"] = {"$regex": name, "$options": "i"}
    
    skip = (current - 1) * pageSize
    permissions = await db["permissions"].find(query).skip(skip).limit(pageSize).to_list(pageSize)
    
    total = await db["permissions"].count_documents(query)
    return {
        "data": [Permission(**permission).dict() for permission in permissions],
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 添加新权限
@router.post("/api/permissions", response_model=Permission)
async def add_permission(permission: PermissionCreate, current_user: dict = Depends(verify_token)):
    permission_dict = permission.dict(exclude_unset=True)
    permission_dict["created_at"] = datetime.now()
    permission_dict["created_by"] = current_user["username"]
    result = await db["permissions"].insert_one(permission_dict)
    created_permission = await db["permissions"].find_one({"_id": result.inserted_id})
    return Permission(**created_permission)

# 更新权限
@router.put("/api/permissions/{permission_id}", response_model=Permission)
async def update_permission(permission_id: str, permission: PermissionUpdate, current_user: dict = Depends(verify_token)):
    existing_permission = await db["permissions"].find_one({"_id": ObjectId(permission_id)})
    if existing_permission is None:
        raise HTTPException(status_code=404, detail="Permission not found")
    
    update_data = permission.dict(exclude_unset=True)
    
    await db["permissions"].update_one({"_id": ObjectId(permission_id)}, {"$set": update_data})
    updated_permission = await db["permissions"].find_one({"_id": ObjectId(permission_id)})
    return Permission(**updated_permission)

# 删除权限
@router.delete("/api/permissions/{permission_id}", response_model=dict)
async def delete_permission(permission_id: str, current_user: dict = Depends(verify_token)):
    result = await db["permissions"].delete_one({"_id": ObjectId(permission_id)})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Permission not found")
    return {"message": "Permission deleted successfully"}