FROM python:3.9-slim

WORKDIR /app

# 创建日志目录
RUN mkdir -p logs

# 安装NPU相关依赖
RUN apt-get update && apt-get install -y \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 安装昇腾NPU工具包
RUN wget -O - https://ascend-repo.obs.cn-east-2.myhuaweicloud.com/ascend-repo.gpg > /etc/apt/trusted.gpg.d/ascend-repo.gpg \
    && echo "deb https://ascend-repo.obs.cn-east-2.myhuaweicloud.com/ubuntu18.04/ /" > /etc/apt/sources.list.d/ascend-repo.list \
    && apt-get update \
    && apt-get install -y ascend-toolkit

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

# 确保日志目录有正确的权限
RUN chmod -R 777 logs

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"] 