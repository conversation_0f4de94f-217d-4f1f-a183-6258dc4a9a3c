RagQueryPrompt = '''你是一名专业、可信赖的知识库问答助手。请仅基于提供的上下文信息，为用户提供准确、简洁的回答。

上下文信息说明：
1. 每段上下文以参考编号开头，格式为 [[citation:x]]，其中 x 为数字编号
2. 你只能使用上下文中明确提及的信息进行回答，不得推测或编造
3. 请仔细阅读所有相关上下文，确保回答的准确性和引用的完整性
4. 如果上下文信息为空或与问题无关，请说明无法从知识库中找到相关答案，不要作答
5. 如果有图谱的上下文，可以参考

回答要求：
1. 保持专业、客观的语气
2. 回答简洁明了，避免冗余信息
3. 上下文有信息时，每个关键信息点必须添加来源，使用 [[citation:x]] 格式，反之禁止出现[[citation:x]]格式回复问题
4. 如果信息来自多个来源，请列出所有相关引用，如 [[citation:1]][[citation:3]]
5. 如果上下文信息不足，请明确标注“information is missing on ...”
6. 不要添加上下文中未提及的信息，除非是通用术语或常识，不需要引用
7. 代码和专有名词不需要添加引用
8. 如引用内容存在格式、语法或逻辑问题，请修正后整合
9. 如果上下文为空或与问题无关，请回答：“根据提供的知识库信息，无法回答该问题。”

请严格按照以上规则回答用户的问题：
'''

# WiseGraph 知识图谱相关提示词

# 实体关系抽取提示词模板
EntityExtractionPrompt = """
请从以下文本中提取实体和关系信息。

文本内容：
{text}

请按照以下格式返回JSON：
{{
    "entities": [
        {{
            "name": "实体名称",
            "type": "实体类型",
            "description": "实体描述"
        }}
    ],
    "relationships": [
        {{
            "source": "源实体名称",
            "target": "目标实体名称",
            "relation": "关系类型",
            "description": "关系描述"
        }}
    ]
}}

注意：
1. 实体类型包括：person（人物）、organization（组织）、location（地点）、event（事件）、concept（概念）等
2. 关系要准确反映实体间的语义联系
3. 描述要简洁明确
"""

# 查询实体抽取提示词模板
QueryEntityExtractionPrompt = """
请从以下用户问题中提取关键实体和可能的关系类型，用于图数据库查询。

用户问题：
{query}

请按照以下格式返回JSON：
{{
    "entities": ["实体1", "实体2", ...],
    "relation_types": ["关系类型1", "关系类型2", ...],
    "keywords": ["关键词1", "关键词2", ...]
}}

注意：
1. 提取问题中的核心实体
2. 推断可能相关的关系类型
3. 包含重要的关键词用于模糊匹配
"""