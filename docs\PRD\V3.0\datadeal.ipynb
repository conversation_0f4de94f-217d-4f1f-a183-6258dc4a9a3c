{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python3\n", "# -*- coding: utf-8 -*-\n", "\n", "import json\n", "\n", "def transform_conversations_to_messages(conversations):\n", "    \"\"\"\n", "    将原始的 conversations 数组转换为满足规则的 messages 数组：\n", "      - system 消息（若存在）在数组首位\n", "      - 第一条非 system 消息必须是 user\n", "      - role 只能是 system/user/assistant\n", "      - 支持连续多条 user 或 assistant 消息\n", "    \"\"\"\n", "    role_map = {\n", "        \"system\": \"system\",\n", "        \"human\": \"user\",\n", "        \"gpt\": \"assistant\"\n", "    }\n", "\n", "    system_msgs = []\n", "    other_msgs = []\n", "\n", "    for conv in conversations:\n", "        raw_from = conv.get(\"from\", \"\").lower()\n", "        content = conv.get(\"value\", \"\")\n", "        # 将原始的 \"from\" 字段映射到目标 role\n", "        role = role_map.get(raw_from, \"user\")  # 默认用 user\n", "\n", "        if role == \"system\":\n", "            system_msgs.append({\"role\": \"system\", \"content\": content})\n", "        else:\n", "            other_msgs.append({\"role\": role, \"content\": content})\n", "\n", "    # 将 system 消息放在最前\n", "    messages = system_msgs + other_msgs\n", "    \n", "    # 强制第一条非 system 消息为 user\n", "    for i in range(len(system_msgs), len(messages)):\n", "        if messages[i][\"role\"] != \"user\":\n", "            messages[i][\"role\"] = \"user\"\n", "        # 仅处理第一条非 system 消息\n", "        break\n", "    \n", "    return messages\n", "\n", "def transform_record(obj):\n", "    \"\"\"\n", "    将原始 JSON 对象转换为符合要求的格式：\n", "      {\n", "        \"messages\": [ ... ]\n", "      }\n", "    如果 \"conversations\" 为空，则放一条 user 消息占位，保证数组不为空。\n", "    \"\"\"\n", "    conversations = obj.get(\"conversations\", [])\n", "    if not conversations:\n", "        return {\n", "            \"messages\": [\n", "                {\"role\": \"user\", \"content\": \"No content found.\"}\n", "            ]\n", "        }\n", "    \n", "    messages = transform_conversations_to_messages(conversations)\n", "    # 若 messages 最终还是空，最低限度放一条 user\n", "    if not messages:\n", "        messages = [\n", "            {\"role\": \"user\", \"content\": \"No content found.\"}\n", "        ]\n", "\n", "    return {\"messages\": messages}\n", "\n", "def main():\n", "    \"\"\"\n", "    不使用命令行参数，直接指定输入/输出文件。\n", "    读取 chatlogs_v2_cleaned.1500_2000w.en.jsonl，每行转换后保存到 output.jsonl。\n", "    \"\"\"\n", "    input_file = \"chatlogs_v2_cleaned.1500_2000w.en.jsonl\"\n", "    output_file = \"output.jsonl\"\n", "\n", "    output_lines = []\n", "\n", "    # 建议逐行读取，减少对内存的占用\n", "    with open(input_file, \"r\", encoding=\"utf-8\") as f:\n", "        for line in f:\n", "            line = line.strip()\n", "            if not line:\n", "                continue\n", "            \n", "            try:\n", "                obj = json.loads(line)\n", "            except json.JSONDecodeError:\n", "                # 当前行不是合法 JSON，按需处理（这里选择跳过）\n", "                continue\n", "            \n", "            new_obj = transform_record(obj)\n", "            output_lines.append(json.dumps(new_obj, ensure_ascii=False))\n", "\n", "    # 将结果逐行写入 .jsonl 文件\n", "    with open(output_file, \"w\", encoding=\"utf-8\") as f_out:\n", "        for out_line in output_lines:\n", "            f_out.write(out_line + \"\\n\")\n", "\n", "    print(f\"转换完成！结果已保存到 {output_file}\")\n", "\n", "# if __name__ == \"__main__\":\n", "#     main()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["转换完成！结果已保存到 output.jsonl\n"]}], "source": ["main()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import os\n", "import requests\n", "import urllib.parse\n", "\n", "# 需要下载的图标 URL 列表\n", "icon_urls = [\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/Tongyi.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/FunAudioLLM.png\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/DeepSeek.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/Meta.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/genmo.png\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/hunyuan.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/Fishaudio.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/Stability.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/blackforestlabs.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/internlm.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/Lightricks.png\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/Property%201%3D5G.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/netease-youdao.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/BAAI.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/Zhipu.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/Yi.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/Google.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/RVC.png\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/AIDC_AI.png\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/Mistral.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/NVIDIA.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/TencentARC.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/InstantX.svg\",\n", "    \"https://sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com/Model_LOGO/ByteDance.svg\"\n", "]\n", "\n", "def download_icons(urls, dest_dir):\n", "    \"\"\"\n", "    将给定的 icon_urls 列表中的图标下载到 dest_dir 目录下。\n", "    如果 dest_dir 不存在，会自动创建。\n", "    \"\"\"\n", "    # 如果目标目录不存在则自动创建\n", "    if not os.path.exists(dest_dir):\n", "        os.makedirs(dest_dir)\n", "\n", "    for url in urls:\n", "        # 从 URL 中提取文件名（可能包含百分号编码）\n", "        file_name = url.split('/')[-1]\n", "        # 对文件名进行 URL 解码，防止出现 %XX 字符\n", "        file_name = urllib.parse.unquote(file_name)\n", "\n", "        # 拼接完整本地保存路径\n", "        file_path = os.path.join(dest_dir, file_name)\n", "\n", "        try:\n", "            # 以流式获取（stream=True），防止大文件一次性读入内存\n", "            response = requests.get(url, stream=True, timeout=30)\n", "            response.raise_for_status()  # 检查请求是否成功\n", "            with open(file_path, 'wb') as f:\n", "                for chunk in response.iter_content(chunk_size=8192):\n", "                    if chunk:\n", "                        f.write(chunk)\n", "            print(f\"[OK] Downloaded: {file_path}\")\n", "        except Exception as e:\n", "            print(f\"[ERROR] Failed to download {url}. Reason: {e}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/Tongyi.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/FunAudioLLM.png\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/DeepSeek.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/Meta.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/genmo.png\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/hunyuan.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/Fishaudio.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/Stability.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/blackforestlabs.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/internlm.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/Lightricks.png\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/Property 1=5G.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/netease-youdao.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/BAAI.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/Zhipu.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/Yi.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/Google.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/RVC.png\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/AIDC_AI.png\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/Mistral.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/NVIDIA.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/TencentARC.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/InstantX.svg\n", "[OK] Downloaded: /Users/<USER>/workspace/LLM/wiseAgent/public/avatar/ByteDance.svg\n"]}], "source": ["destination_directory = \"/Users/<USER>/workspace/LLM/wiseAgent/public/avatar\"\n", "download_icons(icon_urls, destination_directory)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "wisechat", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}