(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2239],{43887:function(e,n,t){"use strict";t.d(n,{f:function(){return Ct}});var o=t(4942),r=t(74165),i=t(15861),l=t(91),a=t(97685),c=t(1413),u=t(10915),s=t(21770),d=t(67294);function p(e){var n="undefined"==typeof window,t=(0,d.useState)((function(){return!n&&window.matchMedia(e).matches})),o=(0,a.Z)(t,2),r=o[0],i=o[1];return(0,d.useLayoutEffect)((function(){if(!n){var t=window.matchMedia(e),o=function(e){return i(e.matches)};return t.addListener(o),function(){return t.removeListener(o)}}}),[e]),r}var m={xs:{maxWidth:575,matchMedia:"(max-width: 575px)"},sm:{minWidth:576,maxWidth:767,matchMedia:"(min-width: 576px) and (max-width: 767px)"},md:{minWidth:768,maxWidth:991,matchMedia:"(min-width: 768px) and (max-width: 991px)"},lg:{minWidth:992,maxWidth:1199,matchMedia:"(min-width: 992px) and (max-width: 1199px)"},xl:{minWidth:1200,maxWidth:1599,matchMedia:"(min-width: 1200px) and (max-width: 1599px)"},xxl:{minWidth:1600,matchMedia:"(min-width: 1600px)"}},v=function(){var e,n=p(m.md.matchMedia),t=p(m.lg.matchMedia),o=p(m.xxl.matchMedia),r=p(m.xl.matchMedia),i=p(m.sm.matchMedia),l=p(m.xs.matchMedia),c=(0,d.useState)((e=void 0,"undefined"==typeof window?e:e=Object.keys(m).find((function(e){var n=m[e].matchMedia;return!!window.matchMedia(n).matches})))),u=(0,a.Z)(c,2),s=u[0],v=u[1];return(0,d.useEffect)((function(){v(o?"xxl":r?"xl":t?"lg":n?"md":i?"sm":l?"xs":"md")}),[n,t,o,r,i,l]),s},h=t(12044);var f=t(1977),g=t(73177);function y(e){if((0,f.n)((0,g.b)(),"5.6.0")<0)return e;var n={colorGroupTitle:"groupTitleColor",radiusItem:"itemBorderRadius",radiusSubMenuItem:"subMenuItemBorderRadius",colorItemText:"itemColor",colorItemTextHover:"itemHoverColor",colorItemTextHoverHorizontal:"horizontalItemHoverColor",colorItemTextSelected:"itemSelectedColor",colorItemTextSelectedHorizontal:"horizontalItemSelectedColor",colorItemTextDisabled:"itemDisabledColor",colorDangerItemText:"dangerItemColor",colorDangerItemTextHover:"dangerItemHoverColor",colorDangerItemTextSelected:"dangerItemSelectedColor",colorDangerItemBgActive:"dangerItemActiveBg",colorDangerItemBgSelected:"dangerItemSelectedBg",colorItemBg:"itemBg",colorItemBgHover:"itemHoverBg",colorSubItemBg:"subMenuItemBg",colorItemBgActive:"itemActiveBg",colorItemBgSelected:"itemSelectedBg",colorItemBgSelectedHorizontal:"horizontalItemSelectedBg",colorActiveBarWidth:"activeBarWidth",colorActiveBarHeight:"activeBarHeight",colorActiveBarBorderSize:"activeBarBorderWidth"},t=(0,c.Z)({},e);return Object.keys(n).forEach((function(e){void 0!==t[e]&&(t[n[e]]=t[e],delete t[e])})),t}var x=t(90743);function b(e,n){return n>>>e|n<<32-e}function C(e,n,t){return e&n^~e&t}function Z(e,n,t){return e&n^e&t^n&t}function j(e){return b(2,e)^b(13,e)^b(22,e)}function w(e,n){return e[15&n]+=(b(17,t=e[n+14&15])^b(19,t)^t>>>10)+e[n+9&15]+function(e){return b(7,e)^b(18,e)^e>>>3}(e[n+1&15]);var t}var k,S,I,M=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298];function B(e,n){var t=(65535&e)+(65535&n);return(e>>16)+(n>>16)+(t>>16)<<16|65535&t}function R(){var e,n,t,o,r,i,l,a,c,u,s,d=new Array(16);e=k[0],n=k[1],t=k[2],o=k[3],r=k[4],i=k[5],l=k[6],a=k[7];for(var p=0;p<16;p++)d[p]=I[3+(p<<2)]|I[2+(p<<2)]<<8|I[1+(p<<2)]<<16|I[p<<2]<<24;for(var m=0;m<64;m++)c=a+(b(6,s=r)^b(11,s)^b(25,s))+C(r,i,l)+M[m],c+=m<16?d[m]:w(d,m),u=j(e)+Z(e,n,t),a=l,l=i,i=r,r=B(o,c),o=t,t=n,n=e,e=B(c,u);k[0]+=e,k[1]+=n,k[2]+=t,k[3]+=o,k[4]+=r,k[5]+=i,k[6]+=l,k[7]+=a}var T=function(e){return k=new Array(8),S=new Array(2),I=new Array(64),S[0]=S[1]=0,k[0]=1779033703,k[1]=3144134277,k[2]=1013904242,k[3]=2773480762,k[4]=1359893119,k[5]=2600822924,k[6]=528734635,k[7]=1541459225,function(e,n){var t,o,r=0;o=S[0]>>3&63;var i=63&n;for((S[0]+=n<<3)<n<<3&&S[1]++,S[1]+=n>>29,t=0;t+63<n;t+=64){for(var l=o;l<64;l++)I[l]=e.charCodeAt(r++);R(),o=0}for(var a=0;a<i;a++)I[a]=e.charCodeAt(r++)}(e,e.length),function(){var e=S[0]>>3&63;if(I[e++]=128,e<=56)for(var n=e;n<56;n++)I[n]=0;else{for(var t=e;t<64;t++)I[t]=0;R();for(var o=0;o<56;o++)I[o]=0}I[56]=S[1]>>>24&255,I[57]=S[1]>>>16&255,I[58]=S[1]>>>8&255,I[59]=255&S[1],I[60]=S[0]>>>24&255,I[61]=S[0]>>>16&255,I[62]=S[0]>>>8&255,I[63]=255&S[0],R()}(),function(){for(var e=new String,n=0;n<8;n++)for(var t=28;t>=0;t-=4)e+="0123456789abcdef".charAt(k[n]>>>t&15);return e}()};function E(e){return E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},E(e)}var N=["pro_layout_parentKeys","children","icon","flatMenu","indexRoute","routes"];function H(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==t)return;var o,r,i=[],l=!0,a=!1;try{for(t=t.call(e);!(l=(o=t.next()).done)&&(i.push(o.value),!n||i.length!==n);l=!0);}catch(e){a=!0,r=e}finally{try{l||null==t.return||t.return()}finally{if(a)throw r}}return i}(e,n)||$(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function A(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function P(e,n){for(var t=0;t<n.length;t++){var o=n[t];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function O(e,n){if(n&&("object"===E(n)||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function L(e){var n="function"==typeof Map?new Map:void 0;return L=function(e){if(null===e||(t=e,-1===Function.toString.call(t).indexOf("[native code]")))return e;var t;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==n){if(n.has(e))return n.get(e);n.set(e,o)}function o(){return z(e,arguments,_(this).constructor)}return o.prototype=Object.create(e.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),W(o,e)},L(e)}function z(e,n,t){return z=D()?Reflect.construct.bind():function(e,n,t){var o=[null];o.push.apply(o,n);var r=new(Function.bind.apply(e,o));return t&&W(r,t.prototype),r},z.apply(null,arguments)}function D(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function W(e,n){return W=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,n){return e.__proto__=n,e},W(e,n)}function _(e){return _=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},_(e)}function F(e){return function(e){if(Array.isArray(e))return X(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||$(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(e,n){if(e){if("string"==typeof e)return X(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?X(e,n):void 0}}function X(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,o=new Array(n);t<n;t++)o[t]=e[t];return o}function K(e,n){if(null==e)return{};var t,o,r=function(e,n){if(null==e)return{};var t,o,r={},i=Object.keys(e);for(o=0;o<i.length;o++)t=i[o],n.indexOf(t)>=0||(r[t]=e[t]);return r}(e,n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)t=i[o],n.indexOf(t)>=0||Object.prototype.propertyIsEnumerable.call(e,t)&&(r[t]=e[t])}return r}function G(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);n&&(o=o.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,o)}return t}function U(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?G(Object(t),!0).forEach((function(n){V(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):G(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}function V(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var Q="routes";function Y(e){return e.split("?")[0].split("#")[0]}var q=function(e){if(!e.startsWith("http"))return!1;try{return!!new URL(e)}catch(e){return!1}},J=function(e){var n=e.path;if(!n||"/"===n)try{return"/".concat(T(JSON.stringify(e)))}catch(e){}return n?Y(n):n},ee=function(e,n){var t=e.name,o=e.locale;return!("locale"in e&&!1===o||!t)&&(e.locale||"".concat(n,".").concat(t))},ne=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/";return e.endsWith("/*")?e.replace("/*","/"):(e||n).startsWith("/")||q(e)?e:"/".concat(n,"/").concat(e).replace(/\/\//g,"/").replace(/\/\//g,"/")},te=function(e,n){var t=e.menu,o=void 0===t?{}:t,r=e.indexRoute,i=e.path,l=void 0===i?"":i,a=e.children||[],c=o.name,u=void 0===c?e.name:c,s=o.icon,d=void 0===s?e.icon:s,p=o.hideChildren,m=void 0===p?e.hideChildren:p,v=o.flatMenu,h=void 0===v?e.flatMenu:v,f=r&&"redirect"!==Object.keys(r).join(",")?[U({path:l,menu:o},r)].concat(a||[]):a,g=U({},e);if(u&&(g.name=u),d&&(g.icon=d),f&&f.length){if(m)return delete g.children,g;var y=re(U(U({},n),{},{data:f}),e);if(h)return y;delete g[Q]}return g},oe=function(e){return Array.isArray(e)&&e.length>0};function re(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{path:"/"},t=e.data,o=e.formatMessage,r=e.parentName,i=e.locale;return t&&Array.isArray(t)?t.filter((function(e){return!!e&&(!!oe(e.children)||(!!e.path||(!!e.originPath||(!!e.layout||(e.redirect||e.unaccessible,!1)))))})).filter((function(e){var n,t;return!!((null==e||null===(n=e.menu)||void 0===n?void 0:n.name)||(null==e?void 0:e.flatMenu)||(null==e||null===(t=e.menu)||void 0===t?void 0:t.flatMenu))||!1!==e.menu})).map((function(e){var n=U(U({},e),{},{path:e.path||e.originPath});return!n.children&&n[Q]&&(n.children=n[Q],delete n[Q]),n.unaccessible&&delete n.name,"*"===n.path&&(n.path="."),"/*"===n.path&&(n.path="."),!n.path&&n.originPath&&(n.path=n.originPath),n})).map((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{path:"/"},l=t.children||t[Q]||[],a=ne(t.path,n?n.path:"/"),c=t.name,u=ee(t,r||"menu"),s=!1!==u&&!1!==i&&o&&u?o({id:u,defaultMessage:c}):c,d=n.pro_layout_parentKeys,p=void 0===d?[]:d,m=(n.children,n.icon,n.flatMenu,n.indexRoute,n.routes,K(n,N)),v=new Set([].concat(F(p),F(t.parentKeys||[])));n.key&&v.add(n.key);var h=U(U(U({},m),{},{menu:void 0},t),{},{path:a,locale:u,key:t.key||J(U(U({},t),{},{path:a})),pro_layout_parentKeys:Array.from(v).filter((function(e){return e&&"/"!==e}))});if(s?h.name=s:delete h.name,void 0===h.menu&&delete h.menu,oe(l)){var f=re(U(U({},e),{},{data:l,parentName:u||""}),h);oe(f)&&(h.children=f)}return te(h,e)})).flat(1):[]}var ie=function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return n.filter((function(e){return e&&(e.name||oe(e.children))&&!e.hideInMenu&&!e.redirect})).map((function(n){var t=U({},n),o=t.children||n[Q]||[];if(delete t[Q],oe(o)&&!t.hideChildrenInMenu&&o.some((function(e){return e&&!!e.name}))){var r=e(o);if(r.length)return U(U({},t),{},{children:r})}return U({},n)})).filter((function(e){return e}))},le=function(e){!function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),n&&W(e,n)}(a,e);var n,t,o,r,i,l=(n=a,t=D(),function(){var e,o=_(n);if(t){var r=_(this).constructor;e=Reflect.construct(o,arguments,r)}else e=o.apply(this,arguments);return O(this,e)});function a(){return A(this,a),l.apply(this,arguments)}return o=a,(r=[{key:"get",value:function(e){var n;try{var t,o=function(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=$(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var o=0,r=function(){};return{s:r,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,l=!0,a=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return l=e.done,e},e:function(e){a=!0,i=e},f:function(){try{l||null==t.return||t.return()}finally{if(a)throw i}}}}(this.entries());try{for(o.s();!(t=o.n()).done;){var r=H(t.value,2),i=r[0],l=r[1],a=Y(i);if(!q(i)&&(0,x.Bo)(a,[]).test(e)){n=l;break}}}catch(e){o.e(e)}finally{o.f()}}catch(e){n=void 0}return n}}])&&P(o.prototype,r),i&&P(o,i),Object.defineProperty(o,"prototype",{writable:!1}),a}(L(Map)),ae=function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return n.map((function(n){var t=n.children||n[Q];if(oe(t)&&e(t).length)return U({},n);var o=U({},n);return delete o[Q],delete o.children,o})).filter((function(e){return e}))},ce=function(e,n,t,o){var r=re({data:e,formatMessage:t,locale:n}),i=o?ae(r):ie(r),l=function(e){var n=new le;return function e(t,o){t.forEach((function(t){var r=t.children||t[Q]||[];oe(r)&&e(r,t);var i=ne(t.path,o?o.path:"/");n.set(Y(i),t)}))}(e),n}(r);return{breadcrumb:l,menuData:i}};function ue(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);n&&(o=o.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,o)}return t}function se(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?ue(Object(t),!0).forEach((function(n){de(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ue(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}function de(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var pe=function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t={};return n.forEach((function(n){var o=se({},n);if(o&&o.key){!o.children&&o[Q]&&(o.children=o[Q],delete o[Q]);var r=o.children||[];t[Y(o.path||o.key||"/")]=se({},o),t[o.key||o.path||"/"]=se({},o),r&&(t=se(se({},t),e(r)))}})),t},me=function(e,n,t,o){var r=pe(n),i=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0,t=arguments.length>2?arguments[2]:void 0;return e.filter((function(e){if("/"===e&&"/"===n)return!0;if("/"!==e&&"/*"!==e&&e&&!q(e)){var o=Y(e);try{if(t&&(0,x.Bo)("".concat(o)).test(n))return!0;if((0,x.Bo)("".concat(o),[]).test(n))return!0;if((0,x.Bo)("".concat(o,"/(.*)")).test(n))return!0}catch(e){}}return!1})).sort((function(e,t){return e===n?10:t===n?-10:e.substr(1).split("/").length-t.substr(1).split("/").length}))}(Object.keys(r),e||"/",o);return!i||i.length<1?[]:(t||(i=[i[i.length-1]]),i.map((function(e){var n=r[e]||{pro_layout_parentKeys:"",key:""},t=new Map,o=(n.pro_layout_parentKeys||[]).map((function(e){return t.has(e)?null:(t.set(e,!0),r[e])})).filter((function(e){return e}));return n.key&&o.push(n),o})).flat(1))},ve=t(21532),he=t(26058),fe=t(93967),ge=t.n(fe),ye=t(98423),xe=t(80334),be=t(5068),Ce=t(25269),Ze=t(78164),je=t(85893),we=function(e){var n=(0,d.useContext)(u.L_).hashId,t=e.style,r=e.prefixCls,i=e.children,l=e.hasPageContainer,a=void 0===l?0:l,c=ge()("".concat(r,"-content"),n,(0,o.Z)((0,o.Z)({},"".concat(r,"-has-header"),e.hasHeader),"".concat(r,"-content-has-page-container"),a>0)),s=e.ErrorBoundary||Ze.S;return!1===e.ErrorBoundary?(0,je.jsx)(he.Z.Content,{className:c,style:t,children:i}):(0,je.jsx)(s,{children:(0,je.jsx)(he.Z.Content,{className:c,style:t,children:i})})},ke=function(){return(0,je.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 200 200",children:[(0,je.jsxs)("defs",{children:[(0,je.jsxs)("linearGradient",{x1:"62.1023273%",y1:"0%",x2:"108.19718%",y2:"37.8635764%",id:"linearGradient-1",children:[(0,je.jsx)("stop",{stopColor:"#4285EB",offset:"0%"}),(0,je.jsx)("stop",{stopColor:"#2EC7FF",offset:"100%"})]}),(0,je.jsxs)("linearGradient",{x1:"69.644116%",y1:"0%",x2:"54.0428975%",y2:"108.456714%",id:"linearGradient-2",children:[(0,je.jsx)("stop",{stopColor:"#29CDFF",offset:"0%"}),(0,je.jsx)("stop",{stopColor:"#148EFF",offset:"37.8600687%"}),(0,je.jsx)("stop",{stopColor:"#0A60FF",offset:"100%"})]}),(0,je.jsxs)("linearGradient",{x1:"69.6908165%",y1:"-12.9743587%",x2:"16.7228981%",y2:"117.391248%",id:"linearGradient-3",children:[(0,je.jsx)("stop",{stopColor:"#FA816E",offset:"0%"}),(0,je.jsx)("stop",{stopColor:"#F74A5C",offset:"41.472606%"}),(0,je.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]}),(0,je.jsxs)("linearGradient",{x1:"68.1279872%",y1:"-35.6905737%",x2:"30.4400914%",y2:"114.942679%",id:"linearGradient-4",children:[(0,je.jsx)("stop",{stopColor:"#FA8E7D",offset:"0%"}),(0,je.jsx)("stop",{stopColor:"#F74A5C",offset:"51.2635191%"}),(0,je.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]})]}),(0,je.jsx)("g",{stroke:"none",strokeWidth:1,fill:"none",fillRule:"evenodd",children:(0,je.jsx)("g",{transform:"translate(-20.000000, -20.000000)",children:(0,je.jsx)("g",{transform:"translate(20.000000, 20.000000)",children:(0,je.jsxs)("g",{children:[(0,je.jsxs)("g",{fillRule:"nonzero",children:[(0,je.jsxs)("g",{children:[(0,je.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-1)"}),(0,je.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-2)"})]}),(0,je.jsx)("path",{d:"M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z",fill:"url(#linearGradient-3)"})]}),(0,je.jsx)("ellipse",{fill:"url(#linearGradient-4)",cx:"100.519339",cy:"100.436681",rx:"23.6001926",ry:"23.580786"})]})})})})]})},Se=t(87462),Ie={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm5.6-532.7c53 0 89 33.8 93 83.4.3 4.2 3.8 7.4 8 7.4h56.7c2.6 0 4.7-2.1 4.7-4.7 0-86.7-68.4-147.4-162.7-147.4C407.4 290 344 364.2 344 486.8v52.3C344 660.8 407.4 734 517.3 734c94 0 162.7-58.8 162.7-141.4 0-2.6-2.1-4.7-4.7-4.7h-56.8c-4.2 0-7.6 3.2-8 7.3-4.2 46.1-40.1 77.8-93 77.8-65.3 0-102.1-47.9-102.1-133.6v-52.6c.1-87 37-135.5 102.2-135.5z"}}]},name:"copyright",theme:"outlined"},Me=t(65555),Be=function(e,n){return d.createElement(Me.Z,(0,Se.Z)({},e,{ref:n,icon:Ie}))};var Re=d.forwardRef(Be),Te=t(64847),Ee=function(e){return(0,o.Z)({},e.componentCls,{marginBlock:0,marginBlockStart:48,marginBlockEnd:24,marginInline:0,paddingBlock:0,paddingInline:16,textAlign:"center","&-list":{marginBlockEnd:8,color:e.colorTextSecondary,"&-link":{color:e.colorTextSecondary,textDecoration:e.linkDecoration},"*:not(:last-child)":{marginInlineEnd:8},"&:hover":{color:e.colorPrimary}},"&-copyright":{fontSize:"14px",color:e.colorText}})};var Ne=function(e){var n=e.className,t=e.prefixCls,o=e.links,r=e.copyright,i=e.style,l=(0,d.useContext)(ve.ZP.ConfigContext).getPrefixCls(t||"pro-global-footer"),a=function(e){return(0,Te.Xj)("ProLayoutFooter",(function(n){var t=(0,c.Z)((0,c.Z)({},n),{},{componentCls:".".concat(e)});return[Ee(t)]}))}(l),u=a.wrapSSR,s=a.hashId;return!(null==o||!1===o||Array.isArray(o)&&0===o.length)||null!=r&&!1!==r?u((0,je.jsxs)("div",{className:ge()(l,s,n),style:i,children:[o&&(0,je.jsx)("div",{className:"".concat(l,"-list ").concat(s).trim(),children:o.map((function(e){return(0,je.jsx)("a",{className:"".concat(l,"-list-link ").concat(s).trim(),title:e.key,target:e.blankTarget?"_blank":"_self",href:e.href,rel:"noreferrer",children:e.title},e.key)}))}),r&&(0,je.jsx)("div",{className:"".concat(l,"-copyright ").concat(s).trim(),children:r})]})):null},He=he.Z.Footer,Ae=function(e){var n=e.links,t=e.copyright,o=e.style,r=e.className,i=e.prefixCls;return(0,je.jsx)(He,{className:r,style:(0,c.Z)({padding:0},o),children:(0,je.jsx)(Ne,{links:n,prefixCls:i,copyright:!1===t?null:(0,je.jsxs)(d.Fragment,{children:[(0,je.jsx)(Re,{})," ",t]})})})},Pe=t(62812),Oe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},Le=function(e,n){return d.createElement(Me.Z,(0,Se.Z)({},e,{ref:n,icon:Oe}))};var ze=d.forwardRef(Le),De=t(55241),We=function(){return(0,je.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,je.jsx)("path",{d:"M0 0h3v3H0V0zm4.5 0h3v3h-3V0zM9 0h3v3H9V0zM0 4.5h3v3H0v-3zm4.503 0h3v3h-3v-3zM9 4.5h3v3H9v-3zM0 9h3v3H0V9zm4.503 0h3v3h-3V9zM9 9h3v3H9V9z"})})},_e=function e(n){var t=n.appList,o=n.baseClassName,r=n.hashId,i=n.itemClick;return(0,je.jsx)("div",{className:"".concat(o,"-content ").concat(r).trim(),children:(0,je.jsx)("ul",{className:"".concat(o,"-content-list ").concat(r).trim(),children:null==t?void 0:t.map((function(n,t){var l;return null!=n&&null!==(l=n.children)&&void 0!==l&&l.length?(0,je.jsxs)("div",{className:"".concat(o,"-content-list-item-group ").concat(r).trim(),children:[(0,je.jsx)("div",{className:"".concat(o,"-content-list-item-group-title ").concat(r).trim(),children:n.title}),(0,je.jsx)(e,{hashId:r,itemClick:i,appList:null==n?void 0:n.children,baseClassName:o})]},t):(0,je.jsx)("li",{className:"".concat(o,"-content-list-item ").concat(r).trim(),onClick:function(e){e.stopPropagation(),null==i||i(n)},children:(0,je.jsxs)("a",{href:i?void 0:n.url,target:n.target,rel:"noreferrer",children:[Ve(n.icon),(0,je.jsxs)("div",{children:[(0,je.jsx)("div",{children:n.title}),n.desc?(0,je.jsx)("span",{children:n.desc}):null]})]})},t)}))})})},Fe=function(e){if(!e)return!1;if(!e.startsWith("http"))return!1;try{return!!new URL(e)}catch(e){return!1}},$e=function(e,n){if(e&&"string"==typeof e&&Fe(e))return(0,je.jsx)("img",{src:e,alt:"logo"});if("function"==typeof e)return e();if(e&&"string"==typeof e)return(0,je.jsx)("div",{id:"avatarLogo",children:e});if(!e&&n&&"string"==typeof n){var t=n.substring(0,1);return(0,je.jsx)("div",{id:"avatarLogo",children:t})}return e},Xe=function e(n){var t=n.appList,o=n.baseClassName,r=n.hashId,i=n.itemClick;return(0,je.jsx)("div",{className:"".concat(o,"-content ").concat(r).trim(),children:(0,je.jsx)("ul",{className:"".concat(o,"-content-list ").concat(r).trim(),children:null==t?void 0:t.map((function(n,t){var l;return null!=n&&null!==(l=n.children)&&void 0!==l&&l.length?(0,je.jsxs)("div",{className:"".concat(o,"-content-list-item-group ").concat(r).trim(),children:[(0,je.jsx)("div",{className:"".concat(o,"-content-list-item-group-title ").concat(r).trim(),children:n.title}),(0,je.jsx)(e,{hashId:r,itemClick:i,appList:null==n?void 0:n.children,baseClassName:o})]},t):(0,je.jsx)("li",{className:"".concat(o,"-content-list-item ").concat(r).trim(),onClick:function(e){e.stopPropagation(),null==i||i(n)},children:(0,je.jsxs)("a",{href:i?"javascript:;":n.url,target:n.target,rel:"noreferrer",children:[$e(n.icon,n.title),(0,je.jsx)("div",{children:(0,je.jsx)("div",{children:n.title})})]})},t)}))})})},Ke=function(e){return{"&-content":{maxHeight:"calc(100vh - 48px)",overflow:"auto","&-list":{boxSizing:"content-box",maxWidth:656,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","&-item":{position:"relative",display:"inline-block",width:328,height:72,paddingInline:16,paddingBlock:16,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.borderRadius,"&-group":{marginBottom:16,"&-title":{margin:"16px 0 8px 12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginTop:12}}},"&:hover":{backgroundColor:e.colorBgTextHover},"* div":null===Te.Wf||void 0===Te.Wf?void 0:(0,Te.Wf)(e),a:{display:"flex",height:"100%",fontSize:12,textDecoration:"none","& > img":{width:40,height:40},"& > div":{marginInlineStart:14,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}},Ge=function(e){return{"&-content":{maxHeight:"calc(100vh - 48px)",overflow:"auto","&-list":{boxSizing:"border-box",maxWidth:376,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","&-item":{position:"relative",display:"inline-block",width:104,height:104,marginBlock:8,marginInline:8,paddingInline:24,paddingBlock:24,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.borderRadius,"&-group":{marginBottom:16,"&-title":{margin:"16px 0 8px 12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginTop:12}}},"&:hover":{backgroundColor:e.colorBgTextHover},a:{display:"flex",flexDirection:"column",alignItems:"center",height:"100%",fontSize:12,textDecoration:"none","& > #avatarLogo":{width:40,height:40,margin:"0 auto",color:e.colorPrimary,fontSize:22,lineHeight:"40px",textAlign:"center",backgroundImage:"linear-gradient(180deg, #E8F0FB 0%, #F6F8FC 100%)",borderRadius:e.borderRadius},"& > img":{width:40,height:40},"& > div":{marginBlockStart:5,marginInlineStart:0,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}},Ue=function(e){var n,t,r,i,l;return(0,o.Z)({},e.componentCls,{"&-icon":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInline:4,paddingBlock:0,fontSize:14,lineHeight:"14px",height:28,width:28,cursor:"pointer",color:null===(n=e.layout)||void 0===n?void 0:n.colorTextAppListIcon,borderRadius:e.borderRadius,"&:hover":{color:null===(t=e.layout)||void 0===t?void 0:t.colorTextAppListIconHover,backgroundColor:null===(r=e.layout)||void 0===r?void 0:r.colorBgAppListIconHover},"&-active":{color:null===(i=e.layout)||void 0===i?void 0:i.colorTextAppListIconHover,backgroundColor:null===(l=e.layout)||void 0===l?void 0:l.colorBgAppListIconHover}},"&-item-title":{marginInlineStart:"16px",marginInlineEnd:"8px",marginBlockStart:0,marginBlockEnd:"12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginBlockStart:12}},"&-popover":(0,o.Z)({},"".concat(e.antCls,"-popover-arrow"),{display:"none"}),"&-simple":Ge(e),"&-default":Ke(e)})};var Ve=function(e){return"string"==typeof e?(0,je.jsx)("img",{width:"auto",height:22,src:e,alt:"logo"}):"function"==typeof e?e():e},Qe=function(e){var n,t=e.appList,r=e.appListRender,i=e.prefixCls,l=void 0===i?"ant-pro":i,u=e.onItemClick,s=d.useRef(null),p=d.useRef(null),m="".concat(l,"-layout-apps"),v=function(e){return(0,Te.Xj)("AppsLogoComponents",(function(n){var t=(0,c.Z)((0,c.Z)({},n),{},{componentCls:".".concat(e)});return[Ue(t)]}))}(m),h=v.wrapSSR,f=v.hashId,y=(0,d.useState)(!1),x=(0,a.Z)(y,2),b=x[0],C=x[1],Z=function(e){null==u||u(e,p)},j=(0,d.useMemo)((function(){return(null==t?void 0:t.some((function(e){return!(null!=e&&e.desc)})))?(0,je.jsx)(Xe,{hashId:f,appList:t,itemClick:u?Z:void 0,baseClassName:"".concat(m,"-simple")}):(0,je.jsx)(_e,{hashId:f,appList:t,itemClick:u?Z:void 0,baseClassName:"".concat(m,"-default")})}),[t,m,f]);if(null==e||null===(n=e.appList)||void 0===n||!n.length)return null;var w=r?r(null==e?void 0:e.appList,j):j,k=(0,g.X)(void 0,(function(e){return C(e)}));return h((0,je.jsxs)(je.Fragment,{children:[(0,je.jsx)("div",{ref:s,onClick:function(e){e.stopPropagation(),e.preventDefault()}}),(0,je.jsx)(De.Z,(0,c.Z)((0,c.Z)({placement:"bottomRight",trigger:["click"],zIndex:9999,arrow:!1},k),{},{overlayClassName:"".concat(m,"-popover ").concat(f).trim(),content:w,getPopupContainer:function(){return s.current||document.body},children:(0,je.jsx)("span",{ref:p,onClick:function(e){e.stopPropagation()},className:ge()("".concat(m,"-icon"),f,(0,o.Z)({},"".concat(m,"-icon-active"),b)),children:(0,je.jsx)(We,{})})}))]}))},Ye=t(68997),qe=t(42075),Je=t(50136);function en(){return(0,je.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,je.jsx)("path",{d:"M6.432 7.967a.448.448 0 01-.318.133h-.228a.46.46 0 01-.318-.133L2.488 4.85a.305.305 0 010-.43l.427-.43a.293.293 0 01.42 0L6 6.687l2.665-2.699a.299.299 0 01.426 0l.42.431a.305.305 0 010 .43L6.432 7.967z"})})}var nn=function(e){var n,t,r;return(0,o.Z)({},e.componentCls,{position:"absolute",insetBlockStart:"18px",zIndex:"101",width:"24px",height:"24px",fontSize:["14px","16px"],textAlign:"center",borderRadius:"40px",insetInlineEnd:"-13px",transition:"transform 0.3s",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",color:null===(n=e.layout)||void 0===n||null===(n=n.sider)||void 0===n?void 0:n.colorTextCollapsedButton,backgroundColor:null===(t=e.layout)||void 0===t||null===(t=t.sider)||void 0===t?void 0:t.colorBgCollapsedButton,boxShadow:"0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08)","&:hover":{color:null===(r=e.layout)||void 0===r||null===(r=r.sider)||void 0===r?void 0:r.colorTextCollapsedButtonHover,boxShadow:"0 4px 16px -4px rgba(0,0,0,0.05), 0 2px 8px -2px rgba(25,15,15,0.07), 0 1px 2px 0 rgba(0,0,0,0.08)"},".anticon":{fontSize:"14px"},"& > svg":{transition:"transform  0.3s",transform:"rotate(90deg)"},"&-collapsed":{"& > svg":{transform:"rotate(-90deg)"}}})};var tn=["isMobile","collapsed"],on=function(e){var n,t=e.isMobile,r=e.collapsed,i=(0,l.Z)(e,tn),a=(n=e.className,(0,Te.Xj)("SiderMenuCollapsedIcon",(function(e){var t=(0,c.Z)((0,c.Z)({},e),{},{componentCls:".".concat(n)});return[nn(t)]}))),u=a.wrapSSR,s=a.hashId;return t&&r?null:u((0,je.jsx)("div",(0,c.Z)((0,c.Z)({},i),{},{className:ge()(e.className,s,(0,o.Z)((0,o.Z)({},"".concat(e.className,"-collapsed"),r),"".concat(e.className,"-is-mobile"),t)),children:(0,je.jsx)(en,{})})))},rn=t(74902),ln=t(43144),an=t(15671),cn=t(42550),un=t(2446),sn=t(14004),dn=["className","component","viewBox","spin","rotate","tabIndex","onClick","children"],pn=d.forwardRef((function(e,n){var t=e.className,r=e.component,i=e.viewBox,a=e.spin,u=e.rotate,s=e.tabIndex,p=e.onClick,m=e.children,v=(0,l.Z)(e,dn),h=d.useRef(),f=(0,cn.x1)(h,n);(0,sn.Kp)(Boolean(r||m),"Should have `component` prop or `children`."),(0,sn.C3)(h);var g=d.useContext(un.Z),y=g.prefixCls,x=void 0===y?"anticon":y,b=g.rootClassName,C=ge()(b,x,(0,o.Z)({},"".concat(x,"-spin"),!!a&&!!r),t),Z=ge()((0,o.Z)({},"".concat(x,"-spin"),!!a)),j=u?{msTransform:"rotate(".concat(u,"deg)"),transform:"rotate(".concat(u,"deg)")}:void 0,w=(0,c.Z)((0,c.Z)({},sn.vD),{},{className:Z,style:j,viewBox:i});i||delete w.viewBox;var k=s;return void 0===k&&p&&(k=-1),d.createElement("span",(0,Se.Z)({role:"img"},v,{ref:f,tabIndex:k,onClick:p,className:C}),r?d.createElement(r,w,m):m?((0,sn.Kp)(Boolean(i)||1===d.Children.count(m)&&d.isValidElement(m)&&"use"===d.Children.only(m).type,"Make sure that you provide correct `viewBox` prop (default `0 0 1024 1024`) to the icon."),d.createElement("svg",(0,Se.Z)({},w,{viewBox:i}),m)):null)}));pn.displayName="AntdIcon";var mn=pn,vn=["type","children"],hn=new Set;function fn(e){return Boolean("string"==typeof e&&e.length&&!hn.has(e))}function gn(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,t=e[n];if(fn(t)){var o=document.createElement("script");o.setAttribute("src",t),o.setAttribute("data-namespace",t),e.length>n+1&&(o.onload=function(){gn(e,n+1)},o.onerror=function(){gn(e,n+1)}),hn.add(t),document.body.appendChild(o)}}function yn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.scriptUrl,t=e.extraCommonProps,o=void 0===t?{}:t;n&&"undefined"!=typeof document&&"undefined"!=typeof window&&"function"==typeof document.createElement&&(Array.isArray(n)?gn(n.reverse()):gn([n]));var r=d.forwardRef((function(e,n){var t=e.type,r=e.children,i=(0,l.Z)(e,vn),a=null;return e.type&&(a=d.createElement("use",{xlinkHref:"#".concat(t)})),r&&(a=r),d.createElement(mn,(0,Se.Z)({},o,i,{ref:n}),a)}));return r.displayName="Iconfont",r}function xn(e){return/\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(e)}var bn=t(83062),Cn=t(48054),Zn=t(14192),jn=function(e,n){var t,r,i=n.includes("horizontal")?null===(t=e.layout)||void 0===t?void 0:t.header:null===(r=e.layout)||void 0===r?void 0:r.sider;return(0,c.Z)((0,c.Z)((0,o.Z)({},"".concat(e.componentCls),(0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)({background:"transparent",color:null==i?void 0:i.colorTextMenu,border:"none"},"".concat(e.componentCls,"-menu-item"),{transition:"none !important"}),"".concat(e.componentCls,"-submenu-has-icon"),(0,o.Z)({},"> ".concat(e.antCls,"-menu-sub"),{paddingInlineStart:10})),"".concat(e.antCls,"-menu-title-content"),{width:"100%",height:"100%",display:"inline-flex"}),"".concat(e.antCls,"-menu-title-content"),{"&:first-child":{width:"100%"}}),"".concat(e.componentCls,"-item-icon"),{display:"flex",alignItems:"center"}),"&&-collapsed",(0,o.Z)((0,o.Z)((0,o.Z)({},"".concat(e.antCls,"-menu-item, \n        ").concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-item, \n        ").concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu > ").concat(e.antCls,"-menu-submenu-title, \n        ").concat(e.antCls,"-menu-submenu > ").concat(e.antCls,"-menu-submenu-title"),{paddingInline:"0 !important",marginBlock:"4px !important"}),"".concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,"-menu-submenu-title, \n        ").concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,"-menu-submenu-title"),{backgroundColor:null==i?void 0:i.colorBgMenuItemSelected,borderRadius:e.borderRadiusLG}),"".concat(e.componentCls,"-group"),(0,o.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{paddingInline:0}))),"&-item-title",(0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)({display:"flex",flexDirection:"row",alignItems:"center",gap:e.marginXS},"".concat(e.componentCls,"-item-text"),{maxWidth:"100%",textOverflow:"ellipsis",overflow:"hidden",wordBreak:"break-all",whiteSpace:"nowrap"}),"&-collapsed",(0,o.Z)((0,o.Z)({minWidth:40,height:40},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px !important",height:"16px"}}),"".concat(e.componentCls,"-item-text-has-icon"),{display:"none !important"})),"&-collapsed-level-0",{flexDirection:"column",justifyContent:"center"}),"&".concat(e.componentCls,"-group-item-title"),{gap:e.marginXS,height:18,overflow:"hidden"}),"&".concat(e.componentCls,"-item-collapsed-show-title"),(0,o.Z)({lineHeight:"16px",gap:0},"&".concat(e.componentCls,"-item-title-collapsed"),(0,o.Z)((0,o.Z)({display:"flex"},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px!important",height:"16px"}}),"".concat(e.componentCls,"-item-text"),{opacity:"1 !important",display:"inline !important",textAlign:"center",fontSize:12,height:12,lineHeight:"12px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",margin:0,padding:0,marginBlockStart:4})))),"&-group",(0,o.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:12,color:e.colorTextLabel,".anticon":{marginInlineEnd:8}})),"&-group-divider",{color:e.colorTextSecondary,fontSize:12,lineHeight:20})),n.includes("horizontal")?{}:(0,o.Z)({},"".concat(e.antCls,"-menu-submenu").concat(e.antCls,"-menu-submenu-popup"),(0,o.Z)({},"".concat(e.componentCls,"-item-title"),{alignItems:"flex-start"}))),{},(0,o.Z)({},"".concat(e.antCls,"-menu-submenu-popup"),{backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"}))};var wn=function(e){var n=(0,d.useState)(e.collapsed),t=(0,a.Z)(n,2),o=t[0],r=t[1],i=(0,d.useState)(!1),l=(0,a.Z)(i,2),c=l[0],u=l[1];return(0,d.useEffect)((function(){u(!1),setTimeout((function(){r(e.collapsed)}),400)}),[e.collapsed]),e.disable?e.children:(0,je.jsx)(bn.Z,{title:e.title,open:!(!o||!e.collapsed)&&c,placement:"right",onOpenChange:u,children:e.children})},kn=yn({scriptUrl:Zn.h.iconfontUrl}),Sn=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"icon-",t=arguments.length>2?arguments[2]:void 0;if("string"==typeof e&&""!==e){if(Fe(e)||xn(e))return(0,je.jsx)("img",{width:16,src:e,alt:"icon",className:t},e);if(e.startsWith(n))return(0,je.jsx)(kn,{type:e})}return e},In=function(e){return e&&"string"==typeof e?e.substring(0,1).toUpperCase():null},Mn=(0,ln.Z)((function e(n){var t=this;(0,an.Z)(this,e),(0,o.Z)(this,"props",void 0),(0,o.Z)(this,"getNavMenuItems",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0,o=arguments.length>2?arguments[2]:void 0;return e.map((function(e){return t.getSubMenuOrItem(e,n,o)})).filter((function(e){return e})).flat(1)})),(0,o.Z)(this,"getSubMenuOrItem",(function(e,n,r){var i=t.props,l=i.subMenuItemRender,a=i.baseClassName,u=i.prefixCls,s=i.collapsed,d=i.menu,p=i.iconPrefixes,m=i.layout,v="group"===(null==d?void 0:d.type)&&"top"!==m,h=t.props.token,f=t.getIntlName(e),g=(null==e?void 0:e.children)||(null==e?void 0:e.routes),y=v&&0===n?"group":void 0;if(Array.isArray(g)&&g.length>0){var x,b,C,Z,j,w=0===n||v&&1===n,k=Sn(e.icon,p,"".concat(a,"-icon ").concat(null===(x=t.props)||void 0===x?void 0:x.hashId)),S=s&&w?In(f):null,I=(0,je.jsxs)("div",{className:ge()("".concat(a,"-item-title"),null===(b=t.props)||void 0===b?void 0:b.hashId,(0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)({},"".concat(a,"-item-title-collapsed"),s),"".concat(a,"-item-title-collapsed-level-").concat(r),s),"".concat(a,"-group-item-title"),"group"===y),"".concat(a,"-item-collapsed-show-title"),(null==d?void 0:d.collapsedShowTitle)&&s)),children:["group"===y&&s?null:w&&k?(0,je.jsx)("span",{className:"".concat(a,"-item-icon ").concat(null===(C=t.props)||void 0===C?void 0:C.hashId).trim(),children:k}):S,(0,je.jsx)("span",{className:ge()("".concat(a,"-item-text"),null===(Z=t.props)||void 0===Z?void 0:Z.hashId,(0,o.Z)({},"".concat(a,"-item-text-has-icon"),"group"!==y&&w&&(k||S))),children:f})]}),M=l?l((0,c.Z)((0,c.Z)({},e),{},{isUrl:!1}),I,t.props):I;if(v&&0===n&&t.props.collapsed&&!d.collapsedShowGroupTitle)return t.getNavMenuItems(g,n+1,n);var B=t.getNavMenuItems(g,n+1,v&&0===n&&t.props.collapsed?n:n+1);return[{type:y,key:e.key||e.path,label:M,onClick:v?void 0:e.onTitleClick,children:B,className:ge()((0,o.Z)((0,o.Z)((0,o.Z)({},"".concat(a,"-group"),"group"===y),"".concat(a,"-submenu"),"group"!==y),"".concat(a,"-submenu-has-icon"),"group"!==y&&w&&k))},v&&0===n?{type:"divider",prefixCls:u,className:"".concat(a,"-divider"),key:(e.key||e.path)+"-group-divider",style:{padding:0,borderBlockEnd:0,margin:t.props.collapsed?"4px":"6px 16px",marginBlockStart:t.props.collapsed?4:8,borderColor:null==h||null===(j=h.layout)||void 0===j||null===(j=j.sider)||void 0===j?void 0:j.colorMenuItemDivider}}:void 0].filter(Boolean)}return{className:"".concat(a,"-menu-item"),disabled:e.disabled,key:e.key||e.path,onClick:e.onTitleClick,label:t.getMenuItemPath(e,n,r)}})),(0,o.Z)(this,"getIntlName",(function(e){var n=e.name,o=e.locale,r=t.props,i=r.menu,l=r.formatMessage,a=n;return o&&!1!==(null==i?void 0:i.locale)&&(a=null==l?void 0:l({id:o,defaultMessage:n})),t.props.menuTextRender?t.props.menuTextRender(e,a,t.props):a})),(0,o.Z)(this,"getMenuItemPath",(function(e,n,r){var i,l,a,u,s,d,p,m=t.conversionPath(e.path||"/"),v=t.props,h=v.location,f=void 0===h?{pathname:"/"}:h,g=v.isMobile,y=v.onCollapse,x=v.menuItemRender,b=v.iconPrefixes,C=t.getIntlName(e),Z=t.props,j=Z.baseClassName,w=Z.menu,k=Z.collapsed,S="group"===(null==w?void 0:w.type),I=0===n||S&&1===n,M=I?Sn(e.icon,b,"".concat(j,"-icon ").concat(null===(i=t.props)||void 0===i?void 0:i.hashId)):null,B=k&&I?In(C):null,R=(0,je.jsxs)("div",{className:ge()("".concat(j,"-item-title"),null===(l=t.props)||void 0===l?void 0:l.hashId,(0,o.Z)((0,o.Z)((0,o.Z)({},"".concat(j,"-item-title-collapsed"),k),"".concat(j,"-item-title-collapsed-level-").concat(r),k),"".concat(j,"-item-collapsed-show-title"),(null==w?void 0:w.collapsedShowTitle)&&k)),children:[(0,je.jsx)("span",{className:"".concat(j,"-item-icon ").concat(null===(a=t.props)||void 0===a?void 0:a.hashId).trim(),style:{display:null!==B||M?"":"none"},children:M||(0,je.jsx)("span",{className:"anticon",children:B})}),(0,je.jsx)("span",{className:ge()("".concat(j,"-item-text"),null===(u=t.props)||void 0===u?void 0:u.hashId,(0,o.Z)({},"".concat(j,"-item-text-has-icon"),I&&(M||B))),children:C})]},m),T=Fe(m);T&&(R=(0,je.jsxs)("span",{onClick:function(){var e,n;null===(e=window)||void 0===e||null===(n=e.open)||void 0===n||n.call(e,m,"_blank")},className:ge()("".concat(j,"-item-title"),null===(s=t.props)||void 0===s?void 0:s.hashId,(0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)({},"".concat(j,"-item-title-collapsed"),k),"".concat(j,"-item-title-collapsed-level-").concat(r),k),"".concat(j,"-item-link"),!0),"".concat(j,"-item-collapsed-show-title"),(null==w?void 0:w.collapsedShowTitle)&&k)),children:[(0,je.jsx)("span",{className:"".concat(j,"-item-icon ").concat(null===(d=t.props)||void 0===d?void 0:d.hashId).trim(),style:{display:null!==B||M?"":"none"},children:M||(0,je.jsx)("span",{className:"anticon",children:B})}),(0,je.jsx)("span",{className:ge()("".concat(j,"-item-text"),null===(p=t.props)||void 0===p?void 0:p.hashId,(0,o.Z)({},"".concat(j,"-item-text-has-icon"),I&&(M||B))),children:C})]},m));if(x){var E=(0,c.Z)((0,c.Z)({},e),{},{isUrl:T,itemPath:m,isMobile:g,replace:m===f.pathname,onClick:function(){return y&&y(!0)},children:void 0});return 0===n?(0,je.jsx)(wn,{collapsed:k,title:C,disable:e.disabledTooltip,children:x(E,R,t.props)}):x(E,R,t.props)}return 0===n?(0,je.jsx)(wn,{collapsed:k,title:C,disable:e.disabledTooltip,children:R}):R})),(0,o.Z)(this,"conversionPath",(function(e){return e&&0===e.indexOf("http")?e:"/".concat(e||"").replace(/\/+/g,"/")})),this.props=n})),Bn=function(e){var n=e.mode,t=e.className,r=e.handleOpenChange,i=e.style,l=e.menuData,p=e.prefixCls,m=e.menu,v=e.matchMenuKeys,h=e.iconfontUrl,f=e.selectedKeys,g=e.onSelect,y=e.menuRenderType,x=e.openKeys,b=(0,d.useContext)(u.L_),C=b.dark,Z=b.token,j="".concat(p,"-base-menu-").concat(n),w=(0,d.useRef)([]),k=(0,s.Z)(null==m?void 0:m.defaultOpenAll),S=(0,a.Z)(k,2),I=S[0],M=S[1],B=(0,s.Z)((function(){return null!=m&&m.defaultOpenAll?(0,Pe.O7)(l)||[]:!1!==x&&[]}),{value:!1===x?void 0:x,onChange:r}),R=(0,a.Z)(B,2),T=R[0],E=R[1],N=(0,s.Z)([],{value:f,onChange:g?function(e){g&&e&&g(e)}:void 0}),H=(0,a.Z)(N,2),A=H[0],P=H[1];(0,d.useEffect)((function(){null!=m&&m.defaultOpenAll||!1===x||v&&(E(v),P(v))}),[v.join("-")]),(0,d.useEffect)((function(){h&&(kn=yn({scriptUrl:h}))}),[h]),(0,d.useEffect)((function(){if(v.join("-")!==(A||[]).join("-")&&P(v),I||!1===x||v.join("-")===(T||[]).join("-"))null!=m&&m.ignoreFlatMenu&&I?E((0,Pe.O7)(l)):M(!1);else{var e=v;!1===(null==m?void 0:m.autoClose)&&(e=Array.from(new Set([].concat((0,rn.Z)(v),(0,rn.Z)(T||[]))))),E(e)}}),[v.join("-")]);var O=(0,d.useMemo)((function(){return function(e,n){var t=n.layout,o=n.collapsed,r={};return e&&!o&&["side","mix"].includes(t||"mix")&&(r={openKeys:e}),r}(T,e)}),[T&&T.join(","),e.layout,e.collapsed]),L=function(e,n){return(0,Te.Xj)("ProLayoutBaseMenu"+n,(function(t){var o=(0,c.Z)((0,c.Z)({},t),{},{componentCls:".".concat(e)});return[jn(o,n||"inline")]}))}(j,n),z=L.wrapSSR,D=L.hashId,W=(0,d.useMemo)((function(){return new Mn((0,c.Z)((0,c.Z)({},e),{},{token:Z,menuRenderType:y,baseClassName:j,hashId:D}))}),[e,Z,y,j,D]);if(null!=m&&m.loading)return(0,je.jsx)("div",{style:null!=n&&n.includes("inline")?{padding:24}:{marginBlockStart:16},children:(0,je.jsx)(Cn.Z,{active:!0,title:!1,paragraph:{rows:null!=n&&n.includes("inline")?6:1}})});!1!==e.openKeys||e.handleOpenChange||(w.current=v);var _=e.postMenuData?e.postMenuData(l):l;return _&&(null==_?void 0:_.length)<1?null:z((0,d.createElement)(Je.Z,(0,c.Z)((0,c.Z)({},O),{},{_internalDisableMenuItemTitleTooltip:!0,key:"Menu",mode:n,inlineIndent:16,defaultOpenKeys:w.current,theme:C?"dark":"light",selectedKeys:A,style:(0,c.Z)({backgroundColor:"transparent",border:"none"},i),className:ge()(t,D,j,(0,o.Z)((0,o.Z)({},"".concat(j,"-horizontal"),"horizontal"===n),"".concat(j,"-collapsed"),e.collapsed)),items:W.getNavMenuItems(_,0,0),onOpenChange:function(n){e.collapsed||E(n)}},e.menuProps)))};var Rn=["title","render"],Tn=d.memo((function(e){return(0,je.jsx)(je.Fragment,{children:e.children})})),En=he.Z.Sider,Nn=he.Z._InternalSiderContext,Hn=void 0===Nn?{Provider:Tn}:Nn,An=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"menuHeaderRender",t=e.logo,o=e.title,r=e.layout,i=e[n];if(!1===i)return null;var l=Ve(t),a=(0,je.jsx)("h1",{children:null!=o?o:"Ant Design Pro"});return i?i(l,e.collapsed?null:a,e):e.isMobile?null:("mix"!==r||"menuHeaderRender"!==n)&&(e.collapsed?(0,je.jsx)("a",{children:l},"title"):(0,je.jsxs)("a",{children:[l,a]},"title"))},Pn=function(e){var n,t=e.collapsed,r=e.originCollapsed,i=e.fixSiderbar,a=e.menuFooterRender,s=e.onCollapse,p=e.theme,m=e.siderWidth,v=e.isMobile,h=e.onMenuHeaderClick,f=e.breakpoint,g=void 0===f?"lg":f,y=e.style,x=e.layout,b=e.menuExtraRender,C=void 0!==b&&b,Z=e.links,j=e.menuContentRender,w=e.collapsedButtonRender,k=e.prefixCls,S=e.avatarProps,I=e.rightContentRender,M=e.actionsRender,B=e.onOpenChange,R=e.stylish,T=e.logoStyle,E=(0,d.useContext)(u.L_).hashId,N=(0,d.useMemo)((function(){return!v&&"mix"!==x}),[v,x]),H="".concat(k,"-sider"),A=64,P=function(e,n){var t=n.stylish,r=n.proLayoutCollapsedWidth;return(0,Te.Xj)("ProLayoutSiderMenuStylish",(function(n){var i=(0,c.Z)((0,c.Z)({},n),{},{componentCls:".".concat(e),proLayoutCollapsedWidth:r});return t?[(0,o.Z)({},"div".concat(n.proComponentsCls,"-layout"),(0,o.Z)({},"".concat(i.componentCls),null==t?void 0:t(i)))]:[]}))}("".concat(H,".").concat(H,"-stylish"),{stylish:R,proLayoutCollapsedWidth:A}),O=ge()("".concat(H),E,(0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)({},"".concat(H,"-fixed"),i),"".concat(H,"-fixed-mix"),"mix"===x&&!v&&i),"".concat(H,"-collapsed"),e.collapsed),"".concat(H,"-layout-").concat(x),x&&!v),"".concat(H,"-light"),"dark"!==p),"".concat(H,"-mix"),"mix"===x&&!v),"".concat(H,"-stylish"),!!R)),L=An(e),z=C&&C(e),D=(0,d.useMemo)((function(){return!1!==j&&(0,d.createElement)(Bn,(0,c.Z)((0,c.Z)({},e),{},{key:"base-menu",mode:t&&!v?"vertical":"inline",handleOpenChange:B,style:{width:"100%"},className:"".concat(H,"-menu ").concat(E).trim()}))}),[H,E,j,B,e]),W=(Z||[]).map((function(e,n){return{className:"".concat(H,"-link"),label:e,key:n}})),_=(0,d.useMemo)((function(){return j?j(e,D):D}),[j,D,e]),F=(0,d.useMemo)((function(){if(!S)return null;var n=S.title,o=S.render,r=(0,l.Z)(S,Rn),i=(0,je.jsxs)("div",{className:"".concat(H,"-actions-avatar"),children:[null!=r&&r.src||null!=r&&r.srcSet||r.icon||r.children?(0,je.jsx)(Ye.Z,(0,c.Z)({size:28},r)):null,S.title&&!t&&(0,je.jsx)("span",{children:n})]});return o?o(S,i,e):i}),[S,H,t]),$=(0,d.useMemo)((function(){return M?(0,je.jsx)(qe.Z,{align:"center",size:4,direction:t?"vertical":"horizontal",className:ge()(["".concat(H,"-actions-list"),t&&"".concat(H,"-actions-list-collapsed"),E]),children:[null==M?void 0:M(e)].flat(1).map((function(e,n){return(0,je.jsx)("div",{className:"".concat(H,"-actions-list-item ").concat(E).trim(),children:e},n)}))}):null}),[M,H,t]),X=(0,d.useMemo)((function(){return(0,je.jsx)(Qe,{onItemClick:e.itemClick,appListRender:e.appListRender,appList:e.appList,prefixCls:e.prefixCls})}),[e.appList,e.appListRender,e.prefixCls]),K=(0,d.useMemo)((function(){if(!1===w)return null;var e=(0,je.jsx)(on,{isMobile:v,collapsed:r,className:"".concat(H,"-collapsed-button"),onClick:function(){null==s||s(!r)}});return w?w(t,e):e}),[w,v,r,H,t,s]),G=(0,d.useMemo)((function(){return F||$?(0,je.jsxs)("div",{className:ge()("".concat(H,"-actions"),E,t&&"".concat(H,"-actions-collapsed")),children:[F,$]}):null}),[$,F,H,t,E]),U=(0,d.useMemo)((function(){var n;return null!=e&&null!==(n=e.menu)&&void 0!==n&&n.hideMenuWhenCollapsed&&t?"".concat(H,"-hide-menu-collapsed"):null}),[H,t,null==e||null===(n=e.menu)||void 0===n?void 0:n.hideMenuWhenCollapsed]),V=a&&(null==a?void 0:a(e)),Q=(0,je.jsxs)(je.Fragment,{children:[L&&(0,je.jsxs)("div",{className:ge()([ge()("".concat(H,"-logo"),E,(0,o.Z)({},"".concat(H,"-logo-collapsed"),t))]),onClick:N?h:void 0,id:"logo",style:T,children:[L,X]}),z&&(0,je.jsx)("div",{className:ge()(["".concat(H,"-extra"),!L&&"".concat(H,"-extra-no-logo"),E]),children:z}),(0,je.jsx)("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"},children:_}),(0,je.jsxs)(Hn.Provider,{value:{},children:[Z?(0,je.jsx)("div",{className:"".concat(H,"-links ").concat(E).trim(),children:(0,je.jsx)(Je.Z,{inlineIndent:16,className:"".concat(H,"-link-menu ").concat(E).trim(),selectedKeys:[],openKeys:[],theme:p,mode:"inline",items:W})}):null,N&&(0,je.jsxs)(je.Fragment,{children:[G,!$&&I?(0,je.jsx)("div",{className:ge()("".concat(H,"-actions"),E,(0,o.Z)({},"".concat(H,"-actions-collapsed"),t)),children:null==I?void 0:I(e)}):null]}),V&&(0,je.jsx)("div",{className:ge()(["".concat(H,"-footer"),E,(0,o.Z)({},"".concat(H,"-footer-collapsed"),t)]),children:V})]})]});return P.wrapSSR((0,je.jsxs)(je.Fragment,{children:[i&&!v&&!U&&(0,je.jsx)("div",{style:(0,c.Z)({width:t?A:m,overflow:"hidden",flex:"0 0 ".concat(t?A:m,"px"),maxWidth:t?A:m,minWidth:t?A:m,transition:"all 0.2s ease 0s"},y)}),(0,je.jsxs)(En,{collapsible:!0,trigger:null,collapsed:t,breakpoint:!1===g?void 0:g,onCollapse:function(e){v||null==s||s(e)},collapsedWidth:A,style:y,theme:p,width:m,className:ge()(O,E,U),children:[U?(0,je.jsx)("div",{className:"".concat(H,"-hide-when-collapsed ").concat(E).trim(),style:{height:"100%",width:"100%",opacity:U?0:1},children:Q}):Q,K]})]}))},On=t(10178),Ln=t(9220),zn=function(e){var n,t,r,i,l;return(0,o.Z)({},e.componentCls,{"&-header-actions":{display:"flex",height:"100%",alignItems:"center","&-item":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingBlock:0,paddingInline:2,color:null===(n=e.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.colorTextRightActionsItem,fontSize:"16px",cursor:"pointer",borderRadius:e.borderRadius,"> *":{paddingInline:6,paddingBlock:6,borderRadius:e.borderRadius,"&:hover":{backgroundColor:null===(t=e.layout)||void 0===t||null===(t=t.header)||void 0===t?void 0:t.colorBgRightActionsItemHover}}},"&-avatar":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInlineStart:e.padding,paddingInlineEnd:e.padding,cursor:"pointer",color:null===(r=e.layout)||void 0===r||null===(r=r.header)||void 0===r?void 0:r.colorTextRightActionsItem,"> div":{height:"44px",color:null===(i=e.layout)||void 0===i||null===(i=i.header)||void 0===i?void 0:i.colorTextRightActionsItem,paddingInline:8,paddingBlock:8,cursor:"pointer",display:"flex",alignItems:"center",lineHeight:"44px",borderRadius:e.borderRadius,"&:hover":{backgroundColor:null===(l=e.layout)||void 0===l||null===(l=l.header)||void 0===l?void 0:l.colorBgRightActionsItemHover}}}}})};var Dn=["rightContentRender","avatarProps","actionsRender","headerContentRender"],Wn=["title","render"],_n=function(e){var n=e.rightContentRender,t=e.avatarProps,u=e.actionsRender,s=(e.headerContentRender,(0,l.Z)(e,Dn)),p=(0,d.useContext)(ve.ZP.ConfigContext).getPrefixCls,m="".concat(p(),"-pro-global-header"),v=function(e){return(0,Te.Xj)("ProLayoutRightContent",(function(n){var t=(0,c.Z)((0,c.Z)({},n),{},{componentCls:".".concat(e)});return[zn(t)]}))}(m),h=v.wrapSSR,f=v.hashId,g=(0,d.useState)("auto"),y=(0,a.Z)(g,2),x=y[0],b=y[1],C=(0,d.useMemo)((function(){if(!t)return null;var e=t.title,n=t.render,o=(0,l.Z)(t,Wn),r=[null!=o&&o.src||null!=o&&o.srcSet||o.icon||o.children?(0,d.createElement)(Ye.Z,(0,c.Z)((0,c.Z)({},o),{},{size:28,key:"avatar"})):null,e?(0,je.jsx)("span",{style:{marginInlineStart:8},children:e},"name"):void 0];return n?n(t,(0,je.jsx)("div",{children:r}),s):(0,je.jsx)("div",{children:r})}),[t]),Z=u||C?function(e){var n=u&&(null==u?void 0:u(e));return n||C?Array.isArray(n)?h((0,je.jsxs)("div",{className:"".concat(m,"-header-actions ").concat(f).trim(),children:[n.filter(Boolean).map((function(e,n){var t,r=!1;d.isValidElement(e)&&(r=!(null==e||null===(t=e.props)||void 0===t||!t["aria-hidden"]));return(0,je.jsx)("div",{className:ge()("".concat(m,"-header-actions-item ").concat(f),(0,o.Z)({},"".concat(m,"-header-actions-hover"),!r)),children:e},n)})),C&&(0,je.jsx)("span",{className:"".concat(m,"-header-actions-avatar ").concat(f).trim(),children:C})]})):h((0,je.jsxs)("div",{className:"".concat(m,"-header-actions ").concat(f).trim(),children:[n,C&&(0,je.jsx)("span",{className:"".concat(m,"-header-actions-avatar ").concat(f).trim(),children:C})]})):null}:void 0,j=(0,On.D)(function(){var e=(0,i.Z)((0,r.Z)().mark((function e(n){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:b(n);case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),160),w=Z||n;return(0,je.jsx)("div",{className:"".concat(m,"-right-content ").concat(f).trim(),style:{minWidth:x,height:"100%"},children:(0,je.jsx)("div",{style:{height:"100%"},children:(0,je.jsx)(Ln.Z,{onResize:function(e){var n=e.width;j.run(n)},children:w?(0,je.jsx)("div",{style:{display:"flex",alignItems:"center",height:"100%",justifyContent:"flex-end"},children:w((0,c.Z)((0,c.Z)({},s),{},{rightContentSize:x}))}):null})})})},Fn=function(e){var n,t;return(0,o.Z)({},e.componentCls,{position:"relative",width:"100%",height:"100%",backgroundColor:"transparent",".anticon":{color:"inherit"},"&-main":{display:"flex",height:"100%",paddingInlineStart:"16px","&-left":(0,o.Z)({display:"flex",alignItems:"center"},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16,marginInlineStart:-8})},"&-wide":{maxWidth:1152,margin:"0 auto"},"&-logo":{position:"relative",display:"flex",height:"100%",alignItems:"center",overflow:"hidden","> *:first-child":{display:"flex",alignItems:"center",minHeight:"22px",fontSize:"22px"},"> *:first-child > img":{display:"inline-block",height:"32px",verticalAlign:"middle"},"> *:first-child > h1":{display:"inline-block",marginBlock:0,marginInline:0,lineHeight:"24px",marginInlineStart:6,fontWeight:"600",fontSize:"16px",color:null===(n=e.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.colorHeaderTitle,verticalAlign:"top"}},"&-menu":{minWidth:0,display:"flex",alignItems:"center",paddingInline:6,paddingBlock:6,lineHeight:"".concat(Math.max(((null===(t=e.layout)||void 0===t||null===(t=t.header)||void 0===t?void 0:t.heightLayoutHeader)||56)-12,40),"px")}})};var $n=function(e){var n,t,r,i,l,a,s,p=(0,d.useRef)(null),m=e.onMenuHeaderClick,v=e.contentWidth,h=e.rightContentRender,f=e.className,g=e.style,x=e.headerContentRender,b=e.layout,C=e.actionsRender,Z=(0,d.useContext)(ve.ZP.ConfigContext).getPrefixCls,j=(0,d.useContext)(u.L_).dark,w="".concat(e.prefixCls||Z("pro"),"-top-nav-header"),k=function(e){return(0,Te.Xj)("ProLayoutTopNavHeader",(function(n){var t=(0,c.Z)((0,c.Z)({},n),{},{componentCls:".".concat(e)});return[Fn(t)]}))}(w),S=k.wrapSSR,I=k.hashId,M=void 0;void 0!==e.menuHeaderRender?M="menuHeaderRender":"mix"!==b&&"top"!==b||(M="headerTitleRender");var B=An((0,c.Z)((0,c.Z)({},e),{},{collapsed:!1}),M),R=(0,d.useContext)(u.L_).token,T=(0,d.useMemo)((function(){var n,t,o,r,i,l,a,s,d,p,m,v,h,f=(0,je.jsx)(ve.ZP,{theme:{hashed:(0,u.nu)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"},Menu:(0,c.Z)({},y({colorItemBg:(null===(n=R.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.colorBgHeader)||"transparent",colorSubItemBg:(null===(t=R.layout)||void 0===t||null===(t=t.header)||void 0===t?void 0:t.colorBgHeader)||"transparent",radiusItem:R.borderRadius,colorItemBgSelected:(null===(o=R.layout)||void 0===o||null===(o=o.header)||void 0===o?void 0:o.colorBgMenuItemSelected)||(null==R?void 0:R.colorBgTextHover),itemHoverBg:(null===(r=R.layout)||void 0===r||null===(r=r.header)||void 0===r?void 0:r.colorBgMenuItemHover)||(null==R?void 0:R.colorBgTextHover),colorItemBgSelectedHorizontal:(null===(i=R.layout)||void 0===i||null===(i=i.header)||void 0===i?void 0:i.colorBgMenuItemSelected)||(null==R?void 0:R.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:(null===(l=R.layout)||void 0===l||null===(l=l.header)||void 0===l?void 0:l.colorTextMenu)||(null==R?void 0:R.colorTextSecondary),colorItemTextHoverHorizontal:(null===(a=R.layout)||void 0===a||null===(a=a.header)||void 0===a?void 0:a.colorTextMenuActive)||(null==R?void 0:R.colorText),colorItemTextSelectedHorizontal:(null===(s=R.layout)||void 0===s||null===(s=s.header)||void 0===s?void 0:s.colorTextMenuSelected)||(null==R?void 0:R.colorTextBase),horizontalItemBorderRadius:4,colorItemTextHover:(null===(d=R.layout)||void 0===d||null===(d=d.header)||void 0===d?void 0:d.colorTextMenuActive)||"rgba(0, 0, 0, 0.85)",horizontalItemHoverBg:(null===(p=R.layout)||void 0===p||null===(p=p.header)||void 0===p?void 0:p.colorBgMenuItemHover)||"rgba(0, 0, 0, 0.04)",colorItemTextSelected:(null===(m=R.layout)||void 0===m||null===(m=m.header)||void 0===m?void 0:m.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:null==R?void 0:R.colorBgElevated,subMenuItemBg:null==R?void 0:R.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:null==R?void 0:R.colorBgElevated}))},token:{colorBgElevated:(null===(v=R.layout)||void 0===v||null===(v=v.header)||void 0===v?void 0:v.colorBgHeader)||"transparent"}},children:(0,je.jsx)(Bn,(0,c.Z)((0,c.Z)((0,c.Z)({theme:j?"dark":"light"},e),{},{className:"".concat(w,"-base-menu ").concat(I).trim()},e.menuProps),{},{style:(0,c.Z)({width:"100%"},null===(h=e.menuProps)||void 0===h?void 0:h.style),collapsed:!1,menuRenderType:"header",mode:"horizontal"}))});return x?x(e,f):f}),[null===(n=R.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.colorBgHeader,null===(t=R.layout)||void 0===t||null===(t=t.header)||void 0===t?void 0:t.colorBgMenuItemSelected,null===(r=R.layout)||void 0===r||null===(r=r.header)||void 0===r?void 0:r.colorBgMenuItemHover,null===(i=R.layout)||void 0===i||null===(i=i.header)||void 0===i?void 0:i.colorTextMenu,null===(l=R.layout)||void 0===l||null===(l=l.header)||void 0===l?void 0:l.colorTextMenuActive,null===(a=R.layout)||void 0===a||null===(a=a.header)||void 0===a?void 0:a.colorTextMenuSelected,null===(s=R.layout)||void 0===s||null===(s=s.header)||void 0===s?void 0:s.colorBgMenuElevated,R.borderRadius,null==R?void 0:R.colorBgTextHover,null==R?void 0:R.colorTextSecondary,null==R?void 0:R.colorText,null==R?void 0:R.colorTextBase,R.colorBgElevated,j,e,w,I,x]);return S((0,je.jsx)("div",{className:ge()(w,I,f,(0,o.Z)({},"".concat(w,"-light"),!0)),style:g,children:(0,je.jsxs)("div",{ref:p,className:ge()("".concat(w,"-main"),I,(0,o.Z)({},"".concat(w,"-wide"),"Fixed"===v&&"top"===b)),children:[B&&(0,je.jsxs)("div",{className:ge()("".concat(w,"-main-left ").concat(I)),onClick:m,children:[(0,je.jsx)(Qe,(0,c.Z)({},e)),(0,je.jsx)("div",{className:"".concat(w,"-logo ").concat(I).trim(),id:"logo",children:B},"logo")]}),(0,je.jsx)("div",{style:{flex:1},className:"".concat(w,"-menu ").concat(I).trim(),children:T}),(h||C||e.avatarProps)&&(0,je.jsx)(_n,(0,c.Z)((0,c.Z)({rightContentRender:h},e),{},{prefixCls:w}))]})}))},Xn=function(e){var n,t,r;return(0,o.Z)({},e.componentCls,(0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)({position:"relative",background:"transparent",display:"flex",alignItems:"center",marginBlock:0,marginInline:16,height:(null===(n=e.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.heightLayoutHeader)||56,boxSizing:"border-box","> a":{height:"100%"}},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16}),"&-collapsed-button",{minHeight:"22px",color:null===(t=e.layout)||void 0===t||null===(t=t.header)||void 0===t?void 0:t.colorHeaderTitle,fontSize:"18px",marginInlineEnd:"16px"}),"&-logo",{position:"relative",marginInlineEnd:"16px",a:{display:"flex",alignItems:"center",height:"100%",minHeight:"22px",fontSize:"20px"},img:{height:"28px"},h1:{height:"32px",marginBlock:0,marginInline:0,marginInlineStart:8,fontWeight:"600",color:(null===(r=e.layout)||void 0===r||null===(r=r.header)||void 0===r?void 0:r.colorHeaderTitle)||e.colorTextHeading,fontSize:"18px",lineHeight:"32px"},"&-mix":{display:"flex",alignItems:"center"}}),"&-logo-mobile",{minWidth:"24px",marginInlineEnd:0}))};var Kn=function(e,n){return!1===e?null:e?e(n,null):n},Gn=function(e){var n=e.isMobile,t=e.logo,r=e.collapsed,i=e.onCollapse,l=e.rightContentRender,a=e.menuHeaderRender,u=e.onMenuHeaderClick,s=e.className,p=e.style,m=e.layout,v=e.children,h=e.splitMenus,f=e.menuData,g=e.prefixCls,y=(0,d.useContext)(ve.ZP.ConfigContext),x=y.getPrefixCls,b=y.direction,C="".concat(g||x("pro"),"-global-header"),Z=function(e){return(0,Te.Xj)("ProLayoutGlobalHeader",(function(n){var t=(0,c.Z)((0,c.Z)({},n),{},{componentCls:".".concat(e)});return[Xn(t)]}))}(C),j=Z.wrapSSR,w=Z.hashId,k=ge()(s,C,w);if("mix"===m&&!n&&h){var S=(f||[]).map((function(e){return(0,c.Z)((0,c.Z)({},e),{},{children:void 0,routes:void 0})})),I=(0,Pe.QX)(S);return(0,je.jsx)($n,(0,c.Z)((0,c.Z)({mode:"horizontal"},e),{},{splitMenus:!1,menuData:I}))}var M=ge()("".concat(C,"-logo"),w,(0,o.Z)((0,o.Z)((0,o.Z)({},"".concat(C,"-logo-rtl"),"rtl"===b),"".concat(C,"-logo-mix"),"mix"===m),"".concat(C,"-logo-mobile"),n)),B=(0,je.jsx)("span",{className:M,children:(0,je.jsx)("a",{children:Ve(t)})},"logo");return j((0,je.jsxs)("div",{className:k,style:(0,c.Z)({},p),children:[n&&(0,je.jsx)("span",{className:"".concat(C,"-collapsed-button ").concat(w).trim(),onClick:function(){null==i||i(!r)},children:(0,je.jsx)(ze,{})}),n&&Kn(a,B),"mix"===m&&!n&&(0,je.jsxs)(je.Fragment,{children:[(0,je.jsx)(Qe,(0,c.Z)({},e)),(0,je.jsx)("div",{className:M,onClick:u,children:An((0,c.Z)((0,c.Z)({},e),{},{collapsed:!1}),"headerTitleRender")})]}),(0,je.jsx)("div",{style:{flex:1},children:v}),(l||e.actionsRender||e.avatarProps)&&(0,je.jsx)(_n,(0,c.Z)({rightContentRender:l},e))]}))},Un=function(e){var n,t,r,i;return(0,o.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,o.Z)({},"".concat(e.antCls,"-layout-header").concat(e.componentCls),{height:(null===(n=e.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.heightLayoutHeader)||56,lineHeight:"".concat((null===(t=e.layout)||void 0===t||null===(t=t.header)||void 0===t?void 0:t.heightLayoutHeader)||56,"px"),zIndex:19,width:"100%",paddingBlock:0,paddingInline:0,borderBlockEnd:"1px solid ".concat(e.colorSplit),backgroundColor:(null===(r=e.layout)||void 0===r||null===(r=r.header)||void 0===r?void 0:r.colorBgHeader)||"rgba(255, 255, 255, 0.4)",WebkitBackdropFilter:"blur(8px)",backdropFilter:"blur(8px)",transition:"background-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)","&-fixed-header":{position:"fixed",insetBlockStart:0,width:"100%",zIndex:100,insetInlineEnd:0},"&-fixed-header-scroll":{backgroundColor:(null===(i=e.layout)||void 0===i||null===(i=i.header)||void 0===i?void 0:i.colorBgScrollHeader)||"rgba(255, 255, 255, 0.8)"},"&-header-actions":{display:"flex",alignItems:"center",fontSize:"16",cursor:"pointer","& &-item":{paddingBlock:0,paddingInline:8,"&:hover":{color:e.colorText}}},"&-header-realDark":{boxShadow:"0 2px 8px 0 rgba(0, 0, 0, 65%)"},"&-header-actions-header-action":{transition:"width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)"}}))};var Vn=he.Z.Header,Qn=function(e){var n,t,r,i=e.isMobile,l=e.fixedHeader,s=e.className,p=e.style,m=e.collapsed,v=e.prefixCls,h=e.onCollapse,f=e.layout,g=e.headerRender,y=e.headerContentRender,x=(0,d.useContext)(u.L_).token,b=(0,d.useContext)(ve.ZP.ConfigContext),C=(0,d.useState)(!1),Z=(0,a.Z)(C,2),j=Z[0],w=Z[1],k=l||"mix"===f,S=(0,d.useCallback)((function(){var n="top"===f,t=(0,Pe.QX)(e.menuData||[]),o=(0,je.jsx)(Gn,(0,c.Z)((0,c.Z)({onCollapse:h},e),{},{menuData:t,children:y&&y(e,null)}));return n&&!i&&(o=(0,je.jsx)($n,(0,c.Z)((0,c.Z)({mode:"horizontal",onCollapse:h},e),{},{menuData:t}))),g&&"function"==typeof g?g(e,o):o}),[y,g,i,f,h,e]);(0,d.useEffect)((function(){var e,n=(null==b||null===(e=b.getTargetContainer)||void 0===e?void 0:e.call(b))||document.body,t=function(){var e;return n.scrollTop>((null===(e=x.layout)||void 0===e||null===(e=e.header)||void 0===e?void 0:e.heightLayoutHeader)||56)&&!j?(w(!0),!0):(j&&w(!1),!1)};if(k&&"undefined"!=typeof window)return n.addEventListener("scroll",t,{passive:!0}),function(){n.removeEventListener("scroll",t)}}),[null===(n=x.layout)||void 0===n||null===(n=n.header)||void 0===n?void 0:n.heightLayoutHeader,k,j]);var I="top"===f,M="".concat(v,"-layout-header"),B=function(e){return(0,Te.Xj)("ProLayoutHeader",(function(n){var t=(0,c.Z)((0,c.Z)({},n),{},{componentCls:".".concat(e)});return[Un(t)]}))}(M),R=B.wrapSSR,T=B.hashId,E=function(e,n){var t=n.stylish,r=n.proLayoutCollapsedWidth;return(0,Te.Xj)("ProLayoutHeaderStylish",(function(n){var i=(0,c.Z)((0,c.Z)({},n),{},{componentCls:".".concat(e),proLayoutCollapsedWidth:r});return t?[(0,o.Z)({},"div".concat(n.proComponentsCls,"-layout"),(0,o.Z)({},"".concat(i.componentCls),null==t?void 0:t(i)))]:[]}))}("".concat(M,".").concat(M,"-stylish"),{proLayoutCollapsedWidth:64,stylish:e.stylish}),N=ge()(s,T,M,(0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)({},"".concat(M,"-fixed-header"),k),"".concat(M,"-fixed-header-scroll"),j),"".concat(M,"-mix"),"mix"===f),"".concat(M,"-fixed-header-action"),!m),"".concat(M,"-top-menu"),I),"".concat(M,"-header"),!0),"".concat(M,"-stylish"),!!e.stylish));return"side"!==f||i?E.wrapSSR(R((0,je.jsx)(je.Fragment,{children:(0,je.jsxs)(ve.ZP,{theme:{hashed:(0,u.nu)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"}}},children:[k&&(0,je.jsx)(Vn,{style:(0,c.Z)({height:(null===(t=x.layout)||void 0===t||null===(t=t.header)||void 0===t?void 0:t.heightLayoutHeader)||56,lineHeight:"".concat((null===(r=x.layout)||void 0===r||null===(r=r.header)||void 0===r?void 0:r.heightLayoutHeader)||56,"px"),backgroundColor:"transparent",zIndex:19},p)}),(0,je.jsx)(Vn,{className:N,style:p,children:S()})]})}))):null},Yn=t(83832),qn=t(85265),Jn=new(t(11568).E4)("antBadgeLoadingCircle",{"0%":{display:"none",opacity:0,overflow:"hidden"},"80%":{overflow:"hidden"},"100%":{display:"unset",opacity:1}}),et=function(e){var n,t,r,i,l,a,c,u,s,d,p,m;return(0,o.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,o.Z)((0,o.Z)((0,o.Z)({},"".concat(e.antCls,"-layout-sider").concat(e.componentCls),{background:(null===(n=e.layout)||void 0===n||null===(n=n.sider)||void 0===n?void 0:n.colorMenuBackground)||"transparent"}),e.componentCls,(0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)({position:"relative",boxSizing:"border-box","&-menu":{position:"relative",zIndex:10,minHeight:"100%"}},"& ".concat(e.antCls,"-layout-sider-children"),{position:"relative",display:"flex",flexDirection:"column",height:"100%",paddingInline:null===(t=e.layout)||void 0===t||null===(t=t.sider)||void 0===t?void 0:t.paddingInlineLayoutMenu,paddingBlock:null===(r=e.layout)||void 0===r||null===(r=r.sider)||void 0===r?void 0:r.paddingBlockLayoutMenu,borderInlineEnd:"1px solid ".concat(e.colorSplit),marginInlineEnd:-1}),"".concat(e.antCls,"-menu"),(0,o.Z)((0,o.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:e.fontSizeSM,paddingBottom:4}),"".concat(e.antCls,"-menu-item:not(").concat(e.antCls,"-menu-item-selected):hover"),{color:null===(i=e.layout)||void 0===i||null===(i=i.sider)||void 0===i?void 0:i.colorTextMenuItemHover})),"&-logo",{position:"relative",display:"flex",alignItems:"center",justifyContent:"space-between",paddingInline:12,paddingBlock:16,color:null===(l=e.layout)||void 0===l||null===(l=l.sider)||void 0===l?void 0:l.colorTextMenu,cursor:"pointer",borderBlockEnd:"1px solid ".concat(null===(a=e.layout)||void 0===a||null===(a=a.sider)||void 0===a?void 0:a.colorMenuItemDivider),"> a":{display:"flex",alignItems:"center",justifyContent:"center",minHeight:22,fontSize:22,"> img":{display:"inline-block",height:22,verticalAlign:"middle"},"> h1":{display:"inline-block",height:22,marginBlock:0,marginInlineEnd:0,marginInlineStart:6,color:null===(c=e.layout)||void 0===c||null===(c=c.sider)||void 0===c?void 0:c.colorTextMenuTitle,animationName:Jn,animationDuration:".4s",animationTimingFunction:"ease",fontWeight:600,fontSize:16,lineHeight:"22px",verticalAlign:"middle"}},"&-collapsed":(0,o.Z)({flexDirection:"column-reverse",margin:0,padding:12},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginBlockEnd:8,fontSize:16,transition:"font-size 0.2s ease-in-out,color 0.2s ease-in-out"})}),"&-actions",{display:"flex",alignItems:"center",justifyContent:"space-between",marginBlock:4,marginInline:0,color:null===(u=e.layout)||void 0===u||null===(u=u.sider)||void 0===u?void 0:u.colorTextMenu,"&-collapsed":{flexDirection:"column-reverse",paddingBlock:0,paddingInline:8,fontSize:16,transition:"font-size 0.3s ease-in-out"},"&-list":{color:null===(s=e.layout)||void 0===s||null===(s=s.sider)||void 0===s?void 0:s.colorTextMenuSecondary,"&-collapsed":{marginBlockEnd:8,animationName:"none"},"&-item":{paddingInline:6,paddingBlock:6,lineHeight:"16px",fontSize:16,cursor:"pointer",borderRadius:e.borderRadius,"&:hover":{background:e.colorBgTextHover}}},"&-avatar":{fontSize:14,paddingInline:8,paddingBlock:8,display:"flex",alignItems:"center",gap:e.marginXS,borderRadius:e.borderRadius,"& *":{cursor:"pointer"},"&:hover":{background:e.colorBgTextHover}}}),"&-hide-menu-collapsed",{insetInlineStart:"-".concat(e.proLayoutCollapsedWidth-12,"px"),position:"absolute"}),"&-extra",{marginBlockEnd:16,marginBlock:0,marginInline:16,"&-no-logo":{marginBlockStart:16}}),"&-links",{width:"100%",ul:{height:"auto"}}),"&-link-menu",{border:"none",boxShadow:"none",background:"transparent"}),"&-footer",{color:null===(d=e.layout)||void 0===d||null===(d=d.sider)||void 0===d?void 0:d.colorTextMenuSecondary,paddingBlockEnd:16,fontSize:e.fontSize,animationName:Jn,animationDuration:".4s",animationTimingFunction:"ease"})),"".concat(e.componentCls).concat(e.componentCls,"-fixed"),{position:"fixed",insetBlockStart:0,insetInlineStart:0,zIndex:"100",height:"100%","&-mix":{height:"calc(100% - ".concat((null===(p=e.layout)||void 0===p||null===(p=p.header)||void 0===p?void 0:p.heightLayoutHeader)||56,"px)"),insetBlockStart:"".concat((null===(m=e.layout)||void 0===m||null===(m=m.header)||void 0===m?void 0:m.heightLayoutHeader)||56,"px")}}))};var nt=function(e){var n,t=e.isMobile,o=e.siderWidth,r=e.collapsed,i=e.onCollapse,l=e.style,a=e.className,s=e.hide,p=e.prefixCls,m=e.getContainer,v=(0,d.useContext)(u.L_).token;(0,d.useEffect)((function(){!0===t&&(null==i||i(!0))}),[t]);var h=(0,ye.Z)(e,["className","style"]),f=d.useContext(ve.ZP.ConfigContext).direction,y=function(e,n){var t=n.proLayoutCollapsedWidth;return(0,Te.Xj)("ProLayoutSiderMenu",(function(n){var o=(0,c.Z)((0,c.Z)({},n),{},{componentCls:".".concat(e),proLayoutCollapsedWidth:t});return[et(o)]}))}("".concat(p,"-sider"),{proLayoutCollapsedWidth:64}),x=y.wrapSSR,b=y.hashId,C=ge()("".concat(p,"-sider"),a,b);if(s)return null;var Z=(0,g.X)(!r,(function(){return null==i?void 0:i(!0)}));return x(t?(0,je.jsx)(qn.Z,(0,c.Z)((0,c.Z)({placement:"rtl"===f?"right":"left",className:ge()("".concat(p,"-drawer-sider"),a)},Z),{},{style:(0,c.Z)({padding:0,height:"100vh"},l),onClose:function(){null==i||i(!0)},maskClosable:!0,closable:!1,getContainer:m||!1,width:o,styles:{body:{height:"100vh",padding:0,display:"flex",flexDirection:"row",backgroundColor:null===(n=v.layout)||void 0===n||null===(n=n.sider)||void 0===n?void 0:n.colorMenuBackground}},children:(0,je.jsx)(Pn,(0,c.Z)((0,c.Z)({},h),{},{isMobile:!0,className:C,collapsed:!t&&r,splitMenus:!1,originCollapsed:r}))})):(0,je.jsx)(Pn,(0,c.Z)((0,c.Z)({className:C,originCollapsed:r},h),{},{style:l})))},tt=t(76509),ot=t(16305),rt=function(e,n){var t=e.pathname,o=void 0===t?"/":t,r=e.breadcrumb,i=e.breadcrumbMap,l=e.formatMessage,a=e.title,c=e.menu,u=void 0===c?{locale:!1}:c,s=n?"":a||"",d=function(e,n,t){if(t){var o=(0,rn.Z)(t.keys()).find((function(n){try{return!n.startsWith("http")&&(0,ot.EQ)(n)(e)}catch(e){return console.log("key",n,e),!1}}));if(o)return t.get(o)}if(n){var r=Object.keys(n).find((function(n){try{return(null==n||!n.startsWith("http"))&&(0,ot.EQ)(n)(e)}catch(e){return console.log("key",n,e),!1}}));if(r)return n[r]}return{path:""}}(o,r,i);if(!d)return{title:s,id:"",pageName:s};var p=d.name;return!1!==u.locale&&d.locale&&l&&(p=l({id:d.locale||"",defaultMessage:d.name})),p?n||!a?{title:p,id:d.locale||"",pageName:p}:{title:"".concat(p," - ").concat(a),id:d.locale||"",pageName:p}:{title:s,id:d.locale||"",pageName:s}},it=t(52676),lt=t(67159),at=t(34155),ct=function(e){var n,t,r,i,l,a,c,u,s,d,p,m,v,h,f,g,y,x,b,C,Z,j,w,k,S,I,M,B,R,T,E,N;return null!==(n=void 0===at?lt.Z:(null==at||null===(at={NODE_ENV:"production",PUBLIC_PATH:"/static/"})||void 0===at?void 0:at.ANTD_VERSION)||lt.Z)&&void 0!==n&&n.startsWith("5")?{}:(0,o.Z)((0,o.Z)((0,o.Z)({},e.componentCls,(0,o.Z)((0,o.Z)({width:"100%",height:"100%"},"".concat(e.proComponentsCls,"-base-menu"),(Z={color:null===(t=e.layout)||void 0===t||null===(t=t.sider)||void 0===t?void 0:t.colorTextMenu},(0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)(Z,"".concat(e.antCls,"-menu-sub"),{backgroundColor:"transparent!important",color:null===(r=e.layout)||void 0===r||null===(r=r.sider)||void 0===r?void 0:r.colorTextMenu}),"& ".concat(e.antCls,"-layout"),{backgroundColor:"transparent",width:"100%"}),"".concat(e.antCls,"-menu-submenu-expand-icon, ").concat(e.antCls,"-menu-submenu-arrow"),{color:"inherit"}),"&".concat(e.antCls,"-menu"),(0,o.Z)((0,o.Z)({color:null===(i=e.layout)||void 0===i||null===(i=i.sider)||void 0===i?void 0:i.colorTextMenu},"".concat(e.antCls,"-menu-item"),{"*":{transition:"none !important"}}),"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-inline"),(0,o.Z)({},"".concat(e.antCls,"-menu-selected::after,").concat(e.antCls,"-menu-item-selected::after"),{display:"none"})),"".concat(e.antCls,"-menu-sub ").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,"-menu-item:active, \n        ").concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"}),"&".concat(e.antCls,"-menu-light"),(0,o.Z)({},"".concat(e.antCls,"-menu-item:hover, \n            ").concat(e.antCls,"-menu-item-active,\n            ").concat(e.antCls,"-menu-submenu-active, \n            ").concat(e.antCls,"-menu-submenu-title:hover"),(0,o.Z)({color:null===(l=e.layout)||void 0===l||null===(l=l.sider)||void 0===l?void 0:l.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:null===(a=e.layout)||void 0===a||null===(a=a.sider)||void 0===a?void 0:a.colorTextMenuActive}))),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,o.Z)((0,o.Z)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:null===(c=e.layout)||void 0===c||null===(c=c.sider)||void 0===c?void 0:c.colorBgMenuItemSelected,borderRadius:e.borderRadius}),"".concat(e.antCls,"-menu-item:hover, \n            ").concat(e.antCls,"-menu-item-active,\n            ").concat(e.antCls,"-menu-submenu-title:hover"),(0,o.Z)({color:null===(u=e.layout)||void 0===u||null===(u=u.sider)||void 0===u?void 0:u.colorTextMenuActive,borderRadius:e.borderRadius,backgroundColor:"".concat(null===(s=e.layout)||void 0===s||null===(s=s.header)||void 0===s?void 0:s.colorBgMenuItemHover," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:null===(d=e.layout)||void 0===d||null===(d=d.sider)||void 0===d?void 0:d.colorTextMenuActive}))),"".concat(e.antCls,"-menu-item-selected"),{color:null===(p=e.layout)||void 0===p||null===(p=p.sider)||void 0===p?void 0:p.colorTextMenuSelected}),(0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)(Z,"".concat(e.antCls,"-menu-submenu-selected"),{color:null===(m=e.layout)||void 0===m||null===(m=m.sider)||void 0===m?void 0:m.colorTextMenuSelected}),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-inline) ").concat(e.antCls,"-menu-submenu-open"),{color:null===(v=e.layout)||void 0===v||null===(v=v.sider)||void 0===v?void 0:v.colorTextMenuSelected}),"&".concat(e.antCls,"-menu-vertical"),(0,o.Z)({},"".concat(e.antCls,"-menu-submenu-selected"),{borderRadius:e.borderRadius,color:null===(h=e.layout)||void 0===h||null===(h=h.sider)||void 0===h?void 0:h.colorTextMenuSelected})),"".concat(e.antCls,"-menu-submenu:hover > ").concat(e.antCls,"-menu-submenu-title > ").concat(e.antCls,"-menu-submenu-arrow"),{color:null===(f=e.layout)||void 0===f||null===(f=f.sider)||void 0===f?void 0:f.colorTextMenuActive}),"&".concat(e.antCls,"-menu-horizontal"),(0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)({},"".concat(e.antCls,"-menu-item:hover,\n          ").concat(e.antCls,"-menu-submenu:hover,\n          ").concat(e.antCls,"-menu-item-active,\n          ").concat(e.antCls,"-menu-submenu-active"),{borderRadius:4,transition:"none",color:null===(g=e.layout)||void 0===g||null===(g=g.header)||void 0===g?void 0:g.colorTextMenuActive,backgroundColor:"".concat(null===(y=e.layout)||void 0===y||null===(y=y.header)||void 0===y?void 0:y.colorBgMenuItemHover," !important")}),"".concat(e.antCls,"-menu-item-open,\n          ").concat(e.antCls,"-menu-submenu-open,\n          ").concat(e.antCls,"-menu-item-selected,\n          ").concat(e.antCls,"-menu-submenu-selected"),(0,o.Z)({backgroundColor:null===(x=e.layout)||void 0===x||null===(x=x.header)||void 0===x?void 0:x.colorBgMenuItemSelected,borderRadius:e.borderRadius,transition:"none",color:"".concat(null===(b=e.layout)||void 0===b||null===(b=b.header)||void 0===b?void 0:b.colorTextMenuSelected," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:"".concat(null===(C=e.layout)||void 0===C||null===(C=C.header)||void 0===C?void 0:C.colorTextMenuSelected," !important")})),"> ".concat(e.antCls,"-menu-item, > ").concat(e.antCls,"-menu-submenu"),{paddingInline:16,marginInline:4}),"> ".concat(e.antCls,"-menu-item::after, > ").concat(e.antCls,"-menu-submenu::after"),{display:"none"})))),"".concat(e.proComponentsCls,"-top-nav-header-base-menu"),(0,o.Z)((0,o.Z)({},"&".concat(e.antCls,"-menu"),(0,o.Z)({color:null===(j=e.layout)||void 0===j||null===(j=j.header)||void 0===j?void 0:j.colorTextMenu},"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-light"),(0,o.Z)((0,o.Z)({},"".concat(e.antCls,"-menu-item:hover, \n            ").concat(e.antCls,"-menu-item-active,\n            ").concat(e.antCls,"-menu-submenu-active, \n            ").concat(e.antCls,"-menu-submenu-title:hover"),(0,o.Z)({color:null===(w=e.layout)||void 0===w||null===(w=w.header)||void 0===w?void 0:w.colorTextMenuActive,borderRadius:e.borderRadius,transition:"none",backgroundColor:null===(k=e.layout)||void 0===k||null===(k=k.header)||void 0===k?void 0:k.colorBgMenuItemSelected},"".concat(e.antCls,"-menu-submenu-arrow"),{color:null===(S=e.layout)||void 0===S||null===(S=S.header)||void 0===S?void 0:S.colorTextMenuActive})),"".concat(e.antCls,"-menu-item-selected"),{color:null===(I=e.layout)||void 0===I||null===(I=I.header)||void 0===I?void 0:I.colorTextMenuSelected,borderRadius:e.borderRadius,backgroundColor:null===(M=e.layout)||void 0===M||null===(M=M.header)||void 0===M?void 0:M.colorBgMenuItemSelected})))),"".concat(e.antCls,"-menu-sub").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,"-menu-submenu-popup"),(0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)({backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"},"".concat(e.antCls,"-menu"),(0,o.Z)({background:"transparent !important",backgroundColor:"transparent !important"},"".concat(e.antCls,"-menu-item:active, \n        ").concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"})),"".concat(e.antCls,"-menu-item-selected"),{color:null===(B=e.layout)||void 0===B||null===(B=B.sider)||void 0===B?void 0:B.colorTextMenuSelected}),"".concat(e.antCls,"-menu-submenu-selected"),{color:null===(R=e.layout)||void 0===R||null===(R=R.sider)||void 0===R?void 0:R.colorTextMenuSelected}),"".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,o.Z)((0,o.Z)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:"rgba(0, 0, 0, 0.04)",borderRadius:e.borderRadius,color:null===(T=e.layout)||void 0===T||null===(T=T.sider)||void 0===T?void 0:T.colorTextMenuSelected}),"".concat(e.antCls,"-menu-item:hover, \n          ").concat(e.antCls,"-menu-item-active,\n          ").concat(e.antCls,"-menu-submenu-title:hover"),(0,o.Z)({color:null===(E=e.layout)||void 0===E||null===(E=E.sider)||void 0===E?void 0:E.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:null===(N=e.layout)||void 0===N||null===(N=N.sider)||void 0===N?void 0:N.colorTextMenuActive}))))},ut=function(e){var n,t,r,i;return(0,o.Z)((0,o.Z)({},"".concat(e.antCls,"-layout"),{backgroundColor:"transparent !important"}),e.componentCls,(0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)({},"& ".concat(e.antCls,"-layout"),{display:"flex",backgroundColor:"transparent",width:"100%"}),"".concat(e.componentCls,"-content"),{display:"flex",flexDirection:"column",width:"100%",backgroundColor:(null===(n=e.layout)||void 0===n||null===(n=n.pageContainer)||void 0===n?void 0:n.colorBgPageContainer)||"transparent",position:"relative",paddingBlock:null===(t=e.layout)||void 0===t||null===(t=t.pageContainer)||void 0===t?void 0:t.paddingBlockPageContainerContent,paddingInline:null===(r=e.layout)||void 0===r||null===(r=r.pageContainer)||void 0===r?void 0:r.paddingInlinePageContainerContent,"&-has-page-container":{padding:0}}),"".concat(e.componentCls,"-container"),{width:"100%",display:"flex",flexDirection:"column",minWidth:0,minHeight:0,backgroundColor:"transparent"}),"".concat(e.componentCls,"-bg-list"),{pointerEvents:"none",position:"fixed",overflow:"hidden",insetBlockStart:0,insetInlineStart:0,zIndex:0,height:"100%",width:"100%",background:null===(i=e.layout)||void 0===i?void 0:i.bgLayout}))};var st=t(34155),dt=function(e,n,t){var o=e,r=o.breadcrumbName,i=o.title,l=o.path;return t.findIndex((function(n){return n.linkPath===e.path}))===t.length-1?(0,je.jsx)("span",{children:i||r}):(0,je.jsx)("span",{onClick:l?function(){return location.href=l}:void 0,children:i||r})},pt=function(e,n,t){var o=function(e){if(!e||"/"===e)return["/"];var n=e.split("/").filter((function(e){return e}));return n.map((function(e,t){return"/".concat(n.slice(0,t+1).join("/"))}))}(null==e?void 0:e.pathname),r=o.map((function(e){var o=function(e,n){var t=e.get(n);if(!t){var o=(Array.from(e.keys())||[]).find((function(e){try{return(null==e||!e.startsWith("http"))&&(0,ot.EQ)(e.replace("?",""))(n)}catch(n){return console.log("path",e,n),!1}}));o&&(t=e.get(o))}return t||{path:""}}(n,e),r=function(e,n){var t=n.formatMessage,o=n.menu;return e.locale&&t&&!1!==(null==o?void 0:o.locale)?t({id:e.locale,defaultMessage:e.name}):e.name}(o,t),i=o.hideInBreadcrumb;return r&&!i?{linkPath:e,breadcrumbName:r,title:r,component:o.component}:{linkPath:"",breadcrumbName:"",title:""}})).filter((function(e){return e&&e.linkPath}));return r},mt=function(e,n){var t=e.breadcrumbRender,o=e.itemRender,r=(n.breadcrumbProps||{}).minLength,i=void 0===r?2:r,l=function(e){var n=function(e){return{location:e.location,breadcrumbMap:e.breadcrumbMap}}(e),t=n.location,o=n.breadcrumbMap;return t&&t.pathname&&o?pt(t,o,e):[]}(e),a=function(e){for(var n=o||dt,t=arguments.length,r=new Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];return null==n?void 0:n.apply(void 0,[(0,c.Z)((0,c.Z)({},e),{},{path:e.linkPath||e.path})].concat(r))},u=l;return t&&(u=t(u||[])||void 0),(u&&u.length<i||!1===t)&&(u=void 0),(0,f.n)(void 0===st?lt.Z:(null==st||null===(st={NODE_ENV:"production",PUBLIC_PATH:"/static/"})||void 0===st?void 0:st.ANTD_VERSION)||lt.Z,"5.3.0")>-1?{items:u,itemRender:a}:{routes:u,itemRender:a}};var vt=function e(n,t,o,r){var i,l=ce(n,(null==t?void 0:t.locale)||!1,o,!0),c=l.menuData,u=l.breadcrumb;return r?e(r(c),t,o,void 0):{breadcrumb:(i=u,(0,rn.Z)(i).reduce((function(e,n){var t=(0,a.Z)(n,2),o=t[0],r=t[1];return e[o]=r,e}),{})),breadcrumbMap:u,menuData:c}},ht=t(71002),ft=t(51812),gt=["id","defaultMessage"],yt=["fixSiderbar","navTheme","layout"],xt=0,bt=function(e){var n,t,p,m,f,g,x,b,C,Z,j,w,k,S,I=e||{},M=I.children,B=I.onCollapse,R=I.location,T=void 0===R?{pathname:"/"}:R,E=I.contentStyle,N=I.route,H=I.defaultCollapsed,A=I.style,P=I.siderWidth,O=I.menu,L=I.siderMenuType,z=I.isChildrenLayout,D=I.menuDataRender,W=I.actionRef,_=I.bgLayoutImgList,F=I.formatMessage,$=I.loading,X=(0,d.useMemo)((function(){return P||("mix"===e.layout?215:256)}),[e.layout,P]),K=(0,d.useContext)(ve.ZP.ConfigContext),G=null!==(n=e.prefixCls)&&void 0!==n?n:K.getPrefixCls("pro"),U=(0,s.Z)(!1,{value:null==O?void 0:O.loading,onChange:null==O?void 0:O.onLoadingChange}),V=(0,a.Z)(U,2),Q=V[0],Y=V[1],q=(0,d.useState)((function(){return"pro-layout-".concat(xt+=1)})),J=(0,a.Z)(q,1)[0],ee=(0,d.useCallback)((function(e){var n=e.id,t=e.defaultMessage,o=(0,l.Z)(e,gt);if(F)return F((0,c.Z)({id:n,defaultMessage:t},o));var r=(0,it.e)();return r[n]?r[n]:t}),[F]),ne=(0,be.ZP)([J,null==O?void 0:O.params],function(){var e=(0,i.Z)((0,r.Z)().mark((function e(n){var t,o,i,l;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=(0,a.Z)(n,2),i=o[1],Y(!0),e.next=4,null==O||null===(t=O.request)||void 0===t?void 0:t.call(O,i||{},(null==N?void 0:N.children)||(null==N?void 0:N.routes)||[]);case 4:return l=e.sent,Y(!1),e.abrupt("return",l);case 7:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),{revalidateOnFocus:!1,shouldRetryOnError:!1,revalidateOnReconnect:!1}),te=ne.data,oe=ne.mutate,re=ne.isLoading;(0,d.useEffect)((function(){Y(re)}),[re]);var ie=(0,Ce.kY)().cache;(0,d.useEffect)((function(){return function(){ie instanceof Map&&ie.delete(J)}}),[]);var le=(0,d.useMemo)((function(){return vt(te||(null==N?void 0:N.children)||(null==N?void 0:N.routes)||[],O,ee,D)}),[ee,O,D,te,null==N?void 0:N.children,null==N?void 0:N.routes])||{},ae=le.breadcrumb,ce=le.breadcrumbMap,ue=le.menuData,se=void 0===ue?[]:ue;W&&null!=O&&O.request&&(W.current={reload:function(){oe()}});var de=(0,d.useMemo)((function(){return me(T.pathname||"/",se||[],!0)}),[T.pathname,se]),pe=(0,d.useMemo)((function(){return Array.from(new Set(de.map((function(e){return e.key||e.path||""}))))}),[de]),fe=de[de.length-1]||{},Ze=function(e){var n=(0,d.useState)({}),t=(0,a.Z)(n,2),o=t[0],r=t[1];return(0,d.useEffect)((function(){r((0,ft.Y)({layout:"object"!==(0,ht.Z)(e.layout)?e.layout:void 0,navTheme:e.navTheme,menuRender:e.menuRender,footerRender:e.footerRender,menuHeaderRender:e.menuHeaderRender,headerRender:e.headerRender,fixSiderbar:e.fixSiderbar}))}),[e.layout,e.navTheme,e.menuRender,e.footerRender,e.menuHeaderRender,e.headerRender,e.fixSiderbar]),o}(fe),ke=(0,c.Z)((0,c.Z)({},e),Ze),Se=ke.fixSiderbar,Ie=(ke.navTheme,ke.layout),Me=(0,l.Z)(ke,yt),Be=v(),Re=(0,d.useMemo)((function(){return("sm"===Be||"xs"===Be)&&!e.disableMobile}),[Be,e.disableMobile]),Ee="top"!==Ie&&!Re,Ne=(0,s.Z)((function(){return void 0!==H?H:!!Re||"md"===Be}),{value:e.collapsed,onChange:B}),He=(0,a.Z)(Ne,2),Oe=He[0],Le=He[1],ze=(0,ye.Z)((0,c.Z)((0,c.Z)((0,c.Z)({prefixCls:G},e),{},{siderWidth:X},Ze),{},{formatMessage:ee,breadcrumb:ae,menu:(0,c.Z)((0,c.Z)({},O),{},{type:L||(null==O?void 0:O.type),loading:Q}),layout:Ie}),["className","style","breadcrumbRender"]),De=function(e,n){var t=n.pageTitleRender,o=rt(e);if(!1===t)return{title:n.title||"",id:"",pageName:""};if(t){var r=t(e,o.title,o);if("string"==typeof r)return rt((0,c.Z)((0,c.Z)({},o),{},{title:r}));(0,xe.ZP)("string"==typeof r,"pro-layout: renderPageTitle return value should be a string")}return o}((0,c.Z)((0,c.Z)({pathname:T.pathname},ze),{},{breadcrumbMap:ce}),e),We=mt((0,c.Z)((0,c.Z)({},ze),{},{breadcrumbRender:e.breadcrumbRender,breadcrumbMap:ce}),e),_e=function(e,n){var t,o=e.layout,r=e.isMobile,i=e.selectedKeys,l=e.openKeys,u=e.splitMenus,s=e.suppressSiderWhenMenuEmpty,d=e.menuRender;if(!1===e.menuRender||e.pure)return null;var p=e.menuData;if(u&&(!1!==l||"mix"===o)&&!r){var m,v=i||n,h=(0,a.Z)(v,1)[0];p=h&&(null===(m=e.menuData)||void 0===m||null===(m=m.find((function(e){return e.key===h})))||void 0===m?void 0:m.children)||[]}var f,g=(0,Pe.QX)(p||[]);if(g&&(null==g?void 0:g.length)<1&&(u||s))return null;if("top"===o&&!r)return(0,je.jsx)(nt,(0,c.Z)((0,c.Z)({matchMenuKeys:n},e),{},{hide:!0,stylish:null===(f=e.stylish)||void 0===f?void 0:f.sider}));var y=(0,je.jsx)(nt,(0,c.Z)((0,c.Z)({matchMenuKeys:n},e),{},{menuData:g,stylish:null===(t=e.stylish)||void 0===t?void 0:t.sider}));return d?d(e,y):y}((0,c.Z)((0,c.Z)({},ze),{},{menuData:se,onCollapse:Le,isMobile:Re,collapsed:Oe}),pe),Fe=function(e,n){var t;return!1===e.headerRender||e.pure?null:(0,je.jsx)(Qn,(0,c.Z)((0,c.Z)({matchMenuKeys:n},e),{},{stylish:null===(t=e.stylish)||void 0===t?void 0:t.header}))}((0,c.Z)((0,c.Z)({},ze),{},{children:null,hasSiderMenu:!!_e,menuData:se,isMobile:Re,collapsed:Oe,onCollapse:Le}),pe),$e=function(e){return!1===e.footerRender||e.pure?null:e.footerRender?e.footerRender((0,c.Z)({},e),(0,je.jsx)(Ae,{})):null}((0,c.Z)({isMobile:Re,collapsed:Oe},ze)),Xe=(0,d.useContext)(tt.X).isChildrenLayout,Ke=void 0!==z?z:Xe,Ge="".concat(G,"-layout"),Ue=function(e){return(0,Te.Xj)("ProLayout",(function(n){var t=(0,c.Z)((0,c.Z)({},n),{},{componentCls:".".concat(e)});return[ut(t),ct(t)]}))}(Ge),Ve=Ue.wrapSSR,Qe=Ue.hashId,Ye=ge()(e.className,Qe,"ant-design-pro",Ge,(0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)((0,o.Z)({},"screen-".concat(Be),Be),"".concat(Ge,"-top-menu"),"top"===Ie),"".concat(Ge,"-is-children"),Ke),"".concat(Ge,"-fix-siderbar"),Se),"".concat(Ge,"-").concat(Ie),Ie)),qe=function(e,n,t){return e?n?64:t:0}(!!Ee,Oe,X),Je={position:"relative"};(Ke||E&&E.minHeight)&&(Je.minHeight=0),(0,d.useEffect)((function(){var n;null===(n=e.onPageChange)||void 0===n||n.call(e,e.location)}),[T.pathname,null===(t=T.pathname)||void 0===t?void 0:t.search]);var en,nn,tn,on=(0,d.useState)(!1),rn=(0,a.Z)(on,2),ln=rn[0],an=rn[1],cn=(0,d.useState)(0),un=(0,a.Z)(cn,2),sn=un[0],dn=un[1];en=De,nn=e.title||!1,tn="string"==typeof en.pageName?en.title:nn,(0,d.useEffect)((function(){(0,h.j)()&&tn&&(document.title=tn)}),[en.title,tn]);var pn=(0,d.useContext)(u.L_).token,mn=(0,d.useMemo)((function(){return _&&_.length>0?null==_?void 0:_.map((function(e,n){return(0,je.jsx)("img",{src:e.src,style:(0,c.Z)({position:"absolute"},e)},n)})):null}),[_]);return Ve((0,je.jsx)(tt.X.Provider,{value:(0,c.Z)((0,c.Z)({},ze),{},{breadcrumb:We,menuData:se,isMobile:Re,collapsed:Oe,hasPageContainer:sn,setHasPageContainer:dn,isChildrenLayout:!0,title:De.pageName,hasSiderMenu:!!_e,hasHeader:!!Fe,siderWidth:qe,hasFooter:!!$e,hasFooterToolbar:ln,setHasFooterToolbar:an,pageTitleInfo:De,matchMenus:de,matchMenuKeys:pe,currentMenu:fe}),children:e.pure?(0,je.jsx)(je.Fragment,{children:M}):(0,je.jsxs)("div",{className:Ye,children:[mn||null!==(p=pn.layout)&&void 0!==p&&p.bgLayout?(0,je.jsx)("div",{className:ge()("".concat(Ge,"-bg-list"),Qe),children:mn}):null,(0,je.jsxs)(he.Z,{style:(0,c.Z)({minHeight:"100%",flexDirection:_e?"row":void 0},A),children:[(0,je.jsx)(ve.ZP,{theme:{hashed:(0,u.nu)(),token:{controlHeightLG:(null===(m=pn.layout)||void 0===m||null===(m=m.sider)||void 0===m?void 0:m.menuHeight)||(null==pn?void 0:pn.controlHeightLG)},components:{Menu:y({colorItemBg:(null===(f=pn.layout)||void 0===f||null===(f=f.sider)||void 0===f?void 0:f.colorMenuBackground)||"transparent",colorSubItemBg:(null===(g=pn.layout)||void 0===g||null===(g=g.sider)||void 0===g?void 0:g.colorMenuBackground)||"transparent",radiusItem:pn.borderRadius,colorItemBgSelected:(null===(x=pn.layout)||void 0===x||null===(x=x.sider)||void 0===x?void 0:x.colorBgMenuItemSelected)||(null==pn?void 0:pn.colorBgTextHover),colorItemBgHover:(null===(b=pn.layout)||void 0===b||null===(b=b.sider)||void 0===b?void 0:b.colorBgMenuItemHover)||(null==pn?void 0:pn.colorBgTextHover),colorItemBgActive:(null===(C=pn.layout)||void 0===C||null===(C=C.sider)||void 0===C?void 0:C.colorBgMenuItemActive)||(null==pn?void 0:pn.colorBgTextActive),colorItemBgSelectedHorizontal:(null===(Z=pn.layout)||void 0===Z||null===(Z=Z.sider)||void 0===Z?void 0:Z.colorBgMenuItemSelected)||(null==pn?void 0:pn.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:(null===(j=pn.layout)||void 0===j||null===(j=j.sider)||void 0===j?void 0:j.colorTextMenu)||(null==pn?void 0:pn.colorTextSecondary),colorItemTextHover:(null===(w=pn.layout)||void 0===w||null===(w=w.sider)||void 0===w?void 0:w.colorTextMenuItemHover)||"rgba(0, 0, 0, 0.85)",colorItemTextSelected:(null===(k=pn.layout)||void 0===k||null===(k=k.sider)||void 0===k?void 0:k.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:null==pn?void 0:pn.colorBgElevated,subMenuItemBg:null==pn?void 0:pn.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:null==pn?void 0:pn.colorBgElevated})}},children:_e}),(0,je.jsxs)("div",{style:Je,className:"".concat(Ge,"-container ").concat(Qe).trim(),children:[Fe,(0,je.jsx)(we,(0,c.Z)((0,c.Z)({hasPageContainer:sn,isChildrenLayout:Ke},Me),{},{hasHeader:!!Fe,prefixCls:Ge,style:E,children:$?(0,je.jsx)(Yn.S,{}):M})),$e,ln&&(0,je.jsx)("div",{className:"".concat(Ge,"-has-footer"),style:{height:64,marginBlockStart:null===(S=pn.layout)||void 0===S||null===(S=S.pageContainer)||void 0===S?void 0:S.paddingBlockPageContainerContent}})]})]})]})}))},Ct=function(e){var n=e.colorPrimary,t=void 0!==e.navTheme?{dark:"realDark"===e.navTheme}:{};return(0,je.jsx)(ve.ZP,{theme:n?{token:{colorPrimary:n}}:void 0,children:(0,je.jsx)(u._Y,(0,c.Z)((0,c.Z)({},t),{},{token:e.token,prefixCls:e.prefixCls,children:(0,je.jsx)(bt,(0,c.Z)((0,c.Z)({logo:(0,je.jsx)(ke,{})},Zn.h),{},{location:(0,h.j)()?window.location:void 0},e))}))})}},83832:function(e,n,t){"use strict";t.d(n,{S:function(){return c}});var o=t(1413),r=t(91),i=t(74330),l=(t(67294),t(85893)),a=["isLoading","pastDelay","timedOut","error","retry"],c=function(e){e.isLoading,e.pastDelay,e.timedOut,e.error,e.retry;var n=(0,r.Z)(e,a);return(0,l.jsx)("div",{style:{paddingBlockStart:100,textAlign:"center"},children:(0,l.jsx)(i.Z,(0,o.Z)({size:"large"},n))})}},76509:function(e,n,t){"use strict";t.d(n,{X:function(){return o}});var o=(0,t(67294).createContext)({})},90743:function(e,n){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function o(e,n){void 0===n&&(n={});for(var t=function(e){for(var n=[],t=0;t<e.length;){var o=e[t];if("*"!==o&&"+"!==o&&"?"!==o)if("\\"!==o)if("{"!==o)if("}"!==o)if(":"!==o)if("("!==o)n.push({type:"CHAR",index:t,value:e[t++]});else{var r=1,i="";if("?"===e[a=t+1])throw new TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;)if("\\"!==e[a]){if(")"===e[a]){if(0==--r){a++;break}}else if("("===e[a]&&(r++,"?"!==e[a+1]))throw new TypeError("Capturing groups are not allowed at "+a);i+=e[a++]}else i+=e[a++]+e[a++];if(r)throw new TypeError("Unbalanced pattern at "+t);if(!i)throw new TypeError("Missing pattern at "+t);n.push({type:"PATTERN",index:t,value:i}),t=a}else{for(var l="",a=t+1;a<e.length;){var c=e.charCodeAt(a);if(!(c>=48&&c<=57||c>=65&&c<=90||c>=97&&c<=122||95===c))break;l+=e[a++]}if(!l)throw new TypeError("Missing parameter name at "+t);n.push({type:"NAME",index:t,value:l}),t=a}else n.push({type:"CLOSE",index:t,value:e[t++]});else n.push({type:"OPEN",index:t,value:e[t++]});else n.push({type:"ESCAPED_CHAR",index:t++,value:e[t++]});else n.push({type:"MODIFIER",index:t,value:e[t++]})}return n.push({type:"END",index:t,value:""}),n}(e),o=n.prefixes,r=void 0===o?"./":o,i="[^"+l(n.delimiter||"/#?")+"]+?",a=[],c=0,u=0,s="",d=function(e){if(u<t.length&&t[u].type===e)return t[u++].value},p=function(e){var n=d(e);if(void 0!==n)return n;var o=t[u],r=o.type,i=o.index;throw new TypeError("Unexpected "+r+" at "+i+", expected "+e)},m=function(){for(var e,n="";e=d("CHAR")||d("ESCAPED_CHAR");)n+=e;return n};u<t.length;){var v=d("CHAR"),h=d("NAME"),f=d("PATTERN");if(h||f){var g=v||"";-1===r.indexOf(g)&&(s+=g,g=""),s&&(a.push(s),s=""),a.push({name:h||c++,prefix:g,suffix:"",pattern:f||i,modifier:d("MODIFIER")||""})}else{var y=v||d("ESCAPED_CHAR");if(y)s+=y;else if(s&&(a.push(s),s=""),d("OPEN")){g=m();var x=d("NAME")||"",b=d("PATTERN")||"",C=m();p("CLOSE"),a.push({name:x||(b?c++:""),pattern:x&&!b?i:b,prefix:g,suffix:C,modifier:d("MODIFIER")||""})}else p("END")}}return a}function r(e,n){void 0===n&&(n={});var o=a(n),r=n.encode,i=void 0===r?function(e){return e}:r,l=n.validate,c=void 0===l||l,u=e.map((function(e){if("object"===t(e))return new RegExp("^(?:"+e.pattern+")$",o)}));return function(n){for(var t="",o=0;o<e.length;o++){var r=e[o];if("string"!=typeof r){var l=n?n[r.name]:void 0,a="?"===r.modifier||"*"===r.modifier,s="*"===r.modifier||"+"===r.modifier;if(Array.isArray(l)){if(!s)throw new TypeError('Expected "'+r.name+'" to not repeat, but got an array');if(0===l.length){if(a)continue;throw new TypeError('Expected "'+r.name+'" to not be empty')}for(var d=0;d<l.length;d++){var p=i(l[d],r);if(c&&!u[o].test(p))throw new TypeError('Expected all "'+r.name+'" to match "'+r.pattern+'", but got "'+p+'"');t+=r.prefix+p+r.suffix}}else if("string"!=typeof l&&"number"!=typeof l){if(!a){var m=s?"an array":"a string";throw new TypeError('Expected "'+r.name+'" to be '+m)}}else{p=i(String(l),r);if(c&&!u[o].test(p))throw new TypeError('Expected "'+r.name+'" to match "'+r.pattern+'", but got "'+p+'"');t+=r.prefix+p+r.suffix}}else t+=r}return t}}function i(e,n,t){void 0===t&&(t={});var o=t.decode,r=void 0===o?function(e){return e}:o;return function(t){var o=e.exec(t);if(!o)return!1;for(var i=o[0],l=o.index,a=Object.create(null),c=function(e){if(void 0===o[e])return"continue";var t=n[e-1];"*"===t.modifier||"+"===t.modifier?a[t.name]=o[e].split(t.prefix+t.suffix).map((function(e){return r(e,t)})):a[t.name]=r(o[e],t)},u=1;u<o.length;u++)c(u);return{path:i,index:l,params:a}}}function l(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function c(e,n,t){void 0===t&&(t={});for(var o=t.strict,r=void 0!==o&&o,i=t.start,c=void 0===i||i,u=t.end,s=void 0===u||u,d=t.encode,p=void 0===d?function(e){return e}:d,m="["+l(t.endsWith||"")+"]|$",v="["+l(t.delimiter||"/#?")+"]",h=c?"^":"",f=0,g=e;f<g.length;f++){var y=g[f];if("string"==typeof y)h+=l(p(y));else{var x=l(p(y.prefix)),b=l(p(y.suffix));if(y.pattern)if(n&&n.push(y),x||b)if("+"===y.modifier||"*"===y.modifier){var C="*"===y.modifier?"?":"";h+="(?:"+x+"((?:"+y.pattern+")(?:"+b+x+"(?:"+y.pattern+"))*)"+b+")"+C}else h+="(?:"+x+"("+y.pattern+")"+b+")"+y.modifier;else h+="("+y.pattern+")"+y.modifier;else h+="(?:"+x+b+")"+y.modifier}}if(s)r||(h+=v+"?"),h+=t.endsWith?"(?="+m+")":"$";else{var Z=e[e.length-1],j="string"==typeof Z?v.indexOf(Z[Z.length-1])>-1:void 0===Z;r||(h+="(?:"+v+"(?="+m+"))?"),j||(h+="(?="+v+"|"+m+")")}return new RegExp(h,a(t))}function u(e,n,t){return e instanceof RegExp?function(e,n){if(!n)return e;var t=e.source.match(/\((?!\?)/g);if(t)for(var o=0;o<t.length;o++)n.push({name:o,prefix:"",suffix:"",modifier:"",pattern:""});return e}(e,n):Array.isArray(e)?function(e,n,t){var o=e.map((function(e){return u(e,n,t).source}));return new RegExp("(?:"+o.join("|")+")",a(t))}(e,n,t):function(e,n,t){return c(o(e,t),n,t)}(e,n,t)}n.Bo=void 0,n.Bo=u},78164:function(e,n,t){"use strict";t.d(n,{S:function(){return p}});var o=t(15671),r=t(43144),i=t(97326),l=t(60136),a=t(29388),c=t(4942),u=t(29905),s=t(67294),d=t(85893),p=function(e){(0,l.Z)(t,e);var n=(0,a.Z)(t);function t(){var e;(0,o.Z)(this,t);for(var r=arguments.length,l=new Array(r),a=0;a<r;a++)l[a]=arguments[a];return e=n.call.apply(n,[this].concat(l)),(0,c.Z)((0,i.Z)(e),"state",{hasError:!1,errorInfo:""}),e}return(0,r.Z)(t,[{key:"componentDidCatch",value:function(e,n){console.log(e,n)}},{key:"render",value:function(){return this.state.hasError?(0,d.jsx)(u.ZP,{status:"error",title:"Something went wrong.",extra:this.state.errorInfo}):this.props.children}}],[{key:"getDerivedStateFromError",value:function(e){return{hasError:!0,errorInfo:e.message}}}]),t}(s.Component)},10178:function(e,n,t){"use strict";t.d(n,{D:function(){return a}});var o=t(74165),r=t(15861),i=t(67294),l=t(48171);function a(e,n){var t=(0,l.J)(e),a=(0,i.useRef)(),c=(0,i.useCallback)((function(){a.current&&(clearTimeout(a.current),a.current=null)}),[]),u=(0,i.useCallback)((0,r.Z)((0,o.Z)().mark((function e(){var i,l,u,s=arguments;return(0,o.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(i=s.length,l=new Array(i),u=0;u<i;u++)l[u]=s[u];if(0!==n&&void 0!==n){e.next=3;break}return e.abrupt("return",t.apply(void 0,l));case 3:return c(),e.abrupt("return",new Promise((function(e){a.current=setTimeout((0,r.Z)((0,o.Z)().mark((function n(){return(0,o.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.t0=e,n.next=3,t.apply(void 0,l);case 3:return n.t1=n.sent,(0,n.t0)(n.t1),n.abrupt("return");case 6:case"end":return n.stop()}}),n)}))),n)})));case 5:case"end":return e.stop()}}),e)}))),[t,c,n]);return(0,i.useEffect)((function(){return c}),[c]),{run:u,cancel:c}}},48171:function(e,n,t){"use strict";t.d(n,{J:function(){return i}});var o=t(74902),r=t(67294),i=function(e){var n=(0,r.useRef)(null);return n.current=e,(0,r.useCallback)((function(){for(var e,t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];return null===(e=n.current)||void 0===e?void 0:e.call.apply(e,[n].concat((0,o.Z)(r)))}),[])}},26058:function(e,n,t){"use strict";t.d(n,{Z:function(){return Z}});var o=t(74902),r=t(67294),i=t(93967),l=t.n(i),a=t(98423),c=t(53124),u=t(82401),s=t(50344),d=t(70985);var p=t(24793),m=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(t[o[r]]=e[o[r]])}return t};function v(e){let{suffixCls:n,tagName:t,displayName:o}=e;return e=>r.forwardRef(((o,i)=>r.createElement(e,Object.assign({ref:i,suffixCls:n,tagName:t},o))))}const h=r.forwardRef(((e,n)=>{const{prefixCls:t,suffixCls:o,className:i,tagName:a}=e,u=m(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:s}=r.useContext(c.E_),d=s("layout",t),[v,h,f]=(0,p.ZP)(d),g=o?`${d}-${o}`:d;return v(r.createElement(a,Object.assign({className:l()(t||g,i,h,f),ref:n},u)))})),f=r.forwardRef(((e,n)=>{const{direction:t}=r.useContext(c.E_),[i,v]=r.useState([]),{prefixCls:h,className:f,rootClassName:g,children:y,hasSider:x,tagName:b,style:C}=e,Z=m(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),j=(0,a.Z)(Z,["suffixCls"]),{getPrefixCls:w,className:k,style:S}=(0,c.dj)("layout"),I=w("layout",h),M=function(e,n,t){return"boolean"==typeof t?t:!!e.length||(0,s.Z)(n).some((e=>e.type===d.Z))}(i,y,x),[B,R,T]=(0,p.ZP)(I),E=l()(I,{[`${I}-has-sider`]:M,[`${I}-rtl`]:"rtl"===t},k,f,g,R,T),N=r.useMemo((()=>({siderHook:{addSider:e=>{v((n=>[].concat((0,o.Z)(n),[e])))},removeSider:e=>{v((n=>n.filter((n=>n!==e))))}}})),[]);return B(r.createElement(u.V.Provider,{value:N},r.createElement(b,Object.assign({ref:n,className:E,style:Object.assign(Object.assign({},S),C)},j),y)))})),g=v({tagName:"div",displayName:"Layout"})(f),y=v({suffixCls:"header",tagName:"header",displayName:"Header"})(h),x=v({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(h),b=v({suffixCls:"content",tagName:"main",displayName:"Content"})(h);const C=g;C.Header=y,C.Footer=x,C.Content=b,C.Sider=d.Z,C._InternalSiderContext=d.D;var Z=C},16305:function(e,n){"use strict";n.EQ=function(e,n={}){const{decode:r=decodeURIComponent,delimiter:i=t}=n,{regexp:l,keys:a}=m(e,n),c=a.map((e=>!1===r?o:"param"===e.type?r:e=>e.split(i).map(r)));return function(e){const n=l.exec(e);if(!n)return!1;const t=n[0],o=Object.create(null);for(let e=1;e<n.length;e++){if(void 0===n[e])continue;const t=a[e-1],r=c[e-1];o[t.name]=r(n[e])}return{path:t,params:o}}};const t="/",o=e=>e,r=/^[$_\p{ID_Start}]$/u,i=/^[$\u200c\u200d\p{ID_Continue}]$/u,l="https://git.new/pathToRegexpError",a={"{":"{","}":"}","(":"(",")":")","[":"[","]":"]","+":"+","?":"?","!":"!"};function c(e){return e.replace(/[.+*?^${}()[\]|/\\]/g,"\\$&")}class u{constructor(e){this.tokens=e}peek(){if(!this._peek){const e=this.tokens.next();this._peek=e.value}return this._peek}tryConsume(e){const n=this.peek();if(n.type===e)return this._peek=void 0,n.value}consume(e){const n=this.tryConsume(e);if(void 0!==n)return n;const{type:t,index:o}=this.peek();throw new TypeError(`Unexpected ${t} at ${o}, expected ${e}: ${l}`)}text(){let e,n="";for(;e=this.tryConsume("CHAR")||this.tryConsume("ESCAPED");)n+=e;return n}}class s{constructor(e){this.tokens=e}}function d(e,n={}){const{encodePath:t=o}=n,c=new u(function*(e){const n=[...e];let t=0;function o(){let e="";if(r.test(n[++t]))for(e+=n[t];i.test(n[++t]);)e+=n[t];else if('"'===n[t]){let o=t;for(;t<n.length;){if('"'===n[++t]){t++,o=0;break}e+="\\"===n[t]?n[++t]:n[t]}if(o)throw new TypeError(`Unterminated quote at ${o}: ${l}`)}if(!e)throw new TypeError(`Missing parameter name at ${t}: ${l}`);return e}for(;t<n.length;){const e=n[t],r=a[e];if(r)yield{type:r,index:t++,value:e};else if("\\"===e)yield{type:"ESCAPED",index:t++,value:n[t++]};else if(":"===e){const e=o();yield{type:"PARAM",index:t,value:e}}else if("*"===e){const e=o();yield{type:"WILDCARD",index:t,value:e}}else yield{type:"CHAR",index:t,value:n[t++]}}return{type:"END",index:t,value:""}}(e));const d=function e(n){const o=[];for(;;){const r=c.text();r&&o.push({type:"text",value:t(r)});const i=c.tryConsume("PARAM");if(i){o.push({type:"param",name:i});continue}const l=c.tryConsume("WILDCARD");if(l){o.push({type:"wildcard",name:l});continue}if(!c.tryConsume("{"))return c.consume(n),o;o.push({type:"group",tokens:e("}")})}}("END");return new s(d)}function p(e,n,t){const r=e.map((e=>function(e,n,t){if("text"===e.type)return()=>[e.value];if("group"===e.type){const o=p(e.tokens,n,t);return e=>{const[n,...t]=o(e);return t.length?[""]:[n]}}const r=t||o;if("wildcard"===e.type&&!1!==t)return t=>{const o=t[e.name];if(null==o)return["",e.name];if(!Array.isArray(o)||0===o.length)throw new TypeError(`Expected "${e.name}" to be a non-empty array`);return[o.map(((n,t)=>{if("string"!=typeof n)throw new TypeError(`Expected "${e.name}/${t}" to be a string`);return r(n)})).join(n)]};return n=>{const t=n[e.name];if(null==t)return["",e.name];if("string"!=typeof t)throw new TypeError(`Expected "${e.name}" to be a string`);return[r(t)]}}(e,n,t)));return e=>{const n=[""];for(const t of r){const[o,...r]=t(e);n[0]+=o,n.push(...r)}return n}}function m(e,n={}){const{delimiter:o=t,end:r=!0,sensitive:i=!1,trailing:l=!0}=n,a=[],u=[],p=i?"":"i",m=(Array.isArray(e)?e:[e]).map((e=>e instanceof s?e:d(e,n)));for(const{tokens:e}of m)for(const n of v(e,0,[])){const e=h(n,o,a);u.push(e)}let f=`^(?:${u.join("|")})`;l&&(f+=`(?:${c(o)}$)?`),f+=r?"$":`(?=${c(o)}|$)`;return{regexp:new RegExp(f,p),keys:a}}function*v(e,n,t){if(n===e.length)return yield t;const o=e[n];if("group"===o.type){const r=t.slice();for(const t of v(o.tokens,0,r))yield*v(e,n+1,t)}else t.push(o);yield*v(e,n+1,t)}function h(e,n,t){let o="",r="",i=!0;for(let a=0;a<e.length;a++){const u=e[a];if("text"!==u.type)if("param"!==u.type&&"wildcard"!==u.type);else{if(!i&&!r)throw new TypeError(`Missing text after "${u.name}": ${l}`);"param"===u.type?o+=`(${f(n,i?"":r)}+)`:o+="([\\s\\S]+)",t.push(u),r="",i=!1}else o+=c(u.value),r+=u.value,i||(i=u.value.includes(n))}return o}function f(e,n){return n.length<2?e.length<2?`[^${c(e+n)}]`:`(?:(?!${c(e)})[^${c(n)}])`:e.length<2?`(?:(?!${c(n)})[^${c(e)}])`:`(?:(?!${c(n)}|${c(e)})[\\s\\S])`}},64599:function(e,n,t){var o=t(96263);e.exports=function(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=o(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,a=!0,c=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){c=!0,l=e},f:function(){try{a||null==t.return||t.return()}finally{if(c)throw l}}}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);