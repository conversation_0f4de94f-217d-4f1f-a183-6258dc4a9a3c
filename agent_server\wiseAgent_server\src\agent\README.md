# 金融风险分析代理

这是一个使用LangGraph实现的金融风险分析代理，它能够从API调用中接收参数，如数据库连接信息和文件路径，实现灵活的客户信用风险分析。

## 功能

- 通过API调用传入配置参数
- 从数据库获取客户信息
- 读取风险政策文件
- 分析客户信用风险并提供专业评估

## 如何在LangGraph Server中通过API参数传递配置

本示例展示了三种常见的参数传递场景：

1. 数据库连接信息
2. 文件路径
3. LLM配置（模型名称、温度等）

## 代码结构

- `graph.py`: 定义了代理的核心逻辑和图结构
- `client_example.py`: 展示如何调用代理并传入参数的客户端示例

## 如何使用

### 运行LangGraph Server

```bash
cd wiseAgent/agent_server/wiseAgent_server
langgraph dev 
```

### 调用代理API并传入参数

使用Python客户端：

```python
import requests
import json

# 准备配置参数
config = {
    "db_url": "postgres://localhost:5432/finance_db",
    "db_username": "financial_analyst",
    "db_password": "secure_password",
    "risk_policy_path": "/path/to/risk_policy.txt",
    "model_name": "gpt-3.5-turbo",
    "temperature": 0.3,
    "api_key": "your-openai-api-key"
}

# 准备请求
payload = {
    "inputs": {
        "query": "分析客户的信用风险",
        "messages": []
    },
    "config": {
        "configurable": config
    }
}

# 发送请求
response = requests.post(
    "http://localhost:8000/agent/invoke",
    headers={"Content-Type": "application/json"},
    data=json.dumps(payload)
)

# 处理响应
result = response.json()
print(json.dumps(result, ensure_ascii=False, indent=2))
```

### 可配置参数

| 参数名 | 描述 | 类型 | 是否必需 |
|--------|------|------|----------|
| db_url | 数据库连接URL | 字符串 | 否 |
| db_username | 数据库用户名 | 字符串 | 否 |
| db_password | 数据库密码 | 字符串 | 否 |
| risk_policy_path | 风险政策文件路径 | 字符串 | 否 |
| model_name | 使用的LLM模型 | 字符串 | 否 |
| temperature | 模型温度参数 | 浮点数 | 否 |
| api_key | OpenAI API密钥 | 字符串 | 是 |

## 最佳实践

1. **敏感信息处理**: 避免在请求中硬编码敏感信息，特别是数据库密码和API密钥。可以使用环境变量和安全凭证管理。

2. **错误处理**: 代理实现了健壮的错误处理，确保即使在参数缺失或服务不可用时，仍能返回有意义的错误信息。

3. **灵活配置**: 所有参数都是可选的，代理会根据提供的参数适应不同的运行环境。

4. **状态管理**: 使用消息历史记录保存对话状态，支持多轮交互。

## 扩展示例

这个示例可以很容易扩展为支持更多的配置选项：

- 添加额外的数据源
- 支持不同类型的分析
- 集成其他外部服务
- 实现更复杂的多轮对话流程 