import unittest
import asyncio
import json
import pytest
from httpx import AsyncClient
from datetime import datetime
from typing import List, Dict
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"  # 根据实际情况修改

class TestAPIEndpoints(unittest.TestCase):
    def setUp(self):
        # 使用实例变量而不是类变量
        self.app_id = None
        self.chat_id = None
        self.dataset_id = None
        self.collection_id = None
        self.data_block_ids = []
        
        self.loop = asyncio.get_event_loop()
        self.loop.run_until_complete(self.async_setUp())
        logger.info("初始化测试实例...")
        logger.info(f"初始状态: app_id={self.app_id}, dataset_id={self.dataset_id}, collection_id={self.collection_id}")

    async def async_setUp(self):
        self.client = AsyncClient(
            base_url=BASE_URL,
            timeout=30.0  # 增加超时时间
        )
        
    async def async_tearDown(self):
        await self.client.aclose()

    def tearDown(self):
        self.loop.run_until_complete(self.async_tearDown())

    async def test_1_create_app(self):
        """测试创建应用"""
        logger.info("开始测试创建应用...")
        
        test_app = {
            "app_name": "cc测试厦门知识库应用"
        }
        
        response = await self.client.post("/api/core/dataset/create/app", 
            json=test_app)
        data = response.json()
        logger.info(f"创建应用响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
        self.app_id = data.get("data")  # 使用实例变量
        
        self.assertEqual(response.status_code, 200)
        self.assertIsNotNone(self.app_id, "App ID should not be None")
        logger.info(f"创建应用完成，app_id: {self.app_id}")

    async def test_2_create_chat(self):
        """测试创建聊天"""
        logger.info("开始测试创建聊天...")
        logger.info(f"使用 app_id: {self.app_id}")
        
        test_chat = {
            "appId": str(self.app_id)
        }
        
        response = await self.client.post("/api/v1/chat/createChat", 
            json=test_chat)
        data = response.json()
        logger.info(f"创建聊天响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
        self.chat_id = data.get("chatId")  # 使用实例变量
        
        self.assertEqual(response.status_code, 200)
        self.assertIsNotNone(self.chat_id, "Chat ID should not be None")
        logger.info(f"创建聊天完成，chat_id: {self.chat_id}")

    async def test_3_create_knowledge_base(self):
        """测试创建知识库"""
        logger.info("开始测试创建知识库...")
        
        test_kb = {
            "name": "test知识库",
            "type": "dataset",
            "intro": "test知识库介绍",
            "avatar": "",
            "vectorModel": "bge-large-en",
            "agentModel": "DeepSeek-R1-Distill-Qwen-32B"
        }
        
        response = await self.client.post("/api/core/dataset/create", 
            json=test_kb)
        data = response.json()
        logger.info(f"创建知识库响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        # 修改这里，确保从正确的响应结构中获取 ID
        if data.get("code") == 200:
            self.dataset_id = data.get("datasetId")
            logger.info(f"成功保存 dataset_id: {self.dataset_id}")
        else:
            self.fail(f"创建知识库失败: {data}")
        
        self.assertIsNotNone(self.dataset_id, "Dataset ID should not be None")

    async def test_4_create_collection(self):
        """测试创建集合"""
        logger.info("开始测试创建集合...")
        
        # 先验证 dataset_id 是否存在
        if not self.dataset_id:
            self.fail("Dataset ID is required for creating collection")
        
        test_collection = {
            "datasetId": str(self.dataset_id),
            "parentId": None
        }
        
        response = await self.client.post("/api/core/dataset/collection/create",
            json=test_collection)
        data = response.json()
        logger.info(f"创建集合响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        # 修改这里，确保从正确的响应结构中获取 ID
        if data.get("code") == 200:
            self.collection_id = data.get("collectionId")
            logger.info(f"成功保存 collection_id: {self.collection_id}")
        else:
            self.fail(f"创建集合失败: {data}")
        
        self.assertIsNotNone(self.collection_id, "Collection ID should not be None")

    async def test_5_push_knowledge_data(self):
        """测试添加知识库数据"""
        logger.info("开始测试添加知识库数据...")
        
        # 首先验证 ID 是否有效
        if not self.collection_id or not self.dataset_id:
            self.fail("Collection ID or Dataset ID is missing")
        
        logger.info(f"Using collection_id: {self.collection_id}")
        logger.info(f"Using dataset_id: {self.dataset_id}")
        
        test_data = {
            "collectionId": str(self.collection_id),
            "datasetId": str(self.dataset_id),
            "mode": "chunk",
            "prompt": "",
            "docName": "test.txt",
            "data": [
                "《哪吒之魔童闹海》是饺子编剧并执导的奇幻动画电影...",
                "影片上映后成绩斐然，截至 2 月 15 日，票房突破 157.38 亿元..."
            ]
        }
        
        response = await self.client.post("/api/core/dataset/data/pushData", 
            json=test_data)
        data = response.json()
        logger.info(f"添加知识库数据响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(data.get("code"), 200)

    async def test_6_chat_dialogue(self):
        """测试聊天对话"""
        logger.info("开始测试聊天对话...")
        
        # 测试场景 1: stream=False, detail=False
        await self._test_chat_simple()
        
        # 测试场景 2: stream=False, detail=True
        await self._test_chat_detailed()

        # 测试场景 3: stream=True, detail=False
        await self._test_chat_stream_simple()

        # 测试场景 4: stream=True, detail=True
        await self._test_chat_stream_detailed()

    async def _test_chat_simple(self):
        """测试简单聊天场景 (非流式，无详情)"""
        logger.info("\n测试场景 1: 非流式，无详情...")
        simple_case = {
            "chatId": str(self.chat_id),
            "datasetId": [str(self.dataset_id)],
            "stream": False,
            "detail": False,
            "messages": [{"role": "user", "content": "电影哪吒之魔童闹海谁导演的？"}]
        }
        
        await self._process_chat_response(simple_case)

    async def _test_chat_detailed(self):
        """测试详细聊天场景 (非流式，有详情)"""
        logger.info("\n测试场景 2: 非流式，有详情...")
        detailed_case = {
            "chatId": str(self.chat_id),
            "datasetId": [str(self.dataset_id)],
            "stream": False,
            "detail": True,
            "messages": [{"role": "user", "content": "电影哪吒之魔童闹海谁导演的？"}]
        }
        
        await self._process_chat_response(detailed_case)

    async def _process_chat_response(self, test_case):
        """处理聊天响应的通用方法"""
        logger.info(f"发送请求: {json.dumps(test_case, ensure_ascii=False, indent=2)}")
        
        response = await self.client.post(
            "/api/v1/chat/completions",
            json=test_case,
            timeout=60.0
        )
        
        self.assertEqual(response.status_code, 200)
        
        # 获取原始响应内容
        raw_content = response.content.decode('utf-8')
        logger.info(f"原始响应内容:\n{raw_content}")
        
        # 分割多个 JSON 响应
        json_responses = []
        for line in raw_content.split('\n\n'):
            line = line.strip()
            if not line:
                continue
            
            logger.info(f"处理响应行: {line}")
            
            try:
                # 处理事件格式的响应
                if line.startswith('event:'):
                    event_line = line.split('\n')
                    if len(event_line) >= 2:
                        event_type = event_line[0].replace('event:', '').strip()
                        event_data = event_line[1].replace('data:', '').strip()
                        logger.info(f"事件类型: {event_type}")
                        logger.info(f"事件数据: {event_data}")
                        if event_data:
                            try:
                                json_responses.append(json.loads(event_data))
                            except json.JSONDecodeError:
                                pass
                else:
                    # 处理普通 JSON 响应
                    json_responses.append(json.loads(line))
                
            except json.JSONDecodeError as e:
                logger.warning(f"JSON 解析错误: {str(e)}")
                logger.warning(f"尝试解析的行: {line}")
                continue
        
        # 找到包含 choices 的响应
        llm_response = None
        for resp in json_responses:
            if "choices" in resp:
                llm_response = resp
                break
        
        # 验证 LLM 响应
        self.assertIsNotNone(llm_response, "No LLM response found in the response")
        self.assertIn("choices", llm_response)
        self.assertTrue(len(llm_response["choices"]) > 0)
        
        content = llm_response["choices"][0].get("message", {}).get("content", "")
        self.assertTrue(len(content) > 0, "Response content should not be empty")
        logger.info(f"LLM 响应内容: {content}")

    async def _test_chat_stream_simple(self):
        """测试流式聊天场景 (流式，无详情)"""
        logger.info("\n测试场景 3: 流式，无详情...")
        stream_case = {
            "chatId": str(self.chat_id),
            "datasetId": [str(self.dataset_id)],
            "stream": True,
            "detail": False,
            "messages": [{"role": "user", "content": "电影哪吒之魔童闹海谁导演的？"}]
        }
        
        logger.info(f"发送流式请求: {json.dumps(stream_case, ensure_ascii=False, indent=2)}")
        
        async with self.client.stream(
            "POST",
            "/api/v1/chat/completions",
            json=stream_case,
            timeout=60.0
        ) as response:
            self.assertEqual(response.status_code, 200)
            full_response = ""
            
            async for chunk in response.aiter_bytes():
                try:
                    decoded = chunk.decode('utf-8')
                    for line in decoded.split('\n\n'):
                        line = line.strip()
                        if not line:
                            continue
                        
                        # 更彻底地清理数据字符串
                        data_str = (
                            line
                            .replace('data: ', '')  # 移除 data: 前缀
                            .strip()                # 移除首尾空白
                            .strip('"')            # 移除外层引号
                            .replace('\\"', '"')   # 处理转义的引号
                            .rstrip('\\n')         # 移除末尾的 \n
                            .rstrip()              # 再次移除可能的空白
                        )
                        
                        if data_str in ["[DONE]", "Stream has ended"]:
                            logger.info(f"收到结束标记: {data_str}")
                            continue
                            
                        try:
                            # 尝试解析清理后的 JSON 字符串
                            data = json.loads(data_str)
                            logger.info(f"解析的数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                            
                            if "choices" in data and data["choices"]:
                                delta = data["choices"][0].get("delta", {})
                                content = delta.get("content", "")
                                if content:
                                    full_response += content
                                    logger.info(f"当前内容: {content}")
                                    logger.info(f"累积响应 (长度={len(full_response)}): {full_response}")
                                    
                        except json.JSONDecodeError as e:
                            logger.warning(f"JSON解析错误: {e}, 数据: {data_str}")
                            # 添加更多调试信息
                            logger.warning(f"数据字符串长度: {len(data_str)}")
                            logger.warning(f"数据字符串最后10个字符: {repr(data_str[-10:])}")
                            continue
                        
                        if line.startswith('event:'):
                            event_type = line.replace('event:', '').strip()
                            logger.info(f"事件类型: {event_type}")
                            
                except UnicodeDecodeError as e:
                    logger.error(f"解码错误: {str(e)}")
                    continue
                
                except Exception as e:
                    logger.error(f"处理数据块时出错: {str(e)}")
                    continue
            
            logger.info(f"最终完整响应: {full_response}")
            self.assertTrue(len(full_response) > 0, "Stream response should not be empty")

    async def _test_chat_stream_detailed(self):
        """测试流式聊天场景 (流式，有详情)"""
        logger.info("\n测试场景 4: 流式，有详情...")
        stream_case = {
            "chatId": str(self.chat_id),
            "datasetId": [str(self.dataset_id)],
            "stream": True,
            "detail": True,
            "messages": [{"role": "user", "content": "电影哪吒之魔童闹海谁导演的？"}]
        }
        
        logger.info(f"发送流式请求: {json.dumps(stream_case, ensure_ascii=False, indent=2)}")
        
        async with self.client.stream(
            "POST",
            "/api/v1/chat/completions",
            json=stream_case,
            timeout=60.0
        ) as response:
            self.assertEqual(response.status_code, 200)
            full_response = ""
            
            async for chunk in response.aiter_bytes():
                try:
                    decoded = chunk.decode('utf-8')
                    for line in decoded.split('\n\n'):
                        line = line.strip()
                        if not line:
                            continue
                        
                        # 更彻底地清理数据字符串
                        data_str = (
                            line
                            .replace('data: ', '')  # 移除 data: 前缀
                            .strip()                # 移除首尾空白
                            .strip('"')            # 移除外层引号
                            .replace('\\"', '"')   # 处理转义的引号
                            .rstrip('\\n')         # 移除末尾的 \n
                            .rstrip()              # 再次移除可能的空白
                        )
                        
                        if data_str in ["[DONE]", "Stream has ended"]:
                            logger.info(f"收到结束标记: {data_str}")
                            continue
                            
                        try:
                            data = json.loads(data_str)
                            logger.info(f"解析的数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                            
                            if "choices" in data and data["choices"]:
                                delta = data["choices"][0].get("delta", {})
                                content = delta.get("content", "")
                                if content:
                                    full_response += content
                                    logger.info(f"当前内容: {content}")
                                    logger.info(f"累积响应 (长度={len(full_response)}): {full_response}")
                                    
                        except json.JSONDecodeError as e:
                            logger.warning(f"JSON解析错误: {e}, 数据: {data_str}")
                            logger.warning(f"数据字符串长度: {len(data_str)}")
                            logger.warning(f"数据字符串最后10个字符: {repr(data_str[-10:])}")
                            continue
                        
                        if line.startswith('event:'):
                            event_type = line.replace('event:', '').strip()
                            logger.info(f"事件类型: {event_type}")
                            
                except UnicodeDecodeError as e:
                    logger.error(f"解码错误: {str(e)}")
                    continue
                
                except Exception as e:
                    logger.error(f"处理数据块时出错: {str(e)}")
                    continue
            
            logger.info(f"最终完整响应: {full_response}")
            self.assertTrue(len(full_response) > 0, "Stream response should not be empty")

    def test_all(self):
        """运行所有测试"""
        logger.info("开始运行所有测试...")
        
        async def run_tests():
            # 按顺序执行测试
            await self.test_1_create_app()
            await self.test_2_create_chat()
            await self.test_3_create_knowledge_base()
            await self.test_4_create_collection()
            await self.test_5_push_knowledge_data()
            await self.test_6_chat_dialogue()
        
        try:
            self.loop.run_until_complete(run_tests())
        except Exception as e:
            logger.error(f"测试过程中发生错误: {str(e)}")
            raise

def generate_test_report():
    """生成测试报告"""
    import pytest
    import sys
    
    # 修改为使用 pytest.main 的正确方式
    pytest_args = [
        __file__,  # 当前文件
        "--capture=sys",  # 捕获输出
        "--html=test_report.html",  # HTML报告
        "--self-contained-html"  # 自包含HTML
    ]
    
    # 运行测试
    pytest.main(pytest_args)

if __name__ == "__main__":
    # 直接运行单元测试
    unittest.main(argv=['first-arg-is-ignored'])
    # 或者生成报告
    # generate_test_report() 