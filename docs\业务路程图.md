```mermaid
flowchart TD
  subgraph PRE["贷前 | Customer & Credit Origination"]
    A1[客户准入/黑名单校验]:::cust
    A2[客户基本信息维护]:::cust
    A3[集团客户家谱维护]:::cust
    A4[授信额度登记/分配]:::quota
    A5[授信方案“待处理申请”\n  - 新增→填报→风险探测→签署意见]:::apply
    A6[提交→系统选路由\n(授权参数 & 规则) ]:::auth
  end

  subgraph IN["贷中 | Approval • Contract • Disbursement"]
    B1[尽职调查报告编写/上传]:::apply
    B2[审查审批\n(协办->审查->风控->信审会)]:::review
    B3{批复结果}:::review
    B4[驳回/补充资料]:::reject
    B5[合同登记\n(专项/授信项下)]:::contract
    B6[合同自动风险探测]:::contract
    B7[合同审核复核]:::contract
    B8[放贷申请\n(还款计划+费用+签署意见)]:::loan
    B9[放贷审核复核 → 记账]:::loan
  end

  subgraph POST["贷后 | Monitoring • Risk • Collection"]
    C1[授信台账/合同台账]:::post
    C2[贷后检查/工作笔记]:::post
    C3[风险预警信号 ► 发起→认定→批准/否决]:::warn
    C4[预警解除流程]:::warn
    C5[不良资产分发 ► 指定管理人]:::npl
    C6[不良资产日常管理\n(日常工作/终结/还款方式补登)]:::npl
    C7[资产保全\n(诉讼、抵债资产、法律事务)]:::npl
  end

  %% —— 连接关系 ——
  A1 --> A2 --> A3 --> A4 --> A5 --> A6 --> B1
  B1 --> B2 --> B3
  B3 --通过--> B5
  B3 --退回--> B4 --> A5
  B5 --> B6 --> B7 --> B8 --> B9 --> C1
  C1 --> C2 --> C3
  C3 -->|风险解除| C4
  C1 -->|逾期/五级分类下迁| C5 --> C6 --> C7

  %% —— 样式 ——  
  classDef cust fill:#dceefb,stroke:#1d4ed8;
  classDef quota fill:#fef9c3,stroke:#b45309;
  classDef apply fill:#ecfccb,stroke:#65a30d;
  classDef auth fill:#fcd7e4,stroke:#be185d;
  classDef review fill:#e0e7ff,stroke:#4338ca;
  classDef reject fill:#fee2e2,stroke:#b91c1c;
  classDef contract fill:#e0f2fe,stroke:#0284c7;
  classDef loan fill:#e7fee7,stroke:#15803d;
  classDef post fill:#faf5ff,stroke:#7e22ce;
  classDef warn fill:#fff7ed,stroke:#c2410c;
  classDef npl fill:#fef2f2,stroke:#991b1b;
```