from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.system_app_setting import SystemAppSetting, SystemAppSettingUpdate
from ..db.mongodb import db
from ..utils.auth import verify_token
from datetime import datetime

router = APIRouter()

# 分页查询系统应用设置
@router.get("/api/system_app_settings", response_model=Dict[str, Any])
async def get_system_app_settings(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    name: Optional[str] = None,  # 支持按名称检索
    app_info: Optional[str] = None,  # 支持按应用信息检索
    type: Optional[str] = None,  # 支持按应用信息检索
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {}

    if name:
        query["name"] = {"$regex": name, "$options": "i"}  # 使用正则表达式进行模糊匹配，忽略大小写
    if app_info:
        query["app_info"] = {"$regex": app_info, "$options": "i"}  # 支持应用信息的模糊匹配
    if type:
        query["type"] = type  # 支持应用信息的模糊匹配

    settings = await db["system_app_settings"].find(query, {
        "_id": 0,
        "id": 1,
        "name": 1,
        "service_url": 1,
        "description": 1,
        "app_info": 1,
        "type": 1,
        "token_key": 1,
        "token_price": 1,
        "created_name": 1,
        "created_at": 1
    }).sort("created_at", -1).skip(skip).limit(pageSize).to_list(pageSize)
    total = await db["system_app_settings"].count_documents(query)

    # 将 ObjectId 转换为字符串
    # for setting in settings:
    #     setting["id"] = str(setting["_id"])
    #     del setting["_id"]  # 删除原始 _id

    return {
        "data": settings,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }




# 分页查询系统应用设置 /api/system-app-setting/logs
@router.get("/api/system-app-setting/logs", response_model=Dict[str, Any])
async def get_system_app_settings_logs(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    app_id: Optional[str] = None,  # 支持按名称检索
    type: Optional[str] = None,  # 支持按类型检索
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"id": int(app_id)}
    if type:
        query["type"] = type


    print(query)

    settings = await db["system_app_settings_logs"].find(query, {
        "_id": 1,
        "type": 1,
        "params": 1,
        "created_by": 1,
        "created_name": 1,
        "created_at": 1
    }).sort("created_at", -1).skip(skip).limit(pageSize).to_list(pageSize)
    total = await db["system_app_settings_logs"].count_documents(query)

    # 将 ObjectId 转换为字符串
    for setting in settings:
        setting["id"] = str(setting["_id"])
        del setting["_id"]  # 删除原始 _id

    return {
        "data": settings,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 分页查询系统应用设置 /api/system-app-setting/chat/{app_id}
@router.get("/api/system-app-setting/chat", response_model=Dict[str, Any])
async def get_system_app_settings_logs(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    app_info: Optional[str] = None,  # 支持按名称检索
    conversation_name: Optional[str] = None,  # 支持按名称检索
    user_name: Optional[str] = None,  # 支持按名称检索
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"app_info": app_info}
    if conversation_name:
        query["conversation_name"] = {"$regex": conversation_name, "$options": "i"}  # 
    if user_name:
        query["user_name"] = {"$regex": user_name, "$options": "i"}  # 

    conversations = await db["conversations"].find(query, {
        "conversation_name": 1,
        "user_id": 1,
        "user_name": 1,
        "app_info": 1,
        "created_at": 1,
        "pinned_at": 1,
        "pinned": 1,
        "active_at": 1
    }).sort("created_at", -1).skip(skip).limit(pageSize).to_list(pageSize)
    total = await db["conversations"].count_documents(query)

    # 将 ObjectId 转换为字符串
    for conversation in conversations:
        conversation["id"] = str(conversation["_id"])
        del conversation["_id"]  # 删除原始 _id

    return {
        "data": conversations,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }



# 更新系统应用设置
@router.put("/api/system-app-settings/{app_id}", response_model=Dict[str, Any])
async def update_system_app_setting(
    app_id: int, 
    data: SystemAppSettingUpdate, 
    current_user: dict = Depends(verify_token)
):
    # # 验证参数
    # try:
    #     data.validate_params()
    # except ValueError as e:
    #     raise HTTPException(status_code=422, detail=str(e))

    update_data = data.dict(exclude_unset=True)
    
    print(update_data)
    result = await db["system_app_settings"].update_one({"id": app_id}, {"$set": update_data})
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="System app setting not found")
    
    updated_setting = await db["system_app_settings"].find_one({"id": app_id})
    if updated_setting is None:
        raise HTTPException(status_code=404, detail="System app setting not found")

    # 将 ObjectId 转换为字符串
    if "_id" in updated_setting:
        updated_setting["id"] = str(updated_setting["_id"])
        del updated_setting["_id"]

    # 添加日志记录
    log_entry = {
        "id": app_id,
        "type": updated_setting.get("type"),
        "params": updated_setting.get("params"),
        "app_info": updated_setting.get("app_info"),
        "created_by": current_user.get("id"),
        "created_name": current_user.get("name"),
        "created_at": datetime.now()
    }
    await db["system_app_settings_logs"].insert_one(log_entry)

    return {
        "success": True,
        "data": updated_setting
    }




# 更新系统应用设置
@router.get("/api/system-app-info/{app_id}", response_model=Dict[str, Any])
async def get_system_app_info(app_id: str, current_user: dict = Depends(verify_token)):

    result = await db["system_app_settings"].find_one({"id": int(app_id)},{
        "_id": 0,
        "id": 1,
        "app_info": 1,
        "name": 1,
        "created_at": 1,
        "description": 1,
        "tags": 1,
        "type": 1,
        "app_info": 1,
        "params": 1,
    })
    print(result)
    if result is None:
        raise HTTPException(status_code=404, detail="System app setting not found")
    return {
        "success": True,
        "data": result
    }




@router.get("/api/system_app_settings/llms", response_model=Dict[str, Any])
async def get_llms(
    current_user: dict = Depends(verify_token)
):
   

    llms = await db["llms"].find({"is_active": True},{"_id":0, 
                                        "id":1,
                                        "api_key":1, 
                                        "service_url":1, 
                                        "description":1, 
                                        "name":1,
                                        "m_name":1,
                                        "provider":1,
                                        "is_active":1,
                                        "max_tokens":1,
                                        "temperature":1,
                                        "top_p":1,
                                        "created_at":1
                                        }).to_list()

    return {
        "data": llms,
        "success": True,
    }




@router.get("/api/system_app_settings/knowledge_bases", response_model=Dict[str, Any])
async def get_llms(
    current_user: dict = Depends(verify_token)
):
   

    knowledge_bases = await db["knowledge_bases"].find({"is_active": True},{"_id":1, 
                                        "name":1, 
                                        "description":1, 
                                        "created_at":1
                                        }).to_list()
    
    for kb in knowledge_bases:
        kb["id"] = str(kb["_id"])
        del kb["_id"]

    return {
        "data": knowledge_bases,
        "success": True,
    }

# 创建系统应用设置
@router.post("/api/system_app_settings", response_model=Dict[str, Any])
async def create_system_app_setting(
    data: dict, 
    current_user: dict = Depends(verify_token)
):
    # 查询最大ID值
    last_record = await db["system_app_settings"].find_one(
        sort=[("id", -1)],
        projection={"id": 1}
    )
    new_id = 1 if last_record is None else last_record["id"] + 1
    
    # 处理标签数据
    tags = data.get("tags", None)
    
    # 准备要插入的数据
    system_app_data = {
        "id": new_id,
        "name": data.get("name", ""),
        "description": data.get("description", ""),
        "app_info": data.get("app_info", ""),
        "type": data.get("type", ""),
        "params": {},  # 默认为空对象
        "tags": tags,
        "created_at": datetime.now(),
        "created_by": current_user.get("id"),
        "created_name": current_user.get("name")
    }
    
    # 插入数据
    result = await db["system_app_settings"].insert_one(system_app_data)
    if not result.inserted_id:
        raise HTTPException(status_code=500, detail="Failed to create system app setting")
    
    # 查询创建后的记录
    created_setting = await db["system_app_settings"].find_one({"id": new_id})
    if created_setting is None:
        raise HTTPException(status_code=404, detail="System app setting not found after creation")

    # 移除 _id 字段
    if "_id" in created_setting:
        del created_setting["_id"]
    
    # 添加日志记录
    log_entry = {
        "id": new_id,
        "type": system_app_data.get("type"),
        "params": system_app_data.get("params"),
        "app_info": system_app_data.get("app_info"),
        "created_by": current_user.get("id"),
        "created_name": current_user.get("name"),
        "created_at": datetime.now()
    }
    await db["system_app_settings_logs"].insert_one(log_entry)

    return {
        "success": True,
        "data": created_setting
    }

# 删除系统应用设置
@router.delete("/api/system_app_settings/{app_id}", response_model=Dict[str, Any])
async def delete_system_app_setting(
    app_id: int, 
    current_user: dict = Depends(verify_token)
):
    # 查询要删除的记录
    app_to_delete = await db["system_app_settings"].find_one({"id": app_id})
    if app_to_delete is None:
        raise HTTPException(status_code=404, detail="系统应用设置不存在")
    
    # 备份删除的记录到日志
    log_entry = {
        "id": app_id,
        "type": app_to_delete.get("type"),
        "name": app_to_delete.get("name"),
        "params": app_to_delete.get("params"),
        "app_info": app_to_delete.get("app_info"),
        "operation": "delete",
        "created_by": current_user.get("id"),
        "created_name": current_user.get("name"),
        "created_at": datetime.now()
    }
    await db["system_app_settings_logs"].insert_one(log_entry)
    
    # 执行删除操作
    result = await db["system_app_settings"].delete_one({"id": app_id})
    if result.deleted_count == 0:
        raise HTTPException(status_code=500, detail="删除失败")
    
    return {
        "success": True,
        "message": "删除成功"
    }