"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7491],{51042:function(e,r,n){var t=n(1413),o=n(67294),a=n(42110),c=n(91146),i=function(e,r){return o.createElement(c.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:r,icon:a.Z}))},s=o.forwardRef(i);r.Z=s},33445:function(e,r,n){n.r(r),n.d(r,{default:function(){return R}});var t=n(5574),o=n.n(t),a=n(15009),c=n.n(a),i=n(97857),s=n.n(i),l=n(99289),d=n.n(l),u=n(67294),p=n(97131),m=n(12453),h=n(8232),f=n(2453),g=n(66309),b=n(42075),x=n(83622),v=n(86738),k=n(17788),y=n(55102),_=n(72269),C=n(84567),w=n(51042),Z=n(78158);function j(e){return S.apply(this,arguments)}function S(){return(S=d()(c()().mark((function e(r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,Z.N)("/api/knowledge_base",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function $(e){return P.apply(this,arguments)}function P(){return(P=d()(c()().mark((function e(r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,Z.N)("/api/knowledge_bases",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function O(e,r){return I.apply(this,arguments)}function I(){return(I=d()(c()().mark((function e(r,n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,Z.N)("/api/knowledge_bases/".concat(r),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function T(e){return E.apply(this,arguments)}function E(){return(E=d()(c()().mark((function e(r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,Z.N)("/api/knowledge_bases/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var N=n(85893),z=function(){var e=d()(c()().mark((function e(r){var n,t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,$({current:r.current,pageSize:r.pageSize,name:r.name});case 2:return n=e.sent,t=n.data.map((function(e){return s()(s()({},e),{},{basic_index:!0,graph_index:!1,semantic_index:!0,embedding_model:"default-model",embedding_model_id:""})})),e.abrupt("return",{data:t,success:n.success,total:n.total});case 5:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),B=function(){var e=d()(c()().mark((function e(r){var n;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,j({name:r.name,embedding_model:r.embedding_model,embedding_model_id:r.embedding_model_id});case 3:return n=e.sent,e.abrupt("return",{success:n.success});case 7:return e.prev=7,e.t0=e.catch(0),console.error("添加知识库失败:",e.t0),e.abrupt("return",{success:!1,error:"添加知识库失败"});case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(r){return e.apply(this,arguments)}}(),F=function(){var e=d()(c()().mark((function e(r,n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,O(r,{name:n.name});case 3:return e.abrupt("return",{success:!0});case 6:return e.prev=6,e.t0=e.catch(0),console.error("更新知识库 ".concat(r," 失败:"),e.t0),e.abrupt("return",{success:!1,error:"更新知识库失败"});case 10:case"end":return e.stop()}}),e,null,[[0,6]])})));return function(r,n){return e.apply(this,arguments)}}(),H=function(){var e=d()(c()().mark((function e(r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,T(r);case 3:return e.abrupt("return",{success:!0});case 6:return e.prev=6,e.t0=e.catch(0),console.error("删除知识库 ".concat(r," 失败:"),e.t0),e.abrupt("return",{success:!1,error:"删除知识库失败"});case 10:case"end":return e.stop()}}),e,null,[[0,6]])})));return function(r){return e.apply(this,arguments)}}(),R=function(){var e=(0,u.useState)(!1),r=o()(e,2),n=r[0],t=r[1],a=(0,u.useState)(null),i=o()(a,2),s=i[0],l=i[1],Z=(0,u.useRef)(),j=h.Z.useForm(),S=o()(j,1)[0],$=function(e){e?(l(e),S.setFieldsValue(e)):(l(null),S.resetFields(),S.setFieldsValue({is_active:!0,basic_index:!0,semantic_index:!0,graph_index:!1})),t(!0)},P=function(){var e=d()(c()().mark((function e(){var r,n,o,a,i,l;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,S.validateFields();case 3:if(r=e.sent,n=f.ZP.loading(s?"正在更新...":"正在添加..."),o=!1,!s){e.next=14;break}return e.next=9,F(s.id,{name:r.name,is_active:r.is_active,basic_index:r.basic_index,graph_index:r.graph_index,semantic_index:r.semantic_index,embedding_model:r.embedding_model,embedding_model_id:r.embedding_model_id});case 9:a=e.sent,(o=a.success)||f.ZP.error(a.error||"更新失败"),e.next=19;break;case 14:return e.next=16,B({name:r.name,is_active:r.is_active,basic_index:r.basic_index,graph_index:r.graph_index,semantic_index:r.semantic_index,embedding_model:r.embedding_model,embedding_model_id:r.embedding_model_id});case 16:i=e.sent,(o=i.success)||f.ZP.error(i.error||"添加失败");case 19:n(),o&&(f.ZP.success(s?"更新成功":"添加成功"),t(!1),null===(l=Z.current)||void 0===l||l.reload()),e.next=27;break;case 23:e.prev=23,e.t0=e.catch(0),console.error("Failed:",e.t0),f.ZP.error("操作失败，请检查表单");case 27:case"end":return e.stop()}}),e,null,[[0,23]])})));return function(){return e.apply(this,arguments)}}(),O=function(){var e=d()(c()().mark((function e(r){var n,t,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=f.ZP.loading("正在删除..."),e.prev=1,e.next=4,H(r);case 4:t=e.sent,n(),t.success?(f.ZP.success("删除成功"),null===(o=Z.current)||void 0===o||o.reload()):f.ZP.error(t.error||"删除失败"),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),n(),f.ZP.error("删除失败");case 13:case"end":return e.stop()}}),e,null,[[1,9]])})));return function(r){return e.apply(this,arguments)}}(),I=[{title:"名称",dataIndex:"name",key:"name",copyable:!0,ellipsis:!0,width:180},{title:"创建人",dataIndex:"user_name",key:"user_name",width:100,ellipsis:!0},{title:"文件数",dataIndex:"file_count",key:"file_count",width:80,align:"right",render:function(e){return(0,N.jsx)("span",{children:e||0})}},{title:"文本块数",dataIndex:"chunk_count",key:"chunk_count",width:90,align:"right",render:function(e){return(0,N.jsx)("span",{children:e||0})}},{title:"状态",dataIndex:"is_active",key:"is_active",width:80,render:function(e,r){return(0,N.jsx)(g.Z,{color:r.is_active?"green":"red",children:r.is_active?"启用":"禁用"})}},{title:"向量模型",dataIndex:"embedding_model",key:"embedding_model",width:120,ellipsis:!0,render:function(e){return e||"-"}},{title:"索引类型",key:"index_types",width:200,render:function(e,r){return(0,N.jsxs)(b.Z,{children:[r.basic_index&&(0,N.jsx)(g.Z,{color:"blue",children:"基础索引"}),r.graph_index&&(0,N.jsx)(g.Z,{color:"purple",children:"图索引"}),r.semantic_index&&(0,N.jsx)(g.Z,{color:"cyan",children:"语义索引"})]})}},{title:"创建时间",dataIndex:"created_at",key:"created_at",width:160,valueType:"dateTime",sorter:!0,search:!1},{title:"操作",key:"action",valueType:"option",fixed:"right",width:120,render:function(e,r){return[(0,N.jsx)(x.ZP,{type:"link",size:"small",onClick:function(){return $(r)},children:"编辑"},"edit"),(0,N.jsx)(v.Z,{title:'确定删除知识库 "'.concat(r.name,'" 吗？'),onConfirm:function(){return O(r.id)},okText:"确定",cancelText:"取消",children:(0,N.jsx)(x.ZP,{type:"link",danger:!0,size:"small",children:"删除"})},"delete")]}}];return(0,N.jsxs)(p._z,{header:{title:"知识库管理"},children:[(0,N.jsx)(m.Z,{columns:I,actionRef:Z,cardBordered:!0,request:function(){var e=d()(c()().mark((function e(r,n,t){var o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log(n,t),e.next=3,z({current:r.current,pageSize:r.pageSize,name:r.name});case 3:return o=e.sent,e.abrupt("return",{data:o.data,success:o.success,total:o.total});case 5:case"end":return e.stop()}}),e)})));return function(r,n,t){return e.apply(this,arguments)}}(),rowKey:"id",search:{labelWidth:"auto"},options:{setting:{listsHeight:400},reload:!0,density:!0},pagination:{pageSize:10,showSizeChanger:!0},dateFormatter:"string",headerTitle:"知识库列表",toolBarRender:function(){return[(0,N.jsx)(x.ZP,{icon:(0,N.jsx)(w.Z,{}),onClick:function(){return $()},type:"primary",children:"新建知识库"},"button")]}}),(0,N.jsx)(k.Z,{title:s?"编辑知识库":"新建知识库",open:n,onOk:P,onCancel:function(){t(!1),l(null),S.resetFields()},destroyOnClose:!0,children:(0,N.jsxs)(h.Z,{form:S,layout:"vertical",name:"knowledgeBaseForm",children:[(0,N.jsx)(h.Z.Item,{name:"name",label:"名称",rules:[{required:!0,message:"请输入知识库名称!"}],children:(0,N.jsx)(y.Z,{placeholder:"请输入知识库名称"})}),(0,N.jsx)(h.Z.Item,{name:"is_active",label:"状态",initialValue:!0,children:(0,N.jsx)(_.Z,{checkedChildren:"启用",unCheckedChildren:"禁用",defaultChecked:!0})}),(0,N.jsx)(h.Z.Item,{label:"索引类型",children:(0,N.jsxs)(b.Z,{direction:"vertical",children:[(0,N.jsx)(h.Z.Item,{name:"basic_index",valuePropName:"checked",noStyle:!0,children:(0,N.jsx)(C.Z,{defaultChecked:!0,children:"基础索引（向量检索）"})}),(0,N.jsx)(h.Z.Item,{name:"graph_index",valuePropName:"checked",noStyle:!0,children:(0,N.jsx)(C.Z,{children:"图索引"})}),(0,N.jsx)(h.Z.Item,{name:"semantic_index",valuePropName:"checked",noStyle:!0,children:(0,N.jsx)(C.Z,{children:"语义索引"})})]})})]})})]})}},66309:function(e,r,n){n.d(r,{Z:function(){return O}});var t=n(67294),o=n(93967),a=n.n(o),c=n(98423),i=n(98787),s=n(69760),l=n(96159),d=n(45353),u=n(53124),p=n(11568),m=n(15063),h=n(14747),f=n(83262),g=n(83559);const b=e=>{const{lineWidth:r,fontSizeIcon:n,calc:t}=e,o=e.fontSizeSM;return(0,f.IX)(e,{tagFontSize:o,tagLineHeight:(0,p.bf)(t(e.lineHeightSM).mul(o).equal()),tagIconSize:t(n).sub(t(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},x=e=>({defaultBg:new m.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var v=(0,g.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:n,tagPaddingHorizontal:t,componentCls:o,calc:a}=e,c=a(t).sub(n).equal(),i=a(r).sub(n).equal();return{[o]:Object.assign(Object.assign({},(0,h.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:i,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:c}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(b(e))),x),k=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(n[t[o]]=e[t[o]])}return n};const y=t.forwardRef(((e,r)=>{const{prefixCls:n,style:o,className:c,checked:i,onChange:s,onClick:l}=e,d=k(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:m}=t.useContext(u.E_),h=p("tag",n),[f,g,b]=v(h),x=a()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:i},null==m?void 0:m.className,c,g,b);return f(t.createElement("span",Object.assign({},d,{ref:r,style:Object.assign(Object.assign({},o),null==m?void 0:m.style),className:x,onClick:e=>{null==s||s(!i),null==l||l(e)}})))}));var _=y,C=n(98719);var w=(0,g.bk)(["Tag","preset"],(e=>(e=>(0,C.Z)(e,((r,n)=>{let{textColor:t,lightBorderColor:o,lightColor:a,darkColor:c}=n;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:t,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(b(e))),x);const Z=(e,r,n)=>{const t="string"!=typeof(o=n)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${n}`],background:e[`color${t}Bg`],borderColor:e[`color${t}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var j=(0,g.bk)(["Tag","status"],(e=>{const r=b(e);return[Z(r,"success","Success"),Z(r,"processing","Info"),Z(r,"error","Error"),Z(r,"warning","Warning")]}),x),S=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(n[t[o]]=e[t[o]])}return n};const $=t.forwardRef(((e,r)=>{const{prefixCls:n,className:o,rootClassName:p,style:m,children:h,icon:f,color:g,onClose:b,bordered:x=!0,visible:k}=e,y=S(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:_,direction:C,tag:Z}=t.useContext(u.E_),[$,P]=t.useState(!0),O=(0,c.Z)(y,["closeIcon","closable"]);t.useEffect((()=>{void 0!==k&&P(k)}),[k]);const I=(0,i.o2)(g),T=(0,i.yT)(g),E=I||T,N=Object.assign(Object.assign({backgroundColor:g&&!E?g:void 0},null==Z?void 0:Z.style),m),z=_("tag",n),[B,F,H]=v(z),R=a()(z,null==Z?void 0:Z.className,{[`${z}-${g}`]:E,[`${z}-has-color`]:g&&!E,[`${z}-hidden`]:!$,[`${z}-rtl`]:"rtl"===C,[`${z}-borderless`]:!x},o,p,F,H),q=e=>{e.stopPropagation(),null==b||b(e),e.defaultPrevented||P(!1)},[,L]=(0,s.Z)((0,s.w)(e),(0,s.w)(Z),{closable:!1,closeIconRender:e=>{const r=t.createElement("span",{className:`${z}-close-icon`,onClick:q},e);return(0,l.wm)(e,r,(e=>({onClick:r=>{var n;null===(n=null==e?void 0:e.onClick)||void 0===n||n.call(e,r),q(r)},className:a()(null==e?void 0:e.className,`${z}-close-icon`)})))}}),W="function"==typeof y.onClick||h&&"a"===h.type,M=f||null,D=M?t.createElement(t.Fragment,null,M,h&&t.createElement("span",null,h)):h,X=t.createElement("span",Object.assign({},O,{ref:r,className:R,style:N}),D,L,I&&t.createElement(w,{key:"preset",prefixCls:z}),T&&t.createElement(j,{key:"status",prefixCls:z}));return B(W?t.createElement(d.Z,{component:"Tag"},X):X)})),P=$;P.CheckableTag=_;var O=P}}]);