from typing import List, Optional
from llama_index import (
    VectorStoreIndex,
    ServiceContext,
    Document as <PERSON><PERSON>aDocument,
    SimpleD<PERSON>ctory<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ict<PERSON>,
    PromptHelper
)
from llama_index.embeddings import LangchainEmbedding
from llama_index.vector_stores import SimpleVectorStore
from llama_index.node_parser import SimpleNodeParser
from langchain.embeddings.huggingface import HuggingFaceEmbeddings

from .base_rag import (
    Document,
    BaseVectorStore,
    BaseEmbedding,
    BaseLLM,
    BaseTokenizer,
    BasePromptTemplate,
    BaseRetriever
)

class LlamaVectorStore(BaseVectorStore):
    def __init__(self):
        self.vector_store = SimpleVectorStore()
        self.index = None
    
    def add_documents(self, documents: List[Document]):
        llama_docs = [
            LlamaDocument(text=doc.content, metadata=doc.metadata)
            for doc in documents
        ]
        self.index = VectorStoreIndex.from_documents(
            llama_docs,
            service_context=self.service_context
        )
    
    def search(self, query: str, top_k: int = 3) -> List[Document]:
        if not self.index:
            return []
        
        retriever = self.index.as_retriever(similarity_top_k=top_k)
        nodes = retriever.retrieve(query)
        
        return [
            Document(
                content=node.text,
                metadata=node.metadata,
                score=node.score
            )
            for node in nodes
        ]

class LlamaEmbedding(BaseEmbedding):
    def __init__(self, model_name: str = "sentence-transformers/all-MiniLM-L6-v2"):
        self.embedding_model = LangchainEmbedding(
            HuggingFaceEmbeddings(model_name=model_name)
        )
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        return [self.embedding_model.get_text_embedding(text) for text in texts]
    
    def embed_query(self, query: str) -> List[float]:
        return self.embedding_model.get_text_embedding(query)

class LlamaTokenizer(BaseTokenizer):
    def __init__(self, chunk_size: int = 512, chunk_overlap: int = 20):
        self.parser = SimpleNodeParser.from_defaults(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
    
    def split_text(self, text: str) -> List[str]:
        nodes = self.parser.get_nodes_from_documents(
            [LlamaDocument(text=text)]
        )
        return [node.text for node in nodes]
    
    def merge_texts(self, texts: List[str]) -> str:
        return "\n".join(texts)

class LlamaPromptTemplate(BasePromptTemplate):
    def __init__(self, template: Optional[str] = None):
        self.template = template or """基于以下上下文回答问题。如果上下文中没有相关信息，请说明无法回答。

上下文信息:
{context}

问题: {query}

回答:"""
    
    def format(self, **kwargs) -> str:
        return self.template.format(**kwargs)

class LlamaWorkflow:
    def __init__(
        self,
        llm: BaseLLM,
        embedding_model: Optional[BaseEmbedding] = None,
        chunk_size: int = 512,
        chunk_overlap: int = 20
    ):
        # 初始化组件
        self.embedding_model = embedding_model or LlamaEmbedding()
        self.tokenizer = LlamaTokenizer(chunk_size, chunk_overlap)
        self.vector_store = LlamaVectorStore()
        self.prompt_template = LlamaPromptTemplate()
        
        # 设置 LlamaIndex 服务上下文
        self.service_context = ServiceContext.from_defaults(
            llm_predictor=LLMPredictor(llm=llm),
            embed_model=self.embedding_model,
            node_parser=self.tokenizer.parser
        )
        
        # 创建 RAG 工作流
        self.rag_workflow = RAGWorkflow(
            vector_store=self.vector_store,
            embedding_model=self.embedding_model,
            llm=llm,
            tokenizer=self.tokenizer,
            prompt_template=self.prompt_template
        )
    
    def add_documents(self, documents: List[Document]):
        """添加文档到知识库"""
        self.rag_workflow.add_documents(documents)
    
    def query(self, user_query: str, top_k: int = 3) -> str:
        """处理用户查询"""
        return self.rag_workflow.query(user_query, top_k) 