from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.consumerProtectionRule import ConsumerProtectionRule, ConsumerProtectionRuleCreate, ConsumerProtectionRuleUpdate, ConsumerProtectionRuleResponse
from datetime import datetime
from ..utils.auth import verify_token
from bson.objectid import ObjectId
import traceback
from ..db.mongodb import db

from app.utils.logging_config import setup_logging, get_logger
setup_logging()
logger = get_logger(__name__)

router = APIRouter(
    prefix="/api",
    tags=["consumer-protection-rules"]
)

COLLECTION = "consumer_protection_rules"

# 创建新规则
@router.post("/consumer-protection-rules", response_model=ConsumerProtectionRuleResponse)
async def create_rule(rule: ConsumerProtectionRuleCreate, current_user: dict = Depends(verify_token)):
    """创建新的消费者保护规则"""
    try:
        now = datetime.now()
        new_rule = {
            "ruleName": rule.ruleName,
            "ruleType": rule.ruleType,
            "description": rule.description,
            "source": rule.source,
            "logic": rule.logic,
            "user_id": current_user.get("id"),
            "result_definition": rule.result_definition,
            "created_at": now,
            "updated_at": now
        }
        result = await db[COLLECTION].insert_one(new_rule)
        new_rule["id"] = str(result.inserted_id)
        new_rule["created_at"] = new_rule["created_at"].strftime("%Y-%m-%d %H:%M:%S")
        new_rule["updated_at"] = new_rule["updated_at"].strftime("%Y-%m-%d %H:%M:%S")
        logger.info(f"创建新规则成功: {new_rule['id']}")
        return ConsumerProtectionRuleResponse(**new_rule)
    except Exception as e:
        traceback.print_exc()
        logger.error(f"创建规则失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建规则失败: {str(e)}")

# 获取规则列表
@router.get("/consumer-protection-rules", response_model=Dict[str, Any])
async def get_rules(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    name: Optional[str] = None,
    type: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    """获取消费者保护规则列表，支持分页和筛选"""
    try:
        query = {"user_id": current_user.get("id")}
        if name:
            query["ruleName"] = {"$regex": name, "$options": "i"}
        if type:
            query["ruleType"] = type
        logger.info(f"查询条件: {query}")
        total = await db[COLLECTION].count_documents(query)
        skip = (current - 1) * pageSize
        cursor = db[COLLECTION].find(query).sort("created_at", -1).skip(skip).limit(pageSize)
        rules = []
        async for rule in cursor:
            rule["id"] = str(rule.pop("_id"))
            rule["created_at"] = rule["created_at"].strftime("%Y-%m-%d %H:%M:%S") if rule.get("created_at") else None
            rule["updated_at"] = rule["updated_at"].strftime("%Y-%m-%d %H:%M:%S") if rule.get("updated_at") else None
            rules.append(rule)
        return {
            "data": rules,
            "total": total,
            "success": True,
            "pageSize": pageSize,
            "current": current
        }
    except Exception as e:
        traceback.print_exc()
        logger.error(f"获取规则列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取规则列表失败: {str(e)}")

# 获取单个规则详情
@router.get("/consumer-protection-rules/{rule_id}", response_model=ConsumerProtectionRuleResponse)
async def get_rule(rule_id: str, current_user: dict = Depends(verify_token)):
    """获取单个消费者保护规则详情"""
    try:
        rule = None
        try:
            if ObjectId.is_valid(rule_id):
                rule = await db[COLLECTION].find_one({"_id": ObjectId(rule_id)})
        except:
            pass
        if not rule:
            raise HTTPException(status_code=404, detail=f"规则不存在: {rule_id}")
        response = {
            "id": str(rule["_id"]),
            "ruleName": rule["ruleName"],
            "ruleType": rule["ruleType"],
            "description": rule["description"],
            "source": rule["source"],
            "logic": rule["logic"],
            "result_definition": rule["result_definition"],
            "created_at": rule["created_at"].strftime("%Y-%m-%d %H:%M:%S") if rule.get("created_at") else None,
            "updated_at": rule["updated_at"].strftime("%Y-%m-%d %H:%M:%S") if rule.get("updated_at") else None
        }
        return ConsumerProtectionRuleResponse(**response)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取规则详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取规则详情失败: {str(e)}")

# 更新规则
@router.put("/consumer-protection-rules/{rule_id}", response_model=ConsumerProtectionRuleResponse)
async def update_rule(rule_id: str, rule_update: ConsumerProtectionRuleUpdate, current_user: dict = Depends(verify_token)):
    """更新消费者保护规则"""
    try:
        query = {"_id": ObjectId(rule_id)} if ObjectId.is_valid(rule_id) else {"_id": rule_id}
        rule = await db[COLLECTION].find_one(query)
        if not rule:
            raise HTTPException(status_code=404, detail=f"规则不存在: {rule_id}")
        update_data = {k: v for k, v in rule_update.dict(exclude_unset=True).items()}
        update_data["updated_at"] = datetime.now()
        await db[COLLECTION].update_one(
            {"_id": rule["_id"]},
            {"$set": update_data}
        )
        updated_rule = await db[COLLECTION].find_one({"_id": rule["_id"]})
        updated_rule["id"] = str(updated_rule.pop("_id"))
        updated_rule["created_at"] = updated_rule["created_at"].strftime("%Y-%m-%d %H:%M:%S") if updated_rule.get("created_at") else None
        updated_rule["updated_at"] = updated_rule["updated_at"].strftime("%Y-%m-%d %H:%M:%S") if updated_rule.get("updated_at") else None
        logger.info(f"更新规则成功: {updated_rule['id']}")
        return ConsumerProtectionRuleResponse(**updated_rule)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新规则失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新规则失败: {str(e)}")

# 删除规则
@router.delete("/consumer-protection-rules/{rule_id}", response_model=Dict[str, Any])
async def delete_rule(rule_id: str, current_user: dict = Depends(verify_token)):
    """删除消费者保护规则"""
    try:
        rule = None
        try:
            if ObjectId.is_valid(rule_id):
                rule = await db[COLLECTION].find_one({"_id": ObjectId(rule_id)})
        except:
            pass
        if not rule:
            raise HTTPException(status_code=404, detail=f"规则不存在: {rule_id}")
        await db[COLLECTION].delete_one({"_id": rule["_id"]})
        logger.info(f"删除规则成功: {rule_id}")
        return {"success": True, "message": f"规则 '{rule_id}' 已成功删除"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除规则失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除规则失败: {str(e)}")

# 批量删除规则
@router.post("/consumer-protection-rules/batch-delete", response_model=Dict[str, Any])
async def batch_delete_rules(rule_ids: List[str], current_user: dict = Depends(verify_token)):
    """批量删除消费者保护规则"""
    try:
        deleted_count = 0
        not_found = []
        for rule_id in rule_ids:
            rule = None
            try:
                if ObjectId.is_valid(rule_id):
                    rule = await db[COLLECTION].find_one({"_id": ObjectId(rule_id)})
            except:
                pass
            if rule:
                await db[COLLECTION].delete_one({"_id": rule["_id"]})
                deleted_count += 1
            else:
                not_found.append(rule_id)
        response = {
            "success": True,
            "deleted_count": deleted_count,
            "message": f"成功删除 {deleted_count} 条规则"
        }
        if not_found:
            response["not_found"] = not_found
            response["message"] += f", {len(not_found)} 条规则未找到"
        logger.info(f"批量删除规则: 成功 {deleted_count}, 未找到 {len(not_found)}")
        return response
    except Exception as e:
        logger.error(f"批量删除规则失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量删除规则失败: {str(e)}")
