from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.rerank import RerankModel, RerankCreate, RerankUpdate, RerankResponse
from ..db.mongodb import db
from datetime import datetime
from ..utils.auth import verify_token

router = APIRouter()

# 获取所有 Embedding 模型，支持分页
@router.get("/api/reranks", response_model=Dict[str, Any])
async def get_reranks(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    m_name: Optional[str] = None,  # 支持按模型名称检索
    provider: Optional[str] = None,  # 支持按提供商检索
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {}
    if m_name:
        query["m_name"] = {"$regex": m_name, "$options": "i"}
    if provider:
        query["provider"] = {"$regex": provider, "$options": "i"}

    reranks = await db["reranks"].find(query, {
        "_id": 0,
        "id": 1,
        "api_key": 1,
        "service_url": 1,
        "name": 1,
        "reRank_model": 1,
        "provider": 1,
        "is_active": 1,
        "created_at": 1
    }).skip(skip).limit(pageSize).to_list(pageSize)
    total = await db["reranks"].count_documents(query)
    return {
        "data": reranks,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 添加新 Embedding 模型
@router.post("/api/reranks", response_model=RerankResponse)
async def add_rerank(rerank: RerankCreate, current_user: dict = Depends(verify_token)):
    last_rerank = await db["reranks"].find_one(sort=[("id", -1)])
    new_id = (last_rerank["id"] + 1) if last_rerank else 1

    new_rerank = rerank.dict()
    new_rerank.update({
        "id": new_id,
        "created_at": datetime.now(),
        "created_by": current_user["id"],  # 确保设置 created_by
        "is_active": True,
        "api_key": rerank.api_key,
        "embedding_name": rerank.embedding_name,
        "provider": rerank.provider,
        "service_url": rerank.service_url,
        "vector_size": rerank.vector_size  # 确保设置 vector_size
    })

    await db["reranks"].insert_one(new_rerank)
    return RerankResponse(**new_rerank)

# 更新 Embedding 模型
@router.put("/api/reranks/{rerank_id}", response_model=RerankResponse)
async def update_rerank(rerank_id: int, rerank: RerankUpdate, current_user: dict = Depends(verify_token)):
    result = await db["reranks"].update_one({"id": rerank_id}, {"$set": rerank.dict(exclude_unset=True)})
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Rerank not found")
    updated_rerank = await db["reranks"].find_one({"id": rerank_id})
    return RerankResponse(**updated_rerank)

# 删除 Embedding 模型
@router.delete("/api/reranks/{rerank_id}", response_model=Dict[str, int])
async def delete_rerank(rerank_id: int, current_user: dict = Depends(verify_token)):
    rerank = await db["reranks"].find_one({"id": rerank_id})
    if not rerank:
        raise HTTPException(status_code=404, detail="Rerank not found")
    result = await db["reranks"].delete_one({"id": rerank_id})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Rerank not found")
    return {"id": rerank_id}

@router.get("/api/rerankModelList", response_model=Dict[str, Any])
async def get_reranks(
    current_user: dict = Depends(verify_token)
):
    reranks = await db["reranks"].find({"is_active": True}, {
        "_id": 0,
        "id": 1,
        "name": 1,
        "reRank_model": 1,
    }).to_list()
    return {
        "data": reranks,
        "success": True,
    }