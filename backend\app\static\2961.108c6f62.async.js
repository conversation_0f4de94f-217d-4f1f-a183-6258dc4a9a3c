"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2961],{2961:function(e,t,n){n.d(t,{Z:function(){return K}});var a,r=n(67294),o=n(93967),i=n.n(o),l=n(87462),s=n(4942),u=n(1413),c=n(74902),d=n(97685),f=n(91),p=n(67656),m=n(82234),g=n(87887),v=n(21770),x=n(71002),b=n(9220),h=n(8410),w=n(75164),y="\n  min-height:0 !important;\n  max-height:none !important;\n  height:0 !important;\n  visibility:hidden !important;\n  overflow:hidden !important;\n  position:absolute !important;\n  z-index:-1000 !important;\n  top:0 !important;\n  right:0 !important;\n  pointer-events: none !important;\n",C=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","font-variant","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing","word-break","white-space"],z={};function S(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.getAttribute("id")||e.getAttribute("data-reactid")||e.getAttribute("name");if(t&&z[n])return z[n];var a=window.getComputedStyle(e),r=a.getPropertyValue("box-sizing")||a.getPropertyValue("-moz-box-sizing")||a.getPropertyValue("-webkit-box-sizing"),o=parseFloat(a.getPropertyValue("padding-bottom"))+parseFloat(a.getPropertyValue("padding-top")),i=parseFloat(a.getPropertyValue("border-bottom-width"))+parseFloat(a.getPropertyValue("border-top-width")),l=C.map((function(e){return"".concat(e,":").concat(a.getPropertyValue(e))})).join(";"),s={sizingStyle:l,paddingSize:o,borderSize:i,boxSizing:r};return t&&n&&(z[n]=s),s}var Z=["prefixCls","defaultValue","value","autoSize","onResize","className","style","disabled","onChange","onInternalAutoSize"],E=r.forwardRef((function(e,t){var n=e,o=n.prefixCls,c=n.defaultValue,p=n.value,m=n.autoSize,g=n.onResize,C=n.className,z=n.style,E=n.disabled,$=n.onChange,A=(n.onInternalAutoSize,(0,f.Z)(n,Z)),I=(0,v.Z)(c,{value:p,postState:function(e){return null!=e?e:""}}),N=(0,d.Z)(I,2),O=N[0],R=N[1],j=r.useRef();r.useImperativeHandle(t,(function(){return{textArea:j.current}}));var F=r.useMemo((function(){return m&&"object"===(0,x.Z)(m)?[m.minRows,m.maxRows]:[]}),[m]),P=(0,d.Z)(F,2),T=P[0],V=P[1],k=!!m,H=r.useState(2),L=(0,d.Z)(H,2),M=L[0],W=L[1],B=r.useState(),D=(0,d.Z)(B,2),_=D[0],K=D[1],X=function(){W(0)};(0,h.Z)((function(){k&&X()}),[p,T,V,k]),(0,h.Z)((function(){if(0===M)W(1);else if(1===M){var e=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;a||((a=document.createElement("textarea")).setAttribute("tab-index","-1"),a.setAttribute("aria-hidden","true"),a.setAttribute("name","hiddenTextarea"),document.body.appendChild(a)),e.getAttribute("wrap")?a.setAttribute("wrap",e.getAttribute("wrap")):a.removeAttribute("wrap");var o=S(e,t),i=o.paddingSize,l=o.borderSize,s=o.boxSizing,u=o.sizingStyle;a.setAttribute("style","".concat(u,";").concat(y)),a.value=e.value||e.placeholder||"";var c,d=void 0,f=void 0,p=a.scrollHeight;if("border-box"===s?p+=l:"content-box"===s&&(p-=i),null!==n||null!==r){a.value=" ";var m=a.scrollHeight-i;null!==n&&(d=m*n,"border-box"===s&&(d=d+i+l),p=Math.max(d,p)),null!==r&&(f=m*r,"border-box"===s&&(f=f+i+l),c=p>f?"":"hidden",p=Math.min(f,p))}var g={height:p,overflowY:c,resize:"none"};return d&&(g.minHeight=d),f&&(g.maxHeight=f),g}(j.current,!1,T,V);W(2),K(e)}else!function(){try{if(document.activeElement===j.current){var e=j.current,t=e.selectionStart,n=e.selectionEnd,a=e.scrollTop;j.current.setSelectionRange(t,n),j.current.scrollTop=a}}catch(e){}}()}),[M]);var q=r.useRef(),J=function(){w.Z.cancel(q.current)};r.useEffect((function(){return J}),[]);var Y=k?_:null,G=(0,u.Z)((0,u.Z)({},z),Y);return 0!==M&&1!==M||(G.overflowY="hidden",G.overflowX="hidden"),r.createElement(b.Z,{onResize:function(e){2===M&&(null==g||g(e),m&&(J(),q.current=(0,w.Z)((function(){X()}))))},disabled:!(m||g)},r.createElement("textarea",(0,l.Z)({},A,{ref:j,style:G,className:i()(o,C,(0,s.Z)({},"".concat(o,"-disabled"),E)),disabled:E,value:O,onChange:function(e){R(e.target.value),null==$||$(e)}})))})),$=E,A=["defaultValue","value","onFocus","onBlur","onChange","allowClear","maxLength","onCompositionStart","onCompositionEnd","suffix","prefixCls","showCount","count","className","style","disabled","hidden","classNames","styles","onResize","onClear","onPressEnter","readOnly","autoSize","onKeyDown"],I=r.forwardRef((function(e,t){var n,a=e.defaultValue,o=e.value,x=e.onFocus,b=e.onBlur,h=e.onChange,w=e.allowClear,y=e.maxLength,C=e.onCompositionStart,z=e.onCompositionEnd,S=e.suffix,Z=e.prefixCls,E=void 0===Z?"rc-textarea":Z,I=e.showCount,N=e.count,O=e.className,R=e.style,j=e.disabled,F=e.hidden,P=e.classNames,T=e.styles,V=e.onResize,k=e.onClear,H=e.onPressEnter,L=e.readOnly,M=e.autoSize,W=e.onKeyDown,B=(0,f.Z)(e,A),D=(0,v.Z)(a,{value:o,defaultValue:a}),_=(0,d.Z)(D,2),K=_[0],X=_[1],q=null==K?"":String(K),J=r.useState(!1),Y=(0,d.Z)(J,2),G=Y[0],Q=Y[1],U=r.useRef(!1),ee=r.useState(null),te=(0,d.Z)(ee,2),ne=te[0],ae=te[1],re=(0,r.useRef)(null),oe=(0,r.useRef)(null),ie=function(){var e;return null===(e=oe.current)||void 0===e?void 0:e.textArea},le=function(){ie().focus()};(0,r.useImperativeHandle)(t,(function(){var e;return{resizableTextArea:oe.current,focus:le,blur:function(){ie().blur()},nativeElement:(null===(e=re.current)||void 0===e?void 0:e.nativeElement)||ie()}})),(0,r.useEffect)((function(){Q((function(e){return!j&&e}))}),[j]);var se=r.useState(null),ue=(0,d.Z)(se,2),ce=ue[0],de=ue[1];r.useEffect((function(){var e;ce&&(e=ie()).setSelectionRange.apply(e,(0,c.Z)(ce))}),[ce]);var fe,pe=(0,m.Z)(N,I),me=null!==(n=pe.max)&&void 0!==n?n:y,ge=Number(me)>0,ve=pe.strategy(q),xe=!!me&&ve>me,be=function(e,t){var n=t;!U.current&&pe.exceedFormatter&&pe.max&&pe.strategy(t)>pe.max&&t!==(n=pe.exceedFormatter(t,{max:pe.max}))&&de([ie().selectionStart||0,ie().selectionEnd||0]),X(n),(0,g.rJ)(e.currentTarget,e,h,n)},he=S;pe.show&&(fe=pe.showFormatter?pe.showFormatter({value:q,count:ve,maxLength:me}):"".concat(ve).concat(ge?" / ".concat(me):""),he=r.createElement(r.Fragment,null,he,r.createElement("span",{className:i()("".concat(E,"-data-count"),null==P?void 0:P.count),style:null==T?void 0:T.count},fe)));var we=!M&&!I&&!w;return r.createElement(p.Q,{ref:re,value:q,allowClear:w,handleReset:function(e){X(""),le(),(0,g.rJ)(ie(),e,h)},suffix:he,prefixCls:E,classNames:(0,u.Z)((0,u.Z)({},P),{},{affixWrapper:i()(null==P?void 0:P.affixWrapper,(0,s.Z)((0,s.Z)({},"".concat(E,"-show-count"),I),"".concat(E,"-textarea-allow-clear"),w))}),disabled:j,focused:G,className:i()(O,xe&&"".concat(E,"-out-of-range")),style:(0,u.Z)((0,u.Z)({},R),ne&&!we?{height:"auto"}:{}),dataAttrs:{affixWrapper:{"data-count":"string"==typeof fe?fe:void 0}},hidden:F,readOnly:L,onClear:k},r.createElement($,(0,l.Z)({},B,{autoSize:M,maxLength:y,onKeyDown:function(e){"Enter"===e.key&&H&&H(e),null==W||W(e)},onChange:function(e){be(e,e.target.value)},onFocus:function(e){Q(!0),null==x||x(e)},onBlur:function(e){Q(!1),null==b||b(e)},onCompositionStart:function(e){U.current=!0,null==C||C(e)},onCompositionEnd:function(e){U.current=!1,be(e,e.currentTarget.value),null==z||z(e)},className:i()(null==P?void 0:P.textarea),style:(0,u.Z)((0,u.Z)({},null==T?void 0:T.textarea),{},{resize:null==R?void 0:R.resize}),disabled:j,prefixCls:E,onResize:function(e){var t;null==V||V(e),null!==(t=ie())&&void 0!==t&&t.style.height&&ae(!0)},ref:oe,readOnly:L})))})),N=n(78290),O=n(9708),R=n(53124),j=n(98866),F=n(35792),P=n(98675),T=n(65223),V=n(27833),k=n(4173),H=n(47673),L=n(83559),M=n(83262),W=n(20353);const B=e=>{const{componentCls:t,paddingLG:n}=e,a=`${t}-textarea`;return{[a]:{position:"relative","&-show-count":{[`> ${t}`]:{height:"100%"},[`${t}-data-count`]:{position:"absolute",bottom:e.calc(e.fontSize).mul(e.lineHeight).mul(-1).equal(),insetInlineEnd:0,color:e.colorTextDescription,whiteSpace:"nowrap",pointerEvents:"none"}},[`\n        &-allow-clear > ${t},\n        &-affix-wrapper${a}-has-feedback ${t}\n      `]:{paddingInlineEnd:n},[`&-affix-wrapper${t}-affix-wrapper`]:{padding:0,[`> textarea${t}`]:{fontSize:"inherit",border:"none",outline:"none",background:"transparent",minHeight:e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),"&:focus":{boxShadow:"none !important"}},[`${t}-suffix`]:{margin:0,"> *:not(:last-child)":{marginInline:0},[`${t}-clear-icon`]:{position:"absolute",insetInlineEnd:e.paddingInline,insetBlockStart:e.paddingXS},[`${a}-suffix`]:{position:"absolute",top:0,insetInlineEnd:e.paddingInline,bottom:0,zIndex:1,display:"inline-flex",alignItems:"center",margin:"auto",pointerEvents:"none"}}},[`&-affix-wrapper${t}-affix-wrapper-sm`]:{[`${t}-suffix`]:{[`${t}-clear-icon`]:{insetInlineEnd:e.paddingInlineSM}}}}}};var D=(0,L.I$)(["Input","TextArea"],(e=>{const t=(0,M.IX)(e,(0,W.e)(e));return[B(t)]}),W.T,{resetFont:!1}),_=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n};var K=(0,r.forwardRef)(((e,t)=>{var n;const{prefixCls:a,bordered:o=!0,size:l,disabled:s,status:u,allowClear:c,classNames:d,rootClassName:f,className:p,style:m,styles:v,variant:x}=e,b=_(e,["prefixCls","bordered","size","disabled","status","allowClear","classNames","rootClassName","className","style","styles","variant"]);const{getPrefixCls:h,direction:w,allowClear:y,autoComplete:C,className:z,style:S,classNames:Z,styles:E}=(0,R.dj)("textArea"),$=r.useContext(j.Z),A=null!=s?s:$,{status:L,hasFeedback:M,feedbackIcon:W}=r.useContext(T.aM),B=(0,O.F)(L,u),K=r.useRef(null);r.useImperativeHandle(t,(()=>{var e;return{resizableTextArea:null===(e=K.current)||void 0===e?void 0:e.resizableTextArea,focus:e=>{var t,n;(0,g.nH)(null===(n=null===(t=K.current)||void 0===t?void 0:t.resizableTextArea)||void 0===n?void 0:n.textArea,e)},blur:()=>{var e;return null===(e=K.current)||void 0===e?void 0:e.blur()}}}));const X=h("input",a),q=(0,F.Z)(X),[J,Y,G]=(0,H.TI)(X,f),[Q]=D(X,q),{compactSize:U,compactItemClassnames:ee}=(0,k.ri)(X,w),te=(0,P.Z)((e=>{var t;return null!==(t=null!=l?l:U)&&void 0!==t?t:e})),[ne,ae]=(0,V.Z)("textArea",x,o),re=(0,N.Z)(null!=c?c:y);return J(Q(r.createElement(I,Object.assign({autoComplete:C},b,{style:Object.assign(Object.assign({},S),m),styles:Object.assign(Object.assign({},E),v),disabled:A,allowClear:re,className:i()(G,q,p,f,ee,z),classNames:Object.assign(Object.assign(Object.assign({},d),Z),{textarea:i()({[`${X}-sm`]:"small"===te,[`${X}-lg`]:"large"===te},Y,null==d?void 0:d.textarea,Z.textarea),variant:i()({[`${X}-${ne}`]:ae},(0,O.Z)(X,B)),affixWrapper:i()(`${X}-textarea-affix-wrapper`,{[`${X}-affix-wrapper-rtl`]:"rtl"===w,[`${X}-affix-wrapper-sm`]:"small"===te,[`${X}-affix-wrapper-lg`]:"large"===te,[`${X}-textarea-show-count`]:e.showCount||(null===(n=e.count)||void 0===n?void 0:n.show)},Y)}),prefixCls:X,suffix:M&&r.createElement("span",{className:`${X}-textarea-suffix`},W),ref:K}))))}))}}]);