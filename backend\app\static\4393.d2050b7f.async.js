"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4393],{4393:function(e,t,a){a.d(t,{Z:function(){return L}});var n=a(67294),r=a(93967),o=a.n(r),i=a(98423),l=a(53124),d=a(98675),s=a(48054),c=a(11941),b=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]])}return a};var g=e=>{var{prefixCls:t,className:a,hoverable:r=!0}=e,i=b(e,["prefixCls","className","hoverable"]);const{getPrefixCls:d}=n.useContext(l.E_),s=d("card",t),c=o()(`${s}-grid`,a,{[`${s}-grid-hoverable`]:r});return n.createElement("div",Object.assign({},i,{className:c}))},p=a(11568),m=a(14747),h=a(83559),f=a(83262);const $=e=>{const{antCls:t,componentCls:a,headerHeight:n,headerPadding:r,tabsMarginBottom:o}=e;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:n,marginBottom:-1,padding:`0 ${(0,p.bf)(r)}`,color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.headerFontSize,background:e.headerBg,borderBottom:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`,borderRadius:`${(0,p.bf)(e.borderRadiusLG)} ${(0,p.bf)(e.borderRadiusLG)} 0 0`},(0,m.dF)()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},m.vS),{[`\n          > ${a}-typography,\n          > ${a}-typography-edit-content\n        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${t}-tabs-top`]:{clear:"both",marginBottom:o,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`}}})},u=e=>{const{cardPaddingBase:t,colorBorderSecondary:a,cardShadow:n,lineWidth:r}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:`\n      ${(0,p.bf)(r)} 0 0 0 ${a},\n      0 ${(0,p.bf)(r)} 0 0 ${a},\n      ${(0,p.bf)(r)} ${(0,p.bf)(r)} 0 0 ${a},\n      ${(0,p.bf)(r)} 0 0 0 ${a} inset,\n      0 ${(0,p.bf)(r)} 0 0 ${a} inset;\n    `,transition:`all ${e.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:n}}},y=e=>{const{componentCls:t,iconCls:a,actionsLiMargin:n,cardActionsIconSize:r,colorBorderSecondary:o,actionsBg:i}=e;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:i,borderTop:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${o}`,display:"flex",borderRadius:`0 0 ${(0,p.bf)(e.borderRadiusLG)} ${(0,p.bf)(e.borderRadiusLG)}`},(0,m.dF)()),{"& > li":{margin:n,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.calc(e.cardActionsIconSize).mul(2).equal(),fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:`color ${e.motionDurationMid}`},[`a:not(${t}-btn), > ${a}`]:{display:"inline-block",width:"100%",color:e.colorTextDescription,lineHeight:(0,p.bf)(e.fontHeight),transition:`color ${e.motionDurationMid}`,"&:hover":{color:e.colorPrimary}},[`> ${a}`]:{fontSize:r,lineHeight:(0,p.bf)(e.calc(r).mul(e.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${o}`}}})},v=e=>Object.assign(Object.assign({margin:`${(0,p.bf)(e.calc(e.marginXXS).mul(-1).equal())} 0`,display:"flex"},(0,m.dF)()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":Object.assign({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},m.vS),"&-description":{color:e.colorTextDescription}}),S=e=>{const{componentCls:t,colorFillAlter:a,headerPadding:n,bodyPadding:r}=e;return{[`${t}-head`]:{padding:`0 ${(0,p.bf)(n)}`,background:a,"&-title":{fontSize:e.fontSize}},[`${t}-body`]:{padding:`${(0,p.bf)(e.padding)} ${(0,p.bf)(r)}`}}},x=e=>{const{componentCls:t}=e;return{overflow:"hidden",[`${t}-body`]:{userSelect:"none"}}},O=e=>{const{componentCls:t,cardShadow:a,cardHeadPadding:n,colorBorderSecondary:r,boxShadowTertiary:o,bodyPadding:i,extraColor:l}=e;return{[t]:Object.assign(Object.assign({},(0,m.Wf)(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,[`&:not(${t}-bordered)`]:{boxShadow:o},[`${t}-head`]:$(e),[`${t}-extra`]:{marginInlineStart:"auto",color:l,fontWeight:"normal",fontSize:e.fontSize},[`${t}-body`]:Object.assign({padding:i,borderRadius:`0 0 ${(0,p.bf)(e.borderRadiusLG)} ${(0,p.bf)(e.borderRadiusLG)}`},(0,m.dF)()),[`${t}-grid`]:u(e),[`${t}-cover`]:{"> *":{display:"block",width:"100%",borderRadius:`${(0,p.bf)(e.borderRadiusLG)} ${(0,p.bf)(e.borderRadiusLG)} 0 0`}},[`${t}-actions`]:y(e),[`${t}-meta`]:v(e)}),[`${t}-bordered`]:{border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${r}`,[`${t}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${t}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${e.motionDurationMid}, border-color ${e.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:a}},[`${t}-contain-grid`]:{borderRadius:`${(0,p.bf)(e.borderRadiusLG)} ${(0,p.bf)(e.borderRadiusLG)} 0 0 `,[`${t}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${t}-loading) ${t}-body`]:{marginBlockStart:e.calc(e.lineWidth).mul(-1).equal(),marginInlineStart:e.calc(e.lineWidth).mul(-1).equal(),padding:0}},[`${t}-contain-tabs`]:{[`> div${t}-head`]:{minHeight:0,[`${t}-head-title, ${t}-extra`]:{paddingTop:n}}},[`${t}-type-inner`]:S(e),[`${t}-loading`]:x(e),[`${t}-rtl`]:{direction:"rtl"}}},C=e=>{const{componentCls:t,bodyPaddingSM:a,headerPaddingSM:n,headerHeightSM:r,headerFontSizeSM:o}=e;return{[`${t}-small`]:{[`> ${t}-head`]:{minHeight:r,padding:`0 ${(0,p.bf)(n)}`,fontSize:o,[`> ${t}-head-wrapper`]:{[`> ${t}-extra`]:{fontSize:e.fontSize}}},[`> ${t}-body`]:{padding:a}},[`${t}-small${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{paddingTop:0,display:"flex",alignItems:"center"}}}}};var j=(0,h.I$)("Card",(e=>{const t=(0,f.IX)(e,{cardShadow:e.boxShadowCard,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardActionsIconSize:e.fontSize});return[O(t),C(t)]}),(e=>{var t,a;return{headerBg:"transparent",headerFontSize:e.fontSizeLG,headerFontSizeSM:e.fontSize,headerHeight:e.fontSizeLG*e.lineHeightLG+2*e.padding,headerHeightSM:e.fontSize*e.lineHeight+2*e.paddingXS,actionsBg:e.colorBgContainer,actionsLiMargin:`${e.paddingSM}px 0`,tabsMarginBottom:-e.padding-e.lineWidth,extraColor:e.colorText,bodyPaddingSM:12,headerPaddingSM:12,bodyPadding:null!==(t=e.bodyPadding)&&void 0!==t?t:e.paddingLG,headerPadding:null!==(a=e.headerPadding)&&void 0!==a?a:e.paddingLG}})),w=a(27833),E=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]])}return a};const P=e=>{const{actionClasses:t,actions:a=[],actionStyle:r}=e;return n.createElement("ul",{className:t,style:r},a.map(((e,t)=>{const r=`action-${t}`;return n.createElement("li",{style:{width:100/a.length+"%"},key:r},n.createElement("span",null,e))})))},z=n.forwardRef(((e,t)=>{const{prefixCls:a,className:r,rootClassName:b,style:p,extra:m,headStyle:h={},bodyStyle:f={},title:$,loading:u,bordered:y,variant:v,size:S,type:x,cover:O,actions:C,tabList:z,children:N,activeTabKey:B,defaultActiveTabKey:T,tabBarExtraContent:L,hoverable:M,tabProps:G={},classNames:H,styles:R}=e,I=E(e,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","variant","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:W,direction:k,card:D}=n.useContext(l.E_),[F]=(0,w.Z)("card",v,y);const A=e=>{var t;return o()(null===(t=null==D?void 0:D.classNames)||void 0===t?void 0:t[e],null==H?void 0:H[e])},_=e=>{var t;return Object.assign(Object.assign({},null===(t=null==D?void 0:D.styles)||void 0===t?void 0:t[e]),null==R?void 0:R[e])},K=n.useMemo((()=>{let e=!1;return n.Children.forEach(N,(t=>{(null==t?void 0:t.type)===g&&(e=!0)})),e}),[N]),Z=W("card",a),[q,X,J]=j(Z),Q=n.createElement(s.Z,{loading:!0,active:!0,paragraph:{rows:4},title:!1},N),U=void 0!==B,V=Object.assign(Object.assign({},G),{[U?"activeKey":"defaultActiveKey"]:U?B:T,tabBarExtraContent:L});let Y;const ee=(0,d.Z)(S),te=ee&&"default"!==ee?ee:"large",ae=z?n.createElement(c.Z,Object.assign({size:te},V,{className:`${Z}-head-tabs`,onChange:t=>{var a;null===(a=e.onTabChange)||void 0===a||a.call(e,t)},items:z.map((e=>{var{tab:t}=e,a=E(e,["tab"]);return Object.assign({label:t},a)}))})):null;if($||m||ae){const e=o()(`${Z}-head`,A("header")),t=o()(`${Z}-head-title`,A("title")),a=o()(`${Z}-extra`,A("extra")),r=Object.assign(Object.assign({},h),_("header"));Y=n.createElement("div",{className:e,style:r},n.createElement("div",{className:`${Z}-head-wrapper`},$&&n.createElement("div",{className:t,style:_("title")},$),m&&n.createElement("div",{className:a,style:_("extra")},m)),ae)}const ne=o()(`${Z}-cover`,A("cover")),re=O?n.createElement("div",{className:ne,style:_("cover")},O):null,oe=o()(`${Z}-body`,A("body")),ie=Object.assign(Object.assign({},f),_("body")),le=n.createElement("div",{className:oe,style:ie},u?Q:N),de=o()(`${Z}-actions`,A("actions")),se=(null==C?void 0:C.length)?n.createElement(P,{actionClasses:de,actionStyle:_("actions"),actions:C}):null,ce=(0,i.Z)(I,["onTabChange"]),be=o()(Z,null==D?void 0:D.className,{[`${Z}-loading`]:u,[`${Z}-bordered`]:"borderless"!==F,[`${Z}-hoverable`]:M,[`${Z}-contain-grid`]:K,[`${Z}-contain-tabs`]:null==z?void 0:z.length,[`${Z}-${ee}`]:ee,[`${Z}-type-${x}`]:!!x,[`${Z}-rtl`]:"rtl"===k},r,b,X,J),ge=Object.assign(Object.assign({},null==D?void 0:D.style),p);return q(n.createElement("div",Object.assign({ref:t},ce,{className:be,style:ge}),Y,re,le,se))}));var N=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]])}return a};var B=e=>{const{prefixCls:t,className:a,avatar:r,title:i,description:d}=e,s=N(e,["prefixCls","className","avatar","title","description"]),{getPrefixCls:c}=n.useContext(l.E_),b=c("card",t),g=o()(`${b}-meta`,a),p=r?n.createElement("div",{className:`${b}-meta-avatar`},r):null,m=i?n.createElement("div",{className:`${b}-meta-title`},i):null,h=d?n.createElement("div",{className:`${b}-meta-description`},d):null,f=m||h?n.createElement("div",{className:`${b}-meta-detail`},m,h):null;return n.createElement("div",Object.assign({},s,{className:g}),p,f)};const T=z;T.Grid=g,T.Meta=B;var L=T}}]);