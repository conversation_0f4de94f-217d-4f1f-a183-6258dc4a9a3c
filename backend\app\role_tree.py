routes = [
  {
    "name": 'UseCases',
    "access": 'canAccessUseCases',
  },
    {
        "name": 'LLMmarket',
        "access": 'canAccessLLMmarket',
        "routes": [
            {
                "name": 'llmChat',
                "access": 'canAccessLLMmarketLlmChat',
            },
            {
                "name": 'llmModels',
                "access": 'canAccessLLMmarketModels',
            },
            {
                "name": 'llmComparison',
                "access": 'canAccessLLMmarketLlmComparison',
            },
        ],
    },
    {
    "name": 'LMTools',
    "access": 'canAccessLMTools',
    "routes": [
      {
        "name": 'llmodelTuning',
        "access": 'canAccessLLModelTuning',
      },
      {
        "name": 'llmodelEvaluation',
        "access": 'canAccessModelEvaluation',
      },
    ],
  },
  {
        "name": "admin",
        "access": "canAccessAdmin",
        "routes": [
            {
                "name": "userManagement",
                "access": "canAccessUserManagement"
            },
            {
                "name": "roleManagement",
                "access": "canAccessRoleManagement"
            },
            {
                "name": "systemManagement",
                "access": "canAccessSystemManagement"
            },
            {
                "name": "groupManagement",
                "access": "canAccessGroupManagement"
            },
            {
                "name": 'systemAppSettings',
                "access": 'canAccessSystemAppSettings',
            },
            {
                "name": 'systemAppInfo',
                "access": 'canViewSystemAppInfo',
            },
            {
                "name": "conversationManagement",
                "access": "canAccessConversationManagement"
            },
            {
                "name": "messageManagement",
                "access": "canAccessMessageManagement"
            },
            {
                "name": "promptAdminManagement",
                "access": "canAccessPromptAdminManagement"
            },
            {
                "name": "useCasesManagement",
                "access": "canAccessUseCasesManagement"
            },
            {
                "name": "knowledgeBaseManagement",
                "access": "canAccessKnowledgeBaseManagement"
            },
            {
                "name": "mcpManagement",
                "access": "canAccessMcpManagement"
            },
            {
                "name": "announcementManagement",
                "access": "canAccessAnnouncementManagement"
            },
            {
                "name": "logManagement",
                "access": "canAccessLogManagement"
            },
            {
                "name": "ruleManagement",
                "access": "canAccessRuleManagement"
            },
        ]
    },
    {
        "name": "account",
        "access": "canAccessAccount",
        "routes": [
            {
                "name": "center",
                "access": "canAccessCenter"
            },
            {
                "name": "settings",
                "access": "canAccessSettings"
            }
        ]
    },
    {
        "name": "modelManagement",
        "access": "canAccessModelManagement",
        "routes": [
            {
                "name": "largeLanguageModel",
                "access": "canAccessLargeLanguageModel"
            },
            {
                "name": "embeddingModel",
                "access": "canAccessEmbeddingModel"
            },
            {
                "name": "rerankModel",
                "access": "canAccessRerankModel"
            },
            {
                "name": "modelAuthorization",
                "access": "canAccessModelAuthorization"
            }
        ]
    },
    {
        "name": "knowledgeManagement",  # 新增的一级菜单：知识管理
        "access": "canAccessKnowledgeManagement",
        "routes": [
            {
                "name": "knowledgeBase",  # 新增的二级菜单：知识库
                "access": "canAccessKnowledgeBase"
            },
            {
                "name": 'knowledgeInfo',
                "access": 'canAccessKnowledgeInfo',
            },
            {
                "name": 'fileInfo', 
                "access": 'canAccessFileInfo',
            },
            {
                "name": "knowledgeDashboard",  # 新增的二级菜单：知识控制台
                "access": "canAccessKnowledgeDashboard"
            },
            {
                "name": "knowledgeQuestionAnswer",  # 新增的二级菜单：知识问答
                "access": "canAccessKnowledgeQuestionAnswer"
            }
        ]
    },
    
    
    {
        "name": "promotBase",  # 新增的一级菜单：提示词管理
        "access": "canAccessPromotBase",
    },
    {
        "name": "mcpSquare",
        "access": "canAccessMCPSquare",
    },
    {
        "name": "FlowManagement",  # 一级菜单：AI内容识别
        "access": "canAccessFlowManagement",
        "routes": [
            {
                "name": "wiseflow",  # 二级菜单：OCR识别
                "access": "canAccessWiseflow"
            },
            {
                "name": "talklist",  # 新增的二级菜单：合规问答
                "access": "canAccessTalklist"
            }
        ]
    },
    {
        "name": "complianceAssistant",  # 新增的一级菜单：合规助手
        "access": "canAccessComplianceAssistant",
        "routes": [
            {
                "name": "metasploitAssistant",  # 新增的二级菜单：metasploit助手
                "access": "canAccessMetasploitAssistant"
            },
            {
                "name": "complianceQA",  # 新增的二级菜单：合规问答
                "access": "canAccessComplianceQA"
            },
            {
                "name": "complianceQA_EU",  # 新增的二级菜单：合规问答欧盟
                "access": "canAccessComplianceQA_EU"
            },
            {
                "name": "caseAnalysis",  # 新增的二级菜单：案例分析
                "access": "canAccessCaseAnalysis"
            },
            {
                "name": "LitigationCase",  # 新增的二级菜单：诉讼案例助手
                "access": "canAccessLitigationCase"
            },
            {
                "name": "informationRetrieval",  # 新增的二级菜单：资讯检索
                "access": "canAccessInformationRetrieval"
            },
            
        ]
    },
    {
        "name": "fileAuditAssistant",  # 一级菜单：文件审核助手
        "access": "canAccessFileAuditAssistant",
        "routes": [
            # {
            #     "name": "fileAudit",  # 二级菜单：文件审核
            #     "access": "canAccessFileAudit"
            # },
            {
                "name": "offlineAuditTaskList",  # 新增的二级菜单：离线审核任务列表
                "access": "canAccessOfflineAuditTaskList"
            },
            {
                "name": "offlineAuditTask",  # 新增的二级菜单：离线审核任务
                "access": "canAccessOfflineAuditTask"
            },
            # {
            #     "name": "ruleManagement",  # 二级菜单：规则管理
            #     "access": "canAccessRuleManagement"
            # },
            # {
            #     "name": "historyInfo",  # 二级菜单：历史信息
            #     "access": "canAccessHistoryInfo"
            # }
        ]
    },
    {
        "name": "aiContentRecognition",  # 一级菜单：AI内容识别
        "access": "canAccessAiContentRecognition",
        "routes": [
            {
                "name": "ocrRecognition",  # 二级菜单：OCR识别
                "access": "canAccessOcrRecognition"
            },
            {
                "name": "layoutRecognition",  # 二级菜单：版面识别
                "access": "canAccessLayoutRecognition"
            },
            {
                "name": "endToEndRecognition",  # 二级菜单：AI端到端识别
                "access": "canAccessEndToEndRecognition"
            },
            {
                "name": "multimodalRecognition",  # 二级菜单：多模态识别
                "access": "canAccessMultimodalRecognition"
            }
        ]
    },

    {
    "name": 'ComplaintQuestionAnswer',
    "access": 'ComplaintQuestionAnswer',
  },
  {
    "name": 'IntelligentReview',
    "access": 'canAccessIntelligentReview',
  },
  {
    "name": 'KnowledgeCenter',
    "access": 'canAccessKnowledgeCenter',
  },
    {
    "name": 'ComplaintAnalysis',
    "access": 'canAccessComplaintAnalysis',
  },
#     {
#         "name": 'ConsumerProtection', # 一级菜单：消保助手
#         "access": 'canAccessConsumerProtection',
#         "routes": [
#         {
#             "name": 'ComplaintQuestionAnswer', # 二级菜单：投诉问答
#             "access": 'ComplaintQuestionAnswer',
#         },
#       {
#         "name": 'IntelligentReview', # 二级菜单：智能审核
#         "access": 'canAccessIntelligentReview',
#       },
#       {
#         "name": 'KnowledgeCenter', # 二级菜单：知识中心
#         "access": 'canAccessKnowledgeCenter',
#       },
#       {
#         "name": 'MaliciousComplaints', # 二级菜单：恶意投诉
#         "access": 'canAccessMaliciousComplaints',
#       },
#       {
#         "name": 'ComplaintAnalysis', # 二级菜单：投诉分析
#         "access": 'canAccessComplaintAnalysis',
#       },
#       {
#         "name": 'ProductMap', # 二级菜单：产品图谱
#         "access": 'canAccessProductMap',
#       },
#       {
#         "name": 'NewConsumerProtectionRule', # 二级菜单：产品图谱
#         "access": 'canAccessNewConsumerProtectionRule',
#       },
#     ],
#   },
    {
        "name": "WebInfoAssistant",  # 一级菜单：web信息助手
        "access": "canAccessWebInfoAssistant",
        "routes": [
            {
                "name": "DiplomaticIntelligentSearch",  # 二级菜单：外交智能检索
                "access": "canAccessDiplomaticIntelligentSearch"
            },
            {
                "name": "FinancialReputationRisk",  # 二级菜单：金融声誉风险助手
                "access": "canAccessFinancialReputationRisk"
            },
            {
                "name": "PolicyTrackingTool",  # 二级菜单：金融声誉风险助手
                "access": "canAccessPolicyTrackingTool"
            },
            {
                "name": "ReputationRiskCase",  # 二级菜单：声誉风险案例助手
                "access": "canAccessReputationRiskCase"
            }
            
        ]
    },
    {
    "name": 'MediaInsightsReport',
    "access": 'canAccessMediaInsightsReport',
    },

    {
        "name": "BusinessOpportunity",  # 一级菜单：商机助手
        "access": "canAccessBusinessOpportunity",
        "routes": [
            {
                "name": "businessOpportunityAssistant",  # 二级菜单：商机助手
                "access": "canAccessBusinessOpportunityAssistant"
            },
            {
                "name": "AssistantSettings",  # 二级菜单：助手设置
                "access": "canAccessAssistantSettings"
            }
        ]
    },
    {
        "name": "BidAssistant",  # 一级菜单：项目投标助手
        "access": "canAccessBidAssistant",
        "routes": [
            {
                "name": "Qualification",  # 二级菜单：资质助手
                "access": "canAccessQualification"
            },
            {
                "name": "ContractCase",  # 二级菜单：合同案例助手
                "access": "canAccessContractCase"
            }
    ],
  },
  {
    "name": 'SolutionAssistant', # 一级菜单：方案助手
    "access": 'canAccessSolutionAssistant',
    "routes": [
      {
        "name": 'SolutionGeneration', # 二级菜单：方案生成
        "access": 'canAccessSolutionGeneration',
      },
      {
        "name": 'HistorySolution', # 二级菜单：历史方案
        "access": 'canAccessHistorySolution',
      },
      {
        "name": 'SolutionRetrieval', # 二级菜单：方案检索
        "access": 'canAccessSolutionRetrieval',
      },
    ],
  },
  {
    "name": 'NegotiationAssistant', # 一级菜单：谈参助手
    "access": 'canAccessNegotiationAssistant',
    "routes": [
      {
        "name": 'DocumentGeneration', # 二级菜单：谈参文档生成
        "access": 'canAccessDocumentGeneration',
      },
      {
        "name": 'StrategyManagement', # 二级菜单：策略管理
        "access": 'canAccessStrategyManagement',
      },
    ],
  },
    {
        "name": "dataAnalysisAssistant",  # 一级菜单：数据分析助手
        "access": "canAccessDataAnalysisAssistant", 
        "routes": [
            {
                "name": "dataExploration",  # 二级菜单：数据探索
                "access": "canAccessDataExploration"
            },
            {
                "name": "statisticalAnalysis",  # 二级菜单：统计分析
                "access": "canAccessStatisticalAnalysis"
            },
            {
                "name": "visualAnalysis",  # 二级菜单：可视化分析
                "access": "canAccessVisualAnalysis"
            }
        ]
    },
     {
        "name": "customerAssistant",  # 一级菜单：客户助手
        "access": "canAccessCustomerAssistant",
        "routes": [
            {
                "name": "customerServiceBot",  # 二级菜单：客服助手
                "access": "canAccessCustomerServiceBot"
            },
            {
                "name": "customerKnowledgeAssistant",  # 二级菜单：客户知识助手
                "access": "canAccessCustomerKnowledgeAssistant"
            },
            {
                "name": "workOrderAssistant",  # 二级菜单：工单助手
                "access": "canAccessWorkOrderAssistant"
            },
            {
                "name": "scriptMining",  # 二级菜单：话术挖掘
                "access": "canAccessScriptMining"
            },
            {
                "name": "ITIncidentReporting",  # 二级菜单：IT事件上报
                "access": "canAccessITIncidentReporting"
            },
            {
                "name": "PublicOpinionClsureReport",  # 二级菜单：舆情结案报告
                "access": "canAccessPublicOpinionClsureReport"
            },
            {
                "name": "PublicOpinionInitialReport",  # 二级菜单：舆情初报
                "access": "canAccessPublicOpinionInitialReport"
            },
        ]
    },
    {
        "name": "marketingAssistant",  # 新增的一级菜单：合规助手
        "access": "canAccessMarketingAssistant",
        "routes": [
            {
                "name": "marketingQA",  # 新增的二级菜单：营销助手
                "access": "canAccessMarketingQA"
            }
        ]
    },
    {
        "name": "IntelligentRetrieval",  # 新增的一级菜单：合规助手
        "access": "canAccessIntelligentRetrieval",
        
    },
    # {
    #     "name": "agentBuilder",  # 新增的一级菜单：智能体构建
    #     "access": "canAccessAgentBuilder",
    #     "routes": [
    #         {
    #             "name": "agentPlayground",  # 新增的二级菜单：技能
    #             "access": "canAccessAgentPlayground"
    #         },
    #         {
    #             "name": "skills",  # 新增的二级菜单：技能
    #             "access": "canAccessSkills"
    #         },
    #         {
    #             "name": "agents",  # 新增的二级菜单：智能体
    #             "access": "canAccessAgents"
    #         },
    #         {
    #             "name": "workflows",  # 新增的二级菜单：工作流
    #             "access": "canAccessWorkflows"
    #         }
    #     ]
    # },
    {
        "name": "voiceAssistant",  # 新增的一级菜单：智能体工作台
        "access": "canAccessVoiceAssistant",
        "routes": [
            {
                "name": "realTimeVoiceRecognition",  # 新增的二级菜单：实时语音识别
                "access": "canAccessRealTimeVoiceRecognition"
            }
        ]
    },
    # {
    #     "name": "smartBIManagement",  # 新增的一级菜单：智能BI管理
    #     "access": "canAccessSmartBIManagement",
    #     "routes": [
    #         {
    #             "name": "biAssistant",  # 新增的二级菜单：BI助理
    #             "access": "canAccessBIAssistant"
    #         },
    #         {
    #             "name": "dataSourceManagement",  # 新增的二级菜单：数据源管理
    #             "access": "canAccessDataSourceManagement"
    #         },
    #         {
    #             "name": "semanticModeling",  # 新增的二级菜单：语义建模
    #             "access": "canAccessSemanticModeling"
    #         },
    #         {
    #             "name": "indicatorManagement",  # 新增的二级菜单：指标管理
    #             "access": "canAccessIndicatorManagement"
    #         },
    #         {
    #             "name": "pluginManagement",  # 新增的二级菜单：插件管理
    #             "access": "canAccessPluginManagement"
    #         }
    #     ]
    # },
    # {
    #     "name": "biWorkspace",  # 新增的一级菜单：BI工作台
    #     "access": "canAccessBIPlayground",
    # },
    {
        "name": "preLoanAssistant",  # 新增的一级菜单：贷前助手
        "access": "canAccessPreLoanAssistant",
        "routes": [
            {
                "name": "customerInvestigation",  # 新增的二级菜单：客户调查
                "access": "canAccessCustomerInvestigation"
            },
            {
                "name": "creditReport",  # 新增的二级菜单：信贷报告
                "access": "canAccessCreditReport"
            },
            {
                "name": "aiReportGeneration",  # 新增的二级菜单：AI报告生成
                "access": "canAccessAiReportGeneration"
            },
            {
                "name": "reportTemplateManagement",  # 新增的二级菜单：报告模板管理
                "access": "canAccessReportTemplateManagement"
            },
            {
                "name": "reportTaskManagement",  # 新增的二级菜单：报告任务管理 
                "access": "canAccessReportTaskManagement"
            },
            {
                "name": "createReportTemplate",  # 新增的二级菜单：创建报告模版
                "access": "canAccessCreateReportTemplate"
            }
        ]
    },
    {
        "name": "midLoanAssistant",  # 一级菜单：贷中助手
        "access": "canAccessMidLoanAssistant",
        "routes": [
            {
                "name": "contractReview",  # 二级菜单：合同审核
                "access": "canAccessContractReview"
            },
            {
                "name": "loanMonitoring",  # 二级菜单：贷中监控
                "access": "canAccessLoanMonitoring"
            }
        ]
    },
    {
        "name": "postLoanAssistant",  # 新增的一级菜单：贷后助手
        "access": "canAccessPostLoanAssistant",
        "routes": [
            {
                "name": "riskMonitoring",  # 新增的二级菜单：风险监控
                "access": "canAccessRiskMonitoring"
            }
        ]
    },   
      {
        "name": "Dataset",
        "access": "canAccessDataset",
        "routes": [
            {
                "name": "structuredData",
                "access": "canAccessStructuredData",
                "routes": [
                    {
                        "name": "newStructuredData",
                        "access": "canCreateStructuredData",
                    },
                    {
                        "name": "viewStructuredData",
                        "access": "canViewStructuredData",
                    },
                ],
            },
            {
                "name": "unstructuredData",
                "access": "canAccessUnstructuredData",
                "routes": [
                    {
                        "name": "newUnstructuredData",
                        "access": "canCreateUnstructuredData",
                    },
                    {
                        "name": "viewUnstructuredData",
                        "access": "canViewUnstructuredData",
                    },
                ],
            },
        ],
    },
    
   
]

 

