#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import schedule
import time
from datetime import datetime, timedelta
from typing import Optional, Dict
import traceback

# 导入金融舆情分析智能体
from financialPublicOpinionReport_weixin_V5_4 import run_agent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s'
)
logger = logging.getLogger(__name__)

def print_banner():
    """打印横幅"""
    banner = """
============================================================
                金融舆情分析智能体 - 定时任务调度
                     版本: V5.4
            启动时间: {}
============================================================
""".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print(banner)

def run_financial_report_agent(days: Optional[int] = 3) -> Dict:
    """
    运行金融舆情分析智能体
    
    Args:
        days (int, optional): 分析的天数范围. 默认为3天.
        
    Returns:
        Dict: 运行结果
    """
    try:
        logger.info("+" + "="*60 + "+")
        logger.info("|" + " "*20 + f"开始分析近 {days} 天的数据" + " "*20 + "|")
        logger.info("+" + "="*60 + "+")
        
        # 计算时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        
        # 格式化时间字符串（仅用于日志显示）
        end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
        start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
        
        logger.info(f"分析时间范围：{start_time_str} 至 {end_time_str}")
        
        # 调用智能体（传入 datetime 对象）
        result = run_agent(start_time, end_time)
        
        if result.get("status") == "success":
            logger.info("[成功] 金融舆情分析报告生成成功")
            logger.info(f"报告存储路径: {result.get('storage_path', '未知')}")
        else:
            logger.error(f"[失败] 金融舆情分析报告生成失败: {result.get('message', '未知错误')}")
            
        return result
        
    except Exception as e:
        error_msg = f"运行金融舆情分析智能体时发生错误: {str(e)}"
        logger.error(f"[错误] {error_msg}")
        logger.error(traceback.format_exc())
        return {"status": "error", "message": error_msg}

def schedule_daily_report():
    """设置每日定时任务"""
    try:
        # 每天下午6点运行
        schedule.every().day.at("18:00").do(run_financial_report_agent)
        logger.info("[设置] 已设置每天下午6点运行金融舆情分析智能体")
        
        # 显示下次运行时间
        next_run = schedule.next_run()
        if next_run:
            time_diff = next_run - datetime.now()
            hours, remainder = divmod(time_diff.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            logger.info("-" * 70)
            logger.info(f"下次运行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"距离下次运行还有: {hours}小时{minutes}分钟")
            logger.info("-" * 70)
        
        # 持续运行调度器
        logger.info("[运行] 调度器已启动，等待执行...")
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
            
    except Exception as e:
        logger.error(f"[错误] 调度器运行出错: {str(e)}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    print_banner()
    logger.info("[启动] 启动金融舆情分析智能体定时任务")
    schedule_daily_report()
