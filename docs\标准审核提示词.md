你是一个自身的技术标准制定专家，有多年经验，精通知识图谱相关技术，现在的任务是对一个标准草案进行审核。

## 核心原则
1.	探索胜于结论
- 不要急于下结论
- 持续探索，直到从问题中自然得出解决方案
- 如果不确定，则提出问题进行确认
- 质疑每一个假设和推论
2.	推理的深度
- 进行广泛的思考，但是要简略的回答（至多于 10,000 字符）
- 以自然的、对话式的内在独白表达思想
- 将复杂的思维分解为简单的、基本的步骤
- 接受不确定性，并不断修订先前的想法
3.	思考过程
- 使用短小、简单的句子来反映自然的思维模式
- 自由表达不确定性和内部辩论
- 展示正在进行的思考过程
- 承认并探索思路的死胡同
- 经常回溯并修订
4.	坚持
- 重视彻底探索，而非快速解决问题


## 审核要求：
- 请结合“意见处理列表”对送审稿全文进行审核，主要审核送审稿中存在的【非编辑性问题】（如技术方法（规则、参数）不合理、逻辑错误、前后矛盾等问题。编辑性问题指：表述、排版、格式不规范、用词不准确不统一、内容无关等。），并提出对应的修改建议
-已经提出的处理意见不要重复

## 修改意见格式要求
1. 以表格格式
2. 表格的标题包括：章条号、修改建议、修改理由、建议单位、建议人、说明


-----

## 标准原文：


XXXX-XX-XX       发 布                                                 XXXX-XX-XX      实  施


国家市场监督管理总局 国家标准化管理委员会



发 布







GB/T XXXXX—XXXX



目    次


前言 	III
引言 	 IV
1   范 围 	 1
2   规范性引用文件 	1
3   术语和定义 	1
4   缩略语 	 2
5   标准知识图谱实现框架 	 人人 . . . . . . . . . . . . . . . .2
6   标准数据预处理 	3
6.1    数据接入 	YX.....................3
6.2    数据清洗 	5
6.3    数据存储 	6
6.4    数据管理 	 !……………………………………………7
7   标准知识图谱构建 	 X…………………………………………………8
7.1   概述 	 M ……………………………………………………8
7.2   SKG本体构建 	 2…………………………………………………… 9
7.3    标准知识获取 	 12
7.4   标准知识融合 	 X………………………………………………………………… 14
7.5    标 准 知 识 存 储 	 A 1…………………………………………………………………15
8   标准知识图谱评估 	AX!             …………………………………………………………………  15
8.1    构建预评估 	 A……………………………………………………………………………… 15
8.2    构建过程评估 	 16
8.3    质 量 评 估 	 ! 4………………………………………………………………………………16
9   标准知识图谱应用	 17
9.1    基础功能 	17
9.2    应用范围 	17
10   标准知识图谱更新 	19
10.1    实体及其属性更新 	19
10.2    关系更新 	20
10.3    持续改进 	21
10.4    版本管理 	21
附录A(资料性) 标准知识图谱构建示例 	 23
A.1   本体构建 	23
A.2   标准知识获取 	26
附录B(资料性)标准知识图谱应用案例 	35

B.1  概述 	 35
B.2  应用案例 	 35
参考文献 	 44







引    言


当前由于标准的数据服务、信息服务仍处于初级阶段，大部分标准均采用纸质或简单电子版形态展 现，机器难以读取、理解与使用，阻碍了标准数字化进程，从而影响智能服务应用技术的发展。标准知 识图谱是标准数字化过程中的关键技术，是使用人工智能算法对标准及其生命周期全过程赋能，对标准 所承载的规则知识建立完整且符合逻辑的关联体系，具备可通过数字设备读取、传输、理解以及使用的 能力。
标准知识图谱采用当前主流的先进知识图谱构建技术，能够对半结构化和非结构化的标准文本数 据进行知识抽取、知识融合和知识表示，构建成为机器可读的形式参与计算，以实现知识检索、知识推 理等功能，提高标准知识高效利用，助力实现标准数字化。
GB/T XXXXX.X《标准知识图谱》拟由2个部分组成。
——第 1 部分：实现指南。目的在于为标准知识图谱构建、评估、应用和更新的实现提供建议。
——第 2 部分：质量评价。目的在于给出标准知识图谱的质量评价方法及评价指标。
本文件的发布机构提请注意,声明符合本文件时,可能涉及到7.2与本体构建、标准知识获取、标准 知识融合和标准知识存储相关的专利的使用。专利号及其专利名称如下：
序号	专利号	专利名称
1	ZL 2021 1 0733216.9	标准知识图谱构建、标准查询方法及装置
2	ZL 2022 1 0148740.4	多层结构标准知识图谱构建、标准检索方法及装置
3	ZL 2021 1 0337471.1	标准知识图谱构建、标准查询方法及装置
4	ZL 2021 1 0337459.0	标准文本标注、标准图谱构建方法及装置
5	ZL 2021 1 0588256.9	标准文本纠错方法、装置、电子设备和存储介质

本文件的发布机构对于该专利的真实性、有效性和范围无任何立场。
该专利持有人已向本文件的发布机构承诺,他愿意同任何申请人在合理且无歧视的条款和条件下。 就专利授权许可进行谈判。该专利持有人的声明已在本文件的发布机构备案。相关信息可以通过以下联 系方式获得：
专利持有人：中国电子技术标准化研究院/北京赛西科技发展有限责任公司
地址：北京东城区安定门东大街1号
请注意除上述专利外,本文件的某些内容仍可能涉及专利。本文件的发布机构不承担识别专利的责 任。



标准知识图谱  第 1 部分：实现指南


1  范围

本文件描述了标准数据预处理、标准知识图谱构建、评估、应用和更新方法，提供了标准知识图谱 应用建议，给出了标准知识图谱构建示例和行业应用参考案例。
本文件适用于标准知识图谱的构建、评估、应用和更新。

2  规范性引用文件

下列文件中的内容通过文中的规范性引用而构成本文件必不可少的条款。其中，注日期的引用文件， 仅该日期对应的版本适用于本文件；不注日期的引用文件，其最新版本（包括所有的修改单）适用于本 文件。
GB/T 1.1—2020  标准化工作导则  第1部分：标准化文件的结构和起草规则 GB/T 31500  信息安全技术  存储介质数据恢复服务要求
GB/T 42093.1  标准文档结构化  元模型  第1部分：全文 GB/T 42131—2022  人工智能  知识图谱技术框架
GB/T 43697  数据安全技术  数据分类分级规则

3  术语和定义

GB/T 42131—2022界定的以及下列术语和定义适用于本文件。

标准要素  standard element
从标准化文件内容划分的功能单元。
注：标准要素包括标准化文件的规范性要素和资料性要素，如封面、目次、前言、引言、范围、规范性引用文件、 核心技术要素等。
标准知识元素  standard knowledge element 从标准化文件中获取的知识元素。
注1：标准知识图谱中的实体、实体类型（概念）、属性、关系、关系类型、事件、规则等统称为标准知识元素。 注2：标准知识元素简称为标准知识。

标准知识图谱  standard-oriented knowledge graph
基于标准化文件中的信息，形成的包含标准要素及其关系的知识图谱。

数据标注  data labelling
给数据样本指定目标变量和赋值的过程。 [来源：GB/T 41867—2020,3.2.29]

数据质量  data quality

在指定条件下使用时，数据的特性满足明确的和隐含的要求的程度。 [来源：GB/T 36073—2018,3.10]

关系抽取  relationship extraction   识别文本中提到的实体之间关系的任务。 [来源：GB/T 41867—2020,3.3.4]

映射  mapping
有向变换的可能实现。
[来源:GB/T 32392.4—2015,3.1.1]

4  缩略语

下列缩略语适用于本文件。
API：应用程序编程接口（Application Programming Interface） FTP：文件传输协议（File Transfer Protocol）
JSON：JS对象简谱（JavaScript Object Notation） NFS：网络文件系统（Network File System）
OFD：开放式版式文档（Open Fixed-layout Document） PDF：可携带文件格式（Portable Document Format）
SFTP：安全文件传输协议（Secure File Transfer Protocol） SKG：标准知识图谱（Standard-oriented Knowledge Graph）  SSL：安全套接层（Secure Sockets Layer）
TLS：传输层安全性（Transport Layer Security）
XML：可拓展标记语言（Extensible Markup Language）

5  标准知识图谱实现框架

SKG标准数据预处理、构建、评估、应用和更新活动的实现框架如图1所示。


图 1 SKG实现框架
SKG相关活动简述如下：
a)   标准数据预处理：通过数据接入、数据清洗、数据存储和数据管理等， 形 成SKG基础数据；
b)SKG    构建：基于SKG基础数据，进行本体构建、标准知识获取、标准知识融合、标准知识存储，
形 成SKG本体模型和SKG数 据 ；
c)   SKG评估：开展构建预评估、构建过程评估和质量评估，对SKG基 础 数 据 、SKG本体模型和SKG 数据的质量进行评估；
d)   SKG应用：通过SKG可视化、检索、推荐、问答、推理等基础功能开展不同领域的标准管理、 研制和实施应用；
e)   SKG更新：基于SKG本体模型图谱数据及应用反馈需求，对SKG的知识和版本进行迭代更新及 持续改进。

6  标准数据预处理

6.1  数据接入
6.1.1  概 述

依据目标使用场景获取源标准数据。标准数据接入需考虑合适的接入格式、接入方式，并按照接入 规则进行接入。在接入规则下，接入格式和接入方式互为关联，共同支撑数据接入过程中的业务活动并 为数据清洗提供保障。
6.1.2  接入格式
标准数据接入格式宜参考表1。
表1  常用标准数据接入格式

数据接入格式	特点	优势	接入对象
OFD	版式固定，扩展性强，安全性高	易于提高数据安全性、可控性、 兼容性，利于存储和读取	已发布的原始标准文件
PDF	布局固定，跨平台兼容性强，可 加密	易于保留数据的文字、字体、图 形、表格等原始格式	已发布的原始标准文件
JPG	直观易读，兼容性强	易于简单化处理复杂格式的数 据，利于数据结构化处理	已发布的原始标准文件

XML	以树状结构组织数据，具有明确 的层次和嵌套关系	易于存储复杂层次结构和属性 类信息数据，利于复杂数据结构 解析	经工具识别、代码转换等方式 处理后的标准文件

JSON	
纯文本格式，结构简单	易于存储简洁的键值对格式类 数据，利于快速实现数据结构化 处理	经工具识别、代码转换等方式 处理后的标准文件

6.1.3  接入方式
标准数据接入方式分为：
——文档传输接入：支持 OFD、PDF 等格式数据接入，接入宜采用的文档传输服务见表 2； 表2  常用文档传输服务
文档传输服务	功能
文档接收FTP服务	支持标准的FTP协议；
支持文档断点续传功能
文档拉取FTP服务	支持文档断点续传功能；
支持文档同步、异步拉取功能
文档NFS服务	支持文档断点续传功能

——API 接入：可用于对 JSON、XML 等格式数据接入；
注：如简单对象访问协议（SOAP）、RESTful等API接入方式。
——数据库接入：与数据提供方的数据库建立连接，接入标准数据。
标准数据接入可在公网或局域网上进行，可根据需要进行网络配置。接入的数据宜按GB/T 42093(所 有部分）、GB/T 37967等元模型构建和结构化处理。
6.1.4  接入规则

*******  安全措施
参考GB/T 39786—2021、GB/T 22239—2019，数据接入宜采用的安全措施包括但不限于： ——数据加密：采用数字签名技术保证数据的完整性、有效性和真实性；
——数据校验：对接入数据的文件名、日期、大小等内容进行校验，保证数据准确性；
——异常数据识别及处理：对接入的异常数据进行识别并通知异常数据提供方重新传入数据； ——传输网络访问权限管理：采用身份验证、网络访问控制等方式接入标准数据；
——数据传输安全协议：采用 SFTP、SSL/TLS 等安全协议接入标准数据；
——访问日志监控：及时告警接入异常、访问异常等非正常情况，保证接入的稳定性。 6.1.4.2  更新方式
宜按以下方式更新标准数据：
——实时更新：标准数据出现修正、被替代、废止时，实时进行数据接入； ——定时更新：在固定时间间隔内（如每天、每周、每月）进行数据接入； ——增量更新：在原有数据基础上只更新上次更新以来发生变化的部分；
——全量更新：在原有数据基础上更新全部标准数据。
  数据清洗
6.2.1  清洗对象
标准数据清洗对象包括：
——标准元数据：参照 GB/T 22373，选取适用的标准文献元数据作为标准元数据（见表 3）； 注：标准内容清洗工作已在标准结构化加工或生成阶段完成，无需再次清洗。
——错误数据：标准在接入、转换、更新过程中产生的错误信息。
6.2.2  清洗原则
为满足SKG的构建、更新和评估需求，标准数据宜按以下原则进行清洗： ——规范性：数据质量及存储标准一致；
——唯一性：标准数据在字段、记录内容、数据集内无重复值；
——完整性：标准数据清洗过程中对缺失和异常数据进行补充和处理，以保证数据完整； ——一致性：清洗过程中，不同数据来源和不同时间节点的标准数据具有一致性。
6.2.3  清洗规则
标准数据清洗规则包括：
a)  标准元数据核检：
1)  元数据非空核检：宜参考表 3，若出现定义的非空字段的值为空时，则进行标记并给出修 改提示；
2)  主键重复清洗：定义“文件编号”作为标准主键字段，文件编号的数据表示见表 3。文件 编号经数据表示校验和归一化处理后，进行文件编号重复校验，记录并标记重复数据，进 行合并、删除处理；
3)  元数据表示核检：包括数据类型、数据表示核检，宜参考表 3。
表3  标准元数据表示


标准元数据	数据类型	数据表示	非空核检说明
文件编号	字符型	文件代号+1个空格+顺序号+1个连字符“- ”+4 位发布年份号	不为空
发布日期	日期型	YYYY-MM-DD	不为空
发布机构	字符型	发布机构的全称	不为空
标准状态	字符型	所著录标准或其发布机构的通报和目录所标明的符号著录标准状态代 码	不为空
实施或试行日期	日期型	YYYY-MM-DD	不为空
中文标准名称	字符型	自由文本(中国标准著录中文标准名称；国外标准著录标准的中文译 名)	不为空
英文标准名称	字符型	自由文本(中国标准著录标准的英文译名；国外标准著录标准的英文 名称)	可为空
被替代标准	字符型	著录被替代标准的标准号，有多个被替代标准时，各标准号之间以半 角分号“ ; ”相隔	可为空
代替标准	字符型	著录代替标准的标准号,有多个代替标准时，各标准号之间以半角分 号“ ; ”相隔	可为空
提出单位	字符型	提出单位的全称	不为空
归口单位	字符型	归口单位的全称	不为空
批准单位	字符型	批准单位的全称	不为空
中国标准分类号	字符型	《中国标准文献分类法》的分类号，有多个分类号时，各分类号之间 以半角分号“; ”相隔	不为空
国际标准分类号	字符型	《国际标准分类法》的分类号，有多个分类号时，各分类号之间以半 角分号“ ; ”相隔	不为空
起草单位	字符型	起草单位的全称，各起草单位之间以半角分号“ ; ”相隔	不为空
主要起草人	字符型	主要起草人姓名全称，各起草人之间以半角符号“ ; ”相隔	不为空
一致性程度	字符型	采用 ISO/IEC 标准的标准号+半角逗号“, ”+一致性程度代码，有多 个采用关系时，各采用关系之间以半角分号“ ; ”相隔	可为空
前言	字符型	自由文本	不为空
引言	字符型	自由文本	可为空
引用标准	字符型	著录被引用标准的标准号，有多个被代替标准时，各标准号之间以半 角分号“ ; ”相隔	可为空
语种	字符型	GB/T 4880.1 和 GB/T 4880.2 规定著录所用语种的代码	可为空
标准类型	字符型	所著录标准的标准化方面类别的代码	可为空

b)  非法值清洗：检查标准数据出现异常的信息，如：包括非法代码、代码与数据标准不一致、取 值错误、格式错误、多余字符、乱码等，根据具体情况进行校核及修正；
c)  记录数核验：检查标准数据清洗前后的记录数是否一致，若不一致，检测是否有处理数据未被 记录。
  数据存储
6.3.1  存储对象
数据预处理阶段中的节点数据需要存储在计算机内部或外部存储介质上，存储对象包括但不限于：
a)  原始数据：6.1 数据接入过程接入的标准数据；

b)  过程数据：数据接入过程和数据清洗过程中产生的数据；
c)  清洗后的数据：按 6.2 清洗后得到的标准数据。
6.3.2  考虑因素
根据组织内部对标准数据管理以及构建SKG的需求，在进行数据存储时宜综合考虑下列因素：
——性能：选择的存储组件能够在给定数据量范围内满足数据管理和构建 SKG 过程中要求的读写 效率；
——可扩展性：选择的存储组件支持水平扩容，能适应数据规模性增长和 SKG 的扩展； ——安全：符合 6.4.3 中的数据安全需求；
——成本：在兼顾性能、可扩展、安全原则下，选择合适的软硬件资源，达到成本最优。 6.3.3  存储组件
宜根据不同数据形式和数据形态，参照表4选择适宜的存储组件。
表4  标准数据形式及存储组件

数据形式	数据形态	存储组件
关系型数据	结构化标准数据	关系型数据库
文档型数据	半结构化标准数据	文档型数据库
多模态数据	标准中提取的图片、表格、公式等	文件系统服务组件
时序型数据	数据预处理过程中的日志数据	缓存存储组件、时序数据库存储组件、 列式存储组件

  数据管理
6.4.1  概述
从SKG构建对标准数据的需求出发，通过对原始数据、过程数据和预处理后的数据实施有效的质量 管理、安全管理和服务管理，实现数据的安全、可访问、可维护、可扩展，为SKG构建与更新提供安全、 可靠的标准数据支撑。
6.4.2  质量管理
数据预处理阶段的质量管理宜包括但不限于：
——人工审核规范性要素，确保标准数据唯一准确，数据条目、字段、属性完整；
——全量审核或抽样审核资料性要素，其中抽样审核需综合考虑随机性、样本量、置信水平和置 信区间、数据的分布特性和异常值处理等因素设计抽样规则；
——检查清洗数据结果的数据完整性及规范性；
——检查标准要素分类、标签定义分级分类，标准元数据设计的需求符合性。
6.4.3  安全管理
数据预处理阶段的安全管理宜包括但不限于：
a)  根据敏感程度、价值、重要性等安全特性对数据进行分级分类、访问授权控制、数据加密、数 据隐私管理，数据分级分类标准遵循 GB/T 43697；

b)  依据组织内硬件资源条件以及数据安全规范，采用适宜的数据备份恢复策略，保证数据安全。 数据备份和恢复规范遵循 GB/T 31500。
6.4.4  服务管理
数据预处理阶段的服务管理宜包括但不限于：
——采用结构化查询语言直接进行标准数据查询、修改以及更新； ——将多种标准领域中的数据进行可视化呈现；
——对标准数据库进行日常管理操作，如更新、备份、恢复等数据管理操作； ——对标准数据库中知识进行外部知识交互及关联；
——溯源预处理后的标准数据，根据保留的过程数据和预处理路径重现标准数据的历史状态和处 理过程。

7  标准知识图谱构建

  概述
在第6章预处理得到的标准数据的基础上，通过图2的构建流程，构建得到能够准确涵盖标准要素及 其关联关系的SKG，构建流程的主要环节所包含内容和关系如下：
a)  SKG 本体构建：基于 SKG 应用方的需求，确立标准知识获取环节的具体抽取规则；
b)  标准知识获取：基于构建完成的SKG 本体，从标准数据中抽取与既定本体相匹配的知识内容， 形成特定领域的SKG；
注：SKG本体构建和知识获取的具体实现方法见附录A。
c)  标准知识融合：对同领域或不同领域知识库中的 SKG进行融合，构建满足应用需求范围的 SKG；
d)  标准知识存储：对构建完成的SKG 进行存储，确立存储内容、存储方式及存储功能。




    预处理标准数据    

本体构建


实体抽取

关系抽取

属性抽取
	标准知识获取


概念层标准知识融合

实例层标准知识融合
	标准知识融合

标准知识存储

输出SKG
图2  SKG 构建流程
  SKG 本体构建
7.2.1  构建原则
SKG 本体构建宜符合以下原则：
——覆盖标准元数据，包括标准的基本信息，如文件编号、发布日期、发布机构等； ——覆盖标准要素，确保准确、全面地抽取实体；
——支持实体类型、属性类型及关系类型的更新，确保模型的灵活性和可维护性；
——满足 SKG 本体模型约束条件的定义，包括约束规则和逻辑，确保数据的一致性和完整性。 7.2.2  构建流程
SKG本体构建流程见图3。




确定现有可复用本体

列举SKG本体核心要素

建立 SKG 实体 类型 层级 体系


修正
定义实体间关系类型

本体模型验证和评价

符合


图3  SKG 本体构建流程
构建流程各环节描述如下：
a)  确定 SKG 本体领域：按国际标准分类法（ICS）和中国标准文献分类法（CCS）确定 SKG 本体所 属领域；
b)  确定现有可复用 SKG 本体：按 GB/T 42131—2022 中 6.3.3.3 列项 b）给出的原则确认可复用 SKG 本体；
c)  列举 SKG 本体核心要素：结合 SKG 的应用需求，宜选取GB/T 1.1—2020 表 3 中适用的标准要 素作为 SKG 本体核心要素并进行列举；
注：对于食品安全、环境保护、工程建设等领域，参照其领域标准化文件的结构和起草规则确定并选取SKG本体核心 要素。
d)  建立 SKG 实体类型层级体系：
1)  按标准的结构划分：将 SKG 本体核心要素定义为 SKG 实体，并按标准要素的构成，定义下 一层级的 SKG 实体，建立 SKG 实体类型层级体系；
2)  自定义：根据应用需求，结合专家意见，建立所需的 SKG 实体类型层级体系。
e)  定义 SKG 实体属性类型：定义描述 SKG 实体特征的元素或数据，以提供关于 SKG 实体的具体 信息或描述，如基本属性、关系属性、描述性属性、时间属性、分类属性；
f)  定义 SKG 实体间关系类型：宜按表 5 定义 SKG 实体间关系类型；
表5  SKG 实体间关系类型


相互关系	SKG实体间 关系类型	说明






标准内部	从属关系	标准的层次结构，例如，标准中的章包含条，条款包含段，段包含列项
	前后关系	标准条款之间存在的逻辑先后顺序，例如“先……后…… ”、“在……之前”、“在……之 后”
	条件关系	标准条款之间存在的条件顺序，例如“如果……则…… ”、“除非……则…… ”
	定义关系	标准中对术语的定义
	范围关系	标准的适用范围，例如“本文件适用于…… ”
	约束关系	标准中对实现或应用标准的行为或结果具有约束作用的要求，例如“……不超过…… ”、 “仅当……时，才…… ”、“若……，按…… ”
	例外关系	标准中可能包含例外条款，允许自特定条件下偏离标准的要求，例如“偏差在…… ”、“在 …… 条件下，允许…… ”、“……除外”
	关联关系	标准中的具有关联性的不同条款或部分



标准之间	等同关系	两个标准在技术内容上完全一致，没有差异。例如“本部分等同采用…… ”
	兼容关系	两个标准在某些方面是兼容的，可以一起使用，但可能存在一些差异
	配套关系	标准和其他标准配套使用
	引用关系	标准中引用其他标准作为其技术内容的一部分，在标准的“规范性引用文件”章节中列出
	代替关系	在标准修订或新标准发布后，新标准代替旧标准，例如“本标准代替…… ”
	修正关系	标准修改单对关联标准内容的特定部分进行修改、增加或删除
标准与其他文 件之间	披露关系	披露标准中涉及的专利
	参考关系	标准资料性引用的文件，以及其他信息资源清单
注：其他未列明的SKG实体间关系类型由用户自行界定。

g)  SKG 本体验证和评价：通过建立评价标准，对 SKG 本体进行验证和评价，如果结果不符合应用 需求，则重新回到 7.2.2 列项 d)进行修正。
7.2.3  构建方式
SKG本体构建宜采用以下3种方式：
a)  自顶向下方式。SKG 本体通常由专业技术人员与标准化专家依据用户需求进行人工构建。从最 顶层开始构建本体,然后细化实体属性类型和实体间关系类型，形成结构良好的 SKG 本体，见 图4；

用户1需求

构建

SKG顶层本体
细化

SKG底层本体


图4  SKG 本体自顶向下方式建模

b)  自底向上方式。对已有的领域专有名词进行归纳整合，定义实体属性类型和实体间关系类型， 形成底层 SKG 本体，明确层级关系，然后逐层向上抽象，构建顶层 SKG 本体。自底而上方式多 用于开放领域 SKG 本体构建，以满足不断增长的概念需求，见图 5；

SKG顶层本体

抽象
SKG底层本体

抽象
SKG实体

归纳

领域专有名词
图5  SKG 本体自底向上方式建模
c)  混合方式。将自顶向下法和自底向上法相结合，双向开展标准知识建模，充分考虑标准核心技 术要素和专业特性，能够在实现过程中对数据源和技术进行优化和调整，沿着最有效的路径发 展，见图 6。

SKG顶层本体
	
SKG底层本体
	
SKG实体
图6  SKG 本体混合方式建模
  标准知识获取
7.3.1  SKG 实体抽取 7.3.1.1  抽取原则
SKG实体抽取原则包括：
——存在性：对于标准必备要素，抽取结果中要存在对应的SKG 实体内容要素；
——准确性：能正确地识别出文本中的实体内容，并将其与正确且相关的SKG 实体标签相关联； ——召回性：能识别出所有 SKG 实体；
——有效性：有高效的性能，处理大规模文本时，能在合理的时间内完成任务；
——泛化性：能识别处理不同的领域、语言、文本风格和格式； ——可扩展性：能够添加、更新实体，以应对不断变化的需求；
——易维护性：面对各要素变化，能够定期更新和维护，保持准确性和相关性。 7.3.1.2  抽取流程
SKG 实体抽取流程如下：

a)  根据 SKG 本体中的SKG 实体类型层级体系构建 SKG 实体标签集合；
b)  在确保标注质量的前提下，对已处理标准数据进行标注；
c)  设计符合具体任务需求的 SKG 实体抽取算法并搭建 SKG 实体抽取模型；
d)  进行实体抽取模型训练；
e)  利用训练完成的SKG 实体抽取模型进行推理，预测 SKG 实体标签结果。 7.3.1.3  抽取方法
SKG 实体抽取方法包括但不限于：
——基于规则的方法：使用手工编写的规则和预定义的模式来匹配 SKG 实体；
——基于机器学习的方法：用包含标准数据和对应的 SKG 实体的数据集来训练模型，识别标准 文本中的特征并进行 SKG 实体抽取，包括传统机器学习、深度学习等方法。
7.3.2  属性抽取
7.3.2.1  抽取原则
属性抽取原则包括：
——SKG 本体完成 SKG 实体抽取后，在 SKG 实体抽取结果的基础上进行属性抽取； ——属性抽取不仅需要抽取属性对应的类型，还需要抽取各属性对应的属性值。
7.3.2.2  抽取流程
属性抽取流程如下：
a)  根据 SKG 本体中的属性分类体系构建属性标签集合，同时需要确定属性值的数据类型；
b)  在确保标注质量的前提下，对已处理标准数据进行标注，在实体标注的基础上对 SKG 实体包含 的属性类型、数值进行标注；
c)  设计符合需求的属性抽取算法并搭建 SKG 实体抽取模型；
d)  给定标准数据以及其对应的 SKG 实体抽取结果，进行属性抽取模型训练；
e)  基于 SKG 实体抽取模型的结果，利用训练完成的属性抽取模型进行推理，得到预测标准的属性 及其属性值。
7.3.2.3  抽取方法
属性抽取方法包括但不限于：
——基于规则的方法，如槽填充算法、文本聚类等方法；
——基于深度学习的方法，如序列标注、提示学习、基于大语言模型的抽取方法等。
7.3.3  关系抽取
7.3.3.1  抽取原则
关系抽取原则包括：
——SKG 本体宜先完成 SKG 实体抽取，在 SKG 实体抽取结果的基础上进行关系抽取；
——对于在同一标准内部定义的SKG 实体间关系，需要考虑标准条款之间的从属关系、条件关 系、关联关系等；
——对于在不同标准之间定义的SKG 实体间关系，需要考虑标准和外部知识（库）之间的兼容关 系、标准和被引用标准之间的引用关系等。

7.3.3.2  抽取流程
关系抽取流程如下：
a)  根据 SKG 本体中的关系分类体系构建关系标签集合；
b)  在确保标注质量的前提下，对已处理标准数据进行关系标注，关系与对应实体的链接标注；
c)  基于 SKG 本体，人工专家分析不同关系类型的上下文特点，根据不同关系类型的上下文，针对 性地设计关系抽取方法；
d)  进行关系抽取模型训练，可以与 SKG 实体抽取模型进行联合训练，也可以在 SKG 实体抽取模 型的基础上进行流水线式训练；
e)  基于 SKG 实体抽取模型的结果，利用训练完成的关系抽取模型进行推理，得到预测标准关系标 签结果。
7.3.3.3  抽取方法
关系抽取方法包括但不限于：
——基于无监督学习的方法：基于依存句法树的结构，定义规则或模式来识别关系； ——基于监督学习的方法：捕捉上下文信息训练神经网络模型来抽取关系；
——基于半监督学习的方法：基于已知的SKG 实体关系作为训练种子，基于自训练或伪标签的半 监督学习框架来预测未标记的 SKG 实体间关系，反复迭代来实现关系抽取；
——基于远程监督的方法：人工标注标准文本中的关系特征，然后基于丰富的人工标记数据来学 习并实现关系抽取。
  标准知识融合
7.4.1  融合流程
7.4.1.1  概念层标准知识融合
概念层的标准知识融合面向本体映射，包括：
a)  确定等效的标准术语等概念或定义；
b)  SKG 本体对齐：对齐不同概念系统中的等效 SKG 本体；
c)  关系对齐：对齐不同概念系统中的等效关系，对关系进行合并；
d)  确定潜在的关系：识别一个/多个标准体系中存在的潜在关系；
e)  SKG 本体属性对齐：对齐不同概念系统中的等效属性，合并本体属性类型和取值空间等。 7.4.1.2  实例层标准知识融合
实例层的标准知识融合面向SKG实体匹配，包括：
a)  识别等效 SKG 实体：识别 SKG 中等效 SKG 实体，并将 SKG 实体归属关系和属性进行合并；
b)  在 SKG 实体之间建立链接：根据融合后概念体系，对融合后的 SKG 实体建立实体间关系链接；
c)  识别等效属性/关系：识别标准术语概念体系中的等效和冗余属性，进行删减和融合；
d)  检查冲突，消除歧义。
7.4.2  融合功能
标准知识融合技术，宜具备以下功能：
——支持使用聚类方法实现标准知识的对齐和合并；
——支持使用领域算法库或规则库实现标准要素匹配、链接、更新；


——支持不同分类领域下多源异构标准数据算法的融合和鲁棒性。比如标准实体对齐、术语消 岐、标准链接等算法；
——支持标准数据存储的可扩展性，满足系统的扩展和增长的需求。
7.5  标准知识存储
7.5.1  存储内容
标准知识存储内容包含但不限于：
——SKG 本体及建模流程输出的内容；
——标准知识获取、标准知识融合输出的内容； ——SKG 版本和时间信息。
7.5.2  存储方式
标准知识存储方式包括不限于：
——基于表结构的存储：利用二维的数据表对SKG中的数据进行存储，如三元组表、类型表、关 系数据库；
——基于图结构的存储：利用图的方式对SKG 中的数据进行存储，如图数据库。
7.5.3  存储功能
采用标准知识存储技术，宜具备以下存储功能：
——支持多种标准数据类型以及多模态数据(如文本、图片、视频以及其它多媒体数据等)的存 储；
——标准数据的存储模型支持与应用程序接口的交互，如常用的JDBC、ODBC等 ；
——支持标准数据存储的一致性，如多个标准数据库对同一SKG实体存储的一致性；
——支持标准数据存储算法的兼容性，如满足结构化标准知识的快速查询、快速读取和快速写入 操作；
——支持存储标准知识的导入导出； ——支持知识存储的备份机制；
——支持对知识存储过程的目志进行记录； ——具备数据安全机制，保障存储安全。

8 标准知识图谱评估

8.1  构建预评估
SKG构建预评估是确保其质量和有效性的重要手段，具体评估内容及评估要点见表6。
表6 构建预评估内容及要点

评估内容	评估要点
需求	按照不同维度明确构建需求，包括：可视化需求、实体之间的关系约定、响应速度要求、数据涵盖 要求，应用需求以及预算支持情况
数据质量	利用专家审核或算法校验标注数据的准确性，采用组内相关系数法评价评估标注数据的一致性； 预处理阶段的数据质量要求参见6.4.2



评估内容	评估要点
技术可行性	评估现有技术是否支持SKG构建；评估技术团队是否具备构建SKG所需的技术能力和经验，以及是否 需要引入外部技术资源或合作伙伴
构建方法	评估采用的人工构建、半自动构建还是自动构建方法的适用性与优缺点
成本	评估构建SKG所需的成本（包括人力成本、时间成本、技术成本），比对分析成本、预算与投入，确 保投入满足SKG构建需要
风险	评估在SKG构建过程中可能遇到的风险和挑战（如数据质量问题、技术难题、项目进度） ，制定风险 应对策略和预案，确保及时有效地应对风险
可持续性	结合SKG的可持续性和可维护性，评估SKG架构是否具有扩展能力


  构建过程评估
SKG 构建过程评估宜符合表 7 的规定。
表7  SKG 构建过程评估内容、要点及方法

评估内容	评估指标	指标说明	计算方法
SKG实体、属  性、关系评估 标准知识融合 评估	精确率	度量模型预测的标签中正确标签的占比	按GB/T 42131—2022中6.3.4.4公式（1）计算。
	召回率	度量模型预测的标签中对正确标签的覆盖程 度	按GB/T 42131—2022中6.3.4.4公式（2）计算。
	F1测量值	综合度量标签预测结果的精确性和完整性	按GB/T 42131—2022中6.3.4.4公式（3）计算。
注1：根据数据标注结果得到SKG实体、属性或关系的真实标签。
注2：根据模型预测结果得到SKG实体、属性或关系的预测标签。


  质量评估
SKG质量评估宜符合表8的规定。
表8  SKG 评估内容、要点及方法

评估内容	评估要点	评估方法
完整性	SKG 涵盖的实体数量和关系数量； SKG更新的及时程度	使用领域SKG本体或其他数据源作为参考对比评估
一致性	SKG 中的实体冗余性；
不同实体类型关系之间的一致性	使用推理机制或规则引擎对SKG进行一致性校验
准确性	SKG 中实体的准确性； 关系的准确性	采用人工与机器相结合的方法进行SKG准确性的评估，包 括使用测试集以及聘请领域专家
覆盖率	实体对标准要素的覆盖度； 关系的覆盖度	覆盖率=已标注的要素数量/标准总要素数量×100%

可读性	可视化呈现；
信息层次与结构； 交互性；
文本描述	
采取测试和反馈来评估SKG的可读性
可扩展性	数据源类型； 数据规模；	自动化工具或人工方法验证






GB/T  XXXXX—XXXX

评估内容	评估要点	评估方法
	系统架构的扩展性； 模型的扩展性	


9  标准知识图谱应用

9.1  基础功能
9.1.1  可 视 化
基 于SKG的可视化展示可包括：
——关系呈现：展示标准分类、标准及其要素之间的关系网络；
——要素展示：展示标准要素的名称及属性； — — 人机交互：提供用户与SKG的交互功能。
9.1.2  检 索
基 于SKG的检索方式可包括：
——关键词检索：通过单个或多个关键词组合条件实现语句级、段落级等多粒度搜索，对文本、 符号、图表等进行检索，快速定位到特定的标准或相关信息；
— — 语义检索：利用SKG的语义关系，适当结合大模型、检索增强生成等技术查找用户输入语句 的相似、相近语义的内容。
9.1.3  推 荐
基 于SKG的推荐方式可包括：
——关联推荐：通过分析SKG实体之间的关系，推荐与查询内容有关联的其他标准或文献，如产 品分类与组成、产业链上下游、流程等关系；
——个性化推荐：根据用户的历史查询和浏览行为，构建用户画像，定向推荐标准和文献。
9.1.4  问 答
基 于SKG的问答方式可包括：
— — 检索式问答：根据提问内容，通过SKG 实体抽取、关系属性识别等步骤将问题转换为可检索 的查询，从而定位到相关标准文档，得到相应答案；
——提问式问答：通过自然语言处理、大模型等技术技术对问题进行解析，结合上下文进行语义 理解，从SKG中整合信息以生成相应答案。
9.1.5  推 理
基 于SKG的推理方式可包括：
——SKG本体推理：通过预先定义的规则和属性执行推理过程，识别并预测概念和类别之间的逻 辑关系；
——关系推理：通过在 SKG中查找实体间的路径来发现其关系，处理SKG实体之间的多跳、比较、 集合操作；
9.2  应 用 范 围

9.2.1  标准管理
SKG在标准管理中的应用可包括：
——体系维护：建立和持续改进企业标准体系，定期组织梳理企业标准、团体标准、地方标准、 行业标准、国家标准、国外标准、国际标准，发布企业标准体系表及重要业务子体系表，建 立统一的企业标准库，在企业内部自上而下推行；
——标准辨识：在标准体系内，明确满足产品、过程、服务质量要求应实施的标准，将标准应用 到工作岗位，促进标准与业务工作深入结合；
——宣贯培训：开展标准和标准化相关培训，不断提高企业员工的标准化意识；
——标准执行：推动标准实施、标准评价，确保标准精准、高效落地，推动标准与日常业务工作 精准对接；
——监督检查：对标准实施评价工作的有效性进行监督检查，将标准实施评价的监督检查融入日 常质量监督、技术监督、安全检查等工作中；
——结果评价：形成与标准实施流程相匹配的实施评价标准，客观评价标准实施工作成效，促进 标准实施与标准评价的相互融合；
——改进提升：建立反馈机制，标准实施过程中发现标准不适用、交叉矛盾以及标准缺失等问题
时能够及时反馈并提出改进提升意见。 SKG 在标准管理中的应用见 B.2.1。
9.2.2  标准制定
SKG在标准制定中的应用可包括：
——标准查询：通过 SKG 的范围查询、模糊查询等索引机制，查询相关标准的编号、名称、发布日 期、实施日期等信息，为新标准立项或已有标准修订提供参考；
——标准对标：通过匹配对标标准、比对指标提取、对标单位换算、限定条件提示等环节的运行输 出比对结果；
——标准查重：基于 SKG，通过知识搜索，对标准间的名称、章节结构、段落语义、技术指标进行 比对，计算标准之间的重复率与相似度；
——标准引用：辅助标准的规范性引用、资料性引用以及参考文献识别与生成；
——专利识别：实现涉及专利的标准内容识别、涉及专利内容的比对分析，以及相似专利的识别； ——标准编制：依据标准起草者对标准内容的规划、设计，结合智能撰写工具、、大模型等辅助功
能，实现包括但不限于标准框架比对、编写规则符合性检查与纠错、标准编写素材推荐，输出
标准内容。
9.2.3  标准实施
9.2.3.1  产品标准化
SKG在产品标准化中的应用可包括：
——产品属性分类：依据标准内容将产品按规格、型号、技术指标等属性进行分类，精准定位各方 需求，辅助达成产品一致性；
——产品合规测评：使用 SKG 检索产品设计标准、技术指标及检测标准，对产品相关指标进行比 对，识别风险、缺陷，确保安全性及合规性要求；
——产品需求分析：通过产品所在产业链数据的挖掘，提供新产品的标准化需求和趋势分析，支持 产品选型和创新决策。
SKG在产品标准化中的应用见B.2.2。

9.2.3.2  过程标准化
SKG在过程标准化中的应用可包括：
——过程可视化与优化：将复杂的行业流程以可视化的方式展示，识别流程中的瓶颈和潜在问题， 对流程进行优化和改进；
——过程自动化与集成化：将 SKG 与业务管理系统等集成，实现流程的自动化执行和监控；
——过程更新与维护：根据业务的发展和变化进行标准数据更新和SKG 优化，支撑行业流程的持续 改进；
SKG在过程标准化中的应用见B.2.3和B.2.4。 9.2.3.3  服务标准化
SKG在服务标准化中的应用可包括：
——服务流程的规范和引导：依据 SKG 数据，对相关产业的服务流程进行标准化设计，明确各环节 的步骤及要求，为服务提供者和消费者提供精准的服务方案指导；
——服务个性化推荐：结合用户的属性、行为、偏好、需求等信息，包括但不限于产业政策、产 业服务需求、标准化历史数据，根据用户的偏好，为用户提供个性化的服务推荐；
——服务质量与用户满意度提升：通过 SKG 推荐、推理相功能，对服务产品和服务质量进行管理和 优化，在确保 SKG 数据准确性的同时，提高服务产品质量和竞争力。
SKG 在服务标准化中的应用见 B.2.5。

10  标准知识图谱更新

  SKG 实体及其属性更新
10.1.1  更新条件
SKG 实体及其属性的更新条件包括但不限于： ——标准状态变化：
•     修正；
注：标准批准发布后，因个别技术内容影响标准使用需要进行修改，或者对原标准内容进行增减时，采用修改单方 式修正，其对应标准化文件为修改单。标准再版时，修改单作为附件一并出版。
•     被替代； •     废止。
——标准元数据变化； ——用户需求变化。
10.1.2  更新流程
SKG 实体及其属性更新流程见图 7。





	合
	

修改实体/属性


更新注释、约束和元 数据


通过

新版本SKG

图7  SKG 实体及其属性更新流程
10.1.3  更新方法
SKG 更新方法包括：
——全量更新：对整个模型进行审查,根据标准变化对有相互关系的实体及其属性进行更新遍 历，按如下步骤更新：
1)  识别需要新增的 SKG 实体、属性；
2)  修改需要调整的 SKG 实体、属性；
3)  删除已废止或不再需要的 SKG 实体、属性；
4)  更新相关注释、约束和元数据；
5)  执行一致性、完整性检查,以确保模型质量。
——差异更新：根据标准知识变化与现有 SKG 的比对，仅更新变化部分，按照以下步骤更新：
1)  与现有 SKG 进行比对,识别差异；
2)  针对差异执行更新操作；
3)  调整受影响部分的分类层次和关联关系并执行一致性检测。
  关系更新
10.2.1  更新条件
关系更新条件包括但不限于： ——SKG 实体变化；
——关系属性变化；
——数据源质量提升；

——SKG 使用反馈。 10.2.2  更新流程
关系更新流程见图 8。





模型识别并修复



通过

新版本SKG

图8  SKG 关系及其属性更新流程
10.2.3  更新方法
关系更新方法包括但不限于：
——自动推理和验证：利用推理引擎来自动检测和修复 SKG 中关系的不一致性或错误。通过与其 他可信数据源进行比对或利用领域知识来验证新关系的准确性；
——基于机器学习的更新：利用机器学习算法（如SKG 实体链接、关系抽取等）来自动抽取新的 SKG 实体和关系。通过训练模型来识别并修复 SKG 中的缺失或错误的关系信息；
——人工编辑：允许用户通过界面提交新的实体、关系或修正错误的信息。建立一个社区编辑平 台，让领域专家或志愿者参与 SKG 的维护和更新；
——接口更新：提供 API 接口，允许合作方或系统集成商通过程序利用 API 接口进行更新。
  持续改进
为确保 SKG 的准确性和完整性能够适应不断变化的数据和需求，宜建立以下持续改进机制： ——定期更新：根据 SKG 实体、关系以及需求的变化，定期更新SKG；
——增量学习：使用增量学习的方法，逐步将新的数据和信息融入到 SKG 中；
——反馈循环：需要建立用户反馈机制，收集用户对 SKG 的反馈和建议，用于改进和优化 SKG； ——自动化更新：利用自动化抽取工具、自动化校验工具实现 SKG 自动化更新和维护。
  版本管理
为保障 SKG 更新过程的稳定性、可追溯性，防范更新风险，宜采用以下版本管理策略：

——采用版本管理工具记录 SKG 的变更历史，为每个版本编写详细更新说明文档（包括版本更新 内容、时间、执行者、变更原因、测试结果信息）。版本管理工具至少具备以下功能：
•     支持多版本管理，允许同时存在多个版本的SKG，以适应不同的需求和场景；
•     支持数据回滚。
——定期备份 SKG 的版本数据，以防止数据丢失或损坏；
——制定规范的版本号命名规则，如“主版本号.次版本号.修订号”。




附 录  A
（资料性）
标准知识图谱构建示例
A.1  本体构建
A.1.1  SKG本体领域
根据7.2.2的SKG本体构建流程，以中国标准文献分类（CCS）为例，确定SKG本体领域，见图A.1。


中国标准文献分类（CCS）

C 医药、卫生、劳动保护


A90/94 社会公 共安全


A14
图书  馆、档 案、文 献与情 报工作


图A.1  SKG 本体领域构建
A.1.2  SKG本体核心要素
根据7.2.2的SKG本体构建流程，列举SKG本体核心要素，见图A.2。

标准文件

规范 性引 用文 件

图A.2  SKG 本体核心要素列举
A.1.3  SKG实体类型层级体系
列举SKG本体核心要素后，以核心要素“封面”为例，逐层划分下级子实体，见图A.3。


标准文件



规范 性引 用文 件


中国 标准 文献 分类 （CCS )号


图A.3  实体类型层级体系
A.1.4  SKG实体属性类型与实体间关系类型
以子实体“文件编号”为例，标注SKG实体属性，见图A.4。







 从属   






图例：
  实体   属性
  关系类型

图A.4  定义 SKG 实体属性类型与SKG 实体间关系类型
A.1.5  SKG本体
经过A.1.2～A.1.4构建过程得到的SKG本体模型见图A.5。







 从属    








图例：
  实体   属性
  关系类型




A.2  标准知识获取
A.2.1  实体抽取
A.2.1.1  实体标注
SKG实体抽取中的部分SKG实体标注示例见图A.6，对下面标准内容进行领域专有名词实体标注，如 标注发动机本体、进气系统、排气系统等词条为飞行器装置部件，推力、转速、耗油率等词条为飞行器 技术要求。

3.3  动力装置 3.3.1  说明
动力装置包括发动机本体、进气系统、排气系统、冷却系统、润滑系统、燃油系统、控制系统、起动系统、螺旋 桨、附件传动装置、发动机安装和固定件、隔振器、助推火箭、整流罩等。
根据飞机的战术技术要求、动力装置的选择和设计应考虑下述因素：推力、转速、耗油率、推重比、重量限制、 寿命、成本、最大迎面尺寸限制、高度速度特性及在最佳设计状态以外的工作特性、全功况工作稳定性、起动性能、 操纵性、发动机在飞机上的安装位置、空间利用率；还应考虑加速装置(如助推火箭)应与发动机工作特性相匹配。



3.3.2  安装
动力装置的安装、固定应符合总体设计要求；为便于检查、维修、安装、拆卸和更换，应具备可达性。 3.3.2.1  发动机安装架
发动机安装架通常应有足够的强度和刚度，排气管喷口应能随热膨胀移动而不改变推力线方向。发动机应易于拆 卸更换。发动机安装架应设有调整发动机在舱内位置的装置，满足设计提出的推力线位置要求；安装架应结构简单， 应能整体装上和卸下。
3.3.2.2  隔振器
发动机的安装架应有隔振器。 3.3.3  发动机舱(涡轮发动机)
发动机舱或整流罩应易于整体卸下，以便于接近或敞开发动机。舱内应通风良好。放气带和高温部件处舱体应设 有进排风口，按不同发动机的要求设置油气分离器和发动机漏油装置的排放口，防止舱内存油积水。发动机工作时舱 体结构温升超过1000 ℃的部分，要采取隔热设计或采用耐热材料。
图A.6  实体标注
A.2.1.2  实体抽取模型
搭建BERT-CRF模型并在已标注的数据集上进行训练，模型部分代码如下所示： import torch
import torch.nn as nn
from transformers import BertModel, BertTokenizer, BertConfig

class BERT_CRF(nn.Module):
def    init   (self, bert_model_name, num_labels): super(BERT_CRF, self).   init   ()
self.num_labels = num_labels
self.bert = BertModel.from_pretrained(bert_model_name) self.dropout = nn.Dropout(0.1)
self.classifier = nn.Linear(self.bert.config.hidden_size, num_labels) self.crf = CRF(num_labels, batch_first=True)

def forward(self, input_ids, attention_mask=None, token_type_ids=None, labels=None):
outputs = self.bert(input_ids, attention_mask=attention_mask, token_type_ids=token_type_ids)
sequence_output = outputs[0]
sequence_output = self.dropout(sequence_output)
logits = self.classifier(sequence_output) if labels is not None:

loss = -self.crf(logits, labels, mask=attention_mask.byte(), reduction='mean')
return loss else:
prediction = self.crf.decode(logits, mask=attention_mask.byte()) return prediction
A.2.1.3  实体抽取结果
经过训练完成的SKG实体抽取模型，对图A.6中标准文本内容进行词条抽取和标签预测，抽取得到的 SKG实体结果见表A.1。
表A.1  实体抽取结果

词条	SKG实体标签	词条	SKG实体标签
发动机本体	飞行器装置部件	进气系统	飞行器装置部件
排气系统	飞行器装置部件	冷却系统	飞行器装置部件
润滑系统	飞行器装置部件	燃油系统	飞行器装置部件
控制系统	飞行器装置部件	起动系统	飞行器装置部件
螺旋桨	飞行器装置部件	隔振器	飞行器装置部件
推力	飞行器技术要求	转速	飞行器技术要求
耗油率	飞行器技术要求	推重比	飞行器技术要求
重量限制	飞行器技术要求	最大迎面	飞行器技术要求
起动性能	飞行器技术要求	空间利用率	飞行器技术要求


A.2.2  关系抽取
A.2.2.1  关系标注
关系抽取中的部分关系标注示例见图A.7，对下面标准内容进行领域专有名词SKG实体之间关系标 注，如标注动力装置与发动机本体、进气系统等实体之间关系为从属关系，舱体结构与隔热设计实体之 间关系为约束关系，发动机与油气分离实体之间为关联关系。

3.3 动力装置 3.3.1 说明
动力装置包括发动机本体、进气系统、排气系统、冷却系统、润滑系统、燃油系统、控制系统、起动系统、螺旋 桨、附件传动装置、发动机安装和固定件、隔振器、助推火箭、整流罩等。
根据飞机的战术技术要求、动力装置的选择和设计应考虑下述因素：推力、转速、耗油率、推重比、重量限制、 寿命、成本、最大迎面尺寸限制、高度速度特性及在最佳设计状态以外的工作特性、全功况工作稳定性、起动性能、 操纵性、发动机在飞机上的安装位置、空间利用率；还应考虑加速装置 (如助推火箭)应与发动机工作特性相匹配。
3.3.2 安装
动力装置的安装、固定应符合总体设计要求；为便于检查、维修、安装、拆卸和更换，应具备可达性。



3.3.2.1 发动机安装架
发动机安装架通常应有足够的强度和刚度，排气管喷口应能随热膨胀移动而不改变推力线方向。发动机应易于拆 卸更换。发动机安装架应设有调整发动机在舱内位置的装置，满足设计提出的推力线位置要求；安装架应结构简单， 应能整体装上和卸下。
3.3.2.2 隔振器
发动机的安装架应有隔振器。 3.3.3 发动机舱(涡轮发动机)
发动机舱或整流罩应易于整体卸下，以便于接近或敞开发动机。舱内应通风良好。放气带和高温部件处舱体应设 有进排风口，按不同发动机的要求设置油气分离器和发动机漏油装置的排放口，防止舱内存油积水。发动机工作时舱 体结构温升超过1000 ℃的部分，要采取隔热设计或采用耐热材料。
图A.7  关系标注
A.2.2.2  关系抽取模型
搭建基于BERT的关系分类模型并在已标注的数据集上进行训练，模型部分代码如下所示： import torch.nn as nn
from transformers import BertModel

class BERTForRelationClassification(nn.Module):
def    init   (self, bert_model_name, num_labels):
super(BERTForRelationClassification, self).   init   ()  self.bert = BertModel.from_pretrained(bert_model_name) self.dropout = nn.Dropout(0.1)
self.classifier = nn.Linear(self.bert.config.hidden_size, num_labels)

def  forward(self,  input_ids,  attention_mask,  token_type_ids,  entity 1 start,
entity 1 end, entity 2 start, entity 2 end):
outputs      =       self.bert(input_ids,      attention_mask=attention_mask,
token_type_ids=token_type_ids)
sequence_output = outputs[0]

entity 1 output	=   sequence_output[torch.arange(sequence_output.size(0)),
entity 1 start]	
entity 2 output	=   sequence_output[torch.arange(sequence_output.size(0)),
entity 2 start]	
combined_output = torch.cat([entity 1 output, entity 2 output], dim=1) combined_output = self.dropout(combined_output)
logits = self.classifier(combined_output) return logits
A.2.2.3  关系抽取结果

经过训练完成的关系分类模型对标准文本数据进行SKG实体抽取和关系标签预测，抽取得到的SKG 实体间关系结果见表A.2：
表A.2  关系抽取结果

实体1	实体2	关系名称	实体1	实体2	关系名称
动力装置	动机本体	包含	技术要求	推力	包含
动力装置	进气系统	包含	技术要求	转速	包含
动力装置	排气系统	包含	技术要求	耗油率	包含
动力装置	冷却系统	包含	技术要求	推重比	包含
动力装置	润滑系统	包含	技术要求	重量限制	包含
动力装置	燃油系统	包含	技术要求	寿命	包含
动力装置	控制系统	包含	技术要求	成本	包含
动力装置	起动系统	包含	技术要求	最大迎面	包含
动力装置	螺旋桨	包含	技术要求	起动性能	包含
动力装置	附件传动装置	包含	技术要求	空间利用率	包含
动力装置	助推火箭	包含	加速装置	发动机	上位词
发动机	安装架	下位词	安装架	隔振器	下位词


A.2.3  属性抽取
A.2.3.1  属性标注
属性抽取中的部分SKG实体属性标注示例见图A.8，对下面标准内容进行领域专有名词SKG实体与属 性标注，如标注发动机安装架的特性属性为足够的强度和刚度，发动机的特性属性为易于拆卸更换，安 装架的特性属性为结构简单。

3.3 动力装置 3.3.1 说明
动力装置包括发动机本体、进气系统、排气系统、冷却系统、润滑系统、燃油系统、控制系统、起动系统、螺旋 桨、附件传动装置、发动机安装和固定件、隔振器、助推火箭、整流罩等。
根据飞机的战术技术要求、动力装置的选择和设计应考虑下述因素：推力、转速、耗油率、推重比、重量限制、 寿命、成本、最大迎面尺寸限制、高度速度特性及在最佳设计状态以外的工作特性、全功况工作稳定性、起动性能、 操纵性、发动机在飞机上的安装位置、空间利用率；还应考虑加速装置(如助推火箭)应与发动机工作特性相匹配。
3.3.2 安装
动力装置的安装、固定应符合总体设计要求；为便于检查、维修、安装、拆卸和更换，应具备可达性。 3.3.2.1 发动机安装架
发动机安装架通常应有足够的强度和刚度，排气管喷口应能随热膨胀移动而不改变推力线方向。发动机应易于拆 卸更换。发动机安装架应设有调整发动机在舱内位置的装置，满足设计提出的推力线位置要求；安装架应结构简单， 应能整体装上和卸下。



3.3.2.2 隔振器
发动机的安装架应有隔振器。 3.3.3 发动机舱(涡轮发动机)
发动机舱或整流罩应易于整体卸下，以便于接近或敞开发动机。舱内应通风良好。放气带和高温部件处舱体应 设有进排风口，按不同发动机的要求设置油气分离器和发动机漏油装置的排放口，防止舱内存油积水。发动机工作 时舱体结构温升超过1000 ℃的部分，要采取隔热设计或采用耐热材料。
图A.8  实体属性标注
A.2.3.2  属性抽取模型
属性抽取与实体抽取均可通过序列标注模型实现。搭建BiLSTM-CRF模型并在已标注的数据集上进行 训练，模型部分代码如下所示：
import torch
import torch.nn as nn import torchcrf
import numpy as np
from torch.utils.data import Dataset, DataLoader import jieba  # 用于中文分词

# 构建词汇表
def build_vocab(texts):
word2idx = {"<PAD>": 0, "<UNK>": 1} idx = 2
for text in texts:
words = jieba.lcut(text) if isinstance(text, str) else text for word in words:
if word not in word2idx: word2idx[word] = idx idx += 1
return word2idx

# 标签到索引的映射（预定义好的标签集合） def build_tag2idx(tags):
tag2idx = {} idx = 0
for tag in tags:
if tag not in tag2idx: tag2idx[tag] = idx idx += 1
return tag2idx

# 自定义数据集类
class NerDataset(Dataset):
def    init   (self, texts, tags, word2idx, tag2idx): self.texts = texts
self.tags = tags
self.word2idx = word2idx self.tag2idx = tag2idx

def    len  (self):
return len(self.texts)

def    getitem   (self, idx): text = self.texts[idx]  tag = self.tags[idx]
words = jieba.lcut(text) if isinstance(text, str) else text text_ids = [self.word2idx.get(word, 1) for word in words]
tag_ids = [self.tag2idx[t] for t in tag]
return torch.tensor(text_ids), torch.tensor(tag_ids)

#构建BiLSTM_CRF序列标注模型  class BiLSTM_CRF(nn.Module):
def    init   (self, vocab_size, embedding_dim, hidden_dim, tagset_size): super(BiLSTM_CRF, self).   init   ()
self.embedding = nn.Embedding(vocab_size, embedding_dim)
self.lstm = nn.LSTM(embedding_dim, hidden_dim // 2, num_layers=1, bidirectional=True)
self.hidden2tag = nn.Linear(hidden_dim, tagset_size)
self.crf = torchcrf.CRF(tagset_size, batch_first=True)

def _get_lstm_features(self, sentence):
embeds = self.embedding(sentence)
lstm_out, _ = self.lstm(embeds)
lstm_feats = self.hidden2tag(lstm_out) return lstm_feats

def forward(self, sentence, tags):
lstm_feats = self._get_lstm_features(sentence) loss = -self.crf(lstm_feats, tags)
return loss

def predict(self, sentence):
lstm_feats = self._get_lstm_features(sentence) best_path = self.crf.decode(lstm_feats)

return best_path

# 训练文本数据
texts = ["发动机安装架通常应有足够的强度和刚度，排气管喷口应能随热膨胀移动而不改变推 力线方向", "安装架应结构简单"]
# 训练标签数据（格式与文本对应，实际需按标注体系来）
tags = [["实体", "属性名称", "属性值"], ["实体", "属性名称", "属性值"]]

# 构建词汇表和标签到索引的映射 word2idx = build_vocab(texts)
tag2idx = build_tag2idx(set([t for tag in tags for t in tag]))

# 创建数据集和数据加载器
dataset = NerDataset(texts, tags, word2idx, tag2idx)
dataloader = DataLoader(dataset, batch_size=1, shuffle=True)

# 定义模型参数
vocab_size = len(word2idx)
embedding_dim = 100 hidden_dim = 128
tagset_size = len(tag2idx)

# 创建模型实例、优化器和定义损失函数
model = BiLSTM_CRF(vocab_size, embedding_dim, hidden_dim, tagset_size) optimizer = torch.optim.Adam(model.parameters(), lr=0.01)

# 训练模型
num_epochs = 10
for epoch in range(num_epochs):
for batch_texts, batch_tags in datataloader:
model.zero_grad()
loss = model(batch_texts, batch_tags) loss.backward()
optimizer.step()

# 测试模型（预测示例）
test_text = "发动机安装架通常应有足够的强度和刚度，排气管喷口应能随热膨胀移动而不改变 推力线方向。发动机应易于拆卸更换。发动机安装架应设有调整发动机在舱内位置的装置，满足设计  提出的推力线位置要求；安装架应结构简单，应能整体装上和卸下。发动机舱或整流罩应易于整体卸  下，以便于接近或敞开发动机。舱内应通风良好。放气带和高温部件处舱体应设有进排风口，按不同  发动机的要求设置油气分离器和发动机漏油装置的排放口，防止舱内存油积水。发动机工作时舱体结  构温升超过1000 ℃的部分，要采取隔热设计或采用耐热材料。"
words = jieba.lcut(test_text)

test_text_ids = [word2idx.get(word, 1) for word in words]
test_text_tensor = torch.tensor(test_text_ids).unsqueeze(0) predicted_tags = model.predict(test_text_tensor)[0]
predicted_tags = [list(tag2idx.keys())[list(tag2idx.values()).index(tag)] for tag in predicted_tags]
print(predicted_tags)
A.2.3.3  属性抽取结果
采用训练完成的实体属性分类模型对标准文本数据进行SKG实体和属性抽取，并进行属性标签预测， 抽取得到的SKG实体属性结果见表A.3：
表A.3  属性抽取结果

SKG实体	属性名称	属性值	SKG实体	属性名称	属性值
发动机安装架	特性	足够强度刚度	排气管喷口	特性	不改变推力线方向
发动机	特性	易于拆卸更换	安装架	特性	结构简单
整流罩	特性	易于整体卸下	发动机舱	特性	易于整体卸
发动机舱	特性	通风良好	舱体结构温度	阈值	1000 ℃
动力装置	特性	可达性	发动机安装架	特性	调整位置





附 录 B
（资料性）
标准知识图谱应用案例
B.1  概述
SKG在标准实施层面基于其可视化、检索、推荐、问答、推理的基础功能，实现在国民经济行业各 门类中的应用。
B.2  应用案例
B.2.1  信通研发SKG在标准管理中的应用
B.2.1.1  场景描述
按照GB/T 4754，信通研发可归属于电力、热力、燃气及水生产和供应业。
研发业务标准实施服务平台构建基于技术标准与研发业务的SKG，实现电网企业信通研发领域技术  标准管理及基于标准数字化的场景应用，包括标准数据管理、SKG管理、研发领域标准管理。标准数据  管理实现技术标准预处理，对标准信息、标准要素、标准分类等标准基础数据进行维护，另外实现信通  研发领域岗位角色、开发运维标准工艺流程和项目管理流程等业务数据维护。SKG管理建立“五维一体” 模型、标准调度运行图，构建信通研发领域SKG。研发领域标准管理实现体系维护、标准辨识、标准执  行、改进提升等标准管理业务。研发业务标准实施服务平台总体架构图见图B.1。

																		
	研发业务标准实施服务平台
	
		
		

	



标 准 知 识 图 谱 模 型
	
标准嵌入





	
			
			
			
			
			

	岗位角色	标准信息	标准要素	
				
																		

图B.1  总体框架
B.2.1.2  标准知识图谱功能
信通研发领域 SKG 在标准管理中可实现：

a)  体系维护：基于信通研发领域业务特点确定标准分类，基于 SKG 中标准分类关系实现体系快速 构建和灵活维护、动态扩展，基于 SKG 中标准题录、状态等信息实现标准体系数据核查和对 比；
b)  标准辨识：基于 SKG 中标准与岗位角色、研发工艺的映射关系，实现基于研发岗位、研发过程 与技术标准的图形化展示和分析，实现标准辨识；
c)  标准执行：基于 SKG，实现标准指标、条款等内容与研发领域流程、输入、输出、工具、模板 的业务融合，通过模型应用将标准嵌入式实施落地到软件的设计、开发、测试、运维各阶段。 例如，标准指标嵌入到代码检查工具，对项目中的代码进行实时检查；
d)  改进提升：在研发过程中，系统用户可在线快速提交标准实施评价意见，并自动维护到 SKG 中 意见实体中，管理人员可在线提查看、处理针对某一标准的具体意见。
B.2.2  增材制造SKG在产品标准化中的应用
B.2.2.1  场景概述
按照GB/T 4754，增材制造可归属于制造业。
增材制造SKG涵盖增材制造基础共性、数据和设计、原材料、工艺、设备、制件、测试方法、质量 评价、服务等标准。通过SKG，将原材料、设备、制件等产品标准分类建立关联，并与基于流程的数据 和设计、工艺、测试方法、质量评价、服务标准建立关联。从而实现以产品标准为中心的可视化展示、 检索、推荐、问答、推理等功能，为产品研制和使用单位、标准制定者、使用者和管理者提供全面、快 捷、高效的标准信息查询与使用服务。
B.2.2.2  SKG 功能
B.2.2.2.1  可视化
通过SKG可视化，将不同标准规定的产品技术要求、检测方法等内容进行对比展示，可用于对同类 产品不同标准的技术指标查询与对比。例如针对增材制造用钛及钛合金粉末，基于图B.2所示的SKG，得 到GB/T 42622—2023《增材制造  激光定向能量沉积用钛及钛合金粉末》与DB32/T 3599—2019《增材 制造  钛合金零件激光选区熔化用粉末通用技术要求》规定的粉末粒度及粒度分布、松装密度要求比对， 见表B.1。







































B.2.2.2.2  检索

通过SKG，针对单个或多个关键词组合条件实现语句级、段落级、篇章级等多粒度搜索，并可实现 基于产品分类、组成、牌号、型号、规格等信息的自动归并和识别。
例如针对增材制造用钛及钛合金粉末，基于如图B.2所示的SKG，当用户检索“增材制造；钛及钛合 金粉末；松装密度”或“增材制造；TC4粉末；松装密度”时，均可检索到和定位至GB/T 42622—2023 《增材制造 激光定向能量沉积用钛及钛合金粉末》的4.3，以及DB32/T 3599—2019《增材制造  钛合 金零件激光选区熔化用粉末通用技术要求》的4.6.1。
B.2.2.2.3  推荐
通过SKG，在检索基础上，为用户推荐不同维度的关联标准，具体包括：
a)  标准引用、代替关联展示：当用户检索到某一标准时，推荐该标准引用与被引用、代替与被代 替的标准；
b)  基于同一产品的关联标准推荐：当用户搜索某一产品标准时，同时推荐该产品的设计标准、试 验标准、工艺标准等；
c)  基于产品分类或组成的关联标准推荐：当用户搜索某一产品标准时，可根据产品分类或组成关 系，推荐该产品的父类（系）或子类（系）产品的相关标准。例如搜索增材制造 TC4 粉末合金 材料时，同时推荐其他钛合金粉末材料标准；
d)  基于产品技术指标的关联标准推荐：当用户搜索某一产品的技术指标要求时，同时推荐该产品 的其他技术指标要求，帮助用户掌握该产品技术指标要求的全面信息。
B.2.2.2.4  问答
通过SKG，用户以问答的形式，直接获得需要的标准知识。针对产品标准的典型问题包括：
a)  “标准化对象”（如增材制造用钛及钛合金粉末）的技术指标有哪些；
b)  标准中（可写明具体标准号）对“标准化对象”（如增材制造用钛及钛合金粉末）的“技术指 标”（如粒度、松装密度）是如何规定的；
c)  标准中（可写明具体标准号）对“标准化对象”（如增材制造用钛及钛合金粉末）的“检验规 则”/“检验项目”/“检验结果判定”是如何规定的；
d)  标准中（可写明具体标准号）对“标准化对象”（如增材制造用钛及钛合金粉末）的“技术指 标”（如粒度、松装密度）的检验方法是如何规定的；
e)  标准中（可写明具体标准号）对“标准化对象”（如增材制造用钛及钛合金粉末）的“包装” /“运输”/“贮存”/“标志”/“随行文件”/“订货单”是如何规定的。
B.2.2.2.5  推理
通过SKG，实现以下推理：
a)  判定“标准化对象”（如增材制造用钛及钛合金粉末）的“技术指标”（如松装密度为 2.0 g/cm3） 是否满足标准（可写明具体标准号）要求；
b)  判断针对“标准化对象”（如增材制造用钛及钛合金粉末）的产品全生命周期过程，是否存在 缺项标准；
c)  当某一标准修订后，判断是否有其他关联标准需要同步修订或修改。 B.2.3  食品安全SKG在过程标准化中的应用
B.2.3.1  场景概述
按照GB/T 4754，食品安全可归属于农、林、牧、渔业。

食品安全SKG涵盖通用标准、产品标准、生产经营过程卫生要求以及检验方法标准四大类型标准。 以食品供应链过程标准化应用为例，将SKG与种植养殖、生产加工、物流运输、零售消费等环节的业务 管理系统进行集成，实现食品供应链过程可视化管理与优化；集成食品工业互联网平台，为食品供应链 全链条风险预测预警、平台不同层级用户推送标准化响应处置预案提供智能化技术支撑。
B.2.3.2  SKG 功能
B.2.3.2.1  可视化
食品安全SKG可视化包括但不限于：
a)  种植养殖环节可视化与优化。将 SKG 与农业管理系统集成，实现动物管理、种植养殖追溯、种 植养殖运营与物联网设备管理规范可视化与优化；
b)  生产加工环节可视化与优化。将 SKG 与食品生产加工管理系统集成，实现生产物料管理、生产 过程控制、包装标签管理、品质监控管理规范可视化与优化，见图 B.3；
c)  物流运输环节可视化与优化。将 SKG 与食品生鲜（冷链）物流系统集成，实现物流、仓储和配 送管理规范可视化与优化；
d)  零售消费环节可视化与优化。将 SKG 与食品零售系统集成，实现食品管理规范可视化与优化。



定义

定义
定义





图B.3  食品安全 SKG 可视化
B.2.3.2.2  检索与推荐
食品安全SKG检索与推荐包括但不限于：
a)  种植养殖检索与推荐。SKG 帮助确定适合当地环境和市场需求的作物品种，为种植养殖周期内 的各项农事活动提供时间表和操作规范；
b)  生产加工检索与推荐。SKG 帮助确定食品的基本成分和安全属性，为生产加工周期内的各项生 产活动提供产品标准、生产经营过程卫生要求以及检验方法操作规范；
c)  物流运输和零售消费检索与推荐。SKG 帮助确定食品物流运输和消费经营过程卫生要求，为物 流和消费周期内各项经营活动提供生产经营过程卫生要求。

B.2.3.2.3  推理与问答
食品安全SKG推理与问答包括但不限于：
a)  食品供应链管理。通过 SKG 推理机制，汇聚、整合、规范食品工业互联网平台不同环节多源异  构数据，提供一种促进食品供应链全程信息共建与共享、数据分发与协同的数据治理解决方案；
b)  食品安全风险预测预警。针对食品供应链上出现的食品安全突发事件或潜在的食品安全问题， 通过 SKG 推理机制，对食品工业互联网平台中的关联食品进行食品安全风险预测预警；
c)  食源性疾病智能问答。基于 SKG，集成对话机器人开源框架构造食源性疾病知识智能问答系统， 实现食品供应链有关食品安全知识答案的自动获取。
B.2.4  中药产业链SKG在过程标准化中的应用
B.2.4.1  场景概述
B.2.4.2.1  可视化


图B.4  中药产业链 SKG 可视化
B.2.4.2.2  检索

通过SKG，支持以搜索关键词/语句的语义搜索、展示用户所有的历史搜索记录的历史搜索，基于复  杂组合条件的搜索。例如，基于图B.4所示的SKG，当用户使用关键词检索“三七；质量要求；种子种苗” 时，均可检索到和定位至GB/T 41624—2022。
B.2.4.2.3  推荐
通过SKG，在检索基础上，为用户推荐不同维度的关联标准，包括但不限于：
a)  用户关注标准：用户通过设置关注主题，为该用户推荐关注标准；
b)  关联展示：当用户检索到某一标准时，推荐该标准引用与被引用、代替与被代替等关联标准；
c)  基于同一中药饮片的关联标准推荐：当用户搜索某一中药饮片标准时，同时推荐该中药饮片相 关联的种苗标准、炮制规范等。
B.2.4.2.4  问答
通过SKG，用户可以多种问答形式获取需要的标准知识。针对中药产业链相关标准的典型问题包括 但不限于：
a)  种子种苗问答。通过 SKG，实现种子种苗检验规则、等级规格、质量要求等相关标准问答；
b)  中药材问答。通过 SKG，实现中药材产地加工技术规范、中药材包装技术规范等相关标准问答；
c)  中药饮片问答。通过 SKG，实现炮制方法、性味归经、用法用量等相关标准问答；
d)  种植、加工、贮藏、销售等多环节问答。通过 SKG，实现中药全产业链相关标准问答。 B.2.4.2.5  推理
通过SKG，可针对中药产业链过程标准实现推理，包括但不限于：
a)  支撑检索、问答功能。通过知识图谱推理，根据用户的检索和问题，从知识图谱中找到相关的 实体和关系，进而给出准确的答案；
b)  中药产品质量追溯、预测预警。针对中药产业链出现的中药质量突发事件或潜在的中药质量问 题，通过 SKG 推理机制，对中药产品进行质量追溯、预测预警。
B.2.5  标准服务SKG在服务标准化中的应用
B.2.5.1  场景概述
按照GB/T 4754，标准服务可归属于科学研究和技术服务业。
以第三方机构向委托方提供标准化服务为例，委托方要求依据组织现有标准情况，以组织某核心关 键产品（如：新能源汽车）为标准化服务对象，通过新能源汽车产业SKG在标准服务过程中的功能应用， 为企业提供标准化服务。
B.2.5.2  标准知识图谱功能
B.2.5.2.1  可视化、检索、推荐与推理
标准化服务SKG通过可视化、检索、推荐与推理的基础功能可实现：
a)  需求分析，服务方宜通过关键词或语义检索，对委托方咨询的标准化服务需求进行知识信息整 合和标准相关性分析，通过关系呈现、要素展示或人机交互方式对分析内容进行可视化展示。 分析的信息宜包括：
1)  产业政策分析，结合企业所处产业环境，对企业关联政策、法规进行检索分析，形成产业 政策、法规清单；
注1：企业政策包括但不限于产业扶持政策、项目申报政策、资金资助政策。

2)  产业链标准分析，服务方通过检索现有产业链标准及相关专利，基于上下文感知、关联推 荐、个性化推荐方式和标准知识推理，生成产业 SKG。新能源汽车产业链标准体系示意图 见图 B.5；

新能源汽车产业链标准


上游


技术 服务


燃料 电池 生产 设备

产业链节点上链标准：
GB/T 30262—2013 空冷式热交换器火用效率评价方法； QC/T 1182—2023 汽车空调铝合金板式换热器；
T/CI   —      毫米小管径管翅式不锈钢换热器；
ASHRAE CH-03-8-3-2003换热器对提高R-744新一代汽车 空调系统性能的贡献(废止)。
图B.5  新能源汽车产业链标准
注2：标准化需求分析包括企业标准概况、企业标准化需求、产业链标准、专利、政策和法规、标准查新、标准比对、 标准服务设计。
b)  标准服务方案设计，服务方结合用户的需求和关注信息数据，检索分析用户已有标准化基础能
力、资源和服务要求，通过标准检索、查新，对标准化需求的合理性进行判断并形成服务方案。 注3：用户标准化基础能力和资源包括但不限于委托方已有标准化基础、标准专家、技术资源。
注4：标准化需求的合理性包括但不限于参与制修订标准所处阶段、牵头制定标准国内外标准的协调性、企业标准体 系建设的全面性。
c)  提供服务，依据标准化服务方案，通过 SKG 的可视化、检索、推荐、推理功能，向用户提供标 准化服务，并对服务质量进行管理和优化。通过 SKG，支持提供的标准化服务可包括：
1)  标准体系构建；
2)  标准查新，通过系统检索 SKG 数据，生成标准查新报告；






--------
## 意见处理列表

条款中，不写保证xx（目的或作用），如*******、6.2.2
第1、第2部分的关系考量里面的第1部分指标是不是和第2部分有关系？如果是有关系的话，是不是要这个标准要考量的？
大模型的应用、构建过程的自动化的前瞻布局
描述再斟酌一下：宜包括但不限于（应才这么写）的表述方式是否合适，确实需要可以 用 应
“宜包括但不限于”描述中，建议改为“包括但不限于”，而“应宜可”应限定在“包括但不限于”下面的具体条款里面。
把标准大模型加进去，给大模型用，也可以解决维护更新迭代的问题，让大家更好理解和应用
GB/T 42093.1与正文的不一致
规范性引用文件/参考文献并没有标上第6章的相关引用文件
各章节中“遵循XXX”标准，这些XXX标准要需要出现在“2 规范性引用文件”或“参考文献”中，但是标准中引用其他标准较多，建议一一核对
“本体”与标准术语等概念有联系，建议本标准可以参考国家标准中已有术语的标准，如软件工程、网络安全等
建议增加“标准数据”、"多模态数据"和“实体抽取”、“属性抽取”、“流程抽取"的定义与说明。
例如：
多模态数据 multi-modal data
多种形态的数据。
注:包含结构化数据(例如业务系统数据等)、半结构化数据(例如XML文件、JSON文件等)和非结构化数据（例如文本、语音和图像视频等)。
实体抽取 属性抽取的术语是否需要给出；多模态以及一些新概念的相关术语
GB/T 41867-2020的年号应为2022
此标准中的数据质量定义来源与GB/T 25000.12-2017,所以此定义的源头应该是GB/T 25000.12-2017,不是GB/T 36073
缩略语中JSON的中文解释建议改为“一种轻量级数据交换格式”
本标准同步有英文版，建议对删除相关安全类的国家标准，如*******的“参考GB/T 39786-2021、22239-2019”等
实现框架的箭头 预处理和数据管理，的指向和文本不对应，比如数据管理是对于预处理的
编号a）b）的指代不清（第五章，应列明编号）
列项d）句末改为“；”
表1 中文字内容建议改为首行空一个汉字（类似问题统一修改）
图1示出了…… 的表述不通顺
梳理明确接入格式的分类（文档格式、语言格式、文档类型混淆）
Json xml是否有国家标准进行规定，如有请引用相关标准
OFD（国内专用，国外用不了）可以和pdf合并为一类
表2中的要求在指南标准中否合适
列项句末改用“；”
接入“宜”采用……的表述是否妥当，文中多处都用了宜，如果都是推荐的，那标准的意义就不大了
GB/T 42093有两个部分，.1和.2，如果两个部分都适用可以写成GB/T 42093(所有部分）或者写明具体编号
接入要求 的 要求 在指南标准中否合适
引导语的列出的标准列入参考文献
签名（保证完整性、有效性 真实性）不能实现加密，传输 存储要不要加密，加密的形式是什么，到底怎么加密，如何加密数据，建议修改
表3 元数据 文件编号只适用于已发布的标准，那对在研、未发布 怎么考虑
标准化对象 是从元数据 还是 实体抽取 进行确定，考虑从元数据就开始定义（不管自上而下或自下而上都要明确）标准的标准化对象，否则不利于标准查重或标准体系构建
表3中归口单位，国行地有明确的规范，但团企、企业不一定规范，是否改为可为空
表3 最后一行 标准类型和类别是什么关系（全文 的类似问题），注意类型和类别用词的准确性，在标准化中类型是功能类型，比如产品标准、技术标准等（根据标准化的起草规则（书籍）或GB/T 20000.1系列来确定）
表3跨页格式不规范，同一表换页时，应重复表的编号+（续），表题（可选）
请核对表1里面发布单位 发布机构 的措辞是否准确
符合6.4.2 应为 6.4.3
表4中的“缓存存储组件”不用居中
预处理 与 数据管理 是什么关系？数据管理是管理预处理数据，但在图1中又划分在预处理章节中
预处理后的数据 在图1中已经超出了预处理的范畴，本身属于预处理（类似6.4的意见）
建议增加对不按GB/T 1.1 规则起草的行业领域标准（比如住建、环保）的说明
概述 能不能改为 流程
a）特定领域的SKG 改为特定标准的SKG？
c）对同领域或不同领域、不同供应商的知识库中 与图2的内容不对应
7.1的c）不同供应商，但7.4的相关内容没有相关内容，本体构建过程中只需要做到统一定义，技术要素能完全对齐，供应商的要求不一定需要。如需要的话，则应做一定的记录
覆盖标准元数据 的表述 和表1的不太对应
属性类型 中的 类型 一词是否规范，请确认（全文类似问题统一处理）
图3没有开始框
7.2.2 a）里面 isc ccs只能确定领域，不能确定范围
列项b) 6.3.3.3列项b）改为6.3.3.3b)
列项c) GB/T1.1不属于规范性引用，不应列入第2章
列项e f格式问题：

列项g)：7.2.2列项d）同样修改

标准知识获取
建议在实体抽取和关系抽取之外，增加实体与关系的联合抽取的相关内容。
实体属性关系中的流程规则建议用流程图形式作为附录或正文给出
大量标准化的文件如何标注，如何保证标准质量
现有内容为针对非结构化文本的抽取方式，对半结构化 结构化3种类型是否应该都给一个解决方案
实体、属性、关系的方法都不一样，可否归纳统一，比如按人工 规则 机器学习 深度学习 大模型的种类次序列出（现在是只挑了几种放进去）
数据标注可以单独拿出来，目前数据标注分解到各个细节上
机器学习、深度、半监督 监督学习是否需要统一表达

列项4的表述不通顺，建议改为“处理大规模文本时，能在合理时间内完成任务”
段末改为句号。
基于远程监督和基于监督学习是否重复
流程抽取没太看懂，现在包含了原则 过程 和方法，像是个思想，写法上和其他不一样，其他图也没有流程抽取（王晓玲有回应，考虑将流程性 过程性作为流程抽取）
融合技术方法 作为附录（未听清）
采用标准知识存储技术，已具备以下功能 是存储功能还是存储技术的功能
SKG构建与评估是确保其质量、性能和有效性的重要手段。这里“性能”在表6中未见相关，建议修改或增加指标。同时建议将“性能”改为“效率”。
表6“”评估“内容里 把“评估”两字都去掉，列出评估对象即可
完整性的评估要点 和 完整性 关联性不大









以下为根据“意见处理列表”对送审稿提出的非编辑性修改建议示例，供参考。表格中“章条号”有的针对具体条文标注，如无法精准对应则采用“全文件”或“相关条款”表述；“建议单位”“建议人”可根据实际情况填写；“说明”栏对修改背景或具体修改思路进行简要说明。

章条号	修改建议	修改理由	建议单位	建议人	说明
*******、6.2.2 等处	删除或调整“保证……”等用于说明“目的或作用”的冗余描述，直接阐明处理措施或结果。	“保证”类表述属于编辑性冗余或目的性描述，易造成歧义；本标准应更加聚焦于可操作的技术要求或规则，以避免“保证某某”之类的字句反复出现。	XX单位	XX人	建议在 *******、6.2.2 及其他出现类似“保证XX”字样处均做统改，删去“保证”或将其改为更直接的可操作性描述，如“应……”“可……”。
第1部分/第2部分	在第1部分概述中，明确与第2部分（质量评价）的关联：第1部分构建的指标或内容可作为第2部分质量评价的输入或参考；若确有参数共享或评价要素，则应在第1部分中进行说明或预留接口。	本标准分为两部分，如第1部分产出的指标、数据与第2部分评价指标有逻辑关联，需要在第1部分进行相应的说明和衔接，避免后续实施和评估时不一致或冲突。	XX单位	XX人	建议在“引言”或“1 范围”中简要说明两部分的衔接方式，并在相关条款中增补必要的接口说明。
全文件（前瞻布局）	在“标准知识图谱构建”及“应用”章节增补对大模型应用及构建过程自动化的前瞻性思考：在数据标注、实体抽取、问答和迭代更新等环节中引入大模型与自动化工具的可行性和潜在价值。	当前知识图谱技术与大模型(LLM)技术融合是趋势，可在本标准中体现前瞻性，引导行业在构建、标注和迭代更新环节中做好与大模型的对接和自动化布局，以便更好地理解与应用。	XX单位	XX人	建议在第 7 章（标准知识图谱构建）或第 9 章（应用）中单列或增补一小节对大模型及自动化场景加以阐述，如“基于大语言模型的自动化实体抽取与属性抽取”“大模型辅助质量评估”等。
全文件（“宜包括但不限于”）	对出现“宜包括但不限于”的条款，若系推荐性要求则改为“包括但不限于”；若涉及到“应”“宜”“可”的强制或推荐用语，则将“包括但不限于”置于具体列项之下，以免混淆。	“宜包括但不限于”混用多重助动词，易造成理解歧义；根据 GB/T 1.1 等标准化规则，推荐或可选用语应严格区分“应”“宜”“可”，表述精炼可读；“包括但不限于”仅表示列举示例的开放性。	XX单位	XX人	建议在通篇排查，对“应”“宜”“可”用语进行统一；凡列举示例时，可直接使用“包括但不限于：……”。对于确需表达推荐性或可选性时，采用“宜”或“可”，不叠加使用“宜包括但不限于”等结构。
全文件（加入“大模型”）	在标准知识图谱构建、维护及应用章节，增加对“以大模型（LLM）作为辅助工具或集成组件”相关内容，并阐述大模型助力SKG自动化更新、推理和问答的可行性与建议做法。	当前大模型在知识抽取、语义关联、意图理解及迭代更新方面具有显著潜力，可与SKG技术互补，在本标准中适度纳入有助于让用户更好理解大模型在标准知识图谱中的角色与应用。	XX单位	XX人	建议在第 7 章（标准知识图谱构建流程）或第 9 章（标准知识图谱应用）增加相关小节，如“与大模型结合的自动化抽取与推理”。
*******、6.4 等处	审核 GB/T 42093.1 与正文内容是否一致。如有不一致之处，应按 GB/T 42093.1（或适用的其他部分/版本）要求进行修改；若本标准需要与 42093.1 保持一致性，则应在文本中保持条款对应或在引用处进一步说明。	送审稿正文中提到 GB/T 42093.1，但内容和该标准不完全对应；为确保技术内容一致，需核实 GB/T 42093 是否为最新版本及是否全套适用（.1/.2），并保持引用内容与原标准条文匹配。	XX单位	XX人	建议在“2 规范性引用文件”或“参考文献”中准确列出 GB/T 42093 的具体部分；如已使用 42093.1 的条款或概念，需要确保文本一致或在备注中注明差异和理由。
第6章、附录	将实际使用到的引用标准（如 GB/T 39786-2021、GB/T 22239-2019 等安全类标准），一并列入“2 规范性引用文件”或“参考文献”，并在正文中保持对应引用标识与引用内容前后呼应。	本标准中多次出现“参考 GB/T XXX”但在“2 规范性引用文件”或“参考文献”并未列出，应统一校核列明；若不属于规范性必须遵循的，可放在“参考文献”，若属于必须执行的标准则列入“规范性引用文件”。	XX单位	XX人	建议在标准正式行文前进行统一的交叉核查，对所有“引用”“遵循”之类说法进行确认，决定其列入“规范性引用文件”或“参考文献”。
7.2（本体概念）	对“本体”概念与国家标准中的相关定义或术语（如软件工程、网络安全等领域已有的本体或模型概念）进行比对，若引用了通用定义或与其存在差异，需在术语和定义章节中加以说明。	“本体”在信息领域已有若干国家标准定义，若本标准需与已有定义保持一致或说明差异，应明示；避免读者对“本体”产生混淆。	XX单位	XX人	可在“3 术语和定义”或附录新增“本体”对应的术语解释，也可在注中说明与 GB/T 42131、GB/T 41867 等国标中的用法关系。
3 术语和定义	建议增加对“标准数据”“多模态数据”“实体抽取”“属性抽取”“流程抽取”等新概念的术语定义。	送审稿正文中频繁使用了“多模态数据”、“实体抽取”、“属性抽取”，但未在术语和定义中给出明确解释；有助于统一本标准技术术语的认知。	XX单位	XX人	可参考“多模态数据”定义：多种形态的数据（结构化、半结构化、非结构化，包括文本、图像、音频、视频等）；“实体抽取”与“属性抽取”可对应 GB/T 41867-2022（原文年号应校正）及其他知识图谱相关标准中的概念。
3.1（数据质量）	将“数据质量（data quality）”的定义来源修改为 GB/T 25000.12-2017，不再标注为 GB/T 36073-2018。	现行稿中“数据质量”定义标注了 GB/T 36073，但送审稿内文所附注释实际应对应 GB/T 25000.12-2017 中对数据质量的表述，更符合本标准所需的概念范围。	XX单位	XX人	建议在术语和定义或参考文献中加以更正，并在注释中表明“源自 GB/T 25000.12-2017 第 X 条”之类说明。
4（缩略语 JSON）	将“JSON：JS 对象简谱（JavaScript Object Notation）”改为“JSON：一种轻量级数据交换格式（JavaScript Object Notation）”。	“JS对象简谱”说法并不常见，容易造成理解不一致；“一种轻量级数据交换格式”更贴近 JSON 在工业及开发实践中的常用定义，且便于读者理解。	XX单位	XX人	建议在第4章“缩略语”或相应术语处直接更正即可。
*******（安全措施）等	若本标准需同步英文版，为避免涉密或复杂安全规范难以对外翻译，可删除或简化对“参考 GB/T 39786-2021、GB/T 22239-2019”等国家安全标准的具体条目引用，仅保留要点概念，并在“参考文献”中列出即可。	国际读者很难获取到此类国内安全标准，且在英文版中若大篇幅引用较为敏感；若希望该标准能在国内外落地，可在正文适度简化，避免冗长安全细节。	XX单位	XX人	若确需保留原文，可将其放在参考文献或附录中，并注明“仅限国内环境下适用”之类说明。
图1/第5章（实现框架）	调整框图箭头及文本表述，使“数据管理”与“预处理”之间的关系在流程图中更加准确，例如将“数据管理”以覆盖或支撑作用表现，而非简单平级箭头。	目前图1描述“SKG实现框架”里，“数据管理”被划入预处理流程，但文字又提到其更全面的管理功能；图与文存在一定不对应，容易造成读者理解偏差。	XX单位	XX人	建议在图1中清晰区分“数据接入—数据清洗—数据存储—数据管理”之间的层次或支撑关系。可在图中使用不同颜色或双箭头体现“管理”的全局性或贯穿性。
第5章（a）(b)等列项	明确使用 a）、b）、c）编号的文字引用关系，避免各小段内容之间的指代不清；必要时可改为“5.1、5.2、5.3”或重新编号，让小节结构更清晰。	送审稿中有些小段采用 a）、b）、c）列项，但在后文中引用时出现“见 a）的内容”不够直观，或出现漏号/乱序。应尽量遵循 GB/T 1.1 的编排规则，保证引用的准确性。	XX单位	XX人	建议统一检查全稿中出现的类似列项，视情况采用 1）、2）、3）或 5.1、5.2 的形式，以减少歧义。
第6章（表1、表2等）	重新梳理“接入格式”“接入方式”的分类；考虑将OFD、PDF等同类文档格式合并表述，并单独说明XML、JSON等结构化交换格式；同时删除或简化部分不适合作为指南标准的文档传输服务细节。	目前表1把PDF、OFD和JPG等归为一类，但XML/JSON属于结构化格式，分类混杂；表2中的“文档接收FTP服务/拉取FTP服务”是技术实现细节，不一定适合出现在“指南标准”。	XX单位	XX人	建议改为：1）文档类格式（OFD/PDF） 2）图像类格式（JPG/PNG） 3）结构化格式（XML/JSON）等。传输服务改为在文字中简要介绍即可，或放在附录示例中。
第6章（表3）	表3中“归口单位”字段建议允许为空；并在“数据类型”或“非空核检说明”中加一条注释，说明团体标准、企业标准或在研标准可能暂时无归口单位的情况。	团体标准、企业标准在立项或刚编制时往往没有所谓的“归口单位”，若在此表中强制要求不为空会导致数据预处理时无法通过。	XX单位	XX人	建议在“非空核检说明”列改为“可为空”；或增加注：对于团体、企业标准，若暂无归口单位信息时可空缺。
第6章（表3末行）	“标准类型”与“标准类别”用词应统一、并与 GB/T 20000.1 等标准化文件对照，明确是“标准的性质”（如基础标准、方法标准等）还是“产品标准/技术标准/管理标准”之类。	本标准中有时使用“标准类型”，有时用“标准类别”，但在标准化通用术语中二者常常概念不同；需遵循已有标准化规则，确保用词准确。	XX单位	XX人	建议引用 GB/T 20000.1、GB/T 1.1 或相关文件中的表述：如“标准类型：产品标准、试验方法标准、术语标准、管理标准”等。
第7章（流程抽取）	建议在“标准知识获取”部分增加“流程抽取”相关内容或单列一节，说明对标准中流程性、过程性要素抽取的方法；如对大规模流程性标准文本的标注、模型训练、抽取准确率控制等。	目前实体抽取、属性抽取、关系抽取都有专节，但流程（过程）抽取仅在少量文字中提及，未形成系统性方法；对于某些行业标准中广泛存在的“过程条款”，需要明确抽取路径与精度要求。	XX单位	XX人	建议在7.3“标准知识获取”下增设“7.3.X 流程（过程）抽取”子条，或在7.3.3后补充一节，从原则、方法、评估三个维度给出流程抽取示例。
第8章（评估内容）	若要评估“性能”，则需在表6、表7或表8中增加相应指标（如时间效率、推理效率等），否则可将“性能”表述为“效率”或删除；或另行增加“构建效率”或“推理效率”等可量化的指标。	送审稿中提到“SKG构建评估是确保质量、性能和有效性的重要手段”，但“性能”在后文评估表中并未有对应指标，需保持一致。	XX单位	XX人	建议增加如“构建时间、推理响应时间”等量化指标；或干脆改“性能”为“效率”更为合适。
第8章（表6“评估内容”）	建议将“需求评估”、“数据质量评估”、“技术可行性评估”等简写为“需求”“数据质量”“技术可行性”，去掉“评估”二字，使表中列项目标与说明相对应；并进一步精简每一要点的文字，控制篇幅。	在同一列中出现“评估内容”与“评估要点”，文字重复；为了保持逻辑一致性，可将“评估内容”简化为“需求、数据质量、技术可行性”等条目名。	XX单位	XX人	建议按照表8的格式，去掉“评估内容”中的“评估”二字，或在标题处合并说明即可。

上述表格中，“章条号”、“建议单位”、“建议人”可根据实际审查情况和组织信息进行替换或填写。
所有修改均聚焦于非编辑性问题（技术逻辑、方法合理性、引用文件一致性、术语定义完整性等），与单纯的格式、排版和用词统一等编辑性问题区分。
如有更多细节或在后续审查中发现新增问题，可在此表格基础上增补行项目。



```
flowchart LR
    A[信息收集Agent] --> B[情感分析Agent]
    B --> C[决策分析Agent]
    C --> D[审计与合规Agent]
    D --> E[报告生成 & 推送]
```