from markitdown import MarkItDown
from typing import List, Dict, Any
import requests
import os
import traceback
import re
from enums import chunk_type
from logging_config import setup_logging, get_logger
from global_interface import chunk, chunk_index
import tiktoken
enc = tiktoken.encoding_for_model(os.getenv("TOKENS_COUNT_MODEL","gpt-4"))

from magic_pdf.data.data_reader_writer import FileBasedDataWriter, FileBasedDataReader
from magic_pdf.data.dataset import PymuDocDataset
from magic_pdf.model.doc_analyze_by_custom_model import doc_analyze
from magic_pdf.config.enums import SupportedPdfParseMethod

from PIL import Image
import pandas as pd
import subprocess
import shutil
# 设置基本的日志配置
setup_logging()
logger = get_logger(__name__)


def file_convert_to_markdown(file_path, file_data):
    
    md = MarkItDown()
    file_type = file_data.get('data_type')
    if file_type == "PDF":
        result = md.convert(file_path)
        return result.text_content
    # elif file_type == "Word":
    elif file_type in ["DOC", "DOCX", "XLS", "XLSX", "CSV"]:
        result = md.convert(file_path)
        return result.text_content
    elif file_type in ["TXT", "MD", "HTML"]:
        result = md.convert(file_path)
        return result.text_content 
    elif file_type in ["JSON", "JSONL"]:
        result = md.convert(file_path)
        return result.text_content
    else:
        raise ValueError("文件类型不支持")


def detect_document_features(text):
    features = {}
    
    # 元数据识别（使用正则表达式）
    features['has_metadata'] = bool(re.search(r'索\s*引\s*号:|主题分类:|办文部门:', text[:500]))
    
    # 章节结构识别
    features['has_chapters'] = bool(re.search(r'第[一二三四五六七八九十]+章\s+', text))
    
    # 条款层级识别
    features['clause_levels'] = len(re.findall(r'第[零一二三四五六七八九十百]+条\s+', text))
    
    # 评分标准识别
    features['has_scoring'] = bool(re.search(r'\d+分\s*（含）', text))
    
    # 条件判断识别
    features['conditional_phrases'] = len(re.findall(r'表示金融租赁公司.*?，.*?存在', text))
    
    return features


def extract_metadata(text):
    # 匹配文头元数据（处理可能的重复字段）
    metadata_pattern = r"""
        索\s*引\s*号[：:]\s*(?P<index_no>[^\n]+)
        .*?
        主\s*题\s*分\s*类[：:]\s*(?P<category>[^\n]+)
        .*?
        办\s*文\s*部\s*门[：:]\s*(?P<department>[^\n]+)
        .*?
        发\s*文\s*日\s*期[：:]\s*(?P<date>[^\n]+)
        .*?
        公\s*文\s*名\s*称[：:]\s*(?P<title>[^\n]+)
        .*?
        文\s*号[：:]\s*(?P<doc_number>[^\n]+)
    """
    
    match = re.search(metadata_pattern, text[:1000], re.DOTALL|re.VERBOSE)
    if not match:
        return None
    
    # 构造结构化元数据
    metadata = {
        'index_no': re.sub(r'\s+', '', match.group('index_no')),  # 清理空格
        'category': match.group('category').split()[0],  # 取首个分类
        'department': match.group('department').strip(),
        'date': match.group('date').replace('\t', ''),
        'title': match.group('title').replace('\n', ''),
        'doc_number': re.sub(r'(金规〔){2}', '金规〔', match.group('doc_number'))  # 修正重复
    }
    
    # 转换为文本块（保留原始格式特征）
    metadata_block = f"""
        索引号: {metadata['index_no']}
        主题分类: {metadata['category']}
        办文部门: {metadata['department']}
        发文日期: {metadata['date']}
        公文名称: {metadata['title']}
        文号: {metadata['doc_number']}
    """.strip()
    
    return metadata_block


def split_long_clause(clause: str, max_len: int) -> List[str]:
    """分割超长条款（在自然段或标点处分割）"""
    sub_blocks = []
    buffer = ""
    
    # 按自然段分割
    paragraphs = re.split(r'(\n\s*\n)', clause)
    for para in paragraphs:
        if len(buffer) + len(para) > max_len:
            # 按标点二次分割
            sentences = re.split(r'([。；])', para)
            for sent in sentences:
                if len(buffer) + len(sent) > max_len:
                    if buffer:
                        sub_blocks.append(buffer)
                        buffer = ""
                buffer += sent
        else:
            buffer += para
    
    if buffer:
        sub_blocks.append(buffer)
    
    return sub_blocks


def intelligent_chunk(text: str, max_length: int = 1000) -> List[str]:
    """智能分块方法（保持结构+语义完整）
    
    Args:
        text: 原始文本内容
        max_length: 最大分块长度（字符数）
        
    Returns:
        分块后的文本列表
    """
    chunks = []
    
    # 阶段1：提取元数据块
    metadata = extract_metadata(text)
    if metadata:
        chunks.append(metadata)
        text = text[len(metadata):]
    
    # 阶段2：结构分割（章-条-自然段）
    structural_blocks = []
    
    # 分割章节（保留标题）
    chapters = re.split(r'(第[一二三四五六七八九十]+章\s+[^\n]+)', text)
    for chap in chapters:
        if not chap.strip():
            continue
        if re.match(r'第[一二三四五六七八九十]+章', chap):
            structural_blocks.append(chap.strip())
        else:
            # 分割条款（保留条款号）
            clauses = re.split(r'(第[零一二三四五六七八九十百]+条\s+)', chap)
            current_clause = ""
            for clause_part in clauses:
                if re.match(r'第[零一二三四五六七八九十百]+条', clause_part):
                    if current_clause:
                        structural_blocks.append(current_clause.strip())
                        current_clause = ""
                    current_clause = clause_part
                else:
                    current_clause += clause_part
            if current_clause:
                structural_blocks.append(current_clause.strip())
    
    # 阶段3：动态合并分块
    final_chunks = []
    current_chunk = ""
    
    for block in structural_blocks:
        # 长度判断（当前块+新块）
        if len(current_chunk) + len(block) + 1 <= max_length:
            current_chunk += "\n" + block if current_chunk else block
        else:
            # 处理超长单个条款
            if len(block) > max_length * 0.8:
                sub_blocks = split_long_clause(block, max_length)
                final_chunks.extend(sub_blocks)
            else:
                if current_chunk:
                    final_chunks.append(current_chunk)
                current_chunk = block
                
    if current_chunk:
        final_chunks.append(current_chunk)
    
    return final_chunks


def preprocess_text(text: str) -> str:
    """文本预处理"""
    # 合并连续空白字符
    text = re.sub(r'\s+', ' ', text)
    # 移除首尾空白
    return text.strip()


def is_list_item(text: str) -> bool:
    """检测列表项（支持多种格式）"""
    return bool(re.match(r'^(\d+[\.、]|[\•▪•·])', text.strip()))


def handle_list_item(item: str, chunks: List[str], current: str, max_len: int) -> tuple:
    """处理列表项分块"""
    if len(current) + len(item) > max_len:
        if current:
            chunks.append(current.strip())
            current = item
        else:
            # 超长列表项强制分割
            chunks.extend([item[i:i+max_len] for i in range(0, len(item), max_len)])
    else:
        current += '\n' + item
    return chunks, current


def merge_short_chunks(chunks: List[str], min_length: int) -> List[str]:
    """合并过短分块"""
    merged = []
    buffer = ""
    for chunk in chunks:
        if len(buffer) + len(chunk) <= min_length*2:
            buffer += " " + chunk
        else:
            if buffer:
                merged.append(buffer.strip())
            buffer = chunk
    if buffer:
        merged.append(buffer.strip())
    return merged


def universal_chunk(text: str, max_length: int = 512, overlap: int = 64) -> List[str]:
    """通用文档分块方法（支持中英文混合）
    
    Args:
        text: 原始文本内容
        max_length: 最大分块长度（字符数）
        overlap: 块间重叠长度（避免切分关键信息）
        
    Returns:
        分块后的文本列表
    """
    # 预处理阶段
    cleaned_text = preprocess_text(text)
    
    # 分阶段分割
    chunks = []
    current_chunk = ""
    
    # 按段落分割（保留自然段落）
    paragraphs = re.split(r'(\n\s*\n)', cleaned_text)
    for para in paragraphs:
        if not para.strip():
            continue
            
        # 处理列表项（如 1. 2. 或 • 等）
        if is_list_item(para):
            chunks, current_chunk = handle_list_item(para, chunks, current_chunk, max_length)
            continue
            
        # 按句子分割（中英文标点兼容）
        sentences = re.split(r'([。！？\.\?!]+\s*)', para)
        sentences = [s for s in sentences if s.strip()]
        
        for i in range(0, len(sentences), 2):
            if i+1 < len(sentences):
                sentence = sentences[i] + sentences[i+1]
            else:
                sentence = sentences[i]
                
            # 动态窗口处理
            if len(current_chunk) + len(sentence) <= max_length - overlap:
                current_chunk += sentence
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = current_chunk[-overlap:] + sentence  # 添加重叠
                else:
                    current_chunk = sentence
                    
    if current_chunk:
        chunks.append(current_chunk.strip())
        
    # 后处理：合并过短分块
    return merge_short_chunks(chunks, min_length=50)

    
def markdown_to_chunk_index(markdown_text):
    """将Markdown文本转换为结构化分块索引"""
    # 检测文档特征
    try:
        features = detect_document_features(markdown_text)
        
        # 根据特征选择分块策略
        if features['has_metadata'] or features['has_chapters'] or features['clause_levels'] > 3:
            chunks = intelligent_chunk(markdown_text)
        else:
            chunks = universal_chunk(markdown_text)
        
        # 构建分块索引
        chunk_objects = []
        # metadata = extract_metadata(markdown_text) or {}
        
        for idx, content in enumerate(chunks):
            # 提取块级元数据
            # section = detect_section(content)
            # clause_level = detect_clause_level(content)
            
            # 构建索引结构
            chunk_index_list = [{
                "index_content": content,
            }]
            # enc.encode(content)
            chunk_object: chunk = {
                "tokens_count": len(enc.encode(content)),
                "answer": content,
                "question": "",
                "chunk_type": chunk_type.BASE,
                "chunk_index":idx+1,
                "chunk_index_list": chunk_index_list
            }
            
            chunk_objects.append(chunk_object)
        
        return chunk_objects
    except Exception as e:
        traceback.print_exc()
        logger.error(f"markdown_to_chunk_index error: {e}")
        return []


############################# 文件处理 #################################
def pdf_processing(pdf_file_path, file_type):
    """process pdf file analysis"""
    
    # generate pdf_file_name and name_without_suff
    pdf_file_name = pdf_file_path.split("/")[-1]
    name_without_suff = pdf_file_name.split(".")[0]
    # suff = pdf_file_name.split(".")[-1]

    # prepare env
    local_image_dir, local_md_dir = f"output/{name_without_suff}" + "_" + file_type + "/images", f"output/{name_without_suff}" + "_" + file_type
    image_dir = str(os.path.basename(local_image_dir))

    os.makedirs(local_image_dir, exist_ok=True)

    image_writer, md_writer = FileBasedDataWriter(local_image_dir), FileBasedDataWriter(local_md_dir)

    # read bytes
    reader1 = FileBasedDataReader("")
    pdf_bytes = reader1.read(pdf_file_path)  # read the pdf content

    # proc
    ## Create Dataset Instance
    ds = PymuDocDataset(pdf_bytes)

    ## inference
    # 获取当前CUDA设备ID
    # device_id = torch.cuda.current_device()
    
    # 初始化PaddleOCR时指定设备
    if ds.classify() == SupportedPdfParseMethod.OCR:
        infer_result = ds.apply(
            doc_analyze, 
            ocr=True,
            # device=f'gpu:{device_id}'  # 添加设备参数
        )

        ## pipeline
        pipe_result = infer_result.pipe_ocr_mode(image_writer)

    else:
        infer_result = ds.apply(doc_analyze, ocr=False)

        ## pipeline
        pipe_result = infer_result.pipe_txt_mode(image_writer)

    ### draw model result on each page
    infer_result.draw_model(os.path.join(local_md_dir, f"{name_without_suff}_model.pdf"))

    ### get model inference result
    model_inference_result = infer_result.get_infer_res()

    ### draw layout result on each page
    pipe_result.draw_layout(os.path.join(local_md_dir, f"{name_without_suff}_layout.pdf"))

    ### draw spans result on each page
    pipe_result.draw_span(os.path.join(local_md_dir, f"{name_without_suff}_spans.pdf"))

    ### get markdown content
    md_content = pipe_result.get_markdown(image_dir)

    ### dump markdown
    pipe_result.dump_md(md_writer, f"{name_without_suff}.md", image_dir)

    ### get content list content
    content_list_content = pipe_result.get_content_list(image_dir)

    ### dump content list
    pipe_result.dump_content_list(md_writer, f"{name_without_suff}_content_list.json", image_dir)

    ### get middle json
    middle_json_content = pipe_result.get_middle_json()

    ### dump middle json
    pipe_result.dump_middle_json(md_writer, f'{name_without_suff}_middle.json')
    
    ### print analysis end
    logger.info(f'file analysis end: {pdf_file_name}')
    
    return md_content


def convert_file_to_pdf(file_path, output_dir):
    """Convert docx and ppt to pdf"""
    try:
        result = subprocess.run(
            ['libreoffice', '--headless', '--convert-to', 'pdf', '--outdir', output_dir, file_path],
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE
            )
        if result.returncode == 0:
            return True
        else:
            return False
    except Exception as e:
        traceback.print_exc()
        return False
    
    
def convert_image_to_pdf(image_path, pdf_path):
    try:
        # 打开图片
        with Image.open(image_path) as img:
            # 检查图片是否有效
            if img is None:
                raise ValueError("无法打开图片")

            # 如果需要，将图片转换为PDF
            img.convert('RGB').save(pdf_path)
            return True

    except Exception as e:
        logger.error(f"处理图片时出错: {e}")
        return False


def convert_xls_to_xslx(xls_file, xlsx_file):
    try:
        # 使用 pandas 读取 .xls 文件
        df = pd.read_excel(xls_file, sheet_name=None)  # sheet_name=None 读取所有的sheet

        # 将数据写入到 .xlsx 文件
        with pd.ExcelWriter(xlsx_file, engine='openpyxl') as writer:
            # 将每个sheet写入到 .xlsx 文件
            for sheet_name, data in df.items():
                data.to_excel(writer, sheet_name=sheet_name, index=False)
        return True
    except Exception as e:
        traceback.print_exc()
        return False


def xlsx_processing(xlsx_file_path, md_path):
    """analysis xlsx by markitdown"""
    try:
        md = MarkItDown()
        result = md.convert(xlsx_file_path)
        md_text = result.text_content
        with open(md_path, "a") as file:
            file.write(md_text)
        return md_text
    except Exception as e:
        logger.err("Failed to pearse to excel:", e)
        return None


def audio_processing(file_path: str) -> tuple[bool, str]:
    """
    处理音频文件并调用语音识别API
    
    Args:
        file_path (str): 音频文件的路径
        
    Returns:
        tuple[bool, str]: (是否成功, 识别文本或错误信息)
    """
    audio_url = "http://localhost:8001/speech-to-text"
    headers = {
        'Accept': 'application/json',
    }
    
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return False, f"文件不存在: {file_path}"
            
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            return False, "文件为空"
            
        # 准备文件
        with open(file_path, 'rb') as f:
            files = {
                'audio_file': (os.path.basename(file_path), f, 'audio/mpeg')
            }
            
            # 发送请求
            response = requests.post(
                audio_url,
                files=files,
                headers=headers,
                timeout=300  # 设置300秒超时
            )
            
            # 检查HTTP状态码
            response.raise_for_status()
            result = response.json()
        
        # 检查结果
        if result.get('status') == 'success':
            text = result.get('text', '')
            logger.info(f"识别成功，文本: {text}")
            return True, text
        else:
            error_msg = result.get('message', '未知错误')
            logger.error(f"识别失败: {error_msg}")
            return False, error_msg
            
    except requests.exceptions.Timeout:
        error_msg = "请求超时"
        logger.error(error_msg)
        return False, error_msg
    except requests.exceptions.RequestException as e:
        error_msg = f"请求异常: {str(e)}"
        logger.error(error_msg)
        return False, error_msg
    except Exception as e:
        error_msg = f"处理异常: {str(e)}"
        logger.error(error_msg)
        return False, error_msg
   
    
def file_analyze_to_markdown(file_path):
    """将文件转换为markdown格式"""
    filename = os.path.basename(file_path)
    print(f"====>filename: {filename}")
    name_without_suff = filename.rsplit('.', 1)[0]
    file_type = filename.rsplit('.', 1)[1].lower()
    result_folder_path = f"output/{name_without_suff}" + "_" + file_type
    os.makedirs(result_folder_path, exist_ok=True)
    
    ### 根据文件类型调用相应的处理函数
    # 处理pdf
    if file_type in ['pdf']:
        markdown_text = pdf_processing(file_path, file_type)
    
    # 处理图片
    elif file_type in ['jpg', 'png', 'jpeg']:
        temp_pdf_file_path = os.path.join(result_folder_path, name_without_suff + '.pdf')
        convert_result = convert_image_to_pdf(file_path, temp_pdf_file_path)
        if convert_result:
            markdown_text = pdf_processing(temp_pdf_file_path, file_type)
        else:
            logger.error(f"Failed to convert image to PDF: {file_path}")
            markdown_text = ""
    
    # 处理word/ppt
    elif file_type in ['doc', 'ppt', 'docx', 'pptx']:
        convert_result = convert_file_to_pdf(file_path, result_folder_path)
        if convert_result:
            temp_pdf_file_path = os.path.join(result_folder_path, name_without_suff + '.pdf')
            markdown_text = pdf_processing(temp_pdf_file_path, file_type)
        else:
            logger.error(f"Failed to convert file to PDF: {file_path}")
            markdown_text = ""

    # 处理excel
    elif file_type in ['xls', 'xlsx']:
        md_path = os.path.join(result_folder_path, name_without_suff + '.md')
        print(f"====>md_path: {md_path}")
        if file_type == 'xls':
            temp_xlsx_file_path = os.path.join(result_folder_path, name_without_suff + '.xlsx')
            convert_result = convert_xls_to_xslx(file_path, temp_xlsx_file_path)
            if convert_result:
                markdown_text = xlsx_processing(temp_xlsx_file_path, md_path)
            else:
                logger.error(f"Failed to convert file to XLSX: {file_path}")
                markdown_text = ""
        else:
            markdown_text = xlsx_processing(file_path, md_path)
    
    # 处理txt、md、yaml、json
    elif file_type in ['txt', 'md', 'yaml', 'json']:
        with open(file_path, "r") as f:
            markdown_text = f.read()
            
    else:
        logger.error(f"Unsupported file type: {file_path}")
        markdown_text = ""

    # 删除output文件夹中的处理结果
    shutil.rmtree(result_folder_path)
    # print("========>markdown_text: ", markdown_text)
    return markdown_text
    
    
if __name__ == "__main__":
    # file_path = "/data/jinxu/RAG/wiseAgent/data/《 人民银行关于规范支付创新业务的通知》.pdf"
    # file_path = "/data/jinxu/RAG/wiseAgent/data/Snipaste_2025-04-17_12-11-07.jpg"
    file_path = "/data/jinxu/RAG/wiseAgent/data/V4打印版-商号&独家参会客户清单明细.xlsx"
    markdown_text = file_analyze_to_markdown(file_path)
    print(f"====>markdown_text: {markdown_text}")