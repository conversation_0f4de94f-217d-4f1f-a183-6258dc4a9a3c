# import httpx
from typing import AsyncGenerator, List, Dict, Any, Optional

from ..engines.rag.flow_langgraph import FlowRAG,FlowRAGforchat2kb
from ..models.system_app_setting import SystemAppSetting
from .auth import verify_token
import httpx
from ..db.mongodb import db
from ..models.llm import LLMModel, Provider
import traceback
import json
from ..models.system_app_setting import KnowledgeQAParams
from ..engines.agent.consumer_protection import ConsumerProtectionAgent
from ..agent.agent_router import robot_message_generator
from ..engines.wisegraph.example import graph_retrieval_for_wiseagent
from ..engines.wisegraph.graph_retriever import neo4j


# 设置日志配置
from .logging_config import setup_logging, get_logger
setup_logging()
logger = get_logger(__name__)

async def get_graph_retrieval_results(query: str, knowledge_base_ids: List[str]):
    """
    处理多个知识库的图检索，参考 parallel_knowledge_search 的循环方式
    """
    if not knowledge_base_ids:
        return None

    all_graph_results = []

    for kb_id in knowledge_base_ids:
        try:
            logger.info(f"执行知识库 {kb_id} 的图检索")
            # 调用单个知识库的图检索
            graph_result = await graph_retrieval_for_wiseagent(neo4j, query, kb_id)

            if graph_result and graph_result.get("success"):
                all_graph_results.append({
                    "kb_id": kb_id,
                    "context": graph_result.get("context", ""),
                    "entities_found": graph_result.get("entities_found", []),
                    "triplets_count": graph_result.get("stats", {}).get("triplets_count", 0),
                    "chunk_ids": graph_result.get("chunk_ids", [])
                })
                logger.info(f"知识库 {kb_id} 图检索成功 - 三元组: {graph_result.get('stats', {}).get('triplets_count', 0)}")
            else:
                logger.warning(f"知识库 {kb_id} 图检索未找到相关内容")

        except Exception as e:
            logger.warning(f"知识库 {kb_id} 图检索失败: {e}")
            # 冷处理：继续处理其他知识库
            continue

    return merge_and_deduplicate_graph_results(all_graph_results)

def merge_and_deduplicate_graph_results(all_graph_results):
    """
    合并多个知识库的图检索结果并去重
    """
    if not all_graph_results:
        return None  # 冷处理：没有结果时返回None

    # 合并所有实体（去重）
    all_entities = list(set(
        entity for result in all_graph_results
        for entity in result["entities_found"]
    ))

    # 合并所有上下文
    all_contexts = [result["context"] for result in all_graph_results if result["context"]]

    # 合并chunk_ids（去重）
    all_chunk_ids = list(set(
        chunk_id for result in all_graph_results
        for chunk_id in result["chunk_ids"]
    ))

    return {
        "merged_context": "\n\n".join(all_contexts),
        "total_entities": all_entities,
        "total_triplets": sum(r["triplets_count"] for r in all_graph_results),
        "total_chunk_ids": all_chunk_ids,
        "kb_count": len(all_graph_results)
    }

def merge_graph_context(original_message: str, graph_results):
    """
    将图检索结果拼接到原始消息中
    """
    if not graph_results:
        return original_message  # 冷处理：没有图检索结果时保持原样

    # 在原始消息前添加图检索上下文
    graph_context_section = f"""
## 知识图谱上下文
{graph_results["merged_context"]}

找到实体：{', '.join(graph_results["total_entities"])}
三元组数量：{graph_results["total_triplets"]}
涉及知识库：{graph_results["kb_count"]}个

---

"""

    return graph_context_section + original_message

def prepare_llm_request(
    messages: List[Dict[str, Any]],
    params: Dict[str, Any],
    provider: str = Provider.OPENAI
) -> tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """
    统一处理 LLM 请求的消息和参数
    
    Args:
        messages: 原始消息列表
        params: 请求参数
        provider: 模型提供商
    
    Returns:
        tuple[处理后的消息列表, 处理后的参数]
    """
    logger.info("=========================处理信息===============================")
    processed_messages = messages
    validated_params = params.copy()
    
    # 新增：移除空的系统消息
    if processed_messages and processed_messages[0]['role'] == 'system' and not processed_messages[0]['content'].strip():
        processed_messages = processed_messages[1:]  # 移除空系统消息
        logger.info(f"[{provider}] 移除了空系统消息")

    # 处理 DeepSeek 特殊要求
    if provider == Provider.DEEPSEEK:
        # 1. 处理消息格式，确保用户和助手消息交替
        if messages:
            processed_messages = [messages[0]]  # 保留第一条消息
            for i in range(1, len(messages)):
                current_msg = messages[i]
                prev_msg = processed_messages[-1]
                if current_msg['role'] == prev_msg['role']:
                    prev_msg['content'] = f"{prev_msg['content']}\n{current_msg['content']}"
                else:
                    processed_messages.append(current_msg)
        
        # 2. 验证和调整参数
        if 'top_p' in validated_params:
            top_p = float(validated_params['top_p'])
            if top_p <= 0 or top_p > 1.0:
                validated_params['top_p'] = 0.9
                logger.warning(f"[DeepSeek] Invalid top_p value: {top_p}, using default value: 0.9")
                
        # 3. 其他 DeepSeek 特定的参数处理
        if 'temperature' in validated_params:
            temp = float(validated_params['temperature'])
            if temp < 0 or temp > 1.0:
                validated_params['temperature'] = 0.7
                logger.warning(f"[DeepSeek] Invalid temperature value: {temp}, using default value: 0.7")
                
        # 4. DeepSeek 的 max_tokens 限制 [1, 8192]
        if 'max_tokens' in validated_params:
            max_tokens = int(validated_params['max_tokens'])
            if max_tokens < 1:
                validated_params['max_tokens'] = 2000
                logger.warning(f"[DeepSeek] Invalid max_tokens value: {max_tokens}, using default value: 2000")
            elif max_tokens > 8192:
                validated_params['max_tokens'] = 8192
                logger.warning(f"[DeepSeek] max_tokens value {max_tokens} exceeds limit, using max value: 8192")
                
    # 处理豆包 API 的特殊要求
    elif provider == Provider.DOUBAO:
        # 1. 验证和调整 top_p
        if 'top_p' in validated_params:
            top_p = float(validated_params['top_p'])
            if top_p <= 0 or top_p > 1.0:
                validated_params['top_p'] = 0.9
                logger.warning(f"[Doubao] Invalid top_p value: {top_p}, using default value: 0.9")
        
        # 2. 验证和调整 temperature
        if 'temperature' in validated_params:
            temp = float(validated_params['temperature'])
            if temp < 0 or temp > 1.0:
                validated_params['temperature'] = 0.7
                logger.warning(f"[Doubao] Invalid temperature value: {temp}, using default value: 0.7")
        
        # 3. 验证和调整 max_tokens（豆包限制为 4096）
        if 'max_tokens' in validated_params:
            max_tokens = int(validated_params['max_tokens'])
            if max_tokens < 1:
                validated_params['max_tokens'] = 2000
                logger.warning(f"[Doubao] Invalid max_tokens value: {max_tokens}, using default value: 2000")
            elif max_tokens > 4096:
                validated_params['max_tokens'] = 4096
                logger.warning(f"[Doubao] max_tokens value {max_tokens} exceeds limit, using max value: 4096")
    
    else:
        # 通用参数验证
        if 'max_tokens' in validated_params:
            max_tokens = int(validated_params['max_tokens'])
            if max_tokens <= 0:
                validated_params['max_tokens'] = 2000
                logger.warning(f"Invalid max_tokens value: {max_tokens}, using default value: 2000")
        
        if 'top_p' in validated_params:
            top_p = float(validated_params['top_p'])
            if top_p <= 0 or top_p > 1.0:
                validated_params['top_p'] = 0.9
                logger.warning(f"Invalid top_p value: {top_p}, using default value: 0.9")
    
    # 移除空值参数
    validated_params = {k: v for k, v in validated_params.items() if v is not None}
    
    return processed_messages, validated_params

async def stream_jiutian_api(
    api_key: str,
    model: str,
    messages: List[Dict[str, str]],
    service_url: str,
    extra: Dict = None,
    system_prompt: str = None,
    provider: str = Provider.JIUTIAN,
    **kwargs
) -> AsyncGenerator[str, None]:
    """
    九天模型的流式访问方法
    """
    logger.info('==>stream_jiutian_api')
    logger.info(f"URL: {service_url}")
    logger.info(f"Model: {model}")
    
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": model,
        "messages": messages,
        "stream": True
    }
    
    if system_prompt:
        messages.insert(0, {"role": "system", "content": system_prompt})
        
    if extra:
        if 'system_prompt' in extra:
            messages[0]['content'] = extra['system_prompt']
        if 'temperature' in extra:
            data['temperature'] = extra['temperature']
        if 'top_p' in extra:
            data['top_p'] = extra['top_p']
        if 'max_tokens' in extra:
            data['max_tokens'] = extra['max_tokens']
    
    # 统一处理消息和参数
    processed_messages, validated_params = prepare_llm_request(messages, data, provider)
    validated_params['messages'] = processed_messages
    validated_params['stream'] = True
            
    logger.info("Request Headers:")
    logger.info(headers)
    logger.info("Request Data:")
    logger.info(validated_params)
    
    try:
        async with httpx.AsyncClient() as client:
            async with client.stream(
                "POST",
                service_url,
                headers=headers,
                json=validated_params,
                timeout=60.0
            ) as response:
                if response.status_code != 200:
                    error_msg = await response.aread()
                    logger.error(f"API Error - Status Code: {response.status_code}")
                    logger.error(f"Response Headers: {response.headers}")
                    logger.error(f"Error Body: {error_msg.decode('utf-8')}")
                    yield json.dumps({"error": f"HTTP error {response.status_code}: {error_msg}"})
                    return

                async for line in response.aiter_lines():
                    if line.strip():
                        if line.startswith("data: "):
                            data = line[6:]  # 去掉 "data: " 前缀
                            if data.strip() == "[DONE]":
                                break
                            try:
                                json_data = json.loads(data)
                                if content := json_data.get("choices", [{}])[0].get("delta", {}).get("content"):
                                    yield json.dumps({"content": content})
                            except json.JSONDecodeError as e:
                                logger.error(f"JSON decode error: {e}")
                                continue
                                
    except Exception as e:
        error_message = f"Stream error: {str(e)}"
        logger.error(error_message)
        yield json.dumps({"error": error_message})

async def stream_openai_api(api_key: str, 
                            model: str, 
                            messages: List[Dict[str, Any]], 
                            url: str,
                            extra: Optional[Dict[str, Any]] = None,
                            system_prompt: Optional[str] = None,
                            provider: str = Provider.OPENAI) -> AsyncGenerator[str, None]:
    logger.debug('==>stream_openai_api')
    logger.debug(f"URL: {url}")
    logger.debug(f"Model: {model}")
    logger.debug(f"Provider: {provider}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    question_url = f'{url}/chat/completions'
   
    
    data = {
        "model": model,
        "messages": messages,
        "stream": True,
        "temperature":0.1
    }
    timeout = 300
    
    if system_prompt:
        data['messages'][0]['content'] = system_prompt
        
    if extra:
        if 'system_prompt' in extra:
            data['messages'][0]['content'] = extra['system_prompt']
        if 'temperature' in extra:
            data['temperature'] = extra['temperature']
        if 'top_p' in extra:
            data['top_p'] = extra['top_p']
        # if 'max_tokens' in extra:
            # data['max_tokens'] = extra['max_tokens']
        if 'timeout' in extra:
            timeout = extra['timeout']
    
    # 统一处理消息和参数
    processed_messages, validated_params = prepare_llm_request(messages, data, provider)
    validated_params['messages'] = processed_messages
            
    logger.debug("=========================请求信息===============================")
    logger.debug("Request Headers:")
    logger.debug(headers)
    logger.debug("Request Data:")
    logger.debug(validated_params)
    logger.debug(f"URL: {question_url}")
    logger.debug("========================================================")
    try:
        async with httpx.AsyncClient() as client:
            async with client.stream(
                'POST', 
                question_url, 
                headers=headers, 
                json=validated_params,
                timeout=timeout # 将超时时间从60秒延长到300秒
            ) as response:
                logger.info(f"Response Status Code: {response.status_code}")
                if response.status_code != 200:
                    error_body = await response.aread()
                    logger.error(f"API Error - Status Code: {response.status_code}")
                    logger.error(f"Response Headers: {response.headers}")
                    logger.error(f"Error Body: {error_body.decode('utf-8')}")
                    yield f"event: error\ndata: API request failed with status code: {error_body.decode('utf-8')}\n\n"
                    return
                # logger.info(f"Response data: {response.json()}")
                async for chunk in response.aiter_bytes():
                    try:
                        decoded = chunk.decode('utf-8')
                        # 使用更可靠的分割方式处理数据块
                        for line in decoded.split('\n\n'):
                            line = line.strip()
                            if not line:
                                continue
                                
                            # 规范SSE格式
                            if line.startswith('data: '):
                                # 直接转发规范格式的数据
                                yield f"{line}\n\n"
                            else:
                                # 处理非标准格式数据
                                if '{' in line:
                                    json_data = line[line.find('{'):]
                                    yield f"data: {json_data}\n\n"
                    
                    except UnicodeDecodeError as e:
                        traceback.print_exc()
                        logger.error(f"Decode Error: {str(e)}")
                        continue
    except httpx.RequestError as e:
        traceback.print_exc()
        error_message = f"请求错误: {str(e)}"
        logger.error(error_message)
        yield f"event: error\ndata: {error_message}\n\n"
    except Exception as e:
        error_message = f"未知错误: {str(e)}"
        logger.error(error_message)
        traceback.print_exc()
        yield f"event: error\ndata: {error_message}\n\n"


async def openai_api(api_key: str, 
                            model: str, 
                            messages: List[Dict[str, Any]], 
                            url: str,
                            extra: Optional[Dict[str, Any]] = None,
                            system_prompt: Optional[str] = None,
                            provider: str = Provider.OPENAI) -> Dict[str, Any]:
    logger.info('==>openai_api')
    logger.info(f"URL: {url}")
    logger.info(f"Model: {model}")
    logger.info(f"Provider: {provider}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": model,
        "messages": messages,
        "stream": False
    }
    
    if system_prompt:
        data['messages'][0]['content'] = system_prompt
        
    if extra:
        # 直接使用已经验证过的参数
        for key in ['temperature', 'top_p', 'max_tokens']:
            if key in extra:
                data[key] = extra[key]
    
    # 在发送请求前进行参数验证
    processed_messages, validated_params = prepare_llm_request(messages, data, provider)
    validated_params['messages'] = processed_messages
                
    # logger.info("Request Headers:")
    # logger.info(headers)
    # logger.info("Request Data:")
    # logger.info(validated_params)
            
    async with httpx.AsyncClient(timeout=httpx.Timeout(60.0)) as client:  # 设置60秒超时
        try:
            response = await client.post(f'{url}/chat/completions', headers=headers, json=validated_params)
            if response.status_code == 200:
                return response.json()
            else:
                error_body = await response.aread()
                logger.error(f"API Error - Status Code: {response.status_code}")
                logger.error(f"Response Headers: {response.headers}")
                logger.error(f"Error Body: {error_body.decode('utf-8')}")
                raise Exception(f"API request failed with status code: {response.status_code}")
        except httpx.ReadTimeout:
            error_message = "请求超时，请稍后重试"
            logger.error(error_message)
            return {"error": error_message}
        except Exception as e:
            traceback.print_exc()
            error_message = f"API请求失败: {str(e)}"
            logger.error(error_message)
            return {"error": error_message}



# async for chunk in stream_custom_api(system_app_info.token_key, chat_id, user_id, messages, system_app_info.server_url):

async def stream_wiserag_api(
    token_key: str,
    chat_id: str,
    messages: List[Dict[str, Any]],
    url: str
) -> AsyncGenerator[str, None]:
    # print('==>stream_custom_api')
    # print(messages)
    headers = {
        "Authorization": f"Bearer {token_key}",
        "Content-Type": "application/json"
    }
    data = {
        "chatId": chat_id,
        "stream": True,
        "detail": True,
        "messages": [messages[-1]]
    }
    logger.info(data)

    async with httpx.AsyncClient() as client:
        async with client.stream('POST', f'{url}/v1/chat/completions', headers=headers, json=data) as response:
            buffer = b""
            async for chunk in response.aiter_bytes():
                buffer += chunk
                try:
                    # 尝试解码累积的数据
                    decoded = buffer.decode('utf-8')
                    yield decoded
                    buffer = b""  # 清空缓冲区
                except UnicodeDecodeError:
                    # 如果解码失败，继续累积数据
                    continue


async def wiserag_api(
    token_key: str,
    chat_id: str,
    messages: List[Dict[str, Any]],
    url: str
) -> Dict[str, Any]:
    headers = {
        "Authorization": f"Bearer {token_key}",
        "Content-Type": "application/json"
    }
    data = {
        "chatId": chat_id,
        "stream": False,
        "detail": True,
        "messages": [messages[-1]]
    }
    logger.info(data)

    async with httpx.AsyncClient(timeout=httpx.Timeout(60.0)) as client:  # 设置60秒超时
        try:
            response = await client.post(f'{url}/v1/chat/completions', headers=headers, json=data)
            logger.info(response.json())
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"API request failed with status code: {response.status_code}")
        except httpx.ReadTimeout:
            error_message = "请求超时，请稍后重试"
            logger.error(error_message)
            return {"error": error_message}
        except Exception as e:
            error_message = f"API请求失败: {str(e)}"
            logger.error(error_message)
            return {"error": error_message}



async def stream_model_api(
    chat_id: str,
    user_id: str,
    messages: List[Dict[str, Any]],
    system_app_info: SystemAppSetting
) -> AsyncGenerator[str, None]:
    """
    统一的模型流式调用接口，支持 WISERAG、LLM 和 KNOWLEDGE_QA
    """
    logger.info('========stream_model_api')
    logger.info(system_app_info)

    if not system_app_info:
        raise ValueError("System app setting not found")
    
    try:
        messages_list = convert_messages(messages)
        system_app_params = system_app_info.get('params', {})
        
        # 处理 prompt template
        if prompt_template := system_app_params.get('prompt_template', None):
            new_content = prompt_template.replace('{user_message}', messages_list[-1]['content'])
            messages_list[-1]['content'] = new_content

        app_type = system_app_info["type"]
        
        if app_type == "WISERAG":
            async for chunk in stream_wiserag_api(
                system_app_params['token_key'], 
                chat_id, 
                messages_list, 
                system_app_params['service_url']
            ):
                yield chunk
                
        elif app_type == "LLM":
            llm: LLMModel = await db["llms"].find_one({"id": int(system_app_params.get('llm_id', None))})
            if not llm:
                raise ValueError("LLM model not found")
            
            logger.info("LLM Model Config:")
            logger.info(messages_list)
            
            model = llm.get('m_name', None)
            provider = llm.get('provider', Provider.OPENAI)
            
            
            # 根据不同的 provider 使用对应的 API 调用方法
            if provider == Provider.DEEPSEEK:
                logger.info('调用 DEEPSEEK')
                stream_api = stream_openai_api
            elif provider == Provider.DOUBAO:
                logger.info('调用 DOUBAO')
                stream_api = stream_openai_api
            elif provider == Provider.LOCAL:
                logger.info('调用 LOCAL')
                stream_api = stream_openai_api
            elif provider == Provider.OLLAMA:
                logger.info('调用 OLLAMA')
                stream_api = stream_openai_api
            elif provider == Provider.JIUTIAN:  # 添加九天模型的处理
                logger.info('调用 JIUTIAN')
                stream_api = stream_jiutian_api
            else:
                logger.info('调用 OpenAI')
                stream_api = stream_openai_api
            
            async for chunk in stream_api(
                llm["api_key"],
                model,
                messages_list,
                llm["service_url"],
                extra=system_app_info.get('extra', None),
                system_prompt=system_app_params.get('system_prompt', None),
                provider=provider
            ):
                yield f"event: answer\n{chunk}"
                
        elif app_type == "KNOWLEDGE_QA":
            llm: LLMModel = await db["llms"].find_one({"id": int(system_app_params.get('llm_id', None))})
            if not llm:
                raise ValueError("LLM model not found")
            
            logger.info('调用 KNOWLEDGE_QA')
            ## 这里需要根据 app_info 获取知识的配置信息
            logger.info(system_app_info.get('params', {}))
            kg_qa_flow_rag = FlowRAG(config=KnowledgeQAParams(**system_app_params),user_id=user_id)
            # 获取用户的最新消息内容
            user_query = messages_list[-1]['content']
            logger.info(f"用户查询: {user_query}")
            
            # 等待异步结果
            kg_qa_results = await kg_qa_flow_rag.run(user_query) # 最后一条信息
            logger.info(f"检索结果: {kg_qa_results}")
            last_message = kg_qa_results['output']
            messages_list[-1]=last_message
            # print(f"messages_list==》{messages_list}")

            model = llm.get('m_name', None)
            provider = llm.get('provider', Provider.OPENAI)


            # 根据不同的 provider 使用对应的 API 调用方法
            if provider == Provider.DEEPSEEK:
                logger.info('调用 DEEPSEEK')
                stream_api = stream_openai_api
            elif provider == Provider.DOUBAO:
                logger.info('调用 DOUBAO')
                stream_api = stream_openai_api
            elif provider == Provider.LOCAL:
                logger.info('调用 LOCAL')
                stream_api = stream_openai_api
            elif provider == Provider.OLLAMA:
                logger.info('调用 OLLAMA')
                stream_api = stream_openai_api
            elif provider == Provider.JIUTIAN:  # 添加九天模型的处理
                logger.info('调用 JIUTIAN')
                stream_api = stream_jiutian_api
            else:
                logger.info('调用 OpenAI')
                stream_api = stream_openai_api

            yield 'event: moduleStatus\ndata: {"status":"running","name":"Base RAG Agent"}\n\n'

            quoteList = {
                "moduleName": "AI 对话",
                "moduleType": "chatNode",
                "model": model,
                "query": user_query,
                "quoteList": [
                    getattr(one, 'additional_kwargs', {}) 
                    for one in kg_qa_results.get('rerank_results', []) 
                    if hasattr(one, 'additional_kwargs')
                ],
            }
            yield f'event: appStreamResponse\ndata: [{json.dumps(quoteList, ensure_ascii=False)}]\n\n'


            # event: moduleStatus
            # data: {"status":"running","name":"涉黄涉政识别"}
            
            async for chunk in stream_api(
                llm["api_key"],
                model,
                messages_list,
                llm["service_url"],
                extra=system_app_info.get('extra', None),
                system_prompt=system_app_params.get('system_prompt', None),
                provider=provider
            ):
                yield f"event: answer\n{chunk}"


        elif app_type == "KNOWLEDGE_QA_V2":
            llm: LLMModel = await db["llms"].find_one({"id": int(system_app_params.get('llm_id', None))})
            if not llm:
                raise ValueError("LLM model not found")
            
            logger.info('调用 KNOWLEDGE_QA_V2')
            ## 这里需要根据 app_info 获取知识的配置信息
            logger.info(system_app_info.get('params', {}))
            kg_qa_flow_rag = FlowRAGforchat2kb(config=KnowledgeQAParams(**system_app_params),user_id=user_id)
            # 获取用户的最新消息内容
            user_query = messages_list[-1]['content']
            logger.info(f"用户查询: {user_query}")
            
            # 等待异步结果
            kg_qa_results = await kg_qa_flow_rag.run(user_query) # 最后一条信息
            logger.info(f"检索结果: {kg_qa_results}")
            last_message = kg_qa_results['output']
            context_strs = last_message['context']

            # 630行左右：调用图检索并合并结果
            try:
                knowledge_base_ids = system_app_params.get("knowledge_base_ids", [])
                logger.info(f"开始图检索，知识库IDs: {knowledge_base_ids}")
                graph_results = await get_graph_retrieval_results(user_query, knowledge_base_ids)

                if graph_results:
                    logger.info(f"图检索成功 - 实体: {len(graph_results['total_entities'])}, 三元组: {graph_results['total_triplets']}")
                    # 将图检索结果合并到消息中
                    enhanced_content = merge_graph_context(last_message['content'], graph_results)
                    last_message['content'] = enhanced_content
                    logger.info(f"已将图检索结果合并到消息中")
                else:
                    logger.info("图检索未找到相关内容，保持原始消息")

            except Exception as e:
                logger.warning(f"图检索失败，继续使用原始检索结果: {e}")
                # 冷处理：图检索失败不影响主流程

            logger.info(f"合并后的last_message==》{last_message}")
            messages_list[-1]=last_message
            # print(f"messages_list==》{messages_list}")

            model = llm.get('m_name', None)
            provider = llm.get('provider', Provider.OPENAI)


            # 根据不同的 provider 使用对应的 API 调用方法
            if provider == Provider.DEEPSEEK:
                logger.info('调用 DEEPSEEK')
                stream_api = stream_openai_api
            elif provider == Provider.DOUBAO:
                logger.info('调用 DOUBAO')
                stream_api = stream_openai_api
            elif provider == Provider.LOCAL:
                logger.info('调用 LOCAL')
                stream_api = stream_openai_api
            elif provider == Provider.OLLAMA:
                logger.info('调用 OLLAMA')
                stream_api = stream_openai_api
            elif provider == Provider.JIUTIAN:  # 添加九天模型的处理
                logger.info('调用 JIUTIAN')
                stream_api = stream_jiutian_api
            else:
                logger.info('调用 OpenAI')
                stream_api = stream_openai_api

            yield 'event: moduleStatus\ndata: {"status":"running","name":"Base RAG Agent"}\n\n'

            quoteList = {
                "moduleName": "AI 对话",
                "moduleType": "chatNode",
                "model": model,
                "query": user_query,
                "quoteList": [
                    getattr(one, 'additional_kwargs', {}) 
                    for one in kg_qa_results.get('rerank_results', []) 
                    if hasattr(one, 'additional_kwargs')
                ],
                "context": context_strs
            }
            yield f'event: appStreamResponse\ndata: [{json.dumps(quoteList, ensure_ascii=False)}]\n\n'


            # event: moduleStatus
            # data: {"status":"running","name":"涉黄涉政识别"}
            
            async for chunk in stream_api(
                llm["api_key"],
                model,
                messages_list,
                llm["service_url"],
                extra=system_app_info.get('extra', None),
                system_prompt=system_app_params.get('system_prompt', None),
                provider=provider
            ):
                yield f"event: answer\n{chunk}"

        elif app_type == "WEBSEARCH":
            llm: LLMModel = await db["llms"].find_one({"id": int(system_app_params.get('llm_id', None))})
            model = llm.get('m_name', None)
            provider = llm.get('provider', Provider.OPENAI)
            agent = ConsumerProtectionAgent()
            async for event in agent.run(
                messages=messages,
                llm_params=llm
            ):
                # yield event
                yield f"{event}\n\n"

        elif app_type == "AGENT":
            async for chunk in robot_message_generator(system_app_params, messages, stream=True):
                yield chunk
                
        else:
            raise ValueError(f"Unsupported application type: {app_type}")
            
    except Exception as e:
        error_message = f"发生错误：{str(e)}"
        traceback.print_exc()
        logger.error(error_message)
        yield f"event: error\n{error_message}"


def convert_messages(original_messages):
    converted_messages = []

    # 添加系统消息（如果需要）
    converted_messages.append({
        "role": "system",
        "content": ""
    })

    # 转换用户和助手消息
    for msg in original_messages:
        if msg.get('role') in ['user', 'assistant']:
            converted_messages.append({
                "role": msg['role'],
                "content": msg['content']
            })

    return converted_messages




async def app_model_api(
    chat_id: str,
    user_id: str,
    messages: List[Dict[str, Any]],
    system_app_info: SystemAppSetting
) -> Dict[str, Any]:
    logger.info('========app_model_api')

    if not system_app_info:
        raise ValueError("System app setting not found")
    try:
        messages_list = convert_messages(messages)
        system_app_params = system_app_info.get('params', None)

        prompt_template = system_app_params.get('prompt_template', None)
        logger.info(system_app_params)
        if prompt_template:
            new_content = prompt_template.replace('{user_message}', messages_list[-1]['content'])
            messages_list[-1]['content'] = new_content

        if system_app_info["type"] == "WISERAG":
            return await wiserag_api(system_app_params['token_key'], chat_id, messages_list, system_app_params['service_url'])
        elif system_app_info["type"] == "LLM":
            llm: LLMModel = await db["llms"].find_one({"id": int(system_app_params.get('llm_id', None))})
            if not llm:
                raise ValueError("LLM model not found")
            else:
                logger.info(messages_list)

                model = llm.get('m_name', None)
                provider = llm.get('provider', Provider.OPENAI)
                
                # 构建请求数据
                data = {
                    "model": model,
                    "messages": messages_list,
                    "stream": False
                }
                
                if system_app_params.get('system_prompt'):
                    data['messages'][0]['content'] = system_app_params['system_prompt']
                    
                if extra := system_app_info.get('extra'):
                    if 'system_prompt' in extra:
                        data['messages'][0]['content'] = extra['system_prompt']
                    if 'temperature' in extra:
                        data['temperature'] = extra['temperature']
                    if 'top_p' in extra:
                        data['top_p'] = extra['top_p']
                    if 'max_tokens' in extra:
                        data['max_tokens'] = extra['max_tokens']
                
                # 使用统一的参数验证
                processed_messages, validated_params = prepare_llm_request(messages_list, data, provider)
                validated_params['messages'] = processed_messages

                # 根据不同的 provider 使用对应的 API 调用方法
            if provider == Provider.DEEPSEEK:
                logger.info('调用 DEEPSEEK')
                api = openai_api
            elif provider == Provider.DOUBAO:
                logger.info('调用 DOUBAO')
                api = openai_api
            elif provider == Provider.LOCAL:
                logger.info('调用 LOCAL')
                api = openai_api
            elif provider == Provider.OLLAMA:
                logger.info('调用 OLLAMA')
                api = openai_api
            elif provider == Provider.JIUTIAN:  # 添加九天模型的处理
                logger.info('调用 JIUTIAN')
                api = openai_api
            else:
                logger.info('调用 OpenAI')
                api = openai_api
            return await api(
                    llm["api_key"],
                    model,
                    processed_messages,
                    llm["service_url"],
                    extra=validated_params,  # 使用验证后的参数
                    system_prompt=system_app_params.get('system_prompt', None),
                    provider=provider  # 添加 provider 参数
                )

        else:
            raise ValueError("Unsupported application type")
    except Exception as e:
        traceback.print_exc()
        error_message = f"发生错误：{str(e)}"
        logger.error(error_message)
        return {"error": error_message}
    

async def app_model_api_test(
    llm_id: int,
) -> Dict[str, Any]:
    llm: LLMModel = await db["llms"].find_one({"id": int(llm_id)})
    if not llm:
        raise ValueError("LLM model not found")
    else:
        try:
            model = llm.get('m_name', None)
            provider = llm.get('provider', Provider.OPENAI)
            messages_list = [{"role": "user", "content": "你好"}]
                    
            # 构建请求数据
            data = {
                "model": model,
                "messages": messages_list,
                "stream": False
            }       
            # 使用统一的参数验证
            processed_messages, validated_params = prepare_llm_request(messages_list, data, provider)
            validated_params['messages'] = processed_messages

                    # 根据不同的 provider 使用对应的 API 调用方法
            if provider == Provider.DEEPSEEK:
                logger.info('调用 DEEPSEEK')
                api = openai_api
            elif provider == Provider.DOUBAO:
                logger.info('调用 DOUBAO')
                api = openai_api
            elif provider == Provider.LOCAL:
                logger.info('调用 LOCAL')
                api = openai_api
            elif provider == Provider.OLLAMA:
                logger.info('调用 OLLAMA')
                api = openai_api
            elif provider == Provider.JIUTIAN:  # 添加九天模型的处理
                logger.info('调用 JIUTIAN')
                api = openai_api
            else:
                logger.info('调用 OpenAI')
                api = openai_api

            return await api(
                    llm["api_key"],
                    model,
                    processed_messages,
                    llm["service_url"],
                    extra=validated_params,  # 使用验证后的参数
                    system_prompt=None,
                    provider=provider  # 添加 provider 参数
                )
        except Exception as e:
            traceback.print_exc()
            error_message = f"发生错误：{str(e)}"
            logger.error(error_message)
            return {"error": error_message}