# app/routers/auth.py
from fastapi import APIRouter, Depends, Response, status
from fastapi.security import <PERSON>Auth2P<PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from pydantic import BaseModel
from typing import List, Optional
from app.utils.logging_config import setup_logging, get_logger
from fastapi.responses import JSONResponse
import random
from ..utils.config import settings
from ..utils.wiseflow_auth import auth
from datetime import datetime, timedelta
from ..db.mongodb import db
import hashlib
from app.utils.logging_config import setup_logging, get_logger
from ..utils.auth import verify_token
import os
import json
import httpx

# 设置日志配置
setup_logging()
logger = get_logger(__name__)


router = APIRouter(
        prefix="/api",
    tags=["auth"]
)

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


class LoginData(BaseModel):
    username: str
    password: str
    autoLogin: Optional[bool] = False
    type: Optional[str] = None

def generate_auth_token(username: str) -> str:
    current_time = datetime.now().isoformat()
    return hashlib.md5(f"{username}{current_time}".encode()).hexdigest()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    try:
        return hashlib.md5(plain_password.encode('utf-8')).hexdigest() == hashed_password
    except Exception as e:
        logger.error(f"密码验证过程中发生错误: {str(e)}")
        return False
    

async def login(username: str, password: str, type: str = None, response: Response = None):
    logger.info(f"收到登录请求，用户名: {username}, 类型: {type}")

    # 从数据库中查找用户
    user = await db["users"].find_one({"phone": username})

    if not user:
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "status": 'error',
                "type": type,
                "currentAuthority": 'guest',
                "message": "用户不存在"
            }
        )

    # 验证密码
    if not verify_password(password, user["password"]):
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "status": 'error',
                "type": type,
                "currentAuthority": 'guest',
                "message": "密码错误"
            }
        )
    # 生成新的 auth_token
    auth_token = generate_auth_token(username)
    
    # 使用 settings 中的配置
    token_expiry = datetime.now() + timedelta(seconds=settings.ACCESS_TOKEN_EXPIRE_SECONDS)

    # 更新用户的 auth_token 和 token_expiry
    await db["users"].update_one(
        {"phone": username},
        {"$set": {"auth_token": auth_token, "token_expiry": token_expiry}}
    )

    if settings.enable_wiseflow:
        try:
            username = user["role_name"]
            sign = auth.generate_signature(username)
            async with httpx.AsyncClient() as client:
                wiseflow_response = await client.post(
                    settings.wiseflow_url,
                    params={"username": username, "sign": sign}
                )
                if wiseflow_response.status_code == 200:
                    token_data = wiseflow_response.json()
                    response.set_cookie(
                        key="access_token_lf",
                        value=token_data["access_token"],
                        # httponly=settings.ACCESS_HTTPONLY,
                        # samesite=settings.ACCESS_SAME_SITE,
                        secure=False,
                        expires=settings.ACCESS_TOKEN_EXPIRE_SECONDS,
                        # domain="localhost"
                    )
                    response.set_cookie(
                        key="refresh_token_lf",
                        value=token_data["refresh_token"],
                        # httponly=settings.REFRESH_HTTPONLY,
                        # samesite=settings.REFRESH_SAME_SITE,
                        secure=False,
                        expires=settings.ACCESS_TOKEN_EXPIRE_SECONDS * 7,
                        # domain="localhost"
                    )
        except Exception as e:
            logger.error(f"Wiseflow认证过程中发生错误: {str(e)}")

    # 登录成功
    response_data = {
        "status": 'ok',
        "type": type,
        "currentAuthority": user["role_id"],
        "message": "登录成功",
        "auth_token": auth_token,
        "token_expiry": token_expiry.isoformat()
    }
    
    # 使用传入的response对象设置响应内容
    response.body = json.dumps(response_data).encode()
    response.headers["content-type"] = "application/json"
    response.status_code = 200
    return response

async def get_role_access(role_id: int) -> dict:
    # 这里应该是实际的数据库查询或API调用
    # 返回一个包含权限信息的字典
    return {
        "canAccessAdmin": True,
        "canAccessUserManagement": False,
        # 其他权限...
    }


@router.post("/login/account")
async def login_account(login_data: LoginData, response: Response):
    return await login(username=login_data.username, password=login_data.password, type=login_data.type, response = response)

@router.post("/login/outLogin")
async def logout():
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "success": True,
            "data": {},
            "errorCode": "0",
            "errorMessage": "",
            "showType": 0
        }
    )

@router.get("/currentUser")
async def get_current_user(current_user: dict = Depends(verify_token)):
    role = await db["roles"].find_one({"id": current_user["role_id"]})

    return JSONResponse(
        content={
            "success": True,
            "data": {
                "name": current_user.get("name", ""),
                "avatar": current_user.get("avatar", ""),
                "id": current_user.get("id", 0),
                "signature": current_user.get("signature", ""),
                "title": current_user.get("title", ""),
                "group_id": current_user.get("group_id", 0),
                "group_name": current_user.get("group_name", ""),
                "tags": current_user.get("tags", []),
                "notifyCount": current_user.get("notifyCount", 0),
                "unreadCount": current_user.get("unreadCount", 0),
                "country": current_user.get("country", ""),
                "role_name": current_user.get("role_name", ""),
                # "geographic": current_user.get("geographic", {}),
                "address": current_user.get("address", ""),
                "phone": current_user.get("phone", ""),
                "role_id": current_user.get("role_id", 0),
                "access":role["access"] if role else None,
            }
        }
    )

@router.get("/notices")
async def get_notices(current_user: dict = Depends(verify_token)):
    # 这里可以根据当前用户获取相应的通知数据
    return []

class MenuDataItem(BaseModel):
    path: str
    name: str
    icon: Optional[str] = None
    component: Optional[str] = None
    routes: Optional[List['MenuDataItem']] = None

MenuDataItem.update_forward_refs()

@router.get("/getMenuData")
async def get_menu_data() -> List[MenuDataItem]:
    # 这里应该返回菜单数据，但为了简化，我们返回一个基本的菜单结构
    return [
        MenuDataItem(
            path="/home",
            name="home",
            icon="smile",
            component="./home"
        ),
        MenuDataItem(
            path="/admin",
            name="admin",
            icon="crown",
            routes=[
                # MenuDataItem(
                #     path="/admin/sub-page",
                #     name="sub-page",
                #     component="./Home"
                # ),
            ]
        ),
    ]

# # 刷新令牌接口
# @router.post("/refresh-token")
# async def refresh_token(current_user: dict = Depends(verify_token)):
#     # 假设令牌有效期为 15 分钟
#     new_expiration = datetime.utcnow() + timedelta(minutes=15)
#     new_token = create_access_token(data={"sub": current_user["id"]}, expires_delta=new_expiration)
#     return {"token": new_token}
