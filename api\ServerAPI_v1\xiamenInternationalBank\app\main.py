# main.py
import os
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .chat import router as chat_router
from .knowledge import router as knowledge_router
from .initsql import init_db, close_db
from .logging_config import setup_logging, get_logger
import asyncio

# 设置日志
setup_logging()
logger = get_logger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="厦门国际银行 API",
    description="厦门国际银行知识库问答系统 API",
    version="1.0.0"
)

# CORS 配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(chat_router)
app.include_router(knowledge_router)

# 启动时的日志信息
@app.on_event("startup")
async def startup_event():
    """应用启动时执行"""
    logger.info("正在初始化数据库...")
    await init_db()
    logger.info("数据库初始化完成")

# 启动时的日志信息
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时执行"""
    logger.info("正在关闭数据库连接...")
    await close_db()
    logger.info("数据库连接已关闭")

#根路由
@app.get("/")
async def root():
    return {"message": "厦门国际银行知识库问答系统 API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000) 