from mongoengine import Document, ObjectIdField,StringField, IntField, DateTimeField, FloatField, DictField, ListField, BooleanField

from datetime import datetime
from pydantic import BaseModel
from bson import ObjectId
from typing import Optional, List
from typing import Dict 
from .message import MessageResponse


class Conversation(Document):
    meta = {
        'collection': 'conversations'
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId())
    created_at = DateTimeField(default=datetime.now())
    user_id = IntField(index=True)
    user_name = StringField(index=True)
    conversation_name = StringField(index=True)
    pinned_at = DateTimeField(nullable=True)
    pinned = IntField()
    app_info = StringField(nullable=True)
    active_at = DateTimeField(default=datetime.now())

class Conversation(BaseModel):
    conversation_name: str
    user_id: Optional[int] = None
    user_name: Optional[str] = None
    app_info: Optional[str] = None
    created_at: Optional[datetime] = None
    pinned_at: Optional[datetime] = None
    pinned: Optional[int] = 0
    active_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ConversationCreate(BaseModel):
    conversation_name: str
    app_info: str
    user_id: int
    user_name: str

class ConversationUpdate(BaseModel):
    conversation_name: Optional[str] = None
    pinned_at: Optional[str] = None
    pinned: Optional[int] = None
    active_at: Optional[str] = None

class ConversationResponse(BaseModel):
    id: str
    created_at: Optional[str]
    conversation_name: str
    pinned_at: Optional[str]
    pinned: Optional[int] = None
    active_at: Optional[str]

class ConversationMessageResponse(BaseModel):
    success: bool
    conversation: ConversationResponse
    messages: List[MessageResponse]

class ConversationInDB(BaseModel):
    created_at: datetime
    user_id: int
    user_name: str
    conversation_name: str
    pinned_at: Optional[datetime]
    pinned: Optional[int]
    app_info: str
    active_at: Optional[datetime]
