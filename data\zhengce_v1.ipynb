{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 整体流程\n", "\n", "\n", "#根据时间按照一定顺序 ，读取数据、处理数据、存储数据\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python\n", "# -*- coding: utf-8 -*-\n", "\n", "\"\"\"\n", "简单ES到ES数据迁移代理\n", "基于LangGraph实现，只包含三个节点：读取数据、处理数据和保存数据\n", "\"\"\"\n", "import json\n", "from typing import Dict, List, Any, Optional, TypedDict\n", "from datetime import datetime\n", "import logging\n", "import requests\n", "import traceback\n", "\n", "import re \n", "import pandas as pd\n", "from elasticsearch import Elasticsearch\n", "from langchain_openai import ChatOpenAI,OpenAIEmbeddings\n", "from langchain_core.messages import HumanMessage, SystemMessage\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.output_parsers import JsonOutputParser\n", "from langgraph.graph import StateGraph, END\n", "from langchain_core.tools import tool\n", "\n", "# 配置日志\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 这是要存入的ES"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# 配置变量\n", "\n", "TARGET_ES_HOST = \"**************\"\n", "TARGET_ES_PORT = 9600\n", "TARGET_ES_USER = None\n", "TARGET_ES_PASSWORD = None\n", "TARGET_ES_INDEX = \"pro_mcp_data_zhengce_v2\"\n", "\n", "BATCH_SIZE = 10  # 批处理大小\n", "\n", "\n", "hosts = '************'\n", "port = 9200\n", "http_auth=('qianxiang', 'aRQ8K$3ymIbG')\n", "\n", "\n", "# hosts = '************'\n", "# port = 9200\n", "# http_auth=('jiachengbin', 'b0>srW=!UfTC')\n", " # 可替换为其他模型\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 这是大模型参数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["embedding_service_url = \"http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/bge_m3_finetune_20240710/v1/embeddings\"\n", "embedding_api_key =  \"ZWY0YzJkZTM4OGI0YmI3YjljOTI4NGE5ZDMxMDVhMzUzZjA3YmYxMw==\"\n", "embedding_name = \"bge-m3\"\n", "\n", "\n", "\n", "llm_service_url = \"http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen3_32b_2/v1\"\n", "llm_api_key = \"ZmYzMmQ1N2E2YWZjMzcwN2Y3NTBjNmQzYjk3Njk3ZWY4Njk5MWQ0ZQ==\"\n", "OPENAI_MODEL = \"Qwen3-32B\"\n", "\n", "\n", "\n", "llm = ChatOpenAI(\n", "    temperature=0, \n", "    model=OPENAI_MODEL, \n", "    openai_api_base=llm_service_url,\n", "    api_key=llm_api_key\n", ")\n", "\n", "\n", "\n", "embedding_model = OpenAIEmbeddings(\n", "           model=embedding_name,\n", "           openai_api_key=embedding_api_key,\n", "           openai_api_base=embedding_service_url\n", ")\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 这是获取mbedding方法"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["embedding_config = {\n", "    \"api_key\":embedding_api_key,\n", "    \"service_url\":embedding_service_url,\n", "    \"embedding_name\":embedding_name\n", "}\n", "def get_embedding(text,embedding_config):\n", "    access_token = embedding_config.get(\"api_key\", \"\")\n", "    service_url = embedding_config.get(\"service_url\", \"\")\n", "    model = embedding_config.get(\"embedding_name\", \"bge-m3\")\n", "    try:\n", "        headers = {'Content-Type': 'application/json', 'Authorization': f'{access_token}'}\n", "        req = {\n", "            \"input\": [text],\n", "            \"model\": model\n", "        }\n", "        embdd_response = requests.post(url=service_url, json=req, headers=headers)\n", "        if embdd_response.status_code == 200:\n", "            # print(\"embdd_response.json():\", len(embdd_response.json()['data'][0]['embedding']))\n", "            query_embedding =  embdd_response.json()['data'][0]['embedding']\n", "        \n", "            DB_VECTOR_DIMENSION = 1536\n", "\n", "            # 如果生成的向量维度与数据库不匹配，进行扩充或截断\n", "            if len(query_embedding) != DB_VECTOR_DIMENSION:\n", "                if len(query_embedding) < DB_VECTOR_DIMENSION:\n", "                    # 扩充向量维度\n", "                    query_embedding = query_embedding + [0.0] * (DB_VECTOR_DIMENSION - len(query_embedding))\n", "                    logger.info(f\"向量维度已扩充至 {DB_VECTOR_DIMENSION}\")\n", "                else:\n", "                    # 截断向量维度\n", "                    query_embedding = query_embedding[:DB_VECTOR_DIMENSION]\n", "                    logger.info(f\"向量维度已截断至 {DB_VECTOR_DIMENSION}\")\n", "\n", "\n", "\n", "            logger.info(f\"最终查询向量维度: {len(query_embedding)}\")\n", "            return query_embedding\n", "        else:\n", "            logger.error(f\"获取embedding失败: {embdd_response.status_code}\")\n", "            return None\n", "    except Exception as e:\n", "        traceback.print_exc()\n", "        logger.error(f\"从ES获取数据时出错: {str(e)}\")\n", "        return None\n", "   \n", "# a = get_embedding(\"如果您需要在多个操作之间重用同一个连接，可以保持默认设置，并在所有操作完成后手动关闭：\",embedding_config)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 这个是取数据的ES。需要把地址、端口、账号要到。可做找张博 \n", "# 樊总政策信息所存的 ES\n", "\n", "需要改的包括\n", "1、地址 端口、账号\n", "2、build_body的查询条件 如果按照现在的 ，可以把这个改掉inserttime\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# \n", "\n", "\n", "\n", "# 这几个\n", "def build_body(start_time,end_time):\n", "    body = {\n", "        \"query\": {\n", "                \"bool\": {\n", "                \"filter\": [\n", "                    {\n", "                    \"range\": {\n", "                         \"laws_public_time\": {\n", "                          \"gte\": start_time,\n", "                          \"lte\": end_time\n", "                        }\n", "                    }\n", "                    }\n", "                ]\n", "                }\n", "            },\n", "        \"sort\": [{\"laws_public_time\": \"desc\"}]  # 按gathertime降序排序\n", "    }\n", "    return body\n", "\n", "def get_es_scroll_data(index,build_body): #滚动查询 获取数据es\n", "    es7 = Elasticsearch(hosts, http_auth=http_auth, port=port, timeout=3600)\n", "    # elasticsearch滚动查询\n", "    result = es7.search(index=index, scroll='10m', body=build_body, size=1000, request_timeout=3600)\n", "    # 滚动id\n", "    sid = result['_scroll_id']\n", "    # 滚动总数量\n", "    scroll_size = result['hits']['total']['value']\n", "    print(f\"数据量：{scroll_size}\")\n", "    res_list = []\n", "    while scroll_size > 0:\n", "        # 滚动获取source集合\n", "        source_list = [data for data in result['hits']['hits']]\n", "        res_list.extend(source_list)\n", "        # print ('Scrolling...')\n", "        # 继续滚动\n", "        result = es7.scroll(scroll_id = sid, scroll ='10m',request_timeout=5000)\n", "        sid = result['_scroll_id']\n", "        # 滚动数量\n", "        scroll_size = len(result['hits']['hits'])\n", "    es7.clear_scroll(scroll_id = sid) #清除滚动\n", "    return res_list\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["# 示例：从ES获取数据并转为DataFrame\n", "# 这里根据实际情况调整查询条件\n", "from datetime import datetime\n", "\n", "def format_es_date(date_str):\n", "    # 支持 2025-5-9 00:00:00 这种格式，自动补零\n", "    dt = datetime.strptime(date_str, \"%Y-%m-%d %H:%M:%S\")\n", "    return dt.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "# 用法\n", "start = format_es_date(\"2025-5-1 00:00:00\")\n", "end = format_es_date(\"2025-5-31 00:00:00\")\n", "body = build_body(start, end)\n", "\n", "\n", "\n", "es_data = get_es_scroll_data('wzty_laws_database_alias', body)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(es_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["es_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "def get_day_range(end_time_str: str = None):\n", "    \"\"\"\n", "    输入：结束时间字符串（如 '2024-05-19 15:23:00'），为空则用当前时间\n", "    输出：前一天的开始时间和结束时间（'2024-05-18 00:00:00', '2024-05-18 23:59:59'）\n", "    \"\"\"\n", "    if not end_time_str or end_time_str.strip() == \"\":\n", "        dt = datetime.now()\n", "    else:\n", "        dt = datetime.strptime(end_time_str, \"%Y-%m-%d %H:%M:%S\")\n", "    # 减去一天\n", "    dt = dt - <PERSON><PERSON><PERSON>(days=1)\n", "    day_str = dt.strftime(\"%Y-%m-%d\")\n", "    start_time = f\"{day_str} 00:00:00\"\n", "    end_time = f\"{day_str} 23:59:59\"\n", "    return start_time, end_time\n", "\n"]}, {"cell_type": "markdown", "metadata": {"scrolled": false}, "source": ["\n", "# 这个是我写的去标签的方法，可以替换成你的"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "from html import unescape\n", "from bs4 import BeautifulSoup\n", "\n", "def html_to_markdown(html):\n", "    \"\"\"\n", "    将HTML转换为Markdown格式\n", "    \n", "    Args:\n", "        html (str): HTML字符串\n", "        \n", "    Returns:\n", "        str: 转换后的Markdown文本\n", "    \"\"\"\n", "    if not html:\n", "        return ''\n", "    \n", "    # 使用BeautifulSoup解析HTML\n", "    soup = BeautifulSoup(html, 'html.parser')\n", "    \n", "    # 移除script和style标签\n", "    for script in soup([\"script\", \"style\"]):\n", "        script.extract()\n", "    \n", "    # 处理标题\n", "    for i in range(1, 7):\n", "        for tag in soup.find_all(f'h{i}'):\n", "            tag.replace_with(f\"{'#' * i} {tag.get_text().strip()}\\n\\n\")\n", "    \n", "    # 处理段落\n", "    for tag in soup.find_all('p'):\n", "        tag.replace_with(f\"{tag.get_text().strip()}\\n\\n\")\n", "    \n", "    # 处理列表\n", "    for ul in soup.find_all('ul'):\n", "        for li in ul.find_all('li'):\n", "            li.replace_with(f\"* {li.get_text().strip()}\\n\")\n", "    \n", "    for ol in soup.find_all('ol'):\n", "        for i, li in enumerate(ol.find_all('li')):\n", "            li.replace_with(f\"{i+1}. {li.get_text().strip()}\\n\")\n", "    \n", "    # 处理链接\n", "    for a in soup.find_all('a', href=True):\n", "        text = a.get_text().strip()\n", "        href = a['href']\n", "        a.replace_with(f\"[{text}]({href})\")\n", "    \n", "    # 处理图片\n", "    for img in soup.find_all('img', src=True):\n", "        alt = img.get('alt', '')\n", "        src = img['src']\n", "        img.replace_with(f\"![{alt}]({src})\")\n", "    \n", "    # 处理粗体和斜体\n", "    for strong in soup.find_all(['strong', 'b']):\n", "        strong.replace_with(f\"**{strong.get_text().strip()}**\")\n", "    \n", "    for em in soup.find_all(['em', 'i']):\n", "        em.replace_with(f\"*{em.get_text().strip()}*\")\n", "    \n", "    # 处理引用块\n", "    for blockquote in soup.find_all('blockquote'):\n", "        lines = blockquote.get_text().strip().split('\\n')\n", "        quoted_text = '\\n'.join([f\"> {line}\" for line in lines])\n", "        blockquote.replace_with(f\"{quoted_text}\\n\\n\")\n", "    \n", "    # 处理代码块\n", "    for pre in soup.find_all('pre'):\n", "        code = pre.get_text().strip()\n", "        pre.replace_with(f\"```\\n{code}\\n```\\n\\n\")\n", "    \n", "    for code in soup.find_all('code'):\n", "        code.replace_with(f\"`{code.get_text().strip()}`\")\n", "    \n", "    # 处理表格 (简单实现)\n", "    for table in soup.find_all('table'):\n", "        md_table = []\n", "        \n", "        # 表头\n", "        headers = []\n", "        for th in table.find_all('th'):\n", "            headers.append(th.get_text().strip())\n", "        \n", "        if headers:\n", "            md_table.append('| ' + ' | '.join(headers) + ' |')\n", "            md_table.append('| ' + ' | '.join(['---'] * len(headers)) + ' |')\n", "        \n", "        # 表格内容\n", "        for tr in table.find_all('tr'):\n", "            row = []\n", "            for td in tr.find_all('td'):\n", "                row.append(td.get_text().strip())\n", "            if row:\n", "                md_table.append('| ' + ' | '.join(row) + ' |')\n", "        \n", "        table.replace_with('\\n'.join(md_table) + '\\n\\n')\n", "    \n", "    # 处理水平线\n", "    for hr in soup.find_all('hr'):\n", "        hr.replace_with('---\\n\\n')\n", "    \n", "    # 提取并清理结果文本\n", "    markdown = soup.get_text()\n", "    \n", "    # 修复可能的格式问题\n", "    markdown = re.sub(r'\\n{3,}', '\\n\\n', markdown)  # 移除多余空行\n", "    markdown = markdown.strip()\n", "    \n", "    return markdown"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "    \n", "# def get_default_result() -> Dict[str, Any]:\n", "#     \"\"\"返回默认的空结果结构\"\"\"\n", "#     return {\n", "#         \"policy_theme\": [],\n", "#         \"affected_industries\": [],\n", "#         \"policy_impact\": {\n", "#             \"regulatory_impact\": \"\",\n", "#             \"compliance_requirements\": \"\",\n", "#             \"risk_level\": \"中\"\n", "#         },\n", "#         \"policy_direction\": {\n", "#             \"trend\": \"\",\n", "#             \"key_points\": [],\n", "#             \"implementation_timeline\": \"\"\n", "#         },\n", "#         \"market_impact\": {\n", "#             \"industry_effect\": \"\",\n", "#             \"business_opportunities\": \"\",\n", "#             \"market_risk\": \"\"\n", "#         },\n", "#         \"area\": [],\n", "#         \"summary\": \"\"\n", "#     }\n", "\n", "\n", "# def analyze_text(title: str, content: str) -> Dict[str, Any]:\n", "#     \"\"\"分析文本内容，提取关键信息，返回结构化字段\"\"\"\n", "\n", "#     # 构建提示词模板\n", "#     prompt = ChatPromptTemplate.from_messages([\n", "#         SystemMessage(content=\"\"\"你是一个专业的政策分析助手，需要分析政策文件并提取以下信息：\n", "\n", "#     1. 政策主题 (policy_theme): 提取政策的核心主题关键词列表，如\"金融监管\"、\"环境保护\"等\n", "\n", "#     2. 影响行业 (affected_industries): 提取政策影响的行业列表，如\"银行业\"、\"房地产\"等\n", "\n", "#     3. 政策影响 (policy_impact):\n", "#        - 监管影响 (regulatory_impact): 政策对监管环境的影响\n", "#        - 合规要求 (compliance_requirements): 需要遵守的具体要求\n", "#        - 风险等级 (risk_level): 必须是以下之一：高、中、低\n", "\n", "#     4. 政策方向 (policy_direction):\n", "#        - 政策趋势 (trend): 政策的发展方向\n", "#        - 政策要点 (key_points): 政策的核心内容要点\n", "#        - 实施时间表 (implementation_timeline): 政策实施的时间安排\n", "\n", "#     5. 市场影响 (market_impact):\n", "#        - 行业影响 (industry_effect): 对特定行业的具体影响\n", "#        - 商业机会 (business_opportunities): 政策带来的潜在机会\n", "#        - 市场风险 (market_risk): 对市场可能带来的风险\n", "\n", "#     6. 影响区域 (area): 提取政策影响的地区列表，如\"北京市\"、\"上海市\"等\n", "\n", "#     7. 摘要 (summary): 对政策内容进行300字以内的摘要，要包括政策的关键信息\n", "\n", "#     请以JSON格式返回，包含以上所有字段。\n", "#     如: {\n", "#         \"policy_theme\": [\"金融监管\", \"风险防控\"],\n", "#         \"affected_industries\": [\"银行业\", \"保险业\"],\n", "#         \"policy_impact\": {\n", "#             \"regulatory_impact\": \"加强金融监管力度\",\n", "#             \"compliance_requirements\": \"要求金融机构加强风险控制\",\n", "#             \"risk_level\": \"中\"\n", "#         },\n", "#         \"policy_direction\": {\n", "#             \"trend\": \"趋严\",\n", "#             \"key_points\": [\"加强监管\", \"防范风险\"],\n", "#             \"implementation_timeline\": \"2024年1月1日起实施\"\n", "#         },\n", "#         \"market_impact\": {\n", "#             \"industry_effect\": \"促进金融业规范发展\",\n", "#             \"business_opportunities\": \"合规业务需求增加\",\n", "#             \"market_risk\": \"短期可能增加合规成本\"\n", "#         },\n", "#         \"area\": [\"全国\"],\n", "#         \"summary\": \"该政策主要针对...\"\n", "#     }\"\"\"),\n", "#         HumanMessage(content=f\"\"\"请分析以下政策文件：\n", "#     标题: {title}\n", "#     内容: {content}\n", "#     /no_think\"\"\")\n", "#     ])\n", "    \n", "#     chain = prompt | llm  # 确保 llm 已初始化为 ChatModel 类型\n", "\n", "#     try:\n", "#         result = chain.invoke({})\n", "#         # 获取内容：先尝试 .content 属性，如果是对象\n", "#         raw_content = getattr(result, 'content', result)\n", "        \n", "#         print(raw_content)\n", "#         text = re.sub(r'<think>.*?</think>', '', raw_content, flags=re.DOTALL)\n", "\n", "#         # 匹配第一个 JSON 块（大括号包围）\n", "#         json_match = re.search(r'({.*?})', raw_content, flags=re.DOTALL)\n", "#         if not json_match:\n", "#             raise '无匹配'\n", "#         json_str = json_match.group(1)\n", "\n", "#         # 尝试解析\n", "#         return json.loads(json_str)\n", "\n", "#     except Exception as e:\n", "#         traceback.print_exc()\n", "#         logger.error(f\"分析文本时出错: {str(e)}\")\n", "#         return get_default_result\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_text(title: str, content: str) -> Dict[str, Any]:\n", "    \"\"\"分析文本内容，提取关键信息，返回结构化字段\"\"\"\n", "\n", "    # 构建提示词模板\n", "    prompt = ChatPromptTemplate.from_messages([\n", "    SystemMessage(content=\"\"\"你是一个专业的政策分析助手，需要分析政策文件并提取以下信息，所有字段均为一级字段：\n", "\n", "1. 政策主题 (policy_theme): 列表，如 [\"金融监管\", \"环境保护\"]\n", "2. 影响行业 (affected_industries): 列表，如 [\"银行业\", \"房地产\"]\n", "3. 监管影响 (policy_impact_regulatory_impact): 字符串\n", "4. 合规要求 (policy_impact_compliance_requirements): 字符串\n", "5. 风险等级 (policy_impact_risk_level): 必须是以下之一：\"高\"、\"中\"、\"低\"\n", "6. 政策趋势 (policy_direction_trend): 字符串\n", "7. 政策要点 (policy_direction_key_points): 字符串\n", "8. 实施时间表 (policy_direction_implementation_timeline): 字符串\n", "9. 行业影响 (market_impact_industry_effect): 字符串\n", "10. 商业机会 (market_impact_business_opportunities): 字符串\n", "11. 市场风险 (market_impact_market_risk): 字符串\n", "12. 影响区域 (area): 列表，如 [\"北京市\", \"上海市\"]\n", "13. 摘要 (summary): 300字以内摘要，包含政策关键信息\n", "14. 关联法规 (related_laws): 关联法规名称的字符串数组，如 [\"公司法\", \"证券法\"]\n", "\n", "请以JSON格式返回，包含以上所有字段，字段名必须与上面完全一致。\n", "如: {\n", "    \"policy_theme\": [\"金融监管\", \"风险防控\"],\n", "    \"affected_industries\": [\"银行业\", \"保险业\"],\n", "    \"policy_impact_regulatory_impact\": \"加强金融监管力度\",\n", "    \"policy_impact_compliance_requirements\": \"要求金融机构加强风险控制\",\n", "    \"policy_impact_risk_level\": \"中\",\n", "    \"policy_direction_trend\": \"趋严\",\n", "    \"policy_direction_key_points\": \"加强监管、防范风险\",\n", "    \"policy_direction_implementation_timeline\": \"2024年1月1日起实施\",\n", "    \"market_impact_industry_effect\": \"促进金融业规范发展\",\n", "    \"market_impact_business_opportunities\": \"合规业务需求增加\",\n", "    \"market_impact_market_risk\": \"短期可能增加合规成本\",\n", "    \"area\": [\"全国\"],\n", "    \"summary\": \"该政策主要针对...\",\n", "    \"related_laws\": [\"公司法\", \"证券法\"]\n", "}\"\"\"),\n", "    HumanMessage(content=f\"\"\"请分析以下政策文件：\n", "标题: {title}\n", "内容: {content}\n", "/no_think\"\"\")\n", "])\n", "    \n", "    chain = prompt | llm  # 确保 llm 已初始化为 ChatModel 类型\n", "\n", "    try:\n", "        result = chain.invoke({})\n", "        # 获取内容：先尝试 .content 属性，如果是对象\n", "        raw_content = getattr(result, 'content', result)\n", "        \n", "        # 清理内容\n", "        # 1. 移除 <think> 标签及其内容\n", "        text = re.sub(r'<think>.*?</think>', '', raw_content, flags=re.DOTALL)\n", "        \n", "        # 2. 查找 JSON 内容\n", "        # 首先尝试查找 ```json 和 ``` 之间的内容\n", "        json_match = re.search(r'```json\\s*({.*?})\\s*```', text, flags=re.DOTALL)\n", "        if not json_match:\n", "            # 如果没有找到 ```json 标记，则直接查找第一个 { 和最后一个 } 之间的内容\n", "            json_match = re.search(r'({.*})', text, flags=re.DOTALL)\n", "            \n", "        if not json_match:\n", "            logger.error(\"未找到有效的JSON内容\")\n", "            return get_default_result()\n", "            \n", "        json_str = json_match.group(1)\n", "        \n", "        # 3. 清理 JSON 字符串\n", "        # 移除可能的转义字符\n", "        json_str = json_str.replace('\\\\n', ' ').replace('\\\\r', '')\n", "        # 移除尾随逗号\n", "        json_str = re.sub(r',\\s*}', '}', json_str)\n", "        json_str = re.sub(r',\\s*]', ']', json_str)\n", "        \n", "        # 4. 解析 JSON\n", "        try:\n", "            result = json.loads(json_str)\n", "            \n", "            # 5. 验证结果结构\n", "            if not validate_result_structure(result):\n", "                logger.error(\"JSON结构不符合预期\")\n", "                return get_default_result()\n", "                \n", "            return result\n", "            \n", "        except json.JSONDecodeError as je:\n", "            logger.error(f\"JSON解析错误: {str(je)}\")\n", "            return get_default_result()\n", "\n", "    except Exception as e:\n", "        logger.error(f\"分析文本时出错: {str(e)}\")\n", "        return get_default_result()\n", "\n", "\n", "def validate_result_structure(data: Dict[str, Any]) -> bool:\n", "    \"\"\"验证返回的JSON结构是否符合预期（全部一级字段）\"\"\"\n", "    try:\n", "        required_fields = {\n", "            \"policy_theme\": list,\n", "            \"affected_industries\": list,\n", "            \"policy_impact_regulatory_impact\": str,\n", "            \"policy_impact_compliance_requirements\": str,\n", "            \"policy_impact_risk_level\": str,\n", "            \"policy_direction_trend\": str,\n", "            \"policy_direction_key_points\": str,\n", "            \"policy_direction_implementation_timeline\": str,\n", "            \"market_impact_industry_effect\": str,\n", "            \"market_impact_business_opportunities\": str,\n", "            \"market_impact_market_risk\": str,\n", "            \"area\": list,\n", "            \"summary\": str,\n", "            \"related_laws\": list\n", "        }\n", "        \n", "        # 检查必需字段\n", "        for field, field_type in required_fields.items():\n", "            if field not in data:\n", "                logger.error(f\"缺少必需字段: {field}\")\n", "                return False\n", "            if not isinstance(data[field], field_type):\n", "                logger.error(f\"字段类型错误: {field}, 期望 {field_type}, 实际 {type(data[field])}\")\n", "                return False\n", "\n", "        # 风险等级校验\n", "        if data[\"policy_impact_risk_level\"] not in [\"高\", \"中\", \"低\"]:\n", "            logger.error(\"policy_impact_risk_level 字段值必须为 '高'、'中' 或 '低'\")\n", "            return False\n", "\n", "        return True\n", "\n", "    except Exception as e:\n", "        logger.error(f\"验证JSON结构时发生错误: {str(e)}\")\n", "        return False\n", "    \n", "\n", "def get_default_result() -> Dict[str, Any]:\n", "    \"\"\"返回默认的空结果结构（全部一级字段）\"\"\"\n", "    return {\n", "        \"policy_theme\": [],\n", "        \"affected_industries\": [],\n", "        \"policy_impact_regulatory_impact\": \"\",\n", "        \"policy_impact_compliance_requirements\": \"\",\n", "        \"policy_impact_risk_level\": \"中\",\n", "        \"policy_direction_trend\": \"\",\n", "        \"policy_direction_key_points\": \"\",\n", "        \"policy_direction_implementation_timeline\": \"\",\n", "        \"market_impact_industry_effect\": \"\",\n", "        \"market_impact_business_opportunities\": \"\",\n", "        \"market_impact_market_risk\": \"\",\n", "        \"area\": [],\n", "        \"summary\": \"\",\n", "        \"related_laws\": []\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Agent 流程\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# al = analyze_text(title,content)\n", "# print(al)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def format_date(date_value) -> str:\n", "    \"\"\"\n", "    将日期格式统一转换为 yyyy-MM-dd HH:mm:ss 格式\n", "    支持处理 Pandas Timestamp、datetime 对象和字符串\n", "    \"\"\"\n", "    try:\n", "        # 处理空值\n", "        if pd.isna(date_value) or date_value is None:\n", "            return \"1970-01-01 00:00:00\"\n", "            \n", "        # 如果是 Pandas Timestamp\n", "        if isinstance(date_value, pd.Timestamp):\n", "            return date_value.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "            \n", "        # 如果是 datetime 对象\n", "        if isinstance(date_value, datetime):\n", "            return date_value.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "            \n", "        # 如果是字符串\n", "        if isinstance(date_value, str):\n", "            # 如果是默认值，直接返回\n", "            if date_value == \"1970-01-01 00:00:00\":\n", "                return date_value\n", "            # 处理 ISO 格式\n", "            if 'T' in date_value:\n", "                date_value = date_value.replace('T', ' ')\n", "            return date_value\n", "            \n", "        # 如果都不是，尝试转换为字符串\n", "        return str(date_value)\n", "        \n", "    except Exception as e:\n", "        logger.warning(f\"日期格式转换失败: {date_value}, 类型: {type(date_value)}, 错误: {str(e)}\")\n", "        return \"1970-01-01 00:00:00\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_data(current_docs):\n", "    \"\"\"处理数据，使用LLM提取信息\"\"\"\n", "\n", "    es = Elasticsearch(\n", "        [f\"{TARGET_ES_HOST}:{TARGET_ES_PORT}\"]\n", "    )\n", "\n", "    # 检查连接\n", "    if es.ping():\n", "        print(f\"ES连接成功: {TARGET_ES_HOST}:{TARGET_ES_PORT}\")\n", "        es_info = es.info()\n", "        logger.info(f\"ES版本: {es_info['version']['number']}\")\n", "    else:\n", "        print(f\"ES连接失败: {TARGET_ES_HOST}:{TARGET_ES_PORT}\")\n", "    \n", "    try:\n", "        processed_count = 0\n", "        \n", "        for  source in current_docs:\n", "            # 获取并清理文本内容\n", "            _id = source['_id']\n", "            row = source['_source']\n", "            content = row[\"laws_text\"] if \"laws_text\" in row else \"\"\n", "            clean_text = html_to_markdown(content)\n", "            \n", "            # 使用LLM分析文本\n", "            analysis_result = analyze_text(row[\"laws_title\"] if \"laws_title\" in row else \"\", clean_text)\n", "            \n", "            # 创建目标文档\n", "            target_doc = {\n", "                # 基础字段\n", "                \"lid\": row[\"lid\"] if \"lid\" in row else None,\n", "                \"laws_title\": row[\"laws_title\"] if \"laws_title\" in row else \"\",\n", "                \"laws_text\": clean_text,\n", "                \"laws_text_search\": row[\"laws_text_search\"] if \"laws_text_search\" in row else \"\",\n", "                \"laws_url\": row[\"laws_url\"] if \"laws_url\" in row else \"\",\n", "                \"laws_site_name\": row[\"laws_site_name\"] if \"laws_site_name\" in row else \"\",\n", "                \"laws_type\": row[\"laws_type\"] if \"laws_type\" in row else \"\",\n", "                \"laws_level\": row[\"laws_level\"] if \"laws_level\" in row else \"\",\n", "                \"laws_level_two\": row[\"laws_level_two\"] if \"laws_level_two\" in row else \"\",\n", "                \"laws_level_file\": row[\"laws_level_file\"] if \"laws_level_file\" in row else \"\",\n", "                \"laws_formulate_authority\": row[\"laws_formulate_authority\"] if \"laws_formulate_authority\" in row else \"\",\n", "                \"laws_approving_authority\": row[\"laws_approving_authority\"] if \"laws_approving_authority\" in row else \"\",\n", "                \"laws_formulate_way\": row[\"laws_formulate_way\"] if \"laws_formulate_way\" in row else \"\",\n", "                \"laws_object\": row[\"laws_object\"] if \"laws_object\" in row else 0,\n", "                \"laws_code\": row[\"laws_code\"] if \"laws_code\" in row else \"\",\n", "                \"laws_ageing\": row[\"laws_ageing\"] if \"laws_ageing\" in row else \"\",\n", "                # 时间相关字段\n", "                \"laws_written_time\": format_date(row.get(\"laws_written_time\")),\n", "                \"laws_pass_time\": format_date(row.get(\"laws_pass_time\")),\n", "                \"laws_public_time\": format_date(row.get(\"laws_public_time\")),\n", "                \"laws_excute_time\": format_date(row.get(\"laws_excute_time\")),\n", "                \"laws_expiry_time\": format_date(row.get(\"laws_expiry_time\")),\n", "                \"laws_clean_time\": format_date(row.get(\"laws_clean_time\")),\n", "                \"laws_change_pass_time\": format_date(row.get(\"laws_change_pass_time\")),\n", "                \"laws_change_public_time\": format_date(row.get(\"laws_change_public_time\")),\n", "                \"laws_change_excute_time\": format_date(row.get(\"laws_change_excute_time\")),\n", "                \"laws_crawler_time\": format_date(row.get(\"laws_crawler_time\")),\n", "                \"laws_update_time\": format_date(row.get(\"laws_update_time\")),\n", "                # 区域相关字段\n", "                \"laws_area\": row[\"laws_area\"] if \"laws_area\" in row else \"\",\n", "                \"laws_area_code\": row[\"laws_area_code\"] if \"laws_area_code\" in row else 0,\n", "                \"laws_area_address\": row[\"laws_area_address\"] if \"laws_area_address\" in row else \"\",\n", "                # 文档相关字段\n", "                \"laws_doc_url\": row[\"laws_doc_url\"] if \"laws_doc_url\" in row else \"\",\n", "                \"laws_doc_local_url\": row[\"laws_doc_local_url\"] if \"laws_doc_local_url\" in row else \"\",\n", "                \"laws_relevant_docs\": row[\"laws_relevant_docs\"] if \"laws_relevant_docs\" in row else \"\",\n", "                \"laws_relevant_datas\": row[\"laws_relevant_datas\"] if \"laws_relevant_datas\" in row else \"\",\n", "                \"laws_brief\": row[\"laws_brief\"] if \"laws_brief\" in row else \"\",\n", "                \"laws_text_from\": row[\"laws_text_from\"] if \"laws_text_from\" in row else 0,\n", "                \"law_doc_is_download\": row[\"law_doc_is_download\"] if \"law_doc_is_download\" in row else 0,\n", "                # LLM分析的字段（全部一级）\n", "                \"policy_theme\": analysis_result.get(\"policy_theme\", []),\n", "                \"affected_industries\": analysis_result.get(\"affected_industries\", []),\n", "                \"policy_impact_regulatory_impact\": analysis_result.get(\"policy_impact_regulatory_impact\", \"\"),\n", "                \"policy_impact_compliance_requirements\": analysis_result.get(\"policy_impact_compliance_requirements\", \"\"),\n", "                \"policy_impact_risk_level\": analysis_result.get(\"policy_impact_risk_level\", \"中\"),\n", "                \"policy_direction_trend\": analysis_result.get(\"policy_direction_trend\", \"\"),\n", "                \"policy_direction_key_points\": analysis_result.get(\"policy_direction_key_points\", \"\"),\n", "                \"policy_direction_implementation_timeline\": analysis_result.get(\"policy_direction_implementation_timeline\", \"\"),\n", "                \"market_impact_industry_effect\": analysis_result.get(\"market_impact_industry_effect\", \"\"),\n", "                \"market_impact_business_opportunities\": analysis_result.get(\"market_impact_business_opportunities\", \"\"),\n", "                \"market_impact_market_risk\": analysis_result.get(\"market_impact_market_risk\", \"\"),\n", "                \"area\": analysis_result.get(\"area\", []),\n", "                \"summary\": analysis_result.get(\"summary\", \"\"),\n", "                \"related_laws\": analysis_result.get(\"related_laws\", []),\n", "                # 向量字段\n", "                \"embedding\": get_embedding(analysis_result.get(\"summary\", \"\"), embedding_config)\n", "            }\n", "\n", "            try:\n", "                es.index(\n", "                    index=TARGET_ES_INDEX,\n", "                    id=_id,\n", "                    body=target_doc\n", "                )\n", "                print(f'插入：{_id}')\n", "                processed_count += 1\n", "            except Exception as e:\n", "                traceback.print_exc()\n", "                logger.error(f\"保存文档 {row['_id'] if '_id' in row else 'unknown'} 时出错: {str(e)}\")\n", "\n", "        logger.info(f\"处理文档: {processed_count}\")\n", "        return processed_count\n", "\n", "    except Exception as e:\n", "        traceback.print_exc()\n", "        logger.error(f\"处理数据时出错: {str(e)}\")\n", "    finally:\n", "        if es:\n", "            try:\n", "                es.close()\n", "                logger.debug(\"ES客户端已关闭\")\n", "            except Exception as close_error:\n", "                traceback.print_exc()\n", "                logger.warning(f\"关闭ES客户端时出错: {str(close_error)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# df = pd.DataFrame([item['_source'] for item in es_data])\n", "# print(f\"获取到{len(df)}条数据\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# a = df.loc[0]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# a[\"laws_public_time\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# process_data(df.loc[2:])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["# result['processed_docs'][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# result['current_docs'][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class AgentState(TypedDict):\n", "    \"\"\"代理的状态\"\"\"\n", "    current_docs: List[Dict] # 查询数据\n", "    start_time: Optional[str] # 循环取数的参考ID\n", "    end_time: Optional[str] # 循环取数的参考ID\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义工作流的三个节点函数\n", "def fetch_data(state):\n", "    body = build_body(start, end)\n", "    es_data = get_es_scroll_data('wzty_laws_database_alias', body)\n", "    state[\"current_docs\"] = es_data\n", "    return state\n", "\n", "def process_batch(state):\n", "    # 使用您已有的process_data函数处理数据\n", "    # ...\n", "    sum0dcl = process_data(state[\"current_docs\"])\n", "    print(sum0dcl)\n", "    \n", "    return state\n", "\n", "\n", "\n", "# 创建工作流\n", "workflow = StateGraph(AgentState)\n", "\n", "# 添加节点\n", "workflow.add_node(\"fetch_data\", fetch_data)\n", "workflow.add_node(\"process_batch\", process_batch)\n", "\n", "# 连接节点\n", "workflow.add_edge(\"fetch_data\", \"process_batch\")\n", "workflow.add_edge(\"process_batch\", END)\n", "\n", "\n", "# 设置入口节点\n", "workflow.set_entry_point(\"fetch_data\")\n", "\n", "# 编译工作流\n", "workflow = workflow.compile()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 循环执行智能体 处理数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 示例用法\n", "start, end = get_day_range()  # 传空，自动用当前日期\n", "print(\"开始时间:\", start)\n", "print(\"结束时间:\", end)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "initial_state = {\n", "    \"current_docs\": [],\n", "    \"start_time\": start,\n", "    \"end_time\": end\n", "}\n", "result = await workflow.ainvoke(initial_state)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-22 06:10:36,935 - elasticsearch - INFO - POST http://************:9200/wzty_laws_database_alias/_search?scroll=10m&size=1000 [status:200 request:0.133s]\n", "2025-05-22 06:10:36,947 - elasticsearch - INFO - POST http://************:9200/_search/scroll?scroll=10m [status:200 request:0.007s]\n", "2025-05-22 06:10:36,953 - elasticsearch - INFO - DELETE http://************:9200/_search/scroll [status:200 request:0.006s]\n", "2025-05-22 06:10:36,956 - elasticsearch - INFO - HEAD http://**************:9600/ [status:200 request:0.001s]\n", "2025-05-22 06:10:36,957 - elasticsearch - INFO - GET http://**************:9600/ [status:200 request:0.001s]\n", "2025-05-22 06:10:36,958 - __main__ - INFO - ES版本: 7.9.1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["开始时间: 2025-01-27 00:00:00\n", "结束时间: 2025-01-27 23:59:59\n", "36\n", "ES连接成功: **************:9600\n"]}], "source": ["sum = 100\n", "now_index = 0\n", "while  now_index < sum:\n", "    start, end = get_day_range(end)\n", "    print(\"开始时间:\", start)\n", "    print(\"结束时间:\", end)\n", "    initial_state = {\n", "        \"current_docs\": [],\n", "        \"start_time\": start,\n", "        \"end_time\": end\n", "    }\n", "    result = await workflow.ainvoke(initial_state)\n", "    now_index += 1\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# body = build_body(\"2020-01-01\", \"2025-5-19\")\n", "# es_data = get_es_scroll_data('wzty_laws_database_alias', body)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["测试es"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["   # 测试ES连接\n", "  # 测试ES连接\n", "from elasticsearch import Elasticsearch\n", "\n", "try:\n", "    hosts = '************'\n", "    port = 9200\n", "    http_auth = ('qianxiang', 'aRQ8K$3ymIbG')\n", "    \n", "    print(f\"尝试连接到 {hosts}:{port}\")\n", "    # 设置较短的超时时间进行测试\n", "    es_test = Elasticsearch(hosts, http_auth=http_auth, port=port, timeout=10)\n", "    \n", "    if es_test.ping():\n", "        print(\"✅ 连接成功!\")\n", "        print(f\"ES信息: {es_test.info()}\")\n", "    else:\n", "        print(\"❌ 连接失败，但无超时错误\")\n", "except Exception as e:\n", "    print(f\"❌ 连接错误: {str(e)}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 2}