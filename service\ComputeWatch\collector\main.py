from fastapi import FastAP<PERSON>, HTTPException
from motor.motor_asyncio import AsyncIOMotorClient
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
import asyncio
import os
from datetime import datetime
from typing import Optional, List, Dict
import aiohttp
from pydantic import BaseModel
from dotenv import load_dotenv
from db import Database
from models import ServerInfo, MetricsRecord
from logger import get_logger, setup_logging
setup_logging()
# 加载环境变量
load_dotenv()
logger = get_logger(__name__)

app = FastAPI()
scheduler = AsyncIOScheduler()
db = Database()

def create_response(success: bool, data: Optional[Dict] = None, message: str = "", error: str = None) -> Dict:
    """创建统一的响应格式"""
    if not success:
        logger.error(f"API错误: {error}")
    else:
        logger.info(f"API响应: {message}")
    return {
        "success": success,
        "message": message,
        "data": data,
        "error": error
    }

class ServerConfig(BaseModel):
    """服务器配置"""
    name: str  # 服务器名称
    hostname: str  # 主机名
    port: int = 8000  # 端口
    api_key: str  # API密钥
    collect_interval: int = 60  # 采集间隔(秒)
    cron_expression: Optional[str] = None  # Cron表达式
    description: Optional[str] = None  # 描述
    enabled: bool = True  # 是否启用

class CollectorTask:
    def __init__(self, server_config: ServerConfig):
        self.config = server_config
        self.api_base = f"http://{server_config.hostname}:{server_config.port}"
        self.headers = {"api-key": server_config.api_key}

    async def fetch_data(self, endpoint: str) -> Dict:
        """从API获取数据"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.api_base}{endpoint}", headers=self.headers) as response:
                data = await response.json()
                if not data["success"]:
                    raise Exception(data["error"])
                return data["data"]

    async def collect_and_store(self):
        """收集并存储监控数据"""
        try:
            # 获取系统信息
            system_info = await self.fetch_data("/system/info")
            gpu_info = None
            npu_info = None
            
            try:
                gpu_info = await self.fetch_data("/gpu/info")
            except:
                logger.warning(f"获取GPU信息失败: {self.config.name}")
            
            try:
                npu_info = await self.fetch_data("/npu/info")
            except:
                logger.warning(f"获取NPU信息失败: {self.config.name}")

            # 更新服务器信息
            server_info = ServerInfo(
                server_id=self.config.name,
                hostname=self.config.hostname,
                port=self.config.port,
                api_key=self.config.api_key,
                collect_interval=self.config.collect_interval,
                cron_expression=self.config.cron_expression,
                description=self.config.description,
                enabled=self.config.enabled,
                **system_info,
                gpu_info=gpu_info,
                npu_info=npu_info,
                last_update=datetime.now()
            )
            await db.upsert_server_info(server_info)

            # 获取监控指标
            system_metrics = await self.fetch_data("/system/metrics")
            gpu_metrics = None
            npu_metrics = None
            
            try:
                gpu_metrics = await self.fetch_data("/gpu/metrics")
            except:
                logger.warning(f"获取GPU指标失败: {self.config.name}")
            
            try:
                npu_metrics = await self.fetch_data("/npu/metrics")
            except:
                logger.warning(f"获取NPU指标失败: {self.config.name}")

            # 存储监控记录
            metrics = MetricsRecord(
                server_id=self.config.name,
                timestamp=datetime.now(),
                cpu_metrics=system_metrics["cpu"],
                memory_metrics=system_metrics["memory"],
                disk_metrics=system_metrics["disk"],
                network_metrics=system_metrics["network"],
                gpu_metrics=gpu_metrics,
                npu_metrics=npu_metrics
            )
            await db.insert_metrics(metrics)
            logger.info(f"服务器 {self.config.name} 监控数据采集成功")

        except Exception as e:
            logger.error(f"服务器 {self.config.name} 监控数据采集失败: {str(e)}")
            # raise

async def schedule_collector_task(config: ServerConfig, collector: CollectorTask):
    """调度采集任务"""
    job_id = config.name
    
    # 移除现有任务（如果存在）
    try:
        scheduler.remove_job(job_id)
    except:
        pass
    
    if not config.enabled:
        logger.info(f"服务器 {config.name} 采集任务已禁用")
        return
    
    # 根据配置创建触发器
    if config.cron_expression:
        trigger = CronTrigger.from_crontab(config.cron_expression)
        logger.info(f"服务器 {config.name} 使用Cron表达式调度: {config.cron_expression}")
    else:
        trigger = IntervalTrigger(seconds=config.collect_interval)
        logger.info(f"服务器 {config.name} 使用间隔调度: {config.collect_interval}秒")
    
    # 添加新任务
    scheduler.add_job(
        collector.collect_and_store,
        trigger=trigger,
        id=job_id,
        replace_existing=True
    )

async def reload_collectors():
    """重新加载所有采集任务"""
    logger.info("\n" + "="*50)
    logger.info("开始重新加载采集任务...")
    logger.info("="*50)
    
    # 清除所有现有任务
    scheduler.remove_all_jobs()
    
    # 从数据库加载服务器配置
    cursor = db.server_info.find()
    loaded_count = 0
    enabled_count = 0
    disabled_count = 0
    
    async for server in cursor:
        loaded_count += 1
        logger.info(f"\n加载服务器 [{server['server_id']}]:")
        logger.info(f"  主机: {server['hostname']}:{server.get('port', 8000)}")
        logger.info(f"  描述: {server.get('description', '无')}")
        
        if server.get("enabled", True):
            enabled_count += 1
            config = ServerConfig(
                name=server["server_id"],
                hostname=server["hostname"],
                port=server.get("port", 8000),
                api_key=server.get("api_key", ""),
                collect_interval=server.get("collect_interval", 60),
                cron_expression=server.get("cron_expression"),
                description=server.get("description", ""),
                enabled=True
            )
            collector = CollectorTask(config)
            await schedule_collector_task(config, collector)
            
            # 打印调度信息
            if config.cron_expression:
                logger.info(f"  调度: Cron表达式 [{config.cron_expression}]")
            else:
                logger.info(f"  调度: 固定间隔 [{config.collect_interval}秒]")
            logger.info("  状态: 已启用 ✓")
        else:
            disabled_count += 1
            logger.info("  状态: 已禁用 ✗")
    
    # 打印加载统计
    logger.info("\n" + "="*50)
    logger.info("采集任务加载完成:")
    logger.info(f"  总任务数: {loaded_count}")
    logger.info(f"  已启用: {enabled_count}")
    logger.info(f"  已禁用: {disabled_count}")
    logger.info("="*50)
    
    return {
        "total": loaded_count,
        "enabled": enabled_count,
        "disabled": disabled_count
    }

@app.post("/servers")
async def add_server(config: ServerConfig):
    """添加服务器"""
    try:
        # 检查server_id是否已存在
        existing_server = await db.server_info.find_one({"server_id": config.name})
        if existing_server:
            raise HTTPException(
                status_code=409,  # 409 Conflict
                detail={
                    "message": "服务器ID已存在",
                    "server_id": config.name,
                    "existing_server": {
                        "hostname": existing_server["hostname"],
                        "description": existing_server.get("description", "无"),
                        "last_update": existing_server.get("last_update")
                    }
                }
            )

        # 测试服务器连接
        try:
            collector = CollectorTask(config)
            system_info = await collector.fetch_data("/system/info")
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail={
                    "message": "服务器连接测试失败",
                    "server_id": config.name,
                    "hostname": config.hostname,
                    "error": str(e)
                }
            )
        
        # 构建服务器信息
        server_info = ServerInfo(
            server_id=config.name,
            hostname=config.hostname,
            port=config.port,
            api_key=config.api_key,
            collect_interval=config.collect_interval,
            cron_expression=config.cron_expression,
            description=config.description,
            enabled=config.enabled,
            **system_info
        )
        
        # 保存到数据库
        await db.upsert_server_info(server_info)
        
        # 添加采集任务
        await schedule_collector_task(config, collector)
        
        # 返回成功信息
        return create_response(
            success=True,
            message="服务器添加成功",
            data={
                "server_id": config.name,
                "details": {
                    "hostname": config.hostname,
                    "port": config.port,
                    "description": config.description,
                    "schedule": config.cron_expression or f"每{config.collect_interval}秒",
                    "enabled": config.enabled
                }
            }
        )
    except HTTPException as he:
        return create_response(success=False, error=str(he.detail))
    except Exception as e:
        return create_response(success=False, error=str(e))

@app.put("/servers/{server_id}")
async def update_server(server_id: str, config: ServerConfig):
    """更新服务器配置"""
    try:
        server = await db.server_info.find_one({"server_id": server_id})
        if not server:
            raise HTTPException(status_code=404, detail="服务器不存在")
        
        collector = CollectorTask(config)
        system_info = await collector.fetch_data("/system/info")
        
        server_info = ServerInfo(
            server_id=server_id,
            hostname=config.hostname,
            port=config.port,
            api_key=config.api_key,
            collect_interval=config.collect_interval,
            cron_expression=config.cron_expression,
            description=config.description,
            enabled=config.enabled,
            **system_info
        )
        
        await db.upsert_server_info(server_info)
        await schedule_collector_task(config, collector)
        
        return create_response(
            success=True,
            message="服务器配置更新成功",
            data={"server_id": server_id}
        )
    except Exception as e:
        return create_response(success=False, error=str(e))

@app.post("/reload")
async def reload_tasks():
    """重新加载所有采集任务"""
    try:
        stats = await reload_collectors()
        return create_response(
            success=True,
            message="采集任务重新加载成功",
            data={"stats": stats}
        )
    except Exception as e:
        return create_response(success=False, error=str(e))

@app.delete("/servers/{server_id}")
async def remove_server(server_id: str):
    """移除服务器"""
    try:
        # 检查服务器是否存在
        server = await db.server_info.find_one({"server_id": server_id})
        if not server:
            return create_response(
                success=False,
                error=f"服务器不存在: {server_id}"
            )

        # 1. 删除服务器信息
        result = await db.server_info.delete_one({"server_id": server_id})
        if not result.deleted_count:
            return create_response(
                success=False,
                error=f"删除服务器信息失败: {server_id}"
            )

        # 2. 删除相关的监控数据
        delete_metrics = await db.server_metrics.delete_many({"server_id": server_id})
        logger.info(f"已删除 {delete_metrics.deleted_count} 条监控记录")

        # 3. 停止并移除采集任务
        try:
            scheduler.remove_job(server_id)
            logger.info(f"已停止采集任务: {server_id}")
        except Exception as e:
            logger.warning(f"移除采集任务失败(可能任务不存在): {str(e)}")

        return create_response(
            success=True,
            message="服务器移除成功",
            data={
                "server_id": server_id,
                "deleted_metrics": delete_metrics.deleted_count
            }
        )
    except Exception as e:
        logger.error(f"删除服务器失败: {str(e)}")
        return create_response(
            success=False,
            error=f"删除服务器失败: {str(e)}"
        )

@app.get("/servers")
async def list_servers():
    """获取所有服务器列表"""
    try:
        cursor = db.server_info.find()
        servers = await cursor.to_list(length=100)
        return create_response(
            success=True,
            message="获取服务器列表成功",
            data={"servers": servers}
        )
    except Exception as e:
        return create_response(success=False, error=str(e))

@app.get("/servers/{server_id}/metrics")
async def get_server_metrics(
    server_id: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100
):
    """获取服务器监控数据"""
    try:
        metrics = await db.get_latest_metrics(server_id, limit)
        return create_response(
            success=True,
            message="获取监控数据成功",
            data={"metrics": metrics}
        )
    except Exception as e:
        return create_response(success=False, error=str(e))

@app.on_event("startup")
async def startup_event():
    """启动时初始化"""
    logger.info("\n" + "="*50)
    logger.info("监控采集服务启动")
    logger.info("="*50)
    
    # 连接数据库
    await db.connect()
    logger.info("数据库连接成功")
    
    # 加载采集任务
    stats = await reload_collectors()
    
    # 启动调度器
    scheduler.start()
    logger.info("\n调度器启动成功")
    
    if stats["enabled"] > 0:
        logger.info(f"开始执行 {stats['enabled']} 个采集任务")
    else:
        logger.warning("没有启用的采集任务")

@app.on_event("shutdown")
async def shutdown_event():
    """关闭时清理"""
    scheduler.shutdown()
    await db.close()
    logger.info("服务关闭")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 