from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.api_tokens import (
    ApiToken,
    ApiTokenCreate,
    ApiTokenUpdate,
    ApiTokenResponse,
    ApiTokenInfo
)
from ..db.mongodb import db
from ..utils.auth import verify_token
from ..api.auth import create_api_token, revoke_api_token, refresh_api_token
from bson import ObjectId
from datetime import datetime

router = APIRouter(
    prefix="/api/api-tokens",
    tags=["api_tokens"]
)

# 创建 API Token
@router.post("/", response_model=Dict[str, Any])
async def create_token(
    token_create: ApiTokenCreate,
    current_user: dict = Depends(verify_token)
):
    try:
        token = await create_api_token(
            app_id=token_create.app_id,
            app_name=token_create.app_name,
            user_id=current_user["id"]
        )
        
        token_doc = await db["api_tokens"].find_one({"token": token})
        return {
            "data": ApiTokenResponse(**token_doc),
            "success": True
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# 获取 API Token 列表
@router.get("/", response_model=Dict[str, Any])
async def get_tokens(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    app_name: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"user_id": current_user["id"]}
    
    if app_name:
        query["app_name"] = {"$regex": app_name, "$options": "i"}
    if is_active is not None:
        query["is_active"] = is_active

    tokens = await db["api_tokens"].find(query).sort(
        "created_at", -1
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    total = await db["api_tokens"].count_documents(query)
    
    return {
        "data": [ApiTokenResponse(**token) for token in tokens],
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取单个 API Token 信息
@router.get("/{token_id}", response_model=ApiTokenResponse)
async def get_token(
    token_id: str,
    current_user: dict = Depends(verify_token)
):
    token = await db["api_tokens"].find_one({
        "_id": ObjectId(token_id),
        "user_id": current_user["id"]
    })
    
    if not token:
        raise HTTPException(status_code=404, detail="API token not found")
    
    return ApiTokenResponse(**token)

# 更新 API Token
@router.put("/{token_id}", response_model=ApiTokenResponse)
async def update_token(
    token_id: str,
    token_update: ApiTokenUpdate,
    current_user: dict = Depends(verify_token)
):
    token = await db["api_tokens"].find_one({
        "_id": ObjectId(token_id),
        "user_id": current_user["id"]
    })
    
    if not token:
        raise HTTPException(status_code=404, detail="API token not found")
    
    update_data = token_update.dict(exclude_unset=True)
    
    result = await db["api_tokens"].update_one(
        {"_id": ObjectId(token_id)},
        {"$set": update_data}
    )
    
    if result.modified_count == 0:
        raise HTTPException(status_code=400, detail="Update failed")
        
    updated_token = await db["api_tokens"].find_one({"_id": ObjectId(token_id)})
    return ApiTokenResponse(**updated_token)

# 撤销 API Token
@router.delete("/{token_id}", response_model=dict)
async def delete_token(
    token_id: str,
    current_user: dict = Depends(verify_token)
):
    token = await db["api_tokens"].find_one({
        "_id": ObjectId(token_id),
        "user_id": current_user["id"]
    })
    
    if not token:
        raise HTTPException(status_code=404, detail="API token not found")
    
    success = await revoke_api_token(token["token"])
    if not success:
        raise HTTPException(status_code=400, detail="Revoke failed")
    
    return {"message": "Token revoked successfully"}

# 刷新 API Token
@router.post("/{token_id}/refresh", response_model=ApiTokenResponse)
async def refresh_token(
    token_id: str,
    current_user: dict = Depends(verify_token)
):
    token = await db["api_tokens"].find_one({
        "_id": ObjectId(token_id),
        "user_id": current_user["id"]
    })
    
    if not token:
        raise HTTPException(status_code=404, detail="API token not found")
    
    success = await refresh_api_token(token["token"])
    if not success:
        raise HTTPException(status_code=400, detail="Refresh failed")
    
    refreshed_token = await db["api_tokens"].find_one({"_id": ObjectId(token_id)})
    return ApiTokenResponse(**refreshed_token)

# 获取 Token 使用统计
@router.get("/{token_id}/stats", response_model=Dict[str, Any])
async def get_token_stats(
    token_id: str,
    current_user: dict = Depends(verify_token)
):
    token = await db["api_tokens"].find_one({
        "_id": ObjectId(token_id),
        "user_id": current_user["id"]
    })
    
    if not token:
        raise HTTPException(status_code=404, detail="API token not found")
    
    return {
        "use_count": token.get("use_count", 0),
        "last_used_at": token.get("last_used_at"),
        "is_active": token.get("is_active", True),
        "success": True
    } 