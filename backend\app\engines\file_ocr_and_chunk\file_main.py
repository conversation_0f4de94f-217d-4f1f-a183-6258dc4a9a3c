import os
import sys
backend_dir = "/data/jinxu/RAG/wiseAgent_git/wiseagent/backend"
sys.path.insert(0, backend_dir)

from loguru import logger
import traceback
import os
import json
from typing import List, Dict, Any
from elasticsearch import Elasticsearch
from pymongo import MongoClient
from bson import ObjectId
import requests
import concurrent.futures
from file_index_build import build_chunk_index
from apscheduler.schedulers.background import BackgroundScheduler
from dotenv import load_dotenv
from file_process import file_analyze_to_markdown, file_convert_to_markdown, markdown_to_chunk_index, audio_processing
from enums import chunk_type
from enums import FileStatus,FileStorageType
import tiktoken
import asyncio
import threading
from datetime import datetime
from app.engines.wisegraph.example import demo_graph_building
enc = tiktoken.encoding_for_model(os.getenv("TOKENS_COUNT_MODEL", "gpt-4"))


class BaseIndexer():
    def __init__(self):
        self.client = None
        self.db = None
        # 添加知识库缓存字典
        self.knowledge_base_cache = {}
        # 加载环境变量
        load_dotenv()
    
    def connect(self):
        """建立MongoDB连接"""
        try:
            host = os.getenv('MONGODB_HOST', 'localhost')
            # print("=======>MONGODB_HOST:", host)
            self.client = MongoClient(
                host=os.getenv('MONGODB_HOST', 'localhost'),
                port=int(os.getenv('MONGODB_PORT', 27017)),
                username=os.getenv('MONGODB_USER'),
                password=os.getenv('MONGODB_PASSWORD'),
                authSource=os.getenv('MONGODB_AUTH_SOURCE', 'admin'),
                serverSelectionTimeoutMS=5000
            )
            self.db = self.client[os.getenv('DATABASE_NAME', 'file_processing_db')]
            logger.info("成功连接到MongoDB数据库")
            es_host = os.getenv('ES_HOST', '[]')
            try:
                es_host = es_host.replace("'", '"')
                es_host = json.loads(es_host)
                # 为每个主机添加 scheme
                for host in es_host:
                    host.setdefault('scheme', 'http')  # 默认使用 http
            except json.JSONDecodeError as e:
                logger.error(f"解析 ES_HOST 失败: {str(e)}")
                raise
            logger.info(f"es_host: {es_host}")
            self.es = Elasticsearch(es_host, request_timeout=3600)
            self.ES_INDEX = os.getenv('ES_CHUNK_INDEX_NAME','wise_agent_chunk_index')
            self.ES_HOST = os.getenv('ES_HOST', '[]')
            # print("=======>ES_INDEX:", self.ES_INDEX)
            local_data_path = os.getenv('LOCAL_DATA_PATH')
            if not os.path.exists(local_data_path):
                os.makedirs(local_data_path)
        except Exception as e:
            traceback.print_exc()
            logger.error(f"数据库连接失败: {str(e)}")
            raise
        
    def disconnect(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close()
            logger.info("已关闭数据库连接")
        # if self.minio_client:
        #     self.minio_client.close()
        #     logger.info("已关闭MinIO连接")
        
    def get_files_to_process(self):
            """从source_files集合获取状态为WAITING的前10个文件，按时间排序"""
            try:
                collection = self.db['source_files']
                return list(collection.find(
                    {
                        "flg": 0,
                        "storage_type": "minio"
                    },  # 查询条件
                    sort=[("upload_time", 1)],          # 按上传时间升序排列
                    limit=10                            # 限制10条结果
                ))
            except Exception as e:
                traceback.print_exc()
                logger.error(f"查询待处理文件失败: {str(e)}")
                return []
            
    def _get_knowledge_base(self, knowledge_base_id):
        """获取知识库信息（带缓存机制）"""
        if knowledge_base_id in self.knowledge_base_cache:
            logger.debug(f"从缓存获取知识库信息: {knowledge_base_id}")
            return self.knowledge_base_cache[knowledge_base_id]
            
        try:
            collection = self.db['knowledge_bases']
            kb_info = collection.find_one({"_id": ObjectId(knowledge_base_id)})
            if not kb_info:
                raise ValueError(f"知识库 {knowledge_base_id} 不存在")
                
            self.knowledge_base_cache[knowledge_base_id] = kb_info
            return kb_info
        except Exception as e:
            traceback.print_exc()
            logger.error(f"获取知识库信息失败: {str(e)}")
            raise
        
    def save_markdown_to_file(self, markdown_text, file_name):
            """
            将markdown文本保存到文件
            
            Args:
                markdown_text: markdown格式的文本内容
                file_name: 原始文件名
            
            Returns:
                保存的文件路径
            """
            try:
                # 获取文件名（不含扩展名）
                base_name = os.path.splitext(os.path.basename(file_name))[0]
                # 创建保存目录
                save_dir = os.path.join(os.getenv('LOCAL_DATA_PATH', './data'))
                os.makedirs(save_dir, exist_ok=True)
                
                # 构建保存路径
                save_path = os.path.join(save_dir, f"{base_name}.md")
                
                # 写入文件
                with open(save_path, 'w', encoding='utf-8') as f:
                    f.write(markdown_text)
                
                logger.info(f"已将Markdown内容保存到: {save_path}")
                return save_path
            except Exception as e:
                logger.error(f"保存Markdown文件失败: {str(e)}")
                return None
        
    def process_basic_index(self, file_path, file_data, kb_info):
        """处理基础索引（子类必须实现）"""
        logger.info(f"处理基础索引: {file_data}")
        logger.info(f"kb_info: {kb_info}")
        logger.info(f"file_path: {file_path}")

        # 第一步，先判断文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return
        
        # 第二步 获取文件类型 file_data.data_type
        
        ocr_enable = file_data.get('ocr',0)
        chunk_index_list = []
        
        # 获取文件类型
        filename = os.path.basename(file_path)
        file_type = filename.rsplit('.', 1)[1].lower()
        print("===>file_type:", file_type)
        
        # 处理音频
        if file_type in ['mp3', 'wav']:
            status, text = audio_processing(file_path)
            # print("text:", text)
            if status:
                markdown_text = text
                
            else:
                markdown_text = ""
                
            if len(markdown_text) > 10:
                split_url = os.getenv("SPLIT_API")
                split_data = {"text": markdown_text}
                response = requests.post(split_url, json=split_data)
                if response.status_code==200:
                    chunk_index_list = response.json()['result']
                    # print(f"======>chunk_index_list: {chunk_index_list}")
                    for chunk_index in chunk_index_list:
                        chunk_index['tokens_count'] = len(enc.encode(chunk_index['answer']))
                        chunk_index['chunk_type'] = chunk_type.BASE
                    # chunk_index_list = markdown_to_chunk_index(markdown_text)
                else:
                    raise ValueError("调用文档切块接口报错")
            else:
                raise ValueError("文件内容太少，无法进行索引")
            self.save_markdown_to_file(markdown_text, file_path)
                
        # 处理图片
        elif file_type in ['jpg', 'png', 'jpeg']:
            # 默认使用ocr
            ocr_enable == 1
            # 获取文件的ocr结果
            markdown_text = file_analyze_to_markdown(file_path)
            # print("===markdown_text:", markdown_text)
            if len(markdown_text) > 10:
                split_url = os.getenv("SPLIT_API")
                split_data = {"text": markdown_text}
                response = requests.post(split_url, json=split_data)
                if response.status_code==200:
                    chunk_index_list = response.json()['result']
                    # print(f"======>chunk_index_list: {chunk_index_list}")
                    for chunk_index in chunk_index_list:
                        chunk_index['tokens_count'] = len(enc.encode(chunk_index['answer']))
                        chunk_index['chunk_type'] = chunk_type.BASE
                    # chunk_index_list = markdown_to_chunk_index(markdown_text)
                    # print(f"======>chunk_index_list: {chunk_index_list}")
                else:
                    raise ValueError("调用文档切块接口报错")
            else:
                raise ValueError("文件内容太少，无法进行索引")
            self.save_markdown_to_file(markdown_text, file_path)
            
        else: 
            if ocr_enable == 1: 
                logger.info("ocr_enable == 1")
                # 获取文件的ocr结果
                markdown_text = file_analyze_to_markdown(file_path)
                if len(markdown_text) > 10:
                    split_url = os.getenv("SPLIT_API")
                    split_data = {"text": markdown_text}
                    response = requests.post(split_url, json=split_data)
                    if response.status_code==200:
                        chunk_index_list = response.json()['result']
                        # print(f"======>chunk_index_list: {chunk_index_list}")
                        for chunk_index in chunk_index_list:
                            chunk_index['tokens_count'] = len(enc.encode(chunk_index['answer']))
                            chunk_index['chunk_type'] = chunk_type.BASE
                        # chunk_index_list = markdown_to_chunk_index(markdown_text)
                    else:
                        raise ValueError("调用文档切块接口报错")
                else:
                    raise ValueError("文件内容太少，无法进行索引")
                self.save_markdown_to_file(markdown_text, file_path)

            else:
                markdown_text = file_convert_to_markdown(file_path, file_data)
                logger.info(f"markdown_text: {markdown_text}")
                if len(markdown_text) > 10:
                    chunk_index_list = markdown_to_chunk_index(markdown_text)
                    # logger.info(f"chunk_index_list: {chunk_index_list}")
                else:
                    raise ValueError("文件内容太少，无法进行索引")
            
        return chunk_index_list

    def process_file(self, file_data):
        """处理文件的统一入口方法"""
        try:
            logger.info(f"处理 file_data: {file_data}")
            # 获取知识库ID
            kb_id = file_data.get('knowledge_base_id')
            if not kb_id:
                raise ValueError("文件缺少知识库ID")
                
            # 获取知识库信息
            kb_info = self._get_knowledge_base(kb_id)

            local_data_path = os.getenv('LOCAL_DATA_PATH')
            file_path = os.path.join(local_data_path,str(kb_info.get('_id') ), file_data.get('name'))
            
            # 新增文件下载逻辑
            storage_path = file_data.get('storage_path')
            if not storage_path:
                raise ValueError("文件缺少storage_path字段")
                
            try:
                # 判断文件是否存在
                if os.path.exists(file_path):
                    logger.info(f"文件已存在: {file_path}")
                else:
                    # 创建目录（如果不存在）
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)
                    
                    # 下载文件到本地
                    response = requests.get(storage_path, timeout=30)
                    response.raise_for_status()
                    
                    with open(file_path, 'wb') as f:
                        f.write(response.content)
                    logger.info(f"文件下载成功: {storage_path} -> {file_path}")
                
            except Exception as e:
                traceback.print_exc()
                logger.error(f"文件下载失败: {str(e)}")
                raise

            # 并行处理不同索引类型
            with concurrent.futures.ThreadPoolExecutor() as executor:
                futures = []
                
                if kb_info.get('basic_index'):
                    futures.append(executor.submit(
                        self.process_basic_index, 
                        file_path,
                        file_data, 
                        kb_info
                    ))
                # if kb_info.get('graph_index'):
                #     futures.append(executor.submit(
                #         self.process_graph_index,
                #         file_path,
                #         file_data,
                #         kb_info
                #     ))
                # if kb_info.get('semantic_index'):
                #     futures.append(executor.submit(
                #         self.process_semantic_index,
                #         file_path,
                #         file_data,
                #         kb_info
                #     ))

                # 等待所有任务完成并处理异常
                for future in concurrent.futures.as_completed(futures):
                    try:
                        all_chunk_index_list = future.result()
                        # print("===all_chunk_index_list:", all_chunk_index_list)
                        index_count = build_chunk_index(file_data,kb_info,all_chunk_index_list,self.db,self.ES_HOST,self.ES_INDEX)
                        chunk_count = len(all_chunk_index_list)
                        self.db['knowledge_bases'].update_one(
                            {"_id": ObjectId(kb_id)},
                            {"$inc": {"index_count": index_count, "chunk_count": chunk_count}}
                        )
                        logger.info(f"索引处理成功: {file_data.get('_id')} 索引数量: {index_count} 文本块数量: {chunk_count}")
                    except Exception as e:
                        traceback.print_exc()
                        logger.error(f"索引处理失败: {str(e)}")
                        raise
                        
        except Exception as e:
            traceback.print_exc()
            logger.error(f"处理文件 {file_data.get('_id')} 失败: {str(e)}")
            raise
        
    def execute(self):
            """执行完整的处理流程"""
            try:
                # 建立连接
                self.connect()
                
                # 获取待处理文件
                files = self.get_files_to_process()
                logger.info(f"共找到 {len(files)} 个待处理文件")
                if len(files) == 0:
                    logger.info("没有待处理文件，任务结束")
                    return
                
                # 处理每个文件
                for file in files:
                    try:
                        self.db['source_files'].update_one({"_id": file.get('_id')}, {"$set": {"flg": FileStatus.PROCESSING.value}})
                        self.process_file(file)
                        self.db['source_files'].update_one({"_id": file.get('_id')}, {"$set": {"flg": FileStatus.COMPLETED.value}})
                        logger.info(f"已处理文件: {file.get('filename', '未知文件')}")

                        # 检查知识库是否完成，触发图谱构建
                        kb_id = file.get('knowledge_base_id')
                        if kb_id and self.is_knowledge_base_complete(kb_id):
                            self._run_graph_build_async(str(kb_id))

                    except Exception as e:
                        self.db['source_files'].update_one({"_id": file.get('_id')}, {"$set": {"flg": FileStatus.ERROR.value}})
                        traceback.print_exc()
                        logger.error(f"处理文件 {file.get('_id')} 时出错: {str(e)}")
                        continue
                return
                
            except Exception as e:
                logger.error(f"任务执行失败: {str(e)}")
            finally:
                # 确保断开连接
                self.disconnect()

    def is_knowledge_base_complete(self, kb_id):
        """检查知识库是否所有文件都处理完成"""
        try:
            # 查询该知识库是否还有待处理文件
            pending_count = self.db['source_files'].count_documents({
                "knowledge_base_id": kb_id,
                "flg": {"$in": [FileStatus.WAITING.value, FileStatus.PROCESSING.value]}
            })

            # 检查知识库是否启用了图索引
            kb_info = self.db['knowledge_bases'].find_one({"_id": ObjectId(kb_id)})
            has_graph_index = kb_info and kb_info.get('graph_index', False)

            is_complete = pending_count == 0 and has_graph_index
            if is_complete:
                logger.info(f"知识库 {kb_id} 所有文件处理完成，准备构建图谱")

            return is_complete

        except Exception as e:
            logger.error(f"检查知识库完成状态失败: {str(e)}")
            return False

    def _run_graph_build_async(self, kb_id):
        """在新线程中运行异步图谱构建"""
        def target():
            try:
                asyncio.run(self.build_graph_async(kb_id))
            except Exception as e:
                logger.error(f"异步图谱构建线程失败: {str(e)}")
                traceback.print_exc()

        thread = threading.Thread(target=target, name=f"GraphBuild-{kb_id}")
        thread.daemon = True
        thread.start()
        logger.info(f"已启动图谱构建线程: {thread.name}")

    async def build_graph_async(self, kb_id):
        """异步构建图谱"""
        try:
            logger.info(f"开始异步构建知识库 {kb_id} 的图谱")

            # 调用图谱构建
            await demo_graph_building(kb_id)

            # 更新知识库的图谱构建时间
            self.db['knowledge_bases'].update_one(
                {"_id": ObjectId(kb_id)},
                {"$set": {"last_graph_build": datetime.now()}}
            )

            logger.info(f"知识库 {kb_id} 图谱构建完成")

        except Exception as e:
            logger.error(f"知识库 {kb_id} 图谱构建失败: {str(e)}")
            traceback.print_exc()

    def process_graph_index(self, file_path: str, file_data: Dict[str, Any], kb_info: Dict[str, Any]):
        """处理图索引（子类必须实现）"""
        logger.info(f"处理图索引（暂时没实现）: {file_data}")
        pass

    def process_semantic_index(self, file_path: str, file_data: Dict[str, Any], kb_info: Dict[str, Any]):
        """处理语义索引（子类必须实现）"""
        logger.info(f"处理语义索引（暂时没实现）: {file_data}")
        pass
                
                
def main():
    """主程序入口方法"""
    import argparse
    import signal
    
    parser = argparse.ArgumentParser(description='文件处理索引程序')
    parser.add_argument('--immediate', action='store_true', help='立即执行一次处理任务')
    args = parser.parse_args()

    if args.immediate:
        # 立即执行模式不需要调度器
        indexer = BaseIndexer()
        indexer.execute()
    else:
        # 定时任务模式才需要调度器
        scheduler = BackgroundScheduler()
        indexer = BaseIndexer()
        scheduler.add_job(indexer.execute, 'interval', seconds=1)
        scheduler.start()
        print("定时任务已启动，按 Ctrl+C 退出...")
        
        try:
            signal.pause()
        except KeyboardInterrupt:
            scheduler.shutdown()
            print("\n定时任务已安全停止")


if __name__ == '__main__':
    # 运行位置：http://************:8888/terminals/20
    main()