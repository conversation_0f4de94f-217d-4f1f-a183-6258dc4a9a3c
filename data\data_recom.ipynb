{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from __future__ import absolute_import\n", "from __future__ import print_function\n", "from pymongo import MongoClient\n", "from elasticsearch import Elasticsearch\n", "from bson import ObjectId\n", "import redis\n", "import os\n", "import re\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "import json\n", "import math\n", "from time import strftime, localtime,sleep\n", "\n", "import time\n", "import datetime\n", "from dateutil import tz, zoneinfo\n", "import networkx as nx\n", "from operator import itemgetter\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["MONGODB_SETTING = {\n", "    'host':'**************',\n", "    'port':30000,\n", "    'db_name':'roardata_recommenders'\n", "}"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# 对应的表\n", "COL_CONTENT = 'd_content'\n", "COL_CONTENT_PROFILE = 'd_contentProfile'"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["db = MongoClient(host=MONGODB_SETTING['host'], \n", "                                   port=MONGODB_SETTING['port'])[MONGODB_SETTING['db_name']]\n", "es = Elasticsearch([\n", "    {'host':'**************','port':9600},\n", "                    {'host':'**************','port':9600},\n", "                    {'host':'**************','port':9600}\n", "                   ], timeout=3600)\n", "\n", "INDEX_NAME = 'pro_mcp_data_1358' # 推荐数据服务"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}