{"cells": [{"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["import random\n", "from datetime import datetime, timedelta\n", "from bson import ObjectId\n", "import pymongo\n", "import json"]}, {"cell_type": "markdown", "metadata": {}, "source": ["原始备份\n", "```{\n", "    \"_id\" : ObjectId(\"671d7553291a6d3d6beab168\"),\n", "    \"dataset_id\" : ObjectId(\"671cd8d3291a6d3d6beab144\"),\n", "    \"file_id\" : ObjectId(\"671cd8d3291a6d3d6beab201\"),\n", "    \"data\" : \"  {\\n    \\\"typeName\\\": \\\"选择题\\\",\\n    \\\"source\\\": \\\"\\\",\\n    \\\"subjectId\\\": 1,\\n    \\\"parseContent\\\": \\\"<p>此题考查学生对句子及短语知识的理解能力。这要求学生要加强基础知识的掌握，对教材中要求掌握的内容要掌握牢固。本题中，A项中的主干应为：天文学家发现，地球大气会使光线散射（主谓短语作宾语）；B项中的句子只有一层意思，应该是单句；C项中，“神采奕奕”是主谓短语。故选D。​</p>\\\",\\n    \\\"subjectName\\\": \\\"语文\\\",\\n    \\\"optionList\\\": [\\n      {\\n        \\\"correct\\\": false,\\n        \\\"optionNo\\\": \\\"A\\\",\\n        \\\"content\\\": \\\"天文学家早就发现，地球大气会使光线散射。主干：天文学家发现散射。 <p style=\\\"\\\">   </p>\\\"\\n      },\\n      {\\n        \\\"correct\\\": false,\\n        \\\"optionNo\\\": \\\"B\\\",\\n        \\\"content\\\": \\\"假山的堆叠，全在乎设计者和匠师们生平多阅历，胸中有丘壑。 这个句子是个复句。 <p style=\\\"\\\">   </p>\\\"\\n      },\\n      {\\n        \\\"correct\\\": false,\\n        \\\"optionNo\\\": \\\"C\\\",\\n        \\\"content\\\": \\\"字里行间   神采奕奕  我行我素   多姿多彩 都是并列短语。 <p style=\\\"\\\">   </p>\\\"\\n      },\\n      {\\n        \\\"correct\\\": true,\\n        \\\"optionNo\\\": \\\"D\\\",\\n        \\\"content\\\": \\\"喋喋不休 、无所不为、死灰复燃、恃才放旷都是贬义词。\\\"\\n      }\\n    ],\\n    \\\"difficulty\\\": 5,\\n    \\\"answer\\\": \\\"D\\\"\\n  }\",\n", "    \"images\" : [ \n", "        \"/data_images/671d7553291a6d3d6beab168/2024-09-12_00-00-00_0000.png\"\n", "    ],\n", "    \"created_at\" : ISODate(\"2024-09-15T00:00:00.000Z\"),\n", "    \"updated_at\" : ISODate(\"2024-09-15T00:00:00.000Z\")\n", "}```"]}, {"cell_type": "code", "execution_count": 143, "metadata": {}, "outputs": [], "source": ["client = pymongo.MongoClient(\"mongodb://memInterview:<EMAIL>:37017/roardataAiApp_test?authSource=admin\")\n", "db = client[\"roardataAiApp_test\"]\n", "collection = db[\"structured_datasets\"]"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["\n", "# 模拟从JSON文件中读取数据\n", "def load_json(file_path):\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "    return data\n", "\n", "# 组装每条记录的逻辑\n", "def create_new_data_record(data_item, dataset_id, file_id, current_time,images):\n", "    return {\n", "        \"_id\": ObjectId(),  # 生成新的 ObjectId\n", "        \"dataset_id\": ObjectId(dataset_id),  # 使用数据集_id\n", "        \"file_id\": ObjectId(file_id),  # 使用文件_id\n", "        \"data\": str(data_item),  # 从JSON文件中读取的数据项\n", "        \"images\": images,  # 固定图片路径\n", "        \"created_at\": current_time,\n", "        \"updated_at\": current_time,\n", "    }\n", "\n", "\n", "\n", "# 主处理函数，单次处理data_items，直到插入的总量达到max_records\n", "def process_data(file_record, data_items, max_records=80000):\n", "    dataset_id = file_record[\"dataset_id\"]  # 从文件记录中获取 dataset_id\n", "    file_id = file_record[\"file_id\"]  # 从文件记录中获取 file_id\n", "    newtime = file_record[\"created_at\"]  # 初始时间\n", "    images = file_record[\"images\"]  # 初始时间\n", "    created_data_records = []  # 用于存储新生成的记录\n", "    current_time = newtime\n", "\n", "    # 模拟数据库插入操作，可以替换成你实际的数据库操作\n", "    collectionstructured_data_records = db[\"structured_data_records\"]\n", "\n", "    total_inserted = 0  # 总插入记录数\n", "    while total_inserted < max_records:\n", "        \n", "        for data_item in data_items:\n", "            if total_inserted >= max_records:\n", "                break  # 插入数达到最大数量则停止\n", "            \n", "            # 创建新的记录\n", "            new_record = create_new_data_record(data_item, dataset_id, file_id, current_time,images)\n", "            \n", "            # 模拟插入数据库操作\n", "            collectionstructured_data_records.insert_many([new_record])\n", "            \n", "            # 保存到列表中（可以实际替换成数据库操作）\n", "            created_data_records.append(new_record)\n", "            total_inserted += 1\n", "\n", "            # 打印或记录插入的进度\n", "            if total_inserted % 1000 == 0:  # 每1000条记录输出一次进度\n", "                print(f\"{total_inserted} records inserted.\")\n", "\n", "        # 每次完整循环完 data_items 后增加1分钟的时间\n", "        print(\"total_inserted:\",total_inserted)\n", "        current_time += <PERSON><PERSON>ta(seconds=1)\n", "\n", "    return created_data_records\n", "\n", "\n", "# 将生成的记录写入文件或保存到数据库\n", "def save_to_json(output_path, data):\n", "    def convert_to_serializable(obj):\n", "        if isinstance(obj, ObjectId):\n", "            return str(obj)\n", "        if isinstance(obj, datetime):\n", "            return obj.isoformat()\n", "        return obj\n", "    \n", "    with open(output_path, 'w', encoding='utf-8') as f:\n", "        json.dump(data, f, default=convert_to_serializable, ensure_ascii=False, indent=4)\n", "\n"]}, {"cell_type": "code", "execution_count": 109, "metadata": {}, "outputs": [], "source": ["def load_jsonl(file_path):\n", "    data = []\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        for line in f:\n", "            if line.strip():  # 跳过空行\n", "                try:\n", "                    item = json.loads(line.strip())\n", "                    data.append(item)\n", "                except json.JSONDecodeError as e:\n", "                    print(f\"解析错误: {e}\")\n", "                    continue\n", "    return data"]}, {"cell_type": "code", "execution_count": 141, "metadata": {}, "outputs": [], "source": ["file_obj ={\n", "    \"_id\" : ObjectId(\"671de511ff6d44912446e277\"),\n", "    \"name\" : \"图书杂志.csv\",\n", "    \"storage_path\" : \"/dataset/图书杂志.csv\",\n", "    \"dataset_id\" : ObjectId(\"671de4d0ff6d44912446e214\"),\n", "    \"created_at\" : datetime(2024, 10, 17, 0, 0),\n", "    \"data_type\" : \"CSV\",\n", "    \"dataset_type\" : \"structured\",\n", "    \"processing_status\" : \"completed\",\n", "    \"user_id\" : 1,\n", "    \"user_name\" : \"超级管理员\",\n", "    \"tags\" : [ \n", "        \"文章\"\n", "    ],\n", "    \"row_count\" : 0\n", "}"]}, {"cell_type": "code", "execution_count": 144, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1000 records inserted.\n", "2000 records inserted.\n", "3000 records inserted.\n", "4000 records inserted.\n", "5000 records inserted.\n", "6000 records inserted.\n", "7000 records inserted.\n", "8000 records inserted.\n", "9000 records inserted.\n", "10000 records inserted.\n", "11000 records inserted.\n", "12000 records inserted.\n", "13000 records inserted.\n", "14000 records inserted.\n", "15000 records inserted.\n", "16000 records inserted.\n", "17000 records inserted.\n", "18000 records inserted.\n", "19000 records inserted.\n", "20000 records inserted.\n", "21000 records inserted.\n", "22000 records inserted.\n", "23000 records inserted.\n", "24000 records inserted.\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[144], line 12\u001b[0m\n\u001b[1;32m      3\u001b[0m file_record \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m      4\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfile_id\u001b[39m\u001b[38;5;124m\"\u001b[39m: file_obj[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_id\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[1;32m      5\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdataset_id\u001b[39m\u001b[38;5;124m\"\u001b[39m: file_obj[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdataset_id\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[1;32m      6\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcreated_at\u001b[39m\u001b[38;5;124m\"\u001b[39m: file_obj[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcreated_at\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[1;32m      7\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mimages\u001b[39m\u001b[38;5;124m\"\u001b[39m:[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/data_images/671de4d0ff6d44912446e214/0000.png\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m      8\u001b[0m     }\n\u001b[1;32m      9\u001b[0m \u001b[38;5;66;03m# data_items = load_json(json_file_path)  # 读取的json文件中的数据\u001b[39;00m\n\u001b[1;32m     10\u001b[0m \n\u001b[1;32m     11\u001b[0m \u001b[38;5;66;03m# 生成80000条记录\u001b[39;00m\n\u001b[0;32m---> 12\u001b[0m result_records \u001b[38;5;241m=\u001b[39m \u001b[43mprocess_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfile_record\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdataList\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_records\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m200000\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     14\u001b[0m     \u001b[38;5;66;03m# 保存结果到新文件\u001b[39;00m\n\u001b[1;32m     15\u001b[0m \u001b[38;5;66;03m# output_file_path = 'output_data.json'  # 保存生成的json数据路径\u001b[39;00m\n\u001b[1;32m     16\u001b[0m \u001b[38;5;66;03m# save_to_json(output_file_path, result_records)\u001b[39;00m\n", "Cell \u001b[0;32mIn[45], line 44\u001b[0m, in \u001b[0;36mprocess_data\u001b[0;34m(file_record, data_items, max_records)\u001b[0m\n\u001b[1;32m     41\u001b[0m new_record \u001b[38;5;241m=\u001b[39m create_new_data_record(data_item, dataset_id, file_id, current_time,images)\n\u001b[1;32m     43\u001b[0m \u001b[38;5;66;03m# 模拟插入数据库操作\u001b[39;00m\n\u001b[0;32m---> 44\u001b[0m \u001b[43mcollectionstructured_data_records\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minsert_many\u001b[49m\u001b[43m(\u001b[49m\u001b[43m[\u001b[49m\u001b[43mnew_record\u001b[49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     46\u001b[0m \u001b[38;5;66;03m# 保存到列表中（可以实际替换成数据库操作）\u001b[39;00m\n\u001b[1;32m     47\u001b[0m created_data_records\u001b[38;5;241m.\u001b[39mappend(new_record)\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/_csot.py:120\u001b[0m, in \u001b[0;36mapply.<locals>.csot_wrapper\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    118\u001b[0m         \u001b[38;5;28;01mwith\u001b[39;00m _TimeoutContext(timeout):\n\u001b[1;32m    119\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m--> 120\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/collection.py:954\u001b[0m, in \u001b[0;36mCollection.insert_many\u001b[0;34m(self, documents, ordered, bypass_document_validation, session, comment)\u001b[0m\n\u001b[1;32m    952\u001b[0m blk \u001b[38;5;241m=\u001b[39m _Bulk(\u001b[38;5;28mself\u001b[39m, ordered, bypass_document_validation, comment\u001b[38;5;241m=\u001b[39mcomment)\n\u001b[1;32m    953\u001b[0m blk\u001b[38;5;241m.\u001b[39mops \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(gen())\n\u001b[0;32m--> 954\u001b[0m \u001b[43mblk\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mwrite_concern\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msession\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_Op\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mINSERT\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    955\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m InsertManyR<PERSON>ult(inserted_ids, write_concern\u001b[38;5;241m.\u001b[39macknowledged)\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/bulk.py:736\u001b[0m, in \u001b[0;36m_Bulk.execute\u001b[0;34m(self, write_concern, session, operation)\u001b[0m\n\u001b[1;32m    734\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    735\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[0;32m--> 736\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute_command\u001b[49m\u001b[43m(\u001b[49m\u001b[43mgenerator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mwrite_concern\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msession\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/bulk.py:593\u001b[0m, in \u001b[0;36m_Bulk.execute_command\u001b[0;34m(self, generator, write_concern, session, operation)\u001b[0m\n\u001b[1;32m    582\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_execute_command(\n\u001b[1;32m    583\u001b[0m         generator,\n\u001b[1;32m    584\u001b[0m         write_concern,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    589\u001b[0m         full_result,\n\u001b[1;32m    590\u001b[0m     )\n\u001b[1;32m    592\u001b[0m client \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcollection\u001b[38;5;241m.\u001b[39mdatabase\u001b[38;5;241m.\u001b[39mclient\n\u001b[0;32m--> 593\u001b[0m _ \u001b[38;5;241m=\u001b[39m \u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_retryable_write\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    594\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mis_retryable\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    595\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretryable_bulk\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    596\u001b[0m \u001b[43m    \u001b[49m\u001b[43msession\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    597\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    598\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbulk\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore[arg-type]\u001b[39;49;00m\n\u001b[1;32m    599\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mop_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    600\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    602\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m full_result[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwriteErrors\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;129;01mor\u001b[39;00m full_result[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwriteConcernErrors\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n\u001b[1;32m    603\u001b[0m     _raise_bulk_write_error(full_result)\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py:1898\u001b[0m, in \u001b[0;36mMongoClient._retryable_write\u001b[0;34m(self, retryable, func, session, operation, bulk, operation_id)\u001b[0m\n\u001b[1;32m   1884\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Execute an operation with consecutive retries if possible\u001b[39;00m\n\u001b[1;32m   1885\u001b[0m \n\u001b[1;32m   1886\u001b[0m \u001b[38;5;124;03mReturns func()'s return value on success. On error retries the same\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1895\u001b[0m \u001b[38;5;124;03m:param bulk: bulk abstraction to execute operations in bulk, defaults to None\u001b[39;00m\n\u001b[1;32m   1896\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1897\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_tmp_session(session) \u001b[38;5;28;01mas\u001b[39;00m s:\n\u001b[0;32m-> 1898\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_retry_with_session\u001b[49m\u001b[43m(\u001b[49m\u001b[43mretryable\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43ms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbulk\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py:1784\u001b[0m, in \u001b[0;36mMongoClient._retry_with_session\u001b[0;34m(self, retryable, func, session, bulk, operation, operation_id)\u001b[0m\n\u001b[1;32m   1779\u001b[0m \u001b[38;5;66;03m# Ensure that the options supports retry_writes and there is a valid session not in\u001b[39;00m\n\u001b[1;32m   1780\u001b[0m \u001b[38;5;66;03m# transaction, otherwise, we will not support retry behavior for this txn.\u001b[39;00m\n\u001b[1;32m   1781\u001b[0m retryable \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mbool\u001b[39m(\n\u001b[1;32m   1782\u001b[0m     retryable \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mretry_writes \u001b[38;5;129;01mand\u001b[39;00m session \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m session\u001b[38;5;241m.\u001b[39min_transaction\n\u001b[1;32m   1783\u001b[0m )\n\u001b[0;32m-> 1784\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_retry_internal\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1785\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfunc\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfunc\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1786\u001b[0m \u001b[43m    \u001b[49m\u001b[43msession\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msession\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1787\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbulk\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbulk\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1788\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1789\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretryable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretryable\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1790\u001b[0m \u001b[43m    \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1791\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/_csot.py:120\u001b[0m, in \u001b[0;36mapply.<locals>.csot_wrapper\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    118\u001b[0m         \u001b[38;5;28;01mwith\u001b[39;00m _TimeoutContext(timeout):\n\u001b[1;32m    119\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m--> 120\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py:1819\u001b[0m, in \u001b[0;36mMongoClient._retry_internal\u001b[0;34m(self, func, session, bulk, operation, is_read, address, read_pref, retryable, operation_id)\u001b[0m\n\u001b[1;32m   1793\u001b[0m \u001b[38;5;129m@_csot\u001b[39m\u001b[38;5;241m.\u001b[39mapply\n\u001b[1;32m   1794\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_retry_internal\u001b[39m(\n\u001b[1;32m   1795\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1804\u001b[0m     operation_id: Optional[\u001b[38;5;28mint\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1805\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m T:\n\u001b[1;32m   1806\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Internal retryable helper for all client transactions.\u001b[39;00m\n\u001b[1;32m   1807\u001b[0m \n\u001b[1;32m   1808\u001b[0m \u001b[38;5;124;03m    :param func: Callback function we want to retry\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1817\u001b[0m \u001b[38;5;124;03m    :return: Output of the calling func()\u001b[39;00m\n\u001b[1;32m   1818\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 1819\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_ClientConnectionRetryable\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1820\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmongo_client\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1821\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfunc\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfunc\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1822\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbulk\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbulk\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1823\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1824\u001b[0m \u001b[43m        \u001b[49m\u001b[43mis_read\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mis_read\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1825\u001b[0m \u001b[43m        \u001b[49m\u001b[43msession\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msession\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1826\u001b[0m \u001b[43m        \u001b[49m\u001b[43mread_pref\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mread_pref\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1827\u001b[0m \u001b[43m        \u001b[49m\u001b[43maddress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maddress\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1828\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretryable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretryable\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1829\u001b[0m \u001b[43m        \u001b[49m\u001b[43moperation_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moperation_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1830\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py:2554\u001b[0m, in \u001b[0;36m_ClientConnectionRetryable.run\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   2552\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_last_error(check_csot\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m   2553\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 2554\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_read() \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_is_read \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_write\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2555\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ServerSelectionTimeoutError:\n\u001b[1;32m   2556\u001b[0m     \u001b[38;5;66;03m# The application may think the write was never attempted\u001b[39;00m\n\u001b[1;32m   2557\u001b[0m     \u001b[38;5;66;03m# if we raise ServerSelectionTimeoutError on the retry\u001b[39;00m\n\u001b[1;32m   2558\u001b[0m     \u001b[38;5;66;03m# attempt. Raise the original exception instead.\u001b[39;00m\n\u001b[1;32m   2559\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_last_error()\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/mongo_client.py:2676\u001b[0m, in \u001b[0;36m_ClientConnectionRetryable._write\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   2674\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_last_error()\n\u001b[1;32m   2675\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_retryable \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[0;32m-> 2676\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_func\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_session\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_retryable\u001b[49m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[1;32m   2677\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m PyMongoError \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m   2678\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_retryable:\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/bulk.py:582\u001b[0m, in \u001b[0;36m_Bulk.execute_command.<locals>.retryable_bulk\u001b[0;34m(session, conn, retryable)\u001b[0m\n\u001b[1;32m    579\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mretryable_bulk\u001b[39m(\n\u001b[1;32m    580\u001b[0m     session: Optional[ClientSession], conn: Connection, retryable: \u001b[38;5;28mbool\u001b[39m\n\u001b[1;32m    581\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 582\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_execute_command\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    583\u001b[0m \u001b[43m        \u001b[49m\u001b[43mgenerator\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    584\u001b[0m \u001b[43m        \u001b[49m\u001b[43mwrite_concern\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    585\u001b[0m \u001b[43m        \u001b[49m\u001b[43msession\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    586\u001b[0m \u001b[43m        \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    587\u001b[0m \u001b[43m        \u001b[49m\u001b[43mop_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    588\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretryable\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    589\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfull_result\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    590\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/bulk.py:527\u001b[0m, in \u001b[0;36m_Bulk._execute_command\u001b[0;34m(self, generator, write_concern, session, conn, op_id, retryable, full_result, final_write_concern)\u001b[0m\n\u001b[1;32m    525\u001b[0m \u001b[38;5;66;03m# Run as many ops as possible in one command.\u001b[39;00m\n\u001b[1;32m    526\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m write_concern\u001b[38;5;241m.\u001b[39macknowledged:\n\u001b[0;32m--> 527\u001b[0m     result, to_send \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_execute_batch\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbwc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcmd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mops\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclient\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    529\u001b[0m     \u001b[38;5;66;03m# Retryable writeConcernErrors halt the execution of this run.\u001b[39;00m\n\u001b[1;32m    530\u001b[0m     wce \u001b[38;5;241m=\u001b[39m result\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwriteConcernError\u001b[39m\u001b[38;5;124m\"\u001b[39m, {})\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/bulk.py:451\u001b[0m, in \u001b[0;36m_Bulk._execute_batch\u001b[0;34m(self, bwc, cmd, ops, client)\u001b[0m\n\u001b[1;32m    449\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    450\u001b[0m     request_id, msg, to_send \u001b[38;5;241m=\u001b[39m bwc\u001b[38;5;241m.\u001b[39mbatch_command(cmd, ops)\n\u001b[0;32m--> 451\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwrite_command\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbwc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcmd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequest_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmsg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mto_send\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclient\u001b[49m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m# type: ignore[arg-type]\u001b[39;00m\n\u001b[1;32m    453\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m result, to_send\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/helpers.py:45\u001b[0m, in \u001b[0;36m_handle_reauth.<locals>.inner\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m     42\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON>ymongo\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msynchronous\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpool\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Connection\n\u001b[1;32m     44\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 45\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     46\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m OperationFailure \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m     47\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m no_reauth:\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/bulk.py:263\u001b[0m, in \u001b[0;36m_Bulk.write_command\u001b[0;34m(self, bwc, cmd, request_id, msg, docs, client)\u001b[0m\n\u001b[1;32m    261\u001b[0m     bwc\u001b[38;5;241m.\u001b[39m_start(cmd, request_id, docs)\n\u001b[1;32m    262\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 263\u001b[0m     reply \u001b[38;5;241m=\u001b[39m \u001b[43mbwc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwrite_command\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmsg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbwc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcodec\u001b[49m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m    264\u001b[0m     duration \u001b[38;5;241m=\u001b[39m datetime\u001b[38;5;241m.\u001b[39mdatetime\u001b[38;5;241m.\u001b[39mnow() \u001b[38;5;241m-\u001b[39m bwc\u001b[38;5;241m.\u001b[39mstart_time\n\u001b[1;32m    265\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m _COMMAND_LOGGER\u001b[38;5;241m.\u001b[39misEnabledFor(logging\u001b[38;5;241m.\u001b[39mDEBUG):\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/pool.py:624\u001b[0m, in \u001b[0;36mConnection.write_command\u001b[0;34m(self, request_id, msg, codec_options)\u001b[0m\n\u001b[1;32m    616\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Send \"insert\" etc. command, returning response as a dict.\u001b[39;00m\n\u001b[1;32m    617\u001b[0m \n\u001b[1;32m    618\u001b[0m \u001b[38;5;124;03mCan raise ConnectionFailure or OperationFailure.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    621\u001b[0m \u001b[38;5;124;03m:param msg: bytes, the command message.\u001b[39;00m\n\u001b[1;32m    622\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    623\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msend_message(msg, \u001b[38;5;241m0\u001b[39m)\n\u001b[0;32m--> 624\u001b[0m reply \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mreceive_message\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest_id\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    625\u001b[0m result \u001b[38;5;241m=\u001b[39m reply\u001b[38;5;241m.\u001b[39mcommand_response(codec_options)\n\u001b[1;32m    627\u001b[0m \u001b[38;5;66;03m# Raises NotPrimaryError or OperationFailure.\u001b[39;00m\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/pool.py:592\u001b[0m, in \u001b[0;36mConnection.receive_message\u001b[0;34m(self, request_id)\u001b[0m\n\u001b[1;32m    590\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m receive_message(\u001b[38;5;28mself\u001b[39m, request_id, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmax_message_size)\n\u001b[1;32m    591\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m error:\n\u001b[0;32m--> 592\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_raise_connection_failure\u001b[49m\u001b[43m(\u001b[49m\u001b[43merror\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/pool.py:590\u001b[0m, in \u001b[0;36mConnection.receive_message\u001b[0;34m(self, request_id)\u001b[0m\n\u001b[1;32m    585\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Receive a raw BSON message or raise ConnectionFailure.\u001b[39;00m\n\u001b[1;32m    586\u001b[0m \n\u001b[1;32m    587\u001b[0m \u001b[38;5;124;03mIf any exception is raised, the socket is closed.\u001b[39;00m\n\u001b[1;32m    588\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    589\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 590\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mreceive_message\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequest_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_message_size\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    591\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m error:\n\u001b[1;32m    592\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_raise_connection_failure(error)\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/network.py:320\u001b[0m, in \u001b[0;36mreceive_message\u001b[0;34m(conn, request_id, max_message_size)\u001b[0m\n\u001b[1;32m    318\u001b[0m         deadline \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    319\u001b[0m \u001b[38;5;66;03m# Ignore the response's request id.\u001b[39;00m\n\u001b[0;32m--> 320\u001b[0m length, _, response_to, op_code \u001b[38;5;241m=\u001b[39m _UNPACK_HEADER(\u001b[43m_receive_data_on_socket\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m16\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdeadline\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m    321\u001b[0m \u001b[38;5;66;03m# No request_id for exhaust cursor \"getMore\".\u001b[39;00m\n\u001b[1;32m    322\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m request_id \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/network.py:390\u001b[0m, in \u001b[0;36m_receive_data_on_socket\u001b[0;34m(conn, length, deadline)\u001b[0m\n\u001b[1;32m    388\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m bytes_read \u001b[38;5;241m<\u001b[39m length:\n\u001b[1;32m    389\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 390\u001b[0m         \u001b[43mwait_for_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdeadline\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    391\u001b[0m         \u001b[38;5;66;03m# CSOT: Update timeout. When the timeout has expired perform one\u001b[39;00m\n\u001b[1;32m    392\u001b[0m         \u001b[38;5;66;03m# final non-blocking recv. This helps avoid spurious timeouts when\u001b[39;00m\n\u001b[1;32m    393\u001b[0m         \u001b[38;5;66;03m# the response is actually already buffered on the client.\u001b[39;00m\n\u001b[1;32m    394\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m _csot\u001b[38;5;241m.\u001b[39mget_timeout() \u001b[38;5;129;01mand\u001b[39;00m deadline \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/synchronous/network.py:375\u001b[0m, in \u001b[0;36mwait_for_read\u001b[0;34m(conn, deadline)\u001b[0m\n\u001b[1;32m    373\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    374\u001b[0m         timeout \u001b[38;5;241m=\u001b[39m _POLL_TIMEOUT\n\u001b[0;32m--> 375\u001b[0m     readable \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msocket_checker\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mselect\u001b[49m\u001b[43m(\u001b[49m\u001b[43msock\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mread\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    376\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m conn\u001b[38;5;241m.\u001b[39mcancel_context\u001b[38;5;241m.\u001b[39mcancelled:\n\u001b[1;32m    377\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m _OperationCancelled(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124moperation cancelled\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/miniforge3/envs/wisechat/lib/python3.8/site-packages/pymongo/socket_checker.py:66\u001b[0m, in \u001b[0;36mSocketChecker.select\u001b[0;34m(self, sock, read, write, timeout)\u001b[0m\n\u001b[1;32m     62\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m     63\u001b[0m     \u001b[38;5;66;03m# poll() timeout is in milliseconds. select()\u001b[39;00m\n\u001b[1;32m     64\u001b[0m     \u001b[38;5;66;03m# timeout is in seconds.\u001b[39;00m\n\u001b[1;32m     65\u001b[0m     timeout_ \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON>one\u001b[39;00m \u001b[38;5;28;01mif\u001b[39;00m timeout \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01melse\u001b[39;00m timeout \u001b[38;5;241m*\u001b[39m \u001b[38;5;241m1000\u001b[39m\n\u001b[0;32m---> 66\u001b[0m     res \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_poller\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpoll\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout_\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     67\u001b[0m     \u001b[38;5;66;03m# poll returns a possibly-empty list containing\u001b[39;00m\n\u001b[1;32m     68\u001b[0m     \u001b[38;5;66;03m# (fd, event) 2-tuples for the descriptors that have\u001b[39;00m\n\u001b[1;32m     69\u001b[0m     \u001b[38;5;66;03m# events or errors to report. Return True if the list\u001b[39;00m\n\u001b[1;32m     70\u001b[0m     \u001b[38;5;66;03m# is not empty.\u001b[39;00m\n\u001b[1;32m     71\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mbool\u001b[39m(res)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["\n", "# 加载json文件中的数据\n", "# json_file_path = './data/高中语文阅读题.json'  # 请修改为实际的json文件路径\n", "file_record = {\n", "    \"file_id\": file_obj[\"_id\"],\n", "    \"dataset_id\": file_obj[\"dataset_id\"],\n", "    \"created_at\": file_obj[\"created_at\"],\n", "    \"images\":[\"/data_images/671de4d0ff6d44912446e214/0000.png\"]\n", "    }\n", "# data_items = load_json(json_file_path)  # 读取的json文件中的数据\n", "\n", "# 生成80000条记录\n", "result_records = process_data(file_record, dataList, max_records=200000)\n", "\n", "    # 保存结果到新文件\n", "# output_file_path = 'output_data.json'  # 保存生成的json数据路径\n", "# save_to_json(output_file_path, result_records)\n", "\n"]}, {"cell_type": "code", "execution_count": 113, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1000 records inserted.\n", "2000 records inserted.\n", "total_inserted: 2487\n", "total_inserted: 2490\n"]}], "source": ["\n", "# 加载json文件中的数据\n", "jsonl_file_path = './data/train.jsonl'  # 请修改为实际的json文件路径\n", "file_record = {\n", "    \"file_id\": file_obj[\"_id\"],\n", "    \"dataset_id\": file_obj[\"dataset_id\"],\n", "    \"created_at\": file_obj[\"created_at\"],\n", "    \"images\":[\"/data_images/671dabe9ff6d449124461d80/0000.png\"]\n", "    }\n", "data_items = load_jsonl(jsonl_file_path)  # 读取的json文件中的数据\n", "\n", "# 生成80000条记录\n", "result_records = process_data(file_record, data_items, max_records=2490)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["# newdata_record = {\n", "#     \"_id\": _id,\n", "#     \"dataset_id\": ObjectId(\"671cd8d3291a6d3d6beab144\"),\n", "#     \"file_id\":  ObjectId(\"671cd8d3291a6d3d6beab201\"),\n", "#     \"data\": newdata,\n", "#     \"images\": [f'/{str(_id)}/{file_record[\"created_at\"].strftime(\"%Y-%m-%d_%H-%M-%S\")}_{image_name}'],\n", "#     \"created_at\": file_record[\"created_at\"],\n", "#     \"updated_at\": file_record[\"created_at\"]\n", "#  }\n", "  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [], "source": ["newdata = ''' {\n", "\n", "    \"typeName\": \"阅读题\",\n", "    \"source\": \"\",\n", "    \"subjectId\": 1,\n", "    \"parseContent\": \"【分析】解答理解和分析题要辨明检索区间，确定对应语句．联系上下文体会，要死抠字眼．对词语的解说要抓住本质．将解释的含意放在被解释的词语处，看上下文是否连贯、恰当．寻求称代词与称代内容的联系，将称代的内容代入原文理解．联系全文的倾向性，检查该解释是否与全文保持一致．\",\n", "    \"subjectName\": \"语文\",\n", "    \"optionList\": [],\n", "    \"difficulty\": 4,\n", "    \"answer\": \"D$###$B$###$D\"\n", "  }'''"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"data": {"text/plain": ["' {\\n\\n    \"typeName\": \"阅读题\",\\n    \"source\": \"\",\\n    \"subjectId\": 1,\\n    \"parseContent\": \"【分析】解答理解和分析题要辨明检索区间，确定对应语句．联系上下文体会，要死抠字眼．对词语的解说要抓住本质．将解释的含意放在被解释的词语处，看上下文是否连贯、恰当．寻求称代词与称代内容的联系，将称代的内容代入原文理解．联系全文的倾向性，检查该解释是否与全文保持一致．\",\\n    \"subjectName\": \"语文\",\\n    \"optionList\": [],\\n    \"difficulty\": 4,\\n    \"answer\": \"D$###$B$###$D\"\\n  }'"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["newdata"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [], "source": ["# client = pymongo.MongoClient(\"mongodb://memInterview:<EMAIL>:37017/roardataAiApp_test?authSource=admin\")\n", "db = client[\"roardataAiApp_test\"]\n", "collectionstructured_data_records = db[\"structured_data_records\"]"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/plain": ["InsertManyResult([ObjectId('671d7553291a6d3d6beab168')], acknowledged=True)"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["collectionstructured_data_records.insert_many([newdata_record])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 小学英语选择题\n", "\n", "{\n", "    \"_id\" : ObjectId(\"671d9ef3ff6d4491244551c2\"),\n", "    \"name\" : \"小学英语选择题\",\n", "    \"description\" : \"涵盖了动词时态、短语动词、定语从句、过去时、时间表达等多种语法知识点。\",\n", "    \"tags\" : [ \n", "        \"小学\", \n", "        \"英语\", \n", "        \"选择题\", \n", "        \"答案解析\", \n", "        \"习题图片\"\n", "    ],\n", "    \"created_at\" : ISODate(\"2024-09-02T00:00:00.000Z\"),\n", "    \"last_updated\" : ISODate(\"2024-09-02T00:00:00.000Z\"),\n", "    \"user_id\" : 1,\n", "    \"is_active\" : true,\n", "    \"user_name\" : \"超级管理员\",\n", "    \"row_count\" : 0,\n", "    \"processing_status\" : \"completed\"\n", "}\n", "# 文件\n", "{\n", "   \n", "    \"name\" : \"小学英语选择题_file_1.json\",\n", "    \"storage_path\" : \"/dataset/小学英语选择题_file_1.json\",\n", "    \"dataset_id\" : ObjectId(\"671d9ef3ff6d4491244551c2\"),\n", "    \"created_at\" : ISODate(\"2024-09-12T00:00:00.000Z\"),\n", "    \"data_type\" : \"JSON\",\n", "    \"dataset_type\" : \"structured\",\n", "    \"processing_status\" : \"completed\",\n", "    \"user_id\" : 1,\n", "    \"deleted_by\" : null,\n", "    \"user_name\" : \"超级管理员\",\n", "    \"tags\" : [ \n", "        \"英语\", \n", "        \"数据集\"\n", "    ],\n", "    \"row_count\" : 0\n", "}\n", "\n", "# 小学数学\n", "{\n", "    \"_id\" : ObjectId(\"671da2e0ff6d44912445a1e1\"),\n", "    \"name\" : \"小学数学教学资料数据集\",\n", "    \"description\" : \"包含小学数学教学资料文章，提取了文章中的配图并独立存储，标识了配图在文章中的位置。每篇文章至少有1张配图。\",\n", "    \"tags\" : [ \n", "        \"小学\", \n", "        \"数学\", \n", "        \"教学资料\", \n", "        \"文章\", \n", "        \"配图\"\n", "    ],\n", "    \"created_at\" : ISODate(\"2024-09-08T00:00:00.000Z\"),\n", "    \"last_updated\" : ISODate(\"2024-09-08T00:00:00.000Z\"),\n", "    \"user_id\" : 2,\n", "    \"is_active\" : true,\n", "    \"user_name\" : \"超级管理员\",\n", "    \"row_count\" : 0,\n", "    \"processing_status\" : \"completed\"\n", "}\n", "{\n", "   \n", "    \"name\" : \"小学数学选择题_file_1.json\",\n", "    \"storage_path\" : \"/dataset/小学数学选择题_file_1.json\",\n", "    \"dataset_id\" : ObjectId(\"671da2e0ff6d44912445a1e1\"),\n", "    \"created_at\" :ISODate(\"2024-09-08T00:00:00.000Z\"),\n", "    \"data_type\" : \"JSON\",\n", "    \"dataset_type\" : \"structured\",\n", "    \"processing_status\" : \"completed\",\n", "    \"user_id\" : 1,\n", "    \"deleted_by\" : null,\n", "    \"user_name\" : \"超级管理员\",\n", "    \"tags\" : [ \n", "        \"英语\", \n", "        \"数据集\"\n", "    ],\n", "    \"row_count\" : 0\n", "}"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [], "source": ["a = ''' {\n", "\n", "    \"typeName\": \"解答题\",\n", "    \"source\": \"\",\n", "    \"subjectId\": 2,\n", "    \"parseContent\": \"【分析】（1）把行驶记录相加，再根据正负数的意义解答即可；<br />（2）求出行驶记录的绝对值的和，然后乘0.1计算即可得解．\",\n", "    \"subjectName\": \"数学\",\n", "    \"optionList\": [],\n", "    \"difficulty\": 4,\n", "    \"answer\": \"【解答】解：（1）9-3********-3-6-4+10，<br />=6-6+9+4+10-3-8-5-3-4，<br />=0+23-23，<br />=0，<br />答：将最后一名客人送到目的地后，出租车在鼓楼，距鼓楼0km；<br /><br />（2）9+3+4+8+6+5+3+6+4+10=58km，<br />58×0.1=5.8升．<br />答：这天下午出租车司机一共耗油5.8升．\"\n", "  }'''"]}, {"cell_type": "code", "execution_count": 100, "metadata": {}, "outputs": [{"data": {"text/plain": ["' {\\n\\n    \"typeName\": \"解答题\",\\n    \"source\": \"\",\\n    \"subjectId\": 2,\\n    \"parseContent\": \"【分析】（1）把行驶记录相加，再根据正负数的意义解答即可；<br />（2）求出行驶记录的绝对值的和，然后乘0.1计算即可得解．\",\\n    \"subjectName\": \"数学\",\\n    \"optionList\": [],\\n    \"difficulty\": 4,\\n    \"answer\": \"【解答】解：（1）9-3********-3-6-4+10，<br />=6-6+9+4+10-3-8-5-3-4，<br />=0+23-23，<br />=0，<br />答：将最后一名客人送到目的地后，出租车在鼓楼，距鼓楼0km；<br /><br />（2）9+3+4+8+6+5+3+6+4+10=58km，<br />58×0.1=5.8升．<br />答：这天下午出租车司机一共耗油5.8升．\"\\n  }'"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": 116, "metadata": {}, "outputs": [], "source": ["\n", "import pyarrow.parquet as pq\n", "import json"]}, {"cell_type": "code", "execution_count": 117, "metadata": {}, "outputs": [], "source": ["# !pip install pandas pyarrow"]}, {"cell_type": "code", "execution_count": 119, "metadata": {}, "outputs": [], "source": ["parquet_file = './data/train-00000-of-00001-0394cf7d05acc987.parquet'\n", "table = pq.read_table(parquet_file)"]}, {"cell_type": "code", "execution_count": 121, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = table.to_pandas()"]}, {"cell_type": "code", "execution_count": 122, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>category</th>\n", "      <th>title</th>\n", "      <th>content</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>US</td>\n", "      <td>Track the Path of Hurricane Maria As It Streng...</td>\n", "      <td>Hurricane Maria is strengthening in the Caribb...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>US</td>\n", "      <td>Vandals Have Attacked a Jewish Cemetery in Mis...</td>\n", "      <td>More than 100 headstones were reportedly damag...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>US</td>\n", "      <td><PERSON><PERSON> in Charleston Church Shooting</td>\n", "      <td>Authorities on Thursday said they had arrested...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>US</td>\n", "      <td>Child Contracts the Plague After Trip to Yosem...</td>\n", "      <td>Public Health officials in California are inve...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>US</td>\n", "      <td>CDC Confirms Zika Virus Infection in Minnesota...</td>\n", "      <td>A case of the Zika virus has been confirmed We...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  category                                              title  \\\n", "0       US  Track the Path of Hurricane Maria As It Streng...   \n", "1       US  Vandals Have Attacked a Jewish Cemetery in Mis...   \n", "2       US       Suspect <PERSON>t in Charleston Church Shooting   \n", "3       US  Child Contracts the Plague After Trip to Yosem...   \n", "4       US  CDC Confirms Zika Virus Infection in Minnesota...   \n", "\n", "                                             content  \n", "0  Hurricane Maria is strengthening in the Caribb...  \n", "1  More than 100 headstones were reportedly damag...  \n", "2  Authorities on Thursday said they had arrested...  \n", "3  Public Health officials in California are inve...  \n", "4  A case of the Zika virus has been confirmed We...  "]}, "execution_count": 122, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 138, "metadata": {}, "outputs": [], "source": ["dataList = df_to_json_array(df)"]}, {"cell_type": "code", "execution_count": 128, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "def df_to_json_array(df):\n", "    \"\"\"\n", "    将 DataFrame 转换为 JSON 数组\n", "    \n", "    参数:\n", "        df (pd.DataFrame): 输入的 DataFrame\n", "    \n", "    返回:\n", "        list: JSON 数组\n", "    \"\"\"\n", "    def convert_value(val):\n", "        \"\"\"处理特殊数据类型\"\"\"\n", "        if pd.isna(val):  # 处理 NaN/None\n", "            return None\n", "        elif isinstance(val, (datetime, np.datetime64)):  # 处理日期时间\n", "            return val.strftime('%Y-%m-%d %H:%M:%S')\n", "        elif isinstance(val, np.integer):  # 处理 numpy 整数类型\n", "            return int(val)\n", "        elif isinstance(val, np.floating):  # 处理 numpy 浮点类型\n", "            return float(val)\n", "        elif isinstance(val, np.bool_):  # 处理 numpy 布尔类型\n", "            return bool(val)\n", "        return val\n", "\n", "    try:\n", "        # 转换为字典列表\n", "        records = df.to_dict('records')\n", "        \n", "        # 处理每条记录中的特殊类型\n", "        json_array = []\n", "        for record in records:\n", "            processed_record = {k: convert_value(v) for k, v in record.items()}\n", "            json_array.append(processed_record)\n", "            \n", "        return json_array\n", "        \n", "    except Exception as e:\n", "        print(f\"转换过程中出错: {e}\")\n", "        return []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'书名': '道家文化与中医学',\n", " '作者': '江幼，李原撰',\n", " '出版社': '北京中国中医药出版社',\n", " '关键词': '道家文化研究中医学医学史研究',\n", " '摘要': '本书以充足的史料和严密的逻辑证明了道家思想是中医理论的基石回顾了中华文明发展史概括了阴阳五行、藏象经络等中医基础理论和道家学说。本书还理论联系实际从古为今用洋为中用的原则出发介绍了许多道、医共用的治疗保健技术和方法。',\n", " '中国图书分类号': 'R-092',\n", " '出版年月': '2017.07'}"]}, "execution_count": 139, "metadata": {}, "output_type": "execute_result"}], "source": ["dataList[0]"]}, {"cell_type": "code", "execution_count": 140, "metadata": {}, "outputs": [{"data": {"text/plain": ["133425"]}, "execution_count": 140, "metadata": {}, "output_type": "execute_result"}], "source": ["len(dataList)"]}, {"cell_type": "code", "execution_count": 137, "metadata": {}, "outputs": [], "source": ["df = pd.read_csv('./data/中文图书数据集.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "wisechat", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}