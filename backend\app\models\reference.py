from mongoengine import Document, EmbeddedDocument, StringField, ListField, DictField, DateTimeField, IntField, URLField, EmbeddedDocumentField
from datetime import datetime

class RAGReference(EmbeddedDocument):
    title = StringField(required=True)
    content = StringField(required=True)
    source_file = DictField(required=True)  # 包含 name, id, url
    additional_info = DictField()

class ChartReference(EmbeddedDocument):
    title = StringField(required=True)
    data = DictField(required=True)
    chart_type = StringField(required=True)
    source = StringField()

class Reference(Document):
    meta = {
        'collection': 'references'
    }
    conversation_id = StringField(required=True)
    message_id = StringField(required=True)
    rag_references = ListField(EmbeddedDocumentField(RAGReference))
    chart_references = ListField(EmbeddedDocumentField(ChartReference))
    created_at = DateTimeField(default=datetime.now)
    app_info = StringField()
    user_id = IntField(required=True)
    user_name = StringField()
