"""
知识库检索测试接口
支持语义检索、全文检索、混合检索三种模式
可选重排序功能
"""

import asyncio
import time
import json
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import requests
from ...utils.logging_config import get_logger
from .base_retriever import es_vector_retriever_new, _parse_es_host, _check_es_version
from ..embedding.embedding_utils import get_embedding
from ...utils.enums import chunk_type, RAGSearchType

logger = get_logger(__name__)

class RetrievalTestRequest(BaseModel):
    """检索测试请求参数"""
    knowledge_base_id: str          # 知识库ID
    query: str                      # 检索查询
    search_mode: str               # 检索模式：semantic/fulltext/hybrid
    max_tokens: int = 1000         # 最大token数
    min_similarity: float = 0.7    # 最小相似度阈值
    topk: int = 10                 # 返回结果数量
    rerank_id: Optional[str] = None # 重排序模型ID（可选）
    es_host: str = '[{"host": "**************", "port": 9600},{"host": "**************", "port": 9600}]'
    index_name: str = "wise_agent_chunk_index"

class RetrievalTestResponse(BaseModel):
    """检索测试响应结果"""
    success: bool
    search_mode: str
    total_results: int
    reranked: bool
    results: List[Dict[str, Any]]
    execution_time: float
    error_message: Optional[str] = None

async def semantic_search(kb_id: str, query: str, params: RetrievalTestRequest) -> List[Dict[str, Any]]:
    """
    语义检索（向量检索）
    """
    try:
        logger.info(f"执行语义检索 - 知识库: {kb_id}, 查询: {query}")

        # 1. 获取知识库的embedding配置
        from ...db.mongodb import db
        from bson import ObjectId

        kb_info = await db.knowledge_bases.find_one({"_id": ObjectId(kb_id)})
        if not kb_info:
            raise ValueError(f"知识库不存在: {kb_id}")

        embedding_id = kb_info.get("embedding_model_id")
        if not embedding_id:
            raise ValueError("知识库未配置embedding模型")

        embedding_config = await db.embeddings.find_one({"id": int(embedding_id)})
        if not embedding_config:
            raise ValueError("embedding配置不存在")

        # 2. 获取查询向量
        embedding = get_embedding(query, embedding_config)
        if not embedding:
            raise ValueError("获取查询向量失败")

        # 3. 补齐向量维度到1536
        if len(embedding) < 1536:
            embedding.extend([0] * (1536 - len(embedding)))

        # 4. 调用向量检索
        results = es_vector_retriever_new(
            query_embedding=embedding,
            chunk_type=chunk_type.BASE,
            kb_id=kb_id,
            top_k=params.topk,
            threshold=params.min_similarity,
            es_host=params.es_host,
            index_name=params.index_name
        )

        logger.info(f"语义检索完成 - 返回结果: {len(results)}条")
        return results

    except Exception as e:
        logger.error(f"语义检索失败: {e}")
        raise

async def fulltext_search(kb_id: str, query: str, params: RetrievalTestRequest) -> List[Dict[str, Any]]:
    """
    全文检索
    """
    try:
        logger.info(f"执行全文检索 - 知识库: {kb_id}, 查询: {query}")

        # 解析ES主机配置
        host_config = _parse_es_host(params.es_host)
        es_base_url = f"http://{host_config['host']}:{host_config['port']}"
        es_url = f"{es_base_url}/{params.index_name}/_search"

        # 构建全文检索查询
        search_query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"knowledge_base_id": kb_id}},
                        {"multi_match": {
                            "query": query,
                            "fields": ["index_content"],
                            "type": "best_fields",
                            "fuzziness": "AUTO"
                        }}
                    ]
                }
            },
            "size": params.topk,
            "_source": ["id", "chunk_id", "file_id", "knowledge_base_id", "index_content", "chunk_index"]
        }

        # 检查ES版本并获取请求参数
        request_params = _check_es_version(es_base_url)

        # 执行检索
        headers = {"Content-Type": "application/json"}
        response = requests.post(
            url=es_url,
            headers=headers,
            json=search_query,
            params=request_params,
            timeout=10
        )
        response.raise_for_status()

        # 解析结果
        es_response = response.json()
        hits = es_response.get("hits", {}).get("hits", [])

        results = []
        for hit in hits:
            source = hit["_source"]
            score = hit["_score"]

            # 过滤低分结果
            if score < params.min_similarity:
                continue

            results.append({
                "id": hit["_id"],
                "content": source.get("index_content", ""),
                "score": score,
                "knowledge_base_id": source.get("knowledge_base_id"),
                "chunk_id": source.get("chunk_id"),
                "file_id": source.get("file_id"),
                "chunk_index": source.get("chunk_index"),
                "search_type": "fulltext"
            })

        logger.info(f"全文检索完成 - 返回结果: {len(results)}条")
        return results

    except Exception as e:
        logger.error(f"全文检索失败: {e}")
        raise

def rrf_merge(semantic_results: List[Dict], fulltext_results: List[Dict], k: int = 60) -> List[Dict[str, Any]]:
    """
    使用RRF算法合并语义检索和全文检索结果
    """
    try:
        logger.info(f"开始RRF合并 - 语义结果: {len(semantic_results)}条, 全文结果: {len(fulltext_results)}条")

        # 1. 构建文档ID到排名的映射
        semantic_ranks = {doc["id"]: rank + 1 for rank, doc in enumerate(semantic_results)}
        fulltext_ranks = {doc["id"]: rank + 1 for rank, doc in enumerate(fulltext_results)}

        # 2. 构建文档ID到文档详情的映射
        all_docs = {}
        for doc in semantic_results:
            all_docs[doc["id"]] = doc
        for doc in fulltext_results:
            if doc["id"] not in all_docs:
                all_docs[doc["id"]] = doc

        # 3. 计算RRF分数
        all_doc_ids = set(semantic_ranks.keys()) | set(fulltext_ranks.keys())
        rrf_scores = {}

        for doc_id in all_doc_ids:
            score = 0
            if doc_id in semantic_ranks:
                score += 1 / (k + semantic_ranks[doc_id])
            if doc_id in fulltext_ranks:
                score += 1 / (k + fulltext_ranks[doc_id])
            rrf_scores[doc_id] = score

        # 4. 按RRF分数排序并构建最终结果
        sorted_doc_ids = sorted(rrf_scores.items(), key=lambda x: x[1], reverse=True)

        merged_results = []
        for doc_id, rrf_score in sorted_doc_ids:
            doc = all_docs[doc_id].copy()
            doc["rrf_score"] = rrf_score
            doc["search_type"] = "hybrid"
            # 保留原始分数信息
            if doc_id in semantic_ranks:
                doc["semantic_rank"] = semantic_ranks[doc_id]
            if doc_id in fulltext_ranks:
                doc["fulltext_rank"] = fulltext_ranks[doc_id]
            merged_results.append(doc)

        logger.info(f"RRF合并完成 - 最终结果: {len(merged_results)}条")
        return merged_results

    except Exception as e:
        logger.error(f"RRF合并失败: {e}")
        raise

async def hybrid_search(kb_id: str, query: str, params: RetrievalTestRequest) -> List[Dict[str, Any]]:
    """
    混合检索（语义+全文检索，使用RRF算法合并）
    """
    try:
        logger.info(f"执行混合检索 - 知识库: {kb_id}, 查询: {query}")

        # 1. 并行执行两种检索
        semantic_results, fulltext_results = await asyncio.gather(
            semantic_search(kb_id, query, params),
            fulltext_search(kb_id, query, params)
        )

        # 2. 使用RRF算法合并结果
        merged_results = rrf_merge(semantic_results, fulltext_results)

        # 3. 限制返回数量
        final_results = merged_results[:params.topk]

        logger.info(f"混合检索完成 - 最终结果: {len(final_results)}条")
        return final_results

    except Exception as e:
        logger.error(f"混合检索失败: {e}")
        raise

async def get_rerank_config(rerank_id: str) -> Dict[str, Any]:
    """
    根据rerank_id获取重排序配置
    参考 flow_langgraph.py 中的配置获取逻辑
    """
    try:
        from ...db.mongodb import db

        # 查询重排序配置
        rerank_config = await db.rerank_models.find_one({"_id": rerank_id})
        if not rerank_config:
            raise ValueError(f"未找到重排序配置: {rerank_id}")

        return rerank_config

    except Exception as e:
        logger.error(f"获取重排序配置失败: {e}")
        raise

async def apply_rerank(results: List[Dict], query: str, rerank_id: str) -> List[Dict[str, Any]]:
    """
    应用重排序
    参考 flow_langgraph.py 中的 rerank_node 实现
    """
    try:
        logger.info(f"开始重排序 - 结果数: {len(results)}, 重排序ID: {rerank_id}")

        if not results:
            return results

        # 1. 获取重排序配置
        rerank_config = await get_rerank_config(rerank_id)

        # 2. 构建重排序输入
        texts = []
        for res in results:
            # 构建文本内容，优先使用content，其次使用q+a
            text_content = res.get("content", "")
            if not text_content:
                q = res.get("q", "")
                a = res.get("a", "")
                text_content = f"{q} {a}".strip()

            texts.append({
                'id': res["id"],
                'text': text_content
            })

        # 3. 调用重排序服务
        rerank_url = rerank_config.get("api_url", "")
        rerank_headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {rerank_config.get('api_key', '')}"
        }

        rerank_payload = {
            "query": query,
            "texts": texts,
            "top_k": len(texts)
        }

        response = requests.post(
            url=rerank_url,
            headers=rerank_headers,
            json=rerank_payload,
            timeout=30
        )
        response.raise_for_status()

        # 4. 解析重排序结果
        rerank_response = response.json()
        reranked_texts = rerank_response.get("results", [])

        # 5. 重新排序原始结果
        id_to_result = {res["id"]: res for res in results}
        reranked_results = []

        for reranked_item in reranked_texts:
            doc_id = reranked_item.get("id")
            if doc_id in id_to_result:
                result = id_to_result[doc_id].copy()
                result["rerank_score"] = reranked_item.get("score", 0)
                reranked_results.append(result)

        logger.info(f"重排序完成 - 重排序结果: {len(reranked_results)}条")
        return reranked_results

    except Exception as e:
        logger.error(f"重排序失败: {e}")
        # 重排序失败时返回原始结果
        return results

def validate_parameters(request: RetrievalTestRequest):
    """
    验证请求参数
    """
    if request.search_mode not in ["semantic", "fulltext", "hybrid"]:
        raise ValueError(f"不支持的检索模式: {request.search_mode}")

    if request.topk <= 0 or request.topk > 100:
        raise ValueError("topk参数必须在1-100之间")

    if request.min_similarity < 0 or request.min_similarity > 1:
        raise ValueError("min_similarity参数必须在0-1之间")

    if not request.query.strip():
        raise ValueError("查询内容不能为空")

async def test_knowledge_retrieval(request: RetrievalTestRequest) -> RetrievalTestResponse:
    """
    检索测试主接口
    """
    start_time = time.time()

    try:
        # 1. 参数验证
        validate_parameters(request)
        logger.info(f"开始检索测试 - 模式: {request.search_mode}, 知识库: {request.knowledge_base_id}")

        # 2. 根据模式执行检索
        if request.search_mode == "semantic":
            results = await semantic_search(request.knowledge_base_id, request.query, request)
        elif request.search_mode == "fulltext":
            results = await fulltext_search(request.knowledge_base_id, request.query, request)
        elif request.search_mode == "hybrid":
            results = await hybrid_search(request.knowledge_base_id, request.query, request)
        else:
            raise ValueError(f"不支持的检索模式: {request.search_mode}")

        # 3. 可选的重排序
        reranked = False
        if request.rerank_id and results:
            logger.info(f"开始重排序 - 重排序ID: {request.rerank_id}")
            results = await apply_rerank(results, request.query, request.rerank_id)
            reranked = True

        # 4. 结果处理和返回
        execution_time = time.time() - start_time

        logger.info(f"检索测试完成 - 模式: {request.search_mode}, 结果数: {len(results)}, 重排序: {reranked}, 耗时: {execution_time:.2f}s")

        return RetrievalTestResponse(
            success=True,
            search_mode=request.search_mode,
            total_results=len(results),
            reranked=reranked,
            results=results,
            execution_time=execution_time
        )

    except Exception as e:
        execution_time = time.time() - start_time
        error_msg = str(e)
        logger.error(f"检索测试失败 - 错误: {error_msg}, 耗时: {execution_time:.2f}s")

        return RetrievalTestResponse(
            success=False,
            search_mode=request.search_mode,
            total_results=0,
            reranked=False,
            results=[],
            execution_time=execution_time,
            error_message=error_msg
        )