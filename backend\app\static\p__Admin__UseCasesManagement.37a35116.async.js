"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6224],{30967:function(e,r,n){n.r(r);var t=n(15009),o=n.n(t),a=n(99289),i=n.n(a),c=n(5574),s=n.n(c),u=n(97131),l=n(12453),p=n(8232),d=n(17788),m=n(55102),f=n(78045),g=n(71230),h=n(15746),v=n(34041),C=n(72269),b=n(83622),y=n(42075),x=n(66309),j=n(67294),w=n(69044),k=n(35312),S=n(85893);r.default=function(){var e=(0,j.useRef)(),r=(0,j.useState)(!1),n=s()(r,2),t=n[0],a=n[1],c=(0,j.useState)([]),$=s()(c,2),E=$[0],O=$[1],T=p.Z.useForm(),N=s()(T,1)[0],P=(0,k.useIntl)();(0,j.useEffect)((function(){var e=function(){var e=i()(o()().mark((function e(){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:O([{url:"/imgs/cover/01.jpg",name:"Cover 1"},{url:"/imgs/cover/02.jpg",name:"Cover 2"},{url:"/imgs/cover/03.jpg",name:"Cover 3"},{url:"/imgs/cover/04.jpg",name:"Cover 4"},{url:"/imgs/cover/05.jpg",name:"Cover 5"},{url:"/imgs/cover/06.jpg",name:"Cover 6"},{url:"/imgs/cover/07.jpg",name:"Cover 7"},{url:"/imgs/cover/08.jpg",name:"Cover 8"},{url:"/imgs/cover/09.jpg",name:"Cover 9"},{url:"/imgs/cover/10.jpg",name:"Cover 10"},{url:"/imgs/cover/11.jpg",name:"Cover 11"},{url:"/imgs/cover/12.jpg",name:"Cover 12"},{url:"/imgs/cover/13.jpg",name:"Cover 13"},{url:"/imgs/cover/14.jpg",name:"Cover 14"},{url:"/imgs/cover/15.jpg",name:"Cover 15"},{url:"/imgs/cover/16.jpg",name:"Cover 16"},{url:"/imgs/cover/17.jpg",name:"Cover 17"},{url:"/imgs/cover/18.jpg",name:"Cover 18"},{url:"/imgs/cover/19.jpg",name:"Cover 19"},{url:"/imgs/cover/20.jpg",name:"Cover 20"},{url:"/imgs/cover/21.jpg",name:"Cover 21"},{url:"/imgs/cover/22.jpg",name:"Cover 22"},{url:"/imgs/cover/23.jpg",name:"Cover 23"},{url:"/imgs/cover/24.jpg",name:"Cover 24"},{url:"/imgs/cover/25.jpg",name:"Cover 25"},{url:"/imgs/cover/26.jpg",name:"Cover 26"},{url:"/imgs/cover/27.jpg",name:"Cover 27"},{url:"/imgs/cover/28.jpg",name:"Cover 28"},{url:"/imgs/cover/29.jpg",name:"Cover 29"},{url:"/imgs/cover/30.jpg",name:"Cover 30"},{url:"/imgs/cover/31.jpg",name:"Cover 31"},{url:"/imgs/cover/32.jpg",name:"Cover 32"},{url:"/imgs/cover/33.jpg",name:"Cover 33"},{url:"/imgs/cover/34.jpg",name:"Cover 34"},{url:"/imgs/cover/35.jpg",name:"Cover 35"},{url:"/imgs/cover/36.jpg",name:"Cover 36"},{url:"/imgs/cover/37.jpg",name:"Cover 37"},{url:"/imgs/cover/38.jpg",name:"Cover 38"},{url:"/imgs/cover/39.jpg",name:"Cover 39"},{url:"/imgs/cover/40.jpg",name:"Cover 40"}]);case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();e()}),[]);var I=function(){var r=i()(o()().mark((function r(){var n,t;return o()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,N.validateFields();case 3:t=r.sent,console.log("Form values:",t),a(!1),N.resetFields(),null===(n=e.current)||void 0===n||n.reload(),r.next=13;break;case 10:r.prev=10,r.t0=r.catch(0),console.error("Validation failed:",r.t0);case 13:case"end":return r.stop()}}),r,null,[[0,10]])})));return function(){return r.apply(this,arguments)}}(),Z=function(){return(0,S.jsx)(d.Z,{title:"新增案例",open:t,onOk:I,onCancel:function(){a(!1),N.resetFields()},width:800,children:(0,S.jsxs)(p.Z,{form:N,layout:"vertical",children:[(0,S.jsx)(p.Z.Item,{name:"name",label:P.formatMessage({id:"useCases.form.name"}),rules:[{required:!0,message:P.formatMessage({id:"useCases.form.name.required"})}],children:(0,S.jsx)(m.Z,{})}),(0,S.jsx)(p.Z.Item,{name:"cover_image",label:"封面图",rules:[{required:!0,message:"请选择封面图"}],children:(0,S.jsx)(f.ZP.Group,{style:{width:"100%"},children:(0,S.jsx)(g.Z,{gutter:[16,16],children:E.map((function(e){return(0,S.jsx)(h.Z,{span:6,children:(0,S.jsxs)(f.ZP.Button,{value:e.url,style:{width:"100%",height:"140px",padding:"8px",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"space-between",position:"relative",textAlign:"center"},children:[(0,S.jsx)("div",{style:{width:"100%",height:"100px",marginBottom:"8px"},children:(0,S.jsx)("img",{src:e.url,alt:e.name,style:{width:"100%",height:"100%",objectFit:"cover",borderRadius:"4px"}})}),(0,S.jsx)("div",{style:{fontSize:"12px",lineHeight:"1.2",width:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",color:"#666"},children:e.name})]})},e.url)}))})})}),(0,S.jsx)(p.Z.Item,{name:"redirect_url",label:"跳转链接",rules:[{required:!0,message:"请输入跳转链接"}],children:(0,S.jsx)(m.Z,{})}),(0,S.jsx)(p.Z.Item,{name:"tags",label:"标签",rules:[{required:!0,message:"请选择标签"}],children:(0,S.jsx)(v.default,{mode:"tags",placeholder:"请输入或选择标签",options:[{label:"技术",value:"技术"},{label:"设计",value:"设计"},{label:"创新",value:"创新"}]})}),(0,S.jsx)(p.Z.Item,{name:"is_active",label:"状态",initialValue:!0,children:(0,S.jsx)(C.Z,{checkedChildren:"启用",unCheckedChildren:"禁用",defaultChecked:!0})})]})})},_=[{title:P.formatMessage({id:"useCases.table.name"}),dataIndex:"name",width:100,ellipsis:!0,search:!0},{title:P.formatMessage({id:"useCases.table.coverImage"}),dataIndex:"cover_image",width:120,search:!1,render:function(e,r){return(0,S.jsx)("img",{src:r.cover_image,alt:r.name,style:{width:"80px",height:"60px",objectFit:"cover"}})}},{title:"跳转链接",search:!1,dataIndex:"redirect_url",width:150,ellipsis:!0,render:function(e){return(0,S.jsx)("a",{href:e,target:"_blank",rel:"noopener noreferrer",children:e})}},{title:"标签",dataIndex:"tags",width:200,render:function(e,r){return(0,S.jsx)(y.Z,{wrap:!0,children:r.tags.map((function(e){return(0,S.jsx)(x.Z,{children:e},e)}))})}},{title:"状态",search:!1,dataIndex:"is_active",width:100,render:function(e,r){return(0,S.jsx)(C.Z,{checked:r.is_active,checkedChildren:"启用",unCheckedChildren:"禁用"})}},{title:"操作",width:180,key:"option",valueType:"option",render:function(e,r){return[(0,S.jsx)(b.ZP,{type:"link",onClick:function(){console.log("Edit record:",r.id)},children:P.formatMessage({id:"useCases.operations.edit"})},"edit"),(0,S.jsx)(b.ZP,{type:"link",danger:!0,onClick:function(){console.log("Delete record:",r.id)},children:P.formatMessage({id:"useCases.operations.delete"})},"delete")]}}];return(0,S.jsxs)(u._z,{children:[(0,S.jsx)(Z,{}),(0,S.jsx)(l.Z,{headerTitle:P.formatMessage({id:"menu.admin.useCases.management"}),actionRef:e,rowKey:"id",search:{labelWidth:120},toolBarRender:function(){return[(0,S.jsx)(b.ZP,{type:"primary",onClick:function(){return a(!0)},children:P.formatMessage({id:"useCases.operations.add"})},"add")]},request:function(){var e=i()(o()().mark((function e(r,n,t){var a,i;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,w.wG)({current:r.current||1,pageSize:r.pageSize||10,category:null===(a=t.category)||void 0===a?void 0:a[0],searchText:r.keyword});case 2:return i=e.sent,e.abrupt("return",{data:i.data,success:i.success,total:i.total});case 4:case"end":return e.stop()}}),e)})));return function(r,n,t){return e.apply(this,arguments)}}(),columns:_})]})}},69044:function(e,r,n){n.d(r,{CW:function(){return z},F3:function(){return O},Nq:function(){return f},Rd:function(){return _},Rf:function(){return l},Rp:function(){return j},_d:function(){return N},az:function(){return C},cY:function(){return M},cn:function(){return d},h8:function(){return h},iE:function(){return $},jA:function(){return y},mD:function(){return k},ul:function(){return I},w1:function(){return q},wG:function(){return D}});var t=n(5574),o=n.n(t),a=n(15009),i=n.n(a),c=n(99289),s=n.n(c),u=n(78158);function l(e){return p.apply(this,arguments)}function p(){return(p=s()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/users",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function d(e){return m.apply(this,arguments)}function m(){return(m=s()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/users",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function f(e){return g.apply(this,arguments)}function g(){return(g=s()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/users/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(e){return v.apply(this,arguments)}function v(){return(v=s()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/users/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function C(e,r){return b.apply(this,arguments)}function b(){return(b=s()(i()().mark((function e(r,n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/users/changeStatus",{method:"POST",data:{id:r,status:n}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(e){return x.apply(this,arguments)}function x(){return(x=s()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/groups",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function j(e){return w.apply(this,arguments)}function w(){return(w=s()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/groups",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function k(e){return S.apply(this,arguments)}function S(){return(S=s()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/groups/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function $(e){return E.apply(this,arguments)}function E(){return(E=s()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/groups/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function O(e){return T.apply(this,arguments)}function T(){return(T=s()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/roles",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function N(e){return P.apply(this,arguments)}function P(){return(P=s()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/roles",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function I(e,r){return Z.apply(this,arguments)}function Z(){return(Z=s()(i()().mark((function e(r,n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/roles/".concat(r),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function _(e){return B.apply(this,arguments)}function B(){return(B=s()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/roles/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function z(){return F.apply(this,arguments)}function F(){return(F=s()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/role/tree",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function M(e){return R.apply(this,arguments)}function R(){return(R=s()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/roles/".concat(r),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function q(e){return H.apply(this,arguments)}function H(){return(H=s()(i()().mark((function e(r){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=new URLSearchParams,Object.entries(r).forEach((function(e){var r=o()(e,2),t=r[0],a=r[1];n.append(t,a)})),e.abrupt("return",(0,u.N)("/api/system/config?".concat(n.toString()),{method:"POST"}));case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function D(e){return G.apply(this,arguments)}function G(){return(G=s()(i()().mark((function e(r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,u.N)("/api/useActiveCases",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},66309:function(e,r,n){n.d(r,{Z:function(){return N}});var t=n(67294),o=n(93967),a=n.n(o),i=n(98423),c=n(98787),s=n(69760),u=n(96159),l=n(45353),p=n(53124),d=n(11568),m=n(15063),f=n(14747),g=n(83262),h=n(83559);const v=e=>{const{lineWidth:r,fontSizeIcon:n,calc:t}=e,o=e.fontSizeSM;return(0,g.IX)(e,{tagFontSize:o,tagLineHeight:(0,d.bf)(t(e.lineHeightSM).mul(o).equal()),tagIconSize:t(n).sub(t(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},C=e=>({defaultBg:new m.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var b=(0,h.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:n,tagPaddingHorizontal:t,componentCls:o,calc:a}=e,i=a(t).sub(n).equal(),c=a(r).sub(n).equal();return{[o]:Object.assign(Object.assign({},(0,f.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:i,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,d.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:c,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:i}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(v(e))),C),y=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(n[t[o]]=e[t[o]])}return n};const x=t.forwardRef(((e,r)=>{const{prefixCls:n,style:o,className:i,checked:c,onChange:s,onClick:u}=e,l=y(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:d,tag:m}=t.useContext(p.E_),f=d("tag",n),[g,h,v]=b(f),C=a()(f,`${f}-checkable`,{[`${f}-checkable-checked`]:c},null==m?void 0:m.className,i,h,v);return g(t.createElement("span",Object.assign({},l,{ref:r,style:Object.assign(Object.assign({},o),null==m?void 0:m.style),className:C,onClick:e=>{null==s||s(!c),null==u||u(e)}})))}));var j=x,w=n(98719);var k=(0,h.bk)(["Tag","preset"],(e=>(e=>(0,w.Z)(e,((r,n)=>{let{textColor:t,lightBorderColor:o,lightColor:a,darkColor:i}=n;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:t,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:i,borderColor:i},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(v(e))),C);const S=(e,r,n)=>{const t="string"!=typeof(o=n)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${n}`],background:e[`color${t}Bg`],borderColor:e[`color${t}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var $=(0,h.bk)(["Tag","status"],(e=>{const r=v(e);return[S(r,"success","Success"),S(r,"processing","Info"),S(r,"error","Error"),S(r,"warning","Warning")]}),C),E=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(n[t[o]]=e[t[o]])}return n};const O=t.forwardRef(((e,r)=>{const{prefixCls:n,className:o,rootClassName:d,style:m,children:f,icon:g,color:h,onClose:v,bordered:C=!0,visible:y}=e,x=E(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:j,direction:w,tag:S}=t.useContext(p.E_),[O,T]=t.useState(!0),N=(0,i.Z)(x,["closeIcon","closable"]);t.useEffect((()=>{void 0!==y&&T(y)}),[y]);const P=(0,c.o2)(h),I=(0,c.yT)(h),Z=P||I,_=Object.assign(Object.assign({backgroundColor:h&&!Z?h:void 0},null==S?void 0:S.style),m),B=j("tag",n),[z,F,M]=b(B),R=a()(B,null==S?void 0:S.className,{[`${B}-${h}`]:Z,[`${B}-has-color`]:h&&!Z,[`${B}-hidden`]:!O,[`${B}-rtl`]:"rtl"===w,[`${B}-borderless`]:!C},o,d,F,M),q=e=>{e.stopPropagation(),null==v||v(e),e.defaultPrevented||T(!1)},[,H]=(0,s.Z)((0,s.w)(e),(0,s.w)(S),{closable:!1,closeIconRender:e=>{const r=t.createElement("span",{className:`${B}-close-icon`,onClick:q},e);return(0,u.wm)(e,r,(e=>({onClick:r=>{var n;null===(n=null==e?void 0:e.onClick)||void 0===n||n.call(e,r),q(r)},className:a()(null==e?void 0:e.className,`${B}-close-icon`)})))}}),D="function"==typeof x.onClick||f&&"a"===f.type,G=g||null,L=G?t.createElement(t.Fragment,null,G,f&&t.createElement("span",null,f)):f,W=t.createElement("span",Object.assign({},N,{ref:r,className:R,style:_}),L,H,P&&t.createElement(k,{key:"preset",prefixCls:B}),I&&t.createElement($,{key:"status",prefixCls:B}));return z(D?t.createElement(l.Z,{component:"Tag"},W):W)})),T=O;T.CheckableTag=j;var N=T}}]);