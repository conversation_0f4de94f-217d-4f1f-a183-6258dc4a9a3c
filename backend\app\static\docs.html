<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档预览</title>
    <!-- 引入样式 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Markdown渲染器 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- Mermaid图表支持 -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        .container {
            max-width: 800px;
            margin: 40px auto;
        }
        #passwordForm {
            max-width: 400px;
            margin: 100px auto;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        #content {
            display: none;
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .mermaid {
            margin: 20px 0;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <!-- 密码输入表单 -->
        <div id="passwordForm" class="bg-white">
            <h3 class="mb-4 text-center">文档访问验证</h3>
            <div class="mb-3">
                <input type="password" class="form-control" id="password" placeholder="请输入访问密码">
            </div>
            <button class="btn btn-primary w-100" onclick="verifyPassword()">查看文档</button>
        </div>

        <!-- 文档内容区域 -->
        <div id="content"></div>
    </div>

    <script>
        // 初始化 mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: { curve: 'basis' }
        });

        // 获取URL参数
        function getUrlParam(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 验证密码并获取文档
        async function verifyPassword() {
            const docId = getUrlParam('id');
            const password = document.getElementById('password').value;

            if (!docId) {
                alert('文档ID不能为空');
                return;
            }

            if (!password) {
                alert('请输入访问密码');
                return;
            }

            try {
                const response = await fetch(`/api/docs/${docId}?password=${password}`);
                
                if (!response.ok) {
                    throw new Error('访问密码错误或文档不存在');
                }

                const data = await response.json();
                if (data.success) {
                    showDocument(data.content);
                } else {
                    alert(data.message || '获取文档失败');
                }
            } catch (error) {
                alert(error.message);
            }
        }

        // 显示文档内容
        function showDocument(content) {
            // 隐藏密码表单
            document.getElementById('passwordForm').style.display = 'none';
            
            // 显示内容区域
            const contentDiv = document.getElementById('content');
            contentDiv.style.display = 'block';

            // 配置 marked 以支持 mermaid
            marked.setOptions({
                highlight: function(code, lang) {
                    if (lang === 'mermaid') {
                        return `<div class="mermaid">${code}</div>`;
                    }
                    return code;
                }
            });

            // 渲染 Markdown 内容
            contentDiv.innerHTML = marked(content);

            // 重新渲染 mermaid 图表
            mermaid.init(undefined, document.querySelectorAll('.mermaid'));
        }

        // 页面加载完成后检查 URL 参数
        document.addEventListener('DOMContentLoaded', function() {
            const docId = getUrlParam('id');
            if (!docId) {
                alert('未指定文档ID');
            }
        });
    </script>
</body>
</html>
