import logging
import json
import requests
from typing import List, Dict, Any
from base_retriever import BaseRetriever

logger = logging.getLogger(__name__)

class GraphRAGRetriever(BaseRetriever):
    def __init__(self, api_base):
        """
        Initialize Pyserini retriever

        Args:

        """
        self.api_base = api_base

        logger.info(
            f'Initialized LuceneSearcher api_base: {self.api_base}')

    def retrieve_prompt(self, query: str, top_k: int = 3) -> str:
        """
        检索相关文档片段
        Args:
            query: 查询文本
            top_k: 返回结果数量
        Returns:
            相关带有Prompt的文本信息
        """
        url = f'http://{self.api_base}/query/getprompt/?q={query}'
        response = requests.get(url)
        if response.status_code == 200:
            return response.json()

        return ''

    def retrieve(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        pass


if __name__ == "__main__":
    graphRagRetriever = GraphRAGRetriever(api_base='10.0.0.248:9998')
    results = graphRagRetriever.retrieve_prompt('非银行支付机构网络支付业务管理办法内容是什么?')
    print(results)
