#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
微信公众号数据ES到ES迁移与结构化处理脚本
基于LangGraph实现，包含读取、处理、写入三个节点
"""
import json
from typing import Dict, List, Any, Optional, TypedDict
from datetime import datetime, timedelta
import logging
import requests
import traceback
import re
import time
import schedule

from elasticsearch import Elasticsearch
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate
from langgraph.graph import StateGraph, END

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ES配置
SOURCE_ES_HOST = "**********"
SOURCE_ES_PORT = 9200
SOURCE_ES_USER = "jiachengbin"
SOURCE_ES_PASSWORD = "jcb1qaz@WSX#EDC$RFV"
SOURCE_ES_INDEX = 'wiseweb_bayou_weixinpage_alias'

TARGET_ES_HOST = "**************"
TARGET_ES_PORT = 9600
TARGET_ES_USER = None
TARGET_ES_PASSWORD = None
TARGET_ES_INDEX = "pro_mcp_data_weixin"

BATCH_SIZE = 1000  # 批处理大小

# 大模型参数
embedding_service_url = "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/bge_m3_finetune_20240710/v1/embeddings"
embedding_api_key = "ZWY0YzJkZTM4OGI0YmI3YjljOTI4NGE5ZDMxMDVhMzUzZjA3YmYxMw=="
embedding_name = "bge-m3"

# llm_service_url = "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1"
# llm_api_key = "ZjI1MDkzMDMyYmQ0MzA2MGYxNTQ5MGIxNWU3NDIxODk0MWMxMWFhYw=="
# OPENAI_MODEL = "Qwen2-14B-Instruct"

llm_service_url = "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen3_32b_2/v1"
llm_api_key = "ZmYzMmQ1N2E2YWZjMzcwN2Y3NTBjNmQzYjk3Njk3ZWY4Njk5MWQ0ZQ=="
OPENAI_MODEL = "Qwen3-32B"

llm = ChatOpenAI(
    temperature=0, 
    model=OPENAI_MODEL, 
    openai_api_base=llm_service_url,
    api_key=llm_api_key
)

embedding_config = {
    "api_key": embedding_api_key,
    "service_url": embedding_service_url,
    "embedding_name": embedding_name
}

def get_embedding(text, embedding_config):
    """获取文本的向量嵌入"""
    access_token = embedding_config.get("api_key", "")
    service_url = embedding_config.get("service_url", "")
    model = embedding_config.get("embedding_name", "bge-m3")
    try:
        headers = {'Content-Type': 'application/json', 'Authorization': f'{access_token}'}
        req = {
            "input": [text],
            "model": model
        }
        embdd_response = requests.post(url=service_url, json=req, headers=headers)
        if embdd_response.status_code == 200:
            query_embedding = embdd_response.json()['data'][0]['embedding']
        
            DB_VECTOR_DIMENSION = 1536

            # 如果生成的向量维度与数据库不匹配，进行扩充或截断
            if len(query_embedding) != DB_VECTOR_DIMENSION:
                if len(query_embedding) < DB_VECTOR_DIMENSION:
                    # 扩充向量维度
                    query_embedding = query_embedding + [0.0] * (DB_VECTOR_DIMENSION - len(query_embedding))
                    logger.info(f"向量维度已扩充至 {DB_VECTOR_DIMENSION}")
                else:
                    # 截断向量维度
                    query_embedding = query_embedding[:DB_VECTOR_DIMENSION]
                    logger.info(f"向量维度已截断至 {DB_VECTOR_DIMENSION}")

            logger.info(f"最终查询向量维度: {len(query_embedding)}")
            return query_embedding
        else:
            logger.error(f"获取embedding失败: {embdd_response.status_code}")
            return None
    except Exception as e:
        traceback.print_exc()
        logger.error(f"获取embedding时出错: {str(e)}")
        return None

def analyze_text(title: str, content: str) -> Dict[str, Any]:
    """分析文本内容，提取关键信息，返回结构化字段"""
    # 构建提示词模板
    prompt = ChatPromptTemplate.from_messages([
        SystemMessage(content="""你是一个专业的文本分析助手，需要分析中文新闻文章并提取以下信息：
1. 作者态度 (authorAttitude): 必须是以下之一：积极、中立、消极
2. 作者观点 (authorViewpoint): 概括作者的核心观点，及观点依据，包括但不限于机构对宏观经济的预期、对宏观政策的预期、对利率走势的预测、对投资的建议等
3. 人物实体 (characterEntity): 提取所有人物名称列表
4. 机构实体 (institutionalEntities): 提取所有机构名称列表
5. 地点实体 (locationEntity): 提取所有地点名称列表
6. 事件信息 (eventInfo): 提取主要事实事件列表
7. 摘要事实 (summaryFacts): 提取3-5个关键事实
8. 摘要 (summary): 对文章内容进行300字以内的摘要，要包括内容的关键信息

请以JSON格式返回，包含以上所有字段。
如: {"authorAttitude": "积极", "authorViewpoint": "支持该政策的实施", "characterEntity": ["张三"], "institutionalEntities": ["某公司"], "locationEntity": ["北京"], "eventInfo": ["签署合同"], "summaryFacts": ["会议顺利召开"], "summary": "这篇文章主要讨论了..."}"""),
        HumanMessage(content=f"""请分析以下文章：
标题: {title}
内容: {content}
/no_think""")
    ])
    
    chain = prompt | llm

    try:
        result = chain.invoke({})
        # 获取内容：先尝试 .content 属性，如果是对象
        raw_content = getattr(result, 'content', result)
        text = re.sub(r'<think>.*?</think>', '', raw_content, flags=re.DOTALL)

        # 匹配第一个 JSON 块
        json_match = re.search(r'({.*?})', text, flags=re.DOTALL)
        if not json_match:
            logger.error("未找到JSON内容")
            return get_default_result()
            
        json_str = json_match.group(1)

        # 尝试解析
        result_dict = json.loads(json_str)
        
        # 验证结果结构
        if not validate_result_structure(result_dict):
            logger.error("结果结构不符合预期")
            return get_default_result()
            
        return result_dict
    except Exception as e:
        traceback.print_exc()
        logger.error(f"分析文本时出错: {str(e)}")
        return get_default_result()

def validate_result_structure(data: Dict[str, Any]) -> bool:
    """验证结果结构是否符合预期"""
    required_fields = [
        "authorAttitude", 
        "authorViewpoint", 
        "characterEntity", 
        "institutionalEntities", 
        "locationEntity", 
        "eventInfo", 
        "summaryFacts", 
        "summary"
    ]
    
    for field in required_fields:
        if field not in data:
            logger.error(f"缺少必需字段: {field}")
            return False
    
    # 验证authorAttitude是否为预期值
    if data.get("authorAttitude") not in ["积极", "中立", "消极"]:
        logger.error("authorAttitude字段值必须为'积极'、'中立'或'消极'")
        data["authorAttitude"] = "中立"  # 设置默认值
    
    # 验证列表字段
    list_fields = ["characterEntity", "institutionalEntities", "locationEntity", "eventInfo", "summaryFacts"]
    for field in list_fields:
        if not isinstance(data.get(field), list):
            logger.error(f"{field}字段必须为列表类型")
            data[field] = []  # 设置默认值
    
    return True

def get_default_result() -> Dict[str, Any]:
    """返回默认的分析结果"""
    return {
        "authorAttitude": "中立",
        "authorViewpoint": "",
        "characterEntity": [],
        "institutionalEntities": [],
        "locationEntity": [],
        "eventInfo": [],
        "summaryFacts": [],
        "summary": ""
    }

# 公众号ID列表
BIZ_LIST = [
    "MzA4MDY1NTUyMg==",
    "MzA5OTY5MzM4Ng==",
    "Mzg2Mjg1NTg3NA==",
    "Mzg4NTEwMzA5NQ==",
    "Mzg5MjU4MDkyMw==",
    "MzI4NTU0NDE4Mw==",
    "MzI5MzQxOTI0MQ==",
    "MzIwMDI3NjM2Mg==",
    "MzIzMjc3NTYyNQ==",
    "Mzk0NjUwMDIxOQ==",
    "MzkxMDYyNzExMA==",
    "MzkzNTYzMDYxMg==",
    "MzU3MDMwODc2MA==",
    "MzU4NzcwNDcxOA==",
    "MzU4ODM4NzI5Nw==",
    "MzU5MzkzMTY3Mg==",
    "MzUxMDk5NDgwNQ==",
    "MzUxMDkyMzA4Mw==",
    "MzUzMzEyODIyMA==",
    "MzUzNDcxMDgzNg==",
    "MzA3MTIzNDcwMg==",
    "MzA3NjU1NTQwMA==",
    "MzA4MzY2ODYwMw==",
    "MzAwNzMxODYyNg==",
    "Mzg4NDU2MDM3Mw==",
    "MzI1NzAwODc3Nw==",
    "MzU3MDMwODc2MA==",
    "MzU3NTYyNTIyMQ==",
    "MzUzMzYwODI2MA=="
]

def build_body_for_index(start_time, end_time, biz=None):
    """构建ES查询体"""
    body = {
        "query": {
            "bool": {
                "filter": [
                    {
                        "range": {
                            "inserttime": {
                                "gte": start_time,
                                "lte": end_time
                            }
                        }
                    }
                ]
            }
        },
        "sort": [{"inserttime": "desc"}]  # 按inserttime降序排序
    }
    
    # 如果指定了biz参数，则添加biz过滤条件
    if biz:
        # 如果biz是字符串，则使用term查询
        if isinstance(biz, str):
            body["query"]["bool"]["filter"].append({
                "term": {
                    "author_unique": {
                        "value": biz
                    }
                }
            })
        # 如果biz是列表，则使用terms查询
        elif isinstance(biz, list) and len(biz) > 0:
            body["query"]["bool"]["filter"].append({
                "terms": {
                    "author_unique": biz
                }
            })
        
    return body

def get_es_scroll_data_batched(index, query_body, batch_size=1000):
    """滚动查询ES数据，并以批次方式返回"""
    es7 = Elasticsearch(
        [f"{SOURCE_ES_HOST}:{SOURCE_ES_PORT}"],
        http_auth=(SOURCE_ES_USER, SOURCE_ES_PASSWORD) if SOURCE_ES_USER else None,
        timeout=3600
    )
    
    sid = None
    try:
        # 初始搜索
        result = es7.search(index=index, scroll='10m', body=query_body, size=batch_size, request_timeout=3600)
        sid = result['_scroll_id']
        scroll_size = result['hits']['total']['value']
        print(f"索引 {index} 总数据量: {scroll_size}")
        
        # 如果有结果，返回第一批数据
        if len(result['hits']['hits']) > 0:
            yield result['hits']['hits']
        
        # 继续滚动直到没有更多数据
        scroll_count = len(result['hits']['hits'])
        while scroll_count > 0:
            result = es7.scroll(scroll_id=sid, scroll='10m', request_timeout=3600)
            batch_data = result['hits']['hits']
            scroll_count = len(batch_data)
            if scroll_count == 0:
                break
            yield batch_data
            
    except Exception as e:
        logger.error(f"获取索引 {index} 数据时出错: {str(e)}")
        traceback.print_exc()
    finally:
        if sid:
            try:
                es7.clear_scroll(scroll_id=sid)
            except:
                pass
        print(f"索引 {index} 查询完成")

def process_data(current_docs):
    """处理数据并保存到目标ES"""
    if not current_docs or len(current_docs) < 1:
        print('无数据需要处理')
        return 0
        
    print('开始处理数据')
    es = None
    try:
        es = Elasticsearch(
            [f"{TARGET_ES_HOST}:{TARGET_ES_PORT}"],
            http_auth=(TARGET_ES_USER, TARGET_ES_PASSWORD) if TARGET_ES_USER else None
        )

        # 检查连接
        if es.ping():
            print(f"ES连接成功: {TARGET_ES_HOST}:{TARGET_ES_PORT}")
            es_info = es.info()
            logger.info(f"ES版本: {es_info['version']['number']}")
        else:
            print(f"ES连接失败: {TARGET_ES_HOST}:{TARGET_ES_PORT}")
            return 0
        
        processed_count = 0
        for doc in current_docs:
            source_doc = doc["_source"]
            doc_id = doc["_id"]
            title = source_doc.get('title', '')
            content = source_doc.get('content', '')
            
            # 使用LLM分析文本
            analysis_result = analyze_text(title, content)
            
            # 创建目标文档
            target_doc = {
                # 直接复制的字段
                "author": source_doc.get("author", ""),
                "area": source_doc.get("area", ""),
                "source": source_doc.get("source", ""),
                "publishtime": source_doc.get("pubtime", ""),
                "site_media_nature": source_doc.get("site_media_nature", ""),
                "site_id": source_doc.get("author_unique", ""),
                "site_level": source_doc.get("site_level", ""),
                "new_author": source_doc.get("new_author", ""),
                "reply": source_doc.get("reply", ""),
                "imgs": source_doc.get("imgs", ""),
                "site_area_code": source_doc.get("site_area_code", ""),
                "site_name": source_doc.get("site_name", ""),
                "content": source_doc.get("content", ""),
                "new_site_name": source_doc.get("new_site_name", ""),
                "agg_domain_1": source_doc.get("agg_domain_1", ""),
                "title": source_doc.get("title", ""),
                "url": source_doc.get("url", ""),
                "correlationOrg": source_doc.get("correlationOrg", []),
                "tag": source_doc.get("tag", []),
                
                # LLM分析的字段
                "authorAttitude": analysis_result.get("authorAttitude", "中立"),
                "authorViewpoint": analysis_result.get("authorViewpoint", ""),
                "characterEntity": analysis_result.get("characterEntity", []),
                "institutionalEntities": analysis_result.get("institutionalEntities", []),
                "locationEntity": analysis_result.get("locationEntity", []),
                "eventInfo": analysis_result.get("eventInfo", []),
                "summaryFacts": analysis_result.get("summaryFacts", []),
                "summary": analysis_result.get("summary", ""),
                
                # 创建时间
                "createTimeES": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # 生成embedding
            target_doc['embedding'] = get_embedding(target_doc['summary'], embedding_config)
            
            try:
                es.index(
                    index=TARGET_ES_INDEX,
                    id=doc_id,
                    body=target_doc
                )
                processed_count += 1
                print(f"处理文档: {doc_id}")
            except Exception as e:
                traceback.print_exc()
                logger.error(f"保存文档 {doc_id} 时出错: {str(e)}")
        
        logger.info(f"成功处理文档数量: {processed_count}")
        return processed_count
        
    except Exception as e:
        traceback.print_exc()
        logger.error(f"处理数据时出错: {str(e)}")
        return 0
    finally:
        if es:
            try:
                es.close()
                logger.debug("ES客户端已关闭")
            except Exception as close_error:
                traceback.print_exc()
                logger.warning(f"关闭ES客户端时出错: {str(close_error)}")

def get_half_hour_range(end_time_str: str = None):
    """
    获取最近半小时的时间范围
    
    Args:
        end_time_str: 可选的结束时间字符串，格式为 'YYYY-MM-DD HH:MM:SS'
                     如果不提供，则使用当前时间
    
    Returns:
        tuple: (start_time, end_time) 格式为 'YYYY-MM-DD HH:MM:SS'
               start_time 为上一个半小时的开始
               end_time 为上一个半小时的结束
    
    示例:
        当前时间为 14:40:00
        返回 (14:00:00, 14:29:59)
        
        当前时间为 14:10:00
        返回 (13:30:00, 13:59:59)
    """
    # 获取当前时间或解析输入的时间
    if not end_time_str:
        current_time = datetime.now()
    else:
        current_time = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")
    
    # 获取当前分钟数
    minutes = current_time.minute
    
    # 创建基准时间（当前小时的开始）
    base_time = current_time.replace(minute=0, second=0, microsecond=0)
    
    # 如果当前分钟在0-29之间，需要查看上一个小时的后半段
    if minutes < 30:
        start_time = (base_time - timedelta(minutes=30)).strftime("%Y-%m-%d %H:%M:%S")
        end_time = (base_time - timedelta(seconds=1)).strftime("%Y-%m-%d %H:%M:%S")
    # 如果当前分钟在30-59之间，查看当前小时的前半段
    else:
        start_time = base_time.strftime("%Y-%m-%d %H:%M:%S")
        end_time = (base_time + timedelta(minutes=29, seconds=59)).strftime("%Y-%m-%d %H:%M:%S")
    
    logger.info(f"生成时间范围: {start_time} - {end_time}")
    return start_time, end_time

# LangGraph工作流定义
class AgentState(TypedDict):
    """代理的状态"""
    current_docs: List[Dict]  # 查询数据
    start_time: Optional[str]  # 开始时间
    end_time: Optional[str]  # 结束时间

def fetch_data(state):
    """从源ES中获取数据"""
    try:
        print('开始获取数据')
        body = build_body_for_index(state["start_time"], state["end_time"], BIZ_LIST)
        
        all_batches = []
        for batch in get_es_scroll_data_batched(SOURCE_ES_INDEX, body, BATCH_SIZE):
            all_batches.append(batch)
            
        state["current_docs"] = all_batches
        print(f'获取到数据批次数量：{len(all_batches)}')
        return state
            
    except Exception as e:
        logger.error(f"获取数据时出错: {str(e)}")
        state["error"] = f"获取数据出错: {str(e)}"
        return state

def process_batch(state):
    """处理数据批次"""
    sum0dcl = process_data(state["current_docs"])
    print(f"处理完成，共处理{sum0dcl}条数据")
    return state

# 创建工作流
workflow = StateGraph(AgentState)
workflow.add_node("fetch_data", fetch_data)
workflow.add_node("process_batch", process_batch)
workflow.add_edge("fetch_data", "process_batch")
workflow.add_edge("process_batch", END)
workflow.set_entry_point("fetch_data")
workflow = workflow.compile()

def job():
    """定时任务"""
    try:
        start, end = get_half_hour_range()
        logger.info(f"开始处理时间范围: {start} 至 {end}")
        
        initial_state = {
            "current_docs": [],
            "start_time": start,
            "end_time": end
        }
        
        state = fetch_data(initial_state)
        
        if not state["current_docs"]:
            logger.info("当前时间范围内无数据")
        else:
            for batch in state["current_docs"]:
                state["current_docs"] = batch
                state = process_batch(state)
                
        logger.info("本次任务处理完成")
        
    except Exception as e:
        logger.error(f"处理过程中出现错误: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    logger.info("启动微信公众号数据处理服务")
    
    # 设置定时任务，每30分钟执行一次
    schedule.every(30).minutes.do(job)
    
    # 立即执行一次
    job()
    
    # 持续运行定时任务
    while True:
        try:
            schedule.run_pending()
            time.sleep(1)
        except Exception as e:
            logger.error(f"定时任务执行出错: {str(e)}")
            traceback.print_exc()
            time.sleep(60)  # 发生错误时等待1分钟
            continue 