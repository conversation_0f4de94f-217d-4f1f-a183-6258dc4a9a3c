from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class Permission(BaseModel):
    id: Optional[str]
    name: str
    description: Optional[str] = None
    created_at: Optional[datetime] = None
    created_by: Optional[str] = None

class PermissionCreate(BaseModel):
    name: str
    description: Optional[str] = None

class PermissionUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None

class Config:
    from_attributes = True