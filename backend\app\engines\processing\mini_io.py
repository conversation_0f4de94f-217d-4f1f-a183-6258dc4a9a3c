from minio import Minio
import io

def upload_image_to_minio(image_path, minio_endpoint, minio_access_key, minio_secret_key, bucket_name, object_name):
    # Initialize MinIO client
    minio_client = Minio(minio_endpoint,
                         access_key=minio_access_key,
                         secret_key=minio_secret_key,
                         secure=False)  # Set secure=True if using HTTPS

    try:
        # Check if bucket exists, if not, create it
        found = minio_client.bucket_exists(bucket_name)
        if not found:
            minio_client.make_bucket(bucket_name)

        # Upload image to MinIO
        with open(image_path, 'rb') as image_file:
            image_data = image_file.read()
            image_stream = io.BytesIO(image_data)
            image_size = len(image_data)
            # 上传
            minio_client.put_object(bucket_name, object_name, image_stream, image_size)

        # Generate public URL for the uploaded image
        image_url = minio_client.presigned_get_object(bucket_name, object_name)
        return image_url

    except Exception as err:
        print("Error:", err)
        return None


def download_image_from_minio(minio_endpoint, minio_access_key, minio_secret_key, bucket_name, object_name, local_path):
    # Initialize MinIO client
    minio_client = Minio(minio_endpoint,
                         access_key=minio_access_key,
                         secret_key=minio_secret_key,
                         secure=False)  # Set secure=True if using HTTPS

    try:
        # download image from MinIO
        # minio_client.get_object(bucket_name, object_name)
        minio_client.fget_object(bucket_name, object_name, local_path)

    except Exception as err:
        print("Error:", err)
        return None