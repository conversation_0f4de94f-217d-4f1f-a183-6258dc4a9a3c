#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ES到ES数据迁移代理
基于LangGraph实现，包含读取数据、处理数据和保存数据三个节点
"""

import json
import logging
import traceback
import re
import pandas as pd
from typing import Dict, List, Any, Optional, TypedDict
from datetime import datetime, timedelta
import requests
from elasticsearch import Elasticsearch
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langgraph.graph import StateGraph, END

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ES配置
TARGET_ES_HOST = "**************"
TARGET_ES_PORT = 9600
TARGET_ES_USER = None
TARGET_ES_PASSWORD = None
TARGET_ES_INDEX = "pro_mcp_data_zhengce_v2"

BATCH_SIZE = 10

hosts = '************'
port = 9200
http_auth = ('qianxiang', 'aRQ8K$3ymIbG')

# 大模型参数
embedding_service_url = "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/bge_m3_finetune_20240710/v1/embeddings"
embedding_api_key = "ZWY0YzJkZTM4OGI0YmI3YjljOTI4NGE5ZDMxMDVhMzUzZjA3YmYxMw=="
embedding_name = "bge-m3"

llm_service_url = "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen3_32b_2/v1"
llm_api_key = "ZmYzMmQ1N2E2YWZjMzcwN2Y3NTBjNmQzYjk3Njk3ZWY4Njk5MWQ0ZQ=="
OPENAI_MODEL = "Qwen3-32B"

llm = ChatOpenAI(
    temperature=0,
    model=OPENAI_MODEL,
    openai_api_base=llm_service_url,
    api_key=llm_api_key
)

embedding_model = OpenAIEmbeddings(
    model=embedding_name,
    openai_api_key=embedding_api_key,
    openai_api_base=embedding_service_url
)

embedding_config = {
    "api_key": embedding_api_key,
    "service_url": embedding_service_url,
    "embedding_name": embedding_name
}

def get_embedding(text, embedding_config):
    access_token = embedding_config.get("api_key", "")
    service_url = embedding_config.get("service_url", "")
    model = embedding_config.get("embedding_name", "bge-m3")
    try:
        headers = {'Content-Type': 'application/json', 'Authorization': f'{access_token}'}
        req = {
            "input": [text],
            "model": model
        }
        embdd_response = requests.post(url=service_url, json=req, headers=headers)
        if embdd_response.status_code == 200:
            query_embedding = embdd_response.json()['data'][0]['embedding']
            DB_VECTOR_DIMENSION = 1536
            if len(query_embedding) != DB_VECTOR_DIMENSION:
                if len(query_embedding) < DB_VECTOR_DIMENSION:
                    query_embedding = query_embedding + [0.0] * (DB_VECTOR_DIMENSION - len(query_embedding))
                    logger.info(f"向量维度已扩充至 {DB_VECTOR_DIMENSION}")
                else:
                    query_embedding = query_embedding[:DB_VECTOR_DIMENSION]
                    logger.info(f"向量维度已截断至 {DB_VECTOR_DIMENSION}")
            logger.info(f"最终查询向量维度: {len(query_embedding)}")
            return query_embedding
        else:
            logger.error(f"获取embedding失败: {embdd_response.status_code}")
            return None
    except Exception as e:
        traceback.print_exc()
        logger.error(f"从ES获取数据时出错: {str(e)}")
        return None

def build_body(start_time, end_time):
    body = {
        "query": {
            "bool": {
                "filter": [
                    {
                        "range": {
                            "laws_public_time": {
                                "gte": start_time,
                                "lte": end_time
                            }
                        }
                    }
                ]
            }
        },
        "sort": [{"laws_public_time": "desc"}]
    }
    return body

def get_es_scroll_data(index, build_body):  # 滚动查询 获取数据es
    es7 = Elasticsearch(hosts, http_auth=http_auth, port=port, timeout=3600)
    result = es7.search(index=index, scroll='10m', body=build_body, size=1000, request_timeout=3600)
    sid = result['_scroll_id']
    scroll_size = result['hits']['total']['value']
    print(f"数据量：{scroll_size}")
    res_list = []
    while scroll_size > 0:
        source_list = [data for data in result['hits']['hits']]
        res_list.extend(source_list)
        result = es7.scroll(scroll_id=sid, scroll='10m', request_timeout=5000)
        sid = result['_scroll_id']
        scroll_size = len(result['hits']['hits'])
    es7.clear_scroll(scroll_id=sid)
    return res_list

def format_es_date(date_str):
    dt = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
    return dt.strftime("%Y-%m-%d %H:%M:%S")

def get_day_range(end_time_str: str = None):
    """
    输入：结束时间字符串（如 '2024-05-19 15:23:00'），为空则用当前时间
    输出：前一天的开始时间和结束时间（'2024-05-18 00:00:00', '2024-05-18 23:59:59'）
    """
    if not end_time_str or end_time_str.strip() == "":
        dt = datetime.now()
    else:
        dt = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")
    dt = dt - timedelta(days=1)
    day_str = dt.strftime("%Y-%m-%d")
    start_time = f"{day_str} 00:00:00"
    end_time = f"{day_str} 23:59:59"
    return start_time, end_time

from html import unescape
from bs4 import BeautifulSoup

def html_to_markdown(html):
    if not html:
        return ''
    soup = BeautifulSoup(html, 'html.parser')
    for script in soup(["script", "style"]):
        script.extract()
    for i in range(1, 7):
        for tag in soup.find_all(f'h{i}'):
            tag.replace_with(f"{'#' * i} {tag.get_text().strip()}\n\n")
    for tag in soup.find_all('p'):
        tag.replace_with(f"{tag.get_text().strip()}\n\n")
    for ul in soup.find_all('ul'):
        for li in ul.find_all('li'):
            li.replace_with(f"* {li.get_text().strip()}\n")
    for ol in soup.find_all('ol'):
        for i, li in enumerate(ol.find_all('li')):
            li.replace_with(f"{i+1}. {li.get_text().strip()}\n")
    for a in soup.find_all('a', href=True):
        text = a.get_text().strip()
        href = a['href']
        a.replace_with(f"[{text}]({href})")
    for img in soup.find_all('img', src=True):
        alt = img.get('alt', '')
        src = img['src']
        img.replace_with(f"![{alt}]({src})")
    for strong in soup.find_all(['strong', 'b']):
        strong.replace_with(f"**{strong.get_text().strip()}**")
    for em in soup.find_all(['em', 'i']):
        em.replace_with(f"*{em.get_text().strip()}*")
    for blockquote in soup.find_all('blockquote'):
        lines = blockquote.get_text().strip().split('\n')
        quoted_text = '\n'.join([f"> {line}" for line in lines])
        blockquote.replace_with(f"{quoted_text}\n\n")
    for pre in soup.find_all('pre'):
        code = pre.get_text().strip()
        pre.replace_with(f"```\n{code}\n```\n\n")
    for code in soup.find_all('code'):
        code.replace_with(f"`{code.get_text().strip()}`")
    for table in soup.find_all('table'):
        md_table = []
        headers = []
        for th in table.find_all('th'):
            headers.append(th.get_text().strip())
        if headers:
            md_table.append('| ' + ' | '.join(headers) + ' |')
            md_table.append('| ' + ' | '.join(['---'] * len(headers)) + ' |')
        for tr in table.find_all('tr'):
            row = []
            for td in tr.find_all('td'):
                row.append(td.get_text().strip())
            if row:
                md_table.append('| ' + ' | '.join(row) + ' |')
        table.replace_with('\n'.join(md_table) + '\n\n')
    for hr in soup.find_all('hr'):
        hr.replace_with('---\n\n')
    markdown = soup.get_text()
    markdown = re.sub(r'\n{3,}', '\n\n', markdown)
    markdown = markdown.strip()
    return markdown

def analyze_text(title: str, content: str) -> Dict[str, Any]:
    """分析文本内容，提取关键信息，返回结构化字段"""
    prompt = ChatPromptTemplate.from_messages([
        SystemMessage(content="""你是一个专业的政策分析助手，需要分析政策文件并提取以下信息，所有字段均为一级字段：

1. 政策主题 (policy_theme): 列表，如 ["金融监管", "环境保护"]
2. 影响行业 (affected_industries): 列表，如 ["银行业", "房地产"]
3. 监管影响 (policy_impact_regulatory_impact): 字符串
4. 合规要求 (policy_impact_compliance_requirements): 字符串
5. 风险等级 (policy_impact_risk_level): 必须是以下之一："高"、"中"、"低"
6. 政策趋势 (policy_direction_trend): 字符串
7. 政策要点 (policy_direction_key_points): 字符串
8. 实施时间表 (policy_direction_implementation_timeline): 字符串
9. 行业影响 (market_impact_industry_effect): 字符串
10. 商业机会 (market_impact_business_opportunities): 字符串
11. 市场风险 (market_impact_market_risk): 字符串
12. 影响区域 (area): 列表，如 ["北京市", "上海市"]
13. 摘要 (summary): 300字以内摘要，包含政策关键信息
14. 关联法规 (related_laws): 关联法规名称的字符串数组，如 ["公司法", "证券法"]

请以JSON格式返回，包含以上所有字段，字段名必须与上面完全一致。
如: {
    "policy_theme": ["金融监管", "风险防控"],
    "affected_industries": ["银行业", "保险业"],
    "policy_impact_regulatory_impact": "加强金融监管力度",
    "policy_impact_compliance_requirements": "要求金融机构加强风险控制",
    "policy_impact_risk_level": "中",
    "policy_direction_trend": "趋严",
    "policy_direction_key_points": "加强监管、防范风险",
    "policy_direction_implementation_timeline": "2024年1月1日起实施",
    "market_impact_industry_effect": "促进金融业规范发展",
    "market_impact_business_opportunities": "合规业务需求增加",
    "market_impact_market_risk": "短期可能增加合规成本",
    "area": ["全国"],
    "summary": "该政策主要针对...",
    "related_laws": ["公司法", "证券法"]
}"""),
        HumanMessage(content=f"""请分析以下政策文件：\n标题: {title}\n内容: {content}\n/no_think""")
    ])
    chain = prompt | llm
    try:
        result = chain.invoke({})
        raw_content = getattr(result, 'content', result)
        text = re.sub(r'<think>.*?</think>', '', raw_content, flags=re.DOTALL)
        json_match = re.search(r'```json\s*({.*?})\s*```', text, flags=re.DOTALL)
        if not json_match:
            json_match = re.search(r'({.*})', text, flags=re.DOTALL)
        if not json_match:
            logger.error("未找到有效的JSON内容")
            return get_default_result()
        json_str = json_match.group(1)
        json_str = json_str.replace('\\n', ' ').replace('\\r', '')
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)
        try:
            result = json.loads(json_str)
            if not validate_result_structure(result):
                logger.error("JSON结构不符合预期")
                return get_default_result()
            return result
        except json.JSONDecodeError as je:
            logger.error(f"JSON解析错误: {str(je)}")
            return get_default_result()
    except Exception as e:
        logger.error(f"分析文本时出错: {str(e)}")
        return get_default_result()

def validate_result_structure(data: Dict[str, Any]) -> bool:
    """验证返回的JSON结构是否符合预期（全部一级字段）"""
    try:
        required_fields = {
            "policy_theme": list,
            "affected_industries": list,
            "policy_impact_regulatory_impact": str,
            "policy_impact_compliance_requirements": str,
            "policy_impact_risk_level": str,
            "policy_direction_trend": str,
            "policy_direction_key_points": str,
            "policy_direction_implementation_timeline": str,
            "market_impact_industry_effect": str,
            "market_impact_business_opportunities": str,
            "market_impact_market_risk": str,
            "area": list,
            "summary": str,
            "related_laws": list
        }
        for field, field_type in required_fields.items():
            if field not in data:
                logger.error(f"缺少必需字段: {field}")
                return False
            if not isinstance(data[field], field_type):
                logger.error(f"字段类型错误: {field}, 期望 {field_type}, 实际 {type(data[field])}")
                return False
        if data["policy_impact_risk_level"] not in ["高", "中", "低"]:
            logger.error("policy_impact_risk_level 字段值必须为 '高'、'中' 或 '低'")
            return False
        return True
    except Exception as e:
        logger.error(f"验证JSON结构时发生错误: {str(e)}")
        return False

def get_default_result() -> Dict[str, Any]:
    """返回默认的空结果结构（全部一级字段）"""
    return {
        "policy_theme": [],
        "affected_industries": [],
        "policy_impact_regulatory_impact": "",
        "policy_impact_compliance_requirements": "",
        "policy_impact_risk_level": "中",
        "policy_direction_trend": "",
        "policy_direction_key_points": "",
        "policy_direction_implementation_timeline": "",
        "market_impact_industry_effect": "",
        "market_impact_business_opportunities": "",
        "market_impact_market_risk": "",
        "area": [],
        "summary": "",
        "related_laws": []
    }

def format_date(date_value) -> str:
    try:
        if pd.isna(date_value) or date_value is None:
            return "1970-01-01 00:00:00"
        if isinstance(date_value, pd.Timestamp):
            return date_value.strftime("%Y-%m-%d %H:%M:%S")
        if isinstance(date_value, datetime):
            return date_value.strftime("%Y-%m-%d %H:%M:%S")
        if isinstance(date_value, str):
            if date_value == "1970-01-01 00:00:00":
                return date_value
            if 'T' in date_value:
                date_value = date_value.replace('T', ' ')
            return date_value
        return str(date_value)
    except Exception as e:
        logger.warning(f"日期格式转换失败: {date_value}, 类型: {type(date_value)}, 错误: {str(e)}")
        return "1970-01-01 00:00:00"

def process_data(current_docs):
    """处理数据，使用LLM提取信息"""
    es = Elasticsearch([f"{TARGET_ES_HOST}:{TARGET_ES_PORT}"])
    if es.ping():
        print(f"ES连接成功: {TARGET_ES_HOST}:{TARGET_ES_PORT}")
        es_info = es.info()
        logger.info(f"ES版本: {es_info['version']['number']}")
    else:
        print(f"ES连接失败: {TARGET_ES_HOST}:{TARGET_ES_PORT}")
    try:
        processed_count = 0
        for source in current_docs:
            _id = source['_id']
            row = source['_source']
            content = row["laws_text"] if "laws_text" in row else ""
            clean_text = html_to_markdown(content)
            analysis_result = analyze_text(row["laws_title"] if "laws_title" in row else "", clean_text)
            target_doc = {
                "lid": row["lid"] if "lid" in row else None,
                "laws_title": row["laws_title"] if "laws_title" in row else "",
                "laws_text": clean_text,
                "laws_text_search": row["laws_text_search"] if "laws_text_search" in row else "",
                "laws_url": row["laws_url"] if "laws_url" in row else "",
                "laws_site_name": row["laws_site_name"] if "laws_site_name" in row else "",
                "laws_type": row["laws_type"] if "laws_type" in row else "",
                "laws_level": row["laws_level"] if "laws_level" in row else "",
                "laws_level_two": row["laws_level_two"] if "laws_level_two" in row else "",
                "laws_level_file": row["laws_level_file"] if "laws_level_file" in row else "",
                "laws_formulate_authority": row["laws_formulate_authority"] if "laws_formulate_authority" in row else "",
                "laws_approving_authority": row["laws_approving_authority"] if "laws_approving_authority" in row else "",
                "laws_formulate_way": row["laws_formulate_way"] if "laws_formulate_way" in row else "",
                "laws_object": row["laws_object"] if "laws_object" in row else 0,
                "laws_code": row["laws_code"] if "laws_code" in row else "",
                "laws_ageing": row["laws_ageing"] if "laws_ageing" in row else "",
                "laws_written_time": format_date(row.get("laws_written_time")),
                "laws_pass_time": format_date(row.get("laws_pass_time")),
                "laws_public_time": format_date(row.get("laws_public_time")),
                "laws_excute_time": format_date(row.get("laws_excute_time")),
                "laws_expiry_time": format_date(row.get("laws_expiry_time")),
                "laws_clean_time": format_date(row.get("laws_clean_time")),
                "laws_change_pass_time": format_date(row.get("laws_change_pass_time")),
                "laws_change_public_time": format_date(row.get("laws_change_public_time")),
                "laws_change_excute_time": format_date(row.get("laws_change_excute_time")),
                "laws_crawler_time": format_date(row.get("laws_crawler_time")),
                "laws_update_time": format_date(row.get("laws_update_time")),
                "laws_area": row["laws_area"] if "laws_area" in row else "",
                "laws_area_code": row["laws_area_code"] if "laws_area_code" in row else 0,
                "laws_area_address": row["laws_area_address"] if "laws_area_address" in row else "",
                "laws_doc_url": row["laws_doc_url"] if "laws_doc_url" in row else "",
                "laws_doc_local_url": row["laws_doc_local_url"] if "laws_doc_local_url" in row else "",
                "laws_relevant_docs": row["laws_relevant_docs"] if "laws_relevant_docs" in row else "",
                "laws_relevant_datas": row["laws_relevant_datas"] if "laws_relevant_datas" in row else "",
                "laws_brief": row["laws_brief"] if "laws_brief" in row else "",
                "laws_text_from": row["laws_text_from"] if "laws_text_from" in row else 0,
                "law_doc_is_download": row["law_doc_is_download"] if "law_doc_is_download" in row else 0,
                "policy_theme": analysis_result.get("policy_theme", []),
                "affected_industries": analysis_result.get("affected_industries", []),
                "policy_impact_regulatory_impact": analysis_result.get("policy_impact_regulatory_impact", ""),
                "policy_impact_compliance_requirements": analysis_result.get("policy_impact_compliance_requirements", ""),
                "policy_impact_risk_level": analysis_result.get("policy_impact_risk_level", "中"),
                "policy_direction_trend": analysis_result.get("policy_direction_trend", ""),
                "policy_direction_key_points": analysis_result.get("policy_direction_key_points", ""),
                "policy_direction_implementation_timeline": analysis_result.get("policy_direction_implementation_timeline", ""),
                "market_impact_industry_effect": analysis_result.get("market_impact_industry_effect", ""),
                "market_impact_business_opportunities": analysis_result.get("market_impact_business_opportunities", ""),
                "market_impact_market_risk": analysis_result.get("market_impact_market_risk", ""),
                "area": analysis_result.get("area", []),
                "summary": analysis_result.get("summary", ""),
                "related_laws": analysis_result.get("related_laws", []),
                "embedding": get_embedding(analysis_result.get("summary", ""), embedding_config)
            }
            try:
                es.index(
                    index=TARGET_ES_INDEX,
                    id=_id,
                    body=target_doc
                )
                print(f'插入：{_id}')
                processed_count += 1
            except Exception as e:
                traceback.print_exc()
                logger.error(f"保存文档 {row['_id'] if '_id' in row else 'unknown'} 时出错: {str(e)}")
        logger.info(f"处理文档: {processed_count}")
        return processed_count
    except Exception as e:
        traceback.print_exc()
        logger.error(f"处理数据时出错: {str(e)}")
    finally:
        if es:
            try:
                es.close()
                logger.debug("ES客户端已关闭")
            except Exception as close_error:
                traceback.print_exc()
                logger.warning(f"关闭ES客户端时出错: {str(close_error)}")

# 工作流定义
class AgentState(TypedDict):
    current_docs: List[Dict]
    start_time: Optional[str]
    end_time: Optional[str]

def fetch_data(state):
    body = build_body(state["start_time"], state["end_time"])
    es_data = get_es_scroll_data('wzty_laws_database_alias', body)
    state["current_docs"] = es_data
    return state

def process_batch(state):
    sum0dcl = process_data(state["current_docs"])
    print(sum0dcl)
    return state

workflow = StateGraph(AgentState)
workflow.add_node("fetch_data", fetch_data)
workflow.add_node("process_batch", process_batch)
workflow.add_edge("fetch_data", "process_batch")
workflow.add_edge("process_batch", END)
workflow.set_entry_point("fetch_data")
workflow = workflow.compile()

# 示例循环调用
if __name__ == "__main__":
    sum = 100
    now_index = 0
    start, end = get_day_range()
    while now_index < sum:
        print("开始时间:", start)
        print("结束时间:", end)
        initial_state = {
            "current_docs": [],
            "start_time": start,
            "end_time": end
        }
        # 注意：workflow.ainvoke 需在异步环境下运行
        # result = await workflow.ainvoke(initial_state)
        # 这里可用同步方式或手动调用节点测试
        state = fetch_data(initial_state)
        state = process_batch(state)
        now_index += 1
        start, end = get_day_range(end) 