from fastapi import APIRouter, Query
from typing import List, Optional
import random
from datetime import datetime
from pydantic import BaseModel

router = APIRouter()

# 定义规则项模型
class RuleListItem(BaseModel):
    key: int
    href: str
    avatar: str
    name: str
    owner: str
    desc: str
    callNo: int
    status: int
    updatedAt: str
    createdAt: str
    progress: int

# 模拟数据存储
table_list_data_source: List[RuleListItem] = []

# 生成初始数据
for i in range(100):
    table_list_data_source.append(RuleListItem(
        key=i,
        href='https://ant.design',
        avatar=['https://gw.alipayobjects.com/zos/rmsportal/eeHMaZBwmTvLdIwMfBpg.png', 
                'https://gw.alipayobjects.com/zos/rmsportal/udxAbMEhpwthVVcjLXik.png'][i % 2],
        name=f'TradeCode {i}',
        owner='曲丽丽',
        desc='这是一段描述',
        callNo=random.randint(0, 999),
        status=random.randint(0, 1),
        updatedAt=datetime.now().strftime('%Y-%m-%d'),
        createdAt=datetime.now().strftime('%Y-%m-%d'),
        progress=random.randint(0, 100)
    ))

@router.get("/api/rule")
async def get_rule(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size")
):
    start = (current - 1) * pageSize
    end = start + pageSize
    return {
        "data": table_list_data_source[start:end],
        "total": len(table_list_data_source),
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

@router.post("/api/rule")
async def add_rule(rule: RuleListItem):
    table_list_data_source.insert(0, rule)
    return {"success": True, "data": rule}

@router.put("/api/rule")
async def update_rule(rule: RuleListItem):
    for i, item in enumerate(table_list_data_source):
        if item.key == rule.key:
            table_list_data_source[i] = rule
            return {"success": True, "data": rule}
    return {"success": False, "message": "Rule not found"}

@router.delete("/api/rule")
async def delete_rule(key: int):
    global table_list_data_source
    table_list_data_source = [item for item in table_list_data_source if item.key != key]
    return {"success": True}