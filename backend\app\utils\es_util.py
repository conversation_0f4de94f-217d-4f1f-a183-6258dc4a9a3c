# encoding:utf8
# from elasticsearch5 import Elasticsearch
from tqdm import tqdm
import requests
import logging
# from app.utils.es_utils import insert_chunk_index_to_es
# from app.core.config import settings  # 假设ES配置在settings
from datetime import datetime


# class ESUtil():
#     def __init__(self, es_addresses, user_name, password, cluster_name):
#         # elasticsearch集群服务器的地址
#         self.ES = es_addresses
#         self.user_name, self.password, self.cluster_name = user_name, password, cluster_name

#     def get_es(self):
#         # 创建elasticsearch客户端
#         es = Elasticsearch(
#             self.ES,
#             # 启动前嗅探es集群服务器
#             # i
#             # es集群服务器结点连接异常时是否刷新es节点信息
#             sniff_on_connection_fail=True,
#             # 每60秒刷新节点信息
#             sniffer_timeout=60,
#             sniff_timeout=30,
#             http_auth=(self.user_name, self.password) if self.user_name else None,
#             cluster=self.cluster_name,
#             timeout=30, max_retries=1, retry_on_timeout=True
#         )

#         if es.ping(): print('ES连接成功')

#         return es

#     def get_data_ids(self, search_doc, es, es_index, detail=False):
#         res = []
#         page_num = 100  # 每次获取数据
#         if es_index:
#             query = es.search(index=es_index, doc_type="type_doc_test", size=page_num, scroll='5m', body=search_doc)
#         else:
#             query = es.search(index='index_sy', doc_type="type_doc_test", size=page_num, scroll='5m', body=search_doc)

#         pre_results = query['hits']['hits']  # es查询出的结果第一页
#         total = query['hits']['total']  # es查询出的结果总量
#         scroll_id = query['_scroll_id']  # 游标用于输出es查询出的所有结果
#         every_num = int(total / page_num)

#         for i in tqdm(range(0, every_num + 1)):

#             # scroll参数必须指定否则会报错
#             query_scroll = es.scroll(scroll_id=scroll_id, scroll='5m')['hits']['hits']

#             results = query_scroll
#             if pre_results:
#                 results += pre_results
#                 pre_results = []

#             for hit in results:
#                 if detail:
#                     data = hit['_source']
#                     res.append(data)
#                 else:
#                     res.append(hit['_id'])
#         return res

#     def get_data_once(self, search_doc, es, es_index, size=10, detail=False):
#         res = []

#         query = es.search(index=es_index, doc_type="type_doc_test", size=size, body=search_doc)

#         results = query['hits']['hits']  # es查询出的结果第一页

#         for hit in results:
#             if detail:
#                 data = hit['_source']
#                 res.append(data)
#             else:
#                 res.append(hit['_id'])
#         return res

def insert_chunk_index_to_es(es_host, index_name, doc_id, doc_body):
    """
    通过HTTP请求将chunk_index插入到ES
    :param es_host: ES主机地址（如 http://localhost:9200）
    :param index_name: 索引名
    :param doc_id: 文档ID
    :param doc_body: 文档内容（dict）
    :return: 插入结果
    """
    url = f"{es_host}/{index_name}/_doc/{doc_id}"
    try:
        response = requests.put(url, json=doc_body, timeout=5)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logging.error(f"插入ES失败: {e}")
        raise