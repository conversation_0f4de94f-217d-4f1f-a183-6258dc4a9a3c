"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9139],{51042:function(e,r,n){var t=n(1413),a=n(67294),u=n(42110),s=n(91146),i=function(e,r){return a.createElement(s.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:r,icon:u.Z}))},c=a.forwardRef(i);r.Z=c},41742:function(e,r,n){n.r(r),n.d(r,{default:function(){return O}});var t=n(15009),a=n.n(t),u=n(97857),s=n.n(u),i=n(99289),c=n.n(i),l=n(5574),o=n.n(l),d=n(67294),p=n(97131),h=n(12453),m=n(8232),f=n(2453),x=n(83622),v=n(17788),Z=n(55102),b=n(34041),j=n(51042),y=n(78158);function g(e){return I.apply(this,arguments)}function I(){return(I=c()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,y.N)("/api/embeddings",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function k(e){return w.apply(this,arguments)}function w(){return(w=c()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,y.N)("/api/embeddings",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function P(e){return _.apply(this,arguments)}function _(){return(_=c()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,y.N)("/api/embeddings/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function T(e){return q.apply(this,arguments)}function q(){return(q=c()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,y.N)("/api/embeddings/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var C=n(85893),O=function(){var e=(0,d.useState)(!1),r=o()(e,2),n=r[0],t=r[1],u=(0,d.useState)(!1),i=o()(u,2),l=i[0],y=i[1],I=(0,d.useState)(void 0),w=o()(I,2),_=w[0],q=w[1],O=(0,d.useRef)(),z=m.Z.useForm(),A=o()(z,1)[0],R=function(){var e=c()(a()().mark((function e(r){var n,u;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=f.ZP.loading("正在添加"),e.prev=1,console.log(r),e.next=5,k(s()({},r));case 5:return n(),f.ZP.success("添加成功"),t(!1),null===(u=O.current)||void 0===u||u.reload(),A.resetFields(),e.abrupt("return",!0);case 13:return e.prev=13,e.t0=e.catch(1),n(),f.ZP.error("添加失败，请重试"),e.abrupt("return",!1);case 18:case"end":return e.stop()}}),e,null,[[1,13]])})));return function(r){return e.apply(this,arguments)}}(),S=function(){var e=c()(a()().mark((function e(r){var n,t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r.id){e.next=3;break}return f.ZP.error("更新失败，缺少嵌入 ID"),e.abrupt("return",!1);case 3:return n=f.ZP.loading("正在更新"),e.prev=4,e.next=7,P(r);case 7:return n(),f.ZP.success("更新成功"),y(!1),q(void 0),null===(t=O.current)||void 0===t||t.reload(),A.resetFields(),e.abrupt("return",!0);case 16:return e.prev=16,e.t0=e.catch(4),n(),f.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 21:case"end":return e.stop()}}),e,null,[[4,16]])})));return function(r){return e.apply(this,arguments)}}(),E=function(){var e=c()(a()().mark((function e(r){var n,t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=f.ZP.loading("正在删除"),e.prev=1,e.next=4,T(r.id);case 4:return n(),f.ZP.success("删除成功"),null===(t=O.current)||void 0===t||t.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),n(),f.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(r){return e.apply(this,arguments)}}(),F=[{title:"ID",dataIndex:"id",valueType:"digit"},{title:"名称",dataIndex:"name",valueType:"text"},{title:"服务地址",dataIndex:"service_url",valueType:"text"},{title:"接口类型",dataIndex:"provider",valueType:"text",search:!1},{title:"状态",dataIndex:"is_active",valueType:"boolean",render:function(e){return e?"激活":"未激活"},search:!1},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"操作",dataIndex:"option",valueType:"option",render:function(e,r){return[(0,C.jsx)(x.ZP,{type:"link",onClick:function(){y(!0),q(r)},children:"编辑"},"edit-".concat(r.id)),(0,C.jsx)(x.ZP,{type:"link",danger:!0,onClick:function(){return E(r)},children:"删除"},"delete-".concat(r.id))]}}];return(0,C.jsxs)(p._z,{children:[(0,C.jsx)(h.Z,{headerTitle:"嵌入管理",actionRef:O,rowKey:"id",search:{labelWidth:120,defaultCollapsed:!0},toolBarRender:function(){return[(0,C.jsxs)(x.ZP,{type:"primary",onClick:function(){t(!0)},children:[(0,C.jsx)(j.Z,{})," 新建"]},"primary")]},request:function(){var e=c()(a()().mark((function e(r){var n;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,g(s()({current:r.current,pageSize:r.pageSize},r));case 2:return n=e.sent,e.abrupt("return",{data:n.data,success:n.success,total:n.total});case 4:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),columns:F}),(0,C.jsx)(v.Z,{visible:n,title:"新建嵌入",onCancel:function(){return t(!1)},onOk:function(){return A.submit()},children:(0,C.jsxs)(m.Z,{form:A,layout:"horizontal",onFinish:R,initialValues:{provider:"openai"},labelCol:{span:6},wrapperCol:{span:18},children:[(0,C.jsx)(m.Z.Item,{name:"name",label:"名称",rules:[{required:!0,message:"请输入名称"}],children:(0,C.jsx)(Z.Z,{})}),(0,C.jsx)(m.Z.Item,{name:"embedding_name",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:(0,C.jsx)(Z.Z,{})}),(0,C.jsx)(m.Z.Item,{name:"vector_size",label:"向量大小",initialValue:300,rules:[{required:!0,message:"请输入向量大小"}],children:(0,C.jsx)(Z.Z,{type:"number"})}),(0,C.jsx)(m.Z.Item,{name:"service_url",label:"服务地址",rules:[{required:!0,message:"请输入服务地址"},{pattern:/^(http|https):\/\/[^ "]+$/,message:"请输入有效的 URL，必须以 http 或 https 开头"}],children:(0,C.jsx)(Z.Z,{})}),(0,C.jsx)(m.Z.Item,{name:"api_key",label:"API Key",rules:[{required:!0,message:"请输入 API Key"}],children:(0,C.jsx)(Z.Z,{})}),(0,C.jsx)(m.Z.Item,{name:"provider",label:"接口标准",rules:[{required:!0,message:"请选择接口调用标准"}],children:(0,C.jsx)(b.default,{children:(0,C.jsx)(b.default.Option,{value:"openai",children:"OpenAI"})})})]})}),_&&(0,C.jsx)(v.Z,{visible:l,title:"更新嵌入",onCancel:function(){return y(!1)},onOk:function(){return A.submit()},children:(0,C.jsxs)(m.Z,{form:A,layout:"horizontal",initialValues:_,onFinish:S,labelCol:{span:6},wrapperCol:{span:18},children:[(0,C.jsx)(m.Z.Item,{name:"id",hidden:!0,children:(0,C.jsx)(Z.Z,{})}),(0,C.jsx)(m.Z.Item,{name:"name",label:"名称",rules:[{required:!0,message:"请输入名称"}],children:(0,C.jsx)(Z.Z,{})}),(0,C.jsx)(m.Z.Item,{name:"embedding_name",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:(0,C.jsx)(Z.Z,{})}),(0,C.jsx)(m.Z.Item,{name:"service_url",label:"服务地址",rules:[{required:!0,message:"请输入服务地址"},{pattern:/^(http|https):\/\/[^ "]+$/,message:"请输入有效的 URL，必须以 http 或 https 开头"}],children:(0,C.jsx)(Z.Z,{})}),(0,C.jsx)(m.Z.Item,{name:"api_key",label:"API Key",rules:[{required:!0,message:"请输入 API Key"}],children:(0,C.jsx)(Z.Z,{})}),(0,C.jsx)(m.Z.Item,{name:"provider",label:"接口标准",rules:[{required:!0,message:"请选择接口调用标准"}],children:(0,C.jsx)(b.default,{children:(0,C.jsx)(b.default.Option,{value:"openai",children:"OpenAI"})})})]})})]})}}}]);