<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问答 Stream Chat 测试</title>
    <style>
        body, html {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        #chatArea {
            flex: 1;
            padding: 10px;
            overflow-y: auto;
            border: 1px solid #ccc;
            margin: 10px;
        }
        .message {
            margin: 5px;
            padding: 10px;
            border-radius: 10px;
            color: #fff;
        }
        .user-message {
            background-color: #007bff;
            align-self: flex-end;
        }
        .bot-message {
            background-color: #28a745;
            align-self: flex-start;
        }
        #userInput, button {
            padding: 10px;
            margin: 10px;
            width: calc(100% - 22px); /* Adjust based on margin and padding */
        }
        button {
            width: auto;
        }
    </style>
</head>
<body></body>
    <div id="chatArea"></div>
    <input type="text" id="userInput" placeholder="Type your message here..." />
    <button onclick="sendChat()">Send</button>

    <script>
        async function sendChat() {
            let userInput = document.getElementById("userInput").value.trim();
            if (!userInput) return; // Don't send empty messages

            appendMessage(userInput, 'user-message'); // Display user's message
            document.getElementById("userInput").value = ''; // Clear input after sending

            const token = "your_token_here"; // Replace with your actual token
            const data = {
                "app_info": "complianceQA",
                "conversation_id": "670cc0712c22f6e196aa7179",
                "user_id": 1,
                "messages": [
                    {
                        "content": userInput,
                        "createAt": Date.now(),
                        "id": "message_id",
                        "updateAt": Date.now(),
                        "message": userInput,
                        "role": "user",
                        "meta": {"avatar": "😀"}
                    }
                ]
            };

            try {
                const response = await fetch('/api/app/chat/complianceQA/status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token,
                        'Accept': 'text/event-stream' // Ensure the server knows we accept SSE
                    },
                    // body: JSON.stringify(data),
                    // The following options help with keeping the connection alive
                    cache: 'no-cache',
                    keepalive: true,
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                if (!response.body) {
                    throw new Error('ReadableStream not yet supported in this browser.');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let fullAnswer = '';
                let partialData = '';

                while (true) {
                    const { value, done } = await reader.read();
                    if (done) break;

                    partialData += decoder.decode(value, { stream: true });

                    let messages = partialData.split('\n\n');
                    partialData = messages.pop(); // Keep incomplete data

                    for (let message of messages) {
                        if (message.trim() === '') continue;
                        console.log(message);

                        let lines = message.split('\n');
                        let eventType = null;
                        let dataLine = null;

                        for (let line of lines) {
                            if (line.startsWith('event: ')) {
                                eventType = line.substring(7).trim();
                            } else if (line.startsWith('data: ')) {
                                dataLine = line.substring(6);
                            }
                        }
                        fullAnswer += lines;
                        updateBotMessage(fullAnswer);

                        // if (dataLine) {
                        //     if (eventType === 'answer') {
                        //         if (dataLine === '[DONE]') {
                        //             // Stream finished
                        //             return;
                        //         } else {
                        //             const parsedData = JSON.parse(dataLine);
                        //             const content = parsedData.choices[0].delta.content;
                        //             fullAnswer += content;
                        //             updateBotMessage(fullAnswer);
                        //         }
                        //     } else if (eventType === 'moduleStatus') {
                        //         // Handle moduleStatus event if needed
                        //         console.log('Module Status:', JSON.parse(dataLine));
                        //     }
                        // }
                    }
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }

        function appendMessage(text, className) {
            let div = document.createElement('div');
            div.classList.add('message', className);
            div.innerHTML = text.replace(/\n/g, '<br>'); // Replace newlines with <br>
            document.getElementById('chatArea').appendChild(div);
            div.scrollIntoView({ behavior: 'smooth' });
        }

        function updateBotMessage(text) {
            let botMessageContainer = document.querySelector('.bot-message:last-child');
            if (!botMessageContainer) {
                botMessageContainer = document.createElement('div');
                botMessageContainer.classList.add('message', 'bot-message');
                document.getElementById('chatArea').appendChild(botMessageContainer);
            }
            botMessageContainer.innerHTML = text.replace(/\n/g, '<br>'); // Update text
            botMessageContainer.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>