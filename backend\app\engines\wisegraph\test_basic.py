"""
WiseGraph 基础功能测试
"""

import asyncio
from wisegraph.graph_builder import GraphBuilder
from wisegraph.graph_retriever import GraphRetriever

from loguru import logger

# 简单测试数据
TEST_CHUNKS = {
    "test_chunk": "余春明是某公司的董事长，负责公司的整体战略规划。该公司主要从事股票发行业务。"
}

async def test_graph_building():
    """测试图构建功能"""
    print("🧪 测试图构建功能...")
    
    builder = GraphBuilder()
    
    try:
        await builder.initialize()
        print("✅ GraphBuilder初始化成功")
        
        result = await builder.build_graph_from_chunks(TEST_CHUNKS, clear_existing=True)
        
        if result["success"]:
            print("✅ 图构建测试通过")
            print(f"   创建实体: {result['total_entities_created']}")
            print(f"   创建关系: {result['total_relationships_created']}")
            return True
        else:
            print("❌ 图构建测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 图构建测试异常: {e}")
        return False
    finally:
        await builder.close()

async def test_graph_retrieval():
    """测试图检索功能"""
    print("\n🧪 测试图检索功能...")
    
    retriever = GraphRetriever()
    
    try:
        await retriever.initialize()
        print("✅ GraphRetriever初始化成功")
        
        result = await retriever.retrieve("余春明是谁？")
        
        if result["context"]:
            print("✅ 图检索测试通过")
            print(f"   上下文长度: {len(result['context'])}")
            print(f"   找到实体: {len(result['entities_found'])}")
            return True
        else:
            print("❌ 图检索测试失败 - 未返回上下文")
            return False
            
    except Exception as e:
        print(f"❌ 图检索测试异常: {e}")
        return False
    finally:
        await retriever.close()

async def main():
    """主测试函数"""
    print("🚀 WiseGraph 基础功能测试")
    print("=" * 40)
    
    # 测试图构建
    build_success = await test_graph_building()
    
    if build_success:
        # 测试图检索
        retrieve_success = await test_graph_retrieval()
        
        if retrieve_success:
            print("\n🎉 所有测试通过！")
        else:
            print("\n❌ 检索测试失败")
    else:
        print("\n❌ 构建测试失败，跳过检索测试")

if __name__ == "__main__":
    asyncio.run(main())
