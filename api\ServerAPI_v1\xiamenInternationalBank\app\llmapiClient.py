# import httpx
from typing import AsyncGenerator, List, Dict, Any, Optional
from .langgraphchat import FlowRAG
import httpx
import traceback
import json
from .models import KnowledgeQAParams, DeepSeekConfig  # 添加这行导入语句
from dotenv import load_dotenv
import os
from .models import Provider

# 加载环境变量
load_dotenv('../.env')

# 从环境变量中获取配置
DEFAULT_MODEL = os.getenv('LLM_MODEL_NAME', 'DeepSeek-R1-Distill-Qwen-32B')
DEFAULT_PROVIDER = os.getenv('LLM_PROVIDER', Provider.DEEPSEEK)

# 设置日志配置
from .logging_config import setup_logging, get_logger
setup_logging()
logger = get_logger(__name__)

def prepare_llm_request(
    messages: List[Dict[str, Any]],
    params: Dict[str, Any],
    provider: str = Provider.OPENAI
) -> tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """
    统一处理 LLM 请求的消息和参数
    
    Args:
        messages: 原始消息列表
        params: 请求参数
        provider: 模型提供商
    
    Returns:
        tuple[处理后的消息列表, 处理后的参数]
    """
    logger.info("=========================处理信息===============================")
    processed_messages = messages
    validated_params = params.copy()
    
    # 新增：移除空的系统消息
    if processed_messages and processed_messages[0]['role'] == 'system' and not processed_messages[0]['content'].strip():
        processed_messages = processed_messages[1:]  # 移除空系统消息
        logger.info(f"[{provider}] 移除了空系统消息")

    # 处理 DeepSeek 特殊要求
    if provider == Provider.DEEPSEEK:
        # 1. 处理消息格式，确保用户和助手消息交替
        if messages:
            processed_messages = [messages[0]]  # 保留第一条消息
            for i in range(1, len(messages)):
                current_msg = messages[i]
                prev_msg = processed_messages[-1]
                if current_msg['role'] == prev_msg['role']:
                    prev_msg['content'] = f"{prev_msg['content']}\n{current_msg['content']}"
                else:
                    processed_messages.append(current_msg)
        
        # 2. 验证和调整参数
        if 'top_p' in validated_params:
            top_p = float(validated_params['top_p'])
            if top_p <= 0 or top_p > 1.0:
                validated_params['top_p'] = 0.9
                logger.warning(f"[DeepSeek] Invalid top_p value: {top_p}, using default value: 0.9")
                
        # 3. 其他 DeepSeek 特定的参数处理
        if 'temperature' in validated_params:
            temp = float(validated_params['temperature'])
            if temp < 0 or temp > 1.0:
                validated_params['temperature'] = 0.7
                logger.warning(f"[DeepSeek] Invalid temperature value: {temp}, using default value: 0.7")
                
        # 4. DeepSeek 的 max_tokens 限制 [1, 8192]
        if 'max_tokens' in validated_params:
            max_tokens = int(validated_params['max_tokens'])
            if max_tokens < 1:
                validated_params['max_tokens'] = 2000
                logger.warning(f"[DeepSeek] Invalid max_tokens value: {max_tokens}, using default value: 2000")
            elif max_tokens > 8192:
                validated_params['max_tokens'] = 8192
                logger.warning(f"[DeepSeek] max_tokens value {max_tokens} exceeds limit, using max value: 8192")
                
    # 处理豆包 API 的特殊要求
    elif provider == Provider.DOUBAO:
        # 1. 验证和调整 top_p
        if 'top_p' in validated_params:
            top_p = float(validated_params['top_p'])
            if top_p <= 0 or top_p > 1.0:
                validated_params['top_p'] = 0.9
                logger.warning(f"[Doubao] Invalid top_p value: {top_p}, using default value: 0.9")
        
        # 2. 验证和调整 temperature
        if 'temperature' in validated_params:
            temp = float(validated_params['temperature'])
            if temp < 0 or temp > 1.0:
                validated_params['temperature'] = 0.7
                logger.warning(f"[Doubao] Invalid temperature value: {temp}, using default value: 0.7")
        
        # 3. 验证和调整 max_tokens（豆包限制为 4096）
        if 'max_tokens' in validated_params:
            max_tokens = int(validated_params['max_tokens'])
            if max_tokens < 1:
                validated_params['max_tokens'] = 2000
                logger.warning(f"[Doubao] Invalid max_tokens value: {max_tokens}, using default value: 2000")
            elif max_tokens > 4096:
                validated_params['max_tokens'] = 4096
                logger.warning(f"[Doubao] max_tokens value {max_tokens} exceeds limit, using max value: 4096")
    
    else:
        # 通用参数验证
        if 'max_tokens' in validated_params:
            max_tokens = int(validated_params['max_tokens'])
            if max_tokens <= 0:
                validated_params['max_tokens'] = 2000
                logger.warning(f"Invalid max_tokens value: {max_tokens}, using default value: 2000")
        
        if 'top_p' in validated_params:
            top_p = float(validated_params['top_p'])
            if top_p <= 0 or top_p > 1.0:
                validated_params['top_p'] = 0.9
                logger.warning(f"Invalid top_p value: {top_p}, using default value: 0.9")
    
    # 移除空值参数
    validated_params = {k: v for k, v in validated_params.items() if v is not None}
    
    return processed_messages, validated_params

async def stream_openai_api(
    api_key: str, 
    model: str, 
    messages: List[Dict[str, Any]], 
    url: str,
    extra: Optional[Dict[str, Any]] = None,
    system_prompt: Optional[str] = None,
    provider: str = Provider.OPENAI
) -> AsyncGenerator[str, None]:
    logger.info('==>stream_openai_api')
    logger.info(f"URL: {url}")
    logger.info(f"Model: {model}")
    logger.info(f"Provider: {provider}")
    
    # 验证必要参数
    if not api_key or not model or not messages:
        raise ValueError("Missing required parameters: api_key, model, or messages")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 智能处理 URL 路径
    question_url = url if url.endswith('/chat/completions') else f'{url}/chat/completions'
    
    data = {
        "model": model,
        "messages": messages,
        "stream": True
    }
    timeout = 300
    
    if system_prompt:
        if not messages or len(messages) == 0:
            messages = [{"role": "system", "content": system_prompt}]
        else:
            messages[0]['content'] = system_prompt
            
    if extra:
        # 只添加有效的参数
        valid_params = ['temperature', 'top_p', 'timeout']
        for param in valid_params:
            if param in extra and extra[param] is not None:
                if param == 'timeout':
                    timeout = extra[param]
                else:
                    data[param] = extra[param]
    
    # 统一处理消息和参数
    processed_messages, validated_params = prepare_llm_request(messages, data, provider)
    validated_params['messages'] = processed_messages
            
    logger.info("=========================请求信息===============================")
    logger.info(f"Request URL: {question_url}")
    logger.info(f"Request Headers: {headers}")
    logger.info(f"Request Data: {validated_params}")
    logger.info("========================================================")

    try:
        async with httpx.AsyncClient() as client:
            async with client.stream(
                'POST', 
                question_url, 
                headers=headers, 
                json=validated_params,
                timeout=timeout
            ) as response:
                logger.info(f"Response Status Code: {response.status_code}")
                if response.status_code != 200:
                    error_body = await response.aread()
                    logger.error(f"API Error - Status Code: {response.status_code}")
                    logger.error(f"Response Headers: {response.headers}")
                    logger.error(f"Error Body: {error_body.decode('utf-8')}")
                    yield f"event: error\ndata: API request failed with status code: {error_body.decode('utf-8')}\n\n"
                    return
                # logger.info(f"Response data: {response.json()}")
                async for chunk in response.aiter_bytes():
                    # logger.info(f"收到流式响应: {chunk}")
                    try:
                        decoded = chunk.decode('utf-8')
                        # logger.info(f"解码后的流式响应: {decoded}")
                        # 使用更可靠的分割方式处理数据块
                        for line in decoded.split('\n\n'):
                            line = line.strip()
                            if not line:
                                continue
                                
                            # 规范SSE格式
                            if line.startswith('data: '):
                                # 直接转发规范格式的数据
                                # logger.info(f"转发规范格式的数据: {line}")
                                yield f"{line}\n\n"
                            else:
                                # 处理非标准格式数据
                                if '{' in line:
                                    json_data = line[line.find('{'):]
                                    # logger.info(f"处理非标准格式数据: {json_data}")
                                    yield f"data: {json_data}\n\n"
                    
                    except UnicodeDecodeError as e:
                        traceback.print_exc()
                        logger.error(f"Decode Error: {str(e)}")
                        continue
    except httpx.RequestError as e:
        traceback.print_exc()
        error_message = f"请求错误: {str(e)}"
        logger.error(error_message)
        yield f"event: error\ndata: {error_message}\n\n"
    except Exception as e:
        error_message = f"未知错误: {str(e)}"
        logger.error(error_message)
        traceback.print_exc()
        yield f"event: error\ndata: {error_message}\n\n"


async def openai_api(api_key: str, 
                            model: str, 
                            messages: List[Dict[str, Any]], 
                            url: str,
                            extra: Optional[Dict[str, Any]] = None,
                            system_prompt: Optional[str] = None,
                            provider: str = Provider.OPENAI) -> Dict[str, Any]:
    logger.info('==>openai_api')
    logger.info(f"URL: {url}")
    logger.info(f"Model: {model}")
    logger.info(f"Provider: {provider}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": model,
        "messages": messages,
        "stream": False
    }
    
    if system_prompt:
        data['messages'][0]['content'] = system_prompt
        
    if extra:
        # 直接使用已经验证过的参数
        for key in ['temperature', 'top_p', 'max_tokens']:
            if key in extra:
                data[key] = extra[key]
    
    # 在发送请求前进行参数验证
    processed_messages, validated_params = prepare_llm_request(messages, data, provider)
    validated_params['messages'] = processed_messages
                
    logger.info("Request Headers:")
    logger.info(headers)
    logger.info("Request Data:")
    logger.info(validated_params)
            
    async with httpx.AsyncClient(timeout=httpx.Timeout(60.0)) as client:  # 设置60秒超时
        try:
            response = await client.post(f'{url}/chat/completions', headers=headers, json=validated_params)
            logger.info(f"API响应: {response.json()}")
            if response.status_code == 200:
                return response.json()
            else:
                error_body = await response.aread()
                logger.error(f"API Error - Status Code: {response.status_code}")
                logger.error(f"Response Headers: {response.headers}")
                logger.error(f"Error Body: {error_body.decode('utf-8')}")
                raise Exception(f"API request failed with status code: {response.status_code}")
        except httpx.ReadTimeout:
            error_message = "请求超时，请稍后重试"
            logger.error(error_message)
            return {"error": error_message}
        except Exception as e:
            traceback.print_exc()
            error_message = f"API请求失败: {str(e)}"
            logger.error(error_message)
            return {"error": error_message}

async def chat_model_api(
    chat_id: str,
    messages: List[Dict[str, Any]],
    knowledgebase_ids: List[str],
    stream: bool,
    detail: bool
) -> AsyncGenerator[str, None]:
    try:
        messages_list = convert_messages(messages)      
        logger.info(f'调用 KNOWLEDGE_QA - stream: {stream}, detail: {detail}')
        
        # 调用langgraphchat 的FlowRAG
        kg_qa_flow_rag = FlowRAG(knowledgebase_ids=knowledgebase_ids)
        user_query = messages_list[-1]['content']
        logger.info(f"用户查询: {user_query}")
        
        # 等待异步结果
        kg_qa_results = await kg_qa_flow_rag.run(user_query)
        logger.info(f"知识库检索结果: {json.dumps(kg_qa_results, ensure_ascii=False)}")
        last_message = kg_qa_results['output']
        messages_list[-1] = last_message

        # 使用 DeepSeekConfig 获取配置
        service_config = DeepSeekConfig.get_config()
        
        # 只有在 detail=True 时才发送模块状态和引用列表
        if detail:
            yield json.dumps({
                "event": "moduleStatus",
                "data": {
                    "status": "running",
                    "name": "知识库搜索"
                }
            }) + "\n\n"

            # 构建引用列表
            quoteList = {
                "moduleName": "AI 对话",
                "moduleType": "chatNode",
                "model": service_config["model"],
                "query": user_query,
                "quoteList": [
                    {
                        "content": res["content"],
                        "score": res["score"]
                    }
                    for res in kg_qa_results.get("search_results", [])
                ]
            }
            yield f'event: appStreamResponse\ndata: [{json.dumps(quoteList, ensure_ascii=False)}]\n\n'
            yield 'event: moduleStatus\ndata: {"status":"running","name":"AI 对话"}\n\n'
        
        # 记录发送给模型的消息
        logger.info(f"发送给模型的消息: {json.dumps(messages_list, ensure_ascii=False)}")
        
        if stream:
            # 流式响应
            full_response = []
            async for chunk in stream_openai_api(
                service_config["api_key"],
                service_config["model"],
                messages_list,
                service_config["service_url"],
                provider=service_config["provider"]
            ):
                # 打印一下chunk
                # logger.info(f"流式响应========: {chunk}")
                # 提取实际内容
                if 'data: ' in chunk:
                    try:
                        json_str = chunk.replace('data: ', '').strip()
                        if json_str:
                            chunk_data = json.loads(json_str)
                            content = chunk_data.get('choices', [{}])[0].get('delta', {}).get('content', '')
                            if content:
                                full_response.append(content)
                    except json.JSONDecodeError:
                        pass
                # 这里直接返回LLM的响应
                yield json.dumps(chunk) + "\n\n"
            
            logger.info(f"模型完整响应: {''.join(full_response)}")
        else:
            # 非流式响应
            response = await openai_api(
                service_config["api_key"],
                service_config["model"],
                messages_list,
                service_config["service_url"],
                provider=service_config["provider"]
            )
            # logger.info(f"LLM原本的模型响应: {response}")
            content = response.get('choices', [{}])[0].get('message', {}).get('content', '')
            logger.info(f"取模型响应的content内容: {content}")
            yield json.dumps(response) + "\n\n"
        
    except Exception as e:
        error_message = f"发生错误：{str(e)}"
        traceback.print_exc()
        logger.error(error_message)
        yield f"event: error\ndata: {error_message}\n\n"






def convert_messages(original_messages):
    converted_messages = []

    # 添加系统消息（如果需要）
    converted_messages.append({
        "role": "system",
        "content": ""
    })

    # 转换用户和助手消息
    for msg in original_messages:
        if msg.get('role') in ['user', 'assistant']:
            converted_messages.append({
                "role": msg['role'],
                "content": msg['content']
            })

    return converted_messages

   