# Start Generation Here
cd wiseAgent/backend

npm install -g pm2 --registry=https://registry.npmmirror.com


# Start of Selection
# Start of Selection
git checkout develop_v0.1 origin/develop_v0.1
# End of Selection
# End of Selection

# 创建conda环境
conda create -n wiseAgent python=3.10 -y

# 激活环境
conda activate wiseAgent

# Start of Selection
# 安装依赖包
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install opencv-python langchain_openai pdf2image markitdown langgraph langchain -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install apscheduler werkzeug sqlalchemy minio -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install elasticsearch==7.13.1  -i https://pypi.tuna.tsinghua.edu.cn/simple
conda install -c conda-forge poppler -y -i https://pypi.tuna.tsinghua.edu.cn/simple

langchain_openai
pdf2image
markitdown
langgraph
cv2
pip install opencv-python
# End of Selection

# 启动命令


ENV_MODE=dev uvicorn app.main:app --host 0.0.0.0 --port 8800 --reload
# 158:8892 测试环境
ENV_MODE=157 uvicorn app.main:app --host 0.0.0.0 --port 8800 --reload

# 使用开发环境配置启动
pm2 start "uvicorn app.main:app --host 0.0.0.0 --port 8808" --name WiseAgent --env ENV_MODE=dev

# 使用生产环境配置启动
pm2 start "uvicorn app.main:app --host 0.0.0.0 --port 8800" --name fastapi-app --env ENV_MODE=prod




# MINIo


 docker run -d --name minio_wiseagent   -p 9002:9000 -p 9001:9001   -e "MINIO_ROOT_USER=admin"   -e "MINIO_ROOT_PASSWORD=1qaz2wsx"   quay.io/minio/minio server /data --console-address ":9001"





docker run -d \
  --name mongo_wiseagent \
  -p 37017:27017 \
  -e MONGO_INITDB_ROOT_USERNAME=admin \
  -e MONGO_INITDB_ROOT_PASSWORD=StrongP@ssw0rd \
  -v /path/on/host/mongo-data:/data/wiseagent_db \
  mongo:5.0



mongodump \
  --host kg --port 37017 \
  -u "memInterview" -p "memInterview@202408" \
  --authenticationDatabase "admin" \
  --db "roardataAiApp_test" \
  --out "20250609"



mongorestore \
  --host ************* --port 37017 \
  -u "admin" -p "StrongP@ssw0rd" \
  --authenticationDatabase "admin" \
  --db "wiseagent" \
  --drop "20250609/roardataAiApp_test"



  mongorestore \
  --host kg --port 37017 \
  -u "memInterview" -p "memInterview@202408" \
  --authenticationDatabase "admin" \
  --db "wiseagent" \
  --drop \
  "20250609/roardataAiApp_test"


  wiseAgent001
  1qaz@WSX