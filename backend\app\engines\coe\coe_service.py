from typing import Optional
from app.engines.coe.coe_engine import COEEngine
from app.models.expert import Expert
from app.db.mongodb import db
from app.utils.logging_config import get_logger
from app.engines.coe.general_expert import GeneralExpert

logger = get_logger(__name__)

class COEService:
    _instance: Optional['COEService'] = None
    _engine: Optional[COEEngine] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    async def initialize(cls) -> None:
        """初始化 COE 服务"""
        if cls._engine is not None:
            return
            
        # 获取系统设置
        settings = await db.system_settings.find_one({})
        if not settings or not settings.get("features", {}).get("enable_coe", False):
            logger.info("CoE 功能未启用")
            return
            
        # 加载所有专家配置
        experts = []
        async for expert_doc in db.experts.find({"status": "active"}):
            try:
                # 根据专家类型创建对应的专家实例
                if expert_doc["expert_type"] == "general":
                    expert = GeneralExpert(
                        model_config=expert_doc["llm_config"],
                        name=expert_doc["name"],
                        description=expert_doc["description"]
                    )
                    experts.append(expert)
                # TODO: 添加其他类型专家的实例化逻辑
                
            except Exception as e:
                logger.error(f"加载专家 {expert_doc['name']} 失败: {str(e)}")
        
        # 创建 COE 引擎
        confidence_threshold = settings["features"]["coe_config"]["default_confidence_threshold"]
        cls._engine = COEEngine(experts=experts, confidence_threshold=confidence_threshold)
        logger.info(f"COE 服务初始化完成，加载了 {len(experts)} 个专家")
    
    @classmethod
    def get_engine(cls) -> Optional[COEEngine]:
        """获取 COE 引擎实例"""
        return cls._engine
    
    @classmethod
    async def process_query(cls, query: str, context: Optional[dict] = None):
        """处理查询"""
        if cls._engine is None:
            raise RuntimeError("COE 服务未初始化")
        
        return await cls._engine.process_query(query, context) 