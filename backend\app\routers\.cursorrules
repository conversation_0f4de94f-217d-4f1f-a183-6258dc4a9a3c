# FastAPI路由开发规范指南

## 一、路由认证
所有需要认证的接口必须按照以下方式实现：

```python
from ..utils.auth import verify_token

@router.get("/some_path", response_model=Dict[str, Any])
async def method_name(
    # 其他参数...
    current_user: dict = Depends(verify_token)  # 认证并获取当前用户信息
):
    # 实现逻辑
```

## 二、统一响应格式
所有接口必须使用以下统一的响应格式：

```python
return {
    "success": True,      # 布尔值，表示请求是否成功
    "data": data_object,  # 返回的数据对象，失败时可为None或省略
    "message": "success",  # 成功或错误消息
    "total": count,       # 可选，列表类接口返回总数
    "current": current,   # 可选，当前页码
    "pageSize": pageSize  # 可选，每页数量
}
```

## 三、分页参数规范
列表类接口必须支持标准分页参数：

```python
@router.get("/items", response_model=Dict[str, Any])
async def get_items(
    current: int = Query(1, description="当前页码", ge=1),
    pageSize: int = Query(10, description="每页数量", ge=1, le=100),
    # 其他过滤参数...
    current_user: dict = Depends(verify_token)
):
    # 实现逻辑
```

## 四、HTTP方法使用规范
遵循RESTful API设计原则：
- GET：获取资源，不应修改数据
- POST：创建新资源
- PUT：完整更新资源
- PATCH：部分更新资源
- DELETE：删除资源

## 五、路由路径命名规范
- 使用复数名词表示资源集合: `/users`, `/articles`
- 使用资源ID标识具体资源: `/users/{user_id}`
- 资源关联使用嵌套路径: `/users/posts/{user_id}`
- 使用动词表示特殊操作: `/users/activate/{user_id}`

## 六、状态码使用
- 200: 成功
- 201: 创建成功
- 400: 请求参数错误
- 401: 未认证
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器错误

## 七、文档注释
每个路由函数必须添加docstring说明：

```python
@router.get("/items/{item_id}", response_model=Dict[str, Any])
async def get_item(item_id: str, current_user: dict = Depends(verify_token)):
    """
    获取指定ID的项目详情

    Args:
        item_id: 项目ID
        current_user: 当前登录用户

    Returns:
        包含项目详情的字典
    """
    # 实现逻辑
```

## 八、异常处理
使用FastAPI的HTTPException处理异常：

```python
from fastapi import HTTPException

if not item:
    raise HTTPException(status_code=404, detail="Item not found")
```

## 九、示例代码
完整的CRUD示例：

```python
@router.get("/items", response_model=Dict[str, Any])
async def get_items(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    name: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    # 获取列表实现...
    return {
        "success": True,
        "data": items,
        "total": total_count,
        "current": current,
        "pageSize": pageSize
    }

@router.get("/items/{item_id}", response_model=Dict[str, Any])
async def get_item(item_id: str, current_user: dict = Depends(verify_token)):
    # 获取单个实现...
    return {"success": True, "data": item}

@router.post("/items", response_model=Dict[str, Any])
async def create_item(item: ItemCreate, current_user: dict = Depends(verify_token)):
    # 创建实现...
    return {"success": True, "data": new_item, "message": "创建成功"}

@router.put("/items/{item_id}", response_model=Dict[str, Any])
async def update_item(
    item_id: str, item: ItemUpdate, current_user: dict = Depends(verify_token)
):
    # 更新实现...
    return {"success": True, "data": updated_item, "message": "更新成功"}

@router.delete("/items/{item_id}", response_model=Dict[str, Any])
async def delete_item(item_id: str, current_user: dict = Depends(verify_token)):
    # 删除实现...
    return {"success": True, "message": "删除成功"}
```

## 十，路由入口的定义
在路径前添加路由定义，例如：
router = APIRouter(
    prefix="/api",
    tags=["路由的标签"]
)



# 数据库引用 使用这种方式引入
from ..db.mongodb import db