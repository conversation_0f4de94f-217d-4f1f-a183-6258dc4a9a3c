{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 网站信息提取工具\n", "\n", "此notebook用于从Elasticsearch数据库中提取特定URL的网站信息，并将结果保存为本地JSON文件。"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "from elasticsearch import Elasticsearch\n", "import json\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "import time\n", "import os\n", "from datetime import datetime"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 配置信息\n", "\n", "设置Elasticsearch连接信息和目标URL列表"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["# ES连接配置\n", "SOURCE_ES_HOST = \"**********\"\n", "SOURCE_ES_PORT = 9201\n", "SOURCE_ES_USER = \"jinxu\"\n", "SOURCE_ES_PASSWORD = \"WiseWeb@123\"\n", "SOURCE_ES_INDEX = \"media_level_wiseweb_crawler_website\"\n", "\n", "# 需要提取的URL列表\n", "target_urls = [\n", "    \"www.people.com.cn\", \"www.xinhuanet.com\", \"www.news.cctv.com\", \"www.chinadaily.com.cn\",\n", "    \"www.chinanews.com\", \"www.southcn.com\", \"news.sina.com.cn\", \"news.sohu.com\",\n", "    \"news.163.com\", \"news.ifeng.com\", \"news.cctv.com\", \"www.youth.cn\",\n", "    \"www.ce.cn\", \"www.china.com.cn\", \"www.cs.com.cn\", \"www.stcn.com\",\n", "    \"www.yicai.com\", \"www.21jingji.com\", \"www.caixin.com\", \"www.eeo.com.cn\",\n", "    \"www.jrj.com.cn\", \"www.ce.cn\", \"www.eastmoney.com\", \"www.hexun.com\",\n", "    \"finance.sina.com.cn\", \"www.caijing.com.cn\", \"www.cfi.net.cn\", \"www.cb.com.cn\",\n", "    \"www.weibo.com\", \"www.kuaishou.com\", \"www.douyin.com\", \"www.bilibili.com\",\n", "    \"www.zhihu.com\", \"www.douban.com\", \"www.immomo.com\", \"www.toutiao.com\",\n", "    \"www.xiaohongshu.com\", \"www.fliggy.com\", \"www.meituan.com\", \"www.pinduoduo.com\",\n", "    \"xueqiu.com\", \"bbs.eastmoney.com\", \"bbs.hexun.com\", \"bbs.jrj.com.cn\",\n", "    \"guba.eastmoney.com\", \"www.stockstar.com\", \"www.youcaiwang.com\", \"www.cngold.org\",\n", "    \"bbs.jrj.com.cn\", \"bbs.cfi.net.cn\"\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ES连接函数\n", "\n", "创建与Elasticsearch的连接"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["# 修改连接到Elasticsearch的函数\n", "def connect_to_elasticsearch():\n", "    try:\n", "        # 尝试兼容旧版ES API，关闭SSL验证\n", "        es = Elasticsearch(\n", "            [f\"http://{SOURCE_ES_HOST}:{SOURCE_ES_PORT}\"],\n", "            http_auth=(SOURCE_ES_USER, SOURCE_ES_PASSWORD),\n", "            verify_certs=False,\n", "            ssl_show_warn=False,\n", "            timeout=30,\n", "            request_timeout=60,\n", "            retry_on_timeout=True,\n", "            max_retries=3\n", "        )\n", "        \n", "        # 跳过ping检查，直接测试一个简单查询\n", "        try:\n", "            test_query = {\"query\": {\"match_all\": {}}, \"size\": 1}\n", "            es.search(index=SOURCE_ES_INDEX, body=test_query)\n", "            print(f\"成功连接到Elasticsearch: {SOURCE_ES_HOST}:{SOURCE_ES_PORT}\")\n", "            return es\n", "        except Exception as e:\n", "            print(f\"连接测试查询失败: {str(e)}\")\n", "            \n", "            # 尝试使用另一种连接方式（适用于较旧版本的ES）\n", "            print(\"尝试使用旧版连接方式...\")\n", "            from elasticsearch import Elasticsearch as OldES\n", "            es_old = OldES([f\"{SOURCE_ES_HOST}:{SOURCE_ES_PORT}\"], \n", "                          http_auth=(SOURCE_ES_USER, SOURCE_ES_PASSWORD),\n", "                          timeout=30)\n", "            \n", "            # 测试连接\n", "            test_query = {\"query\": {\"match_all\": {}}, \"size\": 1}\n", "            es_old.search(index=SOURCE_ES_INDEX, body=test_query)\n", "            print(\"旧版连接方式成功\")\n", "            return es_old\n", "            \n", "    except Exception as e:\n", "        print(f\"所有连接尝试均失败: {str(e)}\")\n", "        print(\"请检查以下可能的原因：\")\n", "        print(\"1. 网络连接：确认您能够ping通ES服务器IP\")\n", "        print(\"2. 认证信息：确认用户名和密码正确\")\n", "        print(\"3. ES服务状态：确认ES服务正在运行\")\n", "        print(\"4. 防火墙设置：确认端口9201已开放\")\n", "        return None\n", "\n", "# 修改查询函数以适应可能的情况\n", "def extract_and_save_data():\n", "    es = connect_to_elasticsearch()\n", "    if not es:\n", "        return\n", "    \n", "    # 创建输出目录\n", "    output_dir = \"es_extracted_data\"\n", "    if not os.path.exists(output_dir):\n", "        os.makedirs(output_dir)\n", "    \n", "    # 生成输出文件名\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    output_file = os.path.join(output_dir, f\"website_data_{timestamp}.json\")\n", "    \n", "    # 初始化变量\n", "    search_after = None\n", "    total_extracted = 0\n", "    batch_size = 50  # 减小批量大小\n", "    all_results = []\n", "    \n", "    print(\"开始提取数据...\")\n", "    \n", "    # 测试索引是否存在\n", "    try:\n", "        index_exists = es.indices.exists(index=SOURCE_ES_INDEX)\n", "        print(f\"索引 {SOURCE_ES_INDEX} 存在: {index_exists}\")\n", "    except Exception as e:\n", "        print(f\"检查索引失败: {str(e)}\")\n", "        # 继续尝试查询，因为某些版本可能不支持indices API\n", "    \n", "    max_attempts = 5\n", "    current_attempt = 0\n", "    \n", "    try:\n", "        while current_attempt < max_attempts:\n", "            current_attempt += 1\n", "            try:\n", "                # 构建查询\n", "                query = {\n", "                    \"query\": {\n", "                        \"bool\": {\n", "                            \"should\": [{\"wildcard\": {\"url\": f\"*{url}*\"}} for url in target_urls],\n", "                            \"minimum_should_match\": 1\n", "                        }\n", "                    },\n", "                    \"sort\": [{\"_id\": \"asc\"}],\n", "                    \"size\": batch_size\n", "                }\n", "                \n", "                if search_after:\n", "                    query[\"search_after\"] = search_after\n", "                \n", "                # 使用旧版查询方式\n", "                print(f\"执行第 {current_attempt} 批查询...\")\n", "                response = es.search(index=SOURCE_ES_INDEX, body=query)\n", "                \n", "                hits = response[\"hits\"][\"hits\"]\n", "                if not hits:\n", "                    print(\"没有更多结果，查询完成\")\n", "                    break\n", "                \n", "                # 处理结果\n", "                batch_results = []\n", "                for hit in hits:\n", "                    doc = hit[\"_source\"]\n", "                    url = doc.get(\"url\", \"\")\n", "                    if any(target_url in url for target_url in target_urls):\n", "                        batch_results.append(doc)\n", "                \n", "                all_results.extend(batch_results)\n", "                total_extracted += len(batch_results)\n", "                \n", "                print(f\"已处理 {len(hits)} 条记录，提取了 {len(batch_results)} 条匹配数据，累计: {total_extracted}\")\n", "                \n", "                # 更新search_after\n", "                search_after = hits[-1][\"sort\"]\n", "                time.sleep(1)  # 增加等待时间\n", "                \n", "            except Exception as e:\n", "                print(f\"批次 {current_attempt} 查询出错: {str(e)}\")\n", "                if current_attempt >= max_attempts:\n", "                    break\n", "                print(\"等待15秒后重试...\")\n", "                time.sleep(15)\n", "    \n", "    except KeyboardInterrupt:\n", "        print(\"用户中断了查询过程\")\n", "    \n", "    # 保存已获取的结果\n", "    if all_results:\n", "        try:\n", "            with open(output_file, 'w', encoding='utf-8') as f:\n", "                json.dump(all_results, f, ensure_ascii=False, indent=2)\n", "            print(f\"成功提取 {total_extracted} 条数据，已保存到 {output_file}\")\n", "        except Exception as e:\n", "            print(f\"保存文件时出错: {str(e)}\")\n", "    else:\n", "        print(\"未找到匹配的数据\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 查询构建函数\n", "\n", "根据目标URL列表构建Elasticsearch查询"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# 构建查询\n", "def build_query(urls, size=100, search_after=None):\n", "    # 构建URL匹配条件\n", "    should_clauses = []\n", "    for url in urls:\n", "        should_clauses.append({\"wildcard\": {\"url\": f\"*{url}*\"}})\n", "    \n", "    query = {\n", "        \"query\": {\n", "            \"bool\": {\n", "                \"should\": should_clauses,\n", "                \"minimum_should_match\": 1\n", "            }\n", "        },\n", "        \"sort\": [{\"_id\": \"asc\"}],  # 使用_id排序以便使用search_after\n", "        \"size\": size\n", "    }\n", "    \n", "    if search_after:\n", "        query[\"search_after\"] = search_after\n", "    \n", "    return query"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据提取与保存函数\n", "\n", "执行查询并将结果保存到JSON文件"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["# 执行查询并保存结果\n", "def extract_and_save_data():\n", "    es = connect_to_elasticsearch()\n", "    if not es:\n", "        return\n", "    \n", "    # 创建输出目录\n", "    output_dir = \"es_extracted_data\"\n", "    if not os.path.exists(output_dir):\n", "        os.makedirs(output_dir)\n", "    \n", "    # 生成输出文件名\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    output_file = os.path.join(output_dir, f\"website_data_{timestamp}.json\")\n", "    \n", "    # 初始化变量\n", "    search_after = None\n", "    total_extracted = 0\n", "    batch_size = 100\n", "    all_results = []\n", "    \n", "    print(\"开始提取数据...\")\n", "    \n", "    try:\n", "        while True:\n", "            # 构建查询\n", "            query = build_query(target_urls, batch_size, search_after)\n", "            \n", "            # 执行查询\n", "            response = es.search(index=SOURCE_ES_INDEX, body=query)\n", "            hits = response[\"hits\"][\"hits\"]\n", "            \n", "            # 如果没有更多结果则结束\n", "            if not hits:\n", "                break\n", "            \n", "            # 处理结果\n", "            batch_results = []\n", "            for hit in hits:\n", "                doc = hit[\"_source\"]\n", "                # 检查URL是否匹配目标列表中的任一URL\n", "                url = doc.get(\"url\", \"\")\n", "                if any(target_url in url for target_url in target_urls):\n", "                    batch_results.append(doc)\n", "            \n", "            # 添加到总结果\n", "            all_results.extend(batch_results)\n", "            total_extracted += len(batch_results)\n", "            \n", "            print(f\"已处理 {len(hits)} 条记录，提取了 {len(batch_results)} 条匹配数据，累计: {total_extracted}\")\n", "            \n", "            # 更新search_after\n", "            search_after = hits[-1][\"sort\"]\n", "            \n", "            # 添加短暂休眠，避免过度请求\n", "            time.sleep(0.1)\n", "    except Exception as e:\n", "        print(f\"查询过程中出错: {str(e)}\")\n", "    \n", "    # 保存结果\n", "    if all_results:\n", "        try:\n", "            with open(output_file, 'w', encoding='utf-8') as f:\n", "                json.dump(all_results, f, ensure_ascii=False, indent=2)\n", "            print(f\"成功提取 {total_extracted} 条数据，已保存到 {output_file}\")\n", "        except Exception as e:\n", "            print(f\"保存文件时出错: {str(e)}\")\n", "    else:\n", "        print(\"未找到匹配的数据\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据分析函数（可选）\n", "\n", "对提取的数据进行简单分析"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["# 数据统计分析（可选）\n", "def analyze_extracted_data():\n", "    # 查找最新的输出文件\n", "    output_dir = \"es_extracted_data\"\n", "    if not os.path.exists(output_dir):\n", "        print(\"数据目录不存在，请先提取数据\")\n", "        return\n", "    \n", "    files = [os.path.join(output_dir, f) for f in os.listdir(output_dir) if f.endswith('.json')]\n", "    if not files:\n", "        print(\"未找到数据文件\")\n", "        return\n", "    \n", "    latest_file = max(files, key=os.path.getctime)\n", "    \n", "    # 加载数据\n", "    with open(latest_file, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "    \n", "    # 转换为DataFrame进行分析\n", "    df = pd.DataFrame(data)\n", "    \n", "    # 显示基本统计信息\n", "    print(f\"总记录数: {len(df)}\")\n", "    \n", "    # 按网站域名分组统计\n", "    if 'url' in df.columns:\n", "        # 提取域名\n", "        df['domain'] = df['url'].apply(lambda x: x.split('//')[-1].split('/')[0] if isinstance(x, str) else '')\n", "        domain_counts = df['domain'].value_counts()\n", "        \n", "        print(\"\\n按网站统计:\")\n", "        print(domain_counts.head(20))  # 显示前20个域名的统计\n", "    \n", "    # 其他可能的分析\n", "    print(\"\\n数据字段列表:\")\n", "    print(df.columns.tolist())\n", "    \n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 执行数据提取\n", "\n", "运行以下单元格开始提取数据"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["# 完整的分组查询函数实现\n", "def extract_and_save_data():\n", "    es = connect_to_elasticsearch()\n", "    if not es:\n", "        return\n", "    \n", "    # 创建输出目录\n", "    output_dir = \"es_extracted_data\"\n", "    if not os.path.exists(output_dir):\n", "        os.makedirs(output_dir)\n", "    \n", "    # 生成输出文件名\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    output_file = os.path.join(output_dir, f\"website_data_{timestamp}.json\")\n", "    \n", "    # 将URL列表分成较小的组（每组5个URL）\n", "    url_groups = [target_urls[i:i+5] for i in range(0, len(target_urls), 5)]\n", "    print(f\"已将{len(target_urls)}个URL分成{len(url_groups)}组进行查询\")\n", "    \n", "    all_results = []\n", "    total_extracted = 0\n", "    \n", "    # 对每组URL进行查询\n", "    for group_idx, url_group in enumerate(url_groups):\n", "        print(f\"\\n开始处理第{group_idx+1}组URL（共{len(url_groups)}组）\")\n", "        print(f\"当前组URL: {', '.join(url_group)}\")\n", "        \n", "        try:\n", "            # 针对这组URL执行查询\n", "            search_after = None\n", "            batch_size = 20  # 使用更小的批量\n", "            group_results = []\n", "            batch_count = 0\n", "            \n", "            while batch_count < 10:  # 限制每组最多查询10批\n", "                batch_count += 1\n", "                \n", "                try:\n", "                    # 构建查询\n", "                    should_clauses = [{\"wildcard\": {\"url\": f\"*{url}*\"}} for url in url_group]\n", "                    \n", "                    # 使用旧版兼容方式查询\n", "                    query = {\n", "                        \"query\": {\n", "                            \"bool\": {\n", "                                \"should\": should_clauses,\n", "                                \"minimum_should_match\": 1\n", "                            }\n", "                        },\n", "                        \"sort\": [{\"_id\": \"asc\"}],\n", "                        \"size\": batch_size\n", "                    }\n", "                    \n", "                    if search_after:\n", "                        query[\"search_after\"] = search_after\n", "                    \n", "                    print(f\"执行组{group_idx+1}的第{batch_count}批查询\")\n", "                    \n", "                    # 使用兼容的方式执行查询\n", "                    response = es.search(index=SOURCE_ES_INDEX, body=query, request_timeout=60)\n", "                    \n", "                    hits = response[\"hits\"][\"hits\"]\n", "                    if not hits:\n", "                        print(f\"组{group_idx+1}没有更多结果\")\n", "                        break\n", "                    \n", "                    # 处理结果\n", "                    batch_results = []\n", "                    for hit in hits:\n", "                        doc = hit[\"_source\"]\n", "                        url = doc.get(\"url\", \"\")\n", "                        if any(target_url in url for target_url in url_group):\n", "                            batch_results.append(doc)\n", "                    \n", "                    group_results.extend(batch_results)\n", "                    \n", "                    print(f\"批次{batch_count}: 获取{len(hits)}条记录，提取{len(batch_results)}条匹配数据\")\n", "                    \n", "                    # 更新search_after\n", "                    search_after = hits[-1][\"sort\"]\n", "                    \n", "                    # 每批结束后保存一次中间结果（防止完全丢失）\n", "                    if batch_count % 3 == 0:  # 每3批保存一次\n", "                        intermediate_file = os.path.join(output_dir, f\"partial_data_group{group_idx+1}_{timestamp}.json\")\n", "                        with open(intermediate_file, 'w', encoding='utf-8') as f:\n", "                            json.dump(group_results, f, ensure_ascii=False)\n", "                        print(f\"已保存组{group_idx+1}的中间结果: {len(group_results)}条\")\n", "                    \n", "                    # 适当休眠，避免服务器过载\n", "                    time.sleep(2)\n", "                    \n", "                except Exception as e:\n", "                    print(f\"组{group_idx+1}批次{batch_count}查询出错: {str(e)}\")\n", "                    time.sleep(5)  # 出错后等待时间长一些\n", "                    continue\n", "            \n", "            # 添加这组查询的结果\n", "            all_results.extend(group_results)\n", "            total_extracted += len(group_results)\n", "            print(f\"组{group_idx+1}完成: 提取了{len(group_results)}条记录，累计: {total_extracted}\")\n", "            \n", "            # 每完成一组查询就保存一次结果\n", "            with open(output_file, 'w', encoding='utf-8') as f:\n", "                json.dump(all_results, f, ensure_ascii=False, indent=2)\n", "            print(f\"阶段性保存成功: {output_file}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"处理组{group_idx+1}时出错: {str(e)}\")\n", "            print(\"继续处理下一组...\")\n", "            \n", "        # 组之间的等待时间\n", "        time.sleep(5)\n", "    \n", "    # 最终保存所有结果\n", "    if all_results:\n", "        try:\n", "            with open(output_file, 'w', encoding='utf-8') as f:\n", "                json.dump(all_results, f, ensure_ascii=False, indent=2)\n", "            print(f\"\\n全部完成！成功提取 {total_extracted} 条数据，已保存到 {output_file}\")\n", "        except Exception as e:\n", "            print(f\"最终保存文件时出错: {str(e)}\")\n", "            # 尝试使用不同的文件名保存\n", "            try:\n", "                backup_file = os.path.join(output_dir, f\"backup_final_{timestamp}.json\")\n", "                with open(backup_file, 'w', encoding='utf-8') as f:\n", "                    json.dump(all_results, f, ensure_ascii=False)\n", "                print(f\"使用备用文件名保存成功: {backup_file}\")\n", "            except:\n", "                print(\"所有保存尝试均失败\")\n", "    else:\n", "        print(\"未找到匹配的数据\")\n", "\n", "    return total_extracted"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["def extract_and_save_data(start_date=None, end_date=None):\n", "    \"\"\"\n", "    从ES中提取数据并保存到本地JSON文件\n", "    \n", "    参数:\n", "        start_date: 开始日期，格式为\"YYYY-MM-DD\"，例如\"2022-04-27\"\n", "        end_date: 结束日期，格式为\"YYYY-MM-DD\"，例如\"2022-05-01\"\n", "    \"\"\"\n", "    es = connect_to_elasticsearch()\n", "    if not es:\n", "        return\n", "    \n", "    # 创建输出目录\n", "    output_dir = \"es_extracted_data\"\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    \n", "    # 生成带时间戳的输出文件名\n", "    timestamp = time.strftime(\"%Y%m%d_%H%M%S\")\n", "    output_file = os.path.join(output_dir, f\"website_data_{timestamp}.json\")\n", "    \n", "    print(f\"成功连接到Elasticsearch: {SOURCE_ES_HOST}:{SOURCE_ES_PORT}\")\n", "    print(f\"匹配的数据将保存到: {output_file}\")\n", "    \n", "    # 目标URL列表\n", "    target_urls = [\n", "        \"www.people.com.cn\", \"www.xinhuanet.com\", \"www.news.cctv.com\", \"www.chinadaily.com.cn\",\n", "        \"www.chinanews.com\", \"www.southcn.com\", \"news.sina.com.cn\", \"news.sohu.com\",\n", "        \"news.163.com\", \"news.ifeng.com\", \"news.cctv.com\", \"www.youth.cn\",\n", "        \"www.ce.cn\", \"www.china.com.cn\", \"www.cs.com.cn\", \"www.stcn.com\",\n", "        \"www.yicai.com\", \"www.21jingji.com\", \"www.caixin.com\", \"www.eeo.com.cn\",\n", "        \"www.jrj.com.cn\", \"www.ce.cn\", \"www.eastmoney.com\", \"www.hexun.com\",\n", "        \"finance.sina.com.cn\", \"www.caijing.com.cn\", \"www.cfi.net.cn\", \"www.cb.com.cn\",\n", "        \"www.weibo.com\", \"www.kuaishou.com\", \"www.douyin.com\", \"www.bilibili.com\",\n", "        \"www.zhihu.com\", \"www.douban.com\", \"www.immomo.com\", \"www.toutiao.com\",\n", "        \"www.xiaohongshu.com\", \"www.fliggy.com\", \"www.meituan.com\", \"www.pinduoduo.com\",\n", "        \"xueqiu.com\", \"bbs.eastmoney.com\", \"bbs.hexun.com\", \"bbs.jrj.com.cn\",\n", "        \"guba.eastmoney.com\", \"www.stockstar.com\", \"www.youcaiwang.com\", \"www.cngold.org\",\n", "        \"bbs.jrj.com.cn\", \"bbs.cfi.net.cn\"\n", "    ]\n", "    \n", "    # 构建时间范围查询条件\n", "    time_range = {}\n", "    if start_date:\n", "        # 添加时间部分 00:00:00\n", "        start_datetime = f\"{start_date} 00:00:00\"\n", "        time_range[\"gte\"] = start_datetime\n", "    if end_date:\n", "        # 添加时间部分 23:59:59\n", "        end_datetime = f\"{end_date} 23:59:59\"\n", "        time_range[\"lte\"] = end_datetime\n", "    \n", "    query = {\"query\": {\"match_all\": {}}}\n", "    \n", "    if time_range:\n", "        start_display = start_date + \" 00:00:00\" if start_date else \"不限\"\n", "        end_display = end_date + \" 23:59:59\" if end_date else \"不限\"\n", "        print(f\"按时间范围过滤: {start_display} 到 {end_display}\")\n", "        \n", "        query = {\n", "            \"query\": {\n", "                \"bool\": {\n", "                    \"must\": [{\n", "                        \"range\": {\n", "                            \"gathertime\": time_range\n", "                        }\n", "                    }]\n", "                }\n", "            }\n", "        }\n", "    \n", "    print(\"开始读取并筛选数据...\")\n", "    \n", "    # 初始化保存总数据的列表\n", "    all_matched_data = []\n", "    total_matched = 0\n", "    \n", "    # 开始读取数据的参数\n", "    size = 100  # 每批数据大小\n", "    current_batch = 1\n", "    scroll_time = \"5m\"  # scroll保持时间\n", "    \n", "    try:\n", "        # 使用scroll API开始查询\n", "        print(f\"读取第{current_batch}批数据（每批{size}条）...\")\n", "        \n", "        response = es.search(\n", "            index=SOURCE_ES_INDEX,\n", "            body=query,\n", "            size=size,\n", "            scroll=scroll_time,\n", "            request_timeout=120\n", "        )\n", "        \n", "        # 获取scroll_id用于后续查询\n", "        scroll_id = response[\"_scroll_id\"]\n", "        total_hits = response[\"hits\"][\"total\"][\"value\"]\n", "        print(f\"ES库中共有{total_hits}条符合时间条件的数据\")\n", "        \n", "        while True:\n", "            hits = response[\"hits\"][\"hits\"]\n", "            if not hits:\n", "                print(\"没有更多数据\")\n", "                break\n", "                \n", "            batch_matched = 0\n", "            \n", "            # 处理当前批次的数据\n", "            for hit in hits:\n", "                source = hit[\"_source\"]\n", "                # 获取URL字段\n", "                url = source.get(\"url\", \"\")\n", "                \n", "                # 检查是否匹配目标URL列表中的任何一个\n", "                if any(target_url in url for target_url in target_urls):\n", "                    all_matched_data.append(source)\n", "                    batch_matched += 1\n", "            \n", "            total_matched += batch_matched\n", "            print(f\"批次{current_batch}: 处理了{len(hits)}条数据，匹配了{batch_matched}条\")\n", "            \n", "            # 定期保存数据，防止内存溢出\n", "            if current_batch % 10 == 0 and all_matched_data:\n", "                print(f\"临时保存已匹配的{len(all_matched_data)}条数据...\")\n", "                with open(output_file, 'w', encoding='utf-8') as f:\n", "                    json.dump(all_matched_data, f, ensure_ascii=False, indent=2)\n", "            \n", "            # 使用scroll API获取下一批数据\n", "            current_batch += 1\n", "            print(f\"读取第{current_batch}批数据（每批{size}条）...\")\n", "            \n", "            response = es.scroll(\n", "                scroll_id=scroll_id,\n", "                scroll=scroll_time,\n", "                request_timeout=120\n", "            )\n", "            \n", "            # 每处理10批数据休息一下，避免服务器过载\n", "            if current_batch % 10 == 0:\n", "                print(\"暂停5秒...\")\n", "                time.sleep(5)\n", "    \n", "    except Exception as e:\n", "        print(f\"处理批次时出错: {e}\")\n", "        # 如果发生错误，尝试保存已收集的数据\n", "        if all_matched_data:\n", "            print(f\"尝试保存已收集的{len(all_matched_data)}条数据...\")\n", "            with open(output_file, 'w', encoding='utf-8') as f:\n", "                json.dump(all_matched_data, f, ensure_ascii=False, indent=2)\n", "    \n", "    finally:\n", "        # 保存所有匹配的数据到JSON文件\n", "        if all_matched_data:\n", "            print(f\"保存{len(all_matched_data)}条匹配的数据到{output_file}\")\n", "            with open(output_file, 'w', encoding='utf-8') as f:\n", "                json.dump(all_matched_data, f, ensure_ascii=False, indent=2)\n", "        else:\n", "            print(\"未找到匹配的数据\")\n", "        \n", "        # 尝试清理scroll\n", "        try:\n", "            if 'scroll_id' in locals():\n", "                es.clear_scroll(scroll_id=scroll_id)\n", "        except:\n", "            pass\n", "    \n", "    print(f\"数据提取完成，共匹配{total_matched}条数据\")\n", "    return total_matched"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_3256\\2649904908.py:19: DeprecationWarning: The 'body' parameter is deprecated for the 'search' API and will be removed in a future version. Instead use API parameters directly. See https://github.com/elastic/elasticsearch-py/issues/1698 for more information\n", "  es.search(index=SOURCE_ES_INDEX, body=test_query)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_3256\\2173720713.py:86: DeprecationWarning: The 'body' parameter is deprecated for the 'search' API and will be removed in a future version. Instead use API parameters directly. See https://github.com/elastic/elasticsearch-py/issues/1698 for more information\n", "  response = es.search(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["成功连接到Elasticsearch: **********:9201\n", "成功连接到Elasticsearch: **********:9201\n", "匹配的数据将保存到: es_extracted_data\\website_data_20250530_150807.json\n", "按时间范围过滤: 2025-05-01 00:00:00 到 2025-05-30 23:59:59\n", "开始读取并筛选数据...\n", "读取第1批数据（每批100条）...\n", "ES库中共有1835311条符合时间条件的数据\n", "批次1: 处理了100条数据，匹配了2条\n", "读取第2批数据（每批100条）...\n", "批次2: 处理了100条数据，匹配了6条\n", "读取第3批数据（每批100条）...\n", "批次3: 处理了100条数据，匹配了5条\n", "读取第4批数据（每批100条）...\n", "批次4: 处理了100条数据，匹配了3条\n", "读取第5批数据（每批100条）...\n", "批次5: 处理了100条数据，匹配了1条\n", "读取第6批数据（每批100条）...\n", "批次6: 处理了100条数据，匹配了3条\n", "读取第7批数据（每批100条）...\n", "批次7: 处理了100条数据，匹配了0条\n", "读取第8批数据（每批100条）...\n", "批次8: 处理了100条数据，匹配了1条\n", "读取第9批数据（每批100条）...\n", "批次9: 处理了100条数据，匹配了5条\n", "读取第10批数据（每批100条）...\n", "暂停5秒...\n", "批次10: 处理了100条数据，匹配了1条\n", "临时保存已匹配的27条数据...\n", "读取第11批数据（每批100条）...\n", "批次11: 处理了100条数据，匹配了8条\n", "读取第12批数据（每批100条）...\n", "批次12: 处理了100条数据，匹配了0条\n", "读取第13批数据（每批100条）...\n", "批次13: 处理了100条数据，匹配了1条\n", "读取第14批数据（每批100条）...\n", "批次14: 处理了100条数据，匹配了1条\n", "读取第15批数据（每批100条）...\n", "批次15: 处理了100条数据，匹配了30条\n", "读取第16批数据（每批100条）...\n", "批次16: 处理了100条数据，匹配了5条\n", "读取第17批数据（每批100条）...\n", "批次17: 处理了100条数据，匹配了2条\n", "读取第18批数据（每批100条）...\n", "批次18: 处理了100条数据，匹配了1条\n", "读取第19批数据（每批100条）...\n", "批次19: 处理了100条数据，匹配了0条\n", "读取第20批数据（每批100条）...\n", "暂停5秒...\n", "批次20: 处理了100条数据，匹配了9条\n", "临时保存已匹配的84条数据...\n", "读取第21批数据（每批100条）...\n", "批次21: 处理了100条数据，匹配了4条\n", "读取第22批数据（每批100条）...\n", "批次22: 处理了100条数据，匹配了1条\n", "读取第23批数据（每批100条）...\n", "批次23: 处理了100条数据，匹配了3条\n", "读取第24批数据（每批100条）...\n", "批次24: 处理了100条数据，匹配了0条\n", "读取第25批数据（每批100条）...\n", "批次25: 处理了100条数据，匹配了2条\n", "读取第26批数据（每批100条）...\n", "批次26: 处理了100条数据，匹配了3条\n", "读取第27批数据（每批100条）...\n", "批次27: 处理了100条数据，匹配了4条\n", "读取第28批数据（每批100条）...\n", "批次28: 处理了100条数据，匹配了1条\n", "读取第29批数据（每批100条）...\n", "批次29: 处理了100条数据，匹配了10条\n", "读取第30批数据（每批100条）...\n", "暂停5秒...\n", "批次30: 处理了100条数据，匹配了4条\n", "临时保存已匹配的116条数据...\n", "读取第31批数据（每批100条）...\n", "批次31: 处理了100条数据，匹配了3条\n", "读取第32批数据（每批100条）...\n", "批次32: 处理了100条数据，匹配了14条\n", "读取第33批数据（每批100条）...\n", "批次33: 处理了100条数据，匹配了3条\n", "读取第34批数据（每批100条）...\n", "批次34: 处理了100条数据，匹配了7条\n", "读取第35批数据（每批100条）...\n", "批次35: 处理了100条数据，匹配了11条\n", "读取第36批数据（每批100条）...\n", "批次36: 处理了100条数据，匹配了20条\n", "读取第37批数据（每批100条）...\n", "批次37: 处理了100条数据，匹配了3条\n", "读取第38批数据（每批100条）...\n", "批次38: 处理了100条数据，匹配了2条\n", "读取第39批数据（每批100条）...\n", "批次39: 处理了100条数据，匹配了1条\n", "读取第40批数据（每批100条）...\n", "暂停5秒...\n", "批次40: 处理了100条数据，匹配了18条\n", "临时保存已匹配的198条数据...\n", "读取第41批数据（每批100条）...\n", "批次41: 处理了100条数据，匹配了6条\n", "读取第42批数据（每批100条）...\n", "批次42: 处理了100条数据，匹配了6条\n", "读取第43批数据（每批100条）...\n", "批次43: 处理了100条数据，匹配了2条\n", "读取第44批数据（每批100条）...\n", "批次44: 处理了100条数据，匹配了9条\n", "读取第45批数据（每批100条）...\n", "批次45: 处理了100条数据，匹配了0条\n", "读取第46批数据（每批100条）...\n", "批次46: 处理了100条数据，匹配了5条\n", "读取第47批数据（每批100条）...\n", "批次47: 处理了100条数据，匹配了8条\n", "读取第48批数据（每批100条）...\n", "批次48: 处理了100条数据，匹配了0条\n", "读取第49批数据（每批100条）...\n", "批次49: 处理了100条数据，匹配了2条\n", "读取第50批数据（每批100条）...\n", "暂停5秒...\n", "批次50: 处理了100条数据，匹配了5条\n", "临时保存已匹配的241条数据...\n", "读取第51批数据（每批100条）...\n", "批次51: 处理了100条数据，匹配了2条\n", "读取第52批数据（每批100条）...\n", "批次52: 处理了100条数据，匹配了9条\n", "读取第53批数据（每批100条）...\n", "批次53: 处理了100条数据，匹配了3条\n", "读取第54批数据（每批100条）...\n", "批次54: 处理了100条数据，匹配了11条\n", "读取第55批数据（每批100条）...\n", "批次55: 处理了100条数据，匹配了5条\n", "读取第56批数据（每批100条）...\n", "批次56: 处理了100条数据，匹配了29条\n", "读取第57批数据（每批100条）...\n", "批次57: 处理了100条数据，匹配了1条\n", "读取第58批数据（每批100条）...\n", "批次58: 处理了100条数据，匹配了4条\n", "读取第59批数据（每批100条）...\n", "批次59: 处理了100条数据，匹配了3条\n", "读取第60批数据（每批100条）...\n", "暂停5秒...\n", "批次60: 处理了100条数据，匹配了0条\n", "临时保存已匹配的308条数据...\n", "读取第61批数据（每批100条）...\n", "批次61: 处理了100条数据，匹配了19条\n", "读取第62批数据（每批100条）...\n", "批次62: 处理了100条数据，匹配了2条\n", "读取第63批数据（每批100条）...\n", "批次63: 处理了100条数据，匹配了5条\n", "读取第64批数据（每批100条）...\n", "批次64: 处理了100条数据，匹配了19条\n", "读取第65批数据（每批100条）...\n", "批次65: 处理了100条数据，匹配了4条\n", "读取第66批数据（每批100条）...\n", "批次66: 处理了100条数据，匹配了8条\n", "读取第67批数据（每批100条）...\n", "批次67: 处理了100条数据，匹配了0条\n", "读取第68批数据（每批100条）...\n", "批次68: 处理了100条数据，匹配了3条\n", "读取第69批数据（每批100条）...\n", "批次69: 处理了100条数据，匹配了5条\n", "读取第70批数据（每批100条）...\n", "暂停5秒...\n", "批次70: 处理了100条数据，匹配了5条\n", "临时保存已匹配的378条数据...\n", "读取第71批数据（每批100条）...\n", "批次71: 处理了100条数据，匹配了6条\n", "读取第72批数据（每批100条）...\n", "批次72: 处理了100条数据，匹配了5条\n", "读取第73批数据（每批100条）...\n", "批次73: 处理了100条数据，匹配了1条\n", "读取第74批数据（每批100条）...\n", "批次74: 处理了100条数据，匹配了2条\n", "读取第75批数据（每批100条）...\n", "批次75: 处理了100条数据，匹配了7条\n", "读取第76批数据（每批100条）...\n", "批次76: 处理了100条数据，匹配了1条\n", "读取第77批数据（每批100条）...\n", "批次77: 处理了100条数据，匹配了4条\n", "读取第78批数据（每批100条）...\n", "批次78: 处理了100条数据，匹配了1条\n", "读取第79批数据（每批100条）...\n", "批次79: 处理了100条数据，匹配了0条\n", "读取第80批数据（每批100条）...\n", "暂停5秒...\n", "批次80: 处理了100条数据，匹配了0条\n", "临时保存已匹配的405条数据...\n", "读取第81批数据（每批100条）...\n", "批次81: 处理了100条数据，匹配了2条\n", "读取第82批数据（每批100条）...\n", "批次82: 处理了100条数据，匹配了0条\n", "读取第83批数据（每批100条）...\n", "批次83: 处理了100条数据，匹配了10条\n", "读取第84批数据（每批100条）...\n", "批次84: 处理了100条数据，匹配了7条\n", "读取第85批数据（每批100条）...\n", "批次85: 处理了100条数据，匹配了9条\n", "读取第86批数据（每批100条）...\n", "批次86: 处理了100条数据，匹配了1条\n", "读取第87批数据（每批100条）...\n", "批次87: 处理了100条数据，匹配了3条\n", "读取第88批数据（每批100条）...\n", "批次88: 处理了100条数据，匹配了7条\n", "读取第89批数据（每批100条）...\n", "批次89: 处理了100条数据，匹配了2条\n", "读取第90批数据（每批100条）...\n", "暂停5秒...\n", "批次90: 处理了100条数据，匹配了9条\n", "临时保存已匹配的455条数据...\n", "读取第91批数据（每批100条）...\n", "批次91: 处理了100条数据，匹配了15条\n", "读取第92批数据（每批100条）...\n", "批次92: 处理了100条数据，匹配了5条\n", "读取第93批数据（每批100条）...\n", "批次93: 处理了100条数据，匹配了2条\n", "读取第94批数据（每批100条）...\n", "批次94: 处理了100条数据，匹配了1条\n", "读取第95批数据（每批100条）...\n", "批次95: 处理了100条数据，匹配了5条\n", "读取第96批数据（每批100条）...\n", "批次96: 处理了100条数据，匹配了1条\n", "读取第97批数据（每批100条）...\n", "批次97: 处理了100条数据，匹配了2条\n", "读取第98批数据（每批100条）...\n", "批次98: 处理了100条数据，匹配了3条\n", "读取第99批数据（每批100条）...\n", "批次99: 处理了100条数据，匹配了6条\n", "读取第100批数据（每批100条）...\n", "暂停5秒...\n", "批次100: 处理了100条数据，匹配了5条\n", "临时保存已匹配的500条数据...\n", "读取第101批数据（每批100条）...\n", "批次101: 处理了100条数据，匹配了1条\n", "读取第102批数据（每批100条）...\n", "批次102: 处理了100条数据，匹配了1条\n", "读取第103批数据（每批100条）...\n", "批次103: 处理了100条数据，匹配了2条\n", "读取第104批数据（每批100条）...\n", "批次104: 处理了100条数据，匹配了2条\n", "读取第105批数据（每批100条）...\n", "批次105: 处理了100条数据，匹配了6条\n", "读取第106批数据（每批100条）...\n", "批次106: 处理了100条数据，匹配了3条\n", "读取第107批数据（每批100条）...\n", "批次107: 处理了100条数据，匹配了6条\n", "读取第108批数据（每批100条）...\n", "批次108: 处理了100条数据，匹配了0条\n", "读取第109批数据（每批100条）...\n", "批次109: 处理了100条数据，匹配了0条\n", "读取第110批数据（每批100条）...\n", "暂停5秒...\n", "批次110: 处理了100条数据，匹配了1条\n", "临时保存已匹配的522条数据...\n", "读取第111批数据（每批100条）...\n", "批次111: 处理了100条数据，匹配了2条\n", "读取第112批数据（每批100条）...\n", "批次112: 处理了100条数据，匹配了6条\n", "读取第113批数据（每批100条）...\n", "批次113: 处理了100条数据，匹配了6条\n", "读取第114批数据（每批100条）...\n", "批次114: 处理了100条数据，匹配了8条\n", "读取第115批数据（每批100条）...\n", "批次115: 处理了100条数据，匹配了15条\n", "读取第116批数据（每批100条）...\n", "批次116: 处理了100条数据，匹配了8条\n", "读取第117批数据（每批100条）...\n", "批次117: 处理了100条数据，匹配了6条\n", "读取第118批数据（每批100条）...\n", "批次118: 处理了100条数据，匹配了6条\n", "读取第119批数据（每批100条）...\n", "批次119: 处理了100条数据，匹配了0条\n", "读取第120批数据（每批100条）...\n", "暂停5秒...\n", "批次120: 处理了100条数据，匹配了3条\n", "临时保存已匹配的582条数据...\n", "读取第121批数据（每批100条）...\n", "批次121: 处理了100条数据，匹配了3条\n", "读取第122批数据（每批100条）...\n", "批次122: 处理了100条数据，匹配了2条\n", "读取第123批数据（每批100条）...\n", "批次123: 处理了100条数据，匹配了6条\n", "读取第124批数据（每批100条）...\n", "批次124: 处理了100条数据，匹配了2条\n", "读取第125批数据（每批100条）...\n", "批次125: 处理了100条数据，匹配了1条\n", "读取第126批数据（每批100条）...\n", "批次126: 处理了100条数据，匹配了11条\n", "读取第127批数据（每批100条）...\n", "批次127: 处理了100条数据，匹配了3条\n", "读取第128批数据（每批100条）...\n", "批次128: 处理了100条数据，匹配了3条\n", "读取第129批数据（每批100条）...\n", "批次129: 处理了100条数据，匹配了8条\n", "读取第130批数据（每批100条）...\n", "暂停5秒...\n", "批次130: 处理了100条数据，匹配了0条\n", "临时保存已匹配的621条数据...\n", "读取第131批数据（每批100条）...\n", "批次131: 处理了100条数据，匹配了5条\n", "读取第132批数据（每批100条）...\n", "批次132: 处理了100条数据，匹配了2条\n", "读取第133批数据（每批100条）...\n", "批次133: 处理了100条数据，匹配了4条\n", "读取第134批数据（每批100条）...\n", "批次134: 处理了100条数据，匹配了1条\n", "读取第135批数据（每批100条）...\n", "批次135: 处理了100条数据，匹配了8条\n", "读取第136批数据（每批100条）...\n", "批次136: 处理了100条数据，匹配了15条\n", "读取第137批数据（每批100条）...\n", "批次137: 处理了100条数据，匹配了8条\n", "读取第138批数据（每批100条）...\n", "批次138: 处理了100条数据，匹配了3条\n", "读取第139批数据（每批100条）...\n", "批次139: 处理了100条数据，匹配了3条\n", "读取第140批数据（每批100条）...\n", "暂停5秒...\n", "批次140: 处理了100条数据，匹配了3条\n", "临时保存已匹配的673条数据...\n", "读取第141批数据（每批100条）...\n", "批次141: 处理了100条数据，匹配了2条\n", "读取第142批数据（每批100条）...\n", "批次142: 处理了100条数据，匹配了13条\n", "读取第143批数据（每批100条）...\n", "批次143: 处理了100条数据，匹配了32条\n", "读取第144批数据（每批100条）...\n", "批次144: 处理了100条数据，匹配了4条\n", "读取第145批数据（每批100条）...\n", "批次145: 处理了100条数据，匹配了1条\n", "读取第146批数据（每批100条）...\n", "批次146: 处理了100条数据，匹配了2条\n", "读取第147批数据（每批100条）...\n", "批次147: 处理了100条数据，匹配了12条\n", "读取第148批数据（每批100条）...\n", "批次148: 处理了100条数据，匹配了1条\n", "读取第149批数据（每批100条）...\n", "批次149: 处理了100条数据，匹配了16条\n", "读取第150批数据（每批100条）...\n", "暂停5秒...\n", "批次150: 处理了100条数据，匹配了3条\n", "临时保存已匹配的759条数据...\n", "读取第151批数据（每批100条）...\n", "批次151: 处理了100条数据，匹配了5条\n", "读取第152批数据（每批100条）...\n", "批次152: 处理了100条数据，匹配了3条\n", "读取第153批数据（每批100条）...\n", "批次153: 处理了100条数据，匹配了6条\n", "读取第154批数据（每批100条）...\n", "批次154: 处理了100条数据，匹配了6条\n", "读取第155批数据（每批100条）...\n", "批次155: 处理了100条数据，匹配了4条\n", "读取第156批数据（每批100条）...\n", "批次156: 处理了100条数据，匹配了11条\n", "读取第157批数据（每批100条）...\n", "批次157: 处理了100条数据，匹配了4条\n", "读取第158批数据（每批100条）...\n", "批次158: 处理了100条数据，匹配了5条\n", "读取第159批数据（每批100条）...\n", "批次159: 处理了100条数据，匹配了7条\n", "读取第160批数据（每批100条）...\n", "暂停5秒...\n", "批次160: 处理了100条数据，匹配了14条\n", "临时保存已匹配的824条数据...\n", "读取第161批数据（每批100条）...\n", "批次161: 处理了100条数据，匹配了0条\n", "读取第162批数据（每批100条）...\n", "批次162: 处理了100条数据，匹配了4条\n", "读取第163批数据（每批100条）...\n", "批次163: 处理了100条数据，匹配了12条\n", "读取第164批数据（每批100条）...\n", "批次164: 处理了100条数据，匹配了3条\n", "读取第165批数据（每批100条）...\n", "批次165: 处理了100条数据，匹配了1条\n", "读取第166批数据（每批100条）...\n", "批次166: 处理了100条数据，匹配了3条\n", "读取第167批数据（每批100条）...\n", "批次167: 处理了100条数据，匹配了5条\n", "读取第168批数据（每批100条）...\n", "批次168: 处理了100条数据，匹配了2条\n", "读取第169批数据（每批100条）...\n", "批次169: 处理了100条数据，匹配了3条\n", "读取第170批数据（每批100条）...\n", "暂停5秒...\n", "批次170: 处理了100条数据，匹配了1条\n", "临时保存已匹配的858条数据...\n", "读取第171批数据（每批100条）...\n", "批次171: 处理了100条数据，匹配了9条\n", "读取第172批数据（每批100条）...\n", "批次172: 处理了100条数据，匹配了4条\n", "读取第173批数据（每批100条）...\n", "批次173: 处理了100条数据，匹配了0条\n", "读取第174批数据（每批100条）...\n", "批次174: 处理了100条数据，匹配了3条\n", "读取第175批数据（每批100条）...\n", "批次175: 处理了100条数据，匹配了1条\n", "读取第176批数据（每批100条）...\n", "批次176: 处理了100条数据，匹配了3条\n", "读取第177批数据（每批100条）...\n", "批次177: 处理了100条数据，匹配了5条\n", "读取第178批数据（每批100条）...\n", "批次178: 处理了100条数据，匹配了4条\n", "读取第179批数据（每批100条）...\n", "批次179: 处理了100条数据，匹配了4条\n", "读取第180批数据（每批100条）...\n", "暂停5秒...\n", "批次180: 处理了100条数据，匹配了4条\n", "临时保存已匹配的895条数据...\n", "读取第181批数据（每批100条）...\n", "批次181: 处理了100条数据，匹配了0条\n", "读取第182批数据（每批100条）...\n", "批次182: 处理了100条数据，匹配了1条\n", "读取第183批数据（每批100条）...\n", "批次183: 处理了100条数据，匹配了2条\n", "读取第184批数据（每批100条）...\n", "批次184: 处理了100条数据，匹配了4条\n", "读取第185批数据（每批100条）...\n", "批次185: 处理了100条数据，匹配了7条\n", "读取第186批数据（每批100条）...\n", "批次186: 处理了100条数据，匹配了4条\n", "读取第187批数据（每批100条）...\n", "批次187: 处理了100条数据，匹配了1条\n", "读取第188批数据（每批100条）...\n", "批次188: 处理了100条数据，匹配了4条\n", "读取第189批数据（每批100条）...\n", "批次189: 处理了100条数据，匹配了2条\n", "读取第190批数据（每批100条）...\n", "暂停5秒...\n", "批次190: 处理了100条数据，匹配了5条\n", "临时保存已匹配的925条数据...\n", "读取第191批数据（每批100条）...\n", "批次191: 处理了100条数据，匹配了2条\n", "读取第192批数据（每批100条）...\n", "批次192: 处理了100条数据，匹配了2条\n", "读取第193批数据（每批100条）...\n", "批次193: 处理了100条数据，匹配了1条\n", "读取第194批数据（每批100条）...\n", "批次194: 处理了100条数据，匹配了3条\n", "读取第195批数据（每批100条）...\n", "批次195: 处理了100条数据，匹配了1条\n", "读取第196批数据（每批100条）...\n", "批次196: 处理了100条数据，匹配了0条\n", "读取第197批数据（每批100条）...\n", "批次197: 处理了100条数据，匹配了1条\n", "读取第198批数据（每批100条）...\n", "批次198: 处理了100条数据，匹配了4条\n", "读取第199批数据（每批100条）...\n", "批次199: 处理了100条数据，匹配了4条\n", "读取第200批数据（每批100条）...\n", "暂停5秒...\n", "批次200: 处理了100条数据，匹配了1条\n", "临时保存已匹配的944条数据...\n", "读取第201批数据（每批100条）...\n", "批次201: 处理了100条数据，匹配了3条\n", "读取第202批数据（每批100条）...\n", "批次202: 处理了100条数据，匹配了4条\n", "读取第203批数据（每批100条）...\n", "批次203: 处理了100条数据，匹配了6条\n", "读取第204批数据（每批100条）...\n", "批次204: 处理了100条数据，匹配了2条\n", "读取第205批数据（每批100条）...\n", "批次205: 处理了100条数据，匹配了0条\n", "读取第206批数据（每批100条）...\n", "批次206: 处理了100条数据，匹配了2条\n", "读取第207批数据（每批100条）...\n", "批次207: 处理了100条数据，匹配了2条\n", "读取第208批数据（每批100条）...\n", "批次208: 处理了100条数据，匹配了5条\n", "读取第209批数据（每批100条）...\n", "批次209: 处理了100条数据，匹配了1条\n", "读取第210批数据（每批100条）...\n", "暂停5秒...\n", "批次210: 处理了100条数据，匹配了4条\n", "临时保存已匹配的973条数据...\n", "读取第211批数据（每批100条）...\n", "批次211: 处理了100条数据，匹配了1条\n", "读取第212批数据（每批100条）...\n", "批次212: 处理了100条数据，匹配了2条\n", "读取第213批数据（每批100条）...\n", "批次213: 处理了100条数据，匹配了1条\n", "读取第214批数据（每批100条）...\n", "批次214: 处理了100条数据，匹配了1条\n", "读取第215批数据（每批100条）...\n", "批次215: 处理了100条数据，匹配了7条\n", "读取第216批数据（每批100条）...\n", "批次216: 处理了100条数据，匹配了0条\n", "读取第217批数据（每批100条）...\n", "批次217: 处理了100条数据，匹配了0条\n", "读取第218批数据（每批100条）...\n", "批次218: 处理了100条数据，匹配了3条\n", "读取第219批数据（每批100条）...\n", "批次219: 处理了100条数据，匹配了2条\n", "读取第220批数据（每批100条）...\n", "暂停5秒...\n", "批次220: 处理了100条数据，匹配了1条\n", "临时保存已匹配的991条数据...\n", "读取第221批数据（每批100条）...\n", "批次221: 处理了100条数据，匹配了0条\n", "读取第222批数据（每批100条）...\n", "批次222: 处理了100条数据，匹配了5条\n", "读取第223批数据（每批100条）...\n", "批次223: 处理了100条数据，匹配了5条\n", "读取第224批数据（每批100条）...\n", "批次224: 处理了100条数据，匹配了10条\n", "读取第225批数据（每批100条）...\n", "批次225: 处理了100条数据，匹配了3条\n", "读取第226批数据（每批100条）...\n", "批次226: 处理了100条数据，匹配了5条\n", "读取第227批数据（每批100条）...\n", "批次227: 处理了100条数据，匹配了8条\n", "读取第228批数据（每批100条）...\n", "批次228: 处理了100条数据，匹配了0条\n", "读取第229批数据（每批100条）...\n", "批次229: 处理了100条数据，匹配了0条\n", "读取第230批数据（每批100条）...\n", "暂停5秒...\n", "批次230: 处理了100条数据，匹配了2条\n", "临时保存已匹配的1029条数据...\n", "读取第231批数据（每批100条）...\n", "批次231: 处理了100条数据，匹配了9条\n", "读取第232批数据（每批100条）...\n", "批次232: 处理了100条数据，匹配了1条\n", "读取第233批数据（每批100条）...\n", "批次233: 处理了100条数据，匹配了5条\n", "读取第234批数据（每批100条）...\n", "批次234: 处理了100条数据，匹配了2条\n", "读取第235批数据（每批100条）...\n", "批次235: 处理了100条数据，匹配了0条\n", "读取第236批数据（每批100条）...\n", "批次236: 处理了100条数据，匹配了3条\n", "读取第237批数据（每批100条）...\n", "批次237: 处理了100条数据，匹配了4条\n", "读取第238批数据（每批100条）...\n", "批次238: 处理了100条数据，匹配了0条\n", "读取第239批数据（每批100条）...\n", "批次239: 处理了100条数据，匹配了1条\n", "读取第240批数据（每批100条）...\n", "暂停5秒...\n", "批次240: 处理了100条数据，匹配了10条\n", "临时保存已匹配的1064条数据...\n", "读取第241批数据（每批100条）...\n", "批次241: 处理了100条数据，匹配了4条\n", "读取第242批数据（每批100条）...\n", "批次242: 处理了100条数据，匹配了4条\n", "读取第243批数据（每批100条）...\n", "批次243: 处理了100条数据，匹配了2条\n", "读取第244批数据（每批100条）...\n", "批次244: 处理了100条数据，匹配了3条\n", "读取第245批数据（每批100条）...\n", "批次245: 处理了100条数据，匹配了1条\n", "读取第246批数据（每批100条）...\n", "批次246: 处理了100条数据，匹配了6条\n", "读取第247批数据（每批100条）...\n", "批次247: 处理了100条数据，匹配了2条\n", "读取第248批数据（每批100条）...\n", "批次248: 处理了100条数据，匹配了4条\n", "读取第249批数据（每批100条）...\n", "批次249: 处理了100条数据，匹配了5条\n", "读取第250批数据（每批100条）...\n", "暂停5秒...\n", "批次250: 处理了100条数据，匹配了1条\n", "临时保存已匹配的1096条数据...\n", "读取第251批数据（每批100条）...\n", "批次251: 处理了100条数据，匹配了2条\n", "读取第252批数据（每批100条）...\n", "批次252: 处理了100条数据，匹配了5条\n", "读取第253批数据（每批100条）...\n", "批次253: 处理了100条数据，匹配了3条\n", "读取第254批数据（每批100条）...\n", "批次254: 处理了100条数据，匹配了4条\n", "读取第255批数据（每批100条）...\n", "批次255: 处理了100条数据，匹配了1条\n", "读取第256批数据（每批100条）...\n", "批次256: 处理了100条数据，匹配了0条\n", "读取第257批数据（每批100条）...\n", "批次257: 处理了100条数据，匹配了2条\n", "读取第258批数据（每批100条）...\n", "批次258: 处理了100条数据，匹配了4条\n", "读取第259批数据（每批100条）...\n", "批次259: 处理了100条数据，匹配了6条\n", "读取第260批数据（每批100条）...\n", "暂停5秒...\n", "批次260: 处理了100条数据，匹配了6条\n", "临时保存已匹配的1129条数据...\n", "读取第261批数据（每批100条）...\n", "批次261: 处理了100条数据，匹配了17条\n", "读取第262批数据（每批100条）...\n", "批次262: 处理了100条数据，匹配了2条\n", "读取第263批数据（每批100条）...\n", "批次263: 处理了100条数据，匹配了2条\n", "读取第264批数据（每批100条）...\n", "批次264: 处理了100条数据，匹配了4条\n", "读取第265批数据（每批100条）...\n", "批次265: 处理了100条数据，匹配了2条\n", "读取第266批数据（每批100条）...\n", "批次266: 处理了100条数据，匹配了2条\n", "读取第267批数据（每批100条）...\n", "批次267: 处理了100条数据，匹配了5条\n", "读取第268批数据（每批100条）...\n", "批次268: 处理了100条数据，匹配了15条\n", "读取第269批数据（每批100条）...\n", "批次269: 处理了100条数据，匹配了2条\n", "读取第270批数据（每批100条）...\n", "暂停5秒...\n", "批次270: 处理了100条数据，匹配了1条\n", "临时保存已匹配的1181条数据...\n", "读取第271批数据（每批100条）...\n", "批次271: 处理了100条数据，匹配了1条\n", "读取第272批数据（每批100条）...\n", "批次272: 处理了100条数据，匹配了4条\n", "读取第273批数据（每批100条）...\n", "批次273: 处理了100条数据，匹配了9条\n", "读取第274批数据（每批100条）...\n", "批次274: 处理了100条数据，匹配了17条\n", "读取第275批数据（每批100条）...\n", "批次275: 处理了100条数据，匹配了11条\n", "读取第276批数据（每批100条）...\n", "批次276: 处理了100条数据，匹配了15条\n", "读取第277批数据（每批100条）...\n", "批次277: 处理了100条数据，匹配了1条\n", "读取第278批数据（每批100条）...\n", "批次278: 处理了100条数据，匹配了0条\n", "读取第279批数据（每批100条）...\n", "批次279: 处理了100条数据，匹配了17条\n", "读取第280批数据（每批100条）...\n", "暂停5秒...\n", "批次280: 处理了100条数据，匹配了0条\n", "临时保存已匹配的1256条数据...\n", "读取第281批数据（每批100条）...\n", "批次281: 处理了100条数据，匹配了1条\n", "读取第282批数据（每批100条）...\n", "批次282: 处理了100条数据，匹配了3条\n", "读取第283批数据（每批100条）...\n", "批次283: 处理了100条数据，匹配了1条\n", "读取第284批数据（每批100条）...\n", "批次284: 处理了100条数据，匹配了2条\n", "读取第285批数据（每批100条）...\n", "批次285: 处理了100条数据，匹配了15条\n", "读取第286批数据（每批100条）...\n", "批次286: 处理了100条数据，匹配了9条\n", "读取第287批数据（每批100条）...\n", "批次287: 处理了100条数据，匹配了1条\n", "读取第288批数据（每批100条）...\n", "批次288: 处理了100条数据，匹配了0条\n", "读取第289批数据（每批100条）...\n", "批次289: 处理了100条数据，匹配了0条\n", "读取第290批数据（每批100条）...\n", "暂停5秒...\n", "批次290: 处理了100条数据，匹配了4条\n", "临时保存已匹配的1292条数据...\n", "读取第291批数据（每批100条）...\n", "批次291: 处理了100条数据，匹配了1条\n", "读取第292批数据（每批100条）...\n", "批次292: 处理了100条数据，匹配了9条\n", "读取第293批数据（每批100条）...\n", "批次293: 处理了100条数据，匹配了2条\n", "读取第294批数据（每批100条）...\n", "批次294: 处理了100条数据，匹配了0条\n", "读取第295批数据（每批100条）...\n", "批次295: 处理了100条数据，匹配了1条\n", "读取第296批数据（每批100条）...\n", "批次296: 处理了100条数据，匹配了1条\n", "读取第297批数据（每批100条）...\n", "批次297: 处理了100条数据，匹配了2条\n", "读取第298批数据（每批100条）...\n", "批次298: 处理了100条数据，匹配了3条\n", "读取第299批数据（每批100条）...\n", "批次299: 处理了100条数据，匹配了1条\n", "读取第300批数据（每批100条）...\n", "暂停5秒...\n", "批次300: 处理了100条数据，匹配了8条\n", "临时保存已匹配的1320条数据...\n", "读取第301批数据（每批100条）...\n", "批次301: 处理了100条数据，匹配了0条\n", "读取第302批数据（每批100条）...\n", "批次302: 处理了100条数据，匹配了10条\n", "读取第303批数据（每批100条）...\n", "批次303: 处理了100条数据，匹配了8条\n", "读取第304批数据（每批100条）...\n", "批次304: 处理了100条数据，匹配了11条\n", "读取第305批数据（每批100条）...\n", "批次305: 处理了100条数据，匹配了0条\n", "读取第306批数据（每批100条）...\n", "批次306: 处理了100条数据，匹配了3条\n", "读取第307批数据（每批100条）...\n", "批次307: 处理了100条数据，匹配了31条\n", "读取第308批数据（每批100条）...\n", "批次308: 处理了100条数据，匹配了20条\n", "读取第309批数据（每批100条）...\n", "批次309: 处理了100条数据，匹配了1条\n", "读取第310批数据（每批100条）...\n", "暂停5秒...\n", "批次310: 处理了100条数据，匹配了5条\n", "临时保存已匹配的1409条数据...\n", "读取第311批数据（每批100条）...\n", "批次311: 处理了100条数据，匹配了3条\n", "读取第312批数据（每批100条）...\n", "批次312: 处理了100条数据，匹配了1条\n", "读取第313批数据（每批100条）...\n", "批次313: 处理了100条数据，匹配了8条\n", "读取第314批数据（每批100条）...\n", "批次314: 处理了100条数据，匹配了2条\n", "读取第315批数据（每批100条）...\n", "批次315: 处理了100条数据，匹配了4条\n", "读取第316批数据（每批100条）...\n", "批次316: 处理了100条数据，匹配了2条\n", "读取第317批数据（每批100条）...\n", "批次317: 处理了100条数据，匹配了5条\n", "读取第318批数据（每批100条）...\n", "批次318: 处理了100条数据，匹配了6条\n", "读取第319批数据（每批100条）...\n", "批次319: 处理了100条数据，匹配了0条\n", "读取第320批数据（每批100条）...\n", "暂停5秒...\n", "批次320: 处理了100条数据，匹配了5条\n", "临时保存已匹配的1445条数据...\n", "读取第321批数据（每批100条）...\n", "批次321: 处理了100条数据，匹配了5条\n", "读取第322批数据（每批100条）...\n", "批次322: 处理了100条数据，匹配了3条\n", "读取第323批数据（每批100条）...\n", "批次323: 处理了100条数据，匹配了0条\n", "读取第324批数据（每批100条）...\n", "批次324: 处理了100条数据，匹配了1条\n", "读取第325批数据（每批100条）...\n", "批次325: 处理了100条数据，匹配了3条\n", "读取第326批数据（每批100条）...\n", "批次326: 处理了100条数据，匹配了4条\n", "读取第327批数据（每批100条）...\n", "批次327: 处理了100条数据，匹配了7条\n", "读取第328批数据（每批100条）...\n", "批次328: 处理了100条数据，匹配了12条\n", "读取第329批数据（每批100条）...\n", "批次329: 处理了100条数据，匹配了7条\n", "读取第330批数据（每批100条）...\n", "暂停5秒...\n", "批次330: 处理了100条数据，匹配了6条\n", "临时保存已匹配的1493条数据...\n", "读取第331批数据（每批100条）...\n", "批次331: 处理了100条数据，匹配了1条\n", "读取第332批数据（每批100条）...\n", "批次332: 处理了100条数据，匹配了4条\n", "读取第333批数据（每批100条）...\n", "批次333: 处理了100条数据，匹配了2条\n", "读取第334批数据（每批100条）...\n", "批次334: 处理了100条数据，匹配了6条\n", "读取第335批数据（每批100条）...\n", "批次335: 处理了100条数据，匹配了9条\n", "读取第336批数据（每批100条）...\n", "批次336: 处理了100条数据，匹配了4条\n", "读取第337批数据（每批100条）...\n", "批次337: 处理了100条数据，匹配了8条\n", "读取第338批数据（每批100条）...\n", "批次338: 处理了100条数据，匹配了5条\n", "读取第339批数据（每批100条）...\n", "批次339: 处理了100条数据，匹配了1条\n", "读取第340批数据（每批100条）...\n", "暂停5秒...\n", "批次340: 处理了100条数据，匹配了1条\n", "临时保存已匹配的1534条数据...\n", "读取第341批数据（每批100条）...\n", "批次341: 处理了100条数据，匹配了14条\n", "读取第342批数据（每批100条）...\n", "批次342: 处理了100条数据，匹配了0条\n", "读取第343批数据（每批100条）...\n", "批次343: 处理了100条数据，匹配了2条\n", "读取第344批数据（每批100条）...\n", "批次344: 处理了100条数据，匹配了2条\n", "读取第345批数据（每批100条）...\n", "批次345: 处理了100条数据，匹配了1条\n", "读取第346批数据（每批100条）...\n", "批次346: 处理了100条数据，匹配了2条\n", "读取第347批数据（每批100条）...\n", "批次347: 处理了100条数据，匹配了0条\n", "读取第348批数据（每批100条）...\n", "批次348: 处理了100条数据，匹配了3条\n", "读取第349批数据（每批100条）...\n", "批次349: 处理了100条数据，匹配了9条\n", "读取第350批数据（每批100条）...\n", "暂停5秒...\n", "批次350: 处理了100条数据，匹配了4条\n", "临时保存已匹配的1571条数据...\n", "读取第351批数据（每批100条）...\n", "批次351: 处理了100条数据，匹配了1条\n", "读取第352批数据（每批100条）...\n", "批次352: 处理了100条数据，匹配了4条\n", "读取第353批数据（每批100条）...\n", "批次353: 处理了100条数据，匹配了6条\n", "读取第354批数据（每批100条）...\n", "批次354: 处理了100条数据，匹配了4条\n", "读取第355批数据（每批100条）...\n", "批次355: 处理了100条数据，匹配了5条\n", "读取第356批数据（每批100条）...\n", "批次356: 处理了100条数据，匹配了0条\n", "读取第357批数据（每批100条）...\n", "批次357: 处理了100条数据，匹配了3条\n", "读取第358批数据（每批100条）...\n", "批次358: 处理了100条数据，匹配了1条\n", "读取第359批数据（每批100条）...\n", "批次359: 处理了100条数据，匹配了2条\n", "读取第360批数据（每批100条）...\n", "暂停5秒...\n", "批次360: 处理了100条数据，匹配了1条\n", "临时保存已匹配的1598条数据...\n", "读取第361批数据（每批100条）...\n", "批次361: 处理了100条数据，匹配了0条\n", "读取第362批数据（每批100条）...\n", "批次362: 处理了100条数据，匹配了5条\n", "读取第363批数据（每批100条）...\n", "批次363: 处理了100条数据，匹配了2条\n", "读取第364批数据（每批100条）...\n", "批次364: 处理了100条数据，匹配了2条\n", "读取第365批数据（每批100条）...\n", "批次365: 处理了100条数据，匹配了1条\n", "读取第366批数据（每批100条）...\n", "批次366: 处理了100条数据，匹配了2条\n", "读取第367批数据（每批100条）...\n", "批次367: 处理了100条数据，匹配了2条\n", "读取第368批数据（每批100条）...\n", "批次368: 处理了100条数据，匹配了0条\n", "读取第369批数据（每批100条）...\n", "批次369: 处理了100条数据，匹配了0条\n", "读取第370批数据（每批100条）...\n", "暂停5秒...\n", "批次370: 处理了100条数据，匹配了1条\n", "临时保存已匹配的1613条数据...\n", "读取第371批数据（每批100条）...\n", "批次371: 处理了100条数据，匹配了0条\n", "读取第372批数据（每批100条）...\n", "批次372: 处理了100条数据，匹配了1条\n", "读取第373批数据（每批100条）...\n", "批次373: 处理了100条数据，匹配了2条\n", "读取第374批数据（每批100条）...\n", "批次374: 处理了100条数据，匹配了1条\n", "读取第375批数据（每批100条）...\n", "批次375: 处理了100条数据，匹配了0条\n", "读取第376批数据（每批100条）...\n", "批次376: 处理了100条数据，匹配了1条\n", "读取第377批数据（每批100条）...\n", "批次377: 处理了100条数据，匹配了4条\n", "读取第378批数据（每批100条）...\n", "批次378: 处理了100条数据，匹配了1条\n", "读取第379批数据（每批100条）...\n", "批次379: 处理了100条数据，匹配了5条\n", "读取第380批数据（每批100条）...\n", "暂停5秒...\n", "批次380: 处理了100条数据，匹配了0条\n", "临时保存已匹配的1628条数据...\n", "读取第381批数据（每批100条）...\n", "批次381: 处理了100条数据，匹配了2条\n", "读取第382批数据（每批100条）...\n", "批次382: 处理了100条数据，匹配了3条\n", "读取第383批数据（每批100条）...\n", "批次383: 处理了100条数据，匹配了3条\n", "读取第384批数据（每批100条）...\n", "批次384: 处理了100条数据，匹配了1条\n", "读取第385批数据（每批100条）...\n", "批次385: 处理了100条数据，匹配了1条\n", "读取第386批数据（每批100条）...\n", "批次386: 处理了100条数据，匹配了9条\n", "读取第387批数据（每批100条）...\n", "批次387: 处理了100条数据，匹配了2条\n", "读取第388批数据（每批100条）...\n", "批次388: 处理了100条数据，匹配了1条\n", "读取第389批数据（每批100条）...\n", "批次389: 处理了100条数据，匹配了0条\n", "读取第390批数据（每批100条）...\n", "暂停5秒...\n", "批次390: 处理了100条数据，匹配了1条\n", "临时保存已匹配的1651条数据...\n", "读取第391批数据（每批100条）...\n", "批次391: 处理了100条数据，匹配了2条\n", "读取第392批数据（每批100条）...\n", "批次392: 处理了100条数据，匹配了4条\n", "读取第393批数据（每批100条）...\n", "批次393: 处理了100条数据，匹配了14条\n", "读取第394批数据（每批100条）...\n", "批次394: 处理了100条数据，匹配了10条\n", "读取第395批数据（每批100条）...\n", "批次395: 处理了100条数据，匹配了5条\n", "读取第396批数据（每批100条）...\n", "批次396: 处理了100条数据，匹配了26条\n", "读取第397批数据（每批100条）...\n", "批次397: 处理了100条数据，匹配了4条\n", "读取第398批数据（每批100条）...\n", "批次398: 处理了100条数据，匹配了17条\n", "读取第399批数据（每批100条）...\n", "批次399: 处理了100条数据，匹配了4条\n", "读取第400批数据（每批100条）...\n", "暂停5秒...\n", "批次400: 处理了100条数据，匹配了2条\n", "临时保存已匹配的1739条数据...\n", "读取第401批数据（每批100条）...\n", "批次401: 处理了100条数据，匹配了4条\n", "读取第402批数据（每批100条）...\n", "批次402: 处理了100条数据，匹配了14条\n", "读取第403批数据（每批100条）...\n", "批次403: 处理了100条数据，匹配了3条\n", "读取第404批数据（每批100条）...\n", "批次404: 处理了100条数据，匹配了0条\n", "读取第405批数据（每批100条）...\n", "批次405: 处理了100条数据，匹配了6条\n", "读取第406批数据（每批100条）...\n", "批次406: 处理了100条数据，匹配了7条\n", "读取第407批数据（每批100条）...\n", "批次407: 处理了100条数据，匹配了4条\n", "读取第408批数据（每批100条）...\n", "批次408: 处理了100条数据，匹配了1条\n", "读取第409批数据（每批100条）...\n", "批次409: 处理了100条数据，匹配了4条\n", "读取第410批数据（每批100条）...\n", "暂停5秒...\n", "批次410: 处理了100条数据，匹配了5条\n", "临时保存已匹配的1787条数据...\n", "读取第411批数据（每批100条）...\n", "批次411: 处理了100条数据，匹配了3条\n", "读取第412批数据（每批100条）...\n", "批次412: 处理了100条数据，匹配了4条\n", "读取第413批数据（每批100条）...\n", "批次413: 处理了100条数据，匹配了8条\n", "读取第414批数据（每批100条）...\n", "批次414: 处理了100条数据，匹配了5条\n", "读取第415批数据（每批100条）...\n", "批次415: 处理了100条数据，匹配了4条\n", "读取第416批数据（每批100条）...\n", "批次416: 处理了100条数据，匹配了12条\n", "读取第417批数据（每批100条）...\n", "批次417: 处理了100条数据，匹配了2条\n", "读取第418批数据（每批100条）...\n", "批次418: 处理了100条数据，匹配了1条\n", "读取第419批数据（每批100条）...\n", "批次419: 处理了100条数据，匹配了5条\n", "读取第420批数据（每批100条）...\n", "暂停5秒...\n", "批次420: 处理了100条数据，匹配了3条\n", "临时保存已匹配的1834条数据...\n", "读取第421批数据（每批100条）...\n", "批次421: 处理了100条数据，匹配了8条\n", "读取第422批数据（每批100条）...\n", "批次422: 处理了100条数据，匹配了1条\n", "读取第423批数据（每批100条）...\n", "批次423: 处理了100条数据，匹配了5条\n", "读取第424批数据（每批100条）...\n", "批次424: 处理了100条数据，匹配了2条\n", "读取第425批数据（每批100条）...\n", "批次425: 处理了100条数据，匹配了7条\n", "读取第426批数据（每批100条）...\n", "批次426: 处理了100条数据，匹配了0条\n", "读取第427批数据（每批100条）...\n", "批次427: 处理了100条数据，匹配了2条\n", "读取第428批数据（每批100条）...\n", "批次428: 处理了100条数据，匹配了12条\n", "读取第429批数据（每批100条）...\n", "批次429: 处理了100条数据，匹配了4条\n", "读取第430批数据（每批100条）...\n", "暂停5秒...\n", "批次430: 处理了100条数据，匹配了3条\n", "临时保存已匹配的1878条数据...\n", "读取第431批数据（每批100条）...\n", "批次431: 处理了100条数据，匹配了0条\n", "读取第432批数据（每批100条）...\n", "批次432: 处理了100条数据，匹配了1条\n", "读取第433批数据（每批100条）...\n", "批次433: 处理了100条数据，匹配了0条\n", "读取第434批数据（每批100条）...\n", "批次434: 处理了100条数据，匹配了1条\n", "读取第435批数据（每批100条）...\n", "批次435: 处理了100条数据，匹配了5条\n", "读取第436批数据（每批100条）...\n", "批次436: 处理了100条数据，匹配了5条\n", "读取第437批数据（每批100条）...\n", "批次437: 处理了100条数据，匹配了5条\n", "读取第438批数据（每批100条）...\n", "批次438: 处理了100条数据，匹配了2条\n", "读取第439批数据（每批100条）...\n", "批次439: 处理了100条数据，匹配了2条\n", "读取第440批数据（每批100条）...\n", "暂停5秒...\n", "批次440: 处理了100条数据，匹配了4条\n", "临时保存已匹配的1903条数据...\n", "读取第441批数据（每批100条）...\n", "批次441: 处理了100条数据，匹配了7条\n", "读取第442批数据（每批100条）...\n", "批次442: 处理了100条数据，匹配了8条\n", "读取第443批数据（每批100条）...\n", "批次443: 处理了100条数据，匹配了1条\n", "读取第444批数据（每批100条）...\n", "批次444: 处理了100条数据，匹配了1条\n", "读取第445批数据（每批100条）...\n", "批次445: 处理了100条数据，匹配了7条\n", "读取第446批数据（每批100条）...\n", "批次446: 处理了100条数据，匹配了2条\n", "读取第447批数据（每批100条）...\n", "批次447: 处理了100条数据，匹配了4条\n", "读取第448批数据（每批100条）...\n", "批次448: 处理了100条数据，匹配了2条\n", "读取第449批数据（每批100条）...\n", "批次449: 处理了100条数据，匹配了7条\n", "读取第450批数据（每批100条）...\n", "暂停5秒...\n", "批次450: 处理了100条数据，匹配了3条\n", "临时保存已匹配的1945条数据...\n", "读取第451批数据（每批100条）...\n", "批次451: 处理了100条数据，匹配了5条\n", "读取第452批数据（每批100条）...\n", "批次452: 处理了100条数据，匹配了4条\n", "读取第453批数据（每批100条）...\n", "批次453: 处理了100条数据，匹配了12条\n", "读取第454批数据（每批100条）...\n", "批次454: 处理了100条数据，匹配了6条\n", "读取第455批数据（每批100条）...\n", "批次455: 处理了100条数据，匹配了1条\n", "读取第456批数据（每批100条）...\n", "批次456: 处理了100条数据，匹配了2条\n", "读取第457批数据（每批100条）...\n", "批次457: 处理了100条数据，匹配了2条\n", "读取第458批数据（每批100条）...\n", "批次458: 处理了100条数据，匹配了2条\n", "读取第459批数据（每批100条）...\n", "批次459: 处理了100条数据，匹配了6条\n", "读取第460批数据（每批100条）...\n", "暂停5秒...\n", "批次460: 处理了100条数据，匹配了4条\n", "临时保存已匹配的1989条数据...\n", "读取第461批数据（每批100条）...\n", "批次461: 处理了100条数据，匹配了2条\n", "读取第462批数据（每批100条）...\n", "批次462: 处理了100条数据，匹配了6条\n", "读取第463批数据（每批100条）...\n", "批次463: 处理了100条数据，匹配了2条\n", "读取第464批数据（每批100条）...\n", "批次464: 处理了100条数据，匹配了1条\n", "读取第465批数据（每批100条）...\n", "批次465: 处理了100条数据，匹配了4条\n", "读取第466批数据（每批100条）...\n", "批次466: 处理了100条数据，匹配了0条\n", "读取第467批数据（每批100条）...\n", "批次467: 处理了100条数据，匹配了3条\n", "读取第468批数据（每批100条）...\n", "批次468: 处理了100条数据，匹配了2条\n", "读取第469批数据（每批100条）...\n", "批次469: 处理了100条数据，匹配了6条\n", "读取第470批数据（每批100条）...\n", "暂停5秒...\n", "批次470: 处理了100条数据，匹配了6条\n", "临时保存已匹配的2021条数据...\n", "读取第471批数据（每批100条）...\n", "批次471: 处理了100条数据，匹配了7条\n", "读取第472批数据（每批100条）...\n", "批次472: 处理了100条数据，匹配了5条\n", "读取第473批数据（每批100条）...\n", "批次473: 处理了100条数据，匹配了10条\n", "读取第474批数据（每批100条）...\n", "批次474: 处理了100条数据，匹配了12条\n", "读取第475批数据（每批100条）...\n", "批次475: 处理了100条数据，匹配了15条\n", "读取第476批数据（每批100条）...\n", "批次476: 处理了100条数据，匹配了3条\n", "读取第477批数据（每批100条）...\n", "批次477: 处理了100条数据，匹配了0条\n", "读取第478批数据（每批100条）...\n", "批次478: 处理了100条数据，匹配了1条\n", "读取第479批数据（每批100条）...\n", "批次479: 处理了100条数据，匹配了1条\n", "读取第480批数据（每批100条）...\n", "暂停5秒...\n", "批次480: 处理了100条数据，匹配了2条\n", "临时保存已匹配的2077条数据...\n", "读取第481批数据（每批100条）...\n", "批次481: 处理了100条数据，匹配了1条\n", "读取第482批数据（每批100条）...\n", "批次482: 处理了100条数据，匹配了3条\n", "读取第483批数据（每批100条）...\n", "批次483: 处理了100条数据，匹配了6条\n", "读取第484批数据（每批100条）...\n", "批次484: 处理了100条数据，匹配了2条\n", "读取第485批数据（每批100条）...\n", "批次485: 处理了100条数据，匹配了0条\n", "读取第486批数据（每批100条）...\n", "批次486: 处理了100条数据，匹配了8条\n", "读取第487批数据（每批100条）...\n", "批次487: 处理了100条数据，匹配了2条\n", "读取第488批数据（每批100条）...\n", "批次488: 处理了100条数据，匹配了2条\n", "读取第489批数据（每批100条）...\n", "批次489: 处理了100条数据，匹配了4条\n", "读取第490批数据（每批100条）...\n", "暂停5秒...\n", "批次490: 处理了100条数据，匹配了4条\n", "临时保存已匹配的2109条数据...\n", "读取第491批数据（每批100条）...\n", "批次491: 处理了100条数据，匹配了7条\n", "读取第492批数据（每批100条）...\n", "批次492: 处理了100条数据，匹配了1条\n", "读取第493批数据（每批100条）...\n", "批次493: 处理了100条数据，匹配了5条\n", "读取第494批数据（每批100条）...\n", "批次494: 处理了100条数据，匹配了12条\n", "读取第495批数据（每批100条）...\n", "批次495: 处理了100条数据，匹配了11条\n", "读取第496批数据（每批100条）...\n", "批次496: 处理了100条数据，匹配了6条\n", "读取第497批数据（每批100条）...\n", "批次497: 处理了100条数据，匹配了1条\n", "读取第498批数据（每批100条）...\n", "批次498: 处理了100条数据，匹配了1条\n", "读取第499批数据（每批100条）...\n", "批次499: 处理了100条数据，匹配了1条\n", "读取第500批数据（每批100条）...\n", "暂停5秒...\n", "批次500: 处理了100条数据，匹配了6条\n", "临时保存已匹配的2160条数据...\n", "读取第501批数据（每批100条）...\n", "批次501: 处理了100条数据，匹配了3条\n", "读取第502批数据（每批100条）...\n", "批次502: 处理了100条数据，匹配了2条\n", "读取第503批数据（每批100条）...\n", "批次503: 处理了100条数据，匹配了1条\n", "读取第504批数据（每批100条）...\n", "批次504: 处理了100条数据，匹配了0条\n", "读取第505批数据（每批100条）...\n", "批次505: 处理了100条数据，匹配了10条\n", "读取第506批数据（每批100条）...\n", "批次506: 处理了100条数据，匹配了2条\n", "读取第507批数据（每批100条）...\n", "批次507: 处理了100条数据，匹配了3条\n", "读取第508批数据（每批100条）...\n", "批次508: 处理了100条数据，匹配了1条\n", "读取第509批数据（每批100条）...\n", "批次509: 处理了100条数据，匹配了0条\n", "读取第510批数据（每批100条）...\n", "暂停5秒...\n", "批次510: 处理了100条数据，匹配了2条\n", "临时保存已匹配的2184条数据...\n", "读取第511批数据（每批100条）...\n", "批次511: 处理了100条数据，匹配了2条\n", "读取第512批数据（每批100条）...\n", "批次512: 处理了100条数据，匹配了5条\n", "读取第513批数据（每批100条）...\n", "批次513: 处理了100条数据，匹配了1条\n", "读取第514批数据（每批100条）...\n", "批次514: 处理了100条数据，匹配了4条\n", "读取第515批数据（每批100条）...\n", "批次515: 处理了100条数据，匹配了8条\n", "读取第516批数据（每批100条）...\n", "批次516: 处理了100条数据，匹配了5条\n", "读取第517批数据（每批100条）...\n", "批次517: 处理了100条数据，匹配了3条\n", "读取第518批数据（每批100条）...\n", "批次518: 处理了100条数据，匹配了4条\n", "读取第519批数据（每批100条）...\n", "批次519: 处理了100条数据，匹配了4条\n", "读取第520批数据（每批100条）...\n", "暂停5秒...\n", "批次520: 处理了100条数据，匹配了8条\n", "临时保存已匹配的2228条数据...\n", "读取第521批数据（每批100条）...\n", "批次521: 处理了100条数据，匹配了1条\n", "读取第522批数据（每批100条）...\n", "批次522: 处理了100条数据，匹配了0条\n", "读取第523批数据（每批100条）...\n", "批次523: 处理了100条数据，匹配了1条\n", "读取第524批数据（每批100条）...\n", "批次524: 处理了100条数据，匹配了2条\n", "读取第525批数据（每批100条）...\n", "批次525: 处理了100条数据，匹配了0条\n", "读取第526批数据（每批100条）...\n", "批次526: 处理了100条数据，匹配了10条\n", "读取第527批数据（每批100条）...\n", "批次527: 处理了100条数据，匹配了1条\n", "读取第528批数据（每批100条）...\n", "批次528: 处理了100条数据，匹配了4条\n", "读取第529批数据（每批100条）...\n", "批次529: 处理了100条数据，匹配了7条\n", "读取第530批数据（每批100条）...\n", "暂停5秒...\n", "批次530: 处理了100条数据，匹配了8条\n", "临时保存已匹配的2262条数据...\n", "读取第531批数据（每批100条）...\n", "批次531: 处理了100条数据，匹配了4条\n", "读取第532批数据（每批100条）...\n", "批次532: 处理了100条数据，匹配了4条\n", "读取第533批数据（每批100条）...\n", "批次533: 处理了100条数据，匹配了1条\n", "读取第534批数据（每批100条）...\n", "批次534: 处理了100条数据，匹配了1条\n", "读取第535批数据（每批100条）...\n", "批次535: 处理了100条数据，匹配了1条\n", "读取第536批数据（每批100条）...\n", "批次536: 处理了100条数据，匹配了1条\n", "读取第537批数据（每批100条）...\n", "批次537: 处理了100条数据，匹配了9条\n", "读取第538批数据（每批100条）...\n", "批次538: 处理了100条数据，匹配了5条\n", "读取第539批数据（每批100条）...\n", "批次539: 处理了100条数据，匹配了6条\n", "读取第540批数据（每批100条）...\n", "暂停5秒...\n", "批次540: 处理了100条数据，匹配了2条\n", "临时保存已匹配的2296条数据...\n", "读取第541批数据（每批100条）...\n", "批次541: 处理了100条数据，匹配了8条\n", "读取第542批数据（每批100条）...\n", "批次542: 处理了100条数据，匹配了1条\n", "读取第543批数据（每批100条）...\n", "批次543: 处理了100条数据，匹配了7条\n", "读取第544批数据（每批100条）...\n", "批次544: 处理了100条数据，匹配了6条\n", "读取第545批数据（每批100条）...\n", "批次545: 处理了100条数据，匹配了2条\n", "读取第546批数据（每批100条）...\n", "批次546: 处理了100条数据，匹配了9条\n", "读取第547批数据（每批100条）...\n", "批次547: 处理了100条数据，匹配了4条\n", "读取第548批数据（每批100条）...\n", "批次548: 处理了100条数据，匹配了0条\n", "读取第549批数据（每批100条）...\n", "批次549: 处理了100条数据，匹配了1条\n", "读取第550批数据（每批100条）...\n", "暂停5秒...\n", "批次550: 处理了100条数据，匹配了0条\n", "临时保存已匹配的2334条数据...\n", "读取第551批数据（每批100条）...\n", "批次551: 处理了100条数据，匹配了5条\n", "读取第552批数据（每批100条）...\n", "批次552: 处理了100条数据，匹配了1条\n", "读取第553批数据（每批100条）...\n", "批次553: 处理了100条数据，匹配了1条\n", "读取第554批数据（每批100条）...\n", "批次554: 处理了100条数据，匹配了0条\n", "读取第555批数据（每批100条）...\n", "批次555: 处理了100条数据，匹配了1条\n", "读取第556批数据（每批100条）...\n", "批次556: 处理了100条数据，匹配了0条\n", "读取第557批数据（每批100条）...\n", "批次557: 处理了100条数据，匹配了3条\n", "读取第558批数据（每批100条）...\n", "批次558: 处理了100条数据，匹配了0条\n", "读取第559批数据（每批100条）...\n", "批次559: 处理了100条数据，匹配了2条\n", "读取第560批数据（每批100条）...\n", "暂停5秒...\n", "批次560: 处理了100条数据，匹配了3条\n", "临时保存已匹配的2350条数据...\n", "读取第561批数据（每批100条）...\n", "批次561: 处理了100条数据，匹配了1条\n", "读取第562批数据（每批100条）...\n", "批次562: 处理了100条数据，匹配了2条\n", "读取第563批数据（每批100条）...\n", "批次563: 处理了100条数据，匹配了2条\n", "读取第564批数据（每批100条）...\n", "批次564: 处理了100条数据，匹配了0条\n", "读取第565批数据（每批100条）...\n", "批次565: 处理了100条数据，匹配了0条\n", "读取第566批数据（每批100条）...\n", "批次566: 处理了100条数据，匹配了4条\n", "读取第567批数据（每批100条）...\n", "批次567: 处理了100条数据，匹配了4条\n", "读取第568批数据（每批100条）...\n", "批次568: 处理了100条数据，匹配了4条\n", "读取第569批数据（每批100条）...\n", "批次569: 处理了100条数据，匹配了5条\n", "读取第570批数据（每批100条）...\n", "暂停5秒...\n", "批次570: 处理了100条数据，匹配了3条\n", "临时保存已匹配的2375条数据...\n", "读取第571批数据（每批100条）...\n", "批次571: 处理了100条数据，匹配了6条\n", "读取第572批数据（每批100条）...\n", "批次572: 处理了100条数据，匹配了0条\n", "读取第573批数据（每批100条）...\n", "批次573: 处理了100条数据，匹配了4条\n", "读取第574批数据（每批100条）...\n", "批次574: 处理了100条数据，匹配了7条\n", "读取第575批数据（每批100条）...\n", "批次575: 处理了100条数据，匹配了2条\n", "读取第576批数据（每批100条）...\n", "批次576: 处理了100条数据，匹配了3条\n", "读取第577批数据（每批100条）...\n", "批次577: 处理了100条数据，匹配了3条\n", "读取第578批数据（每批100条）...\n", "批次578: 处理了100条数据，匹配了2条\n", "读取第579批数据（每批100条）...\n", "批次579: 处理了100条数据，匹配了0条\n", "读取第580批数据（每批100条）...\n", "暂停5秒...\n", "批次580: 处理了100条数据，匹配了0条\n", "临时保存已匹配的2402条数据...\n", "读取第581批数据（每批100条）...\n", "批次581: 处理了100条数据，匹配了5条\n", "读取第582批数据（每批100条）...\n", "批次582: 处理了100条数据，匹配了3条\n", "读取第583批数据（每批100条）...\n", "批次583: 处理了100条数据，匹配了6条\n", "读取第584批数据（每批100条）...\n", "批次584: 处理了100条数据，匹配了7条\n", "读取第585批数据（每批100条）...\n", "批次585: 处理了100条数据，匹配了9条\n", "读取第586批数据（每批100条）...\n", "批次586: 处理了100条数据，匹配了7条\n", "读取第587批数据（每批100条）...\n", "批次587: 处理了100条数据，匹配了14条\n", "读取第588批数据（每批100条）...\n", "批次588: 处理了100条数据，匹配了4条\n", "读取第589批数据（每批100条）...\n", "批次589: 处理了100条数据，匹配了3条\n", "读取第590批数据（每批100条）...\n", "暂停5秒...\n", "批次590: 处理了100条数据，匹配了3条\n", "临时保存已匹配的2463条数据...\n", "读取第591批数据（每批100条）...\n", "批次591: 处理了100条数据，匹配了9条\n", "读取第592批数据（每批100条）...\n", "批次592: 处理了100条数据，匹配了1条\n", "读取第593批数据（每批100条）...\n", "批次593: 处理了100条数据，匹配了7条\n", "读取第594批数据（每批100条）...\n", "批次594: 处理了100条数据，匹配了3条\n", "读取第595批数据（每批100条）...\n", "批次595: 处理了100条数据，匹配了14条\n", "读取第596批数据（每批100条）...\n", "批次596: 处理了100条数据，匹配了4条\n", "读取第597批数据（每批100条）...\n", "批次597: 处理了100条数据，匹配了5条\n", "读取第598批数据（每批100条）...\n", "批次598: 处理了100条数据，匹配了2条\n", "读取第599批数据（每批100条）...\n", "批次599: 处理了100条数据，匹配了3条\n", "读取第600批数据（每批100条）...\n", "暂停5秒...\n", "批次600: 处理了100条数据，匹配了3条\n", "临时保存已匹配的2514条数据...\n", "读取第601批数据（每批100条）...\n", "批次601: 处理了100条数据，匹配了2条\n", "读取第602批数据（每批100条）...\n", "批次602: 处理了100条数据，匹配了3条\n", "读取第603批数据（每批100条）...\n", "批次603: 处理了100条数据，匹配了3条\n", "读取第604批数据（每批100条）...\n", "批次604: 处理了100条数据，匹配了5条\n", "读取第605批数据（每批100条）...\n", "批次605: 处理了100条数据，匹配了5条\n", "读取第606批数据（每批100条）...\n", "批次606: 处理了100条数据，匹配了7条\n", "读取第607批数据（每批100条）...\n", "批次607: 处理了100条数据，匹配了1条\n", "读取第608批数据（每批100条）...\n", "批次608: 处理了100条数据，匹配了1条\n", "读取第609批数据（每批100条）...\n", "批次609: 处理了100条数据，匹配了0条\n", "读取第610批数据（每批100条）...\n", "暂停5秒...\n", "批次610: 处理了100条数据，匹配了12条\n", "临时保存已匹配的2553条数据...\n", "读取第611批数据（每批100条）...\n", "批次611: 处理了100条数据，匹配了3条\n", "读取第612批数据（每批100条）...\n", "批次612: 处理了100条数据，匹配了3条\n", "读取第613批数据（每批100条）...\n", "批次613: 处理了100条数据，匹配了1条\n", "读取第614批数据（每批100条）...\n", "批次614: 处理了100条数据，匹配了6条\n", "读取第615批数据（每批100条）...\n", "批次615: 处理了100条数据，匹配了11条\n", "读取第616批数据（每批100条）...\n", "批次616: 处理了100条数据，匹配了0条\n", "读取第617批数据（每批100条）...\n", "批次617: 处理了100条数据，匹配了6条\n", "读取第618批数据（每批100条）...\n", "批次618: 处理了100条数据，匹配了6条\n", "读取第619批数据（每批100条）...\n", "批次619: 处理了100条数据，匹配了10条\n", "读取第620批数据（每批100条）...\n", "暂停5秒...\n", "批次620: 处理了100条数据，匹配了10条\n", "临时保存已匹配的2609条数据...\n", "读取第621批数据（每批100条）...\n", "批次621: 处理了100条数据，匹配了8条\n", "读取第622批数据（每批100条）...\n", "批次622: 处理了100条数据，匹配了22条\n", "读取第623批数据（每批100条）...\n", "批次623: 处理了100条数据，匹配了4条\n", "读取第624批数据（每批100条）...\n", "批次624: 处理了100条数据，匹配了6条\n", "读取第625批数据（每批100条）...\n", "批次625: 处理了100条数据，匹配了5条\n", "读取第626批数据（每批100条）...\n", "批次626: 处理了100条数据，匹配了2条\n", "读取第627批数据（每批100条）...\n", "批次627: 处理了100条数据，匹配了1条\n", "读取第628批数据（每批100条）...\n", "批次628: 处理了100条数据，匹配了2条\n", "读取第629批数据（每批100条）...\n", "批次629: 处理了100条数据，匹配了0条\n", "读取第630批数据（每批100条）...\n", "暂停5秒...\n", "批次630: 处理了100条数据，匹配了1条\n", "临时保存已匹配的2660条数据...\n", "读取第631批数据（每批100条）...\n", "批次631: 处理了100条数据，匹配了0条\n", "读取第632批数据（每批100条）...\n", "批次632: 处理了100条数据，匹配了4条\n", "读取第633批数据（每批100条）...\n", "批次633: 处理了100条数据，匹配了4条\n", "读取第634批数据（每批100条）...\n", "批次634: 处理了100条数据，匹配了2条\n", "读取第635批数据（每批100条）...\n", "批次635: 处理了100条数据，匹配了2条\n", "读取第636批数据（每批100条）...\n", "批次636: 处理了100条数据，匹配了1条\n", "读取第637批数据（每批100条）...\n", "批次637: 处理了100条数据，匹配了25条\n", "读取第638批数据（每批100条）...\n", "批次638: 处理了100条数据，匹配了11条\n", "读取第639批数据（每批100条）...\n", "批次639: 处理了100条数据，匹配了15条\n", "读取第640批数据（每批100条）...\n", "暂停5秒...\n", "批次640: 处理了100条数据，匹配了4条\n", "临时保存已匹配的2728条数据...\n", "读取第641批数据（每批100条）...\n", "批次641: 处理了100条数据，匹配了34条\n", "读取第642批数据（每批100条）...\n", "批次642: 处理了100条数据，匹配了7条\n", "读取第643批数据（每批100条）...\n", "批次643: 处理了100条数据，匹配了1条\n", "读取第644批数据（每批100条）...\n", "批次644: 处理了100条数据，匹配了0条\n", "读取第645批数据（每批100条）...\n", "批次645: 处理了100条数据，匹配了2条\n", "读取第646批数据（每批100条）...\n", "批次646: 处理了100条数据，匹配了0条\n", "读取第647批数据（每批100条）...\n", "批次647: 处理了100条数据，匹配了3条\n", "读取第648批数据（每批100条）...\n", "批次648: 处理了100条数据，匹配了2条\n", "读取第649批数据（每批100条）...\n", "批次649: 处理了100条数据，匹配了3条\n", "读取第650批数据（每批100条）...\n", "暂停5秒...\n", "批次650: 处理了100条数据，匹配了2条\n", "临时保存已匹配的2782条数据...\n", "读取第651批数据（每批100条）...\n", "批次651: 处理了100条数据，匹配了3条\n", "读取第652批数据（每批100条）...\n", "批次652: 处理了100条数据，匹配了6条\n", "读取第653批数据（每批100条）...\n", "批次653: 处理了100条数据，匹配了19条\n", "读取第654批数据（每批100条）...\n", "批次654: 处理了100条数据，匹配了1条\n", "读取第655批数据（每批100条）...\n", "批次655: 处理了100条数据，匹配了3条\n", "读取第656批数据（每批100条）...\n", "批次656: 处理了100条数据，匹配了6条\n", "读取第657批数据（每批100条）...\n", "批次657: 处理了100条数据，匹配了5条\n", "读取第658批数据（每批100条）...\n", "批次658: 处理了100条数据，匹配了9条\n", "读取第659批数据（每批100条）...\n", "批次659: 处理了100条数据，匹配了5条\n", "读取第660批数据（每批100条）...\n", "暂停5秒...\n", "批次660: 处理了100条数据，匹配了5条\n", "临时保存已匹配的2844条数据...\n", "读取第661批数据（每批100条）...\n", "批次661: 处理了100条数据，匹配了4条\n", "读取第662批数据（每批100条）...\n", "批次662: 处理了100条数据，匹配了1条\n", "读取第663批数据（每批100条）...\n", "批次663: 处理了100条数据，匹配了3条\n", "读取第664批数据（每批100条）...\n", "批次664: 处理了100条数据，匹配了6条\n", "读取第665批数据（每批100条）...\n", "批次665: 处理了100条数据，匹配了8条\n", "读取第666批数据（每批100条）...\n", "批次666: 处理了100条数据，匹配了1条\n", "读取第667批数据（每批100条）...\n", "批次667: 处理了100条数据，匹配了0条\n", "读取第668批数据（每批100条）...\n", "批次668: 处理了100条数据，匹配了1条\n", "读取第669批数据（每批100条）...\n", "批次669: 处理了100条数据，匹配了1条\n", "读取第670批数据（每批100条）...\n", "暂停5秒...\n", "批次670: 处理了100条数据，匹配了4条\n", "临时保存已匹配的2873条数据...\n", "读取第671批数据（每批100条）...\n", "批次671: 处理了100条数据，匹配了1条\n", "读取第672批数据（每批100条）...\n", "批次672: 处理了100条数据，匹配了4条\n", "读取第673批数据（每批100条）...\n", "批次673: 处理了100条数据，匹配了2条\n", "读取第674批数据（每批100条）...\n", "批次674: 处理了100条数据，匹配了0条\n", "读取第675批数据（每批100条）...\n", "批次675: 处理了100条数据，匹配了0条\n", "读取第676批数据（每批100条）...\n", "批次676: 处理了100条数据，匹配了7条\n", "读取第677批数据（每批100条）...\n", "批次677: 处理了100条数据，匹配了0条\n", "读取第678批数据（每批100条）...\n", "批次678: 处理了100条数据，匹配了0条\n", "读取第679批数据（每批100条）...\n", "批次679: 处理了100条数据，匹配了5条\n", "读取第680批数据（每批100条）...\n", "暂停5秒...\n", "批次680: 处理了100条数据，匹配了0条\n", "临时保存已匹配的2892条数据...\n", "读取第681批数据（每批100条）...\n", "批次681: 处理了100条数据，匹配了2条\n", "读取第682批数据（每批100条）...\n", "批次682: 处理了100条数据，匹配了0条\n", "读取第683批数据（每批100条）...\n", "批次683: 处理了100条数据，匹配了1条\n", "读取第684批数据（每批100条）...\n", "批次684: 处理了100条数据，匹配了6条\n", "读取第685批数据（每批100条）...\n", "批次685: 处理了100条数据，匹配了0条\n", "读取第686批数据（每批100条）...\n", "批次686: 处理了100条数据，匹配了2条\n", "读取第687批数据（每批100条）...\n", "批次687: 处理了100条数据，匹配了2条\n", "读取第688批数据（每批100条）...\n", "批次688: 处理了100条数据，匹配了0条\n", "读取第689批数据（每批100条）...\n", "批次689: 处理了100条数据，匹配了1条\n", "读取第690批数据（每批100条）...\n", "暂停5秒...\n", "批次690: 处理了100条数据，匹配了0条\n", "临时保存已匹配的2906条数据...\n", "读取第691批数据（每批100条）...\n", "批次691: 处理了100条数据，匹配了0条\n", "读取第692批数据（每批100条）...\n", "批次692: 处理了100条数据，匹配了1条\n", "读取第693批数据（每批100条）...\n", "批次693: 处理了100条数据，匹配了0条\n", "读取第694批数据（每批100条）...\n", "批次694: 处理了100条数据，匹配了0条\n", "读取第695批数据（每批100条）...\n", "批次695: 处理了100条数据，匹配了4条\n", "读取第696批数据（每批100条）...\n", "批次696: 处理了100条数据，匹配了1条\n", "读取第697批数据（每批100条）...\n", "批次697: 处理了100条数据，匹配了0条\n", "读取第698批数据（每批100条）...\n", "批次698: 处理了100条数据，匹配了5条\n", "读取第699批数据（每批100条）...\n", "批次699: 处理了100条数据，匹配了1条\n", "读取第700批数据（每批100条）...\n", "暂停5秒...\n", "批次700: 处理了100条数据，匹配了1条\n", "临时保存已匹配的2919条数据...\n", "读取第701批数据（每批100条）...\n", "批次701: 处理了100条数据，匹配了8条\n", "读取第702批数据（每批100条）...\n", "批次702: 处理了100条数据，匹配了7条\n", "读取第703批数据（每批100条）...\n", "批次703: 处理了100条数据，匹配了3条\n", "读取第704批数据（每批100条）...\n", "批次704: 处理了100条数据，匹配了4条\n", "读取第705批数据（每批100条）...\n", "批次705: 处理了100条数据，匹配了1条\n", "读取第706批数据（每批100条）...\n", "批次706: 处理了100条数据，匹配了2条\n", "读取第707批数据（每批100条）...\n", "批次707: 处理了100条数据，匹配了0条\n", "读取第708批数据（每批100条）...\n", "批次708: 处理了100条数据，匹配了0条\n", "读取第709批数据（每批100条）...\n", "批次709: 处理了100条数据，匹配了2条\n", "读取第710批数据（每批100条）...\n", "暂停5秒...\n", "批次710: 处理了100条数据，匹配了3条\n", "临时保存已匹配的2949条数据...\n", "读取第711批数据（每批100条）...\n", "批次711: 处理了100条数据，匹配了1条\n", "读取第712批数据（每批100条）...\n", "批次712: 处理了100条数据，匹配了2条\n", "读取第713批数据（每批100条）...\n", "批次713: 处理了100条数据，匹配了0条\n", "读取第714批数据（每批100条）...\n", "批次714: 处理了100条数据，匹配了4条\n", "读取第715批数据（每批100条）...\n", "批次715: 处理了100条数据，匹配了2条\n", "读取第716批数据（每批100条）...\n", "批次716: 处理了100条数据，匹配了0条\n", "读取第717批数据（每批100条）...\n", "批次717: 处理了100条数据，匹配了2条\n", "读取第718批数据（每批100条）...\n", "批次718: 处理了100条数据，匹配了0条\n", "读取第719批数据（每批100条）...\n", "批次719: 处理了100条数据，匹配了0条\n", "读取第720批数据（每批100条）...\n", "暂停5秒...\n", "批次720: 处理了100条数据，匹配了0条\n", "临时保存已匹配的2960条数据...\n", "读取第721批数据（每批100条）...\n", "批次721: 处理了100条数据，匹配了1条\n", "读取第722批数据（每批100条）...\n", "批次722: 处理了100条数据，匹配了3条\n", "读取第723批数据（每批100条）...\n", "批次723: 处理了100条数据，匹配了1条\n", "读取第724批数据（每批100条）...\n", "批次724: 处理了100条数据，匹配了1条\n", "读取第725批数据（每批100条）...\n", "批次725: 处理了100条数据，匹配了0条\n", "读取第726批数据（每批100条）...\n", "批次726: 处理了100条数据，匹配了0条\n", "读取第727批数据（每批100条）...\n", "批次727: 处理了100条数据，匹配了0条\n", "读取第728批数据（每批100条）...\n", "批次728: 处理了100条数据，匹配了0条\n", "读取第729批数据（每批100条）...\n", "批次729: 处理了100条数据，匹配了0条\n", "读取第730批数据（每批100条）...\n", "暂停5秒...\n", "批次730: 处理了100条数据，匹配了0条\n", "临时保存已匹配的2966条数据...\n", "读取第731批数据（每批100条）...\n", "批次731: 处理了100条数据，匹配了1条\n", "读取第732批数据（每批100条）...\n", "批次732: 处理了100条数据，匹配了0条\n", "读取第733批数据（每批100条）...\n", "批次733: 处理了100条数据，匹配了1条\n", "读取第734批数据（每批100条）...\n", "批次734: 处理了100条数据，匹配了0条\n", "读取第735批数据（每批100条）...\n", "批次735: 处理了100条数据，匹配了0条\n", "读取第736批数据（每批100条）...\n", "批次736: 处理了100条数据，匹配了1条\n", "读取第737批数据（每批100条）...\n", "批次737: 处理了100条数据，匹配了5条\n", "读取第738批数据（每批100条）...\n", "批次738: 处理了100条数据，匹配了0条\n", "读取第739批数据（每批100条）...\n", "批次739: 处理了100条数据，匹配了1条\n", "读取第740批数据（每批100条）...\n", "暂停5秒...\n", "批次740: 处理了100条数据，匹配了4条\n", "临时保存已匹配的2979条数据...\n", "读取第741批数据（每批100条）...\n", "批次741: 处理了100条数据，匹配了1条\n", "读取第742批数据（每批100条）...\n", "批次742: 处理了100条数据，匹配了7条\n", "读取第743批数据（每批100条）...\n", "批次743: 处理了100条数据，匹配了1条\n", "读取第744批数据（每批100条）...\n", "批次744: 处理了100条数据，匹配了2条\n", "读取第745批数据（每批100条）...\n", "批次745: 处理了100条数据，匹配了1条\n", "读取第746批数据（每批100条）...\n", "批次746: 处理了100条数据，匹配了10条\n", "读取第747批数据（每批100条）...\n", "批次747: 处理了100条数据，匹配了3条\n", "读取第748批数据（每批100条）...\n", "批次748: 处理了100条数据，匹配了2条\n", "读取第749批数据（每批100条）...\n", "批次749: 处理了100条数据，匹配了9条\n", "读取第750批数据（每批100条）...\n", "暂停5秒...\n", "批次750: 处理了100条数据，匹配了9条\n", "临时保存已匹配的3024条数据...\n", "读取第751批数据（每批100条）...\n", "批次751: 处理了100条数据，匹配了2条\n", "读取第752批数据（每批100条）...\n", "批次752: 处理了100条数据，匹配了1条\n", "读取第753批数据（每批100条）...\n", "批次753: 处理了100条数据，匹配了4条\n", "读取第754批数据（每批100条）...\n", "批次754: 处理了100条数据，匹配了1条\n", "读取第755批数据（每批100条）...\n", "批次755: 处理了100条数据，匹配了2条\n", "读取第756批数据（每批100条）...\n", "批次756: 处理了100条数据，匹配了1条\n", "读取第757批数据（每批100条）...\n", "批次757: 处理了100条数据，匹配了2条\n", "读取第758批数据（每批100条）...\n", "批次758: 处理了100条数据，匹配了0条\n", "读取第759批数据（每批100条）...\n", "批次759: 处理了100条数据，匹配了4条\n", "读取第760批数据（每批100条）...\n", "暂停5秒...\n", "批次760: 处理了100条数据，匹配了2条\n", "临时保存已匹配的3043条数据...\n", "读取第761批数据（每批100条）...\n", "批次761: 处理了100条数据，匹配了1条\n", "读取第762批数据（每批100条）...\n", "批次762: 处理了100条数据，匹配了2条\n", "读取第763批数据（每批100条）...\n", "批次763: 处理了100条数据，匹配了0条\n", "读取第764批数据（每批100条）...\n", "批次764: 处理了100条数据，匹配了2条\n", "读取第765批数据（每批100条）...\n", "批次765: 处理了100条数据，匹配了1条\n", "读取第766批数据（每批100条）...\n", "批次766: 处理了100条数据，匹配了1条\n", "读取第767批数据（每批100条）...\n", "批次767: 处理了100条数据，匹配了2条\n", "读取第768批数据（每批100条）...\n", "批次768: 处理了100条数据，匹配了4条\n", "读取第769批数据（每批100条）...\n", "批次769: 处理了100条数据，匹配了51条\n", "读取第770批数据（每批100条）...\n", "暂停5秒...\n", "批次770: 处理了100条数据，匹配了1条\n", "临时保存已匹配的3108条数据...\n", "读取第771批数据（每批100条）...\n", "批次771: 处理了100条数据，匹配了0条\n", "读取第772批数据（每批100条）...\n", "批次772: 处理了100条数据，匹配了7条\n", "读取第773批数据（每批100条）...\n", "批次773: 处理了100条数据，匹配了5条\n", "读取第774批数据（每批100条）...\n", "批次774: 处理了100条数据，匹配了0条\n", "读取第775批数据（每批100条）...\n", "批次775: 处理了100条数据，匹配了4条\n", "读取第776批数据（每批100条）...\n", "批次776: 处理了100条数据，匹配了12条\n", "读取第777批数据（每批100条）...\n", "批次777: 处理了100条数据，匹配了7条\n", "读取第778批数据（每批100条）...\n", "批次778: 处理了100条数据，匹配了14条\n", "读取第779批数据（每批100条）...\n", "批次779: 处理了100条数据，匹配了12条\n", "读取第780批数据（每批100条）...\n", "暂停5秒...\n", "批次780: 处理了100条数据，匹配了22条\n", "临时保存已匹配的3191条数据...\n", "读取第781批数据（每批100条）...\n", "批次781: 处理了100条数据，匹配了1条\n", "读取第782批数据（每批100条）...\n", "批次782: 处理了100条数据，匹配了0条\n", "读取第783批数据（每批100条）...\n", "批次783: 处理了100条数据，匹配了0条\n", "读取第784批数据（每批100条）...\n", "批次784: 处理了100条数据，匹配了5条\n", "读取第785批数据（每批100条）...\n", "批次785: 处理了100条数据，匹配了3条\n", "读取第786批数据（每批100条）...\n", "批次786: 处理了100条数据，匹配了6条\n", "读取第787批数据（每批100条）...\n", "批次787: 处理了100条数据，匹配了2条\n", "读取第788批数据（每批100条）...\n", "批次788: 处理了100条数据，匹配了0条\n", "读取第789批数据（每批100条）...\n", "批次789: 处理了100条数据，匹配了5条\n", "读取第790批数据（每批100条）...\n", "暂停5秒...\n", "批次790: 处理了100条数据，匹配了6条\n", "临时保存已匹配的3219条数据...\n", "读取第791批数据（每批100条）...\n", "批次791: 处理了100条数据，匹配了0条\n", "读取第792批数据（每批100条）...\n", "批次792: 处理了100条数据，匹配了2条\n", "读取第793批数据（每批100条）...\n", "批次793: 处理了100条数据，匹配了9条\n", "读取第794批数据（每批100条）...\n", "批次794: 处理了100条数据，匹配了5条\n", "读取第795批数据（每批100条）...\n", "批次795: 处理了100条数据，匹配了1条\n", "读取第796批数据（每批100条）...\n", "批次796: 处理了100条数据，匹配了4条\n", "读取第797批数据（每批100条）...\n", "批次797: 处理了100条数据，匹配了6条\n", "读取第798批数据（每批100条）...\n", "批次798: 处理了100条数据，匹配了13条\n", "读取第799批数据（每批100条）...\n", "批次799: 处理了100条数据，匹配了6条\n", "读取第800批数据（每批100条）...\n", "暂停5秒...\n", "批次800: 处理了100条数据，匹配了4条\n", "临时保存已匹配的3269条数据...\n", "读取第801批数据（每批100条）...\n", "批次801: 处理了100条数据，匹配了4条\n", "读取第802批数据（每批100条）...\n", "批次802: 处理了100条数据，匹配了5条\n", "读取第803批数据（每批100条）...\n", "批次803: 处理了100条数据，匹配了4条\n", "读取第804批数据（每批100条）...\n", "批次804: 处理了100条数据，匹配了0条\n", "读取第805批数据（每批100条）...\n", "批次805: 处理了100条数据，匹配了3条\n", "读取第806批数据（每批100条）...\n", "批次806: 处理了100条数据，匹配了1条\n", "读取第807批数据（每批100条）...\n", "批次807: 处理了100条数据，匹配了3条\n", "读取第808批数据（每批100条）...\n", "批次808: 处理了100条数据，匹配了1条\n", "读取第809批数据（每批100条）...\n", "批次809: 处理了100条数据，匹配了1条\n", "读取第810批数据（每批100条）...\n", "暂停5秒...\n", "批次810: 处理了100条数据，匹配了2条\n", "临时保存已匹配的3293条数据...\n", "读取第811批数据（每批100条）...\n", "批次811: 处理了100条数据，匹配了2条\n", "读取第812批数据（每批100条）...\n", "批次812: 处理了100条数据，匹配了5条\n", "读取第813批数据（每批100条）...\n", "批次813: 处理了100条数据，匹配了12条\n", "读取第814批数据（每批100条）...\n", "批次814: 处理了100条数据，匹配了4条\n", "读取第815批数据（每批100条）...\n", "批次815: 处理了100条数据，匹配了43条\n", "读取第816批数据（每批100条）...\n", "批次816: 处理了100条数据，匹配了4条\n", "读取第817批数据（每批100条）...\n", "批次817: 处理了100条数据，匹配了1条\n", "读取第818批数据（每批100条）...\n", "批次818: 处理了100条数据，匹配了3条\n", "读取第819批数据（每批100条）...\n", "批次819: 处理了100条数据，匹配了4条\n", "读取第820批数据（每批100条）...\n", "暂停5秒...\n", "批次820: 处理了100条数据，匹配了6条\n", "临时保存已匹配的3377条数据...\n", "读取第821批数据（每批100条）...\n", "批次821: 处理了100条数据，匹配了1条\n", "读取第822批数据（每批100条）...\n", "批次822: 处理了100条数据，匹配了3条\n", "读取第823批数据（每批100条）...\n", "批次823: 处理了100条数据，匹配了9条\n", "读取第824批数据（每批100条）...\n", "批次824: 处理了100条数据，匹配了1条\n", "读取第825批数据（每批100条）...\n", "批次825: 处理了100条数据，匹配了2条\n", "读取第826批数据（每批100条）...\n", "批次826: 处理了100条数据，匹配了0条\n", "读取第827批数据（每批100条）...\n", "批次827: 处理了100条数据，匹配了2条\n", "读取第828批数据（每批100条）...\n", "批次828: 处理了100条数据，匹配了0条\n", "读取第829批数据（每批100条）...\n", "批次829: 处理了100条数据，匹配了2条\n", "读取第830批数据（每批100条）...\n", "暂停5秒...\n", "批次830: 处理了100条数据，匹配了16条\n", "临时保存已匹配的3413条数据...\n", "读取第831批数据（每批100条）...\n", "批次831: 处理了100条数据，匹配了23条\n", "读取第832批数据（每批100条）...\n", "批次832: 处理了100条数据，匹配了9条\n", "读取第833批数据（每批100条）...\n", "批次833: 处理了100条数据，匹配了3条\n", "读取第834批数据（每批100条）...\n", "批次834: 处理了100条数据，匹配了1条\n", "读取第835批数据（每批100条）...\n", "批次835: 处理了100条数据，匹配了1条\n", "读取第836批数据（每批100条）...\n", "批次836: 处理了100条数据，匹配了17条\n", "读取第837批数据（每批100条）...\n", "批次837: 处理了100条数据，匹配了8条\n", "读取第838批数据（每批100条）...\n", "批次838: 处理了100条数据，匹配了5条\n", "读取第839批数据（每批100条）...\n", "批次839: 处理了100条数据，匹配了0条\n", "读取第840批数据（每批100条）...\n", "暂停5秒...\n", "批次840: 处理了100条数据，匹配了5条\n", "临时保存已匹配的3485条数据...\n", "读取第841批数据（每批100条）...\n", "批次841: 处理了100条数据，匹配了0条\n", "读取第842批数据（每批100条）...\n", "批次842: 处理了100条数据，匹配了0条\n", "读取第843批数据（每批100条）...\n", "批次843: 处理了100条数据，匹配了0条\n", "读取第844批数据（每批100条）...\n", "批次844: 处理了100条数据，匹配了0条\n", "读取第845批数据（每批100条）...\n", "批次845: 处理了100条数据，匹配了3条\n", "读取第846批数据（每批100条）...\n", "批次846: 处理了100条数据，匹配了1条\n", "读取第847批数据（每批100条）...\n", "批次847: 处理了100条数据，匹配了0条\n", "读取第848批数据（每批100条）...\n", "批次848: 处理了100条数据，匹配了1条\n", "读取第849批数据（每批100条）...\n", "批次849: 处理了100条数据，匹配了3条\n", "读取第850批数据（每批100条）...\n", "暂停5秒...\n", "批次850: 处理了100条数据，匹配了2条\n", "临时保存已匹配的3495条数据...\n", "读取第851批数据（每批100条）...\n", "批次851: 处理了100条数据，匹配了3条\n", "读取第852批数据（每批100条）...\n", "批次852: 处理了100条数据，匹配了0条\n", "读取第853批数据（每批100条）...\n", "批次853: 处理了100条数据，匹配了0条\n", "读取第854批数据（每批100条）...\n", "批次854: 处理了100条数据，匹配了5条\n", "读取第855批数据（每批100条）...\n", "批次855: 处理了100条数据，匹配了3条\n", "读取第856批数据（每批100条）...\n", "批次856: 处理了100条数据，匹配了6条\n", "读取第857批数据（每批100条）...\n", "批次857: 处理了100条数据，匹配了3条\n", "读取第858批数据（每批100条）...\n", "批次858: 处理了100条数据，匹配了1条\n", "读取第859批数据（每批100条）...\n", "批次859: 处理了100条数据，匹配了3条\n", "读取第860批数据（每批100条）...\n", "暂停5秒...\n", "批次860: 处理了100条数据，匹配了1条\n", "临时保存已匹配的3520条数据...\n", "读取第861批数据（每批100条）...\n", "批次861: 处理了100条数据，匹配了0条\n", "读取第862批数据（每批100条）...\n", "批次862: 处理了100条数据，匹配了4条\n", "读取第863批数据（每批100条）...\n", "批次863: 处理了100条数据，匹配了6条\n", "读取第864批数据（每批100条）...\n", "批次864: 处理了100条数据，匹配了1条\n", "读取第865批数据（每批100条）...\n", "批次865: 处理了100条数据，匹配了2条\n", "读取第866批数据（每批100条）...\n", "批次866: 处理了100条数据，匹配了2条\n", "读取第867批数据（每批100条）...\n", "批次867: 处理了100条数据，匹配了7条\n", "读取第868批数据（每批100条）...\n", "批次868: 处理了100条数据，匹配了3条\n", "读取第869批数据（每批100条）...\n", "批次869: 处理了100条数据，匹配了2条\n", "读取第870批数据（每批100条）...\n", "暂停5秒...\n", "批次870: 处理了100条数据，匹配了5条\n", "临时保存已匹配的3552条数据...\n", "读取第871批数据（每批100条）...\n", "批次871: 处理了100条数据，匹配了7条\n", "读取第872批数据（每批100条）...\n", "批次872: 处理了100条数据，匹配了1条\n", "读取第873批数据（每批100条）...\n", "批次873: 处理了100条数据，匹配了5条\n", "读取第874批数据（每批100条）...\n", "批次874: 处理了100条数据，匹配了3条\n", "读取第875批数据（每批100条）...\n", "批次875: 处理了100条数据，匹配了1条\n", "读取第876批数据（每批100条）...\n", "批次876: 处理了100条数据，匹配了2条\n", "读取第877批数据（每批100条）...\n", "批次877: 处理了100条数据，匹配了15条\n", "读取第878批数据（每批100条）...\n", "批次878: 处理了100条数据，匹配了19条\n", "读取第879批数据（每批100条）...\n", "批次879: 处理了100条数据，匹配了7条\n", "读取第880批数据（每批100条）...\n", "暂停5秒...\n", "批次880: 处理了100条数据，匹配了6条\n", "临时保存已匹配的3618条数据...\n", "读取第881批数据（每批100条）...\n", "批次881: 处理了100条数据，匹配了0条\n", "读取第882批数据（每批100条）...\n", "批次882: 处理了100条数据，匹配了2条\n", "读取第883批数据（每批100条）...\n", "批次883: 处理了100条数据，匹配了14条\n", "读取第884批数据（每批100条）...\n", "批次884: 处理了100条数据，匹配了1条\n", "读取第885批数据（每批100条）...\n", "批次885: 处理了100条数据，匹配了8条\n", "读取第886批数据（每批100条）...\n", "批次886: 处理了100条数据，匹配了7条\n", "读取第887批数据（每批100条）...\n", "批次887: 处理了100条数据，匹配了1条\n", "读取第888批数据（每批100条）...\n", "批次888: 处理了100条数据，匹配了1条\n", "读取第889批数据（每批100条）...\n", "批次889: 处理了100条数据，匹配了0条\n", "读取第890批数据（每批100条）...\n", "暂停5秒...\n", "批次890: 处理了100条数据，匹配了3条\n", "临时保存已匹配的3655条数据...\n", "读取第891批数据（每批100条）...\n", "批次891: 处理了100条数据，匹配了6条\n", "读取第892批数据（每批100条）...\n", "批次892: 处理了100条数据，匹配了2条\n", "读取第893批数据（每批100条）...\n", "批次893: 处理了100条数据，匹配了3条\n", "读取第894批数据（每批100条）...\n", "批次894: 处理了100条数据，匹配了2条\n", "读取第895批数据（每批100条）...\n", "批次895: 处理了100条数据，匹配了2条\n", "读取第896批数据（每批100条）...\n", "批次896: 处理了100条数据，匹配了1条\n", "读取第897批数据（每批100条）...\n", "批次897: 处理了100条数据，匹配了2条\n", "读取第898批数据（每批100条）...\n", "批次898: 处理了100条数据，匹配了0条\n", "读取第899批数据（每批100条）...\n", "批次899: 处理了100条数据，匹配了2条\n", "读取第900批数据（每批100条）...\n", "暂停5秒...\n", "批次900: 处理了100条数据，匹配了3条\n", "临时保存已匹配的3678条数据...\n", "读取第901批数据（每批100条）...\n", "批次901: 处理了100条数据，匹配了4条\n", "读取第902批数据（每批100条）...\n", "批次902: 处理了100条数据，匹配了7条\n", "读取第903批数据（每批100条）...\n", "批次903: 处理了100条数据，匹配了10条\n", "读取第904批数据（每批100条）...\n", "批次904: 处理了100条数据，匹配了6条\n", "读取第905批数据（每批100条）...\n", "批次905: 处理了100条数据，匹配了5条\n", "读取第906批数据（每批100条）...\n", "批次906: 处理了100条数据，匹配了1条\n", "读取第907批数据（每批100条）...\n", "批次907: 处理了100条数据，匹配了6条\n", "读取第908批数据（每批100条）...\n", "批次908: 处理了100条数据，匹配了3条\n", "读取第909批数据（每批100条）...\n", "批次909: 处理了100条数据，匹配了0条\n", "读取第910批数据（每批100条）...\n", "暂停5秒...\n", "批次910: 处理了100条数据，匹配了1条\n", "临时保存已匹配的3721条数据...\n", "读取第911批数据（每批100条）...\n", "批次911: 处理了100条数据，匹配了4条\n", "读取第912批数据（每批100条）...\n", "批次912: 处理了100条数据，匹配了4条\n", "读取第913批数据（每批100条）...\n", "批次913: 处理了100条数据，匹配了1条\n", "读取第914批数据（每批100条）...\n", "批次914: 处理了100条数据，匹配了2条\n", "读取第915批数据（每批100条）...\n", "批次915: 处理了100条数据，匹配了6条\n", "读取第916批数据（每批100条）...\n", "批次916: 处理了100条数据，匹配了2条\n", "读取第917批数据（每批100条）...\n", "批次917: 处理了100条数据，匹配了2条\n", "读取第918批数据（每批100条）...\n", "批次918: 处理了100条数据，匹配了6条\n", "读取第919批数据（每批100条）...\n", "批次919: 处理了100条数据，匹配了11条\n", "读取第920批数据（每批100条）...\n", "暂停5秒...\n", "批次920: 处理了100条数据，匹配了7条\n", "临时保存已匹配的3766条数据...\n", "读取第921批数据（每批100条）...\n", "批次921: 处理了100条数据，匹配了4条\n", "读取第922批数据（每批100条）...\n", "批次922: 处理了100条数据，匹配了5条\n", "读取第923批数据（每批100条）...\n", "批次923: 处理了100条数据，匹配了8条\n", "读取第924批数据（每批100条）...\n", "批次924: 处理了100条数据，匹配了4条\n", "读取第925批数据（每批100条）...\n", "批次925: 处理了100条数据，匹配了4条\n", "读取第926批数据（每批100条）...\n", "批次926: 处理了100条数据，匹配了4条\n", "读取第927批数据（每批100条）...\n", "批次927: 处理了100条数据，匹配了4条\n", "读取第928批数据（每批100条）...\n", "批次928: 处理了100条数据，匹配了4条\n", "读取第929批数据（每批100条）...\n", "批次929: 处理了100条数据，匹配了2条\n", "读取第930批数据（每批100条）...\n", "暂停5秒...\n", "批次930: 处理了100条数据，匹配了0条\n", "临时保存已匹配的3805条数据...\n", "读取第931批数据（每批100条）...\n", "批次931: 处理了100条数据，匹配了6条\n", "读取第932批数据（每批100条）...\n", "批次932: 处理了100条数据，匹配了5条\n", "读取第933批数据（每批100条）...\n", "批次933: 处理了100条数据，匹配了2条\n", "读取第934批数据（每批100条）...\n", "批次934: 处理了100条数据，匹配了0条\n", "读取第935批数据（每批100条）...\n", "批次935: 处理了100条数据，匹配了0条\n", "读取第936批数据（每批100条）...\n", "批次936: 处理了100条数据，匹配了1条\n", "读取第937批数据（每批100条）...\n", "批次937: 处理了100条数据，匹配了3条\n", "读取第938批数据（每批100条）...\n", "批次938: 处理了100条数据，匹配了2条\n", "读取第939批数据（每批100条）...\n", "批次939: 处理了100条数据，匹配了10条\n", "读取第940批数据（每批100条）...\n", "暂停5秒...\n", "批次940: 处理了100条数据，匹配了2条\n", "临时保存已匹配的3836条数据...\n", "读取第941批数据（每批100条）...\n", "批次941: 处理了100条数据，匹配了6条\n", "读取第942批数据（每批100条）...\n", "批次942: 处理了100条数据，匹配了0条\n", "读取第943批数据（每批100条）...\n", "批次943: 处理了100条数据，匹配了4条\n", "读取第944批数据（每批100条）...\n", "批次944: 处理了100条数据，匹配了11条\n", "读取第945批数据（每批100条）...\n", "批次945: 处理了100条数据，匹配了5条\n", "读取第946批数据（每批100条）...\n", "批次946: 处理了100条数据，匹配了8条\n", "读取第947批数据（每批100条）...\n", "批次947: 处理了100条数据，匹配了5条\n", "读取第948批数据（每批100条）...\n", "批次948: 处理了100条数据，匹配了0条\n", "读取第949批数据（每批100条）...\n", "批次949: 处理了100条数据，匹配了8条\n", "读取第950批数据（每批100条）...\n", "暂停5秒...\n", "批次950: 处理了100条数据，匹配了2条\n", "临时保存已匹配的3885条数据...\n", "读取第951批数据（每批100条）...\n", "批次951: 处理了100条数据，匹配了3条\n", "读取第952批数据（每批100条）...\n", "批次952: 处理了100条数据，匹配了6条\n", "读取第953批数据（每批100条）...\n", "批次953: 处理了100条数据，匹配了12条\n", "读取第954批数据（每批100条）...\n", "批次954: 处理了100条数据，匹配了3条\n", "读取第955批数据（每批100条）...\n", "批次955: 处理了100条数据，匹配了2条\n", "读取第956批数据（每批100条）...\n", "批次956: 处理了100条数据，匹配了1条\n", "读取第957批数据（每批100条）...\n", "批次957: 处理了100条数据，匹配了0条\n", "读取第958批数据（每批100条）...\n", "批次958: 处理了100条数据，匹配了8条\n", "读取第959批数据（每批100条）...\n", "批次959: 处理了100条数据，匹配了2条\n", "读取第960批数据（每批100条）...\n", "暂停5秒...\n", "批次960: 处理了100条数据，匹配了8条\n", "临时保存已匹配的3930条数据...\n", "读取第961批数据（每批100条）...\n", "批次961: 处理了100条数据，匹配了2条\n", "读取第962批数据（每批100条）...\n", "批次962: 处理了100条数据，匹配了0条\n", "读取第963批数据（每批100条）...\n", "批次963: 处理了100条数据，匹配了9条\n", "读取第964批数据（每批100条）...\n", "批次964: 处理了100条数据，匹配了1条\n", "读取第965批数据（每批100条）...\n", "批次965: 处理了100条数据，匹配了0条\n", "读取第966批数据（每批100条）...\n", "批次966: 处理了100条数据，匹配了1条\n", "读取第967批数据（每批100条）...\n", "批次967: 处理了100条数据，匹配了1条\n", "读取第968批数据（每批100条）...\n", "批次968: 处理了100条数据，匹配了4条\n", "读取第969批数据（每批100条）...\n", "批次969: 处理了100条数据，匹配了1条\n", "读取第970批数据（每批100条）...\n", "暂停5秒...\n", "批次970: 处理了100条数据，匹配了2条\n", "临时保存已匹配的3951条数据...\n", "读取第971批数据（每批100条）...\n", "批次971: 处理了100条数据，匹配了2条\n", "读取第972批数据（每批100条）...\n", "批次972: 处理了100条数据，匹配了8条\n", "读取第973批数据（每批100条）...\n", "批次973: 处理了100条数据，匹配了15条\n", "读取第974批数据（每批100条）...\n", "批次974: 处理了100条数据，匹配了6条\n", "读取第975批数据（每批100条）...\n", "批次975: 处理了100条数据，匹配了8条\n", "读取第976批数据（每批100条）...\n", "批次976: 处理了100条数据，匹配了1条\n", "读取第977批数据（每批100条）...\n", "批次977: 处理了100条数据，匹配了0条\n", "读取第978批数据（每批100条）...\n", "批次978: 处理了100条数据，匹配了7条\n", "读取第979批数据（每批100条）...\n", "批次979: 处理了100条数据，匹配了2条\n", "读取第980批数据（每批100条）...\n", "暂停5秒...\n", "批次980: 处理了100条数据，匹配了4条\n", "临时保存已匹配的4004条数据...\n", "读取第981批数据（每批100条）...\n", "批次981: 处理了100条数据，匹配了3条\n", "读取第982批数据（每批100条）...\n", "批次982: 处理了100条数据，匹配了0条\n", "读取第983批数据（每批100条）...\n", "批次983: 处理了100条数据，匹配了0条\n", "读取第984批数据（每批100条）...\n", "批次984: 处理了100条数据，匹配了14条\n", "读取第985批数据（每批100条）...\n", "批次985: 处理了100条数据，匹配了3条\n", "读取第986批数据（每批100条）...\n", "批次986: 处理了100条数据，匹配了4条\n", "读取第987批数据（每批100条）...\n", "批次987: 处理了100条数据，匹配了2条\n", "读取第988批数据（每批100条）...\n", "批次988: 处理了100条数据，匹配了4条\n", "读取第989批数据（每批100条）...\n", "批次989: 处理了100条数据，匹配了2条\n", "读取第990批数据（每批100条）...\n", "暂停5秒...\n", "批次990: 处理了100条数据，匹配了4条\n", "临时保存已匹配的4040条数据...\n", "读取第991批数据（每批100条）...\n", "批次991: 处理了100条数据，匹配了7条\n", "读取第992批数据（每批100条）...\n", "批次992: 处理了100条数据，匹配了25条\n", "读取第993批数据（每批100条）...\n", "批次993: 处理了100条数据，匹配了8条\n", "读取第994批数据（每批100条）...\n", "批次994: 处理了100条数据，匹配了1条\n", "读取第995批数据（每批100条）...\n", "批次995: 处理了100条数据，匹配了2条\n", "读取第996批数据（每批100条）...\n", "批次996: 处理了100条数据，匹配了0条\n", "读取第997批数据（每批100条）...\n", "批次997: 处理了100条数据，匹配了4条\n", "读取第998批数据（每批100条）...\n", "批次998: 处理了100条数据，匹配了0条\n", "读取第999批数据（每批100条）...\n", "批次999: 处理了100条数据，匹配了7条\n", "读取第1000批数据（每批100条）...\n", "暂停5秒...\n", "批次1000: 处理了100条数据，匹配了15条\n", "临时保存已匹配的4109条数据...\n", "读取第1001批数据（每批100条）...\n", "批次1001: 处理了100条数据，匹配了8条\n", "读取第1002批数据（每批100条）...\n", "批次1002: 处理了100条数据，匹配了1条\n", "读取第1003批数据（每批100条）...\n", "批次1003: 处理了100条数据，匹配了5条\n", "读取第1004批数据（每批100条）...\n", "批次1004: 处理了100条数据，匹配了9条\n", "读取第1005批数据（每批100条）...\n", "批次1005: 处理了100条数据，匹配了1条\n", "读取第1006批数据（每批100条）...\n", "批次1006: 处理了100条数据，匹配了2条\n", "读取第1007批数据（每批100条）...\n", "批次1007: 处理了100条数据，匹配了3条\n", "读取第1008批数据（每批100条）...\n", "批次1008: 处理了100条数据，匹配了2条\n", "读取第1009批数据（每批100条）...\n", "批次1009: 处理了100条数据，匹配了10条\n", "读取第1010批数据（每批100条）...\n", "暂停5秒...\n", "批次1010: 处理了100条数据，匹配了6条\n", "临时保存已匹配的4156条数据...\n", "读取第1011批数据（每批100条）...\n", "批次1011: 处理了100条数据，匹配了4条\n", "读取第1012批数据（每批100条）...\n", "批次1012: 处理了100条数据，匹配了12条\n", "读取第1013批数据（每批100条）...\n", "批次1013: 处理了100条数据，匹配了10条\n", "读取第1014批数据（每批100条）...\n", "批次1014: 处理了100条数据，匹配了4条\n", "读取第1015批数据（每批100条）...\n", "批次1015: 处理了100条数据，匹配了4条\n", "读取第1016批数据（每批100条）...\n", "批次1016: 处理了100条数据，匹配了13条\n", "读取第1017批数据（每批100条）...\n", "批次1017: 处理了100条数据，匹配了2条\n", "读取第1018批数据（每批100条）...\n", "批次1018: 处理了100条数据，匹配了4条\n", "读取第1019批数据（每批100条）...\n", "批次1019: 处理了100条数据，匹配了0条\n", "读取第1020批数据（每批100条）...\n", "暂停5秒...\n", "批次1020: 处理了100条数据，匹配了1条\n", "临时保存已匹配的4210条数据...\n", "读取第1021批数据（每批100条）...\n", "批次1021: 处理了100条数据，匹配了3条\n", "读取第1022批数据（每批100条）...\n", "批次1022: 处理了100条数据，匹配了2条\n", "读取第1023批数据（每批100条）...\n", "批次1023: 处理了100条数据，匹配了7条\n", "读取第1024批数据（每批100条）...\n", "批次1024: 处理了100条数据，匹配了2条\n", "读取第1025批数据（每批100条）...\n", "批次1025: 处理了100条数据，匹配了4条\n", "读取第1026批数据（每批100条）...\n", "批次1026: 处理了100条数据，匹配了12条\n", "读取第1027批数据（每批100条）...\n", "批次1027: 处理了100条数据，匹配了5条\n", "读取第1028批数据（每批100条）...\n", "批次1028: 处理了100条数据，匹配了4条\n", "读取第1029批数据（每批100条）...\n", "批次1029: 处理了100条数据，匹配了12条\n", "读取第1030批数据（每批100条）...\n", "暂停5秒...\n", "批次1030: 处理了100条数据，匹配了6条\n", "临时保存已匹配的4267条数据...\n", "读取第1031批数据（每批100条）...\n", "批次1031: 处理了100条数据，匹配了5条\n", "读取第1032批数据（每批100条）...\n", "批次1032: 处理了100条数据，匹配了5条\n", "读取第1033批数据（每批100条）...\n", "批次1033: 处理了100条数据，匹配了23条\n", "读取第1034批数据（每批100条）...\n", "批次1034: 处理了100条数据，匹配了3条\n", "读取第1035批数据（每批100条）...\n", "批次1035: 处理了100条数据，匹配了1条\n", "读取第1036批数据（每批100条）...\n", "批次1036: 处理了100条数据，匹配了2条\n", "读取第1037批数据（每批100条）...\n", "批次1037: 处理了100条数据，匹配了0条\n", "读取第1038批数据（每批100条）...\n", "批次1038: 处理了100条数据，匹配了8条\n", "读取第1039批数据（每批100条）...\n", "批次1039: 处理了100条数据，匹配了2条\n", "读取第1040批数据（每批100条）...\n", "暂停5秒...\n", "批次1040: 处理了100条数据，匹配了5条\n", "临时保存已匹配的4321条数据...\n", "读取第1041批数据（每批100条）...\n", "批次1041: 处理了100条数据，匹配了3条\n", "读取第1042批数据（每批100条）...\n", "批次1042: 处理了100条数据，匹配了6条\n", "读取第1043批数据（每批100条）...\n", "批次1043: 处理了100条数据，匹配了3条\n", "读取第1044批数据（每批100条）...\n", "批次1044: 处理了100条数据，匹配了3条\n", "读取第1045批数据（每批100条）...\n", "批次1045: 处理了100条数据，匹配了1条\n", "读取第1046批数据（每批100条）...\n", "批次1046: 处理了100条数据，匹配了3条\n", "读取第1047批数据（每批100条）...\n", "批次1047: 处理了100条数据，匹配了4条\n", "读取第1048批数据（每批100条）...\n", "批次1048: 处理了100条数据，匹配了1条\n", "读取第1049批数据（每批100条）...\n", "批次1049: 处理了100条数据，匹配了6条\n", "读取第1050批数据（每批100条）...\n", "暂停5秒...\n", "批次1050: 处理了100条数据，匹配了10条\n", "临时保存已匹配的4361条数据...\n", "读取第1051批数据（每批100条）...\n", "批次1051: 处理了100条数据，匹配了6条\n", "读取第1052批数据（每批100条）...\n", "批次1052: 处理了100条数据，匹配了3条\n", "读取第1053批数据（每批100条）...\n", "批次1053: 处理了100条数据，匹配了6条\n", "读取第1054批数据（每批100条）...\n", "批次1054: 处理了100条数据，匹配了9条\n", "读取第1055批数据（每批100条）...\n", "批次1055: 处理了100条数据，匹配了3条\n", "读取第1056批数据（每批100条）...\n", "批次1056: 处理了100条数据，匹配了3条\n", "读取第1057批数据（每批100条）...\n", "批次1057: 处理了100条数据，匹配了2条\n", "读取第1058批数据（每批100条）...\n", "批次1058: 处理了100条数据，匹配了6条\n", "读取第1059批数据（每批100条）...\n", "批次1059: 处理了100条数据，匹配了4条\n", "读取第1060批数据（每批100条）...\n", "暂停5秒...\n", "批次1060: 处理了100条数据，匹配了10条\n", "临时保存已匹配的4413条数据...\n", "读取第1061批数据（每批100条）...\n", "批次1061: 处理了100条数据，匹配了1条\n", "读取第1062批数据（每批100条）...\n", "批次1062: 处理了100条数据，匹配了9条\n", "读取第1063批数据（每批100条）...\n", "批次1063: 处理了100条数据，匹配了12条\n", "读取第1064批数据（每批100条）...\n", "批次1064: 处理了100条数据，匹配了7条\n", "读取第1065批数据（每批100条）...\n", "批次1065: 处理了100条数据，匹配了5条\n", "读取第1066批数据（每批100条）...\n", "批次1066: 处理了100条数据，匹配了3条\n", "读取第1067批数据（每批100条）...\n", "批次1067: 处理了100条数据，匹配了5条\n", "读取第1068批数据（每批100条）...\n", "批次1068: 处理了100条数据，匹配了1条\n", "读取第1069批数据（每批100条）...\n", "批次1069: 处理了100条数据，匹配了4条\n", "读取第1070批数据（每批100条）...\n", "暂停5秒...\n", "批次1070: 处理了100条数据，匹配了1条\n", "临时保存已匹配的4461条数据...\n", "读取第1071批数据（每批100条）...\n", "批次1071: 处理了100条数据，匹配了3条\n", "读取第1072批数据（每批100条）...\n", "批次1072: 处理了100条数据，匹配了4条\n", "读取第1073批数据（每批100条）...\n", "批次1073: 处理了100条数据，匹配了7条\n", "读取第1074批数据（每批100条）...\n", "批次1074: 处理了100条数据，匹配了0条\n", "读取第1075批数据（每批100条）...\n", "批次1075: 处理了100条数据，匹配了2条\n", "读取第1076批数据（每批100条）...\n", "批次1076: 处理了100条数据，匹配了4条\n", "读取第1077批数据（每批100条）...\n", "批次1077: 处理了100条数据，匹配了6条\n", "读取第1078批数据（每批100条）...\n", "批次1078: 处理了100条数据，匹配了1条\n", "读取第1079批数据（每批100条）...\n", "批次1079: 处理了100条数据，匹配了10条\n", "读取第1080批数据（每批100条）...\n", "暂停5秒...\n", "批次1080: 处理了100条数据，匹配了2条\n", "临时保存已匹配的4500条数据...\n", "读取第1081批数据（每批100条）...\n", "批次1081: 处理了100条数据，匹配了0条\n", "读取第1082批数据（每批100条）...\n", "批次1082: 处理了100条数据，匹配了12条\n", "读取第1083批数据（每批100条）...\n", "批次1083: 处理了100条数据，匹配了1条\n", "读取第1084批数据（每批100条）...\n", "批次1084: 处理了100条数据，匹配了4条\n", "读取第1085批数据（每批100条）...\n", "批次1085: 处理了100条数据，匹配了6条\n", "读取第1086批数据（每批100条）...\n", "批次1086: 处理了100条数据，匹配了1条\n", "读取第1087批数据（每批100条）...\n", "批次1087: 处理了100条数据，匹配了2条\n", "读取第1088批数据（每批100条）...\n", "批次1088: 处理了100条数据，匹配了15条\n", "读取第1089批数据（每批100条）...\n", "批次1089: 处理了100条数据，匹配了6条\n", "读取第1090批数据（每批100条）...\n", "暂停5秒...\n", "批次1090: 处理了100条数据，匹配了1条\n", "临时保存已匹配的4548条数据...\n", "读取第1091批数据（每批100条）...\n", "批次1091: 处理了100条数据，匹配了1条\n", "读取第1092批数据（每批100条）...\n", "批次1092: 处理了100条数据，匹配了2条\n", "读取第1093批数据（每批100条）...\n", "批次1093: 处理了100条数据，匹配了7条\n", "读取第1094批数据（每批100条）...\n", "批次1094: 处理了100条数据，匹配了1条\n", "读取第1095批数据（每批100条）...\n", "批次1095: 处理了100条数据，匹配了4条\n", "读取第1096批数据（每批100条）...\n", "批次1096: 处理了100条数据，匹配了4条\n", "读取第1097批数据（每批100条）...\n", "批次1097: 处理了100条数据，匹配了4条\n", "读取第1098批数据（每批100条）...\n", "批次1098: 处理了100条数据，匹配了0条\n", "读取第1099批数据（每批100条）...\n", "批次1099: 处理了100条数据，匹配了3条\n", "读取第1100批数据（每批100条）...\n", "暂停5秒...\n", "批次1100: 处理了100条数据，匹配了2条\n", "临时保存已匹配的4576条数据...\n", "读取第1101批数据（每批100条）...\n", "批次1101: 处理了100条数据，匹配了1条\n", "读取第1102批数据（每批100条）...\n", "批次1102: 处理了100条数据，匹配了7条\n", "读取第1103批数据（每批100条）...\n", "批次1103: 处理了100条数据，匹配了6条\n", "读取第1104批数据（每批100条）...\n", "批次1104: 处理了100条数据，匹配了3条\n", "读取第1105批数据（每批100条）...\n", "批次1105: 处理了100条数据，匹配了7条\n", "读取第1106批数据（每批100条）...\n", "批次1106: 处理了100条数据，匹配了5条\n", "读取第1107批数据（每批100条）...\n", "批次1107: 处理了100条数据，匹配了10条\n", "读取第1108批数据（每批100条）...\n", "批次1108: 处理了100条数据，匹配了3条\n", "读取第1109批数据（每批100条）...\n", "批次1109: 处理了100条数据，匹配了4条\n", "读取第1110批数据（每批100条）...\n", "暂停5秒...\n", "批次1110: 处理了100条数据，匹配了2条\n", "临时保存已匹配的4624条数据...\n", "读取第1111批数据（每批100条）...\n", "批次1111: 处理了100条数据，匹配了6条\n", "读取第1112批数据（每批100条）...\n", "批次1112: 处理了100条数据，匹配了3条\n", "读取第1113批数据（每批100条）...\n", "批次1113: 处理了100条数据，匹配了1条\n", "读取第1114批数据（每批100条）...\n", "批次1114: 处理了100条数据，匹配了0条\n", "读取第1115批数据（每批100条）...\n", "批次1115: 处理了100条数据，匹配了7条\n", "读取第1116批数据（每批100条）...\n", "批次1116: 处理了100条数据，匹配了5条\n", "读取第1117批数据（每批100条）...\n", "批次1117: 处理了100条数据，匹配了3条\n", "读取第1118批数据（每批100条）...\n", "批次1118: 处理了100条数据，匹配了2条\n", "读取第1119批数据（每批100条）...\n", "批次1119: 处理了100条数据，匹配了2条\n", "读取第1120批数据（每批100条）...\n", "暂停5秒...\n", "批次1120: 处理了100条数据，匹配了0条\n", "临时保存已匹配的4653条数据...\n", "读取第1121批数据（每批100条）...\n", "批次1121: 处理了100条数据，匹配了3条\n", "读取第1122批数据（每批100条）...\n", "批次1122: 处理了100条数据，匹配了5条\n", "读取第1123批数据（每批100条）...\n", "批次1123: 处理了100条数据，匹配了9条\n", "读取第1124批数据（每批100条）...\n", "批次1124: 处理了100条数据，匹配了0条\n", "读取第1125批数据（每批100条）...\n", "批次1125: 处理了100条数据，匹配了0条\n", "读取第1126批数据（每批100条）...\n", "批次1126: 处理了100条数据，匹配了1条\n", "读取第1127批数据（每批100条）...\n", "批次1127: 处理了100条数据，匹配了2条\n", "读取第1128批数据（每批100条）...\n", "批次1128: 处理了100条数据，匹配了3条\n", "读取第1129批数据（每批100条）...\n", "批次1129: 处理了100条数据，匹配了3条\n", "读取第1130批数据（每批100条）...\n", "暂停5秒...\n", "批次1130: 处理了100条数据，匹配了6条\n", "临时保存已匹配的4685条数据...\n", "读取第1131批数据（每批100条）...\n", "批次1131: 处理了100条数据，匹配了4条\n", "读取第1132批数据（每批100条）...\n", "批次1132: 处理了100条数据，匹配了1条\n", "读取第1133批数据（每批100条）...\n", "批次1133: 处理了100条数据，匹配了2条\n", "读取第1134批数据（每批100条）...\n", "批次1134: 处理了100条数据，匹配了2条\n", "读取第1135批数据（每批100条）...\n", "批次1135: 处理了100条数据，匹配了4条\n", "读取第1136批数据（每批100条）...\n", "批次1136: 处理了100条数据，匹配了7条\n", "读取第1137批数据（每批100条）...\n", "批次1137: 处理了100条数据，匹配了1条\n", "读取第1138批数据（每批100条）...\n", "批次1138: 处理了100条数据，匹配了3条\n", "读取第1139批数据（每批100条）...\n", "批次1139: 处理了100条数据，匹配了1条\n", "读取第1140批数据（每批100条）...\n", "暂停5秒...\n", "批次1140: 处理了100条数据，匹配了4条\n", "临时保存已匹配的4714条数据...\n", "读取第1141批数据（每批100条）...\n", "批次1141: 处理了100条数据，匹配了3条\n", "读取第1142批数据（每批100条）...\n", "批次1142: 处理了100条数据，匹配了1条\n", "读取第1143批数据（每批100条）...\n", "批次1143: 处理了100条数据，匹配了15条\n", "读取第1144批数据（每批100条）...\n", "批次1144: 处理了100条数据，匹配了9条\n", "读取第1145批数据（每批100条）...\n", "批次1145: 处理了100条数据，匹配了7条\n", "读取第1146批数据（每批100条）...\n", "批次1146: 处理了100条数据，匹配了0条\n", "读取第1147批数据（每批100条）...\n", "批次1147: 处理了100条数据，匹配了13条\n", "读取第1148批数据（每批100条）...\n", "批次1148: 处理了100条数据，匹配了0条\n", "读取第1149批数据（每批100条）...\n", "批次1149: 处理了100条数据，匹配了0条\n", "读取第1150批数据（每批100条）...\n", "暂停5秒...\n", "批次1150: 处理了100条数据，匹配了12条\n", "临时保存已匹配的4774条数据...\n", "读取第1151批数据（每批100条）...\n", "批次1151: 处理了100条数据，匹配了7条\n", "读取第1152批数据（每批100条）...\n", "批次1152: 处理了100条数据，匹配了2条\n", "读取第1153批数据（每批100条）...\n", "批次1153: 处理了100条数据，匹配了1条\n", "读取第1154批数据（每批100条）...\n", "批次1154: 处理了100条数据，匹配了3条\n", "读取第1155批数据（每批100条）...\n", "批次1155: 处理了100条数据，匹配了6条\n", "读取第1156批数据（每批100条）...\n", "批次1156: 处理了100条数据，匹配了6条\n", "读取第1157批数据（每批100条）...\n", "批次1157: 处理了100条数据，匹配了0条\n", "读取第1158批数据（每批100条）...\n", "批次1158: 处理了100条数据，匹配了2条\n", "读取第1159批数据（每批100条）...\n", "批次1159: 处理了100条数据，匹配了2条\n", "读取第1160批数据（每批100条）...\n", "暂停5秒...\n", "批次1160: 处理了100条数据，匹配了7条\n", "临时保存已匹配的4810条数据...\n", "读取第1161批数据（每批100条）...\n", "批次1161: 处理了100条数据，匹配了4条\n", "读取第1162批数据（每批100条）...\n", "批次1162: 处理了100条数据，匹配了4条\n", "读取第1163批数据（每批100条）...\n", "批次1163: 处理了100条数据，匹配了4条\n", "读取第1164批数据（每批100条）...\n", "批次1164: 处理了100条数据，匹配了1条\n", "读取第1165批数据（每批100条）...\n", "批次1165: 处理了100条数据，匹配了9条\n", "读取第1166批数据（每批100条）...\n", "批次1166: 处理了100条数据，匹配了5条\n", "读取第1167批数据（每批100条）...\n", "批次1167: 处理了100条数据，匹配了6条\n", "读取第1168批数据（每批100条）...\n", "批次1168: 处理了100条数据，匹配了3条\n", "读取第1169批数据（每批100条）...\n", "批次1169: 处理了100条数据，匹配了8条\n", "读取第1170批数据（每批100条）...\n", "暂停5秒...\n", "批次1170: 处理了100条数据，匹配了11条\n", "临时保存已匹配的4865条数据...\n", "读取第1171批数据（每批100条）...\n", "批次1171: 处理了100条数据，匹配了8条\n", "读取第1172批数据（每批100条）...\n", "批次1172: 处理了100条数据，匹配了7条\n", "读取第1173批数据（每批100条）...\n", "批次1173: 处理了100条数据，匹配了4条\n", "读取第1174批数据（每批100条）...\n", "批次1174: 处理了100条数据，匹配了3条\n", "读取第1175批数据（每批100条）...\n", "批次1175: 处理了100条数据，匹配了6条\n", "读取第1176批数据（每批100条）...\n", "批次1176: 处理了100条数据，匹配了2条\n", "读取第1177批数据（每批100条）...\n", "批次1177: 处理了100条数据，匹配了1条\n", "读取第1178批数据（每批100条）...\n", "批次1178: 处理了100条数据，匹配了3条\n", "读取第1179批数据（每批100条）...\n", "批次1179: 处理了100条数据，匹配了3条\n", "读取第1180批数据（每批100条）...\n", "暂停5秒...\n", "批次1180: 处理了100条数据，匹配了7条\n", "临时保存已匹配的4909条数据...\n", "读取第1181批数据（每批100条）...\n", "批次1181: 处理了100条数据，匹配了6条\n", "读取第1182批数据（每批100条）...\n", "批次1182: 处理了100条数据，匹配了1条\n", "读取第1183批数据（每批100条）...\n", "批次1183: 处理了100条数据，匹配了3条\n", "读取第1184批数据（每批100条）...\n", "批次1184: 处理了100条数据，匹配了5条\n", "读取第1185批数据（每批100条）...\n", "批次1185: 处理了100条数据，匹配了2条\n", "读取第1186批数据（每批100条）...\n", "批次1186: 处理了100条数据，匹配了5条\n", "读取第1187批数据（每批100条）...\n", "批次1187: 处理了100条数据，匹配了3条\n", "读取第1188批数据（每批100条）...\n", "批次1188: 处理了100条数据，匹配了2条\n", "读取第1189批数据（每批100条）...\n", "批次1189: 处理了100条数据，匹配了1条\n", "读取第1190批数据（每批100条）...\n", "暂停5秒...\n", "批次1190: 处理了100条数据，匹配了1条\n", "临时保存已匹配的4938条数据...\n", "读取第1191批数据（每批100条）...\n", "批次1191: 处理了100条数据，匹配了12条\n", "读取第1192批数据（每批100条）...\n", "批次1192: 处理了100条数据，匹配了6条\n", "读取第1193批数据（每批100条）...\n", "批次1193: 处理了100条数据，匹配了6条\n", "读取第1194批数据（每批100条）...\n", "批次1194: 处理了100条数据，匹配了7条\n", "读取第1195批数据（每批100条）...\n", "批次1195: 处理了100条数据，匹配了5条\n", "读取第1196批数据（每批100条）...\n", "批次1196: 处理了100条数据，匹配了0条\n", "读取第1197批数据（每批100条）...\n", "批次1197: 处理了100条数据，匹配了4条\n", "读取第1198批数据（每批100条）...\n", "批次1198: 处理了100条数据，匹配了4条\n", "读取第1199批数据（每批100条）...\n", "批次1199: 处理了100条数据，匹配了10条\n", "读取第1200批数据（每批100条）...\n", "暂停5秒...\n", "批次1200: 处理了100条数据，匹配了1条\n", "临时保存已匹配的4993条数据...\n", "读取第1201批数据（每批100条）...\n", "批次1201: 处理了100条数据，匹配了2条\n", "读取第1202批数据（每批100条）...\n", "批次1202: 处理了100条数据，匹配了4条\n", "读取第1203批数据（每批100条）...\n", "批次1203: 处理了100条数据，匹配了3条\n", "读取第1204批数据（每批100条）...\n", "批次1204: 处理了100条数据，匹配了1条\n", "读取第1205批数据（每批100条）...\n", "批次1205: 处理了100条数据，匹配了4条\n", "读取第1206批数据（每批100条）...\n", "批次1206: 处理了100条数据，匹配了5条\n", "读取第1207批数据（每批100条）...\n", "批次1207: 处理了100条数据，匹配了1条\n", "读取第1208批数据（每批100条）...\n", "批次1208: 处理了100条数据，匹配了7条\n", "读取第1209批数据（每批100条）...\n", "批次1209: 处理了100条数据，匹配了4条\n", "读取第1210批数据（每批100条）...\n", "暂停5秒...\n", "批次1210: 处理了100条数据，匹配了3条\n", "临时保存已匹配的5027条数据...\n", "读取第1211批数据（每批100条）...\n", "批次1211: 处理了100条数据，匹配了6条\n", "读取第1212批数据（每批100条）...\n", "批次1212: 处理了100条数据，匹配了5条\n", "读取第1213批数据（每批100条）...\n", "批次1213: 处理了100条数据，匹配了13条\n", "读取第1214批数据（每批100条）...\n", "批次1214: 处理了100条数据，匹配了10条\n", "读取第1215批数据（每批100条）...\n", "批次1215: 处理了100条数据，匹配了5条\n", "读取第1216批数据（每批100条）...\n", "批次1216: 处理了100条数据，匹配了2条\n", "读取第1217批数据（每批100条）...\n", "批次1217: 处理了100条数据，匹配了0条\n", "读取第1218批数据（每批100条）...\n", "批次1218: 处理了100条数据，匹配了1条\n", "读取第1219批数据（每批100条）...\n", "批次1219: 处理了100条数据，匹配了0条\n", "读取第1220批数据（每批100条）...\n", "暂停5秒...\n", "批次1220: 处理了100条数据，匹配了4条\n", "临时保存已匹配的5073条数据...\n", "读取第1221批数据（每批100条）...\n", "批次1221: 处理了100条数据，匹配了7条\n", "读取第1222批数据（每批100条）...\n", "批次1222: 处理了100条数据，匹配了13条\n", "读取第1223批数据（每批100条）...\n", "批次1223: 处理了100条数据，匹配了3条\n", "读取第1224批数据（每批100条）...\n", "批次1224: 处理了100条数据，匹配了5条\n", "读取第1225批数据（每批100条）...\n", "批次1225: 处理了100条数据，匹配了3条\n", "读取第1226批数据（每批100条）...\n", "批次1226: 处理了100条数据，匹配了3条\n", "读取第1227批数据（每批100条）...\n", "批次1227: 处理了100条数据，匹配了1条\n", "读取第1228批数据（每批100条）...\n", "批次1228: 处理了100条数据，匹配了1条\n", "读取第1229批数据（每批100条）...\n", "批次1229: 处理了100条数据，匹配了2条\n", "读取第1230批数据（每批100条）...\n", "暂停5秒...\n", "批次1230: 处理了100条数据，匹配了9条\n", "临时保存已匹配的5120条数据...\n", "读取第1231批数据（每批100条）...\n", "批次1231: 处理了100条数据，匹配了12条\n", "读取第1232批数据（每批100条）...\n", "批次1232: 处理了100条数据，匹配了3条\n", "读取第1233批数据（每批100条）...\n", "批次1233: 处理了100条数据，匹配了4条\n", "读取第1234批数据（每批100条）...\n", "批次1234: 处理了100条数据，匹配了5条\n", "读取第1235批数据（每批100条）...\n", "批次1235: 处理了100条数据，匹配了1条\n", "读取第1236批数据（每批100条）...\n", "批次1236: 处理了100条数据，匹配了11条\n", "读取第1237批数据（每批100条）...\n", "批次1237: 处理了100条数据，匹配了4条\n", "读取第1238批数据（每批100条）...\n", "批次1238: 处理了100条数据，匹配了0条\n", "读取第1239批数据（每批100条）...\n", "批次1239: 处理了100条数据，匹配了2条\n", "读取第1240批数据（每批100条）...\n", "暂停5秒...\n", "批次1240: 处理了100条数据，匹配了5条\n", "临时保存已匹配的5167条数据...\n", "读取第1241批数据（每批100条）...\n", "批次1241: 处理了100条数据，匹配了8条\n", "读取第1242批数据（每批100条）...\n", "批次1242: 处理了100条数据，匹配了4条\n", "读取第1243批数据（每批100条）...\n", "批次1243: 处理了100条数据，匹配了2条\n", "读取第1244批数据（每批100条）...\n", "批次1244: 处理了100条数据，匹配了6条\n", "读取第1245批数据（每批100条）...\n", "批次1245: 处理了100条数据，匹配了0条\n", "读取第1246批数据（每批100条）...\n", "批次1246: 处理了100条数据，匹配了4条\n", "读取第1247批数据（每批100条）...\n", "批次1247: 处理了100条数据，匹配了2条\n", "读取第1248批数据（每批100条）...\n", "批次1248: 处理了100条数据，匹配了10条\n", "读取第1249批数据（每批100条）...\n", "批次1249: 处理了100条数据，匹配了4条\n", "读取第1250批数据（每批100条）...\n", "暂停5秒...\n", "批次1250: 处理了100条数据，匹配了7条\n", "临时保存已匹配的5214条数据...\n", "读取第1251批数据（每批100条）...\n", "批次1251: 处理了100条数据，匹配了1条\n", "读取第1252批数据（每批100条）...\n", "批次1252: 处理了100条数据，匹配了10条\n", "读取第1253批数据（每批100条）...\n", "批次1253: 处理了100条数据，匹配了6条\n", "读取第1254批数据（每批100条）...\n", "批次1254: 处理了100条数据，匹配了2条\n", "读取第1255批数据（每批100条）...\n", "批次1255: 处理了100条数据，匹配了2条\n", "读取第1256批数据（每批100条）...\n", "批次1256: 处理了100条数据，匹配了2条\n", "读取第1257批数据（每批100条）...\n", "批次1257: 处理了100条数据，匹配了5条\n", "读取第1258批数据（每批100条）...\n", "批次1258: 处理了100条数据，匹配了1条\n", "读取第1259批数据（每批100条）...\n", "批次1259: 处理了100条数据，匹配了2条\n", "读取第1260批数据（每批100条）...\n", "暂停5秒...\n", "批次1260: 处理了100条数据，匹配了9条\n", "临时保存已匹配的5254条数据...\n", "读取第1261批数据（每批100条）...\n", "批次1261: 处理了100条数据，匹配了1条\n", "读取第1262批数据（每批100条）...\n", "批次1262: 处理了100条数据，匹配了0条\n", "读取第1263批数据（每批100条）...\n", "批次1263: 处理了100条数据，匹配了4条\n", "读取第1264批数据（每批100条）...\n", "批次1264: 处理了100条数据，匹配了4条\n", "读取第1265批数据（每批100条）...\n", "批次1265: 处理了100条数据，匹配了0条\n", "读取第1266批数据（每批100条）...\n", "批次1266: 处理了100条数据，匹配了7条\n", "读取第1267批数据（每批100条）...\n", "批次1267: 处理了100条数据，匹配了5条\n", "读取第1268批数据（每批100条）...\n", "批次1268: 处理了100条数据，匹配了6条\n", "读取第1269批数据（每批100条）...\n", "批次1269: 处理了100条数据，匹配了10条\n", "读取第1270批数据（每批100条）...\n", "暂停5秒...\n", "批次1270: 处理了100条数据，匹配了4条\n", "临时保存已匹配的5295条数据...\n", "读取第1271批数据（每批100条）...\n", "批次1271: 处理了100条数据，匹配了4条\n", "读取第1272批数据（每批100条）...\n", "批次1272: 处理了100条数据，匹配了5条\n", "读取第1273批数据（每批100条）...\n", "批次1273: 处理了100条数据，匹配了3条\n", "读取第1274批数据（每批100条）...\n", "批次1274: 处理了100条数据，匹配了1条\n", "读取第1275批数据（每批100条）...\n", "批次1275: 处理了100条数据，匹配了10条\n", "读取第1276批数据（每批100条）...\n", "批次1276: 处理了100条数据，匹配了5条\n", "读取第1277批数据（每批100条）...\n", "批次1277: 处理了100条数据，匹配了4条\n", "读取第1278批数据（每批100条）...\n", "批次1278: 处理了100条数据，匹配了6条\n", "读取第1279批数据（每批100条）...\n", "批次1279: 处理了100条数据，匹配了2条\n", "读取第1280批数据（每批100条）...\n", "暂停5秒...\n", "批次1280: 处理了100条数据，匹配了9条\n", "临时保存已匹配的5344条数据...\n", "读取第1281批数据（每批100条）...\n", "批次1281: 处理了100条数据，匹配了0条\n", "读取第1282批数据（每批100条）...\n", "批次1282: 处理了100条数据，匹配了2条\n", "读取第1283批数据（每批100条）...\n", "批次1283: 处理了100条数据，匹配了3条\n", "读取第1284批数据（每批100条）...\n", "批次1284: 处理了100条数据，匹配了1条\n", "读取第1285批数据（每批100条）...\n", "批次1285: 处理了100条数据，匹配了2条\n", "读取第1286批数据（每批100条）...\n", "批次1286: 处理了100条数据，匹配了3条\n", "读取第1287批数据（每批100条）...\n", "批次1287: 处理了100条数据，匹配了3条\n", "读取第1288批数据（每批100条）...\n", "批次1288: 处理了100条数据，匹配了1条\n", "读取第1289批数据（每批100条）...\n", "批次1289: 处理了100条数据，匹配了3条\n", "读取第1290批数据（每批100条）...\n", "暂停5秒...\n", "批次1290: 处理了100条数据，匹配了0条\n", "临时保存已匹配的5362条数据...\n", "读取第1291批数据（每批100条）...\n", "批次1291: 处理了100条数据，匹配了5条\n", "读取第1292批数据（每批100条）...\n", "批次1292: 处理了100条数据，匹配了3条\n", "读取第1293批数据（每批100条）...\n", "批次1293: 处理了100条数据，匹配了3条\n", "读取第1294批数据（每批100条）...\n", "批次1294: 处理了100条数据，匹配了0条\n", "读取第1295批数据（每批100条）...\n", "批次1295: 处理了100条数据，匹配了4条\n", "读取第1296批数据（每批100条）...\n", "批次1296: 处理了100条数据，匹配了3条\n", "读取第1297批数据（每批100条）...\n", "批次1297: 处理了100条数据，匹配了3条\n", "读取第1298批数据（每批100条）...\n", "批次1298: 处理了100条数据，匹配了1条\n", "读取第1299批数据（每批100条）...\n", "批次1299: 处理了100条数据，匹配了2条\n", "读取第1300批数据（每批100条）...\n", "暂停5秒...\n", "批次1300: 处理了100条数据，匹配了15条\n", "临时保存已匹配的5401条数据...\n", "读取第1301批数据（每批100条）...\n", "批次1301: 处理了100条数据，匹配了5条\n", "读取第1302批数据（每批100条）...\n", "批次1302: 处理了100条数据，匹配了2条\n", "读取第1303批数据（每批100条）...\n", "批次1303: 处理了100条数据，匹配了0条\n", "读取第1304批数据（每批100条）...\n", "批次1304: 处理了100条数据，匹配了3条\n", "读取第1305批数据（每批100条）...\n", "批次1305: 处理了100条数据，匹配了11条\n", "读取第1306批数据（每批100条）...\n", "批次1306: 处理了100条数据，匹配了6条\n", "读取第1307批数据（每批100条）...\n", "批次1307: 处理了100条数据，匹配了18条\n", "读取第1308批数据（每批100条）...\n", "批次1308: 处理了100条数据，匹配了15条\n", "读取第1309批数据（每批100条）...\n", "批次1309: 处理了100条数据，匹配了12条\n", "读取第1310批数据（每批100条）...\n", "暂停5秒...\n", "批次1310: 处理了100条数据，匹配了1条\n", "临时保存已匹配的5474条数据...\n", "读取第1311批数据（每批100条）...\n", "批次1311: 处理了100条数据，匹配了2条\n", "读取第1312批数据（每批100条）...\n", "批次1312: 处理了100条数据，匹配了5条\n", "读取第1313批数据（每批100条）...\n", "批次1313: 处理了100条数据，匹配了6条\n", "读取第1314批数据（每批100条）...\n", "批次1314: 处理了100条数据，匹配了16条\n", "读取第1315批数据（每批100条）...\n", "批次1315: 处理了100条数据，匹配了8条\n", "读取第1316批数据（每批100条）...\n", "批次1316: 处理了100条数据，匹配了1条\n", "读取第1317批数据（每批100条）...\n", "批次1317: 处理了100条数据，匹配了4条\n", "读取第1318批数据（每批100条）...\n", "批次1318: 处理了100条数据，匹配了5条\n", "读取第1319批数据（每批100条）...\n", "批次1319: 处理了100条数据，匹配了1条\n", "读取第1320批数据（每批100条）...\n", "暂停5秒...\n", "批次1320: 处理了100条数据，匹配了6条\n", "临时保存已匹配的5528条数据...\n", "读取第1321批数据（每批100条）...\n", "批次1321: 处理了100条数据，匹配了4条\n", "读取第1322批数据（每批100条）...\n", "批次1322: 处理了100条数据，匹配了4条\n", "读取第1323批数据（每批100条）...\n", "批次1323: 处理了100条数据，匹配了0条\n", "读取第1324批数据（每批100条）...\n", "批次1324: 处理了100条数据，匹配了0条\n", "读取第1325批数据（每批100条）...\n", "批次1325: 处理了100条数据，匹配了4条\n", "读取第1326批数据（每批100条）...\n", "批次1326: 处理了100条数据，匹配了2条\n", "读取第1327批数据（每批100条）...\n", "批次1327: 处理了100条数据，匹配了1条\n", "读取第1328批数据（每批100条）...\n", "批次1328: 处理了100条数据，匹配了5条\n", "读取第1329批数据（每批100条）...\n", "批次1329: 处理了100条数据，匹配了5条\n", "读取第1330批数据（每批100条）...\n", "暂停5秒...\n", "批次1330: 处理了100条数据，匹配了32条\n", "临时保存已匹配的5585条数据...\n", "读取第1331批数据（每批100条）...\n", "批次1331: 处理了100条数据，匹配了1条\n", "读取第1332批数据（每批100条）...\n", "批次1332: 处理了100条数据，匹配了4条\n", "读取第1333批数据（每批100条）...\n", "批次1333: 处理了100条数据，匹配了1条\n", "读取第1334批数据（每批100条）...\n", "批次1334: 处理了100条数据，匹配了5条\n", "读取第1335批数据（每批100条）...\n", "批次1335: 处理了100条数据，匹配了20条\n", "读取第1336批数据（每批100条）...\n", "批次1336: 处理了100条数据，匹配了3条\n", "读取第1337批数据（每批100条）...\n", "批次1337: 处理了100条数据，匹配了1条\n", "读取第1338批数据（每批100条）...\n", "批次1338: 处理了100条数据，匹配了8条\n", "读取第1339批数据（每批100条）...\n", "批次1339: 处理了100条数据，匹配了5条\n", "读取第1340批数据（每批100条）...\n", "暂停5秒...\n", "批次1340: 处理了100条数据，匹配了3条\n", "临时保存已匹配的5636条数据...\n", "读取第1341批数据（每批100条）...\n", "批次1341: 处理了100条数据，匹配了4条\n", "读取第1342批数据（每批100条）...\n", "批次1342: 处理了100条数据，匹配了3条\n", "读取第1343批数据（每批100条）...\n", "批次1343: 处理了100条数据，匹配了6条\n", "读取第1344批数据（每批100条）...\n", "批次1344: 处理了100条数据，匹配了0条\n", "读取第1345批数据（每批100条）...\n", "批次1345: 处理了100条数据，匹配了2条\n", "读取第1346批数据（每批100条）...\n", "批次1346: 处理了100条数据，匹配了2条\n", "读取第1347批数据（每批100条）...\n", "批次1347: 处理了100条数据，匹配了1条\n", "读取第1348批数据（每批100条）...\n", "批次1348: 处理了100条数据，匹配了3条\n", "读取第1349批数据（每批100条）...\n", "批次1349: 处理了100条数据，匹配了0条\n", "读取第1350批数据（每批100条）...\n", "暂停5秒...\n", "批次1350: 处理了100条数据，匹配了0条\n", "临时保存已匹配的5657条数据...\n", "读取第1351批数据（每批100条）...\n", "批次1351: 处理了100条数据，匹配了0条\n", "读取第1352批数据（每批100条）...\n", "批次1352: 处理了100条数据，匹配了6条\n", "读取第1353批数据（每批100条）...\n", "批次1353: 处理了100条数据，匹配了4条\n", "读取第1354批数据（每批100条）...\n", "批次1354: 处理了100条数据，匹配了3条\n", "读取第1355批数据（每批100条）...\n", "批次1355: 处理了100条数据，匹配了9条\n", "读取第1356批数据（每批100条）...\n", "批次1356: 处理了100条数据，匹配了0条\n", "读取第1357批数据（每批100条）...\n", "批次1357: 处理了100条数据，匹配了3条\n", "读取第1358批数据（每批100条）...\n", "批次1358: 处理了100条数据，匹配了12条\n", "读取第1359批数据（每批100条）...\n", "批次1359: 处理了100条数据，匹配了1条\n", "读取第1360批数据（每批100条）...\n", "暂停5秒...\n", "批次1360: 处理了100条数据，匹配了8条\n", "临时保存已匹配的5703条数据...\n", "读取第1361批数据（每批100条）...\n", "批次1361: 处理了100条数据，匹配了5条\n", "读取第1362批数据（每批100条）...\n", "批次1362: 处理了100条数据，匹配了4条\n", "读取第1363批数据（每批100条）...\n", "批次1363: 处理了100条数据，匹配了6条\n", "读取第1364批数据（每批100条）...\n", "批次1364: 处理了100条数据，匹配了1条\n", "读取第1365批数据（每批100条）...\n", "批次1365: 处理了100条数据，匹配了2条\n", "读取第1366批数据（每批100条）...\n", "批次1366: 处理了100条数据，匹配了1条\n", "读取第1367批数据（每批100条）...\n", "批次1367: 处理了100条数据，匹配了1条\n", "读取第1368批数据（每批100条）...\n", "批次1368: 处理了100条数据，匹配了3条\n", "读取第1369批数据（每批100条）...\n", "批次1369: 处理了100条数据，匹配了1条\n", "读取第1370批数据（每批100条）...\n", "暂停5秒...\n", "批次1370: 处理了100条数据，匹配了0条\n", "临时保存已匹配的5727条数据...\n", "读取第1371批数据（每批100条）...\n", "批次1371: 处理了100条数据，匹配了2条\n", "读取第1372批数据（每批100条）...\n", "批次1372: 处理了100条数据，匹配了0条\n", "读取第1373批数据（每批100条）...\n", "批次1373: 处理了100条数据，匹配了1条\n", "读取第1374批数据（每批100条）...\n", "批次1374: 处理了100条数据，匹配了6条\n", "读取第1375批数据（每批100条）...\n", "批次1375: 处理了100条数据，匹配了5条\n", "读取第1376批数据（每批100条）...\n", "批次1376: 处理了100条数据，匹配了2条\n", "读取第1377批数据（每批100条）...\n", "批次1377: 处理了100条数据，匹配了4条\n", "读取第1378批数据（每批100条）...\n", "批次1378: 处理了100条数据，匹配了1条\n", "读取第1379批数据（每批100条）...\n", "批次1379: 处理了100条数据，匹配了1条\n", "读取第1380批数据（每批100条）...\n", "暂停5秒...\n", "批次1380: 处理了100条数据，匹配了5条\n", "临时保存已匹配的5754条数据...\n", "读取第1381批数据（每批100条）...\n", "批次1381: 处理了100条数据，匹配了7条\n", "读取第1382批数据（每批100条）...\n", "批次1382: 处理了100条数据，匹配了0条\n", "读取第1383批数据（每批100条）...\n", "批次1383: 处理了100条数据，匹配了3条\n", "读取第1384批数据（每批100条）...\n", "批次1384: 处理了100条数据，匹配了2条\n", "读取第1385批数据（每批100条）...\n", "批次1385: 处理了100条数据，匹配了3条\n", "读取第1386批数据（每批100条）...\n", "批次1386: 处理了100条数据，匹配了4条\n", "读取第1387批数据（每批100条）...\n", "批次1387: 处理了100条数据，匹配了2条\n", "读取第1388批数据（每批100条）...\n", "批次1388: 处理了100条数据，匹配了3条\n", "读取第1389批数据（每批100条）...\n", "批次1389: 处理了100条数据，匹配了8条\n", "读取第1390批数据（每批100条）...\n", "暂停5秒...\n", "批次1390: 处理了100条数据，匹配了5条\n", "临时保存已匹配的5791条数据...\n", "读取第1391批数据（每批100条）...\n", "批次1391: 处理了100条数据，匹配了6条\n", "读取第1392批数据（每批100条）...\n", "批次1392: 处理了100条数据，匹配了3条\n", "读取第1393批数据（每批100条）...\n", "批次1393: 处理了100条数据，匹配了5条\n", "读取第1394批数据（每批100条）...\n", "批次1394: 处理了100条数据，匹配了1条\n", "读取第1395批数据（每批100条）...\n", "批次1395: 处理了100条数据，匹配了0条\n", "读取第1396批数据（每批100条）...\n", "批次1396: 处理了100条数据，匹配了37条\n", "读取第1397批数据（每批100条）...\n", "批次1397: 处理了100条数据，匹配了5条\n", "读取第1398批数据（每批100条）...\n", "批次1398: 处理了100条数据，匹配了3条\n", "读取第1399批数据（每批100条）...\n", "批次1399: 处理了100条数据，匹配了4条\n", "读取第1400批数据（每批100条）...\n", "暂停5秒...\n", "批次1400: 处理了100条数据，匹配了29条\n", "临时保存已匹配的5884条数据...\n", "读取第1401批数据（每批100条）...\n", "批次1401: 处理了100条数据，匹配了13条\n", "读取第1402批数据（每批100条）...\n", "批次1402: 处理了100条数据，匹配了12条\n", "读取第1403批数据（每批100条）...\n", "批次1403: 处理了100条数据，匹配了6条\n", "读取第1404批数据（每批100条）...\n", "批次1404: 处理了100条数据，匹配了23条\n", "读取第1405批数据（每批100条）...\n", "批次1405: 处理了100条数据，匹配了7条\n", "读取第1406批数据（每批100条）...\n", "批次1406: 处理了100条数据，匹配了4条\n", "读取第1407批数据（每批100条）...\n", "批次1407: 处理了100条数据，匹配了2条\n", "读取第1408批数据（每批100条）...\n", "批次1408: 处理了100条数据，匹配了0条\n", "读取第1409批数据（每批100条）...\n", "批次1409: 处理了100条数据，匹配了3条\n", "读取第1410批数据（每批100条）...\n", "暂停5秒...\n", "批次1410: 处理了100条数据，匹配了2条\n", "临时保存已匹配的5956条数据...\n", "读取第1411批数据（每批100条）...\n", "批次1411: 处理了100条数据，匹配了7条\n", "读取第1412批数据（每批100条）...\n", "批次1412: 处理了100条数据，匹配了4条\n", "读取第1413批数据（每批100条）...\n", "批次1413: 处理了100条数据，匹配了10条\n", "读取第1414批数据（每批100条）...\n", "批次1414: 处理了100条数据，匹配了4条\n", "读取第1415批数据（每批100条）...\n", "批次1415: 处理了100条数据，匹配了7条\n", "读取第1416批数据（每批100条）...\n", "批次1416: 处理了100条数据，匹配了1条\n", "读取第1417批数据（每批100条）...\n", "批次1417: 处理了100条数据，匹配了0条\n", "读取第1418批数据（每批100条）...\n", "批次1418: 处理了100条数据，匹配了2条\n", "读取第1419批数据（每批100条）...\n", "批次1419: 处理了100条数据，匹配了1条\n", "读取第1420批数据（每批100条）...\n", "暂停5秒...\n", "批次1420: 处理了100条数据，匹配了1条\n", "临时保存已匹配的5993条数据...\n", "读取第1421批数据（每批100条）...\n", "批次1421: 处理了100条数据，匹配了3条\n", "读取第1422批数据（每批100条）...\n", "批次1422: 处理了100条数据，匹配了2条\n", "读取第1423批数据（每批100条）...\n", "批次1423: 处理了100条数据，匹配了8条\n", "读取第1424批数据（每批100条）...\n", "批次1424: 处理了100条数据，匹配了3条\n", "读取第1425批数据（每批100条）...\n", "批次1425: 处理了100条数据，匹配了18条\n", "读取第1426批数据（每批100条）...\n", "批次1426: 处理了100条数据，匹配了3条\n", "读取第1427批数据（每批100条）...\n", "批次1427: 处理了100条数据，匹配了2条\n", "读取第1428批数据（每批100条）...\n", "批次1428: 处理了100条数据，匹配了1条\n", "读取第1429批数据（每批100条）...\n", "批次1429: 处理了100条数据，匹配了3条\n", "读取第1430批数据（每批100条）...\n", "暂停5秒...\n", "批次1430: 处理了100条数据，匹配了1条\n", "临时保存已匹配的6037条数据...\n", "读取第1431批数据（每批100条）...\n", "批次1431: 处理了100条数据，匹配了2条\n", "读取第1432批数据（每批100条）...\n", "批次1432: 处理了100条数据，匹配了14条\n", "读取第1433批数据（每批100条）...\n", "批次1433: 处理了100条数据，匹配了6条\n", "读取第1434批数据（每批100条）...\n", "批次1434: 处理了100条数据，匹配了1条\n", "读取第1435批数据（每批100条）...\n", "批次1435: 处理了100条数据，匹配了3条\n", "读取第1436批数据（每批100条）...\n", "批次1436: 处理了100条数据，匹配了5条\n", "读取第1437批数据（每批100条）...\n", "批次1437: 处理了100条数据，匹配了4条\n", "读取第1438批数据（每批100条）...\n", "批次1438: 处理了100条数据，匹配了6条\n", "读取第1439批数据（每批100条）...\n", "批次1439: 处理了100条数据，匹配了2条\n", "读取第1440批数据（每批100条）...\n", "暂停5秒...\n", "批次1440: 处理了100条数据，匹配了5条\n", "临时保存已匹配的6085条数据...\n", "读取第1441批数据（每批100条）...\n", "批次1441: 处理了100条数据，匹配了2条\n", "读取第1442批数据（每批100条）...\n", "批次1442: 处理了100条数据，匹配了4条\n", "读取第1443批数据（每批100条）...\n", "批次1443: 处理了100条数据，匹配了7条\n", "读取第1444批数据（每批100条）...\n", "批次1444: 处理了100条数据，匹配了3条\n", "读取第1445批数据（每批100条）...\n", "批次1445: 处理了100条数据，匹配了3条\n", "读取第1446批数据（每批100条）...\n", "批次1446: 处理了100条数据，匹配了3条\n", "读取第1447批数据（每批100条）...\n", "批次1447: 处理了100条数据，匹配了2条\n", "读取第1448批数据（每批100条）...\n", "批次1448: 处理了100条数据，匹配了0条\n", "读取第1449批数据（每批100条）...\n", "批次1449: 处理了100条数据，匹配了3条\n", "读取第1450批数据（每批100条）...\n", "暂停5秒...\n", "批次1450: 处理了100条数据，匹配了1条\n", "临时保存已匹配的6113条数据...\n", "读取第1451批数据（每批100条）...\n", "批次1451: 处理了100条数据，匹配了2条\n", "读取第1452批数据（每批100条）...\n", "批次1452: 处理了100条数据，匹配了2条\n", "读取第1453批数据（每批100条）...\n", "批次1453: 处理了100条数据，匹配了5条\n", "读取第1454批数据（每批100条）...\n", "批次1454: 处理了100条数据，匹配了7条\n", "读取第1455批数据（每批100条）...\n", "批次1455: 处理了100条数据，匹配了2条\n", "读取第1456批数据（每批100条）...\n", "批次1456: 处理了100条数据，匹配了4条\n", "读取第1457批数据（每批100条）...\n", "批次1457: 处理了100条数据，匹配了3条\n", "读取第1458批数据（每批100条）...\n", "批次1458: 处理了100条数据，匹配了0条\n", "读取第1459批数据（每批100条）...\n", "批次1459: 处理了100条数据，匹配了5条\n", "读取第1460批数据（每批100条）...\n", "暂停5秒...\n", "批次1460: 处理了100条数据，匹配了3条\n", "临时保存已匹配的6146条数据...\n", "读取第1461批数据（每批100条）...\n", "批次1461: 处理了100条数据，匹配了1条\n", "读取第1462批数据（每批100条）...\n", "批次1462: 处理了100条数据，匹配了2条\n", "读取第1463批数据（每批100条）...\n", "批次1463: 处理了100条数据，匹配了1条\n", "读取第1464批数据（每批100条）...\n", "批次1464: 处理了100条数据，匹配了3条\n", "读取第1465批数据（每批100条）...\n", "批次1465: 处理了100条数据，匹配了5条\n", "读取第1466批数据（每批100条）...\n", "批次1466: 处理了100条数据，匹配了3条\n", "读取第1467批数据（每批100条）...\n", "批次1467: 处理了100条数据，匹配了0条\n", "读取第1468批数据（每批100条）...\n", "批次1468: 处理了100条数据，匹配了2条\n", "读取第1469批数据（每批100条）...\n", "批次1469: 处理了100条数据，匹配了1条\n", "读取第1470批数据（每批100条）...\n", "暂停5秒...\n", "批次1470: 处理了100条数据，匹配了15条\n", "临时保存已匹配的6179条数据...\n", "读取第1471批数据（每批100条）...\n", "批次1471: 处理了100条数据，匹配了11条\n", "读取第1472批数据（每批100条）...\n", "批次1472: 处理了100条数据，匹配了0条\n", "读取第1473批数据（每批100条）...\n", "批次1473: 处理了100条数据，匹配了4条\n", "读取第1474批数据（每批100条）...\n", "批次1474: 处理了100条数据，匹配了5条\n", "读取第1475批数据（每批100条）...\n", "批次1475: 处理了100条数据，匹配了1条\n", "读取第1476批数据（每批100条）...\n", "批次1476: 处理了100条数据，匹配了3条\n", "读取第1477批数据（每批100条）...\n", "批次1477: 处理了100条数据，匹配了2条\n", "读取第1478批数据（每批100条）...\n", "批次1478: 处理了100条数据，匹配了0条\n", "读取第1479批数据（每批100条）...\n", "批次1479: 处理了100条数据，匹配了0条\n", "读取第1480批数据（每批100条）...\n", "暂停5秒...\n", "批次1480: 处理了100条数据，匹配了4条\n", "临时保存已匹配的6209条数据...\n", "读取第1481批数据（每批100条）...\n", "批次1481: 处理了100条数据，匹配了4条\n", "读取第1482批数据（每批100条）...\n", "批次1482: 处理了100条数据，匹配了2条\n", "读取第1483批数据（每批100条）...\n", "批次1483: 处理了100条数据，匹配了1条\n", "读取第1484批数据（每批100条）...\n", "批次1484: 处理了100条数据，匹配了6条\n", "读取第1485批数据（每批100条）...\n", "批次1485: 处理了100条数据，匹配了3条\n", "读取第1486批数据（每批100条）...\n", "批次1486: 处理了100条数据，匹配了7条\n", "读取第1487批数据（每批100条）...\n", "批次1487: 处理了100条数据，匹配了3条\n", "读取第1488批数据（每批100条）...\n", "批次1488: 处理了100条数据，匹配了0条\n", "读取第1489批数据（每批100条）...\n", "批次1489: 处理了100条数据，匹配了3条\n", "读取第1490批数据（每批100条）...\n", "暂停5秒...\n", "批次1490: 处理了100条数据，匹配了5条\n", "临时保存已匹配的6243条数据...\n", "读取第1491批数据（每批100条）...\n", "批次1491: 处理了100条数据，匹配了3条\n", "读取第1492批数据（每批100条）...\n", "批次1492: 处理了100条数据，匹配了3条\n", "读取第1493批数据（每批100条）...\n", "批次1493: 处理了100条数据，匹配了17条\n", "读取第1494批数据（每批100条）...\n", "批次1494: 处理了100条数据，匹配了5条\n", "读取第1495批数据（每批100条）...\n", "批次1495: 处理了100条数据，匹配了5条\n", "读取第1496批数据（每批100条）...\n", "批次1496: 处理了100条数据，匹配了1条\n", "读取第1497批数据（每批100条）...\n", "批次1497: 处理了100条数据，匹配了1条\n", "读取第1498批数据（每批100条）...\n", "批次1498: 处理了100条数据，匹配了8条\n", "读取第1499批数据（每批100条）...\n", "批次1499: 处理了100条数据，匹配了4条\n", "读取第1500批数据（每批100条）...\n", "暂停5秒...\n", "批次1500: 处理了100条数据，匹配了5条\n", "临时保存已匹配的6295条数据...\n", "读取第1501批数据（每批100条）...\n", "批次1501: 处理了100条数据，匹配了5条\n", "读取第1502批数据（每批100条）...\n", "批次1502: 处理了100条数据，匹配了4条\n", "读取第1503批数据（每批100条）...\n", "批次1503: 处理了100条数据，匹配了6条\n", "读取第1504批数据（每批100条）...\n", "批次1504: 处理了100条数据，匹配了6条\n", "读取第1505批数据（每批100条）...\n", "批次1505: 处理了100条数据，匹配了3条\n", "读取第1506批数据（每批100条）...\n", "批次1506: 处理了100条数据，匹配了7条\n", "读取第1507批数据（每批100条）...\n", "批次1507: 处理了100条数据，匹配了6条\n", "读取第1508批数据（每批100条）...\n", "批次1508: 处理了100条数据，匹配了10条\n", "读取第1509批数据（每批100条）...\n", "批次1509: 处理了100条数据，匹配了5条\n", "读取第1510批数据（每批100条）...\n", "暂停5秒...\n", "批次1510: 处理了100条数据，匹配了0条\n", "临时保存已匹配的6347条数据...\n", "读取第1511批数据（每批100条）...\n", "批次1511: 处理了100条数据，匹配了11条\n", "读取第1512批数据（每批100条）...\n", "批次1512: 处理了100条数据，匹配了12条\n", "读取第1513批数据（每批100条）...\n", "批次1513: 处理了100条数据，匹配了5条\n", "读取第1514批数据（每批100条）...\n", "批次1514: 处理了100条数据，匹配了6条\n", "读取第1515批数据（每批100条）...\n", "批次1515: 处理了100条数据，匹配了2条\n", "读取第1516批数据（每批100条）...\n", "批次1516: 处理了100条数据，匹配了0条\n", "读取第1517批数据（每批100条）...\n", "批次1517: 处理了100条数据，匹配了7条\n", "读取第1518批数据（每批100条）...\n", "批次1518: 处理了100条数据，匹配了5条\n", "读取第1519批数据（每批100条）...\n", "批次1519: 处理了100条数据，匹配了1条\n", "读取第1520批数据（每批100条）...\n", "暂停5秒...\n", "批次1520: 处理了100条数据，匹配了3条\n", "临时保存已匹配的6399条数据...\n", "读取第1521批数据（每批100条）...\n", "批次1521: 处理了100条数据，匹配了10条\n", "读取第1522批数据（每批100条）...\n", "批次1522: 处理了100条数据，匹配了2条\n", "读取第1523批数据（每批100条）...\n", "批次1523: 处理了100条数据，匹配了5条\n", "读取第1524批数据（每批100条）...\n", "批次1524: 处理了100条数据，匹配了9条\n", "读取第1525批数据（每批100条）...\n", "批次1525: 处理了100条数据，匹配了2条\n", "读取第1526批数据（每批100条）...\n", "批次1526: 处理了100条数据，匹配了8条\n", "读取第1527批数据（每批100条）...\n", "批次1527: 处理了100条数据，匹配了3条\n", "读取第1528批数据（每批100条）...\n", "批次1528: 处理了100条数据，匹配了10条\n", "读取第1529批数据（每批100条）...\n", "批次1529: 处理了100条数据，匹配了1条\n", "读取第1530批数据（每批100条）...\n", "暂停5秒...\n", "批次1530: 处理了100条数据，匹配了6条\n", "临时保存已匹配的6455条数据...\n", "读取第1531批数据（每批100条）...\n", "批次1531: 处理了100条数据，匹配了8条\n", "读取第1532批数据（每批100条）...\n", "批次1532: 处理了100条数据，匹配了3条\n", "读取第1533批数据（每批100条）...\n", "批次1533: 处理了100条数据，匹配了2条\n", "读取第1534批数据（每批100条）...\n", "批次1534: 处理了100条数据，匹配了8条\n", "读取第1535批数据（每批100条）...\n", "批次1535: 处理了100条数据，匹配了4条\n", "读取第1536批数据（每批100条）...\n", "批次1536: 处理了100条数据，匹配了7条\n", "读取第1537批数据（每批100条）...\n", "批次1537: 处理了100条数据，匹配了10条\n", "读取第1538批数据（每批100条）...\n", "批次1538: 处理了100条数据，匹配了7条\n", "读取第1539批数据（每批100条）...\n", "批次1539: 处理了100条数据，匹配了4条\n", "读取第1540批数据（每批100条）...\n", "暂停5秒...\n", "批次1540: 处理了100条数据，匹配了0条\n", "临时保存已匹配的6508条数据...\n", "读取第1541批数据（每批100条）...\n", "批次1541: 处理了100条数据，匹配了4条\n", "读取第1542批数据（每批100条）...\n", "批次1542: 处理了100条数据，匹配了2条\n", "读取第1543批数据（每批100条）...\n", "批次1543: 处理了100条数据，匹配了1条\n", "读取第1544批数据（每批100条）...\n", "批次1544: 处理了100条数据，匹配了5条\n", "读取第1545批数据（每批100条）...\n", "批次1545: 处理了100条数据，匹配了5条\n", "读取第1546批数据（每批100条）...\n", "批次1546: 处理了100条数据，匹配了5条\n", "读取第1547批数据（每批100条）...\n", "批次1547: 处理了100条数据，匹配了4条\n", "读取第1548批数据（每批100条）...\n", "批次1548: 处理了100条数据，匹配了18条\n", "读取第1549批数据（每批100条）...\n", "批次1549: 处理了100条数据，匹配了10条\n", "读取第1550批数据（每批100条）...\n", "暂停5秒...\n", "批次1550: 处理了100条数据，匹配了18条\n", "临时保存已匹配的6580条数据...\n", "读取第1551批数据（每批100条）...\n", "批次1551: 处理了100条数据，匹配了4条\n", "读取第1552批数据（每批100条）...\n", "批次1552: 处理了100条数据，匹配了0条\n", "读取第1553批数据（每批100条）...\n", "批次1553: 处理了100条数据，匹配了2条\n", "读取第1554批数据（每批100条）...\n", "批次1554: 处理了100条数据，匹配了8条\n", "读取第1555批数据（每批100条）...\n", "批次1555: 处理了100条数据，匹配了2条\n", "读取第1556批数据（每批100条）...\n", "批次1556: 处理了100条数据，匹配了7条\n", "读取第1557批数据（每批100条）...\n", "批次1557: 处理了100条数据，匹配了10条\n", "读取第1558批数据（每批100条）...\n", "批次1558: 处理了100条数据，匹配了1条\n", "读取第1559批数据（每批100条）...\n", "批次1559: 处理了100条数据，匹配了2条\n", "读取第1560批数据（每批100条）...\n", "暂停5秒...\n", "批次1560: 处理了100条数据，匹配了3条\n", "临时保存已匹配的6619条数据...\n", "读取第1561批数据（每批100条）...\n", "批次1561: 处理了100条数据，匹配了9条\n", "读取第1562批数据（每批100条）...\n", "批次1562: 处理了100条数据，匹配了0条\n", "读取第1563批数据（每批100条）...\n", "批次1563: 处理了100条数据，匹配了1条\n", "读取第1564批数据（每批100条）...\n", "批次1564: 处理了100条数据，匹配了3条\n", "读取第1565批数据（每批100条）...\n", "批次1565: 处理了100条数据，匹配了2条\n", "读取第1566批数据（每批100条）...\n", "批次1566: 处理了100条数据，匹配了2条\n", "读取第1567批数据（每批100条）...\n", "批次1567: 处理了100条数据，匹配了8条\n", "读取第1568批数据（每批100条）...\n", "批次1568: 处理了100条数据，匹配了4条\n", "读取第1569批数据（每批100条）...\n", "批次1569: 处理了100条数据，匹配了2条\n", "读取第1570批数据（每批100条）...\n", "暂停5秒...\n", "批次1570: 处理了100条数据，匹配了1条\n", "临时保存已匹配的6651条数据...\n", "读取第1571批数据（每批100条）...\n", "批次1571: 处理了100条数据，匹配了3条\n", "读取第1572批数据（每批100条）...\n", "批次1572: 处理了100条数据，匹配了5条\n", "读取第1573批数据（每批100条）...\n", "批次1573: 处理了100条数据，匹配了5条\n", "读取第1574批数据（每批100条）...\n", "批次1574: 处理了100条数据，匹配了0条\n", "读取第1575批数据（每批100条）...\n", "批次1575: 处理了100条数据，匹配了3条\n", "读取第1576批数据（每批100条）...\n", "批次1576: 处理了100条数据，匹配了0条\n", "读取第1577批数据（每批100条）...\n", "批次1577: 处理了100条数据，匹配了4条\n", "读取第1578批数据（每批100条）...\n", "批次1578: 处理了100条数据，匹配了0条\n", "读取第1579批数据（每批100条）...\n", "批次1579: 处理了100条数据，匹配了2条\n", "读取第1580批数据（每批100条）...\n", "暂停5秒...\n", "批次1580: 处理了100条数据，匹配了7条\n", "临时保存已匹配的6680条数据...\n", "读取第1581批数据（每批100条）...\n", "批次1581: 处理了100条数据，匹配了2条\n", "读取第1582批数据（每批100条）...\n", "批次1582: 处理了100条数据，匹配了6条\n", "读取第1583批数据（每批100条）...\n", "批次1583: 处理了100条数据，匹配了9条\n", "读取第1584批数据（每批100条）...\n", "批次1584: 处理了100条数据，匹配了2条\n", "读取第1585批数据（每批100条）...\n", "批次1585: 处理了100条数据，匹配了2条\n", "读取第1586批数据（每批100条）...\n", "批次1586: 处理了100条数据，匹配了4条\n", "读取第1587批数据（每批100条）...\n", "批次1587: 处理了100条数据，匹配了5条\n", "读取第1588批数据（每批100条）...\n", "批次1588: 处理了100条数据，匹配了5条\n", "读取第1589批数据（每批100条）...\n", "批次1589: 处理了100条数据，匹配了1条\n", "读取第1590批数据（每批100条）...\n", "暂停5秒...\n", "批次1590: 处理了100条数据，匹配了2条\n", "临时保存已匹配的6718条数据...\n", "读取第1591批数据（每批100条）...\n", "批次1591: 处理了100条数据，匹配了2条\n", "读取第1592批数据（每批100条）...\n", "批次1592: 处理了100条数据，匹配了2条\n", "读取第1593批数据（每批100条）...\n", "批次1593: 处理了100条数据，匹配了7条\n", "读取第1594批数据（每批100条）...\n", "批次1594: 处理了100条数据，匹配了0条\n", "读取第1595批数据（每批100条）...\n", "批次1595: 处理了100条数据，匹配了8条\n", "读取第1596批数据（每批100条）...\n", "批次1596: 处理了100条数据，匹配了7条\n", "读取第1597批数据（每批100条）...\n", "批次1597: 处理了100条数据，匹配了13条\n", "读取第1598批数据（每批100条）...\n", "批次1598: 处理了100条数据，匹配了5条\n", "读取第1599批数据（每批100条）...\n", "批次1599: 处理了100条数据，匹配了1条\n", "读取第1600批数据（每批100条）...\n", "暂停5秒...\n", "批次1600: 处理了100条数据，匹配了4条\n", "临时保存已匹配的6767条数据...\n", "读取第1601批数据（每批100条）...\n", "批次1601: 处理了100条数据，匹配了0条\n", "读取第1602批数据（每批100条）...\n", "批次1602: 处理了100条数据，匹配了2条\n", "读取第1603批数据（每批100条）...\n", "批次1603: 处理了100条数据，匹配了4条\n", "读取第1604批数据（每批100条）...\n", "批次1604: 处理了100条数据，匹配了1条\n", "读取第1605批数据（每批100条）...\n", "批次1605: 处理了100条数据，匹配了2条\n", "读取第1606批数据（每批100条）...\n", "批次1606: 处理了100条数据，匹配了4条\n", "读取第1607批数据（每批100条）...\n", "批次1607: 处理了100条数据，匹配了3条\n", "读取第1608批数据（每批100条）...\n", "批次1608: 处理了100条数据，匹配了1条\n", "读取第1609批数据（每批100条）...\n", "批次1609: 处理了100条数据，匹配了7条\n", "读取第1610批数据（每批100条）...\n", "暂停5秒...\n", "批次1610: 处理了100条数据，匹配了20条\n", "临时保存已匹配的6811条数据...\n", "读取第1611批数据（每批100条）...\n", "批次1611: 处理了100条数据，匹配了1条\n", "读取第1612批数据（每批100条）...\n", "批次1612: 处理了100条数据，匹配了4条\n", "读取第1613批数据（每批100条）...\n", "批次1613: 处理了100条数据，匹配了0条\n", "读取第1614批数据（每批100条）...\n", "批次1614: 处理了100条数据，匹配了4条\n", "读取第1615批数据（每批100条）...\n", "批次1615: 处理了100条数据，匹配了6条\n", "读取第1616批数据（每批100条）...\n", "批次1616: 处理了100条数据，匹配了1条\n", "读取第1617批数据（每批100条）...\n", "批次1617: 处理了100条数据，匹配了10条\n", "读取第1618批数据（每批100条）...\n", "批次1618: 处理了100条数据，匹配了1条\n", "读取第1619批数据（每批100条）...\n", "批次1619: 处理了100条数据，匹配了9条\n", "读取第1620批数据（每批100条）...\n", "暂停5秒...\n", "批次1620: 处理了100条数据，匹配了10条\n", "临时保存已匹配的6857条数据...\n", "读取第1621批数据（每批100条）...\n", "批次1621: 处理了100条数据，匹配了5条\n", "读取第1622批数据（每批100条）...\n", "批次1622: 处理了100条数据，匹配了2条\n", "读取第1623批数据（每批100条）...\n", "批次1623: 处理了100条数据，匹配了9条\n", "读取第1624批数据（每批100条）...\n", "批次1624: 处理了100条数据，匹配了3条\n", "读取第1625批数据（每批100条）...\n", "批次1625: 处理了100条数据，匹配了0条\n", "读取第1626批数据（每批100条）...\n", "批次1626: 处理了100条数据，匹配了3条\n", "读取第1627批数据（每批100条）...\n", "批次1627: 处理了100条数据，匹配了2条\n", "读取第1628批数据（每批100条）...\n", "批次1628: 处理了100条数据，匹配了5条\n", "读取第1629批数据（每批100条）...\n", "批次1629: 处理了100条数据，匹配了3条\n", "读取第1630批数据（每批100条）...\n", "暂停5秒...\n", "批次1630: 处理了100条数据，匹配了10条\n", "临时保存已匹配的6899条数据...\n", "读取第1631批数据（每批100条）...\n", "批次1631: 处理了100条数据，匹配了2条\n", "读取第1632批数据（每批100条）...\n", "批次1632: 处理了100条数据，匹配了1条\n", "读取第1633批数据（每批100条）...\n", "批次1633: 处理了100条数据，匹配了5条\n", "读取第1634批数据（每批100条）...\n", "批次1634: 处理了100条数据，匹配了17条\n", "读取第1635批数据（每批100条）...\n", "批次1635: 处理了100条数据，匹配了14条\n", "读取第1636批数据（每批100条）...\n", "批次1636: 处理了100条数据，匹配了1条\n", "读取第1637批数据（每批100条）...\n", "批次1637: 处理了100条数据，匹配了1条\n", "读取第1638批数据（每批100条）...\n", "批次1638: 处理了100条数据，匹配了4条\n", "读取第1639批数据（每批100条）...\n", "批次1639: 处理了100条数据，匹配了2条\n", "读取第1640批数据（每批100条）...\n", "暂停5秒...\n", "批次1640: 处理了100条数据，匹配了3条\n", "临时保存已匹配的6949条数据...\n", "读取第1641批数据（每批100条）...\n", "批次1641: 处理了100条数据，匹配了5条\n", "读取第1642批数据（每批100条）...\n", "批次1642: 处理了100条数据，匹配了7条\n", "读取第1643批数据（每批100条）...\n", "批次1643: 处理了100条数据，匹配了5条\n", "读取第1644批数据（每批100条）...\n", "批次1644: 处理了100条数据，匹配了5条\n", "读取第1645批数据（每批100条）...\n", "批次1645: 处理了100条数据，匹配了2条\n", "读取第1646批数据（每批100条）...\n", "批次1646: 处理了100条数据，匹配了4条\n", "读取第1647批数据（每批100条）...\n", "批次1647: 处理了100条数据，匹配了0条\n", "读取第1648批数据（每批100条）...\n", "批次1648: 处理了100条数据，匹配了7条\n", "读取第1649批数据（每批100条）...\n", "批次1649: 处理了100条数据，匹配了14条\n", "读取第1650批数据（每批100条）...\n", "暂停5秒...\n", "批次1650: 处理了100条数据，匹配了1条\n", "临时保存已匹配的6999条数据...\n", "读取第1651批数据（每批100条）...\n", "批次1651: 处理了100条数据，匹配了8条\n", "读取第1652批数据（每批100条）...\n", "批次1652: 处理了100条数据，匹配了3条\n", "读取第1653批数据（每批100条）...\n", "批次1653: 处理了100条数据，匹配了4条\n", "读取第1654批数据（每批100条）...\n", "批次1654: 处理了100条数据，匹配了1条\n", "读取第1655批数据（每批100条）...\n", "批次1655: 处理了100条数据，匹配了4条\n", "读取第1656批数据（每批100条）...\n", "批次1656: 处理了100条数据，匹配了2条\n", "读取第1657批数据（每批100条）...\n", "批次1657: 处理了100条数据，匹配了6条\n", "读取第1658批数据（每批100条）...\n", "批次1658: 处理了100条数据，匹配了0条\n", "读取第1659批数据（每批100条）...\n", "批次1659: 处理了100条数据，匹配了2条\n", "读取第1660批数据（每批100条）...\n", "暂停5秒...\n", "批次1660: 处理了100条数据，匹配了2条\n", "临时保存已匹配的7031条数据...\n", "读取第1661批数据（每批100条）...\n", "批次1661: 处理了100条数据，匹配了4条\n", "读取第1662批数据（每批100条）...\n", "批次1662: 处理了100条数据，匹配了3条\n", "读取第1663批数据（每批100条）...\n", "批次1663: 处理了100条数据，匹配了4条\n", "读取第1664批数据（每批100条）...\n", "批次1664: 处理了100条数据，匹配了0条\n", "读取第1665批数据（每批100条）...\n", "批次1665: 处理了100条数据，匹配了4条\n", "读取第1666批数据（每批100条）...\n", "批次1666: 处理了100条数据，匹配了5条\n", "读取第1667批数据（每批100条）...\n", "批次1667: 处理了100条数据，匹配了1条\n", "读取第1668批数据（每批100条）...\n", "批次1668: 处理了100条数据，匹配了1条\n", "读取第1669批数据（每批100条）...\n", "批次1669: 处理了100条数据，匹配了1条\n", "读取第1670批数据（每批100条）...\n", "暂停5秒...\n", "批次1670: 处理了100条数据，匹配了7条\n", "临时保存已匹配的7061条数据...\n", "读取第1671批数据（每批100条）...\n", "批次1671: 处理了100条数据，匹配了4条\n", "读取第1672批数据（每批100条）...\n", "批次1672: 处理了100条数据，匹配了3条\n", "读取第1673批数据（每批100条）...\n", "批次1673: 处理了100条数据，匹配了3条\n", "读取第1674批数据（每批100条）...\n", "批次1674: 处理了100条数据，匹配了5条\n", "读取第1675批数据（每批100条）...\n", "批次1675: 处理了100条数据，匹配了5条\n", "读取第1676批数据（每批100条）...\n", "批次1676: 处理了100条数据，匹配了3条\n", "读取第1677批数据（每批100条）...\n", "批次1677: 处理了100条数据，匹配了10条\n", "读取第1678批数据（每批100条）...\n", "批次1678: 处理了100条数据，匹配了3条\n", "读取第1679批数据（每批100条）...\n", "批次1679: 处理了100条数据，匹配了1条\n", "读取第1680批数据（每批100条）...\n", "暂停5秒...\n", "批次1680: 处理了100条数据，匹配了5条\n", "临时保存已匹配的7103条数据...\n", "读取第1681批数据（每批100条）...\n", "批次1681: 处理了100条数据，匹配了14条\n", "读取第1682批数据（每批100条）...\n", "批次1682: 处理了100条数据，匹配了4条\n", "读取第1683批数据（每批100条）...\n", "批次1683: 处理了100条数据，匹配了3条\n", "读取第1684批数据（每批100条）...\n", "批次1684: 处理了100条数据，匹配了11条\n", "读取第1685批数据（每批100条）...\n", "批次1685: 处理了100条数据，匹配了6条\n", "读取第1686批数据（每批100条）...\n", "批次1686: 处理了100条数据，匹配了6条\n", "读取第1687批数据（每批100条）...\n", "批次1687: 处理了100条数据，匹配了5条\n", "读取第1688批数据（每批100条）...\n", "批次1688: 处理了100条数据，匹配了7条\n", "读取第1689批数据（每批100条）...\n", "批次1689: 处理了100条数据，匹配了6条\n", "读取第1690批数据（每批100条）...\n", "暂停5秒...\n", "批次1690: 处理了100条数据，匹配了1条\n", "临时保存已匹配的7166条数据...\n", "读取第1691批数据（每批100条）...\n", "批次1691: 处理了100条数据，匹配了5条\n", "读取第1692批数据（每批100条）...\n", "批次1692: 处理了100条数据，匹配了3条\n", "读取第1693批数据（每批100条）...\n", "批次1693: 处理了100条数据，匹配了1条\n", "读取第1694批数据（每批100条）...\n", "批次1694: 处理了100条数据，匹配了5条\n", "读取第1695批数据（每批100条）...\n", "批次1695: 处理了100条数据，匹配了2条\n", "读取第1696批数据（每批100条）...\n", "批次1696: 处理了100条数据，匹配了4条\n", "读取第1697批数据（每批100条）...\n", "批次1697: 处理了100条数据，匹配了13条\n", "读取第1698批数据（每批100条）...\n", "批次1698: 处理了100条数据，匹配了5条\n", "读取第1699批数据（每批100条）...\n", "批次1699: 处理了100条数据，匹配了6条\n", "读取第1700批数据（每批100条）...\n", "暂停5秒...\n", "批次1700: 处理了100条数据，匹配了3条\n", "临时保存已匹配的7213条数据...\n", "读取第1701批数据（每批100条）...\n", "批次1701: 处理了100条数据，匹配了6条\n", "读取第1702批数据（每批100条）...\n", "批次1702: 处理了100条数据，匹配了5条\n", "读取第1703批数据（每批100条）...\n", "批次1703: 处理了100条数据，匹配了14条\n", "读取第1704批数据（每批100条）...\n", "批次1704: 处理了100条数据，匹配了14条\n", "读取第1705批数据（每批100条）...\n", "批次1705: 处理了100条数据，匹配了2条\n", "读取第1706批数据（每批100条）...\n", "批次1706: 处理了100条数据，匹配了0条\n", "读取第1707批数据（每批100条）...\n", "批次1707: 处理了100条数据，匹配了11条\n", "读取第1708批数据（每批100条）...\n", "批次1708: 处理了100条数据，匹配了2条\n", "读取第1709批数据（每批100条）...\n", "批次1709: 处理了100条数据，匹配了8条\n", "读取第1710批数据（每批100条）...\n", "暂停5秒...\n", "批次1710: 处理了100条数据，匹配了5条\n", "临时保存已匹配的7280条数据...\n", "读取第1711批数据（每批100条）...\n", "批次1711: 处理了100条数据，匹配了6条\n", "读取第1712批数据（每批100条）...\n", "批次1712: 处理了100条数据，匹配了10条\n", "读取第1713批数据（每批100条）...\n", "批次1713: 处理了100条数据，匹配了2条\n", "读取第1714批数据（每批100条）...\n", "批次1714: 处理了100条数据，匹配了9条\n", "读取第1715批数据（每批100条）...\n", "批次1715: 处理了100条数据，匹配了7条\n", "读取第1716批数据（每批100条）...\n", "批次1716: 处理了100条数据，匹配了4条\n", "读取第1717批数据（每批100条）...\n", "批次1717: 处理了100条数据，匹配了3条\n", "读取第1718批数据（每批100条）...\n", "批次1718: 处理了100条数据，匹配了4条\n", "读取第1719批数据（每批100条）...\n", "批次1719: 处理了100条数据，匹配了6条\n", "读取第1720批数据（每批100条）...\n", "暂停5秒...\n", "批次1720: 处理了100条数据，匹配了3条\n", "临时保存已匹配的7334条数据...\n", "读取第1721批数据（每批100条）...\n", "批次1721: 处理了100条数据，匹配了5条\n", "读取第1722批数据（每批100条）...\n", "批次1722: 处理了100条数据，匹配了2条\n", "读取第1723批数据（每批100条）...\n", "批次1723: 处理了100条数据，匹配了5条\n", "读取第1724批数据（每批100条）...\n", "批次1724: 处理了100条数据，匹配了3条\n", "读取第1725批数据（每批100条）...\n", "批次1725: 处理了100条数据，匹配了7条\n", "读取第1726批数据（每批100条）...\n", "批次1726: 处理了100条数据，匹配了7条\n", "读取第1727批数据（每批100条）...\n", "批次1727: 处理了100条数据，匹配了5条\n", "读取第1728批数据（每批100条）...\n", "批次1728: 处理了100条数据，匹配了9条\n", "读取第1729批数据（每批100条）...\n", "批次1729: 处理了100条数据，匹配了2条\n", "读取第1730批数据（每批100条）...\n", "暂停5秒...\n", "批次1730: 处理了100条数据，匹配了1条\n", "临时保存已匹配的7380条数据...\n", "读取第1731批数据（每批100条）...\n", "批次1731: 处理了100条数据，匹配了5条\n", "读取第1732批数据（每批100条）...\n", "批次1732: 处理了100条数据，匹配了6条\n", "读取第1733批数据（每批100条）...\n", "批次1733: 处理了100条数据，匹配了3条\n", "读取第1734批数据（每批100条）...\n", "批次1734: 处理了100条数据，匹配了3条\n", "读取第1735批数据（每批100条）...\n", "批次1735: 处理了100条数据，匹配了16条\n", "读取第1736批数据（每批100条）...\n", "批次1736: 处理了100条数据，匹配了0条\n", "读取第1737批数据（每批100条）...\n", "批次1737: 处理了100条数据，匹配了0条\n", "读取第1738批数据（每批100条）...\n", "批次1738: 处理了100条数据，匹配了1条\n", "读取第1739批数据（每批100条）...\n", "批次1739: 处理了100条数据，匹配了0条\n", "读取第1740批数据（每批100条）...\n", "暂停5秒...\n", "批次1740: 处理了100条数据，匹配了0条\n", "临时保存已匹配的7414条数据...\n", "读取第1741批数据（每批100条）...\n", "批次1741: 处理了100条数据，匹配了1条\n", "读取第1742批数据（每批100条）...\n", "批次1742: 处理了100条数据，匹配了6条\n", "读取第1743批数据（每批100条）...\n", "批次1743: 处理了100条数据，匹配了0条\n", "读取第1744批数据（每批100条）...\n", "批次1744: 处理了100条数据，匹配了4条\n", "读取第1745批数据（每批100条）...\n", "批次1745: 处理了100条数据，匹配了6条\n", "读取第1746批数据（每批100条）...\n", "批次1746: 处理了100条数据，匹配了9条\n", "读取第1747批数据（每批100条）...\n", "批次1747: 处理了100条数据，匹配了4条\n", "读取第1748批数据（每批100条）...\n", "批次1748: 处理了100条数据，匹配了3条\n", "读取第1749批数据（每批100条）...\n", "批次1749: 处理了100条数据，匹配了4条\n", "读取第1750批数据（每批100条）...\n", "暂停5秒...\n", "批次1750: 处理了100条数据，匹配了0条\n", "临时保存已匹配的7451条数据...\n", "读取第1751批数据（每批100条）...\n", "批次1751: 处理了100条数据，匹配了4条\n", "读取第1752批数据（每批100条）...\n", "批次1752: 处理了100条数据，匹配了0条\n", "读取第1753批数据（每批100条）...\n", "批次1753: 处理了100条数据，匹配了0条\n", "读取第1754批数据（每批100条）...\n", "批次1754: 处理了100条数据，匹配了2条\n", "读取第1755批数据（每批100条）...\n", "批次1755: 处理了100条数据，匹配了2条\n", "读取第1756批数据（每批100条）...\n", "批次1756: 处理了100条数据，匹配了4条\n", "读取第1757批数据（每批100条）...\n", "批次1757: 处理了100条数据，匹配了1条\n", "读取第1758批数据（每批100条）...\n", "批次1758: 处理了100条数据，匹配了3条\n", "读取第1759批数据（每批100条）...\n", "批次1759: 处理了100条数据，匹配了1条\n", "读取第1760批数据（每批100条）...\n", "暂停5秒...\n", "批次1760: 处理了100条数据，匹配了1条\n", "临时保存已匹配的7469条数据...\n", "读取第1761批数据（每批100条）...\n", "批次1761: 处理了100条数据，匹配了4条\n", "读取第1762批数据（每批100条）...\n", "批次1762: 处理了100条数据，匹配了0条\n", "读取第1763批数据（每批100条）...\n", "批次1763: 处理了100条数据，匹配了0条\n", "读取第1764批数据（每批100条）...\n", "批次1764: 处理了100条数据，匹配了2条\n", "读取第1765批数据（每批100条）...\n", "批次1765: 处理了100条数据，匹配了0条\n", "读取第1766批数据（每批100条）...\n", "批次1766: 处理了100条数据，匹配了10条\n", "读取第1767批数据（每批100条）...\n", "批次1767: 处理了100条数据，匹配了12条\n", "读取第1768批数据（每批100条）...\n", "批次1768: 处理了100条数据，匹配了6条\n", "读取第1769批数据（每批100条）...\n", "批次1769: 处理了100条数据，匹配了7条\n", "读取第1770批数据（每批100条）...\n", "暂停5秒...\n", "批次1770: 处理了100条数据，匹配了9条\n", "临时保存已匹配的7519条数据...\n", "读取第1771批数据（每批100条）...\n", "批次1771: 处理了100条数据，匹配了3条\n", "读取第1772批数据（每批100条）...\n", "批次1772: 处理了100条数据，匹配了2条\n", "读取第1773批数据（每批100条）...\n", "批次1773: 处理了100条数据，匹配了5条\n", "读取第1774批数据（每批100条）...\n", "批次1774: 处理了100条数据，匹配了6条\n", "读取第1775批数据（每批100条）...\n", "批次1775: 处理了100条数据，匹配了2条\n", "读取第1776批数据（每批100条）...\n", "批次1776: 处理了100条数据，匹配了3条\n", "读取第1777批数据（每批100条）...\n", "批次1777: 处理了100条数据，匹配了12条\n", "读取第1778批数据（每批100条）...\n", "批次1778: 处理了100条数据，匹配了5条\n", "读取第1779批数据（每批100条）...\n", "批次1779: 处理了100条数据，匹配了5条\n", "读取第1780批数据（每批100条）...\n", "暂停5秒...\n", "批次1780: 处理了100条数据，匹配了8条\n", "临时保存已匹配的7570条数据...\n", "读取第1781批数据（每批100条）...\n", "批次1781: 处理了100条数据，匹配了4条\n", "读取第1782批数据（每批100条）...\n", "批次1782: 处理了100条数据，匹配了6条\n", "读取第1783批数据（每批100条）...\n", "批次1783: 处理了100条数据，匹配了8条\n", "读取第1784批数据（每批100条）...\n", "批次1784: 处理了100条数据，匹配了5条\n", "读取第1785批数据（每批100条）...\n", "批次1785: 处理了100条数据，匹配了4条\n", "读取第1786批数据（每批100条）...\n", "批次1786: 处理了100条数据，匹配了6条\n", "读取第1787批数据（每批100条）...\n", "批次1787: 处理了100条数据，匹配了6条\n", "读取第1788批数据（每批100条）...\n", "批次1788: 处理了100条数据，匹配了8条\n", "读取第1789批数据（每批100条）...\n", "批次1789: 处理了100条数据，匹配了5条\n", "读取第1790批数据（每批100条）...\n", "暂停5秒...\n", "批次1790: 处理了100条数据，匹配了7条\n", "临时保存已匹配的7629条数据...\n", "读取第1791批数据（每批100条）...\n", "批次1791: 处理了100条数据，匹配了3条\n", "读取第1792批数据（每批100条）...\n", "批次1792: 处理了100条数据，匹配了3条\n", "读取第1793批数据（每批100条）...\n", "批次1793: 处理了100条数据，匹配了6条\n", "读取第1794批数据（每批100条）...\n", "批次1794: 处理了100条数据，匹配了6条\n", "读取第1795批数据（每批100条）...\n", "批次1795: 处理了100条数据，匹配了3条\n", "读取第1796批数据（每批100条）...\n", "批次1796: 处理了100条数据，匹配了3条\n", "读取第1797批数据（每批100条）...\n", "批次1797: 处理了100条数据，匹配了4条\n", "读取第1798批数据（每批100条）...\n", "批次1798: 处理了100条数据，匹配了2条\n", "读取第1799批数据（每批100条）...\n", "批次1799: 处理了100条数据，匹配了6条\n", "读取第1800批数据（每批100条）...\n", "暂停5秒...\n", "批次1800: 处理了100条数据，匹配了4条\n", "临时保存已匹配的7669条数据...\n", "读取第1801批数据（每批100条）...\n", "批次1801: 处理了100条数据，匹配了8条\n", "读取第1802批数据（每批100条）...\n", "批次1802: 处理了100条数据，匹配了4条\n", "读取第1803批数据（每批100条）...\n", "批次1803: 处理了100条数据，匹配了4条\n", "读取第1804批数据（每批100条）...\n", "批次1804: 处理了100条数据，匹配了2条\n", "读取第1805批数据（每批100条）...\n", "批次1805: 处理了100条数据，匹配了2条\n", "读取第1806批数据（每批100条）...\n", "批次1806: 处理了100条数据，匹配了2条\n", "读取第1807批数据（每批100条）...\n", "批次1807: 处理了100条数据，匹配了2条\n", "读取第1808批数据（每批100条）...\n", "批次1808: 处理了100条数据，匹配了1条\n", "读取第1809批数据（每批100条）...\n", "批次1809: 处理了100条数据，匹配了3条\n", "读取第1810批数据（每批100条）...\n", "暂停5秒...\n", "批次1810: 处理了100条数据，匹配了7条\n", "临时保存已匹配的7704条数据...\n", "读取第1811批数据（每批100条）...\n", "批次1811: 处理了100条数据，匹配了3条\n", "读取第1812批数据（每批100条）...\n", "批次1812: 处理了100条数据，匹配了3条\n", "读取第1813批数据（每批100条）...\n", "批次1813: 处理了100条数据，匹配了5条\n", "读取第1814批数据（每批100条）...\n", "批次1814: 处理了100条数据，匹配了2条\n", "读取第1815批数据（每批100条）...\n", "批次1815: 处理了100条数据，匹配了5条\n", "读取第1816批数据（每批100条）...\n", "批次1816: 处理了100条数据，匹配了6条\n", "读取第1817批数据（每批100条）...\n", "批次1817: 处理了100条数据，匹配了4条\n", "读取第1818批数据（每批100条）...\n", "批次1818: 处理了100条数据，匹配了2条\n", "读取第1819批数据（每批100条）...\n", "批次1819: 处理了100条数据，匹配了5条\n", "读取第1820批数据（每批100条）...\n", "暂停5秒...\n", "批次1820: 处理了100条数据，匹配了5条\n", "临时保存已匹配的7744条数据...\n", "读取第1821批数据（每批100条）...\n", "批次1821: 处理了100条数据，匹配了7条\n", "读取第1822批数据（每批100条）...\n", "批次1822: 处理了100条数据，匹配了8条\n", "读取第1823批数据（每批100条）...\n", "批次1823: 处理了100条数据，匹配了5条\n", "读取第1824批数据（每批100条）...\n", "批次1824: 处理了100条数据，匹配了5条\n", "读取第1825批数据（每批100条）...\n", "批次1825: 处理了100条数据，匹配了3条\n", "读取第1826批数据（每批100条）...\n", "批次1826: 处理了100条数据，匹配了6条\n", "读取第1827批数据（每批100条）...\n", "批次1827: 处理了100条数据，匹配了9条\n", "读取第1828批数据（每批100条）...\n", "批次1828: 处理了100条数据，匹配了8条\n", "读取第1829批数据（每批100条）...\n", "批次1829: 处理了100条数据，匹配了5条\n", "读取第1830批数据（每批100条）...\n", "暂停5秒...\n", "批次1830: 处理了100条数据，匹配了7条\n", "临时保存已匹配的7807条数据...\n", "读取第1831批数据（每批100条）...\n", "批次1831: 处理了100条数据，匹配了1条\n", "读取第1832批数据（每批100条）...\n", "批次1832: 处理了100条数据，匹配了3条\n", "读取第1833批数据（每批100条）...\n", "批次1833: 处理了100条数据，匹配了2条\n", "读取第1834批数据（每批100条）...\n", "批次1834: 处理了100条数据，匹配了1条\n", "读取第1835批数据（每批100条）...\n", "批次1835: 处理了100条数据，匹配了2条\n", "读取第1836批数据（每批100条）...\n", "批次1836: 处理了100条数据，匹配了1条\n", "读取第1837批数据（每批100条）...\n", "批次1837: 处理了100条数据，匹配了0条\n", "读取第1838批数据（每批100条）...\n", "批次1838: 处理了100条数据，匹配了2条\n", "读取第1839批数据（每批100条）...\n", "批次1839: 处理了100条数据，匹配了2条\n", "读取第1840批数据（每批100条）...\n", "暂停5秒...\n", "批次1840: 处理了100条数据，匹配了8条\n", "临时保存已匹配的7829条数据...\n", "读取第1841批数据（每批100条）...\n", "批次1841: 处理了100条数据，匹配了19条\n", "读取第1842批数据（每批100条）...\n", "批次1842: 处理了100条数据，匹配了0条\n", "读取第1843批数据（每批100条）...\n", "批次1843: 处理了100条数据，匹配了2条\n", "读取第1844批数据（每批100条）...\n", "批次1844: 处理了100条数据，匹配了2条\n", "读取第1845批数据（每批100条）...\n", "批次1845: 处理了100条数据，匹配了1条\n", "读取第1846批数据（每批100条）...\n", "批次1846: 处理了100条数据，匹配了3条\n", "读取第1847批数据（每批100条）...\n", "批次1847: 处理了100条数据，匹配了4条\n", "读取第1848批数据（每批100条）...\n", "批次1848: 处理了100条数据，匹配了1条\n", "读取第1849批数据（每批100条）...\n", "批次1849: 处理了100条数据，匹配了3条\n", "读取第1850批数据（每批100条）...\n", "暂停5秒...\n", "批次1850: 处理了100条数据，匹配了20条\n", "临时保存已匹配的7884条数据...\n", "读取第1851批数据（每批100条）...\n", "批次1851: 处理了100条数据，匹配了12条\n", "读取第1852批数据（每批100条）...\n", "批次1852: 处理了100条数据，匹配了4条\n", "读取第1853批数据（每批100条）...\n", "批次1853: 处理了100条数据，匹配了2条\n", "读取第1854批数据（每批100条）...\n", "批次1854: 处理了100条数据，匹配了1条\n", "读取第1855批数据（每批100条）...\n", "批次1855: 处理了100条数据，匹配了3条\n", "读取第1856批数据（每批100条）...\n", "批次1856: 处理了100条数据，匹配了8条\n", "读取第1857批数据（每批100条）...\n", "批次1857: 处理了100条数据，匹配了3条\n", "读取第1858批数据（每批100条）...\n", "批次1858: 处理了100条数据，匹配了4条\n", "读取第1859批数据（每批100条）...\n", "批次1859: 处理了100条数据，匹配了5条\n", "读取第1860批数据（每批100条）...\n", "暂停5秒...\n", "批次1860: 处理了100条数据，匹配了4条\n", "临时保存已匹配的7930条数据...\n", "读取第1861批数据（每批100条）...\n", "批次1861: 处理了100条数据，匹配了1条\n", "读取第1862批数据（每批100条）...\n", "批次1862: 处理了100条数据，匹配了0条\n", "读取第1863批数据（每批100条）...\n", "批次1863: 处理了100条数据，匹配了14条\n", "读取第1864批数据（每批100条）...\n", "批次1864: 处理了100条数据，匹配了2条\n", "读取第1865批数据（每批100条）...\n", "批次1865: 处理了100条数据，匹配了1条\n", "读取第1866批数据（每批100条）...\n", "批次1866: 处理了100条数据，匹配了4条\n", "读取第1867批数据（每批100条）...\n", "批次1867: 处理了100条数据，匹配了4条\n", "读取第1868批数据（每批100条）...\n", "批次1868: 处理了100条数据，匹配了6条\n", "读取第1869批数据（每批100条）...\n", "批次1869: 处理了100条数据，匹配了5条\n", "读取第1870批数据（每批100条）...\n", "暂停5秒...\n", "批次1870: 处理了100条数据，匹配了3条\n", "临时保存已匹配的7970条数据...\n", "读取第1871批数据（每批100条）...\n", "批次1871: 处理了100条数据，匹配了7条\n", "读取第1872批数据（每批100条）...\n", "批次1872: 处理了100条数据，匹配了1条\n", "读取第1873批数据（每批100条）...\n", "批次1873: 处理了100条数据，匹配了6条\n", "读取第1874批数据（每批100条）...\n", "批次1874: 处理了100条数据，匹配了18条\n", "读取第1875批数据（每批100条）...\n", "批次1875: 处理了100条数据，匹配了0条\n", "读取第1876批数据（每批100条）...\n", "批次1876: 处理了100条数据，匹配了3条\n", "读取第1877批数据（每批100条）...\n", "批次1877: 处理了100条数据，匹配了2条\n", "读取第1878批数据（每批100条）...\n", "批次1878: 处理了100条数据，匹配了0条\n", "读取第1879批数据（每批100条）...\n", "批次1879: 处理了100条数据，匹配了2条\n", "读取第1880批数据（每批100条）...\n", "暂停5秒...\n", "批次1880: 处理了100条数据，匹配了5条\n", "临时保存已匹配的8014条数据...\n", "读取第1881批数据（每批100条）...\n", "批次1881: 处理了100条数据，匹配了17条\n", "读取第1882批数据（每批100条）...\n", "批次1882: 处理了100条数据，匹配了3条\n", "读取第1883批数据（每批100条）...\n", "批次1883: 处理了100条数据，匹配了6条\n", "读取第1884批数据（每批100条）...\n", "批次1884: 处理了100条数据，匹配了5条\n", "读取第1885批数据（每批100条）...\n", "批次1885: 处理了100条数据，匹配了4条\n", "读取第1886批数据（每批100条）...\n", "批次1886: 处理了100条数据，匹配了3条\n", "读取第1887批数据（每批100条）...\n", "批次1887: 处理了100条数据，匹配了12条\n", "读取第1888批数据（每批100条）...\n", "批次1888: 处理了100条数据，匹配了7条\n", "读取第1889批数据（每批100条）...\n", "批次1889: 处理了100条数据，匹配了6条\n", "读取第1890批数据（每批100条）...\n", "暂停5秒...\n", "批次1890: 处理了100条数据，匹配了2条\n", "临时保存已匹配的8079条数据...\n", "读取第1891批数据（每批100条）...\n", "批次1891: 处理了100条数据，匹配了5条\n", "读取第1892批数据（每批100条）...\n", "批次1892: 处理了100条数据，匹配了3条\n", "读取第1893批数据（每批100条）...\n", "批次1893: 处理了100条数据，匹配了2条\n", "读取第1894批数据（每批100条）...\n", "批次1894: 处理了100条数据，匹配了7条\n", "读取第1895批数据（每批100条）...\n", "批次1895: 处理了100条数据，匹配了2条\n", "读取第1896批数据（每批100条）...\n", "批次1896: 处理了100条数据，匹配了1条\n", "读取第1897批数据（每批100条）...\n", "批次1897: 处理了100条数据，匹配了4条\n", "读取第1898批数据（每批100条）...\n", "批次1898: 处理了100条数据，匹配了6条\n", "读取第1899批数据（每批100条）...\n", "批次1899: 处理了100条数据，匹配了7条\n", "读取第1900批数据（每批100条）...\n", "暂停5秒...\n", "批次1900: 处理了100条数据，匹配了6条\n", "临时保存已匹配的8122条数据...\n", "读取第1901批数据（每批100条）...\n", "批次1901: 处理了100条数据，匹配了6条\n", "读取第1902批数据（每批100条）...\n", "批次1902: 处理了100条数据，匹配了3条\n", "读取第1903批数据（每批100条）...\n", "批次1903: 处理了100条数据，匹配了4条\n", "读取第1904批数据（每批100条）...\n", "批次1904: 处理了100条数据，匹配了11条\n", "读取第1905批数据（每批100条）...\n", "批次1905: 处理了100条数据，匹配了7条\n", "读取第1906批数据（每批100条）...\n", "批次1906: 处理了100条数据，匹配了16条\n", "读取第1907批数据（每批100条）...\n", "批次1907: 处理了100条数据，匹配了8条\n", "读取第1908批数据（每批100条）...\n", "批次1908: 处理了100条数据，匹配了9条\n", "读取第1909批数据（每批100条）...\n", "批次1909: 处理了100条数据，匹配了5条\n", "读取第1910批数据（每批100条）...\n", "暂停5秒...\n", "批次1910: 处理了100条数据，匹配了2条\n", "临时保存已匹配的8193条数据...\n", "读取第1911批数据（每批100条）...\n", "批次1911: 处理了100条数据，匹配了10条\n", "读取第1912批数据（每批100条）...\n", "批次1912: 处理了100条数据，匹配了7条\n", "读取第1913批数据（每批100条）...\n", "批次1913: 处理了100条数据，匹配了3条\n", "读取第1914批数据（每批100条）...\n", "批次1914: 处理了100条数据，匹配了9条\n", "读取第1915批数据（每批100条）...\n", "批次1915: 处理了100条数据，匹配了8条\n", "读取第1916批数据（每批100条）...\n", "批次1916: 处理了100条数据，匹配了7条\n", "读取第1917批数据（每批100条）...\n", "批次1917: 处理了100条数据，匹配了2条\n", "读取第1918批数据（每批100条）...\n", "批次1918: 处理了100条数据，匹配了3条\n", "读取第1919批数据（每批100条）...\n", "批次1919: 处理了100条数据，匹配了0条\n", "读取第1920批数据（每批100条）...\n", "暂停5秒...\n", "批次1920: 处理了100条数据，匹配了7条\n", "临时保存已匹配的8249条数据...\n", "读取第1921批数据（每批100条）...\n", "批次1921: 处理了100条数据，匹配了5条\n", "读取第1922批数据（每批100条）...\n", "批次1922: 处理了100条数据，匹配了4条\n", "读取第1923批数据（每批100条）...\n", "批次1923: 处理了100条数据，匹配了4条\n", "读取第1924批数据（每批100条）...\n", "批次1924: 处理了100条数据，匹配了4条\n", "读取第1925批数据（每批100条）...\n", "批次1925: 处理了100条数据，匹配了7条\n", "读取第1926批数据（每批100条）...\n", "批次1926: 处理了100条数据，匹配了9条\n", "读取第1927批数据（每批100条）...\n", "批次1927: 处理了100条数据，匹配了3条\n", "读取第1928批数据（每批100条）...\n", "批次1928: 处理了100条数据，匹配了5条\n", "读取第1929批数据（每批100条）...\n", "批次1929: 处理了100条数据，匹配了4条\n", "读取第1930批数据（每批100条）...\n", "暂停5秒...\n", "批次1930: 处理了100条数据，匹配了14条\n", "临时保存已匹配的8308条数据...\n", "读取第1931批数据（每批100条）...\n", "批次1931: 处理了100条数据，匹配了4条\n", "读取第1932批数据（每批100条）...\n", "批次1932: 处理了100条数据，匹配了3条\n", "读取第1933批数据（每批100条）...\n", "批次1933: 处理了100条数据，匹配了12条\n", "读取第1934批数据（每批100条）...\n", "批次1934: 处理了100条数据，匹配了5条\n", "读取第1935批数据（每批100条）...\n", "批次1935: 处理了100条数据，匹配了6条\n", "读取第1936批数据（每批100条）...\n", "批次1936: 处理了100条数据，匹配了2条\n", "读取第1937批数据（每批100条）...\n", "批次1937: 处理了100条数据，匹配了2条\n", "读取第1938批数据（每批100条）...\n", "批次1938: 处理了100条数据，匹配了12条\n", "读取第1939批数据（每批100条）...\n", "批次1939: 处理了100条数据，匹配了8条\n", "读取第1940批数据（每批100条）...\n", "暂停5秒...\n", "批次1940: 处理了100条数据，匹配了12条\n", "临时保存已匹配的8374条数据...\n", "读取第1941批数据（每批100条）...\n", "批次1941: 处理了100条数据，匹配了4条\n", "读取第1942批数据（每批100条）...\n", "批次1942: 处理了100条数据，匹配了7条\n", "读取第1943批数据（每批100条）...\n", "批次1943: 处理了100条数据，匹配了5条\n", "读取第1944批数据（每批100条）...\n", "批次1944: 处理了100条数据，匹配了2条\n", "读取第1945批数据（每批100条）...\n", "批次1945: 处理了100条数据，匹配了5条\n", "读取第1946批数据（每批100条）...\n", "批次1946: 处理了100条数据，匹配了1条\n", "读取第1947批数据（每批100条）...\n", "批次1947: 处理了100条数据，匹配了5条\n", "读取第1948批数据（每批100条）...\n", "批次1948: 处理了100条数据，匹配了3条\n", "读取第1949批数据（每批100条）...\n", "批次1949: 处理了100条数据，匹配了7条\n", "读取第1950批数据（每批100条）...\n", "暂停5秒...\n", "批次1950: 处理了100条数据，匹配了1条\n", "临时保存已匹配的8414条数据...\n", "读取第1951批数据（每批100条）...\n", "批次1951: 处理了100条数据，匹配了3条\n", "读取第1952批数据（每批100条）...\n", "批次1952: 处理了100条数据，匹配了8条\n", "读取第1953批数据（每批100条）...\n", "批次1953: 处理了100条数据，匹配了9条\n", "读取第1954批数据（每批100条）...\n", "批次1954: 处理了100条数据，匹配了3条\n", "读取第1955批数据（每批100条）...\n", "批次1955: 处理了100条数据，匹配了4条\n", "读取第1956批数据（每批100条）...\n", "批次1956: 处理了100条数据，匹配了3条\n", "读取第1957批数据（每批100条）...\n", "批次1957: 处理了100条数据，匹配了4条\n", "读取第1958批数据（每批100条）...\n", "批次1958: 处理了100条数据，匹配了6条\n", "读取第1959批数据（每批100条）...\n", "批次1959: 处理了100条数据，匹配了6条\n", "读取第1960批数据（每批100条）...\n", "暂停5秒...\n", "批次1960: 处理了100条数据，匹配了6条\n", "临时保存已匹配的8466条数据...\n", "读取第1961批数据（每批100条）...\n", "批次1961: 处理了100条数据，匹配了1条\n", "读取第1962批数据（每批100条）...\n", "批次1962: 处理了100条数据，匹配了10条\n", "读取第1963批数据（每批100条）...\n", "批次1963: 处理了100条数据，匹配了7条\n", "读取第1964批数据（每批100条）...\n", "批次1964: 处理了100条数据，匹配了8条\n", "读取第1965批数据（每批100条）...\n", "批次1965: 处理了100条数据，匹配了4条\n", "读取第1966批数据（每批100条）...\n", "批次1966: 处理了100条数据，匹配了4条\n", "读取第1967批数据（每批100条）...\n", "批次1967: 处理了100条数据，匹配了1条\n", "读取第1968批数据（每批100条）...\n", "批次1968: 处理了100条数据，匹配了8条\n", "读取第1969批数据（每批100条）...\n", "批次1969: 处理了100条数据，匹配了6条\n", "读取第1970批数据（每批100条）...\n", "暂停5秒...\n", "批次1970: 处理了100条数据，匹配了3条\n", "临时保存已匹配的8518条数据...\n", "读取第1971批数据（每批100条）...\n", "批次1971: 处理了100条数据，匹配了3条\n", "读取第1972批数据（每批100条）...\n", "批次1972: 处理了100条数据，匹配了0条\n", "读取第1973批数据（每批100条）...\n", "批次1973: 处理了100条数据，匹配了6条\n", "读取第1974批数据（每批100条）...\n", "批次1974: 处理了100条数据，匹配了9条\n", "读取第1975批数据（每批100条）...\n", "批次1975: 处理了100条数据，匹配了1条\n", "读取第1976批数据（每批100条）...\n", "批次1976: 处理了100条数据，匹配了4条\n", "读取第1977批数据（每批100条）...\n", "批次1977: 处理了100条数据，匹配了6条\n", "读取第1978批数据（每批100条）...\n", "批次1978: 处理了100条数据，匹配了3条\n", "读取第1979批数据（每批100条）...\n", "批次1979: 处理了100条数据，匹配了2条\n", "读取第1980批数据（每批100条）...\n", "暂停5秒...\n", "批次1980: 处理了100条数据，匹配了6条\n", "临时保存已匹配的8558条数据...\n", "读取第1981批数据（每批100条）...\n", "批次1981: 处理了100条数据，匹配了2条\n", "读取第1982批数据（每批100条）...\n", "批次1982: 处理了100条数据，匹配了3条\n", "读取第1983批数据（每批100条）...\n", "批次1983: 处理了100条数据，匹配了10条\n", "读取第1984批数据（每批100条）...\n", "批次1984: 处理了100条数据，匹配了8条\n", "读取第1985批数据（每批100条）...\n", "批次1985: 处理了100条数据，匹配了1条\n", "读取第1986批数据（每批100条）...\n", "批次1986: 处理了100条数据，匹配了3条\n", "读取第1987批数据（每批100条）...\n", "批次1987: 处理了100条数据，匹配了4条\n", "读取第1988批数据（每批100条）...\n", "批次1988: 处理了100条数据，匹配了5条\n", "读取第1989批数据（每批100条）...\n", "批次1989: 处理了100条数据，匹配了1条\n", "读取第1990批数据（每批100条）...\n", "暂停5秒...\n", "批次1990: 处理了100条数据，匹配了7条\n", "临时保存已匹配的8602条数据...\n", "读取第1991批数据（每批100条）...\n", "批次1991: 处理了100条数据，匹配了3条\n", "读取第1992批数据（每批100条）...\n", "批次1992: 处理了100条数据，匹配了0条\n", "读取第1993批数据（每批100条）...\n", "批次1993: 处理了100条数据，匹配了5条\n", "读取第1994批数据（每批100条）...\n", "批次1994: 处理了100条数据，匹配了8条\n", "读取第1995批数据（每批100条）...\n", "批次1995: 处理了100条数据，匹配了8条\n", "读取第1996批数据（每批100条）...\n", "批次1996: 处理了100条数据，匹配了0条\n", "读取第1997批数据（每批100条）...\n", "批次1997: 处理了100条数据，匹配了4条\n", "读取第1998批数据（每批100条）...\n", "批次1998: 处理了100条数据，匹配了3条\n", "读取第1999批数据（每批100条）...\n", "批次1999: 处理了100条数据，匹配了4条\n", "读取第2000批数据（每批100条）...\n", "暂停5秒...\n", "批次2000: 处理了100条数据，匹配了5条\n", "临时保存已匹配的8642条数据...\n", "读取第2001批数据（每批100条）...\n", "批次2001: 处理了100条数据，匹配了3条\n", "读取第2002批数据（每批100条）...\n", "批次2002: 处理了100条数据，匹配了10条\n", "读取第2003批数据（每批100条）...\n", "批次2003: 处理了100条数据，匹配了8条\n", "读取第2004批数据（每批100条）...\n", "批次2004: 处理了100条数据，匹配了4条\n", "读取第2005批数据（每批100条）...\n", "批次2005: 处理了100条数据，匹配了1条\n", "读取第2006批数据（每批100条）...\n", "批次2006: 处理了100条数据，匹配了5条\n", "读取第2007批数据（每批100条）...\n", "批次2007: 处理了100条数据，匹配了2条\n", "读取第2008批数据（每批100条）...\n", "批次2008: 处理了100条数据，匹配了2条\n", "读取第2009批数据（每批100条）...\n", "批次2009: 处理了100条数据，匹配了3条\n", "读取第2010批数据（每批100条）...\n", "暂停5秒...\n", "批次2010: 处理了100条数据，匹配了5条\n", "临时保存已匹配的8685条数据...\n", "读取第2011批数据（每批100条）...\n", "批次2011: 处理了100条数据，匹配了5条\n", "读取第2012批数据（每批100条）...\n", "批次2012: 处理了100条数据，匹配了7条\n", "读取第2013批数据（每批100条）...\n", "批次2013: 处理了100条数据，匹配了8条\n", "读取第2014批数据（每批100条）...\n", "批次2014: 处理了100条数据，匹配了3条\n", "读取第2015批数据（每批100条）...\n", "批次2015: 处理了100条数据，匹配了4条\n", "读取第2016批数据（每批100条）...\n", "批次2016: 处理了100条数据，匹配了1条\n", "读取第2017批数据（每批100条）...\n", "批次2017: 处理了100条数据，匹配了3条\n", "读取第2018批数据（每批100条）...\n", "批次2018: 处理了100条数据，匹配了10条\n", "读取第2019批数据（每批100条）...\n", "批次2019: 处理了100条数据，匹配了3条\n", "读取第2020批数据（每批100条）...\n", "暂停5秒...\n", "批次2020: 处理了100条数据，匹配了3条\n", "临时保存已匹配的8732条数据...\n", "读取第2021批数据（每批100条）...\n", "批次2021: 处理了100条数据，匹配了2条\n", "读取第2022批数据（每批100条）...\n", "批次2022: 处理了100条数据，匹配了4条\n", "读取第2023批数据（每批100条）...\n", "批次2023: 处理了100条数据，匹配了5条\n", "读取第2024批数据（每批100条）...\n", "批次2024: 处理了100条数据，匹配了10条\n", "读取第2025批数据（每批100条）...\n", "批次2025: 处理了100条数据，匹配了10条\n", "读取第2026批数据（每批100条）...\n", "批次2026: 处理了100条数据，匹配了4条\n", "读取第2027批数据（每批100条）...\n", "批次2027: 处理了100条数据，匹配了9条\n", "读取第2028批数据（每批100条）...\n", "批次2028: 处理了100条数据，匹配了4条\n", "读取第2029批数据（每批100条）...\n", "批次2029: 处理了100条数据，匹配了2条\n", "读取第2030批数据（每批100条）...\n", "暂停5秒...\n", "批次2030: 处理了100条数据，匹配了4条\n", "临时保存已匹配的8786条数据...\n", "读取第2031批数据（每批100条）...\n", "批次2031: 处理了100条数据，匹配了6条\n", "读取第2032批数据（每批100条）...\n", "批次2032: 处理了100条数据，匹配了7条\n", "读取第2033批数据（每批100条）...\n", "批次2033: 处理了100条数据，匹配了4条\n", "读取第2034批数据（每批100条）...\n", "批次2034: 处理了100条数据，匹配了2条\n", "读取第2035批数据（每批100条）...\n", "批次2035: 处理了100条数据，匹配了3条\n", "读取第2036批数据（每批100条）...\n", "批次2036: 处理了100条数据，匹配了6条\n", "读取第2037批数据（每批100条）...\n", "批次2037: 处理了100条数据，匹配了2条\n", "读取第2038批数据（每批100条）...\n", "批次2038: 处理了100条数据，匹配了2条\n", "读取第2039批数据（每批100条）...\n", "批次2039: 处理了100条数据，匹配了5条\n", "读取第2040批数据（每批100条）...\n", "暂停5秒...\n", "批次2040: 处理了100条数据，匹配了4条\n", "临时保存已匹配的8827条数据...\n", "读取第2041批数据（每批100条）...\n", "批次2041: 处理了100条数据，匹配了1条\n", "读取第2042批数据（每批100条）...\n", "批次2042: 处理了100条数据，匹配了4条\n", "读取第2043批数据（每批100条）...\n", "批次2043: 处理了100条数据，匹配了0条\n", "读取第2044批数据（每批100条）...\n", "批次2044: 处理了100条数据，匹配了2条\n", "读取第2045批数据（每批100条）...\n", "批次2045: 处理了100条数据，匹配了4条\n", "读取第2046批数据（每批100条）...\n", "批次2046: 处理了100条数据，匹配了4条\n", "读取第2047批数据（每批100条）...\n", "批次2047: 处理了100条数据，匹配了0条\n", "读取第2048批数据（每批100条）...\n", "批次2048: 处理了100条数据，匹配了0条\n", "读取第2049批数据（每批100条）...\n", "批次2049: 处理了100条数据，匹配了0条\n", "读取第2050批数据（每批100条）...\n", "暂停5秒...\n", "批次2050: 处理了100条数据，匹配了0条\n", "临时保存已匹配的8842条数据...\n", "读取第2051批数据（每批100条）...\n", "批次2051: 处理了100条数据，匹配了0条\n", "读取第2052批数据（每批100条）...\n", "批次2052: 处理了100条数据，匹配了1条\n", "读取第2053批数据（每批100条）...\n", "批次2053: 处理了100条数据，匹配了2条\n", "读取第2054批数据（每批100条）...\n", "批次2054: 处理了100条数据，匹配了2条\n", "读取第2055批数据（每批100条）...\n", "批次2055: 处理了100条数据，匹配了4条\n", "读取第2056批数据（每批100条）...\n", "批次2056: 处理了100条数据，匹配了7条\n", "读取第2057批数据（每批100条）...\n", "批次2057: 处理了100条数据，匹配了3条\n", "读取第2058批数据（每批100条）...\n", "批次2058: 处理了100条数据，匹配了1条\n", "读取第2059批数据（每批100条）...\n", "批次2059: 处理了100条数据，匹配了3条\n", "读取第2060批数据（每批100条）...\n", "暂停5秒...\n", "批次2060: 处理了100条数据，匹配了4条\n", "临时保存已匹配的8869条数据...\n", "读取第2061批数据（每批100条）...\n", "批次2061: 处理了100条数据，匹配了2条\n", "读取第2062批数据（每批100条）...\n", "批次2062: 处理了100条数据，匹配了2条\n", "读取第2063批数据（每批100条）...\n", "批次2063: 处理了100条数据，匹配了6条\n", "读取第2064批数据（每批100条）...\n", "批次2064: 处理了100条数据，匹配了9条\n", "读取第2065批数据（每批100条）...\n", "批次2065: 处理了100条数据，匹配了3条\n", "读取第2066批数据（每批100条）...\n", "批次2066: 处理了100条数据，匹配了3条\n", "读取第2067批数据（每批100条）...\n", "批次2067: 处理了100条数据，匹配了6条\n", "读取第2068批数据（每批100条）...\n", "批次2068: 处理了100条数据，匹配了4条\n", "读取第2069批数据（每批100条）...\n", "批次2069: 处理了100条数据，匹配了2条\n", "读取第2070批数据（每批100条）...\n", "暂停5秒...\n", "批次2070: 处理了100条数据，匹配了6条\n", "临时保存已匹配的8912条数据...\n", "读取第2071批数据（每批100条）...\n", "批次2071: 处理了100条数据，匹配了8条\n", "读取第2072批数据（每批100条）...\n", "批次2072: 处理了100条数据，匹配了6条\n", "读取第2073批数据（每批100条）...\n", "批次2073: 处理了100条数据，匹配了3条\n", "读取第2074批数据（每批100条）...\n", "批次2074: 处理了100条数据，匹配了2条\n", "读取第2075批数据（每批100条）...\n", "批次2075: 处理了100条数据，匹配了6条\n", "读取第2076批数据（每批100条）...\n", "批次2076: 处理了100条数据，匹配了2条\n", "读取第2077批数据（每批100条）...\n", "批次2077: 处理了100条数据，匹配了0条\n", "读取第2078批数据（每批100条）...\n", "批次2078: 处理了100条数据，匹配了2条\n", "读取第2079批数据（每批100条）...\n", "批次2079: 处理了100条数据，匹配了4条\n", "读取第2080批数据（每批100条）...\n", "暂停5秒...\n", "批次2080: 处理了100条数据，匹配了5条\n", "临时保存已匹配的8950条数据...\n", "读取第2081批数据（每批100条）...\n", "批次2081: 处理了100条数据，匹配了6条\n", "读取第2082批数据（每批100条）...\n", "批次2082: 处理了100条数据，匹配了8条\n", "读取第2083批数据（每批100条）...\n", "批次2083: 处理了100条数据，匹配了9条\n", "读取第2084批数据（每批100条）...\n", "批次2084: 处理了100条数据，匹配了8条\n", "读取第2085批数据（每批100条）...\n", "批次2085: 处理了100条数据，匹配了3条\n", "读取第2086批数据（每批100条）...\n", "批次2086: 处理了100条数据，匹配了2条\n", "读取第2087批数据（每批100条）...\n", "批次2087: 处理了100条数据，匹配了3条\n", "读取第2088批数据（每批100条）...\n", "批次2088: 处理了100条数据，匹配了2条\n", "读取第2089批数据（每批100条）...\n", "批次2089: 处理了100条数据，匹配了5条\n", "读取第2090批数据（每批100条）...\n", "暂停5秒...\n", "批次2090: 处理了100条数据，匹配了4条\n", "临时保存已匹配的9000条数据...\n", "读取第2091批数据（每批100条）...\n", "批次2091: 处理了100条数据，匹配了2条\n", "读取第2092批数据（每批100条）...\n", "批次2092: 处理了100条数据，匹配了4条\n", "读取第2093批数据（每批100条）...\n", "批次2093: 处理了100条数据，匹配了1条\n", "读取第2094批数据（每批100条）...\n", "批次2094: 处理了100条数据，匹配了0条\n", "读取第2095批数据（每批100条）...\n", "批次2095: 处理了100条数据，匹配了0条\n", "读取第2096批数据（每批100条）...\n", "批次2096: 处理了100条数据，匹配了11条\n", "读取第2097批数据（每批100条）...\n", "批次2097: 处理了100条数据，匹配了5条\n", "读取第2098批数据（每批100条）...\n", "批次2098: 处理了100条数据，匹配了5条\n", "读取第2099批数据（每批100条）...\n", "批次2099: 处理了100条数据，匹配了1条\n", "读取第2100批数据（每批100条）...\n", "暂停5秒...\n", "批次2100: 处理了100条数据，匹配了8条\n", "临时保存已匹配的9037条数据...\n", "读取第2101批数据（每批100条）...\n", "批次2101: 处理了100条数据，匹配了4条\n", "读取第2102批数据（每批100条）...\n", "批次2102: 处理了100条数据，匹配了6条\n", "读取第2103批数据（每批100条）...\n", "批次2103: 处理了100条数据，匹配了6条\n", "读取第2104批数据（每批100条）...\n", "批次2104: 处理了100条数据，匹配了11条\n", "读取第2105批数据（每批100条）...\n", "批次2105: 处理了100条数据，匹配了6条\n", "读取第2106批数据（每批100条）...\n", "批次2106: 处理了100条数据，匹配了9条\n", "读取第2107批数据（每批100条）...\n", "批次2107: 处理了100条数据，匹配了0条\n", "读取第2108批数据（每批100条）...\n", "批次2108: 处理了100条数据，匹配了4条\n", "读取第2109批数据（每批100条）...\n", "批次2109: 处理了100条数据，匹配了6条\n", "读取第2110批数据（每批100条）...\n", "暂停5秒...\n", "批次2110: 处理了100条数据，匹配了4条\n", "临时保存已匹配的9093条数据...\n", "读取第2111批数据（每批100条）...\n", "批次2111: 处理了100条数据，匹配了7条\n", "读取第2112批数据（每批100条）...\n", "批次2112: 处理了100条数据，匹配了4条\n", "读取第2113批数据（每批100条）...\n", "批次2113: 处理了100条数据，匹配了6条\n", "读取第2114批数据（每批100条）...\n", "批次2114: 处理了100条数据，匹配了2条\n", "读取第2115批数据（每批100条）...\n", "批次2115: 处理了100条数据，匹配了5条\n", "读取第2116批数据（每批100条）...\n", "批次2116: 处理了100条数据，匹配了5条\n", "读取第2117批数据（每批100条）...\n", "批次2117: 处理了100条数据，匹配了4条\n", "读取第2118批数据（每批100条）...\n", "批次2118: 处理了100条数据，匹配了5条\n", "读取第2119批数据（每批100条）...\n", "批次2119: 处理了100条数据，匹配了2条\n", "读取第2120批数据（每批100条）...\n", "暂停5秒...\n", "批次2120: 处理了100条数据，匹配了6条\n", "临时保存已匹配的9139条数据...\n", "读取第2121批数据（每批100条）...\n", "批次2121: 处理了100条数据，匹配了2条\n", "读取第2122批数据（每批100条）...\n", "批次2122: 处理了100条数据，匹配了5条\n", "读取第2123批数据（每批100条）...\n", "批次2123: 处理了100条数据，匹配了7条\n", "读取第2124批数据（每批100条）...\n", "批次2124: 处理了100条数据，匹配了1条\n", "读取第2125批数据（每批100条）...\n", "批次2125: 处理了100条数据，匹配了12条\n", "读取第2126批数据（每批100条）...\n", "批次2126: 处理了100条数据，匹配了1条\n", "读取第2127批数据（每批100条）...\n", "批次2127: 处理了100条数据，匹配了5条\n", "读取第2128批数据（每批100条）...\n", "批次2128: 处理了100条数据，匹配了1条\n", "读取第2129批数据（每批100条）...\n", "批次2129: 处理了100条数据，匹配了6条\n", "读取第2130批数据（每批100条）...\n", "暂停5秒...\n", "批次2130: 处理了100条数据，匹配了4条\n", "临时保存已匹配的9183条数据...\n", "读取第2131批数据（每批100条）...\n", "批次2131: 处理了100条数据，匹配了5条\n", "读取第2132批数据（每批100条）...\n", "批次2132: 处理了100条数据，匹配了2条\n", "读取第2133批数据（每批100条）...\n", "批次2133: 处理了100条数据，匹配了7条\n", "读取第2134批数据（每批100条）...\n", "批次2134: 处理了100条数据，匹配了4条\n", "读取第2135批数据（每批100条）...\n", "批次2135: 处理了100条数据，匹配了3条\n", "读取第2136批数据（每批100条）...\n", "批次2136: 处理了100条数据，匹配了3条\n", "读取第2137批数据（每批100条）...\n", "批次2137: 处理了100条数据，匹配了6条\n", "读取第2138批数据（每批100条）...\n", "批次2138: 处理了100条数据，匹配了5条\n", "读取第2139批数据（每批100条）...\n", "批次2139: 处理了100条数据，匹配了4条\n", "读取第2140批数据（每批100条）...\n", "暂停5秒...\n", "批次2140: 处理了100条数据，匹配了3条\n", "临时保存已匹配的9225条数据...\n", "读取第2141批数据（每批100条）...\n", "批次2141: 处理了100条数据，匹配了0条\n", "读取第2142批数据（每批100条）...\n", "批次2142: 处理了100条数据，匹配了6条\n", "读取第2143批数据（每批100条）...\n", "批次2143: 处理了100条数据，匹配了4条\n", "读取第2144批数据（每批100条）...\n", "批次2144: 处理了100条数据，匹配了3条\n", "读取第2145批数据（每批100条）...\n", "批次2145: 处理了100条数据，匹配了1条\n", "读取第2146批数据（每批100条）...\n", "批次2146: 处理了100条数据，匹配了4条\n", "读取第2147批数据（每批100条）...\n", "批次2147: 处理了100条数据，匹配了1条\n", "读取第2148批数据（每批100条）...\n", "批次2148: 处理了100条数据，匹配了3条\n", "读取第2149批数据（每批100条）...\n", "批次2149: 处理了100条数据，匹配了5条\n", "读取第2150批数据（每批100条）...\n", "暂停5秒...\n", "批次2150: 处理了100条数据，匹配了6条\n", "临时保存已匹配的9258条数据...\n", "读取第2151批数据（每批100条）...\n", "批次2151: 处理了100条数据，匹配了2条\n", "读取第2152批数据（每批100条）...\n", "批次2152: 处理了100条数据，匹配了0条\n", "读取第2153批数据（每批100条）...\n", "批次2153: 处理了100条数据，匹配了2条\n", "读取第2154批数据（每批100条）...\n", "批次2154: 处理了100条数据，匹配了2条\n", "读取第2155批数据（每批100条）...\n", "批次2155: 处理了100条数据，匹配了3条\n", "读取第2156批数据（每批100条）...\n", "批次2156: 处理了100条数据，匹配了3条\n", "读取第2157批数据（每批100条）...\n", "批次2157: 处理了100条数据，匹配了2条\n", "读取第2158批数据（每批100条）...\n", "批次2158: 处理了100条数据，匹配了3条\n", "读取第2159批数据（每批100条）...\n", "批次2159: 处理了100条数据，匹配了4条\n", "读取第2160批数据（每批100条）...\n", "暂停5秒...\n", "批次2160: 处理了100条数据，匹配了5条\n", "临时保存已匹配的9284条数据...\n", "读取第2161批数据（每批100条）...\n", "批次2161: 处理了100条数据，匹配了6条\n", "读取第2162批数据（每批100条）...\n", "批次2162: 处理了100条数据，匹配了0条\n", "读取第2163批数据（每批100条）...\n", "批次2163: 处理了100条数据，匹配了9条\n", "读取第2164批数据（每批100条）...\n", "批次2164: 处理了100条数据，匹配了9条\n", "读取第2165批数据（每批100条）...\n", "批次2165: 处理了100条数据，匹配了4条\n", "读取第2166批数据（每批100条）...\n", "批次2166: 处理了100条数据，匹配了5条\n", "读取第2167批数据（每批100条）...\n", "批次2167: 处理了100条数据，匹配了4条\n", "读取第2168批数据（每批100条）...\n", "批次2168: 处理了100条数据，匹配了3条\n", "读取第2169批数据（每批100条）...\n", "批次2169: 处理了100条数据，匹配了1条\n", "读取第2170批数据（每批100条）...\n", "暂停5秒...\n", "批次2170: 处理了100条数据，匹配了5条\n", "临时保存已匹配的9330条数据...\n", "读取第2171批数据（每批100条）...\n", "批次2171: 处理了100条数据，匹配了9条\n", "读取第2172批数据（每批100条）...\n", "批次2172: 处理了100条数据，匹配了2条\n", "读取第2173批数据（每批100条）...\n", "批次2173: 处理了100条数据，匹配了2条\n", "读取第2174批数据（每批100条）...\n", "批次2174: 处理了100条数据，匹配了8条\n", "读取第2175批数据（每批100条）...\n", "批次2175: 处理了100条数据，匹配了10条\n", "读取第2176批数据（每批100条）...\n", "批次2176: 处理了100条数据，匹配了5条\n", "读取第2177批数据（每批100条）...\n", "批次2177: 处理了100条数据，匹配了7条\n", "读取第2178批数据（每批100条）...\n", "批次2178: 处理了100条数据，匹配了6条\n", "读取第2179批数据（每批100条）...\n", "批次2179: 处理了100条数据，匹配了5条\n", "读取第2180批数据（每批100条）...\n", "暂停5秒...\n", "批次2180: 处理了100条数据，匹配了3条\n", "临时保存已匹配的9387条数据...\n", "读取第2181批数据（每批100条）...\n", "批次2181: 处理了100条数据，匹配了8条\n", "读取第2182批数据（每批100条）...\n", "批次2182: 处理了100条数据，匹配了4条\n", "读取第2183批数据（每批100条）...\n", "批次2183: 处理了100条数据，匹配了3条\n", "读取第2184批数据（每批100条）...\n", "批次2184: 处理了100条数据，匹配了3条\n", "读取第2185批数据（每批100条）...\n", "批次2185: 处理了100条数据，匹配了6条\n", "读取第2186批数据（每批100条）...\n", "批次2186: 处理了100条数据，匹配了2条\n", "读取第2187批数据（每批100条）...\n", "批次2187: 处理了100条数据，匹配了4条\n", "读取第2188批数据（每批100条）...\n", "批次2188: 处理了100条数据，匹配了8条\n", "读取第2189批数据（每批100条）...\n", "批次2189: 处理了100条数据，匹配了2条\n", "读取第2190批数据（每批100条）...\n", "暂停5秒...\n", "批次2190: 处理了100条数据，匹配了6条\n", "临时保存已匹配的9433条数据...\n", "读取第2191批数据（每批100条）...\n", "批次2191: 处理了100条数据，匹配了5条\n", "读取第2192批数据（每批100条）...\n", "批次2192: 处理了100条数据，匹配了14条\n", "读取第2193批数据（每批100条）...\n", "批次2193: 处理了100条数据，匹配了4条\n", "读取第2194批数据（每批100条）...\n", "批次2194: 处理了100条数据，匹配了4条\n", "读取第2195批数据（每批100条）...\n", "批次2195: 处理了100条数据，匹配了4条\n", "读取第2196批数据（每批100条）...\n", "批次2196: 处理了100条数据，匹配了4条\n", "读取第2197批数据（每批100条）...\n", "批次2197: 处理了100条数据，匹配了1条\n", "读取第2198批数据（每批100条）...\n", "批次2198: 处理了100条数据，匹配了5条\n", "读取第2199批数据（每批100条）...\n", "批次2199: 处理了100条数据，匹配了6条\n", "读取第2200批数据（每批100条）...\n", "暂停5秒...\n", "批次2200: 处理了100条数据，匹配了25条\n", "临时保存已匹配的9505条数据...\n", "读取第2201批数据（每批100条）...\n", "批次2201: 处理了100条数据，匹配了3条\n", "读取第2202批数据（每批100条）...\n", "批次2202: 处理了100条数据，匹配了11条\n", "读取第2203批数据（每批100条）...\n", "批次2203: 处理了100条数据，匹配了6条\n", "读取第2204批数据（每批100条）...\n", "批次2204: 处理了100条数据，匹配了0条\n", "读取第2205批数据（每批100条）...\n", "批次2205: 处理了100条数据，匹配了6条\n", "读取第2206批数据（每批100条）...\n", "批次2206: 处理了100条数据，匹配了0条\n", "读取第2207批数据（每批100条）...\n", "批次2207: 处理了100条数据，匹配了22条\n", "读取第2208批数据（每批100条）...\n", "批次2208: 处理了100条数据，匹配了16条\n", "读取第2209批数据（每批100条）...\n", "批次2209: 处理了100条数据，匹配了1条\n", "读取第2210批数据（每批100条）...\n", "暂停5秒...\n", "批次2210: 处理了100条数据，匹配了1条\n", "临时保存已匹配的9571条数据...\n", "读取第2211批数据（每批100条）...\n", "批次2211: 处理了100条数据，匹配了2条\n", "读取第2212批数据（每批100条）...\n", "批次2212: 处理了100条数据，匹配了3条\n", "读取第2213批数据（每批100条）...\n", "批次2213: 处理了100条数据，匹配了6条\n", "读取第2214批数据（每批100条）...\n", "批次2214: 处理了100条数据，匹配了5条\n", "读取第2215批数据（每批100条）...\n", "批次2215: 处理了100条数据，匹配了3条\n", "读取第2216批数据（每批100条）...\n", "批次2216: 处理了100条数据，匹配了1条\n", "读取第2217批数据（每批100条）...\n", "批次2217: 处理了100条数据，匹配了4条\n", "读取第2218批数据（每批100条）...\n", "批次2218: 处理了100条数据，匹配了6条\n", "读取第2219批数据（每批100条）...\n", "批次2219: 处理了100条数据，匹配了3条\n", "读取第2220批数据（每批100条）...\n", "暂停5秒...\n", "批次2220: 处理了100条数据，匹配了5条\n", "临时保存已匹配的9609条数据...\n", "读取第2221批数据（每批100条）...\n", "批次2221: 处理了100条数据，匹配了3条\n", "读取第2222批数据（每批100条）...\n", "批次2222: 处理了100条数据，匹配了9条\n", "读取第2223批数据（每批100条）...\n", "批次2223: 处理了100条数据，匹配了2条\n", "读取第2224批数据（每批100条）...\n", "批次2224: 处理了100条数据，匹配了6条\n", "读取第2225批数据（每批100条）...\n", "批次2225: 处理了100条数据，匹配了5条\n", "读取第2226批数据（每批100条）...\n", "批次2226: 处理了100条数据，匹配了6条\n", "读取第2227批数据（每批100条）...\n", "批次2227: 处理了100条数据，匹配了2条\n", "读取第2228批数据（每批100条）...\n", "批次2228: 处理了100条数据，匹配了2条\n", "读取第2229批数据（每批100条）...\n", "批次2229: 处理了100条数据，匹配了1条\n", "读取第2230批数据（每批100条）...\n", "暂停5秒...\n", "批次2230: 处理了100条数据，匹配了7条\n", "临时保存已匹配的9652条数据...\n", "读取第2231批数据（每批100条）...\n", "批次2231: 处理了100条数据，匹配了1条\n", "读取第2232批数据（每批100条）...\n", "批次2232: 处理了100条数据，匹配了6条\n", "读取第2233批数据（每批100条）...\n", "批次2233: 处理了100条数据，匹配了2条\n", "读取第2234批数据（每批100条）...\n", "批次2234: 处理了100条数据，匹配了5条\n", "读取第2235批数据（每批100条）...\n", "批次2235: 处理了100条数据，匹配了5条\n", "读取第2236批数据（每批100条）...\n", "批次2236: 处理了100条数据，匹配了3条\n", "读取第2237批数据（每批100条）...\n", "批次2237: 处理了100条数据，匹配了3条\n", "读取第2238批数据（每批100条）...\n", "批次2238: 处理了100条数据，匹配了0条\n", "读取第2239批数据（每批100条）...\n", "批次2239: 处理了100条数据，匹配了2条\n", "读取第2240批数据（每批100条）...\n", "暂停5秒...\n", "批次2240: 处理了100条数据，匹配了4条\n", "临时保存已匹配的9683条数据...\n", "读取第2241批数据（每批100条）...\n", "批次2241: 处理了100条数据，匹配了0条\n", "读取第2242批数据（每批100条）...\n", "批次2242: 处理了100条数据，匹配了2条\n", "读取第2243批数据（每批100条）...\n", "批次2243: 处理了100条数据，匹配了8条\n", "读取第2244批数据（每批100条）...\n", "批次2244: 处理了100条数据，匹配了9条\n", "读取第2245批数据（每批100条）...\n", "批次2245: 处理了100条数据，匹配了4条\n", "读取第2246批数据（每批100条）...\n", "批次2246: 处理了100条数据，匹配了5条\n", "读取第2247批数据（每批100条）...\n", "批次2247: 处理了100条数据，匹配了1条\n", "读取第2248批数据（每批100条）...\n", "批次2248: 处理了100条数据，匹配了7条\n", "读取第2249批数据（每批100条）...\n", "批次2249: 处理了100条数据，匹配了3条\n", "读取第2250批数据（每批100条）...\n", "暂停5秒...\n", "批次2250: 处理了100条数据，匹配了3条\n", "临时保存已匹配的9725条数据...\n", "读取第2251批数据（每批100条）...\n", "批次2251: 处理了100条数据，匹配了6条\n", "读取第2252批数据（每批100条）...\n", "批次2252: 处理了100条数据，匹配了8条\n", "读取第2253批数据（每批100条）...\n", "批次2253: 处理了100条数据，匹配了2条\n", "读取第2254批数据（每批100条）...\n", "批次2254: 处理了100条数据，匹配了7条\n", "读取第2255批数据（每批100条）...\n", "批次2255: 处理了100条数据，匹配了6条\n", "读取第2256批数据（每批100条）...\n", "批次2256: 处理了100条数据，匹配了3条\n", "读取第2257批数据（每批100条）...\n", "批次2257: 处理了100条数据，匹配了5条\n", "读取第2258批数据（每批100条）...\n", "批次2258: 处理了100条数据，匹配了1条\n", "读取第2259批数据（每批100条）...\n", "批次2259: 处理了100条数据，匹配了4条\n", "读取第2260批数据（每批100条）...\n", "暂停5秒...\n", "批次2260: 处理了100条数据，匹配了4条\n", "临时保存已匹配的9771条数据...\n", "读取第2261批数据（每批100条）...\n", "批次2261: 处理了100条数据，匹配了3条\n", "读取第2262批数据（每批100条）...\n", "批次2262: 处理了100条数据，匹配了2条\n", "读取第2263批数据（每批100条）...\n", "批次2263: 处理了100条数据，匹配了4条\n", "读取第2264批数据（每批100条）...\n", "批次2264: 处理了100条数据，匹配了5条\n", "读取第2265批数据（每批100条）...\n", "批次2265: 处理了100条数据，匹配了2条\n", "读取第2266批数据（每批100条）...\n", "批次2266: 处理了100条数据，匹配了5条\n", "读取第2267批数据（每批100条）...\n", "批次2267: 处理了100条数据，匹配了5条\n", "读取第2268批数据（每批100条）...\n", "批次2268: 处理了100条数据，匹配了1条\n", "读取第2269批数据（每批100条）...\n", "批次2269: 处理了100条数据，匹配了6条\n", "读取第2270批数据（每批100条）...\n", "暂停5秒...\n", "批次2270: 处理了100条数据，匹配了14条\n", "临时保存已匹配的9818条数据...\n", "读取第2271批数据（每批100条）...\n", "批次2271: 处理了100条数据，匹配了7条\n", "读取第2272批数据（每批100条）...\n", "批次2272: 处理了100条数据，匹配了5条\n", "读取第2273批数据（每批100条）...\n", "批次2273: 处理了100条数据，匹配了6条\n", "读取第2274批数据（每批100条）...\n", "批次2274: 处理了100条数据，匹配了10条\n", "读取第2275批数据（每批100条）...\n", "批次2275: 处理了100条数据，匹配了3条\n", "读取第2276批数据（每批100条）...\n", "批次2276: 处理了100条数据，匹配了7条\n", "读取第2277批数据（每批100条）...\n", "批次2277: 处理了100条数据，匹配了2条\n", "读取第2278批数据（每批100条）...\n", "批次2278: 处理了100条数据，匹配了1条\n", "读取第2279批数据（每批100条）...\n", "批次2279: 处理了100条数据，匹配了2条\n", "读取第2280批数据（每批100条）...\n", "暂停5秒...\n", "批次2280: 处理了100条数据，匹配了5条\n", "临时保存已匹配的9866条数据...\n", "读取第2281批数据（每批100条）...\n", "批次2281: 处理了100条数据，匹配了3条\n", "读取第2282批数据（每批100条）...\n", "批次2282: 处理了100条数据，匹配了2条\n", "读取第2283批数据（每批100条）...\n", "批次2283: 处理了100条数据，匹配了5条\n", "读取第2284批数据（每批100条）...\n", "批次2284: 处理了100条数据，匹配了4条\n", "读取第2285批数据（每批100条）...\n", "批次2285: 处理了100条数据，匹配了9条\n", "读取第2286批数据（每批100条）...\n", "批次2286: 处理了100条数据，匹配了4条\n", "读取第2287批数据（每批100条）...\n", "批次2287: 处理了100条数据，匹配了2条\n", "读取第2288批数据（每批100条）...\n", "批次2288: 处理了100条数据，匹配了5条\n", "读取第2289批数据（每批100条）...\n", "批次2289: 处理了100条数据，匹配了4条\n", "读取第2290批数据（每批100条）...\n", "暂停5秒...\n", "批次2290: 处理了100条数据，匹配了0条\n", "临时保存已匹配的9904条数据...\n", "读取第2291批数据（每批100条）...\n", "批次2291: 处理了100条数据，匹配了2条\n", "读取第2292批数据（每批100条）...\n", "批次2292: 处理了100条数据，匹配了3条\n", "读取第2293批数据（每批100条）...\n", "批次2293: 处理了100条数据，匹配了1条\n", "读取第2294批数据（每批100条）...\n", "批次2294: 处理了100条数据，匹配了4条\n", "读取第2295批数据（每批100条）...\n", "批次2295: 处理了100条数据，匹配了3条\n", "读取第2296批数据（每批100条）...\n", "批次2296: 处理了100条数据，匹配了2条\n", "读取第2297批数据（每批100条）...\n", "批次2297: 处理了100条数据，匹配了7条\n", "读取第2298批数据（每批100条）...\n", "批次2298: 处理了100条数据，匹配了4条\n", "读取第2299批数据（每批100条）...\n", "批次2299: 处理了100条数据，匹配了2条\n", "读取第2300批数据（每批100条）...\n", "暂停5秒...\n", "批次2300: 处理了100条数据，匹配了0条\n", "临时保存已匹配的9932条数据...\n", "读取第2301批数据（每批100条）...\n", "批次2301: 处理了100条数据，匹配了0条\n", "读取第2302批数据（每批100条）...\n", "批次2302: 处理了100条数据，匹配了0条\n", "读取第2303批数据（每批100条）...\n", "批次2303: 处理了100条数据，匹配了1条\n", "读取第2304批数据（每批100条）...\n", "批次2304: 处理了100条数据，匹配了2条\n", "读取第2305批数据（每批100条）...\n", "批次2305: 处理了100条数据，匹配了1条\n", "读取第2306批数据（每批100条）...\n", "批次2306: 处理了100条数据，匹配了2条\n", "读取第2307批数据（每批100条）...\n", "批次2307: 处理了100条数据，匹配了1条\n", "读取第2308批数据（每批100条）...\n", "批次2308: 处理了100条数据，匹配了0条\n", "读取第2309批数据（每批100条）...\n", "批次2309: 处理了100条数据，匹配了1条\n", "读取第2310批数据（每批100条）...\n", "暂停5秒...\n", "批次2310: 处理了100条数据，匹配了8条\n", "临时保存已匹配的9948条数据...\n", "读取第2311批数据（每批100条）...\n", "批次2311: 处理了100条数据，匹配了1条\n", "读取第2312批数据（每批100条）...\n", "批次2312: 处理了100条数据，匹配了6条\n", "读取第2313批数据（每批100条）...\n", "批次2313: 处理了100条数据，匹配了7条\n", "读取第2314批数据（每批100条）...\n", "批次2314: 处理了100条数据，匹配了9条\n", "读取第2315批数据（每批100条）...\n", "批次2315: 处理了100条数据，匹配了4条\n", "读取第2316批数据（每批100条）...\n", "批次2316: 处理了100条数据，匹配了0条\n", "读取第2317批数据（每批100条）...\n", "批次2317: 处理了100条数据，匹配了16条\n", "读取第2318批数据（每批100条）...\n", "批次2318: 处理了100条数据，匹配了5条\n", "读取第2319批数据（每批100条）...\n", "批次2319: 处理了100条数据，匹配了7条\n", "读取第2320批数据（每批100条）...\n", "暂停5秒...\n", "批次2320: 处理了100条数据，匹配了1条\n", "临时保存已匹配的10004条数据...\n", "读取第2321批数据（每批100条）...\n", "批次2321: 处理了100条数据，匹配了5条\n", "读取第2322批数据（每批100条）...\n", "批次2322: 处理了100条数据，匹配了6条\n", "读取第2323批数据（每批100条）...\n", "批次2323: 处理了100条数据，匹配了1条\n", "读取第2324批数据（每批100条）...\n", "批次2324: 处理了100条数据，匹配了3条\n", "读取第2325批数据（每批100条）...\n", "批次2325: 处理了100条数据，匹配了3条\n", "读取第2326批数据（每批100条）...\n", "批次2326: 处理了100条数据，匹配了2条\n", "读取第2327批数据（每批100条）...\n", "批次2327: 处理了100条数据，匹配了1条\n", "读取第2328批数据（每批100条）...\n", "批次2328: 处理了100条数据，匹配了5条\n", "读取第2329批数据（每批100条）...\n", "批次2329: 处理了100条数据，匹配了23条\n", "读取第2330批数据（每批100条）...\n", "暂停5秒...\n", "批次2330: 处理了100条数据，匹配了0条\n", "临时保存已匹配的10053条数据...\n", "读取第2331批数据（每批100条）...\n", "批次2331: 处理了100条数据，匹配了1条\n", "读取第2332批数据（每批100条）...\n", "批次2332: 处理了100条数据，匹配了5条\n", "读取第2333批数据（每批100条）...\n", "批次2333: 处理了100条数据，匹配了4条\n", "读取第2334批数据（每批100条）...\n", "批次2334: 处理了100条数据，匹配了7条\n", "读取第2335批数据（每批100条）...\n", "批次2335: 处理了100条数据，匹配了4条\n", "读取第2336批数据（每批100条）...\n", "批次2336: 处理了100条数据，匹配了1条\n", "读取第2337批数据（每批100条）...\n", "批次2337: 处理了100条数据，匹配了2条\n", "读取第2338批数据（每批100条）...\n", "批次2338: 处理了100条数据，匹配了10条\n", "读取第2339批数据（每批100条）...\n", "批次2339: 处理了100条数据，匹配了8条\n", "读取第2340批数据（每批100条）...\n", "暂停5秒...\n", "批次2340: 处理了100条数据，匹配了2条\n", "临时保存已匹配的10097条数据...\n", "读取第2341批数据（每批100条）...\n", "批次2341: 处理了100条数据，匹配了0条\n", "读取第2342批数据（每批100条）...\n", "批次2342: 处理了100条数据，匹配了3条\n", "读取第2343批数据（每批100条）...\n", "批次2343: 处理了100条数据，匹配了0条\n", "读取第2344批数据（每批100条）...\n", "批次2344: 处理了100条数据，匹配了6条\n", "读取第2345批数据（每批100条）...\n", "批次2345: 处理了100条数据，匹配了0条\n", "读取第2346批数据（每批100条）...\n", "批次2346: 处理了100条数据，匹配了17条\n", "读取第2347批数据（每批100条）...\n", "批次2347: 处理了100条数据，匹配了1条\n", "读取第2348批数据（每批100条）...\n", "批次2348: 处理了100条数据，匹配了1条\n", "读取第2349批数据（每批100条）...\n", "批次2349: 处理了100条数据，匹配了1条\n", "读取第2350批数据（每批100条）...\n", "暂停5秒...\n", "批次2350: 处理了100条数据，匹配了3条\n", "临时保存已匹配的10129条数据...\n", "读取第2351批数据（每批100条）...\n", "批次2351: 处理了100条数据，匹配了23条\n", "读取第2352批数据（每批100条）...\n", "批次2352: 处理了100条数据，匹配了2条\n", "读取第2353批数据（每批100条）...\n", "批次2353: 处理了100条数据，匹配了18条\n", "读取第2354批数据（每批100条）...\n", "批次2354: 处理了100条数据，匹配了3条\n", "读取第2355批数据（每批100条）...\n", "批次2355: 处理了100条数据，匹配了0条\n", "读取第2356批数据（每批100条）...\n", "批次2356: 处理了100条数据，匹配了9条\n", "读取第2357批数据（每批100条）...\n", "批次2357: 处理了100条数据，匹配了6条\n", "读取第2358批数据（每批100条）...\n", "批次2358: 处理了100条数据，匹配了2条\n", "读取第2359批数据（每批100条）...\n", "批次2359: 处理了100条数据，匹配了1条\n", "读取第2360批数据（每批100条）...\n", "暂停5秒...\n", "批次2360: 处理了100条数据，匹配了16条\n", "临时保存已匹配的10209条数据...\n", "读取第2361批数据（每批100条）...\n", "批次2361: 处理了100条数据，匹配了3条\n", "读取第2362批数据（每批100条）...\n", "批次2362: 处理了100条数据，匹配了3条\n", "读取第2363批数据（每批100条）...\n", "批次2363: 处理了100条数据，匹配了4条\n", "读取第2364批数据（每批100条）...\n", "批次2364: 处理了100条数据，匹配了5条\n", "读取第2365批数据（每批100条）...\n", "批次2365: 处理了100条数据，匹配了2条\n", "读取第2366批数据（每批100条）...\n", "批次2366: 处理了100条数据，匹配了3条\n", "读取第2367批数据（每批100条）...\n", "批次2367: 处理了100条数据，匹配了1条\n", "读取第2368批数据（每批100条）...\n", "批次2368: 处理了100条数据，匹配了3条\n", "读取第2369批数据（每批100条）...\n", "批次2369: 处理了100条数据，匹配了0条\n", "读取第2370批数据（每批100条）...\n", "暂停5秒...\n", "批次2370: 处理了100条数据，匹配了1条\n", "临时保存已匹配的10234条数据...\n", "读取第2371批数据（每批100条）...\n", "批次2371: 处理了100条数据，匹配了2条\n", "读取第2372批数据（每批100条）...\n", "批次2372: 处理了100条数据，匹配了1条\n", "读取第2373批数据（每批100条）...\n", "批次2373: 处理了100条数据，匹配了1条\n", "读取第2374批数据（每批100条）...\n", "批次2374: 处理了100条数据，匹配了4条\n", "读取第2375批数据（每批100条）...\n", "批次2375: 处理了100条数据，匹配了5条\n", "读取第2376批数据（每批100条）...\n", "批次2376: 处理了100条数据，匹配了3条\n", "读取第2377批数据（每批100条）...\n", "批次2377: 处理了100条数据，匹配了10条\n", "读取第2378批数据（每批100条）...\n", "批次2378: 处理了100条数据，匹配了5条\n", "读取第2379批数据（每批100条）...\n", "批次2379: 处理了100条数据，匹配了0条\n", "读取第2380批数据（每批100条）...\n", "暂停5秒...\n", "批次2380: 处理了100条数据，匹配了2条\n", "临时保存已匹配的10267条数据...\n", "读取第2381批数据（每批100条）...\n", "批次2381: 处理了100条数据，匹配了4条\n", "读取第2382批数据（每批100条）...\n", "批次2382: 处理了100条数据，匹配了3条\n", "读取第2383批数据（每批100条）...\n", "批次2383: 处理了100条数据，匹配了2条\n", "读取第2384批数据（每批100条）...\n", "批次2384: 处理了100条数据，匹配了7条\n", "读取第2385批数据（每批100条）...\n", "批次2385: 处理了100条数据，匹配了2条\n", "读取第2386批数据（每批100条）...\n", "批次2386: 处理了100条数据，匹配了1条\n", "读取第2387批数据（每批100条）...\n", "批次2387: 处理了100条数据，匹配了3条\n", "读取第2388批数据（每批100条）...\n", "批次2388: 处理了100条数据，匹配了1条\n", "读取第2389批数据（每批100条）...\n", "批次2389: 处理了100条数据，匹配了3条\n", "读取第2390批数据（每批100条）...\n", "暂停5秒...\n", "批次2390: 处理了100条数据，匹配了2条\n", "临时保存已匹配的10295条数据...\n", "读取第2391批数据（每批100条）...\n", "批次2391: 处理了100条数据，匹配了0条\n", "读取第2392批数据（每批100条）...\n", "批次2392: 处理了100条数据，匹配了8条\n", "读取第2393批数据（每批100条）...\n", "批次2393: 处理了100条数据，匹配了6条\n", "读取第2394批数据（每批100条）...\n", "批次2394: 处理了100条数据，匹配了7条\n", "读取第2395批数据（每批100条）...\n", "批次2395: 处理了100条数据，匹配了2条\n", "读取第2396批数据（每批100条）...\n", "批次2396: 处理了100条数据，匹配了3条\n", "读取第2397批数据（每批100条）...\n", "批次2397: 处理了100条数据，匹配了4条\n", "读取第2398批数据（每批100条）...\n", "批次2398: 处理了100条数据，匹配了2条\n", "读取第2399批数据（每批100条）...\n", "批次2399: 处理了100条数据，匹配了3条\n", "读取第2400批数据（每批100条）...\n", "暂停5秒...\n", "批次2400: 处理了100条数据，匹配了3条\n", "临时保存已匹配的10333条数据...\n", "读取第2401批数据（每批100条）...\n", "批次2401: 处理了100条数据，匹配了6条\n", "读取第2402批数据（每批100条）...\n", "批次2402: 处理了100条数据，匹配了6条\n", "读取第2403批数据（每批100条）...\n", "批次2403: 处理了100条数据，匹配了5条\n", "读取第2404批数据（每批100条）...\n", "批次2404: 处理了100条数据，匹配了5条\n", "读取第2405批数据（每批100条）...\n", "批次2405: 处理了100条数据，匹配了5条\n", "读取第2406批数据（每批100条）...\n", "批次2406: 处理了100条数据，匹配了5条\n", "读取第2407批数据（每批100条）...\n", "批次2407: 处理了100条数据，匹配了2条\n", "读取第2408批数据（每批100条）...\n", "批次2408: 处理了100条数据，匹配了3条\n", "读取第2409批数据（每批100条）...\n", "批次2409: 处理了100条数据，匹配了0条\n", "读取第2410批数据（每批100条）...\n", "暂停5秒...\n", "批次2410: 处理了100条数据，匹配了11条\n", "临时保存已匹配的10381条数据...\n", "读取第2411批数据（每批100条）...\n", "批次2411: 处理了100条数据，匹配了0条\n", "读取第2412批数据（每批100条）...\n", "批次2412: 处理了100条数据，匹配了0条\n", "读取第2413批数据（每批100条）...\n", "批次2413: 处理了100条数据，匹配了3条\n", "读取第2414批数据（每批100条）...\n", "批次2414: 处理了100条数据，匹配了8条\n", "读取第2415批数据（每批100条）...\n", "批次2415: 处理了100条数据，匹配了13条\n", "读取第2416批数据（每批100条）...\n", "批次2416: 处理了100条数据，匹配了14条\n", "读取第2417批数据（每批100条）...\n", "批次2417: 处理了100条数据，匹配了8条\n", "读取第2418批数据（每批100条）...\n", "批次2418: 处理了100条数据，匹配了2条\n", "读取第2419批数据（每批100条）...\n", "批次2419: 处理了100条数据，匹配了0条\n", "读取第2420批数据（每批100条）...\n", "暂停5秒...\n", "批次2420: 处理了100条数据，匹配了0条\n", "临时保存已匹配的10429条数据...\n", "读取第2421批数据（每批100条）...\n", "批次2421: 处理了100条数据，匹配了6条\n", "读取第2422批数据（每批100条）...\n", "批次2422: 处理了100条数据，匹配了5条\n", "读取第2423批数据（每批100条）...\n", "批次2423: 处理了100条数据，匹配了10条\n", "读取第2424批数据（每批100条）...\n", "批次2424: 处理了100条数据，匹配了12条\n", "读取第2425批数据（每批100条）...\n", "批次2425: 处理了100条数据，匹配了0条\n", "读取第2426批数据（每批100条）...\n", "批次2426: 处理了100条数据，匹配了0条\n", "读取第2427批数据（每批100条）...\n", "批次2427: 处理了100条数据，匹配了17条\n", "读取第2428批数据（每批100条）...\n", "批次2428: 处理了100条数据，匹配了15条\n", "读取第2429批数据（每批100条）...\n", "批次2429: 处理了100条数据，匹配了1条\n", "读取第2430批数据（每批100条）...\n", "暂停5秒...\n", "批次2430: 处理了100条数据，匹配了2条\n", "临时保存已匹配的10497条数据...\n", "读取第2431批数据（每批100条）...\n", "批次2431: 处理了100条数据，匹配了0条\n", "读取第2432批数据（每批100条）...\n", "批次2432: 处理了100条数据，匹配了6条\n", "读取第2433批数据（每批100条）...\n", "批次2433: 处理了100条数据，匹配了7条\n", "读取第2434批数据（每批100条）...\n", "批次2434: 处理了100条数据，匹配了1条\n", "读取第2435批数据（每批100条）...\n", "批次2435: 处理了100条数据，匹配了5条\n", "读取第2436批数据（每批100条）...\n", "批次2436: 处理了100条数据，匹配了2条\n", "读取第2437批数据（每批100条）...\n", "批次2437: 处理了100条数据，匹配了3条\n", "读取第2438批数据（每批100条）...\n", "批次2438: 处理了100条数据，匹配了6条\n", "读取第2439批数据（每批100条）...\n", "批次2439: 处理了100条数据，匹配了8条\n", "读取第2440批数据（每批100条）...\n", "暂停5秒...\n", "批次2440: 处理了100条数据，匹配了7条\n", "临时保存已匹配的10542条数据...\n", "读取第2441批数据（每批100条）...\n", "批次2441: 处理了100条数据，匹配了0条\n", "读取第2442批数据（每批100条）...\n", "批次2442: 处理了100条数据，匹配了1条\n", "读取第2443批数据（每批100条）...\n", "批次2443: 处理了100条数据，匹配了8条\n", "读取第2444批数据（每批100条）...\n", "批次2444: 处理了100条数据，匹配了5条\n", "读取第2445批数据（每批100条）...\n", "批次2445: 处理了100条数据，匹配了3条\n", "读取第2446批数据（每批100条）...\n", "批次2446: 处理了100条数据，匹配了4条\n", "读取第2447批数据（每批100条）...\n", "批次2447: 处理了100条数据，匹配了3条\n", "读取第2448批数据（每批100条）...\n", "批次2448: 处理了100条数据，匹配了3条\n", "读取第2449批数据（每批100条）...\n", "批次2449: 处理了100条数据，匹配了3条\n", "读取第2450批数据（每批100条）...\n", "暂停5秒...\n", "批次2450: 处理了100条数据，匹配了0条\n", "临时保存已匹配的10572条数据...\n", "读取第2451批数据（每批100条）...\n", "批次2451: 处理了100条数据，匹配了7条\n", "读取第2452批数据（每批100条）...\n", "批次2452: 处理了100条数据，匹配了3条\n", "读取第2453批数据（每批100条）...\n", "批次2453: 处理了100条数据，匹配了0条\n", "读取第2454批数据（每批100条）...\n", "批次2454: 处理了100条数据，匹配了3条\n", "读取第2455批数据（每批100条）...\n", "批次2455: 处理了100条数据，匹配了0条\n", "读取第2456批数据（每批100条）...\n", "批次2456: 处理了100条数据，匹配了1条\n", "读取第2457批数据（每批100条）...\n", "批次2457: 处理了100条数据，匹配了4条\n", "读取第2458批数据（每批100条）...\n", "批次2458: 处理了100条数据，匹配了4条\n", "读取第2459批数据（每批100条）...\n", "批次2459: 处理了100条数据，匹配了1条\n", "读取第2460批数据（每批100条）...\n", "暂停5秒...\n", "批次2460: 处理了100条数据，匹配了8条\n", "临时保存已匹配的10603条数据...\n", "读取第2461批数据（每批100条）...\n", "批次2461: 处理了100条数据，匹配了1条\n", "读取第2462批数据（每批100条）...\n", "批次2462: 处理了100条数据，匹配了0条\n", "读取第2463批数据（每批100条）...\n", "批次2463: 处理了100条数据，匹配了4条\n", "读取第2464批数据（每批100条）...\n", "批次2464: 处理了100条数据，匹配了5条\n", "读取第2465批数据（每批100条）...\n", "批次2465: 处理了100条数据，匹配了4条\n", "读取第2466批数据（每批100条）...\n", "批次2466: 处理了100条数据，匹配了7条\n", "读取第2467批数据（每批100条）...\n", "批次2467: 处理了100条数据，匹配了9条\n", "读取第2468批数据（每批100条）...\n", "批次2468: 处理了100条数据，匹配了5条\n", "读取第2469批数据（每批100条）...\n", "批次2469: 处理了100条数据，匹配了0条\n", "读取第2470批数据（每批100条）...\n", "暂停5秒...\n", "批次2470: 处理了100条数据，匹配了7条\n", "临时保存已匹配的10645条数据...\n", "读取第2471批数据（每批100条）...\n", "批次2471: 处理了100条数据，匹配了1条\n", "读取第2472批数据（每批100条）...\n", "批次2472: 处理了100条数据，匹配了2条\n", "读取第2473批数据（每批100条）...\n", "批次2473: 处理了100条数据，匹配了2条\n", "读取第2474批数据（每批100条）...\n", "批次2474: 处理了100条数据，匹配了7条\n", "读取第2475批数据（每批100条）...\n", "批次2475: 处理了100条数据，匹配了5条\n", "读取第2476批数据（每批100条）...\n", "批次2476: 处理了100条数据，匹配了6条\n", "读取第2477批数据（每批100条）...\n", "批次2477: 处理了100条数据，匹配了0条\n", "读取第2478批数据（每批100条）...\n", "批次2478: 处理了100条数据，匹配了4条\n", "读取第2479批数据（每批100条）...\n", "批次2479: 处理了100条数据，匹配了2条\n", "读取第2480批数据（每批100条）...\n", "暂停5秒...\n", "批次2480: 处理了100条数据，匹配了1条\n", "临时保存已匹配的10675条数据...\n", "读取第2481批数据（每批100条）...\n", "批次2481: 处理了100条数据，匹配了2条\n", "读取第2482批数据（每批100条）...\n", "批次2482: 处理了100条数据，匹配了2条\n", "读取第2483批数据（每批100条）...\n", "批次2483: 处理了100条数据，匹配了0条\n", "读取第2484批数据（每批100条）...\n", "批次2484: 处理了100条数据，匹配了1条\n", "读取第2485批数据（每批100条）...\n", "批次2485: 处理了100条数据，匹配了0条\n", "读取第2486批数据（每批100条）...\n", "批次2486: 处理了100条数据，匹配了1条\n", "读取第2487批数据（每批100条）...\n", "批次2487: 处理了100条数据，匹配了2条\n", "读取第2488批数据（每批100条）...\n", "批次2488: 处理了100条数据，匹配了4条\n", "读取第2489批数据（每批100条）...\n", "批次2489: 处理了100条数据，匹配了4条\n", "读取第2490批数据（每批100条）...\n", "暂停5秒...\n", "批次2490: 处理了100条数据，匹配了4条\n", "临时保存已匹配的10695条数据...\n", "读取第2491批数据（每批100条）...\n", "批次2491: 处理了100条数据，匹配了23条\n", "读取第2492批数据（每批100条）...\n", "批次2492: 处理了100条数据，匹配了4条\n", "读取第2493批数据（每批100条）...\n", "批次2493: 处理了100条数据，匹配了1条\n", "读取第2494批数据（每批100条）...\n", "批次2494: 处理了100条数据，匹配了1条\n", "读取第2495批数据（每批100条）...\n", "批次2495: 处理了100条数据，匹配了1条\n", "读取第2496批数据（每批100条）...\n", "批次2496: 处理了100条数据，匹配了12条\n", "读取第2497批数据（每批100条）...\n", "批次2497: 处理了100条数据，匹配了0条\n", "读取第2498批数据（每批100条）...\n", "批次2498: 处理了100条数据，匹配了3条\n", "读取第2499批数据（每批100条）...\n", "批次2499: 处理了100条数据，匹配了1条\n", "读取第2500批数据（每批100条）...\n", "暂停5秒...\n", "批次2500: 处理了100条数据，匹配了3条\n", "临时保存已匹配的10744条数据...\n", "读取第2501批数据（每批100条）...\n", "批次2501: 处理了100条数据，匹配了4条\n", "读取第2502批数据（每批100条）...\n", "批次2502: 处理了100条数据，匹配了0条\n", "读取第2503批数据（每批100条）...\n", "批次2503: 处理了100条数据，匹配了1条\n", "读取第2504批数据（每批100条）...\n", "批次2504: 处理了100条数据，匹配了1条\n", "读取第2505批数据（每批100条）...\n", "批次2505: 处理了100条数据，匹配了2条\n", "读取第2506批数据（每批100条）...\n", "批次2506: 处理了100条数据，匹配了1条\n", "读取第2507批数据（每批100条）...\n", "批次2507: 处理了100条数据，匹配了1条\n", "读取第2508批数据（每批100条）...\n", "批次2508: 处理了100条数据，匹配了1条\n", "读取第2509批数据（每批100条）...\n", "批次2509: 处理了100条数据，匹配了1条\n", "读取第2510批数据（每批100条）...\n", "暂停5秒...\n", "批次2510: 处理了100条数据，匹配了1条\n", "临时保存已匹配的10757条数据...\n", "读取第2511批数据（每批100条）...\n", "批次2511: 处理了100条数据，匹配了3条\n", "读取第2512批数据（每批100条）...\n", "批次2512: 处理了100条数据，匹配了1条\n", "读取第2513批数据（每批100条）...\n", "批次2513: 处理了100条数据，匹配了0条\n", "读取第2514批数据（每批100条）...\n", "批次2514: 处理了100条数据，匹配了1条\n", "读取第2515批数据（每批100条）...\n", "批次2515: 处理了100条数据，匹配了0条\n", "读取第2516批数据（每批100条）...\n", "批次2516: 处理了100条数据，匹配了6条\n", "读取第2517批数据（每批100条）...\n", "批次2517: 处理了100条数据，匹配了8条\n", "读取第2518批数据（每批100条）...\n", "批次2518: 处理了100条数据，匹配了4条\n", "读取第2519批数据（每批100条）...\n", "批次2519: 处理了100条数据，匹配了1条\n", "读取第2520批数据（每批100条）...\n", "暂停5秒...\n", "批次2520: 处理了100条数据，匹配了4条\n", "临时保存已匹配的10785条数据...\n", "读取第2521批数据（每批100条）...\n", "批次2521: 处理了100条数据，匹配了5条\n", "读取第2522批数据（每批100条）...\n", "批次2522: 处理了100条数据，匹配了2条\n", "读取第2523批数据（每批100条）...\n", "批次2523: 处理了100条数据，匹配了3条\n", "读取第2524批数据（每批100条）...\n", "批次2524: 处理了100条数据，匹配了9条\n", "读取第2525批数据（每批100条）...\n", "批次2525: 处理了100条数据，匹配了5条\n", "读取第2526批数据（每批100条）...\n", "批次2526: 处理了100条数据，匹配了4条\n", "读取第2527批数据（每批100条）...\n", "批次2527: 处理了100条数据，匹配了2条\n", "读取第2528批数据（每批100条）...\n", "批次2528: 处理了100条数据，匹配了5条\n", "读取第2529批数据（每批100条）...\n", "批次2529: 处理了100条数据，匹配了4条\n", "读取第2530批数据（每批100条）...\n", "暂停5秒...\n", "批次2530: 处理了100条数据，匹配了4条\n", "临时保存已匹配的10828条数据...\n", "读取第2531批数据（每批100条）...\n", "批次2531: 处理了100条数据，匹配了2条\n", "读取第2532批数据（每批100条）...\n", "批次2532: 处理了100条数据，匹配了7条\n", "读取第2533批数据（每批100条）...\n", "批次2533: 处理了100条数据，匹配了7条\n", "读取第2534批数据（每批100条）...\n", "批次2534: 处理了100条数据，匹配了6条\n", "读取第2535批数据（每批100条）...\n", "批次2535: 处理了100条数据，匹配了5条\n", "读取第2536批数据（每批100条）...\n", "批次2536: 处理了100条数据，匹配了4条\n", "读取第2537批数据（每批100条）...\n", "批次2537: 处理了100条数据，匹配了7条\n", "读取第2538批数据（每批100条）...\n", "批次2538: 处理了100条数据，匹配了0条\n", "读取第2539批数据（每批100条）...\n", "批次2539: 处理了100条数据，匹配了0条\n", "读取第2540批数据（每批100条）...\n", "暂停5秒...\n", "批次2540: 处理了100条数据，匹配了11条\n", "临时保存已匹配的10877条数据...\n", "读取第2541批数据（每批100条）...\n", "批次2541: 处理了100条数据，匹配了1条\n", "读取第2542批数据（每批100条）...\n", "批次2542: 处理了100条数据，匹配了6条\n", "读取第2543批数据（每批100条）...\n", "批次2543: 处理了100条数据，匹配了4条\n", "读取第2544批数据（每批100条）...\n", "批次2544: 处理了100条数据，匹配了2条\n", "读取第2545批数据（每批100条）...\n", "批次2545: 处理了100条数据，匹配了30条\n", "读取第2546批数据（每批100条）...\n", "批次2546: 处理了100条数据，匹配了1条\n", "读取第2547批数据（每批100条）...\n", "批次2547: 处理了100条数据，匹配了4条\n", "读取第2548批数据（每批100条）...\n", "批次2548: 处理了100条数据，匹配了0条\n", "读取第2549批数据（每批100条）...\n", "批次2549: 处理了100条数据，匹配了19条\n", "读取第2550批数据（每批100条）...\n", "暂停5秒...\n", "批次2550: 处理了100条数据，匹配了0条\n", "临时保存已匹配的10944条数据...\n", "读取第2551批数据（每批100条）...\n", "批次2551: 处理了100条数据，匹配了2条\n", "读取第2552批数据（每批100条）...\n", "批次2552: 处理了100条数据，匹配了6条\n", "读取第2553批数据（每批100条）...\n", "批次2553: 处理了100条数据，匹配了10条\n", "读取第2554批数据（每批100条）...\n", "批次2554: 处理了100条数据，匹配了26条\n", "读取第2555批数据（每批100条）...\n", "批次2555: 处理了100条数据，匹配了3条\n", "读取第2556批数据（每批100条）...\n", "批次2556: 处理了100条数据，匹配了1条\n", "读取第2557批数据（每批100条）...\n", "批次2557: 处理了100条数据，匹配了6条\n", "读取第2558批数据（每批100条）...\n", "批次2558: 处理了100条数据，匹配了0条\n", "读取第2559批数据（每批100条）...\n", "批次2559: 处理了100条数据，匹配了2条\n", "读取第2560批数据（每批100条）...\n", "暂停5秒...\n", "批次2560: 处理了100条数据，匹配了0条\n", "临时保存已匹配的11000条数据...\n", "读取第2561批数据（每批100条）...\n", "批次2561: 处理了100条数据，匹配了1条\n", "读取第2562批数据（每批100条）...\n", "批次2562: 处理了100条数据，匹配了0条\n", "读取第2563批数据（每批100条）...\n", "批次2563: 处理了100条数据，匹配了26条\n", "读取第2564批数据（每批100条）...\n", "批次2564: 处理了100条数据，匹配了7条\n", "读取第2565批数据（每批100条）...\n", "批次2565: 处理了100条数据，匹配了1条\n", "读取第2566批数据（每批100条）...\n", "批次2566: 处理了100条数据，匹配了1条\n", "读取第2567批数据（每批100条）...\n", "批次2567: 处理了100条数据，匹配了0条\n", "读取第2568批数据（每批100条）...\n", "批次2568: 处理了100条数据，匹配了10条\n", "读取第2569批数据（每批100条）...\n", "批次2569: 处理了100条数据，匹配了8条\n", "读取第2570批数据（每批100条）...\n", "暂停5秒...\n", "批次2570: 处理了100条数据，匹配了3条\n", "临时保存已匹配的11057条数据...\n", "读取第2571批数据（每批100条）...\n", "批次2571: 处理了100条数据，匹配了2条\n", "读取第2572批数据（每批100条）...\n", "批次2572: 处理了100条数据，匹配了4条\n", "读取第2573批数据（每批100条）...\n", "批次2573: 处理了100条数据，匹配了5条\n", "读取第2574批数据（每批100条）...\n", "批次2574: 处理了100条数据，匹配了7条\n", "读取第2575批数据（每批100条）...\n", "批次2575: 处理了100条数据，匹配了7条\n", "读取第2576批数据（每批100条）...\n", "批次2576: 处理了100条数据，匹配了2条\n", "读取第2577批数据（每批100条）...\n", "批次2577: 处理了100条数据，匹配了1条\n", "读取第2578批数据（每批100条）...\n", "批次2578: 处理了100条数据，匹配了11条\n", "读取第2579批数据（每批100条）...\n", "批次2579: 处理了100条数据，匹配了23条\n", "读取第2580批数据（每批100条）...\n", "暂停5秒...\n", "批次2580: 处理了100条数据，匹配了5条\n", "临时保存已匹配的11124条数据...\n", "读取第2581批数据（每批100条）...\n", "批次2581: 处理了100条数据，匹配了6条\n", "读取第2582批数据（每批100条）...\n", "批次2582: 处理了100条数据，匹配了2条\n", "读取第2583批数据（每批100条）...\n", "批次2583: 处理了100条数据，匹配了4条\n", "读取第2584批数据（每批100条）...\n", "批次2584: 处理了100条数据，匹配了2条\n", "读取第2585批数据（每批100条）...\n", "批次2585: 处理了100条数据，匹配了15条\n", "读取第2586批数据（每批100条）...\n", "批次2586: 处理了100条数据，匹配了3条\n", "读取第2587批数据（每批100条）...\n", "批次2587: 处理了100条数据，匹配了3条\n", "读取第2588批数据（每批100条）...\n", "批次2588: 处理了100条数据，匹配了2条\n", "读取第2589批数据（每批100条）...\n", "批次2589: 处理了100条数据，匹配了1条\n", "读取第2590批数据（每批100条）...\n", "暂停5秒...\n", "批次2590: 处理了100条数据，匹配了5条\n", "临时保存已匹配的11167条数据...\n", "读取第2591批数据（每批100条）...\n", "批次2591: 处理了100条数据，匹配了6条\n", "读取第2592批数据（每批100条）...\n", "批次2592: 处理了100条数据，匹配了8条\n", "读取第2593批数据（每批100条）...\n", "批次2593: 处理了100条数据，匹配了5条\n", "读取第2594批数据（每批100条）...\n", "批次2594: 处理了100条数据，匹配了2条\n", "读取第2595批数据（每批100条）...\n", "批次2595: 处理了100条数据，匹配了2条\n", "读取第2596批数据（每批100条）...\n", "批次2596: 处理了100条数据，匹配了7条\n", "读取第2597批数据（每批100条）...\n", "批次2597: 处理了100条数据，匹配了2条\n", "读取第2598批数据（每批100条）...\n", "批次2598: 处理了100条数据，匹配了1条\n", "读取第2599批数据（每批100条）...\n", "批次2599: 处理了100条数据，匹配了6条\n", "读取第2600批数据（每批100条）...\n", "暂停5秒...\n", "批次2600: 处理了100条数据，匹配了0条\n", "临时保存已匹配的11206条数据...\n", "读取第2601批数据（每批100条）...\n", "批次2601: 处理了100条数据，匹配了0条\n", "读取第2602批数据（每批100条）...\n", "批次2602: 处理了100条数据，匹配了2条\n", "读取第2603批数据（每批100条）...\n", "批次2603: 处理了100条数据，匹配了4条\n", "读取第2604批数据（每批100条）...\n", "批次2604: 处理了100条数据，匹配了2条\n", "读取第2605批数据（每批100条）...\n", "批次2605: 处理了100条数据，匹配了2条\n", "读取第2606批数据（每批100条）...\n", "批次2606: 处理了100条数据，匹配了3条\n", "读取第2607批数据（每批100条）...\n", "批次2607: 处理了100条数据，匹配了4条\n", "读取第2608批数据（每批100条）...\n", "批次2608: 处理了100条数据，匹配了1条\n", "读取第2609批数据（每批100条）...\n", "批次2609: 处理了100条数据，匹配了2条\n", "读取第2610批数据（每批100条）...\n", "暂停5秒...\n", "批次2610: 处理了100条数据，匹配了2条\n", "临时保存已匹配的11228条数据...\n", "读取第2611批数据（每批100条）...\n", "批次2611: 处理了100条数据，匹配了5条\n", "读取第2612批数据（每批100条）...\n", "批次2612: 处理了100条数据，匹配了5条\n", "读取第2613批数据（每批100条）...\n", "批次2613: 处理了100条数据，匹配了8条\n", "读取第2614批数据（每批100条）...\n", "批次2614: 处理了100条数据，匹配了7条\n", "读取第2615批数据（每批100条）...\n", "批次2615: 处理了100条数据，匹配了7条\n", "读取第2616批数据（每批100条）...\n", "批次2616: 处理了100条数据，匹配了3条\n", "读取第2617批数据（每批100条）...\n", "批次2617: 处理了100条数据，匹配了1条\n", "读取第2618批数据（每批100条）...\n", "批次2618: 处理了100条数据，匹配了15条\n", "读取第2619批数据（每批100条）...\n", "批次2619: 处理了100条数据，匹配了2条\n", "读取第2620批数据（每批100条）...\n", "暂停5秒...\n", "批次2620: 处理了100条数据，匹配了4条\n", "临时保存已匹配的11285条数据...\n", "读取第2621批数据（每批100条）...\n", "批次2621: 处理了100条数据，匹配了4条\n", "读取第2622批数据（每批100条）...\n", "批次2622: 处理了100条数据，匹配了2条\n", "读取第2623批数据（每批100条）...\n", "批次2623: 处理了100条数据，匹配了1条\n", "读取第2624批数据（每批100条）...\n", "批次2624: 处理了100条数据，匹配了8条\n", "读取第2625批数据（每批100条）...\n", "批次2625: 处理了100条数据，匹配了6条\n", "读取第2626批数据（每批100条）...\n", "批次2626: 处理了100条数据，匹配了10条\n", "读取第2627批数据（每批100条）...\n", "批次2627: 处理了100条数据，匹配了7条\n", "读取第2628批数据（每批100条）...\n", "批次2628: 处理了100条数据，匹配了2条\n", "读取第2629批数据（每批100条）...\n", "批次2629: 处理了100条数据，匹配了18条\n", "读取第2630批数据（每批100条）...\n", "暂停5秒...\n", "批次2630: 处理了100条数据，匹配了1条\n", "临时保存已匹配的11344条数据...\n", "读取第2631批数据（每批100条）...\n", "批次2631: 处理了100条数据，匹配了7条\n", "读取第2632批数据（每批100条）...\n", "批次2632: 处理了100条数据，匹配了2条\n", "读取第2633批数据（每批100条）...\n", "批次2633: 处理了100条数据，匹配了5条\n", "读取第2634批数据（每批100条）...\n", "批次2634: 处理了100条数据，匹配了6条\n", "读取第2635批数据（每批100条）...\n", "批次2635: 处理了100条数据，匹配了7条\n", "读取第2636批数据（每批100条）...\n", "批次2636: 处理了100条数据，匹配了2条\n", "读取第2637批数据（每批100条）...\n", "批次2637: 处理了100条数据，匹配了7条\n", "读取第2638批数据（每批100条）...\n", "批次2638: 处理了100条数据，匹配了0条\n", "读取第2639批数据（每批100条）...\n", "批次2639: 处理了100条数据，匹配了1条\n", "读取第2640批数据（每批100条）...\n", "暂停5秒...\n", "批次2640: 处理了100条数据，匹配了1条\n", "临时保存已匹配的11382条数据...\n", "读取第2641批数据（每批100条）...\n", "批次2641: 处理了100条数据，匹配了3条\n", "读取第2642批数据（每批100条）...\n", "批次2642: 处理了100条数据，匹配了1条\n", "读取第2643批数据（每批100条）...\n", "批次2643: 处理了100条数据，匹配了2条\n", "读取第2644批数据（每批100条）...\n", "批次2644: 处理了100条数据，匹配了0条\n", "读取第2645批数据（每批100条）...\n", "批次2645: 处理了100条数据，匹配了4条\n", "读取第2646批数据（每批100条）...\n", "批次2646: 处理了100条数据，匹配了5条\n", "读取第2647批数据（每批100条）...\n", "批次2647: 处理了100条数据，匹配了2条\n", "读取第2648批数据（每批100条）...\n", "批次2648: 处理了100条数据，匹配了1条\n", "读取第2649批数据（每批100条）...\n", "批次2649: 处理了100条数据，匹配了1条\n", "读取第2650批数据（每批100条）...\n", "暂停5秒...\n", "批次2650: 处理了100条数据，匹配了4条\n", "临时保存已匹配的11405条数据...\n", "读取第2651批数据（每批100条）...\n", "批次2651: 处理了100条数据，匹配了3条\n", "读取第2652批数据（每批100条）...\n", "批次2652: 处理了100条数据，匹配了2条\n", "读取第2653批数据（每批100条）...\n", "批次2653: 处理了100条数据，匹配了7条\n", "读取第2654批数据（每批100条）...\n", "批次2654: 处理了100条数据，匹配了14条\n", "读取第2655批数据（每批100条）...\n", "批次2655: 处理了100条数据，匹配了4条\n", "读取第2656批数据（每批100条）...\n", "批次2656: 处理了100条数据，匹配了2条\n", "读取第2657批数据（每批100条）...\n", "批次2657: 处理了100条数据，匹配了1条\n", "读取第2658批数据（每批100条）...\n", "批次2658: 处理了100条数据，匹配了7条\n", "读取第2659批数据（每批100条）...\n", "批次2659: 处理了100条数据，匹配了2条\n", "读取第2660批数据（每批100条）...\n", "暂停5秒...\n", "批次2660: 处理了100条数据，匹配了0条\n", "临时保存已匹配的11447条数据...\n", "读取第2661批数据（每批100条）...\n", "批次2661: 处理了100条数据，匹配了4条\n", "读取第2662批数据（每批100条）...\n", "批次2662: 处理了100条数据，匹配了2条\n", "读取第2663批数据（每批100条）...\n", "批次2663: 处理了100条数据，匹配了5条\n", "读取第2664批数据（每批100条）...\n", "批次2664: 处理了100条数据，匹配了6条\n", "读取第2665批数据（每批100条）...\n", "批次2665: 处理了100条数据，匹配了0条\n", "读取第2666批数据（每批100条）...\n", "批次2666: 处理了100条数据，匹配了1条\n", "读取第2667批数据（每批100条）...\n", "批次2667: 处理了100条数据，匹配了11条\n", "读取第2668批数据（每批100条）...\n", "批次2668: 处理了100条数据，匹配了8条\n", "读取第2669批数据（每批100条）...\n", "批次2669: 处理了100条数据，匹配了3条\n", "读取第2670批数据（每批100条）...\n", "暂停5秒...\n", "批次2670: 处理了100条数据，匹配了10条\n", "临时保存已匹配的11497条数据...\n", "读取第2671批数据（每批100条）...\n", "批次2671: 处理了100条数据，匹配了6条\n", "读取第2672批数据（每批100条）...\n", "批次2672: 处理了100条数据，匹配了9条\n", "读取第2673批数据（每批100条）...\n", "批次2673: 处理了100条数据，匹配了1条\n", "读取第2674批数据（每批100条）...\n", "批次2674: 处理了100条数据，匹配了11条\n", "读取第2675批数据（每批100条）...\n", "批次2675: 处理了100条数据，匹配了10条\n", "读取第2676批数据（每批100条）...\n", "批次2676: 处理了100条数据，匹配了13条\n", "读取第2677批数据（每批100条）...\n", "批次2677: 处理了100条数据，匹配了8条\n", "读取第2678批数据（每批100条）...\n", "批次2678: 处理了100条数据，匹配了3条\n", "读取第2679批数据（每批100条）...\n", "批次2679: 处理了100条数据，匹配了2条\n", "读取第2680批数据（每批100条）...\n", "暂停5秒...\n", "批次2680: 处理了100条数据，匹配了2条\n", "临时保存已匹配的11562条数据...\n", "读取第2681批数据（每批100条）...\n", "批次2681: 处理了100条数据，匹配了1条\n", "读取第2682批数据（每批100条）...\n", "批次2682: 处理了100条数据，匹配了4条\n", "读取第2683批数据（每批100条）...\n", "批次2683: 处理了100条数据，匹配了5条\n", "读取第2684批数据（每批100条）...\n", "批次2684: 处理了100条数据，匹配了5条\n", "读取第2685批数据（每批100条）...\n", "批次2685: 处理了100条数据，匹配了0条\n", "读取第2686批数据（每批100条）...\n", "批次2686: 处理了100条数据，匹配了5条\n", "读取第2687批数据（每批100条）...\n", "批次2687: 处理了100条数据，匹配了17条\n", "读取第2688批数据（每批100条）...\n", "批次2688: 处理了100条数据，匹配了5条\n", "读取第2689批数据（每批100条）...\n", "批次2689: 处理了100条数据，匹配了2条\n", "读取第2690批数据（每批100条）...\n", "暂停5秒...\n", "批次2690: 处理了100条数据，匹配了1条\n", "临时保存已匹配的11607条数据...\n", "读取第2691批数据（每批100条）...\n", "批次2691: 处理了100条数据，匹配了0条\n", "读取第2692批数据（每批100条）...\n", "批次2692: 处理了100条数据，匹配了4条\n", "读取第2693批数据（每批100条）...\n", "批次2693: 处理了100条数据，匹配了3条\n", "读取第2694批数据（每批100条）...\n", "批次2694: 处理了100条数据，匹配了0条\n", "读取第2695批数据（每批100条）...\n", "批次2695: 处理了100条数据，匹配了11条\n", "读取第2696批数据（每批100条）...\n", "批次2696: 处理了100条数据，匹配了3条\n", "读取第2697批数据（每批100条）...\n", "批次2697: 处理了100条数据，匹配了4条\n", "读取第2698批数据（每批100条）...\n", "批次2698: 处理了100条数据，匹配了8条\n", "读取第2699批数据（每批100条）...\n", "批次2699: 处理了100条数据，匹配了4条\n", "读取第2700批数据（每批100条）...\n", "暂停5秒...\n", "批次2700: 处理了100条数据，匹配了3条\n", "临时保存已匹配的11647条数据...\n", "读取第2701批数据（每批100条）...\n", "批次2701: 处理了100条数据，匹配了3条\n", "读取第2702批数据（每批100条）...\n", "批次2702: 处理了100条数据，匹配了2条\n", "读取第2703批数据（每批100条）...\n", "批次2703: 处理了100条数据，匹配了0条\n", "读取第2704批数据（每批100条）...\n", "批次2704: 处理了100条数据，匹配了7条\n", "读取第2705批数据（每批100条）...\n", "批次2705: 处理了100条数据，匹配了0条\n", "读取第2706批数据（每批100条）...\n", "批次2706: 处理了100条数据，匹配了2条\n", "读取第2707批数据（每批100条）...\n", "批次2707: 处理了100条数据，匹配了3条\n", "读取第2708批数据（每批100条）...\n", "批次2708: 处理了100条数据，匹配了10条\n", "读取第2709批数据（每批100条）...\n", "批次2709: 处理了100条数据，匹配了11条\n", "读取第2710批数据（每批100条）...\n", "暂停5秒...\n", "批次2710: 处理了100条数据，匹配了19条\n", "临时保存已匹配的11704条数据...\n", "读取第2711批数据（每批100条）...\n", "批次2711: 处理了100条数据，匹配了3条\n", "读取第2712批数据（每批100条）...\n", "批次2712: 处理了100条数据，匹配了8条\n", "读取第2713批数据（每批100条）...\n", "批次2713: 处理了100条数据，匹配了9条\n", "读取第2714批数据（每批100条）...\n", "批次2714: 处理了100条数据，匹配了3条\n", "读取第2715批数据（每批100条）...\n", "批次2715: 处理了100条数据，匹配了1条\n", "读取第2716批数据（每批100条）...\n", "批次2716: 处理了100条数据，匹配了3条\n", "读取第2717批数据（每批100条）...\n", "批次2717: 处理了100条数据，匹配了2条\n", "读取第2718批数据（每批100条）...\n", "批次2718: 处理了100条数据，匹配了4条\n", "读取第2719批数据（每批100条）...\n", "批次2719: 处理了100条数据，匹配了6条\n", "读取第2720批数据（每批100条）...\n", "暂停5秒...\n", "批次2720: 处理了100条数据，匹配了2条\n", "临时保存已匹配的11745条数据...\n", "读取第2721批数据（每批100条）...\n", "批次2721: 处理了100条数据，匹配了6条\n", "读取第2722批数据（每批100条）...\n", "批次2722: 处理了100条数据，匹配了1条\n", "读取第2723批数据（每批100条）...\n", "批次2723: 处理了100条数据，匹配了3条\n", "读取第2724批数据（每批100条）...\n", "批次2724: 处理了100条数据，匹配了0条\n", "读取第2725批数据（每批100条）...\n", "批次2725: 处理了100条数据，匹配了0条\n", "读取第2726批数据（每批100条）...\n", "批次2726: 处理了100条数据，匹配了3条\n", "读取第2727批数据（每批100条）...\n", "批次2727: 处理了100条数据，匹配了9条\n", "读取第2728批数据（每批100条）...\n", "批次2728: 处理了100条数据，匹配了7条\n", "读取第2729批数据（每批100条）...\n", "批次2729: 处理了100条数据，匹配了5条\n", "读取第2730批数据（每批100条）...\n", "暂停5秒...\n", "批次2730: 处理了100条数据，匹配了0条\n", "临时保存已匹配的11779条数据...\n", "读取第2731批数据（每批100条）...\n", "批次2731: 处理了100条数据，匹配了11条\n", "读取第2732批数据（每批100条）...\n", "批次2732: 处理了100条数据，匹配了2条\n", "读取第2733批数据（每批100条）...\n", "批次2733: 处理了100条数据，匹配了2条\n", "读取第2734批数据（每批100条）...\n", "批次2734: 处理了100条数据，匹配了3条\n", "读取第2735批数据（每批100条）...\n", "批次2735: 处理了100条数据，匹配了6条\n", "读取第2736批数据（每批100条）...\n", "批次2736: 处理了100条数据，匹配了4条\n", "读取第2737批数据（每批100条）...\n", "批次2737: 处理了100条数据，匹配了8条\n", "读取第2738批数据（每批100条）...\n", "批次2738: 处理了100条数据，匹配了4条\n", "读取第2739批数据（每批100条）...\n", "批次2739: 处理了100条数据，匹配了2条\n", "读取第2740批数据（每批100条）...\n", "暂停5秒...\n", "批次2740: 处理了100条数据，匹配了0条\n", "临时保存已匹配的11821条数据...\n", "读取第2741批数据（每批100条）...\n", "批次2741: 处理了100条数据，匹配了2条\n", "读取第2742批数据（每批100条）...\n", "批次2742: 处理了100条数据，匹配了2条\n", "读取第2743批数据（每批100条）...\n", "批次2743: 处理了100条数据，匹配了0条\n", "读取第2744批数据（每批100条）...\n", "批次2744: 处理了100条数据，匹配了0条\n", "读取第2745批数据（每批100条）...\n", "批次2745: 处理了100条数据，匹配了3条\n", "读取第2746批数据（每批100条）...\n", "批次2746: 处理了100条数据，匹配了2条\n", "读取第2747批数据（每批100条）...\n", "批次2747: 处理了100条数据，匹配了1条\n", "读取第2748批数据（每批100条）...\n", "批次2748: 处理了100条数据，匹配了3条\n", "读取第2749批数据（每批100条）...\n", "批次2749: 处理了100条数据，匹配了3条\n", "读取第2750批数据（每批100条）...\n", "暂停5秒...\n", "批次2750: 处理了100条数据，匹配了1条\n", "临时保存已匹配的11838条数据...\n", "读取第2751批数据（每批100条）...\n", "批次2751: 处理了100条数据，匹配了1条\n", "读取第2752批数据（每批100条）...\n", "批次2752: 处理了100条数据，匹配了9条\n", "读取第2753批数据（每批100条）...\n", "批次2753: 处理了100条数据，匹配了4条\n", "读取第2754批数据（每批100条）...\n", "批次2754: 处理了100条数据，匹配了3条\n", "读取第2755批数据（每批100条）...\n", "批次2755: 处理了100条数据，匹配了11条\n", "读取第2756批数据（每批100条）...\n", "批次2756: 处理了100条数据，匹配了3条\n", "读取第2757批数据（每批100条）...\n", "批次2757: 处理了100条数据，匹配了2条\n", "读取第2758批数据（每批100条）...\n", "批次2758: 处理了100条数据，匹配了20条\n", "读取第2759批数据（每批100条）...\n", "批次2759: 处理了100条数据，匹配了2条\n", "读取第2760批数据（每批100条）...\n", "暂停5秒...\n", "批次2760: 处理了100条数据，匹配了0条\n", "临时保存已匹配的11893条数据...\n", "读取第2761批数据（每批100条）...\n", "批次2761: 处理了100条数据，匹配了2条\n", "读取第2762批数据（每批100条）...\n", "批次2762: 处理了100条数据，匹配了4条\n", "读取第2763批数据（每批100条）...\n", "批次2763: 处理了100条数据，匹配了9条\n", "读取第2764批数据（每批100条）...\n", "批次2764: 处理了100条数据，匹配了8条\n", "读取第2765批数据（每批100条）...\n", "批次2765: 处理了100条数据，匹配了8条\n", "读取第2766批数据（每批100条）...\n", "批次2766: 处理了100条数据，匹配了3条\n", "读取第2767批数据（每批100条）...\n", "批次2767: 处理了100条数据，匹配了1条\n", "读取第2768批数据（每批100条）...\n", "批次2768: 处理了100条数据，匹配了11条\n", "读取第2769批数据（每批100条）...\n", "批次2769: 处理了100条数据，匹配了4条\n", "读取第2770批数据（每批100条）...\n", "暂停5秒...\n", "批次2770: 处理了100条数据，匹配了4条\n", "临时保存已匹配的11947条数据...\n", "读取第2771批数据（每批100条）...\n", "批次2771: 处理了100条数据，匹配了8条\n", "读取第2772批数据（每批100条）...\n", "批次2772: 处理了100条数据，匹配了0条\n", "读取第2773批数据（每批100条）...\n", "批次2773: 处理了100条数据，匹配了1条\n", "读取第2774批数据（每批100条）...\n", "批次2774: 处理了100条数据，匹配了5条\n", "读取第2775批数据（每批100条）...\n", "批次2775: 处理了100条数据，匹配了0条\n", "读取第2776批数据（每批100条）...\n", "批次2776: 处理了100条数据，匹配了2条\n", "读取第2777批数据（每批100条）...\n", "批次2777: 处理了100条数据，匹配了1条\n", "读取第2778批数据（每批100条）...\n", "批次2778: 处理了100条数据，匹配了2条\n", "读取第2779批数据（每批100条）...\n", "批次2779: 处理了100条数据，匹配了1条\n", "读取第2780批数据（每批100条）...\n", "暂停5秒...\n", "批次2780: 处理了100条数据，匹配了0条\n", "临时保存已匹配的11967条数据...\n", "读取第2781批数据（每批100条）...\n", "批次2781: 处理了100条数据，匹配了6条\n", "读取第2782批数据（每批100条）...\n", "批次2782: 处理了100条数据，匹配了2条\n", "读取第2783批数据（每批100条）...\n", "批次2783: 处理了100条数据，匹配了2条\n", "读取第2784批数据（每批100条）...\n", "批次2784: 处理了100条数据，匹配了4条\n", "读取第2785批数据（每批100条）...\n", "批次2785: 处理了100条数据，匹配了3条\n", "读取第2786批数据（每批100条）...\n", "批次2786: 处理了100条数据，匹配了1条\n", "读取第2787批数据（每批100条）...\n", "批次2787: 处理了100条数据，匹配了9条\n", "读取第2788批数据（每批100条）...\n", "批次2788: 处理了100条数据，匹配了2条\n", "读取第2789批数据（每批100条）...\n", "批次2789: 处理了100条数据，匹配了4条\n", "读取第2790批数据（每批100条）...\n", "暂停5秒...\n", "批次2790: 处理了100条数据，匹配了6条\n", "临时保存已匹配的12006条数据...\n", "读取第2791批数据（每批100条）...\n", "批次2791: 处理了100条数据，匹配了2条\n", "读取第2792批数据（每批100条）...\n", "批次2792: 处理了100条数据，匹配了0条\n", "读取第2793批数据（每批100条）...\n", "批次2793: 处理了100条数据，匹配了3条\n", "读取第2794批数据（每批100条）...\n", "批次2794: 处理了100条数据，匹配了0条\n", "读取第2795批数据（每批100条）...\n", "批次2795: 处理了100条数据，匹配了3条\n", "读取第2796批数据（每批100条）...\n", "批次2796: 处理了100条数据，匹配了6条\n", "读取第2797批数据（每批100条）...\n", "批次2797: 处理了100条数据，匹配了5条\n", "读取第2798批数据（每批100条）...\n", "批次2798: 处理了100条数据，匹配了4条\n", "读取第2799批数据（每批100条）...\n", "批次2799: 处理了100条数据，匹配了2条\n", "读取第2800批数据（每批100条）...\n", "暂停5秒...\n", "批次2800: 处理了100条数据，匹配了9条\n", "临时保存已匹配的12040条数据...\n", "读取第2801批数据（每批100条）...\n", "批次2801: 处理了100条数据，匹配了6条\n", "读取第2802批数据（每批100条）...\n", "批次2802: 处理了100条数据，匹配了1条\n", "读取第2803批数据（每批100条）...\n", "批次2803: 处理了100条数据，匹配了2条\n", "读取第2804批数据（每批100条）...\n", "批次2804: 处理了100条数据，匹配了12条\n", "读取第2805批数据（每批100条）...\n", "批次2805: 处理了100条数据，匹配了12条\n", "读取第2806批数据（每批100条）...\n", "批次2806: 处理了100条数据，匹配了4条\n", "读取第2807批数据（每批100条）...\n", "批次2807: 处理了100条数据，匹配了9条\n", "读取第2808批数据（每批100条）...\n", "批次2808: 处理了100条数据，匹配了2条\n", "读取第2809批数据（每批100条）...\n", "批次2809: 处理了100条数据，匹配了15条\n", "读取第2810批数据（每批100条）...\n", "暂停5秒...\n", "批次2810: 处理了100条数据，匹配了2条\n", "临时保存已匹配的12105条数据...\n", "读取第2811批数据（每批100条）...\n", "批次2811: 处理了100条数据，匹配了0条\n", "读取第2812批数据（每批100条）...\n", "批次2812: 处理了100条数据，匹配了5条\n", "读取第2813批数据（每批100条）...\n", "批次2813: 处理了100条数据，匹配了3条\n", "读取第2814批数据（每批100条）...\n", "批次2814: 处理了100条数据，匹配了1条\n", "读取第2815批数据（每批100条）...\n", "批次2815: 处理了100条数据，匹配了6条\n", "读取第2816批数据（每批100条）...\n", "批次2816: 处理了100条数据，匹配了5条\n", "读取第2817批数据（每批100条）...\n", "批次2817: 处理了100条数据，匹配了1条\n", "读取第2818批数据（每批100条）...\n", "批次2818: 处理了100条数据，匹配了4条\n", "读取第2819批数据（每批100条）...\n", "批次2819: 处理了100条数据，匹配了4条\n", "读取第2820批数据（每批100条）...\n", "暂停5秒...\n", "批次2820: 处理了100条数据，匹配了5条\n", "临时保存已匹配的12139条数据...\n", "读取第2821批数据（每批100条）...\n", "批次2821: 处理了100条数据，匹配了16条\n", "读取第2822批数据（每批100条）...\n", "批次2822: 处理了100条数据，匹配了33条\n", "读取第2823批数据（每批100条）...\n", "批次2823: 处理了100条数据，匹配了4条\n", "读取第2824批数据（每批100条）...\n", "批次2824: 处理了100条数据，匹配了1条\n", "读取第2825批数据（每批100条）...\n", "批次2825: 处理了100条数据，匹配了3条\n", "读取第2826批数据（每批100条）...\n", "批次2826: 处理了100条数据，匹配了0条\n", "读取第2827批数据（每批100条）...\n", "批次2827: 处理了100条数据，匹配了0条\n", "读取第2828批数据（每批100条）...\n", "批次2828: 处理了100条数据，匹配了5条\n", "读取第2829批数据（每批100条）...\n", "批次2829: 处理了100条数据，匹配了1条\n", "读取第2830批数据（每批100条）...\n", "暂停5秒...\n", "批次2830: 处理了100条数据，匹配了3条\n", "临时保存已匹配的12205条数据...\n", "读取第2831批数据（每批100条）...\n", "批次2831: 处理了100条数据，匹配了1条\n", "读取第2832批数据（每批100条）...\n", "批次2832: 处理了100条数据，匹配了3条\n", "读取第2833批数据（每批100条）...\n", "批次2833: 处理了100条数据，匹配了4条\n", "读取第2834批数据（每批100条）...\n", "批次2834: 处理了100条数据，匹配了6条\n", "读取第2835批数据（每批100条）...\n", "批次2835: 处理了100条数据，匹配了2条\n", "读取第2836批数据（每批100条）...\n", "批次2836: 处理了100条数据，匹配了3条\n", "读取第2837批数据（每批100条）...\n", "批次2837: 处理了100条数据，匹配了0条\n", "读取第2838批数据（每批100条）...\n", "批次2838: 处理了100条数据，匹配了3条\n", "读取第2839批数据（每批100条）...\n", "批次2839: 处理了100条数据，匹配了5条\n", "读取第2840批数据（每批100条）...\n", "暂停5秒...\n", "批次2840: 处理了100条数据，匹配了9条\n", "临时保存已匹配的12241条数据...\n", "读取第2841批数据（每批100条）...\n", "批次2841: 处理了100条数据，匹配了6条\n", "读取第2842批数据（每批100条）...\n", "批次2842: 处理了100条数据，匹配了7条\n", "读取第2843批数据（每批100条）...\n", "批次2843: 处理了100条数据，匹配了6条\n", "读取第2844批数据（每批100条）...\n", "批次2844: 处理了100条数据，匹配了3条\n", "读取第2845批数据（每批100条）...\n", "批次2845: 处理了100条数据，匹配了4条\n", "读取第2846批数据（每批100条）...\n", "批次2846: 处理了100条数据，匹配了1条\n", "读取第2847批数据（每批100条）...\n", "批次2847: 处理了100条数据，匹配了3条\n", "读取第2848批数据（每批100条）...\n", "批次2848: 处理了100条数据，匹配了12条\n", "读取第2849批数据（每批100条）...\n", "批次2849: 处理了100条数据，匹配了8条\n", "读取第2850批数据（每批100条）...\n", "暂停5秒...\n", "批次2850: 处理了100条数据，匹配了8条\n", "临时保存已匹配的12299条数据...\n", "读取第2851批数据（每批100条）...\n", "批次2851: 处理了100条数据，匹配了1条\n", "读取第2852批数据（每批100条）...\n", "批次2852: 处理了100条数据，匹配了0条\n", "读取第2853批数据（每批100条）...\n", "批次2853: 处理了100条数据，匹配了3条\n", "读取第2854批数据（每批100条）...\n", "批次2854: 处理了100条数据，匹配了4条\n", "读取第2855批数据（每批100条）...\n", "批次2855: 处理了100条数据，匹配了8条\n", "读取第2856批数据（每批100条）...\n", "批次2856: 处理了100条数据，匹配了9条\n", "读取第2857批数据（每批100条）...\n", "批次2857: 处理了100条数据，匹配了7条\n", "读取第2858批数据（每批100条）...\n", "批次2858: 处理了100条数据，匹配了11条\n", "读取第2859批数据（每批100条）...\n", "批次2859: 处理了100条数据，匹配了1条\n", "读取第2860批数据（每批100条）...\n", "暂停5秒...\n", "批次2860: 处理了100条数据，匹配了1条\n", "临时保存已匹配的12344条数据...\n", "读取第2861批数据（每批100条）...\n", "批次2861: 处理了100条数据，匹配了0条\n", "读取第2862批数据（每批100条）...\n", "批次2862: 处理了100条数据，匹配了6条\n", "读取第2863批数据（每批100条）...\n", "批次2863: 处理了100条数据，匹配了1条\n", "读取第2864批数据（每批100条）...\n", "批次2864: 处理了100条数据，匹配了1条\n", "读取第2865批数据（每批100条）...\n", "批次2865: 处理了100条数据，匹配了4条\n", "读取第2866批数据（每批100条）...\n", "批次2866: 处理了100条数据，匹配了6条\n", "读取第2867批数据（每批100条）...\n", "批次2867: 处理了100条数据，匹配了5条\n", "读取第2868批数据（每批100条）...\n", "批次2868: 处理了100条数据，匹配了7条\n", "读取第2869批数据（每批100条）...\n", "批次2869: 处理了100条数据，匹配了4条\n", "读取第2870批数据（每批100条）...\n", "暂停5秒...\n", "批次2870: 处理了100条数据，匹配了0条\n", "临时保存已匹配的12378条数据...\n", "读取第2871批数据（每批100条）...\n", "批次2871: 处理了100条数据，匹配了6条\n", "读取第2872批数据（每批100条）...\n", "批次2872: 处理了100条数据，匹配了2条\n", "读取第2873批数据（每批100条）...\n", "批次2873: 处理了100条数据，匹配了2条\n", "读取第2874批数据（每批100条）...\n", "批次2874: 处理了100条数据，匹配了0条\n", "读取第2875批数据（每批100条）...\n", "批次2875: 处理了100条数据，匹配了3条\n", "读取第2876批数据（每批100条）...\n", "批次2876: 处理了100条数据，匹配了8条\n", "读取第2877批数据（每批100条）...\n", "批次2877: 处理了100条数据，匹配了4条\n", "读取第2878批数据（每批100条）...\n", "批次2878: 处理了100条数据，匹配了2条\n", "读取第2879批数据（每批100条）...\n", "批次2879: 处理了100条数据，匹配了5条\n", "读取第2880批数据（每批100条）...\n", "暂停5秒...\n", "批次2880: 处理了100条数据，匹配了2条\n", "临时保存已匹配的12412条数据...\n", "读取第2881批数据（每批100条）...\n", "批次2881: 处理了100条数据，匹配了3条\n", "读取第2882批数据（每批100条）...\n", "批次2882: 处理了100条数据，匹配了5条\n", "读取第2883批数据（每批100条）...\n", "批次2883: 处理了100条数据，匹配了5条\n", "读取第2884批数据（每批100条）...\n", "批次2884: 处理了100条数据，匹配了2条\n", "读取第2885批数据（每批100条）...\n", "批次2885: 处理了100条数据，匹配了0条\n", "读取第2886批数据（每批100条）...\n", "批次2886: 处理了100条数据，匹配了5条\n", "读取第2887批数据（每批100条）...\n", "批次2887: 处理了100条数据，匹配了3条\n", "读取第2888批数据（每批100条）...\n", "批次2888: 处理了100条数据，匹配了2条\n", "读取第2889批数据（每批100条）...\n", "批次2889: 处理了100条数据，匹配了12条\n", "读取第2890批数据（每批100条）...\n", "暂停5秒...\n", "批次2890: 处理了100条数据，匹配了20条\n", "临时保存已匹配的12469条数据...\n", "读取第2891批数据（每批100条）...\n", "批次2891: 处理了100条数据，匹配了10条\n", "读取第2892批数据（每批100条）...\n", "批次2892: 处理了100条数据，匹配了15条\n", "读取第2893批数据（每批100条）...\n", "批次2893: 处理了100条数据，匹配了17条\n", "读取第2894批数据（每批100条）...\n", "批次2894: 处理了100条数据，匹配了13条\n", "读取第2895批数据（每批100条）...\n", "批次2895: 处理了100条数据，匹配了4条\n", "读取第2896批数据（每批100条）...\n", "批次2896: 处理了100条数据，匹配了13条\n", "读取第2897批数据（每批100条）...\n", "批次2897: 处理了100条数据，匹配了7条\n", "读取第2898批数据（每批100条）...\n", "批次2898: 处理了100条数据，匹配了6条\n", "读取第2899批数据（每批100条）...\n", "批次2899: 处理了100条数据，匹配了7条\n", "读取第2900批数据（每批100条）...\n", "暂停5秒...\n", "批次2900: 处理了100条数据，匹配了0条\n", "临时保存已匹配的12561条数据...\n", "读取第2901批数据（每批100条）...\n", "批次2901: 处理了100条数据，匹配了2条\n", "读取第2902批数据（每批100条）...\n", "批次2902: 处理了100条数据，匹配了4条\n", "读取第2903批数据（每批100条）...\n", "批次2903: 处理了100条数据，匹配了3条\n", "读取第2904批数据（每批100条）...\n", "批次2904: 处理了100条数据，匹配了4条\n", "读取第2905批数据（每批100条）...\n", "批次2905: 处理了100条数据，匹配了0条\n", "读取第2906批数据（每批100条）...\n", "批次2906: 处理了100条数据，匹配了4条\n", "读取第2907批数据（每批100条）...\n", "批次2907: 处理了100条数据，匹配了1条\n", "读取第2908批数据（每批100条）...\n", "批次2908: 处理了100条数据，匹配了3条\n", "读取第2909批数据（每批100条）...\n", "批次2909: 处理了100条数据，匹配了2条\n", "读取第2910批数据（每批100条）...\n", "暂停5秒...\n", "批次2910: 处理了100条数据，匹配了1条\n", "临时保存已匹配的12585条数据...\n", "读取第2911批数据（每批100条）...\n", "批次2911: 处理了100条数据，匹配了1条\n", "读取第2912批数据（每批100条）...\n", "批次2912: 处理了100条数据，匹配了0条\n", "读取第2913批数据（每批100条）...\n", "批次2913: 处理了100条数据，匹配了3条\n", "读取第2914批数据（每批100条）...\n", "批次2914: 处理了100条数据，匹配了7条\n", "读取第2915批数据（每批100条）...\n", "批次2915: 处理了100条数据，匹配了3条\n", "读取第2916批数据（每批100条）...\n", "批次2916: 处理了100条数据，匹配了0条\n", "读取第2917批数据（每批100条）...\n", "批次2917: 处理了100条数据，匹配了3条\n", "读取第2918批数据（每批100条）...\n", "批次2918: 处理了100条数据，匹配了0条\n", "读取第2919批数据（每批100条）...\n", "批次2919: 处理了100条数据，匹配了1条\n", "读取第2920批数据（每批100条）...\n", "暂停5秒...\n", "批次2920: 处理了100条数据，匹配了2条\n", "临时保存已匹配的12605条数据...\n", "读取第2921批数据（每批100条）...\n", "批次2921: 处理了100条数据，匹配了3条\n", "读取第2922批数据（每批100条）...\n", "批次2922: 处理了100条数据，匹配了2条\n", "读取第2923批数据（每批100条）...\n", "批次2923: 处理了100条数据，匹配了4条\n", "读取第2924批数据（每批100条）...\n", "批次2924: 处理了100条数据，匹配了6条\n", "读取第2925批数据（每批100条）...\n", "批次2925: 处理了100条数据，匹配了7条\n", "读取第2926批数据（每批100条）...\n", "批次2926: 处理了100条数据，匹配了3条\n", "读取第2927批数据（每批100条）...\n", "批次2927: 处理了100条数据，匹配了3条\n", "读取第2928批数据（每批100条）...\n", "批次2928: 处理了100条数据，匹配了4条\n", "读取第2929批数据（每批100条）...\n", "批次2929: 处理了100条数据，匹配了3条\n", "读取第2930批数据（每批100条）...\n", "暂停5秒...\n", "批次2930: 处理了100条数据，匹配了2条\n", "临时保存已匹配的12642条数据...\n", "读取第2931批数据（每批100条）...\n", "批次2931: 处理了100条数据，匹配了2条\n", "读取第2932批数据（每批100条）...\n", "批次2932: 处理了100条数据，匹配了3条\n", "读取第2933批数据（每批100条）...\n", "批次2933: 处理了100条数据，匹配了15条\n", "读取第2934批数据（每批100条）...\n", "批次2934: 处理了100条数据，匹配了3条\n", "读取第2935批数据（每批100条）...\n", "批次2935: 处理了100条数据，匹配了2条\n", "读取第2936批数据（每批100条）...\n", "批次2936: 处理了100条数据，匹配了9条\n", "读取第2937批数据（每批100条）...\n", "批次2937: 处理了100条数据，匹配了13条\n", "读取第2938批数据（每批100条）...\n", "批次2938: 处理了100条数据，匹配了3条\n", "读取第2939批数据（每批100条）...\n", "批次2939: 处理了100条数据，匹配了20条\n", "读取第2940批数据（每批100条）...\n", "暂停5秒...\n", "批次2940: 处理了100条数据，匹配了2条\n", "临时保存已匹配的12714条数据...\n", "读取第2941批数据（每批100条）...\n", "批次2941: 处理了100条数据，匹配了10条\n", "读取第2942批数据（每批100条）...\n", "批次2942: 处理了100条数据，匹配了1条\n", "读取第2943批数据（每批100条）...\n", "批次2943: 处理了100条数据，匹配了2条\n", "读取第2944批数据（每批100条）...\n", "批次2944: 处理了100条数据，匹配了3条\n", "读取第2945批数据（每批100条）...\n", "批次2945: 处理了100条数据，匹配了7条\n", "读取第2946批数据（每批100条）...\n", "批次2946: 处理了100条数据，匹配了4条\n", "读取第2947批数据（每批100条）...\n", "批次2947: 处理了100条数据，匹配了0条\n", "读取第2948批数据（每批100条）...\n", "批次2948: 处理了100条数据，匹配了8条\n", "读取第2949批数据（每批100条）...\n", "批次2949: 处理了100条数据，匹配了3条\n", "读取第2950批数据（每批100条）...\n", "暂停5秒...\n", "批次2950: 处理了100条数据，匹配了3条\n", "临时保存已匹配的12755条数据...\n", "保存12755条匹配的数据到es_extracted_data\\website_data_20250530_150807.json\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[36]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# 示例：提取2022年4月份的数据\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[43mextract_and_save_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstart_date\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m2025-05-01\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mend_date\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m2025-05-30\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m      4\u001b[39m \u001b[38;5;66;03m# 如需查询所有数据，不指定日期\u001b[39;00m\n\u001b[32m      5\u001b[39m \u001b[38;5;66;03m# extract_and_save_data()\u001b[39;00m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[35]\u001b[39m\u001b[32m, line 125\u001b[39m, in \u001b[36mextract_and_save_data\u001b[39m\u001b[34m(start_date, end_date)\u001b[39m\n\u001b[32m    123\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m临时保存已匹配的\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(all_matched_data)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m条数据...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    124\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(output_file, \u001b[33m'\u001b[39m\u001b[33mw\u001b[39m\u001b[33m'\u001b[39m, encoding=\u001b[33m'\u001b[39m\u001b[33mutf-8\u001b[39m\u001b[33m'\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[32m--> \u001b[39m\u001b[32m125\u001b[39m         \u001b[43mjson\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdump\u001b[49m\u001b[43m(\u001b[49m\u001b[43mall_matched_data\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mensure_ascii\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindent\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m2\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    127\u001b[39m \u001b[38;5;66;03m# 使用scroll API获取下一批数据\u001b[39;00m\n\u001b[32m    128\u001b[39m current_batch += \u001b[32m1\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Python313\\Lib\\json\\__init__.py:180\u001b[39m, in \u001b[36mdump\u001b[39m\u001b[34m(obj, fp, skipkeys, ensure_ascii, check_circular, allow_nan, cls, indent, separators, default, sort_keys, **kw)\u001b[39m\n\u001b[32m    177\u001b[39m \u001b[38;5;66;03m# could accelerate with writelines in some versions of Python, at\u001b[39;00m\n\u001b[32m    178\u001b[39m \u001b[38;5;66;03m# a debuggability cost\u001b[39;00m\n\u001b[32m    179\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m chunk \u001b[38;5;129;01min\u001b[39;00m iterable:\n\u001b[32m--> \u001b[39m\u001b[32m180\u001b[39m     \u001b[43mfp\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwrite\u001b[49m\u001b[43m(\u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["# 示例：提取2022年4月份的数据\n", "extract_and_save_data(start_date=\"2025-05-01\", end_date=\"2025-05-30\")\n", "\n", "# 如需查询所有数据，不指定日期\n", "# extract_and_save_data()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}