from fastapi import APIRouter, HTTPException, Depends, Body, Query
from fastapi.responses import StreamingResponse
import json
from typing import AsyncGenerator, List, Dict, Any, Optional
from ..models.chat import ChatResponse
from ..models.llm import LLMModel
from typing import List, Dict, Any
from ..utils.auth import verify_token
from ..db.mongodb import db  # 确保导入数据库连接
from urllib.parse import urljoin
from ..models.system_app_setting import SystemAppSetting
import asyncio
from ..models.system_app_setting import SystemAppSettingModel
from ..utils.llmClient import stream_openai_api,convert_messages,stream_jiutian_api
from ..models.message import Message
from datetime import datetime
from ..models.llm import LLMModel, Provider
from bson import ObjectId
import traceback
from app.utils.logging_config import setup_logging, get_logger
from app.engines.contentSearch.search_agent import SearchWorkflow

setup_logging()
logger = get_logger(__name__)


router = APIRouter(
    prefix="/api/contentSearch",
    tags=["agent_contentSearch"]
)

@router.post("/", response_model=Dict[str, Any])
async def content_search(conversation: ChatResponse, current_user: dict = Depends(verify_token)):
     # print(conversation)
    logger.info( conversation)
    system_app_info: SystemAppSettingModel = await db["system_app_settings"].find_one({"app_info": conversation.app_info})
    if not system_app_info:
        logger.error(f"没有找到应用信息: {conversation.app_info}")
        raise HTTPException(status_code=404, detail="没有找到应用信息")
    # 提取最后一条消息
    last_message = conversation.messages[-1]


    new_user_message = Message(
        _id=str(ObjectId()),
        conversation_id=conversation.conversation_id,
        message_id=last_message['id'],
        meta_data=last_message.get('meta', {}),
        extra=last_message.get('extra', {}),
        user_id=conversation.user_id,
        user_name=conversation.user_name,
        role=last_message['role'],
        content=last_message['content'],
        created_at=datetime.now(),
        app_info=conversation.app_info,
        token_count=0,  # 这里可以添加计算token的逻辑
        price=0.0  # 这里可以添加计算价格的逻辑
    )


    # 将 MongoEngine 实例转换为字典
    new_user_message_dict = new_user_message.to_mongo().to_dict()
    await db["messages"].insert_one(new_user_message_dict)

    async def stream_response():
        # ai_message_content = ""
        async for chunk in stream_search(
            chat_id=conversation.conversation_id,
            user_id=conversation.user_id,
            messages=conversation.messages,
            system_app_info=system_app_info
        ):
            # ai_message_content += chunk
            yield chunk
            await asyncio.sleep(0)

        # 添加 end 事件类型
        yield "event: end\ndata: Stream has ended\n\n"

    return StreamingResponse(stream_response(), media_type='text/event-stream')



async def stream_search(
    chat_id: str,
    user_id: str,
    messages: List[Dict[str, Any]],
    system_app_info: SystemAppSetting
) -> AsyncGenerator[str, None]:
    """
    统一的模型流式调用接口，支持 WISERAG、LLM 和 KNOWLEDGE_QA
    """
    logger.info('========stream_model_api')
    logger.info(system_app_info)

    if not system_app_info:
        raise ValueError("System app setting not found")
    
    try:
        messages_list = convert_messages(messages)
        system_app_params = system_app_info.get('params', {})
        
        # 处理 prompt template
        if prompt_template := system_app_params.get('prompt_template', None):
            new_content = prompt_template.replace('{user_message}', messages_list[-1]['content'])
            messages_list[-1]['content'] = new_content
    
        llm: LLMModel = await db["llms"].find_one({"id": int(system_app_params.get('llm_id', None))})
        if not llm:
            raise ValueError("LLM model not found")
        
        logger.info('调用 KNOWLEDGE_QA')
        ## 这里需要根据 app_info 获取知识的配置信息
        logger.info(system_app_info.get('params', {}))
        # llm = ChatOpenAI(
        #         model=settings.LLM_MODEL,
        #         temperature=settings.LLM_TEMPERATURE,
        #         openai_api_key=settings.OPENAI_API_KEY,
        #         openai_api_base= settings.LLM_API_BASE,
        #         stream = False

        #     )
        search_workflow = SearchWorkflow(config=system_app_params, user_id=user_id,llm=llm)
        # 获取用户的最新消息内容
        user_query = messages_list[-1]['content']
        logger.info(f"用户查询: {user_query}")
        
        # 等待异步结果
        kg_qa_results = await search_workflow.run(user_query) # 最后一条信息
        logger.info(f"检索结果: {kg_qa_results}")
        last_message = kg_qa_results['output']
        messages_list[-1]=last_message


        model = llm.get('m_name', None)
        provider = llm.get('provider', Provider.OPENAI)


        # 根据不同的 provider 使用对应的 API 调用方法
        if provider == Provider.DEEPSEEK:
            logger.info('调用 DEEPSEEK')
            stream_api = stream_openai_api
        elif provider == Provider.DOUBAO:
            logger.info('调用 DOUBAO')
            stream_api = stream_openai_api
        elif provider == Provider.LOCAL:
            logger.info('调用 LOCAL')
            stream_api = stream_openai_api
        elif provider == Provider.OLLAMA:
            logger.info('调用 OLLAMA')
            stream_api = stream_openai_api
        elif provider == Provider.JIUTIAN:  # 添加九天模型的处理
            logger.info('调用 JIUTIAN')
            stream_api = stream_jiutian_api
        else:
            logger.info('调用 OpenAI')
            stream_api = stream_openai_api
        

        yield 'event: moduleStatus\ndata: {"status":"running","name":"Base RAG Agent"}\n\n'

        quoteList = {
            "moduleName": "AI 对话",
            "moduleType": "chatNode",
            "model": model,
            "query": user_query,
            "quoteList": [
                getattr(one, 'additional_kwargs', {}) 
                for one in kg_qa_results.get('rerank_results', []) 
                if hasattr(one, 'additional_kwargs')
            ],
        }
        yield f'event: appStreamResponse\ndata: [{json.dumps(quoteList, ensure_ascii=False)}]\n\n'

        
        async for chunk in stream_api(
            llm["api_key"],
            model,
            messages_list,
            llm["service_url"],
            extra=system_app_info.get('extra', None),
            system_prompt=system_app_params.get('system_prompt', None),
            provider=provider
        ):
            yield f"event: answer\n{chunk}"
            
        
            
    except Exception as e:
        error_message = f"发生错误：{str(e)}"
        traceback.print_exc()
        logger.error(error_message)
        yield f"event: error\n{error_message}"


# # 建议路由
# @router.get("/suggestions")
# async def get_suggestions(
#     query: str = Query(..., description="搜索建议查询"),
#     current_user: dict = Depends(verify_token)
# ):
#     try:
#         # TODO: 实现搜索建议逻辑
#         return {"success": True, "data": []}
#     except Exception as e:
#         logger.error(f"获取搜索建议失败: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))

# # 聊天历史路由
# @router.get("/chats")
# async def get_chat_history(
#     current: int = Query(1, description="当前页码"),
#     pageSize: int = Query(10, description="每页数量"), 
#     current_user: dict = Depends(verify_token)
# ):
#     try:
#         # TODO: 实现聊天历史查询逻辑
#         return {
#             "success": True,
#             "data": [],
#             "total": 0,
#             "current": current,
#             "pageSize": pageSize
#         }
#     except Exception as e:
#         logger.error(f"获取聊天历史失败: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))

# 搜索路由
@router.get("/search")
async def search_content(
    chatResponse: ChatResponse,
    current_user: dict = Depends(verify_token)
):
    try:
        system_app_info: SystemAppSettingModel = await db["system_app_settings"].find_one({"app_info": chatResponse.app_info})
        if not system_app_info:
            raise HTTPException(status_code=404, detail="没有找到应用信息")
        
        messages_list = convert_messages(chatResponse.messages)
        system_app_params = system_app_info.get('params', {})
        # 获取默认的LLM模型
        if "llm_id" not in system_app_params:
            raise HTTPException(status_code=404, detail="没有找到LLM模型")
        llm: LLMModel = await db["llms"].find_one({"id": int(system_app_params.get('llm_id', None))})
        if not llm:
            raise HTTPException(status_code=404, detail="未找到默认LLM模型")
        
        # 创建搜索工作流实例
        search_workflow = SearchWorkflow(
            config=system_app_params,  # 可以根据需要传入配置
            user_id=current_user["id"],
            llm=llm
        )
        
        # 执行搜索
        result = await search_workflow.run(messages_list)
        
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        
        # 处理搜索结果
        search_results = result.get("relevant_sources", [])
        
        return {
            "success": True, 
            "data": search_results,
            "total": len(search_results)
        }
    except Exception as e:
        logger.error(f"搜索内容失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# # 发现路由
# @router.get("/discover")
# async def discover_content(
#     current: int = Query(1, description="当前页码"),
#     pageSize: int = Query(10, description="每页数量"),
#     current_user: dict = Depends(verify_token)
# ):
#     try:
#         # TODO: 实现内容发现逻辑
#         return {
#             "success": True,
#             "data": [],
#             "total": 0,
#             "current": current,
#             "pageSize": pageSize
#         }
#     except Exception as e:
#         logger.error(f"发现内容失败: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))

# # 高级搜索路由
# @router.post("/advanced-search")
# async def advanced_search(
#     messages: List[Dict[str, Any]] = Body(..., description="对话消息列表"),
#     current_user: dict = Depends(verify_token)
# ):
#     try:
#         # 获取默认的LLM模型
#         llm: LLMModel = await db["llms"].find_one({"is_default": True})
#         if not llm:
#             raise HTTPException(status_code=404, detail="未找到默认LLM模型")
        
#         # 创建搜索工作流实例
#         search_workflow = SearchWorkflow(
#             config={},  # 可以根据需要传入配置
#             user_id=current_user["id"],
#             llm=llm
#         )
        
#         # 提取最后一条用户消息作为查询
#         user_messages = [msg for msg in messages if msg.get("role") == "user"]
#         if not user_messages:
#             raise HTTPException(status_code=400, detail="未找到用户消息")
        
#         last_user_message = user_messages[-1]["content"]
        
#         # 执行搜索
#         result = await search_workflow.run(last_user_message)
        
#         if "error" in result:
#             raise HTTPException(status_code=500, detail=result["error"])
        
#         # 处理搜索结果
#         search_results = result.get("relevant_sources", [])
        
#         return {
#             "success": True, 
#             "data": search_results,
#             "total": len(search_results),
#             "context": messages  # 返回原始上下文
#         }
#     except Exception as e:
#         logger.error(f"高级搜索失败: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))
