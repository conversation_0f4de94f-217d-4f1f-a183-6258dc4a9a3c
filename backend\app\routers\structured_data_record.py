from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.structured_data_record import (
    StructuredDataRecord,
    StructuredDataRecordCreate,
    StructuredDataRecordUpdate,
    StructuredDataRecordResponse
)
from ..db.mongodb import db
from ..utils.auth import verify_token
from bson import ObjectId
from datetime import datetime

router = APIRouter(
    prefix="/api",
    tags=["structured_data_records"]
)

# 创建新的结构化数据记录
@router.post("/structured-data-records", response_model=StructuredDataRecordResponse)
async def create_structured_data_record(
    record: StructuredDataRecordCreate,
    current_user: dict = Depends(verify_token)
):
    new_record = record.dict()
    new_record["created_at"] = datetime.now()
    new_record["updated_at"] = datetime.now()
    result = await db["structured_data_records"].insert_one(new_record)
    created_record = await db["structured_data_records"].find_one({"_id": result.inserted_id})
    return StructuredDataRecordResponse(**created_record)

# 获取结构化数据记录列表
@router.get("/structured-data-records", response_model=Dict[str, Any])
async def read_structured_data_records(
    dataset_id: str,
    file_id: Optional[str] = None,
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    data: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"dataset_id": ObjectId(dataset_id)}
    if data:
        query["data"] = {"$regex": data, "$options": "i"}
    
    if file_id:
        query["file_id"] = ObjectId(file_id)   
    
    records = await db["structured_data_records"].find(query).sort("created_at", -1).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 转换 ObjectId 为字符串
    for record in records:
        record["id"] = str(record["_id"])
        record["dataset_id"] = str(record["dataset_id"])
        record["file_id"] = str(record["file_id"])
        del record["_id"]
    
    total = await db["structured_data_records"].count_documents(query)
    return {
        "data": [StructuredDataRecordResponse(**record) for record in records],
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }


# 获取单个结构化数据记录
@router.get("//structured-data-records/{record_id}", response_model=StructuredDataRecordResponse)
async def read_structured_data_record(
    record_id: str,
    current_user: dict = Depends(verify_token)
):
    record = await db["structured_data_records"].find_one({"_id": ObjectId(record_id)})
    if record is None:
        raise HTTPException(status_code=404, detail="Structured data record not found")
    return StructuredDataRecordResponse(**record)

# 更新结构化数据记录
@router.put("//structured-data-records/{record_id}", response_model=StructuredDataRecordResponse)
async def update_structured_data_record(
    record_id: str,
    record: StructuredDataRecordUpdate,
    current_user: dict = Depends(verify_token)
):
    update_data = record.dict(exclude_unset=True)
    update_data["updated_at"] = datetime.now()
    result = await db["structured_data_records"].update_one(
        {"_id": ObjectId(record_id)},
        {"$set": update_data}
    )
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Structured data record not found")
    updated_record = await db["structured_data_records"].find_one({"_id": ObjectId(record_id)})
    return StructuredDataRecordResponse(**updated_record)

# 删除结构化数据记录
@router.delete("/structured-data-records/{record_id}", response_model=dict)
async def delete_structured_data_record(
    record_id: str,
    current_user: dict = Depends(verify_token)
):
    result = await db["structured_data_records"].delete_one({"_id": ObjectId(record_id)})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Structured data record not found")
    return {"message": "Structured data record deleted successfully"}
