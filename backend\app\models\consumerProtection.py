from mongoengine import Document, StringField, ListField, DateTimeField, BooleanField, IntField, FloatField, DictField, ReferenceField, EmbeddedDocument, EmbeddedDocumentField, EmbeddedDocumentListField
from datetime import datetime
from pydantic import BaseModel
from typing import Optional, List, Dict, Any

# 引用类型
class QuoteInfo(EmbeddedDocument):
    regulation_name = StringField(required=True)  # 来源（原sourceName）
    provision = StringField(required=True)        # 规则（原a）
    requirement = StringField(required=True)      # 合规要求（原q）
    similarity_scores = ListField(DictField())    # 匹配分数（原score）
    tags = ListField(StringField())               # 标签分类
    effective_date = DateTimeField()              # 法规生效日期

class Issue(EmbeddedDocument):
    rule_id = StringField(required=True)          # 相关规则ID
    description = StringField(required=True)      # 问题描述
    location = StringField()                      # 问题位置
    severity = StringField()                      # 严重程度：高、中、低
    suggestion = StringField()                    # 修改建议

class ChunkContent(EmbeddedDocument):
    file_name = StringField(required=True)
    chunk_number = IntField(required=True)
    content = StringField(required=True)
    assessment_tag = StringField()                # 评估标签
    analysis_result = StringField()               # 分析结果
    thought_process = StringField()               # 思考过程
    suggestion = StringField()                    # 修改建议
    quote_list = EmbeddedDocumentListField(QuoteInfo)
    is_analyzed = BooleanField(default=False)     # 是否完成分析
    has_issue = BooleanField(default=False)       # 新增问题标记字段
    issues = EmbeddedDocumentListField(Issue)     # 问题列表
    related_rules = ListField(StringField())      # 相关规则IDs

class RuleEvaluationResult(EmbeddedDocument):
    rule_id = StringField(required=True)          # 规则ID
    rule_name = StringField(required=True)        # 规则名称
    description = ListField(StringField())        # 规则描述
    is_evaluated = BooleanField(default=False)    # 是否已评估
    evaluation_status = StringField()             # 评估状态：success, warning, error, failed
    evaluation_result = StringField()             # 评估结果：通过，部分通过，不通过
    analysis = StringField()                      # 分析内容
    overall_suggestion = StringField()            # 整体建议
    affected_chunks = ListField(IntField())       # 受影响的块索引

class ConsumerProtection(Document):
    meta = {
        'collection': 'consumer_protection_results'
    }
    file_id = StringField(required=True)                              # 文件ID
    file_name = StringField(required=True)                            # 文件名称
    file_type = StringField(required=True)                            # 文件类型 (PDF/图片)
    evaluation_type = StringField(required=True)                       # 评估类型
    app_info = StringField(required=False)                             # 应用信息
    chunk_contents = EmbeddedDocumentListField(ChunkContent)          # 文档内容块
    rule_results = EmbeddedDocumentListField(RuleEvaluationResult)    # 规则评估结果
    total_rules = IntField(default=0)                                 # 总规则数
    evaluated_rules = IntField(default=0)                             # 已评估规则数
    failed_rules = IntField(default=0)                                # 不通过规则数
    user_id = IntField()                                              # 用户ID
    user_name = StringField()                                         # 用户名
    is_completed = BooleanField(default=False)                        # 是否完成评估
    created_at = DateTimeField(default=datetime.now)                  # 创建时间
    updated_at = DateTimeField(default=datetime.now)                  # 更新时间
    is_deleted = BooleanField(default=False)                          # 是否已删除（软删除标记）

# 定义请求和响应模型
class ConsumerProtectionCreate(BaseModel):
    file_id: str
    file_name: str
    file_type: str
    evaluation_type: str
    app_info: Optional[str] = None
    chunk_contents: List[Dict[str, Any]]
    user_id: Optional[int] = None
    user_name: Optional[str] = None

class ConsumerProtectionUpdate(BaseModel):
    rule_results: Optional[List[Dict[str, Any]]] = None
    chunk_contents: Optional[List[Dict[str, Any]]] = None
    is_completed: Optional[bool] = None
    total_rules: Optional[int] = None
    evaluated_rules: Optional[int] = None
    failed_rules: Optional[int] = None

class ConsumerProtectionResponse(BaseModel):
    id: str
    file_id: str
    file_name: str
    file_type: str
    evaluation_type: str
    app_info: Optional[str] = None
    chunk_contents: List[Dict[str, Any]]
    rule_results: List[Dict[str, Any]]
    total_rules: int
    evaluated_rules: int
    failed_rules: int
    is_completed: bool
    user_id: Optional[int] = None
    user_name: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
