"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5099],{51042:function(r,n,t){var e=t(1413),u=t(67294),a=t(42110),i=t(91146),c=function(r,n){return u.createElement(i.Z,(0,e.Z)((0,e.Z)({},r),{},{ref:n,icon:a.Z}))},o=u.forwardRef(c);n.Z=o},23544:function(r,n,t){var e=t(97857),u=t.n(e),a=t(15009),i=t.n(a),c=t(99289),o=t.n(c),s=t(5574),p=t.n(s),f=t(67294),l=t(8232),d=t(17788),h=t(55102),m=t(34041),v=t(69044),y=t(85893);n.Z=function(r){var n=l.Z.useForm(),t=p()(n,1)[0],e=r.modalVisible,a=r.onCancel,c=r.onSubmit,s=r.values,w=(0,f.useState)([]),x=p()(w,2),b=x[0],k=x[1],g=(0,f.useState)([]),E=p()(g,2),_=E[0],N=E[1],T=(0,f.useState)(!0),j=p()(T,2),Z=j[0],S=j[1];return(0,f.useEffect)((function(){var r=function(){var r=o()(i()().mark((function r(){var n,t,e,u;return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return S(!0),r.prev=1,r.prev=2,r.next=5,(0,v.F3)();case 5:n=r.sent,(t=n)&&Array.isArray(t)?k(t):t&&t.data&&Array.isArray(t.data)&&k(t.data),r.next=13;break;case 10:r.prev=10,r.t0=r.catch(2),console.error("获取角色失败:",r.t0);case 13:return r.prev=13,r.next=16,(0,v.jA)();case 16:e=r.sent,(u=e)&&Array.isArray(u)?N(u):u&&u.data&&Array.isArray(u.data)&&N(u.data),r.next=24;break;case 21:r.prev=21,r.t1=r.catch(13),console.error("获取组织失败:",r.t1);case 24:r.next=29;break;case 26:r.prev=26,r.t2=r.catch(1),console.error("获取角色或组织失败",r.t2);case 29:return r.prev=29,S(!1),r.finish(29);case 32:case"end":return r.stop()}}),r,null,[[1,26,29,32],[2,10],[13,21]])})));return function(){return r.apply(this,arguments)}}();e&&r()}),[e]),(0,f.useEffect)((function(){e&&s&&!Z&&(console.log("设置表单初始值:",{id:s.id,name:s.name,phone:s.phone,role_id:s.role_id,group_id:s.group_id}),t.setFieldsValue({id:s.id,name:s.name,phone:s.phone,role_id:s.role_id,group_id:s.group_id}))}),[e,s,t,Z,b,_]),(0,y.jsx)(d.Z,{destroyOnClose:!0,title:"编辑用户",visible:e,onCancel:function(){return a(!1,s)},onOk:o()(i()().mark((function r(){var n;return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,t.validateFields();case 2:n=r.sent,console.log("提交表单数据:",n),c(u()(u()({},s),n));case 5:case"end":return r.stop()}}),r)}))),confirmLoading:Z,children:(0,y.jsxs)(l.Z,{form:t,layout:"vertical",initialValues:{id:null==s?void 0:s.id,name:null==s?void 0:s.name,phone:null==s?void 0:s.phone,role_id:null==s?void 0:s.role_id,group_id:null==s?void 0:s.group_id},children:[(0,y.jsx)(l.Z.Item,{name:"name",label:"姓名",rules:[{required:!0,message:"请输入姓名"}],children:(0,y.jsx)(h.Z,{placeholder:"请输入姓名"})}),(0,y.jsx)(l.Z.Item,{name:"phone",label:"手机号",rules:[{required:!0,message:"请输入手机号"}],children:(0,y.jsx)(h.Z,{placeholder:"请输入手机号"})}),(0,y.jsx)(l.Z.Item,{name:"role_id",label:"选择权限",rules:[{required:!0,message:"请选择权限"}],children:(0,y.jsx)(m.default,{placeholder:"请选择权限",loading:Z,children:b.map((function(r){return(0,y.jsx)(m.default.Option,{value:r.id,children:r.name},r.id)}))})}),(0,y.jsx)(l.Z.Item,{name:"group_id",label:"选择组织",rules:[{required:!0,message:"请选择组织"}],children:(0,y.jsx)(m.default,{placeholder:"请选择组织",loading:Z,children:_.map((function(r){return(0,y.jsx)(m.default.Option,{value:r.id,children:r.name},r.id)}))})})]})})}},69044:function(r,n,t){t.d(n,{CW:function(){return F},F3:function(){return Z},Nq:function(){return h},Rd:function(){return G},Rf:function(){return p},Rp:function(){return g},_d:function(){return A},az:function(){return w},cY:function(){return R},cn:function(){return l},h8:function(){return v},iE:function(){return T},jA:function(){return b},mD:function(){return _},ul:function(){return P},w1:function(){return I},wG:function(){return V}});var e=t(5574),u=t.n(e),a=t(15009),i=t.n(a),c=t(99289),o=t.n(c),s=t(78158);function p(r){return f.apply(this,arguments)}function f(){return(f=o()(i()().mark((function r(n){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/users",{method:"GET",params:n}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function l(r){return d.apply(this,arguments)}function d(){return(d=o()(i()().mark((function r(n){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/users",{method:"POST",data:n}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function h(r){return m.apply(this,arguments)}function m(){return(m=o()(i()().mark((function r(n){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/users/".concat(n.id),{method:"PUT",data:n}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function v(r){return y.apply(this,arguments)}function y(){return(y=o()(i()().mark((function r(n){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/users/".concat(n),{method:"DELETE"}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function w(r,n){return x.apply(this,arguments)}function x(){return(x=o()(i()().mark((function r(n,t){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/users/changeStatus",{method:"POST",data:{id:n,status:t}}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function b(r){return k.apply(this,arguments)}function k(){return(k=o()(i()().mark((function r(n){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/groups",{method:"GET",params:n}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function g(r){return E.apply(this,arguments)}function E(){return(E=o()(i()().mark((function r(n){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/groups",{method:"POST",data:n}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function _(r){return N.apply(this,arguments)}function N(){return(N=o()(i()().mark((function r(n){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/groups/".concat(n.id),{method:"PUT",data:n}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function T(r){return j.apply(this,arguments)}function j(){return(j=o()(i()().mark((function r(n){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/groups/".concat(n),{method:"DELETE"}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function Z(r){return S.apply(this,arguments)}function S(){return(S=o()(i()().mark((function r(n){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/roles",{method:"GET",params:n}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function A(r){return O.apply(this,arguments)}function O(){return(O=o()(i()().mark((function r(n){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/roles",{method:"POST",data:n}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function P(r,n){return C.apply(this,arguments)}function C(){return(C=o()(i()().mark((function r(n,t){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/roles/".concat(n),{method:"PUT",data:t}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function G(r){return q.apply(this,arguments)}function q(){return(q=o()(i()().mark((function r(n){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/roles/".concat(n),{method:"DELETE"}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function F(){return L.apply(this,arguments)}function L(){return(L=o()(i()().mark((function r(){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/role/tree",{method:"GET"}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function R(r){return D.apply(this,arguments)}function D(){return(D=o()(i()().mark((function r(n){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/roles/".concat(n),{method:"GET"}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function I(r){return U.apply(this,arguments)}function U(){return(U=o()(i()().mark((function r(n){var t;return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return t=new URLSearchParams,Object.entries(n).forEach((function(r){var n=u()(r,2),e=n[0],a=n[1];t.append(e,a)})),r.abrupt("return",(0,s.N)("/api/system/config?".concat(t.toString()),{method:"POST"}));case 3:case"end":return r.stop()}}),r)})))).apply(this,arguments)}function V(r){return z.apply(this,arguments)}function z(){return(z=o()(i()().mark((function r(n){return i()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.abrupt("return",(0,s.N)("/api/useActiveCases",{method:"GET",params:n}));case 1:case"end":return r.stop()}}),r)})))).apply(this,arguments)}}}]);