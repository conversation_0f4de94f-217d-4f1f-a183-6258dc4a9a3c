from fastapi import APIRouter, HTTPException, Depends, Query, UploadFile, File
from typing import List, Optional, Dict, Any, List
from ..models.dataset_file import (
    DatasetFile,
    DatasetFileCreate,
    DatasetFileUpdate,
    DatasetFileResponse
)
from ..db.mongodb import db
from ..utils.auth import verify_token
from bson import ObjectId
from datetime import datetime
import os
from dotenv import load_dotenv

# 加载 .env 文件中的环境变量
load_dotenv()

router = APIRouter(
    prefix="/api",
    tags=["dataset_files"]
)

FILE_UPLOAD_PATH = os.getenv("FILE_UPLOAD_PATH")

def get_file_format(filename: str) -> str:
    """根据文件后缀返回文件格式"""
    extension = os.path.splitext(filename)[1].lower()
    format_map = {
        '.csv': 'CSV',
        '.pdf': 'PDF',
        '.docx': 'DOCX',
        '.doc': 'DOC',
        '.xls': 'Excel',
        '.xlsx': 'Excel',
        '.json': 'JSON',
        '.jsonl': 'JSONL',
    }
    return format_map.get(extension, 'Unknown')

# 创建新的数据集文件
@router.post("/dataset-files", response_model=Dict[str, Any])
async def create_dataset_file(
    file: DatasetFileCreate,
    current_user: dict = Depends(verify_token)
):
    new_file = file.dict()
    new_file["created_at"] = datetime.now()
    result = await db["dataset_files"].insert_one(new_file)
    created_file = await db["dataset_files"].find_one({"_id": result.inserted_id})
    return {
        "data": DatasetFileResponse(**created_file),
        "success": True
    }


# 多文件上传接口 /api/dataset-files/upload
@router.post("/dataset-files/upload", response_model=List[DatasetFileResponse])
async def upload_files(
    dataset_id: str,
    dataset_type: str,  # 新增 dataset_type 参数
    files: List[UploadFile] = File(...),
    tags: Optional[str] = Query(None),  # 添加tags参数
    current_user: dict = Depends(verify_token)
):
    print('upload_files',dataset_id,dataset_type,tags,files)
    # 使用 pathlib 处理路径
    file_upload_path = Path(FILE_UPLOAD_PATH)
    if not file_upload_path.exists():
        file_upload_path.mkdir(parents=True)

    # 处理 tags 字符串，转换为列表
    tag_list = tags.split(',') if tags else []

    uploaded_files = []
    try:
        for file in files:
            file_location = file_upload_path / file.filename
            # 使用 aiofiles 异步读写文件
            async with aiofiles.open(file_location, "wb") as f:
                await f.write(await file.read())

            file_format = get_file_format(file.filename)

            new_file = {
                "name": file.filename,
                "storage_path": str(file_location),
                "dataset_id": ObjectId(dataset_id),
                "dataset_type": dataset_type,  # 添加 dataset_type 字段
                "data_type": file_format,
                "processing_status": "pending",
                "created_at": datetime.now(),
                "user_id": current_user["id"],
                "user_name": current_user["name"],
                "tags": tag_list  # 添加tags字段
            }
            result = await db["dataset_files"].insert_one(new_file)
            # 直接将插入的 ID 添加到 new_file，避免再次查询
            new_file["_id"] = result.inserted_id
            uploaded_files.append(DatasetFileResponse(**new_file))

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

    return uploaded_files

# 获取数据集文件列表
@router.get("/dataset-files", response_model=Dict[str, Any])
async def read_dataset_files(
    dataset_id: str,
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    name: Optional[str] = None,
    tags: Optional[str] = None,
    processing_status: Optional[str] = None,
    data_type: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"dataset_id": ObjectId(dataset_id)}
    print('query',query)
    
    # 添加搜索条件
    if name:
        query["name"] = {"$regex": name, "$options": "i"}
    if processing_status:
        query["processing_status"] = processing_status
    if tags:
        query["tags"] = tags
    if data_type:
        query["data_type"] = data_type

    files = await db["dataset_files"].find(query).sort("created_at", -1).skip(skip).limit(pageSize).to_list(length=pageSize)
    total = await db["dataset_files"].count_documents(query)
    for file in files:
        file["id"] = str(file["_id"]) 
        file["dataset_id"] = str(file["dataset_id"])
        del file["_id"]

    
    return {
        "data": [DatasetFileResponse(**file) for file in files],
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取单个数据集文件
@router.get("/dataset-files/{file_id}", response_model=DatasetFileResponse)
async def read_dataset_file(
    file_id: str,
    current_user: dict = Depends(verify_token)
):
    file = await db["dataset_files"].find_one({"_id": ObjectId(file_id)})
    if file is None:
        raise HTTPException(status_code=404, detail="Dataset file not found")
    return DatasetFileResponse(**file)

# 更新数据集文件
@router.put("/dataset-files/{file_id}", response_model=DatasetFileResponse)
async def update_dataset_file(
    file_id: str,
    file: DatasetFileUpdate,
    current_user: dict = Depends(verify_token)
):
    update_data = file.dict(exclude_unset=True)
    result = await db["dataset_files"].update_one(
        {"_id": ObjectId(file_id)},
        {"$set": update_data}
    )
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Dataset file not found")
    updated_file = await db["dataset_files"].find_one({"_id": ObjectId(file_id)})
    return DatasetFileResponse(**updated_file)

# 删除数据集文件
@router.delete("/dataset-files/{file_id}", response_model=dict)
async def delete_dataset_file(
    file_id: str,
    current_user: dict = Depends(verify_token)
):
    result = await db["dataset_files"].delete_one({"_id": ObjectId(file_id)})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Dataset file not found")
    return {"message": "Dataset file deleted successfully"}
