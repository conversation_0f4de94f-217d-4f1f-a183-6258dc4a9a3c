from mongoengine import Document, ObjectIdField,StringField, IntField, DateTimeField, FloatField, DictField, ListField, BooleanField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, Dict, List
from bson import ObjectId

# MongoEngine 模型
class Chunk(Document):
    meta = {
        'collection': 'chunks',
        'indexes': [
            {
                'fields': 'knowledge_base_id',
            },
            {
                'fields': 'file_id',
            },
            {
                'fields': ['answer', 'question', 'chunk_type'],
                'unique': True,
                'sparse': True,
                'background': True,
                'type': 'hashed'  # 使用哈希索引
            }
        ]
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId())
    file_id = ObjectIdField() # 问价名
    file_name = StringField() # 问价名
    page_id = StringField() # 页面id
    knowledge_base_id = ObjectIdField(required=True) # 知识库id
    chunk_index = IntField(required=True) # 排序
    answer = StringField(required=True,default="")
    question = StringField(required=True,default="")
    tokens_count = IntField(required=True,default=0)
    embedding = ListField(FloatField())
    metadata = DictField()
    chunk_type = StringField(required=True)
    created_at = DateTimeField(required=True,default=datetime.now)
    last_updated = DateTimeField(required=True,default=datetime.now)
    app_info = StringField()
    is_expired = BooleanField(required=True,default=False)  # 新增字段

# Pydantic 模型
class ChunkBase(BaseModel):
    file_id: str
    knowledge_base_id: str
    chunk_index: int
    answer: str
    question: str
    chunk_type: str
    embedding: Optional[List[float]] = None
    metadata: Optional[Dict] = Field(default_factory=dict)
    app_info: Optional[str] = None
    is_expired: bool = False  # 新增字段

class ChunkCreate(ChunkBase):
    pass

class ChunkUpdate(BaseModel):
    content: Optional[str] = None
    embedding: Optional[List[float]] = None
    metadata: Optional[Dict] = None
    is_expired: Optional[bool] = None  # 新增字段

class ChunkResponse(ChunkBase):
    id: str
    created_at: datetime
    last_updated: datetime

    class Config:
        from_attributes = True
