"""LangGraph 金融风险分析代理。

该图支持通过API参数传递数据库连接信息和文件路径，实现客户信用风险分析。
"""

from __future__ import annotations

from typing import Dict, List, Optional, Any, TypedDict
import os

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END


class Configuration(TypedDict):
    """代理的可配置参数。
    
    可以在创建助手或调用图时设置这些参数。
    """
    
    # 数据库配置
    db_url: Optional[str]
    db_username: Optional[str]
    db_password: Optional[str]
    
    # 文件配置
    risk_policy_path: Optional[str]
    
    # LLM配置
    model_name: Optional[str]
    temperature: Optional[float]
    api_key: Optional[str]


class State(TypedDict):
    """代理的输入状态。
    
    定义传入数据的初始结构。
    """
    
    # 输入查询
    query: str
    
    # 数据库查询结果
    db_results: Optional[Dict[str, Any]]
    
    # 文件内容
    policy_data: Optional[Dict[str, Any]]
    
    # 分析结果
    analysis: Optional[str]
    
    # 消息历史
    messages: List[Dict[str, str]]


# 模拟数据库访问函数
def query_database(config: Configuration, query: str) -> Dict[str, Any]:
    """从数据库获取客户信息（模拟）"""
    
    # 打印连接信息以验证参数传递
    print(f"连接到数据库: {config.get('db_url', '未提供URL')}")
    print(f"使用用户名: {config.get('db_username', '未提供用户名')}")
    
    # 模拟客户数据 - 实际应用中会从真实数据库读取
    if "客户" in query or "信用" in query or "风险" in query:
        return {
            "customers": [
                {"id": 1, "name": "张三", "credit_score": 780, "loan_amount": 50000, "risk_level": "低"},
                {"id": 2, "name": "李四", "credit_score": 650, "loan_amount": 100000, "risk_level": "中"},
                {"id": 3, "name": "王五", "credit_score": 520, "loan_amount": 200000, "risk_level": "高"}
            ]
        }
    return {"result": "未找到相关数据"}


# 模拟文件读取函数
def read_policy_file(file_path: str) -> Dict[str, Any]:
    """读取风险政策文件（模拟）"""
    
    print(f"读取文件: {file_path}")
    
    # 模拟政策数据 - 实际应用中会从真实文件读取
    if file_path and ("policy" in file_path.lower() or "风险" in file_path):
        return {
            "policies": [
                "信用分数低于600为高风险客户，需要特别审查",
                "信用分数600-700为中等风险客户，贷款额度不超过10万",
                "信用分数高于700为低风险客户，可以提供更优惠的利率"
            ]
        }
    return {"warning": "未找到风险政策文件或文件路径无效"}


# 节点函数

async def retrieve_customer_data(state: State, config: Dict[str, Any]) -> State:
    """从数据库获取客户数据的节点"""
    
    query = state["query"]
    
    try:
        # 获取数据库查询结果
        db_results = query_database(config["configurable"], query)
        return {**state, "db_results": db_results}
    except Exception as e:
        return {**state, "db_results": {"error": f"数据库查询失败: {str(e)}"}}


async def retrieve_risk_policy(state: State, config: Dict[str, Any]) -> State:
    """获取风险政策的节点"""
    
    try:
        # 获取配置中的文件路径
        file_path = config["configurable"].get("risk_policy_path", "")
        
        if file_path:
            policy_data = read_policy_file(file_path)
        else:
            policy_data = {"warning": "未提供风险政策文件路径"}
            
        return {**state, "policy_data": policy_data}
    except Exception as e:
        return {**state, "policy_data": {"error": f"读取风险政策失败: {str(e)}"}}


async def analyze_risk(state: State, config: Dict[str, Any]) -> State:
    """分析客户风险的节点"""
    
    query = state["query"]
    db_results = state.get("db_results", {})
    policy_data = state.get("policy_data", {})
    
    # 获取LLM配置
    model_name = config["configurable"].get("model_name", "gpt-3.5-turbo")
    temperature = config["configurable"].get("temperature", 0.7)
    api_key = config["configurable"].get("api_key") or os.getenv("OPENAI_API_KEY")
    
    # 准备消息
    messages = state.get("messages", [])
    
    try:
        if not api_key:
            return {**state, "analysis": "错误: 未提供OpenAI API密钥", "messages": messages}
        
        # 创建LLM
        llm = ChatOpenAI(
            model=model_name,
            temperature=temperature,
            openai_api_key=api_key
        )
        
        # 构建系统提示
        system_message = SystemMessage(
            content="你是一位资深的金融风险分析师，专门评估客户的信用风险。请基于提供的客户数据和风险政策，提供专业的风险评估和建议。"
        )
        
        # 构建用户消息
        user_content = f"""
        查询: {query}
        
        客户数据: {db_results}
        
        风险政策: {policy_data}
        
        请分析这些客户的信用风险，并根据风险政策提供评估和建议。
        """
        
        human_message = HumanMessage(content=user_content)
        
        # 生成回答
        response = await llm.ainvoke([system_message, human_message])
        analysis = response.content
        
        # 更新消息历史
        new_messages = messages + [
            {"role": "system", "content": system_message.content},
            {"role": "user", "content": user_content},
            {"role": "assistant", "content": analysis}
        ]
        
        return {**state, "analysis": analysis, "messages": new_messages}
    except Exception as e:
        return {**state, "analysis": f"分析风险时出错: {str(e)}", "messages": messages}


async def decide_next_step(state: State, config: Dict[str, Any]) -> str:
    """决定下一步操作"""
    # 简单实现，直接结束流程
    return END


# 构建图
graph = (
    StateGraph(State, config_schema=Configuration)
    .add_node("retrieve_customer_data", retrieve_customer_data)
    .add_node("retrieve_risk_policy", retrieve_risk_policy)
    .add_node("analyze_risk", analyze_risk)
    .set_entry_point("retrieve_customer_data")
    .add_edge("retrieve_customer_data", "retrieve_risk_policy")
    .add_edge("retrieve_risk_policy", "analyze_risk")
    .add_edge("analyze_risk", decide_next_step)
    .compile(name="金融风险分析代理")
)
