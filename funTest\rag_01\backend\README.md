这是一个使用 [FastAPI](https://fastapi.tiangolo.com/) 的 [LlamaIndex](https://www.llamaindex.ai/) 项目,使用 [`create-llama`](https://github.com/run-llama/LlamaIndexTS/tree/main/packages/create-llama) 引导创建。

## 开始使用

首先,使用 poetry 设置环境:

> **_注意:_** 如果您使用 dev-container,则不需要此步骤。

```
poetry install
poetry shell
```

然后检查此目录中 `.env` 文件中预先配置的参数。(例如,如果您使用 OpenAI 作为模型提供商,则可能需要配置 `OPENAI_API_KEY`)。

如果您使用任何工具或数据源,可以在 `config` 文件夹中更新它们的配置文件。

其次,生成 `./data` 目录中文档的嵌入向量(如果该文件夹存在 - 否则,跳过此步骤):

```
poetry run generate
```

第三,运行开发服务器:

```
python main.py
```

该示例提供两个不同的 API 端点:

1. `/api/chat` - 流式聊天端点
2. `/api/chat/request` - 非流式聊天端点

您可以使用以下 curl 请求测试流式端点:

```
curl --location 'localhost:8000/api/chat' \
--header 'Content-Type: application/json' \
--data '{ "messages": [{ "role": "user", "content": "Hello" }] }'
```

对于非流式端点,运行:

```
curl --location 'localhost:8000/api/chat/request' \
--header 'Content-Type: application/json' \
--data '{ "messages": [{ "role": "user", "content": "Hello" }] }'
```

您可以通过修改 `app/api/routers/chat.py` 开始编辑 API 端点。保存文件时端点会自动更新。您可以删除不使用的端点。

在浏览器中打开 [http://localhost:8000/docs](http://localhost:8000/docs) 查看 API 的 Swagger UI。

API 允许所有来源的 CORS 以简化开发。您可以通过将 `ENVIRONMENT` 环境变量设置为 `prod` 来更改此行为:

```
ENVIRONMENT=prod python main.py
```

## 使用 Docker

1. 为 FastAPI 应用构建镜像:

```
docker build -t <your_backend_image_name> .
```

2. 生成嵌入向量:

如果 `./data` 文件夹存在,解析数据并生成向量嵌入 - 否则,跳过此步骤:

```
docker run \
  --rm \
  -v $(pwd)/.env:/app/.env \ # 使用您文件系统中的 ENV 变量和配置
  -v $(pwd)/config:/app/config \
  -v $(pwd)/data:/app/data \ # 使用您的本地文件夹读取数据
  -v $(pwd)/storage:/app/storage \ # 使用您的文件系统存储向量数据库
  <your_backend_image_name> \
  poetry run generate
```

3. 启动 API:

```
docker run \
  -v $(pwd)/.env:/app/.env \ # 使用您文件系统中的 ENV 变量和配置
  -v $(pwd)/config:/app/config \
  -v $(pwd)/storage:/app/storage \ # 使用您的文件系统存储向量数据库
  -p 8000:8000 \
  <your_backend_image_name>
```

## 了解更多

要了解更多关于 LlamaIndex 的信息,请查看以下资源:

- [LlamaIndex 文档](https://docs.llamaindex.ai) - 了解 LlamaIndex。

您可以查看 [LlamaIndex GitHub 仓库](https://github.com/run-llama/llama_index) - 欢迎您的反馈和贡献!
