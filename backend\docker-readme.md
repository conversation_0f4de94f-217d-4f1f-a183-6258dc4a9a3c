# Docker 部署说明

## 环境要求
- Docker 20.10+
- Docker Compose 2.0+

## 目录结构
```
backend/
├── Dockerfile              # Docker 构建文件
├── docker-compose.yml      # Docker Compose 配置文件
├── .env.production         # 生产环境配置
├── requirements.txt        # Python 依赖
└── app/                    # 应用代码
```

## 快速开始

### 1. 配置环境变量
确保 `.env.production` 文件包含所有必要的环境变量：
```bash
ENV=production
DEBUG=false
PORT=8800
MONGODB_URL=mongodb://mongodb:27017
MONGODB_DB_NAME=preloan_assistant_prod
MONGODB_USER=your_username
MONGODB_PASSWORD=your_password
```

### 2. 构建镜像
```bash
docker-compose build
```

### 3. 启动服务
```bash
docker-compose up -d
```

### 4. 查看日志
```bash
docker-compose logs -f api
```

### 5. 停止服务
```bash
docker-compose down
```

## 部署说明

### 1. 生产环境配置
- 使用 `.env.production` 环境变量文件
- 禁用调试模式
- 使用生产级别的 MongoDB 配置

### 2. 数据持久化
- MongoDB 数据存储在 Docker volume 中
- 静态文件通过 volume 映射到容器

### 3. 安全配置
- 使用非 root 用户运行应用
- 环境变量通过 Docker secrets 管理
- 网络隔离使用 Docker network

### 4. 性能优化
- 使用 Python 3.9 slim 镜像减小体积
- 多阶段构建优化镜像大小
- 合理配置 MongoDB 连接池

### 5. 监控和维护
- 配置容器自动重启
- 日志收集和管理
- 健康检查和监控

## 常见问题

### 1. 数据库连接失败
检查 MongoDB 连接配置：
```bash
docker-compose exec api python -c "from app.db.mongodb import db; print(db.client.server_info())"
```

### 2. 权限问题
确保 volume 目录权限正确：
```bash
sudo chown -R 1000:1000 ./static
```

### 3. 端口冲突
修改 docker-compose.yml 中的端口映射：
```yaml
ports:
  - "8801:8800"  # 将主机端口改为 8801
```

## 更新部署

### 1. 拉取最新代码
```bash
git pull origin main
```

### 2. 重新构建镜像
```bash
docker-compose build
```

### 3. 更新服务
```bash
docker-compose up -d --no-deps api
```

## 备份和恢复

### 1. 备份数据库
```bash
docker-compose exec mongodb mongodump --out /backup
```

### 2. 恢复数据库
```bash
docker-compose exec mongodb mongorestore /backup
``` 