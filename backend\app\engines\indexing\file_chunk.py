import os
# import sys
# import logging

# # 获取backend目录的绝对路径
# current_dir = os.path.dirname(os.path.abspath(__file__))  # indexing目录
# parent_dir = os.path.dirname(
#     os.path.dirname(os.path.dirname(current_dir))
# )  # backend目录
# sys.path.append(parent_dir)

# 然后再导入app相关模块
from abc import ABC, abstractmethod
from typing import List, Dict, Any
from app.utils.logging_config import setup_logging, get_logger
from app.utils.enums import FileStatus, FileStorageType
from markitdown import MarkItDown
import subprocess
import shutil
from pdf2image import convert_from_path
import cv2
from minio import Minio
import io
from tqdm import tqdm
import base64
import pandas as pd
import numpy as np
import requests
from app.engines.indexing import layout_sort
from app.utils.global_interface import chunk, chunk_index
import re
from app.utils.enums import chunk_type
import traceback
import tiktoken
import os

from dotenv import load_dotenv

load_dotenv()
enc = tiktoken.encoding_for_model(os.getenv("TOKENS_COUNT_MODEL","gpt-4"))
# from app.utils.enums import FileStatus,FileStorageType

# # 设置基本的日志配置
# logging.basicConfig(
#     level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
# )
# logger = logging.getLogger(__name__)
setup_logging()
logger = get_logger(__name__)


doclayout_names = {
    0: 'title', 
    1: 'plain text', 
    2: 'abandon', 
    3: 'figure', 
    4: 'figure_caption', 
    5: 'table', 
    6: 'table_caption', 
    7: 'table_footnote', 
    8: 'isolate_formula', 
    9: 'formula_caption'
}
def file_convert_to_markdown_base(file_path: str, file_type: str):
    
    # md = MarkItDown()
    # result = md.convert(file_path)
    # return result.text_content
    md = MarkItDown()
    if file_type.lower() == "pdf":
        result = md.convert(file_path)
        return result.text_content
    elif file_type.lower() == "docx":
        result = md.convert(file_path)
        return result.text_content
    elif file_type.lower() == "doc":
        result = md.convert(file_path)
        return result.text_content
    elif file_type.lower() == "txt":
        result = md.convert(file_path)
        return result.text_content 
    elif file_type.lower() == "json":
        result = md.convert(file_path)
        return result.text_content
    elif file_type.lower() == "excel":
        result = md.convert(file_path)
        return result.text_content
    else:
        raise ValueError("文件类型不支持")



def file_convert_to_markdown(file_path: str, file_data: Dict[str, Any]):
    
    # md = MarkItDown()
    # result = md.convert(file_path)
    # return result.text_content


    md = MarkItDown()
    file_type = file_data.get('data_type')
    if file_type == "PDF":
        result = md.convert(file_path)
        return result.text_content
    elif file_type == "Word":
        result = md.convert(file_path)
        return result.text_content
    elif file_type == "TXT":
        result = md.convert(file_path)
        return result.text_content 
    elif file_type == "JSON":
        result = md.convert(file_path)
        return result.text_content
    elif file_type == "EXCEL":
        result = md.convert(file_path)
        return result.text_content
    else:
        raise ValueError("文件类型不支持")


#####################################基于OCR的文档解析#####################################
def file_analyze_to_markdown(file_path: str) -> str:
    """获取带ocr的文档解析结果"""
    # 解析文件名
    file_name = file_path.split("/")[-1]  # 文件名，带扩展名
    file_actual_name = file_name.rsplit(".", 1)[0]  # 文件名，不带扩展名
    file_type = (file_name.rsplit(".", 1)[1]).lower()  # 文件扩展名，不带.
    # 定义输出文件夹路径
    result_folder_path = "./backend/app/engines/indexing/results/" + file_actual_name + "_" + file_type
    # result_folder_path = "./results/" + file_actual_name + "_" + file_type
    # 重新创建输出文件夹，每次重新解析同一文件时保证输出结果为最新
    clean_and_create_directory(result_folder_path)

    # 开始解析...   txt、markdown、word、pdf、excel、ppt、html
    # 1.解析txt、markdown文件
    if file_type == "txt":
        markdown_output_path = txt_parser(file_path)

    # 2.解析excel文件
    elif file_type in ["xls", "xlsx"]:
        if file_type == "xls":
            # print("转为xlsx")
            xlsx_file = file_path.split(".xls")[0] + ".xlsx"
            file_path = xls2xslx(file_path, xlsx_file)
            markdown_output_path = excel_parser(
                file_path, file_actual_name, result_folder_path
            )

    # 3.解析pdf文件
    elif file_type == "pdf":
        markdown_output_path = pdf_parser(
            file_path, file_actual_name, result_folder_path
        )

    # 4.解析word、ppt文件，将word、ppt转为pdf处理
    elif file_type in ["doc", "ppt", "docx", "pptx"]:
        convert_result = convert_to_pdf(file_path, result_folder_path)
        if convert_result:
            # 'doc', 'ppt', 'docx', 'pptx'转为'pdf'
            file_path = result_folder_path + "/" + file_actual_name + ".pdf"
            markdown_output_path = pdf_parser(
                file_path, file_actual_name, result_folder_path
            )
        else:
            print("Failed to convert pdf!")
    else:
        print("Unsupported file type: {}".format(file_type))

    # print("文件处理完成：", file_name)
    with open(markdown_output_path) as file:
        markdown_text = file.read()

    # 删除输出文件夹
    shutil.rmtree(result_folder_path)
    
    return markdown_text


def pdf_parser(file_path, file_actual_name, result_folder_path):
    markdown_output_path = result_folder_path + "/" + file_actual_name + ".md"
    # convert pdf to images
    images = convert_from_path(file_path)

    # Initiate title level index
    # title_indexs = []

    # Image Processing Phase
    for image_idx, image in enumerate(tqdm(images, desc="Processing file:")):
        # Save image to temporary path
        convert_images_path = result_folder_path + "/convert_images"
        if not os.path.exists(convert_images_path):
            os.makedirs(convert_images_path)
        convert_image_path = convert_images_path + "/" + file_actual_name + "_" + str(image_idx + 1) + ".png"
        image.save(convert_image_path, "PNG")

        # layout model predict
        layout_analyze_result = layout_analyze(convert_image_path)
        boxes, classes = layout_analyze_result["boxes"], layout_analyze_result["classes"]
        
        # 用opencv裁剪并根据区块标签进行处理，完毕后保存至Markdown文件中
        with open(markdown_output_path, "a") as file:
            if len(classes) > 0:
                opencv_image_process(result_folder_path, convert_image_path, boxes, classes, image_idx, file, file_actual_name)
                    
    # 移除重复识别的区块
    remove_duplicate_lines(markdown_output_path)
    # 合并跨页表格
    # combine_tables(markdown_output_path)

    return markdown_output_path


def clean_and_create_directory(path):
    if not os.path.exists(path):
        os.makedirs(path)
    else:
        shutil.rmtree(path)
        os.makedirs(path)


def txt_parser(file_path):
    with open(file_path, "r") as file:
        text = file.read()

    md_path = ""
    with open(md_path, "a") as file:
        file.write(text)

    return md_path


def xls2xslx(xls_file, xlsx_file):

    # 使用 pandas 读取 .xls 文件
    df = pd.read_excel(xls_file, sheet_name=None)  # sheet_name=None 读取所有的sheet

    # 将数据写入到 .xlsx 文件
    with pd.ExcelWriter(xlsx_file, engine="openpyxl") as writer:
        # 将每个sheet写入到 .xlsx 文件
        for sheet_name, data in df.items():
            data.to_excel(writer, sheet_name=sheet_name, index=False)
    return xlsx_file


def excel_parser(file_path):
    file_data = {"data_type": "EXCEL"}
    return file_convert_to_markdown(file_path, file_data)


def convert_to_pdf(file_path, output_dir):
    """Convert docx and ppt to pdf"""
    try:
        result = subprocess.run(
            [
                "libreoffice",
                "--headless",
                "--convert-to",
                "pdf",
                "--outdir",
                output_dir,
                file_path,
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
        if result.returncode == 0:
            return True
        else:
            return False
    except Exception as e:
        traceback.print_exc()
        return False


def remove_duplicate_lines(file_path):
    content = ""
    with open(file_path, "r") as file:
        content = file.read()
    if not content == "":
        content = content.replace("（", "(")
        content = content.replace("）", ")")
        content = content.replace("．", ".")
        blocks = content.split("\n\n")
        unique_blocks = [blocks[0]]
        for block in blocks[1:]:
            if block != unique_blocks[-1]:
                unique_blocks.append(block)

        with open(file_path, "w") as file:
            file.write("\n\n".join(unique_blocks))


def opencv_image_process(
    result_folder_path,
    convert_image_path,
    result_box,
    result_classes,
    image_idx,
    md_file,
    big_file_name,
):
    # 加载环境变量
    ocr_bounding_box_x_dimension_widen = int(os.getenv("ocr_bounding_box_x_dimension_widen"))
    ocr_bounding_box_y_dimension_widen = int(os.getenv("ocr_bounding_box_y_dimension_widen"))
    # doclayout_names = eval(os.getenv("doclayout_names"))
    upload_to_minio = eval(os.getenv("upload_to_minio"))

    # 使用cv2读取暂时保存的图像
    recognized_img = cv2.imread(convert_image_path)

    # layout_xy排序算法
    sorted_box_index = layout_sort.layout_sort_xy(result_box)

    # 处理已排序的识别区块
    for i, j in enumerate(tqdm(sorted_box_index, desc=f"Processing Page {image_idx + 1}: ", leave=False)):
        # 获取该区块识别出的标签id和名字
        type_id = int(result_classes[j])
        name = doclayout_names[type_id]

        # 若为页眉/页脚，直接忽略
        if name == "abandon":
            continue

        # 获取边界框的位置并且取整，比识别出的框大一点以避免ocr漏/误识别
        top_left_x, top_left_y, bottom_right_x, bottom_right_y = result_box[j]
        # top_left_x = int(np.floor(top_left_x.cpu()))
        # top_left_y = int(np.floor(top_left_y.cpu()))
        # bottom_right_x = int(np.ceil(bottom_right_x.cpu()))
        # bottom_right_y = int(np.ceil(bottom_right_y.cpu()))
        top_left_x = int(np.floor(top_left_x))
        top_left_y = int(np.floor(top_left_y))
        bottom_right_x = int(np.ceil(bottom_right_x))
        bottom_right_y = int(np.ceil(bottom_right_y))
        if name != "table" and name != "figure":
            top_left_x = max(0, top_left_x - ocr_bounding_box_x_dimension_widen)
            top_left_y = max(0, top_left_y - ocr_bounding_box_y_dimension_widen)
            bottom_right_x = min(recognized_img.shape[1], bottom_right_x + ocr_bounding_box_x_dimension_widen)
            bottom_right_y = min(recognized_img.shape[0], bottom_right_y + ocr_bounding_box_y_dimension_widen)

        # 裁剪调整完毕的边界框并保存裁剪后的图像
        cropped_image_np = recognized_img[top_left_y:bottom_right_y, top_left_x:bottom_right_x]
        cropped_images_path = result_folder_path + "/cropped_images/page_" + str(image_idx)
        if not os.path.exists(cropped_images_path):
            os.makedirs(cropped_images_path)
        cropped_image_path = cropped_images_path + "/" + name + "_" + str(i + 1) + ".jpg"
        cv2.imwrite(cropped_image_path, cropped_image_np)
        
        
        # 若为图像，直接保存至输出文件夹，并将路径写入Markdown文件
        if name == "figure":
            figures_path = result_folder_path + "/figures/page_" + str(image_idx)
            if not os.path.exists(figures_path):
                os.makedirs(figures_path)
            figure_path = figures_path + "/" + name + "_" + str(i + 1) + ".jpg"
            cv2.imwrite(figure_path, cropped_image_np)

            if upload_to_minio:
                image_path = figure_path
                image_name = "page_" + str(image_idx) + "_" + name + "_" + str(i + 1)
                # image_path = os.path.abspath(image_path)
                url = upload_img(image_path, big_file_name, image_name)
                url_without_query = url.split(".jpg")[0] + ".jpg"
                url_infos = url_without_query.split("/")
                new_url = "http://" + url_infos[2]  + "/" + url_infos[3] + "/" + url_infos[4] + "/" + big_file_name + "/" + url_infos[6]
                md_file.write("![" + name + "](" + str(new_url) + ")\n\n")
                md_file.flush()
            else:
                image_path = "./figures/page_" + str(image_idx) + "/" + name + "_" + str(i + 1) + ".jpg"
                md_file.write("![" + name + "](" + image_path + ")\n\n")
                md_file.flush()
        # 若为表格，则先使用tesseractOCR识别，若未识别出表格则再使用paddleOCR识别，然后导入到Markdown文件
        elif name == "table":
            ######################## Tesseract + PaddleOCR ########################
            # tables_path = result_folder_path + '/tables/page_' + str(image_idx)
            # if not os.path.exists(tables_path):
            #     os.makedirs(tables_path)
            # table_path = tables_path + '/' + name + '_' + str(i+1) + '.jpg'
            # cv2.imwrite(table_path, cropped_image)
            # img = table_img(table_path, detect_rotation=False)
            # extracted_tables = img.extract_tables(ocr=TesseractOCR(lang="chi_sim"),
            #                           implicit_rows=False,
            #                           borderless_tables=True,
            #                           min_confidence=50)
            # if len(extracted_tables) > 0:
            #     md_file.write(markdownify((extracted_tables[0]).html).lstrip('\n'))
            # else:
            #     table_engine = PPStructure(layout=False, ocr=False, show_log=False)
            #     table_result = table_engine(cropped_image)
            #     if len(table_result) > 0:
            #         md_file.write(markdownify(table_result[0]['res']['html']).lstrip('\n'))

            # md_file.write(markdownify((extracted_tables[0]).html).lstrip('\n'))
            pass
        
        # 若为文本，则调用OCR接口解析文本内容
        else:
            text = ocr_analyze(cropped_image_path)
            if text and len(text) > 0:
                text = text.replace("\n", "")
                text = text.lstrip()
                if name == "title":
                    text = "#" + " " + text
            else:
                text = ""
            md_file.write(text + "\n")
            md_file.flush()
            os.remove(cropped_image_path)


def upload_img(image_path, file_name, img_name):
    minio_endpoint = os.getenv("minio_endpoint")
    minio_access_key = os.getenv("minio_access_key")
    minio_secret_key = os.getenv("minio_secret_key")
    image_bucket_name = os.getenv("image_bucket_name")
    object_name_prefix = os.getenv("object_name_prefix")
    object_name = object_name_prefix + file_name + "/" + img_name + ".jpg"

    return upload_image_to_minio(
        image_path,
        minio_endpoint,
        minio_access_key,
        minio_secret_key,
        image_bucket_name,
        object_name,
    )


def upload_image_to_minio(
    image_path,
    minio_endpoint,
    minio_access_key,
    minio_secret_key,
    bucket_name,
    object_name,
):
    # Initialize MinIO client
    minio_client = Minio(
        minio_endpoint,
        access_key=minio_access_key,
        secret_key=minio_secret_key,
        secure=False,
    )  # Set secure=True if using HTTPS

    try:
        # Check if bucket exists, if not, create it
        found = minio_client.bucket_exists(bucket_name)
        if not found:
            minio_client.make_bucket(bucket_name)

        # Upload image to MinIO
        with open(image_path, "rb") as image_file:
            image_data = image_file.read()
            image_stream = io.BytesIO(image_data)
            image_size = len(image_data)
            # 上传
            minio_client.put_object(bucket_name, object_name, image_stream, image_size)

        # Generate public URL for the uploaded image
        image_url = minio_client.presigned_get_object(bucket_name, object_name)
        return image_url

    except Exception as err:
        print("Error:", err)
        return None


def download_image_from_minio(
    minio_endpoint,
    minio_access_key,
    minio_secret_key,
    bucket_name,
    object_name,
    local_path,
):
    # Initialize MinIO client
    minio_client = Minio(
        minio_endpoint,
        access_key=minio_access_key,
        secret_key=minio_secret_key,
        secure=False,
    )  # Set secure=True if using HTTPS

    try:
        # download image from MinIO
        # minio_client.get_object(bucket_name, object_name)
        minio_client.fget_object(bucket_name, object_name, local_path)

    except Exception as err:
        print("Error:", err)
        return None

import base64
def image_to_base64(image_path: str):
    with open(image_path, "rb") as image_file:
        # Read the image and encode it in base64
        encoded_string = base64.b64encode(image_file.read()).decode("utf-8")
    return encoded_string


def layout_analyze(image_path: str):
    """调用版面分析接口"""
    try:
        # Specify the API endpoint URL
        layout_url = os.getenv("LAYOUT_PARSER_API")
        
        # Convert image to base64
        base64_image = image_to_base64(image_path)

        # Prepare the request payload
        payload = {
            "image": base64_image,
            "return_text": False,  # or True, depending on whether you want text or not
        }

        # Send the request to the API
        response = requests.post(layout_url, json=payload)

        # Check if the response was successful
        if response.status_code == 200:
            result = response.json()
            # Handle the response (printing for demonstration purposes)
            return result["data"]["layout_result"]
        else:
            print("版面分析接口调用报错！")
            return None
    except Exception as e:
        print("版面分析处理报错：", e)
        return None


def ocr_analyze(file_path: str):
    """调ocr接口"""
    try:
        ocr_url = os.getenv("OCR_PARSER_API")
        
        image_base64 = image_to_base64(file_path)
        request_data = {
            "image": image_base64
        }
    
        response = requests.post(ocr_url, json=request_data, timeout=60)
        
        if response.status_code==200:
            result = response.json()
            return result["text"]
        else:
            print("OCR接口调用报错!")
            return None
    except Exception as e:
        print("OCR处理报错：", e)
        return None


def table_analyze(image_path: str):
    """调用表格解析接口"""
    try:
        # Specify the API endpoint URL
        table_url = os.getenv("TABLE_PARSER_API")
        
        # Convert image to base64
        base64_image = image_to_base64(image_path)
        
        # Prepare the request payload
        payload = {
            "image": base64_image,
            "return_text": False,  # or True, depending on whether you want text or not
        }

        # Send the request to the API
        response = requests.post(table_url, json=payload)

        # Check if the response was successful
        if response.status_code == 200:
            result = response.json()
            # Handle the response (printing for demonstration purposes)
            # print(result)
            return result
        else:
            print("表格解析接口调用报错！")
            return None
    except Exception as e:
        print("表格处理报错：", e)
        return None


##########################################################################

def markdown_to_chunk_index(markdown_text: str) -> List[chunk]:
    """将Markdown文本转换为结构化分块索引"""
    # 检测文档特征
    try:
        features = detect_document_features(markdown_text)
        
        # 根据特征选择分块策略
        if features['has_metadata'] or features['has_chapters'] or features['clause_levels'] > 3:
            chunks = intelligent_chunk(markdown_text)
        else:
            chunks = universal_chunk(markdown_text)
        
        # 构建分块索引
        chunk_objects = []
        # metadata = extract_metadata(markdown_text) or {}
        
        for idx, content in enumerate(chunks):
            # 提取块级元数据
            # section = detect_section(content)
            # clause_level = detect_clause_level(content)
            
            # 构建索引结构
            chunk_index_list = [{
                "index_content": content,
            }]
            # enc.encode(content)
            chunk_object: chunk = {
                "tokens_count": len(enc.encode(content)),
                "answer": content,
                "question": "",
                "chunk_type": chunk_type.BASE,
                "chunk_index":idx+1,
                "chunk_index_list": chunk_index_list
            }
            
            chunk_objects.append(chunk_object)
        
        return chunk_objects
    except Exception as e:
        traceback.print_exc()
        logger.error(f"markdown_to_chunk_index error: {e}")
        return []


def detect_document_features(text):
    features = {}
    
    # 元数据识别（使用正则表达式）
    features['has_metadata'] = bool(re.search(r'索\s*引\s*号:|主题分类:|办文部门:', text[:500]))
    
    # 章节结构识别
    features['has_chapters'] = bool(re.search(r'第[一二三四五六七八九十]+章\s+', text))
    
    # 条款层级识别
    features['clause_levels'] = len(re.findall(r'第[零一二三四五六七八九十百]+条\s+', text))
    
    # 评分标准识别
    features['has_scoring'] = bool(re.search(r'\d+分\s*（含）', text))
    
    # 条件判断识别
    features['conditional_phrases'] = len(re.findall(r'表示金融租赁公司.*?，.*?存在', text))
    
    return features


def extract_metadata(text):
    # 匹配文头元数据（处理可能的重复字段）
    metadata_pattern = r"""
        索\s*引\s*号[：:]\s*(?P<index_no>[^\n]+)
        .*?
        主\s*题\s*分\s*类[：:]\s*(?P<category>[^\n]+)
        .*?
        办\s*文\s*部\s*门[：:]\s*(?P<department>[^\n]+)
        .*?
        发\s*文\s*日\s*期[：:]\s*(?P<date>[^\n]+)
        .*?
        公\s*文\s*名\s*称[：:]\s*(?P<title>[^\n]+)
        .*?
        文\s*号[：:]\s*(?P<doc_number>[^\n]+)
    """
    
    match = re.search(metadata_pattern, text[:1000], re.DOTALL|re.VERBOSE)
    if not match:
        return None
    
    # 构造结构化元数据
    metadata = {
        'index_no': re.sub(r'\s+', '', match.group('index_no')),  # 清理空格
        'category': match.group('category').split()[0],  # 取首个分类
        'department': match.group('department').strip(),
        'date': match.group('date').replace('\t', ''),
        'title': match.group('title').replace('\n', ''),
        'doc_number': re.sub(r'(金规〔){2}', '金规〔', match.group('doc_number'))  # 修正重复
    }
    
    # 转换为文本块（保留原始格式特征）
    metadata_block = f"""
        索引号: {metadata['index_no']}
        主题分类: {metadata['category']}
        办文部门: {metadata['department']}
        发文日期: {metadata['date']}
        公文名称: {metadata['title']}
        文号: {metadata['doc_number']}
    """.strip()
    
    return metadata_block

def intelligent_chunk(text: str, max_length: int = 1000) -> List[str]:
    """智能分块方法（保持结构+语义完整）
    
    Args:
        text: 原始文本内容
        max_length: 最大分块长度（字符数）
        
    Returns:
        分块后的文本列表
    """
    chunks = []
    
    # 阶段1：提取元数据块
    metadata = extract_metadata(text)
    if metadata:
        chunks.append(metadata)
        text = text[len(metadata):]
    
    # 阶段2：结构分割（章-条-自然段）
    structural_blocks = []
    
    # 分割章节（保留标题）
    chapters = re.split(r'(第[一二三四五六七八九十]+章\s+[^\n]+)', text)
    for chap in chapters:
        if not chap.strip():
            continue
        if re.match(r'第[一二三四五六七八九十]+章', chap):
            structural_blocks.append(chap.strip())
        else:
            # 分割条款（保留条款号）
            clauses = re.split(r'(第[零一二三四五六七八九十百]+条\s+)', chap)
            current_clause = ""
            for clause_part in clauses:
                if re.match(r'第[零一二三四五六七八九十百]+条', clause_part):
                    if current_clause:
                        structural_blocks.append(current_clause.strip())
                        current_clause = ""
                    current_clause = clause_part
                else:
                    current_clause += clause_part
            if current_clause:
                structural_blocks.append(current_clause.strip())
    
    # 阶段3：动态合并分块
    final_chunks = []
    current_chunk = ""
    
    for block in structural_blocks:
        # 长度判断（当前块+新块）
        if len(current_chunk) + len(block) + 1 <= max_length:
            current_chunk += "\n" + block if current_chunk else block
        else:
            # 处理超长单个条款
            if len(block) > max_length * 0.8:
                sub_blocks = split_long_clause(block, max_length)
                final_chunks.extend(sub_blocks)
            else:
                if current_chunk:
                    final_chunks.append(current_chunk)
                current_chunk = block
                
    if current_chunk:
        final_chunks.append(current_chunk)
    
    return final_chunks

def split_long_clause(clause: str, max_len: int) -> List[str]:
    """分割超长条款（在自然段或标点处分割）"""
    sub_blocks = []
    buffer = ""
    
    # 按自然段分割
    paragraphs = re.split(r'(\n\s*\n)', clause)
    for para in paragraphs:
        if len(buffer) + len(para) > max_len:
            # 按标点二次分割
            sentences = re.split(r'([。；])', para)
            for sent in sentences:
                if len(buffer) + len(sent) > max_len:
                    if buffer:
                        sub_blocks.append(buffer)
                        buffer = ""
                buffer += sent
        else:
            buffer += para
    
    if buffer:
        sub_blocks.append(buffer)
    
    return sub_blocks

def universal_chunk(text: str, max_length: int = 512, overlap: int = 64) -> List[str]:
    """通用文档分块方法（支持中英文混合）
    
    Args:
        text: 原始文本内容
        max_length: 最大分块长度（字符数）
        overlap: 块间重叠长度（避免切分关键信息）
        
    Returns:
        分块后的文本列表
    """
    # 预处理阶段
    cleaned_text = preprocess_text(text)
    
    # 分阶段分割
    chunks = []
    current_chunk = ""
    
    # 按段落分割（保留自然段落）
    paragraphs = re.split(r'(\n\s*\n)', cleaned_text)
    for para in paragraphs:
        if not para.strip():
            continue
            
        # 处理列表项（如 1. 2. 或 • 等）
        if is_list_item(para):
            chunks, current_chunk = handle_list_item(para, chunks, current_chunk, max_length)
            continue
            
        # 按句子分割（中英文标点兼容）
        sentences = re.split(r'([。！？\.\?!]+\s*)', para)
        sentences = [s for s in sentences if s.strip()]
        
        for i in range(0, len(sentences), 2):
            if i+1 < len(sentences):
                sentence = sentences[i] + sentences[i+1]
            else:
                sentence = sentences[i]
                
            # 动态窗口处理
            if len(current_chunk) + len(sentence) <= max_length - overlap:
                current_chunk += sentence
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = current_chunk[-overlap:] + sentence  # 添加重叠
                else:
                    current_chunk = sentence
                    
    if current_chunk:
        chunks.append(current_chunk.strip())
        
    # 后处理：合并过短分块
    return merge_short_chunks(chunks, min_length=50)

def preprocess_text(text: str) -> str:
    """文本预处理"""
    # 合并连续空白字符
    text = re.sub(r'\s+', ' ', text)
    # 移除首尾空白
    return text.strip()

def is_list_item(text: str) -> bool:
    """检测列表项（支持多种格式）"""
    return bool(re.match(r'^(\d+[\.、]|[\•▪•·])', text.strip()))

def handle_list_item(item: str, chunks: List[str], current: str, max_len: int) -> tuple:
    """处理列表项分块"""
    if len(current) + len(item) > max_len:
        if current:
            chunks.append(current.strip())
            current = item
        else:
            # 超长列表项强制分割
            chunks.extend([item[i:i+max_len] for i in range(0, len(item), max_len)])
    else:
        current += '\n' + item
    return chunks, current

def merge_short_chunks(chunks: List[str], min_length: int) -> List[str]:
    """合并过短分块"""
    merged = []
    buffer = ""
    for chunk in chunks:
        if len(buffer) + len(chunk) <= min_length*2:
            buffer += " " + chunk
        else:
            if buffer:
                merged.append(buffer.strip())
            buffer = chunk
    if buffer:
        merged.append(buffer.strip())
    return merged

def detect_section(text: str) -> str:
    """检测所属章节"""
    chapter_match = re.search(r'第[一二三四五六七八九十]+章\s+(.*)', text)
    return chapter_match.group(1) if chapter_match else "正文"

def detect_clause_level(text: str) -> int:
    """检测条款层级"""
    clauses = re.findall(r'第[零一二三四五六七八九十百]+条', text)
    return len(clauses)

if __name__ == "__main__":
    # file_path = "/Users/<USER>/Downloads/TYZL-CB02002-2022A天翼融资租赁有限公司保理业务管理办法.pdf"
    # file_actual_name = "TYZL-CB02002-2022A天翼融资租赁有限公司保理业务管理办法"
    file_path = "/Users/<USER>/Downloads/关于2024年个税专项附加扣除确认的通知.pdf"
    file_actual_name = "关于2024年个税专项附加扣除确认的通知"
    result_folder_path = "./backend/app/engines/indexing/results/" + file_actual_name + "_" + "pdf"
    pdf_parser(file_path, file_actual_name, result_folder_path)
