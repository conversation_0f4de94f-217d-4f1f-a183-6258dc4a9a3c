{"cells": [{"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["# 整体流程\n", "\n", "\n", "#根据时间按照一定顺序 ，读取数据、处理数据、存储数据\n"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python\n", "# -*- coding: utf-8 -*-\n", "\n", "\"\"\"\n", "简单ES到ES数据迁移代理\n", "基于LangGraph实现，只包含三个节点：读取数据、处理数据和保存数据\n", "\"\"\"\n", "import json\n", "from typing import Dict, List, Any, Optional, TypedDict\n", "from datetime import datetime\n", "import logging\n", "import requests\n", "import traceback\n", "\n", "import re \n", "import pandas as pd\n", "from elasticsearch import Elasticsearch\n", "from langchain_openai import ChatOpenAI,OpenAIEmbeddings\n", "from langchain_core.messages import HumanMessage, SystemMessage\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.output_parsers import JsonOutputParser\n", "from langgraph.graph import StateGraph, END\n", "from langchain_core.tools import tool\n", "\n", "# 配置日志\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "# 定义要处理的索引\n", "indices = [\n", "    \"wzty_cp_merchant_problem\",           # 投诉信息库\n", "]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 这是要存入的ES"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [], "source": ["\n", "# 配置变量\n", "\n", "TARGET_ES_HOST = \"**************\"\n", "TARGET_ES_PORT = 9600\n", "TARGET_ES_USER = None\n", "TARGET_ES_PASSWORD = None\n", "TARGET_ES_INDEX = \"pro_mcp_data_complaint_v1\"\n", "\n", "BATCH_SIZE = 10  # 批处理大小\n", "\n", "\n", "from elasticsearch import Elasticsearch\n", "\n", "hosts = 'http://**********:9200'\n", "port = 9200\n", "http_auth = ('chenchao', 'GhW9U28REX$l')\n", "\n", "es7 = Elasticsearch(\n", "    hosts,\n", "    http_auth=http_auth,\n", "    port=port,\n", "    timeout=3600\n", ")\n", "\n", "\n", "# hosts = '************'\n", "# port = 9200\n", "# http_auth=('jiachengbin', 'b0>srW=!UfTC')\n", " # 可替换为其他模型\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 这是大模型参数"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["embedding_service_url = \"http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/bge_m3_finetune_20240710/v1/embeddings\"\n", "embedding_api_key =  \"ZWY0YzJkZTM4OGI0YmI3YjljOTI4NGE5ZDMxMDVhMzUzZjA3YmYxMw==\"\n", "embedding_name = \"bge-m3\"\n", "\n", "\n", "\n", "llm_service_url = \"http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1\"\n", "llm_api_key = \"ZjI1MDkzMDMyYmQ0MzA2MGYxNTQ5MGIxNWU3NDIxODk0MWMxMWFhYw==\"\n", "OPENAI_MODEL = \"Qwen2-14B-Instruct\"\n", "\n", "\n", "\n", "llm = ChatOpenAI(\n", "    temperature=0, \n", "    model=OPENAI_MODEL, \n", "    openai_api_base=llm_service_url,\n", "    api_key=llm_api_key\n", ")\n", "\n", "\n", "\n", "embedding_model = OpenAIEmbeddings(\n", "           model=embedding_name,\n", "           openai_api_key=embedding_api_key,\n", "           openai_api_base=embedding_service_url\n", ")\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 这是获取mbedding方法"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["embedding_config = {\n", "    \"api_key\":embedding_api_key,\n", "    \"service_url\":embedding_service_url,\n", "    \"embedding_name\":embedding_name\n", "}\n", "def get_embedding(text,embedding_config):\n", "    access_token = embedding_config.get(\"api_key\", \"\")\n", "    service_url = embedding_config.get(\"service_url\", \"\")\n", "    model = embedding_config.get(\"embedding_name\", \"bge-m3\")\n", "    try:\n", "        headers = {'Content-Type': 'application/json', 'Authorization': f'{access_token}'}\n", "        req = {\n", "            \"input\": [text],\n", "            \"model\": model\n", "        }\n", "        embdd_response = requests.post(url=service_url, json=req, headers=headers)\n", "        if embdd_response.status_code == 200:\n", "            # print(\"embdd_response.json():\", len(embdd_response.json()['data'][0]['embedding']))\n", "            query_embedding =  embdd_response.json()['data'][0]['embedding']\n", "        \n", "            DB_VECTOR_DIMENSION = 1536\n", "\n", "            # 如果生成的向量维度与数据库不匹配，进行扩充或截断\n", "            if len(query_embedding) != DB_VECTOR_DIMENSION:\n", "                if len(query_embedding) < DB_VECTOR_DIMENSION:\n", "                    # 扩充向量维度\n", "                    query_embedding = query_embedding + [0.0] * (DB_VECTOR_DIMENSION - len(query_embedding))\n", "                    logger.info(f\"向量维度已扩充至 {DB_VECTOR_DIMENSION}\")\n", "                else:\n", "                    # 截断向量维度\n", "                    query_embedding = query_embedding[:DB_VECTOR_DIMENSION]\n", "                    logger.info(f\"向量维度已截断至 {DB_VECTOR_DIMENSION}\")\n", "\n", "\n", "\n", "            logger.info(f\"最终查询向量维度: {len(query_embedding)}\")\n", "            return query_embedding\n", "        else:\n", "            logger.error(f\"获取embedding失败: {embdd_response.status_code}\")\n", "            return None\n", "    except Exception as e:\n", "        traceback.print_exc()\n", "        logger.error(f\"从ES获取数据时出错: {str(e)}\")\n", "        return None\n", "   \n", "# a = get_embedding(\"如果您需要在多个操作之间重用同一个连接，可以保持默认设置，并在所有操作完成后手动关闭：\",embedding_config)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 这个是取数据的ES。需要把地址、端口、账号要到。可做找张博 \n", "# 樊总政策信息所存的 ES\n", "\n", "需要改的包括\n", "1、地址 端口、账号\n", "2、build_body的查询条件 如果按照现在的 ，可以把这个改掉inserttime\n"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [], "source": ["# \n", "\n", "\n", "\n", "# 这几个\n", "def build_body(start_time,end_time):\n", "    body = {\n", "        \"query\": {\n", "                \"bool\": {\n", "                \"filter\": [\n", "                    {\n", "                    \"range\": {\n", "                         \"laws_public_time\": {\n", "                          \"gte\": start_time,\n", "                          \"lte\": end_time\n", "                        }\n", "                    }\n", "                    }\n", "                ]\n", "                }\n", "            },\n", "        \"sort\": [{\"laws_public_time\": \"desc\"}]  # 按gathertime降序排序\n", "    }\n", "    return body\n", "# 定义针对不同索引的查询构建函数\n", "def build_body_for_index(index_name, start_time, end_time):\n", "    # 所有索引都使用 update_time 作为查询和排序字段\n", "    time_field = \"update_time\"\n", "    sort_field = \"update_time\"\n", "    \n", "    # 构建查询体\n", "    body = {\n", "        \"query\": {\n", "            \"bool\": {\n", "                \"filter\": [\n", "                    {\n", "                        \"range\": {\n", "                            time_field: {\n", "                                \"gte\": start_time,\n", "                                \"lte\": end_time\n", "                            }\n", "                        }\n", "                    }\n", "                ]\n", "            }\n", "        },\n", "        \"sort\": [{sort_field: \"desc\"}]  # 按时间降序排序\n", "    }\n", "    return body\n", "def get_es_scroll_data(index,build_body): #滚动查询 获取数据es\n", "    es7 = Elasticsearch(\n", "    hosts,\n", "    http_auth=http_auth,\n", "    port=port,\n", "    timeout=3600,\n", "    scheme=\"http\",\n", "    verify_certs=False\n", ")\n", "    # elasticsearch滚动查询\n", "    result = es7.search(index=index, scroll='10m', body=build_body, size=1000, request_timeout=3600)\n", "    # 滚动id\n", "    sid = result['_scroll_id']\n", "    # 滚动总数量\n", "    scroll_size = result['hits']['total']['value']\n", "    print(f\"数据量：{scroll_size}\")\n", "    res_list = []\n", "    while scroll_size > 0:\n", "        # 滚动获取source集合\n", "        source_list = [data for data in result['hits']['hits']]\n", "        res_list.extend(source_list)\n", "        # print ('Scrolling...')\n", "        # 继续滚动\n", "        result = es7.scroll(scroll_id = sid, scroll ='10m',request_timeout=5000)\n", "        sid = result['_scroll_id']\n", "        # 滚动数量\n", "        scroll_size = len(result['hits']['hits'])\n", "    es7.clear_scroll(scroll_id = sid) #清除滚动\n", "    return res_list\n"]}, {"cell_type": "code", "execution_count": 82, "metadata": {"scrolled": true}, "outputs": [], "source": ["# 示例：从ES获取数据并转为DataFrame\n", "# 这里根据实际情况调整查询条件\n", "from datetime import datetime\n", "\n", "def format_es_date(date_str):\n", "    # 支持 2025-5-9 00:00:00 这种格式，自动补零\n", "    dt = datetime.strptime(date_str, \"%Y-%m-%d %H:%M:%S\")\n", "    return dt.strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [], "source": ["# 示例：从ES获取数据并转为DataFrame\n", "# 这里根据实际情况调整查询条件\n", "from datetime import datetime\n", "\n", "def format_es_date(date_str):\n", "    # 支持 2025-5-9 00:00:00 这种格式，自动补零\n", "    dt = datetime.strptime(date_str, \"%Y-%m-%d %H:%M:%S\")\n", "    return dt.strftime(\"%Y-%m-%d %H:%M:%S\")"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "def get_day_range(end_time_str: str = None):\n", "    \"\"\"\n", "    输入：结束时间字符串（如 '2024-05-19 15:23:00'），为空则用当前时间\n", "    输出：前一天的开始时间和结束时间（'2024-05-18 00:00:00', '2024-05-18 23:59:59'）\n", "    \"\"\"\n", "    if not end_time_str or end_time_str.strip() == \"\":\n", "        dt = datetime.now()\n", "    else:\n", "        dt = datetime.strptime(end_time_str, \"%Y-%m-%d %H:%M:%S\")\n", "    # 减去一天\n", "    dt = dt - <PERSON><PERSON><PERSON>(days=1)\n", "    day_str = dt.strftime(\"%Y-%m-%d\")\n", "    start_time = f\"{day_str} 00:00:00\"\n", "    end_time = f\"{day_str} 23:59:59\"\n", "    return start_time, end_time\n", "\n"]}, {"cell_type": "markdown", "metadata": {"scrolled": false}, "source": ["\n", "# 这个是我写的去标签的方法，可以替换成你的"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [], "source": ["import re\n", "from html import unescape\n", "from bs4 import BeautifulSoup\n", "\n", "def html_to_markdown(html):\n", "    \"\"\"\n", "    将HTML转换为Markdown格式\n", "    \n", "    Args:\n", "        html (str): HTML字符串\n", "        \n", "    Returns:\n", "        str: 转换后的Markdown文本\n", "    \"\"\"\n", "    if not html:\n", "        return ''\n", "    \n", "    # 使用BeautifulSoup解析HTML\n", "    soup = BeautifulSoup(html, 'html.parser')\n", "    \n", "    # 移除script和style标签\n", "    for script in soup([\"script\", \"style\"]):\n", "        script.extract()\n", "    \n", "    # 处理标题\n", "    for i in range(1, 7):\n", "        for tag in soup.find_all(f'h{i}'):\n", "            tag.replace_with(f\"{'#' * i} {tag.get_text().strip()}\\n\\n\")\n", "    \n", "    # 处理段落\n", "    for tag in soup.find_all('p'):\n", "        tag.replace_with(f\"{tag.get_text().strip()}\\n\\n\")\n", "    \n", "    # 处理列表\n", "    for ul in soup.find_all('ul'):\n", "        for li in ul.find_all('li'):\n", "            li.replace_with(f\"* {li.get_text().strip()}\\n\")\n", "    \n", "    for ol in soup.find_all('ol'):\n", "        for i, li in enumerate(ol.find_all('li')):\n", "            li.replace_with(f\"{i+1}. {li.get_text().strip()}\\n\")\n", "    \n", "    # 处理链接\n", "    for a in soup.find_all('a', href=True):\n", "        text = a.get_text().strip()\n", "        href = a['href']\n", "        a.replace_with(f\"[{text}]({href})\")\n", "    \n", "    # 处理图片\n", "    for img in soup.find_all('img', src=True):\n", "        alt = img.get('alt', '')\n", "        src = img['src']\n", "        img.replace_with(f\"![{alt}]({src})\")\n", "    \n", "    # 处理粗体和斜体\n", "    for strong in soup.find_all(['strong', 'b']):\n", "        strong.replace_with(f\"**{strong.get_text().strip()}**\")\n", "    \n", "    for em in soup.find_all(['em', 'i']):\n", "        em.replace_with(f\"*{em.get_text().strip()}*\")\n", "    \n", "    # 处理引用块\n", "    for blockquote in soup.find_all('blockquote'):\n", "        lines = blockquote.get_text().strip().split('\\n')\n", "        quoted_text = '\\n'.join([f\"> {line}\" for line in lines])\n", "        blockquote.replace_with(f\"{quoted_text}\\n\\n\")\n", "    \n", "    # 处理代码块\n", "    for pre in soup.find_all('pre'):\n", "        code = pre.get_text().strip()\n", "        pre.replace_with(f\"```\\n{code}\\n```\\n\\n\")\n", "    \n", "    for code in soup.find_all('code'):\n", "        code.replace_with(f\"`{code.get_text().strip()}`\")\n", "    \n", "    # 处理表格 (简单实现)\n", "    for table in soup.find_all('table'):\n", "        md_table = []\n", "        \n", "        # 表头\n", "        headers = []\n", "        for th in table.find_all('th'):\n", "            headers.append(th.get_text().strip())\n", "        \n", "        if headers:\n", "            md_table.append('| ' + ' | '.join(headers) + ' |')\n", "            md_table.append('| ' + ' | '.join(['---'] * len(headers)) + ' |')\n", "        \n", "        # 表格内容\n", "        for tr in table.find_all('tr'):\n", "            row = []\n", "            for td in tr.find_all('td'):\n", "                row.append(td.get_text().strip())\n", "            if row:\n", "                md_table.append('| ' + ' | '.join(row) + ' |')\n", "        \n", "        table.replace_with('\\n'.join(md_table) + '\\n\\n')\n", "    \n", "    # 处理水平线\n", "    for hr in soup.find_all('hr'):\n", "        hr.replace_with('---\\n\\n')\n", "    \n", "    # 提取并清理结果文本\n", "    markdown = soup.get_text()\n", "    \n", "    # 修复可能的格式问题\n", "    markdown = re.sub(r'\\n{3,}', '\\n\\n', markdown)  # 移除多余空行\n", "    markdown = markdown.strip()\n", "    \n", "    return markdown"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [], "source": ["def analyze_text(title: str, content: str) -> Dict[str, Any]:\n", "    \"\"\"分析投诉文本内容，提取关键信息，返回结构化字段\"\"\"\n", "    # 添加内容验证\n", "    if not content or len(content.strip()) < 10:\n", "        logger.warning(f\"投诉内容为空或过短 (长度: {len(content) if content else 0})\")\n", "        return get_default_result()\n", "    \n", "    if not title:\n", "        logger.warning(\"投诉标题为空，使用默认标题\")\n", "        title = \"无标题投诉\"\n", "    \n", "    # 检查内容是否过长，大模型有输入限制\n", "    if len(content) > 15000:\n", "        logger.warning(f\"投诉内容过长 ({len(content)}字符)，将截断\")\n", "        content = content[:15000] + \"...(内容已截断)\"\n", "    \n", "    logger.info(f\"处理投诉 - 标题: {title[:30]}... | 内容长度: {len(content)}\")\n", "\n", "    # 构建提示词模板\n", "    prompt = ChatPromptTemplate.from_messages([\n", "    SystemMessage(content=\"\"\"你是一位经验丰富的互联网用户投诉分析专家，擅长从用户反馈中提取关键信息并进行深度洞察。\n", "请仔细阅读以下用户投诉原文，并根据要求提取或生成以下信息。\n", "输出格式要求为JSON，并且JSON的键（key）必须是以下指定的英文名称：\n", "\n", "请分析并输出以下字段（注意，JSON的键名请使用对应的英文名称）：\n", "\n", "1.  `core_issue`: (用一句话或简短几句话概括用户投诉的核心内容。)\n", "2.  `product_service_involved`: (明确指出投诉涉及的具体产品名称、服务环节或功能模块。如果原文未明确提及，请根据上下文推断，如果无法推断，则填写\"未知\"。)\n", "3.  `user_demand`: (用户希望得到什么样的解决方案或结果？例如：退款、道歉、修复、解释、赔偿等。如果用户未明确提出，请根据问题性质推断最可能的诉求。)\n", "4.  `sentiment`: (判断用户的主要情绪，例如：愤怒、失望、焦虑、急切、平静等。选择最主要的一个或两个，以字符串或字符串数组给出。)\n", "5.  `severity`: (评估问题对用户造成的影响程度或问题的紧急性，从\"低\"、\"中\"、\"高\"、\"紧急\"中选择一个。)\n", "6.  `potential_risks`: (从投诉内容中识别可能存在的风险，例如：法律风险、声誉风险、监管风险、用户流失风险、群体性事件风险等。如果无明显风险，则填写\"暂无明显风险\"或输出空数组。如果存在多个风险，请以字符串数组形式输出。)\n", "7.  `potential_causes`: (基于投诉描述，初步推断导致问题的最可能的一个或多个原因，例如：产品缺陷、流程不合理、信息不清、客服失误、系统故障等。请以字符串数组形式输出。)\n", "8.  `improvement_suggestions`: (基于投诉和可能原因，提出1-2条最直接相关的产品、服务或流程的改进建议。如果用户已提出明确建议，可直接采纳或优化表述。请以字符串数组形式输出。)\n", "9.  `complaint_category`: (请从以下预设分类中选择最合适的1-2个分类，并以字符串数组形式输出。预设分类：[\"功能BUG\", \"界面体验\", \"计费问题\", \"客服态度\", \"账号安全\", \"隐私泄露\", \"虚假宣传\", \"活动规则\", \"物流配送\", \"其他\"]。如果都不太合适，可以选择[\"其他\"]。)\n", "10. `keywords`: (提取3-5个最能代表投诉内容的关键词或短语，以字符串数组形式输出。)\n", "11. `summary`: (用300字以内概括投诉内容、分析和建议，这将用于生成向量嵌入)\n", "\n", "输出格式要求为JSON，且JSON的键（key）必须是上述指定的英文名称。\"\"\"),\n", "    HumanMessage(content=f\"\"\"请分析以下用户投诉：\n", "标题: {title}\n", "投诉原文: {content}\n", "/no_think\"\"\")\n", "])\n", "    \n", "    chain = prompt | llm  # 确保 llm 已初始化为 ChatModel 类型\n", "\n", "    try:\n", "        result = chain.invoke({})\n", "        # 获取内容：先尝试 .content 属性，如果是对象\n", "        raw_content = getattr(result, 'content', result)\n", "        \n", "        # 清理内容\n", "        # 1. 移除 <think> 标签及其内容\n", "        text = re.sub(r'<think>.*?</think>', '', raw_content, flags=re.DOTALL)\n", "        \n", "        # 2. 查找 JSON 内容\n", "        # 首先尝试查找 ```json 和 ``` 之间的内容\n", "        # 原始代码可能类似这样:\n", "        # json_match = re.search(r'```json\\s*({.*?})\\\\s*```', text, flags=re.DOTALL)\n", "        # if not json_match:\n", "        #    json_match = re.search(r'({.*})', text, flags=re.DOTALL)\n", "\n", "        # 修改为以下代码:\n", "        # 2. 查找 JSON 内容，尝试多种匹配模式\n", "        json_match = None\n", "\n", "        # 模式1: 查找 ```json 和 ``` 之间的内容\n", "        json_match = re.search(r'```json\\s*({.*?})\\s*```', text, flags=re.DOTALL)\n", "\n", "        # 模式2: 查找任意代码块中的JSON\n", "        if not json_match:\n", "            json_match = re.search(r'```\\s*({.*?})\\s*```', text, flags=re.DOTALL)\n", "\n", "        # 模式3: 尝试查找包含关键字段的JSON对象\n", "        if not json_match:\n", "            json_match = re.search(r'({[\\s\\S]*?\"core_issue\"[\\s\\S]*?})', text, flags=re.DOTALL)\n", "    \n", "        # 模式4: 查找包含其他关键字段的JSON对象\n", "        if not json_match:\n", "            json_match = re.search(r'({[\\s\\S]*?\"keywords\"[\\s\\S]*?})', text, flags=re.DOTALL)\n", "    \n", "        # 模式5: 最后尝试，查找第一个 { 和最后一个 } 之间的内容\n", "        if not json_match:\n", "            json_match = re.search(r'({.*})', text, flags=re.DOTALL)\n", "            \n", "        if not json_match:\n", "        # 添加更多详细日志，打印部分原始响应内容以便调试\n", "            logger.error(f\"未找到有效的JSON内容，原始内容前200字符: {raw_content[:200]}...\")\n", "            return get_default_result()\n", "            \n", "        if not json_match:\n", "            logger.error(\"未找到有效的JSON内容\")\n", "            return get_default_result()\n", "            \n", "        json_str = json_match.group(1)\n", "        \n", "        # 3. 清理 JSON 字符串\n", "        # 移除可能的转义字符\n", "        json_str = json_str.replace('\\\\n', ' ').replace('\\\\r', '')\n", "        # 移除尾随逗号\n", "        json_str = re.sub(r',\\s*}', '}', json_str)\n", "        json_str = re.sub(r',\\s*]', ']', json_str)\n", "        \n", "        # 4. 解析 JSON\n", "        try:\n", "            result = json.loads(json_str)\n", "            \n", "            # 5. 验证结果结构\n", "            if not validate_result_structure(result):\n", "                logger.error(\"JSON结构不符合预期\")\n", "                return get_default_result()\n", "                \n", "            return result\n", "            \n", "        except json.JSONDecodeError as je:\n", "            logger.error(f\"JSON解析错误: {str(je)}\")\n", "            return get_default_result()\n", "\n", "    except Exception as e:\n", "        logger.error(f\"分析文本时出错: {str(e)}\")\n", "        return get_default_result()\n", "\n", "\n", "def validate_result_structure(data: Dict[str, Any]) -> bool:\n", "    \"\"\"验证返回的JSON结构是否符合预期（全部一级字段）\"\"\"\n", "    try:\n", "        required_fields = {\n", "            \"core_issue\": str,\n", "            \"product_service_involved\": str,\n", "            \"user_demand\": str,\n", "            \"sentiment\": [str, list],  # 可以是字符串或字符串数组\n", "            \"severity\": str,\n", "            \"potential_risks\": list,\n", "            \"potential_causes\": list,\n", "            \"improvement_suggestions\": list,\n", "            \"complaint_category\": list,\n", "            \"keywords\": list,\n", "            \"summary\": str  # 保留summary字段用于生成embedding\n", "        }\n", "        \n", "        # 检查必需字段\n", "        for field, field_type in required_fields.items():\n", "            if field not in data:\n", "                logger.error(f\"缺少必需字段: {field}\")\n", "                return False\n", "                \n", "            # 处理特殊情况：sentiment可能是字符串或列表\n", "            if field == \"sentiment\" and isinstance(field_type, list):\n", "                if not any(isinstance(data[field], t) for t in field_type):\n", "                    logger.error(f\"字段类型错误: {field}, 期望 {field_type}, 实际 {type(data[field])}\")\n", "                    return False\n", "            elif not isinstance(data[field], field_type):\n", "                logger.error(f\"字段类型错误: {field}, 期望 {field_type}, 实际 {type(data[field])}\")\n", "                return False\n", "\n", "        # 严重程度校验\n", "        if data[\"severity\"] not in [\"低\", \"中\", \"高\", \"紧急\"]:\n", "            logger.error(\"severity 字段值必须为 '低'、'中'、'高'或'紧急'\")\n", "            return False\n", "\n", "        return True\n", "\n", "    except Exception as e:\n", "        logger.error(f\"验证JSON结构时发生错误: {str(e)}\")\n", "        return False\n", "    \n", "\n", "def get_default_result() -> Dict[str, Any]:\n", "    \"\"\"返回默认的空结果结构（全部一级字段）\"\"\"\n", "    return {\n", "        \"core_issue\": \"\",\n", "        \"product_service_involved\": \"未知\",\n", "        \"user_demand\": \"\",\n", "        \"sentiment\": \"未知\",\n", "        \"severity\": \"中\",\n", "        \"potential_risks\": [],\n", "        \"potential_causes\": [],\n", "        \"improvement_suggestions\": [],\n", "        \"complaint_category\": [\"其他\"],\n", "        \"keywords\": [],\n", "        \"summary\": \"\"\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Agent 流程\n", "\n"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["def format_date(date_value) -> str:\n", "    \"\"\"\n", "    将日期格式统一转换为 yyyy-MM-dd HH:mm:ss 格式\n", "    支持处理 Pandas Timestamp、datetime 对象和字符串\n", "    \"\"\"\n", "    try:\n", "        # 处理空值\n", "        if pd.isna(date_value) or date_value is None:\n", "            return \"1970-01-01 00:00:00\"\n", "            \n", "        # 如果是 Pandas Timestamp\n", "        if isinstance(date_value, pd.Timestamp):\n", "            return date_value.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "            \n", "        # 如果是 datetime 对象\n", "        if isinstance(date_value, datetime):\n", "            return date_value.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "            \n", "        # 如果是字符串\n", "        if isinstance(date_value, str):\n", "            # 如果是默认值，直接返回\n", "            if date_value == \"1970-01-01 00:00:00\":\n", "                return date_value\n", "            # 处理 ISO 格式\n", "            if 'T' in date_value:\n", "                date_value = date_value.replace('T', ' ')\n", "            return date_value\n", "            \n", "        # 如果都不是，尝试转换为字符串\n", "        return str(date_value)\n", "        \n", "    except Exception as e:\n", "        logger.warning(f\"日期格式转换失败: {date_value}, 类型: {type(date_value)}, 错误: {str(e)}\")\n", "        return \"1970-01-01 00:00:00\""]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [], "source": ["def process_data(current_docs, index_name=None):\n", "    \"\"\"处理数据，使用LLM提取信息\"\"\"\n", "    import json\n", "    import datetime\n", "    \n", "    # 如果没有提供索引名，使用默认索引\n", "    if index_name is None:\n", "        index_name = indices[0]  # 使用第一个索引作为默认值\n", "    \n", "    print(f\"正在处理索引: {index_name}\")\n", "\n", "    es = Elasticsearch(\n", "        [f\"{TARGET_ES_HOST}:{TARGET_ES_PORT}\"]\n", "    )\n", "\n", "    # 检查连接\n", "    if es.ping():\n", "        print(f\"ES连接成功: {TARGET_ES_HOST}:{TARGET_ES_PORT}\")\n", "        es_info = es.info()\n", "        logger.info(f\"ES版本: {es_info['version']['number']}\")\n", "    else:\n", "        print(f\"ES连接失败: {TARGET_ES_HOST}:{TARGET_ES_PORT}\")\n", "    \n", "    # 定义允许的字段列表（从映射中提取）\n", "    allowed_fields = {\n", "        \"appeal\", \"applbasques\", \"area\", \"author\", \"author_avatar\", \n", "        \"collective_amount\", \"collective_per\", \"collective_sn\", \"comment_amount\", \n", "        \"comment_id\", \"cost\", \"cotitle\", \"couid\", \"cp_type\", \"crawler_interval\",\n", "        \"crawler_time\", \"created_at\", \"handle_depart\", \"handle_result\", \"id\", \n", "        \"indtypes\", \"industry\", \"issue\", \"last_gather_comment_time\", \n", "        \"last_gather_default_time\", \"last_gather_jiti_time\", \"last_gather_time\",\n", "        \"last_gather_time _2\", \"meger_data_time\", \"obj_id\", \"product_model\",\n", "        \"product_type\", \"satisfaction_level\", \"service_used\", \"share_amount\",\n", "        \"site_name\", \"sn\", \"status\", \"summary\", \"title\", \"update_time\",\n", "        \"upvote_amount\", \"url\", \n", "        # LLM分析字段\n", "        \"core_issue\", \"product_service_involved\", \"user_demand\", \"sentiment\",\n", "        \"severity\", \"potential_risks\", \"potential_causes\", \"improvement_suggestions\",\n", "        \"complaint_category\", \"keywords\", \"embedding\"\n", "    }\n", "    \n", "    # 数据清理函数\n", "    def clean_document(doc):\n", "        \"\"\"清理文档，处理常见的Elasticsearch写入问题\"\"\"\n", "        cleaned = {}\n", "        for key, value in doc.items():\n", "            # 只处理允许的字段\n", "            if key not in allowed_fields:\n", "                continue\n", "                \n", "            # 处理None值\n", "            if value is None:\n", "                cleaned[key] = \"\"\n", "            # 处理日期格式\n", "            elif isinstance(value, (datetime.date, datetime.datetime)):\n", "                cleaned[key] = value.isoformat()\n", "            # 处理可能的NaN值\n", "            <PERSON><PERSON> hasattr(value, \"is_nan\") and value.is_nan():\n", "                cleaned[key] = None\n", "            # 处理其他类型\n", "            else:\n", "                cleaned[key] = value\n", "        return cleaned\n", "    \n", "    try:\n", "        processed_count = 0\n", "        \n", "        for source in current_docs:\n", "            # 获取并清理文本内容\n", "            _id = source['_id']\n", "            row = source['_source']\n", "            # 根据索引类型选择适当的字段\n", "            if \"merchant_problem\" in index_name:\n", "                # 投诉信息库 - 增加可能的字段名并添加详细日志\n", "                content = (row.get(\"complaint_content\", \"\") or \n", "                          row.get(\"content\", \"\") or \n", "                          row.get(\"complaintContent\", \"\") or \n", "                          row.get(\"complaint_desc\", \"\") or\n", "                          row.get(\"description\", \"\"))\n", "    \n", "                title = (row.get(\"complaint_title\", \"\") or \n", "                        row.get(\"title\", \"\") or \n", "                        row.get(\"complaintTitle\", \"\") or\n", "                        row.get(\"subject\", \"\"))\n", "    \n", "                # 调试输出\n", "                if not content or len(content.strip()) < 10:\n", "                    logger.warning(f\"ID:{_id} - 提取到的投诉内容为空或过短\")\n", "                    # 查看行中所有可能包含内容的字段\n", "                    potential_content_fields = {k: v for k, v in row.items() if isinstance(v, str) and len(v) > 50}\n", "                    if potential_content_fields:\n", "                        logger.info(f\"ID:{_id} - 潜在内容字段: {list(potential_content_fields.keys())}\")\n", "                        # 尝试使用最长的字符串字段作为内容\n", "                        longest_field = max(potential_content_fields.items(), key=lambda x: len(x[1]))\n", "                        logger.info(f\"ID:{_id} - 使用字段 {longest_field[0]} 作为内容 (长度: {len(longest_field[1])})\")\n", "                        content = longest_field[1]\n", "    \n", "                logger.info(f\"ID:{_id} - 标题: {title[:30]}... | 内容长度: {len(content)}\")\n", "            elif \"merchant_problem_comment\" in index_name:\n", "                # 保留其他索引类型的逻辑，也可以按类似方式增强\n", "                content = row.get(\"comment_content\", \"\") or row.get(\"content\", \"\")\n", "                title = row.get(\"comment_title\", \"\") or row.get(\"title\", \"\")\n", "            # ... 保留其他索引类型的处理逻辑 ...\n", "            \n", "            clean_text = html_to_markdown(content)\n", "            \n", "            # 使用LLM分析文本\n", "            analysis_result = analyze_text(title, clean_text)\n", "            \n", "            # 创建目标文档，只包含允许的字段\n", "            target_doc = {}\n", "            \n", "            # LLM分析的字段 - 只添加映射中定义的字段\n", "            target_doc[\"core_issue\"] = analysis_result.get(\"core_issue\", \"\")\n", "            target_doc[\"product_service_involved\"] = analysis_result.get(\"product_service_involved\", \"\")\n", "            target_doc[\"user_demand\"] = analysis_result.get(\"user_demand\", \"\")\n", "            \n", "            # 处理sentiment字段 - 确保只输出一个值\n", "            sentiment = analysis_result.get(\"sentiment\", \"\")\n", "            if isinstance(sentiment, list) and sentiment:\n", "                # 如果是列表，只取第一个元素\n", "                target_doc[\"sentiment\"] = sentiment[0]\n", "            else:\n", "                # 否则直接使用值\n", "                target_doc[\"sentiment\"] = sentiment if sentiment else \"\"\n", "                \n", "            target_doc[\"severity\"] = analysis_result.get(\"severity\", \"\")\n", "            target_doc[\"potential_risks\"] = analysis_result.get(\"potential_risks\", [])\n", "            target_doc[\"potential_causes\"] = analysis_result.get(\"potential_causes\", [])\n", "            target_doc[\"improvement_suggestions\"] = analysis_result.get(\"improvement_suggestions\", [])\n", "            target_doc[\"complaint_category\"] = analysis_result.get(\"complaint_category\", [])\n", "            target_doc[\"keywords\"] = analysis_result.get(\"keywords\", [])\n", "            target_doc[\"embedding\"] = get_embedding(analysis_result.get(\"core_issue\", \"\"), embedding_config)\n", "\n", "            # 从原始文档复制字段到目标文档，但只复制允许的字段\n", "            for field in allowed_fields:\n", "                if field in row and field not in target_doc:  # 避免覆盖已设置的LLM字段\n", "                    target_doc[field] = row[field]\n", "            \n", "            # 清理文档数据\n", "            target_doc = clean_document(target_doc)\n", "            \n", "            try:\n", "                # 尝试写入ES\n", "                es.index(\n", "                    index=TARGET_ES_INDEX,\n", "                    id=_id,\n", "                    document=target_doc\n", "                )\n", "                print(f'插入：{_id}')\n", "                processed_count += 1\n", "            except Exception as e:\n", "                print(f\"保存文档 {_id} 时出错: {type(e).__name__}: {str(e)}\")\n", "                \n", "                # 尝试输出部分文档内容以便调试\n", "                try:\n", "                    print(f\"文档部分内容: {json.dumps(dict(list(target_doc.items())[:5]), default=str)}\")\n", "                except:\n", "                    print(\"无法打印文档内容\")\n", "                \n", "                logger.error(f\"保存文档 {_id} 时出错: {str(e)}\")\n", "\n", "        logger.info(f\"处理文档: {processed_count}\")\n", "        return processed_count\n", "\n", "    except Exception as e:\n", "        traceback.print_exc()\n", "        logger.error(f\"处理数据时出错: {str(e)}\")\n", "    finally:\n", "        if es:\n", "            try:\n", "                es.close()\n", "                logger.debug(\"ES客户端已关闭\")\n", "            except Exception as close_error:\n", "                traceback.print_exc()\n", "                logger.warning(f\"关闭ES客户端时出错: {str(close_error)}\")"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [], "source": ["def get_es_scroll_data_batched(index, query_body, batch_size=1000):\n", "    \"\"\"滚动查询获取数据，使用生成器返回批次数据\"\"\"\n", "    es7 = Elasticsearch(\n", "        hosts,\n", "        http_auth=http_auth,\n", "        port=port,\n", "        timeout=3600,\n", "        scheme=\"http\",\n", "        verify_certs=False\n", "    )\n", "    \n", "    try:\n", "        # 执行初始查询\n", "        result = es7.search(index=index, scroll='10m', body=query_body, size=batch_size, request_timeout=3600)\n", "        sid = result['_scroll_id']\n", "        scroll_size = result['hits']['total']['value']\n", "        \n", "        print(f\"索引 {index} 总数据量: {scroll_size}\")\n", "        \n", "        # 返回第一批数据\n", "        if len(result['hits']['hits']) > 0:\n", "            yield result['hits']['hits']\n", "        \n", "        # 批处理循环\n", "        scroll_count = len(result['hits']['hits'])\n", "        while scroll_count > 0:\n", "            # 获取下一批数据\n", "            result = es7.scroll(scroll_id=sid, scroll='10m')\n", "            batch_data = result['hits']['hits']\n", "            scroll_count = len(batch_data)\n", "            \n", "            if scroll_count == 0:\n", "                break\n", "                \n", "            # 返回当前批次数据\n", "            yield batch_data\n", "            \n", "    except Exception as e:\n", "        logger.error(f\"获取索引 {index} 数据时出错: {str(e)}\")\n", "        traceback.print_exc()\n", "    finally:\n", "        # 清理scroll\n", "        try:\n", "            es7.clear_scroll(scroll_id=sid)\n", "        except:\n", "            pass\n", "        print(f\"索引 {index} 查询完成\")\n", "def process_index_in_batches(index_name, start_time, end_time, batch_size=1000):\n", "    \"\"\"批量处理单个索引的数据\"\"\"\n", "    print(f\"开始处理索引: {index_name}\")\n", "    \n", "    # 构建查询\n", "    body = build_body_for_index(index_name, start_time, end_time)\n", "    \n", "    # 计数器\n", "    batch_count = 0\n", "    total_processed = 0\n", "    \n", "    # 使用生成器按批次处理数据\n", "    for batch_data in get_es_scroll_data_batched(index_name, body, batch_size):\n", "        batch_count += 1\n", "        batch_size = len(batch_data)\n", "        \n", "        print(f\"处理批次 {batch_count}，数量: {batch_size}\")\n", "        \n", "        # 处理当前批次数据\n", "        try:\n", "            processed_count = process_data(batch_data, index_name)\n", "            total_processed += processed_count\n", "            print(f\"批次 {batch_count} 成功处理: {processed_count} 条记录\")\n", "            print(f\"累计已处理: {total_processed} 条记录\")\n", "        except Exception as e:\n", "            print(f\"处理批次 {batch_count} 时出错: {str(e)}\")\n", "            traceback.print_exc()\n", "            \n", "    print(f\"索引 {index_name} 处理完成，总计处理: {total_processed} 条记录\")\n", "    return total_processed\n", "# 示例：批量处理所有索引\n", "def process_single_index(start_date=\"2025-5-1 00:00:00\", end_date=\"2025-5-31 00:00:00\", batch_size=1000):\n", "    \"\"\"处理投诉信息库索引的数据\"\"\"\n", "    start_time = format_es_date(start_date)\n", "    end_time = format_es_date(end_date)\n", "    \n", "    print(f\"处理时间范围: {start_time} 到 {end_time}\")\n", "    \n", "    # 固定使用投诉信息库索引\n", "    index_name = \"wzty_cp_merchant_problem\"\n", "    \n", "    try:\n", "        # 直接调用批处理函数处理单个索引\n", "        total_records = process_index_in_batches(index_name, start_time, end_time, batch_size)\n", "        print(f\"索引 {index_name} 处理完成，总计处理记录: {total_records}\")\n", "        return total_records\n", "    except Exception as e:\n", "        print(f\"处理索引 {index_name} 时出错: {str(e)}\")\n", "        traceback.print_exc()\n", "        return 0"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25240\\223458136.py:14: DeprecationWarning: The 'body' parameter is deprecated for the 'search' API and will be removed in a future version. Instead use API parameters directly. See https://github.com/elastic/elasticsearch-py/issues/1698 for more information\n", "  result = es7.search(index=index, scroll='10m', body=query_body, size=batch_size, request_timeout=3600)\n", "2025-05-29 11:55:02,010 - elasticsearch - WARNING - GET http://**********:9200/ [status:403 request:0.087s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["处理时间范围: 2020-05-01 00:00:00 到 2025-05-31 00:00:00\n", "开始处理索引: wzty_cp_merchant_problem\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25240\\223458136.py:14: ElasticsearchWarning: The client is unable to verify that the server is Elasticsearch due security privileges on the server side\n", "  result = es7.search(index=index, scroll='10m', body=query_body, size=batch_size, request_timeout=3600)\n", "2025-05-29 11:55:37,595 - elasticsearch - INFO - POST http://**********:9200/wzty_cp_merchant_problem/_search?scroll=10m&size=1000 [status:200 request:35.581s]\n", "2025-05-29 11:55:37,641 - elasticsearch - INFO - GET http://**************:9600/ [status:200 request:0.031s]\n", "2025-05-29 11:55:37,656 - elasticsearch - INFO - HEAD http://**************:9600/ [status:200 request:0.014s]\n", "2025-05-29 11:55:37,670 - elasticsearch - INFO - GET http://**************:9600/ [status:200 request:0.013s]\n", "2025-05-29 11:55:37,671 - __main__ - INFO - ES版本: 7.9.1\n", "2025-05-29 11:55:37,672 - __main__ - WARNING - ID:95320800_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:55:37,672 - __main__ - INFO - ID:95320800_xfb315 - 潜在内容字段: ['author_avatar']\n", "2025-05-29 11:55:37,673 - __main__ - INFO - ID:95320800_xfb315 - 使用字段 author_avatar 作为内容 (长度: 82)\n", "2025-05-29 11:55:37,673 - __main__ - INFO - ID:95320800_xfb315 - 标题: 咻电归还后乱扣钱... | 内容长度: 82\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25240\\10355110.py:19: MarkupResemblesLocatorWarning: The input passed in on this line looks more like a URL than HTML or XML.\n", "\n", "If you meant to use Beautiful Soup to parse the web page found at a certain URL, then something has gone wrong. You should use an Python package like 'requests' to fetch the content behind the URL. Once you have the content as a string, you can feed that string into Beautiful Soup.\n", "\n", "However, if you want to parse some data that happens to look like a URL, then nothing has gone wrong: you are using Beautiful Soup correctly, and this warning is spurious and can be filtered. To make this warning go away, run this code before calling the BeautifulSoup constructor:\n", "\n", "    from bs4 import MarkupResemblesLocatorWarning\n", "    import warnings\n", "\n", "    warnings.filterwarnings(\"ignore\", category=MarkupResemblesLocatorWarning)\n", "    \n", "  soup = BeautifulSoup(html, 'html.parser')\n", "2025-05-29 11:55:37,674 - __main__ - INFO - 处理投诉 - 标题: 咻电归还后乱扣钱... | 内容长度: 82\n"]}, {"name": "stdout", "output_type": "stream", "text": ["索引 wzty_cp_merchant_problem 总数据量: 36598897\n", "处理批次 1，数量: 1000\n", "正在处理索引: wzty_cp_merchant_problem\n", "ES连接成功: **************:9600\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:55:51,669 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:55:53,595 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:55:53,746 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/95320800_xfb315 [status:201 request:0.148s]\n", "2025-05-29 11:55:53,748 - __main__ - WARNING - ID:17365031831 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:55:53,748 - __main__ - INFO - ID:17365031831 - 潜在内容字段: ['summary', 'url']\n", "2025-05-29 11:55:53,749 - __main__ - INFO - ID:17365031831 - 使用字段 summary 作为内容 (长度: 938)\n", "2025-05-29 11:55:53,749 - __main__ - INFO - ID:17365031831 - 标题: 拼多多店铺“都成数码电器专营店”假货、虚假宣传以及引导消费者... | 内容长度: 938\n", "2025-05-29 11:55:53,750 - __main__ - INFO - 处理投诉 - 标题: 拼多多店铺“都成数码电器专营店”假货、虚假宣传以及引导消费者... | 内容长度: 938\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：95320800_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:56:06,778 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:56:06,956 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:56:07,002 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/17365031831 [status:201 request:0.044s]\n", "2025-05-29 11:56:07,004 - __main__ - WARNING - ID:98792808_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:56:07,005 - __main__ - INFO - ID:98792808_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:56:07,005 - __main__ - INFO - ID:98792808_xfb315 - 使用字段 summary 作为内容 (长度: 195)\n", "2025-05-29 11:56:07,006 - __main__ - INFO - ID:98792808_xfb315 - 标题: 微博借钱出尔反尔，协商还款不被认可，遭遇暴力催收... | 内容长度: 195\n", "2025-05-29 11:56:07,007 - __main__ - INFO - 处理投诉 - 标题: 微博借钱出尔反尔，协商还款不被认可，遭遇暴力催收... | 内容长度: 195\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：17365031831\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:56:20,136 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:56:20,268 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:56:20,314 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98792808_xfb315 [status:201 request:0.043s]\n", "2025-05-29 11:56:20,315 - __main__ - WARNING - ID:97871276_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:56:20,315 - __main__ - INFO - ID:97871276_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:56:20,316 - __main__ - INFO - ID:97871276_xfb315 - 使用字段 summary 作为内容 (长度: 90)\n", "2025-05-29 11:56:20,317 - __main__ - INFO - ID:97871276_xfb315 - 标题: 尚德机构退款纠纷... | 内容长度: 90\n", "2025-05-29 11:56:20,317 - __main__ - INFO - 处理投诉 - 标题: 尚德机构退款纠纷... | 内容长度: 90\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98792808_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:56:30,741 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:56:31,864 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:56:31,930 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/97871276_xfb315 [status:201 request:0.063s]\n", "2025-05-29 11:56:31,931 - __main__ - WARNING - ID:98550741_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:56:31,932 - __main__ - INFO - ID:98550741_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:56:31,932 - __main__ - INFO - ID:98550741_xfb315 - 使用字段 summary 作为内容 (长度: 330)\n", "2025-05-29 11:56:31,933 - __main__ - INFO - ID:98550741_xfb315 - 标题: 对哲智律师事务所服务态度恶劣且存在合同纠纷的投诉... | 内容长度: 330\n", "2025-05-29 11:56:31,934 - __main__ - INFO - 处理投诉 - 标题: 对哲智律师事务所服务态度恶劣且存在合同纠纷的投诉... | 内容长度: 330\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：97871276_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:56:43,222 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:56:43,406 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:56:43,490 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98550741_xfb315 [status:201 request:0.082s]\n", "2025-05-29 11:56:43,491 - __main__ - WARNING - ID:98407224_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:56:43,492 - __main__ - INFO - ID:98407224_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:56:43,492 - __main__ - INFO - ID:98407224_xfb315 - 使用字段 summary 作为内容 (长度: 131)\n", "2025-05-29 11:56:43,493 - __main__ - INFO - ID:98407224_xfb315 - 标题: 投诉华为充电/电池故障... | 内容长度: 131\n", "2025-05-29 11:56:43,494 - __main__ - INFO - 处理投诉 - 标题: 投诉华为充电/电池故障... | 内容长度: 131\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98550741_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:56:54,636 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:56:54,744 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:56:55,753 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98407224_xfb315 [status:201 request:1.006s]\n", "2025-05-29 11:56:55,754 - __main__ - WARNING - ID:98891379_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:56:55,755 - __main__ - INFO - ID:98891379_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:56:55,755 - __main__ - INFO - ID:98891379_xfb315 - 使用字段 summary 作为内容 (长度: 97)\n", "2025-05-29 11:56:55,756 - __main__ - INFO - ID:98891379_xfb315 - 标题: 小红书刷单被*，商家失联无效果... | 内容长度: 97\n", "2025-05-29 11:56:55,756 - __main__ - INFO - 处理投诉 - 标题: 小红书刷单被*，商家失联无效果... | 内容长度: 97\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98407224_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:57:05,183 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:57:05,332 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:57:05,611 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98891379_xfb315 [status:201 request:0.276s]\n", "2025-05-29 11:57:05,612 - __main__ - WARNING - ID:96077227_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:57:05,612 - __main__ - INFO - ID:96077227_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:57:05,613 - __main__ - INFO - ID:96077227_xfb315 - 使用字段 summary 作为内容 (长度: 185)\n", "2025-05-29 11:57:05,613 - __main__ - INFO - ID:96077227_xfb315 - 标题: 随意扣除行为分，随意扣费行为，随意封号... | 内容长度: 185\n", "2025-05-29 11:57:05,614 - __main__ - INFO - 处理投诉 - 标题: 随意扣除行为分，随意扣费行为，随意封号... | 内容长度: 185\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98891379_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:57:16,873 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:57:18,158 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:57:18,322 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/96077227_xfb315 [status:201 request:0.162s]\n", "2025-05-29 11:57:18,324 - __main__ - WARNING - ID:98752917_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:57:18,325 - __main__ - INFO - ID:98752917_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:57:18,325 - __main__ - INFO - ID:98752917_xfb315 - 使用字段 summary 作为内容 (长度: 76)\n", "2025-05-29 11:57:18,326 - __main__ - INFO - ID:98752917_xfb315 - 标题: 招商银行信用卡违规收取高额利息投诉... | 内容长度: 76\n", "2025-05-29 11:57:18,327 - __main__ - INFO - 处理投诉 - 标题: 招商银行信用卡违规收取高额利息投诉... | 内容长度: 76\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：96077227_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:57:28,188 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:57:29,308 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:57:29,578 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98752917_xfb315 [status:201 request:0.267s]\n", "2025-05-29 11:57:29,580 - __main__ - WARNING - ID:94337056_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:57:29,581 - __main__ - INFO - ID:94337056_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:57:29,582 - __main__ - INFO - ID:94337056_xfb315 - 使用字段 summary 作为内容 (长度: 293)\n", "2025-05-29 11:57:29,582 - __main__ - INFO - ID:94337056_xfb315 - 标题: 不知道钱站后台有多硬... | 内容长度: 293\n", "2025-05-29 11:57:29,583 - __main__ - INFO - 处理投诉 - 标题: 不知道钱站后台有多硬... | 内容长度: 293\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98752917_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:57:39,627 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:57:39,767 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:57:39,844 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/94337056_xfb315 [status:201 request:0.075s]\n", "2025-05-29 11:57:39,846 - __main__ - WARNING - ID:98875461_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:57:39,847 - __main__ - INFO - ID:98875461_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:57:39,847 - __main__ - INFO - ID:98875461_xfb315 - 使用字段 summary 作为内容 (长度: 964)\n", "2025-05-29 11:57:39,848 - __main__ - INFO - ID:98875461_xfb315 - 标题: 砺带科技未经同意恶意扣款1150元涉嫌高利贷，大家可以一起到... | 内容长度: 964\n", "2025-05-29 11:57:39,849 - __main__ - INFO - 处理投诉 - 标题: 砺带科技未经同意恶意扣款1150元涉嫌高利贷，大家可以一起到... | 内容长度: 964\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：94337056_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:57:51,508 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:57:51,630 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:57:51,673 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98875461_xfb315 [status:201 request:0.040s]\n", "2025-05-29 11:57:51,673 - __main__ - WARNING - ID:17382482172 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:57:51,674 - __main__ - INFO - ID:17382482172 - 潜在内容字段: ['summary', 'url', 'author']\n", "2025-05-29 11:57:51,675 - __main__ - INFO - ID:17382482172 - 使用字段 summary 作为内容 (长度: 115)\n", "2025-05-29 11:57:51,675 - __main__ - INFO - ID:17382482172 - 标题: 商家的问题让消费者承担责任！... | 内容长度: 115\n", "2025-05-29 11:57:51,676 - __main__ - INFO - 处理投诉 - 标题: 商家的问题让消费者承担责任！... | 内容长度: 115\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98875461_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:58:02,039 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:58:03,880 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:58:03,923 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/17382482172 [status:201 request:0.040s]\n", "2025-05-29 11:58:03,924 - __main__ - WARNING - ID:98562392_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:58:03,925 - __main__ - INFO - ID:98562392_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:58:03,926 - __main__ - INFO - ID:98562392_xfb315 - 使用字段 summary 作为内容 (长度: 110)\n", "2025-05-29 11:58:03,926 - __main__ - INFO - ID:98562392_xfb315 - 标题: 腾讯视频拒绝退还重复购买的包月会员费用... | 内容长度: 110\n", "2025-05-29 11:58:03,927 - __main__ - INFO - 处理投诉 - 标题: 腾讯视频拒绝退还重复购买的包月会员费用... | 内容长度: 110\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：17382482172\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:58:14,397 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:58:15,663 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:58:15,783 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98562392_xfb315 [status:201 request:0.118s]\n", "2025-05-29 11:58:15,784 - __main__ - WARNING - ID:95079588_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:58:15,785 - __main__ - INFO - ID:95079588_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:58:15,786 - __main__ - INFO - ID:95079588_xfb315 - 使用字段 summary 作为内容 (长度: 83)\n", "2025-05-29 11:58:15,787 - __main__ - INFO - ID:95079588_xfb315 - 标题: 香港九龙城2号金玉大厦地下98号铺虚假宣传，说我中奖手机后要... | 内容长度: 83\n", "2025-05-29 11:58:15,788 - __main__ - INFO - 处理投诉 - 标题: 香港九龙城2号金玉大厦地下98号铺虚假宣传，说我中奖手机后要... | 内容长度: 83\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98562392_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:58:26,069 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:58:26,305 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:58:26,358 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/95079588_xfb315 [status:201 request:0.051s]\n", "2025-05-29 11:58:26,359 - __main__ - WARNING - ID:98833365_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:58:26,360 - __main__ - INFO - ID:98833365_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:58:26,361 - __main__ - INFO - ID:98833365_xfb315 - 使用字段 summary 作为内容 (长度: 70)\n", "2025-05-29 11:58:26,361 - __main__ - INFO - ID:98833365_xfb315 - 标题: 平安银行信用卡未经同意强制收取年费申请退款无果... | 内容长度: 70\n", "2025-05-29 11:58:26,362 - __main__ - INFO - 处理投诉 - 标题: 平安银行信用卡未经同意强制收取年费申请退款无果... | 内容长度: 70\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：95079588_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:58:36,659 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:58:36,815 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:58:36,933 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98833365_xfb315 [status:201 request:0.116s]\n", "2025-05-29 11:58:36,934 - __main__ - WARNING - ID:260040_people - 提取到的投诉内容为空或过短\n", "2025-05-29 11:58:36,935 - __main__ - INFO - ID:260040_people - 潜在内容字段: ['summary']\n", "2025-05-29 11:58:36,936 - __main__ - INFO - ID:260040_people - 使用字段 summary 作为内容 (长度: 590)\n", "2025-05-29 11:58:36,936 - __main__ - INFO - ID:260040_people - 标题: 在keep应用内付款，收到奖牌，未拆塑封即发现有较大瑕疵，换... | 内容长度: 590\n", "2025-05-29 11:58:36,937 - __main__ - INFO - 处理投诉 - 标题: 在keep应用内付款，收到奖牌，未拆塑封即发现有较大瑕疵，换... | 内容长度: 326\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98833365_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:58:47,515 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:58:47,657 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:58:48,109 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/260040_people [status:201 request:0.449s]\n", "2025-05-29 11:58:48,111 - __main__ - WARNING - ID:95077891_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:58:48,111 - __main__ - INFO - ID:95077891_xfb315 - 潜在内容字段: ['author_avatar']\n", "2025-05-29 11:58:48,112 - __main__ - INFO - ID:95077891_xfb315 - 使用字段 author_avatar 作为内容 (长度: 82)\n", "2025-05-29 11:58:48,113 - __main__ - INFO - ID:95077891_xfb315 - 标题: 网上购机质量太差... | 内容长度: 82\n", "2025-05-29 11:58:48,114 - __main__ - INFO - 处理投诉 - 标题: 网上购机质量太差... | 内容长度: 82\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：260040_people\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:59:00,680 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:59:00,853 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:59:00,898 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/95077891_xfb315 [status:201 request:0.043s]\n", "2025-05-29 11:59:00,899 - __main__ - WARNING - ID:17382732515 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:59:00,900 - __main__ - INFO - ID:17382732515 - 潜在内容字段: ['summary', 'url']\n", "2025-05-29 11:59:00,901 - __main__ - INFO - ID:17382732515 - 使用字段 summary 作为内容 (长度: 544)\n", "2025-05-29 11:59:00,901 - __main__ - INFO - ID:17382732515 - 标题: 携程重复购票出不了票收取手续费... | 内容长度: 544\n", "2025-05-29 11:59:00,902 - __main__ - INFO - 处理投诉 - 标题: 携程重复购票出不了票收取手续费... | 内容长度: 544\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：95077891_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:59:12,508 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:59:12,681 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:59:12,768 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/17382732515 [status:201 request:0.085s]\n", "2025-05-29 11:59:12,769 - __main__ - WARNING - ID:98672548_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:59:12,770 - __main__ - INFO - ID:98672548_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:59:12,770 - __main__ - INFO - ID:98672548_xfb315 - 使用字段 summary 作为内容 (长度: 216)\n", "2025-05-29 11:59:12,771 - __main__ - INFO - ID:98672548_xfb315 - 标题: OPPO手机卡顿发烫闪退要求退货被拒... | 内容长度: 216\n", "2025-05-29 11:59:12,771 - __main__ - INFO - 处理投诉 - 标题: OPPO手机卡顿发烫闪退要求退货被拒... | 内容长度: 216\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：17382732515\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:59:25,326 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:59:25,769 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:59:25,861 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98672548_xfb315 [status:201 request:0.090s]\n", "2025-05-29 11:59:25,862 - __main__ - WARNING - ID:98610747_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:59:25,863 - __main__ - INFO - ID:98610747_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:59:25,863 - __main__ - INFO - ID:98610747_xfb315 - 使用字段 summary 作为内容 (长度: 502)\n", "2025-05-29 11:59:25,864 - __main__ - INFO - ID:98610747_xfb315 - 标题: 快手账号因发布未成年人低俗内容被封禁请求解封... | 内容长度: 502\n", "2025-05-29 11:59:25,864 - __main__ - INFO - 处理投诉 - 标题: 快手账号因发布未成年人低俗内容被封禁请求解封... | 内容长度: 502\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98672548_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:59:35,786 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:59:36,105 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:59:36,411 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98610747_xfb315 [status:201 request:0.303s]\n", "2025-05-29 11:59:36,412 - __main__ - WARNING - ID:98108666_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:59:36,413 - __main__ - INFO - ID:98108666_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 11:59:36,413 - __main__ - INFO - ID:98108666_xfb315 - 使用字段 summary 作为内容 (长度: 94)\n", "2025-05-29 11:59:36,413 - __main__ - INFO - ID:98108666_xfb315 - 标题: 大河票务未按承诺时间退款... | 内容长度: 94\n", "2025-05-29 11:59:36,414 - __main__ - INFO - 处理投诉 - 标题: 大河票务未按承诺时间退款... | 内容长度: 94\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98610747_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:59:46,373 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 11:59:46,554 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 11:59:46,649 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98108666_xfb315 [status:201 request:0.093s]\n", "2025-05-29 11:59:46,651 - __main__ - WARNING - ID:98753196_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 11:59:46,652 - __main__ - INFO - ID:98753196_xfb315 - 潜在内容字段: ['author_avatar']\n", "2025-05-29 11:59:46,652 - __main__ - INFO - ID:98753196_xfb315 - 使用字段 author_avatar 作为内容 (长度: 58)\n", "2025-05-29 11:59:46,652 - __main__ - INFO - ID:98753196_xfb315 - 标题: 招商银行未经允许私自扣款侵犯权益... | 内容长度: 58\n", "2025-05-29 11:59:46,653 - __main__ - INFO - 处理投诉 - 标题: 招商银行未经允许私自扣款侵犯权益... | 内容长度: 58\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98108666_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 11:59:59,195 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 12:00:00,034 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 12:00:01,478 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98753196_xfb315 [status:201 request:1.442s]\n", "2025-05-29 12:00:01,479 - __main__ - WARNING - ID:97447787_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 12:00:01,479 - __main__ - INFO - ID:97447787_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 12:00:01,480 - __main__ - INFO - ID:97447787_xfb315 - 使用字段 summary 作为内容 (长度: 146)\n", "2025-05-29 12:00:01,481 - __main__ - INFO - ID:97447787_xfb315 - 标题: 投诉霸王茶姬漏送餐... | 内容长度: 146\n", "2025-05-29 12:00:01,482 - __main__ - INFO - 处理投诉 - 标题: 投诉霸王茶姬漏送餐... | 内容长度: 146\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98753196_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 12:00:12,783 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 12:00:13,767 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 12:00:14,076 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/97447787_xfb315 [status:201 request:0.308s]\n", "2025-05-29 12:00:14,079 - __main__ - WARNING - ID:98551707_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 12:00:14,080 - __main__ - INFO - ID:98551707_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 12:00:14,081 - __main__ - INFO - ID:98551707_xfb315 - 使用字段 summary 作为内容 (长度: 125)\n", "2025-05-29 12:00:14,081 - __main__ - INFO - ID:98551707_xfb315 - 标题: 众启法律服务所虚假宣传且服务态度恶劣... | 内容长度: 125\n", "2025-05-29 12:00:14,082 - __main__ - INFO - 处理投诉 - 标题: 众启法律服务所虚假宣传且服务态度恶劣... | 内容长度: 125\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：97447787_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 12:00:25,832 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 12:00:26,358 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 12:00:26,479 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98551707_xfb315 [status:201 request:0.118s]\n", "2025-05-29 12:00:26,480 - __main__ - WARNING - ID:98213134_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 12:00:26,481 - __main__ - INFO - ID:98213134_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 12:00:26,481 - __main__ - INFO - ID:98213134_xfb315 - 使用字段 summary 作为内容 (长度: 160)\n", "2025-05-29 12:00:26,482 - __main__ - INFO - ID:98213134_xfb315 - 标题: 易得花高额担保费及未退款问题... | 内容长度: 160\n", "2025-05-29 12:00:26,482 - __main__ - INFO - 处理投诉 - 标题: 易得花高额担保费及未退款问题... | 内容长度: 160\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98551707_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 12:00:36,174 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 12:00:36,473 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 12:00:36,943 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98213134_xfb315 [status:201 request:0.468s]\n", "2025-05-29 12:00:36,944 - __main__ - WARNING - ID:98007164_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 12:00:36,945 - __main__ - INFO - ID:98007164_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 12:00:36,945 - __main__ - INFO - ID:98007164_xfb315 - 使用字段 summary 作为内容 (长度: 924)\n", "2025-05-29 12:00:36,946 - __main__ - INFO - ID:98007164_xfb315 - 标题: Vivo手机主板故障，售后拒绝免费维修... | 内容长度: 924\n", "2025-05-29 12:00:36,947 - __main__ - INFO - 处理投诉 - 标题: Vivo手机主板故障，售后拒绝免费维修... | 内容长度: 924\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98213134_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 12:00:47,982 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 12:00:48,215 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 12:00:48,498 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/98007164_xfb315 [status:201 request:0.281s]\n", "2025-05-29 12:00:48,499 - __main__ - WARNING - ID:97146477_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 12:00:48,500 - __main__ - INFO - ID:97146477_xfb315 - 潜在内容字段: ['author_avatar']\n", "2025-05-29 12:00:48,501 - __main__ - INFO - ID:97146477_xfb315 - 使用字段 author_avatar 作为内容 (长度: 58)\n", "2025-05-29 12:00:48,501 - __main__ - INFO - ID:97146477_xfb315 - 标题: 投诉瑞幸咖啡售后服务欠缺... | 内容长度: 58\n", "2025-05-29 12:00:48,502 - __main__ - INFO - 处理投诉 - 标题: 投诉瑞幸咖啡售后服务欠缺... | 内容长度: 58\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：98007164_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 12:00:50,596 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 12:00:50,598 - __main__ - ERROR - 未找到有效的JSON内容，原始内容前200字符: 看起来您提供的投诉原文链接似乎没有指向实际的文本内容。为了完成您的请求，我需要具体的投诉内容。请提供完整的投诉文本，以便我能准确地进行分析和生成所需的JSON格式信息。...\n", "2025-05-29 12:00:51,374 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 12:00:51,829 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/97146477_xfb315 [status:201 request:0.453s]\n", "2025-05-29 12:00:51,830 - __main__ - WARNING - ID:96081955_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 12:00:51,831 - __main__ - INFO - ID:96081955_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 12:00:51,831 - __main__ - INFO - ID:96081955_xfb315 - 使用字段 summary 作为内容 (长度: 158)\n", "2025-05-29 12:00:51,832 - __main__ - INFO - ID:96081955_xfb315 - 标题: 大雨天司机无故取消订单... | 内容长度: 158\n", "2025-05-29 12:00:51,833 - __main__ - INFO - 处理投诉 - 标题: 大雨天司机无故取消订单... | 内容长度: 158\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：97146477_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 12:01:01,592 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 12:01:02,339 - __main__ - INFO - 最终查询向量维度: 1536\n", "2025-05-29 12:01:02,790 - elasticsearch - INFO - PUT http://**************:9600/pro_mcp_data_complaint_v1/_doc/96081955_xfb315 [status:201 request:0.449s]\n", "2025-05-29 12:01:02,791 - __main__ - WARNING - ID:95323914_xfb315 - 提取到的投诉内容为空或过短\n", "2025-05-29 12:01:02,791 - __main__ - INFO - ID:95323914_xfb315 - 潜在内容字段: ['summary', 'author_avatar']\n", "2025-05-29 12:01:02,792 - __main__ - INFO - ID:95323914_xfb315 - 使用字段 author_avatar 作为内容 (长度: 82)\n", "2025-05-29 12:01:02,792 - __main__ - INFO - ID:95323914_xfb315 - 标题: 咻电充电宝归还后继续扣费 客服电话也打不通... | 内容长度: 82\n", "2025-05-29 12:01:02,793 - __main__ - INFO - 处理投诉 - 标题: 咻电充电宝归还后继续扣费 客服电话也打不通... | 内容长度: 82\n"]}, {"name": "stdout", "output_type": "stream", "text": ["插入：96081955_xfb315\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-29 12:01:15,076 - httpx - INFO - HTTP Request: POST http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "2025-05-29 12:01:17,736 - elasticsearch - INFO - DELETE http://**********:9200/_search/scroll [status:200 request:0.103s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["索引 wzty_cp_merchant_problem 查询完成\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[90]\u001b[39m\u001b[32m, line 7\u001b[39m\n\u001b[32m      4\u001b[39m end_date = \u001b[33m\"\u001b[39m\u001b[33m2025-5-31 00:00:00\u001b[39m\u001b[33m\"\u001b[39m   \u001b[38;5;66;03m# 修改为您需要的日期\u001b[39;00m\n\u001b[32m      6\u001b[39m \u001b[38;5;66;03m# 执行批处理，每批1000条记录\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m7\u001b[39m total_processed = \u001b[43mprocess_single_index\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstart_date\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mend_date\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbatch_size\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m1000\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m      8\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m总共处理了 \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtotal_processed\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m 条记录\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[89]\u001b[39m\u001b[32m, line 91\u001b[39m, in \u001b[36mprocess_single_index\u001b[39m\u001b[34m(start_date, end_date, batch_size)\u001b[39m\n\u001b[32m     87\u001b[39m index_name = \u001b[33m\"\u001b[39m\u001b[33mwzty_cp_merchant_problem\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m     89\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m     90\u001b[39m     \u001b[38;5;66;03m# 直接调用批处理函数处理单个索引\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m91\u001b[39m     total_records = \u001b[43mprocess_index_in_batches\u001b[49m\u001b[43m(\u001b[49m\u001b[43mindex_name\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstart_time\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mend_time\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbatch_size\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     92\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m索引 \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mindex_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m 处理完成，总计处理记录: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtotal_records\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     93\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m total_records\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[89]\u001b[39m\u001b[32m, line 68\u001b[39m, in \u001b[36mprocess_index_in_batches\u001b[39m\u001b[34m(index_name, start_time, end_time, batch_size)\u001b[39m\n\u001b[32m     66\u001b[39m \u001b[38;5;66;03m# 处理当前批次数据\u001b[39;00m\n\u001b[32m     67\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m68\u001b[39m     processed_count = \u001b[43mprocess_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbatch_data\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindex_name\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     69\u001b[39m     total_processed += processed_count\n\u001b[32m     70\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m批次 \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mbatch_count\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m 成功处理: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mprocessed_count\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m 条记录\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[88]\u001b[39m\u001b[32m, line 133\u001b[39m, in \u001b[36mprocess_data\u001b[39m\u001b[34m(current_docs, index_name)\u001b[39m\n\u001b[32m    131\u001b[39m target_doc[\u001b[33m\"\u001b[39m\u001b[33mcomplaint_category\u001b[39m\u001b[33m\"\u001b[39m] = analysis_result.get(\u001b[33m\"\u001b[39m\u001b[33mcomplaint_category\u001b[39m\u001b[33m\"\u001b[39m, [])\n\u001b[32m    132\u001b[39m target_doc[\u001b[33m\"\u001b[39m\u001b[33mkeywords\u001b[39m\u001b[33m\"\u001b[39m] = analysis_result.get(\u001b[33m\"\u001b[39m\u001b[33mkeywords\u001b[39m\u001b[33m\"\u001b[39m, [])\n\u001b[32m--> \u001b[39m\u001b[32m133\u001b[39m target_doc[\u001b[33m\"\u001b[39m\u001b[33membedding\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[43mget_embedding\u001b[49m\u001b[43m(\u001b[49m\u001b[43manalysis_result\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcore_issue\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43membedding_config\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    135\u001b[39m \u001b[38;5;66;03m# 从原始文档复制字段到目标文档，但只复制允许的字段\u001b[39;00m\n\u001b[32m    136\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m field \u001b[38;5;129;01min\u001b[39;00m allowed_fields:\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[80]\u001b[39m\u001b[32m, line 16\u001b[39m, in \u001b[36mget_embedding\u001b[39m\u001b[34m(text, embedding_config)\u001b[39m\n\u001b[32m     11\u001b[39m headers = {\u001b[33m'\u001b[39m\u001b[33mContent-Type\u001b[39m\u001b[33m'\u001b[39m: \u001b[33m'\u001b[39m\u001b[33mapplication/json\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mAuthorization\u001b[39m\u001b[33m'\u001b[39m: \u001b[33mf\u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00maccess_token\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m}\n\u001b[32m     12\u001b[39m req = {\n\u001b[32m     13\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33minput\u001b[39m\u001b[33m\"\u001b[39m: [text],\n\u001b[32m     14\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m: model\n\u001b[32m     15\u001b[39m }\n\u001b[32m---> \u001b[39m\u001b[32m16\u001b[39m embdd_response = \u001b[43mrequests\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpost\u001b[49m\u001b[43m(\u001b[49m\u001b[43murl\u001b[49m\u001b[43m=\u001b[49m\u001b[43mservice_url\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjson\u001b[49m\u001b[43m=\u001b[49m\u001b[43mreq\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     17\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m embdd_response.status_code == \u001b[32m200\u001b[39m:\n\u001b[32m     18\u001b[39m     \u001b[38;5;66;03m# print(\"embdd_response.json():\", len(embdd_response.json()['data'][0]['embedding']))\u001b[39;00m\n\u001b[32m     19\u001b[39m     query_embedding =  embdd_response.json()[\u001b[33m'\u001b[39m\u001b[33mdata\u001b[39m\u001b[33m'\u001b[39m][\u001b[32m0\u001b[39m][\u001b[33m'\u001b[39m\u001b[33membedding\u001b[39m\u001b[33m'\u001b[39m]\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\api.py:115\u001b[39m, in \u001b[36mpost\u001b[39m\u001b[34m(url, data, json, **kwargs)\u001b[39m\n\u001b[32m    103\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mpost\u001b[39m(url, data=\u001b[38;5;28;01mNone\u001b[39;00m, json=\u001b[38;5;28;01mNone\u001b[39;00m, **kwargs):\n\u001b[32m    104\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33mr\u001b[39m\u001b[33;03m\"\"\"Sends a POST request.\u001b[39;00m\n\u001b[32m    105\u001b[39m \n\u001b[32m    106\u001b[39m \u001b[33;03m    :param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    112\u001b[39m \u001b[33;03m    :rtype: requests.Response\u001b[39;00m\n\u001b[32m    113\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m115\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mpost\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjson\u001b[49m\u001b[43m=\u001b[49m\u001b[43mjson\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\api.py:59\u001b[39m, in \u001b[36mrequest\u001b[39m\u001b[34m(method, url, **kwargs)\u001b[39m\n\u001b[32m     55\u001b[39m \u001b[38;5;66;03m# By using the 'with' statement we are sure the session is closed, thus we\u001b[39;00m\n\u001b[32m     56\u001b[39m \u001b[38;5;66;03m# avoid leaving sockets open which can trigger a ResourceWarning in some\u001b[39;00m\n\u001b[32m     57\u001b[39m \u001b[38;5;66;03m# cases, and look like a memory leak in others.\u001b[39;00m\n\u001b[32m     58\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m sessions.Session() \u001b[38;5;28;01mas\u001b[39;00m session:\n\u001b[32m---> \u001b[39m\u001b[32m59\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43msession\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m=\u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\sessions.py:589\u001b[39m, in \u001b[36mSession.request\u001b[39m\u001b[34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[39m\n\u001b[32m    584\u001b[39m send_kwargs = {\n\u001b[32m    585\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mtimeout\u001b[39m\u001b[33m\"\u001b[39m: timeout,\n\u001b[32m    586\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mallow_redirects\u001b[39m\u001b[33m\"\u001b[39m: allow_redirects,\n\u001b[32m    587\u001b[39m }\n\u001b[32m    588\u001b[39m send_kwargs.update(settings)\n\u001b[32m--> \u001b[39m\u001b[32m589\u001b[39m resp = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprep\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43msend_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    591\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\sessions.py:746\u001b[39m, in \u001b[36mSession.send\u001b[39m\u001b[34m(self, request, **kwargs)\u001b[39m\n\u001b[32m    743\u001b[39m         \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[32m    745\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m stream:\n\u001b[32m--> \u001b[39m\u001b[32m746\u001b[39m     \u001b[43mr\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcontent\u001b[49m\n\u001b[32m    748\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m r\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\models.py:902\u001b[39m, in \u001b[36mResponse.content\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    900\u001b[39m         \u001b[38;5;28mself\u001b[39m._content = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    901\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m902\u001b[39m         \u001b[38;5;28mself\u001b[39m._content = \u001b[33;43mb\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mjoin\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43miter_content\u001b[49m\u001b[43m(\u001b[49m\u001b[43mCONTENT_CHUNK_SIZE\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;129;01mor\u001b[39;00m \u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    904\u001b[39m \u001b[38;5;28mself\u001b[39m._content_consumed = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m    905\u001b[39m \u001b[38;5;66;03m# don't need to release the connection; that's been handled by urllib3\u001b[39;00m\n\u001b[32m    906\u001b[39m \u001b[38;5;66;03m# since we exhausted the data.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\models.py:820\u001b[39m, in \u001b[36mResponse.iter_content.<locals>.generate\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m    818\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(\u001b[38;5;28mself\u001b[39m.raw, \u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m    819\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m820\u001b[39m         \u001b[38;5;28;01myield from\u001b[39;00m \u001b[38;5;28mself\u001b[39m.raw.stream(chunk_size, decode_content=\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m    821\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m ProtocolError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    822\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m ChunkedEncodingError(e)\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Roaming\\Python\\Python313\\site-packages\\urllib3\\response.py:628\u001b[39m, in \u001b[36mHTTPResponse.stream\u001b[39m\u001b[34m(self, amt, decode_content)\u001b[39m\n\u001b[32m    626\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    627\u001b[39m     \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m is_fp_closed(\u001b[38;5;28mself\u001b[39m._fp):\n\u001b[32m--> \u001b[39m\u001b[32m628\u001b[39m         data = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43mamt\u001b[49m\u001b[43m=\u001b[49m\u001b[43mamt\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    630\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m data:\n\u001b[32m    631\u001b[39m             \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m data\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Roaming\\Python\\Python313\\site-packages\\urllib3\\response.py:567\u001b[39m, in \u001b[36mHTTPResponse.read\u001b[39m\u001b[34m(self, amt, decode_content, cache_content)\u001b[39m\n\u001b[32m    564\u001b[39m fp_closed = \u001b[38;5;28mgetattr\u001b[39m(\u001b[38;5;28mself\u001b[39m._fp, \u001b[33m\"\u001b[39m\u001b[33mclosed\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[32m    566\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m._error_catcher():\n\u001b[32m--> \u001b[39m\u001b[32m567\u001b[39m     data = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_fp_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mamt\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m fp_closed \u001b[38;5;28;01melse\u001b[39;00m \u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    568\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m amt \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    569\u001b[39m         flush_decoder = \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\AppData\\Roaming\\Python\\Python313\\site-packages\\urllib3\\response.py:533\u001b[39m, in \u001b[36mHTTPResponse._fp_read\u001b[39m\u001b[34m(self, amt)\u001b[39m\n\u001b[32m    530\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m buffer.getvalue()\n\u001b[32m    531\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    532\u001b[39m     \u001b[38;5;66;03m# StringIO doesn't like amt=None\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m533\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_fp\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43mamt\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mif\u001b[39;00m amt \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m._fp.read()\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Python313\\Lib\\http\\client.py:479\u001b[39m, in \u001b[36mHTTPResponse.read\u001b[39m\u001b[34m(self, amt)\u001b[39m\n\u001b[32m    476\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.length \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m amt > \u001b[38;5;28mself\u001b[39m.length:\n\u001b[32m    477\u001b[39m     \u001b[38;5;66;03m# clip the read to the \"end of response\"\u001b[39;00m\n\u001b[32m    478\u001b[39m     amt = \u001b[38;5;28mself\u001b[39m.length\n\u001b[32m--> \u001b[39m\u001b[32m479\u001b[39m s = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfp\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43mamt\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    480\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m s \u001b[38;5;129;01mand\u001b[39;00m amt:\n\u001b[32m    481\u001b[39m     \u001b[38;5;66;03m# Ideally, we would raise IncompleteRead if the content-length\u001b[39;00m\n\u001b[32m    482\u001b[39m     \u001b[38;5;66;03m# wasn't satisfied, but it might break compatibility.\u001b[39;00m\n\u001b[32m    483\u001b[39m     \u001b[38;5;28mself\u001b[39m._close_conn()\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Python313\\Lib\\socket.py:719\u001b[39m, in \u001b[36mSocketIO.readinto\u001b[39m\u001b[34m(self, b)\u001b[39m\n\u001b[32m    717\u001b[39m     \u001b[38;5;28;01<PERSON><PERSON>se\u001b[39;00m \u001b[38;5;167;01mOS<PERSON>rror\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mcannot read from timed out object\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    718\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m719\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_sock\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrecv_into\u001b[49m\u001b[43m(\u001b[49m\u001b[43mb\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    720\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m timeout:\n\u001b[32m    721\u001b[39m     \u001b[38;5;28mself\u001b[39m._timeout_occurred = \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["# 执行批处理\n", "# 设置要处理的日期范围\n", "start_date = \"2020-5-1 00:00:00\"  # 修改为您需要的日期\n", "end_date = \"2025-5-31 00:00:00\"   # 修改为您需要的日期\n", "\n", "# 执行批处理，每批1000条记录\n", "total_processed = process_single_index(start_date, end_date, batch_size=1000)\n", "print(f\"总共处理了 {total_processed} 条记录\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["测试es"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["   # 测试ES连接\n", "  # 测试ES连接\n", "from elasticsearch import Elasticsearch\n", "\n", "try:\n", "    hosts = 'http://**********'\n", "    port = 9200\n", "    http_auth = ('chenchao', 'GhW9U28REX$l')\n", "    \n", "    print(f\"尝试连接到 {hosts}:{port}\")\n", "    # 设置较短的超时时间进行测试\n", "    es_test = Elasticsearch(hosts, http_auth=http_auth, port=port, timeout=10)\n", "    \n", "    if es_test.ping():\n", "        print(\"✅ 连接成功!\")\n", "        print(f\"ES信息: {es_test.info()}\")\n", "    else:\n", "        print(\"❌ 连接失败，但无超时错误\")\n", "except Exception as e:\n", "    print(f\"❌ 连接错误: {str(e)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["获取索引映射"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_date_fields_only():\n", "    # 导入所需库\n", "    from elasticsearch import Elasticsearch\n", "    \n", "    # 定义ES连接信息\n", "    hosts = 'http://**********:9200'\n", "    port = 9200\n", "    http_auth = ('chenchao', 'GhW9U28REX$l')\n", "    \n", "    # 创建ES连接\n", "    es7 = Elasticsearch(\n", "        hosts,\n", "        http_auth=http_auth,\n", "        port=port,\n", "        timeout=3600,\n", "        scheme=\"http\",\n", "        verify_certs=False\n", "    )\n", "    \n", "    # 定义要查询的索引名称\n", "    index_names = [\n", "        \"wzty_cp_merchant_problem\",             # 投诉信息库\n", "                                                # 投诉对象库\n", "    ]  # 排除了不存在的索引\n", "    \n", "    # 存储结果的字典\n", "    all_date_fields = {}\n", "    \n", "    print(\"正在查找各索引的日期字段...\")\n", "    \n", "    # 检查每个索引的映射\n", "    for index_name in index_names:\n", "        try:\n", "            # 获取索引映射\n", "            mapping = es7.indices.get_mapping(index=index_name)\n", "            \n", "            # 提取字段名\n", "            properties = mapping.get(index_name, {}).get('mappings', {}).get('properties', {})\n", "            \n", "            # 寻找可能的日期类型字段\n", "            date_fields = []\n", "            other_fields = []\n", "            \n", "            for field_name, field_info in properties.items():\n", "                if field_info.get('type') == 'date':\n", "                    date_fields.append(field_name)\n", "                else:\n", "                    other_fields.append(field_name)\n", "            \n", "            all_date_fields[index_name] = {\n", "                'date_fields': date_fields,\n", "                'other_field_count': len(other_fields)\n", "            }\n", "            \n", "            print(f\"\\n===== 索引: {index_name} =====\")\n", "            print(f\"总字段数: {len(properties)}\")\n", "            \n", "            if date_fields:\n", "                print(\"日期类型字段:\")\n", "                for date_field in date_fields:\n", "                    print(f\"- {date_field}\")\n", "            else:\n", "                print(\"未找到日期类型字段\")\n", "                \n", "            # 显示一些其他常见的可能时间字段\n", "            time_keywords = ['time', 'date', 'created', 'updated', 'gmt', 'insert']\n", "            potential_time_fields = []\n", "            \n", "            for field in other_fields:\n", "                for keyword in time_keywords:\n", "                    if keyword.lower() in field.lower():\n", "                        potential_time_fields.append(field)\n", "                        break\n", "            \n", "            if potential_time_fields:\n", "                print(\"\\n其他可能的时间相关字段:\")\n", "                for field in potential_time_fields:\n", "                    print(f\"- {field}\")\n", "                    \n", "        except Exception as e:\n", "            print(f\"获取索引 {index_name} 的映射信息失败: {str(e)}\")\n", "    \n", "    print(\"\\n日期字段查找完成\")\n", "    return all_date_fields\n", "\n", "# 执行函数获取日期字段\n", "date_fields = get_date_fields_only()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}