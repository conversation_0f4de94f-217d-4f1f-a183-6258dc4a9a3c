"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7673],{71879:function(e,n){n.Z={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M885.2 446.3l-.2-.8-112.2-285.1c-5-16.1-19.9-27.2-36.8-27.2H281.2c-17 0-32.1 11.3-36.9 27.6L139.4 443l-.3.7-.2.8c-1.3 4.9-1.7 9.9-1 14.8-.1 1.6-.2 3.2-.2 4.8V830a60.9 60.9 0 0060.8 60.8h627.2c33.5 0 60.8-27.3 60.9-60.8V464.1c0-1.3 0-2.6-.1-3.7.4-4.9 0-9.6-1.3-14.1zm-295.8-43l-.3 15.7c-.8 44.9-31.8 75.1-77.1 75.1-22.1 0-41.1-7.1-54.8-20.6S436 441.2 435.6 419l-.3-15.7H229.5L309 210h399.2l81.7 193.3H589.4zm-375 76.8h157.3c24.3 57.1 76 90.8 140.4 90.8 33.7 0 65-9.4 90.3-27.2 22.2-15.6 39.5-37.4 50.7-63.6h156.5V814H214.4V480.1z"}}]},name:"inbox",theme:"outlined"}},64317:function(e,n,o){var l=o(1413),r=o(91),t=o(22270),a=o(67294),i=o(66758),s=o(62633),d=o(85893),p=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","showSearch","options"],c=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","options"],u=function(e,n){var o=e.fieldProps,c=e.children,u=e.params,f=e.proFieldProps,h=e.mode,m=e.valueEnum,v=e.request,x=e.showSearch,g=e.options,Z=(0,r.Z)(e,p),P=(0,a.useContext)(i.Z);return(0,d.jsx)(s.Z,(0,l.Z)((0,l.Z)({valueEnum:(0,t.h)(m),request:v,params:u,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,l.Z)({options:g,mode:h,showSearch:x,getPopupContainer:P.getPopupContainer},o),ref:n,proFieldProps:f},Z),{},{children:c}))},f=a.forwardRef((function(e,n){var o=e.fieldProps,p=e.children,u=e.params,f=e.proFieldProps,h=e.mode,m=e.valueEnum,v=e.request,x=e.options,g=(0,r.Z)(e,c),Z=(0,l.Z)({options:x,mode:h||"multiple",labelInValue:!0,showSearch:!0,suffixIcon:null,autoClearSearchValue:!0,optionLabelProp:"label"},o),P=(0,a.useContext)(i.Z);return(0,d.jsx)(s.Z,(0,l.Z)((0,l.Z)({valueEnum:(0,t.h)(m),request:v,params:u,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,l.Z)({getPopupContainer:P.getPopupContainer},Z),ref:n,proFieldProps:f},g),{},{children:p}))})),h=a.forwardRef(u);h.SearchSelect=f,h.displayName="ProFormComponent",n.Z=h},5966:function(e,n,o){var l=o(97685),r=o(1413),t=o(91),a=o(21770),i=o(8232),s=o(55241),d=o(98423),p=o(67294),c=o(62633),u=o(85893),f=["fieldProps","proFieldProps"],h=["fieldProps","proFieldProps"],m="text",v=function(e){var n=(0,a.Z)(e.open||!1,{value:e.open,onChange:e.onOpenChange}),o=(0,l.Z)(n,2),t=o[0],d=o[1];return(0,u.jsx)(i.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(n){var o,l=n.getFieldValue(e.name||[]);return(0,u.jsx)(s.Z,(0,r.Z)((0,r.Z)({getPopupContainer:function(e){return e&&e.parentNode?e.parentNode:e},onOpenChange:function(e){return d(e)},content:(0,u.jsxs)("div",{style:{padding:"4px 0"},children:[null===(o=e.statusRender)||void 0===o?void 0:o.call(e,l),e.strengthText?(0,u.jsx)("div",{style:{marginTop:10},children:(0,u.jsx)("span",{children:e.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},e.popoverProps),{},{open:t,children:e.children}))}})},x=function(e){var n=e.fieldProps,o=e.proFieldProps,l=(0,t.Z)(e,f);return(0,u.jsx)(c.Z,(0,r.Z)({valueType:m,fieldProps:n,filedConfig:{valueType:m},proFieldProps:o},l))};x.Password=function(e){var n=e.fieldProps,o=e.proFieldProps,a=(0,t.Z)(e,h),i=(0,p.useState)(!1),s=(0,l.Z)(i,2),f=s[0],x=s[1];return null!=n&&n.statusRender&&a.name?(0,u.jsx)(v,{name:a.name,statusRender:null==n?void 0:n.statusRender,popoverProps:null==n?void 0:n.popoverProps,strengthText:null==n?void 0:n.strengthText,open:f,onOpenChange:x,children:(0,u.jsx)("div",{children:(0,u.jsx)(c.Z,(0,r.Z)({valueType:"password",fieldProps:(0,r.Z)((0,r.Z)({},(0,d.Z)(n,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(e){var o;null==n||null===(o=n.onBlur)||void 0===o||o.call(n,e),x(!1)},onClick:function(e){var o;null==n||null===(o=n.onClick)||void 0===o||o.call(n,e),x(!0)}}),proFieldProps:o,filedConfig:{valueType:m}},a))})}):(0,u.jsx)(c.Z,(0,r.Z)({valueType:"password",fieldProps:n,proFieldProps:o,filedConfig:{valueType:m}},a))},x.displayName="ProFormComponent",n.Z=x},90672:function(e,n,o){var l=o(1413),r=o(91),t=o(67294),a=o(62633),i=o(85893),s=["fieldProps","proFieldProps"],d=function(e,n){var o=e.fieldProps,t=e.proFieldProps,d=(0,r.Z)(e,s);return(0,i.jsx)(a.Z,(0,l.Z)({ref:n,valueType:"textarea",fieldProps:o,proFieldProps:t},d))};n.Z=t.forwardRef(d)},28328:function(e,n,o){o.r(n),o.d(n,{default:function(){return L}});var l=o(97857),r=o.n(l),t=o(15009),a=o.n(t),i=o(99289),s=o.n(i),d=o(97131),p=o(34994),c=o(5966),u=o(90672),f=o(64317),h=o(1413),m=o(87462),v=o(67294),x=o(71879),g=o(57080),Z=function(e,n){return v.createElement(g.Z,(0,m.Z)({},e,{ref:n,icon:x.Z}))};var P=v.forwardRef(Z),C=o(21532),j=o(11550),y=o(9105),F=o(90789),T=o(85893),w=v.forwardRef((function(e,n){var o,l=e.fieldProps,r=e.title,t=void 0===r?"单击或拖动文件到此区域进行上传":r,a=e.icon,i=void 0===a?(0,T.jsx)(P,{}):a,s=e.description,d=void 0===s?"支持单次或批量上传":s,p=e.action,c=e.accept,u=e.onChange,f=e.value,m=e.children,x=e.max,g=e.proFieldProps,Z=(0,v.useContext)(C.ZP.ConfigContext),F=(0,v.useContext)(y.A),w=(null==g?void 0:g.mode)||F.mode||"edit",b=Z.getPrefixCls("upload"),S=(void 0===x||!f||(null==f?void 0:f.length)<x)&&"read"!==w&&!0!==(null==g?void 0:g.readonly);return(0,T.jsxs)(j.Z.Dragger,(0,h.Z)((0,h.Z)({ref:n,name:"files",action:p,accept:c,fileList:f},l),{},{onChange:function(e){null==u||u(e),null!=l&&l.onChange&&(null==l||l.onChange(e))},style:(0,h.Z)((0,h.Z)({flexDirection:"column",alignItems:"center"},null==l?void 0:l.style),{},{display:S?(null==l||null===(o=l.style)||void 0===o?void 0:o.display)||"flex":"none"}),children:[(0,T.jsx)("p",{className:"".concat(b,"-drag-icon"),children:i}),(0,T.jsx)("p",{className:"".concat(b,"-text"),children:t}),(0,T.jsx)("p",{className:"".concat(b,"-hint"),children:d}),m?(0,T.jsx)("div",{className:"".concat(b,"-extra"),style:{padding:16},children:m}):null]}))})),b=(0,F.G)(w,{getValueFromEvent:function(e){return e.fileList}}),S=o(35312),k=o(2453),R=o(4393),N=(0,o(24444).kc)((function(e){return{optional:{color:e.token.colorTextSecondary,fontStyle:"normal"}}})),q=[],V=function(e){console.log("selected ".concat(e))},E={name:"file",multiple:!0,action:"https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload",onChange:function(e){var n=e.file.status;"uploading"!==n&&console.log(e.file,e.fileList),"done"===n?k.ZP.success("".concat(e.file.name," file uploaded successfully.")):"error"===n&&k.ZP.error("".concat(e.file.name," file upload failed."))},onDrop:function(e){console.log("Dropped files",e.dataTransfer.files)}},L=function(){var e=N().styles,n=(0,S.useRequest)(fakeSubmitForm,{manual:!0,onSuccess:function(){k.ZP.success("提交成功")}}).run,o=function(){var e=s()(a()().mark((function e(o){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n(o);case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}();return(0,T.jsx)(d._z,{content:"表单页用于向用户收集或验证信息，基础表单常见于数据项较少的表单场景。",children:(0,T.jsx)(R.Z,{bordered:!1,children:(0,T.jsxs)(p.A,{hideRequiredMark:!0,style:{margin:"auto",marginTop:8,marginBottom:24,maxWidth:600,paddingBottom:24},name:"basic",layout:"vertical",initialValues:{public:"1"},onFinish:o,children:[(0,T.jsx)(c.Z,{width:"md",label:(0,T.jsxs)("span",{children:["数据集名称",(0,T.jsx)("em",{className:e.optional,children:"（选填）"})]}),tooltip:"请输入数据集的名称",name:"title",rules:[{required:!0,message:"请输入标题"}],placeholder:"给目标起个名字"}),(0,T.jsx)(u.Z,{label:(0,T.jsxs)("span",{children:["数据集描述",(0,T.jsx)("em",{className:e.optional,children:"（选填,描述数据集特点和作用等。）"})]}),name:"goal",rules:[{required:!0,message:"请输入目标描述"}],placeholder:"请输入你的阶段性工作目标"}),(0,T.jsx)(f.Z,{name:"数据集标签",width:"xl",label:"数据集标签",style:{width:"100%"},mode:"tags",placeholder:"Tags Mode",onChange:V,options:q}),(0,T.jsx)(b,r()(r()({},E),{},{max:50,label:(0,T.jsxs)("span",{children:["数据文件上传",(0,T.jsx)("em",{className:e.optional,children:"（支持CSV、Excel、TXT文件，每行一个文本数据）"})]}),name:"文件上传",accept:".csv, .xls, .xlsx"}))]})})})}}}]);