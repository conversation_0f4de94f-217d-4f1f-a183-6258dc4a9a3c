from abc import ABC, abstractmethod
import asyncio
from typing import List, Dict, Any
import httpx
import json
import requests
from app.utils.enums import RAGSearchType
from app.utils.enums import chunk_type

# from utils.es_util import ESUtil



def _parse_es_host(es_host: str) -> Dict[str, Any]:
    """解析ES主机配置，支持多种格式"""
    try:
        # 尝试解析为JSON数组格式
        if es_host.startswith('[') and es_host.endswith(']'):
            es_host_list = json.loads(es_host)
            if es_host_list and isinstance(es_host_list, list):
                return es_host_list[0]  # 返回第一个节点

        # 尝试解析为单个主机格式 "host:port"
        if ':' in es_host:
            host, port = es_host.split(':', 1)
            return {"host": host.strip(), "port": int(port.strip())}

        # 默认端口9200
        return {"host": es_host.strip(), "port": 9200}

    except (json.JSONDecodeError, ValueError, KeyError) as e:
        print(f"ES主机配置解析失败: {e}, 使用默认配置")
        return {"host": "localhost", "port": 9200}

def _check_es_version(es_base_url: str) -> Dict[str, Any]:
    """检查ES版本并返回请求参数"""
    request_params = {}
    try:
        version_res = requests.get(f"{es_base_url}/", timeout=5)
        version_res.raise_for_status()  # 检查HTTP状态码

        version_info = version_res.json()
        es_version = version_info["version"]["number"]
        print(f"ES版本: {es_version}")

        # 版本比较：7.3及以上版本支持 allow_partial_search_results 参数
        version_tuple = tuple(map(int, es_version.split('.')[:2]))
        if version_tuple >= (7, 3):
            request_params["allow_partial_search_results"] = "false"
            print("ES版本支持 allow_partial_search_results 参数")

    except requests.exceptions.RequestException as e:
        print(f"ES版本检查失败 - 网络错误: {e}")
    except (KeyError, ValueError, json.JSONDecodeError) as e:
        print(f"ES版本检查失败 - 响应解析错误: {e}")
    except Exception as e:
        print(f"ES版本检查失败 - 未知错误: {e}")

    return request_params

def es_vector_retriever(query_embedding: List[float],
                        chunk_type: str,
                        kb_id: str,
                        top_k: int = 5,
                        threshold: float = 0.7,  # 阈值范围[0, 1]，0.7表示较高相似度
                        es_host: str = '[{"host": "**************", "port": 9600},{"host": "**************", "port": 9600}]',
                        index_name: str = "wise_agent_chunk_index") -> List[Dict[str, Any]]:
    """基于ES REST API的向量检索"""
    # 解析ES主机配置
    host_config = _parse_es_host(es_host)
    es_base_url = f"http://{host_config['host']}:{host_config['port']}"
    es_url = f"{es_base_url}/{index_name}/_search"
    print(f"ES连接地址: {es_url}")

    # 构建请求头
    headers = {"Content-Type": "application/json"}
    
    # 构建查询DSL，添加is_forbidden过滤条件
    search_query = {
        "query": {
            "script_score": {
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"chunk_type": chunk_type}},
                            {"term": {"knowledge_base_id": kb_id}},
                            {
                                "bool": {
                                    "should": [
                                        {"term": {"is_forbidden": False}},
                                        {"bool": {
                                            "must_not": {
                                                "exists": {"field": "is_forbidden"}
                                            }
                                        }}
                                    ]
                                }
                            }
                        ]
                    }
                },
                "script": {
                    "source": "Math.max(0, cosineSimilarity(params.query_vector, 'embedding'))",
                    "params": {"query_vector": query_embedding}
                }
            }
        },
        "size": top_k,
        "_source": ["id", "chunk_id", "file_id", "knowledge_base_id"],
    }
    print("search_query: ",search_query)

    # 检查ES版本并获取请求参数
    request_params = _check_es_version(es_base_url)

    try:
        # 发送HTTP请求
        response = requests.post(
            url=es_url,
            headers=headers,
            json=search_query,
            params=request_params,  # 参数放在这里
            timeout=10
        )
        
        # 检查HTTP状态码
        if response.status_code != 200:
            raise Exception(f"ES请求失败: {response.text}")
            
        # 处理响应结果
        results = []
        for hit in response.json()["hits"]["hits"]:
            score = hit["_score"]  # 使用原始分数，范围[0, 1]
            print(f"score: {score} ")
            # 注意：使用了 Math.max(0, cosineSimilarity)，分数范围是[0, 1]
            # 0表示无相关性，1表示完全相关，阈值可以直观地设置在0-1之间
            if score < threshold:
                continue
                
            source = hit["_source"]
            results.append({
                "chunk_id": source["chunk_id"],
                "score": score,
                "search_type": RAGSearchType.VECTOR
            })
        return results[:top_k]
        
    except requests.exceptions.RequestException as e:
        raise Exception(f"ES连接异常: {str(e)}")
    except Exception as e:
        raise Exception(f"检索处理失败: {str(e)}")


def es_data_ingester(
    chunk_id: str,
    document: Dict[str, Any],
    es_host: str = '[{"host": "**************", "port": 9600},{"host": "**************", "port": 9600}]',
    index_name: str = "wise_agent_chunk_index"
) -> Dict[str, Any]:
    """使用ES REST API插入文档数据"""
    # 解析ES主机配置
    host_config = _parse_es_host(es_host)
    doc_url = f"http://{host_config['host']}:{host_config['port']}/{index_name}/_doc/{chunk_id}"
    headers = {"Content-Type": "application/json"}
    
    # document = {
    #     "chunk_id": chunk_id,
    #     "knowledge_base_id": knowledge_base_id,
    #     "chunk_type": chunk_type,
    #     "embedding": embedding,
    #     "content": content
    # }

    try:
        response = requests.put(
            url=doc_url,
            headers=headers,
            json=document,
            timeout=10
        )
        
        if response.status_code not in [200, 201]:
            raise Exception(f"文档插入失败: {response.text}")
            
        return response.json()
        
    except requests.exceptions.RequestException as e:
        raise Exception(f"ES连接异常: {str(e)}")
    except Exception as e:
        raise Exception(f"插入操作失败: {str(e)}")
    
def update_by_query(
    file_id: str,
    last_updated: str,
    is_forbidden: bool,
    es_host: str = '[{"host": "**************", "port": 9600},{"host": "**************", "port": 9600}]',
    index_name: str = "wise_agent_chunk_index"
) -> Dict[str, Any]:
    """根据file_id批量更新ES文档，添加is_forbidden字段"""
    # 解析ES主机配置
    host_config = _parse_es_host(es_host)
    update_url = f"http://{host_config['host']}:{host_config['port']}/{index_name}/_update_by_query"
    headers = {"Content-Type": "application/json"}
    
    query = {
        "query": {
            "term": {
                "file_id": file_id
            }
        },
        "script": {
            "source": "ctx._source.is_forbidden = params.is_forbidden; ctx._source.last_updated = params.last_updated",
            "lang": "painless",
            "params": {
                "is_forbidden": is_forbidden,
                "last_updated": last_updated
            }
        }
    }

    try:
        response = requests.post(
            url=update_url,
            headers=headers,
            json=query,
            timeout=30
        )
        
        if response.status_code != 200:
            raise Exception(f"批量更新失败: {response.text}")
            
        return response.json()
        
    except requests.exceptions.RequestException as e:
        raise Exception(f"ES连接异常: {str(e)}")
    except Exception as e:
        raise Exception(f"批量更新操作失败: {str(e)}")


def es_data_deleter(ids: List[str],
                   chunk_type: str,
                   kb_id: str,
                   es_host: str = '[{"host": "**************", "port": 9600},{"host": "**************", "port": 9600}]',
                   index_name: str = "wise_agent_chunk_index") -> Dict[str, Any]:
    """使用ES REST API批量删除文档"""
    # 解析ES主机配置
    host_config = _parse_es_host(es_host)
    delete_url = f"http://{host_config['host']}:{host_config['port']}/{index_name}/_delete_by_query"
    headers = {"Content-Type": "application/json"}
    
    query = {
        "query": {
            "bool": {
                "must": [
                    {"terms": {"chunk_id": ids}},
                    {"term": {"chunk_type": chunk_type}},
                    {"term": {"knowledge_base_id": kb_id}}
                ]
            }
        }
    }

    try:
        response = requests.post(
            url=delete_url,
            headers=headers,
            json=query,
            timeout=10
        )
        
        if response.status_code != 200:
            raise Exception(f"文档删除失败: {response.text}")
            
        return response.json()
        
    except requests.exceptions.RequestException as e:
        raise Exception(f"ES连接异常: {str(e)}")
    except Exception as e:
        raise Exception(f"删除操作失败: {str(e)}")


def es_query_retriever(search_query: str,
                      es_host: str = '[{"host": "**************", "port": 9600},{"host": "**************", "port": 9600}]',
                      es_username: str = None,
                      es_password: str = None) -> List[Dict[str, Any]]:
    """基于ES REST API的文本检索"""
    # 构建请求头
    headers = {"Content-Type": "application/json"}

    # 构建认证信息
    auth = None
    if es_username and es_password:
        auth = (es_username, es_password)

    try:
        # 解析ES主机配置
        host_config = _parse_es_host(es_host)
        es_url = f"http://{host_config['host']}:{host_config['port']}"
        
        response = requests.get(
            url=es_url,
            headers=headers,
            auth=auth,
            json=search_query,
            timeout=60
        )
        if response.status_code != 200:
            raise Exception(f"检索失败: {response.text}")
            
        return response.json()["hits"]["hits"]
        
    except requests.exceptions.RequestException as e:
        raise Exception(f"ES连接异常: {str(e)}")
    except Exception as e:
        raise Exception(f"检索操作失败: {str(e)}")




def es_vector_retriever_new(query_embedding: List[float],
                        chunk_type: str,
                        kb_id: str,
                        top_k: int = 5,
                        threshold: float = 0.7,  # 阈值范围[0, 1]，0.7表示较高相似度
                        es_host: str = '[{"host": "**************", "port": 9600},{"host": "**************", "port": 9600}]',
                        index_name: str = "wise_agent_chunk_index") -> List[Dict[str, Any]]:
    """基于ES REST API的向量检索"""
    # 解析ES主机配置
    host_config = _parse_es_host(es_host)
    es_base_url = f"http://{host_config['host']}:{host_config['port']}"
    es_url = f"{es_base_url}/{index_name}/_search"
    print(f"ES连接地址: {es_url}")

    # 构建请求头
    headers = {"Content-Type": "application/json"}
    
    # 构建查询DSL，添加is_forbidden过滤条件
    search_query = {
        "query": {
            "script_score": {
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"chunk_type": chunk_type}},
                            {"term": {"knowledge_base_id": kb_id}},
                            {
                                "bool": {
                                    "should": [
                                        {"term": {"is_forbidden": False}},
                                        {"bool": {
                                            "must_not": {
                                                "exists": {"field": "is_forbidden"}
                                            }
                                        }}
                                    ]
                                }
                            }
                        ]
                    }
                },
                "script": {
                    "source": "Math.max(0, cosineSimilarity(params.query_vector, 'embedding'))",
                    "params": {"query_vector": query_embedding}
                }
            }
        },
        "size": top_k,
        "_source": ["id", "chunk_id", "index_content", "file_id", "knowledge_base_id"],
    }
    print("search_query: ",search_query)

    # 检查ES版本并获取请求参数
    request_params = _check_es_version(es_base_url)

    try:
        # 发送HTTP请求
        response = requests.post(
            url=es_url,
            headers=headers,
            json=search_query,
            params=request_params,  # 参数放在这里
            timeout=10
        )
        
        # 检查HTTP状态码
        if response.status_code != 200:
            raise Exception(f"ES请求失败: {response.text}")
            
        # 处理响应结果
        results = []
        for hit in response.json()["hits"]["hits"]:
            score = hit["_score"]  # 使用原始分数，范围[0, 1]
            print(f"score: {score} ")
            # 注意：使用了 Math.max(0, cosineSimilarity)，分数范围是[0, 1]
            # 0表示无相关性，1表示完全相关，阈值可以直观地设置在0-1之间
            if score < threshold:
                continue
                
            source = hit["_source"]
            results.append({
                "chunk_id": source["chunk_id"],
                "score": score,
                "search_type": RAGSearchType.VECTOR,
                "content": source.get("index_content", "")
            })
        return results[:top_k]
        
    except requests.exceptions.RequestException as e:
        raise Exception(f"ES连接异常: {str(e)}")
    except Exception as e:
        raise Exception(f"检索处理失败: {str(e)}")

