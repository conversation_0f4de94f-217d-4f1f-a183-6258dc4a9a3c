from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel
from mongoengine import (
    StringField, 
    DateTimeField, 
    IntField, 
    ListField, 
    BooleanField,
    URLField,
    Document
)

class UseCase(Document):
    name = StringField(required=True)                    # 案例名称
    description = StringField(required=True)             # 案例描述
    cover_image = StringField()                         # 封面图片路径
    redirect_url = URLField()                           # 跳转链接
    view_count = IntField(default=0)                    # 点击量
    is_active = BooleanField(default=True)             # 是否启用
    creator_id = IntField(required=True)                # 创建人ID
    creator_name = StringField()                        # 创建人姓名
    created_at = DateTimeField(default=datetime.now)    # 创建时间
    updated_at = DateTimeField(default=datetime.now)    # 更新时间
    tags = ListField(StringField(), default=list)       # 标签列表
    deleted_at = DateTimeField()                        # 删除时间
    deleted_by = IntField()                            # 删除人ID

    meta = {
        'collection': 'use_cases',
        'indexes': [
            'name',
            'creator_id',
            'created_at',
            'tags'
        ]
    }

class UseCaseCreate(BaseModel):
    name: str
    description: str
    cover_image: str
    redirect_url: str
    tags: List[str]
 
class UseCaseUpdate(BaseModel):
    name: Optional[str] = None  
    description: Optional[str] = None
    cover_image: Optional[str] = None
    redirect_url: Optional[str] = None
    tags: Optional[List[str]] = None

class UseCaseResponse(BaseModel):
    id: str
    name: str
    description: str
    cover_image: str
    redirect_url: str
    tags: List[str]
    view_count: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
