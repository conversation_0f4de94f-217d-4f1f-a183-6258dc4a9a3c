from langgraph.graph import StateGraph, E<PERSON>
from typing import TypedDict, List,Dict, Optional, Annotated
from concurrent.futures import ThreadPoolExecutor
from app.utils.logging_config import setup_logging, get_logger
from app.engines.retrieval.base_retriever import es_query_retriever
from langchain_openai import ChatOpenAI
from models.llm import LLMModel

setup_logging()
logger = get_logger(__name__)

# 定义状态容器
class SearchState(TypedDict):
    messages: Annotated[Optional[List[Dict]], "用户原始消息"]
    deep_search: Annotated[bool, "是否需要深度搜索"]
    search_query: Annotated[Optional[str], "生成的搜索查询"]
    search_results: Annotated[Optional[List[Dict]], "原始搜索结果"]
    relevant_sources: Annotated[Optional[List[Dict]], "相关来源"]
    response: Annotated[Optional[Dict], "最终响应"]

class SearchWorkflow:
    def __init__(self, config: dict, user_id: str,llm:LLMModel):
        self.config = config
        self.user_id = user_id
        self.llm = ChatOpenAI(
                model=llm.m_name,
                temperature=llm.temperature or 0.1,
                openai_api_key=llm.api_key,
                openai_api_base= llm.service_url,
                stream = False

            )
        self.thread_pool_executor = ThreadPoolExecutor(max_workers=5)
        self.workflow = StateGraph(SearchState)
        self._setup_workflow()

    def _setup_workflow(self):
        """配置工作流节点和边"""
        # 添加节点
        self.workflow.add_node("search_decision", self.decide_search_needs)
        self.workflow.add_node("generate_query", self.generate_search_query)
        self.workflow.add_node("execute_search", self.perform_web_search)
        self.workflow.add_node("process_results", self.process_search_results)
        self.workflow.add_node("generate_response", self.generate_final_response)

        # 设置入口点
        self.workflow.set_entry_point("search_decision")

        # 添加条件边
        self.workflow.add_conditional_edges(
            "search_decision",
            self.route_by_search_decision,
            {
                "needs_search": "generate_query",
                "direct_answer": "generate_response"
            }
        )
        
        # 添加顺序边
        self.workflow.add_edge("generate_query", "execute_search")
        self.workflow.add_edge("execute_search", "process_results")
        self.workflow.add_edge("process_results", "generate_response")
        self.workflow.add_edge("generate_response", END)

    async def decide_search_needs(self, state: SearchState) -> dict:
        """判断是否需要搜索"""
        logger.info('执行搜索需求判断')
        # 实际应接入模型判断逻辑
        return {"deep_search": True}

    async def generate_search_query(self, state: SearchState) -> dict:
        """生成搜索查询"""
        logger.info('生成搜索查询')
        query_message = state['message']
        logger.info(f'query_message==>{query_message}')
        return {"search_query": query_message}

    async def perform_web_search(self, state: SearchState) -> dict:
        """执行网络搜索"""
        logger.info('执行网络搜索')
        query = {
            "_source": [
                "site_url",
                "title",
                "publishtime",
                "tag",
                "url",
			    "content"
                ],
                "size": 5,
                "query": {
                    "bool": {
                        "should": [
                            {
                                "match": {
                                    "title": state['search_query']
                                }
                            },
                            {
                                "match": {
                                    "content": state['search_query']
                                }
                            }
                        ]
                    }
                },
                "sort": [
                    {
                        "publishtime": {
                            "order": "desc"
                        }
                    }
                ]
            }
        
        search_results = es_query_retriever(query)
        # 示例实现，需替换为实际SearXNG集成
        return {"search_results": search_results}

    async def process_search_results(self, state: SearchState) -> dict:
        """处理搜索结果"""
        logger.info('处理搜索结果')
        # 示例实现，需添加实际嵌入转换和相似性搜索
        return {"relevant_sources": state["search_results"]}

    async def generate_final_response(self, state: SearchState) -> dict:
        """生成最终响应"""
        logger.info('生成最终响应')
        if state["deep_search"]:
            return {"response":  state['relevant_sources']}
        return {"response": "无需搜索的直接回答"}

    def route_by_search_decision(self, state: SearchState) -> str:
        """路由决策"""
        return "needs_search" if state["deep_search"] else "direct_answer"

    async def run(self, messages: List[Dict]):
        """执行工作流"""
        try:
            initial_state = SearchState(
                messages=messages,
                deep_search=True,
                search_query=None,
                search_results=None,
                relevant_sources=None,
                response=None
            )
            app = self.workflow.compile()
            return await app.ainvoke(initial_state)
        except Exception as e:
            logger.error(f"工作流执行失败: {str(e)}")
            return {"error": str(e)}
