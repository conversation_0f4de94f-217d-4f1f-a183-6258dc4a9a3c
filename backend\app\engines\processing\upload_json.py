import os
import json
import pandas as pd
import requests
from config import *
from urllib.parse import quote

# 数据上传进知识库的方法
class wiseRagImport:

    def __init__(self, api_base, api_key, dataset_id, parent_id, username, dataseset_name):
        self.fastgpt_api_base = api_base
        self.api_key = api_key
        self.dataset_id = dataset_id
        self.parent_id = parent_id
        self.username = username
        self.dataseset_name = dataseset_name
        print('用户 {} 执行 {} 知识库插入任务(api-key:{} | api-base: {})'.format(username, dataseset_name, api_key, api_base))

    def generate_wiserag_import_data(self, datas):
        results = []
        for index, i in enumerate(datas):
            results.append({
                "q": i['context'],
                "a": i.get('contextExtra', ''),
                "chunkIndex": index,
                "indexes": [
                    {
                        "defaultIndex": True,
                        "type": "chunk",
                        "text": j
                    } for j in i['index'] if j]
            })

        return results

    def judge_post_file_houzhui(self, houzhui: str):
        if houzhui == 'load':
            ContentType = 'text/html'
        elif houzhui == '123':
            ContentType = 'application/vnd.lotus-1-2-3'
        elif houzhui == '3ds':
            ContentType = 'image/x-3ds'
        elif houzhui == '3g2':
            ContentType = 'video/3gpp'
        elif houzhui == '3ga':
            ContentType = 'video/3gpp'
        elif houzhui == '3gp':
            ContentType = 'video/3gpp'
        elif houzhui == '3gpp':
            ContentType = 'video/3gpp'
        elif houzhui == '602':
            ContentType = 'application/x-t602'
        elif houzhui == '669':
            ContentType = 'audio/x-mod'
        elif houzhui == '7z':
            ContentType = 'application/x-7z-compressed'
        elif houzhui == 'a':
            ContentType = 'application/x-archive'
        elif houzhui == 'aac':
            ContentType = 'audio/mp4'
        elif houzhui == 'abw':
            ContentType = 'application/x-abiword'
        elif houzhui == 'abw.crashed':
            ContentType = 'application/x-abiword'
        elif houzhui == 'abw.gz':
            ContentType = 'application/x-abiword'
        elif houzhui == 'ac3':
            ContentType = 'audio/ac3'
        elif houzhui == 'ace':
            ContentType = 'application/x-ace'
        elif houzhui == 'adb':
            ContentType = 'text/x-adasrc'
        elif houzhui == 'ads':
            ContentType = 'text/x-adasrc'
        elif houzhui == 'afm':
            ContentType = 'application/x-font-afm'
        elif houzhui == 'ag':
            ContentType = 'image/x-applix-graphics'
        elif houzhui == 'ai':
            ContentType = 'application/illustrator'
        elif houzhui == 'aif':
            ContentType = 'audio/x-aiff'
        elif houzhui == 'aifc':
            ContentType = 'audio/x-aiff'
        elif houzhui == 'aiff':
            ContentType = 'audio/x-aiff'
        elif houzhui == 'al':
            ContentType = 'application/x-perl'
        elif houzhui == 'alz':
            ContentType = 'application/x-alz'
        elif houzhui == 'amr':
            ContentType = 'audio/amr'
        elif houzhui == 'ani':
            ContentType = 'application/x-navi-animation'
        elif houzhui == 'anim[1-9j]':
            ContentType = 'video/x-anim'
        elif houzhui == 'anx':
            ContentType = 'application/annodex'
        elif houzhui == 'ape':
            ContentType = 'audio/x-ape'
        elif houzhui == 'arj':
            ContentType = 'application/x-arj'
        elif houzhui == 'arw':
            ContentType = 'image/x-sony-arw'
        elif houzhui == 'as':
            ContentType = 'application/x-applix-spreadsheet'
        elif houzhui == 'asc':
            ContentType = 'text/plain'
        elif houzhui == 'asf':
            ContentType = 'video/x-ms-asf'
        elif houzhui == 'asp':
            ContentType = 'application/x-asp'
        elif houzhui == 'ass':
            ContentType = 'text/x-ssa'
        elif houzhui == 'asx':
            ContentType = 'audio/x-ms-asx'
        elif houzhui == 'atom':
            ContentType = 'application/atom+xml'
        elif houzhui == 'au':
            ContentType = 'audio/basic'
        elif houzhui == 'avi':
            ContentType = 'video/x-msvideo'
        elif houzhui == 'aw':
            ContentType = 'application/x-applix-word'
        elif houzhui == 'awb':
            ContentType = 'audio/amr-wb'
        elif houzhui == 'awk':
            ContentType = 'application/x-awk'
        elif houzhui == 'axa':
            ContentType = 'audio/annodex'
        elif houzhui == 'axv':
            ContentType = 'video/annodex'
        elif houzhui == 'bak':
            ContentType = 'application/x-trash'
        elif houzhui == 'bcpio':
            ContentType = 'application/x-bcpio'
        elif houzhui == 'bdf':
            ContentType = 'application/x-font-bdf'
        elif houzhui == 'bib':
            ContentType = 'text/x-bibtex'
        elif houzhui == 'bin':
            ContentType = 'application/octet-stream'
        elif houzhui == 'blend':
            ContentType = 'application/x-blender'
        elif houzhui == 'blender':
            ContentType = 'application/x-blender'
        elif houzhui == 'bmp':
            ContentType = 'image/bmp'
        elif houzhui == 'bz':
            ContentType = 'application/x-bzip'
        elif houzhui == 'bz2':
            ContentType = 'application/x-bzip'
        elif houzhui == 'c':
            ContentType = 'text/x-csrc'
        elif houzhui == 'c++':
            ContentType = 'text/x-c++src'
        elif houzhui == 'cab':
            ContentType = 'application/vnd.ms-cab-compressed'
        elif houzhui == 'cb7':
            ContentType = 'application/x-cb7'
        elif houzhui == 'cbr':
            ContentType = 'application/x-cbr'
        elif houzhui == 'cbt':
            ContentType = 'application/x-cbt'
        elif houzhui == 'cbz':
            ContentType = 'application/x-cbz'
        elif houzhui == 'cc':
            ContentType = 'text/x-c++src'
        elif houzhui == 'cdf':
            ContentType = 'application/x-netcdf'
        elif houzhui == 'cdr':
            ContentType = 'application/vnd.corel-draw'
        elif houzhui == 'cer':
            ContentType = 'application/x-x509-ca-cert'
        elif houzhui == 'cert':
            ContentType = 'application/x-x509-ca-cert'
        elif houzhui == 'cgm':
            ContentType = 'image/cgm'
        elif houzhui == 'chm':
            ContentType = 'application/x-chm'
        elif houzhui == 'chrt':
            ContentType = 'application/x-kchart'
        elif houzhui == 'class':
            ContentType = 'application/x-java'
        elif houzhui == 'cls':
            ContentType = 'text/x-tex'
        elif houzhui == 'cmake':
            ContentType = 'text/x-cmake'
        elif houzhui == 'cpio':
            ContentType = 'application/x-cpio'
        elif houzhui == 'cpio.gz':
            ContentType = 'application/x-cpio-compressed'
        elif houzhui == 'cpp':
            ContentType = 'text/x-c++src'
        elif houzhui == 'cr2':
            ContentType = 'image/x-canon-cr2'
        elif houzhui == 'crt':
            ContentType = 'application/x-x509-ca-cert'
        elif houzhui == 'crw':
            ContentType = 'image/x-canon-crw'
        elif houzhui == 'cs':
            ContentType = 'text/x-csharp'
        elif houzhui == 'csh':
            ContentType = 'application/x-csh'
        elif houzhui == 'css':
            ContentType = 'text/css'
        elif houzhui == 'cssl':
            ContentType = 'text/css'
        elif houzhui == 'csv':
            ContentType = 'text/csv'
        elif houzhui == 'cue':
            ContentType = 'application/x-cue'
        elif houzhui == 'cur':
            ContentType = 'image/x-win-bitmap'
        elif houzhui == 'cxx':
            ContentType = 'text/x-c++src'
        elif houzhui == 'd':
            ContentType = 'text/x-dsrc'
        elif houzhui == 'dar':
            ContentType = 'application/x-dar'
        elif houzhui == 'dbf':
            ContentType = 'application/x-dbf'
        elif houzhui == 'dc':
            ContentType = 'application/x-dc-rom'
        elif houzhui == 'dcl':
            ContentType = 'text/x-dcl'
        elif houzhui == 'dcm':
            ContentType = 'application/dicom'
        elif houzhui == 'dcr':
            ContentType = 'image/x-kodak-dcr'
        elif houzhui == 'dds':
            ContentType = 'image/x-dds'
        elif houzhui == 'deb':
            ContentType = 'application/x-deb'
        elif houzhui == 'der':
            ContentType = 'application/x-x509-ca-cert'
        elif houzhui == 'desktop':
            ContentType = 'application/x-desktop'
        elif houzhui == 'dia':
            ContentType = 'application/x-dia-diagram'
        elif houzhui == 'diff':
            ContentType = 'text/x-patch'
        elif houzhui == 'divx':
            ContentType = 'video/x-msvideo'
        elif houzhui == 'djv':
            ContentType = 'image/vnd.djvu'
        elif houzhui == 'djvu':
            ContentType = 'image/vnd.djvu'
        elif houzhui == 'dng':
            ContentType = 'image/x-adobe-dng'
        elif houzhui == 'doc':
            ContentType = 'application/msword'
        elif houzhui == 'docbook':
            ContentType = 'application/docbook+xml'
        elif houzhui == 'docm':
            ContentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        elif houzhui == 'docx':
            ContentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        elif houzhui == 'dot':
            ContentType = 'text/vnd.graphviz'
        elif houzhui == 'dsl':
            ContentType = 'text/x-dsl'
        elif houzhui == 'dtd':
            ContentType = 'application/xml-dtd'
        elif houzhui == 'dtx':
            ContentType = 'text/x-tex'
        elif houzhui == 'dv':
            ContentType = 'video/dv'
        elif houzhui == 'dvi':
            ContentType = 'application/x-dvi'
        elif houzhui == 'dvi.bz2':
            ContentType = 'application/x-bzdvi'
        elif houzhui == 'dvi.gz':
            ContentType = 'application/x-gzdvi'
        elif houzhui == 'dwg':
            ContentType = 'image/vnd.dwg'
        elif houzhui == 'dxf':
            ContentType = 'image/vnd.dxf'
        elif houzhui == 'e':
            ContentType = 'text/x-eiffel'
        elif houzhui == 'egon':
            ContentType = 'application/x-egon'
        elif houzhui == 'eif':
            ContentType = 'text/x-eiffel'
        elif houzhui == 'el':
            ContentType = 'text/x-emacs-lisp'
        elif houzhui == 'emf':
            ContentType = 'image/x-emf'
        elif houzhui == 'emp':
            ContentType = 'application/vnd.emusic-emusic_package'
        elif houzhui == 'ent':
            ContentType = 'application/xml-external-parsed-entity'
        elif houzhui == 'eps':
            ContentType = 'image/x-eps'
        elif houzhui == 'eps.bz2':
            ContentType = 'image/x-bzeps'
        elif houzhui == 'eps.gz':
            ContentType = 'image/x-gzeps'
        elif houzhui == 'epsf':
            ContentType = 'image/x-eps'
        elif houzhui == 'epsf.bz2':
            ContentType = 'image/x-bzeps'
        elif houzhui == 'epsf.gz':
            ContentType = 'image/x-gzeps'
        elif houzhui == 'epsi':
            ContentType = 'image/x-eps'
        elif houzhui == 'epsi.bz2':
            ContentType = 'image/x-bzeps'
        elif houzhui == 'epsi.gz':
            ContentType = 'image/x-gzeps'
        elif houzhui == 'epub':
            ContentType = 'application/epub+zip'
        elif houzhui == 'erl':
            ContentType = 'text/x-erlang'
        elif houzhui == 'es':
            ContentType = 'application/ecmascript'
        elif houzhui == 'etheme':
            ContentType = 'application/x-e-theme'
        elif houzhui == 'etx':
            ContentType = 'text/x-setext'
        elif houzhui == 'exe':
            ContentType = 'application/x-ms-dos-executable'
        elif houzhui == 'exr':
            ContentType = 'image/x-exr'
        elif houzhui == 'ez':
            ContentType = 'application/andrew-inset'
        elif houzhui == 'f':
            ContentType = 'text/x-fortran'
        elif houzhui == 'f90':
            ContentType = 'text/x-fortran'
        elif houzhui == 'f95':
            ContentType = 'text/x-fortran'
        elif houzhui == 'fb2':
            ContentType = 'application/x-fictionbook+xml'
        elif houzhui == 'fig':
            ContentType = 'image/x-xfig'
        elif houzhui == 'fits':
            ContentType = 'image/fits'
        elif houzhui == 'fl':
            ContentType = 'application/x-fluid'
        elif houzhui == 'flac':
            ContentType = 'audio/x-flac'
        elif houzhui == 'flc':
            ContentType = 'video/x-flic'
        elif houzhui == 'fli':
            ContentType = 'video/x-flic'
        elif houzhui == 'flv':
            ContentType = 'video/x-flv'
        elif houzhui == 'flw':
            ContentType = 'application/x-kivio'
        elif houzhui == 'fo':
            ContentType = 'text/x-xslfo'
        elif houzhui == 'for':
            ContentType = 'text/x-fortran'
        elif houzhui == 'g3':
            ContentType = 'image/fax-g3'
        elif houzhui == 'gb':
            ContentType = 'application/x-gameboy-rom'
        elif houzhui == 'gba':
            ContentType = 'application/x-gba-rom'
        elif houzhui == 'gcrd':
            ContentType = 'text/directory'
        elif houzhui == 'ged':
            ContentType = 'application/x-gedcom'
        elif houzhui == 'gedcom':
            ContentType = 'application/x-gedcom'
        elif houzhui == 'gen':
            ContentType = 'application/x-genesis-rom'
        elif houzhui == 'gf':
            ContentType = 'application/x-tex-gf'
        elif houzhui == 'gg':
            ContentType = 'application/x-sms-rom'
        elif houzhui == 'gif':
            ContentType = 'image/gif'
        elif houzhui == 'glade':
            ContentType = 'application/x-glade'
        elif houzhui == 'gmo':
            ContentType = 'application/x-gettext-translation'
        elif houzhui == 'gnc':
            ContentType = 'application/x-gnucash'
        elif houzhui == 'gnd':
            ContentType = 'application/gnunet-directory'
        elif houzhui == 'gnucash':
            ContentType = 'application/x-gnucash'
        elif houzhui == 'gnumeric':
            ContentType = 'application/x-gnumeric'
        elif houzhui == 'gnuplot':
            ContentType = 'application/x-gnuplot'
        elif houzhui == 'gp':
            ContentType = 'application/x-gnuplot'
        elif houzhui == 'gpg':
            ContentType = 'application/pgp-encrypted'
        elif houzhui == 'gplt':
            ContentType = 'application/x-gnuplot'
        elif houzhui == 'gra':
            ContentType = 'application/x-graphite'
        elif houzhui == 'gsf':
            ContentType = 'application/x-font-type1'
        elif houzhui == 'gsm':
            ContentType = 'audio/x-gsm'
        elif houzhui == 'gtar':
            ContentType = 'application/x-tar'
        elif houzhui == 'gv':
            ContentType = 'text/vnd.graphviz'
        elif houzhui == 'gvp':
            ContentType = 'text/x-google-video-pointer'
        elif houzhui == 'gz':
            ContentType = 'application/x-gzip'
        elif houzhui == 'h':
            ContentType = 'text/x-chdr'
        elif houzhui == 'h++':
            ContentType = 'text/x-c++hdr'
        elif houzhui == 'hdf':
            ContentType = 'application/x-hdf'
        elif houzhui == 'hh':
            ContentType = 'text/x-c++hdr'
        elif houzhui == 'hp':
            ContentType = 'text/x-c++hdr'
        elif houzhui == 'hpgl':
            ContentType = 'application/vnd.hp-hpgl'
        elif houzhui == 'hpp':
            ContentType = 'text/x-c++hdr'
        elif houzhui == 'hs':
            ContentType = 'text/x-haskell'
        elif houzhui == 'htm':
            ContentType = 'text/html'
        elif houzhui == 'html':
            ContentType = 'text/html'
        elif houzhui == 'hwp':
            ContentType = 'application/x-hwp'
        elif houzhui == 'hwt':
            ContentType = 'application/x-hwt'
        elif houzhui == 'hxx':
            ContentType = 'text/x-c++hdr'
        elif houzhui == 'ica':
            ContentType = 'application/x-ica'
        elif houzhui == 'icb':
            ContentType = 'image/x-tga'
        elif houzhui == 'icns':
            ContentType = 'image/x-icns'
        elif houzhui == 'ico':
            ContentType = 'image/vnd.microsoft.icon'
        elif houzhui == 'ics':
            ContentType = 'text/calendar'
        elif houzhui == 'idl':
            ContentType = 'text/x-idl'
        elif houzhui == 'ief':
            ContentType = 'image/ief'
        elif houzhui == 'iff':
            ContentType = 'image/x-iff'
        elif houzhui == 'ilbm':
            ContentType = 'image/x-ilbm'
        elif houzhui == 'ime':
            ContentType = 'text/x-imelody'
        elif houzhui == 'imy':
            ContentType = 'text/x-imelody'
        elif houzhui == 'ins':
            ContentType = 'text/x-tex'
        elif houzhui == 'iptables':
            ContentType = 'text/x-iptables'
        elif houzhui == 'iso':
            ContentType = 'application/x-cd-image'
        elif houzhui == 'iso9660':
            ContentType = 'application/x-cd-image'
        elif houzhui == 'it':
            ContentType = 'audio/x-it'
        elif houzhui == 'j2k':
            ContentType = 'image/jp2'
        elif houzhui == 'jad':
            ContentType = 'text/vnd.sun.j2me.app-descriptor'
        elif houzhui == 'jar':
            ContentType = 'application/x-java-archive'
        elif houzhui == 'java':
            ContentType = 'text/x-java'
        elif houzhui == 'jng':
            ContentType = 'image/x-jng'
        elif houzhui == 'jnlp':
            ContentType = 'application/x-java-jnlp-file'
        elif houzhui == 'jp2':
            ContentType = 'image/jp2'
        elif houzhui == 'jpc':
            ContentType = 'image/jp2'
        elif houzhui == 'jpe':
            ContentType = 'image/jpeg'
        elif houzhui == 'jpeg':
            ContentType = 'image/jpeg'
        elif houzhui == 'jpf':
            ContentType = 'image/jp2'
        elif houzhui == 'jpg':
            ContentType = 'image/jpeg'
        elif houzhui == 'jpr':
            ContentType = 'application/x-jbuilder-project'
        elif houzhui == 'jpx':
            ContentType = 'image/jp2'
        elif houzhui == 'js':
            ContentType = 'application/javascript'
        elif houzhui == 'json':
            ContentType = 'application/json'
        elif houzhui == 'jsonp':
            ContentType = 'application/jsonp'
        elif houzhui == 'k25':
            ContentType = 'image/x-kodak-k25'
        elif houzhui == 'kar':
            ContentType = 'audio/midi'
        elif houzhui == 'karbon':
            ContentType = 'application/x-karbon'
        elif houzhui == 'kdc':
            ContentType = 'image/x-kodak-kdc'
        elif houzhui == 'kdelnk':
            ContentType = 'application/x-desktop'
        elif houzhui == 'kexi':
            ContentType = 'application/x-kexiproject-sqlite3'
        elif houzhui == 'kexic':
            ContentType = 'application/x-kexi-connectiondata'
        elif houzhui == 'kexis':
            ContentType = 'application/x-kexiproject-shortcut'
        elif houzhui == 'kfo':
            ContentType = 'application/x-kformula'
        elif houzhui == 'kil':
            ContentType = 'application/x-killustrator'
        elif houzhui == 'kino':
            ContentType = 'application/smil'
        elif houzhui == 'kml':
            ContentType = 'application/vnd.google-earth.kml+xml'
        elif houzhui == 'kmz':
            ContentType = 'application/vnd.google-earth.kmz'
        elif houzhui == 'kon':
            ContentType = 'application/x-kontour'
        elif houzhui == 'kpm':
            ContentType = 'application/x-kpovmodeler'
        elif houzhui == 'kpr':
            ContentType = 'application/x-kpresenter'
        elif houzhui == 'kpt':
            ContentType = 'application/x-kpresenter'
        elif houzhui == 'kra':
            ContentType = 'application/x-krita'
        elif houzhui == 'ksp':
            ContentType = 'application/x-kspread'
        elif houzhui == 'kud':
            ContentType = 'application/x-kugar'
        elif houzhui == 'kwd':
            ContentType = 'application/x-kword'
        elif houzhui == 'kwt':
            ContentType = 'application/x-kword'
        elif houzhui == 'la':
            ContentType = 'application/x-shared-library-la'
        elif houzhui == 'latex':
            ContentType = 'text/x-tex'
        elif houzhui == 'ldif':
            ContentType = 'text/x-ldif'
        elif houzhui == 'lha':
            ContentType = 'application/x-lha'
        elif houzhui == 'lhs':
            ContentType = 'text/x-literate-haskell'
        elif houzhui == 'lhz':
            ContentType = 'application/x-lhz'
        elif houzhui == 'log':
            ContentType = 'text/x-log'
        elif houzhui == 'ltx':
            ContentType = 'text/x-tex'
        elif houzhui == 'lua':
            ContentType = 'text/x-lua'
        elif houzhui == 'lwo':
            ContentType = 'image/x-lwo'
        elif houzhui == 'lwob':
            ContentType = 'image/x-lwo'
        elif houzhui == 'lws':
            ContentType = 'image/x-lws'
        elif houzhui == 'ly':
            ContentType = 'text/x-lilypond'
        elif houzhui == 'lyx':
            ContentType = 'application/x-lyx'
        elif houzhui == 'lz':
            ContentType = 'application/x-lzip'
        elif houzhui == 'lzh':
            ContentType = 'application/x-lha'
        elif houzhui == 'lzma':
            ContentType = 'application/x-lzma'
        elif houzhui == 'lzo':
            ContentType = 'application/x-lzop'
        elif houzhui == 'm':
            ContentType = 'text/x-matlab'
        elif houzhui == 'm15':
            ContentType = 'audio/x-mod'
        elif houzhui == 'm2t':
            ContentType = 'video/mpeg'
        elif houzhui == 'm3u':
            ContentType = 'audio/x-mpegurl'
        elif houzhui == 'm3u8':
            ContentType = 'audio/x-mpegurl'
        elif houzhui == 'm4':
            ContentType = 'application/x-m4'
        elif houzhui == 'm4a':
            ContentType = 'audio/mp4'
        elif houzhui == 'm4b':
            ContentType = 'audio/x-m4b'
        elif houzhui == 'm4v':
            ContentType = 'video/mp4'
        elif houzhui == 'mab':
            ContentType = 'application/x-markaby'
        elif houzhui == 'man':
            ContentType = 'application/x-troff-man'
        elif houzhui == 'mbox':
            ContentType = 'application/mbox'
        elif houzhui == 'md':
            ContentType = 'application/x-genesis-rom'
        elif houzhui == 'mdb':
            ContentType = 'application/vnd.ms-access'
        elif houzhui == 'mdi':
            ContentType = 'image/vnd.ms-modi'
        elif houzhui == 'me':
            ContentType = 'text/x-troff-me'
        elif houzhui == 'med':
            ContentType = 'audio/x-mod'
        elif houzhui == 'metalink':
            ContentType = 'application/metalink+xml'
        elif houzhui == 'mgp':
            ContentType = 'application/x-magicpoint'
        elif houzhui == 'mid':
            ContentType = 'audio/midi'
        elif houzhui == 'midi':
            ContentType = 'audio/midi'
        elif houzhui == 'mif':
            ContentType = 'application/x-mif'
        elif houzhui == 'minipsf':
            ContentType = 'audio/x-minipsf'
        elif houzhui == 'mka':
            ContentType = 'audio/x-matroska'
        elif houzhui == 'mkv':
            ContentType = 'video/x-matroska'
        elif houzhui == 'ml':
            ContentType = 'text/x-ocaml'
        elif houzhui == 'mli':
            ContentType = 'text/x-ocaml'
        elif houzhui == 'mm':
            ContentType = 'text/x-troff-mm'
        elif houzhui == 'mmf':
            ContentType = 'application/x-smaf'
        elif houzhui == 'mml':
            ContentType = 'text/mathml'
        elif houzhui == 'mng':
            ContentType = 'video/x-mng'
        elif houzhui == 'mo':
            ContentType = 'application/x-gettext-translation'
        elif houzhui == 'mo3':
            ContentType = 'audio/x-mo3'
        elif houzhui == 'moc':
            ContentType = 'text/x-moc'
        elif houzhui == 'mod':
            ContentType = 'audio/x-mod'
        elif houzhui == 'mof':
            ContentType = 'text/x-mof'
        elif houzhui == 'moov':
            ContentType = 'video/quicktime'
        elif houzhui == 'mov':
            ContentType = 'video/quicktime'
        elif houzhui == 'movie':
            ContentType = 'video/x-sgi-movie'
        elif houzhui == 'mp+':
            ContentType = 'audio/x-musepack'
        elif houzhui == 'mp2':
            ContentType = 'video/mpeg'
        elif houzhui == 'mp3':
            ContentType = 'audio/mpeg'
        elif houzhui == 'mp4':
            ContentType = 'video/mp4'
        elif houzhui == 'mpc':
            ContentType = 'audio/x-musepack'
        elif houzhui == 'mpe':
            ContentType = 'video/mpeg'
        elif houzhui == 'mpeg':
            ContentType = 'video/mpeg'
        elif houzhui == 'mpg':
            ContentType = 'video/mpeg'
        elif houzhui == 'mpga':
            ContentType = 'audio/mpeg'
        elif houzhui == 'mpp':
            ContentType = 'audio/x-musepack'
        elif houzhui == 'mrl':
            ContentType = 'text/x-mrml'
        elif houzhui == 'mrml':
            ContentType = 'text/x-mrml'
        elif houzhui == 'mrw':
            ContentType = 'image/x-minolta-mrw'
        elif houzhui == 'ms':
            ContentType = 'text/x-troff-ms'
        elif houzhui == 'msi':
            ContentType = 'application/x-msi'
        elif houzhui == 'msod':
            ContentType = 'image/x-msod'
        elif houzhui == 'msx':
            ContentType = 'application/x-msx-rom'
        elif houzhui == 'mtm':
            ContentType = 'audio/x-mod'
        elif houzhui == 'mup':
            ContentType = 'text/x-mup'
        elif houzhui == 'mxf':
            ContentType = 'application/mxf'
        elif houzhui == 'n64':
            ContentType = 'application/x-n64-rom'
        elif houzhui == 'nb':
            ContentType = 'application/mathematica'
        elif houzhui == 'nc':
            ContentType = 'application/x-netcdf'
        elif houzhui == 'nds':
            ContentType = 'application/x-nintendo-ds-rom'
        elif houzhui == 'nef':
            ContentType = 'image/x-nikon-nef'
        elif houzhui == 'nes':
            ContentType = 'application/x-nes-rom'
        elif houzhui == 'nfo':
            ContentType = 'text/x-nfo'
        elif houzhui == 'not':
            ContentType = 'text/x-mup'
        elif houzhui == 'nsc':
            ContentType = 'application/x-netshow-channel'
        elif houzhui == 'nsv':
            ContentType = 'video/x-nsv'
        elif houzhui == 'o':
            ContentType = 'application/x-object'
        elif houzhui == 'obj':
            ContentType = 'application/x-tgif'
        elif houzhui == 'ocl':
            ContentType = 'text/x-ocl'
        elif houzhui == 'oda':
            ContentType = 'application/oda'
        elif houzhui == 'odb':
            ContentType = 'application/vnd.oasis.opendocument.database'
        elif houzhui == 'odc':
            ContentType = 'application/vnd.oasis.opendocument.chart'
        elif houzhui == 'odf':
            ContentType = 'application/vnd.oasis.opendocument.formula'
        elif houzhui == 'odg':
            ContentType = 'application/vnd.oasis.opendocument.graphics'
        elif houzhui == 'odi':
            ContentType = 'application/vnd.oasis.opendocument.image'
        elif houzhui == 'odm':
            ContentType = 'application/vnd.oasis.opendocument.text-master'
        elif houzhui == 'odp':
            ContentType = 'application/vnd.oasis.opendocument.presentation'
        elif houzhui == 'ods':
            ContentType = 'application/vnd.oasis.opendocument.spreadsheet'
        elif houzhui == 'odt':
            ContentType = 'application/vnd.oasis.opendocument.text'
        elif houzhui == 'oga':
            ContentType = 'audio/ogg'
        elif houzhui == 'ogg':
            ContentType = 'video/x-theora+ogg'
        elif houzhui == 'ogm':
            ContentType = 'video/x-ogm+ogg'
        elif houzhui == 'ogv':
            ContentType = 'video/ogg'
        elif houzhui == 'ogx':
            ContentType = 'application/ogg'
        elif houzhui == 'old':
            ContentType = 'application/x-trash'
        elif houzhui == 'oleo':
            ContentType = 'application/x-oleo'
        elif houzhui == 'opml':
            ContentType = 'text/x-opml+xml'
        elif houzhui == 'ora':
            ContentType = 'image/openraster'
        elif houzhui == 'orf':
            ContentType = 'image/x-olympus-orf'
        elif houzhui == 'otc':
            ContentType = 'application/vnd.oasis.opendocument.chart-template'
        elif houzhui == 'otf':
            ContentType = 'application/x-font-otf'
        elif houzhui == 'otg':
            ContentType = 'application/vnd.oasis.opendocument.graphics-template'
        elif houzhui == 'oth':
            ContentType = 'application/vnd.oasis.opendocument.text-web'
        elif houzhui == 'otp':
            ContentType = 'application/vnd.oasis.opendocument.presentation-template'
        elif houzhui == 'ots':
            ContentType = 'application/vnd.oasis.opendocument.spreadsheet-template'
        elif houzhui == 'ott':
            ContentType = 'application/vnd.oasis.opendocument.text-template'
        elif houzhui == 'owl':
            ContentType = 'application/rdf+xml'
        elif houzhui == 'oxt':
            ContentType = 'application/vnd.openofficeorg.extension'
        elif houzhui == 'p':
            ContentType = 'text/x-pascal'
        elif houzhui == 'p10':
            ContentType = 'application/pkcs10'
        elif houzhui == 'p12':
            ContentType = 'application/x-pkcs12'
        elif houzhui == 'p7b':
            ContentType = 'application/x-pkcs7-certificates'
        elif houzhui == 'p7s':
            ContentType = 'application/pkcs7-signature'
        elif houzhui == 'pack':
            ContentType = 'application/x-java-pack200'
        elif houzhui == 'pak':
            ContentType = 'application/x-pak'
        elif houzhui == 'par2':
            ContentType = 'application/x-par2'
        elif houzhui == 'pas':
            ContentType = 'text/x-pascal'
        elif houzhui == 'patch':
            ContentType = 'text/x-patch'
        elif houzhui == 'pbm':
            ContentType = 'image/x-portable-bitmap'
        elif houzhui == 'pcd':
            ContentType = 'image/x-photo-cd'
        elif houzhui == 'pcf':
            ContentType = 'application/x-cisco-vpn-settings'
        elif houzhui == 'pcf.gz':
            ContentType = 'application/x-font-pcf'
        elif houzhui == 'pcf.z':
            ContentType = 'application/x-font-pcf'
        elif houzhui == 'pcl':
            ContentType = 'application/vnd.hp-pcl'
        elif houzhui == 'pcx':
            ContentType = 'image/x-pcx'
        elif houzhui == 'pdb':
            ContentType = 'chemical/x-pdb'
        elif houzhui == 'pdc':
            ContentType = 'application/x-aportisdoc'
        elif houzhui == 'pdf':
            ContentType = 'application/pdf'
        elif houzhui == 'pdf.bz2':
            ContentType = 'application/x-bzpdf'
        elif houzhui == 'pdf.gz':
            ContentType = 'application/x-gzpdf'
        elif houzhui == 'pef':
            ContentType = 'image/x-pentax-pef'
        elif houzhui == 'pem':
            ContentType = 'application/x-x509-ca-cert'
        elif houzhui == 'perl':
            ContentType = 'application/x-perl'
        elif houzhui == 'pfa':
            ContentType = 'application/x-font-type1'
        elif houzhui == 'pfb':
            ContentType = 'application/x-font-type1'
        elif houzhui == 'pfx':
            ContentType = 'application/x-pkcs12'
        elif houzhui == 'pgm':
            ContentType = 'image/x-portable-graymap'
        elif houzhui == 'pgn':
            ContentType = 'application/x-chess-pgn'
        elif houzhui == 'pgp':
            ContentType = 'application/pgp-encrypted'
        elif houzhui == 'php':
            ContentType = 'application/x-php'
        elif houzhui == 'php3':
            ContentType = 'application/x-php'
        elif houzhui == 'php4':
            ContentType = 'application/x-php'
        elif houzhui == 'pict':
            ContentType = 'image/x-pict'
        elif houzhui == 'pict1':
            ContentType = 'image/x-pict'
        elif houzhui == 'pict2':
            ContentType = 'image/x-pict'
        elif houzhui == 'pickle':
            ContentType = 'application/python-pickle'
        elif houzhui == 'pk':
            ContentType = 'application/x-tex-pk'
        elif houzhui == 'pkipath':
            ContentType = 'application/pkix-pkipath'
        elif houzhui == 'pkr':
            ContentType = 'application/pgp-keys'
        elif houzhui == 'pl':
            ContentType = 'application/x-perl'
        elif houzhui == 'pla':
            ContentType = 'audio/x-iriver-pla'
        elif houzhui == 'pln':
            ContentType = 'application/x-planperfect'
        elif houzhui == 'pls':
            ContentType = 'audio/x-scpls'
        elif houzhui == 'pm':
            ContentType = 'application/x-perl'
        elif houzhui == 'png':
            ContentType = 'image/png'
        elif houzhui == 'pnm':
            ContentType = 'image/x-portable-anymap'
        elif houzhui == 'pntg':
            ContentType = 'image/x-macpaint'
        elif houzhui == 'po':
            ContentType = 'text/x-gettext-translation'
        elif houzhui == 'por':
            ContentType = 'application/x-spss-por'
        elif houzhui == 'pot':
            ContentType = 'text/x-gettext-translation-template'
        elif houzhui == 'ppm':
            ContentType = 'image/x-portable-pixmap'
        elif houzhui == 'pps':
            ContentType = 'application/vnd.ms-powerpoint'
        elif houzhui == 'ppt':
            ContentType = 'application/vnd.ms-powerpoint'
        elif houzhui == 'pptm':
            ContentType = 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        elif houzhui == 'pptx':
            ContentType = 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        elif houzhui == 'ppz':
            ContentType = 'application/vnd.ms-powerpoint'
        elif houzhui == 'prc':
            ContentType = 'application/x-palm-database'
        elif houzhui == 'ps':
            ContentType = 'application/postscript'
        elif houzhui == 'ps.bz2':
            ContentType = 'application/x-bzpostscript'
        elif houzhui == 'ps.gz':
            ContentType = 'application/x-gzpostscript'
        elif houzhui == 'psd':
            ContentType = 'image/vnd.adobe.photoshop'
        elif houzhui == 'psf':
            ContentType = 'audio/x-psf'
        elif houzhui == 'psf.gz':
            ContentType = 'application/x-gz-font-linux-psf'
        elif houzhui == 'psflib':
            ContentType = 'audio/x-psflib'
        elif houzhui == 'psid':
            ContentType = 'audio/prs.sid'
        elif houzhui == 'psw':
            ContentType = 'application/x-pocket-word'
        elif houzhui == 'pw':
            ContentType = 'application/x-pw'
        elif houzhui == 'py':
            ContentType = 'text/x-python'
        elif houzhui == 'pyc':
            ContentType = 'application/x-python-bytecode'
        elif houzhui == 'pyo':
            ContentType = 'application/x-python-bytecode'
        elif houzhui == 'qif':
            ContentType = 'image/x-quicktime'
        elif houzhui == 'qt':
            ContentType = 'video/quicktime'
        elif houzhui == 'qtif':
            ContentType = 'image/x-quicktime'
        elif houzhui == 'qtl':
            ContentType = 'application/x-quicktime-media-link'
        elif houzhui == 'qtvr':
            ContentType = 'video/quicktime'
        elif houzhui == 'ra':
            ContentType = 'audio/vnd.rn-realaudio'
        elif houzhui == 'raf':
            ContentType = 'image/x-fuji-raf'
        elif houzhui == 'ram':
            ContentType = 'application/ram'
        elif houzhui == 'rar':
            ContentType = 'application/x-rar'
        elif houzhui == 'ras':
            ContentType = 'image/x-cmu-raster'
        elif houzhui == 'raw':
            ContentType = 'image/x-panasonic-raw'
        elif houzhui == 'rax':
            ContentType = 'audio/vnd.rn-realaudio'
        elif houzhui == 'rb':
            ContentType = 'application/x-ruby'
        elif houzhui == 'rdf':
            ContentType = 'application/rdf+xml'
        elif houzhui == 'rdfs':
            ContentType = 'application/rdf+xml'
        elif houzhui == 'reg':
            ContentType = 'text/x-ms-regedit'
        elif houzhui == 'rej':
            ContentType = 'application/x-reject'
        elif houzhui == 'rgb':
            ContentType = 'image/x-rgb'
        elif houzhui == 'rle':
            ContentType = 'image/rle'
        elif houzhui == 'rm':
            ContentType = 'application/vnd.rn-realmedia'
        elif houzhui == 'rmj':
            ContentType = 'application/vnd.rn-realmedia'
        elif houzhui == 'rmm':
            ContentType = 'application/vnd.rn-realmedia'
        elif houzhui == 'rms':
            ContentType = 'application/vnd.rn-realmedia'
        elif houzhui == 'rmvb':
            ContentType = 'application/vnd.rn-realmedia'
        elif houzhui == 'rmx':
            ContentType = 'application/vnd.rn-realmedia'
        elif houzhui == 'roff':
            ContentType = 'text/troff'
        elif houzhui == 'rp':
            ContentType = 'image/vnd.rn-realpix'
        elif houzhui == 'rpm':
            ContentType = 'application/x-rpm'
        elif houzhui == 'rss':
            ContentType = 'application/rss+xml'
        elif houzhui == 'rt':
            ContentType = 'text/vnd.rn-realtext'
        elif houzhui == 'rtf':
            ContentType = 'application/rtf'
        elif houzhui == 'rtx':
            ContentType = 'text/richtext'
        elif houzhui == 'rv':
            ContentType = 'video/vnd.rn-realvideo'
        elif houzhui == 'rvx':
            ContentType = 'video/vnd.rn-realvideo'
        elif houzhui == 's3m':
            ContentType = 'audio/x-s3m'
        elif houzhui == 'sam':
            ContentType = 'application/x-amipro'
        elif houzhui == 'sami':
            ContentType = 'application/x-sami'
        elif houzhui == 'sav':
            ContentType = 'application/x-spss-sav'
        elif houzhui == 'scm':
            ContentType = 'text/x-scheme'
        elif houzhui == 'sda':
            ContentType = 'application/vnd.stardivision.draw'
        elif houzhui == 'sdc':
            ContentType = 'application/vnd.stardivision.calc'
        elif houzhui == 'sdd':
            ContentType = 'application/vnd.stardivision.impress'
        elif houzhui == 'sdp':
            ContentType = 'application/sdp'
        elif houzhui == 'sds':
            ContentType = 'application/vnd.stardivision.chart'
        elif houzhui == 'sdw':
            ContentType = 'application/vnd.stardivision.writer'
        elif houzhui == 'sgf':
            ContentType = 'application/x-go-sgf'
        elif houzhui == 'sgi':
            ContentType = 'image/x-sgi'
        elif houzhui == 'sgl':
            ContentType = 'application/vnd.stardivision.writer'
        elif houzhui == 'sgm':
            ContentType = 'text/sgml'
        elif houzhui == 'sgml':
            ContentType = 'text/sgml'
        elif houzhui == 'sh':
            ContentType = 'application/x-shellscript'
        elif houzhui == 'shar':
            ContentType = 'application/x-shar'
        elif houzhui == 'shn':
            ContentType = 'application/x-shorten'
        elif houzhui == 'siag':
            ContentType = 'application/x-siag'
        elif houzhui == 'sid':
            ContentType = 'audio/prs.sid'
        elif houzhui == 'sik':
            ContentType = 'application/x-trash'
        elif houzhui == 'sis':
            ContentType = 'application/vnd.symbian.install'
        elif houzhui == 'sisx':
            ContentType = 'x-epoc/x-sisx-app'
        elif houzhui == 'sit':
            ContentType = 'application/x-stuffit'
        elif houzhui == 'siv':
            ContentType = 'application/sieve'
        elif houzhui == 'sk':
            ContentType = 'image/x-skencil'
        elif houzhui == 'sk1':
            ContentType = 'image/x-skencil'
        elif houzhui == 'skr':
            ContentType = 'application/pgp-keys'
        elif houzhui == 'slk':
            ContentType = 'text/spreadsheet'
        elif houzhui == 'smaf':
            ContentType = 'application/x-smaf'
        elif houzhui == 'smc':
            ContentType = 'application/x-snes-rom'
        elif houzhui == 'smd':
            ContentType = 'application/vnd.stardivision.mail'
        elif houzhui == 'smf':
            ContentType = 'application/vnd.stardivision.math'
        elif houzhui == 'smi':
            ContentType = 'application/x-sami'
        elif houzhui == 'smil':
            ContentType = 'application/smil'
        elif houzhui == 'sml':
            ContentType = 'application/smil'
        elif houzhui == 'sms':
            ContentType = 'application/x-sms-rom'
        elif houzhui == 'snd':
            ContentType = 'audio/basic'
        elif houzhui == 'so':
            ContentType = 'application/x-sharedlib'
        elif houzhui == 'spc':
            ContentType = 'application/x-pkcs7-certificates'
        elif houzhui == 'spd':
            ContentType = 'application/x-font-speedo'
        elif houzhui == 'spec':
            ContentType = 'text/x-rpm-spec'
        elif houzhui == 'spl':
            ContentType = 'application/x-shockwave-flash'
        elif houzhui == 'spx':
            ContentType = 'audio/x-speex'
        elif houzhui == 'sql':
            ContentType = 'text/x-sql'
        elif houzhui == 'sr2':
            ContentType = 'image/x-sony-sr2'
        elif houzhui == 'src':
            ContentType = 'application/x-wais-source'
        elif houzhui == 'srf':
            ContentType = 'image/x-sony-srf'
        elif houzhui == 'srt':
            ContentType = 'application/x-subrip'
        elif houzhui == 'ssa':
            ContentType = 'text/x-ssa'
        elif houzhui == 'stc':
            ContentType = 'application/vnd.sun.xml.calc.template'
        elif houzhui == 'std':
            ContentType = 'application/vnd.sun.xml.draw.template'
        elif houzhui == 'sti':
            ContentType = 'application/vnd.sun.xml.impress.template'
        elif houzhui == 'stm':
            ContentType = 'audio/x-stm'
        elif houzhui == 'stw':
            ContentType = 'application/vnd.sun.xml.writer.template'
        elif houzhui == 'sty':
            ContentType = 'text/x-tex'
        elif houzhui == 'sub':
            ContentType = 'text/x-subviewer'
        elif houzhui == 'sun':
            ContentType = 'image/x-sun-raster'
        elif houzhui == 'sv4cpio':
            ContentType = 'application/x-sv4cpio'
        elif houzhui == 'sv4crc':
            ContentType = 'application/x-sv4crc'
        elif houzhui == 'svg':
            ContentType = 'image/svg+xml'
        elif houzhui == 'svgz':
            ContentType = 'image/svg+xml-compressed'
        elif houzhui == 'swf':
            ContentType = 'application/x-shockwave-flash'
        elif houzhui == 'sxc':
            ContentType = 'application/vnd.sun.xml.calc'
        elif houzhui == 'sxd':
            ContentType = 'application/vnd.sun.xml.draw'
        elif houzhui == 'sxg':
            ContentType = 'application/vnd.sun.xml.writer.global'
        elif houzhui == 'sxi':
            ContentType = 'application/vnd.sun.xml.impress'
        elif houzhui == 'sxm':
            ContentType = 'application/vnd.sun.xml.math'
        elif houzhui == 'sxw':
            ContentType = 'application/vnd.sun.xml.writer'
        elif houzhui == 'sylk':
            ContentType = 'text/spreadsheet'
        elif houzhui == 't':
            ContentType = 'text/troff'
        elif houzhui == 't2t':
            ContentType = 'text/x-txt2tags'
        elif houzhui == 'tar':
            ContentType = 'application/x-tar'
        elif houzhui == 'tar.bz':
            ContentType = 'application/x-bzip-compressed-tar'
        elif houzhui == 'tar.bz2':
            ContentType = 'application/x-bzip-compressed-tar'
        elif houzhui == 'tar.gz':
            ContentType = 'application/x-compressed-tar'
        elif houzhui == 'tar.lzma':
            ContentType = 'application/x-lzma-compressed-tar'
        elif houzhui == 'tar.lzo':
            ContentType = 'application/x-tzo'
        elif houzhui == 'tar.xz':
            ContentType = 'application/x-xz-compressed-tar'
        elif houzhui == 'tar.z':
            ContentType = 'application/x-tarz'
        elif houzhui == 'tbz':
            ContentType = 'application/x-bzip-compressed-tar'
        elif houzhui == 'tbz2':
            ContentType = 'application/x-bzip-compressed-tar'
        elif houzhui == 'tcl':
            ContentType = 'text/x-tcl'
        elif houzhui == 'tex':
            ContentType = 'text/x-tex'
        elif houzhui == 'texi':
            ContentType = 'text/x-texinfo'
        elif houzhui == 'texinfo':
            ContentType = 'text/x-texinfo'
        elif houzhui == 'tga':
            ContentType = 'image/x-tga'
        elif houzhui == 'tgz':
            ContentType = 'application/x-compressed-tar'
        elif houzhui == 'theme':
            ContentType = 'application/x-theme'
        elif houzhui == 'themepack':
            ContentType = 'application/x-windows-themepack'
        elif houzhui == 'tif':
            ContentType = 'image/tiff'
        elif houzhui == 'tiff':
            ContentType = 'image/tiff'
        elif houzhui == 'tk':
            ContentType = 'text/x-tcl'
        elif houzhui == 'tlz':
            ContentType = 'application/x-lzma-compressed-tar'
        elif houzhui == 'tnef':
            ContentType = 'application/vnd.ms-tnef'
        elif houzhui == 'tnf':
            ContentType = 'application/vnd.ms-tnef'
        elif houzhui == 'toc':
            ContentType = 'application/x-cdrdao-toc'
        elif houzhui == 'torrent':
            ContentType = 'application/x-bittorrent'
        elif houzhui == 'tpic':
            ContentType = 'image/x-tga'
        elif houzhui == 'tr':
            ContentType = 'text/troff'
        elif houzhui == 'ts':
            ContentType = 'application/x-linguist'
        elif houzhui == 'tsv':
            ContentType = 'text/tab-separated-values'
        elif houzhui == 'tta':
            ContentType = 'audio/x-tta'
        elif houzhui == 'ttc':
            ContentType = 'application/x-font-ttf'
        elif houzhui == 'ttf':
            ContentType = 'application/x-font-ttf'
        elif houzhui == 'ttx':
            ContentType = 'application/x-font-ttx'
        elif houzhui == 'txt':
            ContentType = 'text/plain'
        elif houzhui == 'txz':
            ContentType = 'application/x-xz-compressed-tar'
        elif houzhui == 'tzo':
            ContentType = 'application/x-tzo'
        elif houzhui == 'ufraw':
            ContentType = 'application/x-ufraw'
        elif houzhui == 'ui':
            ContentType = 'application/x-designer'
        elif houzhui == 'uil':
            ContentType = 'text/x-uil'
        elif houzhui == 'ult':
            ContentType = 'audio/x-mod'
        elif houzhui == 'uni':
            ContentType = 'audio/x-mod'
        elif houzhui == 'uri':
            ContentType = 'text/x-uri'
        elif houzhui == 'url':
            ContentType = 'text/x-uri'
        elif houzhui == 'ustar':
            ContentType = 'application/x-ustar'
        elif houzhui == 'vala':
            ContentType = 'text/x-vala'
        elif houzhui == 'vapi':
            ContentType = 'text/x-vala'
        elif houzhui == 'vcf':
            ContentType = 'text/directory'
        elif houzhui == 'vcs':
            ContentType = 'text/calendar'
        elif houzhui == 'vct':
            ContentType = 'text/directory'
        elif houzhui == 'vda':
            ContentType = 'image/x-tga'
        elif houzhui == 'vhd':
            ContentType = 'text/x-vhdl'
        elif houzhui == 'vhdl':
            ContentType = 'text/x-vhdl'
        elif houzhui == 'viv':
            ContentType = 'video/vivo'
        elif houzhui == 'vivo':
            ContentType = 'video/vivo'
        elif houzhui == 'vlc':
            ContentType = 'audio/x-mpegurl'
        elif houzhui == 'vob':
            ContentType = 'video/mpeg'
        elif houzhui == 'voc':
            ContentType = 'audio/x-voc'
        elif houzhui == 'vor':
            ContentType = 'application/vnd.stardivision.writer'
        elif houzhui == 'vst':
            ContentType = 'image/x-tga'
        elif houzhui == 'wav':
            ContentType = 'audio/x-wav'
        elif houzhui == 'wax':
            ContentType = 'audio/x-ms-asx'
        elif houzhui == 'wb1':
            ContentType = 'application/x-quattropro'
        elif houzhui == 'wb2':
            ContentType = 'application/x-quattropro'
        elif houzhui == 'wb3':
            ContentType = 'application/x-quattropro'
        elif houzhui == 'wbmp':
            ContentType = 'image/vnd.wap.wbmp'
        elif houzhui == 'wcm':
            ContentType = 'application/vnd.ms-works'
        elif houzhui == 'wdb':
            ContentType = 'application/vnd.ms-works'
        elif houzhui == 'webm':
            ContentType = 'video/webm'
        elif houzhui == 'wk1':
            ContentType = 'application/vnd.lotus-1-2-3'
        elif houzhui == 'wk3':
            ContentType = 'application/vnd.lotus-1-2-3'
        elif houzhui == 'wk4':
            ContentType = 'application/vnd.lotus-1-2-3'
        elif houzhui == 'wks':
            ContentType = 'application/vnd.ms-works'
        elif houzhui == 'wma':
            ContentType = 'audio/x-ms-wma'
        elif houzhui == 'wmf':
            ContentType = 'image/x-wmf'
        elif houzhui == 'wml':
            ContentType = 'text/vnd.wap.wml'
        elif houzhui == 'wmls':
            ContentType = 'text/vnd.wap.wmlscript'
        elif houzhui == 'wmv':
            ContentType = 'video/x-ms-wmv'
        elif houzhui == 'wmx':
            ContentType = 'audio/x-ms-asx'
        elif houzhui == 'wp':
            ContentType = 'application/vnd.wordperfect'
        elif houzhui == 'wp4':
            ContentType = 'application/vnd.wordperfect'
        elif houzhui == 'wp5':
            ContentType = 'application/vnd.wordperfect'
        elif houzhui == 'wp6':
            ContentType = 'application/vnd.wordperfect'
        elif houzhui == 'wpd':
            ContentType = 'application/vnd.wordperfect'
        elif houzhui == 'wpg':
            ContentType = 'application/x-wpg'
        elif houzhui == 'wpl':
            ContentType = 'application/vnd.ms-wpl'
        elif houzhui == 'wpp':
            ContentType = 'application/vnd.wordperfect'
        elif houzhui == 'wps':
            ContentType = 'application/vnd.ms-works'
        elif houzhui == 'wri':
            ContentType = 'application/x-mswrite'
        elif houzhui == 'wrl':
            ContentType = 'model/vrml'
        elif houzhui == 'wv':
            ContentType = 'audio/x-wavpack'
        elif houzhui == 'wvc':
            ContentType = 'audio/x-wavpack-correction'
        elif houzhui == 'wvp':
            ContentType = 'audio/x-wavpack'
        elif houzhui == 'wvx':
            ContentType = 'audio/x-ms-asx'
        elif houzhui == 'x3f':
            ContentType = 'image/x-sigma-x3f'
        elif houzhui == 'xac':
            ContentType = 'application/x-gnucash'
        elif houzhui == 'xbel':
            ContentType = 'application/x-xbel'
        elif houzhui == 'xbl':
            ContentType = 'application/xml'
        elif houzhui == 'xbm':
            ContentType = 'image/x-xbitmap'
        elif houzhui == 'xcf':
            ContentType = 'image/x-xcf'
        elif houzhui == 'xcf.bz2':
            ContentType = 'image/x-compressed-xcf'
        elif houzhui == 'xcf.gz':
            ContentType = 'image/x-compressed-xcf'
        elif houzhui == 'xhtml':
            ContentType = 'application/xhtml+xml'
        elif houzhui == 'xi':
            ContentType = 'audio/x-xi'
        elif houzhui == 'xla':
            ContentType = 'application/vnd.ms-excel'
        elif houzhui == 'xlc':
            ContentType = 'application/vnd.ms-excel'
        elif houzhui == 'xld':
            ContentType = 'application/vnd.ms-excel'
        elif houzhui == 'xlf':
            ContentType = 'application/x-xliff'
        elif houzhui == 'xliff':
            ContentType = 'application/x-xliff'
        elif houzhui == 'xll':
            ContentType = 'application/vnd.ms-excel'
        elif houzhui == 'xlm':
            ContentType = 'application/vnd.ms-excel'
        elif houzhui == 'xls':
            ContentType = 'application/vnd.ms-excel'
        elif houzhui == 'xlsm':
            ContentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        elif houzhui == 'xlsx':
            ContentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        elif houzhui == 'xlt':
            ContentType = 'application/vnd.ms-excel'
        elif houzhui == 'xlw':
            ContentType = 'application/vnd.ms-excel'
        elif houzhui == 'xm':
            ContentType = 'audio/x-xm'
        elif houzhui == 'xmf':
            ContentType = 'audio/x-xmf'
        elif houzhui == 'xmi':
            ContentType = 'text/x-xmi'
        elif houzhui == 'xml':
            ContentType = 'application/xml'
        elif houzhui == 'xpm':
            ContentType = 'image/x-xpixmap'
        elif houzhui == 'xps':
            ContentType = 'application/vnd.ms-xpsdocument'
        elif houzhui == 'xsl':
            ContentType = 'application/xml'
        elif houzhui == 'xslfo':
            ContentType = 'text/x-xslfo'
        elif houzhui == 'xslt':
            ContentType = 'application/xml'
        elif houzhui == 'xspf':
            ContentType = 'application/xspf+xml'
        elif houzhui == 'xul':
            ContentType = 'application/vnd.mozilla.xul+xml'
        elif houzhui == 'xwd':
            ContentType = 'image/x-xwindowdump'
        elif houzhui == 'xyz':
            ContentType = 'chemical/x-pdb'
        elif houzhui == 'xz':
            ContentType = 'application/x-xz'
        elif houzhui == 'w2p':
            ContentType = 'application/w2p'
        elif houzhui == 'z':
            ContentType = 'application/x-compress'
        elif houzhui == 'zabw':
            ContentType = 'application/x-abiword'
        elif houzhui == 'zip':
            ContentType = 'application/zip'

        return ContentType

    def upload_file(self, push_datas, file_path, name=None):
        """
        Uploads a file to a given API.

        Args:
            api_url (str): The full URL of the API endpoint.
            file_path (str): The path to the file that needs to be uploaded.
            dataset_id (str): The dataset ID to be included in the form data.
            auth_token (str, optional): The authentication token for API access. Default is None.
            api_key (str, optional): The API key if required by the API. Default is None.

        Returns:
            dict: A dictionary with the status code and response from the API.
        """
        # 准备文件和表单数据
        pushData = {
            "trainingMode": "chunk",
            "data": push_datas,
            "prompt": "<context></context> 标记中是一段文本，学习和分析它，并整理学习成果：\n- 提出问题并给出每个问题的答案。\n- 答案需详细完整，给出相关原文描述。\n- 答案可以包含普通文字、链接、代码、表格、公示、媒体链接等 markdown 元素。\n- 最多提出 30 个问题。\n"
        }
        data = {'datasetId': self.dataset_id,
                "metadata": {},
                "bucketName": source_bucket_name,
                "pushData": pushData,
                "name": name
                }
        metadata = {}  # 替换成API需要的数据集ID
        form_data = {'data': json.dumps(data), 'metadata': json.dumps(metadata), 'bucketName': source_bucket_name}

        # 准备请求头
        headers = {
            "Accept": "application/json",

        }

        file_content_type = self.judge_post_file_houzhui(file_path.split('.')[-1])
        # files = {'file': (name, open(file_path, 'rb'), file_content_type)}  # 文件部分
        files = {'file': (quote(name, safe=''), open(file_path, 'rb'), file_content_type)}  # 文件部分

        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'

        try:
            # response = requests.post(self.fastgpt_api_base+f'/core/dataset/data/fileData?datasetId={self.dataset_id}', headers=headers, files=files, data=form_data)
            response = requests.post(self.fastgpt_api_base+f'/core/dataset/data/fileData?datasetId={self.dataset_id}&parentId={self.parent_id}', headers=headers, files=files, data=form_data)
            status_code = response.status_code
            if status_code == 200:
                print('文件【{}({})】 数据导入完成 {}'.format(name, self.dataset_id, response.json()['data']))
                return True
            else:
                print("=========数据导入结果===========", response.text)

        finally:
            # 关闭文件
            files['file'][1].close()

    def insert_wiserag_import_data(self, name: str, origin_file_path: str, data: list):

        # 构建导入数据结构
        insert_datas = self.generate_wiserag_import_data(data)
        # 导入数据
        status = self.upload_file(push_datas=insert_datas, file_path=origin_file_path, name=name)

        return status

    def get_json_files(self, json_dir_path: str):

        json_meta = {}

        # 获取目录下所有JSON文件
        json_files = [f for f in os.listdir(json_dir_path) if f.endswith('.json')]

        # 读取每个JSON文件
        for json_file in json_files:
            file_path = os.path.join(json_dir_path, json_file)
            with open(file_path, 'r') as f:
                data = json.load(f)
                # 处理读取的数据
                json_meta[json_file.replace('.json', '')] = data
        return json_meta

    def get_md_files(self, json_dir_path: str):
        md_files = [f for f in os.listdir(json_dir_path) if f.endswith('.md')]
        return md_files


def upload_json_from_path(filename_path, origin_file_name, origin_file_path):
    # 线上-电信租赁测试
    wiserag_import = wiseRagImport(api_base, api_key, dataset_id, parent_id, username, dataseset_name)
    data = []
    with open(filename_path, "r", encoding="utf-8") as file:
        for line in file:
            data.append(json.loads(line))
        df = pd.DataFrame(data)
        if all(col in df.columns for col in ['sourceId', 'context', 'index', 'fileName']):  # 判断字段是否齐全
            status = wiserag_import.insert_wiserag_import_data(name=origin_file_name, data=data, origin_file_path=origin_file_path)  # 数据发送
            if status:
                print("=====处理结果上传知识库成功，原始文件上传minio成功=====")
            else:
                print("=====处理结果上传知识库失败，原始文件上传minio失败=====")


if __name__ == '__main__':
    # filename_path = "/Users/<USER>/Documents/wiseweb/projects_other/文档处理/天翼租赁知识库20240619 - 复件(1)/1. 制度类资料-39/1.总公司制度-35/4 业务类-业务部-6/TYZL-CB02001-2023A天翼融资租赁有限公司租赁业务管理办法（2023修订）.pdf"
    # origin_file_name = "个人存单质押借款合同.pdf"
    # origin_file_name = "附件1：租赁公司关联交易核对原则.xlsx"
    # origin_file_path = "/Users/<USER>/Downloads/source/个人存单质押借款合同.pdf"
    # origin_file_path = "/Users/<USER>/Documents/wiseweb/projects_other/文档处理/天翼租赁知识库20240619 - 复件(1)/4. 财务类资料-6/附件1：租赁公司关联交易核对原则.xlsx"
    # filename_path = "/Users/<USER>/Downloads/个人存单质押借款合同.json"
    # filename_path = "/Users/<USER>/Documents/wiseweb/projects/file_analyze_20240903/results/final/附件1：租赁公司关联交易核对原则.json"

    # origin_file_name = "天翼融资租赁有限公司内控制度文件清单-2024.4.1.xlsx"
    # origin_file_path = "/Users/<USER>/Documents/wiseweb/projects_other/文档处理/天翼租赁知识库20240619 - 复件(1)/1. 制度类资料-39/天翼融资租赁有限公司内控制度文件清单-2024.4.1.xlsx"
    # filename_path = "/Users/<USER>/Documents/wiseweb/projects/file_analyze_20240903/results/final/天翼融资租赁有限公司内控制度文件清单-2024.4.1.json"

    # origin_file_name = "0.天翼融资租赁有限公司业务流程操作指引-20230808.xlsx"
    # origin_file_path = "/Users/<USER>/Documents/wiseweb/ToolCodes/文档处理/天翼租赁知识库20240619 - 复件(1)/3. 风控类资料-51/内部风控指引-18/0.天翼融资租赁有限公司业务流程操作指引-20230808.xlsx"
    # filename_path = "/Users/<USER>/Documents/wiseweb/Projects/wiserag_projects/file_analyze_20240903/results/final/0.天翼融资租赁有限公司业务流程操作指引-20230808.json"
    
    # 业务1
    # origin_file_name = "终端直租分期产品介绍（内部培训）-李鑫.pptx"
    # origin_file_path = "/data/jinxu/file_analyze_20240909/data/天翼租赁知识库v2/ppt/终端直租分期产品介绍（内部培训）-李鑫.pptx"
    # filename_path = "/data/jinxu/file_analyze_20240909/results/data_天翼租赁知识库v2_ppt_终端直租分期产品介绍（内部培训）-李鑫_docx_report/终端直租分期产品介绍（内部培训）-李鑫.json"
    
    # 业务2
    # origin_file_name = "新员工培训——大网业务流程详讲-0524.pptx"
    # origin_file_path = "/data/jinxu/file_analyze_20240909/data/天翼租赁知识库v2/ppt/新员工培训——大网业务流程详讲-0524.pptx"
    # filename_path = "/data/jinxu/file_analyze_20240909/results/data_天翼租赁知识库v2_ppt_新员工培训——大网业务流程详讲-0524_docx_report/新员工培训——大网业务流程详讲-0524.json"
    
    # # 业务3
    # origin_file_name = "天翼融资租赁公司业务及产品介绍.pptx"
    # origin_file_path = "/data/jinxu/file_analyze_20240909/data/天翼租赁知识库v2/ppt/天翼融资租赁公司业务及产品介绍.pptx"
    # filename_path = "/data/jinxu/file_analyze_20240909/results/data_天翼租赁知识库v2_ppt_天翼融资租赁公司业务及产品介绍_docx_report/天翼融资租赁公司业务及产品介绍.json"
    
    # # 业务4
    # origin_file_name = "产数类项目LTC流程详解.pptx"
    # origin_file_path = "/data/jinxu/file_analyze_20240909/data/天翼租赁知识库v2/ppt/产数类项目LTC流程详解.pptx"
    # filename_path = "/data/jinxu/file_analyze_20240909/results/data_天翼租赁知识库v2_ppt_产数类项目LTC流程详解_docx_report/产数类项目LTC流程详解.json"
    
    # 风控1
    # origin_file_name = "电子章使用操作指引.pptx"
    # origin_file_path = "/data/jinxu/file_analyze_20240909/data/天翼租赁知识库v2/ppt/电子章使用操作指引.pptx"
    # filename_path = "/data/jinxu/file_analyze_20240909/results/data_天翼租赁知识库v2_ppt_电子章使用操作指引_docx_report/电子章使用操作指引.json"
    
    # 风控2
    # origin_file_name = "合同电子章使用操作指引.pptx"
    # origin_file_path = "/data/jinxu/file_analyze_20240909/data/天翼租赁知识库v2/ppt/合同电子章使用操作指引.pptx"
    # filename_path = "/data/jinxu/file_analyze_20240909/results/data_天翼租赁知识库v2_ppt_合同电子章使用操作指引_docx_report/合同电子章使用操作指引.json"
    
    # 财务类1
    origin_file_name = "直租业务方案-解决当年资本支出不足-调整账务处理-租赁公司预付-v2to郝处1110.pptx"
    origin_file_path = "/data/jinxu/file_analyze_20240909/data/天翼租赁知识库v2/ppt/直租业务方案-解决当年资本支出不足-调整账务处理-租赁公司预付-v2to郝处1110.pptx"
    filename_path = "/data/jinxu/file_analyze_20240909/results/data_天翼租赁知识库v2_ppt_直租业务方案-解决当年资本支出不足-调整账务处理-租赁公司预付-v2to郝处1110_docx_report/直租业务方案-解决当年资本支出不足-调整账务处理-租赁公司预付-v2to郝处1110.json"
    
    upload_json_from_path(filename_path, origin_file_name, origin_file_path)
  
