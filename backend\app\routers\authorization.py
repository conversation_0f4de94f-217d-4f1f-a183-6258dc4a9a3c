from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from datetime import datetime
from ..models.authorization import Authorization, AuthorizationCreate, AuthorizationUpdate, AuthorizationResponse
from ..db.mongodb import db
from ..utils.auth import verify_token

router = APIRouter()

# 获取所有授权信息，支持分页和条件过滤
@router.get("/api/authorizations", response_model=Dict[str, Any])
async def get_authorizations(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    user_name: Optional[str] = None,  # 支持按用户名检索
    m_name: Optional[str] = None,  # 支持按模型名称检索
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {}
    if user_name:
        query["user_name"] = {"$regex": user_name, "$options": "i"}  # 使用正则表达式进行模糊匹配
    if m_name:
        query["m_name"] = {"$regex": m_name, "$options": "i"}  # 使用正则表达式进行模糊匹配

    authorizations = await db["authorizations"].find(query, {
        "_id": 0,
        "id": 1,
        "user_id": 1,
        "user_name": 1,
        "m_id": 1,
        "m_name": 1,
        "expiration_date": 1,
        "authorization_type": 1,
        "created_by": 1,
        "created_at": 1
    }).skip(skip).limit(pageSize).to_list(pageSize)
    total = await db["authorizations"].count_documents(query)
    return {
        "data": authorizations,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 添加新授权
@router.post("/api/authorizations", response_model=AuthorizationResponse)
async def create_authorization(authorization: AuthorizationCreate, current_user: dict = Depends(verify_token)):
    if authorization.user_id is None or authorization.m_id is None:
        raise HTTPException(status_code=400, detail="user_id and m_id are required")
    
    last_authorization = await db["authorizations"].find_one(sort=[("id", -1)])
    new_id = (last_authorization["id"] + 1) if last_authorization else 1

    authorization_data = authorization.dict()
    # authorization_data["m_id"] = authorization_data["m_id"]
    # authorization_data["m_name"] = authorization_data["m_name"]
    authorization_data.update({
        "id": new_id,
        "created_at": datetime.now(),
        "created_by": current_user["id"]
    })
    print(authorization_data)

    await db["authorizations"].insert_one(authorization_data)
    created_authorization = await db["authorizations"].find_one({"id": new_id})
    return AuthorizationResponse(**created_authorization)

# 更新授权
@router.put("/api/authorizations/{authorization_id}", response_model=AuthorizationResponse)
async def update_authorization(authorization_id: int, authorization: AuthorizationUpdate, current_user: dict = Depends(verify_token)):
    update_data = authorization.dict(exclude_unset=True)
    
    # 如果存在 expiration_date 字段，将其转换为 DateTime 对象
    if 'expiration_date' in update_data:
        try:
            update_data['expiration_date'] = datetime.strptime(update_data['expiration_date'], "%Y-%m-%d")
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")

    result = await db["authorizations"].update_one({"id": authorization_id}, {"$set": update_data})
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Authorization not found")
    
    updated_authorization = await db["authorizations"].find_one({"id": authorization_id})
    return AuthorizationResponse(**updated_authorization)

# 删除授权
@router.delete("/api/authorizations/{authorization_id}", response_model=Dict[str, int])
async def delete_authorization(authorization_id: int, current_user: dict = Depends(verify_token)):
    authorization = await db["authorizations"].find_one({"id": authorization_id})
    if not authorization:
        raise HTTPException(status_code=404, detail="Authorization not found")
    result = await db["authorizations"].delete_one({"id": authorization_id})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Authorization not found")
    return {"id": authorization_id}

# 获取用户列表，支持模糊搜索
@router.get("/api/authUsers", response_model=List[Dict[str, Any]])
async def get_users(
    name: Optional[str] = None,  # 支持按用户名模糊搜索
    current_user: dict = Depends(verify_token)
):
    query = {}
    if name:
        query["name"] = {"$regex": name, "$options": "i"}  # 使用正则表达式进行模糊匹配

    users = await db["users"].find(query, {"_id": 0, "id": 1, "name": 1}).to_list(100)
    return users

# 获取模型列表，支持模糊搜索
@router.get("/api/authLLMModels", response_model=List[Dict[str, Any]])
async def get_models(
    name: Optional[str] = None,  # 支持按 name 模糊搜索
    current_user: dict = Depends(verify_token)
):
    query = {}
    if name:
        query["m_name"] = {"$regex": name, "$options": "i"}  # 使用正则表达式进行模糊匹配
        query["name"] = {"$regex": name, "$options": "i"}  # 使用正则表达式进行模糊匹配

    models = await db["llms"].find(query, {"_id": 0, "id": 1, "name": 1}).to_list(100)
    return models

@router.get("/api/authEmbModels", response_model=List[Dict[str, Any]])
async def get_models(
    name: Optional[str] = None,  # 支持按 name 模糊搜索
    current_user: dict = Depends(verify_token)
):
    query = {}
    if name:
        query["m_name"] = {"$regex": name, "$options": "i"}  # 使用正则表达式进行模糊匹配
        query["name"] = {"$regex": name, "$options": "i"}  # 使用正则表达式进行模糊匹配

    models = await db["embeddings"].find(query, {"_id": 0, "id": 1, "name": 1}).to_list(100)
    return models