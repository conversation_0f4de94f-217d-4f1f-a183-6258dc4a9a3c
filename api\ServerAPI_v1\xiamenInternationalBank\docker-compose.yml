version: '3.8'

services:
  db:
    image: registry.cn-hangzhou.aliyuncs.com/pgvector:v0.5.0
    container_name: xiamen_bank_db_pgvector
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - xiamen_bank_network


  api:
    # image: xiamen_bank_api:v1.2_202502211803
    image: xiamen_bank_api:v1.2_202502240937
    container_name: xiamen_bank_api
    ports:
      - "8009:8000"
    env_file:
      - .env    # 这会加载所有环境变量，包括：
                # - PostgreSQL 配置
                # - Embedding Service 配置
                # - DeepSeek Configuration
    # 删除或注释掉 db 服务的依赖
    # depends_on:
    #   db:
    #     condition: service_healthy
    networks:
      - xiamen_bank_network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health"]
      interval: 5s
      timeout: 5s
      retries: 5
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]


networks:
  xiamen_bank_network:
    driver: bridge

# 定义数据卷
volumes:
  postgres_data:    # 取消注释这部分