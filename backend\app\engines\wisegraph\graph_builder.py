"""
知识图谱构建器
"""
import sys
import os
import asyncio
import logging
from typing import Dict, List, Any, Optional
from .llm_client import LLMClient
from .neo4j_client import Neo4jClient
from app.utils.promptStr import EntityExtractionPrompt

logger = logging.getLogger(__name__)

class GraphBuilder:
    """知识图谱构建器"""
    
    def __init__(self):
        self.llm_client = LLMClient()
        self.neo4j_client = Neo4jClient()
        self.is_connected = False
    
    async def initialize(self):
        """初始化连接"""
        try:
            await self.neo4j_client.connect()
            self.is_connected = True
            logger.info("GraphBuilder初始化成功")
        except Exception as e:
            logger.error(f"GraphBuilder初始化失败: {e}")
            raise
    
    async def close(self):
        """关闭连接"""
        if self.is_connected:
            await self.neo4j_client.close()
            self.is_connected = False
            logger.info("GraphBuilder连接已关闭")
    
    async def build_graph_from_chunks(self, chunks: Dict[str, str], clear_existing: bool = False, knowledge_base_id: str = None) -> Dict[str, Any]:
        """
        从文本块构建知识图谱
        
        Args:
            chunks: 文本块字典，格式为 {"chunk1": "text1", "chunk2": "text2", ...}
            clear_existing: 是否清空现有数据
            
        Returns:
            构建结果统计
        """
        if not self.is_connected:
            raise RuntimeError("GraphBuilder未初始化，请先调用initialize()")
        
        logger.info(f"开始构建知识图谱，文本块数量: {len(chunks)}")
        
        # 清空现有数据（如果需要）
        if clear_existing:
            await self.neo4j_client.clear_database()
            logger.info("已清空现有图数据")
        
        total_entities = 0
        total_relationships = 0
        processed_chunks = 0
        failed_chunks = 0
        
        # 处理每个文本块
        for chunk_id, text in chunks.items():
            try:
                logger.info(f"处理文本块: {chunk_id}")
                
                # 使用LLM提取实体和关系
                extraction_result = await self.llm_client.extract_entities_and_relations(
                    text, EntityExtractionPrompt
                )
                
                entities = extraction_result.get("entities", [])
                relationships = extraction_result.get("relationships", [])
                
                logger.info(f"文本块 {chunk_id} 提取结果 - 实体: {len(entities)}, 关系: {len(relationships)}")
                
                # 将实体和关系存储到Neo4j
                if entities or relationships:
                    stats = await self.neo4j_client.batch_create_entities_and_relationships(
                        entities, relationships, chunk_id, knowledge_base_id
                    )
                    
                    total_entities += stats["entities_created"]
                    total_relationships += stats["relationships_created"]
                
                processed_chunks += 1
                
                # 添加小延迟避免API限制
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"处理文本块 {chunk_id} 失败: {e}")
                failed_chunks += 1
                continue
        
        # 获取最终统计
        final_stats = await self.neo4j_client.get_database_stats()
        
        result = {
            "processed_chunks": processed_chunks,
            "failed_chunks": failed_chunks,
            "total_entities_created": total_entities,
            "total_relationships_created": total_relationships,
            "final_database_stats": final_stats,
            "success": processed_chunks > 0
        }
        
        logger.info(f"图构建完成: {result}")
        return result
    
    async def build_graph_from_single_text(self, text: str, chunk_id: str = "single_chunk", knowledge_base_id: str = None) -> Dict[str, Any]:
        """
        从单个文本构建知识图谱

        Args:
            text: 输入文本
            chunk_id: 文本块ID
            knowledge_base_id: 知识库ID

        Returns:
            构建结果
        """
        chunks = {chunk_id: text}
        return await self.build_graph_from_chunks(chunks, clear_existing=False, knowledge_base_id=knowledge_base_id)
    
    async def get_graph_summary(self) -> Dict[str, Any]:
        """
        获取图谱摘要信息
        
        Returns:
            图谱摘要统计
        """
        if not self.is_connected:
            raise RuntimeError("GraphBuilder未初始化，请先调用initialize()")
        
        try:
            async with self.neo4j_client.driver.session(database=self.neo4j_client.database) as session:
                # 获取实体类型统计
                entity_types_result = await session.run("""
                    MATCH (n:Entity)
                    RETURN n.entity_type as type, count(*) as count
                    ORDER BY count DESC
                """)
                
                entity_types = []
                async for record in entity_types_result:
                    entity_types.append({
                        "type": record["type"],
                        "count": record["count"]
                    })
                
                # 获取关系类型统计
                relation_types_result = await session.run("""
                    MATCH ()-[r:RELATED]->()
                    RETURN r.relation_type as type, count(*) as count
                    ORDER BY count DESC
                """)
                
                relation_types = []
                async for record in relation_types_result:
                    relation_types.append({
                        "type": record["type"],
                        "count": record["count"]
                    })
                
                # 获取基本统计
                basic_stats = await self.neo4j_client.get_database_stats()
                
                return {
                    "basic_stats": basic_stats,
                    "entity_types": entity_types,
                    "relation_types": relation_types
                }
                
        except Exception as e:
            logger.error(f"获取图谱摘要失败: {e}")
            return {
                "basic_stats": {"nodes": 0, "relationships": 0},
                "entity_types": [],
                "relation_types": []
            }
