from mongoengine import Document, StringField, ObjectIdField, DateTimeField,IntField,ListField
from datetime import datetime
from pydantic import BaseModel
from typing import Optional, List
from bson import ObjectId
from app.utils.enums import FileStorageType

class ConsumerProtectionFile(Document):
    meta = {
        'collection': 'consumer_protection_files'
    }
    # 文件状态枚举
    FILE_STATUS = (
        'WAITING',      # 等待处理
        'PARSING',      # 解析中
        'PARSED',       # 已解析
        'ANALYZING',    # 分析中
        'ANALYZED',     # 分析完成
        'ERROR'         # 错误
    )
    
    _id = ObjectIdField(primary_key=True, default=ObjectId)
    name = StringField(required=True)
    storage_path = StringField(required=True)
    file_name = StringField(required=True)
    storage_type = StringField(required=True, default=FileStorageType.LOCAL)
    data_type = StringField(required=True)  # 文件类型，如 pdf
    user_id = IntField(required=True)
    deleted_by = IntField(default=None)
    size = IntField(default=None)
    user_id= IntField(required=True)
    user_name = StringField(required=True)
    created_at = DateTimeField(default=datetime.now)
    parsers = ListField(required=True,default=[]) #解析结果和评估结果
    parser_type = StringField(required=True)
    status = StringField(required=True, choices=FILE_STATUS, default='WAITING')
    # 评估负责组
    evaluators = ListField(required=True,default=[])
    # 评估时间
    evaluator_time = DateTimeField(default=datetime.now)




class ConsumerProtectionFileResponse(BaseModel):
    id: str
    name: str
    url: str

class ConsumerProtectionFileUploadRequest(BaseModel):
    folder_name: str
    parentId: str
    action: str
    folder_id: str