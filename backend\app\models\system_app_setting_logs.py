from mongoengine import Document, <PERSON><PERSON>ield, DateTimeField, EnumField,DictField,IntField
from pydantic import BaseModel,ConfigDict
from typing import Optional, Dict, Any
from enum import Enum
from datetime import datetime
from app.utils.enums import AppType


class SystemAppSettingModel(Document):
    meta = {
        'collection': 'system_app_settings_logs'
    }
    id = IntField(primary_key=True)
    app_info = StringField()
    type = EnumField(AppType, required=True)
    params = DictField() # 参数
    created_by = IntField() # 创建人
    created_name = StringField() # 创建人名称
    created_at = DateTimeField(default=datetime.now)
