(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1626],{82947:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"}},52197:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"}}]},name:"star",theme:"filled"}},76853:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M859.9 780H164.1c-4.5 0-8.1 3.6-8.1 8v60c0 4.4 3.6 8 8.1 8h695.8c4.5 0 8.1-3.6 8.1-8v-60c0-4.4-3.6-8-8.1-8zM505.7 669a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V176c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8z"}}]},name:"vertical-align-bottom",theme:"outlined"}},44039:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M859.9 168H164.1c-4.5 0-8.1 3.6-8.1 8v60c0 4.4 3.6 8 8.1 8h695.8c4.5 0 8.1-3.6 8.1-8v-60c0-4.4-3.6-8-8.1-8zM518.3 355a8 8 0 00-12.6 0l-112 141.7a7.98 7.98 0 006.3 12.9h73.9V848c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V509.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 355z"}}]},name:"vertical-align-top",theme:"outlined"}},85175:function(e,t,n){"use strict";var r=n(1413),a=n(67294),c=n(48820),o=n(91146),s=function(e,t){return a.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:c.Z}))},i=a.forwardRef(s);t.Z=i},82061:function(e,t,n){"use strict";var r=n(1413),a=n(67294),c=n(47046),o=n(91146),s=function(e,t){return a.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:c.Z}))},i=a.forwardRef(s);t.Z=i},47389:function(e,t,n){"use strict";var r=n(1413),a=n(67294),c=n(27363),o=n(91146),s=function(e,t){return a.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:c.Z}))},i=a.forwardRef(s);t.Z=i},12906:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM512 533c-85.5 0-155.6 67.3-160 151.6a8 8 0 008 8.4h48.1c4.2 0 7.8-3.2 8.1-7.4C420 636.1 461.5 597 512 597s92.1 39.1 95.8 88.6c.3 4.2 3.9 7.4 8.1 7.4H664a8 8 0 008-8.4C667.6 600.3 597.5 533 512 533z"}}]},name:"frown",theme:"outlined"},o=n(91146),s=function(e,t){return a.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:c}))};var i=a.forwardRef(s)},79090:function(e,t,n){"use strict";var r=n(1413),a=n(67294),c=n(15294),o=n(91146),s=function(e,t){return a.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:c.Z}))},i=a.forwardRef(s);t.Z=i},43471:function(e,t,n){"use strict";var r=n(1413),a=n(67294),c=n(82947),o=n(91146),s=function(e,t){return a.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:c.Z}))},i=a.forwardRef(s);t.Z=i},50228:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 328a60 60 0 10120 0 60 60 0 10-120 0zM852 64H172c-17.7 0-32 14.3-32 32v660c0 17.7 14.3 32 32 32h680c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-32 660H204V128h616v596zM604 328a60 60 0 10120 0 60 60 0 10-120 0zm250.2 556H169.8c-16.5 0-29.8 14.3-29.8 32v36c0 4.4 3.3 8 7.4 8h729.1c4.1 0 7.4-3.6 7.4-8v-36c.1-17.7-13.2-32-29.7-32zM664 508H360c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"robot",theme:"outlined"},o=n(91146),s=function(e,t){return a.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:c}))};var i=a.forwardRef(s)},27496:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z"}}]},name:"send",theme:"outlined"},o=n(91146),s=function(e,t){return a.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:c}))};var i=a.forwardRef(s)},25820:function(e,t,n){"use strict";var r=n(1413),a=n(67294),c=n(52197),o=n(91146),s=function(e,t){return a.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:c.Z}))},i=a.forwardRef(s);t.Z=i},75750:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},o=n(91146),s=function(e,t){return a.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:c}))};var i=a.forwardRef(s)},87784:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"},o=n(91146),s=function(e,t){return a.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:c}))};var i=a.forwardRef(s)},19050:function(e,t,n){"use strict";var r=n(1413),a=n(67294),c=n(76853),o=n(91146),s=function(e,t){return a.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:c.Z}))},i=a.forwardRef(s);t.Z=i},15525:function(e,t,n){"use strict";var r=n(1413),a=n(67294),c=n(44039),o=n(91146),s=function(e,t){return a.createElement(o.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:c.Z}))},i=a.forwardRef(s);t.Z=i},93933:function(e,t,n){"use strict";n.d(t,{$Z:function(){return w},$o:function(){return d},Db:function(){return h},Mw:function(){return u},SJ:function(){return m},X1:function(){return b},Xw:function(){return f},bk:function(){return S},fx:function(){return z},qP:function(){return _},tn:function(){return Z},zl:function(){return E}});var r=n(15009),a=n.n(r),c=n(99289),o=n.n(c),s=n(78158),i=n(10981);function u(e){return l.apply(this,arguments)}function l(){return(l=o()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/myConversations",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function f(e){return p.apply(this,arguments)}function p(){return(p=o()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/conversations",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function d(e,t){return v.apply(this,arguments)}function v(){return(v=o()(a()().mark((function e(t,n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/conversationActive/"+t,{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(e){return x.apply(this,arguments)}function x(){return(x=o()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/clearConversation/"+t,{method:"PUT"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function m(e){return g.apply(this,arguments)}function g(){return(g=o()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/conversations/"+t,{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e,t){return y.apply(this,arguments)}function y(){return(y=o()(a()().mark((function e(t,n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/conversations/".concat(t),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Z(e){return k.apply(this,arguments)}function k(){return(k=o()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/message",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function w(e){return j.apply(this,arguments)}function j(){return(j=o()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/messages/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function _(e){return C.apply(this,arguments)}function C(){return(C=o()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/delete_messages",{method:"DELETE",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function S(e){return P.apply(this,arguments)}function P(){return(P=o()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/messages/collected",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function z(e){return T.apply(this,arguments)}function T(){return(T=o()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/app/get_knowledge_bases",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function E(e){return R.apply(this,arguments)}function R(){return(R=o()(a()().mark((function e(t){var n,r;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=(0,i.bW)(),e.next=3,fetch("/api/app/chat/chat2kb",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(n)},body:JSON.stringify(t)});case 3:if((r=e.sent).ok){e.next=6;break}throw new Error("HTTP 错误！状态码：".concat(r.status));case 6:return e.abrupt("return",r);case 7:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},13973:function(e,t,n){"use strict";n.d(t,{Z:function(){return y}});var r=n(15009),a=n.n(r),c=n(99289),o=n.n(c),s=n(5574),i=n.n(s),u=n(67294),l=n(55102),f=n(2453),p=n(17788),d=n(84567),v=n(78158);function h(e){return x.apply(this,arguments)}function x(){return(x=o()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,v.N)("/api/feedbacks",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var m=n(85893),g=l.Z.TextArea,b=[{label:"回答不准确",value:"inaccurate"},{label:"回答不完整",value:"incomplete"},{label:"理解错误",value:"irrelevant"},{label:"生成幻觉",value:"hallucination"},{label:"内容混乱",value:"error"},{label:"有害内容",value:"harmful"},{label:"其他问题",value:"other"}],y=function(e){var t=e.visible,n=e.messageId,r=e.conversationId,c=e.appInfo,s=e.onClose,l=u.useState(""),v=i()(l,2),x=v[0],y=v[1],Z=u.useState([]),k=i()(Z,2),w=k[0],j=k[1],_=function(){var e=o()(a()().mark((function e(){var t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==w.length){e.next=3;break}return f.ZP.error("请至少选择一个反馈类型"),e.abrupt("return");case 3:return e.prev=3,t={message_id:n,conversation_id:r,app_info:c,content:x,feedback_types:w},console.log("feedbackData===>",t),e.next=8,h(t);case 8:e.sent.success?(f.ZP.success("感谢您的反馈！"),C()):f.ZP.error("提交反馈失败，请稍后重试"),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(3),console.error("提交反馈失败:",e.t0),f.ZP.error("提交反馈失败，请稍后重试");case 16:case"end":return e.stop()}}),e,null,[[3,12]])})));return function(){return e.apply(this,arguments)}}(),C=function(){y(""),j([]),s()};return(0,m.jsxs)(p.Z,{title:"反馈问题",open:t,onOk:_,onCancel:C,okText:"提交",cancelText:"取消",children:[(0,m.jsxs)("div",{style:{marginBottom:16},children:[(0,m.jsx)("div",{style:{marginBottom:8},children:"请选择存在的问题（可多选）："}),(0,m.jsx)(d.Z.Group,{options:b,value:w,onChange:function(e){return j(e)}})]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("div",{style:{marginBottom:8},children:"详细反馈（选填）："}),(0,m.jsx)(g,{value:x,onChange:function(e){return y(e.target.value)},placeholder:"请输入您的具体反馈意见...",rows:4})]})]})}},27441:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return je}});var r=n(97857),a=n.n(r),c=n(15009),o=n.n(c),s=n(64599),i=n.n(s),u=n(19632),l=n.n(u),f=n(99289),p=n.n(f),d=n(5574),v=n.n(d),h=n(15525),x=n(19050),m=n(50228),g=n(1413),b=n(67294),y={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M816 768h-24V428c0-141.1-104.3-257.7-240-277.1V112c0-22.1-17.9-40-40-40s-40 17.9-40 40v38.9c-135.7 19.4-240 136-240 277.1v340h-24c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h216c0 61.8 50.2 112 112 112s112-50.2 112-112h216c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM512 888c-26.5 0-48-21.5-48-48h96c0 26.5-21.5 48-48 48zM304 768V428c0-55.6 21.6-107.8 60.9-147.1S456.4 220 512 220c55.6 0 107.8 21.6 147.1 60.9S720 372.4 720 428v340H304z"}}]},name:"bell",theme:"outlined"},Z=n(91146),k=function(e,t){return b.createElement(Z.Z,(0,g.Z)((0,g.Z)({},e),{},{ref:t,icon:y}))};var w=b.forwardRef(k),j={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M705.6 124.9a8 8 0 00-11.6 7.2v64.2c0 5.5 2.9 10.6 7.5 13.6a352.2 352.2 0 0162.2 49.8c32.7 32.8 58.4 70.9 76.3 113.3a355 355 0 0127.9 138.7c0 48.1-9.4 94.8-27.9 138.7a355.92 355.92 0 01-76.3 113.3 353.06 353.06 0 01-113.2 76.4c-43.8 18.6-90.5 28-138.5 28s-94.7-9.4-138.5-28a353.06 353.06 0 01-113.2-76.4A355.92 355.92 0 01184 650.4a355 355 0 01-27.9-138.7c0-48.1 9.4-94.8 27.9-138.7 17.9-42.4 43.6-80.5 76.3-113.3 19-19 39.8-35.6 62.2-49.8 4.7-2.9 7.5-8.1 7.5-13.6V132c0-6-6.3-9.8-11.6-7.2C178.5 195.2 82 339.3 80 506.3 77.2 745.1 272.5 943.5 511.2 944c239 .5 432.8-193.3 432.8-432.4 0-169.2-97-315.7-238.4-386.7zM480 560h64c4.4 0 8-3.6 8-8V88c0-4.4-3.6-8-8-8h-64c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8z"}}]},name:"poweroff",theme:"outlined"},_=function(e,t){return b.createElement(Z.Z,(0,g.Z)((0,g.Z)({},e),{},{ref:t,icon:j}))};var C=b.forwardRef(_),S=n(27496),P=n(79090),z=n(85175),T=n(55287),E=n(78919),R=n(4628),M=n(2453),D=n(42075),N=n(83062),H=n(83622),A=n(4393),B=n(86250),I=n(24444),L=(0,I.kc)((function(e){var t=e.token;return{layout:{background:"".concat(t.colorBgLayout,"80"),maxWidth:1080,margin:"0 auto",height:"100%",overflowY:"auto"},message:{height:"100%",overflowY:"auto",padding:"20px"},generatedContent:{minHeight:300,maxHeight:800,overflowY:"auto"},toolbar:{position:"sticky",top:0,padding:"8px 12px 8px 12px",zIndex:1,borderBottom:"1px solid #f0f0f0",backgroundColor:t.colorBgLayout,display:"flex",justifyContent:"space-between",alignItems:"center"}}})),Y=n(85893),O=(0,b.forwardRef)((function(e,t){var n=e.bubbleMessage,r=e.onContentReady,c=e.onSendToBubble,s=L().styles,i=(0,b.useState)(!1),u=v()(i,2),l=u[0],f=u[1],d=(0,b.useState)(!1),g=v()(d,2),y=g[0],Z=g[1],k=(0,b.useState)(!1),j=v()(k,2),_=j[0],I=j[1],O=(0,b.useState)("正在等待用户输入..."),q=v()(O,2),F=q[0],V=q[1],W=(0,b.useState)(!1),J=v()(W,2),G=J[0],U=J[1],X=(0,b.useState)("模拟用户输入测试信息"),$=v()(X,2),K=$[0],Q=$[1],ee=(0,b.useRef)(null),te=(0,b.useRef)(null),ne=function(){var e=p()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return");case 2:U(!0);try{setTimeout((function(){var e='根据用户输入内容："'.concat(t,'"，AI分析如下：\n• 用户可能在询问关于我们公司的基本信息\n• 建议回复要点：\n  - 公司名称：华夏保险公司\n  - 营业时间：周一至周五 9:00-17:30\n  - 联系方式：400-123-4567\n  - 提醒客户关注官方网站获取更多信息');V(e),r&&r(e),U(!1)}),1e3)}catch(e){console.error("处理内容时出错:",e),V("内容分析失败，请稍后重试"),U(!1)}case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();(0,b.useImperativeHandle)(t,(function(){return{updateReferenceContent:function(e){ne(e)},getRobotControl:function(){return l},getAiContent:function(){return F}}})),(0,b.useEffect)((function(){n&&ne(n)}),[n]);var re={value:K,onChange:function(e){Q(e)},onSubmit:function(e){ne(e)}};return(0,Y.jsxs)("div",{className:s.layout,children:[(0,Y.jsxs)("div",{className:s.toolbar,children:[(0,Y.jsxs)(D.Z,{children:[(0,Y.jsx)(N.Z,{title:"滚动到顶部",mouseEnterDelay:2,children:(0,Y.jsx)(H.ZP,{type:"text",icon:(0,Y.jsx)(h.Z,{}),onClick:function(){var e;null===(e=te.current)||void 0===e||e.scrollTo({top:0,behavior:"smooth"})}})}),(0,Y.jsx)(N.Z,{title:"滚动到底部",mouseEnterDelay:2,children:(0,Y.jsx)(H.ZP,{type:"text",icon:(0,Y.jsx)(x.Z,{}),onClick:function(){var e;null===(e=te.current)||void 0===e||e.scrollTo({top:te.current.scrollHeight,behavior:"smooth"})}})})]}),(0,Y.jsxs)(D.Z,{children:[(0,Y.jsx)(N.Z,{title:"机器人接管",children:(0,Y.jsx)(H.ZP,{type:l?"primary":"text",icon:(0,Y.jsx)(m.Z,{}),onClick:function(){return f(!l)}})}),(0,Y.jsx)(N.Z,{title:"启动提示",children:(0,Y.jsx)(H.ZP,{type:y?"primary":"text",icon:(0,Y.jsx)(w,{}),onClick:function(){return Z(!y)}})}),(0,Y.jsx)(N.Z,{title:"启动服务",children:(0,Y.jsx)(H.ZP,{type:_?"primary":"text",icon:(0,Y.jsx)(C,{}),onClick:function(){return I(!_)}})})]})]}),(0,Y.jsx)("div",{className:s.message,ref:te,children:(0,Y.jsxs)("div",{children:[(0,Y.jsx)(A.Z,{title:"客服机器人测试",extra:(0,Y.jsx)(H.ZP,{type:"primary",icon:(0,Y.jsx)(S.Z,{}),onClick:function(){K&&c?(c(K),M.ZP.success("已发送到对话框")):M.ZP.error("请先输入内容")},children:"发送到对话"}),children:(0,Y.jsxs)(B.Z,{wrap:!0,gap:12,children:[(0,Y.jsx)(E.Z,{items:[{key:"1",description:"有什么新的投资机会推荐吗？"},{key:"2",description:"能帮我理解当前市场趋势吗？"},{key:"3",description:"如何管理投资风险？分享一些建议吧！"},{key:"4",description:"我怎样才能优化我的投资组合？"}],wrap:!0}),(0,Y.jsx)(R.Z,a()(a()({},re),{},{ref:ee}))]})}),(0,Y.jsx)(A.Z,{title:"AI提示信息",style:{marginTop:16},extra:G?(0,Y.jsx)(P.Z,{}):null,actions:[(0,Y.jsx)(S.Z,{onClick:function(){Q(F),M.ZP.success("内容已发送到输入框")}},"发送"),(0,Y.jsx)(z.Z,{onClick:function(){navigator.clipboard.writeText(F).then((function(){M.ZP.success("内容已复制到剪贴板")})).catch((function(){M.ZP.error("复制失败")}))}},"复制"),(0,Y.jsx)(T.Z,{},"显示")],children:(0,Y.jsx)("div",{id:"ai-generated-content",className:s.generatedContent,children:F})})]})})]})})),q=O,F=n(93461),V=n(34114),W=n(78205),J=n(9502),G=n(37864),U=n(71471),X=n(17788),$=n(74330),K=n(55102),Q=n(85265),ee=n(10048),te=n(10981),ne=n(78404),re=n(14079),ae=n(51042),ce=n(82061),oe=n(47389),se=n(87784),ie=n(25820),ue=n(75750),le=n(12906),fe=n(43471),pe=n(27484),de=n.n(pe),ve=(0,I.kc)((function(e){var t=e.token;return{reference:{background:"".concat(t.colorBgLayout,"80"),minWidth:"400px",maxWidth:"720px",width:"50%"},layout:{width:"100%",minWidth:"800px",borderRadius:t.borderRadius,display:"flex",background:t.colorBgContainer,fontFamily:"AlibabaPuHuiTi, ".concat(t.fontFamily,", sans-serif"),".ant-prompts":{color:t.colorText},border:"3px solid",borderImage:"linear-gradient(45deg, ".concat(t.colorPrimary,", ").concat(t.colorSplit,") 1")},menu:{height:"100%",display:"flex",flexDirection:"column"},conversations:{padding:"0 12px",flex:1,overflowY:"auto"},chat:{height:"100%",width:"50%",maxWidth:"700px",margin:"0 auto",boxSizing:"border-box",display:"flex",flexDirection:"column",padding:t.paddingLG,gap:"16px"},messages:{flex:1},placeholder:{paddingTop:"32px"},sender:{boxShadow:t.boxShadow},logo:{display:"flex",height:"72px",alignItems:"center",justifyContent:"start",padding:"0 24px",boxSizing:"border-box",img:{width:"24px",height:"24px",display:"inline-block"},span:{display:"inline-block",margin:"0 8px",fontWeight:"bold",color:t.colorText,fontSize:"16px"}},addBtn:{background:"#1677ff0f",border:"1px solid #1677ff34",width:"calc(100% - 24px)",margin:"0 12px 24px 12px"}}})),he=n(93933),xe=n(13973);function me(e){return e+"-"+Date.now()}var ge=[{key:"historyConversation",description:"历史对话",icon:(0,Y.jsx)(re.Z,{style:{color:"#FF4D4F"}})},{key:"newConversation",description:"新建对话",icon:(0,Y.jsx)(ae.Z,{style:{color:"#1890FF"}})},{key:"clearConversation",description:"清空对话",icon:(0,Y.jsx)(ce.Z,{style:{color:"#1890FF"}})}],be=(0,te.bG)(),ye=(0,ne.kH)(),Ze=(0,ee.Z)({html:!0,breaks:!0}),ke=function(e){return(0,Y.jsx)(U.Z,{style:{marginBottom:0},children:(0,Y.jsx)("div",{dangerouslySetInnerHTML:{__html:Ze.render(e)}})})},we="CustomerServiceBot",je=function(){var e,t=ve().styles,n=(0,b.useState)(window.innerHeight),r=v()(n,1)[0],c=b.useRef(),s=(0,b.useRef)(null),u=b.useState(!1),f=v()(u,2),d=f[0],h=f[1],x=b.useState(""),m=v()(x,2),g=m[0],y=m[1],Z=b.useState([]),k=v()(Z,2),w=k[0],j=k[1],_=b.useState(),C=v()(_,2),S=C[0],P=C[1],T=(0,b.useState)(!1),A=v()(T,2),I=A[0],L=A[1],O=(0,b.useState)(!1),U=v()(O,2),ee=U[0],ne=U[1],re=(0,b.useState)(!1),pe=v()(re,2),Ze=pe[0],je=pe[1],_e=(0,b.useState)(""),Ce=v()(_e,2),Se=Ce[0],Pe=Ce[1],ze=(0,b.useState)(""),Te=v()(ze,2),Ee=Te[0],Re=Te[1],Me=(0,b.useState)([]),De=v()(Me,2),Ne=De[0],He=De[1],Ae=(0,b.useState)(!1),Be=v()(Ae,2),Ie=Be[0],Le=Be[1],Ye=(0,b.useState)(""),Oe=v()(Ye,2),qe=Oe[0],Fe=Oe[1],Ve=function(e){Fe(e),Le(!0)},We=function(e){var t=Ne.find((function(t){return t.message_id===e}));t&&navigator.clipboard.writeText(t.content).then((function(){M.ZP.success("复制成功")})).catch((function(){M.ZP.error("复制失败")}))},Je=(0,F.Z)({request:(e=p()(o()().mark((function e(t,n){var r,a,u,f,p,d,v,h,x,m,g,b,y,Z,k,w,j,_,C,S,P,z,T,E,R,D,N,H,A,B,I,L,Y,O,q,F,V,W;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.messages,a=t.message,u=n.onSuccess,f=n.onUpdate,p=n.onError,e.prev=2,!ee){e.next=6;break}return M.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 6:if(ne(!0),v=(0,te.bW)(),a&&"user"===a.role&&(null===(h=s.current)||void 0===h||h.updateReferenceContent(a.content)),x=a?a.id:me(c.current),a||u({content:"出现了异常:",role:"assistant",id:x,references:[],query:[],collected:!1}),m={conversation_id:c.current||"",message_id:x,meta_data:{},extra:{},role:a?a.role:"user",content:a?a.content:"",app_info:we,user_id:null==be?void 0:be.id,user_name:null==be?void 0:be.name,references:[],token_count:null,price:null,collected:!1,created_at:de()().format("YYYY-MM-DD HH:mm:ss")},He((function(e){var t=[].concat(l()(e),[m]);return console.log("更新后的消息列表:",t),t})),c.current){e.next=16;break}throw M.ZP.error("No active conversation selected"),new Error("No active conversation selected");case 16:return console.log("activeKey===>",c.current),g={conversation_id:c.current,app_info:we,user_id:null==be?void 0:be.id,user_name:null==be?void 0:be.name,extra:{},messages:r},b={id:me(c.current),role:"user",content:"",query:[],references:[],collected:!1},y=!1,Z="",k=[],f(b),e.next=25,fetch("/api/app/chat/system_app_completions",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(v)},body:JSON.stringify(g)});case 25:if((w=e.sent).ok){e.next=28;break}throw new Error("HTTP 错误！状态码：".concat(w.status));case 28:if(j=null===(d=w.body)||void 0===d?void 0:d.getReader()){e.next=31;break}throw new Error("当前浏览器不支持 ReadableStream。");case 31:_=new TextDecoder("utf-8"),C={conversation_id:c.current||"",message_id:b.id,meta_data:{},extra:{},role:"assistant",content:"",app_info:we,user_id:null==be?void 0:be.id,user_name:null==be?void 0:be.name,references:[],token_count:null,price:null,collected:!1,created_at:de()().format("YYYY-MM-DD HH:mm:ss")};case 33:if(y){e.next=101;break}return e.next=36,j.read();case 36:S=e.sent,P=S.value,S.done&&(y=!0),Z+=_.decode(P,{stream:!0}),z=Z.split("\n\n"),Z=z.pop()||"",T=i()(z),e.prev=44,T.s();case 46:if((E=T.n()).done){e.next=91;break}if(""!==(R=E.value).trim()){e.next=50;break}return e.abrupt("continue",89);case 50:D=R.split("\n"),N=null,H=null,A=i()(D);try{for(A.s();!(B=A.n()).done;)(I=B.value).startsWith("event: ")?N=I.substring(7).trim():I.startsWith("data: ")&&(H=I.substring(6))}catch(e){A.e(e)}finally{A.f()}if(!H){e.next=89;break}e.t0=N,e.next="answer"===e.t0?59:"moduleStatus"===e.t0?71:"appStreamResponse"===e.t0?73:"flowResponses"===e.t0?75:"end"===e.t0?77:"error"===e.t0?79:89;break;case 59:if("[DONE]"===H){e.next=70;break}e.prev=60,Y=JSON.parse(H),(O=(null===(L=Y.choices[0])||void 0===L||null===(L=L.delta)||void 0===L?void 0:L.content)||"")&&(b.content+=O,f(b)),e.next=70;break;case 66:return e.prev=66,e.t1=e.catch(60),console.error("Error parsing answer data:",e.t1),e.abrupt("return",u({content:"出现了异常:"+H,role:"assistant",id:me(c.current),references:[],query:[],collected:!1}));case 70:return e.abrupt("break",89);case 71:try{q=JSON.parse(H),console.log("模块状态：",q)}catch(e){console.error("Error parsing moduleStatus data:",e)}return e.abrupt("break",89);case 73:try{F=JSON.parse(H),console.log("appStreamData===>",F),k=F,b.references=k}catch(e){console.error("Error parsing appStreamResponse data:",e)}return e.abrupt("break",89);case 75:try{console.log("flowResponsesData",H)}catch(e){console.error("Error parsing flowResponses data:",e)}return e.abrupt("break",89);case 77:return y=!0,e.abrupt("break",89);case 79:e.prev=79,V=JSON.parse(H),f(V),e.next=88;break;case 84:throw e.prev=84,e.t2=e.catch(79),console.error("Error event received:",e.t2),e.t2;case 88:return e.abrupt("break",89);case 89:e.next=46;break;case 91:e.next=96;break;case 93:e.prev=93,e.t3=e.catch(44),T.e(e.t3);case 96:return e.prev=96,T.f(),e.finish(96);case 99:e.next=33;break;case 101:if(u(b),!b.content||""===b.content.trim()){e.next=109;break}return C.content=b.content,C.references=k,e.next=107,(0,he.tn)(C);case 107:(W=e.sent).success?(C.message_id=W.data.message_id,console.log("创建消息成功，返回数据:",W.data),He((function(e){var t=[].concat(l()(e),[W.data]);return console.log("更新后的消息列表:",t),t}))):M.ZP.error("消息上报失败");case 109:e.next=116;break;case 111:e.prev=111,e.t4=e.catch(2),console.log("error===>",e.t4),u({content:"出现了，系统正在处理其他对话。请稍后重试",role:"assistant",id:me(c.current),references:[],query:[],collected:!1}),p(e.t4 instanceof Error?e.t4:new Error("Unknown error"));case 116:return e.prev=116,ne(!1),e.finish(116);case 119:case"end":return e.stop()}}),e,null,[[2,111,116,119],[44,93,96,99],[60,66],[79,84]])}))),function(t,n){return e.apply(this,arguments)})}),Ge=v()(Je,1)[0],Ue=(0,V.Z)({agent:Ge}),Xe=Ue.onRequest,$e=Ue.messages,Ke=Ue.setMessages,Qe=function(e){P(e),console.log("activeKey 设置",e),c.current=e},et=function(e){var t=e.filter((function(e){return e.pinned})).sort((function(e,t){return new Date(t.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return a()(a()({},e),{},{key:e.id||"",label:e.conversation_name||e.id,group:"置顶"})})),n=e.filter((function(e){return!e.pinned})).sort((function(e,t){return new Date(t.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return a()(a()({},e),{},{key:e.id||"",label:e.conversation_name||"",group:"对话"})}));j([].concat(l()(t),l()(n)))},tt=function(e){return 0===e.length?null:e.reduce((function(e,t){return new Date(t.active_at)>new Date(e.active_at)?t:e}))},nt=function(){var e=p()(o()().mark((function e(t){var n,r,a;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,ne(!0),console.info("获取对话信息",t),n=de()().format("YYYY-MM-DD HH:mm:ss"),e.next=6,(0,he.$o)(t,{conversation_name:null,active_at:n,pinned_at:null,pinned:null});case 6:null!=(r=e.sent)&&r.messages?(console.info("设置对话信息",r.messages),a=r.messages.map((function(e){return{id:e.id||e.message_id,message:{id:e.id||e.message_id,content:e.content,role:e.role,references:e.references||[],collected:e.collected||!1},status:"assistant"===e.role?"success":"local",meta:e.meta||{avatar:"assistant"===e.role?(null==ye?void 0:ye.logo)||"/static/logo.png":(null==be?void 0:be.avatar)||"/avatar/default.jpeg"}}})),He(r.messages),Ke(a),Qe(t)):M.ZP.error("获取对话信息失败"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("切换对话时出错：",e.t0);case 13:return e.prev=13,ne(!1),P(t),e.finish(13);case 17:case"end":return e.stop()}}),e,null,[[0,10,13,17]])})));return function(t){return e.apply(this,arguments)}}(),rt=function(){var e=p()(o()().mark((function e(){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(c.current){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,(0,he.Db)(c.current);case 4:e.sent.success?(He([]),Ke([])):M.ZP.error("清空对话失败");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),at=function(){var e=p()(o()().mark((function e(){var t,n,r,a;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!ee){e.next=3;break}return M.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(!(t=(0,te.bG)())){e.next=19;break}return e.prev=5,n=(new Date).toLocaleString(),r="对话-".concat(n),e.next=10,(0,he.Xw)({user_id:t.id,user_name:t.name,conversation_name:r,app_info:we});case 10:a=e.sent,et([].concat(l()(w),[{key:a.id||"",id:a.id||"",label:a.conversation_name||"",conversation_name:a.conversation_name||"",active_at:a.active_at||"",pinned_at:a.pinned_at,pinned:a.pinned||!1,messages:[]}])),Qe(a.id||""),rt(),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(5),console.error("创建新对话时出错：",e.t0);case 19:case"end":return e.stop()}}),e,null,[[5,16]])})));return function(){return e.apply(this,arguments)}}(),ct=function(){var e=p()(o()().mark((function e(t){var n,r,c,s,i;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=w.find((function(e){return e.key===t}))){e.next=3;break}return e.abrupt("return");case 3:return r=n.pinned,c=!r,e.prev=6,s=de()().format("YYYY-MM-DD HH:mm:ss"),e.next=10,(0,he.X1)(t,{conversation_name:null,active_at:null,pinned:c,pinned_at:s});case 10:i=w.map((function(e){return e.key===t?a()(a()({},e),{},{pinned:c}):e})),et(i),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),console.error("更新置顶状态时出错：",e.t0);case 17:case"end":return e.stop()}}),e,null,[[6,14]])})));return function(t){return e.apply(this,arguments)}}(),ot=function(){var e=p()(o()().mark((function e(t){var n;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,he.SJ)(t);case 3:n=w.filter((function(e){return e.key!==t})),et(n),c.current===t&&n.length>0&&nt(n[0].key||""),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除对话时出错：",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(t){return e.apply(this,arguments)}}(),st=function(){var e=p()(o()().mark((function e(t,n){var r,c;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,w.find((function(e){return e.key===t}))){e.next=4;break}return e.abrupt("return");case 4:return r={conversation_name:n,active_at:null,pinned_at:null,pinned:null},e.next=7,(0,he.X1)(t,r);case 7:null!=(c=e.sent)&&c.success?j((function(e){return e.map((function(e){return e.key===t?a()(a()({},e),{},{label:n}):e}))})):M.ZP.error("更新对话标题失败"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("更新对话标题时出错：",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t,n){return e.apply(this,arguments)}}(),it=function(){var e=p()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!ee){e.next=3;break}return M.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:return e.next=5,nt(t);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();(0,b.useEffect)((function(){var e=function(){var e=p()(o()().mark((function e(){var t,n,r;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t=(0,te.bG)())){e.next=20;break}return e.prev=2,e.next=5,(0,he.Mw)({user_id:t.id,app_info:we});case 5:if(!(n=e.sent).success||!Array.isArray(n.data)){e.next=15;break}if(0!==n.data.length){e.next=12;break}return e.next=10,at();case 10:e.next=15;break;case 12:r=tt(n.data),et(n.data),nt(r?r.id:n.data[0].id||"");case 15:e.next=20;break;case 17:e.prev=17,e.t0=e.catch(2),console.error("初始化对话时出错：",e.t0);case 20:case"end":return e.stop()}}),e,null,[[2,17]])})));return function(){return e.apply(this,arguments)}}();e()}),[we]);var ut=function(){var e=p()(o()().mark((function e(t){var n,r,a,c;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("重新生成消息:",t),e.prev=1,console.info(Ne),n=Ne.findIndex((function(e){return e.message_id===t})),console.log("currentIndex===>",n),-1!==n){e.next=8;break}return M.ZP.error("未找到指定消息"),e.abrupt("return");case 8:return r=Ne[n],a=Ne.slice(n),console.log("将要删除的消息:",a),e.next=13,(0,he.qP)(a.map((function(e){return e.message_id})));case 13:e.sent.success||M.ZP.error("删除消息失败"),He((function(e){return e.slice(0,n)})),Ke((function(e){return e.slice(0,n)})),"assistant"===r.role?(c=Ne.slice(0,n).reverse().find((function(e){return"user"===e.role})))&&Xe({id:t,role:"user",content:c.content,references:[],collected:!1,query:[]}):Xe({id:t,role:"user",content:r.content,references:[],collected:!1,query:[]}),M.ZP.success("正在重新生成回复..."),e.next=25;break;case 21:e.prev=21,e.t0=e.catch(1),console.error("重新生成消息时出错：",e.t0),M.ZP.error("重新生成消息失败");case 25:case"end":return e.stop()}}),e,null,[[1,21]])})));return function(t){return e.apply(this,arguments)}}(),lt=function(){var e=p()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:X.Z.confirm({title:"确认删除",content:"确定要删除这条消息吗？删除后不可恢复。",okText:"确认",cancelText:"取消",onOk:function(){return p()(o()().mark((function e(){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("delete messageId===>",t),e.next=4,(0,he.$Z)(t);case 4:e.sent.success?(He((function(e){return e.filter((function(e){return e.message_id!==t}))})),console.log("delete currentConversationMessages===>",Ne),Ke((function(e){return e.filter((function(e){return e.message.id!==t}))})),console.log("delete messages===>",$e),M.ZP.success("消息及相关引用已删除")):M.ZP.error("删除消息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除消息时出错：",e.t0),M.ZP.error("删除消息失败");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ft=function(){var e=p()(o()().mark((function e(t,n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("收藏状态切换:",t,n),e.next=3,(0,he.bk)({message_id:t,collected:!n});case 3:e.sent.success?(M.ZP.success(n?"取消收藏成功":"收藏成功"),Ke((function(e){return e.map((function(e){return e.id===t?a()(a()({},e),{},{message:a()(a()({},e.message),{},{collected:!n})}):e}))})),He((function(e){return e.map((function(e){return e.message_id===t?a()(a()({},e),{},{collected:!n}):e}))}))):M.ZP.error(n?"取消收藏失败":"收藏失败");case 5:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),pt=function(e){if(console.log("nextContent===>",e),e){if(Xe({id:me(c.current),role:"user",content:e,references:[],query:[],collected:!1}),s.current&&s.current.getRobotControl()){console.log("机器人接管已开启，使用AI提示信息");var t=s.current.getAiContent();t&&"正在等待用户输入..."!==t&&setTimeout((function(){Xe({id:me(c.current),role:"assistant",content:t,references:[],query:[],collected:!1})}),800)}else console.log("机器人接管已关闭，由后端AI处理");y("")}},dt=function(){var e=p()(o()().mark((function e(t){var n,r,a;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!ee){e.next=3;break}return M.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(n=t.data,r=n.key,a=n.description,"historyConversation"!==r){e.next=8;break}L(!0),e.next=19;break;case 8:if("newConversation"!==r){e.next=13;break}return e.next=11,at();case 11:e.next=19;break;case 13:if("clearConversation"!==r){e.next=18;break}return e.next=16,rt();case 16:e.next=19;break;case 18:Xe({id:me(c.current),role:"user",content:a,references:[],query:[],collected:!1});case 19:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),vt=(0,Y.jsx)(D.Z,{direction:"vertical",size:16,className:t.placeholder,children:(0,Y.jsx)(W.Z,{variant:"borderless",icon:(0,Y.jsx)("img",{src:(null==ye?void 0:ye.logo)||"/static/logo.png",alt:"logo"}),title:"您好，我是智能客服助手",description:"我可以帮您快速查找和了解相关知识信息，请问您想了解什么？"})}),ht=$e.length>0?$e.map((function(e){var t=e.id,n=e.message,r=e.status;return{key:c.current+"_"+t,loadingRender:function(){return(0,Y.jsxs)(D.Z,{children:[(0,Y.jsx)($.Z,{size:"small"}),"模型思考中..."]})},loading:"loading"===r&&n.content.length<1,content:n.content,shape:"corner",variant:"filled",rols:n.role,messageRender:ke,avatar:"local"===r?{src:(null==be?void 0:be.avatar)||"/avatar/default.jpeg"}:{src:(null==ye?void 0:ye.logo)||"/static/logo.png"},placement:"local"!==r?"end":"start",footer:"local"!==r?(0,Y.jsxs)(B.Z,{children:[(0,Y.jsx)(H.ZP,{size:"small",type:"text",icon:(0,Y.jsx)(ce.Z,{style:{color:"#ccc"}}),onClick:function(){return lt(n.id)}}),(0,Y.jsx)(H.ZP,{size:"small",type:"text",icon:n.collected?(0,Y.jsx)(ie.Z,{style:{color:"#FFD700"}}):(0,Y.jsx)(ue.Z,{style:{color:"#ccc"}}),onClick:function(){return ft(n.id,n.collected)}}),(0,Y.jsx)(H.ZP,{size:"small",type:"text",icon:(0,Y.jsx)(le.Z,{style:{color:"#ccc"}}),onClick:function(){return Ve(n.id)}}),(0,Y.jsx)(H.ZP,{size:"small",type:"text",icon:(0,Y.jsx)(z.Z,{style:{color:"#ccc"}}),onClick:function(){return We(n.id)}})]}):(0,Y.jsxs)(B.Z,{children:[(0,Y.jsx)(H.ZP,{size:"small",type:"text",icon:(0,Y.jsx)(fe.Z,{style:{color:"#ccc"}}),onClick:function(){return ut(n.id)}}),(0,Y.jsx)(H.ZP,{size:"small",type:"text",icon:(0,Y.jsx)(ce.Z,{style:{color:"#ccc"}}),onClick:function(){return lt(n.id)}}),(0,Y.jsx)(H.ZP,{size:"small",type:"text",icon:n.collected?(0,Y.jsx)(ie.Z,{style:{color:"#FFD700"}}):(0,Y.jsx)(ue.Z,{style:{color:"#ccc"}}),onClick:function(){return ft(n.id,n.collected)}}),(0,Y.jsx)(H.ZP,{size:"small",type:"text",icon:(0,Y.jsx)(le.Z,{style:{color:"#ccc"}}),onClick:function(){return Ve(n.id)}}),(0,Y.jsx)(H.ZP,{size:"small",type:"text",icon:(0,Y.jsx)(z.Z,{style:{color:"#ccc"}}),onClick:function(){return We(n.id)}})]})}})):[{content:vt,variant:"borderless"}],xt=(0,Y.jsx)(R.Z.Header,{title:"Attachments",open:d,onOpenChange:h,styles:{content:{padding:0}}}),mt=(0,Y.jsxs)("div",{className:t.logo,children:[(0,Y.jsx)("span",{children:"对话记录"}),(0,Y.jsx)(N.Z,{title:"新对话",children:(0,Y.jsx)(H.ZP,{type:"text",icon:(0,Y.jsx)(ae.Z,{}),onClick:at,style:{fontSize:"16px"}})})]}),gt=(0,Y.jsx)(X.Z,{title:"修改对话标题",open:Ze,onOk:function(){Ee&&Se.trim()&&(st(Ee,Se.trim()),je(!1))},onCancel:function(){je(!1),Pe(""),Re("")},children:(0,Y.jsx)(K.Z,{value:Se,onChange:function(e){return Pe(e.target.value)},placeholder:"请输入新的对话标题"})}),bt=(0,Y.jsx)(Q.Z,{title:"历史对话",placement:"right",width:400,onClose:function(){return L(!1)},open:I,children:(0,Y.jsxs)("div",{className:t.menu,children:[mt,(0,Y.jsx)(J.Z,{items:w,activeKey:S,onActiveChange:it,menu:function(e){return{items:[{label:"重命名",key:"edit",icon:(0,Y.jsx)(oe.Z,{})},{label:"置顶",key:"pin",icon:(0,Y.jsx)(se.Z,{})},{label:"删除",key:"delete",icon:(0,Y.jsx)(ce.Z,{}),danger:!0}],onClick:function(t){switch(console.log("menuInfo","Click ".concat(e.key," - ").concat(t.key)),t.key){case"edit":Re(e.key),Pe(e.label),je(!0);break;case"pin":ct(e.key);break;case"delete":if(ee)return void M.ZP.error("系统正在处理其他对话。请稍😊");ot(e.key)}}}},groupable:!0})]})});return(0,b.useEffect)((function(){console.log("currentConversationMessages 更新了:",Ne)}),[Ne]),(0,Y.jsxs)("div",{className:t.layout,style:{height:r-56},children:[(0,Y.jsxs)("div",{className:t.chat,children:[(0,Y.jsx)(G.Z.List,{items:ht,className:t.messages}),(0,Y.jsx)(E.Z,{items:ge,onItemClick:dt}),(0,Y.jsx)(R.Z,{value:g,header:xt,onSubmit:pt,onChange:y,loading:Ge.isRequesting(),className:t.sender})]}),(0,Y.jsx)("div",{className:t.reference,children:(0,Y.jsx)(q,{ref:s,onContentReady:function(e){console.log("AI分析内容已准备好:",e)},onSendToBubble:function(e){var t;e&&(y(e),null!==(t=s.current)&&void 0!==t&&t.getRobotControl()?pt(e):console.log("已更新输入框内容，等待用户发送"))}})}),gt,bt,(0,Y.jsx)(xe.Z,{visible:Ie,messageId:qe,conversationId:S,appInfo:we,onClose:function(){return Le(!1)}})]})}},64599:function(e,t,n){var r=n(96263);e.exports=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=r(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,c=function(){};return{s:c,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:c}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,i=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return s=e.done,e},e:function(e){i=!0,o=e},f:function(){try{s||null==n.return||n.return()}finally{if(i)throw o}}}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);