from fastapi import APIRouter, HTTPException, Depends, Query, Path
from typing import List, Optional, Dict, Any
from ..models.role import Role, RoleCreate, RoleUpdate, RoleResponse
from ..db.mongodb import db
from datetime import datetime
from ..utils.auth import verify_token
from ..role_tree import routes

router = APIRouter()

def generate_tree_data(routes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    return [
        {
            "title": route["name"],
            "key": route["access"],
            "children": generate_tree_data(route.get("routes", []))
        }
        for route in routes if "access" in route
    ]

@router.post("/api/roles", response_model=RoleResponse)
async def create_role(role: RoleCreate, current_user: dict = Depends(verify_token)):
    last_role = await db["roles"].find_one(sort=[("id", -1)])
    new_id = (last_role["id"] + 1) if last_role else 1

    role_dict = {
        "id": new_id,
        "name": role.name,
        "description": role.description,
        "created_at": datetime.now(),
        "created_by": current_user["id"],
        "deletable": True,
        "access": role.access if role.access else {}
    }

    result = await db["roles"].insert_one(role_dict)
    created_role = await db["roles"].find_one({"_id": result.inserted_id})
    return RoleResponse(**created_role)

@router.get("/api/roles", response_model=Dict[str, Any])
async def get_roles(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    name: Optional[str] = None,
    description: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {}
    if name:
        query["name"] = {"$regex": name, "$options": "i"}
    if description:
        query["description"] = {"$regex": description, "$options": "i"}

    roles = await db["roles"].find(query).skip(skip).limit(pageSize).to_list(pageSize)
    total = await db["roles"].count_documents(query)
    
    role_list = [RoleResponse(**role) for role in roles]
    
    return {
        "data": role_list,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

@router.get("/api/roles/{role_id}", response_model=RoleResponse)
async def get_role(role_id: int, current_user: dict = Depends(verify_token)):
    role = await db["roles"].find_one({"id": role_id})
    if role is None:
        raise HTTPException(status_code=404, detail="Role not found")
    return RoleResponse(**role)

@router.put("/api/roles/{role_id}", response_model=RoleResponse)
async def update_role(role_id: int, role: RoleUpdate, current_user: dict = Depends(verify_token)):
    existing_role = await db["roles"].find_one({"id": role_id})
    if existing_role is None:
        raise HTTPException(status_code=404, detail="Role not found")
    
    update_data = {k: v for k, v in role.model_dump(exclude_unset=True).items() if v is not None}
    if update_data:
        await db["roles"].update_one({"id": role_id}, {"$set": update_data})
    
    updated_role = await db["roles"].find_one({"id": role_id})
    return RoleResponse(**updated_role)

@router.delete("/api/roles/{role_id}")
async def delete_role(role_id: int, current_user: dict = Depends(verify_token)):
    role = await db["roles"].find_one({"id": role_id})
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")
    if not role.get("deletable", True):
        raise HTTPException(status_code=403, detail="This role cannot be deleted")
    
    result = await db["roles"].delete_one({"id": role_id})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Role not found")
    
    return {"id": role_id}

@router.get("/api/role/tree", response_model=List[Dict[str, Any]])
async def get_role_tree(current_user: dict = Depends(verify_token)):
    tree_data = generate_tree_data(routes)
    return tree_data