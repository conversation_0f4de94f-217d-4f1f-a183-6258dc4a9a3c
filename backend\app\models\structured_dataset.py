from mongoengine import Document, <PERSON>Field, DateTimeField, IntField, ListField, BooleanField, ObjectIdField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List
from bson import ObjectId
from enum import Enum

# 在文件开头添加枚举定义
class ProcessingStatus(str, Enum):
    PENDING = "pending"      # 待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"    # 已处理
    ERROR = "error"           # 出错

# MongoEngine 模型
class StructuredDataset(Document):
    meta = {
        'collection': 'structured_datasets'
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId)
    name = StringField(required=True)
    description = StringField()
    created_at = DateTimeField(default=datetime.now)
    deleted_at = DateTimeField(default=None)
    last_updated = DateTimeField(default=datetime.now)
    user_id = IntField(required=True)
    deleted_by = IntField(default=None)
    user_name = StringField()
    row_count = IntField(default=0)
    is_active = BooleanField(default=True)
    tags = ListField(StringField(), default=list)
    processing_status = StringField(
        choices=[status.value for status in ProcessingStatus],
        default=ProcessingStatus.PENDING.value
    )

# Pydantic 模型
class StructuredDatasetBase(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    user_id: int
    user_name: Optional[str] = None
    row_count: int = 0
    is_active: bool = True
    tags: List[str] = []
    processing_status: ProcessingStatus = ProcessingStatus.PENDING

class StructuredDatasetCreate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = []

class StructuredDatasetUpdate(BaseModel):
    name: Optional[str] = None
    processing_status: Optional[ProcessingStatus] = None
    description: Optional[str] = None
    row_count: Optional[int] = None
    is_active: Optional[bool] = None
    tags: Optional[List[str]] = None

class StructuredDatasetResponse(StructuredDatasetBase):
    created_at: datetime
    last_updated: datetime

    class Config:
        from_attributes = True
