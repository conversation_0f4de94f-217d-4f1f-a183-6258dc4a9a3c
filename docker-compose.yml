version: '1.0'

services:
  api:
    build: 
      context: .
      dockerfile: Dockerfile
    image: preloan-assistant-api
    extra_hosts:
      - "host.docker.internal:host-gateway"
    container_name: preloan-assistant-api
    restart: always
    environment:
      - ENV_MODE=prod
    command: uvicorn app.main:app --host 0.0.0.0 --port 8800
    ports:
      - "8800:8800"
    env_file:
      - .env.prod
    volumes:
      - ./static:/app/static
    depends_on:
      - mongodb
    networks:
      - app-network

  mongodb:
    image: mongo:4.4
    container_name: preloan-assistant-mongodb
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGODB_USER}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGODB_PASSWORD}
    volumes:
      - mongodb_data:/data/db
    networks:
      - app-network

volumes:
  mongodb_data:

networks:
  app-network:
    driver: bridge 