import subprocess
import time
import os
from pymongo import MongoClient
import logging
import json
import shutil
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,  # 设置日志级别
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",  # 设置日志格式
    handlers=[
        logging.FileHandler("app.log"),  # 输出到文件
        logging.StreamHandler()  # 输出到控制台
    ]
)

# 获取日志记录器
logger = logging.getLogger(__name__)

# 持久化路径
SAVE_PATH = "/app/data/tune/"
MODEL_PATH = SAVE_PATH + "models/"
OUTPUT_PATH = SAVE_PATH + "output/"
DATA_PATH = SAVE_PATH + "data"
# dataset_info.json文件路径
dataset_file_path = Path(SAVE_PATH + "data/dataset_info.json")
backup_file_path = dataset_file_path.with_suffix(".bak")  # 备份文件路径
temp_file_path = dataset_file_path.with_suffix(".tmp")  # 临时文件路径

# MongoDB 连接配置
# MONGO_URI = "mongodb://localhost:27017/"  # MongoDB 连接字符串
DATABASE_NAME = "model_tuning_db"         # 数据库名称
COLLECTION_NAME = "tuning_tasks"          # 集合名称

# 微调任务状态
TASK_STATUS_PENDING = "pending"  # 待处理
TASK_STATUS_RUNNING = "running"  # 运行中
TASK_STATUS_DONE = "done"        # 已完成
TASK_STATUS_FAILED = "failed"        # 失败

# 轮询间隔时间（秒）
POLL_INTERVAL = 60

def connect_to_mongo():
    """连接 MongoDB 数据库"""
    client = MongoClient("mongo", 27017, username="root", password="example", authSource="admin" )
    db = client[DATABASE_NAME]
    collection = db[COLLECTION_NAME]
    return collection

def find_pending_tasks(collection):
    """查找待处理的微调任务"""
    query = {"status": TASK_STATUS_PENDING}  # 查询状态为 pending 的任务
    tasks = list(collection.find(query))
    return tasks

def update_task_status(collection, task_id, status):
    """更新任务状态"""
    collection.update_one(
        {"_id": task_id},
        {"$set": {"status": status}}
    )

def format_tune_data(path):
    if dataset_file_path.exists():
        # 备份原始文件
        shutil.copyfile(dataset_file_path, backup_file_path)
        # 读取 JSON 文件
        with open(dataset_file_path, "r", encoding="utf-8") as f:
            data = json.load(f)  # 读取 JSON 数据
        data[path] = {
            "file_name": path + ".json",
            "formatting": "sharegpt",
            "columns": {
                "messages": "messages"
            },
            "tags": {
                "role_tag": "role",
                "content_tag": "content",
                "user_tag": "user",
                "assistant_tag": "assistant",
                "system_tag": "system"
            }
        }
        try:
            # 将数据写入临时文件
            with open(temp_file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
            # 写入成功后，将临时文件重命名为目标文件
            os.replace(temp_file_path, dataset_file_path)
        except Exception as e:
            # 如果发生错误，恢复备份文件
            if temp_file_path.exists():
                os.remove(temp_file_path)
            if backup_file_path.exists():
                os.replace(backup_file_path, dataset_file_path)
            logger.error(f"写入dataset_info.json文件时发生错误: {e}")
            raise  # 重新抛出异常
        finally:
            # 删除备份文件
            if backup_file_path.exists():
                os.remove(backup_file_path)
    else:
        raise FileNotFoundError(f"微调文件 {path} 不存在")


# 微调模型
def fine_tune_lora(task):
    logger.info("开始处理微调数据...")
    format_tune_data(task['DATA_NAME'])
    logger.info("开始 LoRA 微调...")
    command = [
        "llamafactory-cli", "train",
        "--model_name_or_path", MODEL_PATH + task['BASE_MODEL_PATH'],
        "--stage", "sft",
        "--do_train",
        "--finetuning_type", "lora",
        "--lora_target", "all",
        "--lora_rank", str(task['LORA_R']),
        "--lora_alpha", str(task['LORA_ALPHA']),
        "--lora_dropout", str(task['LORA_DROPOUT']),
        "--dataset_dir", DATA_PATH,
        "--dataset", task['DATA_NAME'],
        "--template", task['MODEL_TYPE'],
        "--cutoff_len", str(task['MAX_SEQ_LENGTH']),
        "--max_samples", "1000",
        "--overwrite_cache",
        "--preprocessing_num_workers", "16",
        "--output_dir", OUTPUT_PATH + task['_id'] + "/lora",
        "--logging_steps", "10",
        "--save_steps", "500",
        "--plot_loss",
        "--overwrite_output_dir",
        "--per_device_train_batch_size", str(task['BATCH_SIZE']),
        "--gradient_accumulation_steps", "8",
        "--learning_rate", str(task['LEARNING_RATE']),
        "--num_train_epochs", str(task['EPOCHS']),
        "--lr_scheduler_type", "cosine",
        "--warmup_ratio", "0.1",
        "--bf16",
        "--ddp_timeout", "180000000",
        "--val_size", "0.1",
        "--per_device_eval_batch_size", str(task['BATCH_SIZE']),
        "--eval_strategy", "steps",
        "--eval_steps", "500"
    ]
    result = subprocess.run(command, capture_output=True, text=True)
    if result.returncode == 0:
        logger.info("LoRA 微调完成！")
    else:
        logger.error(f"LoRA 微调失败！错误信息：{result.stderr}")
        

# 合并模型
def merge_model(task):
    logger.info("开始合并模型...")
    command = [
        "llamafactory-cli", "export",
        "--model_name_or_path", MODEL_PATH + task['BASE_MODEL_PATH'],
        "--adapter_name_or_path", OUTPUT_PATH + task['_id'] + "/lora",  # LoRA 适配器路径
        "--template", task['MODEL_TYPE'],
        "--export_size", str(2),
        "--export_device", "cpu",
        "--export_legacy_format", "False",
        "--export_dir", OUTPUT_PATH + task['_id'] + "/model"  # 合并后的模型输出目录
    ]
    result = subprocess.run(command, capture_output=True, text=True)
    if result.returncode == 0:
        logger.info("模型合并完成！")
    else:
        logger.error(f"模型合并失败！错误信息：{result.stderr}")
        

def fine_tune_model(task):
    """模拟微调模型"""
    logger.info(f"开始微调任务: {task['_id']}")
    # 创建输出目录
    os.makedirs(OUTPUT_PATH + task['_id'], exist_ok=True)
    # 执行 LoRA 微调
    fine_tune_lora(task)
    time.sleep(10)
    # 执行模型合并
    merge_model(task)
    time.sleep(10)
    logger.info(f"微调任务完成: {task['_id']}")

def main():
    # 连接 MongoDB
    collection = connect_to_mongo()
    while True:
        # 查找待处理的任务
        tasks = find_pending_tasks(collection)
        if tasks:
            for task in tasks:
                # 更新任务状态为 running
                update_task_status(collection, task["_id"], TASK_STATUS_RUNNING)
                try:
                    # 执行微调任务
                    fine_tune_model(task)
                    # 更新任务状态为 done
                    update_task_status(collection, task["_id"], TASK_STATUS_DONE)
                except Exception as e:
                    logger.error(f"微调任务失败: {task['_id']}, 错误: {e}")
                    # 更新任务状态为 failed（假设有一个 failed 状态）
                    update_task_status(collection, task["_id"], TASK_STATUS_FAILED)
        else:
            # 没有任务，等待几秒后再次查询
            logger.info("没有待处理的任务，等待中...")
            time.sleep(POLL_INTERVAL)

if __name__ == "__main__":
    main()
