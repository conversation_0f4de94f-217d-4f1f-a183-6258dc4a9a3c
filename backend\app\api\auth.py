from fastapi import Depends, HTTPException, status, Header
from datetime import datetime, timedelta
from ..db.mongodb import db
from dotenv import load_dotenv
import os
import secrets
from fastapi.responses import JSONResponse

# 加载 .env 文件
# load_dotenv()

# 获取环境变量
# API_TOKEN_EXPIRE_MINUTES = int(os.getenv("API_TOKEN_EXPIRE_MINUTES", "43200"))  # 默认30天

async def verify_api_token(authorization: str = Header(None)):
    """
    验证 API Token
    """
    if not authorization:
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Missing Authorization header"},
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    token = authorization.split(" ")[-1]  # 获取 Bearer token
    
    # 从 api_tokens 集合中查找 token
    token_doc = await db["api_tokens"].find_one({"token": token})

    if not token_doc:
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Invalid API token"},
            headers={"WWW-Authenticate": "Bearer"}
        )

    # 检查 token 是否过期
    if token_doc["expires_at"] < datetime.now():
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "API token has expired"},
            headers={"WWW-Authenticate": "Bearer"}
        )

    # 检查 token 是否被禁用
    if not token_doc.get("is_active", True):
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "API token is disabled"},
            headers={"WWW-Authenticate": "Bearer"}
        )

    return token_doc
