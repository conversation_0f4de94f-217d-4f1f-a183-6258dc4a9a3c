"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2911],{36977:function(e,t,n){n.r(t);var i=n(97857),r=n.n(i),l=n(15009),s=n.n(l),c=n(99289),o=n.n(c),a=n(19632),d=n.n(a),x=n(5574),p=n.n(x),h=n(67294),f=n(8232),u=n(2453),m=n(17788),y=n(71471),j=n(42075),g=n(83062),Z=n(83622),v=n(55102),k=n(63496),b=n(55241),P=n(78045),C=n(4393),w=n(11550),S=n(2487),z=n(11941),I=n(55287),T=n(15360),B=n(47389),R=n(51042),L=n(83456),O=n(82061),N=n(14409),_=n(77171),W=n(31545),D=n(97245),F=n(13520),A=n(70125),E=n(33914),M=n(36027),U=n(58638),G=n(60219),J=n(73480),V=n(60520),q=(n(74864),n(85893));t.default=function(){var e=(0,h.useRef)(!0),t=(0,h.useState)([]),n=p()(t,1)[0],i=(0,h.useState)("1"),l=p()(i,2),c=l[0],a=l[1],x=(0,h.useState)(null),K=p()(x,2),H=K[0],X=K[1],Y=(0,h.useState)(!1),Q=p()(Y,2),$=Q[0],ee=Q[1],te=(0,h.useState)([{key:"1",title:"第一章：简介",references:["智能概述指南","企业信贷标准手册","行业研究报告2023"],prompt:"概述报告的主要目的、适用范围和研究方法",children:[{key:"1-1",title:"1.1 背景",references:["基础信息提取文档","金融市场动态分析"],prompt:"介绍客户企业的行业背景、市场地位和经营状况"},{key:"1-2",title:"1.2 目的",references:["目标分析指南","行业标准文档","信贷评估手册"],prompt:"阐述评估的目的、范围和预期成果"}]},{key:"2",title:"第二章：主要内容",references:["内容分析指南","财务分析框架"],prompt:"详细阐述企业的财务状况、运营能力和风险点",children:[{key:"2-1",title:"2.1 数据分析",references:["数据分析模块文档","行业比较数据集"],prompt:"使用财务指标分析企业的偿债能力和盈利能力"},{key:"2-2",title:"2.2 风险评估",references:["风险评估模型指南","风控标准文档","案例分析库"],prompt:"评估潜在风险因素及其影响程度"}]},{key:"3",title:"第三章：结论",references:["结论生成指南","行业信贷建议文档"],prompt:"总结评估结果，提出信贷建议和风险防范措施"}]),ne=p()(te,2),ie=ne[0],re=ne[1],le=(0,h.useRef)(null),se=(0,h.useState)(null),ce=p()(se,2),oe=ce[0],ae=ce[1],de=(0,h.useState)(!1),xe=p()(de,2),pe=xe[0],he=xe[1],fe=(0,h.useState)(!1),ue=p()(fe,2),me=ue[0],ye=ue[1],je=(0,h.useState)(!1),ge=p()(je,2),Ze=ge[0],ve=ge[1],ke=(0,h.useState)([]),be=p()(ke,2),Pe=be[0],Ce=be[1],we=(0,h.useState)(!1),Se=p()(we,2),ze=Se[0],Ie=Se[1],Te=(0,h.useState)(null),Be=p()(Te,2),Re=Be[0],Le=Be[1],Oe=(0,h.useState)("template"),Ne=p()(Oe,2),_e=Ne[0],We=Ne[1],De=f.Z.useForm(),Fe=p()(De,1)[0],Ae=[{key:"template-1",title:"背景与目的",prompt:"描述项目背景、业务目标和评估目的"},{key:"template-2",title:"财务分析",prompt:"分析企业财务状况、现金流和盈利能力"},{key:"template-3",title:"风险评估",prompt:"评估潜在风险因素及其影响程度"},{key:"template-4",title:"结论与建议",prompt:"总结评估结果并提出具体建议"}],Ee=(0,h.useState)("金融风控报告"),Me=p()(Ee,2),Ue=Me[0],Ge=Me[1],Je=(0,h.useState)(!1),Ve=p()(Je,2),qe=Ve[0],Ke=Ve[1],He=(0,h.useState)(""),Xe=p()(He,2),Ye=Xe[0],Qe=Xe[1],$e=(0,h.useState)(!1),et=p()($e,2),tt=et[0],nt=et[1],it=(0,h.useState)(""),rt=p()(it,2),lt=rt[0],st=rt[1],ct=[{id:"template-1",title:"企业信贷风控报告",description:"综合评估企业信贷风险，包含财务分析、行业风险和还款能力评估",chapters:[{key:"t1-ch1",title:"企业基本情况",prompt:"描述企业基本信息、经营状况和市场地位",references:["企业工商信息","经营数据分析"],children:[{key:"t1-ch1-1",title:"企业概况",prompt:"企业规模、产品服务、市场占有率等基本情况",references:[]},{key:"t1-ch1-2",title:"行业分析",prompt:"企业所处行业现状和发展趋势",references:[]}]},{key:"t1-ch2",title:"财务状况分析",prompt:"分析企业财务报表和关键财务指标",references:["财务报表分析","行业数据对比"],children:[{key:"t1-ch2-1",title:"资产负债分析",prompt:"企业资产负债表分析和主要财务比率",references:[]},{key:"t1-ch2-2",title:"盈利能力分析",prompt:"收入、利润、毛利率等盈利指标分析",references:[]}]},{key:"t1-ch3",title:"风险评估与建议",prompt:"总结主要风险点并提出管控建议",references:["风险评估模型","行业风险指南"],children:[]}]},{id:"template-2",title:"项目可行性分析报告",description:"全面分析项目可行性，包含市场分析、财务预测和风险评估",chapters:[{key:"t2-ch1",title:"项目概述",prompt:"描述项目背景、目标和主要内容",references:["项目提案书","行业研究报告"],children:[]},{key:"t2-ch2",title:"市场分析",prompt:"分析目标市场规模、竞争格局和市场趋势",references:["市场调研数据","行业报告"],children:[]},{key:"t2-ch3",title:"财务预测",prompt:"项目投资、收益和回报周期分析",references:["财务模型","投资分析框架"],children:[]},{key:"t2-ch4",title:"风险评估",prompt:"识别项目风险并提出应对措施",references:["风险评估方法论"],children:[]}]}],ot=(0,h.useState)((function(){var e=localStorage.getItem("ai_report_versions");return e?JSON.parse(e):[]})),at=p()(ot,2),dt=at[0],xt=at[1];(0,h.useEffect)((function(){if(le.current&&!oe)try{var e=new V.cW({element:le.current,placeholder:"请输入或粘贴您要评估的报告内容..."});ae(e)}catch(e){console.error("编辑器初始化失败:",e)}return function(){oe&&oe.destroy()}}),[le.current,oe]);var pt,ht=function(){var t=o()(s()().mark((function t(){var n,i;return s()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(oe){t.next=3;break}return u.ZP.error("编辑器未初始化"),t.abrupt("return");case 3:try{n=0,i=setInterval((function(){e.current?(n+=10)>=100&&(clearInterval(i),u.ZP.success("评估完成")):clearInterval(i)}),500)}catch(e){console.error("评估失败:",e),u.ZP.error("评估失败，请重试")}case 4:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),ft=function(e){u.ZP.info("编辑章节: ".concat(e))},ut=function(e){var t=function e(t,n){return t.filter((function(t){return t.key!==n&&(t.children&&(t.children=e(t.children,n)),!0)}))}(d()(ie),e);re(t),u.ZP.success("删除成功")},mt=function(e){var t=function e(t,n){for(var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=0;r<t.length;r++){if(t[r].key===n)return{parent:i,index:r};if(t[r].children){var l=e(t[r].children,n,t[r]);if(l)return l}}return null}(ie,e);if(!t||t.index<=0)u.ZP.info("已经是第一个，无法上移");else{var n=d()(ie),i=t.parent?t.parent.children:n,r=i[t.index];i[t.index]=i[t.index-1],i[t.index-1]=r,re(n),u.ZP.success("上移成功")}},yt=function(e){var t=function e(t,n){for(var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=0;r<t.length;r++){if(t[r].key===n)return{parent:i,index:r};if(t[r].children){var l=e(t[r].children,n,t[r]);if(l)return l}}return null}(ie,e);if(t){var n=t.parent?t.parent.children:ie;if(t.index>=n.length-1)u.ZP.info("已经是最后一个，无法下移");else{var i=d()(ie),r=t.parent?t.parent.children:i,l=r[t.index];r[t.index]=r[t.index+1],r[t.index+1]=l,re(i),u.ZP.success("下移成功")}}},jt=function(e){X(e),ee(!0)},gt=function(){ee(!1),X(null)},Zt=function(e,t,n){return void 0!==t?void 0!==n?"".concat(t+1,".").concat(n+1," ").concat(e.title.replace(/^[\d.]+\s*/,"")):"第".concat((i=t+1,i>0&&i<=10?["一","二","三","四","五","六","七","八","九","十"][i-1]:i.toString()),"章 ").concat(e.title.replace(/^第[一二三四五六七八九十]+章[：:\s]*/,"")):e.title;var i},vt=function(e){Le(e),We("template"),Ie(!0),Fe.resetFields()},kt=function(e){We(e.target.value)},bt=function(e,t){var n={key:"".concat(Date.now()),title:e,prompt:t,references:[]},i=d()(ie);if(!Re)return i.push(n),re(i),Ie(!1),void u.ZP.success("添加一级节点成功");var r=i.findIndex((function(e){return e.key===Re}));if(-1!==r)i[r].children||(i[r].children=[]),i[r].children.push(n);else{for(var l=!1,s=0;s<i.length;s++){var c=i[s];if(c.children&&c.children.length>0){var o=c.children.findIndex((function(e){return e.key===Re}));if(-1!==o){c.children[o].children||(c.children[o].children=[]),c.children[o].children.push(n),l=!0;break}}}if(!l)return void u.ZP.warning("无法找到父节点，添加子节点失败")}re(i),Ie(!1),u.ZP.success("添加子节点成功")},Pt=function(){if("template"===_e){var e=Fe.getFieldValue("templateKey");if(!e)return void u.ZP.error("请选择一个模板");var t=Ae.find((function(t){return t.key===e}));if(!t)return;bt(t.title,t.prompt)}else Fe.validateFields().then((function(e){bt(e.title,e.prompt)})).catch((function(e){console.error("验证失败:",e)}))},Ct=function(){Qe(Ue),Ke(!0)},wt=function(){Ye.trim()?(Ge(Ye),Ke(!1),u.ZP.success("文档标题已更新")):u.ZP.error("标题不能为空")},St=function(){nt(!0)},zt=[{key:"1",label:"文档目录",children:(pt=ct.filter((function(e){return e.title.includes(lt)||e.description.includes(lt)})),(0,q.jsxs)("div",{className:"tab-content",children:[(0,q.jsxs)("div",{className:"document-title-area",children:[qe?(0,q.jsxs)("div",{style:{display:"flex",marginBottom:16},children:[(0,q.jsx)(v.Z,{value:Ye,onChange:function(e){return Qe(e.target.value)},placeholder:"请输入文档标题",style:{flex:1}}),(0,q.jsx)(Z.ZP,{type:"primary",onClick:wt,style:{marginLeft:8},children:"保存"}),(0,q.jsx)(Z.ZP,{onClick:function(){return Ke(!1)},style:{marginLeft:8},children:"取消"})]}):(0,q.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:16},children:[(0,q.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,q.jsx)(T.Z,{style:{fontSize:18,marginRight:8}}),(0,q.jsx)(y.Z.Title,{level:4,style:{margin:0},children:Ue})]}),(0,q.jsxs)(j.Z,{children:[(0,q.jsx)(g.Z,{title:"编辑标题",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(B.Z,{}),onClick:Ct})}),(0,q.jsx)(g.Z,{title:"选择文档模板",children:(0,q.jsx)(Z.ZP,{type:"primary",icon:(0,q.jsx)(R.Z,{}),onClick:St,children:"导入模版"})})]})]}),(0,q.jsx)("div",{style:{height:1,background:"#f0f0f0",marginBottom:16}})]}),(0,q.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:12},children:[(0,q.jsx)("h3",{style:{marginTop:0,marginBottom:0},children:"文档大纲"}),(0,q.jsx)(g.Z,{title:"添加一级节点",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(R.Z,{}),size:"small",style:{color:"#52c41a"},onClick:function(){return Le(null),We("template"),Ie(!0),void Fe.resetFields()}})})]}),(0,q.jsx)(k.Z,{treeData:ie.map((function(e,t){var n;return r()(r()({},e),{},{title:(0,q.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%",fontSize:"13px"},children:[(0,q.jsx)("span",{children:Zt(e,t)}),(0,q.jsxs)(j.Z,{size:2,children:[(0,q.jsx)(g.Z,{title:"添加子节点",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(L.Z,{}),size:"small",style:{color:"#52c41a"},onClick:function(t){t.stopPropagation(),vt(e.key)}})}),(0,q.jsx)(g.Z,{title:"查看详情",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(I.Z,{}),size:"small",style:{color:"#8c8c8c"},onClick:function(t){t.stopPropagation(),jt(e)}})}),(0,q.jsx)(g.Z,{title:"编辑",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(B.Z,{}),size:"small",style:{color:"#8c8c8c"},onClick:function(t){t.stopPropagation(),ft(e.key)}})}),(0,q.jsx)(g.Z,{title:"删除",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(O.Z,{}),size:"small",style:{color:"#8c8c8c"},onClick:function(t){t.stopPropagation(),ut(e.key)}})}),(0,q.jsx)(g.Z,{title:"上移",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(N.Z,{}),size:"small",style:{color:"#8c8c8c"},onClick:function(t){t.stopPropagation(),mt(e.key)}})}),(0,q.jsx)(g.Z,{title:"下移",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(_.Z,{}),size:"small",style:{color:"#8c8c8c"},onClick:function(t){t.stopPropagation(),yt(e.key)}})})]})]}),key:e.key,children:null===(n=e.children)||void 0===n?void 0:n.map((function(e,n){return r()(r()({},e),{},{title:(0,q.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%",fontSize:"13px"},children:[(0,q.jsx)("span",{children:Zt(e,t,n)}),(0,q.jsxs)(j.Z,{size:2,children:[(0,q.jsx)(g.Z,{title:"添加子节点",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(L.Z,{}),size:"small",style:{color:"#52c41a"},onClick:function(t){t.stopPropagation(),vt(e.key)}})}),(0,q.jsx)(g.Z,{title:"查看详情",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(I.Z,{}),size:"small",style:{color:"#8c8c8c"},onClick:function(t){t.stopPropagation(),jt(e)}})}),(0,q.jsx)(g.Z,{title:"编辑",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(B.Z,{}),size:"small",style:{color:"#8c8c8c"},onClick:function(t){t.stopPropagation(),ft(e.key)}})}),(0,q.jsx)(g.Z,{title:"删除",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(O.Z,{}),size:"small",style:{color:"#8c8c8c"},onClick:function(t){t.stopPropagation(),ut(e.key)}})}),(0,q.jsx)(g.Z,{title:"上移",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(N.Z,{}),size:"small",style:{color:"#8c8c8c"},onClick:function(t){t.stopPropagation(),mt(e.key)}})}),(0,q.jsx)(g.Z,{title:"下移",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(_.Z,{}),size:"small",style:{color:"#8c8c8c"},onClick:function(t){t.stopPropagation(),yt(e.key)}})})]})]}),key:e.key})}))})})),defaultExpandAll:!0,showLine:!0,className:"document-outline-tree",titleRender:function(e){var t=e;return(0,q.jsxs)("div",{style:{width:"100%"},children:[t.title,t.references&&t.references.length>0&&(0,q.jsx)(b.Z,{content:(0,q.jsx)("div",{children:(0,q.jsx)("ul",{style:{margin:"4px 0 0 0",padding:"0 0 0 16px"},children:t.references.map((function(e,t){return(0,q.jsx)("li",{children:(0,q.jsx)(y.Z.Text,{type:"secondary",children:e})},t)}))})}),title:"引用信息",trigger:"hover",children:(0,q.jsxs)(y.Z.Text,{type:"secondary",style:{marginLeft:8,fontSize:"12px",cursor:"pointer"},children:[t.references.length,"个引用"]})})]})}}),(0,q.jsx)(m.Z,{title:Re?"添加子节点":"添加一级节点",open:ze,onOk:Pt,onCancel:function(){return Ie(!1)},width:450,children:(0,q.jsxs)(f.Z,{form:Fe,layout:"vertical",children:[(0,q.jsx)("div",{style:{marginBottom:16},children:(0,q.jsx)(y.Z.Text,{type:"secondary",children:Re?"您将为所选节点创建一个子节点，该节点将显示在所选节点的下一级层次中。":"您将创建一个新的一级节点，该节点将显示在文档大纲的顶层。"})}),(0,q.jsx)(f.Z.Item,{name:"mode",initialValue:"template",children:(0,q.jsxs)(P.ZP.Group,{onChange:kt,value:_e,children:[(0,q.jsx)(P.ZP.Button,{value:"template",children:"使用模板"}),(0,q.jsx)(P.ZP.Button,{value:"manual",children:"手动添加"})]})}),"template"===_e?(0,q.jsx)(f.Z.Item,{name:"templateKey",label:"选择模板",rules:[{required:!0,message:"请选择一个模板"}],children:(0,q.jsx)(P.ZP.Group,{children:Ae.map((function(e){return(0,q.jsx)("div",{style:{marginBottom:12,padding:10,border:"1px solid #f0f0f0",borderRadius:4},children:(0,q.jsx)(P.ZP,{value:e.key,children:(0,q.jsxs)("div",{children:[(0,q.jsx)(y.Z.Text,{strong:!0,children:e.title}),(0,q.jsx)("div",{children:(0,q.jsx)(y.Z.Text,{type:"secondary",style:{fontSize:12},children:e.prompt})})]})})},e.key)}))})}):(0,q.jsxs)(q.Fragment,{children:[(0,q.jsx)(f.Z.Item,{name:"title",label:"节点标题",rules:[{required:!0,message:"请输入节点标题"}],children:(0,q.jsx)(v.Z,{placeholder:"请输入节点标题"})}),(0,q.jsx)(f.Z.Item,{name:"prompt",label:"提示词",rules:[{required:!0,message:"请输入提示词"}],children:(0,q.jsx)(v.Z.TextArea,{placeholder:"请输入提示词，用于指导AI生成内容",rows:4})})]})]})}),(0,q.jsxs)(m.Z,{title:"选择文档模板",open:tt,onCancel:function(){return nt(!1)},footer:null,width:720,children:[(0,q.jsxs)("div",{style:{marginBottom:16},children:[(0,q.jsx)(v.Z,{placeholder:"检索模板（支持标题和描述）",value:lt,onChange:function(e){return st(e.target.value)},allowClear:!0,prefix:(0,q.jsx)(W.Z,{style:{color:"#bfbfbf"}}),style:{width:300}}),(0,q.jsx)("div",{style:{fontSize:13,color:"#888",marginTop:6},children:"可快速导入结构化模板，助力高效生成专业报告"})]}),(0,q.jsxs)("div",{style:{maxHeight:400,overflowY:"auto",padding:"8px 0"},children:[0===pt.length&&(0,q.jsx)("div",{style:{color:"#888",padding:24,textAlign:"center"},children:"无匹配模板"}),(0,q.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:16},children:pt.map((function(e){return(0,q.jsxs)(C.Z,{hoverable:!0,style:{width:210,borderRadius:4,border:"1px solid #f0f0f0",background:"#fff",cursor:"pointer"},bodyStyle:{padding:12},onClick:function(){return function(e){m.Z.confirm({title:"应用文档模板",content:"应用新模板将替换当前的文档大纲，确定要继续吗？",onOk:function(){Ge(e.title),re(e.chapters),nt(!1),u.ZP.success("已应用文档模板")}})}(e)},children:[(0,q.jsx)("div",{style:{fontWeight:500,fontSize:14,color:"#222",marginBottom:8},children:e.title}),(0,q.jsx)("div",{style:{color:"#666",fontSize:12,marginBottom:8,lineHeight:1.5,height:36,overflow:"hidden",textOverflow:"ellipsis",display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical"},children:e.description}),(0,q.jsxs)("div",{style:{fontSize:12,color:"#1890ff"},children:[(0,q.jsx)(T.Z,{style:{marginRight:4}}),e.chapters.length," 个章节"]})]},e.id)}))})]})]}),(0,q.jsx)("style",{children:"\n            .document-outline-tree .ant-tree-node-content-wrapper {\n              font-size: 13px;\n            }\n            .document-outline-tree .ant-tree-switcher {\n              font-size: 13px;\n            }\n            .document-outline-tree .ant-tree-title {\n              width: 100%;\n            }\n          "}),(0,q.jsx)(m.Z,{title:"章节详情",open:$,onCancel:gt,footer:[(0,q.jsx)(Z.ZP,{onClick:gt,children:"关闭"},"close")],width:500,children:H&&(0,q.jsxs)("div",{children:[(0,q.jsx)(y.Z.Title,{level:4,style:{marginTop:0},children:Zt(H,ie.findIndex((function(e){return e.key===H.key})),ie.some((function(e){return e.children&&e.children.some((function(e){return e.key===H.key}))}))?ie.findIndex((function(e){return e.children&&e.children.some((function(e){return e.key===H.key}))})):void 0)}),H.prompt&&(0,q.jsxs)("div",{style:{marginBottom:16},children:[(0,q.jsx)(y.Z.Title,{level:5,style:{marginTop:16},children:"提示词"}),(0,q.jsx)(y.Z.Paragraph,{children:H.prompt})]}),H.references&&H.references.length>0&&(0,q.jsxs)("div",{children:[(0,q.jsx)(y.Z.Title,{level:5,style:{marginTop:16},children:"引用信息"}),(0,q.jsx)("ul",{style:{paddingLeft:20},children:H.references.map((function(e,t){return(0,q.jsx)("li",{children:(0,q.jsx)(y.Z.Text,{children:e})},t)}))})]})]})})]}))},{key:"2",label:"版本管理",children:(0,q.jsxs)("div",{className:"tab-content",children:[(0,q.jsx)("h3",{style:{marginTop:0},children:"版本管理"}),0===dt.length&&(0,q.jsx)("p",{children:"暂无历史版本"}),(0,q.jsx)("ul",{style:{padding:0,listStyle:"none"},children:dt.map((function(e,t){return(0,q.jsx)("li",{style:{border:"1px solid #f0f0f0",borderRadius:4,marginBottom:12,padding:12,background:"#fafafa"},children:(0,q.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,q.jsxs)("div",{children:[(0,q.jsxs)("div",{style:{fontWeight:500},children:["保存时间：",e.time]}),(0,q.jsx)("div",{style:{color:"#888",fontSize:13,marginTop:4,maxWidth:400,whiteSpace:"pre",overflow:"hidden",textOverflow:"ellipsis"},children:e.content.slice(0,100)||"（空内容）"})]}),(0,q.jsx)(Z.ZP,{size:"small",type:"primary",onClick:function(){return t=e,void(oe?m.Z.confirm({title:"确认恢复此版本？",content:(0,q.jsxs)("div",{children:[(0,q.jsx)("div",{children:"恢复后当前内容将被覆盖。"}),(0,q.jsxs)("div",{style:{marginTop:8,color:"#888"},children:["保存时间：",t.time]})]}),onOk:function(){oe.setMarkdown(t.content),u.ZP.success("已恢复到该版本")}}):u.ZP.error("编辑器未初始化"));var t},children:"恢复"})]})},t)}))})]})},{key:"3",label:"数据配置",children:(0,q.jsxs)("div",{className:"tab-content",children:[(0,q.jsx)("h3",{style:{marginTop:0},children:"数据配置"}),(0,q.jsxs)("div",{style:{marginBottom:16},children:[(0,q.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:6},children:[(0,q.jsxs)(y.Z.Title,{level:5,style:{margin:0,fontSize:"14px"},children:[(0,q.jsx)(D.Z,{style:{marginRight:6}}),"文件"]}),(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(R.Z,{}),size:"small",onClick:function(){return he(!0)}})]}),(0,q.jsxs)("div",{style:{marginBottom:16},children:[(0,q.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:6},children:[(0,q.jsxs)(y.Z.Title,{level:5,style:{margin:0,fontSize:"14px"},children:[(0,q.jsx)(F.Z,{style:{marginRight:6}}),"知识库"]}),(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(R.Z,{}),size:"small",onClick:function(){return ye(!0)}})]}),(0,q.jsxs)("div",{children:[(0,q.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:6},children:[(0,q.jsxs)(y.Z.Title,{level:5,style:{margin:0,fontSize:"14px"},children:[(0,q.jsx)(A.Z,{style:{marginRight:6}}),"MCP服务"]}),(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(R.Z,{}),size:"small",onClick:function(){return ve(!0)}})]}),(0,q.jsxs)(m.Z,{title:"添加文件",open:pe,onCancel:function(){he(!1),Ce([])},footer:[(0,q.jsx)(Z.ZP,{onClick:function(){he(!1),Ce([])},children:"取消"},"cancel"),(0,q.jsx)(Z.ZP,{type:"primary",disabled:0===Pe.length,onClick:function(){u.ZP.success("已添加 ".concat(Pe.length," 个文件")),he(!1),Ce([])},children:"确认添加"},"upload")],width:600,children:[(0,q.jsx)("div",{style:{marginBottom:16},children:(0,q.jsxs)(w.Z.Dragger,{multiple:!0,fileList:Pe,beforeUpload:function(e){return![".pdf",".docx",".csv",".xls",".xlsx",".json",".txt"].some((function(t){return e.name.toLowerCase().endsWith(t)}))&&(u.ZP.error("仅支持 PDF、Word、Excel、CSV、JSON 和 TXT 格式的文件"),w.Z.LIST_IGNORE)},onChange:function(e){Ce(e.fileList),e.fileList.length>10&&(u.ZP.warning("最多只能上传10个文件"),Ce(e.fileList.slice(0,10)))},onRemove:function(e){var t=Pe.indexOf(e),n=Pe.slice();n.splice(t,1),Ce(n)},children:[(0,q.jsx)("p",{className:"ant-upload-drag-icon",children:(0,q.jsx)(E.Z,{style:{fontSize:48,color:"#1890ff"}})}),(0,q.jsx)("p",{className:"ant-upload-text",children:"点击或拖拽文件到此区域上传"}),(0,q.jsx)("p",{className:"ant-upload-hint",style:{color:"#888"},children:"支持单个或批量上传。支持格式: PDF、Word、Excel、CSV、JSON、TXT"})]})}),Pe.length>0&&(0,q.jsx)("div",{style:{marginTop:16},children:(0,q.jsxs)("div",{style:{fontWeight:500,marginBottom:8},children:["已选择 ",Pe.length," 个文件"]})})]}),(0,q.jsx)(m.Z,{title:"选择知识库",open:me,onOk:function(){},onCancel:function(){return ye(!1)},children:(0,q.jsx)(S.Z,{size:"small",bordered:!0,dataSource:[{id:"demo-kb-1",name:"企业信贷知识库"},{id:"demo-kb-2",name:"行业分析库"},{id:"demo-kb-3",name:"政策法规库"}],renderItem:function(e){return(0,q.jsx)(S.Z.Item,{children:(0,q.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,q.jsx)(F.Z,{style:{color:"#722ed1",marginRight:8}}),(0,q.jsx)(y.Z.Text,{children:e.name})]})})}})}),(0,q.jsx)(m.Z,{title:"选择MCP服务",open:Ze,onOk:function(){},onCancel:function(){return ve(!1)},children:(0,q.jsx)(S.Z,{size:"small",bordered:!0,dataSource:[{id:"demo-mcp-1",name:"数据分析引擎"},{id:"demo-mcp-2",name:"风险评估模型"},{id:"demo-mcp-3",name:"行业对标服务"}],renderItem:function(e){return(0,q.jsx)(S.Z.Item,{children:(0,q.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,q.jsx)(M.Z,{style:{color:"#52c41a",marginRight:8}}),(0,q.jsx)(y.Z.Text,{children:e.name})]})})}})}),(0,q.jsx)("style",{children:"\n                  .data-source-list {\n                    display: flex;\n                    flex-direction: column;\n                    gap: 4px;\n                  }\n                  .data-source-item {\n                    display: flex;\n                    align-items: center;\n                    justify-content: space-between;\n                    padding: 6px 8px;\n                    background-color: #f5f5f5;\n                    border-radius: 4px;\n                  }\n                  .data-source-item:hover {\n                    background-color: #f0f0f0;\n                  }\n                "})]})]})]})]})}];return(0,q.jsxs)("div",{style:{display:"flex",flexDirection:"column",height:"100vh",width:"100%"},children:[(0,q.jsxs)("div",{style:{padding:"8px 16px",borderBottom:"1px solid #f0f0f0",width:"100%",display:"flex",justifyContent:"space-between",backgroundColor:"#fafafa"},children:[(0,q.jsxs)(j.Z,{children:[(0,q.jsx)(y.Z.Title,{level:4,style:{margin:0},children:"AI报告生成"}),0===n.length?null:(0,q.jsxs)(y.Z.Text,{children:[(0,q.jsx)(I.Z,{style:{marginRight:"4px"}}),n.length,"条适用规则"]})]}),(0,q.jsxs)(j.Z,{children:[(0,q.jsxs)(Z.ZP.Group,{children:[(0,q.jsx)(g.Z,{title:"导出",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(U.Z,{}),onClick:function(){if(oe)try{var e=oe.getMarkdown(),t=new Blob([e],{type:"text/markdown"}),n=URL.createObjectURL(t),i=document.createElement("a");i.href=n,i.download="AI报告_".concat((new Date).toLocaleDateString().replace(/\//g,"-"),".md"),document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(n),u.ZP.success("导出成功")}catch(e){console.error("导出失败:",e),u.ZP.error("导出失败，请重试")}else u.ZP.error("编辑器未初始化")}})}),(0,q.jsx)(g.Z,{title:"保存",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(G.Z,{}),onClick:function(){if(oe)try{var e=oe.getMarkdown();localStorage.setItem("ai_report_content",e);var t=[{content:e,time:(new Date).toLocaleString()}].concat(d()(dt)).slice(0,20);xt(t),localStorage.setItem("ai_report_versions",JSON.stringify(t)),u.ZP.success("保存成功")}catch(e){console.error("保存失败:",e),u.ZP.error("保存失败，请重试")}else u.ZP.error("编辑器未初始化")}})}),(0,q.jsx)(g.Z,{title:"预览",children:(0,q.jsx)(Z.ZP,{type:"text",icon:(0,q.jsx)(W.Z,{}),onClick:function(){if(oe)try{ht(),a("2")}catch(e){console.error("预览失败:",e),u.ZP.error("预览失败，请重试")}else u.ZP.error("编辑器未初始化")}})})]}),(0,q.jsx)(g.Z,{title:"报告生成",children:(0,q.jsx)(Z.ZP,{type:"primary",icon:(0,q.jsx)(J.Z,{}),onClick:ht,children:"报告生成"})})]})]}),(0,q.jsxs)("div",{style:{display:"flex",flex:1,overflow:"hidden"},children:[(0,q.jsx)("div",{style:{flex:"0 0 60%",overflow:"auto",padding:"0"},children:(0,q.jsx)("div",{ref:le,style:{height:"calc(100% - 32px)",backgroundColor:"#fff"}})}),(0,q.jsxs)("div",{style:{flex:"0 0 40%",overflow:"auto",height:"100%",padding:"16px",borderLeft:"1px solid #f0f0f0",backgroundColor:"#fff"},children:[(0,q.jsx)(z.Z,{activeKey:c,onChange:a,items:zt,type:"card",style:{height:"100%"},className:"report-tabs"}),(0,q.jsx)("style",{children:"\n              .tab-content {\n                padding: 16px;\n                background-color: white;\n                border: 1px solid #f0f0f0;\n                border-top: none;\n                height: 100%;\n                border-radius: 0 0 2px 2px;\n                min-height: 200px;\n                box-shadow: 0 1px 2px rgba(0,0,0,0.03);\n              }\n              .report-tabs .ant-tabs-nav {\n                margin-bottom: 0;\n              }\n              .report-tabs .ant-tabs-tab {\n                border-radius: 4px 4px 0 0;\n              }\n              .report-tabs .ant-tabs-tab-active {\n                background-color: white !important;\n              }\n              .report-tabs .ant-tabs-content {\n                height: 100%;\n                padding-bottom: 16px;\n              }\n            "})]})]})]})}}}]);