{"cells": [{"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["config = {\n", "    \"elasticsearch\":{\n", "    \"host\": \"jr.roardata.cn\",\n", "    \"port\": 8801,\n", "    \"username\": \"jinxu\",\n", "    \"password\": \"WiseWeb@123\",\n", "    \"index\":\"media_level_wiseweb_crawler_website\"\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import requests"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def get_index_mapping(config):\n", "    \"\"\"获取ES索引的映射信息\n", "    \n", "    Args:\n", "        config: ES配置信息,包含host,port,username,password等\n", "    \n", "    Returns:\n", "        dict: 索引映射信息\n", "    \"\"\"\n", "    try:\n", "        print(config)\n", "        # 构建请求URL\n", "        url = f\"http://{config['elasticsearch']['host']}:{config['elasticsearch']['port']}/{config['elasticsearch']['index']}/_mapping\"\n", "        print(url)\n", "        # 发送GET请求获取映射信息\n", "        response = requests.get(\n", "            url,\n", "            auth=(config['elasticsearch']['username'], config['elasticsearch']['password'])\n", "        )\n", "        \n", "        # 检查响应状态\n", "        response.raise_for_status()\n", "        \n", "        # 返回JSON格式的映射信息\n", "        return response.json()\n", "        \n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"获取索引映射失败: {str(e)}\")\n", "        return None\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'elasticsearch': {'host': '**********', 'port': 9200, 'username': 'jinxu', 'password': 'WiseWeb@123', 'index': 'media_level_wiseweb_crawler_website'}}\n", "http://**********:9200/media_level_wiseweb_crawler_website/_mapping\n", "获取索引映射失败: 401 Client Error: Unauthorized for url: http://**********:9200/media_level_wiseweb_crawler_website/_mapping\n", "None\n"]}], "source": ["print(get_index_mapping(config))"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["class ESTool:\n", "    def __init__(self):\n", "        self.host = config[\"elasticsearch\"][\"host\"]\n", "        self.port = config[\"elasticsearch\"][\"port\"]\n", "        self.username = config[\"elasticsearch\"][\"username\"]\n", "        self.password = config[\"elasticsearch\"][\"password\"]\n", "       \n", "    def search(self, index, query):\n", "        url = \"http://\"+ self.host+\":\"+ str(self.port)+\"/\"+index+\"/_search\"\n", "        headers = {\n", "                'Content-Type': 'application/json',\n", "            }\n", "#         print(url)\n", "#         print(query)\n", "        response = requests.post(url, json=query, headers=headers,auth=(self.username, self.password))\n", "        return response\n", "        # 在 ESTool 类中添加以下方法\n", "    def search_with_bool_query(self, index, credit_code, score_gte, publish_time_gte, publish_time_lte, tendency):\n", "        url = \"http://\"+ self.host+\":\"+self.port+\"/\"+index+\"/_search\"\n", "        query = {\n", "            \"query\": {\n", "                \"bool\": {\n", "                    \"must\": [\n", "                        {\n", "                            \"nested\": {\n", "                                \"path\": \"correlationOrg\",\n", "                                \"query\": {\n", "                                    \"bool\": {\n", "                                        \"must\": [\n", "                                            {\n", "                                                \"term\": {\n", "                                                    \"correlationOrg.creditCode.keyword\": {\n", "                                                        \"value\": credit_code\n", "                                                    }\n", "                                                }\n", "                                            },\n", "                                            {\n", "                                                \"range\": {\n", "                                                    \"correlationOrg.score\": {\n", "                                                        \"gte\": score_gte\n", "                                                    }\n", "                                                }\n", "                                            }\n", "                                        ]\n", "                                    }\n", "                                }\n", "                            }\n", "                        },\n", "                        {\n", "                            \"range\": {\n", "                                \"publishtime\": {\n", "                                    \"gte\": publish_time_gte,\n", "                                    \"lte\": publish_time_lte\n", "                                }\n", "                            }\n", "                        },\n", "                        {\n", "                            \"match\": {\n", "                                \"tendency\": tendency\n", "                            }\n", "                        }\n", "                    ]\n", "                }\n", "            }\n", "        }\n", "        return self.search(index, query)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["query  = {\n", "\t\"index\": \"media_level_wiseweb_crawler_website_child\",\n", "\t\"query\": {\n", "\t\t\"_source\": [\n", "\t\t\t\"site_url\",\n", "\t\t\t\"title\",\n", "\t\t\t\"publishtime\",\n", "\t\t\t\"tag\",\n", "\t\t\t\"url\",\n", "\t\t\t\"content\"\n", "\t\t],\n", "\t\t\"size\": 5,\n", "\t\t\"query\": {\n", "\t\t\t\"bool\": {\n", "\t\t\t\t\"should\": [\n", "\t\t\t\t\t{\n", "\t\t\t\t\t\t\"match\": {\n", "\t\t\t\t\t\t\t\"title\": \"你好工商银行\"\n", "\t\t\t\t\t\t}\n", "\t\t\t\t\t},\n", "\t\t\t\t\t{\n", "\t\t\t\t\t\t\"match\": {\n", "\t\t\t\t\t\t\t\"content\": \"你好工商银行\"\n", "\t\t\t\t\t\t}\n", "\t\t\t\t\t}\n", "\t\t\t\t]\n", "\t\t\t}\n", "\t\t},\n", "\t\t\"sort\": [\n", "\t\t\t{\n", "\t\t\t\t\"publishtime\": {\n", "\t\t\t\t\t\"order\": \"desc\"\n", "\t\t\t\t}\n", "\t\t\t}\n", "\t\t]\n", "\t}\n", "}"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["http://jr.roardata.cn:8801/media_level_wiseweb_crawler_website/_search\n"]}], "source": ["url = f\"http://{config['elasticsearch']['host']}:{config['elasticsearch']['port']}/{config['elasticsearch']['index']}/_search\"\n", "print(url)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["\n", "headers = {\n", "    'Content-Type': 'application/json',\n", "}\n", "#         print(url)\n", "#         print(query)\n", "# response = requests.post(url, json=query, headers=headers,auth=(self.username, self.password))\n", "\n", "response = requests.get(\n", "            url,\n", "            json=query['query'],\n", "            headers=headers,\n", "            auth=(config['elasticsearch']['username'], config['elasticsearch']['password'])\n", ")"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\"took\":342,\"timed_out\":false,\"_shards\":{\"total\":8,\"successful\":8,\"skipped\":0,\"failed\":0},\"hits\":{\"total\":{\"value\":10000,\"relation\":\"gte\"},\"max_score\":null,\"hits\":[{\"_index\":\"media_level_wiseweb_crawler_website\",\"_type\":\"_doc\",\"_id\":\"6439408916588047507\",\"_score\":null,\"_source\":{\"site_url\":\"http://news.fznews.com.cn/\",\"publishtime\":\"2025-03-02 23:01:32\",\"tag\":[],\"title\":\"成为他们生命里的一束光——福建麒麟救援队找回千名走失者背后的故事\",\"content\":\"“如果你看到麒麟救援队认证过的寻人帖，请果断转发，不要犹豫，也许就能救回一条生命。”福建麒麟救援队理事长金丽芬说。\\\\r\\\\n3月1日上午，记者在福建省麒麟减防灾救援服务中心（以下简称“麒麟救援队”）见到身穿红色制服的救援队员们时，他们正在讨论策划学雷锋纪念日的活动。多年来，他们的足迹遍布城市的大街小巷，成功帮助1003名走失市民找到回家路。\\\\r\\\\n<img src=\\\\\"https://image.fznews.com.cn/app/pic/2025-03/02/539001_727c96a5-39d2-4d93-bc7a-3ba59ec6c71c.jpg\\\\\">救援队队员与志愿者在清点救援物资。记者 陈君沂 摄\\\\r\\\\n感受光 成为光\\\\r\\\\n“曾经，救援队就像一束光，给绝望的我带来了希望。如今，我也想成为别人的光。”队员张喜欢谈及自己加入救援队的原因时说。\\\\r\\\\n2019年，张喜欢患有阿尔茨海默病的二姨走丢了，家人们找了一天都没有结果。第二天，一位邻居向她推荐了福建麒麟救援队，在专业寻“失”志愿队伍的帮助下，走失了七天的二姨终于被找到了。\\\\r\\\\n这次寻人的亲身经历，让张喜欢萌生了助人的念头。2023年，张喜欢正式加入福建麒麟救援队，参与各个城市寻人志愿项目。她还是队内的宣传好手，擅长拍摄与剪辑，通过镜头记录下一个个感人的寻人故事，发到微信视频号上，把温暖传播到更多地方。\\\\r\\\\n麒麟救援队的成立，也是源于“一束光”。\\\\r\\\\n2016年7月，金丽芬以志愿者身份参加了闽清县台风“尼伯特”洪灾救援。“以前都觉得灾难离我们很远，当真的发生在身边时，才真正感受到‘助人’的意义。”金丽芬回忆道，她从受助者的眼里看到了希望和感激，志愿者们就像一束光一样，照进了他们的心中。“平凡的人聚在一起，可以做出不平凡的事，那我们为什么不去做呢？”\\\\r\\\\n金丽芬找到了参加过抢险救灾、志同道合的志愿者们，将大家组织起来，麒麟救援队于2016年11月成立。成立至今，麒麟救援队已拥有80名救援队员、近2700名志愿者。大家职业不同，年龄不同，但只要一声令下，他们就会放下手上的事，立刻化身为“一束光”。\\\\r\\\\n换位思考 寻人救命\\\\r\\\\n2017年3月，一起寻找走失孤独症孩子的事件，让金丽芬产生组建“城市寻人”团队的念头。同年7月，麒麟救援城市搜救大队正式建立。\\\\r\\\\n颜承恩是城市搜救大队大队长。因为他有一双能很快发现走失人员的锐利双眼，队员们都叫他“雕哥”。颜承恩的妻子陈碧珍是一名寻人志愿者。在救援队，他们被称作“神雕侠侣”。\\\\r\\\\n“找人能有多难？”颜承恩坦言，刚加入救援队时，他天真地认为这是一件很简单的事情，但在参与寻人救援行动中，他深深体会到这项任务的艰巨性。\\\\r\\\\n那是2019年的中旬，天气炎热，一名小伙子带着老父亲，从莆田来福州的医院做化疗。为了陪伴病重的老伴，他的母亲也执意要同行。一天，母亲只身一人离开医院，上街溜达，却不小心迷了路，小伙子背着写有寻人启事的木板，在街头疯狂又无助地问人。\\\\r\\\\n“收到求助电话后，我们也花了很多时间寻找。最后在一个建筑工地的角落找到老人家时，人已经走了很久了。病床上的老人听到老伴离世的消息，没过两天也离开了。”颜承恩沉重的语气带着一丝哽咽，“如果我们能快点，再快点，是不是就能避免灾祸发生，他们不应该是这样的结局”。\\\\r\\\\n“如果我是他/她，我会去哪里？”这是颜承恩在寻人前必问自己的一个问题，“换位思考，反其道而行”成为他的寻人秘诀。\\\\r\\\\n“我们需要寻找的走失者，大多数是孤独症儿童或患有阿尔茨海默病的老人。他们和我们有着不同的思维，会漫无目的地乱逛，甚至可能坐上不同的交通工具在城市间穿行，等我们赶到他最后一次出现的地方时，他可能已经离开了好远。”陈碧珍说，孤独症儿童更有亲水性，如果寻找不及时，很可能发生溺水事故。\\\\r\\\\n对于麒麟救援队来说，每一次寻人都是在救命，也是在挽救一个家庭。\\\\r\\\\n多方携手 同献爱心\\\\r\\\\n在一次次地复盘之后，救援队不断完善管理制度、明确岗位分工、改进搜救流程，核实走失人员信息后，他们会给不同的寻人任务划分不同等级，并发布寻人帖，接着安排队员、志愿者进行搜救、传播等工作。\\\\r\\\\n“寻人这件事，不只有我们救援队在做。”金丽芬告诉记者，麒麟救援队联动了40多家环卫公司，建立起了洁哥洁嫂寻人群，共有近8000名志愿者。“环卫工人工作很辛苦，需要被关爱。但他们也在用自己的爱心反哺社会，他们是寻人的重要力量。”\\\\r\\\\n2019年，麒麟救援队得到了福州公交的支持，全面打通了公交监控网，也获得了很多志愿者和好心人士的帮助。金丽芬向记者展示了一个名为“城市寻人公交助力组”的微信群聊，有寻人需要时，大家会在群里及时沟通。\\\\r\\\\n“公益无大小，人人都可以成为志愿者，哪怕是转发一次寻人启事，又或是见到走失老人时打一个求助电话，都有可能成为他人的救命稻草。”福建麒麟救援队总队长张光盛说，希望广大市民在路上遇到行为异常的人时，可以先报警，再找救援队，并多多留意对方的行踪，避免出现意外。（记者 陈君沂）\",\"url\":\"https://news.fznews.com.cn/fzxw/20250302/Yv7t81ne1p.shtml\"},\"sort\":[1740956492000]},{\"_index\":\"media_level_wiseweb_crawler_website\",\"_type\":\"_doc\",\"_id\":\"-6006776455332951678\",\"_score\":null,\"_source\":{\"site_url\":\"http://news.fznews.com.cn/\",\"publishtime\":\"2025-03-02 23:01:32\",\"tag\":[\"12393\"],\"title\":\"通过“驾照式记分”守护医保“钱袋子” 记满12分终止医保支付资格\",\"content\":\"省医保局日前会同省卫健委、省药监局印发《福建省定点医药机构相关人员医保支付资格管理实施细则》（以下简称《实施细则》），自3月1日起施行。此举意味着福建省通过“驾照式记分”守护医保“钱袋子”。\\\\r\\\\n《实施细则》明确医保支付资格管理对象为两大类人员：定点医疗机构为参保人提供使用医保基金结算的医药服务的医疗类、药学类、护理类、技术类等卫生专业技术人员，负责医疗费用和医保基金结算审核的工作人员；定点零售药店为参保人提供医保基金结算的医药服务的主要负责人，即药品经营许可证上的主要负责人。\\\\r\\\\n《实施细则》将记分范围严格限定在相关人员所在医药机构受到行政处罚和相对较重的协议处理之后，才根据相关责任人员的行为性质和负有责任程度对其予以记分。记分档次共有4档，分为：记1分至3分的情形针对轻微违法违规行为；记4分至6分的情形针对一般违法违规行为；记7分至9分的情形针对较重违法违规行为，主要包括为非登记备案相关人员，或登记备案状态异常的相关责任人员冒名提供医保基金结算；记10分至12分的情形针对严重违法违规行为，主要包括所在定点医药机构或科室因欺诈骗保受到行政处罚，该人员负有责任的等。一次性记分达到12分的，终止医保支付资格，终止之日起3年内不得再次登记备案。\\\\r\\\\n记者了解到，开展医保支付资格管理，不影响参保人就医购药。（记者 李晖）\",\"url\":\"https://news.fznews.com.cn/fzxw/20250302/46ZzYCHBN8.shtml\"},\"sort\":[1740956492000]},{\"_index\":\"media_level_wiseweb_crawler_website\",\"_type\":\"_doc\",\"_id\":\"-7292347663186242247\",\"_score\":null,\"_source\":{\"site_url\":\"http://jx.cnr.cn/\",\"publishtime\":\"2025-03-02 22:46:55\",\"tag\":[],\"title\":\"全省科技工作会议在江西南昌召开\",\"content\":\"央广网南昌3月2日消息（记者周蓓）3月1日，2025年全省科技工作会议在江西南昌召开。省政府副省长、省委科技委副主任夏文勇出席并讲话。省政府副秘书长熊科平主持会议。省委科技委办公室主任，省科技厅党组书记、厅长宋德雄作工作报告。\\\\r\\\\n<img src=\\\\\"https://mediabluk.cnr.cn/img/cnr/CNRCDP/2025/0301/e6462c345a1a9174083651619299196211.jpg?auth=6096532a92db7f1d9ac6d80f2f11a623\\\\\">\\\\r\\\\n2025年全省科技工作会议现场（央广网记者  周蓓  摄）\\\\r\\\\n2024年，在江西省委、省政府坚强领导下，全省科技系统大力推进科技兴赣六大行动，推动科技创新和产业创新深度融合，着力提升科技创新驱动力，全省科技创新呈现向上向好的发展态势。全省综合科技创新水平指数提升至64.52%，增幅居全国第2位。2023年度江西省研发投入总额突破600亿元大关，研发投入强度达到1.88%，创“十四五”期间最高增速。\\\\r\\\\n夏文勇对2024年全省科技工作给予充分肯定。他指出，2025年是“十四五”的收官之年，也是谋划“十五五”的关键之年，面对新形势新任务新要求，全省科技系统要打头阵，要结合各地各单位实际，细化分解工作任务，加快以科技创新推动产业创新，力争在新一轮区域竞争中抢占先机，赢得主动。\\\\r\\\\n会上，鹰潭市人民政府、华东交通大学、南昌航空大学、赣州高新技术产业开发区管委会、（同济大学）南昌智能新能源汽车研究院、晶科能源股份有限公司等6家单位围绕集聚创新资源，推进产学研深度融合，以科技创新引领现代化产业体系建设等方面作了交流发言。\\\\r\\\\n各设区市政府、赣江新区管委会分管负责同志，各设区市科技局、赣江新区创发局主要负责同志，国家级创新型县（市、区）科技部门主要负责同志，研究型建设高校负责同志，部分科技园区、科研机构、企业负责同志，在赣全国重点实验室、国家实验室创新中心、省实验室负责同志参加会议。\",\"url\":\"http://jx.cnr.cn/yw/20250302/t20250302_527085924.shtml\"},\"sort\":[1740955615000]},{\"_index\":\"media_level_wiseweb_crawler_website\",\"_type\":\"_doc\",\"_id\":\"-575910689392673771\",\"_score\":null,\"_source\":{\"site_url\":\"http://jx.cnr.cn/\",\"publishtime\":\"2025-03-02 22:46:55\",\"tag\":[],\"title\":\"“投资新余”品牌发布\",\"content\":\"央广网新余3月2日消息（记者周蓓）3月1日，“投资新余”启动仪式暨2025新余市“双招双引”与服务一体化发展大会在江西新余举行。\\\\r\\\\n<img src=\\\\\"https://mediabluk.cnr.cn/img/cnr/CNRCDP/2025/0301/3b7acb36704b1174084271500438383511.jpg?auth=99ac3ecd8d984e8adb24ede994db92cf\\\\\">\\\\r\\\\n活动现场（央广网记者  周蓓  摄）\\\\r\\\\n启动仪式发布了“投资新余”品牌，揭晓了首批“投资新余”全球合作伙伴和推介官，并举行了授牌、授聘仪式。郑光泉、方向军分别为“投资新余”先锋队授旗。21个项目举行了签约仪式。\\\\r\\\\n新余市委书记郑光泉介绍，新余将始终以最精准的政策扶商，锚定加快打造新型工业强市这一战略目标和重点产业链“6313”行动计划，推出更多针对性强、含金量高、便利度好的政策措施，持续营造支持企业发展的最优环境、最佳生态，努力为每一个创新创业的“种子”提供最适宜的“土壤”。\\\\r\\\\n<img src=\\\\\"https://mediabluk.cnr.cn/img/cnr/CNRCDP/2025/0301/2e2690c6357ef174084268791672714211.jpg?auth=83a04b6436dc118d60107068e139b914\\\\\">\\\\r\\\\n郑光泉介绍新余投资政策（央广网记者  周蓓  摄）\\\\r\\\\n新余市委副书记、市长方向军表示，新余是一方创新创业的投资热地，重点产业优势明显、数字经济方兴未艾、文旅发展独具特色、商贸消费潜力无限，当前正深入实施“六大行动计划”，创新打造“投资新余”品牌，积极培育“新余制造”“新余数字”“新余文旅”“新余消费”和“新余服务”等品牌矩阵，努力将新余打造成为产业投资的重要目的地、赣商回归的重要选择地。\\\\r\\\\n<img src=\\\\\"https://mediabluk.cnr.cn/img/cnr/CNRCDP/2025/0301/7377c78c01c7d174084274562897362411.jpg?auth=30cfe025cc1f390168c4d398ae2303c0\\\\\">\\\\r\\\\n方向军介绍新余情况（央广网记者  周蓓  摄）\\\\r\\\\n启动仪式前，郑光泉、方向军等市领导会见了与会嘉宾和客商代表。中国工程院院士、江西师范大学校长陈芬儿，蓝迪国际智库专家委员会主席赵白鸽，中国信息通信研究院总工程师敖立等人先后发言，就投资新余、深化务实合作、优化营商环境等进行座谈交流。\",\"url\":\"http://jx.cnr.cn/yw/20250302/t20250302_527085926.shtml\"},\"sort\":[1740955615000]},{\"_index\":\"media_level_wiseweb_crawler_website\",\"_type\":\"_doc\",\"_id\":\"9039846414895862310\",\"_score\":null,\"_source\":{\"site_url\":\"http://jx.cnr.cn/\",\"publishtime\":\"2025-03-02 22:46:55\",\"tag\":[\"12393\"],\"title\":\"江西1.09万家企业实施数字化改造\",\"content\":\"原标题：我省制造业数字化转型成效明显\\\\r\\\\n2月26日，记者从省政府新闻办、省工业和信息化厅联合召开的“推动江西经济高质量发展”系列新闻发布会上获悉：截至2025年1月，全省已有1.09万家企业实施数字化改造，其中，有6000余家企业完成首轮改造，4422家企业计划提档升级至L6级及以上，计划投资规模超400亿元。\\\\r\\\\n加快推动制造业数字化转型是改革发展的重要组成部分和加快新型工业化的重要路径。去年，我省出台制造业数字化转型两年行动计划、“11条”支持措施等政策文件，加大财政资金投入力度，积极探索各具特色的区域推进模式，形成省市县全方位推进体系；在全国率先发布《制造业企业数字化发展水平评价指南》地方标准，形成企业数字化发展水平“四阶十级”评价划分，分别为准备阶段（L1、L2级）、基础爬坡阶段（L3-L5级）、集成提升阶段（L6-L8级）、创新领航阶段（L9、L10级）。\\\\r\\\\n当前，数字化转型已成为推动产业转型升级的强大动能。2024年底，我省数字化转型准备阶段企业占比较2023年底下降13.95个百分点、基础爬坡阶段企业占比提升14.08个百分点，产业数字化发展水平整体结构由“金字塔型”加快向“橄榄型”转变；两化融合发展指数比上年提升4.7个百分点，数字化研发设计工具普及率、关键工序数控化率和工业互联网平台应用普及率分别达到77.9%、62.7%和35.7%，分别比2023年底提升2.8个、3.3个和10.7个百分点。\\\\r\\\\n我省加速构建转型生态，打造“国家级—省级—省级培育”三级工业互联网平台培育体系，8家企业入围工业互联网500强榜单，24个国家级“双跨”工业互联网平台在我省成立分支机构，认定的67个省级工业互联网平台汇聚工业APP近8300个，服务省内外14万家企业，连接设备超265万台套。此外，我省还出台《关于金融支持制造业数字化转型的若干措施》，安排省级财政资金5000万元，以贷款贴息、担保费补助等方式支持6家金融机构发放30亿元贷款；20余家金融机构发布“数转贷”“数转险”等相关产品90余款，累计发放资金超百亿元，破解企业融资困境。\\\\r\\\\n促进实体经济和数字经济深度融合，推进产业数字化是重要着力点。今年是推进制造业数字化转型的关键一年，我省将持续加大制造业数字化转型工作力度，重点抓好加强督导评估、开展场景应用、强化典型引路、优化服务生态、开展宣传培训等工作，推动数字技术赋能制造业高质量发展，着力构建体现江西特色和优势的现代化产业体系。（全媒体记者陈晖）\",\"url\":\"http://jx.cnr.cn/yw/20250302/t20250302_527085923.shtml\"},\"sort\":[1740955615000]}]}}'"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["response.text"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["jinxu\n", "WiseWeb@123\n"]}], "source": ["print(config['elasticsearch']['username'])\n", "print(config['elasticsearch']['password'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}