from mongoengine import Document, <PERSON>Field, DateTimeField, BooleanField, IntField, ObjectIdField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional
from bson import ObjectId

# MongoEngine 模型
class ApiToken(Document):
    meta = {
        'collection': 'api_tokens'
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId)
    token = StringField(required=True, unique=True)    # API Token
    app_id = StringField(required=True)                # 应用ID
    app_name = StringField(required=True)              # 应用名称
    user_id = IntField(required=True)                  # 创建者ID
    created_at = DateTimeField(default=datetime.now)   # 创建时间
    expires_at = DateTimeField(required=True)          # 过期时间
    last_used_at = DateTimeField()                     # 最后使用时间
    is_active = BooleanField(default=True)            # 是否激活
    use_count = IntField(default=0)                   # 使用次数
    permissions = StringField(default='read')          # 权限级别：read/write/admin
    description = StringField()                        # 描述信息
    ip_whitelist = StringField()                      # IP白名单，逗号分隔

# Pydantic 模型
class ApiTokenBase(BaseModel):
    app_id: str
    app_name: str
    permissions: Optional[str] = 'read'
    description: Optional[str] = None
    ip_whitelist: Optional[str] = None

class ApiTokenCreate(ApiTokenBase):
    pass

class ApiTokenUpdate(BaseModel):
    app_name: Optional[str] = None
    permissions: Optional[str] = None
    description: Optional[str] = None
    ip_whitelist: Optional[str] = None
    is_active: Optional[bool] = None

class ApiTokenResponse(ApiTokenBase):
    id: str
    token: str
    created_at: datetime
    expires_at: datetime
    last_used_at: Optional[datetime] = None
    is_active: bool
    use_count: int

    class Config:
        from_attributes = True

class ApiTokenInfo(BaseModel):
    app_id: str
    app_name: str
    permissions: str
    is_active: bool
    expires_at: datetime 