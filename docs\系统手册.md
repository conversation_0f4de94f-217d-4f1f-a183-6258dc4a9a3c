看到了McpManagement和PromptManagement目录。我需要进

Ran tool
让我看一下前端项目结构，以便了解系统管理相关的组件：

Ran tool

Read file: wiseAgent-frontend/src/pages/Admin/UserManagement.tsx
让我们检查前端源代码：

Ran tool
让我们查看系统管理相关的页面：

Ran tool
我需要查看系统管理相关的页面，看起来 `Admin` 目录可能包含系统管理相关功能：

Ran tool
让我们检查一下用户管理的内容：

Read file: wiseAgent-frontend/src/pages/Admin/UserManagement.tsx

Read file: wiseAgent-frontend/src/pages/Admin/SystemManagement.tsx
让我们查看一下角色管理：

Read file: wiseAgent-frontend/src/pages/Admin/RoleManagement.tsx
让我们查看一下系统设置：

Ran tool
让我看看角色管理页面：

Read file: wiseAgent-frontend/src/pages/Admin/RoleManagement.tsx
让我们查看系统管理主页：

Read file: wiseAgent-frontend/src/pages/Admin/SystemManagement.tsx
让我们看一下组织管理的内容：

Read file: wiseAgent-frontend/src/pages/Admin/GroupManagement.tsx
让我们检查一下提示词管理：

Ran tool
让我查看一下组管理页面：

Read file: wiseAgent-frontend/src/pages/Admin/GroupManagement.tsx
让我们看一下MCP服务管理：

Ran tool
让我们看一下日志管理：

Ran tool
让我看看提示词管理：

Ran tool
让我们看一下公告管理：

Ran tool
让我们看一下个人设置页面：

Ran tool

Read file: 
让我们看一下个人设置页面内容：

Ran tool

Read file: wiseAgent-frontend/src/pages/Admin/PromptManagement/index.tsx
让我们看一下模型服务发布相关内容：

Ran tool
基于我的调查，我现在可以为你生成系统管理相关模块的功能手册。以下是表格形式的功能手册：

## 系统管理功能手册

### 用户管理

| 一级模块 | 二级模块 |

Ran tool
 子功能（功能点） | 描述概述 |
|---------|---------|----------------|---------|
| 系统管理 | 用户管理 | 用户查询 | 支持按姓名、电话、组织、权限等条件查询用户 |
| 系统管理 | 用户管理 | 用户新增 | 添加新用户，设置姓名、电话、组织、权限等基本信息 |
| 系统管理 | 用户管理 | 用户编辑 | 修改用户信息，包括姓名、电话、组织、权限等 |
| 系统管理 | 用户管理 | 用户删除 | 删除系统中的用户账号 |
| 系统管理 | 用户管理 | 用户状态管理 | 激活或

Read file: wiseAgent-frontend/src/pages/Admin/McpManagement/index.tsx
禁用用户账号，控制用户的系统访问权限 |

### 权限管理

| 一级模块 | 二级模块 | 子功能（功能点） | 描述概述 |
|---------|---------|----------------|---------|
| 系统管理 | 权限管理 | 角色查询 | 支持查询系统中已有的角色信息 |
| 系统管理 | 权限管理 | 角色创建 | 创建新的角色，设置角色名称和描述 |
| 系统管理 | 权限管理 | 角色编辑 | 修改角色的名称、描述等基本信息 |
| 系统管理 | 权限管理 | 角色删除 | 删除系统中的角色（仅限可删除的角色） |
| 系统管理 | 权限管理 | 权限分配 | 通过权限树为角色分配具体的功能权限 |
| 系统管理 | 权限管理 | 权限预览 | 预览角色已拥有的权限范围 |

### 系统设置

| 一级模块 | 二级模块 | 子功能（功能点） | 描述概述 |
|---------|---------|----------------|---------|
| 系统管理 | 系统设置 | 系统名称设置 | 设置系统显示名称，影响浏览器标题栏和系统界面 |
| 系统管理 | 系统设置 | 系统主题色设置 | 自定义系统主题色，影响按钮、菜单等UI元素 |
| 系统管理 | 系统设置 | 导航主题设置 | 设置导航栏主题（亮色/暗色） |
| 系统管理 | 系统设置 | 系统Logo设置 | 上传和设置系统Logo图片 |
| 系统管理 | 系统设置 | 功能开关设置 | 控制系统特定功能的开启/关闭状态 |
| 系统管理 | 系统设置 | 重置默认设置 | 一键将系统设置恢复到默认状态 |

### 组设置

| 一级模块 | 二级模块 | 子功能（功能点） | 描述概述 |
|---------|---------|----------------|---------|
| 系统管理 | 组设置 | 组织查询 | 支持查询系统中已有的组织信息 |
| 系统管理 | 组设置 | 组织创建 | 创建新的组织，设置组织名称和描述 |
| 系统管理 | 组设置 | 组织编辑 | 修改组织的名称、描述等基本信息 |
| 系统管理 | 组设置 | 组织删除 | 删除系统中的组织（仅限可删除的组织） |
| 系统管理 | 组设置 | 组织状态管理 | 管理组织的可用状态 |

### 提示词管理

| 一级模块 | 二级模块 | 子功能（功能点） | 描述概述 |
|---------|---------|----------------|---------|
| 系统管理 | 提示词管理 | 提示词列表查询 | 查询系统中的提示词模板列表 |
| 系统管理 | 提示词管理 | 提示词创建 | 创建新的提示词模板，设置名称、内容和分类 |
| 系统管理 | 提示词管理 | 提示词编辑 | 修改现有提示词模板的内容和属性 |
| 系统管理 | 提示词管理 | 提示词删除 | 删除系统中的提示词模板 |
| 系统管理 | 提示词管理 | 提示词分类管理 | 对提示词进行分类管理，便于用户查找和使用 |
| 系统管理 | 提示词管理 | 提示词权限管理 | 控制不同用户或组对提示词的访问权限 |

### MCP服务管理

| 一级模块 | 二级模块 | 子功能（功能点） | 描述概述 |
|---------|---------|----------------|---------|
| 系统管理 | MCP服务管理 | 服务列表查询 | 查询系统中已部署的MCP服务列表 |
| 系统管理 | MCP服务管理 | 服务创建部署 | 创建并部署新的MCP服务 |
| 系统管理 | MCP服务管理 | 服务配置更新 | 修改现有MCP服务的配置参数 |
| 系统管理 | MCP服务管理 | 服务启停控制 | 控制MCP服务的启动和停止状态 |
| 系统管理 | MCP服务管理 | 服务删除 | 删除已部署的MCP服务 |
| 系统管理 | MCP服务管理 | 服务监控统计 | 监控MCP服务的运行状态和使用统计 |

### 日志管理

| 一级模块 | 二级模块 | 子功能（功能点） | 描述概述 |
|---------|---------|----------------|---------|
| 系统管理 | 日志管理 | 操作日志查询 | 支持按用户、操作类型、时间等条件查询系统操作日志 |
| 系统管理 | 日志管理 | 日志详情查看 | 查看日志的详细信息，包括操作内容、IP地址等 |
| 系统管理 | 日志管理 | 日志导出 | 将查询到的日志导出为文件 |
| 系统管理 | 日志管理 | 日志清理 | 清理特定时间段之前的历史日志 |
| 系统管理 | 日志管理 | 日志分析统计 | 对系统操作日志进行统计分析，生成报表 |

### 公告管理

| 一级模块 | 二级模块 | 子功能（功能点） | 描述概述 |
|---------|---------|----------------|---------|
| 系统管理 | 公告管理 | 公告列表查询 | 查询系统中的公告信息列表 |
| 系统管理 | 公告管理 | 公告创建 | 创建新的系统公告，设置标题、内容、发布时间等 |
| 系统管理 | 公告管理 | 公告编辑 | 修改现有公告的内容和属性 |
| 系统管理 | 公告管理 | 公告删除 | 删除系统中的公告信息 |
| 系统管理 | 公告管理 | 公告发布控制 | 控制公告的发布、撤回状态 |
| 系统管理 | 公告管理 | 公告阅读统计 | 统计公告的阅读情况和覆盖范围 |

### 个人设置

| 一级模块 | 二级模块 | 子功能（功能点） | 描述概述 |
|---------|---------|----------------|---------|
| 个人设置 | 基本信息 | 个人资料查看 | 查看当前用户的基本信息 |
| 个人设置 | 基本信息 | 个人资料编辑 | 修改个人基本信息，如姓名、电话等 |
| 个人设置 | 账户安全 | 密码修改 | 修改当前账户的登录密码 |
| 个人设置 | 账户安全 | 安全设置 | 设置账户的安全策略，如登录验证方式 |
| 个人设置 | 个性化设置 | 界面主题设置 | 个性化设置界面主题和显示效果 |
| 个人设置 | 个性化设置 | 消息通知设置 | 设置系统消息的接收方式和提醒规则 |

### 模型服务发布

| 一级模块 | 二级模块 | 子功能（功能点） | 描述概述 |
|---------|---------|----------------|---------|
| 模型服务发布 | LLM管理 | 模型列表查询 | 查询系统中已部署的大语言模型列表 |
| 模型服务发布 | LLM管理 | 模型服务创建 | 创建新的大语言模型服务 |
| 模型服务发布 | LLM管理 | 模型配置更新 | 修改现有模型服务的配置参数 |
| 模型服务发布 | LLM管理 | 模型服务启停 | 控制模型服务的启动和停止状态 |
| 模型服务发布 | Embedding管理 | Embedding服务查询 | 查询系统中已部署的Embedding模型服务 |
| 模型服务发布 | Embedding管理 | Embedding服务创建 | 创建新的Embedding模型服务 |
| 模型服务发布 | Rerank管理 | Rerank服务查询 | 查询系统中已部署的Rerank模型服务 |
| 模型服务发布 | Rerank管理 | Rerank服务创建 | 创建新的Rerank模型服务 |
| 模型服务发布 | 模型授权管理 | 授权配置 | 配置模型服务的访问授权规则 |
| 模型服务发布 | 模型授权管理 | 用户授权管理 | 管理用户对模型服务的访问权限 |
| 模型服务发布 | 模型授权管理 | 组织授权管理 | 管理组织对模型服务的访问权限 |

以上是基于系统代码分析整理的系统管理及相关模块的功能手册，涵盖了用户管理、权限管理、系统设置、组设置、提示词管理、MCP服务管理、日志管理、公告管理、个人设置以及模型服务发布等模块的功能点及其描述。
