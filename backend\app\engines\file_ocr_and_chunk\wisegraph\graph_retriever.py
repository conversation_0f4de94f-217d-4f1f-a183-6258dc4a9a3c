"""
知识图谱检索器 - 纯图结构检索
"""


from typing import Dict, List, Any, Optional, Set
from .llm_client import LLMClient
from .neo4j_client import Neo4jClient
from .config import QUERY_ENTITY_EXTRACTION_PROMPT, GRAPH_QUERY_CONFIG
from loguru import logger


class GraphRetriever:
    """知识图谱检索器 - 实现纯图结构检索"""
    
    def __init__(self):
        self.llm_client = LLMClient()
        self.neo4j_client = Neo4jClient()
        self.is_connected = False
        self.config = GRAPH_QUERY_CONFIG
    
    async def initialize(self):
        """初始化连接"""
        try:
            await self.neo4j_client.connect()
            self.is_connected = True
            logger.info("GraphRetriever初始化成功")
        except Exception as e:
            logger.error(f"GraphRetriever初始化失败: {e}")
            raise
    
    async def close(self):
        """关闭连接"""
        if self.is_connected:
            await self.neo4j_client.close()
            self.is_connected = False
            logger.info("GraphRetriever连接已关闭")
    
    async def retrieve(self, query: str, max_depth: int = None, max_results: int = None, knowledge_base_id: str = None) -> Dict[str, Any]:
        """
        纯图结构检索

        Args:
            query: 用户查询
            max_depth: 最大查询深度
            max_results: 最大结果数量
            knowledge_base_id: 知识库ID（可选，用于知识库隔离）

        Returns:
            检索结果，包含上下文信息
        """
        if not self.is_connected:
            raise RuntimeError("GraphRetriever未初始化，请先调用initialize()")
        
        logger.info(f"开始图检索，查询: {query}")
        
        # 使用配置的默认值
        max_depth = max_depth or self.config["max_depth"]
        max_results = max_results or self.config["max_results"]
        
        try:
            # 步骤1: 从查询中提取实体和关系类型
            query_analysis = await self.llm_client.extract_query_entities(
                query, QUERY_ENTITY_EXTRACTION_PROMPT
            )
            
            entities = query_analysis.get("entities", [])
            relation_types = query_analysis.get("relation_types", [])
            keywords = query_analysis.get("keywords", [])
            
            logger.info(f"查询分析结果 - 实体: {entities}, 关系类型: {relation_types}, 关键词: {keywords}")
            
            # 步骤2: 在图中搜索相关实体
            relevant_entities = await self._find_relevant_entities(entities, keywords, knowledge_base_id)
            
            if not relevant_entities:
                logger.warning("未找到相关实体")
                return {
                    "query": query,
                    "context": "",
                    "entities_found": [],
                    "subgraph": {"nodes": [], "relationships": []},
                    "chunk_ids": set()
                }
            
            logger.info(f"找到相关实体: {len(relevant_entities)}")
            
            # 步骤3: 获取实体的子图
            entity_ids = [entity["entity_id"] for entity in relevant_entities]
            subgraph = await self.neo4j_client.get_entity_subgraph(entity_ids, max_depth)
            
            logger.info(f"获取子图 - 节点: {len(subgraph['nodes'])}, 关系: {len(subgraph['relationships'])}")
            
            # 步骤4: 构建上下文
            context = self._build_context_from_subgraph(subgraph, query)
            
            # 步骤5: 收集相关的chunk_ids
            chunk_ids = self._collect_chunk_ids(subgraph)
            
            result = {
                "query": query,
                "context": context,
                "entities_found": relevant_entities,
                "subgraph": subgraph,
                "chunk_ids": chunk_ids
            }
            
            logger.info(f"图检索完成，上下文长度: {len(context)}")
            return result
            
        except Exception as e:
            logger.error(f"图检索失败: {e}")
            return {
                "query": query,
                "context": "",
                "entities_found": [],
                "subgraph": {"nodes": [], "relationships": []},
                "chunk_ids": set()
            }
    
    async def _find_relevant_entities(self, entities: List[str], keywords: List[str], knowledge_base_id: str = None) -> List[Dict[str, Any]]:
        """
        查找相关实体

        Args:
            entities: 提取的实体列表
            keywords: 关键词列表
            knowledge_base_id: 知识库ID（可选）

        Returns:
            相关实体列表
        """
        relevant_entities = []
        
        # 首先通过实体名称精确匹配
        if entities:
            name_matched_entities = await self.neo4j_client.search_entities_by_names(entities)
            relevant_entities.extend(name_matched_entities)
        
        # 然后通过关键词模糊匹配
        if keywords:
            keyword_matched_entities = await self.neo4j_client.search_entities_by_keywords(keywords)
            
            # 去重
            existing_ids = {entity["entity_id"] for entity in relevant_entities}
            for entity in keyword_matched_entities:
                if entity["entity_id"] not in existing_ids:
                    relevant_entities.append(entity)
        
        return relevant_entities
    
    def _build_context_from_subgraph(self, subgraph: Dict[str, Any], query: str) -> str:
        """
        从子图构建上下文
        
        Args:
            subgraph: 子图数据
            query: 原始查询
            
        Returns:
            构建的上下文字符串
        """
        context_parts = []
        
        # 添加查询信息
        context_parts.append(f"查询: {query}\n")
        
        # 添加实体信息
        nodes = subgraph.get("nodes", [])
        if nodes:
            context_parts.append("相关实体:")
            for node in nodes:
                entity_info = f"- {node['entity_name']} ({node['entity_type']})"
                if node.get('description'):
                    entity_info += f": {node['description']}"
                context_parts.append(entity_info)
            context_parts.append("")
        
        # 添加关系信息
        relationships = subgraph.get("relationships", [])
        if relationships:
            context_parts.append("实体关系:")
            for rel in relationships:
                rel_info = f"- {rel['source']} --[{rel['relation_type']}]--> {rel['target']}"
                if rel.get('description'):
                    rel_info += f": {rel['description']}"
                context_parts.append(rel_info)
            context_parts.append("")
        
        # 组合上下文
        context = "\n".join(context_parts)
        return context
    
    def _collect_chunk_ids(self, subgraph: Dict[str, Any]) -> Set[str]:
        """
        收集子图中的chunk_ids
        
        Args:
            subgraph: 子图数据
            
        Returns:
            chunk_ids集合
        """
        chunk_ids = set()
        
        # 从节点收集chunk_ids
        for node in subgraph.get("nodes", []):
            if node.get("chunk_ids"):
                chunk_ids.update(node["chunk_ids"])
        
        # 从关系收集chunk_ids
        for rel in subgraph.get("relationships", []):
            if rel.get("chunk_ids"):
                chunk_ids.update(rel["chunk_ids"])
        
        return chunk_ids
    
    async def get_entity_details(self, entity_name: str) -> Dict[str, Any]:
        """
        获取特定实体的详细信息
        
        Args:
            entity_name: 实体名称
            
        Returns:
            实体详细信息
        """
        if not self.is_connected:
            raise RuntimeError("GraphRetriever未初始化，请先调用initialize()")
        
        entities = await self.neo4j_client.search_entities_by_names([entity_name])
        if entities:
            entity = entities[0]
            subgraph = await self.neo4j_client.get_entity_subgraph([entity["entity_id"]], 1)
            return {
                "entity": entity,
                "connected_entities": subgraph["nodes"],
                "relationships": subgraph["relationships"]
            }
        else:
            return {"entity": None, "connected_entities": [], "relationships": []}

    async def get_entity_subgraph_by_kb(self, entity_name: str, knowledge_base_id: str, max_depth: int = 2) -> Dict[str, Any]:
        """
        获取知识库内特定实体的子图

        Args:
            entity_name: 实体名称
            knowledge_base_id: 知识库ID
            max_depth: 查询深度

        Returns:
            实体子图数据
        """
        if not self.is_connected:
            raise RuntimeError("GraphRetriever未初始化，请先调用initialize()")

        try:
            # 在指定知识库中查找实体
            entities = await self.neo4j_client.search_entities_by_names_and_kb([entity_name], knowledge_base_id)

            if not entities:
                return {
                    "center_entity": None,
                    "nodes": [],
                    "relationships": [],
                    "depth": max_depth,
                    "stats": {"nodes": 0, "relationships": 0}
                }

            center_entity = entities[0]

            # 获取子图
            subgraph = await self.neo4j_client.get_entity_subgraph_by_kb(
                [center_entity["entity_id"]], knowledge_base_id, max_depth
            )

            return {
                "center_entity": center_entity,
                "nodes": subgraph["nodes"],
                "relationships": subgraph["relationships"],
                "depth": max_depth,
                "stats": {
                    "nodes": len(subgraph["nodes"]),
                    "relationships": len(subgraph["relationships"])
                }
            }

        except Exception as e:
            logger.error(f"获取实体子图失败: {e}")
            return {
                "center_entity": None,
                "nodes": [],
                "relationships": [],
                "depth": max_depth,
                "stats": {"nodes": 0, "relationships": 0}
            }

    async def get_knowledge_base_graph(self, knowledge_base_id: str, max_depth: int = 3, limit: int = 100, entity_types: List[str] = None) -> Dict[str, Any]:
        """
        获取知识库的完整图谱数据

        Args:
            knowledge_base_id: 知识库ID
            max_depth: 查询深度
            limit: 最大返回节点数
            entity_types: 过滤的实体类型

        Returns:
            知识库图谱数据
        """
        if not self.is_connected:
            raise RuntimeError("GraphRetriever未初始化，请先调用initialize()")

        try:
            # 获取知识库图谱概览
            graph_data = await self.neo4j_client.get_kb_graph_overview(
                knowledge_base_id, limit, entity_types
            )

            # 获取统计信息
            stats = await self.neo4j_client.get_kb_stats(knowledge_base_id)

            # 获取实体类型分布
            entity_type_stats = await self.neo4j_client.get_kb_entity_type_stats(knowledge_base_id)

            # 获取关系类型分布
            relation_type_stats = await self.neo4j_client.get_kb_relation_type_stats(knowledge_base_id)

            return {
                "knowledge_base_id": knowledge_base_id,
                "nodes": graph_data["nodes"],
                "relationships": graph_data["relationships"],
                "stats": stats,
                "summary": {
                    "entity_types": entity_type_stats,
                    "relation_types": relation_type_stats,
                    "max_depth": max_depth,
                    "limit": limit
                }
            }

        except Exception as e:
            logger.error(f"获取知识库图谱失败: {e}")
            return {
                "knowledge_base_id": knowledge_base_id,
                "nodes": [],
                "relationships": [],
                "stats": {"nodes": 0, "relationships": 0},
                "summary": {
                    "entity_types": [],
                    "relation_types": [],
                    "max_depth": max_depth,
                    "limit": limit
                }
            }

neo4j = GraphRetriever()