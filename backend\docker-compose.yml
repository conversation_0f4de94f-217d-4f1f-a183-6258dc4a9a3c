version: '3.8'

services:
  # LangFlow 服务
  langflow:
    image: langflow:1.2.0
    container_name: langflow
    restart: unless-stopped
    depends_on:
      - postgres
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - LANGFLOW_DATABASE_URL=********************************************/langflow
      - LANGFLOW_SUPERUSER=admin
      - LANGFLOW_SUPERUSER_PASSWORD=securepassword
      - LANGFLOW_SECRET_KEY=dBuuuB_FHLvU8T9eUNlxQF9ppqRxwWpXXQ42kM2_fbg
      - LANGFLOW_AUTO_LOGIN=False
      - LANGFLOW_NEW_USER_IS_ACTIVE=False
    volumes:
      - langflow-data:/app/langflow
    networks:
      - app-network

  # PostgreSQL 数据库
  postgres:
    image: postgres:16
    container_name: postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: langflow
      POSTGRES_PASSWORD: langflow
      POSTGRES_DB: langflow
    volumes:
      - langflow-postgres:/var/lib/postgresql/data
    networks:
      - app-network

  # WiseAgent 服务
  wiseagent:
    image: wiseagent:1.0.0
    container_name: wiseagent
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - ENV_MODE=production
    networks:
      - app-network

  # Nginx 反向代理
  nginx:
    image: nginx-proxy:latest
    container_name: nginx
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - "8888:80"
    depends_on:
      - langflow
      - wiseagent
    networks:
      - app-network

volumes:
  langflow-postgres:
  langflow-data:

networks:
  app-network:
    driver: bridge