from minio import Minio
from minio.error import S3Error
from typing import Optional
import os
from datetime import timedelta
from ..utils.config import settings
from ..utils.logging_config import get_logger

logger = get_logger(__name__)

class MinioManager:
    _instance = None
    _initialized = False
    _client = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        pass
    
    def _init_sync(self):
        """同步初始化 MinIO 连接"""
        try:
            # 从配置文件获取设置
            self.endpoint = settings.MINIO_ENDPOINT
            self.access_key = settings.MINIO_ACCESS_KEY
            self.secret_key = settings.MINIO_SECRET_KEY
            self.secure = settings.MINIO_SECURE
            self.bucket_name = settings.MINIO_BUCKET

            logger.info(f"MinIO连接地址: {self.endpoint}")
            
            # 初始化 MinIO 客户端
            self._client = Minio(
                endpoint=self.endpoint,
                access_key=self.access_key,
                secret_key=self.secret_key,
                secure=self.secure
            )
            
            self._initialized = True
            logger.info("MinIO connection initialized")
        except Exception as e:
            logger.error(f"MinIO connection failed: {str(e)}")
            raise

    async def connect(self):
        """初始化并验证连接"""
        logger.info("开始初始化MinIO连接")
        if not self._initialized:
            self._init_sync()
        
        try:
            # 验证连接并确保 bucket 存在
            if not self._client.bucket_exists(self.bucket_name):
                self._client.make_bucket(self.bucket_name)
            logger.info("MinIO connection verified and bucket ensured")
        except Exception as e:
            logger.error(f"MinIO connection verification failed: {str(e)}")
            raise

    async def disconnect(self):
        """断开连接"""
        if self._client:
            self._client = None
            self._initialized = False
            logger.info("MinIO disconnected")

    @property
    def client(self):
        """获取 MinIO 客户端实例"""
        if not self._initialized:
            self._init_sync()
        return self._client

    def upload_file(self, 
                    file_path: str, 
                    object_name: Optional[str] = None,
                    content_type: Optional[str] = None
                    ) -> str:
        """
        上传文件到 MinIO 并返回可访问的 URL
        
        Args:
            file_path: 本地文件路径
            object_name: 在 MinIO 中的对象名称，如果为 None 则使用文件名
            content_type: 文件的 content-type，如果为 None 则自动检测
            
        Returns:
            str: 文件的可访问 URL
        """
        try:
            if not self._initialized:
                self._init_sync()

            # 如果没有指定对象名，使用文件名
            if object_name is None:
                object_name = os.path.basename(file_path)
            
            # 上传文件
            self._client.fput_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                file_path=file_path,
                content_type=content_type
            )
            
            # 生成预签名 URL（默认 7 天有效）
            url = self._client.presigned_get_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                expires=timedelta(days=7)
            )
            
            return url
            
        except S3Error as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise Exception(f"文件上传失败: {str(e)}")

    def upload_bytes(self,
                    data: bytes,
                    object_name: str,
                    content_type: Optional[str] = None
                    ) -> str:
        """
        上传字节数据到 MinIO 并返回可访问的 URL
        
        Args:
            data: 字节数据
            object_name: 在 MinIO 中的对象名称
            content_type: 文件的 content-type，如果为 None 则自动检测
            
        Returns:
            str: 文件的可访问 URL
        """
        try:
            if not self._initialized:
                self._init_sync()

            from io import BytesIO
            # 上传数据
            self._client.put_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                data=BytesIO(data),
                length=len(data),
                content_type=content_type
            )
            
            # 生成预签名 URL（默认 7 天有效）
            url = self._client.presigned_get_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                expires=timedelta(days=7)
            )
            
            return url
            
        except S3Error as e:
            logger.error(f"数据上传失败: {str(e)}")
            raise Exception(f"数据上传失败: {str(e)}")

# 创建单例实例
minio = MinioManager()
