# 监控端 (Monitor)

监控端用于部署在需要被监控的服务器上，提供系统资源使用情况的API接口。

## 功能特性

### 1. 系统基本信息 `/system/info`
- 操作系统信息
- CPU配置信息（核心数、物理核心数）
- 内存容量
- 磁盘分区信息
- GPU设备信息
- NPU设备信息
- CUDA版本信息
- GPU驱动信息
- NPU驱动信息

### 2. 系统监控指标 `/system/metrics`
- CPU使用率（总体和每核心）
- CPU频率信息（当前、最小、最大）
- 内存使用情况
- 磁盘使用情况
- 网络IO统计
- GPU监控信息：
  - 使用率
  - 内存使用
  - 温度
  - 功耗
  - 风扇转速
- NPU监控信息：
  - 使用率
  - 内存使用
  - 温度
  - 功耗

### 3. GPU详细信息 `/gpu/info`
- GPU硬件详细信息
- 序列号
- BIOS版本
- 计算模式
- PCIe链路宽度
- CUDA版本
- 驱动版本

### 4. NPU详细信息 `/npu/info`
- NPU硬件详细信息
- 驱动版本
- 设备状态

## 环境要求

- Python 3.9+
- NVIDIA驱动（如需GPU监控）
- 昇腾NPU驱动（如需NPU监控）
- CUDA工具包（可选，用于获��CUDA版本）
- 昇腾NPU工具包（可选，用于NPU监控）

## 依赖包

bash
fastapi==0.68.1
uvicorn==0.15.0
python-dotenv==0.19.0
psutil==5.8.0
gputil==1.4.0
nvidia-ml-py3==7.352.0


## 配置说明

在 `.env` 文件中配置：

## 部署方法

### 1. Docker部署（推荐）

bash
构建并启动服务
docker-compose up -d
查看服务状态
docker-compose ps
查看服务日志
docker-compose logs -f
停止服务
docker-compose down

### 2. 直接部署
```bash:service/ComputeWatch/monitor/README.md
# 安装依赖
pip install -r requirements.txt

# 启动服务
uvicorn main:app --host 0.0.0.0 --port 8000
```

## API使用示例

### 1. 获取系统信息
```bash
curl -H "api-key: 363326947" http://localhost:8000/system/info
```

返回示例：
```json
{
    "os": "Linux",
    "os_version": "5.4.0-1018-aws",
    "architecture": "x86_64",
    "cpu_count": 4,
    "cpu_physical_count": 2,
    "memory_total": 16777216,
    "disk_partitions": ["/", "/boot"],
    "gpu_devices": [
        {
            "id": 0,
            "name": "NVIDIA GeForce RTX 3080",
            "memory_total": 10240,
            "uuid": "GPU-123456789"
        }
    ],
    "cuda_version": "11.4",
    "gpu_driver": {
        "driver_version": "470.57.02",
        "cuda_version": "11.4"
    }
}
```

### 2. 获取监控指标
```bash
curl -H "api-key: 363326947" http://localhost:8000/system/metrics
```

返回示例：
```json
{
    "cpu": {
        "percent": 25.6,
        "per_cpu": [20.1, 30.2, 25.3, 26.8],
        "freq": {
            "current": 3600,
            "min": 800,
            "max": 4000
        }
    },
    "memory": {
        "total": 16777216,
        "used": 8388608,
        "free": 8388608,
        "percent": 50.0
    },
    "disk": {
        "/": {
            "total": ************,
            "used": 121634816000,
            "free": 134425698304,
            "percent": 47.5
        }
    },
    "network": {
        "bytes_sent": 1024,
        "bytes_recv": 2048
    },
    "gpu": [
        {
            "id": 0,
            "name": "NVIDIA GeForce RTX 3080",
            "load": 45.6,
            "memory_used": 4096,
            "memory_total": 10240,
            "temperature": 65.0,
            "power_draw": 220.5,
            "power_limit": 320.0,
            "fan_speed": 65.0
        }
    ]
}
```

### 3. 获取GPU详细信息
```bash
curl -H "api-key: 363326947" http://localhost:8000/gpu/info
```

返回示例：
```json
{
    "cuda_version": "11.4",
    "driver_info": {
        "driver_version": "470.57.02",
        "cuda_version": "11.4"
    },
    "gpus": [
        {
            "index": 0,
            "name": "NVIDIA GeForce RTX 3080",
            "uuid": "GPU-123456789",
            "serial": "1234567890",
            "vbios_version": "***********.0F",
            "memory_total": "10240 MiB",
            "compute_mode": "Default",
            "pcie_link_width": "16"
        }
    ]
}
```

### 4. 获取NPU详细信息
```bash
curl -H "api-key: 363326947" http://localhost:8000/npu/info
```

返回示例：
```json
{
    "driver_version": "1.0",
    "npus": [
        {
            "id": "NPU-0",
            "power": 100.0,
            "temperature": 60.0,
            "memory_used": 1024,
            "memory_total": 10240,
            "utilization": 80.0
        }
    ]
}
```

## 注意事项

1. 安全性
   - 确保API_KEY的安全性
   - 建议在生产环境中使用HTTPS
   - 避免将API_KEY暴露在代码或日志中

2. GPU监控
   - 确保已安装NVIDIA驱动
   - 部分GPU信息可能因硬件限制而无法获取
   - 不同型号GPU支持的监控项可能不同

3. 性能考虑
   - CPU使用率统计有1秒延迟
   - 建议根据需求调整监控频率
   - 避免过于频繁的API调用

4. 部署建议
   - 推荐使用Docker部署
   - 确保服务器防火墙允许指定端口访问
   - 定期检查日志确保服务正常运行

## 测试说明

### 1. 安装测试依赖
```bash
pip install pytest pytest-mock httpx
```

### 2. 运行测试
```bash
# 运行所有测试
pytest test_main.py -v

# 运行特定测试
pytest test_main.py -v -k "test_get_system_info"
```

### 3. 测试覆盖范围

#### 认证测试
- 测试无效的API密钥
- 测试缺少API密钥

#### 系统信息接口测试
- 测试基本系统信息获取
- 测试GPU信息获取
- 测试NPU信息获取
- 测试CUDA版本信息

#### 系统指标接口测试
- 测试CPU使用率
- 测试内存使用情况
- 测试磁盘使用情况
- 测试网络IO
- 测试GPU指标
- 测试NPU指标

#### GPU/NPU详细信息接口测试
- 测试GPU硬件信息
- 测试NPU硬件信息
- 测试驱动版本信息

#### 错误处理测试
- 测试无GPU环境
- 测试无NPU环境
- 测试部分功能失败的情况

### 4. 测试示例

```python
# 测试系统信息接口
def test_get_system_info(mock_system_info, mock_gpu_info):
    response = client.get("/system/info", headers={"api-key": "363326947"})
    assert response.status_code == 200
    data = response.json()
    
    assert data["os"] == "Linux"
    assert data["architecture"] == "x86_64"
    assert data["cpu_count"] == 4
    assert data["memory_total"] == 16777216
    assert len(data["disk_partitions"]) == 2
    assert data["cuda_version"] == "11.4"
    assert len(data["gpu_devices"]) == 1
    assert data["gpu_devices"][0]["name"] == "NVIDIA GeForce RTX 3080"

# 测试NPU信息接口
def test_get_npu_info(mock_npu_info):
    response = client.get("/npu/info", headers={"api-key": "363326947"})
    assert response.status_code == 200
    data = response.json()
    
    assert data["driver_version"] == "1.0"
    assert len(data["npus"]) == 1
    assert data["npus"][0]["id"] == "NPU-0"
    assert data["npus"][0]["utilization"] == 80.0
```

### 5. 测试注意事项

1. 模拟硬件环境
   - 测试使用mock模拟硬件环境
   - 避免对实际硬件的依赖
   - 确保测试的可重复性

2. 测试数据
   - 使用固定的测试数据
   - 覆盖正常和异常情况
   - 模拟各种硬件配置

3. 错误处理
   - 测试API的错误响应
   - 验证错误信息的准确性
   - 确保服务的稳定性

4. 持续集成
   - 在代码提交时运行测试
   - 确保测试覆盖率
   - 维护测试用例的时效性