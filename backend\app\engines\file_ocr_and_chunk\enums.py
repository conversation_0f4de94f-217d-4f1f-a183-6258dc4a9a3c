from enum import Enum
class RAGSearchType(str, Enum):
    VECTOR = "vector"
    GRAPH = "graph"
    SEMANTIC = "semantic"
    HYBRID = "hybrid"
class FileStatus(int, Enum):
    """文件处理状态枚举"""
    WAITING = 0    # 等待处理
    COMPLETED = 1  # 处理完成
    PROCESSING = 2 # 处理中
    ERROR = 3      # 处理错误
    DELETED = 100

class graphStatus(str, Enum):
    """图状态枚举"""
    PENDING = "pending" # 等待构建
    PROCESSING = "processing" # 构建中
    COMPLETED = "completed" # 构建完成
    FAILED = "failed" # 构建错误

class chunk_type(str, Enum):
    """chunk类型枚举"""
    BASE = "base" # 向量检索
    GRAPH = "graph" # 图检索
    SEMANTIC = "semantic" # 语义检索
    AUDIO = "audio" # 音频检索
    IMAGE = "image" # 图片检索
    VIDEO = "video" # 视频检索
    QA = "qa" # 问答检索
    INPUT = "input" # 手动录入

class FileStorageType(str, Enum):
    """文件存储类型枚举"""
    LOCAL = 'local'   # 本地
    MINIO = 'minio'      # 云存储


class ExpertType(str, Enum):
    """专家类型枚举"""
    GENERAL = "general"    # 通用型
    SPECIALIZED = "spec"   # 专业型
    FALLBACK = "fallback"  # 兜底型

class ExpertStatus(int, Enum):
    """专家状态枚举"""
    INACTIVE = 0  # 未激活
    ACTIVE = 1    # 已激活
    PAUSED = 2    # 已暂停

class AppType(str, Enum):
    WISERAG = "网智天元知识库系统API"
    WISEBI = "网智天元知识库系统API"
    KNOWLEDGE_QA = "知识库Agent应用"
    AGENT = "智能体应用"
    EXPERT_SYSTEM = "专家系统"
    LLM = "大语言模型"
    OTHER = "其他"


class FileParserType(str, Enum):
    """解析器类型枚举"""
    DEFAULT = "default"           # 默认解析器
    ANNUAL_REPORT = "annual"      # 企业年报解析器
    LAW_REGULATION = "law"        # 法律法规解析器
    USER_MANUAL = "manual"        # 使用手册解析器
    CONTRACT = "contract"        # 合同解析器
    IMAGE = "image"        # 图片解析器
    IMAGE_FILE = "image_file"        # 图片文件解析器

# class AppType(str, Enum):
#     """应用类型枚举"""
#     KNOWLEDGE_QA = "knowledge_qa"  # 知识问答
#     WISERAG = "wise_rag"           # 智能RAG
#     LLM = "llm_service"            # LLM服务

class LLMProvider(str, Enum):
    """服务提供商枚举"""
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    DOUBAO = "doubao"
    JIUTIAN = "jiutian"
    LOCAL = "local"
    OLLAMA = "ollama"

class RerankProvider(str, Enum):
    """服务提供商枚举"""
    OPENAI = "openai"
    LOCAL = "local"
    OLLAMA = "ollama"
class EmbeddingProvider(str, Enum):
    """服务提供商枚举"""
    OPENAI = "openai"
    LOCAL = "local"
    OLLAMA = "ollama"

class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL" 