from typing import TypedDict,List, Dict, Any, Optional
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
import asyncio
import traceback
from datetime import datetime
from .models import SearchResult, KnowledgeQAParams
from .pg_retriever import parallel_knowledge_search
from .logging_config import setup_logging, get_logger
setup_logging()
logger = get_logger(__name__)

class RAGState(TypedDict):
    query: str
    Knowledgebase_ids: List[str]
    search_results: List[SearchResult]
    error: Optional[str]
    output: Optional[Dict]

class FlowRAG:
    def __init__(self, knowledgebase_ids: List[str]):
        logger.info(f"knowledgebase_ids==》{knowledgebase_ids}")
        self.knowledgebase_ids = knowledgebase_ids
        self.workflow = StateGraph(RAGState)
        self._setup_workflow()

    def _setup_workflow(self):
        # 简化流程：搜索 -> 构建答案
        self.workflow.add_node("search", self.search_node)
        self.workflow.add_node("build_answer", self.build_prompt)
        
        # 构建流程
        self.workflow.set_entry_point("search")
        self.workflow.add_edge("search", "build_answer")
        self.workflow.add_edge("build_answer", END)

    async def search_node(self, state: RAGState) -> RAGState:
        """执行搜索任务"""
        logger.info(f"开始执行搜索任务，查询词：{state['query']}")
        try:
            # 向量检索
            results = await parallel_knowledge_search(
                state["query"],
                self.knowledgebase_ids,
            )
            logger.info(f"搜索完成，找到 {len(results)} 条结果")
            return {**state, "search_results": results}
        except Exception as e:
            logger.error(f"搜索失败: {str(e)}")
            traceback.print_exc()
            return {**state, "error": str(e)}

    async def build_prompt(self, state: RAGState) -> RAGState:
        """构建提示"""
        if state.get("error"):
            logger.error(f"构建提示失败: {state['error']}")
            return state
            
        logger.info("开始构建提示")
        # 简化模板，只使用 content
        retrieval_template = "【参考资料{{index}}】{{content}}"
        
        # 处理检索结果
        quote_strs = []
        for idx, res in enumerate(state["search_results"], 1):
            quote_strs.append(
                retrieval_template.replace("{{content}}", res["content"])
                               .replace("{{index}}", str(idx))
            )
        
        # 合并所有检索结果
        quote_block = "\n\n".join(quote_strs)
        final_prompt = f"{quote_block}\n\n请根据以上参考内容回答：{state['query']}"
        
        logger.info(f"构建提示词完成，提示词：{final_prompt}")
        return {**state, "output": {"role": "user", "content": final_prompt}}

    async def run(self, query: str) -> Dict[str, Any]:
        """执行流程"""
        logger.info(f"开始执行流程，查询词：{query}")
        try:
            initial_state = RAGState(
                query=query,
                Knowledgebase_ids=self.knowledgebase_ids,
                search_results=[],
                error=None,
                output=None
            )
            app = self.workflow.compile()
            result = await app.ainvoke(initial_state)
            logger.info(f"流程执行完成，结果：{result}")    
            return result
        except Exception as e:
            logger.error(f"流程执行失败: {str(e)}")
            traceback.print_exc()
            return {"error": str(e)}