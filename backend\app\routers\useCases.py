from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.useCases import  UseCaseCreate, UseCaseUpdate, UseCaseResponse
from ..db.mongodb import db
from datetime import datetime
from ..utils.auth import verify_token
from bson import ObjectId
router = APIRouter(
    prefix="/api",
    tags=["useCases"]
)

# 获取所有 Embedding 模型，支持分页
@router.get("/useCases", response_model=Dict[str, Any])
async def get_useCases(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    name: Optional[str] = None,  # 支持按模型名称检索
    tags: Optional[str] = None,  # 支持按标签检索
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {}
    if name:
        query["name"] = {"$regex": name, "$options": "i"}
    if tags:
        query["tags"] = {"$regex": tags, "$options": "i"}

    useCases = await db["use_cases"].find(query, {
        "_id": 1,
        "name": 1,
        "description": 1,
        "cover_image": 1,
        "redirect_url": 1,
        "view_count": 1,
        "tags": 1,
        "is_active": 1,
    }).skip(skip).limit(pageSize).to_list(pageSize)
    total = await db["use_cases"].count_documents(query)
    for useCase in useCases:
        useCase["id"] = str(useCase["_id"])
        del useCase["_id"]
        # useCase["created_at"] = useCase["created_at"].strftime("%Y-%m-%d %H:%M:%S")

    return {
        "data": useCases,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }


@router.get("/useActiveCases", response_model=Dict[str, Any])
async def get_useActiveCases(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    searchText: Optional[str] = None,  # 支持按模型名称检索
    category: Optional[str] = None,  # 支持按标签检索
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {}
    if searchText:
        query["name"] = {"$regex": searchText, "$options": "i"}
    if category:
        # 将逗号分隔的category字符串分割成列表
        category_list = category.split(',')
        # 使用 $or 操作符来实现多个tag的模糊搜索
        query["$or"] = [{"tags": {"$regex": tag.strip(), "$options": "i"}} for tag in category_list]
    print(query)
    useCases = await db["use_cases"].find(query, {
        "_id": 1,
        "name": 1,
        "description": 1,
        "cover_image": 1,
        "redirect_url": 1,
        "view_count": 1,
        "tags": 1,
        "is_active": 1,
    }).skip(skip).limit(pageSize).to_list(pageSize)
    total = await db["use_cases"].count_documents(query)
    for useCase in useCases:
        useCase["id"] = str(useCase["_id"])
        del useCase["_id"]
        # useCase["created_at"] = useCase["created_at"].strftime("%Y-%m-%d %H:%M:%S")

    return {
        "data": useCases,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }
# 添加新 Embedding 模型
@router.post("/useCases", response_model=UseCaseResponse)
async def add_useCase(useCase: UseCaseCreate, current_user: dict = Depends(verify_token)):
    last_useCase = await db["use_cases"].find_one(sort=[("id", -1)])
    new_id = (last_useCase["id"] + 1) if last_useCase else 1

    new_useCase = useCase.dict()
    new_useCase.update({
        "id": new_id,
        "created_at": datetime.now(),
        "created_by": current_user["id"],  # 确保设置 created_by
        "is_active": True,
        "creator_id": current_user["id"],
        "creator_name": current_user["name"],
        "tags": useCase.tags,
        "deleted_at": None,
        "deleted_by": None,
        "updated_at": datetime.now(),
    })

    await db["use_cases"].insert_one(new_useCase)
    return UseCaseResponse(**new_useCase)

# 更新 Embedding 模型
@router.put("/useCases/{useCase_id}", response_model=UseCaseResponse)
async def update_useCase(useCase_id: int, useCase: UseCaseUpdate, current_user: dict = Depends(verify_token)):
    result = await db["use_cases"].update_one({"id": ObjectId(useCase_id)}, {"$set": useCase.dict(exclude_unset=True)})
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="UseCase not found")
    updated_useCase = await db["use_cases"].find_one({"id": ObjectId(useCase_id)})
    return UseCaseResponse(**updated_useCase)

# 删除 Embedding 模型
@router.delete("/useCases/{useCase_id}", response_model=Dict[str, int])
async def delete_useCase(useCase_id: int, current_user: dict = Depends(verify_token)):
    useCase = await db["use_cases"].find_one({"id": ObjectId(useCase_id)})
    if not useCase:
        raise HTTPException(status_code=404, detail="UseCase not found")
    result = await db["use_cases"].delete_one({"id": ObjectId(useCase_id)})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="UseCase not found")
    return {"id": useCase_id}

@router.get("/useCasesList", response_model=Dict[str, Any])
async def get_useCases(
    current_user: dict = Depends(verify_token)
):
    useCases = await db["use_cases"].find({"is_active": True}, {
        "_id": 0,
        "id": 1,
        "name": 1,
        "description": 1,
        "cover_image": 1,
        "redirect_url": 1,
        "view_count": 1,
        "is_active": 1,
        "created_at": 1
    }).to_list()
    return {
        "data": useCases,
        "success": True,
    }