import asyncio
from dataclasses import asdict, dataclass, field
from typing import List, Dict, Any, Optional,Type
from concurrent.futures import ThreadPoolExecutor

from ..rerank.rerank_utils import RerankResult, rerank_texts
from app.utils.logging_config import get_logger, setup_logging
from lmnr_flow import Flow, TaskOutput, NextTask, Context, StreamChunk
from ..retrieval.base_retriever import ESKeywordRetriever,BaseKeywordRetriever




# 设置日志
setup_logging()
logger = get_logger(__name__)

@dataclass
class FlowRAG:

    max_workers:int = field(default=4)
    config: Dict[str,any] = field(default_factory=lambda:{'vector_search':True,'graph_search':False,'rerank_config':{}})
    keyword_search_cls: Type[BaseKeywordRetriever] = ESKeywordRetriever
    rag_search_top_k:int = field(default=10)

    def __post_init__(self):
        """
        初始化 RAG 流程

        Args:
            config: 配置信息，包含:
                - vector_search: 是否启用向量搜索
                - graph_search: 是否启用图搜索
                - rerank_config: 重排序配置
                - top_k: 每路召回的数量
                - final_top_k: 最终返回的数量
        """
        _print_config = ",\n  ".join([f"{k} = {v}" for k, v in asdict(self).items()])
        print(f"LLM_FLOW init with param:\n\n  {_print_config}\n")
        self.flow = Flow(thread_pool_executor=ThreadPoolExecutor(max_workers=self.max_workers))
        self._setup_flow()

        self.keyword_search = self.keyword_search_cls(index_name='wise_agent_chunking',es_host='**************:9600')





    def _setup_flow(self):
        """设置 Flow 任务"""
        # 添加任务节点
        self.flow.add_task("router", self._route_task)
        self.flow.add_task("vector_search", self._vector_search_task)
        self.flow.add_task("graph_search", self._graph_search_task)
        self.flow.add_task("merge_results", self._merge_results_task)
        self.flow.add_task("rerank", self._rerank_task)

    def _route_task(self, ctx) -> TaskOutput:
        """路由任务，决定使用哪些召回方式"""
        query = ctx.get("query")
        tasks = []

        if self.config.get("vector_search"):
            tasks.append(NextTask("vector_search", {"query": query}))

        if self.config.get("graph_search"):
            tasks.append(NextTask("graph_search", {"query": query}))

        return TaskOutput(output='ROUTE START',next_tasks=tasks)

    def _vector_search_task(self, ctx) -> TaskOutput:
        """向量搜索任务"""
        query = ctx.get("query")
        try:
            # 这里实现向量搜索逻辑
            results = self.keyword_search.retrieve(query, top_k=self.config.get("top_k", self.rag_search_top_k))
            logger.info(f'关键词召回结果{len(results)}个')

            return TaskOutput(output=results)
        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}")
            return TaskOutput(output=str(e))

    def _graph_search_task(self, ctx) -> TaskOutput:
        """图搜索任务"""
        query = ctx.get("query")
        try:
            # 这里实现图搜索逻辑
            # results = self.keyword_search(query, top_k=self.config.get("top_k", 10))
            results = []  # 替换为实际的图搜索结果

            return TaskOutput(output=results)
        except Exception as e:
            logger.error(f"Graph search failed: {str(e)}")
            return TaskOutput(output=str(e))

    def _merge_results_task(self, ctx) -> TaskOutput:
        """合并搜索结果"""
        try:
            all_results = []
            context_ = ctx.to_dict()
            # 获取向量搜索结果
            vector_results = context_.get("vector_search",[])
            if vector_results:
                all_results.extend(vector_results)
                logger.info(f'获取向量搜索结果{len(vector_results)}')

            # 获取图搜索结果
            graph_results = context_.get("graph_search",[])
            if graph_results:
                all_results.extend(graph_results)
                logger.info(f'获取图搜索结果{len(graph_results)}')

            # 去重
            seen = set()
            unique_results = []
            for result in all_results:
                if result["id"] not in seen:
                    seen.add(result["id"])
                    unique_results.append(result)


            return TaskOutput(output=unique_results)
        except Exception as e:
            logger.error(f"Merge results failed: {str(e)}")
            return TaskOutput(output=str(e))

    def _rerank_task(self, ctx) -> TaskOutput:
        """重排序任务"""
        try:
            contest_ = ctx.to_dict()
            query = contest_.get("query")
            print('rerank query',query)
            merged_results = contest_.get("merge_results", [])


           

            # 调用重排序
            rerank_results: List[RerankResult] = rerank_texts(
                query=query,
                texts=merged_results,
                config=self.config.get("rerank_config", {}),
                top_k=self.config.get("final_top_k", 5)
            )

            # 将重排序分数合并回原始结果
            final_results = []
            for rank_result, orig_result in zip(rerank_results, merged_results):
                final_results.append({
                    **orig_result,
                    "score": rank_result.score
                })

            # 按分数排序
            final_results.sort(key=lambda x: x["score"], reverse=True)

            # 只返回指定数量的结果
            final_results = final_results[:self.config.get("final_top_k", 5)]

            return TaskOutput(output=final_results)
        except Exception as e:
            logger.error(f"Rerank failed: {str(e)}")
            return TaskOutput(output=str(e))

    def run(self, query: str) -> List[Dict[str, Any]]:
        """
        运行 RAG 流程

        Args:
            query: 查询文本

        Returns:
            List[Dict[str, Any]]: 排序后的召回结果
        """
        try:
            self.flow.run("router", inputs={"query": query})
            result = self.flow.run('merge_results')
            if self.config.get("rerank_config"):
                rerank_result = self.flow.run('rerank')
                return rerank_result.get('rerank',[])

            if isinstance(result, dict) and "error" in result:
                logger.error(f"Flow execution failed: {result['error']}")
                return []
            return result.get('merge_results',[])
        except Exception as e:
            logger.error(f"Flow execution failed: {str(e)}")
            return []


if __name__ == '__main__':
    print(123)

