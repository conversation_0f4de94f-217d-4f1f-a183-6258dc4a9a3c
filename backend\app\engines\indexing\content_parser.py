import os
import traceback
import requests
import hashlib
from typing import List, Dict, Any, Optional, Union
from app.utils.logging_config import setup_logging, get_logger
from app.utils.enums import FileParserType, FileStorageType
from fastapi import HTTPException
import re
import tempfile
import tarfile
import json
from .file_chunk import (
    file_convert_to_markdown_base,
    markdown_to_chunk_index,
    file_analyze_to_markdown,
    pdf_parser,
    txt_parser,
    excel_parser
)
from langchain.text_splitter import (
        RecursiveCharacterTextSplitter,
        MarkdownHeaderTextSplitter,
        TokenTextSplitter
    )

# 设置日志
setup_logging()
logger = get_logger(__name__)

def download_file(storage_path: str, local_path: str) -> bool:
    """
    从远程存储下载文件到本地
    
    Args:
        storage_path: 远程文件路径
        local_path: 本地文件路径
        
    Returns:
        bool: 下载是否成功
    """
    try:
        # 创建目录（如果不存在）
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        # 下载文件
        logger.info(f"下载文件: {storage_path} -> {local_path}")
        response = requests.get(storage_path, timeout=30)
        response.raise_for_status()
        
        with open(local_path, 'wb') as f:
            f.write(response.content)
        logger.info(f"文件下载成功: {storage_path} -> {local_path}")
        return True
    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def parse_file_markdown(
    storage_path: str, 
    file_type: str, 
    parser_type: Optional[str] = None,
    version: Optional[str] = None,
    storage_type: str = FileStorageType.LOCAL
) -> List[Dict[str, Any]]:
    """
    解析文件内容，返回markdown内容
    
    Args:
        storage_path: 文件存储路径（本地路径或远程URL）
        file_type: 文件类型(如pdf, txt, xlsx等)
        parser_type: 解析器类型，默认为None，使用默认解析器
        version: 文件版本号，默认为None
        storage_type: 存储类型（本地或远程），默认为LOCAL
        
    Returns:
        List[Dict[str, Any]]: 解析后的内容块列表
    """
    try:
        logger.info(f"开始解析文件: {storage_path}, 类型: {file_type}, 解析器: {parser_type}, 版本: {version}")
        
        # 处理文件路径
        file_path = storage_path
        if storage_type != FileStorageType.LOCAL:
            # 如果是远程存储，需要先下载文件
            local_dir = "temp_downloads"
            if not os.path.exists(local_dir):
                os.makedirs(local_dir, exist_ok=True)
            
            # 使用哈希值生成唯一的文件名，避免文件名过长
            hash_name = hashlib.md5(storage_path.encode()).hexdigest()
            file_ext = os.path.splitext(os.path.basename(storage_path.split('?')[0]))[1]
            if not file_ext:
                file_ext = f".{file_type}"
            
            # 构建本地临时文件路径
            temp_filename = f"{hash_name}{file_ext}"
            local_path = os.path.join(local_dir, temp_filename)
            
            # 如果本地文件不存在，则下载
            if not os.path.exists(local_path):
                if not download_file(storage_path, local_path):
                    raise Exception(f"文件下载失败: {storage_path}")
            file_path = local_path
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 根据解析器类型选择不同的解析策略
        
        return parse_file_to_markdown(file_path, file_type, parser_type)

        
    except Exception as e:
        traceback.print_exc()
        logger.error(f"解析文件失败: {str(e)}")
        logger.error(traceback.format_exc())
        return []
    finally:
        # 如果是远程文件，清理临时文件
        if storage_type != FileStorageType.LOCAL and 'local_path' in locals():
            try:
                if os.path.exists(local_path):
                    os.remove(local_path)
                    logger.info(f"清理临时文件: {local_path}")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {str(e)}")



def parse_file_to_markdown(
    file_path: str, 
    file_type: str, 
    parser_type: str
) -> List[Dict[str, Any]]:
    """
    使用特定解析器解析文件
    
    Args:
        file_path: 文件路径
        file_type: 文件类型
        parser_type: 解析器类型
        
    Returns:
        List[Dict[str, Any]]: 解析后的内容块列表
    """
    markdown_content = None
    
    # 根据解析器类型选择不同的解析策略
    if parser_type == FileParserType.ANNUAL_REPORT:
        # 企业年报解析器
        markdown_content = parse_annual_report(file_path, file_type)
    elif parser_type == FileParserType.LAW_REGULATION:
        # 法律法规解析器
        markdown_content = parse_law_regulation(file_path, file_type)
    elif parser_type == FileParserType.USER_MANUAL:
        # 使用手册解析器
        markdown_content = parse_user_manual(file_path, file_type)
    # elif parser_type == FileParserType.CONTRACT:
    #     # 合同解析器
    #     markdown_content = file_convert_to_markdown_base(file_path, file_type)
    # elif parser_type == FileParserType.IMAGE:
    #     # 图片解析器
    #     markdown_content = file_convert_to_markdown(file_path, file_type)
    # elif parser_type == FileParserType.IMAGE_FILE:
    #     # 图片文件解析器
    #     markdown_content = file_convert_to_markdown(file_path, file_type)
    else:
    #     logger.warning(f"未知解析器类型: {parser_type}，使用默认解析方法")
        markdown_content =  file_convert_to_markdown_base(file_path, file_type)
    return markdown_content


def parse_annual_report(file_path: str, file_type: str) -> str:
    """企业年报解析器"""
    logger.info(f"使用企业年报解析器处理文件: {file_path}")
    return file_analyze_to_markdown(file_path)

def parse_law_regulation(file_path: str, file_type: str) -> str:
    """法律法规解析器"""
    logger.info(f"使用法律法规解析器处理文件: {file_path}")
    return file_analyze_to_markdown(file_path)

def parse_user_manual(file_path: str, file_type: str) -> str:
    """使用手册解析器"""
    logger.info(f"使用使用手册解析器处理文件: {file_path}")
    return file_analyze_to_markdown(file_path)

def parse_contract(raw_text: str) -> List[str]:
    """合同解析器
    
    将合同文本按照条款结构进行解析和切分，并将标题转换为 Markdown 格式
    
    Args:
        raw_text: 原始合同文本
        
    Returns:
        List[str]: 解析后的条款内容块列表
    """
    # 定义条款识别模式
    patterns = {
        'part': [
            r'^(第[一二三四五六七八九十百零]+部分\s+.*)$',  # 中文数字部分
            r'^(第\d+部分\s+.*)$',  # 阿拉伯数字部分
            r'^(第[一二三四五六七八九十百零]+章\s+.*)$',  # 中文数字章节
            r'^(第\d+章\s+.*)$',  # 阿拉伯数字章节
        ],
        'section': [
            r'^(第[一二三四五六七八九十百零]+节\s+.*)$',  # 中文数字节
            r'^(第\d+节\s+.*)$',  # 阿拉伯数字节
        ],
        'clause': [
            r'^(第[一二三四五六七八九十百零]+条\s+.*)$',  # 中文数字条款
            r'^(第\d+条\s+.*)$',  # 阿拉伯数字条款
            r'^(\d+\.\s+.*)$',  # 数字点条款
            r'^([一二三四五六七八九十百零]+、\s+.*)$',  # 中文数字顿号条款
        ]
    }
    
    # 编译所有正则表达式
    compiled_patterns = {
        level: [re.compile(pattern, re.MULTILINE) for pattern in patterns[level]]
        for level in patterns
    }
    
    # 初始化结果列表
    chunks = []
    current_part = ""
    current_section = ""
    current_clause = ""
    current_content = []
    
    # 按行处理文本
    for line in raw_text.split('\n'):
        line = line.strip()
        if not line:
            continue
            
        # 检查是否是部分标题
        is_part = any(pattern.match(line) for pattern in compiled_patterns['part'])
        if is_part:
            # 保存之前的条款（如果有）
            if current_clause and current_content:
                chunk_content = []
                if current_part:
                    chunk_content.append(current_part)
                if current_section:
                    chunk_content.append(current_section)
                if current_clause:
                    chunk_content.append(current_clause)
                chunk_content.extend(current_content)
                chunks.append('\n'.join(chunk_content))
                current_content = []
            
            # 将部分标题转换为 Markdown 格式（使用 #）
            current_part = f"## {line}"
            current_section = ""
            current_clause = ""
            continue
            
        # 检查是否是节标题
        is_section = any(pattern.match(line) for pattern in compiled_patterns['section'])
        if is_section:
            # 保存之前的条款（如果有）
            if current_clause and current_content:
                chunk_content = []
                if current_part:
                    chunk_content.append(current_part)
                if current_section:
                    chunk_content.append(current_section)
                if current_clause:
                    chunk_content.append(current_clause)
                chunk_content.extend(current_content)
                chunks.append('\n'.join(chunk_content))
                current_content = []
            
            # 将节标题转换为 Markdown 格式（使用 ##）
            current_section = f"### {line}"
            current_clause = ""
            continue
            
        # 检查是否是条款标题
        is_clause = any(pattern.match(line) for pattern in compiled_patterns['clause'])
        if is_clause:
            # 保存之前的条款（如果有）
            if current_clause and current_content:
                chunk_content = []
                if current_part:
                    chunk_content.append(current_part)
                if current_section:
                    chunk_content.append(current_section)
                if current_clause:
                    chunk_content.append(current_clause)
                chunk_content.extend(current_content)
                chunks.append('\n'.join(chunk_content))
                current_content = []
            
            # 将条款标题转换为 Markdown 格式（使用 ###）
            current_clause = f"#### {line}"
            continue
            
        # 如果是条款内容
        if current_clause:
            current_content.append(line)
    
    # 保存最后一个条款
    if current_clause and current_content:
        chunk_content = []
        if current_part:
            chunk_content.append(current_part)
        if current_section:
            chunk_content.append(current_section)
        if current_clause:
            chunk_content.append(current_clause)
        chunk_content.extend(current_content)
        chunks.append('\n'.join(chunk_content))
    
    # 添加部分和节作为独立的块
    if current_part:
        chunks.insert(0, current_part)
    
    if current_section:
        section_content = [current_part, current_section]
        chunks.insert(1, '\n'.join(section_content))
    
    return chunks


def markdown_to_check_chunk(markdown_content: str, parser_type: str) -> List[Dict[str, Any]]:
    """将markdown内容转换为chunk，支持多种切分策略
    
    Langchain常用文本切分方法:
    1. 递归字符切分（RecursiveCharacterTextSplitter）: 按字符优先级分割
    2. 文档特定切分（MarkdownHeaderTextSplitter）: 按markdown标题结构切分
    3. 令牌切分（TokenTextSplitter）: 按token计数切分
    4. 语义切分（SemanticChunker）: 按语义相似度切分
    """


    if parser_type == FileParserType.LAW_REGULATION:
        # 企业年报使用标题切分
        headers_to_split_on = [("#","##", "header")]
        splitter = MarkdownHeaderTextSplitter(headers_to_split_on)
        chunks = splitter.split_text(markdown_content)
    
    elif parser_type == FileParserType.CONTRACT:
    #     # 法律法规使用递归字符切分
        # splitter = RecursiveCharacterTextSplitter(
        #     chunk_size=1000,
        #     chunk_overlap=200,
        #     separators=["\n\n", "\n", "。", "；", " "]
        # )
        # chunks = splitter.split_text(markdown_content)
        chunks = parse_contract(markdown_content)
    else:
        # 默认使用令牌切分
        splitter = TokenTextSplitter(
            chunk_size=512,
            chunk_overlap=0,
            encoding_name="cl100k_base"
        )
        chunks = splitter.split_text(markdown_content)
    
    return chunks




# 提供简单的函数接口，方便直接调用
def parse_file_content(
    storage_path: str, 
    file_type: str, 
    parser_type: Optional[str] = None,
    version: Optional[str] = None,
    storage_type: str = FileStorageType.LOCAL
) -> List[Dict[str, Any]]:
    """
    解析文件内容，将文件分割成块
    
    Args:
        storage_path: 文件存储路径（本地路径或远程URL）
        file_type: 文件类型(如pdf, txt, xlsx等)
        parser_type: 解析器类型，默认为None，使用默认解析器
        version: 文件版本号，默认为None
        storage_type: 存储类型（本地或远程），默认为LOCAL
        
    Returns:
        List[Dict[str, Any]]: 解析后的内容块列表
    """
    logger.info(f"开始解析文件: {storage_path}, 类型: {file_type}, 解析器: {parser_type}, 版本: {version}, 存储类型: {storage_type}")
     
    markdown_content = parse_file_markdown(storage_path, file_type, parser_type, version, storage_type)
    logger.info(markdown_content)
    if markdown_content and markdown_content!='':
        chunks = markdown_to_check_chunk(markdown_content,parser_type)
        logger.debug(chunks)
        return chunks
    else:
        return None
   
def download_and_extract_files(download_url: str):
    """下载并解压文件，返回文件内容"""
    logger.info(f"下载压缩文件: {download_url}")
    # 创建临时目录用于下载和解压文件，可通过TEMP_FILE_DIR环境变量指定目录
    with tempfile.TemporaryDirectory(dir=os.getenv('TEMP_FILE_DIR','./')) as temp_dir:
        try:
            # 下载压缩包
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            # 保存压缩文件
            tar_path = os.path.join(temp_dir, "temp_image_png.tar.gz")
            with open(tar_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            logger.info(f"下载压缩文件成功: {tar_path}")
            
            # 解压文件
            with tarfile.open(tar_path, "r:gz") as tar:
                tar.extractall(path=temp_dir)
            logger.info(f"解压文件成功: {temp_dir}")
            
            # 打印解压后的文件列表
            extracted_files = [os.path.join(root, f) for root, _, files in os.walk(temp_dir) for f in files]
            logger.info(f"解压后目录内容: {extracted_files}")

            # 动态查找json和markdown文件
            json_files = [f for f in extracted_files if f.endswith('.json')]
            md_files = [f for f in extracted_files if f.endswith('.md')]

            if not json_files:
                raise FileNotFoundError("未找到JSON文件")
            if not md_files:
                raise FileNotFoundError("未找到Markdown文件")

            # 读取第一个找到的json文件
            json_path = json_files[0]
            with open(json_path, 'r', encoding='utf-8') as f:
                json_content = json.load(f)
            logger.info(f"读取json文件成功: {json_path}")
            
            # 读取第一个找到的markdown文件
            md_path = md_files[0]
            with open(md_path, 'r', encoding='utf-8') as f:
                md_content = f.read()
            logger.info(f"读取markdown文件成功: {md_path}")
            
            return {
                "file_content": json_content,
                "markdown_content": md_content
            }
            
        except Exception as e:
            traceback.print_exc()
            error_detail = f"文件处理失败: {str(e)}\n解压目录内容: {extracted_files if 'extracted_files' in locals() else '未解压成功'}"
            logger.error(error_detail)
            raise HTTPException(
                status_code=500,
                detail=error_detail
            )



def parse_file_content_by_image(
    storage_path: str, 
    file_type: str, 
    parser_type: Optional[str] = None,
    version: Optional[str] = None,
    storage_type: str = FileStorageType.LOCAL,
    end_to_end_url: str = ""
) -> Dict[str, Any]:
    """
    解析文件内容，将文件发送到端到端识别API进行处理
    
    Args:
        storage_path: 文件存储路径（本地路径或远程URL）
        file_type: 文件类型(如pdf, txt, xlsx等)
        parser_type: 解析器类型，默认为None，使用默认解析器
        version: 文件版本号，默认为None
        storage_type: 存储类型（本地或远程），默认为LOCAL
        end_to_end_url: 端到端识别API的URL
        
    Returns:
        Dict[str, Any]: 包含解析结果的字典
    """
    logger.info(f"开始解析文件: {storage_path}, 类型: {file_type}, 解析器: {parser_type}, 版本: {version}, 存储类型: {storage_type}")
    
    # 创建临时目录用于下载和处理文件
    with tempfile.TemporaryDirectory(dir=os.getenv('TEMP_FILE_DIR', './')) as temp_dir:
        try:
            # 处理存储路径
            file_path = storage_path
            
            # 如果是远程存储，需要先下载文件
            if storage_type != FileStorageType.LOCAL:
                # 提取文件扩展名
                file_ext = os.path.splitext(os.path.basename(storage_path.split('?')[0]))[1]
                if not file_ext:
                    file_ext = f".{file_type}"
                
                # 使用哈希值生成唯一的文件名
                hash_name = hashlib.md5(storage_path.encode()).hexdigest()
                temp_filename = f"{hash_name}{file_ext}"
                local_path = os.path.join(temp_dir, temp_filename)
                
                # 下载文件
                logger.info(f"下载远程文件: {storage_path} 到 {local_path}")
                if not download_file(storage_path, local_path):
                    raise Exception(f"文件下载失败: {storage_path}")
                file_path = local_path
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 发送文件到端到端API
            logger.info(f"发送文件到端到端API: {end_to_end_url}")
            with open(file_path, 'rb') as file:
                # 构建multipart/form-data格式的请求，文件字段名为'file'
                files = {'file': (os.path.basename(file_path), file, 'application/octet-stream')}
                response = requests.post(end_to_end_url, files=files)
            
            # 检查响应状态码
            if response.status_code != 200:
                error_msg = f"端到端API调用失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "message": error_msg,
                    "data": None
                }
            
            response_json = response.json()
            logger.info(f"端到端API响应状态码: {response.status_code}")
            
            # 处理API返回的结果链接
            if 'result' not in response_json:
                error_msg = "端到端API响应中不包含结果链接"
                logger.error(error_msg)
                return {
                    "success": False,
                    "message": error_msg,
                    "data": None
                }
            
            # 下载API返回的压缩文件
            download_url = response_json['result']
            logger.info(f"下载API返回的压缩文件: {download_url}")
            
            # 创建临时文件路径
            tar_path = os.path.join(temp_dir, "result.tar.gz")
            
            # 下载压缩包
            download_response = requests.get(download_url, stream=True)
            download_response.raise_for_status()
            
            # 保存压缩文件
            with open(tar_path, 'wb') as f:
                for chunk in download_response.iter_content(chunk_size=8192):
                    f.write(chunk)
            logger.info(f"下载压缩文件成功: {tar_path}")
            
            # 解压文件
            extract_dir = os.path.join(temp_dir, "extracted")
            os.makedirs(extract_dir, exist_ok=True)
            
            with tarfile.open(tar_path, "r:gz") as tar:
                tar.extractall(path=extract_dir)
            logger.info(f"解压文件成功: {extract_dir}")
            
            # 查找解压后的文件
            extracted_files = [os.path.join(root, f) for root, _, files in os.walk(extract_dir) for f in files]
            logger.info(f"解压后目录内容: {extracted_files}")
            
            # 动态查找json和markdown文件
            json_files = [f for f in extracted_files if f.endswith('.json')]
            md_files = [f for f in extracted_files if f.endswith('.md')]
            
            if not json_files:
                raise FileNotFoundError("未找到JSON文件")
            if not md_files:
                raise FileNotFoundError("未找到Markdown文件")
            
            # 读取第一个找到的json文件
            json_path = json_files[0]
            with open(json_path, 'r', encoding='utf-8') as f:
                json_content = json.load(f)
            logger.info(f"读取json文件成功: {json_path}")
            
            # 读取第一个找到的markdown文件
            md_path = md_files[0]
            with open(md_path, 'r', encoding='utf-8') as f:
                md_content = f.read()
            logger.info(f"读取markdown文件成功: {md_path}")
            
            # 如果需要将markdown内容转换为文档块
            chunks = None
            if parser_type:
                chunks = markdown_to_check_chunk(md_content, parser_type)
            
            # 返回结果
            result ={
                    "result_download_url": download_url,
                    "file_content": json_content,
                    "markdown_content": md_content,
                    "chunks": chunks
                }

            
            logger.info("文档解析完成")
            return result
            
        except Exception as e:
            error_detail = f"文件解析失败: {str(e)}"
            logger.error(error_detail)
            traceback.print_exc()
            return None