(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[388],{49842:function(t,n){"use strict";n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"}}]},name:"calendar",theme:"outlined"}},93696:function(t,n){"use strict";n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 336a48 48 0 1096 0 48 48 0 10-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"}}]},name:"info-circle",theme:"outlined"}},52197:function(t,n){"use strict";n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"}}]},name:"star",theme:"filled"}},92287:function(t,n){"use strict";n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"}},97302:function(t,n,e){"use strict";var r=e(1413),a=e(67294),c=e(49842),u=e(91146),o=function(t,n){return a.createElement(u.Z,(0,r.Z)((0,r.Z)({},t),{},{ref:n,icon:c.Z}))},i=a.forwardRef(o);n.Z=i},82061:function(t,n,e){"use strict";var r=e(1413),a=e(67294),c=e(47046),u=e(91146),o=function(t,n){return a.createElement(u.Z,(0,r.Z)((0,r.Z)({},t),{},{ref:n,icon:c.Z}))},i=a.forwardRef(o);n.Z=i},34804:function(t,n,e){"use strict";var r=e(1413),a=e(67294),c=e(66023),u=e(91146),o=function(t,n){return a.createElement(u.Z,(0,r.Z)((0,r.Z)({},t),{},{ref:n,icon:c.Z}))},i=a.forwardRef(o);n.Z=i},47389:function(t,n,e){"use strict";var r=e(1413),a=e(67294),c=e(27363),u=e(91146),o=function(t,n){return a.createElement(u.Z,(0,r.Z)((0,r.Z)({},t),{},{ref:n,icon:c.Z}))},i=a.forwardRef(o);n.Z=i},31545:function(t,n,e){"use strict";e.d(n,{Z:function(){return i}});var r=e(1413),a=e(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M688 312v-48c0-4.4-3.6-8-8-8H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8zm-392 88c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H296zm144 452H208V148h560v344c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V108c0-17.7-14.3-32-32-32H168c-17.7 0-32 14.3-32 32v784c0 17.7 14.3 32 32 32h272c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm445.7 51.5l-93.3-93.3C814.7 780.7 828 743.9 828 704c0-97.2-78.8-176-176-176s-176 78.8-176 176 78.8 176 176 176c35.8 0 69-10.7 96.8-29l94.7 94.7c1.6 1.6 3.6 2.3 5.6 2.3s4.1-.8 5.6-2.3l31-31a7.9 7.9 0 000-11.2zM652 816c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"file-search",theme:"outlined"},u=e(91146),o=function(t,n){return a.createElement(u.Z,(0,r.Z)((0,r.Z)({},t),{},{ref:n,icon:c}))};var i=a.forwardRef(o)},12906:function(t,n,e){"use strict";e.d(n,{Z:function(){return i}});var r=e(1413),a=e(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM512 533c-85.5 0-155.6 67.3-160 151.6a8 8 0 008 8.4h48.1c4.2 0 7.8-3.2 8.1-7.4C420 636.1 461.5 597 512 597s92.1 39.1 95.8 88.6c.3 4.2 3.9 7.4 8.1 7.4H664a8 8 0 008-8.4C667.6 600.3 597.5 533 512 533z"}}]},name:"frown",theme:"outlined"},u=e(91146),o=function(t,n){return a.createElement(u.Z,(0,r.Z)((0,r.Z)({},t),{},{ref:n,icon:c}))};var i=a.forwardRef(o)},56717:function(t,n,e){"use strict";var r=e(1413),a=e(67294),c=e(93696),u=e(91146),o=function(t,n){return a.createElement(u.Z,(0,r.Z)((0,r.Z)({},t),{},{ref:n,icon:c.Z}))},i=a.forwardRef(o);n.Z=i},25820:function(t,n,e){"use strict";var r=e(1413),a=e(67294),c=e(52197),u=e(91146),o=function(t,n){return a.createElement(u.Z,(0,r.Z)((0,r.Z)({},t),{},{ref:n,icon:c.Z}))},i=a.forwardRef(o);n.Z=i},75750:function(t,n,e){"use strict";e.d(n,{Z:function(){return i}});var r=e(1413),a=e(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},u=e(91146),o=function(t,n){return a.createElement(u.Z,(0,r.Z)((0,r.Z)({},t),{},{ref:n,icon:c}))};var i=a.forwardRef(o)},87784:function(t,n,e){"use strict";e.d(n,{Z:function(){return i}});var r=e(1413),a=e(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"},u=e(91146),o=function(t,n){return a.createElement(u.Z,(0,r.Z)((0,r.Z)({},t),{},{ref:n,icon:c}))};var i=a.forwardRef(o)},64029:function(t,n,e){"use strict";var r=e(1413),a=e(67294),c=e(92287),u=e(91146),o=function(t,n){return a.createElement(u.Z,(0,r.Z)((0,r.Z)({},t),{},{ref:n,icon:c.Z}))},i=a.forwardRef(o);n.Z=i},93933:function(t,n,e){"use strict";e.d(n,{$Z:function(){return k},$o:function(){return h},Db:function(){return v},Mw:function(){return s},SJ:function(){return w},X1:function(){return b},Xw:function(){return p},bk:function(){return C},fx:function(){return P},qP:function(){return E},tn:function(){return g},zl:function(){return N}});var r=e(15009),a=e.n(r),c=e(99289),u=e.n(c),o=e(78158),i=e(10981);function s(t){return f.apply(this,arguments)}function f(){return(f=u()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,o.N)("/api/myConversations",{method:"GET",params:n}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function p(t){return l.apply(this,arguments)}function l(){return(l=u()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,o.N)("/api/conversations",{method:"POST",data:n}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function h(t,n){return d.apply(this,arguments)}function d(){return(d=u()(a()().mark((function t(n,e){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,o.N)("/api/conversationActive/"+n,{method:"PUT",data:e}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function v(t){return m.apply(this,arguments)}function m(){return(m=u()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,o.N)("/api/clearConversation/"+n,{method:"PUT"}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function w(t){return Z.apply(this,arguments)}function Z(){return(Z=u()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,o.N)("/api/conversations/"+n,{method:"DELETE"}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function b(t,n){return y.apply(this,arguments)}function y(){return(y=u()(a()().mark((function t(n,e){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,o.N)("/api/conversations/".concat(n),{method:"PUT",data:e}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function g(t){return x.apply(this,arguments)}function x(){return(x=u()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,o.N)("/api/message",{method:"POST",data:n}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function k(t){return z.apply(this,arguments)}function z(){return(z=u()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,o.N)("/api/messages/".concat(n),{method:"DELETE"}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function E(t){return T.apply(this,arguments)}function T(){return(T=u()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,o.N)("/api/delete_messages",{method:"DELETE",data:n}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function C(t){return S.apply(this,arguments)}function S(){return(S=u()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,o.N)("/api/messages/collected",{method:"POST",data:n}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function P(t){return M.apply(this,arguments)}function M(){return(M=u()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,o.N)("/api/app/get_knowledge_bases",{method:"POST",data:n}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function N(t){return L.apply(this,arguments)}function L(){return(L=u()(a()().mark((function t(n){var e,r;return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=(0,i.bW)(),t.next=3,fetch("/api/app/chat/chat2kb",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(n)});case 3:if((r=t.sent).ok){t.next=6;break}throw new Error("HTTP 错误！状态码：".concat(r.status));case 6:return t.abrupt("return",r);case 7:case"end":return t.stop()}}),t)})))).apply(this,arguments)}},13973:function(t,n,e){"use strict";e.d(n,{Z:function(){return y}});var r=e(15009),a=e.n(r),c=e(99289),u=e.n(c),o=e(5574),i=e.n(o),s=e(67294),f=e(55102),p=e(2453),l=e(17788),h=e(84567),d=e(78158);function v(t){return m.apply(this,arguments)}function m(){return(m=u()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,d.N)("/api/feedbacks",{method:"POST",data:n}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}var w=e(85893),Z=f.Z.TextArea,b=[{label:"回答不准确",value:"inaccurate"},{label:"回答不完整",value:"incomplete"},{label:"理解错误",value:"irrelevant"},{label:"生成幻觉",value:"hallucination"},{label:"内容混乱",value:"error"},{label:"有害内容",value:"harmful"},{label:"其他问题",value:"other"}],y=function(t){var n=t.visible,e=t.messageId,r=t.conversationId,c=t.appInfo,o=t.onClose,f=s.useState(""),d=i()(f,2),m=d[0],y=d[1],g=s.useState([]),x=i()(g,2),k=x[0],z=x[1],E=function(){var t=u()(a()().mark((function t(){var n;return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(0!==k.length){t.next=3;break}return p.ZP.error("请至少选择一个反馈类型"),t.abrupt("return");case 3:return t.prev=3,n={message_id:e,conversation_id:r,app_info:c,content:m,feedback_types:k},console.log("feedbackData===>",n),t.next=8,v(n);case 8:t.sent.success?(p.ZP.success("感谢您的反馈！"),T()):p.ZP.error("提交反馈失败，请稍后重试"),t.next=16;break;case 12:t.prev=12,t.t0=t.catch(3),console.error("提交反馈失败:",t.t0),p.ZP.error("提交反馈失败，请稍后重试");case 16:case"end":return t.stop()}}),t,null,[[3,12]])})));return function(){return t.apply(this,arguments)}}(),T=function(){y(""),z([]),o()};return(0,w.jsxs)(l.Z,{title:"反馈问题",open:n,onOk:E,onCancel:T,okText:"提交",cancelText:"取消",children:[(0,w.jsxs)("div",{style:{marginBottom:16},children:[(0,w.jsx)("div",{style:{marginBottom:8},children:"请选择存在的问题（可多选）："}),(0,w.jsx)(h.Z.Group,{options:b,value:k,onChange:function(t){return z(t)}})]}),(0,w.jsxs)("div",{children:[(0,w.jsx)("div",{style:{marginBottom:8},children:"详细反馈（选填）："}),(0,w.jsx)(Z,{value:m,onChange:function(t){return y(t.target.value)},placeholder:"请输入您的具体反馈意见...",rows:4})]})]})}},45855:function(t,n,e){"use strict";e.d(n,{Nk:function(){return i},wb:function(){return f}});var r=e(15009),a=e.n(r),c=e(99289),u=e.n(c),o=e(78158);function i(t){return s.apply(this,arguments)}function s(){return(s=u()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,o.N)("/api/wjb/data-query",{method:"POST",data:n}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function f(t){return p.apply(this,arguments)}function p(){return(p=u()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,o.N)("/api/fina/data-query",{method:"POST",data:n}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}},64599:function(t,n,e){var r=e(96263);t.exports=function(t,n){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=r(t))||n&&t&&"number"==typeof t.length){e&&(t=e);var a=0,c=function(){};return{s:c,n:function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function(t){throw t},f:c}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,o=!0,i=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return o=t.done,t},e:function(t){i=!0,u=t},f:function(){try{o||null==e.return||e.return()}finally{if(i)throw u}}}},t.exports.__esModule=!0,t.exports.default=t.exports}}]);