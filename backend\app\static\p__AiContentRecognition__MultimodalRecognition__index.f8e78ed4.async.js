"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3493],{37142:function(e,t,n){n.r(t),n.d(t,{default:function(){return B}});var r=n(15009),s=n.n(r),l=n(99289),i=n.n(l),o=n(5574),d=n.n(o),a=n(42119),c=n(4393),x=n(27808),u=n(42075),p=n(32983),h=n(71471),g=n(11550),f=n(83622),m=n(71230),y=n(15746),j=n(66309),b=n(2453),v=n(74330),Z=n(38703),w=n(67294),R=n(55287),k=n(88484),T=n(15360),S=n(79457),C=n(87784),_=n(58831),z=n(89514),P=n(52514),A=n(79090),I=n(78158);function F(e){return L.apply(this,arguments)}function L(){return(L=i()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,I.N)("/api/ai-content-recognition/multimodal-recognition",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var U=n(10048),N=n(85893),O=(0,U.Z)({html:!0,breaks:!0}),E=a.Z.Step,D=w.memo((function(e){var t=e.imageUrl,n=e.onUpload,r=e.fileList,s=e.zoom;return(0,N.jsx)(c.Z,{bordered:!1,className:"image-preview-card",style:{height:"100%",display:"flex",flexDirection:"column"},children:(0,N.jsx)("div",{style:{flex:1,overflow:"hidden",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"#ffffff",borderRadius:"8px"},children:t?(0,N.jsx)("div",{style:{width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",overflow:"auto"},children:(0,N.jsx)(x.Z,{src:t,style:{maxWidth:s?"".concat(100*s,"%"):"90%",maxHeight:s?"".concat(100*s,"%"):"90%",objectFit:"contain"},preview:{mask:(0,N.jsxs)(u.Z,{children:[(0,N.jsx)(R.Z,{}),(0,N.jsx)("span",{children:"查看原图"})]})}})}):(0,N.jsxs)("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",backgroundColor:"#fff",padding:"20px"},children:[(0,N.jsx)(p.Z,{image:p.Z.PRESENTED_IMAGE_SIMPLE,description:(0,N.jsx)(h.Z.Text,{type:"secondary",children:"请上传待识别图片"})}),(0,N.jsx)(g.Z,{accept:"image/*",showUploadList:!1,fileList:r,beforeUpload:n,style:{marginTop:"16px"},children:(0,N.jsx)(f.ZP,{type:"primary",icon:(0,N.jsx)(k.Z,{}),children:"上传图片"})})]})})})})),H=function(e){return{0:"标题 (title)",1:"正文 (plain text)",2:"废弃区域 (abandon)",3:"图形 (figure)",4:"图形标题 (figure_caption)",5:"表格 (table)",6:"表格标题 (table_caption)",7:"表格脚注 (table_footnote)",8:"独立公式 (isolate_formula)",9:"公式标题 (formula_caption)"}[e]||"类别".concat(e)},M=function(e){return{0:"magenta",1:"blue",2:"gray",3:"cyan",4:"geekblue",5:"purple",6:"volcano",7:"orange",8:"gold",9:"lime"}[e]||"default"},W=function(e){var t,n,r,s,l,i=e.imageContent;if(!i||!i.isAnalyzed)return(0,N.jsx)(c.Z,{bordered:!1,style:{height:"100%"},children:(0,N.jsx)(p.Z,{description:"暂无分析结果"})});if(i.multimodalResult){var o=O.render(i.multimodalResult.analysis);return(0,N.jsxs)(c.Z,{title:(0,N.jsxs)(u.Z,{children:[(0,N.jsx)(T.Z,{}),(0,N.jsx)("span",{children:"多模态分析结果"})]}),bordered:!1,style:{height:"100%",overflow:"auto"},children:[(0,N.jsx)("div",{className:"markdown-content",style:{padding:"8px 0",fontSize:"14px",lineHeight:"1.6"},dangerouslySetInnerHTML:{__html:o}}),(0,N.jsx)("style",{children:"\n          .markdown-content h3 {\n            font-size: 18px;\n            margin-top: 24px;\n            margin-bottom: 12px;\n            font-weight: 600;\n            color: #1890ff;\n          }\n          .markdown-content h4 {\n            font-size: 16px;\n            margin-top: 16px;\n            margin-bottom: 8px;\n            font-weight: 500;\n          }\n          .markdown-content h5 {\n            font-size: 15px;\n            margin-top: 14px;\n            margin-bottom: 8px;\n            font-weight: 500;\n            color: #722ed1;\n          }\n          .markdown-content p {\n            margin-bottom: 12px;\n          }\n          .markdown-content ul, .markdown-content ol {\n            padding-left: 20px;\n            margin: 8px 0 16px 0;\n          }\n          .markdown-content li {\n            margin-bottom: 4px;\n          }\n          .markdown-content code {\n            background-color: #f5f5f5;\n            padding: 2px 4px;\n            border-radius: 3px;\n            font-family: Consolas, Monaco, 'Andale Mono', monospace;\n          }\n          .markdown-content blockquote {\n            border-left: 4px solid #ddd;\n            padding-left: 16px;\n            margin-left: 0;\n            color: #666;\n          }\n        "})]})}return(0,N.jsx)(c.Z,{title:(0,N.jsxs)(u.Z,{children:[(0,N.jsx)(S.Z,{}),(0,N.jsx)("span",{children:"多模态分析结果"})]}),bordered:!1,style:{height:"100%",overflow:"auto"},children:(0,N.jsx)(m.Z,{gutter:[16,16],children:(0,N.jsxs)(y.Z,{span:24,children:[null!==(t=i.layoutResult)&&void 0!==t&&t.image?(0,N.jsxs)("div",{style:{textAlign:"center"},children:[(0,N.jsx)(x.Z,{src:i.layoutResult.image.startsWith("data:")?i.layoutResult.image:"data:image/png;base64,".concat(i.layoutResult.image),alt:"OCR结果",style:{maxWidth:"100%"},preview:{mask:(0,N.jsxs)(u.Z,{children:[(0,N.jsx)(R.Z,{}),(0,N.jsx)("span",{children:"查看详情"})]})}}),(null===(n=i.layoutResult)||void 0===n?void 0:n.boxes)&&i.layoutResult.boxes.length>0&&i.layoutResult.scores&&i.layoutResult.classes&&(0,N.jsxs)("div",{style:{marginTop:"8px",textAlign:"left"},children:[(0,N.jsxs)(h.Z.Text,{type:"secondary",children:["识别到 ",(null===(r=i.layoutResult)||void 0===r||null===(r=r.boxes)||void 0===r?void 0:r.length)||0," 个区域",(null===(s=i.layoutResult)||void 0===s?void 0:s.scores)&&", 置信度: ".concat(i.layoutResult.scores.map((function(e){return(100*e).toFixed(0)+"%"})).join(", "))]}),(null===(l=i.layoutResult)||void 0===l?void 0:l.classes)&&(0,N.jsxs)("div",{style:{marginTop:"8px",marginBottom:"16px"},children:[(0,N.jsx)(h.Z.Text,{strong:!0,children:"类别统计："}),(0,N.jsx)("div",{style:{marginTop:"8px",display:"flex",flexWrap:"wrap",gap:"8px"},children:Array.from(new Set(i.layoutResult.classes)).map((function(e){var t=i.layoutResult.classes.filter((function(t){return t===e})).length;return(0,N.jsxs)(j.Z,{color:M(e),children:[H(e),": ",t,"个"]},e)}))})]}),(0,N.jsxs)("div",{style:{marginTop:"16px",overflow:"auto"},children:[(0,N.jsxs)("div",{style:{marginTop:"0px"},children:[(0,N.jsx)(h.Z.Text,{strong:!0,children:"区域明细："}),(0,N.jsxs)("table",{style:{width:"100%",borderCollapse:"collapse",marginTop:"8px",fontSize:"12px"},children:[(0,N.jsx)("thead",{children:(0,N.jsxs)("tr",{style:{backgroundColor:"#f0f0f0"},children:[(0,N.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"序号"}),(0,N.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"区域坐标 [x1, y1, x2, y2]"}),(0,N.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"置信度"}),(0,N.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"类别"})]})}),(0,N.jsx)("tbody",{children:i.layoutResult.boxes.map((function(e,t){var n,r;return(0,N.jsxs)("tr",{children:[(0,N.jsx)("td",{style:{padding:"4px",border:"1px solid #d9d9d9",textAlign:"center"},children:t+1}),(0,N.jsxs)("td",{style:{padding:"4px",border:"1px solid #d9d9d9",fontFamily:"monospace"},children:["[",e.map((function(e){return e.toFixed(1)})).join(", "),"]"]}),(0,N.jsx)("td",{style:{padding:"4px",border:"1px solid #d9d9d9",textAlign:"center"},children:null!==(n=i.layoutResult)&&void 0!==n&&n.scores?(100*i.layoutResult.scores[t]).toFixed(2)+"%":"未知"}),(0,N.jsx)("td",{style:{padding:"4px",border:"1px solid #d9d9d9",textAlign:"center"},children:null!==(r=i.layoutResult)&&void 0!==r&&r.classes?(0,N.jsx)(j.Z,{color:M(i.layoutResult.classes[t]),children:H(i.layoutResult.classes[t])}):"未知"})]},t)}))})]})]}),(0,N.jsx)(h.Z.Text,{strong:!0,style:{display:"block",marginTop:"16px"},children:"识别详情："}),(0,N.jsx)("div",{style:{backgroundColor:"#f9f9f9",padding:"8px",borderRadius:"4px",marginTop:"8px",maxHeight:"200px",overflow:"auto"},children:(0,N.jsxs)(h.Z.Text,{code:!0,children:["{",'code: 0, message: "success", data: ',"{",(0,N.jsx)("br",{}),"  layout_result: ","{"," ",(0,N.jsx)("br",{}),"    boxes: ",JSON.stringify(i.layoutResult.boxes,null,2),", ",(0,N.jsx)("br",{}),"    scores: ",JSON.stringify(i.layoutResult.scores,null,2),", ",(0,N.jsx)("br",{}),"    classes: ",JSON.stringify(i.layoutResult.classes,null,2)," ",(0,N.jsx)("br",{}),"  ","}","}","}"]})})]})]})]}):(0,N.jsx)(N.Fragment,{}),i.ocrResult&&(0,N.jsxs)("div",{style:{marginTop:"24px"},children:[(0,N.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,N.jsx)(h.Z.Text,{strong:!0,children:"识别文本："}),(0,N.jsx)("div",{style:{backgroundColor:"#f9f9f9",padding:"12px",borderRadius:"4px",marginTop:"8px",maxHeight:"200px",overflow:"auto",whiteSpace:"pre-wrap",fontSize:"14px"},children:i.ocrResult.text})]}),i.ocrResult.results&&i.ocrResult.results.length>0&&i.ocrResult.results[0]&&(0,N.jsxs)("div",{children:[(0,N.jsxs)(h.Z.Text,{strong:!0,children:["识别详情（共 ",i.ocrResult.results[0].length," 项）："]}),(0,N.jsx)("div",{style:{marginTop:"8px",overflow:"auto",maxHeight:"400px"},children:(0,N.jsxs)("table",{style:{width:"100%",borderCollapse:"collapse",fontSize:"12px"},children:[(0,N.jsx)("thead",{children:(0,N.jsxs)("tr",{style:{backgroundColor:"#f0f0f0"},children:[(0,N.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"序号"}),(0,N.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"文本内容"}),(0,N.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"置信度"}),(0,N.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"坐标 [x1,y1,x2,y2,x3,y3,x4,y4]"})]})}),(0,N.jsx)("tbody",{children:i.ocrResult.results[0].map((function(e,t){var n=e[0],r=e[1],s=r[0],l=r[1];return(0,N.jsxs)("tr",{children:[(0,N.jsx)("td",{style:{padding:"4px",border:"1px solid #d9d9d9",textAlign:"center"},children:t+1}),(0,N.jsx)("td",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:s}),(0,N.jsxs)("td",{style:{padding:"4px",border:"1px solid #d9d9d9",textAlign:"center"},children:[(100*l).toFixed(2),"%"]}),(0,N.jsx)("td",{style:{padding:"4px",border:"1px solid #d9d9d9",fontFamily:"monospace",fontSize:"10px"},children:n.map((function(e,t){return"[".concat(e[0],",").concat(e[1],"]").concat(t<n.length-1?",":"")}))})]},t)}))})]})})]})]})]})})})},B=function(){var e=(0,w.useState)(null),t=d()(e,2),n=t[0],r=t[1],l=(0,w.useState)([]),o=d()(l,2),x=o[0],p=o[1],j=(0,w.useState)(null),R=d()(j,2),T=R[0],S=R[1],I=(0,w.useState)(!1),L=d()(I,2),U=L[0],O=L[1],H=(0,w.useRef)(!0),M=(0,w.useState)(0),B=d()(M,2),J=B[0],q=B[1],G=(0,w.useState)(0),K=d()(G,2),Q=K[0],V=K[1],X=(0,w.useState)(null),Y=d()(X,2),$=Y[0],ee=Y[1],te=function(){var e=i()(s()().mark((function e(t){var n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("开始上传文件:",t.name),t.type.startsWith("image/")){e.next=4;break}return b.ZP.error("只接受图片文件"),e.abrupt("return",!1);case 4:if(t.size/1024/1024<10){e.next=8;break}return b.ZP.error("图片大小不能超过 10MB"),e.abrupt("return",!1);case 8:return e.prev=8,n=URL.createObjectURL(t),r(n),p([{uid:"-1",name:t.name,status:"done",url:n,originFileObj:t}]),ee(null),V(1),b.ZP.success("图片上传成功"),console.log("图片上传成功，URL:",n),e.abrupt("return",!1);case 19:return e.prev=19,e.t0=e.catch(8),b.ZP.error("图片处理失败，请重试"),console.error("图片上传或处理失败:",e.t0),e.abrupt("return",!1);case 24:case"end":return e.stop()}}),e,null,[[8,19]])})));return function(t){return e.apply(this,arguments)}}(),ne=function(e){return new Promise((function(t,n){var r=new FileReader;r.readAsDataURL(e),r.onload=function(){if("string"==typeof r.result){var e=r.result.split(",")[1];t(e)}else n(new Error("无法读取文件"))},r.onerror=function(e){return n(e)}}))},re=function(){var e=i()(s()().mark((function e(){var t,r,l,i,o,d,a,c;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.info("=======>handleRecognize"),n){e.next=4;break}return b.ZP.warning("请先上传图片！"),e.abrupt("return");case 4:if(O(!0),q(0),V(2),H.current=!0,e.prev=8,r=null===(t=x[0])||void 0===t?void 0:t.originFileObj){e.next=12;break}throw new Error("文件对象不存在");case 12:return e.next=14,ne(r);case 14:return l=e.sent,i=setInterval((function(){H.current?q((function(e){return e>=90?(clearInterval(i),e):e+5})):clearInterval(i)}),200),e.next=18,F({base64Image:l,fileName:r.name});case 18:o=e.sent,clearInterval(i),q(100),console.log("多模态识别API返回结果:",o),o.success?(d=null,a=null,c=void 0,o.data&&(o.data.multimodal_recognition&&(c={analysis:o.data.multimodal_recognition}),o.data.layout_result&&(d=o.data.layout_result),o.data.ocr_result&&(a=o.data.ocr_result)),ee({fileName:r.name,isAnalyzed:!0,layoutResult:d,ocrResult:a,multimodalResult:c}),b.ZP.success("多模态识别成功！"),V(3)):(b.ZP.error(o.message||"识别失败"),ee(null)),e.next=30;break;case 25:e.prev=25,e.t0=e.catch(8),b.ZP.error("识别过程中出现错误"),console.error(e.t0),ee(null);case 30:return e.prev=30,setTimeout((function(){O(!1),q(0)}),500),e.finish(30);case 33:case"end":return e.stop()}}),e,null,[[8,25,30,33]])})));return function(){return e.apply(this,arguments)}}();return(0,N.jsxs)("div",{style:{display:"flex",flexDirection:"column",height:"100vh",width:"100%",backgroundColor:"#f5f5f5",padding:"16px"},children:[(0,N.jsxs)(c.Z,{bordered:!1,style:{marginBottom:"16px"},children:[(0,N.jsxs)(m.Z,{align:"middle",justify:"space-between",gutter:[16,16],children:[(0,N.jsx)(y.Z,{xs:24,md:8,children:(0,N.jsx)(h.Z.Title,{level:4,style:{margin:0},children:"多模态识别"})}),(0,N.jsx)(y.Z,{xs:24,md:16,children:(0,N.jsx)(m.Z,{justify:"end",align:"middle",gutter:[16,16],children:(0,N.jsx)(y.Z,{children:(0,N.jsxs)(u.Z,{size:"middle",children:[(0,N.jsx)(g.Z,{accept:"image/*",showUploadList:!1,fileList:x,beforeUpload:te,children:(0,N.jsx)(f.ZP,{icon:(0,N.jsx)(k.Z,{}),children:"上传图片"})}),(0,N.jsx)(f.ZP,{type:"primary",icon:U?(0,N.jsx)(C.Z,{}):(0,N.jsx)(_.Z,{}),onClick:U?function(){H.current=!1}:re,disabled:!n,danger:U,loading:U,children:U?"停止":"多模态分析"}),(0,N.jsxs)(u.Z,{children:[(0,N.jsx)(f.ZP,{icon:(0,N.jsx)(z.Z,{}),onClick:function(){T?T<4&&S(T+.1):S(1)},disabled:!n}),(0,N.jsx)(f.ZP,{icon:(0,N.jsx)(P.Z,{}),onClick:function(){T?T>.2&&S(T-.1):S(1)},disabled:!n}),(0,N.jsx)("span",{style:{opacity:T?1:.5},children:T?"".concat((100*T).toFixed(0),"%"):"自动"})]})]})})})})]}),(Q>0||U)&&(0,N.jsx)(m.Z,{style:{marginTop:"16px"},children:(0,N.jsx)(y.Z,{span:24,children:(0,N.jsxs)(a.Z,{current:Q,size:"small",progressDot:!0,children:[(0,N.jsx)(E,{title:"上传"}),(0,N.jsx)(E,{title:"准备"}),(0,N.jsx)(E,{title:U?"分析中 ".concat(J,"%"):"处理"}),(0,N.jsx)(E,{title:"完成"})]})})})]}),(0,N.jsxs)(m.Z,{gutter:[16,16],style:{flex:1,overflow:"hidden"},children:[(0,N.jsx)(y.Z,{xs:24,md:12,style:{height:"100%"},children:(0,N.jsx)(D,{imageUrl:n,onUpload:te,fileList:x,zoom:T})}),(0,N.jsx)(y.Z,{xs:24,md:12,style:{height:"100%",overflow:"auto"},children:U?(0,N.jsx)(c.Z,{bordered:!1,style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},bodyStyle:{width:"100%",textAlign:"center"},children:(0,N.jsxs)(u.Z,{direction:"vertical",size:"large",style:{width:"100%"},children:[(0,N.jsx)(v.Z,{size:"large",indicator:(0,N.jsx)(A.Z,{style:{fontSize:36},spin:!0})}),(0,N.jsx)(h.Z.Title,{level:4,children:"正在进行多模态分析..."}),(0,N.jsx)(Z.Z,{percent:J,status:"active",style:{width:"80%",margin:"0 auto"}})]})}):(0,N.jsx)(W,{imageContent:$})})]})]})}}}]);