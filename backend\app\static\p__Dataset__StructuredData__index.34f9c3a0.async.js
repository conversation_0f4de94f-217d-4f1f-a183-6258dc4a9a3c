"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[515],{75361:function(e,t,n){n.r(t),n.d(t,{default:function(){return I}});var r=n(15009),a=n.n(r),s=n(99289),u=n.n(s),i=n(5574),c=n.n(i),o=n(67294),l=n(97131),p=n(12453),d=n(17788),f=n(2453),v=n(66309),x=n(83622),h=n(35312),g=n(97857),m=n.n(g),b=n(19632),w=n.n(b),k=n(8232),y=n(34994),j=n(5966),Z=n(90672),S=n(64317),C=n(85893),P=function(e){var t=e.modalVisible,n=e.onCancel,r=e.onSubmit,s=e.values,i=k.Z.useForm(),l=c()(i,1)[0],p=o.useState([]),f=c()(p,2),v=f[0],x=f[1];o.useEffect((function(){t&&s&&l.setFieldsValue(s)}),[t,s,l]);var h=function(){var e=u()(a()().mark((function e(){var t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,l.validateFields();case 3:t=e.sent,r(m()(m()({},s),t)),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("表单验证失败:",e.t0);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();return(0,C.jsx)(d.Z,{title:"更新数据",visible:t,onCancel:n,onOk:h,destroyOnClose:!0,children:(0,C.jsxs)(y.A,{form:l,layout:"vertical",initialValues:s,submitter:!1,children:[(0,C.jsx)(j.Z,{name:"name",label:"数据集名称",rules:[{required:!0,message:"请输入数据集名称"}]}),(0,C.jsx)(Z.Z,{name:"description",label:"数据集描述",rules:[{required:!0,message:"请输入数据集描述"}]}),(0,C.jsx)(S.Z,{name:"tags",label:"数据集标签",mode:"tags",placeholder:"请输入数据标签",onChange:function(e){var t=e.map((function(e){return{label:e,value:e}}));x((function(e){var n=e.map((function(e){return e.value})),r=t.filter((function(e){return!n.includes(e.value)}));return[].concat(w()(e),w()(r))}))},options:v}),(0,C.jsx)(S.Z,{name:"processing_status",label:"数据处理状态",options:[{label:"待处理",value:"pending"},{label:"处理中",value:"processing"},{label:"已处理",value:"completed"},{label:"出错",value:"error"}],rules:[{required:!0,message:"请选择数据处理状态"}]})]})})},_=n(44373),z=["purple"],I=function(){var e=(0,o.useRef)(),t=(0,o.useState)(!1),n=c()(t,2),r=n[0],s=n[1],i=(0,o.useState)(void 0),g=c()(i,2),m=g[0],b=g[1],w=(0,o.useState)(0),k=c()(w,2),y=k[0],j=k[1],Z=(0,h.useNavigate)(),S=function(){var t=u()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:d.Z.confirm({title:"确认删除",content:"确定要删除这个数据集吗？",okText:"确认",cancelText:"取消",onOk:function(){var t=u()(a()().mark((function t(){var r,s;return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=f.ZP.loading("正在删除"),t.prev=1,t.next=4,(0,_.n1)(n);case 4:return r(),f.ZP.success("删除成功"),null===(s=e.current)||void 0===s||s.reload(),t.abrupt("return",!0);case 10:return t.prev=10,t.t0=t.catch(1),r(),f.ZP.error("删除失败，请重试"),t.abrupt("return",!1);case 15:case"end":return t.stop()}}),t,null,[[1,10]])})));return function(){return t.apply(this,arguments)}}()});case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),I=function(){var t=u()(a()().mark((function t(n){var r,u;return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=f.ZP.loading("正在更新"),t.prev=1,t.next=4,(0,_.LO)(n.id,n);case 4:return r(),f.ZP.success("更新成功"),s(!1),b(void 0),null===(u=e.current)||void 0===u||u.reload(),t.abrupt("return",!0);case 12:return t.prev=12,t.t0=t.catch(1),r(),f.ZP.error("更新失败，请重试"),t.abrupt("return",!1);case 17:case"end":return t.stop()}}),t,null,[[1,12]])})));return function(e){return t.apply(this,arguments)}}(),R=[{title:"数据集名称",dataIndex:"name",render:function(e,t){return(0,C.jsx)("a",{onClick:function(){return Z("/dataset/structured/view/".concat(t.id))},children:t.name})}},{title:"描述",dataIndex:"description",ellipsis:!0},{title:"数据量",dataIndex:"row_count",search:!1},{title:"状态",dataIndex:"processing_status",valueEnum:{pending:{text:"待处理",status:"default"},processing:{text:"处理中",status:"processing"},completed:{text:"已处理",status:"success"},error:{text:"出错",status:"error"}}},{title:"标签",dataIndex:"tags",render:function(e,t){var n;return(0,C.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:"4px"},children:null===(n=t.tags)||void 0===n?void 0:n.map((function(e,t){return(0,C.jsx)(v.Z,{color:z[t%z.length],style:{borderRadius:"12px",padding:"0 10px",fontSize:"12px",lineHeight:"20px"},children:e},e)}))})}},{title:"操作",valueType:"option",render:function(e,t){return[(0,C.jsx)("a",{onClick:function(){return Z("/dataset/structured/view/".concat(t.id))},children:"查看"},"view"),(0,C.jsx)(x.ZP,{type:"link",onClick:function(){s(!0),b(t)},children:"编辑"},"edit"),(0,C.jsx)(x.ZP,{type:"link",danger:!0,onClick:function(){return S(t.id)},children:"删除"},"delete")]}}];return(0,C.jsxs)(l._z,{extra:[(0,C.jsxs)("div",{style:{marginRight:"24px"},children:[(0,C.jsx)("span",{style:{fontSize:"16px"},children:"总数据量："}),(0,C.jsx)("span",{style:{fontSize:"20px",fontWeight:"bold",color:"#1890ff"},children:y.toLocaleString()}),(0,C.jsx)("span",{style:{fontSize:"16px"},children:" 行"})]},"statistics")],children:[(0,C.jsx)(p.Z,{actionRef:e,columns:R,request:function(){var e=u()(a()().mark((function e(t,n,r){var s;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,_.XB)({current:t.current,pageSize:t.pageSize,name:t.name,description:t.description,processing_status:t.processing_status,tags:t.tags,sorter:n,filter:r});case 3:return s=e.sent,console.log("API Response:",s),j((null==s?void 0:s.total_rows)||0),e.abrupt("return",{data:s.data||[],success:!0,total:s.total||0});case 9:return e.prev=9,e.t0=e.catch(0),console.error("获取数据失败:",e.t0),f.ZP.error("获取数据失败"),e.abrupt("return",{data:[],success:!1,total:0});case 14:case"end":return e.stop()}}),e,null,[[0,9]])})));return function(t,n,r){return e.apply(this,arguments)}}(),rowKey:"id",pagination:{showSizeChanger:!0},toolBarRender:function(){return[(0,C.jsx)(x.ZP,{type:"primary",onClick:function(){return Z("/dataset/structured/new")},children:"新建"},"create")]}}),m&&(0,C.jsx)(P,{onSubmit:function(){var e=u()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,I(t);case 2:e.sent&&(s(!1),b(void 0));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),onCancel:function(){s(!1),b(void 0)},modalVisible:r,values:m})]})}}}]);