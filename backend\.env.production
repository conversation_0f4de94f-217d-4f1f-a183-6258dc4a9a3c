SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_SECONDS=360000

ENV=production
DEBUG=false
PORT=8800

# MongoDB配置
MONGODB_USER=wiseAgent001
MONGODB_PASSWORD=1qaz@WSX
DATABASE_NAME=wiseagent
MONGODB_AUTH_SOURCE=wiseagent
MONGODB_HOST=************
MONGODB_PORT=37017

# 文件上传配置
FILE_UPLOAD_PATH=./static/uploads

# MinIO配置
MINIO_ENABLE=true
MINIO_ENDPOINT=**************:9002
MINIO_ACCESS_KEY=qFHq6y3pNfboA7YBNynE
MINIO_SECRET_KEY=P5lTULcF2IEtX47mkdmfuDpQVkYJEeL2AKEhvDRr
MINIO_BUCKET=wiseagentfiles

# Elasticsearch配置
ES_HOST=http://**************:9600
ES_INDEX=wise_agent_chunk_index

# 开发环境特定配置
RELOAD=true
LOG_LEVEL=debug
INDEXER_ENABLED=false

# Milvus配置
MILVUS_HOST="http://************:19530"
MILVUS_COLLECTION_NAME="wise_agent_chunking"
MILVUS_EMBEDDING_FIELD="embedding"


# COE引擎配置
COE_ENGINE_ENABLED=fasle

# BI配置
BI_APP_ID=wisebi_653a86b5e
BI_APP_SECRET=1cf3cfa89466419baa1cf2e03b0eeee6
BI_APP_URL=http://************:9199/openapi/query

# 开发环境特定配置 CRITICAL ERROR WARNING INFO DEBUG
DEBUG=true
RELOAD=true
LOG_LEVEL=INFO
INDEXER_ENABLED=False

TOKENS_COUNT_MODEL=gpt-4


LOCAL_DATA_PATH=./data

# Elasticsearch配置

ES_HOST=[{"host": "**************", "port": 9600},{"host": "**************", "port": 9600}]
ES_CHUNK_INDEX_NAME=wise_agent_chunk_index

TOKENS_COUNT_MODEL=gpt-4


# 文档解析配置
upload_to_minio = True
ocr_bounding_box_x_dimension_widen = 15  # 将文本区域裁剪给ocr工具时 X 轴拓宽的值的1/2（以防止漏识别）
ocr_bounding_box_y_dimension_widen = 10  # 将文本区域裁剪给ocr工具时 Y 轴拓宽的值的1/2（以防止漏识别）

# MinIO配置
minio_endpoint = **************:9002  
minio_access_key = gP3HFQQXuTKQFyI5s9dQ
minio_secret_key = gyI9eAf1xWfLpQGkqKYhksDCJdRvQgl1egw3ZLsp
image_bucket_name = image
object_name_prefix = wiseAgent/

# 接口配置
LAYOUT_PARSER_API = http://************:8002/api/v1/layout/analyze
OCR_PARSER_API = http://************:8010/api/v1/ocr
TABLE_PARSER_API = http://************:8009/api/v1/table/analyze
# SPLIT_API = http://************:8007/api/v1/split
SPLIT_API = http://127.0.0.1:8007/api/v1/split
END_TO_END_API = 'http://************:8006/api/v1/file/parser'

# wiseflow 配置
enable_wiseflow=True
wiseflow_url="http://langflow:7860/api/v1/wiseagentauth"

# OpenAI 配置
OPENAI_API_KEY=sk-a1a653d74b6d41f6a024d05e65b68865
OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
OPENAI_MODEL=qwen-plus
OPENAI_TEMPERATURE=0.1
OPENAI_MAX_TOKENS=8000

# Neo4j 配置
GRAPH_RAG=True
NEO4J_URI=bolt://123.56.31.133:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password
NEO4J_DATABASE=neo4j

# 图查询配置
GRAPH_QUERY_MAX_DEPTH=3
GRAPH_QUERY_MAX_RESULTS=50
GRAPH_QUERY_SIMILARITY_THRESHOLD=0.7