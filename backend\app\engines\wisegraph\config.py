"""
WiseGraph 配置文件
复用LightRAG demo中的连接配置
"""

import os
from typing import Dict, Any
from urllib.parse import quote_plus

# Neo4j 配置
NEO4J_CONFIG = {
    "uri": "bolt://*************:7687",
    "username": "neo4j", 
    "password": "password",
    "database": "neo4j"
}

# OpenAI 配置
OPENAI_CONFIG = {
    "api_key": "sk-a1a653d74b6d41f6a024d05e65b68865",  # 请替换为实际的API密钥
    "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
    "model": "qwen-plus"
}


# mongo
# username = quote_plus("memInterview")
# password = quote_plus("memInterview@202408")
# os.environ["MONGO_URI"] = f"mongodb://{username}:{password}@kgweb.roardata.cn:37017/?authSource=admin"
# os.environ["MONGO_DATABASE"] = "roardataAiApp_test"

# 新配置
username = quote_plus("wiseAgent001")
password = quote_plus("1qaz@WSX")
os.environ["MONGO_URI"] = f"mongodb://{username}:{password}@*************:37017/?authSource=wiseagent"
os.environ["MONGO_DATABASE"] = "wiseagent"

# LLM 模型配置
LLM_MODEL_CONFIG = {
    "max_token_size": 8000,
    "temperature": 0.1
}

# 实体关系抽取提示词模板
ENTITY_EXTRACTION_PROMPT = """
请从以下文本中提取实体和关系信息。

文本内容：
{text}

请按照以下格式返回JSON：
{{
    "entities": [
        {{
            "name": "实体名称",
            "type": "实体类型",
            "description": "实体描述"
        }}
    ],
    "relationships": [
        {{
            "source": "源实体名称",
            "target": "目标实体名称", 
            "relation": "关系类型",
            "description": "关系描述"
        }}
    ]
}}

注意：
1. 实体类型包括：person（人物）、organization（组织）、location（地点）、event（事件）、concept（概念）等
2. 关系要准确反映实体间的语义联系
3. 描述要简洁明确
"""

# 查询实体抽取提示词模板
QUERY_ENTITY_EXTRACTION_PROMPT = """
请从以下用户问题中提取关键实体和可能的关系类型，用于图数据库查询。

用户问题：
{query}

请按照以下格式返回JSON：
{{
    "entities": ["实体1", "实体2", ...],
    "relation_types": ["关系类型1", "关系类型2", ...],
    "keywords": ["关键词1", "关键词2", ...]
}}

注意：
1. 提取问题中的核心实体
2. 推断可能相关的关系类型
3. 包含重要的关键词用于模糊匹配
"""

# 图查询配置
GRAPH_QUERY_CONFIG = {
    "max_depth": 3,  # 最大查询深度
    "max_results": 50,  # 最大返回结果数
    "similarity_threshold": 0.7  # 实体名称相似度阈值
}

def get_neo4j_config() -> Dict[str, Any]:
    """获取Neo4j配置"""
    return NEO4J_CONFIG.copy()

def get_openai_config() -> Dict[str, Any]:
    """获取OpenAI配置（已迁移到环境变量，保持向后兼容）"""
    # 注意：此配置已迁移到.env文件，建议使用wisegraph_settings
    return OPENAI_CONFIG.copy()

def get_llm_config() -> Dict[str, Any]:
    """获取LLM配置"""
    return LLM_MODEL_CONFIG.copy()

def get_mongo_config() -> Dict[str, Any]:
    """获取MongoDB配置"""
    return {
        "uri": os.environ.get("MONGO_URI"),
        "database": os.environ.get("MONGO_DATABASE")
    }
