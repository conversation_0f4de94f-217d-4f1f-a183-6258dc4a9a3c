"""
WiseGraph 快速演示 - 只处理前10个文本块
"""

import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
from graph_builder import GraphBuilder
from graph_retriever import GraphRetriever
from config import get_mongo_config
from loguru import logger

# MongoDB配置
mongo_config = get_mongo_config()
MONGO_URI = mongo_config["uri"]
MONGO_DATABASE = mongo_config["database"]

# 测试用的knowledge_base_id
TEST_KNOWLEDGE_BASE_ID = "68463f1626512dbb437fd482"

async def fetch_limited_chunks(knowledge_base_id: str, limit: int = 10) -> dict:
    """
    从MongoDB获取有限数量的chunks数据
    
    Args:
        knowledge_base_id: 知识库ID
        limit: 限制数量
        
    Returns:
        字典格式的chunks数据
    """
    print(f"🔍 正在从MongoDB获取前{limit}个数据，knowledge_base_id: {knowledge_base_id}")
    
    client = None
    try:
        # 连接MongoDB
        client = AsyncIOMotorClient(MONGO_URI)
        db = client[MONGO_DATABASE]
        chunks_collection = db["chunks"]
        
        # 查询指定knowledge_base_id的chunks (转换为ObjectId)
        try:
            kb_object_id = ObjectId(knowledge_base_id)
        except Exception as e:
            print(f"❌ 无效的knowledge_base_id格式: {e}")
            return {}
            
        query = {"knowledge_base_id": kb_object_id}
        projection = {"_id": 1, "answer": 1}  # 只获取_id和answer字段
        
        cursor = chunks_collection.find(query, projection).limit(limit)
        chunks_data = {}

        count = 0
        async for doc in cursor:
            chunk_id = str(doc["_id"])  # 使用_id作为chunk_id
            answer = doc.get("answer", "")  # 获取answer字段
            
            if answer and answer.strip():  # 确保answer不为空
                chunks_data[chunk_id] = answer.strip()
                count += 1

        print(f"✅ 成功获取 {count} 个有效chunks数据")
        
        if count == 0:
            print("❌ 未找到任何数据")
            return {}

        # 显示前3个chunks的预览
        print("📄 数据预览:")
        for chunk_id, content in list(chunks_data.items())[:3]:
            preview = content[:100] + "..." if len(content) > 100 else content
            print(f"  {chunk_id}: {preview}")

        return chunks_data
        
    except Exception as e:
        logger.error(f"MongoDB查询失败: {e}")
        print(f"❌ MongoDB查询失败: {e}")
        return {}
        
    finally:
        if client:
            client.close()

async def demo_quick_build():
    """快速图构建演示"""
    print("🔧 开始快速图构建演示...")
    
    # 获取前10个文本块
    chunks_data = await fetch_limited_chunks(TEST_KNOWLEDGE_BASE_ID, limit=10)
    
    if not chunks_data:
        print("❌ 没有数据可处理")
        return False
    
    builder = GraphBuilder()
    
    try:
        # 初始化
        await builder.initialize()
        print("✅ GraphBuilder初始化成功")
        
        # 构建图谱
        print(f"\n📊 开始构建图谱，文本块数量: {len(chunks_data)}")
        result = await builder.build_graph_from_chunks(chunks_data, clear_existing=True)
        
        print(f"✅ 图构建完成!")
        print(f"   - 处理的文本块: {result['processed_chunks']}")
        print(f"   - 失败的文本块: {result['failed_chunks']}")
        print(f"   - 创建的实体: {result['total_entities_created']}")
        print(f"   - 创建的关系: {result['total_relationships_created']}")
        print(f"   - 最终统计: {result['final_database_stats']}")
        
        # 获取图谱摘要
        print(f"\n📈 图谱摘要:")
        summary = await builder.get_graph_summary()
        
        print(f"   基本统计: {summary['basic_stats']}")
        print(f"   实体类型分布:")
        for entity_type in summary['entity_types'][:5]:  # 只显示前5个
            print(f"     - {entity_type['type']}: {entity_type['count']}")
        
        print(f"   关系类型分布:")
        for relation_type in summary['relation_types'][:5]:  # 只显示前5个
            print(f"     - {relation_type['type']}: {relation_type['count']}")
        
        return True
        
    except Exception as e:
        logger.error(f"图构建演示失败: {e}")
        return False
    finally:
        await builder.close()

async def demo_quick_retrieval():
    """快速图检索演示"""
    print("\n🔍 开始快速图检索演示...")
    
    retriever = GraphRetriever()
    
    try:
        # 初始化
        await retriever.initialize()
        print("✅ GraphRetriever初始化成功")
        
        # 测试查询
        test_queries = [
            "什么是电信网络诈骗？",
            "反电信网络诈骗工作的基本原则是什么？",
            "银行业金融机构的责任是什么？"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n--- 查询 {i}: {query} ---")
            
            result = await retriever.retrieve(query)
            
            print(f"找到实体数量: {len(result['entities_found'])}")
            print(f"子图节点数量: {len(result['subgraph']['nodes'])}")
            print(f"子图关系数量: {len(result['subgraph']['relationships'])}")
            print(f"相关文本块: {len(result['chunk_ids'])}")
            
            if result['context']:
                print(f"上下文长度: {len(result['context'])} 字符")
                print(f"上下文内容:")
                print(result['context'][:300] + "..." if len(result['context']) > 300 else result['context'])
            else:
                print("❌ 未找到相关上下文")
        
        return True
        
    except Exception as e:
        logger.error(f"图检索演示失败: {e}")
        return False
    finally:
        await retriever.close()

async def main():
    """主函数"""
    print("🚀 WiseGraph 快速演示 (前10个文本块)")
    print("=" * 50)
    
    try:
        # 演示图构建
        build_success = await demo_quick_build()
        
        if build_success:
            # 演示图检索
            await demo_quick_retrieval()
            print("\n🎉 快速演示完成!")
        else:
            print("\n❌ 图构建失败，跳过检索演示")
        
    except Exception as e:
        logger.error(f"演示过程中出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
