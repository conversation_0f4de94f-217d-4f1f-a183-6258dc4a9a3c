from mongoengine import Document, StringField, DateTimeField, ReferenceField, IntField, ObjectIdField, <PERSON>Field
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List
from bson import ObjectId

# MongoEngine 模型
class DatasetFile(Document):
    meta = {
        'collection': 'dataset_files'
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId)  # 使用 ObjectIdField
    name = StringField(required=True)
    storage_path = StringField(required=True)
    dataset_id = ObjectIdField(required=True)  # 假设有一个 Dataset 模型
    created_at = DateTimeField(default=datetime.now)
    data_type = StringField(required=True)
    dataset_type = StringField(required=True)
    processing_status = StringField(default='pending')  # 例如：pending, processing, completed
    user_id = IntField(required=True)
    deleted_by = IntField(default=None)
    user_name = StringField()
    row_count = IntField(default=0)
    tags = ListField(StringField(), default=list)

# Pydantic 模型
class DatasetFileBase(BaseModel):
    id: str
    name: str
    storage_path: str
    dataset_id: str
    data_type: str
    processing_status: Optional[str] = 'pending'
    tags: Optional[List[str]] = []

class DatasetFileCreate(DatasetFileBase):
    pass

class DatasetFileUpdate(BaseModel):
    name: Optional[str] = None
    storage_path: Optional[str] = None
    data_type: Optional[str] = None
    processing_status: Optional[str] = None

class DatasetFileResponse(DatasetFileBase):
    created_at: datetime
    

    class Config:
        from_attributes = True
