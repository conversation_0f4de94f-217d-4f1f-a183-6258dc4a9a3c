# 金融机构大模型应用平台 V1.0 基础版本需求说明书


### 1.1 版本目标
构建金融行业专用的大模型应用平台基础框架，实现核心功能模块，支持金融场景下的大模型应用开发和安全部署能力。


# 1. 用户管理系统模块

| 功能点 | 功能描述 | 优先级 | 实现情况 | 实现文件 |
|--------|----------|--------|----------|-----------|
| 用户登录 | 支持账号密码、短信验证码登录 | P0 | ✅ | Login/index.tsx |
| 用户退出 | 安全退出登录，清除会话信息 | P0 | ✅ | RightContent/AvatarDropdown.tsx |
| 用户注册与身份认证 | 支持密码、短信、证书等多重认证方式 | P0 | ✅ | UserManagement.tsx |
| RBAC角色权限管理 | 基于角色的访问控制，细粒度权限管理 | P0 | ✅ | RoleManagement.tsx |
| 组织架构管理 | 金融机构组织架构管理，支持多级组织 | P1 | ✅ | GroupManagement.tsx |
| 多租户隔离 | 不同租户数据隔离，安全访问控制 | P1 | ⚠️ | - |
| 用户行为审计 | 完整的用户操作审计日志记录 | P1 | ✅ | service.ts |

# 2. 个人中心模块

| 功能点 | 功能描述 | 优先级 | 实现情况 | 实现文件 |
|--------|----------|--------|----------|-----------|
| 基本信息 | 查看和编辑个人基本信息 | P0 | ✅ | Account/Settings/BaseView.tsx |
| 安全设置 | 修改密码、绑定手机等安全设置 | P0 | ✅ | Account/Settings/SecurityView.tsx |
| 消息通知 | 系统消息和通知管理 | P1 | ✅ | Account/Settings/NotificationView.tsx |
| 使用记录 | 查看个人使用历史记录 | P1 | ✅ | Account/Settings/HistoryView.tsx |
| 偏好设置 | 个性化设置（语言、主题等） | P2 | ✅ | Account/Settings/PreferenceView.tsx |

# 3. 模型管理系统模块

| 功能点 | 功能描述 | 优先级 | 实现情况 | 实现文件 |
|--------|----------|--------|----------|-----------|
| 模型接入配置 | 支持主流金融大模型对接和配置 | P0 | ✅ | SystemAppSettings/ |
| 模型版本管理 | 模型版本与迭代管理，版本控制 | P1 | 部分实现 | - |
| 实时监控 | 实时模型性能监控与告警 | P1 | ✅ | ChatLogs.tsx |





# 4. LLM市场功能模块

| 功能点 | 功能描述 | 优先级 | 实现情况 | 实现文件 |
|--------|----------|--------|----------|-----------|
| 模型浏览 | LLM模型展示与浏览 | P0 | ✅ | llmModels/index.tsx |
| 模型对比 | 多模型并行对比功能 | P1 | ✅ | llmComparison/ |
| 模型对话 | 单模型对话功能 | P0 | ✅ | llmChat/index.tsx |
| 参数配置 | 模型参数自定义配置 | P1 | ✅ | Settings.tsx |
| 多轮对话 | 支持上下文关联的多轮对话 | P0 | ✅ | ConversationManager.tsx |
| 对话管理 | 对话历史记录管理与查看 | P1 | ✅ | ChatComponent.tsx |

# 5. 系统配置模块

| 功能点 | 功能描述 | 优先级 | 实现情况 | 实现文件 |
|--------|----------|--------|----------|-----------|
| 基础配置 | 系统基础信息配置 | P0 | ✅ | SystemManagement.tsx |
| 主题定制 | 系统主题样式配置 | P2 | ✅ | SystemManagement.tsx |
| 多语言支持 | 国际化语言支持 | P1 | ✅ | 多语言配置文件 |
| 系统监控 | 系统运行状态监控 | P1 | 部分实现 | - |

# 6.知识库管理功能模块介绍

## 6.1.知识库管理模块

| 功能点               | 功能描述                                                                 | 优先级 | 实现情况 |
|----------------------|--------------------------------------------------------------------------|--------|----------|
| 创建知识库           | 用户可创建新知识库，设置名称、描述、索引类型、模型配置等信息             | P0     | 已实现   |
| 获取知识库列表       | 分页查询所有知识库，支持按名称/描述过滤，返回总数及文件/分块统计信息     | P0     | 已实现   |
| 获取个人知识库       | 查询当前用户创建的知识库列表，支持分页和过滤                             | P0     | 已实现   |
| 获取知识库详情       | 根据ID获取单个知识库的详细信息                                           | P1     | 已实现   |
| 更新知识库配置       | 修改知识库的基本信息、索引配置和模型配置                                 | P0     | 已实现   |
| 软删除知识库         | 将知识库标记为失效状态（is_active=False），保留数据                      | P1     | 已实现   |

## 6.2.文件管理模块

| 功能点               | 功能描述                                                                 | 优先级 | 实现情况 |
|----------------------|--------------------------------------------------------------------------|--------|----------|
| 多文件上传           | 支持批量上传多种格式文件（txt/pdf/doc等），自动存储到MinIO或本地文件系统 | P0     | 已实现   |
| 文件列表查询         | 分页查询知识库下的文件列表，支持按文件名/状态过滤                        | P0     | 已实现   |
| 文件预览             | 支持在线预览文件内容，自动识别MIME类型，可强制下载                       | p1     | 已实现   |
| 文件删除             | 删除物理文件并移除数据库记录，同步更新知识库文件计数                     | P0     | 已实现   |

## 6.3.知识分块管理模块

| 功能点               | 功能描述                                                                 | 优先级 | 实现情况 |
|----------------------|--------------------------------------------------------------------------|--------|----------|
| 分块列表查询         | 分页查询知识库下的文本块，支持按标题/内容过滤                           | p0     | 已实现   |
| 创建知识分块         | 手动创建问答对形式的知识块，关联文件/页面                                | p1     | 已实现   |
| 更新知识分块         | 修改问答内容，自动更新token计数并生成新embedding，同步更新ES索引         | p0     | 已实现   |
| 删除知识分块         | 删除单个知识块及其关联索引                                               | p0     | 已实现   |
| 批量删除分块         | 支持批量删除多个知识块及其索引                                           | p1     | 已实现   |

## 6.4.索引管理模块

| 功能点               | 功能描述                                                                 | 优先级 | 实现情况 |
|----------------------|--------------------------------------------------------------------------|--------|----------|
| 创建分块索引         | 为知识块创建索引记录，存储索引内容和类型                                 | p0     | 已实现   |
| 更新分块索引         | 修改索引内容和配置信息                                                   | p1     | 已实现   |
| 删除分块索引         | 删除指定索引记录                                                         | p1     | 已实现   |

# 7.系统应用设置管理功能清单

| 功能点                          | 功能描述                                                                 | 优先级 | 实现情况 |
|---------------------------------|--------------------------------------------------------------------------|--------|----------|
| 系统应用设置分页查询 | 支持多条件过滤的分页查询功能                                             | p0     | 已实现   |
| 应用操作日志查询      | 查看特定应用的配置变更历史记录                                           | p1     | 已实现   |
| 应用聊天记录查询       | 查看与特定应用关联的聊天会话记录                                         | p1     | 已实现   |
| 应用配置更新             | 修改应用参数并记录变更日志                                               | p0     | 已实现   |
| 应用详情获取            | 获取单个应用的完整配置信息                                               | p0     | 已实现   |
| 可用LLM列表获取       | 获取所有已激活的LLM服务配置                                              | p1     | 已实现   |
| 知识库列表获取         | 获取所有已激活的知识库信息                                               | p1     | 已实现   |
