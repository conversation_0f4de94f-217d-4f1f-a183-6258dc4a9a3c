import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import pytest
from engines.embedding.embedding_utils import get_embedding_vector, get_embeddings_batch

@pytest.fixture
def embedding_config():
    """测试用的 embedding 配置"""
    return {
        "api_key": "sk-BOstXojjXywbG7QlA76eA282A4724e91915d045512239d9e",
        "model": "m3e",
        # "model": "bge-m3",
        "service_url": "http://api.roardata.cn/v1"
    }

@pytest.mark.asyncio
async def test_get_embedding_vector(embedding_config):
    """测试单个文本的 embedding 转换"""
    # 测试正常文本
    text = "这是一个测试文本"
    embedding = await get_embedding_vector(text, embedding_config)
    
    assert embedding is not None
    assert isinstance(embedding, list)
    assert len(embedding) > 0  # OpenAI 的 embedding 维度应该是固定的
    assert all(isinstance(x, float) for x in embedding)

    # 测试空文本
    empty_text = ""
    empty_embedding = await get_embedding_vector(empty_text, embedding_config)
    assert empty_embedding is not None  # OpenAI API 应该也能处理空字符串

    # 测试长文本
    long_text = "这是一个很长的测试文本" * 100
    long_embedding = await get_embedding_vector(long_text, embedding_config)
    assert long_embedding is not None
    assert len(long_embedding) == len(embedding)  # embedding 维度应该保持一致

@pytest.mark.asyncio
async def test_get_embeddings_batch(embedding_config):
    """测试批量文本的 embedding 转换"""
    # 测试正常批量文本
    texts = [
        "第一个测试文本",
        "第二个测试文本",
        "第三个测试文本"
    ]
    embeddings = await get_embeddings_batch(texts, embedding_config)
    
    assert embeddings is not None
    assert isinstance(embeddings, list)
    assert len(embeddings) == len(texts)
    assert all(isinstance(emb, list) for emb in embeddings)
    assert all(len(emb) == len(embeddings[0]) for emb in embeddings)  # 所有 embedding 维度应该相同

    # 测试空列表
    empty_texts = []
    empty_embeddings = await get_embeddings_batch(empty_texts, embedding_config)
    assert empty_embeddings == []

    # 测试包含空字符串的列表
    mixed_texts = ["正常文本", "", "另一个正常文本"]
    mixed_embeddings = await get_embeddings_batch(mixed_texts, embedding_config)
    assert len(mixed_embeddings) == len(mixed_texts)
    assert all(emb is not None for emb in mixed_embeddings)

@pytest.mark.asyncio
async def test_error_handling(embedding_config):
    """测试错误处理"""
    # 测试无效的 API key
    invalid_config = embedding_config.copy()
    invalid_config["api_key"] = "invalid_key"
    
    embedding = await get_embedding_vector("测试文本", invalid_config)
    assert embedding is None

    # 测试无效的服务 URL
    invalid_url_config = embedding_config.copy()
    invalid_url_config["service_url"] = "https://invalid.url"
    
    embedding = await get_embedding_vector("测试文本", invalid_url_config)
    assert embedding is None

    # 测试无效的模型名称
    invalid_model_config = embedding_config.copy()
    invalid_model_config["model"] = "invalid_model"
    
    embedding = await get_embedding_vector("测试文本", invalid_model_config)
    assert embedding is None

@pytest.mark.asyncio
async def test_batch_size_handling(embedding_config):
    """测试批量处理大小的处理"""
    # 创建一个大于默认批量大小的文本列表
    large_texts = [f"测试文本 {i}" for i in range(10)]  # 默认批量大小是 20
    
    embeddings = await get_embeddings_batch(large_texts, embedding_config)
    
    assert embeddings is not None
    assert len(embeddings) == len(large_texts)
    assert all(isinstance(emb, list) for emb in embeddings)
    assert all(len(emb) == len(embeddings[0]) for emb in embeddings)

if __name__ == "__main__":
    pytest.main(["-v", "test_embedding_utils.py"]) 