from openai import OpenAI
from pdf2image import convert_from_path
from PIL import Image
import time
import subprocess
from pymongo import Mongo<PERSON>lient
from bson import ObjectId
from minio import Minio
from minio.error import S3Error
from urllib.parse import urljoin, quote_plus
from urllib.parse import urlparse
import traceback
import os


# MongoDB配置
MONGODB_USER = "memInterview"
MONGODB_PASSWORD = "memInterview@202408"
DATABASE_NAME = "roardataAiApp_test"
MONGODB_AUTH_SOURCE = "admin"
MONGODB_HOST = "kgweb.roardata.cn"
MONGODB_PORT = 37017

# 对用户名和密码进行URL编码
encoded_user = quote_plus(MONGODB_USER)
encoded_password = quote_plus(MONGODB_PASSWORD)

connection_string = f"mongodb://{encoded_user}:{encoded_password}@{MONGODB_HOST}:{MONGODB_PORT}/{DATABASE_NAME}?authSource={MONGODB_AUTH_SOURCE}"
client = MongoClient(connection_string)
db = client[DATABASE_NAME]
task_table = "audit_tasks"
task_file_table = "audit_task_files"


# MinIO配置
minio_endpoint = "**************:9002"
minio_access_key = "8UsB6TndQy8j9M2Rf5Tr"
minio_secret_key = "EDEBVaUn5ewTnGOvWpA6seRg1Ssf15tnMUg8qwd3"
image_bucket_name = "wiseagentfiles"
object_name_prefix = "wiseAgent/"


def get_task():
    """从任务表中获取待处理任务"""
    task_list = list(
        db[task_table].find({"status": "analyzing"}).sort("_id", 1).limit(1)
    )
    if len(task_list) > 0:
        return task_list[0]
    else:
        return None


def download_files(task, files_local_dir):
    try:
        # 确保目标目录存在
        os.makedirs(files_local_dir, exist_ok=True)
        # 下载
        for file_id in task["file_ids"]:
            file_info = db[task_file_table].find_one({"_id": ObjectId(file_id)})
            # print("file_info:", file_info)
            local_file_path = download_file_from_minio(file_info["storage_path"], files_local_dir, file_info["name"])
            # print(local_file_path)
        return True
    except Exception as e:
        print(e)
        return False


def download_file_from_minio(url: str, local_dir: str, true_file_name: str):
    """
    从 MinIO 的 presigned URL 或标准 URL 中下载压缩文件到指定目录。
    """
    try:
        true_file_name = true_file_name.replace(" ", "")
        # 解析 URL
        parsed_url = urlparse(url)
        endpoint = parsed_url.netloc
        path_parts = parsed_url.path.lstrip("/").split("/", 1)

        # 获取bucket_name和file_name
        bucket_name = path_parts[0]  # 第一个元素是bucket_name
        file_name = (
            path_parts[1] if len(path_parts) > 1 else ""
        )  # 第二个元素是file_name

        # 构建 MinIO 客户端，禁用SSL验证
        client = Minio(
            endpoint,
            access_key=minio_access_key,
            secret_key=minio_secret_key,
            secure=False,
        )

        # 确保目标目录存在
        os.makedirs(local_dir, exist_ok=True)

        # 检查bucket是否存在
        if not client.bucket_exists(bucket_name):
            print(f"Bucket {bucket_name} 不存在")
            return None

        # 如果没有file_name，返回bucket的根目录路径
        if not file_name:
            print(f"没有指定file_name，返回bucket根目录：{local_dir}")
            return local_dir

        # 拼接本地文件路径
        local_file_path = os.path.join(local_dir, os.path.basename(true_file_name))

        # 下载对象
        client.fget_object(bucket_name, file_name, local_file_path)
        print(f"文件已成功下载到：{local_file_path}")
        return local_file_path

    except S3Error as err:
        print(f"下载出错：{err}")
        return None


def upload_file_to_minio(
    file_path: str, bucket_name: str, object_name: str, secure: bool = False
):
    """
    将指定的文件上传到 MinIO，并返回下载链接。
    """
    try:
        # 创建 MinIO 客户端，禁用SSL验证
        client = Minio(
            endpoint=minio_endpoint,
            access_key=minio_access_key,
            secret_key=minio_secret_key,
            secure=secure,
        )

        # 如果存储桶不存在，创建它
        if not client.bucket_exists(bucket_name):
            print(f"创建bucket: {bucket_name}")
            client.make_bucket(bucket_name)
            print(f"Bucket {bucket_name} 创建成功")

        # 上传文件
        client.fput_object(bucket_name, object_name, file_path)
        print(f"文件已成功上传到：{object_name}")

        # 构建文件的下载 URL
        base_url = (
            f"http{'s' if secure else ''}://{client._endpoint_host}/{bucket_name}/"
        )
        download_url = urljoin(base_url, object_name)

        return download_url

    except S3Error as err:
        print(f"上传出错：{err}")
        return None


def get_files_in_directory(directory_path):
    # 获取指定目录下的所有文件名称
    file_names = []
    for root, dirs, files in os.walk(directory_path):
        for file in files:
            file_names.append(os.path.join(root, file))
    # 排序
    sorted_file_names = sorted(file_names)

    return sorted_file_names


def convert_to_pdf(file_path, output_dir):
    """Convert docx and ppt to pdf"""
    try:
        result = subprocess.run(
            [
                "libreoffice",
                "--headless",
                "--convert-to",
                "pdf",
                "--outdir",
                output_dir,
                file_path,
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
        if result.returncode == 0:
            return True
        else:
            return False
    except Exception as e:
        traceback.print_exc()
        return False


def file_to_images(file_path):
    file_name = file_path.split("/")[-1]  # 文件名，带扩展名
    file_actual_name = file_name.rsplit(".", 1)[0]  # 文件名，不带扩展名
    file_type = (file_name.rsplit(".", 1)[1]).lower()  # 文件扩展名，不带.
    file_folder_path = file_path.split(file_type)[0]
    images_path_root = file_path.split("data")[0]
    images_path = images_path_root + "result/temp_images/" + file_actual_name

    if file_type in ["pdf", "PDF"]:
        images = convert_from_path(file_path)
        for image_idx, image in enumerate(images):
            # Save image to temporary path
            if not os.path.exists(images_path):
                os.makedirs(images_path)
            convert_image_path = (
                images_path + "/" + file_actual_name + "_" + str(image_idx + 1) + ".png"
            )
            image.save(convert_image_path, "PNG")

    elif file_type in ["doc", "ppt", "docx", "pptx"]:

        convert_result = convert_to_pdf(file_path, file_folder_path)
        if convert_result:
            file_path = file_folder_path + "/" + file_actual_name + ".pdf"
            images = convert_from_path(file_path)
            for image_idx, image in enumerate(images):
                # Save image to temporary path
                if not os.path.exists(images_path):
                    os.makedirs(images_path)
                convert_image_path = (
                    images_path
                    + "/"
                    + file_actual_name
                    + "/"
                    + file_actual_name
                    + "_"
                    + str(image_idx + 1)
                    + ".png"
                )
                image.save(convert_image_path, "PNG")

    else:
        print("Unsupported file type: {}".format(file_type))
        return None

    return images_path


def concatenate_images_vertically(image_list, output_path):
    """将多张图片按顺序上下拼接成一张图片"""
    # 打开所有图片
    images = [Image.open(image_path) for image_path in image_list]

    # 计算拼接后图片的宽度和高度
    total_width = max(image.width for image in images)
    total_height = sum(image.height for image in images)

    # 创建一个新的空白图像，用于拼接
    new_image = Image.new("RGB", (total_width, total_height))

    # 逐个将图片粘贴到新图像上
    y_offset = 0
    for image in images:
        new_image.paste(image, (0, y_offset))
        y_offset += image.height  # 更新粘贴位置

    # 保存最终的拼接图像
    new_image.save(output_path)
    print(f"拼接后的图片已保存为: {output_path}")

    return output_path


def model_answer(prompt, image_url):
    client = OpenAI(
        base_url="https://api-inference.modelscope.cn/v1/",
        api_key="67320326-5fe7-4244-bc46-c4a99fb5830f",  # ModelScope Token
    )

    response = client.chat.completions.create(
        model="Qwen/Qwen2.5-VL-72B-Instruct",  # ModelScope Model-Id
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt,
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_url,
                        },
                    },
                ],
            }
        ],
        stream=True,
    )

    answer_list = []
    for chunk in response:
        # print(chunk.choices[0].delta.content, end='', flush=True)
        answer_list.append(chunk.choices[0].delta.content)

    answer = "".join(answer_list)

    return answer


def model_prompt(file_name):
    if file_name == "信息披露申请书":
        prompt = """分析该图片内容，做下面三部分工作：
        （工作一）如果该图片中包含表名为'一、转让标的简况'的表格，则提取该表格中下面15个字段的值：
        1.标的基本情况-房屋或土地-坐落位置
        2.标的基本情况-房屋或土地-房产证号/不动产权证号
        3.标的基本情况-房屋或土地-建筑面积
        4.标的基本情况-房屋或土地-目前用途
        5.标的基本情况-房屋或土地-附属设施
        6.标的基本情况-房屋或土地-使用年限
        7.标的基本情况-房屋或土地-已用年限
        8.标的基本情况-房屋或土地-是否为住宅
        9.标的基本情况-房屋或土地-是否有抵押
        10.资产评估情况-评估机构
        11.资产评估情况-核准或备案机构
        12.资产评估情况-评估基准日
        13.资产评估情况-账面价值
        14.资产评估情况-资产评估值
        15.其他需要披露的内容
        
        （工作二）如果该图片中包含表名为'三、交易条件与受让方资格条件'的表格，则提取该表格中下面1个字段的值：
        1.交易条件-转让底价
    
        要求：
        1.如果没有提取到结果，则返回空字符串，否则提取结果以json格式返回，只返回json体即可，不要返回其他内容。表格中字段的值为空的或表格中不包含该字段的，返回空字符串;
        2."交易条件-转让底价"字段的值要换算成以'元'为单位的值。
        
        返回样例：{"标的基本情况-房屋-坐落位置": "xxx", 
              "标的基本情况-房屋-房产证号/不动产权证号": "xxx",
              "标的基本情况-房屋-建筑面积": "xxx",
              "标的基本情况-房屋-目前用途": "xxx",
              "标的基本情况-房屋-附属设施": "",
              "标的基本情况-房屋-使用年限": "",
              "标的基本情况-房屋-已用年限": "",
              ...,
              "交易条件-转让底价": "123743200元"
              }
        
        （工作三）如果该图片中不包含工作一和工作二中的两张表，则只返回一个json字符串：{"result": ""}
        """
    elif file_name == "评估报告":
        prompt = """分析该图片内容，做下面两部分工作：
        （工作一）如果该图片中包含表名为'资产评估业务报告备案回执'的表格，则提取该表格中下面1个字段的值：
        1.评估结论
        
        要求：提取结果以json格式返回，只返回json体即可，不要返回其他内容。表格中字段的值为空的，返回空字符串，如果有值，则换算成以'元'为单位的整型值。
        例如：{"评估结论": "123743200元"}
        
        （工作二）如果该图片中不包含工作一中的表格，则只返回一个json字符串：{"result": ""}
        """
    elif file_name == "评估备案表":
        prompt = """分析该图片内容，做下面两部分工作：
        （工作一）如果该图片中包含表名为'资产评估结果'的表格，则提取该表格中下面1个字段的值：
        1.资产总计-评估价值
        
        要求：提取结果以json格式返回，只返回json体即可，不要返回其他内容。表格中字段的值为空的，返回空字符串，如果有值，则换算成以'元'为单位的整型值。
        例如：{"评估结论": "123743200元"}
        
        （工作二）如果该图片中不包含工作一中的表格，则只返回一个json字符串：{"result": ""}
        """
    elif file_name == "房屋所有权证":
        prompt = """该图片是房屋所有权证每一页的照片，其中可能包含一个房屋所有权证，也可能包含多个所有权证，分析该图片内容，做下面两部分工作：
        1. 提取每个房屋所有权证中'房屋坐落'的值
        2. 不动产证编号
        3. 建筑面积
        4. 使用期限
        5. 规划用途
        6. 是否有抵押
        
        要求：
        1.如果没有提取到结果，则返回空字符串，否则提取结果以json格式返回，只返回json体即可，不要返回其他内容。表格中字段的值为空的或表格中不包含该字段的，返回空字符串;
        2."交易条件-转让底价"字段的值要换算成以'元'为单位的值。
        
        返回样例：{"房屋坐落": "xxx", 
              "标的基本情况-房屋-房产证号/不动产权证号": "xxx",
              "不动产证编号": "xxx",
              "建筑面积": "xxx",
              "使用期限": "",
              "规划用途": "",
              "是否有抵押": ""
              }
        """
    elif file_name == "房屋租赁合同":
        prompt = """该图片是房屋租赁合同每一页的照片，分析该图片内容，做下面两部分工作：
        1. 提取房屋租赁合同中'租赁时间'的值
        
        要求：
        1.如果没有提取到结果，则返回空字符串，否则提取结果以json格式返回，只返回json体即可，不要返回其他内容。
        返回样例：{"租赁时间-起始时间": "xxx", 
              "租赁时间-终止时间": "xxx"}
        """
    else:
        prompt = ""

    return prompt


def model_result_judge(file_info):

    result_structured = []
    all_info = {}
    scene_info = {}
    # Scene 1: 价格比较
    scene_1 = {}
    try:
        transfer_price = float(
            file_info["信息披露申请书"][0]["交易条件-转让底价"].replace("元", "")
        )
        eval_price = float(file_info["评估报告"][0]["评估总价"].replace("元", ""))
        record_price = float(
            file_info["评估备案表"][0]["资产总计-评估价值"].replace("元", "")
        )

        scene_1["judge1"] = {
            "value": transfer_price >= eval_price,
            "desc": f"信息披露申请书中交易条件-转让底价的值为'{transfer_price}元'，评估报告中评估总价的值为'{eval_price}元'，"
            f"因此信息披露申请书中交易条件-转让底价的值{'大于等于' if transfer_price >= eval_price else '小于'}评估报告中评估总价的值，"
            f"{'符合要求。' if transfer_price >= eval_price else '请注意！'}",
        }

        scene_1["judge2"] = {
            "value": transfer_price >= record_price,
            "desc": f"信息披露申请书中交易条件-转让底价的值为'{transfer_price}元'，评估备案表中资产总计-评估价值的值为'{record_price}元'，"
            f"因此信息披露申请书中交易条件-转让底价的值{'大于等于' if transfer_price >= record_price else '小于'}评估备案表中资产总计-评估价值的值，"
            f"{'符合要求。' if transfer_price >= record_price else '请注意！'}",
        }

        scene_1["judge3"] = {
            "value": eval_price == record_price,
            "desc": f"评估报告中评估总价的值为'{eval_price}元'，评估备案表中资产总计-评估价值的值为'{record_price}元'，"
            f"因此评估报告中评估总价的值与评估备案表中资产总计-评估价值的值{'一致' if eval_price == record_price else '不一致'}，"
            f"{'符合要求。' if eval_price == record_price else '请注意！'}",
        }
    except Exception as e:
        scene_1["error"] = {"value": False, "desc": f"价格比较过程出现错误: {str(e)}"}

    # Scene 2: 房屋坐落位置比较
    scene_2 = {}
    try:
        disclosure_address = file_info["信息披露申请书"][0][
            "标的基本情况-房屋-坐落位置"
        ]
        certificate_address = file_info["房屋所有权证"][0]["坐落位置"]

        scene_2["judge1"] = {
            "value": disclosure_address == certificate_address,
            "desc": f"信息披露申请书中标的基本情况-房屋-坐落位置的值为'{disclosure_address}'，"
            f"房屋所有权证中坐落位置的值为'{certificate_address}'，"
            f"因此两个坐落位置的值{'一致' if disclosure_address == certificate_address else '不一致'}，"
            f"{'符合要求。' if disclosure_address == certificate_address else '请注意！'}",
        }

        # 解析地址（这里需要根据实际地址格式调整解析逻辑）
        address_parts = certificate_address.split("市")
        if len(address_parts) > 1:
            scene_2["judge2"] = {
                "省": "安徽省",  # 可以根据实际情况从地址中解析
                "市": "合肥市",
                "区": "高新区",
                "详细地址": certificate_address,
            }
    except Exception as e:
        scene_2["error"] = {"value": False, "desc": f"地址比较过程出现错误: {str(e)}"}

    # Scene 3: 房产证号比较
    scene_3 = {}
    try:
        disclosure_cert = file_info["信息披露申请书"][0][
            "标的基本情况-房屋-房产证号/不动产权证号"
        ]
        house_cert = file_info["房屋所有权证"][0]["不动产证编号"]

        scene_3["judge1"] = {
            "value": disclosure_cert == house_cert,
            "desc": f"信息披露申请书中标的基本情况-房屋-房产证号/不动产权证号的值为'{disclosure_cert}'，"
            f"房屋所有权证中不动产证编号的值为'{house_cert}'，"
            f"因此两个编号的值{'一致' if disclosure_cert == house_cert else '不一致'}，"
            f"{'符合要求。' if disclosure_cert == house_cert else '请注意！'}",
        }
    except Exception as e:
        scene_3["error"] = {
            "value": False,
            "desc": f"房产证号比较过程出现错误: {str(e)}",
        }

    # Scene 4: 建筑面积比较
    scene_4 = {}
    try:
        disclosure_area = file_info["信息披露申请书"][0]["标的基本情况-房屋-建筑面积"]
        house_area = file_info["房屋所有权证"][0]["建筑面积"]

        scene_4["judge1"] = {
            "value": disclosure_area == house_area,
            "desc": f"信息披露申请书中标的基本情况-房屋-建筑面积的值为'{disclosure_area}'，"
            f"房屋所有权证中建筑面积的值为'{house_area}'，"
            f"因此两个建筑面积的值{'一致' if disclosure_area == house_area else '不一致'}。",
        }
    except Exception as e:
        scene_4["error"] = {
            "value": False,
            "desc": f"建筑面积比较过程出现错误: {str(e)}",
        }

    # Scene 5: 使用年限比较
    scene_5 = {}
    try:
        disclosure_period = file_info["信息披露申请书"][0]["标的基本情况-房屋-使用年限"]
        house_period = file_info["房屋所有权证"][0]["使用期限"]

        if disclosure_period:
            scene_5["judge1"] = {
                "value": True,
                "desc": f"信息披露申请书中标的基本情况-房屋-使用年限的值为'{disclosure_period}'，"
                f"房屋所有权证中使用期限的起止值分别为'{house_period['起']}'和'{house_period['止']}'，"
                f"两个使用年限的值一致。",
            }
        else:
            scene_5["judge1"] = {
                "value": False,
                "desc": f"信息披露申请书中标的基本情况-房屋-使用年限的值为空，"
                f"房屋所有权证中使用期限的起止值分别为'{house_period['起']}'和'{house_period['止']}'，"
                f"因此无法判断两个文件的使用年限的值是否一致，请注意！",
            }
    except Exception as e:
        scene_5["error"] = {
            "value": False,
            "desc": f"使用年限比较过程出现错误: {str(e)}",
        }

    # Scene 6: 房屋用途比较
    scene_6 = {}
    try:
        disclosure_usage = file_info["信息披露申请书"][0]["标的基本情况-房屋-目前用途"]
        house_usage = file_info["房屋所有权证"][0]["规划用途"]

        scene_6["judge1"] = {
            "value": disclosure_usage in house_usage,
            "desc": f"信息披露申请书中标的基本情况-房屋-目前用途的值为'{disclosure_usage}'，"
            f"房屋所有权证中规划用途的值为'{house_usage}'，"
            f"因此两个用途的值{'一致' if disclosure_usage in house_usage else '不一致'}，"
            f"{'符合要求。' if disclosure_usage in house_usage else '请注意！'}",
        }
    except Exception as e:
        scene_6["error"] = {
            "value": False,
            "desc": f"房屋用途比较过程出现错误: {str(e)}",
        }

    # Scene 7: 抵押情况比较
    scene_7 = {}
    try:
        disclosure_mortgage = file_info["信息披露申请书"][0][
            "标的基本情况-房屋-是否有抵押"
        ]
        house_mortgage = file_info["房屋所有权证"][0]["是否有抵押"]

        if house_mortgage == "":
            scene_7["judge1"] = {
                "value": False,
                "desc": f"信息披露申请书中标的基本情况-房屋-是否有抵押的值为'{disclosure_mortgage}'，"
                f"房屋所有权证中是否有抵押的值为空，"
                f"因此无法判断两个文件的是否有抵押的值是否一致，请注意！",
            }
        else:
            scene_7["judge1"] = {
                "value": disclosure_mortgage == house_mortgage,
                "desc": f"信息披露申请书中标的基本情况-房屋-是否有抵押的值为'{disclosure_mortgage}'，"
                f"房屋所有权证中是否有抵押的值为'{house_mortgage}'，"
                f"两个值{'一致' if disclosure_mortgage == house_mortgage else '不一致'}。",
            }
    except Exception as e:
        scene_7["error"] = {
            "value": False,
            "desc": f"抵押情况比较过程出现错误: {str(e)}",
        }

    # Scene 8: 文件完整性和优先购买权检查
    scene_8 = {}
    try:
        # 检查必要文件是否存在
        required_files = ["房屋租赁合同", "情况说明"]
        for file in required_files:
            if file not in file_info:
                scene_8[f"judge{len(scene_8)+1}"] = {
                    "value": False,
                    "desc": f"未提供{file}文件，无法判断，请注意！",
                }

        # 检查优先购买权相关内容
        disclosure_content = file_info["信息披露申请书"][0]["其他需要披露的内容"]
        if "优先购买权" not in disclosure_content:
            scene_8[f"judge{len(scene_8)+1}"] = {
                "value": False,
                "desc": "信息披露申请书中其他需要披露的内容的值并未提及行使优先购买权或放弃优先购买权的相关内容，请注意！",
            }

        scene_8[f"judge{len(scene_8)+1}"] = {
            "value": False,
            "desc": "提供信息不全，无法判断，请注意！",
        }
    except Exception as e:
        scene_8["error"] = {
            "value": False,
            "desc": f"文件完整性检查过程出现错误: {str(e)}",
        }

    # Scene 9: 决策文件类型检查
    scene_9 = {}
    try:
        decision_doc = file_info["项目方决策文件"][0]
        scene_9["judge1"] = {
            "决策类型": decision_doc["决策类型"],
            "desc": f"项目方决策文件中文件标题的值为'{decision_doc['文件标题']}'，"
            f"因此决策类型为'{decision_doc['决策类型']}'",
        }
    except Exception as e:
        scene_9["error"] = {
            "value": False,
            "desc": f"决策文件类型检查过程出现错误: {str(e)}",
        }

    # 整合所有场景信息
    scene_info.update(
        {
            "scene_1": scene_1,
            "scene_2": scene_2,
            "scene_3": scene_3,
            "scene_4": scene_4,
            "scene_5": scene_5,
            "scene_6": scene_6,
            "scene_7": scene_7,
            "scene_8": scene_8,
            "scene_9": scene_9,
        }
    )
    # 组装结果
    all_info["file_info"] = file_info
    all_info["scene_info"] = scene_info
    result_structured.append[all_info]

    return result_structured


def main():
    while True:
        # 1.扫描任务表获取待处理任务
        task = get_task()
        if task is not None:
            # 设置本地工作目录
            local_dir = "./backend/app/engines/processing/bjs_poc/" + str(task["_id"])
            # 2.下载待处理文档
            files_local_dir = os.path.join(local_dir, "data")
            download_result = download_files(task, files_local_dir)
            if download_result:
                # 3.判断待处理的文档有哪些，非pdf的转为pdf，然后统一转成多张图片并拼接成一张图片：文档--->图片
                process_files = [
                    "信息披露申请书",
                    "评估报告",
                    "评估备案表",
                    "房屋所有权证",
                    "房屋租赁合同",
                    "情况说明",
                    "项目方决策文件",
                ]
                file_names = get_files_in_directory(files_local_dir)
                # content_extract_result = []
                file_info = {}
                for file_name in file_names:
                    if file_name.split("/")[-1].split(".")[0] in process_files:
                        file_path = os.path.join(files_local_dir, file_name)
                        # file_type = file_name.split(".")[-1]
                        images_path = os.path.join(local_dir, "result")
                        # 文件转为图片
                        temp_images_path = file_to_images(file_path, images_path)
                        temp_image_list = get_files_in_directory(temp_images_path)
                        long_temp_image_path = (local_dir + "/result/" + file_name + ".png")
                        long_temp_image_path = concatenate_images_vertically(temp_image_list, long_temp_image_path)
                        # 4.将待处理图片上传至minio并返回可下载链接
                        long_temp_image_url = upload_file_to_minio(long_temp_image_path, image_bucket_name, object_name_prefix)
                        # 5.根据文档名称获取prompt，然后依次提取每个文档中的信息
                        prompt = model_prompt(file_name)
                        if len(prompt) > 0:
                            # 6.信息抽取
                            model_result = model_answer(prompt, long_temp_image_url)
                            # 7.整理提取的结果
                            file_info[file_name] = eval(model_result)
                        else:
                            print("文件不在待处理范围内！")
                    else:
                        print("不属于待处理文件！")
                if len(file_info) > 0:
                    # 8.信息判断
                    result_structured = model_result_judge(file_info)
                    # 9.更新结果
                    db[task_table].find_one_and_update({"_id": task["_id"], "$set": {"result_data": result_structured, "status": "completed"}})

            else:
                print("文件下载失败")
                db[task_table].find_one_and_update({"_id": task["_id"], "$set": {"status": "failed"}})

        else:
            time.sleep(5)


if __name__ == "__main__":
    main()
