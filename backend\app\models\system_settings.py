# app/models/settings.py
from typing import Optional
from pydantic import BaseModel
from bson import ObjectId

class SystemSettings(BaseModel):
    __tablename__ = 'system_settings'
    
    id: Optional[ObjectId] = None
    logo: str = "/static/logo.png"  # 默认logo路径
    title: str = "金鹏大模型平台"  # 默认系统名称
    navTheme: str = "light"  # 默认导航主题
    colorPrimary: str = "#722ED1"  # 默认主题色
    enable_coe: bool = False  # 添加COE引擎控制开关，默认关闭


    class Config:
        json_encoders = {ObjectId: str}
        arbitrary_types_allowed = True

class SystemSettingsUpdate(BaseModel):
    logo: Optional[str] = None
    title: Optional[str] = None
    navTheme: Optional[str] = None
    colorPrimary: Optional[str] = None
    enable_coe: Optional[bool] = False  # 添加COE引擎控制字段