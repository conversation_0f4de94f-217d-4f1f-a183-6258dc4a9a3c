import os
from motor.motor_asyncio import AsyncIOMotorClient
from urllib.parse import quote_plus
from models import ServerInfo, MetricsRecord
import logging

logger = logging.getLogger(__name__)

class Database:
    def __init__(self):
        self.client = None
        self.db = None
        self.server_info = None  # 服务器信息集合
        self.server_metrics = None      # 监控指标集合

    async def connect(self):
        """连接数据库"""
        # 构建MongoDB连接URL
        user = quote_plus(os.getenv("MONGODB_USER"))
        password = quote_plus(os.getenv("MONGODB_PASSWORD"))
        host = os.getenv("MONGODB_HOST")
        port = os.getenv("MONGODB_PORT")
        db_name = os.getenv("DATABASE_NAME")
        auth_source = os.getenv("MONGODB_AUTH_SOURCE")

        url = f"mongodb://{user}:{password}@{host}:{port}/{db_name}?authSource={auth_source}"
        
        # 创建连接
        self.client = AsyncIOMotorClient(url)
        self.db = self.client[db_name]
        
        # 获取集合
        self.server_info = self.db.server_info
        self.server_metrics = self.db.server_metrics
        
        # 创建索引
        await self.create_indexes()

    async def create_indexes(self):
        """创建必要的索引"""
        # 服务器信息表索引
        await self.server_info.create_index("server_id", unique=True)
        await self.server_info.create_index("last_update")

        # 监控指标表索引
        await self.server_metrics.create_index([
            ("server_id", 1),
            ("timestamp", -1)
        ])
        await self.server_metrics.create_index("timestamp")

    async def upsert_server_info(self, info: ServerInfo):
        """更新服务器信息"""
        await self.server_info.update_one(
            {"server_id": info.server_id},
            {"$set": info.dict()},
            upsert=True
        )

    async def insert_metrics(self, metrics: MetricsRecord):
        """插入监控指标记录"""
        await self.server_metrics.insert_one(metrics.dict())

    async def get_latest_metrics(self, server_id: str, limit: int = 100):
        """获取最新的监控记录"""
        cursor = self.server_metrics.find(
            {"server_id": server_id}
        ).sort("timestamp", -1).limit(limit)
        return await cursor.to_list(length=limit)

    async def close(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close() 

    async def delete_server(self, server_id: str) -> bool:
        """删除服务器及其所有相关数据"""
        try:
            # 删除服务器信息
            server_result = await self.server_info.delete_one({"server_id": server_id})
            
            # 删除监控数据
            metrics_result = await self.server_metrics.delete_many({"server_id": server_id})
            
            logger.info(f"删除服务器 {server_id}: "
                       f"信息{server_result.deleted_count}条, "
                       f"监控记录{metrics_result.deleted_count}条")
            
            return server_result.deleted_count > 0
        except Exception as e:
            logger.error(f"删除服务器数据失败: {str(e)}")
            raise 