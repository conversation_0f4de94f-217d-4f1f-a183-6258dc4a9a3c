from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.prompt import Prompt, PromptBase, PromptCreate, PromptUpdate, PromptResponse
from ..db.mongodb import db
from ..utils.auth import verify_token
from datetime import datetime
from bson import ObjectId

from app.utils.logging_config import setup_logging, get_logger
setup_logging()
logger = get_logger(__name__)

router = APIRouter(
    prefix="/api",
    tags=["prompts"]
)

# 获取个人提示词列表，支持分页和过滤
@router.get("/user-prompts", response_model=Dict[str, Any])
async def get_user_prompts(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    title: Optional[str] = None,
    category: Optional[str] = None,
    language: Optional[str] = None,
    model: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    # 基本查询: 只查询用户自己创建的提示词
    query = {"user_id": current_user["id"]}
    
    # 添加额外过滤条件
    if title:
        query["title"] = {"$regex": title, "$options": "i"}
    if category:
        query["category"] = {"$in": [category]}  # 在分类列表中包含指定分类
    if language:
        query["language"] = language
    if model:
        query["models"] = {"$in": [model]}  # 在模型列表中包含指定模型
    if is_active is not None:
        query["is_active"] = is_active
    
    # 查询数据库
    prompts = await db["prompts"].find(query).sort(
        "created_at", -1
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["prompts"].count_documents(query)
    
    # 处理ID格式
    for prompt in prompts:
        prompt["id"] = str(prompt["_id"])
        del prompt["_id"]
    
    return {
        "data": prompts,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取系统提示词列表，支持分页和过滤
@router.get("/system-prompts", response_model=Dict[str, Any])
async def get_system_prompts(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    title: Optional[str] = None,
    category: Optional[str] = None,
    language: Optional[str] = None,
    model: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    # 基本查询: 只查询系统提示词
    query = {"is_system": True}
    
    # 添加额外过滤条件
    if title:
        query["title"] = {"$regex": title, "$options": "i"}
    if category:
        query["category"] = {"$in": [category]}  # 在分类列表中包含指定分类
    if language:
        query["language"] = language
    if model:
        query["models"] = {"$in": [model]}  # 在模型列表中包含指定模型
    if is_active is not None:
        query["is_active"] = is_active
    
    # 查询数据库
    prompts = await db["prompts"].find(query).sort(
        "created_at", -1
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    # 获取总数
    total = await db["prompts"].count_documents(query)
    
    # 处理ID格式
    for prompt in prompts:
        prompt["id"] = str(prompt["_id"])
        del prompt["_id"]
    
    return {
        "data": prompts,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取提示词详情
@router.get("/prompts/{prompt_id}", response_model=Dict[str, Any])
async def get_prompt(
    prompt_id: str,
    current_user: dict = Depends(verify_token)
):
    try:
        prompt = await db["prompts"].find_one({"_id": ObjectId(prompt_id)})
        if not prompt:
            raise HTTPException(status_code=404, detail="提示词不存在")
        
        # 检查访问权限: 用户只能查看自己的提示词或系统提示词
        if not prompt.get("is_system", False) and prompt.get("user_id") != current_user["id"]:
            raise HTTPException(status_code=403, detail="无权访问此提示词")
        
        # 处理ID格式
        prompt["id"] = str(prompt["_id"])
        del prompt["_id"]
        
        # 更新使用次数
        await db["prompts"].update_one(
            {"_id": ObjectId(prompt_id)},
            {"$inc": {"usage_count": 1}}
        )
        
        return {
            "data": prompt,
            "success": True
        }
    except Exception as e:
        logger.error(f"获取提示词详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取提示词详情失败: {str(e)}")

# 创建新提示词
@router.post("/prompts", response_model=Dict[str, Any])
async def create_prompt(
    prompt: PromptCreate,
    current_user: dict = Depends(verify_token)
):
    try:
        new_prompt = prompt.dict()
        new_prompt.update({
            "user": current_user["name"],
            "user_id": current_user["id"],
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "usage_count": 0
        })
        
        result = await db["prompts"].insert_one(new_prompt)
        created_prompt = await db["prompts"].find_one({"_id": result.inserted_id})
        
        # 处理ID格式
        created_prompt["id"] = str(created_prompt["_id"])
        del created_prompt["_id"]
        
        return {
            "data": created_prompt,
            "success": True
        }
    except Exception as e:
        logger.error(f"创建提示词失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建提示词失败: {str(e)}")

# 更新提示词
@router.put("/prompts/{prompt_id}", response_model=Dict[str, Any])
async def update_prompt(
    prompt_id: str,
    prompt: PromptUpdate,
    current_user: dict = Depends(verify_token)
):
    try:
        # 检查提示词是否存在
        existing_prompt = await db["prompts"].find_one({"_id": ObjectId(prompt_id)})
        if not existing_prompt:
            raise HTTPException(status_code=404, detail="提示词不存在")
        
        # 检查权限: 用户只能更新自己的提示词
        if existing_prompt.get("user_id") != current_user["id"]:
            raise HTTPException(status_code=403, detail="无权更新此提示词")
        
        # 检查是否为系统提示词
        if existing_prompt.get("is_system", False):
            raise HTTPException(status_code=403, detail="系统提示词不能被修改")
        
        # 只更新非空字段
        update_data = {k: v for k, v in prompt.dict(exclude_unset=True).items() if v is not None}
        update_data["updated_at"] = datetime.now()
        
        result = await db["prompts"].update_one(
            {"_id": ObjectId(prompt_id)},
            {"$set": update_data}
        )
        
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="提示词不存在")
        
        updated_prompt = await db["prompts"].find_one({"_id": ObjectId(prompt_id)})
        updated_prompt["id"] = str(updated_prompt["_id"])
        del updated_prompt["_id"]
        
        return {
            "data": updated_prompt,
            "success": True
        }
    except Exception as e:
        logger.error(f"更新提示词失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新提示词失败: {str(e)}")

# 删除提示词
@router.delete("/prompts/{prompt_id}", response_model=Dict[str, Any])
async def delete_prompt(
    prompt_id: str,
    current_user: dict = Depends(verify_token)
):
    try:
        # 检查提示词是否存在
        existing_prompt = await db["prompts"].find_one({"_id": ObjectId(prompt_id)})
        if not existing_prompt:
            raise HTTPException(status_code=404, detail="提示词不存在")
        
        # 检查权限: 用户只能删除自己的提示词
        if existing_prompt.get("user_id") != current_user["id"]:
            raise HTTPException(status_code=403, detail="无权删除此提示词")
        
        # 检查是否为系统提示词
        if existing_prompt.get("is_system", False):
            raise HTTPException(status_code=403, detail="系统提示词不能被删除")
        
        result = await db["prompts"].delete_one({"_id": ObjectId(prompt_id)})
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="提示词不存在")
        
        return {
            "id": prompt_id,
            "success": True
        }
    except Exception as e:
        logger.error(f"删除提示词失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除提示词失败: {str(e)}")

# 复制提示词（创建一个副本）
@router.post("/prompts/{prompt_id}/copy", response_model=Dict[str, Any])
async def copy_prompt(
    prompt_id: str,
    current_user: dict = Depends(verify_token)
):
    try:
        # 检查提示词是否存在
        existing_prompt = await db["prompts"].find_one({"_id": ObjectId(prompt_id)})
        if not existing_prompt:
            raise HTTPException(status_code=404, detail="提示词不存在")
        
        # 检查访问权限: 用户只能复制自己的提示词或系统提示词
        if not existing_prompt.get("is_system", False) and existing_prompt.get("user_id") != current_user["id"]:
            raise HTTPException(status_code=403, detail="无权复制此提示词")
        
        # 创建新的提示词副本
        new_prompt = dict(existing_prompt)
        del new_prompt["_id"]  # 删除原ID
        
        # 更新副本信息
        new_prompt.update({
            "title": f"{new_prompt['title']} (副本)",
            "user": current_user["name"],
            "user_id": current_user["id"],
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "usage_count": 0,
            "is_system": False  # 复制的提示词永远不是系统提示词
        })
        
        result = await db["prompts"].insert_one(new_prompt)
        created_prompt = await db["prompts"].find_one({"_id": result.inserted_id})
        
        # 处理ID格式
        created_prompt["id"] = str(created_prompt["_id"])
        del created_prompt["_id"]
        
        return {
            "data": created_prompt,
            "success": True
        }
    except Exception as e:
        logger.error(f"复制提示词失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"复制提示词失败: {str(e)}")

# 获取提示词分类列表
@router.get("/prompt_categories", response_model=Dict[str, Any])
async def get_prompt_categories(
    current_user: dict = Depends(verify_token)
):
    try:
        # 使用聚合管道获取所有唯一的分类
        pipeline = [
            {"$unwind": "$category"},  # 将分类数组展开
            {"$group": {"_id": "$category"}},  # 按分类分组
            {"$sort": {"_id": 1}}  # 按分类名称排序
        ]
        
        categories_cursor = db["prompts"].aggregate(pipeline)
        categories = []
        async for doc in categories_cursor:
            if doc["_id"]:  # 确保不包含空分类
                categories.append(doc["_id"])
        
        return {
            "data": categories,
            "success": True
        }
    except Exception as e:
        logger.error(f"获取提示词分类失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取提示词分类失败: {str(e)}")
