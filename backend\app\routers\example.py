from fastapi import APIRouter, HTTPException
from ..db.mongodb import db
from ..utils.logging_config import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.get("/items")
async def get_items():
    try:
        items = await db.collection.find().to_list(length=100)
        return items
    except Exception as e:
        logger.error(f"获取数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="数据库操作失败") 