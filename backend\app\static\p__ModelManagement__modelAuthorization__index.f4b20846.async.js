"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3030],{51042:function(e,t,r){var n=r(1413),a=r(67294),u=r(42110),i=r(91146),s=function(e,t){return a.createElement(i.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:u.Z}))},o=a.forwardRef(s);t.Z=o},56251:function(e,t,r){r.r(t),r.d(t,{default:function(){return ee}});var n=r(97857),a=r.n(n),u=r(15009),i=r.n(u),s=r(99289),o=r.n(s),c=r(5574),l=r.n(c),p=r(67294),d=r(97131),f=r(12453),h=r(8232),m=r(2453),x=r(83622),v=r(17788),b=r(98423),y=r(8745),w=r(34041),Z=r(93967),k=r.n(Z),_=r(50344),C=r(87263),j=r(53124);const{Option:g}=w.default;function O(e){return(null==e?void 0:e.type)&&(e.type.isSelectOption||e.type.isSelectOptGroup)}const S=(e,t)=>{var r;const{prefixCls:n,className:a,popupClassName:u,dropdownClassName:i,children:s,dataSource:o}=e,c=(0,_.Z)(s);let l;1===c.length&&p.isValidElement(c[0])&&!O(c[0])&&([l]=c);const d=l?()=>l:void 0;let f;f=c.length&&O(c[0])?s:o?o.map((e=>{if(p.isValidElement(e))return e;switch(typeof e){case"string":return p.createElement(g,{key:e,value:e},e);case"object":{const{value:t}=e;return p.createElement(g,{key:t,value:t},e.text)}default:return}})):[];const{getPrefixCls:h}=p.useContext(j.E_),m=h("select",n),[x]=(0,C.Cn)("SelectLike",null===(r=e.dropdownStyle)||void 0===r?void 0:r.zIndex);return p.createElement(w.default,Object.assign({ref:t,suffixIcon:null},(0,b.Z)(e,["dataSource","dropdownClassName"]),{prefixCls:m,popupClassName:u||i,dropdownStyle:Object.assign(Object.assign({},e.dropdownStyle),{zIndex:x}),className:k()(`${m}-auto-complete`,a),mode:w.default.SECRET_COMBOBOX_MODE_DO_NOT_USE,getInputElement:d}),f)};var E=p.forwardRef(S);const{Option:P}=w.default,I=(0,y.Z)(E,"dropdownAlign",(e=>(0,b.Z)(e,["visible"]))),T=E;T.Option=P,T._InternalPanelDoNotUseOrYouWillBeFired=I;var N=T,z=r(98137),D=r(51042),M=r(78158);function Y(e){return U.apply(this,arguments)}function U(){return(U=o()(i()().mark((function e(t){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,M.N)("/api/authorizations",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function q(e){return F.apply(this,arguments)}function F(){return(F=o()(i()().mark((function e(t){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,M.N)("/api/authorizations",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function R(e,t){return G.apply(this,arguments)}function G(){return(G=o()(i()().mark((function e(t,r){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,M.N)("/api/authorizations/".concat(t),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function B(e){return L.apply(this,arguments)}function L(){return(L=o()(i()().mark((function e(t){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,M.N)("/api/authorizations/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function V(){return W.apply(this,arguments)}function W(){return(W=o()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,M.N)("/api/authUsers",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function A(){return K.apply(this,arguments)}function K(){return(K=o()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,M.N)("/api/authEmbModels",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function X(){return $.apply(this,arguments)}function $(){return($=o()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,M.N)("/api/authLLMModels",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var H=r(27484),J=r.n(H),Q=r(85893),ee=function(){var e=(0,p.useState)(!1),t=l()(e,2),r=t[0],n=t[1],u=(0,p.useState)(!1),s=l()(u,2),c=s[0],b=s[1],y=(0,p.useState)(void 0),Z=l()(y,2),k=Z[0],_=Z[1],C=(0,p.useRef)(),j=h.Z.useForm(),g=l()(j,1)[0],O=(0,p.useState)([]),S=l()(O,2),E=S[0],P=S[1],I=(0,p.useState)([]),T=l()(I,2),M=T[0],U=T[1],F=(0,p.useState)("llm"),G=l()(F,2),L=(G[0],G[1]),W=(0,p.useState)(null),K=l()(W,2),$=K[0],H=K[1],ee=(0,p.useState)(null),te=l()(ee,2),re=te[0],ne=te[1];(0,p.useEffect)((function(){var e=function(){var e=o()(i()().mark((function e(){var t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,V();case 3:t=e.sent,P(t.map((function(e){return{label:e.name,value:e.name,id:e.id}}))),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),m.ZP.error("获取用户数据失败");case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var ae=function(){var e=o()(i()().mark((function e(t){var r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,"llm"!==t){e.next=7;break}return e.next=4,X();case 4:e.t0=e.sent,e.next=10;break;case 7:return e.next=9,A();case 9:e.t0=e.sent;case 10:r=e.t0,U(r.map((function(e){return{label:e.name,value:e.name,id:e.id}}))),e.next=17;break;case 14:e.prev=14,e.t1=e.catch(0),m.ZP.error("获取模型数据失败");case 17:case"end":return e.stop()}}),e,null,[[0,14]])})));return function(t){return e.apply(this,arguments)}}(),ue=function(){var e=o()(i()().mark((function e(t){var r,u;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if($&&re){e.next=3;break}return m.ZP.error("请选择用户和模型"),e.abrupt("return",!1);case 3:return r=m.ZP.loading("正在添加"),e.prev=4,e.next=7,q(a()(a()({},t),{},{user_id:$,m_id:re,expiration_date:t.expiration_date.format("YYYY-MM-DD")}));case 7:return r(),m.ZP.success("添加成功"),n(!1),null===(u=C.current)||void 0===u||u.reload(),g.resetFields(),e.abrupt("return",!0);case 15:return e.prev=15,e.t0=e.catch(4),r(),m.ZP.error("添加失败，请重试"),e.abrupt("return",!1);case 20:case"end":return e.stop()}}),e,null,[[4,15]])})));return function(t){return e.apply(this,arguments)}}(),ie=function(){var e=o()(i()().mark((function e(t){var r,n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null!=k&&k.id){e.next=3;break}return m.ZP.error("更新失败，缺少授权 ID"),e.abrupt("return",!1);case 3:return r=m.ZP.loading("正在更新"),e.prev=4,e.next=7,R(k.id,{expiration_date:t.expiration_date.format("YYYY-MM-DD")});case 7:return r(),m.ZP.success("更新成功"),b(!1),_(void 0),null===(n=C.current)||void 0===n||n.reload(),g.resetFields(),e.abrupt("return",!0);case 16:return e.prev=16,e.t0=e.catch(4),r(),m.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 21:case"end":return e.stop()}}),e,null,[[4,16]])})));return function(t){return e.apply(this,arguments)}}(),se=function(){var e=o()(i()().mark((function e(t){var r,n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=m.ZP.loading("正在删除"),e.prev=1,e.next=4,B(t.id);case 4:return r(),m.ZP.success("删除成功"),null===(n=C.current)||void 0===n||n.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),r(),m.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(t){return e.apply(this,arguments)}}(),oe=[{title:"用户名称",dataIndex:"user_name",valueType:"text"},{title:"模型名称",dataIndex:"m_name",valueType:"text"},{title:"授权类型",dataIndex:"authorization_type",valueType:"text"},{title:"到期时间",dataIndex:"expiration_date",valueType:"date"},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"操作",dataIndex:"option",valueType:"option",render:function(e,t){return[(0,Q.jsx)(x.ZP,{type:"link",onClick:function(){b(!0),_(t)},children:"编辑"},"edit-".concat(t.id)),(0,Q.jsx)(x.ZP,{type:"link",danger:!0,onClick:function(){return se(t)},children:"删除"},"delete-".concat(t.id))]}}];return(0,Q.jsxs)(d._z,{children:[(0,Q.jsx)(f.Z,{headerTitle:"模型授权管理",actionRef:C,rowKey:"id",search:{labelWidth:120,defaultCollapsed:!0},toolBarRender:function(){return[(0,Q.jsxs)(x.ZP,{type:"primary",onClick:function(){n(!0)},children:[(0,Q.jsx)(D.Z,{})," 新建"]},"primary")]},request:function(){var e=o()(i()().mark((function e(t){var r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Y(a()({current:t.current,pageSize:t.pageSize},t));case 2:return r=e.sent,e.abrupt("return",{data:r.data,success:r.success,total:r.total});case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),columns:oe}),(0,Q.jsx)(v.Z,{visible:r,title:"新建授权",onCancel:function(){return n(!1)},onOk:function(){return g.submit()},children:(0,Q.jsxs)(h.Z,{form:g,layout:"horizontal",onFinish:ue,labelCol:{span:6},wrapperCol:{span:18},children:[(0,Q.jsx)(h.Z.Item,{name:"user_name",label:"用户名称",rules:[{required:!0,message:"请输入用户名称"}],children:(0,Q.jsx)(N,{options:E,placeholder:"输入用户名称",onSelect:function(e,t){return H(t.id)},filterOption:function(e,t){return-1!==t.value.toUpperCase().indexOf(e.toUpperCase())}})}),(0,Q.jsx)(h.Z.Item,{name:"authorization_type",label:"授权类型",rules:[{required:!0,message:"请选择授权类型"}],children:(0,Q.jsxs)(w.default,{onChange:function(e){L(e),ae(e)},children:[(0,Q.jsx)(w.default.Option,{value:"llm",children:"大语言模型"}),(0,Q.jsx)(w.default.Option,{value:"embedding",children:"嵌入模型"})]})}),(0,Q.jsx)(h.Z.Item,{name:"m_name",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:(0,Q.jsx)(N,{options:M,placeholder:"输入模型名称",onSelect:function(e,t){return ne(t.id)},filterOption:function(e,t){return-1!==t.value.toUpperCase().indexOf(e.toUpperCase())}})}),(0,Q.jsx)(h.Z.Item,{name:"expiration_date",label:"到期时间",rules:[{required:!0,message:"请选择到期时间"}],children:(0,Q.jsx)(z.default,{})})]})}),k&&(0,Q.jsx)(v.Z,{visible:c,title:"更新授权",onCancel:function(){return b(!1)},onOk:function(){return g.submit()},children:(0,Q.jsx)(h.Z,{form:g,layout:"horizontal",initialValues:{expiration_date:J()(k.expiration_date)},onFinish:ie,labelCol:{span:6},wrapperCol:{span:18},children:(0,Q.jsx)(h.Z.Item,{name:"expiration_date",label:"到期时间",rules:[{required:!0,message:"请选择到期时间"}],children:(0,Q.jsx)(z.default,{})})})})]})}}}]);