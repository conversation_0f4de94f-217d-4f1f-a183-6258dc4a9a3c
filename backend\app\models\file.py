from mongoengine import Document, StringField, IntField, DateTimeField, FloatField, DictField, ListField, BooleanField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, Dict, List
from bson import ObjectId

# MongoEngine 模型
class File(Document):
    meta = {
        'collection': 'files'
    }
    _id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    file_name = StringField(required=True)
    file_type = StringField(required=True)
    file_size = IntField(required=True)
    file_path = StringField(required=True)
    upload_time = DateTimeField(default=datetime.now)
    last_modified = DateTimeField(default=datetime.now)
    user_id = IntField(required=True)
    user_name = StringField()
    metadata = DictField()
    tags = ListField(StringField())
    app_info = StringField()
    is_expired = BooleanField(default=False)  # 新增字段

# Pydantic 模型
class FileBase(BaseModel):
    file_name: str
    file_type: str
    file_size: int
    file_path: str
    user_id: int
    user_name: Optional[str] = None
    metadata: Optional[Dict] = Field(default_factory=dict)
    tags: Optional[List[str]] = Field(default_factory=list)
    app_info: Optional[str] = None
    is_expired: bool = False  # 新增字段

class FileCreate(FileBase):
    pass

class FileUpdate(BaseModel):
    file_name: Optional[str] = None
    metadata: Optional[Dict] = None
    tags: Optional[List[str]] = None
    is_expired: Optional[bool] = None  # 新增字段

class FileResponse(FileBase):
    id: str
    upload_time: datetime
    last_modified: datetime

    class Config:
        from_attributes = True
