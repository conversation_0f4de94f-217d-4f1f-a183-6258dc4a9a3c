"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1006],{44902:function(e,t,n){n.r(t),n.d(t,{default:function(){return $}});var r=n(15009),a=n.n(r),s=n(99289),i=n.n(s),l=n(5574),o=n.n(l),u=n(34804),c=n(76509),d=n(97131),p=n(88372),f=n(35312),x=n(83622),v=n(85418),m=n(42075),h=n(2453),g=n(55054),b=n(26412),j=n(66309),Z=n(4393),y=n(67294),w=n(44373),k=n(9783),S=n.n(k),P=(0,n(24444).kc)((function(e){var t=e.token;return{main:{".ant-descriptions-row > td":{paddingBottom:"8px"},".ant-page-header-heading-extra":{flexDirection:"column"}},headerList:{marginBottom:"4px",".ant-descriptions-row > td":{paddingBottom:"8px"}},stepDescription:S()({position:"relative",left:"38px",paddingTop:"8px",fontSize:"14px",textAlign:"left","> div":{marginTop:"8px",marginBottom:"4px"}},"@media screen and (max-width: ".concat(t.screenSM,"px)"),{left:"8px"}),pageHeader:S()({".ant-page-header-heading-extra > * + *":{marginLeft:"8px"}},"@media screen and (max-width: ".concat(t.screenSM,"px)"),{".ant-pro-page-header-wrap-row":{flexDirection:"column"}}),moreInfo:{display:"flex",justifyContent:"space-between",width:"200px"},uploadCard:{padding:"16px",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)"},uploadHint:{marginTop:"8px",color:"#888",fontSize:"12px"}}})),C=n(27484),_=n.n(C),T=n(12453),I=n(17788),L=n(71471),N=n(97857),O=n.n(N),R=n(19632),B=n.n(R),z=n(8232),D=n(34994),M=n(5966),F=n(90672),H=n(64317),V=n(85893),Y=function(e){var t=e.modalVisible,n=e.onCancel,r=e.onSubmit,s=e.values,l=z.Z.useForm(),u=o()(l,1)[0],c=y.useState([]),d=o()(c,2),p=d[0],f=d[1];y.useEffect((function(){t&&s&&u.setFieldsValue(s)}),[t,s,u]);var x=function(){var e=i()(a()().mark((function e(){var t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,u.validateFields();case 3:t=e.sent,r(O()(O()({},s),t)),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("表单验证失败:",e.t0);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();return(0,V.jsx)(I.Z,{title:"更新数据",visible:t,onCancel:n,onOk:x,destroyOnClose:!0,children:(0,V.jsxs)(D.A,{form:u,layout:"vertical",initialValues:s,submitter:!1,children:[(0,V.jsx)(M.Z,{name:"name",label:"数据集名称",rules:[{required:!0,message:"请输入数据集名称"}]}),(0,V.jsx)(F.Z,{name:"description",label:"数据集描述",rules:[{required:!0,message:"请输入数据集描述"}]}),(0,V.jsx)(H.Z,{name:"tags",label:"数据集标签",mode:"tags",placeholder:"请输入数据标签",onChange:function(e){var t=e.map((function(e){return{label:e,value:e}}));f((function(e){var n=e.map((function(e){return e.value})),r=t.filter((function(e){return!n.includes(e.value)}));return[].concat(B()(e),B()(r))}))},options:p}),(0,V.jsx)(H.Z,{name:"processing_status",label:"数据处理状态",options:[{label:"待处理",value:"pending"},{label:"处理中",value:"processing"},{label:"已处理",value:"completed"},{label:"出错",value:"error"}],rules:[{required:!0,message:"请选择数据处理状态"}]})]})})},E=function(e){var t=e.datasetId,n=(0,y.useRef)(),r=(0,y.useState)(!1),s=o()(r,2),l=s[0],u=s[1],c=(0,y.useState)(void 0),d=o()(c,2),p=d[0],f=d[1],v=(0,y.useState)(!1),m=o()(v,2),g=m[0],b=m[1],j=(0,y.useState)(void 0),Z=o()(j,2),k=Z[0],S=Z[1],P=function(){var e=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:I.Z.confirm({title:"确认删除",content:"确定要删除这条记录吗？",okText:"确认",cancelText:"取消",onOk:function(){var e=i()(a()().mark((function e(){var r,s;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=h.ZP.loading("正在删除"),e.prev=1,e.next=4,(0,w.ZX)(t);case 4:return r(),h.ZP.success("删除成功"),null===(s=n.current)||void 0===s||s.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),r(),h.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),C=function(){var e=i()(a()().mark((function e(t){var r,s;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=h.ZP.loading("正在更新"),e.prev=1,e.next=4,(0,w.UU)(t.id,t);case 4:return r(),h.ZP.success("更新成功"),u(!1),f(void 0),null===(s=n.current)||void 0===s||s.reload(),e.abrupt("return",!0);case 12:return e.prev=12,e.t0=e.catch(1),r(),h.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 17:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(t){return e.apply(this,arguments)}}(),_=[{title:"内容",dataIndex:"data",ellipsis:!0,width:"60%"},{title:"相关图片数量",dataIndex:"images",search:!1,render:function(e){return e?e.length:0}},{title:"操作",valueType:"option",render:function(e,t){return[(0,V.jsx)(x.ZP,{type:"link",onClick:function(){b(!0),S(t)},children:"预览"},"preview"),(0,V.jsx)(x.ZP,{type:"link",onClick:function(){u(!0),f(t)},children:"编辑"},"edit"),(0,V.jsx)(x.ZP,{type:"link",danger:!0,onClick:function(){return P(t.id)},children:"删除"},"delete")]}}];return(0,V.jsxs)("div",{children:[(0,V.jsx)(T.Z,{actionRef:n,columns:_,request:function(){var e=i()(a()().mark((function e(n,r,s){var i;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,w.UU)(t,{current:n.current||1,pageSize:n.pageSize||10,file_id:n.file_id,data:n.data,sorter:r,filter:s});case 3:return i=e.sent,e.abrupt("return",{data:i.data||[],success:!0,total:i.total||0});case 7:return e.prev=7,e.t0=e.catch(0),console.error("获取数据失败:",e.t0),h.ZP.error("获取数据失败"),e.abrupt("return",{data:[],success:!1,total:0});case 12:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t,n,r){return e.apply(this,arguments)}}(),rowKey:"id",pagination:{showSizeChanger:!0,showTotal:!1},toolBarRender:function(){return[(0,V.jsx)(x.ZP,{type:"primary",onClick:function(){return u(!0)},children:"新建记录"},"create")]}}),(0,V.jsx)(I.Z,{title:"数据预览",open:g,onCancel:function(){b(!1),S(void 0)},footer:null,width:800,children:k&&(0,V.jsxs)("div",{style:{padding:"20px"},children:[(0,V.jsx)(L.Z.Title,{level:5,children:"内容"}),(0,V.jsx)(L.Z.Paragraph,{style:{padding:"16px",border:"1px solid #d9d9d9",borderRadius:"4px",backgroundColor:"#fafafa"},children:k.data}),k.images&&k.images.length>0&&(0,V.jsxs)(V.Fragment,{children:[(0,V.jsx)(L.Z.Title,{level:5,children:"相关图片"}),(0,V.jsx)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(240px, 1fr))",gap:"16px",width:"100%"},children:k.images.map((function(e,t){return(0,V.jsx)("img",{src:e,alt:"图片 ".concat(t+1),style:{width:"100%",objectFit:"cover",borderRadius:"4px"}},t)}))})]})]})}),(l||p)&&(0,V.jsx)(Y,{onSubmit:function(){var e=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,C(t);case 2:e.sent&&(u(!1),f(void 0));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),onCancel:function(){u(!1),f(void 0)},modalVisible:l,values:p})]})},A=["blue"],U=function(e){var t=e.datasetId,n=(0,y.useRef)(),r=(0,y.useState)(!1),s=o()(r,2),l=s[0],u=s[1],c=(0,y.useState)(void 0),d=o()(c,2),p=d[0],f=d[1],v=function(){var e=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:I.Z.confirm({title:"确认删除",content:"确定要删除这个文件吗？",okText:"确认",cancelText:"取消",onOk:function(){var e=i()(a()().mark((function e(){var r,s;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=h.ZP.loading("正在删除"),e.prev=1,e.next=4,(0,w.do)(t);case 4:return r(),h.ZP.success("删除成功"),null===(s=n.current)||void 0===s||s.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),r(),h.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),m=function(){var e=i()(a()().mark((function e(t){var r,s;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=h.ZP.loading("正在更新"),e.prev=1,e.next=4,(0,w.k_)(t.id,t);case 4:return r(),h.ZP.success("更新成功"),u(!1),f(void 0),null===(s=n.current)||void 0===s||s.reload(),e.abrupt("return",!0);case 12:return e.prev=12,e.t0=e.catch(1),r(),h.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 17:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(t){return e.apply(this,arguments)}}(),g=[{title:"文件名",dataIndex:"name"},{title:"格式",dataIndex:"data_type",ellipsis:!0},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"标签",dataIndex:"tags",search:!0,render:function(e,t){var n;return(0,V.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:"4px"},children:null===(n=t.tags)||void 0===n?void 0:n.map((function(e,t){return(0,V.jsx)(j.Z,{color:A[t%A.length],style:{borderRadius:"12px",padding:"0 10px",fontSize:"12px",lineHeight:"20px"},children:e},e)}))})}},{title:"状态",dataIndex:"processing_status",valueEnum:{pending:{text:"待处理",status:"Success"},processing:{text:"处理中",status:"Processing"},completed:{text:"已完成",status:"Success"}}},{title:"操作",valueType:"option",render:function(e,t){return[(0,V.jsx)(x.ZP,{type:"link",onClick:function(){u(!0),f(t)},children:"编辑"},"edit"),(0,V.jsx)(x.ZP,{type:"link",danger:!0,onClick:function(){return v(t.id)},children:"删除"},"delete")]}}];return(0,V.jsxs)("div",{children:[(0,V.jsx)(T.Z,{actionRef:n,columns:g,request:function(){var e=i()(a()().mark((function e(n,r,s){var i;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,w.jF)(t,{current:n.current,pageSize:n.pageSize,name:n.name,data_type:n.data_type,tags:n.tags,processing_status:n.processing_status,sorter:r,filter:s});case 3:return i=e.sent,e.abrupt("return",{data:i.data||[],success:!0,total:i.total||0});case 7:return e.prev=7,e.t0=e.catch(0),console.error("获取文件数据失败:",e.t0),h.ZP.error("获取文件数据失败"),e.abrupt("return",{data:[],success:!1,total:0});case 12:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t,n,r){return e.apply(this,arguments)}}(),rowKey:"id",pagination:{showSizeChanger:!0},toolBarRender:function(){return[]}}),(l||p)&&(0,V.jsx)(UpdateForm,{onSubmit:function(){var e=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,m(t);case 2:e.sent&&(u(!1),f(void 0));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),onCancel:function(){u(!1),f(void 0)},modalVisible:l,values:p})]})},q=n(11550),J=n(33914),W=q.Z.Dragger,K=[{label:"微调",value:"微调"},{label:"预训练",value:"预训练"},{label:"测试",value:"测试"},{label:"金融问答",value:"金融问答"},{label:"信贷",value:"信贷"},{label:"LLM微调",value:"LLM微调"},{label:"对话数据",value:"对话数据"},{label:"问答配对",value:"问答配对"},{label:"生成式任务",value:"生成式任务"},{label:"指令微调",value:"指令微调"},{label:"上下文理解",value:"上下文理解"},{label:"语义匹配",value:"语义匹配"},{label:"知识检索",value:"知识检索"},{label:"补全任务",value:"补全任务"},{label:"语言翻译",value:"语言翻译"}],X=function(e){var t=e.datasetId,n=e.datasetTypeOptions,r=P().styles,s=y.useState([]),l=o()(s,2),u=l[0],c=l[1],d={name:"file",multiple:!0,maxCount:50,fileList:u,beforeUpload:function(e){return![".csv",".xls",".xlsx",".json",".jsonl"].some((function(t){return e.name.toLowerCase().endsWith(t)}))&&(h.ZP.error("只支持 CSV、Excel、JSON 和 JSONL 格式的文件"),q.Z.LIST_IGNORE)},onChange:function(e){c(e.fileList);e.file.status;e.fileList.length>50&&h.ZP.warning("最多只能上传50个文件"),console.log(e.file,e.fileList)},onDrop:function(e){console.log("Dropped files",e.dataTransfer.files)}},p=function(){var e=i()(a()().mark((function e(r){var s,i,l;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,0!==u.length){e.next=4;break}return h.ZP.error("请选择要上传的文件"),e.abrupt("return");case 4:return s=u.map((function(e){return e.originFileObj})),i=Array.isArray(r.tags)?r.tags:[],console.log("上传数据：",{tags:i,dataset_id:t,dataset_type:n,files:u.map((function(e){return e.name}))}),e.next=9,(0,w.EN)(t,n,i,s);case 9:(l=e.sent)?h.ZP.success("文件上传成功"):h.ZP.error((null==l?void 0:l.message)||"创建数据集失败"),e.next=17;break;case 13:e.prev=13,e.t0=e.catch(0),h.ZP.error((null===e.t0||void 0===e.t0?void 0:e.t0.message)||"创建数据集失败"),console.error("创建数据集错误:",e.t0);case 17:case"end":return e.stop()}}),e,null,[[0,13]])})));return function(t){return e.apply(this,arguments)}}();return(0,V.jsx)(Z.Z,{bordered:!1,children:(0,V.jsxs)(D.A,{style:{margin:"auto",marginTop:8,marginBottom:24,maxWidth:600,paddingBottom:24},name:"basic",layout:"vertical",initialValues:{name:"新建数据集",description:"这是一个新的数据集，用于..."},onFinish:p,submitter:{searchConfig:{submitText:"上传"}},children:[(0,V.jsx)(H.Z,{name:"tags",width:"xl",label:(0,V.jsxs)("span",{children:["文件表现",(0,V.jsx)("em",{className:r.optional,children:"（选填）"})]}),style:{width:"100%"},mode:"tags",placeholder:"请输入数据标签",onChange:function(e){console.log("selected ".concat(e))},options:K}),(0,V.jsxs)(W,O()(O()({},d),{},{style:{margin:"auto",marginTop:8,marginBottom:24,maxWidth:600,paddingBottom:24},children:[(0,V.jsx)("p",{className:"ant-upload-drag-icon",children:(0,V.jsx)(J.Z,{})}),(0,V.jsx)("p",{className:"ant-upload-text",children:"点击或拖拽文件到此区域上传"}),(0,V.jsx)("p",{className:"ant-upload-hint",children:"支持单个或批量上传。严格禁止上传公司数据或其他禁止的文件。支持数据格式：CSV、Excel、JSON、JSONL"})]}))]})})},G=x.ZP.Group,Q=(0,V.jsx)(c.X.Consumer,{children:function(e){return e.isMobile?(0,V.jsx)(v.Z.Button,{type:"primary",icon:(0,V.jsx)(u.Z,{}),menu:{items:[{key:"1",label:"编辑"},{key:"2",label:"删除"}]},placement:"bottomRight",children:"主操作"}):(0,V.jsx)(m.Z,{children:(0,V.jsxs)(G,{children:[(0,V.jsx)(x.ZP,{children:"编辑"}),(0,V.jsx)(x.ZP,{children:"删除"})]})})}}),$=function(){var e=P().styles,t=(0,f.useParams)().id,n=(0,f.useNavigate)(),r=(0,y.useState)(null),s=o()(r,2),l=s[0],u=s[1],x=(0,y.useState)(!0),v=o()(x,2),m=v[0],k=v[1],S=(0,y.useState)(!1),C=o()(S,2),T=(C[0],C[1],(0,y.useState)({advancedOperation1:[],advancedOperation2:[],advancedOperation3:[]})),I=o()(T,2),L=(I[0],I[1],(0,y.useState)("records")),N=o()(L,2),O=N[0],R=N[1];(0,y.useEffect)((function(){if(console.log("id",t),!t)return h.ZP.error("无效的数据集ID"),void n("/dataset/structured");var e=function(){var e=i()(a()().mark((function e(){var n;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,k(!0),e.next=4,(0,w.BQ)(t);case 4:n=e.sent,u(n),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("加载数据集失败:",e.t0),h.ZP.error("加载数据集失败");case 12:return e.prev=12,k(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(){return e.apply(this,arguments)}}();e()}),[t,n]);var B=(0,V.jsxs)("div",{className:e.moreInfo,children:[(0,V.jsx)(g.Z,{title:"状态",value:null==l?void 0:l.processing_status}),(0,V.jsx)(g.Z,{title:"数据行数",value:null==l?void 0:l.row_count})]}),z=(0,V.jsx)(c.X.Consumer,{children:function(t){var n,r=t.isMobile;return(0,V.jsxs)(b.Z,{className:e.headerList,size:"small",column:r?1:2,children:[(0,V.jsx)(b.Z.Item,{label:"描述",children:null==l?void 0:l.description}),(0,V.jsx)(b.Z.Item,{label:"标签",children:(null==l||null===(n=l.tags)||void 0===n?void 0:n.map((function(e){return(0,V.jsx)(j.Z,{color:"purple",style:{marginRight:4},children:e},e)})))||"-"}),(0,V.jsx)(b.Z.Item,{label:"创建时间",children:null!=l&&l.created_at?_()(l.created_at).format("YYYY-MM-DD HH:mm:ss"):"-"}),(0,V.jsx)(b.Z.Item,{label:"最后更新时间",children:null!=l&&l.last_updated?_()(l.last_updated).format("YYYY-MM-DD HH:mm:ss"):"-"})]})}}),D={records:(0,V.jsx)(E,{datasetId:t}),related:(0,V.jsx)(U,{datasetId:t}),upload:(0,V.jsx)(X,{datasetId:t,datasetTypeOptions:"structured"})};return(0,V.jsx)(d._z,{title:"".concat((null==l?void 0:l.name)||"加载中..."),extra:Q,className:e.pageHeader,content:m?(0,V.jsx)("div",{children:"加载中..."}):z,extraContent:m?(0,V.jsx)("div",{children:"加载中..."}):B,tabActiveKey:O,onTabChange:function(e){R(e)},tabList:[{key:"records",tab:"数据记录"},{key:"related",tab:"相关文件"},{key:"upload",tab:"文件上传"}],children:m?(0,V.jsx)(Z.Z,{loading:!0}):(0,V.jsx)("div",{className:e.main,children:(0,V.jsx)(p.f,{children:D[O]})})})}}}]);