# app/routers/auth.py
from fastapi import APIRouter, Depends, status, Form, Request, Query, Header
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from pydantic import BaseModel
from typing import List, Optional
from app.utils.logging_config import setup_logging, get_logger
from fastapi.responses import JSONResponse
import random
from ..core.config import settings
from datetime import datetime, timedelta
from ..db.mongodb import db
import hashlib
from app.utils.logging_config import setup_logging, get_logger
from ..utils.auth import verify_token
import os

# 设置日志配置
setup_logging()
logger = get_logger(__name__)


router = APIRouter(
        prefix="/api",
    tags=["auth"]
)

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


class LoginData(BaseModel):
    username: str
    password: str
    autoLogin: Optional[bool] = False
    type: Optional[str] = None

def generate_auth_token(username: str) -> str:
    current_time = datetime.now().isoformat()
    return hashlib.md5(f"{username}{current_time}".encode()).hexdigest()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    try:
        return hashlib.md5(plain_password.encode('utf-8')).hexdigest() == hashed_password
    except Exception as e:
        logger.error(f"密码验证过程中发生错误: {str(e)}")
        return False
    

async def login(username: str, password: str, type: str = None):
    logger.info(f"收到登录请求，用户名: {username}, 类型: {type}")

    # 从数据库中查找用户
    user = await db["users"].find_one({"phone": username})

    if not user:
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "status": 'error',
                "type": type,
                "currentAuthority": 'guest',
                "message": "用户不存在"
            }
        )

    # 验证密码
    if not verify_password(password, user["password"]):
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "status": 'error',
                "type": type,
                "currentAuthority": 'guest',
                "message": "密码错误"
            }
        )
    # 生成新的 auth_token
    auth_token = generate_auth_token(username)
    
    # 使用 settings 中的配置
    token_expiry = datetime.now() + timedelta(seconds=settings.ACCESS_TOKEN_EXPIRE_SECONDS)

    # 更新用户的 auth_token 和 token_expiry
    await db["users"].update_one(
        {"phone": username},
        {"$set": {"auth_token": auth_token, "token_expiry": token_expiry}}
    )

    # 登录成功
    return JSONResponse(content={
        "status": 'ok',
        "type": type,
        "currentAuthority": user["role_id"],  # 假设用户角色存储在 'role' 字段中
        "message": "登录成功",
        "auth_token": auth_token,
        "token_expiry": token_expiry.isoformat()
    })

async def get_role_access(role_id: int) -> dict:
    # 这里应该是实际的数据库查询或API调用
    # 返回一个包含权限信息的字典
    return {
        "canAccessAdmin": True,
        "canAccessUserManagement": False,
        # 其他权限...
    }


@router.post("/login/account")
async def login_account(login_data: LoginData):
    return await login(username=login_data.username, password=login_data.password, type=login_data.type)

@router.post("/login/outLogin")
async def logout():
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "success": True,
            "data": {},
            "errorCode": "0",
            "errorMessage": "",
            "showType": 0
        }
    )

@router.get("/currentUser")
async def get_current_user(current_user: dict = Depends(verify_token)):
    role = await db["roles"].find_one({"id": current_user["role_id"]})

    return JSONResponse(
        content={
            "success": True,
            "data": {
                "name": current_user["name"],
                "avatar": current_user["avatar"],
                "id": current_user["id"],
                "signature": current_user["signature"],
                "title": current_user["title"],
                "group_id": current_user["group_id"],
                "group_name": current_user["group_name"],
                "tags": current_user["tags"],
                "notifyCount": current_user["notifyCount"],
                "unreadCount": current_user["unreadCount"],
                "country": current_user["country"],
                "role_name": current_user["role_name"],
                # "geographic": current_user["geographic"],
                "address": current_user["address"],
                "phone": current_user["phone"],
                "role_id": current_user["role_id"],
                "access":role["access"] if role else None,
            }
        }
    )

@router.get("/notices")
async def get_notices(current_user: dict = Depends(verify_token)):
    # 这里可以根据当前用户获取相应的通知数据
    return []

class MenuDataItem(BaseModel):
    path: str
    name: str
    icon: Optional[str] = None
    component: Optional[str] = None
    routes: Optional[List['MenuDataItem']] = None

MenuDataItem.update_forward_refs()

@router.get("/getMenuData")
async def get_menu_data() -> List[MenuDataItem]:
    # 这里应该返回菜单数据，但为了简化，我们返回一个基本的菜单结构
    return [
        MenuDataItem(
            path="/home",
            name="home",
            icon="smile",
            component="./home"
        ),
        MenuDataItem(
            path="/admin",
            name="admin",
            icon="crown",
            routes=[
                MenuDataItem(
                    path="/admin/sub-page",
                    name="sub-page",
                    component="./home"
                ),
            ]
        ),
    ]

# # 刷新令牌接口
# @router.post("/refresh-token")
# async def refresh_token(current_user: dict = Depends(verify_token)):
#     # 假设令牌有效期为 15 分钟
#     new_expiration = datetime.utcnow() + timedelta(minutes=15)
#     new_token = create_access_token(data={"sub": current_user["id"]}, expires_delta=new_expiration)
#     return {"token": new_token}
