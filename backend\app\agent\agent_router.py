import json
import traceback
from collections.abc import AsyncGenerator
from datetime import datetime
from typing import Any, Dict, List
import uuid

from bson.objectid import ObjectId
from dotenv import load_dotenv
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import StreamingResponse
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from langchain_core.messages import AIMessage, AnyMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command
# from langsmith import Client as LangsmithClient
from pydantic import BaseModel
from .agents import CHAT_AGENT, DEFAULT_AGENT, get_agent,get_agent_output_filter
from .utils import convert_message_content_to_string, remove_tool_calls

from ..db.mongodb import db
# from ..schema import StreamInput
from ..utils.logging_config import get_logger, setup_logging

setup_logging()
logger = get_logger(__name__)



async def robot_message_generator(
    system_app_params: Dict[str, Any],
    messages: List[Dict[str, Any]],
    stream:bool
) -> AsyncGenerator[str, None]:
    """
    Generate a stream of messages from the agent in OpenAI-compatible format.

    Args:
        kwargs: Dictionary containing input messages and configuration
        id: 会话ID，用于标识此次对话

    Returns:
        AsyncGenerator yielding SSE formatted messages
    """

    agent_type = system_app_params.get('AGENT_TYPE',None)
    llm_id = system_app_params.get('MODEL_ID', None)
    
    llm = await db["llms"].find_one({"id": int(llm_id)})
    logger.info(llm)
    model_name = llm.get('m_name', None)

    # # logger.info(f"kwargs: {kwargs}")

    # # 用于收集所有生成的内容
    all_generated_content = []
    processed_messages = []
    id = str(uuid.uuid4())  # 将UUID对象转换为字符串
    created_timestamp = int(datetime.now().timestamp())
    message_counter = 1

    # if current_agent is None:
    #     current_agent =default_agent

    if agent_type is None:
    # 如果没有设置current_character，返回提示信息
        created_timestamp = int(datetime.now().timestamp())
        content = "智能体类型不存在，请联系管理员"
        error_chunk = {
            "status": "error",
            "message": content
        }
        yield f"event: error\ndata: {json.dumps(error_chunk)}\n\n"
        processed_messages.append(content)
    else:
        logger.info(f"启用智能体: {agent_type}")
    
        logger.info(f"========================启用智能体: {agent_type}")
        agent: CompiledStateGraph = get_agent(agent_type)
        # agent_type=current_agent['agent_type']
        output_filter=get_agent_output_filter(agent_type)

        # # 根据role区分消息类型

        for msg in messages:
            if isinstance(msg, dict) and "role" in msg and "content" in msg:
                # 处理OpenAI格式的消息
                role = msg["role"]
                content = msg["content"]
                if role == "user":
                    processed_messages.append(HumanMessage(content=content))
                elif role == "assistant":
                    processed_messages.append(AIMessage(content=content))
                # 可以添加更多角色的处理，如system等
            else:
                # 处理已经是LangChain消息对象的情况
                processed_messages.append(msg)
        # 打印信息
        messages_list = [{
            'role': msg.type,
            'content': msg.content
        } for msg in processed_messages]
        logger.info("已处理的消息列表:")
        for idx, msg in enumerate(messages_list, 1):
            logger.info(f"  消息 {idx} ：角色: {msg['role']}")
            logger.info(f"  内容: {msg['content']}")
            logger.info(f"----------------------------")
        logger.info(f"===============================================")

        # # 创建一个唯一的会话ID


        # # 消息计数器，用于生成唯一的消息ID


        # 发送角色信息作为第一个块
        first_chunk = {
            "id": id,
            "created": created_timestamp,
            "model": model_name,
            "choices": [
                {
                    "index": 0,
                    "delta": {"role": "assistant", "content": ""},
                    "finish_reason": None
                }
            ]
        }
        logger.info(f"first_chunk: {first_chunk}")
        yield f"id: test_id_{message_counter}\ndata: {json.dumps(first_chunk)}\n\n"
        message_counter += 1

        # # 跟踪是否已经发送了任何内容
        # has_sent_content = False

        # # 准备运行参数
        thread_id = str(uuid.uuid4())

        # # 根据不同的智能体类型设置不同的运行参数
        # run_id = str(uuid4())
        # logger.info(f"agent_type: {agent_type}")

    
        if agent_type == CHAT_AGENT:
            logger.info(f"=================CHAT_AGENT=========================")
            # 对于默认的聊天机器人，使用标准参数
            run_kwargs = {
                "input": {"messages": processed_messages},
                "config": RunnableConfig(
                    configurable={
                        "system_app_params": system_app_params,
                        "llm_id":int(llm_id),
                        "thread_id": thread_id  # 确保传递thread_id
                    },
                    run_id=id
                ),
            }
        else:
            logger.info(f"=================DEFAULT_AGENT=========================")
            # 对于默认的聊天机器人，使用标准参数
            run_kwargs = {
                "input": {"messages": processed_messages},
                "config": RunnableConfig(
                    configurable={
                        "system_app_params": system_app_params,
                        "llm_id":int(llm_id),
                        "thread_id": thread_id  # 确保传递thread_id
                    },
                    run_id=id
                ),
            }
        # logger.info(f"===========> run_kwargs: {run_kwargs}")

        # 跟踪已处理的流ID

        async for event in agent.astream_events(**run_kwargs, version="v2"):

            if not event:
                continue
            created_timestamp = int(datetime.now().timestamp())

            logger.info(f"============》event: {event}")

            if (
                event["event"] == "on_chat_model_stream"
                and stream
            ):
                if output_filter:
                    if "on_chat_model_stream" in output_filter:
                        tags_list =  output_filter.get("on_chat_model_stream",None)
                        if tags_list:
                            langgraph_node = event["metadata"].get("langgraph_node",None)
                            # logger.info(f"过滤节点:{langgraph_node} in {tags_list}")
                            if langgraph_node not in tags_list:
                                continue;
                logger.info("==========#############################>on_chat_model_stream")
                # 从事件中提取内容
                content = None
                if "data" in event and "chunk" in event["data"]:
                    chunk_data = event["data"]["chunk"]
                    if hasattr(chunk_data, "content"):
                        content = chunk_data.content
                
                if content:
                    chunk = {
                        "id": id,
                        "created": created_timestamp,
                        "model": model_name,
                        "choices": [
                            {
                                "index": 0,
                                "delta": {"content": convert_message_content_to_string(content)},
                                "finish_reason": None
                            }
                        ]
                    }
                    # 添加内容到收集器
                    all_generated_content.append(convert_message_content_to_string(content))

                    logger.info(f"id: test_id_{message_counter}\ndata: {json.dumps(chunk)}\n\n")
                    yield f"id: test_id_{message_counter}\ndata: {json.dumps(chunk)}\n\n"
                    message_counter += 1
                continue


            # 处理 on_chain_stream 事件
            if event["event"] == "on_chain_stream":
                # logger.info("==========#############################>on_chain_stream")
                processed_content = None

                # 增加整个事件的调试输出，仅在调试时启用
                # logger.debug(f"on_chain_stream原始事件: {json.dumps(event, default=str)}")

                if output_filter:
                    if "on_chain_stream" in output_filter:
                        tags_map = output_filter.get("on_chain_stream", None)
                        metadata = event.get("metadata", None)

                        if metadata and tags_map:
                            langgraph_node = metadata.get("langgraph_node", None)

                            if langgraph_node:
                                # logger.info(f"------------langgraph_node-------->{langgraph_node}")
                                out_key = tags_map.get(langgraph_node, None)
                                # logger.info(f"------------out_key-------->{out_key}")

                                # 获取data和chunk
                                data = event.get("data", {})
                                chunk = data.get("chunk", {}) if data else {}

                                if not out_key:
                                    logger.info(f"过滤节点:{langgraph_node}没有匹配的输出键")
                                    continue

                                # 直接查找next_question，不管out_key是什么
                                if "next_question" in chunk:
                                    logger.info(f"找到next_question: {chunk['next_question']}")
                                    processed_content = chunk["next_question"]
                                # 如果没有next_question但out_key存在，则尝试从指定out_key获取
                                elif out_key in chunk:
                                    logger.info(f"找到{out_key}: {chunk[out_key]}")
                                    processed_content = chunk[out_key]
                                # 最后尝试从agent_params中获取
                                elif "agent_params" in chunk and out_key in chunk["agent_params"]:
                                    logger.info(f"在agent_params中找到{out_key}: {chunk['agent_params'][out_key]}")
                                    processed_content = chunk["agent_params"][out_key]
                                else:
                                    logger.warning(f"未找到输出内容，langgraph_node={langgraph_node}, out_key={out_key}")
                                    logger.debug(f"chunk内容: {json.dumps(chunk, default=str)[:200]}...")

                # 如果内容不为空，则发送给客户端
                if processed_content:
                    chunk = {
                        "id": id,
                        "created": created_timestamp,
                        "model": model_name,
                        "choices": [
                            {
                                "index": 0,
                                "delta": {"content": processed_content},
                                "finish_reason": None
                            }
                        ]
                    }
                    # 添加内容到收集器
                    all_generated_content.append(processed_content)

                    logger.info('=========================发送流式输出')
                    logger.info(f"id: test_id_{message_counter}\ndata: {json.dumps(chunk)}\n\n")
                    yield f"id: test_id_{message_counter}\ndata: {json.dumps(chunk)}\n\n"
                    message_counter += 1
                continue


    # 发送结束标记
    final_chunk = {
        "id": id,
        "created": created_timestamp,
        "model": model_name,
        "choices": [
            {
                "index": 0,
                "delta": {},
                "finish_reason": "stop"
            }
        ]
    }
    yield f"id: test_id_{message_counter}\ndata: {json.dumps(final_chunk)}\n\n"

    # 打印完整的生成内容并保存到数据库
    if all_generated_content:
        complete_content = ''.join(all_generated_content)
        logger.info("==================完整内容==================")
        logger.info(complete_content)
        logger.info("==================完整内容结束==================")

        # 保存到数据库

    logger.info("################结束#############")