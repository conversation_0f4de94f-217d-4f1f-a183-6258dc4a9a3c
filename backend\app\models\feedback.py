from mongoengine import Document, StringField, DateTimeField, IntField, ObjectIdField, ListField, Dict<PERSON>ield
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

# MongoDB 文档类
class Feedback(Document):
    message_id = StringField(required=True)
    content = StringField(required=True)
    feedback_types = ListField(StringField(), required=True)
    user_id = StringField(required=False)
    conversation_id = StringField(required=False)
    app_info = StringField(required=False)
    user_name = StringField(required=False)
    created_at = DateTimeField(default=datetime.now)
    
    meta = {
        'collection': 'feedbacks',
        'indexes': ['message_id', 'user_id', 'conversation_id']
    }

class FeedbackCreate(BaseModel):
    message_id: str
    content: str
    feedback_types: List[str]
    conversation_id: Optional[str] = None
    app_info: Optional[str] = None

class FeedbackResponse(FeedbackCreate):
    id: str 


class FeedbackUpdate(FeedbackCreate):
    id: str 
    content: str
    feedback_types: List[str]
