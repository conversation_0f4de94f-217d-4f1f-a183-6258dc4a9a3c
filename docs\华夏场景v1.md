
# 贷前场景：客户评估(辅助客户评级与早期风险识别)

## 目的： 基于外部公开数据，对公客户及其关联方进行舆情监测与分析，形成客户舆情画像，识别潜在声誉风险、经营风险及合规风险信号，为客户准入评估、评级和早期风险预警提供支持。
# 输入：
企业名称： 由统一社会信用代码）作为唯一标识
关联方信息（可选）： 如实际控制人、主要股东、核心高管、重要子公司、主要担保方等。（移动金科数据）
监控时间范围： 例如，近1年、近3年或自定义。
关注的舆情主题/关键词（可选）： 如特定行业风险、特定负面事件类型（如债务违约、环保处罚、劳资纠纷、产品质量问题、诉讼败诉等）。
（可选）行内已知客户风险信息： 用于交叉验证或补充分析。

### 数据处理 / Agent过程：
数据接入==>形成时序图谱
接入主流新闻门户、财经专业媒体、政府部门公开信息网站、主流商业论坛、行业垂直网站、问答社区等。
投诉信息等
信息提取Agent：
LLM进行命名实体识别、处理企业名称的多样性（简称、曾用名、俗称等）。
LLM从相关信息中抽取关键“事件”（如融资、并购、重大合同、高管变动、法律诉讼、行政处罚、经营异常、产品事故、正面荣誉等）。
LLM将抽取的事件按照预设的风险/机会维度进行分类（如：信用风险、经营风险、法律合规风险、市场机会、技术创新等）。
观点挖掘与情感分析Agent：
LLM识别文本中针对企业或相关事件的明确“评价性观点”；
作者/网民的“情感倾向”（正面、负面、中性）；

司法与行政处罚信息专项提取与结构化Agent：
LLM针对司法文书数据库（如裁判文书网）、失信被执行人名单、行政处罚公示网站，定向抽取与企业相关的诉讼案件（案由、涉案金额、判决结果、执行情况）、失信记录、行政处罚（事由、处罚内容、处罚机关、日期）等信息，并进行结构化处理。
风险信号识别与预警Agent：
LM根据预设维度，基于训练模型进行异常检测，识别潜在的重大风险信号。
LLM对风险信号进行分级（如高、中、低风险）。
舆情画像生成Agent：
LLM整合所有分析结果，生成包含核心观点、情感分布、主要事件时间轴、风险点汇总、司法行政处罚记录的客户舆情画像。

### 输出：客户舆情分析报告（评分）：
1. 综合评分：基于分析结果，为客户打上初步的声誉风险标签（如高、中、低、或者十分制）
2. 核心摘要： 舆情声誉综合评价、主要正面/负面信息概览、关键风险提示。
3. 问题清单： 明确的负面事件、司法案件（含案号、状态）、行政处罚记录、经营异常信息、媒体负面报道等。
4. 观点与情感分布： 各类信息来源的观点汇总、情感倾向（正面/负面/中性）统计图表及其随时间变化趋势。
5. 关键事件追踪： 对重大影响事件（如重大诉讼、监管调查、核心产品危机）的时序脉络。
6. 关联方风险传导分析： （如输入关联方）分析关联方的负面舆情对目标客户可能产生的影响。


# 场景：尽职调查报告生成
## 目标： 基于多模态LLM及Agent技术，对客户经理在尽职调查初期收集的各类信贷申请文件材料进行关键信息的自动化、智能化提取与结构化处理，并执行初步的交叉核验与规则校验，提升数据录入效率、标准化程度和早期审核的准确性与覆盖面。
## 技术： 多模态LLM、Agent自动化流程。
## 输入：
访谈材料：PDF扫描件、照片、Excel文件。
经营证明类： 公司章程、验资报告、主要经营场所权属/租赁证明、重要资质许可、重大合同（购销、工程等。
抵质押物资料类（如适用）： 抵质押物清单、权属证明（房产证、土地证、车辆登记证、股权证、存单等）。
申请与调查类： 贷款申请书、客户经理访谈记录（录音转文本、手写/电子笔记）

## 数据处理 / Agent过程：
智能文档分拣与预处理Agent:
LLM自动识别上传文件的类型（如营业执照、财报-资产负债表、合同等）。
多模态关键信息提取Agent (针对不同文档类型调用不同LLM能力):
合同条款抽取： LLM从各类合同中抽取关键条款，如合同方、标的、金额、期限、利率、付款方式、担保条款、违约责任等。
印章/签名识别（可选）： 识别合同、文件上的印章信息（公司名称）和签名笔迹（需高精度模型）。
非结构化文本信息提取： LLM从访谈记录、公司介绍等文本中提取关键信息点。
数据结构化与标准化Agent:
LLM将提取的各类信息统一转换为预定义的结构化数据格式（如JSON，或直接映射到数据库字段）。
按照行内数据字典对字段名、数据类型、日期格式、金额单位等进行标准化处理。
信息交叉核验与规则校验Agent:
一致性核验： LLM对比不同文件来源的同一字段信息（如申请书上的公司名与营业执照上的公司名是否一致；合同中的借款人与营业执照法人是否关联）。
业务规则校验： LLM根据预设的业务规则进行判断（如注册资本是否满足某产品准入门槛、贷款期限是否超限等）。
结果汇总与差异报告生成Agent:
LLM汇总所有提取结果、核验状态（通过/不通过/待人工确认）、差异点、缺失项。
生成结构化的提取与核验报告，高亮显示风险点和需要人工关注的内容。

## 配置项：
待提取与核验的关键信息维度清单 (预设为Agent的任务指令/Prompt模板)： 例如公司全称、统一社会信用代码、合同主要条款、抵押物核心信息（名称、位置、面积、证号）等。
行内标准数据字典与校验规则库： 包括字段命名规范、数据类型、格式要求（如日期YYYY-MM-DD）、值域范围、逻辑校验规则（如资产负债表平衡校验、利润表勾稽关系）

## 输出：客户舆情分析报告（评分）：
结构化的信贷关键信息数据
信贷资料智能审核报告：
信息提取完整度清单： 列出已成功提取的信息项和未能识别/提取的缺失项。
交叉核验结果： 详细列出内部信息一致性检查的匹配项与不匹配项（附带源文件定位）。
规则校验结果： 列出通过校验的规则和触发异常的规则及具体内容。
风险点与待人工复核清单
初步数据可用性评估： 基于提取和核验结果，对当前资料的完整性、合规性和一致性给出一个初步的可用性判断，为后续尽调提供指引。



# 贷前场景：
## 3. 尽调报告生成
### 目的： 基于客户经理在尽调过程中收集和分析的各类信息，结合行内数据，辅助或自动生成标准化的尽调报告初稿，提高报告撰写效率和规范性。
### 输入：
1. 客户基本信息（行内）。
2. 对公客户舆情分析模块的输出（可选）。
3. 信贷资料识别模块的输出： 关键信息抽取结果、对比核验结果（可选）。
4. 其他：财务分析模块的输出，合规性审核模块的输出，抵押物审核模块的输出（可选）。
5. 客户经理输入的现场调研记录、访谈纪要、行业分析等非结构化文本信息（可选）。
6. 银行内部标准尽调报告模板和撰写指引。
### 数据处理 / Agent过程：
1. 信息整合Agent： 根据模版进行数据调用整合（api、mcp）。
2. 内容提取与组织Agent： 根据尽调报告模板的各个章节要求 (如：公司概况、行业分析、管理层分析、财务状况分析、经营风险分析、担保分析、授信建议等)，从整合信息中提取相关内容。
3. 文本生成与润色Agent： 使用LLM的文本生成能力，将提取的内容组织成连贯的段落，填充到报告模板的相应章节，并进行初步的语言润色。
4. 风险归纳与总结Agent： LLM对各模块识别的风险点（例如：舆情风险、财务风险、合规风险、抵押物风险等）进行归纳总结，形成核心风险提示章节。
初步结论与建议生成Agent： （可选，较高级）LLM根据综合信息，尝试生成初步的授信结论和建议的框架，供客户经理参考和修改。
### 输出：
1. 标准化的尽调报告初稿 (例如Word或PDF格式)。
2. 报告中各章节内容的自动填充。
3. 关键风险点汇总列表。
4. 待客户经理人工确认、补充或修改的信息点提示。
   
## 4财务分析 (辅助) + 结合数势科技的内容调整
### 目的： 利用LLM对企业提供的财务报表进行自动化的指标计算、趋势、归因分析和初步风险识别，辅助客户经理快速把握企业财务状况。
### 输入：
1. 企业财务报表数据。
2. 历史同期财务报表 (用于趋势分析)。
3. 预设的财务分析维度和关注的关键指标清单 (如偿债能力、盈利能力、营运能力、成长能力、现金流状况)。
4. 行业对比数据
### 数据处理：
1. 关键财务指标计算： LLM根据预设公式，自动计算关键财务指标，如流动比率、速动比率、资产负债率、销售利润率、净资产收益率(ROE)、总资产报酬率(ROA)、应收账款周转率、存货周转率等。
2. 趋势分析： LLM对比分析连续几期财报数据和指标的变化趋势，识别显著上升或下降的项目。
3. 对比分析： （如提供行业数据）LLM将企业财务指标与行业平均水平或预设基准进行对比，找出差异。
4. 归因分析
5. 初步风险点识别与解释： LLM根据预设规则或从大量财报数据中学习到的模式，高亮异常指标或潜在财务风险点 (例如，偿债能力指标持续恶化、盈利能力大幅下滑、现金流紧张等)，并尝试生成简要的文字解释。
### 输出：
1. 计算完成的关键财务指标清单及其数值。
2. 财务指标的趋势分析图表和文字描述。
3. 与行业/基准的对比分析结果。
4. 识别出的潜在财务风险点提示清单及初步解释。
5. 财务状况初步分析摘要报告。
   
## 5.合规性审核 (辅助)
### 目的： 利用LLM辅助审核客户提交的信贷申请材料及其包含的内容是否符合国家法律法规、监管政策及银行内部的准入和合规要求，初步识别潜在的合规风险。
## 输入：
1. 客户提交的申请材料包
2. 行内/监管的合规知识库/规则库
3. 预设的合规检查点/敏感词/风险模式清单： 例如，涉及“高利贷”、“虚拟货币交易”、“非法集资”、“套取信贷资金”、“规避限购政策”等内容的描述。
## 数据处理 (LLM核心能力)：
1. 关键信息提取与语义理解：根据检查点要求，理解内容并提取信息；LLM对这些文本内容进行深层语义理解，把握其真实意图和潜在含义。
2. 合规规则匹配与风险识别
3. 敏感内容扫描： LLM根据预设的敏感词/风险模式清单，在所有提交的文本材料中进行扫描，识别潜在的违规表述或高风险信号。
4. 风险点标记与依据引用：LLM对识别出的潜在不合规点或高风险内容进行标记。找到相关的法规条款或内部规定作为判断依据，并进行引用。
## 输出：
1. 材料内容合规性初步审核报告合规风险点清单： 
2. 列出识别出的每一个潜在不合规之处或高风险内容，并注明其在哪个文件的哪个部分。
3. 置信度/严重性初判（可选）： LLM对识别出的风险点给出初步的置信度或风险严重性评估。



## 6. 抵押物审核 (辅助) 移动研究院
### 目的： 强化学习验证模型，对抵押物进行验真评估
### 输入：
1. 抵押物清单及描述。
2. 抵押物权属证明文件：如房产证、土地证、车辆登记证、设备购买发票等（通常为扫描件或图片）。
3. 抵押物评估报告（如有，通常为PDF）。
4. 抵押物照片、视频（多模态信息）。
5. 其他辅助餐料

### 数据处理：
1. 权属信息提取与核验： 使用多模态LLM从权属证明文件中提取关键信息（如所有权人、证件号码、地址、面积、规格型号等），并与客户信息、申请信息进行交叉核验。
2. 基于验真模型进行评估
3. （可选）初步价值参考： LLM结合抵押物描述、照片和外部市场价格数据源，给出抵押物价值的初步参考区间或相似案例。
4. 风险点识别： LLM识别权属不清、资料不全、评估价值与市场认知偏差过大、存在法律瑕疵等潜在风险。
### 输出：
1. 抵押物资料审核清单及状态（完整/缺失/异常）。
2. 识别出的抵押物相关风险点提示。
3. 抵押物价值初步参考信息（如适用）。


# 贷中场景：
## 7.风险综合评估 (辅助)
### 目的： 整合贷前尽职调查各环节的分析结果（尤其是尽调报告的核心内容），时序研判宏观数据，再整合内部内部风控指标， 利用LLM辅助风控审核人员进行更全面、一致的风险判断和量化评估，支持授信审批决策。
### 输入：
1. 完整的尽调报告： 包含客户基本情况、财务分析、经营分析、合规分析、舆情分析、抵押物评估、授信方案等所有内容。
2. 银行内部的风险评估模型评估结果。
3. 时序研判宏观风控数据
4. 风控政策和审批权限指引。
5. 历史信贷数据和违约案例特征（用于模型优化或规则参考）。
### 数据处理 / Agent过程：
1. 核心风险因子提取Agent： LLM从尽调报告的各个章节（特别是风险分析部分）精准提取定性和定量的核心风险描述和关键数据。
2. 风险量化与评级Agent：
   - 对于可量化的风险（如财务指标），LLM将其输入风险评分卡或模型进行计算。
   - 对于定性风险（如管理层风险、行业风险），LLM可辅助将其映射到预设的风险等级，或根据文本描述的严重程度进行初步的量化转换。
   - 时序研判宏观风控数据：调用时序图谱，获取宏观风险数据
3. 风险权重与聚合Agent： 根据银行风险偏好和不同风险因子的重要性（预设权重），LLM辅助聚合各风险评分，形成客户的综合风险评分或初步风险等级。
4. 风险情景分析辅助Agent（高级）： （可选）LLM可根据风控人员输入的特定情景假设（如“若销售额下降20%”），分析其对关键财务指标和偿债能力的影响，辅助进行压力测试。
5. 评估意见生成辅助Agent： LLM根据综合评估结果，生成风险评估意见的初步草稿，包括主要风险点、已采取的缓释措施、建议的审批条件或否决理由的初步表述。
### 输出：
1. 客户综合风险评分/初步风险等级。
2. 主要风险点清单及其对综合评估结果的贡献度分析。
3. （可选）压力测试结果摘要。
4. 风险评估报告或审批意见的初步草稿/关键点。


