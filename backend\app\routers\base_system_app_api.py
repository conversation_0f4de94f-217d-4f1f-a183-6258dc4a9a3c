from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
import asyncio  # 导入 asyncio 模块
from ..models.chat import ChatResponse
from ..db.mongodb import db
from ..utils.auth import verify_token
from ..models.system_app_setting import SystemAppSettingModel
from ..utils.llmClient import stream_model_api, app_model_api
from typing import Dict, Any
from ..models.message import Message  # 使用 MongoEngine 模型
from datetime import datetime
from bson import ObjectId
from app.utils.logging_config import setup_logging, get_logger
import traceback
setup_logging()
logger = get_logger(__name__)

router = APIRouter(
    prefix="/api",
    tags=["system_app_completions"]
)


# 创建新对话
@router.post("/app/chat/system_app_completions", response_model=Dict[str, Any])
async def chat_system_app_completions(conversation: ChatResponse, current_user: dict = Depends(verify_token)):
    # print(conversation)
    logger.info( conversation)
    system_app_info: SystemAppSettingModel = await db["system_app_settings"].find_one({"app_info": conversation.app_info})
    if not system_app_info:
        logger.error(f"没有找到应用信息: {conversation.app_info}")
        raise HTTPException(status_code=404, detail="没有找到应用信息")
    # 提取最后一条消息
    last_message = conversation.messages[-1]


    new_user_message = Message(
        _id=str(ObjectId()),
        conversation_id=conversation.conversation_id,
        message_id=last_message['id'],
        meta_data=last_message.get('meta', {}),
        extra=last_message.get('extra', {}),
        user_id=conversation.user_id,
        user_name=conversation.user_name,
        role=last_message['role'],
        content=last_message['content'],
        created_at=datetime.now(),
        app_info=conversation.app_info,
        token_count=0,  # 这里可以添加计算token的逻辑
        price=0.0  # 这里可以添加计算价格的逻辑
    )


    # 将 MongoEngine 实例转换为字典
    new_user_message_dict = new_user_message.to_mongo().to_dict()
    await db["messages"].insert_one(new_user_message_dict)

    async def stream_response():
        ai_message_content = ""
        async for chunk in stream_model_api(
            chat_id=conversation.conversation_id,
            user_id=conversation.user_id,
            messages=conversation.messages,
            system_app_info=system_app_info
        ):
            ai_message_content += chunk
            yield chunk
            await asyncio.sleep(0)

        # 添加 end 事件类型
        yield "event: end\ndata: Stream has ended\n\n"

    return StreamingResponse(stream_response(), media_type='text/event-stream')



# 非流式
@router.post("/app/chat/system_app_completions_ns", response_model=Dict[str, Any])
async def system_app_completions_ns(conversation: ChatResponse, current_user: dict = Depends(verify_token)):
    logger.info(conversation)
    system_app_info: SystemAppSettingModel = await db["system_app_settings"].find_one({"app_info": conversation.app_info})
    if not system_app_info:
        logger.error(f"没有找到应用信息: {conversation.app_info}")
        raise HTTPException(status_code=404, detail="没有找到应用信息")
    
    # 提取最后一条消息
    last_message = conversation.messages[-1]

    # 保存用户消息
    new_id = str(ObjectId())
    new_user_message = Message(
        _id=new_id,
        conversation_id=conversation.conversation_id or new_id,
        message_id=last_message['id'],
        meta_data=last_message.get('meta', {}),
        extra=last_message.get('extra', {}),
        user_id=conversation.user_id,
        user_name=conversation.user_name,
        role=last_message['role'],
        content=last_message['content'],
        created_at=datetime.now(),
        app_info=conversation.app_info,
        token_count=0,  # 这里可以添加计算token的逻辑
        price=0.0  # 这里可以添加计算价格的逻辑
    )

    # 将 MongoEngine 实例转换为字典并保存
    new_user_message_dict = new_user_message.to_mongo().to_dict()
    await db["messages"].insert_one(new_user_message_dict)

    try:
        # 调用 app_model_api 获取回复
        response = await app_model_api(
            chat_id=conversation.conversation_id if conversation.conversation_id else None,
            user_id=conversation.user_id,
            messages=conversation.messages,
            system_app_info=system_app_info
        )

        # 如果返回了错误信息
        if "error" in response:
            return {"error": response["error"]}

        # 保存助手回复消息
        assistant_message = Message(
            _id=str(ObjectId()),
            conversation_id=conversation.conversation_id,
            message_id=str(ObjectId()),  # 生成新的消息ID
            meta_data={},
            extra={},
            user_id=conversation.user_id,
            user_name=conversation.user_name,
            role="assistant",
            content=response.get("choices", [{}])[0].get("message", {}).get("content", ""),
            created_at=datetime.now(),
            app_info=conversation.app_info,
            token_count=0,
            price=0.0
        )

        # 保存助手消息
        assistant_message_dict = assistant_message.to_mongo().to_dict()
        await db["messages"].insert_one(assistant_message_dict)

        return response

    except Exception as e:
        logger.error(f"处理请求时发生错误: {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

