#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
金融舆情分析智能体
定期从ES中获取微信公众号数据，进行分析并生成报告
基于STORM结构化推理方式改进报告生成过程
"""

import concurrent.futures
import functools
import json
import logging
import os
import platform
import signal
import threading
import traceback
import io
import uuid
from concurrent.futures import ThreadPoolExecutor
from contextlib import contextmanager
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, List, Optional, TypedDict, Tuple

import pandas as pd
from elasticsearch import Elasticsearch
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph

# 直接导入MongoDB和MinIO
import mongoengine
from mongoengine import Document, StringField, DateTimeField, ListField
from minio import Minio

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()  # 从当前目录的.env文件加载环境变量

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ES配置
TARGET_ES_HOST = os.getenv("ES_HOST", "**************")
TARGET_ES_PORT = int(os.getenv("ES_PORT", "9600"))
TARGET_ES_INDEX = os.getenv("ES_INDEX", "pro_mcp_data_weixin")

# 大模型参数
LLM_MODEL = os.getenv("LLM_MODEL", "Qwen/Qwen3-32B")
LLM_API_BASE = os.getenv("LLM_API_BASE", "https://api.siliconflow.cn/v1")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "sk-vkmolqivosbciwqhybrjqcpgopurxdmgmbkiliwznxdgunqn")

# MongoDB配置
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017/wiseAgent")

# MinIO配置
MINIO_ENDPOINT = os.getenv("MINIO_ENDPOINT", "localhost:9000")
MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY", "minioadmin")
MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY", "minioadmin")
MINIO_BUCKET = os.getenv("MINIO_BUCKET", "wiseagent")
MINIO_SECURE = os.getenv("MINIO_SECURE", "false").lower() == "true"

# 初始化MinIO客户端
try:
    minio_client = Minio(
        endpoint=MINIO_ENDPOINT,
        access_key=MINIO_ACCESS_KEY,
        secret_key=MINIO_SECRET_KEY,
        secure=MINIO_SECURE
    )

    # 确保MinIO存储桶存在
    if not minio_client.bucket_exists(MINIO_BUCKET):
        minio_client.make_bucket(MINIO_BUCKET)
        logger.info(f"MinIO存储桶 '{MINIO_BUCKET}' 创建成功")
    else:
        logger.info(f"MinIO存储桶 '{MINIO_BUCKET}' 已存在")
except Exception as e:
    logger.error(f"MinIO初始化失败: {str(e)}")
    traceback.print_exc()

# 连接MongoDB
try:
    mongoengine.connect(host=MONGO_URI)
    logger.info(f"MongoDB连接成功: {MONGO_URI.split('@')[-1] if '@' in MONGO_URI else MONGO_URI}")
except Exception as e:
    logger.error(f"MongoDB连接失败: {str(e)}")
    traceback.print_exc()

# 定义媒体洞察报告模型
class MediaInsightsReport(Document):
    title = StringField(required=True)
    content = StringField(required=True)
    storage_path = StringField(required=False)
    created_at = DateTimeField(default=datetime.now)
    report_type = StringField(required=True)
    start_time = DateTimeField(required=True)
    end_time = DateTimeField(required=True)
    media_ids = ListField(StringField())

# 公众号ID列表
BIZ_LIST = [
    "MzA4MDY1NTUyMg==",
    "MzA5OTY5MzM4Ng==",
    "Mzg2Mjg1NTg3NA==",
    "Mzg4NTEwMzA5NQ==",
    "Mzg5MjU4MDkyMw==",
    "MzI4NTU0NDE4Mw==",
    "MzI5MzQxOTI0MQ==",
    "MzIwMDI3NjM2Mg==",
    "MzIzMjc3NTYyNQ==",
    "Mzk0NjUwMDIxOQ==",
    "MzkxMDYyNzExMA==",
    "MzkzNTYzMDYxMg==",
    "MzU3MDMwODc2MA==",
    "MzU4NzcwNDcxOA==",
    "MzU4ODM4NzI5Nw==",
    "MzU5MzkzMTY3Mg==",
    "MzUxMDk5NDgwNQ==",
    "MzUxMDkyMzA4Mw==",
    "MzUzMzEyODIyMA==",
    "MzUzNDcxMDgzNg==",
    "MzA3MTIzNDcwMg==",
    "MzA3NjU1NTQwMA==",
    "MzA4MzY2ODYwMw==",
    "MzAwNzMxODYyNg==",
    "Mzg4NDU2MDM3Mw==",
    "MzI1NzAwODc3Nw==",
    "MzU3MDMwODc2MA==",
    "MzU3NTYyNTIyMQ==",
    "MzUzMzYwODI2MA=="
]

# 自定义超时异常
class TimeoutException(Exception):
    pass

# 跨平台的超时函数装饰器
def timeout(seconds):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 记录开始时间
            start_time = datetime.now()
            func_name = func.__name__
            logger.info(f"开始执行函数 {func_name} 并设置 {seconds} 秒超时...")

            # 使用线程池执行函数
            with ThreadPoolExecutor(max_workers=1) as executor:
                # 提交任务到线程池
                future = executor.submit(func, *args, **kwargs)
                try:
                    # 等待任务完成，超时则抛出异常
                    result = future.result(timeout=seconds)
                    # 记录执行时间
                    elapsed = (datetime.now() - start_time).total_seconds()
                    logger.info(f"函数 {func_name} 成功执行完毕，耗时 {elapsed:.2f} 秒")
                    return result
                except concurrent.futures.TimeoutError:
                    # 超时后取消任务（注意：这只是设置取消标志，线程会继续执行）
                    future.cancel()
                    elapsed = (datetime.now() - start_time).total_seconds()
                    logger.error(f"函数 {func_name} 执行超时! 已运行 {elapsed:.2f} 秒，超过了设定的 {seconds} 秒限制")
                    raise TimeoutException(f"操作超时（{seconds}秒）- 函数 {func_name} 执行时间过长")
        return wrapper
    return decorator

# 检测是否为Unix平台（支持SIGALRM）
IS_UNIX = platform.system() in ('Linux', 'Darwin') and os.name == 'posix'

if IS_UNIX:
    # 在Unix平台上使用signal.SIGALRM实现超时
    @contextmanager
    def timeout_context(seconds):
        def handle_timeout(signum, frame):
            raise TimeoutException(f"操作超时（{seconds}秒）")

        # 设置信号处理器
        original_handler = signal.getsignal(signal.SIGALRM)
        signal.signal(signal.SIGALRM, handle_timeout)

        try:
            # 设置定时器
            signal.alarm(seconds)
            yield
        finally:
            # 取消定时器
            signal.alarm(0)
            # 恢复原始信号处理器
            signal.signal(signal.SIGALRM, original_handler)
else:
    # 在非Unix平台上使用线程池实现超时
    @contextmanager
    def timeout_context(seconds):
        def do_nothing():
            pass  # 占位函数，用于超时测试

        # 使用线程池和Future
        with ThreadPoolExecutor(max_workers=1) as executor:
            # 提交一个简单的任务，仅用于测试超时
            future = executor.submit(do_nothing)

            try:
                # 尝试等待任务完成（但我们实际上只是在测试超时机制）
                future.result(timeout=0.001)  # 确保这会立即返回
                # 现在让主任务运行
                yield
                # 注意：我们不能在这里检查是否超时，因为yield可能会长时间运行
            except concurrent.futures.TimeoutError:
                # 这不应该发生，因为do_nothing应该立即返回
                logger.warning("初始化超时上下文出错")
                yield

        # 超时上下文结束后，原任务应该已经完成或被取消

# 在 ANALYSIS_CONFIG 中添加发布时间过滤配置
ANALYSIS_CONFIG = {
    "max_days": 3,  # 最大获取天数
    "min_word_count": 500,  # 最小文章字数
    "max_retries": 3,  # 分析失败重试次数
    "timeout": {
        "single_analysis": 120,  # 单篇分析超时时间
        "overall_analysis": 180,  # 综合分析超时时间
    },
    "event_filter": {
        "max_event_age_days": 7,  # 事件最大年龄（天）
        "min_event_age_days": 0,  # 事件最小年龄（天）
        "exclude_future_events": True  # 是否排除未来事件
    },
    "publish_filter": {
        "max_publish_age_days": 7,  # 报告最大发布年龄（天）
        "min_publish_age_days": 0,  # 报告最小发布年龄（天）
        "exclude_future_publish": True  # 是否排除未来发布日期
    }
}

# 初始化大模型
try:
    llm = ChatOpenAI(
        temperature=0,
        model=LLM_MODEL,
        openai_api_base=LLM_API_BASE,
        api_key=OPENAI_API_KEY,
        request_timeout=120  # 设置120秒的请求超时时间
    )
    logger.info(f"大模型 {LLM_MODEL} 初始化成功")
except Exception as e:
    logger.error(f"大模型初始化失败: {str(e)}")
    traceback.print_exc()

# 在脚本开始时输出配置信息
logger.info("==================== 配置信息 ====================")
logger.info(f"运行环境: {platform.platform()}")
logger.info(f"Python版本: {platform.python_version()}")

# ES配置信息
logger.info("\n=== ElasticSearch配置 ===")
logger.info(f"ES主机: {TARGET_ES_HOST}")
logger.info(f"ES端口: {TARGET_ES_PORT}")
logger.info(f"ES索引: {TARGET_ES_INDEX}")

# LLM配置信息
logger.info("\n=== LLM配置 ===")
logger.info(f"模型名称: {LLM_MODEL}")
logger.info(f"API基础URL: {LLM_API_BASE}")
logger.info("API密钥: ******" + OPENAI_API_KEY[-8:] if len(OPENAI_API_KEY) > 8 else "*****")  # 只显示最后8位

# MinIO配置信息
logger.info("\n=== MinIO配置 ===")
logger.info(f"MinIO端点: {MINIO_ENDPOINT}")
logger.info(f"MinIO访问密钥: ******{MINIO_ACCESS_KEY[-4:] if len(MINIO_ACCESS_KEY) > 4 else '****'}")  # 只显示最后4位
logger.info(f"MinIO存储桶: {MINIO_BUCKET}")
logger.info(f"MinIO安全连接: {MINIO_SECURE}")

# MongoDB配置信息
logger.info("\n=== MongoDB配置 ===")
mongo_display = MONGO_URI.split('@')[-1] if '@' in MONGO_URI else MONGO_URI.split('//')[1]
logger.info(f"MongoDB URI: {mongo_display}")  # 只显示主机部分

# 分析配置信息
logger.info("\n=== 分析配置 ===")
logger.info(f"最大获取天数: {ANALYSIS_CONFIG['max_days']}")
logger.info(f"最小文章字数: {ANALYSIS_CONFIG['min_word_count']}")
logger.info(f"分析失败重试次数: {ANALYSIS_CONFIG['max_retries']}")
logger.info(f"单篇分析超时时间: {ANALYSIS_CONFIG['timeout']['single_analysis']}秒")
logger.info(f"综合分析超时时间: {ANALYSIS_CONFIG['timeout']['overall_analysis']}秒")
logger.info("事件过滤配置:")
logger.info(f"  - 最大事件年龄: {ANALYSIS_CONFIG['event_filter']['max_event_age_days']}天")
logger.info(f"  - 最小事件年龄: {ANALYSIS_CONFIG['event_filter']['min_event_age_days']}天")
logger.info(f"  - 排除未来事件: {ANALYSIS_CONFIG['event_filter']['exclude_future_events']}")
logger.info("发布过滤配置:")
logger.info(f"  - 最大发布年龄: {ANALYSIS_CONFIG['publish_filter']['max_publish_age_days']}天")
logger.info(f"  - 最小发布年龄: {ANALYSIS_CONFIG['publish_filter']['min_publish_age_days']}天")
logger.info(f"  - 排除未来发布: {ANALYSIS_CONFIG['publish_filter']['exclude_future_publish']}")

# 监控的公众号数量
logger.info(f"\n监控的公众号数量: {len(BIZ_LIST)}")
logger.info("==================== 配置信息结束 ====================\n")

# 添加save_report_to_storage函数
def save_report_to_storage(report_data: dict, report_text: str) -> Tuple[str, str]:
    """
    保存报告到 MongoDB 和 MinIO
    
    Args:
        report_data: 报告数据字典
        report_text: 报告文本内容（Markdown格式）
        
    Returns:
        Tuple[str, str]: 存储路径和报告ID
    """
    try:
        logger.info("开始保存金融舆情分析报告...")
        
        # 从报告数据中提取时间范围
        time_range = report_data['metadata']['time_range']
        try:
            # 尝试从时间范围字符串中提取开始时间
            # "近3天 (截至 2023-05-20)" 格式
            start_date_str = time_range.split('截至')[1].strip().rstrip(')')
            end_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            days = int(time_range.split('近')[1].split('天')[0].strip())
            start_date = end_date - timedelta(days=days)
        except Exception:
            # 如果解析失败，使用当前时间
            logger.warning(f"无法从时间范围 '{time_range}' 解析日期，使用当前时间")
            end_date = datetime.now()
            days = 3  # 默认近3天
            start_date = end_date - timedelta(days=days)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"financial_report_storm_{timestamp}.md"
        
        # 将报告内容转换为字节
        report_bytes = report_text.encode('utf-8')
        
        # 上传到 MinIO
        object_name = f"financial_reports/{filename}"
        logger.info(f"正在将报告上传到 MinIO，文件名: {object_name}")
        
        try:
            # 使用put_object上传到MinIO
            minio_client.put_object(
                bucket_name=MINIO_BUCKET,
                object_name=object_name,
                data=io.BytesIO(report_bytes),
                length=len(report_bytes),
                content_type='text/markdown'
            )
            storage_path = f"{MINIO_BUCKET}/{object_name}"
            logger.info(f"报告已成功上传到MinIO: {storage_path}")
        except Exception as e:
            logger.error(f"MinIO上传失败: {str(e)}")
            storage_path = f"local://{filename}"
            
            # 保存到本地文件作为备份
            with open(filename, 'wb') as f:
                f.write(report_bytes)
            logger.info(f"报告已保存到本地文件: {filename}")
        
        # 创建媒体洞察报告记录
        logger.info("正在创建媒体洞察报告记录...")
        report_doc = MediaInsightsReport(
            title=report_data['report_title'],
            content=report_text,
            storage_path=storage_path,
            report_type='financial_opinion',  # 固定为金融舆情报告类型
            start_time=start_date,
            end_time=end_date,
            media_ids=BIZ_LIST  # 使用配置的公众号列表作为媒体ID
        )
        
        # 保存到 MongoDB
        report_doc.save()
        report_id = str(report_doc.id)
        
        logger.info(f"报告已成功保存。MinIO路径: {storage_path}, MongoDB ID: {report_id}")
        return storage_path, report_id
        
    except Exception as e:
        logger.error(f"保存报告失败: {str(e)}")
        traceback.print_exc()
        # 生成一个简单的ID作为备份
        fallback_id = str(uuid.uuid4())
        # 在出错的情况下，保存到本地文件
        local_filename = f"financial_report_fallback_{fallback_id}.md"
        try:
            with open(local_filename, 'w', encoding='utf-8') as f:
                f.write(report_text)
            logger.info(f"由于错误，报告已备份到本地文件: {local_filename}")
            return local_filename, fallback_id
        except Exception:
            raise

# 添加safe_llm_invoke函数
def safe_llm_invoke(messages, timeout_seconds=120, default_response=None, description="LLM调用"):
    """安全的LLM调用，带有超时处理和错误恢复"""
    logger.info(f"开始{description}，设置超时时间: {timeout_seconds}秒")
    try:
        # 使用timeout装饰器包装llm.invoke调用
        @timeout(timeout_seconds)
        def invoke_with_timeout(msgs):
            return llm.invoke(msgs)

        # 调用包装后的函数
        result = invoke_with_timeout(messages)
        logger.info(f"{description}完成，返回内容长度: {len(result.content)}")
        return result
    except TimeoutException as e:
        logger.error(f"{description}超时: {str(e)}")
        if default_response is not None:
            if isinstance(default_response, str):
                return AIMessage(content=default_response)
            return default_response
        raise
    except Exception as e:
        logger.error(f"{description}失败: {str(e)}")
        traceback.print_exc()
        if default_response is not None:
            if isinstance(default_response, str):
                return AIMessage(content=default_response)
            return default_response
        raise

# 添加run_agent函数
def run_agent(days=None):
    """运行金融舆情分析智能体"""
    logger.info("====================== 开始运行金融舆情分析智能体 ======================")
    start_time = datetime.now()
    logger.info(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"分析天数: {days if days is not None else ANALYSIS_CONFIG['max_days']}天")

    try:
        # 获取报告数据
        logger.info("开始生成报告...")
        report_data = generate_report_with_storm()
        
        if not report_data:
            logger.error("报告生成失败，返回数据为空")
            return {"status": "error", "message": "报告生成失败，返回数据为空"}

        # 格式化报告为Markdown
        logger.info("开始格式化报告...")
        report_text = format_storm_report(report_data)
        
        if not report_text:
            logger.error("报告格式化失败，返回文本为空")
            return {"status": "error", "message": "报告格式化失败，返回文本为空"}

        # 保存报告到存储
        try:
            storage_path, report_id = save_report_to_storage(
                report_data,
                report_text
            )
            
            end_time = datetime.now()
            elapsed_time = (end_time - start_time).total_seconds()
            logger.info(f"金融舆情分析报告生成成功，总耗时: {elapsed_time:.2f} 秒")
            
            return {
                "status": "success",
                "report_text": report_text,
                "report_data": report_data,
                "storage_path": storage_path,
                "report_id": report_id
            }
        except Exception as e:
            logger.error(f"保存报告失败: {str(e)}")
            return {
                "status": "error",
                "message": f"报告生成成功但保存失败: {str(e)}"
            }

    except Exception as e:
        logger.error(f"运行智能体时出错: {str(e)}")
        traceback.print_exc()
        return {"status": "error", "message": str(e)}

# 添加LangGraph状态类
class AgentState(TypedDict):
    """智能体状态"""
    report_data: Optional[Dict]
    report_text: Optional[str]
    error: Optional[str]

# 添加主入口点
if __name__ == "__main__":
    # 打印启动信息
    logger.info("====================== 启动金融舆情分析脚本 ======================")
    logger.info(f"本次运行采用直接调用方式，不使用LangGraph工作流")
    
    # 运行智能体
    result = run_agent()
    
    # 处理结果
    if result["status"] == "success":
        print("报告生成成功！")
        
        # 保存报告到文件
        report_filename = f"financial_report_storm_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        try:
            with open(report_filename, "w", encoding="utf-8") as f:
                f.write(result["report_text"])
            print(f"报告已保存至: {report_filename}")
        except Exception as e:
            print(f"保存报告失败: {str(e)}")
            
        # 打印报告摘要
        lines = result["report_text"].split("\n")
        preview_lines = lines[:20] + ["...", "（报告内容省略）", "..."] + lines[-5:]
        print("\n".join(preview_lines))
    else:
        print(f"报告生成失败: {result['message']}")

# 添加数据处理函数
def get_es_scroll_data_batched(index, query_body, batch_size=1000, es_host=None, es_port=None):
    """滚动查询ES数据，并以批次方式返回"""
    if not es_host:
        es_host = TARGET_ES_HOST
    if not es_port:
        es_port = TARGET_ES_PORT

    es = Elasticsearch([f"{es_host}:{es_port}"])

    sid = None
    try:
        # 初始搜索
        result = es.search(index=index, scroll='10m', body=query_body, size=batch_size, request_timeout=3600)
        sid = result['_scroll_id']
        scroll_size = result['hits']['total']['value']
        logger.info(f"索引 {index} 总数据量: {scroll_size}")

        # 如果有结果，返回第一批数据
        if len(result['hits']['hits']) > 0:
            yield result['hits']['hits']

        # 继续滚动直到没有更多数据
        scroll_count = len(result['hits']['hits'])
        while scroll_count > 0:
            result = es.scroll(scroll_id=sid, scroll='10m', request_timeout=3600)
            batch_data = result['hits']['hits']
            scroll_count = len(batch_data)
            if scroll_count == 0:
                break
            yield batch_data

    except Exception as e:
        logger.error(f"获取索引 {index} 数据时出错: {str(e)}")
        traceback.print_exc()
    finally:
        if sid:
            try:
                es.clear_scroll(scroll_id=sid)
            except:
                pass

        if es:
            try:
                es.close()
            except:
                pass
        logger.info(f"索引 {index} 查询完成")

def build_query_body(start_time, end_time, biz=None):
    """构建ES查询体"""
    body = {
        "query": {
            "bool": {
                "filter": [
                    {
                        "range": {
                            "createTimeES": {
                                "gte": start_time,
                                "lte": end_time
                             }
                        }
                    }
                ]
            }
        },
        "sort": [{"createTimeES": "desc"}]  # 按createTimeES降序排序
    }

    # 如果指定了biz参数，则添加biz过滤条件
    if biz:
        # 如果biz是字符串，则使用term查询
        if isinstance(biz, str):
            body["query"]["bool"]["filter"].append({
                "term": {
                    "site_id": {
                        "value": biz
                    }
                }
            })
        # 如果biz是列表，则使用terms查询
        elif isinstance(biz, list) and len(biz) > 0:
            body["query"]["bool"]["filter"].append({
                "terms": {
                    "site_id": biz
                }
            })

    return body

def get_recent_reports(hours=24):
    """获取近24小时内的报告数据"""
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=hours)

    # 格式化为字符串
    end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
    start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")

    logger.info(f"获取时间范围: {start_time_str} 至 {end_time_str}")

    # 构建查询体
    query_body = build_query_body(start_time_str, end_time_str, BIZ_LIST)

    # 获取数据
    all_data = []
    for batch in get_es_scroll_data_batched(TARGET_ES_INDEX, query_body):
        all_data.extend([doc['_source'] for doc in batch])

    logger.info(f"获取到 {len(all_data)} 条记录")
    return all_data

def is_event_in_time_range(event_time_str):
    """检查事件是否在配置的时间范围内"""
    try:
        # 解析事件时间
        event_time = datetime.strptime(event_time_str, "%Y-%m-%d %H:%M:%S")
        current_time = datetime.now()

        # 计算事件年龄（天数）
        event_age_days = (current_time - event_time).days

        # 检查是否在配置的时间范围内
        if event_age_days < ANALYSIS_CONFIG["event_filter"]["min_event_age_days"]:
            return False

        if event_age_days > ANALYSIS_CONFIG["event_filter"]["max_event_age_days"]:
            return False

        # 检查是否为未来事件
        if ANALYSIS_CONFIG["event_filter"]["exclude_future_events"] and event_time > current_time:
            return False

        return True
    except Exception as e:
        logger.error(f"事件时间解析错误: {str(e)}")
        return False

def filter_events_by_time(report):
    """过滤报告中的事件信息"""
    if not report.get('eventInfo'):
        return report

    filtered_events = []
    for event in report['eventInfo']:
        # 检查事件是否包含时间信息
        event_time = None
        if isinstance(event, dict):
            event_time = event.get('time') or event.get('eventTime') or event.get('timestamp')

        # 如果没有时间信息，保留事件（保守策略）
        if not event_time:
            filtered_events.append(event)
            continue

        # 检查事件时间是否在范围内
        if is_event_in_time_range(event_time):
            filtered_events.append(event)

    report['eventInfo'] = filtered_events
    return report

def is_publish_time_valid(publish_time_str):
    """检查报告发布时间是否在配置的有效范围内"""
    try:
        # 尝试解析不同格式的时间字符串
        try:
            # 尝试标准格式 "YYYY-MM-DD HH:MM:SS"
            publish_time = datetime.strptime(publish_time_str, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                # 尝试日期格式 "YYYY-MM-DD"
                publish_time = datetime.strptime(publish_time_str, "%Y-%m-%d")
            except ValueError:
                try:
                    # 尝试带毫秒的格式
                    publish_time = datetime.strptime(publish_time_str, "%Y-%m-%d %H:%M:%S.%f")
                except ValueError:
                    # 如果无法解析，返回False
                    logger.warning(f"无法解析发布时间: {publish_time_str}")
                    return False

        current_time = datetime.now()

        # 计算发布年龄（天数）
        publish_age_days = (current_time - publish_time).days

        # 检查是否在配置的时间范围内
        if publish_age_days < ANALYSIS_CONFIG["publish_filter"]["min_publish_age_days"]:
            logger.info(f"报告发布时间太新: {publish_time_str}, 年龄: {publish_age_days}天")
            return False

        if publish_age_days > ANALYSIS_CONFIG["publish_filter"]["max_publish_age_days"]:
            logger.info(f"报告发布时间太旧: {publish_time_str}, 年龄: {publish_age_days}天")
            return False

        # 检查是否为未来发布
        if ANALYSIS_CONFIG["publish_filter"]["exclude_future_publish"] and publish_time > current_time:
            logger.info(f"报告发布时间在未来: {publish_time_str}")
            return False

        return True
    except Exception as e:
        logger.error(f"发布时间验证错误: {str(e)}")
        return False

def get_latest_report_by_account(days=3):
    """获取每个公众号最近3天的最新一篇报告"""
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)

    # 格式化为字符串
    end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
    start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")

    logger.info(f"获取最新报告时间范围: {start_time_str} 至 {end_time_str}")

    latest_reports = {}
    filtered_count = 0
    total_count = 0

    for biz in BIZ_LIST:
        # 构建查询体
        query_body = build_query_body(start_time_str, end_time_str, biz)

        # 添加分页限制，获取最新的几篇，以便进行发布时间过滤
        query_body["size"] = 5  # 获取多篇，以便在过滤后仍有可用报告

        try:
            # 获取记录
            es = Elasticsearch([f"{TARGET_ES_HOST}:{TARGET_ES_PORT}"])
            result = es.search(index=TARGET_ES_INDEX, body=query_body)

            valid_report_found = False

            # 遍历结果，找到符合发布时间条件的最新报告
            for hit in result['hits']['hits']:
                total_count += 1
                doc = hit['_source']
                publish_time = doc.get('publishtime', '')

                # 检查发布时间是否有效
                if not publish_time or not is_publish_time_valid(publish_time):
                    filtered_count += 1
                    logger.info(f"过滤掉公众号 {doc.get('source', biz)} 的文章: {doc.get('title', '无标题')}, 发布时间: {publish_time}")
                    continue

                # 找到有效报告
                report = {
                    'title': doc.get('title', '无标题'),
                    'content': doc.get('content', '无内容'),
                    'author': doc.get('new_author', '未知作者'),
                    'site_name': doc.get('source', '未知公众号'),
                    'publishtime': publish_time,
                    'authorViewpoint': doc.get('authorViewpoint', ''),
                    'characterEntity': doc.get('characterEntity', []),
                    'institutionalEntities': doc.get('institutionalEntities', []),
                    'locationEntity': doc.get('locationEntity', []),
                    'eventInfo': doc.get('eventInfo', []),
                    'summaryFacts': doc.get('summaryFacts', []),
                    'summary': doc.get('summary', '')
                }

                # 应用事件过滤
                report = filter_events_by_time(report)

                latest_reports[biz] = report
                logger.info(f"获取到公众号 {doc.get('source', biz)} 有效文章, 发布时间: {publish_time}, 标题: {doc.get('title', '无标题')}")
                valid_report_found = True
                break

            if not valid_report_found:
                logger.warning(f"未找到公众号 {biz} 的有效报告")

        except Exception as e:
            logger.error(f"获取公众号 {biz} 最新报告时出错: {str(e)}")
        finally:
            if 'es' in locals():
                es.close()

    logger.info(f"获取到 {len(latest_reports)} 个公众号的最新报告，过滤掉 {filtered_count}/{total_count} 篇不符合发布时间条件的报告")
    return latest_reports

# 报告分析和格式化相关函数
def analyze_report_with_reasoning(title, content, account_name):
    """使用STORM结构化推理方式分析单个报告内容"""
    logger.info(f"开始分析来自 '{account_name}' 的报告: {title[:50]}...")
    # 步骤1: 初始提示
    system_msg = SystemMessage(content="""你是一位金融研究报告分析专家。请对以下公众号研究报告进行内容归纳，输出包括：
1. 核心观点（简明扼要）
2. 观点依据（数据、事实、逻辑支撑）
3. 其他重要信息（如市场影响、关键洞见等）
请用结构化JSON格式返回。""")

    human_msg = HumanMessage(content=f"""
请对以下来自"{account_name}"公众号的研究报告内容进行归纳总结，归纳总结的内容包括但不限于核心观点、观点依据等。

标题: {title}
内容: {content[:6000] if content and len(content) > 6000 else content}
""")

    # 执行初始思考
    logger.info(f"向LLM发送初始分析请求，内容长度: {len(content[:6000] if content and len(content) > 6000 else content)}")
    messages = [system_msg, human_msg]

    # 使用安全的LLM调用
    default_analysis = AIMessage(content="无法在规定时间内完成分析。")
    initial_analysis = safe_llm_invoke(
        messages,
        timeout_seconds=120,
        default_response=default_analysis,
        description=f"'{account_name}'报告初始分析"
    )

    # 步骤2: 要求生成结构化输出
    logger.info("开始生成结构化分析结果...")
    system_msg2 = SystemMessage(content="现在请基于你的分析，以JSON格式提供最终的结构化结果。")

    human_msg2 = HumanMessage(content="""请以JSON格式返回分析结果，包含以下字段:
1. summary: 整体摘要（不超过300字）
2. core_viewpoints: 核心观点列表（数组格式）
3. supporting_evidence: 观点依据列表（数组格式）
4. key_insights: 关键洞见（不超过200字）
5. market_impact: 对市场的潜在影响
6. reasoning_chain: 你的主要推理过程（简洁列表形式）""")

    try:
        messages2 = [system_msg2, initial_analysis, human_msg2]

        # 使用安全的LLM调用
        default_result = AIMessage(content="""
{
  "summary": "无法在规定时间内完成分析",
  "core_viewpoints": ["无法提取核心观点"],
  "supporting_evidence": ["无法提取支持证据"],
  "key_insights": "无法提取关键洞见",
  "market_impact": "无法分析市场影响",
  "reasoning_chain": ["超时，无法完成分析"]
}
""")
        result = safe_llm_invoke(
            messages2,
            timeout_seconds=60,
            default_response=default_result,
            description=f"'{account_name}'报告结构化分析"
        )
        content = result.content

        # 尝试解析JSON结果
        start_pos = content.find('{')
        end_pos = content.rfind('}') + 1
        if start_pos >= 0 and end_pos > start_pos:
            json_str = content[start_pos:end_pos]
            analysis = json.loads(json_str)
            # 保存推理过程
            analysis["reasoning_process"] = initial_analysis.content
            logger.info(f"成功解析结构化分析结果，包含 {len(analysis.get('core_viewpoints', []))} 条核心观点")
            return analysis
        else:
            logger.error("无法找到JSON内容")
            return {
                "summary": "无法解析报告内容",
                "core_viewpoints": [],
                "supporting_evidence": [],
                "key_insights": "",
                "market_impact": "无法分析",
                "reasoning_chain": []
            }
    except Exception as e:
        logger.error(f"分析报告内容时出错: {str(e)}")
        return {
            "summary": "分析过程出错",
            "core_viewpoints": [],
            "supporting_evidence": [],
            "key_insights": "",
            "market_impact": "分析出错",
            "reasoning_chain": []
        }

def analyze_all_reports_with_storm(reports_data):
    """使用STORM方法综合分析所有报告的观点"""
    logger.info(f"开始综合分析 {len(reports_data)} 份报告的观点...")
    # 提取所有报告的观点和数据
    all_viewpoints = []
    all_entities = set()
    all_institutions = set()
    all_events = set()
    report_summaries = []

    for biz, report in reports_data.items():
        # 收集观点
        if 'analysis' in report and report['analysis'].get('core_viewpoints'):
            for viewpoint in report['analysis']['core_viewpoints']:
                all_viewpoints.append({
                    'site_name': report['site_name'],
                    'viewpoint': viewpoint
                })
        if report.get('authorViewpoint'):
            all_viewpoints.append({
                'site_name': report['site_name'],
                'viewpoint': report['authorViewpoint']
            })

        # 收集实体和事件
        all_entities.update(report.get('characterEntity', []))
        all_institutions.update(report.get('institutionalEntities', []))
        all_events.update(report.get('eventInfo', []))

        # 收集摘要
        if report.get('summary'):
            report_summaries.append({
                'site_name': report['site_name'],
                'summary': report['summary']
            })

    logger.info(f"提取了 {len(all_viewpoints)} 个观点, {len(all_entities)} 个实体, {len(all_institutions)} 个机构, {len(all_events)} 个事件")

    # 构建数据摘要
    data_summary = {
        "total_reports": len(reports_data),
        "unique_entities": list(all_entities)[:10],  # 限制数量
        "unique_institutions": list(all_institutions)[:10],
        "unique_events": list(all_events)[:10],
        "viewpoint_count": len(all_viewpoints)
    }

    # 观点文本
    viewpoints_text = "\n\n".join([f"{item['site_name']}: {item['viewpoint']}" for item in all_viewpoints])
    logger.info(f"合成的观点文本长度: {len(viewpoints_text)}")

    # 步骤1: 初始分析
    logger.info("开始向LLM发送综合分析请求...")
    system_msg = SystemMessage(content="""你是一位资深金融市场分析师。请对以下多家机构的公众号研究报告进行多维度综合归纳，分析内容需要按照以下维度进行详细分析：

1. 宏观经济预期分析
   - 各机构对宏观经济的预判（标注观点来源机构）
   - 主要依据和论据（标注来源）
   - 共识观点和分歧观点

2. 宏观政策预期分析
   - 各机构对政策走向的判断（标注观点来源机构）
   - 政策预期的主要依据（标注来源）
   - 共识观点和分歧观点

3. 利率走势预测分析
   - 各机构对利率走势的预测（标注观点来源机构）
   - 预测的主要依据（标注来源）
   - 共识观点和分歧观点

4. 投资策略建议分析
   - 各机构的投资策略建议（标注观点来源机构）
   - 投资机会分析（标注来源）
   - 共识建议和分歧建议

5. 市场观点分析
   - 市场主流观点
   - 观点共识（哪些机构持有相同观点）
   - 观点分歧（哪些机构观点不同）

6. 风险与机会分析
   - 主要风险点（标注提出风险的机构）
   - 主要机会点（标注提出机会的机构）

请用结构化JSON格式返回，确保每个观点都注明出处机构。""")

    human_msg = HumanMessage(content=f"""
请对以下多家金融机构的研究报告观点进行归纳总结，归纳总结的维度必须包括：
1. 宏观经济预期分析
2. 宏观政策预期分析
3. 利率走势预测分析
4. 投资策略建议分析
5. 市场观点分析（共识与分歧）
6. 风险与机会分析

数据概要:
- 总报告数: {data_summary['total_reports']}
- 涉及机构: {', '.join(data_summary['unique_institutions'][:5]) if data_summary['unique_institutions'] else '无'}
- 主要事件: {', '.join(data_summary['unique_events'][:5]) if data_summary['unique_events'] else '无'}
- 观点数量: {data_summary['viewpoint_count']}

非常重要：请在你的分析中标明每个观点的来源机构，确保读者知道每个观点是由哪家机构提出的。每个维度的分析都要尽可能详细，并确保标注来源机构。

以下是各家机构的观点:
{viewpoints_text}
""")

    # 执行初始思考 - 使用安全的LLM调用
    messages = [system_msg, human_msg]
    default_analysis = AIMessage(content="无法在规定时间内完成综合分析。")
    initial_analysis = safe_llm_invoke(
        messages,
        timeout_seconds=180,
        default_response=default_analysis,
        description="多机构报告综合分析"
    )

    # 步骤2: 要求生成结构化输出
    logger.info("开始生成结构化综合分析结果...")
    system_msg2 = SystemMessage(content="现在请基于你的分析，以JSON格式提供最终的结构化结果。请确保每个观点都标注其来源机构，并按照要求的维度进行详细分析。")

    human_msg2 = HumanMessage(content="""请以JSON格式返回分析结果，必须包含以下字段:

1. macro_economic_outlook: 宏观经济预期分析（数组格式，每项包含观点内容和来源机构）
2. policy_expectations: 宏观政策预期分析（数组格式，每项包含观点内容和来源机构）
3. interest_rate_forecast: 利率走势预测分析（数组格式，每项包含观点内容和来源机构）
4. investment_recommendations: 投资策略建议分析（数组格式，每项包含观点内容和来源机构）
5. consensus_points: 机构观点一致之处（数组，每项包含观点内容和支持该观点的机构列表）
6. divergent_points: 机构观点分歧之处（数组，每项包含观点内容和持该观点的机构）
7. overall_summary: 整体市场观点摘要（300字以内，提及主要观点来源）
8. market_risks: 当前市场主要风险点（数组，每项包含风险及提出该风险的机构）
9. market_opportunities: 当前市场主要机会（数组，每项包含机会及提出该机会的机构）

对于每个数组项，请使用以下格式：
{
  "content": "观点内容",
  "sources": ["机构A", "机构B"]
}

确保在每个字段中都标明信息的来源机构，例如"根据A、B、C三家机构观点，宏观经济..."或使用格式"观点内容（来源：机构A、机构B）"。""")

    try:
        messages2 = [system_msg2, initial_analysis, human_msg2]

        # 使用安全的LLM调用
        default_result = AIMessage(content="""
{
  "macro_economic_outlook": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "policy_expectations": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "interest_rate_forecast": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "investment_recommendations": [{"content": "无法在规定时间内完成综合分析", "sources": []}],
  "consensus_points": [{"content": "无法识别共识观点", "sources": []}],
  "divergent_points": [{"content": "无法识别分歧观点", "sources": []}],
  "overall_summary": "无法在规定时间内生成综合分析",
  "market_risks": [{"content": "无法识别市场风险", "sources": []}],
  "market_opportunities": [{"content": "无法识别市场机会", "sources": []}]
}
""")
        result = safe_llm_invoke(
            messages2,
            timeout_seconds=120,
            default_response=default_result,
            description="生成结构化综合分析"
        )
        content = result.content

        # 尝试解析JSON结果
        start_pos = content.find('{')
        end_pos = content.rfind('}') + 1
        if start_pos >= 0 and end_pos > start_pos:
            json_str = content[start_pos:end_pos]
            analysis = json.loads(json_str)
            # 保存推理过程
            analysis["reasoning_process"] = initial_analysis.content
            logger.info(f"成功解析综合分析结果，包含 {len(analysis.get('consensus_points', []))} 条共识观点, {len(analysis.get('divergent_points', []))} 条分歧观点")
            return analysis
        else:
            logger.error("无法找到综合分析的JSON内容")
            return {
                "macro_economic_outlook": [{"content": "无法解析综合观点", "sources": []}],
                "policy_expectations": [{"content": "无法解析综合观点", "sources": []}],
                "interest_rate_forecast": [{"content": "无法解析综合观点", "sources": []}],
                "investment_recommendations": [{"content": "无法解析综合观点", "sources": []}],
                "consensus_points": [],
                "divergent_points": [],
                "overall_summary": "无法生成综合分析",
                "market_risks": [],
                "market_opportunities": []
            }
    except Exception as e:
        logger.error(f"综合分析报告内容时出错: {str(e)}")
        return {
            "macro_economic_outlook": [{"content": "分析过程出错", "sources": []}],
            "policy_expectations": [{"content": "分析过程出错", "sources": []}],
            "interest_rate_forecast": [{"content": "分析过程出错", "sources": []}],
            "investment_recommendations": [{"content": "分析过程出错", "sources": []}],
            "consensus_points": [],
            "divergent_points": [],
            "overall_summary": "分析过程出错",
            "market_risks": [],
            "market_opportunities": []
        }

def generate_executive_summary(overall_analysis, reports_count):
    """生成执行摘要"""
    logger.info(f"开始为 {reports_count} 家机构的报告生成执行摘要...")

    # 格式化复杂结构为字符串，避免类型错误
    def format_complex_item(items, max_items=3):
        if not items:
            return '无数据'

        if isinstance(items, str):
            return items

        formatted_points = []
        count = 0

        for item in items[:max_items]:
            if isinstance(item, dict):
                content = item.get('content', '')
                sources = item.get('sources', [])
                if isinstance(sources, list) and sources:
                    sources_str = '、'.join(sources)
                    formatted_points.append(f"{content}（来源：{sources_str}）")
                else:
                    formatted_points.append(content)
            elif isinstance(item, str):
                formatted_points.append(item)

            count += 1
            if count >= max_items:
                break

        if len(items) > max_items:
            formatted_points.append("等")

        return '；'.join(formatted_points)

    system_msg = SystemMessage(content="""你是一位专业的金融市场分析师，需要为高管层撰写简明扼要的市场观点执行摘要。
请基于提供的综合分析数据，生成一份简短但信息量大的执行摘要。
摘要应包含以下几个关键维度的分析：宏观经济预期、宏观政策预期、利率走势预测、投资策略建议、市场观点共识与分歧、主要风险和机会。
使用专业但易于理解的语言，避免冗长解释。

非常重要：在执行摘要中，请明确标注各个观点的信息来源（例如"根据A、B、C三家机构的观点..."或"...（来源：机构A、机构B）"）。
这样做的目的是让读者清楚了解各项信息的出处，提高报告的专业性和可信度。""")

    try:
        # 安全地获取各个字段值
        macro_economic = format_complex_item(overall_analysis.get('macro_economic_outlook', []))
        policy_expectations = format_complex_item(overall_analysis.get('policy_expectations', []))
        interest_rate = format_complex_item(overall_analysis.get('interest_rate_forecast', []))
        investment = format_complex_item(overall_analysis.get('investment_recommendations', []))
        consensus = format_complex_item(overall_analysis.get('consensus_points', []))
        divergent = format_complex_item(overall_analysis.get('divergent_points', []))
        risks = format_complex_item(overall_analysis.get('market_risks', []))
        opportunities = format_complex_item(overall_analysis.get('market_opportunities', []))

        # 获取整体市场观点摘要
        overall_summary = overall_analysis.get('overall_summary', '无法获取整体市场观点摘要')

        human_msg = HumanMessage(content=f"""
基于对{reports_count}家金融机构研报的分析，生成一份执行摘要，包含以下维度：

1. 宏观经济预期分析：{macro_economic}

2. 宏观政策预期分析：{policy_expectations}

3. 利率走势预测分析：{interest_rate}

4. 投资策略建议分析：{investment}

5. 市场观点分析：
   - 共识观点：{consensus}
   - 分歧观点：{divergent}

6. 风险与机会：
   - 主要风险：{risks}
   - 主要机会：{opportunities}

7. 整体市场观点：{overall_summary}

请生成一份不超过400字的高管层执行摘要，确保标注每个观点的来源机构，使用"（来源：XX、XX机构）"或"据XX、XX机构认为..."等方式明确标注。
摘要需要结构清晰，重点突出，便于高管快速把握市场关键信息。""")

        logger.info("向LLM发送执行摘要生成请求...")
        messages = [system_msg, human_msg]

        # 使用安全的LLM调用
        default_summary = "无法在规定时间内生成执行摘要。请参考综合分析部分了解详细情况。"
        result = safe_llm_invoke(
            messages,
            timeout_seconds=90,
            default_response=default_summary,
            description="生成执行摘要"
        )
        logger.info(f"执行摘要生成完成，长度: {len(result.content)} 字符")
        return result.content
    except Exception as e:
        logger.error(f"生成执行摘要时出错: {str(e)}")
        traceback.print_exc()
        return "无法生成执行摘要。错误原因：" + str(e)

def format_structured_list(items, content_key='content', source_key='sources'):
    lines = []
    for item in items:
        if isinstance(item, dict):
            content = item.get(content_key) or item.get('point') or item.get('risk') or item.get('opportunity') or ''
            sources = item.get(source_key) or item.get('institutions') or item.get('source') or []
            if isinstance(sources, list):
                sources = '、'.join(sources)
            if sources:
                lines.append(f"- {content}（来源：{sources}）")
            else:
                lines.append(f"- {content}")
        elif isinstance(item, str):
            lines.append(f"- {item}")
    return '\n'.join(lines)

def add_heading_numbering(md_text):
    lines = md_text.split('\n')
    h1, h2, h3 = 0, 0, 0
    new_lines = []
    for line in lines:
        if line.startswith('# '):
            h1 += 1; h2 = 0; h3 = 0
            new_lines.append(f"# {h1}. {line[2:]}")
        elif line.startswith('## '):
            h2 += 1; h3 = 0
            new_lines.append(f"## {h1}.{h2} {line[3:]}")
        elif line.startswith('### '):
            h3 += 1
            new_lines.append(f"### {h1}.{h2}.{h3} {line[4:]}")
        else:
            new_lines.append(line)
    return '\n'.join(new_lines)

def generate_report_with_storm():
    """使用STORM方法生成完整的分析报告"""
    logger.info("==================== 开始使用STORM方法生成分析报告 ====================")
    # 1. 获取每个公众号近几天内最新的一篇报告，使用配置的天数
    days_to_fetch = ANALYSIS_CONFIG.get("max_days", 3)
    logger.info(f"第1步: 获取每个公众号最新报告（{days_to_fetch}天内）...")
    latest_reports = get_latest_report_by_account(days=days_to_fetch)
    logger.info(f"成功获取 {len(latest_reports)} 个公众号的最新报告")

    # 2. 并行分析每篇报告
    errors = []
    total = len(latest_reports)
    logger.info(f"第2步: 开始并行分析 {total} 个公众号报告...")

    def analyze_single_report(biz_report_tuple):
        biz, report = biz_report_tuple
        try:
            # 检查报告内容长度，过滤掉过短的内容
            content = report['content']
            if content and len(content) < ANALYSIS_CONFIG.get("min_word_count", 500):
                return biz, None, f"{report['site_name']} 内容过短，不进行分析"

            logger.info(f"开始分析公众号: {report['site_name']}")
            analysis = analyze_report_with_reasoning(report['title'], content, report['site_name'])
            logger.info(f"完成公众号分析: {report['site_name']}")
            return biz, analysis, None
        except Exception as e:
            error_msg = f"{report['site_name']} 分析失败: {str(e)}"
            logger.error(error_msg)
            return biz, None, error_msg

    # 使用线程池并行处理，根据配置设置最大线程数
    max_workers = min(10, total)
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(executor.map(analyze_single_report, latest_reports.items()))

    # 处理结果
    valid_reports_count = 0
    for biz, analysis, error in results:
        if error:
            errors.append(error)
        elif analysis:
            latest_reports[biz]['analysis'] = analysis
            valid_reports_count += 1

    logger.info(f"成功分析 {valid_reports_count}/{total} 个报告")

    # 如果没有有效的报告分析结果，记录错误并返回
    if valid_reports_count == 0:
        error_msg = "未能成功分析任何报告，无法生成综合分析"
        logger.error(error_msg)
        errors.append(error_msg)
        return {
            "generation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "report_title": f"金融舆情分析报告 - {datetime.now().strftime('%Y年%m月%d日')}",
            "executive_summary": "无法生成报告，未能成功分析任何内容。",
            "individual_reports": {},
            "overall_analysis": {},
            "metadata": {
                "reports_count": 0,
                "generation_method": "STORM结构化推理",
                "time_range": f"近{days_to_fetch}天 (截至 {datetime.now().strftime('%Y-%m-%d')})"
            },
            "errors": errors
        }

    # 3. 综合分析
    logger.info("第3步: 开始综合分析所有报告...")
    try:
        # 直接调用，内部已有超时处理
        overall_analysis = analyze_all_reports_with_storm(latest_reports)
        logger.info("综合分析完成")
    except Exception as e:
        error_msg = f"综合分析失败: {str(e)}"
        logger.error(error_msg)
        errors.append(error_msg)
        overall_analysis = {}

    # 4. 执行摘要
    logger.info("第4步: 开始生成执行摘要...")
    try:
        # 直接调用，内部已有超时处理
        executive_summary = generate_executive_summary(overall_analysis, valid_reports_count)
        logger.info("执行摘要生成完成")
    except Exception as e:
        error_msg = f"执行摘要生成失败: {str(e)}"
        logger.error(error_msg)
        errors.append(error_msg)
        executive_summary = "执行摘要生成失败。"

    # 5. 组装报告
    logger.info("第5步: 组装最终报告...")
    report = {
        "generation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "report_title": f"金融舆情分析报告 - {datetime.now().strftime('%Y年%m月%d日')}",
        "executive_summary": executive_summary,
        "individual_reports": latest_reports,
        "overall_analysis": overall_analysis,
        "metadata": {
            "reports_count": valid_reports_count,
            "generation_method": "STORM结构化推理",
            "time_range": f"近{days_to_fetch}天 (截至 {datetime.now().strftime('%Y-%m-%d')})"
        },
        "errors": errors
    }

    logger.info("==================== 报告生成过程完成 ====================")
    return report

def format_storm_report(report):
    """将STORM报告格式化为易于展示的Markdown格式"""
    logger.info("开始格式化STORM报告...")
    start_time = datetime.now()

    # 报告标题和基本信息
    output = f"# {report['report_title']}\n\n"
    output += f"生成时间: {report['generation_time']}\n"
    output += f"分析报告数: {report['metadata']['reports_count']}\n"
    output += f"时间范围: {report['metadata']['time_range']}\n\n"

    # 获取综合分析数据
    overall = report['overall_analysis']

    # 一、执行摘要
    logger.info("生成第一章：执行摘要...")
    output += "## 执行摘要\n\n"
    output += f"{report['executive_summary']}\n\n"

    # 二、市场综合分析
    logger.info("生成第二章：市场综合分析...")
    output += "## 市场综合分析\n\n"

    # 2.1 宏观经济预期分析
    logger.info("生成2.1节：宏观经济预期分析...")
    output += "### 宏观经济预期分析\n\n"

    # 获取宏观经济预期内容
    macro_economic = overall.get('macro_economic_outlook', '')
    if isinstance(macro_economic, str):
        output += macro_economic + "\n\n"
    else:
        # 主要观点汇总
        output += "#### 主要观点汇总\n\n"
        output += format_structured_list(macro_economic) + "\n\n"

        # 观点共识与分歧
        output += "#### 观点共识与分歧\n\n"
        consensus_found = False
        for point in overall.get('consensus_points', []):
            if isinstance(point, dict) and '宏观经济' in point.get('content', '').lower():
                consensus_found = True
                sources = ', '.join(point['sources']) if isinstance(point['sources'], list) else point['sources']
                output += f"- 共识: {point['content']} (来源: {sources})\n"

        divergent_found = False
        for point in overall.get('divergent_points', []):
            if isinstance(point, dict) and '宏观经济' in point.get('content', '').lower():
                divergent_found = True
                sources = ', '.join(point['sources']) if isinstance(point['sources'], list) else point['sources']
                output += f"- 分歧: {point['content']} (来源: {sources})\n"

        if not consensus_found and not divergent_found:
            output += "未发现明确的宏观经济观点共识与分歧。\n"
        output += "\n"

    # 2.2 宏观政策预期分析
    logger.info("生成2.2节：宏观政策预期分析...")
    output += "### 宏观政策预期分析\n\n"

    # 获取宏观政策预期内容
    policy_expectations = overall.get('policy_expectations', '')
    if isinstance(policy_expectations, str):
        output += policy_expectations + "\n\n"
    else:
        # 主要观点汇总
        output += "#### 主要观点汇总\n\n"
        output += format_structured_list(policy_expectations) + "\n\n"

        # 观点共识与分歧
        output += "#### 观点共识与分歧\n\n"
        consensus_found = False
        for point in overall.get('consensus_points', []):
            if isinstance(point, dict) and '政策' in point.get('content', '').lower():
                consensus_found = True
                sources = ', '.join(point['sources']) if isinstance(point['sources'], list) else point['sources']
                output += f"- 共识: {point['content']} (来源: {sources})\n"

        divergent_found = False
        for point in overall.get('divergent_points', []):
            if isinstance(point, dict) and '政策' in point.get('content', '').lower():
                divergent_found = True
                sources = ', '.join(point['sources']) if isinstance(point['sources'], list) else point['sources']
                output += f"- 分歧: {point['content']} (来源: {sources})\n"

        if not consensus_found and not divergent_found:
            output += "未发现明确的宏观政策观点共识与分歧。\n"
        output += "\n"

    # 2.3 利率走势预测分析
    logger.info("生成2.3节：利率走势预测分析...")
    output += "### 利率走势预测分析\n\n"

    # 获取利率走势预测内容
    interest_rate = overall.get('interest_rate_forecast', '')
    if isinstance(interest_rate, str):
        output += interest_rate + "\n\n"
    else:
        # 主要观点汇总
        output += "#### 主要观点汇总\n\n"
        output += format_structured_list(interest_rate) + "\n\n"

        # 观点共识与分歧
        output += "#### 观点共识与分歧\n\n"
        consensus_found = False
        for point in overall.get('consensus_points', []):
            if isinstance(point, dict) and ('利率' in point.get('content', '').lower() or '收益率' in point.get('content', '').lower()):
                consensus_found = True
                sources = ', '.join(point['sources']) if isinstance(point['sources'], list) else point['sources']
                output += f"- 共识: {point['content']} (来源: {sources})\n"

        divergent_found = False
        for point in overall.get('divergent_points', []):
            if isinstance(point, dict) and ('利率' in point.get('content', '').lower() or '收益率' in point.get('content', '').lower()):
                divergent_found = True
                sources = ', '.join(point['sources']) if isinstance(point['sources'], list) else point['sources']
                output += f"- 分歧: {point['content']} (来源: {sources})\n"

        if not consensus_found and not divergent_found:
            output += "未发现明确的利率走势观点共识与分歧。\n"
        output += "\n"

    # 2.4 投资策略建议分析
    logger.info("生成2.4节：投资策略建议分析...")
    output += "### 投资策略建议分析\n\n"

    # 获取投资建议内容
    investment = overall.get('investment_recommendations', '')
    if isinstance(investment, str):
        output += investment + "\n\n"
    else:
        # 主要观点汇总
        output += "#### 主要投资建议汇总\n\n"
        output += format_structured_list(investment) + "\n\n"

        # 观点共识与分歧
        output += "#### 建议共识与分歧\n\n"
        consensus_found = False
        for point in overall.get('consensus_points', []):
            if isinstance(point, dict) and ('投资' in point.get('content', '').lower() or '配置' in point.get('content', '').lower()):
                consensus_found = True
                sources = ', '.join(point['sources']) if isinstance(point['sources'], list) else point['sources']
                output += f"- 共识: {point['content']} (来源: {sources})\n"

        divergent_found = False
        for point in overall.get('divergent_points', []):
            if isinstance(point, dict) and ('投资' in point.get('content', '').lower() or '配置' in point.get('content', '').lower()):
                divergent_found = True
                sources = ', '.join(point['sources']) if isinstance(point['sources'], list) else point['sources']
                output += f"- 分歧: {point['content']} (来源: {sources})\n"

        if not consensus_found and not divergent_found:
            output += "未发现明确的投资建议共识与分歧。\n"
        output += "\n"

    # 2.5 市场观点分析
    logger.info("生成2.5节：市场观点分析...")
    output += "### 市场观点分析\n\n"

    # 市场主流观点
    output += "#### 市场主流观点\n\n"
    output += f"{overall.get('overall_summary', '未能提取市场主流观点。')}\n\n"

    # 观点共识
    output += "#### 观点共识\n\n"
    consensus_points = overall.get('consensus_points', [])
    if consensus_points:
        for point in consensus_points:
            if isinstance(point, str):
                output += f"- {point}\n"
            elif isinstance(point, dict) and 'content' in point and 'sources' in point:
                sources = ', '.join(point['sources']) if isinstance(point['sources'], list) else point['sources']
                output += f"- {point['content']} (支持机构: {sources})\n"
            else:
                output += f"- {str(point)}\n"
    else:
        output += "未发现明确的市场观点共识。\n"
    output += "\n"

    # 观点分歧
    output += "#### 观点分歧\n\n"
    divergent_points = overall.get('divergent_points', [])
    if divergent_points:
        for point in divergent_points:
            if isinstance(point, str):
                output += f"- {point}\n"
            elif isinstance(point, dict) and 'content' in point and 'sources' in point:
                sources = ', '.join(point['sources']) if isinstance(point['sources'], list) else point['sources']
                output += f"- {point['content']} (相关机构: {sources})\n"
            else:
                output += f"- {str(point)}\n"
    else:
        output += "未发现明确的市场观点分歧。\n"
    output += "\n"

    # 2.6 风险与机会
    logger.info("生成2.6节：风险与机会...")
    output += "### 风险与机会\n\n"

    # 市场主要风险
    output += "#### 市场主要风险\n\n"
    market_risks = overall.get('market_risks', [])
    if market_risks:
        for risk in market_risks:
            if isinstance(risk, str):
                output += f"- {risk}\n"
            elif isinstance(risk, dict):
                content = risk.get('content') or risk.get('risk') or str(risk)
                sources = risk.get('sources') or risk.get('source') or []
                if isinstance(sources, list):
                    sources = '、'.join(sources)
                if sources:
                    output += f"- {content}（提示机构：{sources}）\n"
                else:
                    output += f"- {content}\n"
    else:
        output += "未发现明确的市场风险点。\n"
    output += "\n"

    # 市场主要机会
    output += "#### 市场主要机会\n\n"
    market_opportunities = overall.get('market_opportunities', [])
    if market_opportunities:
        for opportunity in market_opportunities:
            if isinstance(opportunity, str):
                output += f"- {opportunity}\n"
            elif isinstance(opportunity, dict):
                content = opportunity.get('content') or opportunity.get('opportunity') or str(opportunity)
                sources = opportunity.get('sources') or opportunity.get('source') or []
                if isinstance(sources, list):
                    sources = '、'.join(sources)
                if sources:
                    output += f"- {content}（提示机构：{sources}）\n"
                else:
                    output += f"- {content}\n"
    else:
        output += "未发现明确的市场机会点。\n"
    output += "\n"

    # 三、各机构观点明细
    logger.info("生成第三章：各机构观点明细...")
    output += "## 各机构观点明细\n\n"

    report_count = 0
    for biz, report_data in report['individual_reports'].items():
        if 'analysis' not in report_data:
            continue

        analysis = report_data['analysis']
        output += f"### {report_data['site_name']}\n\n"

        # 报告基本信息
        output += "#### 报告信息\n\n"
        output += f"**标题**: {report_data['title']}\n\n"
        output += f"**发布时间**: {report_data['publishtime']}\n\n"

        # 核心观点摘要
        output += "#### 核心观点摘要\n\n"
        output += f"{analysis.get('summary', '未提供摘要')}\n\n"

        # 核心观点
        output += "#### 具体观点\n\n"
        core_viewpoints = analysis.get('core_viewpoints', [])
        if core_viewpoints:
            for point in core_viewpoints:
                output += f"- {point}\n"
        else:
            output += "未提取到核心观点。\n"
        output += "\n"

        # 观点依据
        output += "#### 观点依据\n\n"
        supporting_evidence = analysis.get('supporting_evidence', [])
        if supporting_evidence:
            for evidence in supporting_evidence:
                output += f"- {evidence}\n"
        else:
            output += "未提取到观点依据。\n"
        output += "\n"

        # 市场影响和关键洞见
        if analysis.get('market_impact') or analysis.get('key_insights'):
            output += "#### 市场影响与洞见\n\n"

            if analysis.get('market_impact'):
                output += f"**市场影响**: {analysis.get('market_impact')}\n\n"

            if analysis.get('key_insights'):
                output += f"**关键洞见**: {analysis.get('key_insights')}\n\n"

        output += "---\n\n"
        report_count += 1

    if report_count == 0:
        output += "未能获取到任何机构的有效分析结果。\n\n"

    # 四、附录
    logger.info("生成第四章：附录...")
    output += "## 附录\n\n"

    # 分析方法说明
    output += "### 分析方法说明\n\n"
    output += f"本报告采用 {report['metadata']['generation_method']} 方法，通过大语言模型对各金融机构公众号发布的研究报告进行自动化分析和总结。\n"
    output += "分析过程包括：\n"
    output += "1. 获取各公众号最新研究报告\n"
    output += "2. 对每篇报告进行结构化分析，提取核心观点和依据\n"
    output += "3. 综合分析所有报告，识别共识观点和分歧点\n"
    output += "4. 按照宏观经济、政策预期、利率走势和投资建议等维度进行归纳\n\n"

    # 数据来源说明
    output += "### 数据来源说明\n\n"
    output += f"- 数据时间范围: {report['metadata']['time_range']}\n"
    output += f"- 报告生成时间: {report['generation_time']}\n"
    output += f"- 分析机构列表: {', '.join([report_data.get('site_name', '未知') for biz, report_data in report['individual_reports'].items() if 'analysis' in report_data])}\n\n"

    # 执行情况
    output += "### 执行情况\n\n"
    errors = report.get("errors", [])
    if errors:
        output += "本次报告生成过程中出现如下异常：\n"
        for err in errors:
            output += f"- {err}\n"
    else:
        output += "本次报告生成过程无异常。\n"

    end_time = datetime.now()
    total_elapsed_time = (end_time - start_time).total_seconds()
    logger.info(f"STORM报告格式化完成，总耗时: {total_elapsed_time:.2f} 秒")

    # 添加章节编号
    output = add_heading_numbering(output)
    return output
