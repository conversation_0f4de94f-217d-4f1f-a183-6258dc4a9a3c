"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2437],{76487:function(e,t,r){r.r(t);var n=r(15009),a=r.n(n),u=r(97857),c=r.n(u),s=r(99289),i=r.n(s),o=r(5574),l=r.n(o),p=r(67294),d=r(97131),f=r(12453),v=r(2453),x=r(17788),h=r(83622),m=r(51042),y=r(69044),k=r(23544),b=r(85893);t.default=function(){var e=(0,p.useState)(!1),t=l()(e,2),r=t[0],n=t[1],u=(0,p.useState)(!1),s=l()(u,2),o=s[0],w=s[1],Z=(0,p.useState)(void 0),P=l()(Z,2),j=P[0],g=P[1],T=(0,p.useRef)(),S=function(){var e=i()(a()().mark((function e(t){var r,u;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=v.ZP.loading("正在添加"),e.prev=1,e.next=4,(0,y.Rp)(c()({},t));case 4:return r(),v.ZP.success("添加成功"),n(!1),null===(u=T.current)||void 0===u||u.reload(),e.abrupt("return",!0);case 11:return e.prev=11,e.t0=e.catch(1),r(),v.ZP.error("添加失败，请重试"),e.abrupt("return",!1);case 16:case"end":return e.stop()}}),e,null,[[1,11]])})));return function(t){return e.apply(this,arguments)}}(),C=function(){var e=i()(a()().mark((function e(t){var r,n;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=v.ZP.loading("正在更新"),e.prev=1,e.next=4,(0,y.mD)(t);case 4:return r(),v.ZP.success("更新成功"),w(!1),g(void 0),null===(n=T.current)||void 0===n||n.reload(),e.abrupt("return",!0);case 12:return e.prev=12,e.t0=e.catch(1),r(),v.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 17:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(t){return e.apply(this,arguments)}}(),_=function(){var e=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:x.Z.confirm({title:"确认删除",content:"确定要删除这个组吗？",okText:"确认",cancelText:"取消",onOk:function(){var e=i()(a()().mark((function e(){var r,n;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=v.ZP.loading("正在删除"),e.prev=1,e.next=4,(0,y.iE)(t.id);case 4:return r(),v.ZP.success("删除成功"),null===(n=T.current)||void 0===n||n.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),r(),v.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),I=[{title:"组名",dataIndex:"name",valueType:"text"},{title:"描述",dataIndex:"description",valueType:"text"},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"是否可删除",dataIndex:"deletable",valueType:"select",valueEnum:{all:{text:"全部",status:"Default"},1:{text:"是",status:"Success"},0:{text:"否",status:"Error"}},render:function(e){return(0,b.jsx)("span",{style:{color:e?"green":"red"},children:e?"可删除":"不可删除"})},search:!1},{title:"操作",dataIndex:"option",valueType:"option",render:function(e,t){return[(0,b.jsx)(h.ZP,{type:"link",onClick:function(){w(!0),g(t)},children:"编辑"}),(0,b.jsx)(h.ZP,{type:"link",danger:!0,disabled:!t.deletable,onClick:function(){return _(t)},children:"删除"})]}}];return(0,b.jsxs)(d._z,{children:[(0,b.jsx)(f.Z,{headerTitle:"组管理",rowKey:"id",search:{labelWidth:120},toolBarRender:function(){return[(0,b.jsxs)(h.ZP,{type:"primary",onClick:function(){n(!0)},children:[(0,b.jsx)(m.Z,{})," 新建"]},"primary")]},request:function(){var e=i()(a()().mark((function e(t){var r;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,y.jA)(c()({current:t.current,pageSize:t.pageSize},t));case 2:return r=e.sent,e.abrupt("return",{data:r.data,success:r.success,total:r.total});case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),columns:I}),(0,b.jsx)(k.Z,{onSubmit:function(){var e=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,S(t);case 2:e.sent&&n(!1);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),onCancel:function(){n(!1)},modalVisible:r}),j&&(0,b.jsx)(k.Z,{onSubmit:function(){var e=i()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,C(t);case 2:e.sent&&(w(!1),g(void 0));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),onCancel:function(){w(!1),g(void 0)},modalVisible:o,values:j})]})}}}]);