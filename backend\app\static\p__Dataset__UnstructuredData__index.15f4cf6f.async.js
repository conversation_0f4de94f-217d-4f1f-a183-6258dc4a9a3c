"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3908],{50746:function(t,e,n){n.r(e);var r=n(15009),a=n.n(r),u=n(99289),i=n.n(u),c=(n(67294),n(97131)),s=n(12453),d=n(83622),o=n(35312),l=n(85893);e.default=function(){var t=[{title:"文件名称",dataIndex:"name",render:function(t,e){return(0,l.jsx)("a",{onClick:function(){return o.history.push("/dataset/unstructured/view?id=".concat(e.id))},children:e.name})}},{title:"文件类型",dataIndex:"file_type"},{title:"文件大小",dataIndex:"file_size",valueType:"digit",fieldProps:{formatter:function(t){return"".concat((t/1024/1024).toFixed(2)," MB")}}},{title:"上传时间",dataIndex:"created_at",valueType:"dateTime"},{title:"状态",dataIndex:"status",valueEnum:{active:{text:"活跃",status:"Success"},inactive:{text:"非活跃",status:"Default"}}},{title:"操作",valueType:"option",render:function(t,e){return[(0,l.jsx)("a",{onClick:function(){return o.history.push("/dataset/unstructured/view?id=".concat(e.id))},children:"查看"},"view")]}}];return(0,l.jsx)(c._z,{children:(0,l.jsx)(s.Z,{columns:t,request:function(){var t=i()(a()().mark((function t(e){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",{data:[],success:!0,total:0});case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),rowKey:"id",toolBarRender:function(){return[(0,l.jsx)(d.ZP,{type:"primary",onClick:function(){return o.history.push("/dataset/unstructured/new")},children:"新建非结构化数据"},"create")]}})})}}}]);