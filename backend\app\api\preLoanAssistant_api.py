from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, List
from .auth import verify_api_token
from ..db.mongodb import db
import asyncio
from datetime import datetime
from ..service.loan_analysis_service import analyze_enterprise_stream
from pydantic import BaseModel
from fastapi.responses import StreamingResponse
from app.utils.logging_config import setup_logging, get_logger
# 设置日志配置
setup_logging()
logger = get_logger(__name__)


router = APIRouter(
    prefix="/api/v1",
    tags=["loan_analysis"]
)

class LoanAnalysisRequest(BaseModel):
    enterpriseId: str
    dimensions: List[str] = []
    executionMode: str = "SYNC"
    outputFormat: str = "MARKDOWN"
    stream: bool = False
    context: Dict[str, Any] = {}

@router.post("/loan-analysis", response_model=Dict[str, Any])
async def analyze_loan(
    request: LoanAnalysisRequest,
    token_info: dict = Depends(verify_api_token)
):
    # logger.info(f"executionMode: {request.executionMode}")
    # logger.info(f"outputFormat: {request.outputFormat}")
    # logger.info(f"dimensions: {request.dimensions}")
    # logger.info(f"context: {request.context}")
    try:
        # # 验证权限
        # if token_info.get("permissions") not in ["write", "admin"]:
        #     raise HTTPException(
        #         status_code=403,
        #         detail="Insufficient permissions"
        #     )

        # 验证执行模式
        if request.executionMode != "SYNC":
            raise HTTPException(
                status_code=400,
                detail="Only synchronous mode is supported"
            )

        # 验证输出格式
        if request.outputFormat != "MARKDOWN":
            raise HTTPException(
                status_code=400,
                detail="Only MARKDOWN format is supported"
            )

        # 记录调用日志
        await db["api_calls"].insert_one({
            "api": "loan-analysis",
            "app_id": token_info["app_id"],
            "timestamp": datetime.now(),
            "request_data": request.dict(),
            "status": "processing"
        })
        logger.info(f"request: {request}")

        # 调用分析服务
        if request.stream:
            async def stream_response():
                ai_message_content = ""
                async for chunk in analyze_enterprise_stream(
                    enterprise_id=request.enterpriseId,
                    dimensions=request.dimensions,
                    context=request.context
                ):
                    ai_message_content += chunk
                    yield chunk
                    await asyncio.sleep(0)
                yield "event: end\ndata: Stream has ended\n\n"
                    
            return StreamingResponse(stream_response(), media_type='text/event-stream')
        
        else:
            pass
            # # 同步模式处理
            # result = {}
            # for dimension in request.dimensions:
            #     dimension_result = await analyze_dimension(
            #         enterprise_id=request.enterpriseId,
            #         dimension=dimension,
            #         context=request.context
            #     )
            #     result[dimension] = dimension_result

            # return {
            #     "code": 200,
            #     "message": "success",
            #     "data": result
            # }

    except Exception as e:
        # 记录错误日志
        await db["api_calls"].update_one(
            {"api": "loan-analysis", "app_id": token_info["app_id"]},
            {
                "$set": {
                    "status": "failed",
                    "error": str(e)
                }
            }
        )
        raise HTTPException(status_code=500, detail=str(e)) 