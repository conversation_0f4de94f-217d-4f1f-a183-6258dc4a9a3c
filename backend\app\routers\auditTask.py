from fastapi import APIRouter, HTTPException, Depends, Query, Body, Path, File, Form,UploadFile
from typing import List, Optional, Dict, Any
from ..models.embedding import EmbeddingModel, EmbeddingCreate, EmbeddingUpdate, EmbeddingResponse
from ..models.auditTaskRule import AuditTaskRule, AuditTaskRuleCreate, AuditTaskRuleUpdate, AuditTaskRuleResponse, AuditTaskRuleListResponse
from ..models.auditTaskType import AuditTaskType, AuditTaskTypeCreate, AuditTaskTypeUpdate
from ..db.mongodb import db
from datetime import datetime
from ..utils.auth import verify_token
from bson import ObjectId
import json
from ..db.miniIO import minio
from ..utils.config import settings
import os
import uuid

import traceback
from app.utils.logging_config import setup_logging, get_logger
from ..models.auditTask import AuditTaskCreate, AuditTaskUpdate, AuditTask

setup_logging()
logger = get_logger(__name__)


router = APIRouter(
    prefix="/api",
    tags=["auditTask"]
)

#######Rule 规则管理########

@router.get("/auditTask/audit-rules", response_model=Dict[str, Any])
async def get_rules(
    skip: int = 0, 
    limit: int = 100, 
    current_user: dict = Depends(verify_token)
):
    """
    获取所有规则列表
    """
    try:
        query = {"is_deleted": False, "user_id": current_user["id"]}
        rules = await db["audit_task_rules"].find(query).skip(skip).limit(limit).to_list(limit)
        
        # 转换ObjectId为字符串，以便JSON序列化
        for rule in rules:
            rule["id"] = str(rule["_id"])
            del rule["_id"]
            
        return {
            "success": True,
            "data": rules,
            "total": await db["audit_task_rules"].count_documents(query)
        }
    except Exception as e:
        logger.error(f"获取规则列表失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取规则列表失败: {str(e)}")

@router.get("/auditTask/audit-rules/{rule_id}", response_model=Dict[str, Any])
async def get_rule(
    rule_id: str = Path(..., description="规则ID"),
    current_user: dict = Depends(verify_token)
):
    """
    根据ID获取规则详情
    """
    try:
        rule = await db["audit_task_rules"].find_one({"_id": ObjectId(rule_id), "is_deleted": False})
        if not rule:
            return {"success": False, "error": "规则不存在"}
        
        rule["id"] = str(rule["_id"])
        return {"success": True, "data": rule}
    except Exception as e:
        logger.error(f"获取规则详情失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取规则详情失败: {str(e)}")

@router.post("/auditTask/audit-rules", response_model=Dict[str, Any])
async def create_rule(
    rule: AuditTaskRuleCreate = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    创建新规则
    """
    try:
        rule_dict = rule.dict()
        rule_dict["user_id"] = current_user["id"]
        rule_dict["user_name"] = current_user.get("name", "")
        rule_dict["_id"] = ObjectId()
        rule_dict["created_at"] = datetime.now()
        rule_dict["is_deleted"] = False

        logger.info(rule_dict)
        
        result = await db["audit_task_rules"].insert_one(rule_dict)
        if result.inserted_id:
            rule_dict['id'] = str(rule_dict["_id"])
            del rule_dict["_id"]
            return {"success": True, "data": rule_dict, "message": "规则创建成功"}
        else:
            return {"success": False, "error": "规则创建失败"}
    except Exception as e:
        logger.error(f"创建规则失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"创建规则失败: {str(e)}")

@router.put("/auditTask/audit-rules/{rule_id}", response_model=Dict[str, Any])
async def update_rule(
    rule_id: str = Path(..., description="规则ID"),
    rule: AuditTaskRuleUpdate = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    更新规则
    """
    try:
        # 检查规则是否存在
        existing_rule = await db["audit_task_rules"].find_one({"_id": ObjectId(rule_id), "is_deleted": False})
        if not existing_rule:
            return {"success": False, "error": "规则不存在"}
        
        # 更新数据
        update_data = {k: v for k, v in rule.dict(exclude_unset=True).items() if v is not None}
        if update_data:
            result = await db["audit_task_rules"].update_one(
                {"_id": ObjectId(rule_id)},
                {"$set": update_data}
            )
            if result.modified_count:
                return {"success": True, "message": "规则更新成功"}
            
        return {"success": True, "message": "无数据更新"}
    except Exception as e:
        logger.error(f"更新规则失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"更新规则失败: {str(e)}")

@router.delete("/auditTask/audit-rules/{rule_id}", response_model=Dict[str, Any])
async def delete_rule(
    rule_id: str = Path(..., description="规则ID"),
    current_user: dict = Depends(verify_token)
):
    """
    删除规则（软删除）
    """
    try:
        # 检查规则是否存在
        existing_rule = await db["audit_task_rules"].find_one({"_id": ObjectId(rule_id), "is_deleted": False})
        if not existing_rule:
            return {"success": False, "error": "规则不存在"}
        
        # 软删除
        result = await db["audit_task_rules"].update_one(
            {"_id": ObjectId(rule_id)},
            {"$set": {"is_deleted": True}}
        )
        if result.modified_count:
            return {"success": True, "message": "规则删除成功"}
        else:
            return {"success": False, "error": "规则删除失败"}
    except Exception as e:
        logger.error(f"删除规则失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"删除规则失败: {str(e)}")

@router.get("/auditTask/audit-task-types/{type_id}/rules", response_model=Dict[str, Any])
async def get_rules_by_type(
    type_id: str = Path(..., description="任务类型ID"),
    current_user: dict = Depends(verify_token)
):
    """
    获取指定任务类型下的所有规则
    """
    try:
        # 先获取任务类型
        task_type = await db["audit_task_types"].find_one({"_id": ObjectId(type_id), "is_deleted": False})
        if not task_type:
            return {"success": False, "error": "任务类型不存在"}
        
        # 获取任务类型关联的规则IDs
        rule_ids = task_type.get("rules", [])
        
        # 查询规则
        if rule_ids:
            # 将字符串ID转换为ObjectId
            object_rule_ids = [ObjectId(rule_id) for rule_id in rule_ids]
            rules = await db["audit_task_rules"].find({
                "_id": {"$in": object_rule_ids}, 
                "is_deleted": False
            }).to_list(length=100)
            
            # 转换ObjectId为字符串
            for rule in rules:
                rule["id"] = str(rule["_id"])
                del rule["_id"]
                rule["task_type_id"] = type_id
                
            return {"success": True, "data": rules}
        else:
            return {"success": True, "data": []}
    except Exception as e:
        logger.info(f"获取任务类型规则失败: {str(e)}")
        logger.info(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取任务类型规则失败: {str(e)}")

@router.put("/auditTask/audit-task-types/{type_id}/rules", response_model=Dict[str, Any])
async def add_rule_to_type(
    type_id: str = Path(..., description="任务类型ID"),
    data: Dict[str, str] = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    将规则添加到任务类型
    """
    logger.info(type_id)
    logger.info(data)
    try:
        rule_id = data.get("rule_id")
        if not rule_id:
            return {"success": False, "error": "a。缺少规则ID"}
        
        # 检查规则是否存在
        rule = await db["audit_task_rules"].find_one({"_id": ObjectId(rule_id), "is_deleted": False})
        if not rule:
            return {"success": False, "error": "b.规则不存在"}
        
        # 检查任务类型是否存在
        task_type = await db["audit_task_types"].find_one({"_id": ObjectId(type_id), "is_deleted": False})
        if not task_type:
            return {"success": False, "error": "c.任务类型不存在"}
        
        # 添加规则到任务类型
        rules = task_type.get("rules", [])
        if rule_id not in rules:
            rules.append(rule_id)
            
        # 更新任务类型
        result = await db["audit_task_types"].update_one(
            {"_id": ObjectId(type_id)},
            {"$set": {"rules": rules}}
        )
        logger.info(result)
        
        if result.modified_count:
            return {"success": True, "message": "1:规则添加成功"}
        else:
            return {"success": False, "error": "2规则添加失败"}
     
    except Exception as e:
        logger.info(f"添加规则到任务类型失败: {str(e)}")
        logger.info(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"添加规则到任务类型失败: {str(e)}")

@router.delete("/auditTask/audit-task-types/{type_id}/rules/{rule_id}", response_model=Dict[str, Any])
async def remove_rule_from_type(
    type_id: str = Path(..., description="任务类型ID"),
    rule_id: str = Path(..., description="规则ID"),
    current_user: dict = Depends(verify_token)
):
    """
    从任务类型中移除规则
    """
    try:
        # 检查任务类型是否存在
        task_type = await db["audit_task_types"].find_one({"_id": ObjectId(type_id), "is_deleted": False})
        if not task_type:
            return {"success": False, "error": "任务类型不存在"}
        
        # 移除规则
        rules = task_type.get("rules", [])
        if rule_id in rules:
            rules.remove(rule_id)
            
            # 更新任务类型
            result = await db["audit_task_types"].update_one(
                {"_id": ObjectId(type_id)},
                {"$set": {"rules": rules}}
            )
            
            if result.modified_count:
                return {"success": True, "message": "规则移除成功"}
            else:
                return {"success": False, "error": "规则移除失败"}
        else:
            return {"success": True, "message": "规则不在任务类型中"}
    except Exception as e:
        logger.error(f"从任务类型中移除规则失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"从任务类型中移除规则失败: {str(e)}")

#######Type 类型管理########
@router.get("/auditTask/audit-task-types", response_model=Dict[str, Any])
async def get_task_types(
    skip: int = 0, 
    limit: int = 100, 
    current_user: dict = Depends(verify_token)
):
    """
    获取所有任务类型列表
    """
    try:
        query = {"is_deleted": False, "user_id": current_user["id"]}
        task_types = await db["audit_task_types"].find(query,{
            "code":1,
            "created_at":1,
            "name":1,
            "rules":1,
            "description":1,
            "is_active":1
        }).skip(skip).limit(limit).to_list(limit)
        
        # 转换ObjectId为字符串，添加规则数量
        for task_type in task_types:
            task_type["id"] = str(task_type["_id"])
            del task_type["_id"]
            task_type["rules_count"] = len(task_type.get("rules", []))
            
        return {
            "success": True,
            "data": task_types,
            # "total": await db["audit_task_types"].count_documents(query)
        }
    except Exception as e:
        logger.error(f"获取任务类型列表失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取任务类型列表失败: {str(e)}")

@router.post("/auditTask/audit-task-types", response_model=Dict[str, Any])
async def create_task_type(
    task_type: AuditTaskTypeCreate = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    创建新任务类型
    """
    try:
        logger.info(task_type)
        # 检查code是否已存在
        existing = await db["audit_task_types"].find_one({"code": task_type.code, "is_deleted": False})
        logger.info(existing)
        if existing:
            return {"success": False, "error": "任务类型代码已存在"}
        
        task_type_dict = task_type.dict()
        task_type_dict["user_id"] = current_user["id"]
        task_type_dict["_id"] = ObjectId()
        task_type_dict["created_at"] = datetime.now()
        task_type_dict["updated_at"] = datetime.now()
        task_type_dict["is_deleted"] = False

        logger.info(task_type_dict)
        
        result = await db["audit_task_types"].insert_one(task_type_dict)
        if result.inserted_id:
            task_type_dict["id"] = str(task_type_dict["_id"])
            del task_type_dict["_id"]
            return {"success": True, "data": task_type_dict, "message": "任务类型创建成功"}
        else:
            return {"success": False, "error": "任务类型创建失败"}
    except Exception as e:
        traceback.format_exc()
        logger.error(f"创建任务类型失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"创建任务类型失败: {str(e)}")

@router.put("/auditTask/audit-task-types/{type_id}", response_model=Dict[str, Any])
async def update_task_type(
    type_id: str = Path(..., description="任务类型ID"),
    task_type: AuditTaskTypeUpdate = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    更新任务类型
    """
    try:
        # 检查任务类型是否存在
        existing_type = await db["audit_task_types"].find_one({"_id": ObjectId(type_id), "is_deleted": False})
        if not existing_type:
            return {"success": False, "error": "任务类型不存在"}
        
        # 更新数据
        update_data = {k: v for k, v in task_type.dict(exclude_unset=True).items() if v is not None}
        if update_data:
            update_data["updated_at"] = datetime.now()
            result = await db["audit_task_types"].update_one(
                {"_id": ObjectId(type_id)},
                {"$set": update_data}
            )
            if result.modified_count:
                return {"success": True, "message": "任务类型更新成功"}
            
        return {"success": True, "message": "无数据更新"}
    except Exception as e:
        logger.error(f"更新任务类型失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"更新任务类型失败: {str(e)}")

@router.delete("/auditTask/audit-task-types/{type_id}", response_model=Dict[str, Any])
async def delete_task_type(
    type_id: str = Path(..., description="任务类型ID"),
    current_user: dict = Depends(verify_token)
):
    """
    删除任务类型（软删除）
    """
    try:
        # 检查任务类型是否存在
        existing_type = await db["audit_task_types"].find_one({"_id": ObjectId(type_id), "is_deleted": False})
        if not existing_type:
            return {"success": False, "error": "任务类型不存在"}
        
        # 软删除
        result = await db["audit_task_types"].delete_one(
            {"_id": ObjectId(type_id)}
        )
        
        if result.deleted_count:
            return {"success": True, "message": "任务类型删除成功"}
        else:
            return {"success": False, "error": "任务类型删除失败"}
    except Exception as e:
        logger.error(f"删除任务类型失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"删除任务类型失败: {str(e)}")

#######Task 任务管理########
@router.get("/auditTask/audit-tasks", response_model=Dict[str, Any])
async def get_tasks(
    skip: int = 0, 
    limit: int = 100,
    status: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    """
    获取所有审核任务列表
    """
    try:
        query = {"is_deleted": False, "user_id": int(current_user["id"])}
        
        # 如果提供了状态筛选条件
        if status:
            query["status"] = status


        logger.info(query)
            
        tasks = await db["audit_tasks"].find(query).sort("created_at", -1).skip(skip).limit(limit).to_list(limit)
        
        # 转换ObjectId为字符串
        for task in tasks:
            task["id"] = str(task["_id"])
            del task["_id"]
            
        return {
            "success": True,
            "data": tasks,
            "total": await db["audit_tasks"].count_documents(query)
        }
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")

@router.get("/auditTask/audit-tasks/{task_id}", response_model=Dict[str, Any])
async def get_task(
    task_id: str = Path(..., description="任务ID"),
    current_user: dict = Depends(verify_token)
):
    """
    根据ID获取任务详情
    """
    try:
        task = await db["audit_tasks"].find_one({"_id": ObjectId(task_id), "is_deleted": False})
        if not task:
            return {"success": False, "error": "任务不存在"}
        
        # 检查是否是当前用户的任务
        if task["user_id"] != current_user["id"]:
            return {"success": False, "error": "无权访问此任务"}
            
        task["id"] = str(task["_id"])
        del task["_id"]
        return {"success": True, "data": task}
    except Exception as e:
        logger.error(f"获取任务详情失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取任务详情失败: {str(e)}")

@router.post("/auditTask/audit-tasks", response_model=Dict[str, Any])
async def create_task(
    task: AuditTaskCreate = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    创建新审核任务
    """
    try:
        # 检查任务类型是否存在
        task_type = await db["audit_task_types"].find_one({"code": task.type, "is_deleted": False})
        if not task_type:
            return {"success": False, "error": "指定的任务类型不存在"}
        
        # 创建任务
        task_dict = task.dict()
        task_dict["user_id"] = current_user["id"]
        task_dict["user_name"] = current_user.get("name", "")
        task_dict["_id"] = ObjectId()
        task_dict["created_at"] = datetime.now()
        task_dict["updated_at"] = datetime.now()
        task_dict["status"] = "pending_upload"  # 初始状态：待上传文件
        task_dict["file_count"] = 0
        task_dict["file_ids"] = []
        task_dict["result_data"] = []
        task_dict["is_deleted"] = False
        
        result = await db["audit_tasks"].insert_one(task_dict)
        if result.inserted_id:
            task_dict["id"] = str(task_dict["_id"])
            del task_dict["_id"]
            return {"success": True, "data": task_dict, "message": "任务创建成功"}
        else:
            return {"success": False, "error": "任务创建失败"}
    except Exception as e:
        logger.error(f"创建任务失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")

@router.put("/auditTask/audit-tasks/{task_id}", response_model=Dict[str, Any])
async def update_task(
    task_id: str = Path(..., description="任务ID"),
    task: AuditTaskUpdate = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    更新审核任务
    """
    try:
        # 检查任务是否存在
        existing_task = await db["audit_tasks"].find_one({"_id":ObjectId(task_id), "is_deleted": False})
        if not existing_task:
            return {"success": False, "error": "任务不存在"}
        
        # 检查是否是当前用户的任务
        if existing_task["user_id"] != current_user["id"]:
            return {"success": False, "error": "无权更新此任务"}
        
        # 更新数据
        update_data = {k: v for k, v in task.dict(exclude_unset=True).items() if v is not None}
        if update_data:
            update_data["updated_at"] = datetime.now()
            result = await db["audit_tasks"].update_one(
                {"_id": ObjectId(task_id)},
                {"$set": update_data}
            )
            if result.modified_count:
                return {"success": True, "message": "任务更新成功"}
            
        return {"success": True, "message": "无数据更新"}
    except Exception as e:
        logger.error(f"更新任务失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"更新任务失败: {str(e)}")

@router.delete("/auditTask/audit-tasks/{task_id}", response_model=Dict[str, Any])
async def delete_task(
    task_id: str = Path(..., description="任务ID"),
    current_user: dict = Depends(verify_token)
):
    """
    删除审核任务（软删除）
    """
    try:

        # 检查任务是否存在
        existing_task = await db["audit_tasks"].find_one({"_id": ObjectId(task_id), "is_deleted": False})
        if not existing_task:
            return {"success": False, "error": "任务不存在"}
        
        # 检查是否是当前用户的任务
        if existing_task["user_id"] != current_user["id"]:
            return {"success": False, "error": "无权删除此任务"}
        
        # 软删除
        result = await db["audit_tasks"].update_one(
            {"_id": ObjectId(task_id)},
            {"$set": {"is_deleted": True, "updated_at": datetime.now()}}
        )
        
        if result.modified_count:
            return {"success": True, "message": "任务删除成功"}
        else:
            return {"success": False, "error": "任务删除失败"}
    except Exception as e:
        logger.error(f"删除任务失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"删除任务失败: {str(e)}")

@router.get("/auditTask/audit-tasks/{task_id}/files", response_model=Dict[str, Any])
async def get_task_files(
    task_id: str = Path(..., description="任务ID"),
    current_user: dict = Depends(verify_token)
):
    """
    获取任务关联的文件列表
    """
    try:
        # 检查任务是否存在
        task = await db["audit_tasks"].find_one({"_id": ObjectId(task_id), "is_deleted": False})
        logger.info(task)
        if not task:
            return {"success": False, "error": "任务不存在"}
        
        # 检查是否是当前用户的任务
        if task["user_id"] != current_user["id"]:
            return {"success": False, "error": "无权访问此任务"}
        # 查询文件
        files = await db["audit_task_files"].find({
            "task_id": ObjectId(task_id), 
            "is_deleted": False
        },{
            "name":1,
            "data_type":1,
            "size" : 318577,
    "status" : "pending"
        }).to_list()
        logger.info(files)
        
        # 转换ObjectId为字符串
        for file in files:
            file["id"] = str(file["_id"])
            del file["_id"]
        
        return {"success": True, "data": files}
    except Exception as e:
        logger.error(f"获取任务文件列表失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取任务文件列表失败: {str(e)}")


def get_file_format(filename: str) -> str:
    """根据文件后缀返回文件格式"""
    extension = os.path.splitext(filename)[1].lower()
    format_map = {
        '.csv': 'CSV',
        '.xls': 'Excel',
        '.xlsx': 'Excel',
        '.json': 'JSON',
        '.jsonl': 'JSONL',
        '.txt': 'TXT',
        '.pdf': 'PDF',
        '.doc': 'Word',
        '.docx': 'Word',
    }
    return format_map.get(extension, 'Unknown')



@router.post("/auditTask/upload_task_file", response_model=Dict[str, Any])
async def upload_task_file(
    files: List[UploadFile] = File(...),
    taskId: str = Form(...),
    current_user: dict = Depends(verify_token)
):
    """
    上传文件到任务
    """
    try:
        # 检查任务是否存在
        task = await db["audit_tasks"].find_one({"_id": ObjectId(taskId), "is_deleted": False})
        if not task:
            return {"success": False, "error": "任务不存在"}
        
        # 检查是否是当前用户的任务
        if task["user_id"] != current_user["id"]:
            return {"success": False, "error": "无权操作此任务"}
        
        # 检查任务状态，只有待上传状态才能添加文件
        if task["status"] not in ["pending_upload", "analyzing"]:
            return {"success": False, "error": f"当前任务状态({task['status']})不允许上传文件"}
        
        count = 0
        for file in files:
            content = await file.read()
            file_size = len(content)
            file_ext = os.path.splitext(file.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_ext}"
            try:

                if settings.MINIO_ENABLE:
                    # 上传到MinIO
                    logger.info(f"MiniO 存储:{unique_filename}")
                    file_url = minio.upload_bytes(
                        data=content,
                        object_name=unique_filename,
                        content_type=file.content_type
                    )
                else:
                    logger.info(f"文件上传到本地: {settings.FILE_UPLOAD_PATH}/{unique_filename}")
                    file_url = f"{settings.FILE_UPLOAD_PATH}/{unique_filename}"
                    with open(file_url, "wb") as f:
                        f.write(content)
            except Exception as e:
                
                traceback.print_exc()
                logger.error(f"上传文件失败: {str(e)}")
                continue
            
            file_format = get_file_format(file.filename)
            
            new_file = {
                "_id": ObjectId(),
                "name": file.filename,
                "task_id": ObjectId(taskId),
                "storage_path": file_url,
                "storage_type": "MINIO" if settings.MINIO_ENABLE else "LOCAL",
                "data_type": file_format,
                "size": file_size,
                "status": "pending",
                "uploaded_at": datetime.now(),
                "processed_at": None,
                "user_id": current_user["id"],
                "user_name": current_user["name"],
                "is_deleted": False,
                "url": file_url
            }
            logger.info(f"new_file: {new_file}")
            
            await db["audit_task_files"].insert_one(new_file)
            count +=1
            # 更新任务文件列表和计数

            
        # 从audit_task_files查询与任务关联的所有文件
        task_files = await db["audit_task_files"].find(
            {"task_id": ObjectId(taskId), "is_deleted": False}
        ).to_list(length=None)
        
        # 获取所有文件ID
        file_ids = [str(file["_id"]) for file in task_files]
        file_count = len(file_ids)
        
        # 更新任务信息
        update_result = await db["audit_tasks"].update_one(
            {"_id": ObjectId(taskId)},
            {
                "$set": {
                    "file_ids": file_ids,
                    "file_count": file_count,
                    "status": "analyzing",  # 更新任务状态为分析中
                    "updated_at": datetime.now()
                }
            }
        )
        logger.info(update_result.modified_count)
        
        if update_result.modified_count:
            return {"success": True,"message":f"文件上传成功：{count}"}
        else:
            logger.error("更新任务文件列表失败")
            return {"success": False, "error": "更新任务文件列表失败"}

    except Exception as e:
        logger.error(f"上传文件失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"上传文件失败: {str(e)}")