"""
启动图谱API服务的脚本
自动设置Python路径并启动服务
"""

import sys
import os
from pathlib import Path
import uvicorn
from graph_api import app
# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入并启动API
if __name__ == "__main__":
    
    print("🚀 启动知识图谱可视化API服务...")
    print(f"📁 工作目录: {current_dir}")
    print("🌐 访问地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
