############################### 线上服务 #################################
# # minio配置
# upload_to_minio = False  # 是否上传至MinIO
# minio_endpoint = '122.14.231.158:9002'  # MinIO服务器地址
# minio_access_key = 'gP3HFQQXuTKQFyI5s9dQ'  # MinIO访问密钥
# minio_secret_key = 'gyI9eAf1xWfLpQGkqKYhksDCJdRvQgl1egw3ZLsp'  # MinIO秘密密钥
# source_bucket_name = 'dataset'  # MinIO存储桶名称
# image_bucket_name = 'image'
# object_name_prefix = 'yanbao/'  # MinIO存储文件夹

# # 文档解析配置
# title_length_thres = 70  # 判定为不是标题的字符数阈值
# detect_model_choose = 'report'  # 版式分析模型选择（ paper 或 report ）
# detect_model_threshold = 0.2  # 版式分析模型输出置信度阈值
# ocr_bounding_box_x_dimension_widen = 15  # 将文本区域裁剪给ocr工具时 X 轴拓宽的值的1/2（以防止漏识别）
# ocr_bounding_box_y_dimension_widen = 10  # 将文本区域裁剪给ocr工具时 Y 轴拓宽的值的1/2（以防止漏识别）

# # 文本分块配置
# text_block_chunk_size = 700  # 每个‘index’的最大字符数

# # 数据上传知识库相关配置
# upload_to_database = False  # 是否上传至知识库
# username = "18888888888"
# api_base = "https://wiserag.roardata.cn/api"
# api_key = "fastgpt-pp25Nl6eVo4wFTVQS7lQB1hRhyTRDi8P5pVz4GvEbdTpW8AAMU3Ktk90Z7QVFoLI"
# # 合规规则库
# dataset_id = "66f90f07c2c3c13b697a3fcc"
# dataseset_name = "合规规则库"


# # mongodb配置
# user_name = 'myname'
# user_passwd = 'mypassword'
# db_ip_port = "**************:27018"
# db_name = 'fastgpt'
# collection_name = 'dataset.collections'



############################### 电信租赁 #################################
# minio配置
upload_to_minio = True  # 是否上传至MinIO
minio_endpoint = '*************:9003'  # MinIO服务器地址
minio_access_key = 'dJtp9QvzCzCii5SEf36h'  # MinIO访问密钥
minio_secret_key = 'u760vrBLw4xoqtFit4gSDdOLFZkYgwRFtmDGLLw5'  # MinIO秘密密钥
source_bucket_name = 'dataset'  # MinIO存储桶名称
image_bucket_name = 'image'
object_name_prefix = 'tele_lease_v2/'  # MinIO存储文件夹

# 文档解析配置
title_length_thres = 70  # 判定为不是标题的字符数阈值
detect_model_choose = 'report'  # 版式分析模型选择（ paper 或 report ）
detect_model_threshold = 0.5  # 版式分析模型输出置信度阈值
ocr_bounding_box_x_dimension_widen = 15  # 将文本区域裁剪给ocr工具时 X 轴拓宽的值的1/2（以防止漏识别）
ocr_bounding_box_y_dimension_widen = 10  # 将文本区域裁剪给ocr工具时 Y 轴拓宽的值的1/2（以防止漏识别）

# 文本分块配置
text_block_chunk_size = 700  # 每个‘index’的最大字符数

# 数据上传知识库相关配置
upload_to_database = True  # 是否上传至知识库
username = "18888888888"
# 电信租赁线上环境
# api_base = "http://*************:3005/api"
api_base = "http://*************:3006/api"
# api_key = "wisegpt-GUXeQL8tDuehcYgopLLa9aNeI6soV8s3Fm3mXgHMhBUciuInIEYoUQZDWb5W9zgK"
api_key = "wisegpt-6ROVcSD387HI7eVvYL7Le8A30Lh2O8Z1UMKXhh1Qa7QHdGxIhvVfIvxuQQtWE"
# 贺青雯本地测试
# api_base = "http://192.168.0.55:3000/api"
# api_key = "fastgpt-uC7QGUicXltgreuLzXEnWZbaODsUP3ebEnCHTaE8eu36Qb63Dd3hS4T9kg9Tvag"
# 综合类资料知识库
# dataset_id = "6736ae363d02d02e4be35d31"
# dataseset_name = "综合类资料知识库"
# 财务类
# dataset_id = "66dfe1733e245a1e72474a46"
# dataseset_name = "财务类知识库"
# 业务类
# dataset_id = "66dfe1a03e245a1e72474a9b"
# dataseset_name = "业务类知识库"
# 风控类
# dataset_id = "66dfe18a3e245a1e72474a6f"
# dataseset_name = "风控类知识库"
# 制度类
dataset_id = "66dfe1ba3e245a1e72474ac6"
dataseset_name = "制度类知识库"
# 1-党务类-综合部
# parent_id = "673ada693d02d02e4be3dc34"
# parent_name = "1-党务类-综合部"
# # 2-工会类-综合部
# parent_id = "673ada7b3d02d02e4be3dc57"
# parent_name = "2-工会类-综合部"
# # 3-公司治理类-风险部、综合部
# parent_id = "673ada983d02d02e4be3dc9a"
# parent_name = "3-公司治理类-风险部、综合部"
# # 4-业务类-业务部
# parent_id = "673adab13d02d02e4be3dcc7"
# parent_name = "4-业务类-业务部"
# # 5-融资类-融资部
# parent_id = "673adac43d02d02e4be3dcdb"
# parent_name = "5-融资类-融资部"
# # 6-风险类-风险部
# parent_id = "673adada3d02d02e4be3dcef"
# parent_name = "6-风险类-风险部"
# # 7-综合类-综合部
parent_id = "673adaf03d02d02e4be3dd12"
parent_name = "7-综合类-综合部"


# 贺青雯本地测试
# dataset_id = "6720a5f0217502e07d252b19"
# dataseset_name = "贺青雯"


# mongodb配置
user_name = 'myusername'
user_passwd = 'mypassword'
db_ip_port = "*************:27017"
db_name = 'wisegpt'
collection_name = 'dataset.collections'
# 贺青雯测试
# user_name = 'myname'
# user_passwd = 'mypassword'
# db_ip_port = "**************:27018"
# db_name = 'fastgpt'
# collection_name = 'dataset.collections'





