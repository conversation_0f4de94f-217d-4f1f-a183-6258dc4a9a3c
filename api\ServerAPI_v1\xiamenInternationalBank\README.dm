# 厦门国际银行知识库问答系统部署文档

## 目录
- [1. 环境要求](#1-环境要求)
- [2. 本地开发环境配置](#2-本地开发环境配置)
- [3. Docker镜像构建](#3-docker镜像构建)
- [4. 容器部署](#4-容器部署)
- [5. 常见问题](#5-常见问题)
## 1. 环境要求

### 1.1 基础环境
- Python 3.10+
- PostgreSQL 14+ (with pgvector extension)
- Docker 20.10+
- Docker Compose v2.0+
### 1.2 硬件要求
- CPU: 2核心以上
- 内存: 4GB以上
- 存储: 20GB以上可用空间
## 2. 本地开发环境配置
### 2.1 配置环境变量
.env文件
PostgreSQL 配置
Embedding Service
DeepSeek Configuration
### 2.2 安装依赖
```bash
pip install -r requirements.txt
```
### 2.3 启动服务
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

本地构建：
*# 进入项目目录*

cd api/ServerAPI_v1/xiamenInternationalBank

*# 构建镜像*
使用uname -m 查看当前系统架构
amd64:
docker build -t xiamen_bank_api:v1.0_20250221 --build-arg name=app --platform linux/amd64 -f Dockerfile .
arm64:
docker build -t xiamen_bank_api_arm64:v1.0_20250221 --build-arg name=app --platform linux/arm64 -f Dockerfile .

保存镜像
docker save -o xiamen_bank_api_v1.0_arm64_20250314.tar xiamen_bank_api_arm64:v1.0_20250221
加载镜像
docker load -i xiamen_bank_api_v1.0_20250221.tar

*# 运行容器*
docker run -d -p 8000:8000 xiamen_bank_api:v1.0_20250221

*# 停止容器*
docker stop xiamen_bank_api

*# 删除容器*
docker rm xiamen_bank_api


pgvector 镜像导入-运行
embeddeding模型部署



# 部署问题：数据库标未初始化成功

1、手动启用扩展

*# 使用 psql 连接到数据库*

docker exec -it xiamen_bank_db_pgvector psql -U username -d postgres

2、*-- 在数据库中创建 vector 扩展*

CREATE EXTENSION IF NOT EXISTS vector;

*-- 验证扩展是否创建成功*

\dx vector

3、*# 重启 API 服务*

docker-compose restart api

*# 或者手动执行初始化*

docker exec -it xiamen_bank_api python -m app.initsql



手动执行测试案例

docker exec -it xiamen_bank_api python -m tests.test_api

docker save -o xiamen_bank_api:v1.2_202502211803.tar xiamen_bank_api:v1.2_202502211803

v1.2_202502211803


# 部署说明
========================
服务器创建一个目录，在目录下操作：
硬件都准备好后，把镜像都加载好后，开始部署

1、先启动embedding模型
docker run -d -p 6006:6006 swr.cn-north-4.myhuaweicloud.com/wiseweb/wise-gpt:bge_large_zh_v1_5_amd64_cuda_20240719
2、修改.env文件
postgresql配置：如果本机部署不用修改，如果远程部署，需要修改host:port
embedding_service配置：根据embedding模型修改host:port
deepseek_configuration配置：根据deepseek大模型修改host:port
docker-compose.yml文件中,api的镜像替换为对应镜像

3、启动容器
后续重启api容器，记得注释掉db的启动
docker-compose up -d

4、测试各接口服务
docker exec -it xiamen_bank_api python -m tests.test_api

