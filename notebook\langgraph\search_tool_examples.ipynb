{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/n1/wr_rt_5x4kb3mxz7cp0mndpw0000gn/T/ipykernel_10224/4238479866.py:59: LangChainDeprecationWarning: The method `BaseTool.__call__` was deprecated in langchain-core 0.1.47 and will be removed in 1.0. Use :meth:`~invoke` instead.\n", "  result = search_engine(state[\"query\"])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["场景1结果： 人工智能（AI）是计算机科学的一个分支，致力于创建能够模拟人类智能的系统。\n", "场景2结果： 相关结果 - 大语言模型: 大语言模型（LLM）是基于Transformer架构的深度学习模型，能够理解和生成人类语言。\n", "场景3结果： <think>\n", "好的，用户让我解释一下深度学习，而搜索引擎给出的结果是：“深度学习是机器学习的一个子集，使用多层神经网络处理复杂模式。”我需要基于这个结果来回答。首先，我得确保自己正确理解这个定义，然后组织语言，用简单明了的方式解释清楚。\n", "\n", "用户可能对机器学习和深度学习不太熟悉，所以需要先简要说明机器学习是什么，再引出深度学习作为其子集。然后重点解释多层神经网络和处理复杂模式的部分。可能需要举一些例子，比如图像识别、语音识别，这样用户更容易理解。\n", "\n", "不过，用户提供的搜索结果比较简短，可能需要扩展一些内容，但不能超出给定的信息。比如，可以提到深度学习在处理大量数据时的优越性，或者它在不同领域的应用。但要注意不要添加额外的信息，只基于提供的搜索结果。\n", "\n", "另外，用户可能想知道深度学习和传统机器学习的区别，所以可以对比一下，比如传统机器学习可能用决策树、支持向量机等，而深度学习用神经网络，尤其是多层结构。但搜索结果里没有提到传统机器学习，所以可能需要避免深入比较，只专注于给出的信息。\n", "\n", "需要确保回答准确，不引入错误信息。比如，不能说深度学习是人工智能的子集，而应该明确是机器学习的子集。同时，多层神经网络是关键点，要强调这一点，说明为什么多层结构能处理复杂模式。\n", "\n", "可能的结构：先定义深度学习，然后解释多层神经网络，再说明处理复杂模式的能力，最后可以提到应用领域。但根据搜索结果，可能不需要提到应用，但可以稍微带过，比如在图像、语音等任务中使用。\n", "\n", "检查是否有遗漏的关键点：搜索结果提到“多层神经网络”和“处理复杂模式”，这两个是核心。需要确保这两个点都被涵盖。可能还需要解释神经网络的基本概念，但搜索结果没有，所以可能不需要深入，只提到多层结构。\n", "\n", "总结：回答应该简明，基于提供的搜索结果，明确深度学习是机器学习的子集，使用多层神经网络处理复杂模式。可能用例子帮助理解，但不要超出给定信息。\n", "</think>\n", "\n", "深度学习是机器学习的一个子集，其核心在于使用**多层神经网络**来处理复杂的模式。这种多层结构（如卷积神经网络、循环神经网络等）能够自动从大量数据中提取特征，例如在图像识别中识别物体边缘，或在语音识别中捕捉声音的规律。通过层层递进的计算，神经网络可以逐步抽象出数据中的深层规律，从而解决传统机器学习方法难以处理的复杂问题。\n", "场景4结果： <think>\n", "好的，用户问的是机器学习，我需要根据搜索引擎返回的结果来生成一个有帮助的回答。首先，用户可能对机器学习的基本概念不太清楚，所以需要先解释清楚它是什么。根据搜索结果，机器学习是人工智能的一个子领域，专注于开发能从数据中学习的算法。那我得先确认这个定义是否正确，然后扩展一下，可能需要举一些例子或者应用场景，让用户更容易理解。\n", "\n", "接下来，用户可能想知道机器学习和传统编程有什么不同。传统编程是程序员明确告诉计算机怎么做，而机器学习则是让计算机通过数据自己学习规律。这点需要强调，因为这是机器学习的核心区别。\n", "\n", "然后，可能需要提到机器学习的类型，比如监督学习、无监督学习、强化学习，但根据搜索结果，可能不需要太深入，因为用户可能只需要基础的解释。不过，如果有必要，可以简要提到这些类型，但要根据提供的搜索结果来调整内容。\n", "\n", "另外，用户可能想知道机器学习的应用场景，比如图像识别、自然语言处理、推荐系统等，这些例子能帮助用户理解机器学习的实际用途。但同样，要确保不超出搜索结果提供的信息范围，如果搜索结果里没有提到应用，可能需要避免添加额外信息。\n", "\n", "还要注意回答的结构，先给出定义，再解释其与传统编程的不同，然后可能提到一些常见的应用或类型，最后总结其重要性或优势。需要保持回答简洁明了，避免使用过于专业的术语，让不同层次的用户都能理解。\n", "\n", "检查是否有遗漏的关键点，比如机器学习需要大量数据，或者算法如何从数据中学习，这些可能需要简要提及。但根据搜索结果，可能不需要深入，所以保持回答在搜索结果的基础上扩展，但不过度。\n", "\n", "最后，确保回答准确，不添加搜索结果中没有的信息，同时用用户容易理解的语言表达。可能需要分点说明，但根据用户的问题，可能不需要分点，而是连贯的段落。\n", "</think>\n", "\n", "机器学习是人工智能领域的一个重要分支，其核心目标是通过算法让计算机从数据中自动学习规律和模式，而无需显式编程。与传统编程不同，机器学习依赖于数据驱动的方式，通过分析大量数据样本，让系统逐步优化自身的决策能力。\n", "\n", "例如，当训练一个图像识别模型时，算法会通过大量图片数据学习如何区分猫和狗的特征，而不是程序员手动编写“如果图像中出现猫的特征则分类为猫”的规则。这种“从数据中自动提取知识”的能力，使机器学习在语音识别、推荐系统、医疗诊断等领域展现出强大潜力。\n", "\n", "需要注意的是，机器学习的效果高度依赖于数据质量与算法设计，它并非万能解决方案，但为复杂问题的自动化处理提供了新的思路。\n"]}], "source": ["import os\n", "from typing import Dict, List, Annotated, TypedDict, Union, Literal\n", "import json\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langgraph.graph import StateGraph, END\n", "# 修改导入路径\n", "from langchain.agents import tool\n", "from langchain_core.tools import BaseTool, StructuredTool, Tool\n", "from langchain_core.messages import HumanMessage, AIMessage, ToolMessage\n", "\n", "# 实现搜索引擎工具\n", "@tool\n", "def search_engine(query: str) -> str:\n", "    \"\"\"使用搜索引擎搜索信息。输入搜索查询，返回搜索结果。\"\"\"\n", "    # 这里模拟搜索引擎结果\n", "    results = {\n", "        \"人工智能\": \"人工智能（AI）是计算机科学的一个分支，致力于创建能够模拟人类智能的系统。\",\n", "        \"机器学习\": \"机器学习是人工智能的一个子领域，专注于开发能从数据中学习的算法。\",\n", "        \"深度学习\": \"深度学习是机器学习的一个子集，使用多层神经网络处理复杂模式。\",\n", "        \"大语言模型\": \"大语言模型（LLM）是基于Transformer架构的深度学习模型，能够理解和生成人类语言。\"\n", "    }\n", "    \n", "    # 如果找不到精确匹配，返回模糊匹配结果\n", "    if query in results:\n", "        return results[query]\n", "    else:\n", "        for key, value in results.items():\n", "            if key in query or query in key:\n", "                return f\"相关结果 - {key}: {value}\"\n", "        return f\"没有找到与'{query}'相关的搜索结果。\"\n", "\n", "# 创建工具执行器 - 不使用ToolExecutor\n", "search_tool = Tool.from_function(\n", "    func=search_engine,\n", "    name=\"search_engine\",\n", "    description=\"使用搜索引擎搜索信息\"\n", ")\n", "\n", "# 初始化LLM\n", "\n", "llm_service_url = \"http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen3_4b/v1\"\n", "llm_api_key = \"MzNlYmY1ZGM0ODYwMzc5MjFmZDM4NmEyY2I2ZTAzNWI3ZGZiYzhhOQ==\"\n", "OPENAI_MODEL = \"Qwen3-4B\"  \n", "llm = ChatOpenAI(\n", "    temperature=0, \n", "    model=OPENAI_MODEL, \n", "    openai_api_base=llm_service_url,\n", "    api_key=llm_api_key\n", ")\n", "\n", "\n", "# 场景1：直接调用工具\n", "class DirectToolState(TypedDict):\n", "    query: str\n", "    result: str\n", "\n", "def call_tool(state: DirectToolState) -> DirectToolState:\n", "    result = search_engine(state[\"query\"])\n", "    return {\"query\": state[\"query\"], \"result\": result}\n", "\n", "direct_tool_graph = StateGraph(DirectToolState)\n", "direct_tool_graph.add_node(\"call_tool\", call_tool)\n", "direct_tool_graph.set_entry_point(\"call_tool\")\n", "direct_tool_graph.add_edge(\"call_tool\", END)\n", "direct_tool_app = direct_tool_graph.compile()\n", "\n", "# 场景2：LLM调用工具，直接返回工具结果\n", "class LLMToolState(TypedDict):\n", "    query: str\n", "    messages: List\n", "    result: str\n", "\n", "def llm_decide(state: LLMToolState) -> Dict:\n", "    messages = [HumanMessage(content=f\"用户查询：{state['query']}。请决定是否需要使用搜索引擎工具来回答这个问题。\")]\n", "    response = llm.invoke(messages)\n", "    \n", "    # 简单判断LLM是否决定使用工具\n", "    if \"使用搜索引擎\" in response.content or \"搜索工具\" in response.content:\n", "        return {\"action\": \"call_tool\"}\n", "    else:\n", "        return {\"action\": \"no_tool\"}\n", "\n", "def call_search_tool(state: LLMToolState) -> LLMToolState:\n", "    result = search_engine(state[\"query\"])\n", "    return {**state, \"result\": result}\n", "\n", "def no_tool_needed(state: LLMToolState) -> LLMToolState:\n", "    return {**state, \"result\": \"LLM决定不需要使用搜索工具。\"}\n", "\n", "llm_tool_graph = StateGraph(LLMToolState)\n", "llm_tool_graph.add_node(\"llm_decide\", llm_decide)\n", "llm_tool_graph.add_node(\"call_tool\", call_search_tool)\n", "llm_tool_graph.add_node(\"no_tool\", no_tool_needed)\n", "llm_tool_graph.set_entry_point(\"llm_decide\")\n", "llm_tool_graph.add_conditional_edges(\n", "    \"llm_decide\",\n", "    lambda x: x[\"action\"],\n", "    {\n", "        \"call_tool\": \"call_tool\",\n", "        \"no_tool\": \"no_tool\"\n", "    }\n", ")\n", "llm_tool_graph.add_edge(\"call_tool\", END)\n", "llm_tool_graph.add_edge(\"no_tool\", END)\n", "llm_tool_app = llm_tool_graph.compile()\n", "\n", "# 场景3：LLM决定调用工具，并根据调用结果生成回答\n", "class LLMWithToolState(TypedDict):\n", "    query: str\n", "    messages: List\n", "    tool_result: str\n", "    final_answer: str\n", "\n", "def llm_router(state: LLMWithToolState) -> Dict:\n", "    messages = [HumanMessage(content=f\"用户查询：{state['query']}。请决定是否需要使用搜索引擎工具来回答这个问题。\")]\n", "    response = llm.invoke(messages)\n", "    state[\"messages\"] = messages + [response]\n", "    \n", "    if \"使用搜索引擎\" in response.content or \"搜索工具\" in response.content:\n", "        return {\"action\": \"use_tool\"}\n", "    else:\n", "        return {\"action\": \"direct_answer\"}\n", "\n", "def use_search_tool(state: LLMWithToolState) -> LLMWithToolState:\n", "    tool_result = search_engine(state[\"query\"])\n", "    return {**state, \"tool_result\": tool_result}\n", "\n", "def llm_direct_answer(state: LLMWithToolState) -> LLMWithToolState:\n", "    messages = state[\"messages\"] + [HumanMessage(content=f\"请直接回答用户的问题：{state['query']}\")]\n", "    response = llm.invoke(messages)\n", "    return {**state, \"final_answer\": response.content}\n", "\n", "def llm_answer_with_tool(state: LLMWithToolState) -> LLMWithToolState:\n", "    messages = state[\"messages\"] + [\n", "        HumanMessage(content=f\"搜索引擎返回的结果是：{state['tool_result']}。请基于这个结果回答用户的问题：{state['query']}\")\n", "    ]\n", "    response = llm.invoke(messages)\n", "    return {**state, \"final_answer\": response.content}\n", "\n", "llm_with_tool_graph = StateGraph(LLMWithToolState)\n", "llm_with_tool_graph.add_node(\"llm_router\", llm_router)\n", "llm_with_tool_graph.add_node(\"use_tool\", use_search_tool)\n", "llm_with_tool_graph.add_node(\"llm_direct_answer\", llm_direct_answer)\n", "llm_with_tool_graph.add_node(\"llm_answer_with_tool\", llm_answer_with_tool)\n", "llm_with_tool_graph.set_entry_point(\"llm_router\")\n", "llm_with_tool_graph.add_conditional_edges(\n", "    \"llm_router\",\n", "    lambda x: x[\"action\"],\n", "    {\n", "        \"use_tool\": \"use_tool\",\n", "        \"direct_answer\": \"llm_direct_answer\"\n", "    }\n", ")\n", "llm_with_tool_graph.add_edge(\"use_tool\", \"llm_answer_with_tool\")\n", "llm_with_tool_graph.add_edge(\"llm_direct_answer\", END)\n", "llm_with_tool_graph.add_edge(\"llm_answer_with_tool\", END)\n", "llm_with_tool_app = llm_with_tool_graph.compile()\n", "\n", "# 场景4：只提供参数直接调用工具，LLM根据调用结果生成回答\n", "class DirectParamToolState(TypedDict):\n", "    query: str\n", "    messages: List\n", "    tool_result: str\n", "    final_answer: str\n", "\n", "def direct_tool_call(state: DirectParamToolState) -> DirectParamToolState:\n", "    tool_result = search_engine(state[\"query\"])\n", "    return {**state, \"tool_result\": tool_result}\n", "\n", "def llm_process_result(state: DirectParamToolState) -> DirectParamToolState:\n", "    messages = [HumanMessage(content=f\"用户查询：{state['query']}。搜索引擎返回的结果是：{state['tool_result']}。请基于这个结果生成一个有帮助的回答。\")]\n", "    response = llm.invoke(messages)\n", "    return {**state, \"final_answer\": response.content}\n", "\n", "direct_param_graph = StateGraph(DirectParamToolState)\n", "direct_param_graph.add_node(\"direct_tool_call\", direct_tool_call)\n", "direct_param_graph.add_node(\"llm_process_result\", llm_process_result)\n", "direct_param_graph.set_entry_point(\"direct_tool_call\")\n", "direct_param_graph.add_edge(\"direct_tool_call\", \"llm_process_result\")\n", "direct_param_graph.add_edge(\"llm_process_result\", END)\n", "direct_param_app = direct_param_graph.compile()\n", "\n", "# 测试示例\n", "if __name__ == \"__main__\":\n", "    # 测试场景1\n", "    result1 = direct_tool_app.invoke({\"query\": \"人工智能\"})\n", "    print(\"场景1结果：\", result1[\"result\"])\n", "    \n", "    # 测试场景2\n", "    result2 = llm_tool_app.invoke({\"query\": \"什么是大语言模型？\", \"messages\": []})\n", "    print(\"场景2结果：\", result2[\"result\"])\n", "    \n", "    # 测试场景3\n", "    result3 = llm_with_tool_app.invoke({\"query\": \"解释一下深度学习\", \"messages\": []})\n", "    print(\"场景3结果：\", result3[\"final_answer\"])\n", "    \n", "    # 测试场景4\n", "    result4 = direct_param_app.invoke({\"query\": \"机器学习\", \"messages\": []})\n", "    print(\"场景4结果：\", result4[\"final_answer\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}