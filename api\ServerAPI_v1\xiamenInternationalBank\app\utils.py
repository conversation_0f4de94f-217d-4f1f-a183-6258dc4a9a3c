import uuid
from pydantic import BaseModel, Field
from datetime import datetime
from pydantic import BaseModel
from typing import  List, Dict,Any

def generate_random_id():
    return str(uuid.uuid4())


class Message(BaseModel):
    content: str
    createAt: int
    id: str
    updateAt: int
    message: str
    role: str
    meta: Dict[str, Any]  # 确保 meta 是一个字典

class ChatResponse(BaseModel):
    app_info: str
    conversation_id: str
    extra: Dict[str, Any]
    user_id: int
    user_name: str
    messages: List[Any]



# if __name__ == "__main__":
#     # 示例响应数据
#     data = {
#         "id": "chatcmpl-8f5b2a99-86e8-4940-9d27-d8d04413c9bb",
#         "object": "chat.completion",
#         "created": 1741833881,
#         "model": "DeepSeek-R1-Distill-Qwen-32B",
#         "choices": [{
#             "index": 0,
#             "message": {
#                 "role": "assistant",
#                 "content": "<think>\n\u55ef\uff0c\u8ba9\u6211\u4ed4\u7ec6\u601d\u8003\u4e00\u4e0b\u8fd9\u4e2a\u95ee\u9898\u3002\u7528\u6237\u5e0c\u671b\u6211\u6839\u636e\u63d0\u4f9b\u7684\u53c2\u8003\u8d44\u6599\uff0c\u5236\u5b9a\u4e00\u4e2a\u56de\u5e94\u53e3\u5f84\uff0c\u7528\u4e8e\u5185\u90e8\u8ba8\u8bba\u3002\u6750\u6599\u4e2d\u63d0\u5230\u7684\u662f\u519c\u884c\u9ad8\u7ba1\u81ea\u5bb6\u6f0f\u6c34\u7838\u697c\u4e0b\u7684\u5899\u4e8b\u4ef6\uff0c\u540e\u6765\u9ad8\u7ba1\u97e9\u67d0\u9053\u6b49\uff0c\u519c\u884c\u56e0\u6b64\u53d7\u635f\u3002\n\n\u9996\u5148\uff0c\u6211\u9700\u8981\u7406\u89e3\u4e8b\u4ef6\u7684\u6027\u8d28\u3002\u8fd9\u5c5e\u4e8e\u58f0\u8a89\u98ce\u9669\uff0c\u98ce\u9669\u7c7b\u578b\u662f\u6076\u610f\u62b9\u9ed1\u3002\u673a\u6784\u662f\u6d59\u6c5f\u7684\u5236\u9020\u4e1a\u516c\u53f8\uff0c\u4e8b\u4ef6\u53d1\u751f\u65f6\u95f4\u57282025\u5e741\u670825\u65e5\u3002\u6781\u6c2a\u6cd5\u52a1\u90e8\u7684\u56de\u5e94\u5185\u5bb9\u4e3b\u8981\u662f\u8f9f\u8c23\uff0c\u5f3a\u8c03\u672a\u8fdb\u884c\u6240\u8c13\u7684\u5bf9\u649e\u6d4b\u8bd5\uff0c\u5e76\u8868\u793a\u5c06\u8ffd\u7a76\u9020\u8c23\u8005\u7684\u8d23\u4efb\u3002\u4f46\u56de\u5e94\u540e\u6548\u679c\u663e\u793a\uff0c\u4ed6\u4eec\u6ca1\u6709\u63d0\u4f9b\u5177\u4f53\u8bc1\u636e\uff0c\u5bfc\u81f4\u90e8\u5206\u7f51\u53cb\u4ecd\u6709\u7591\u8651\u3002\n\n\u63a5\u4e0b\u6765\uff0c\u6211\u8981\u8003\u8651\u7528\u6237\u7684\u9700\u6c42\u3002\u7528\u6237\u662f\u4e00\u540d\u4e13\u4e1a\u4e14\u7ecf\u9a8c\u4e30\u5bcc\u7684\u53e3\u5f84\u52a9\u624b\uff0c\u9700\u8981\u6839\u636e\u63d0\u4f9b\u7684\u6750\u6599\uff0c\u5236\u5b9a\u4e00\u4e2a\u5b8c\u7f8e\u7684\u56de\u5e94\u53e3\u5f84\u3002\u56de\u590d\u9700\u8981\u7528\u4e00\u53e5\u8bdd\uff0c\u5e76\u4e14\u7b26\u5408\u793a\u4f8b\u7684\u683c\u5f0f\u3002\n\n\u7528\u6237\u53ef\u80fd\u5e0c\u671b\u56de\u5e94\u65e2\u575a\u5b9a\u53c8\u4e0d\u5931\u4e13\u4e1a\uff0c\u540c\u65f6\u907f\u514d\u5f15\u8d77\u66f4\u591a\u4e89\u8bae\u3002\u6781\u6c2a\u7684\u56de\u5e94\u867d\u7136\u53ca\u65f6\uff0c\u4f46\u6ca1\u6709\u62ff\u51fa\u5177\u4f53\u8bc1\u636e\uff0c\u6548\u679c\u4e0d\u4f73\u3002\u56e0\u6b64\uff0c\u6211\u7684\u56de\u5e94\u5e94\u8be5\u5305\u62ec\uff1a1. \u786e\u8ba4\u4e8b\u4ef6\uff1b2. \u5f3a\u8c03\u6cd5\u5f8b\u624b\u6bb5\uff1b3. \u9080\u8bf7\u5173\u6ce8\u5b98\u65b9\u4fe1\u606f\u3002\n\n\u7efc\u5408\u4ee5\u4e0a\u5206\u6790\uff0c\u56de\u5e94\u5e94\u8be5\u7b80\u6d01\u660e\u4e86\uff0c\u540c\u65f6\u4f53\u73b0\u51fa\u516c\u53f8\u7684\u51b3\u5fc3\u548c\u5bf9\u6cd5\u5f8b\u624b\u6bb5\u7684\u91cd\u89c6\uff0c\u540c\u65f6\u907f\u514d\u731c\u6d4b\u548c\u4e0d\u786e\u5b9a\u6027\uff0c\u8fd9\u53ef\u4ee5\u901a\u8fc7\u9080\u8bf7\u516c\u4f17\u5173\u6ce8\u5b98\u65b9\u4fe1\u606f\u6765\u5b9e\u73b0\u3002\n</think>\n\n\u7b54\uff1a\u6781\u6c2a\u5df2\u5c31\u76f8\u5173\u4e8b\u4ef6\u53d1\u5e03\u58f0\u660e\uff0c\u8868\u660e\u5176\u7acb\u573a\u5e76\u5f3a\u8c03\u5c06\u8ffd\u7a76\u9020\u8c23\u8005\u6cd5\u5f8b\u8d23\u4efb\uff0c\u540c\u65f6\u4e5f\u9080\u8bf7\u516c\u4f17\u5173\u6ce8\u5b98\u65b9\u4fe1\u606f\u4ee5\u83b7\u53d6\u51c6\u786e\u5185\u5bb9\u3002",
#                 "tool_calls": []
#             },
#             "finish_reason": "stop",
#             "stop_reason": None
#         }],
#         "usage": {
#             "prompt_tokens": 537,
#             "total_tokens": 856,
#             "completion_tokens": 319
#         }
#     }
    
#     # 获取content内容
#     content = data["choices"][0]["message"]["content"]
    
#     # 直接打印content（Python会自动处理Unicode编码）
#     print("解码后的内容:")
#     print(content)
    
#     # 如果需要提取答案部分
#     if "<think>" in content:
#         answer_start = content.find("答：")
#         if answer_start != -1:
#             answer = content[answer_start + 2:].strip()
#             print("\n只显示答案部分:")
#             print(answer)