## 消保助手需求文档

 ![](./image.png)



## 四个场景
1.事前的审核
2.事后分析（检索、时间）.
3.投诉案件、案例分析（内容）  ：客诉风险分类标签统计分析：客户画像分析、案件分类
4.数据管理
5 消保工单


分析和报表：支持对标注后的投诉文本进行统计和分析，生成相应的报表和可视化图表。通过对投诉文本的分析，了解用户的投诉趋势、主要问题和原因等，为业务决策提供数据支持。同时，还支持根据用户的需求，定制化生成各类报表和分析结果。


2.投诉处理==》接入处理

"一键提取"：消保端功能，可提前案件关联的客户相关资料，包含：历史投诉资料、营销资料、合同档案资料等。获取后，可帮助承办端执行人更了解案件及客户信息，方便承办端执行人处理案件更方便。

8."案件详情"：可查看单个案件的相关详情内容。可查看"案件信息""处理信息""客户旅程"以及相关的"附件"信息。

案件流程列表
案件流程根据SLA、投诉渠道、内部案件分类配置不同的流程，每个案件流程需要关联一个工作流，对于工作流，多个案件流程可以使用同一个工作流。另外，案件流程可根据不同的配置信息配置不同的流程，其中配置信息支持接口对接获取信息，以便更高效的解决客户的需求和投诉

D典型案例库
支持当预警拦截未能化解投诉时，投诉处理人员可查阅参考相关案例的问题描述、处理方式以及与客户聊天记录（包含文本、语音），回电至客户进行后续投诉处理。

支持展示典型案例库，支持投诉处理人员查阅参考相关案例的问题描述、处理方式以及与客户的聊天记录，支持按照案件类型、投诉渠道、优先级查询典型案例。投诉处理人员查阅完成后可以回电至客户进行后续投诉处理。

支持识别升级投诉和重复投诉


在客服人员建立投诉案件时，客户信息从客服坐席工作台带出。客户标签从客服坐席工作台带出，且支持修改。案件基本信息：输入案件名称、原始投诉渠道、承办渠道、案件登记时间、案件转办时间、案件内部分类、案件类型（内诉案件、外诉案件）、案件标签、预估处理完成时间、投诉内容、诉求、案件相关附件。其中，不同的案件内部分类可配置不同的案件流程，那么在新建案件时，选择不同的案件内容分类，会根据配置的案件流程进行流转。案件内部分类案件处理信息：输入案件要求处理时间、其他要求。创建完成的案件信息，会按照配


H消保专用知识库和通知提醒
建设客诉专用知识库，提供查询和引导；消息通知-支持新产品、新功能上线（扣款规则、风险策略）系统通知和提醒，在系统中可形成公告。
客诉知识库
支持建设客诉专用知识库，知识库支持后台维护和坐席页面查询使用，通过在知识库维护客诉知识，坐席人员能够在和客户沟通过程中参考相关客诉知识，提供正确的引导降诉：

客诉降诉策略管理
降诉策略是指通过一定的措施和方法，降低客户投诉的数量和影响，从而提高客户满意度和忠诚度，增强金融机构的竞争力和声誉。


.投诉监测预警
全局监控客户的投诉信息，形成线上可视化报表，支持包括但不限于客服量与投诉量的对比分析、投诉量异常监控（包括渠道类、产品类、息费类等），投诉处理进度监控，报表可导出。
设置投诉预警阈值，当监控一段时间内投诉数量及问题类别等达到阈值，可自动触发预警，并自动提示相关责任部室及管理部室。
支持对投诉多维度自动分析、展示（自定义），及时掌握公司投诉压降进展，包括投诉数量、处理能力评价、处理时效等。


# 功能介绍：
## 投诉监测预警
1.内诉案件案件量：显示"本周"（本周内每日内诉案件案件量）、"本月"（本月内每日内诉案件案件量）、"全年"（全年内每月内诉案件案件量）
2.外诉案件案件量：显示"本周"（本周内每日外诉案件案件量）、"本月"（本月内每日外诉案件案件量）、"全年"（全年内每月外诉案件案件量）
3.内部分类客诉排名：显示在不同的时间范围内，不同的内部分类下案件数量的排名
4.外部分类客诉排名：显示在不同的时间范围内，不同的外部分类下案件数量的排名
5.各投诉渠道案件量：可显示不同的时间范围内不同的监管渠道的案件量趋势数据。
6.各产品案件量：可显示不同的时间范围内不同产品对应的案件量
7.各省案件数据：可显示不同的时间范围内各个省对应的案件量
8.各承办单位案件数据：可显示不同的时间范围内不同的承办单位对应的案件量排行
9.结案案件：可显示不同时间范围内，每月进件案件和已结案案件的案件量。
10.SLA: 显示"处理中""处理中延期"案件，不同的SLA下的案件量
11.SLA进度分布：显示"处理中""处理中延期"案件，"距离完成时长还剩45%"、"逾期15%时长"、"距离完成时长还剩30%"、"逾期30%时长"、"距离完成时长还剩15%"、"逾期45%时长"的SLA下的案件占比。


投诉分析
支持对投诉多维度自动分析、展示（自定义），及时掌握公司投诉压降进展，包括投诉数量、处理能力评价、处理时效等。

提供对客诉案件的管理平台，支持对投诉多维度自动分析和展示（自定义）。一方面通过图表数据看板了解系统当前投诉案件的情况，另一方面也可对在途的投诉案件进行改派，催办、查看等操作。对系统中的投诉案件有整体的把控和了解。及时掌握公司投诉压降进展，包括投诉数量、处理能力评价、处理时效等。
功能介绍：
1.案件总数：显示"处理中""处理中延期"的案件总数
2.内诉案件总数：显示"处理中""处理中延期"且案件分类为"内部分类"的案件总数
3.外诉案件总数：显示"处理中""处理中延期"且案件分类为"外部分类"的案件总数
4.临期案件：显示"处理中""处理中延期"且距离案件结束时间小于2天的案件总数
5.逾期案件：显示 "处理中延期"的案件总数
6.催办案件：显示被"消保端""客服端"催办的案件总数
7.SLA: 显示"处理中""处理中延期"案件，不同的SLA下的案件量
8.案件内部分类：显示不同案件内部分类下"处理中""处理中延期"案件数
9.单卡片显示的是"处理中""处理中延期"的单个案件，点击单个案件进入到案件详情。

客诉倾向识别
根据客户投诉数据（依托标签数据计算）实时生成客户风险等级，并将相关计算结果数据链接至客户信息页面，相关风险等级数据需同步至客户画像平台，服务公司各部门决策使用。

A潜在客诉风险
"潜在客诉风险"是智能消保系统中的一个重要功能，它可以帮助金融机构从源头加强管理，帮助提前发现和预警可能发生的客诉事件。该模块融合"质检"、"人工判断"等多渠道数据来源，借助自然语言处理技术挖掘投诉文本特征，实现潜在投诉风险的智能化自动排查，有效提高工作质效，降低因投诉、诉讼或舆情事件造成的声誉损失，也让消费者权益保护更加及时有效。
功能介绍：


a.客诉知识库
支持建设客诉专用知识库，知识库支持后台维护和坐席页面查询使用，通过在知识库维护客诉知识，坐席人员能够在和客户沟通过程中参考相关客诉知识，提供正确的引导降诉：


### 智能消保系统功能需求清单  
以下为整理后的功能需求，按模块分类，供开发参考：

---

#### **1. 投诉标签标注**  
**A. 标签管理**  
1. **客户标签管理**  
   - 建立客户标签体系（基础信息类、投诉来源类、合作机构类等）。  
   - 支持标签组与标签的增删改查（删除标签组需确保其下无标签）。  
   - 支持通过接口导入/导出客户标签。  
   - 在客服工作台和案件创建时展示标签。  

2. **案件标签管理**  
   - 建立案件分类标签（渠道类、产品类、问题类等）。  
   - 支持标签组与标签的增删改查。  
   - 支持通过接口导入/导出案件标签。  
   - 案件流程中全程使用案件标签。  

**B. 客户新标签开发与数据分析**  
   - 支持配置化开发新标签（如处理进度类）。  
   - 数据分析功能：整合数据源生成可视化报表（图表、仪表盘）。  
   - 示例分析场景：客诉风险分类统计、各渠道案件量分析、客户画像分析。  

---

#### **2. 案件分类与映射**  
1. **内部分类**  
   - 按金融机构需求自定义案件分类（支持父子层级）。  
   - 支持分类的增删改查（删除父类时子类同步删除）。  

2. **外部分类**  
   - 按监管标准定义案件分类（支持父子层级）。  
   - 支持分类的增删改查。  

3. **内外分类映射**  
   - 支持内部分类与外部分类的多对一或一对多映射。  
   - 映射关系可配置使用状态与说明。  

---

#### **3. 自动标注与结构化处理**  
1. **标签规则配置**  
   - 基于关键词匹配、情感分析、实体识别定义规则。  

2. **自动标注**  
   - 根据规则自动为投诉文本添加标签。  
   - 将非结构化文本转换为结构化数据（标签、时间、内容等）。  

3. **应用场景**  
   - 预防监控：实时预警客诉风险。  
   - 分析与报表：生成趋势分析、问题统计报表。  

---

#### **4. 投诉处理流程**  
**A. 客诉专线接入**  
   - 全渠道接入（APP、H5、电话等），本期完成电话专线（89948994）接入。  
   - 接听后自动生成工单，支持工单状态跟踪。  

**B. 征信异议处理**  
   - 与易开花APP对接，支持客户在线提交申请、上传凭证。  
   - 自动生成工单并流转审批，实时同步进度至APP。  

**C. 案件管理**  
   - 案件列表分类展示（处理中、延期、已完成）。  
   - 支持催办（站内信、短信、邮件）、改派执行人、查看案件详情。  
   - 案件流程可视化配置（关联SLA、渠道、分类）。  

**D. SLA管理**  
   - 定义各节点时限（受理、首联、处理、结案）。  
   - 逾期自动提醒（站内信、短信、钉钉等）。  

**E. 典型案例库**  
   - 存储已处理案例（含客户信息、处理记录、聊天记录）。  
   - 支持按类型、渠道、优先级查询案例供参考。  

**F. 特殊投诉识别**  
   - 标记升级投诉和重复投诉。  
   - 对接监管机构（如新浪黑猫）转办投诉，自动生成内部工单。  

---

#### **5. 监测预警与分析**  
1. **报表中心**  
   - 可视化展示数据总览（内/外诉案件量、渠道趋势、产品分布等）。  
   - 支持导出报表（Excel/PDF）。  

2. **预警监测**  
   - 设置阈值（投诉量、问题类别），触发自动预警并通知责任部门。  

3. **投诉分析**  
   - 多维度分析（处理时效、逾期案件、催办量等）。  
   - 支持批量操作（一键催办）。  

---

#### **6. 客诉倾向识别**  
1. **潜在客诉风险**  
   - 结合质检和人工判断，自动排查潜在风险客户。  
   - 支持转办或忽略操作。  

2. **高风险客户名单**  
   - 根据投诉数据生成风险等级，同步至客户画像平台。  
   - 支持名单加入、解除及审核。  

---

#### **7. 知识库与通知**  
1. **客诉知识库**  
   - 维护客诉处理知识（问题解答、附件上传）。  
   - 客服工作台可实时查询。  

2. **降诉策略管理**  
   - 配置降诉策略（按案件分类），需审核生效。  
   - 客服沟通时参考策略引导客户。  

3. **消息通知**  
   - 支持系统公告发布（新产品、规则变更）。  
   - 工单页面集成电话、短信、邮件通知功能（加密处理）。  

---

#### **8. 系统对接**  
1. **接口管理**  
   - 分类管理第三方接口（如易开花APP、监管机构）。  
   - 配置接口参数（URL、请求格式、授权）。  

2. **数据同步**  
   - 客户风险等级同步至画像平台。  
   - 外部投诉数据（监管机构、黑猫）自动转换为内部工单。  

---

### **开发重点提示**  
1. **模块化开发**：优先实现核心功能（标签管理、工单系统、SLA监控）。  
2. **接口设计**：确保与外部系统（易开花APP、监管平台）的数据互通。  
3. **自动化能力**：标签自动标注、预警触发、流程流转需减少人工干预。  
4. **可视化工具**：集成图表库（如ECharts）实现数据看板。  
5. **权限控制**：基于角色分配功能权限（如案件改派、策略审核）。  

---  
整理完毕，可作为开发任务分解和系统设计的依据。


#### 案件报表
统一展示投诉案件报表数据。
功能介绍：
可查询"已结案"的所有案件信息

### 事前的审核
 主要的解决问题是
 1. 发现文件的消保问题，文件包括pdf docx和图片
 2. 识别和抽取文件的内容，先从前端提取。，展示抽取结果。
 3. 启动审核过程，对内容进行审核，在提取结果上标注问题所在，在消保审核区域展示，判断结果和依据。
 4. 针对问题给出修改建议。


### 事后的审核


数据分析
归因、==》"投诉的关键点"、投诉原因
责任追溯
补偿建议==》成功率
案例==》相似=》话术推荐

投诉处理、维权申请和风险评估 =》填入转发==》评先"评估"==》建议指导==》在线教育培训

数据：消保政策
基础信息类、投诉来源类、合作机构类、投诉问题类、处理进度类等
标签开发


案件内部分类
案件的内部分类是根据金融机构内部具体情况和需要，对客诉案件进行更加细致的划分，以便更好地处理和解决不同类型的问题。


案件外部分类
案件的外部分类是根据监管部门按照具体情况和需要，对客诉案件进行更加细致的划分，以便更好地管理和处理不同类型的客诉案件，提高客户满意度和忠诚度。

内外分类映射
内外分类进行映射，是为了方便金融机构在统一的口径下对投诉案件进行溯源分析、管理和统计。同时，内外分类的映射也需要根据实际情况和需要进行灵活的调整和处理。

支持自动标注
支持依据标签规则自动标注，将投诉文本转成结构化数据，提供给预防监控、分析、报表等场景进行使用

支持依据标签规则自动标注，将投诉文本转化为结构化数据，并提供给预防监控、分析、报表等场景使用：

标签规则包括关键词匹配、情感分析、实体识别等多种方式，以便准确地标注投诉文本。




以下为"数据处理阶段"需求的 Markdown 格式表格示例：

| 需求名称 | 分析/处理目的 | 依赖数据字段 | 处理方式 / 预期结果 |
|---------|-------------|-------------|--------------------|
| 1. 投诉文本解析与分类 | - 基于自然语言处理，对原始投诉文本/通话记录转写进行自动化分类<br>- 识别投诉主题、问题类型、风险等级、情感倾向等 | - 投诉内容文本（文字或通话转写）<br>- 投诉主题/标签（如有）<br>- 现有枚举值（内部分类、外部分类等） | - 使用 LLM 或关键词+规则引擎对文本进行解析<br>- 输出：投诉类型、情感倾向、关键词等结构化标签<br>- 赋值到数据库对应字段或标签体系 |
| 2. 关键字段提取 & 实体识别 | - 从文本中提取特定实体（如产品名称、金额、时间）<br>- 便于后续精细化统计与风险识别 | - 投诉内容文本<br>- 业务产品清单或名称对照表（如有）<br>- 金额、时间等标准化字段 | - 通过实体识别模型或词典匹配，识别并提取关键信息<br>- 输出：结构化信息(如 "产品=XXX"、"金额=1000元")<br>- 存储至"投诉详情"/"业务要素"等字段 |
| 3. 多维度投诉量聚合与统计 | - 为报表中心与对话查询提供基础数据<br>- 实现按时间、渠道、产品、地区、承办部门等统计的自动化计算 | - 投诉ID、客户ID、产品ID<br>- 投诉渠道、投诉时间、投诉状态（处理中/已结案）<br>- 客户地区（省/市/县）<br>- 承办单位/部门 | - ETL/定时任务对投诉数据做分组统计<br>- 输出：如"每日/每周/每月各渠道投诉量"、"各产品投诉量TOP榜"、"各省投诉量"等汇总表或中间表 |
| 4. SLA & 处理时效监控 | - 计算每个投诉的处理时长、逾期情况<br>- 后续可在对话中询问逾期率/处理效率，也可触发预警 | - 投诉创建时间、结案时间<br>- SLA 定义（首次响应时限、最终结案时限等）<br>- 投诉状态/节点（进行中、已结案） | - 计算"实际处理时长 = 结案时间 - 创建时间"<br>- 对比 SLA 阈值判断是否逾期<br>- 输出：分部门/分渠道的平均处理时长、逾期案件列表 |
| 5. 历史案例聚类 & 相似案例检索 | - 用于在对话时提供"相似投诉案例"建议<br>- 帮助客服/承办人员参考已有成功处理经验 | - 历史投诉文本 + 处理过程/结案结果<br>- 客户满意度或回访结果（如有） | - 对历史投诉文本进行向量化或聚类<br>- 输出：相似案例索引（相似度 Top K）<br>- 记录处理结论、对应承办单位、结案类型等，供 LLM 检索 |
| 6. 预警阈值配置 & 实时监测 | - 根据业务规则或历史数据，设置预警阈值（如某渠道一天投诉≥X、息费相关投诉激增率≥Y% 等）<br>- 定期/实时统计投诉量并与阈值比较，触发预警 | - 阈值配置表（监测维度、告警条件、频率、责任部门等）<br>- 实时/准实时投诉数据流 | - 定时或实时任务对投诉数据做聚合统计<br>- 若超出阈值则写入"预警事件"表并关联责任部门/承办单位<br>- 输出：预警列表，用于对话查询或消息推送 |
| 7. 风险等级分级 | - 根据投诉数量、投诉类型、升级/重复投诉频次等要素识别高风险客户<br>- 在对话时可快速查询某客户风险等级 | - 客户ID <br>- 投诉次数/类型<br>- 标签字段（重复投诉、升级投诉等）<br>- 风险等级定义或规则 | - 由定制化规则或 LLM 对客户维度综合评分<br>- 输出：客户-风险等级清单<br>- 在数据库中标记"高风险""潜在风险"等 |
| 8. 数据可视化预处理 | - 为对话式"图表生成"做好数据准备<br>- 存储/缓存关键维度、时间序列，以便快速生成折线图/饼图等 | - 聚合后的统计表<br>- 维度表（渠道、产品、部门、地区等） | - 定期生成数据立方体（OLAP）或中间汇总表<br>- 输出：带时间戳的各维度分组统计数据，供前端或 LLM 调用生成可视化 |




对话场景	可提出的问题类型	期望的输出 / 功能
| 对话场景 | 可提出的问题类型 | 期望的输出 / 功能 |
|---------|------------------|-------------------|
| 1. 基础统计与查询 | - "最近一周的投诉总量是多少？"<br>- "某产品在上个月有多少起投诉？"<br>- "投诉主要集中在哪些渠道？" | - 输出：1) 一段文字描述：如"上周共收到 120起投诉，其中APP渠道占60%。"<br>2) 简要表格/列表：列出各渠道或产品的分布<br>3) 如果对接可视化：可生成简单的折线图/饼图链接 |
| 2. SLA / 时效相关 | - "上个月某部门的平均处理时长是多少？"<br>- "逾期案件有多少？逾期率多少？"<br>- "哪几单投诉超过SLA时限？" | - 输出：1) 逾期案件清单或统计汇总<br>2) 分部门/产品的平均处理时长<br>3) 自然语言解释：如"××部门逾期率为15%，高于公司平均水平10%" |
| 3. 预警与风险 | - "近期有没有触发预警的投诉类型？"<br>- "外部监管转办投诉是否出现异常增多？"<br>- "帮我列一下最新触发的预警事件并给个简短分析。" | - 输出：1) 预警事件列表：时间、投诉类型、触发原因<br>2) LLM对触发事件的简要分析：如"监管转办投诉数值比上周增长50%，主要与近期逾期还款相关"<br>3) 若需进一步措施：给出可操作建议（如"建议外呼核实"、"加强客服话术培训"） |
| 4. 个案查询 / 处理详情 | - "帮我查一下投诉ID=12345的处理进度如何？"<br>- "某客户ID=67890最近有几次投诉？风险等级是多少？" | - 输出：1) 投诉或客户详情：创建时间、承办单位、当前状态、剩余处理时限<br>2) 关联的风险等级、是否重复投诉、是否升级投诉<br>3) 一段简要说明或处理建议 |
| 5. 相似案例与建议 | - "我现在有一单息费纠纷，能否给出相似案例参考？"<br>- "遇到客户质疑利率过高时，建议我如何回复？" | - 输出：1) 相似案例列表：显示案例ID、结案原因、处理要点<br>2) 自动生成或推荐的话术示例：如"我们非常理解您对利率的关注……"<br>3) 若有历史满意度反馈，可列举成功处理经验 |
| 6. 数据洞察 / 趋势分析 | - "本季度内诉案件和外诉案件的变化趋势如何？"<br>- "各渠道投诉量环比上季度有什么变化？"<br>- "哪些产品的投诉量呈现上升趋势？" | - 输出：1) 拟合/对比图表链接或图表描述<br>2) 文字总结：如"内诉案件环比下降5%，外诉案件上涨10%，主要集中在息费纠纷" |
| 7. 报告生成 / 摘要输出 | - "请生成上个月投诉情况简报，并给我一份PDF下载。"<br>- "写一份关于逾期案件的简短报告，包括主要原因和建议。" | - 输出：1) 概要报告：包括投诉数据概览、热点问题、逾期率、案例分析等<br>2) LLM生成可读性较强的文字段落<br>3) 如果与文档系统集成：可导出PDF/Word<br>4) 可在文中自动插入图表或统计数字 |
| 8. 预测与资源安排 | - "如果下个月投诉量继续增长10%，大概需要增配多少客服坐席？"<br>- "有哪些可能的原因导致本月暴增的投诉？" | - 输出：1) 模型/LLM对数据趋势的初步外推（如仅从历史增长规律推断）<br>2) 文字建议：如"基于上个月人均日处理量30单，若投诉增长10%，需增配2-3位客服坐席"<br>3) 可能原因归纳：如"逾期催收规则收紧"、"费用调整告知不及时" |
| 9. 自定义维度或深度分析 | - "从地区、产品类型两个维度交叉分析最近三个月的投诉走势。"<br>- "哪些地区的投诉率最高？与去年同期相比如何？" | - 输出：1) 交叉分析结果的可视化/表格<br>2) LLM对比分析：如"华东地区较去年同比增长20%，其他地区相对稳定"<br>3) 若需更深层原因，可进一步自动筛选典型案例 |




对于数据需要处理的要求
- 内部分类是根据金融机构内部具体情况和需要，对客诉案件进行更加细致的划分，以便更好地处理和解决不同类型的问题。
- 外部分类是根据监管部门按照具体情况和需要，对客诉案件进行更加细致的划分，以便更好地管理和处理不同类型的客诉案件，提高客户满意度和忠诚度。
- 实体信息