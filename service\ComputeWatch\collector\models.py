from datetime import datetime
from typing import List, Optional, Dict
from pydantic import BaseModel, Field

class ServerInfo(BaseModel):
    """服务器基本信息"""
    server_id: str  # 服务器唯一标识
    hostname: str  # 主机名
    port: int = 8000  # 服务器端口
    api_key: str  # API访问密钥
    collect_interval: int = 60  # 采集间隔(秒)
    cron_expression: Optional[str] = None  # Cron表达式，如果设置则优先使用
    description: Optional[str] = None  # 服务器描述
    enabled: bool = True  # 是否启用采集
    
    # 系统信息
    os: str  # 操作系统
    os_version: str  # 系统版本
    architecture: str  # 系统架构
    cpu_count: int  # CPU核心数
    cpu_physical_count: int  # 物理CPU核心数
    memory_total: int  # 总内存(bytes)
    disk_partitions: List[str]  # 磁盘分区列表
    gpu_info: Optional[Dict] = None  # GPU信息
    npu_info: Optional[Dict] = None  # NPU信息
    last_update: datetime = Field(default_factory=datetime.now)  # 最后更新时间

class MetricsRecord(BaseModel):
    """监控指标记录"""
    server_id: str  # 服务器ID
    timestamp: datetime = Field(default_factory=datetime.now)  # 记录时间
    cpu_metrics: Dict  # CPU指标
    memory_metrics: Dict  # 内存指标
    disk_metrics: Dict  # 磁盘指标
    network_metrics: Dict  # 网络指标
    gpu_metrics: Optional[Dict] = None  # GPU指标
    npu_metrics: Optional[Dict] = None  # NPU指标 