from mongoengine import Document, StringField, DateTimeField, DictField, BooleanField, IntField, FloatField, ListField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, Dict, List, Any
from enum import Enum

class ExpertType(str, Enum):
    """专家类型"""
    AGENT = "agent"          # 基于 Agent 的专家
    WORKFLOW = "workflow"    # 基于工作流的专家
    HYBRID = "hybrid"        # 混合型专家
    RAG = "rag"             # 基于检索增强的专家
    GENERAL = "general"      # 通用型专家

class ExpertStatus(str, Enum):
    """专家状态"""
    ACTIVE = "active"        # 激活状态
    INACTIVE = "inactive"    # 未激活
    TRAINING = "training"    # 训练中
    ERROR = "error"          # 错误状态

# MongoEngine 模型
class Expert(Document):
    """专家配置模型"""
    meta = {
        'collection': 'experts',
        'indexes': [
            'name',
            'expert_type',
            'status',
            'created_by'
        ]
    }
    
    # 基本信息
    name = StringField(required=True, unique=True)           # 专家名称
    description = StringField(required=True)                 # 专家描述
    expert_type = StringField(required=True, choices=[t.value for t in ExpertType])  # 专家类型
    version = StringField(default="1.0.0")                  # 版本号
    
    # 配置信息
    llm_config = DictField(required=True)                   # LLM配置
    embedding_config = DictField()                          # Embedding配置
    workflow_config = DictField()                           # 工作流配置
    prompt_templates = DictField()                          # 提示词模板
    tools = ListField(DictField())                         # 可用工具列表
    
    # 专家能力
    capabilities = ListField(StringField())                 # 专家能力列表
    knowledge_bases = ListField(StringField())              # 关联的知识库ID列表
    supported_languages = ListField(StringField())          # 支持的语言
    
    # 性能参数
    max_context_length = IntField(default=4096)            # 最大上下文长度
    response_timeout = IntField(default=30)                # 响应超时时间(秒)
    confidence_threshold = FloatField(default=0.7)         # 置信度阈值
    
    # 状态信息
    status = StringField(                                  # 专家状态
        choices=[s.value for s in ExpertStatus],
        default=ExpertStatus.INACTIVE.value
    )
    is_public = BooleanField(default=False)               # 是否公开
    
    # 统计信息
    total_calls = IntField(default=0)                     # 总调用次数
    success_calls = IntField(default=0)                   # 成功调用次数
    average_latency = FloatField(default=0.0)             # 平均延迟(秒)
    
    # 管理信息
    created_by = IntField(required=True)                  # 创建者ID
    created_at = DateTimeField(default=datetime.now)      # 创建时间
    updated_at = DateTimeField(default=datetime.now)      # 更新时间
    last_active = DateTimeField()                         # 最后活动时间

# Pydantic 模型
class ExpertBase(BaseModel):
    """专家基础模型"""
    name: str
    description: str
    expert_type: ExpertType
    version: str = "1.0.0"
    
    llm_config: Dict[str, Any]
    embedding_config: Optional[Dict[str, Any]] = None
    workflow_config: Optional[Dict[str, Any]] = None
    prompt_templates: Optional[Dict[str, str]] = None
    tools: Optional[List[Dict[str, Any]]] = None
    
    capabilities: Optional[List[str]] = Field(default_factory=list)
    knowledge_bases: Optional[List[str]] = Field(default_factory=list)
    supported_languages: Optional[List[str]] = Field(default_factory=list)
    
    max_context_length: int = 4096
    response_timeout: int = 30
    confidence_threshold: float = 0.7
    
    is_public: bool = False

class ExpertCreate(ExpertBase):
    """创建专家模型"""
    pass

class ExpertUpdate(BaseModel):
    """更新专家模型"""
    description: Optional[str] = None
    version: Optional[str] = None
    
    llm_config: Optional[Dict[str, Any]] = None
    embedding_config: Optional[Dict[str, Any]] = None
    workflow_config: Optional[Dict[str, Any]] = None
    prompt_templates: Optional[Dict[str, str]] = None
    tools: Optional[List[Dict[str, Any]]] = None
    
    capabilities: Optional[List[str]] = None
    knowledge_bases: Optional[List[str]] = None
    supported_languages: Optional[List[str]] = None
    
    max_context_length: Optional[int] = None
    response_timeout: Optional[int] = None
    confidence_threshold: Optional[float] = None
    
    status: Optional[ExpertStatus] = None
    is_public: Optional[bool] = None

class ExpertResponse(ExpertBase):
    """专家响应模型"""
    id: str
    status: ExpertStatus
    total_calls: int
    success_calls: int
    average_latency: float
    created_by: int
    created_at: datetime
    updated_at: datetime
    last_active: Optional[datetime] = None

    class Config:
        from_attributes = True

class ExpertStats(BaseModel):
    """专家统计信息"""
    total_calls: int
    success_calls: int
    error_rate: float
    average_latency: float
    last_active: Optional[datetime] 