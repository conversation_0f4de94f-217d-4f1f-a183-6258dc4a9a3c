## 运行
```bash
# 在backend目录下
python -m app.engines.indexing.base_indexer
```


整体流程：
1. 获取文件
2. 根据不同的文件类型，使用不同解析方法进行解析。
3. 不同的文件类型，也需要判断使用什么方法，主要是确认是否启用ocr
4. 如果不适用则，使用文件解析直接解析
5. 如果需要使用ocr，则使用ocr进行解析
6. 将解析后的文本进行切分和索引
7. 不同的切分处理方法，生成不同的chunk及索引，不混用



es 建索引
PUT /wise_agent_chunk_index
{
  "mappings": {
    "properties": {
      "id": {"type": "keyword"},
      "file_id": {"type": "keyword"},
      "chunk_id": {"type": "keyword"},
      "knowledge_base_id": {"type": "keyword"},
      "chunk_index": {"type": "integer"},
      "index_content": {
        "type": "text",
        "analyzer": "ik_max_word",  
        "search_analyzer": "ik_smart"  
      },
      "embedding": {
        "type": "dense_vector",
        "dims": 1536
        
      },
      "chunk_type": {"type": "keyword"},
      "created_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss"
      }
    }
  }
}

## 开发时需要的配置文件选项

在开发过程中，您需要确保安装以下依赖项：

- `mongoengine`
- `elasticsearch==7.15.1`

这些依赖项可以通过在 `requirements.txt` 文件中列出的包来安装。

## 数据更新时需要遵从的枚举常量

在数据更新过程中，以下枚举常量可能会被使用：

- **文件处理状态枚举 (`FileStatus`)**：
  - `WAITING`：等待处理
  - `COMPLETED`：处理完成
  - `PROCESSING`：处理中
  - `ERROR`：处理错误

- **chunk 类型枚举 (`chunk_type`)**：
  - `BASE`：基础
  - `GRAPH`：图形
  - `SEMANTIC`：语义
  - `AUDIO`：音频
  - `IMAGE`：图像
  - `VIDEO`：视频
  - `QA`：问答

## 需要做的准备工作

在开始使用系统之前，您需要进行以下准备工作：

- **Elasticsearch 建索引**：
  - 您需要在 Elasticsearch 中创建一个索引，名为 `wise_agent_index`，并配置相应的映射（mapping），如在 `README.md` 中所示。

## 启动命令

在 `backend` 目录下，您可以使用以下命令来启动索引器：

```bash
python -m app.engines.indexing.base_indexer
```

## 立即执行任务命令

在 `backend` 目录下，您可以使用以下命令来立即执行任务：

```bash
python -m app.engines.indexing.base_indexer -immediate
```

## 数据处理流程

在数据处理过程中，系统会根据文件类型和内容特征选择适当的解析和索引方法。以下是数据处理的基本流程：

1. **文件获取**：从指定的存储位置获取待处理的文件。
2. **文件解析**：根据文件类型（如 PDF、Word、TXT 等）选择合适的解析方法。某些文件可能需要使用 OCR 技术进行解析。
3. **文本切分**：将解析后的文本内容进行切分，生成多个文本块（chunk）。
4. **索引生成**：根据不同的切分方法，生成相应的索引。每个索引方法的返回值需要遵循统一的格式：

   ```json
   {
     "answer": "文本内容",
     "question": "",
     "chunk_type": "BASE",
     "chunk_index": 1,
     "chunk_index_list": [
       {
         "index_content": "文本内容"
       }
     ]
   }
   ```

5. **索引存储**：将生成的索引存储到 Elasticsearch 中，确保数据的可检索性。

6. **错误处理**：在处理过程中，如果发生错误，系统会记录错误日志并进行相应的处理。

## 索引建立流程图

以下是整个索引建立的流程图，帮助您理解系统如何处理文件并生成索引：

```mermaid
graph TD;
    A[获取文件] --> B[文件解析]
    B --> C{是否需要OCR?}
    C -->|是| D[使用OCR解析]
    C -->|否| E[直接解析]
    %% D --> F[文本切分]
    D -->F[索引处理]
  E-->G
    F --> G[生成索引]
    G --> H{索引类型}
    H -->|基础索引| I[存储到MongoDB和Elasticsearch]
    H -->|图索引| J[图索引处理]
    H -->|语义索引| K[语义索引处理]
    I --> L[索引建立完成]
    J --> L
    K --> L
```

在这个流程中，系统首先获取待处理的文件，然后根据文件类型决定是否需要使用OCR进行解析。解析后的文本会被切分成多个文本块（chunk），并根据不同的索引类型生成相应的索引，最后将索引存储到MongoDB和Elasticsearch中。



# base_indexer.py 执行逻辑
```mermaid
sequenceDiagram
    participant Main as Main程序
    participant BaseIndexer as BaseIndexer
    participant MongoDB as MongoDB
    participant ES as Elasticsearch
    participant FileProcessor as 文件处理器
    participant ChunkBuilder as 文本块生成器
    participant IndexBuilder as 索引构建器

    Main->>BaseIndexer: 1. 启动程序 main()
    
    alt 立即执行模式 (--immediate)
        Main->>BaseIndexer: 2.1 直接执行 execute()
    else 定时任务模式
        Main->>BaseIndexer: 2.2 启动定时任务 (每10秒)
    end
    
    BaseIndexer->>MongoDB: 3. 建立数据库连接 connect()
    BaseIndexer->>ES: 4. 建立ES连接
    
    BaseIndexer->>MongoDB: 5. 获取待处理文件 get_files_to_process()
    MongoDB-->>BaseIndexer: 返回待处理文件列表 (WAITING状态)
    
    loop 对每个文件
        BaseIndexer->>MongoDB: 6. 获取知识库信息 _get_knowledge_base()
        MongoDB-->>BaseIndexer: 返回知识库配置
        
        BaseIndexer->>FileProcessor: 7. 下载并处理文件 process_file()
        
        par 并行处理不同索引类型
            alt 基础索引 (basic_index)
                FileProcessor->>ChunkBuilder: 8.1 处理基础索引
                ChunkBuilder-->>FileProcessor: 返回文本块列表
            end
            
            alt 图索引 (graph_index)
                FileProcessor->>ChunkBuilder: 8.2 处理图索引
                ChunkBuilder-->>FileProcessor: 返回图索引结果
            end
            
            alt 语义索引 (semantic_index)
                FileProcessor->>ChunkBuilder: 8.3 处理语义索引
                ChunkBuilder-->>FileProcessor: 返回语义索引结果
            end
        end
        
        FileProcessor->>IndexBuilder: 9. 构建索引 build_chunk_index()
        IndexBuilder->>MongoDB: 10.1 保存文本块到MongoDB
        IndexBuilder->>ES: 10.2 保存索引到ES
        
        BaseIndexer->>MongoDB: 11. 更新知识库统计信息
        BaseIndexer->>MongoDB: 12. 更新文件状态为COMPLETED
    end
    
    BaseIndexer->>MongoDB: 13. 断开数据库连接 disconnect()
    BaseIndexer->>ES: 14. 断开ES连接
```



需要按照
brew install poppler
pdfinfo -v


pip install pdf2image==1.17.0  -i https://pypi.tuna.tsinghua.edu.cn/simple