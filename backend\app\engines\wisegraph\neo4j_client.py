"""
Neo4j图数据库客户端
"""

import asyncio
from typing import List, Dict, Any, Optional
from neo4j import AsyncGraphDatabase
from .settings import wisegraph_settings
from loguru import logger

class Neo4jClient:
    """Neo4j图数据库客户端"""
    
    def __init__(self):
        self.config = wisegraph_settings.neo4j_config
        self.driver = None
        self.database = self.config["database"]
    
    async def connect(self):
        """连接到Neo4j数据库"""
        try:
            self.driver = AsyncGraphDatabase.driver(
                self.config["uri"],
                auth=(self.config["username"], self.config["password"])
            )
            # 测试连接
            await self.driver.verify_connectivity()
            logger.info("Neo4j连接成功")
        except Exception as e:
            logger.error(f"Neo4j连接失败: {e}")
            raise
    
    async def close(self):
        """关闭数据库连接"""
        if self.driver:
            await self.driver.close()
            logger.info("Neo4j连接已关闭")
    
    async def clear_database(self):
        """清空数据库"""
        async with self.driver.session(database=self.database) as session:
            await session.run("MATCH (n) DETACH DELETE n")
            logger.info("数据库已清空")
    
    async def create_entity(self, entity: Dict[str, Any], chunk_id: str, knowledge_base_id: str = None) -> bool:
        """
        创建实体节点

        Args:
            entity: 实体信息字典
            chunk_id: 文本块ID
            knowledge_base_id: 知识库ID

        Returns:
            是否创建成功
        """
        try:
            async with self.driver.session(database=self.database) as session:
                # 构建动态标签
                labels = ["Entity"]
                if knowledge_base_id:
                    labels.append(f"KB_{knowledge_base_id}")

                # 使用动态标签创建实体
                labels_str = ":".join(labels)
                query = f"""
                MERGE (e:{labels_str} {{entity_id: $entity_id}})
                SET e.entity_name = $name,
                    e.entity_type = $type,
                    e.description = $description,
                    e.knowledge_base_id = $knowledge_base_id,
                    e.chunk_ids = CASE
                        WHEN e.chunk_ids IS NULL THEN [$chunk_id]
                        WHEN NOT $chunk_id IN e.chunk_ids THEN e.chunk_ids + [$chunk_id]
                        ELSE e.chunk_ids
                    END
                RETURN e
                """

                await session.run(query, {
                    "entity_id": entity["name"],
                    "name": entity["name"],
                    "type": entity.get("type", "unknown"),
                    "description": entity.get("description", ""),
                    "knowledge_base_id": knowledge_base_id,
                    "chunk_id": chunk_id
                })
                
                return True
                
        except Exception as e:
            logger.error(f"创建实体失败: {e}")
            return False
    
    async def create_relationship(self, relationship: Dict[str, Any], chunk_id: str, knowledge_base_id: str = None) -> bool:
        """
        创建关系

        Args:
            relationship: 关系信息字典
            chunk_id: 文本块ID
            knowledge_base_id: 知识库ID

        Returns:
            是否创建成功
        """
        try:
            async with self.driver.session(database=self.database) as session:
                # 构建动态标签
                labels = ["Entity"]
                if knowledge_base_id:
                    labels.append(f"KB_{knowledge_base_id}")
                labels_str = ":".join(labels)

                # 首先确保源实体和目标实体存在
                ensure_entities_query = f"""
                MERGE (source:{labels_str} {{entity_id: $source_id}})
                SET source.entity_name = $source_id,
                    source.knowledge_base_id = $knowledge_base_id
                MERGE (target:{labels_str} {{entity_id: $target_id}})
                SET target.entity_name = $target_id,
                    target.knowledge_base_id = $knowledge_base_id
                """

                await session.run(ensure_entities_query, {
                    "source_id": relationship["source"],
                    "target_id": relationship["target"],
                    "knowledge_base_id": knowledge_base_id
                })

                # 创建关系（使用单一关系类型 + 属性区分）
                create_rel_query = f"""
                MATCH (source:{labels_str} {{entity_id: $source_id}})
                MATCH (target:{labels_str} {{entity_id: $target_id}})
                MERGE (source)-[r:RELATED {{
                    relation_type: $relation_type_value,
                    knowledge_base_id: $knowledge_base_id
                }}]->(target)
                SET r.description = $description,
                    r.keywords = $relation_type_value,
                    r.chunk_ids = CASE
                        WHEN r.chunk_ids IS NULL THEN [$chunk_id]
                        WHEN NOT $chunk_id IN r.chunk_ids THEN r.chunk_ids + [$chunk_id]
                        ELSE r.chunk_ids
                    END
                RETURN r
                """

                await session.run(create_rel_query, {
                    "source_id": relationship["source"],
                    "target_id": relationship["target"],
                    "relation_type_value": relationship.get("relation", "RELATED"),
                    "description": relationship.get("description", ""),
                    "knowledge_base_id": knowledge_base_id,
                    "chunk_id": chunk_id
                })
                
                return True
                
        except Exception as e:
            logger.error(f"创建关系失败: {e}")
            return False
    
    async def batch_create_entities_and_relationships(self, entities: List[Dict[str, Any]],
                                                    relationships: List[Dict[str, Any]],
                                                    chunk_id: str,
                                                    knowledge_base_id: str = None) -> Dict[str, int]:
        """
        批量创建实体和关系

        Args:
            entities: 实体列表
            relationships: 关系列表
            chunk_id: 文本块ID
            knowledge_base_id: 知识库ID

        Returns:
            创建统计信息
        """
        entity_count = 0
        relationship_count = 0
        
        # 批量创建实体
        for entity in entities:
            if await self.create_entity(entity, chunk_id, knowledge_base_id):
                entity_count += 1

        # 批量创建关系
        for relationship in relationships:
            if await self.create_relationship(relationship, chunk_id, knowledge_base_id):
                relationship_count += 1
        
        logger.info(f"批量创建完成 - 实体: {entity_count}, 关系: {relationship_count}")
        
        return {
            "entities_created": entity_count,
            "relationships_created": relationship_count
        }
    
    async def get_database_stats(self) -> Dict[str, int]:
        """获取数据库统计信息"""
        try:
            async with self.driver.session(database=self.database) as session:
                # 统计节点数量
                node_result = await session.run("MATCH (n) RETURN count(n) as count")
                node_record = await node_result.single()
                node_count = node_record["count"] if node_record else 0
                
                # 统计关系数量
                rel_result = await session.run("MATCH ()-[r]->() RETURN count(r) as count")
                rel_record = await rel_result.single()
                rel_count = rel_record["count"] if rel_record else 0
                
                return {
                    "nodes": node_count,
                    "relationships": rel_count
                }
                
        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            return {"nodes": 0, "relationships": 0}

    async def get_entity_subgraph(self, entity_ids: List[str], max_depth: int = 2) -> Dict[str, Any]:
        """
        获取实体的子图

        Args:
            entity_ids: 实体ID列表
            max_depth: 最大深度

        Returns:
            子图数据
        """
        try:
            async with self.driver.session(database=self.database) as session:
                query = f"""
                MATCH path = (start:Entity)-[*1..{max_depth}]-(connected:Entity)
                WHERE start.entity_id IN $entity_ids
                WITH nodes(path) as path_nodes, relationships(path) as path_rels
                UNWIND path_nodes as node
                WITH collect(DISTINCT {{
                    entity_id: node.entity_id,
                    entity_name: node.entity_name,
                    entity_type: node.entity_type,
                    description: node.description,
                    chunk_ids: node.chunk_ids
                }}) as nodes, path_rels
                UNWIND path_rels as rel
                WITH nodes, collect(DISTINCT {{
                    source: startNode(rel).entity_id,
                    target: endNode(rel).entity_id,
                    relation_type: rel.relation_type,
                    description: rel.description,
                    chunk_ids: rel.chunk_ids
                }}) as relationships
                RETURN nodes, relationships
                """

                result = await session.run(query, {"entity_ids": entity_ids})
                records = await result.data()

                if records:
                    # 合并所有记录的节点和关系
                    all_nodes = []
                    all_relationships = []

                    for record in records:
                        nodes = record.get("nodes", [])
                        relationships = record.get("relationships", [])

                        if nodes:
                            all_nodes.extend(nodes)
                        if relationships:
                            all_relationships.extend(relationships)

                    # 去重
                    unique_nodes = []
                    seen_node_ids = set()
                    for node in all_nodes:
                        if node["entity_id"] not in seen_node_ids:
                            unique_nodes.append(node)
                            seen_node_ids.add(node["entity_id"])

                    unique_relationships = []
                    seen_rel_keys = set()
                    for rel in all_relationships:
                        rel_key = f"{rel['source']}-{rel['relation_type']}-{rel['target']}"
                        if rel_key not in seen_rel_keys:
                            unique_relationships.append(rel)
                            seen_rel_keys.add(rel_key)

                    return {
                        "nodes": unique_nodes,
                        "relationships": unique_relationships
                    }
                else:
                    return {"nodes": [], "relationships": []}

        except Exception as e:
            logger.error(f"获取实体子图失败: {e}")
            return {"nodes": [], "relationships": []}

    async def search_entities_by_names_and_kb(self, entity_names: List[str], knowledge_base_id: str) -> List[Dict[str, Any]]:
        """
        根据实体名称和知识库ID搜索实体

        Args:
            entity_names: 实体名称列表
            knowledge_base_id: 知识库ID

        Returns:
            匹配的实体列表
        """
        try:
            async with self.driver.session(database=self.database) as session:
                kb_label = f"KB_{knowledge_base_id}"
                query = f"""
                MATCH (e:{kb_label})
                WHERE e.entity_name IN $entity_names
                   OR ANY(name IN $entity_names WHERE e.entity_name CONTAINS name)
                   OR ANY(name IN $entity_names WHERE name CONTAINS e.entity_name)
                RETURN e.entity_id as entity_id,
                       e.entity_name as entity_name,
                       e.entity_type as entity_type,
                       e.description as description,
                       e.knowledge_base_id as knowledge_base_id,
                       e.chunk_ids as chunk_ids
                """

                result = await session.run(query, {"entity_names": entity_names})
                entities = []
                async for record in result:
                    entities.append({
                        "entity_id": record["entity_id"],
                        "entity_name": record["entity_name"],
                        "entity_type": record["entity_type"],
                        "description": record["description"],
                        "knowledge_base_id": record["knowledge_base_id"],
                        "chunk_ids": record["chunk_ids"] or []
                    })

                return entities

        except Exception as e:
            logger.error(f"按知识库搜索实体失败: {e}")
            return []

    async def get_entity_subgraph_by_kb(self, entity_ids: List[str], knowledge_base_id: str, max_depth: int = 2) -> Dict[str, Any]:
        """
        获取知识库内实体的子图

        Args:
            entity_ids: 实体ID列表
            knowledge_base_id: 知识库ID
            max_depth: 最大深度

        Returns:
            子图数据
        """
        try:
            async with self.driver.session(database=self.database) as session:
                kb_label = f"KB_{knowledge_base_id}"
                query = f"""
                // 首先获取起始节点
                MATCH (start:{kb_label})
                WHERE start.entity_id IN $entity_ids

                // 获取连接的节点和路径（可选匹配）
                OPTIONAL MATCH path = (start)-[*1..{max_depth}]-(connected:{kb_label})
                WHERE ALL(r IN relationships(path) WHERE r.knowledge_base_id = $knowledge_base_id)

                // 收集所有节点（包括起始节点）
                WITH start,
                     CASE WHEN path IS NOT NULL
                          THEN nodes(path)
                          ELSE [start]
                     END as path_nodes,
                     CASE WHEN path IS NOT NULL
                          THEN relationships(path)
                          ELSE []
                     END as path_rels

                UNWIND path_nodes as node
                WITH collect(DISTINCT {{
                    entity_id: node.entity_id,
                    entity_name: node.entity_name,
                    entity_type: node.entity_type,
                    description: node.description,
                    knowledge_base_id: node.knowledge_base_id,
                    chunk_ids: node.chunk_ids
                }}) as nodes, path_rels

                // 处理关系（如果存在）
                WITH nodes,
                     CASE WHEN size(path_rels) > 0
                          THEN [rel IN path_rels | {{
                              source: startNode(rel).entity_id,
                              target: endNode(rel).entity_id,
                              relation_type: rel.relation_type,
                              description: rel.description,
                              knowledge_base_id: rel.knowledge_base_id,
                              chunk_ids: rel.chunk_ids
                          }}]
                          ELSE []
                     END as relationships

                RETURN nodes, relationships
                """

                result = await session.run(query, {
                    "entity_ids": entity_ids,
                    "knowledge_base_id": knowledge_base_id
                })
                records = await result.data()

                if records:
                    # 合并所有记录的节点和关系
                    all_nodes = []
                    all_relationships = []

                    for record in records:
                        nodes = record.get("nodes", [])
                        relationships = record.get("relationships", [])

                        if nodes:
                            all_nodes.extend(nodes)
                        if relationships:
                            all_relationships.extend(relationships)

                    # 去重
                    unique_nodes = []
                    seen_node_ids = set()
                    for node in all_nodes:
                        if node["entity_id"] not in seen_node_ids:
                            unique_nodes.append(node)
                            seen_node_ids.add(node["entity_id"])

                    unique_relationships = []
                    seen_rel_keys = set()
                    for rel in all_relationships:
                        rel_key = f"{rel['source']}-{rel['relation_type']}-{rel['target']}"
                        if rel_key not in seen_rel_keys:
                            unique_relationships.append(rel)
                            seen_rel_keys.add(rel_key)

                    return {
                        "nodes": unique_nodes,
                        "relationships": unique_relationships
                    }
                else:
                    return {"nodes": [], "relationships": []}

        except Exception as e:
            logger.error(f"获取知识库实体子图失败: {e}")
            return {"nodes": [], "relationships": []}

    async def get_kb_graph_overview(self, knowledge_base_id: str, limit: int = 100, entity_types: List[str] = None) -> Dict[str, Any]:
        """
        获取知识库图谱概览

        Args:
            knowledge_base_id: 知识库ID
            limit: 最大返回节点数
            entity_types: 过滤的实体类型

        Returns:
            图谱概览数据
        """
        try:
            async with self.driver.session(database=self.database) as session:
                kb_label = f"KB_{knowledge_base_id}"

                # 获取节点（使用标签查询，不需要额外的 WHERE 条件）
                where_clause = "WHERE 1=1"  # 默认条件
                if entity_types:
                    type_conditions = [f"n.entity_type = '{et}'" for et in entity_types]
                    where_clause = f"WHERE ({' OR '.join(type_conditions)})"

                nodes_query = f"""
                MATCH (n:{kb_label})
                {where_clause}
                RETURN n.entity_id as entity_id,
                       n.entity_name as entity_name,
                       n.entity_type as entity_type,
                       n.description as description,
                       n.knowledge_base_id as knowledge_base_id,
                       n.chunk_ids as chunk_ids
                LIMIT {limit}
                """

                nodes_result = await session.run(nodes_query)
                nodes = []
                node_ids = []
                async for record in nodes_result:
                    node_data = {
                        "entity_id": record["entity_id"],
                        "entity_name": record["entity_name"],
                        "entity_type": record["entity_type"],
                        "description": record["description"],
                        "knowledge_base_id": record["knowledge_base_id"],
                        "chunk_ids": record["chunk_ids"] or []
                    }
                    nodes.append(node_data)
                    node_ids.append(record["entity_id"])

                # 获取这些节点之间的关系
                if node_ids:
                    rels_query = f"""
                    MATCH (source:{kb_label})-[r:RELATED]->(target:{kb_label})
                    WHERE source.entity_id IN $node_ids
                      AND target.entity_id IN $node_ids
                      AND r.knowledge_base_id = $knowledge_base_id
                    RETURN source.entity_id as source,
                           target.entity_id as target,
                           r.relation_type as relation_type,
                           r.description as description,
                           r.knowledge_base_id as knowledge_base_id,
                           r.chunk_ids as chunk_ids
                    """

                    rels_result = await session.run(rels_query, {
                        "knowledge_base_id": knowledge_base_id,
                        "node_ids": node_ids
                    })

                    relationships = []
                    async for record in rels_result:
                        relationships.append({
                            "source": record["source"],
                            "target": record["target"],
                            "relation_type": record["relation_type"],
                            "description": record["description"],
                            "knowledge_base_id": record["knowledge_base_id"],
                            "chunk_ids": record["chunk_ids"] or []
                        })
                else:
                    relationships = []

                return {
                    "nodes": nodes,
                    "relationships": relationships
                }

        except Exception as e:
            logger.error(f"获取知识库图谱概览失败: {e}")
            return {"nodes": [], "relationships": []}

    async def get_kb_stats(self, knowledge_base_id: str) -> Dict[str, int]:
        """获取知识库统计信息"""
        try:
            async with self.driver.session(database=self.database) as session:
                kb_label = f"KB_{knowledge_base_id}"

                # 统计节点数量
                node_result = await session.run(f"MATCH (n:{kb_label}) RETURN count(n) as count")
                node_record = await node_result.single()
                node_count = node_record["count"] if node_record else 0

                # 统计关系数量
                rel_result = await session.run(
                    "MATCH ()-[r:RELATED {knowledge_base_id: $kb_id}]->() RETURN count(r) as count",
                    {"kb_id": knowledge_base_id}
                )
                rel_record = await rel_result.single()
                rel_count = rel_record["count"] if rel_record else 0

                return {
                    "nodes": node_count,
                    "relationships": rel_count
                }

        except Exception as e:
            logger.error(f"获取知识库统计失败: {e}")
            return {"nodes": 0, "relationships": 0}

    async def get_kb_entity_type_stats(self, knowledge_base_id: str) -> List[Dict[str, Any]]:
        """获取知识库实体类型统计"""
        try:
            async with self.driver.session(database=self.database) as session:
                kb_label = f"KB_{knowledge_base_id}"
                query = f"""
                MATCH (n:{kb_label})
                RETURN n.entity_type as type, count(*) as count
                ORDER BY count DESC
                """

                result = await session.run(query)
                entity_types = []
                async for record in result:
                    entity_types.append({
                        "type": record["type"],
                        "count": record["count"]
                    })

                return entity_types

        except Exception as e:
            logger.error(f"获取知识库实体类型统计失败: {e}")
            return []

    async def get_kb_relation_type_stats(self, knowledge_base_id: str) -> List[Dict[str, Any]]:
        """获取知识库关系类型统计"""
        try:
            async with self.driver.session(database=self.database) as session:
                query = """
                MATCH ()-[r:RELATED {knowledge_base_id: $kb_id}]->()
                RETURN r.relation_type as type, count(*) as count
                ORDER BY count DESC
                """

                result = await session.run(query, {"kb_id": knowledge_base_id})
                relation_types = []
                async for record in result:
                    relation_types.append({
                        "type": record["type"],
                        "count": record["count"]
                    })

                return relation_types

        except Exception as e:
            logger.error(f"获取知识库关系类型统计失败: {e}")
            return []

    async def search_entities_by_names_with_kb(self, entity_names: List[str], knowledge_base_id: str = None) -> List[Dict[str, Any]]:
        """
        根据实体名称搜索实体（支持知识库过滤）

        Args:
            entity_names: 实体名称列表
            knowledge_base_id: 知识库ID（可选）

        Returns:
            匹配的实体列表
        """
        try:
            async with self.driver.session(database=self.database) as session:
                if knowledge_base_id:
                    # 使用知识库标签查询
                    kb_label = f"KB_{knowledge_base_id}"
                    query = f"""
                    MATCH (e:{kb_label})
                    WHERE e.entity_name IN $entity_names
                       OR ANY(name IN $entity_names WHERE e.entity_name CONTAINS name)
                       OR ANY(name IN $entity_names WHERE name CONTAINS e.entity_name)
                    RETURN e.entity_id as entity_id,
                           e.entity_name as entity_name,
                           e.entity_type as entity_type,
                           e.description as description,
                           e.knowledge_base_id as knowledge_base_id,
                           e.chunk_ids as chunk_ids
                    """
                else:
                    # 不过滤知识库
                    query = """
                    MATCH (e:Entity)
                    WHERE e.entity_name IN $entity_names
                       OR ANY(name IN $entity_names WHERE e.entity_name CONTAINS name)
                       OR ANY(name IN $entity_names WHERE name CONTAINS e.entity_name)
                    RETURN e.entity_id as entity_id,
                           e.entity_name as entity_name,
                           e.entity_type as entity_type,
                           e.description as description,
                           e.knowledge_base_id as knowledge_base_id,
                           e.chunk_ids as chunk_ids
                    """

                result = await session.run(query, {"entity_names": entity_names})
                entities = []
                async for record in result:
                    entities.append({
                        "entity_id": record["entity_id"],
                        "entity_name": record["entity_name"],
                        "entity_type": record["entity_type"],
                        "description": record["description"],
                        "knowledge_base_id": record["knowledge_base_id"],
                        "chunk_ids": record["chunk_ids"] or []
                    })

                return entities

        except Exception as e:
            logger.error(f"按名称搜索实体失败: {e}")
            return []

    async def search_entities_by_keywords_with_kb(self, keywords: List[str], knowledge_base_id: str = None) -> List[Dict[str, Any]]:
        """
        根据关键词搜索实体（支持知识库过滤）

        Args:
            keywords: 关键词列表
            knowledge_base_id: 知识库ID（可选）

        Returns:
            匹配的实体列表
        """
        try:
            async with self.driver.session(database=self.database) as session:
                if knowledge_base_id:
                    # 使用知识库标签查询
                    kb_label = f"KB_{knowledge_base_id}"
                    query = f"""
                    MATCH (e:{kb_label})
                    WHERE ANY(keyword IN $keywords WHERE
                        e.entity_name CONTAINS keyword
                        OR e.description CONTAINS keyword
                    )
                    RETURN e.entity_id as entity_id,
                           e.entity_name as entity_name,
                           e.entity_type as entity_type,
                           e.description as description,
                           e.knowledge_base_id as knowledge_base_id,
                           e.chunk_ids as chunk_ids
                    """
                else:
                    # 不过滤知识库
                    query = """
                    MATCH (e:Entity)
                    WHERE ANY(keyword IN $keywords WHERE
                        e.entity_name CONTAINS keyword
                        OR e.description CONTAINS keyword
                    )
                    RETURN e.entity_id as entity_id,
                           e.entity_name as entity_name,
                           e.entity_type as entity_type,
                           e.description as description,
                           e.knowledge_base_id as knowledge_base_id,
                           e.chunk_ids as chunk_ids
                    """

                result = await session.run(query, {"keywords": keywords})
                entities = []
                async for record in result:
                    entities.append({
                        "entity_id": record["entity_id"],
                        "entity_name": record["entity_name"],
                        "entity_type": record["entity_type"],
                        "description": record["description"],
                        "knowledge_base_id": record["knowledge_base_id"],
                        "chunk_ids": record["chunk_ids"] or []
                    })

                return entities

        except Exception as e:
            logger.error(f"按关键词搜索实体失败: {e}")
            return []


neo4j = Neo4jClient()
