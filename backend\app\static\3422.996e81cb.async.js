"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3422],{43422:function(e,t,n){function r(e,t){const n=String(e);if("string"!=typeof t)throw new TypeError("Expected character");let r=0,o=n.indexOf(t);for(;-1!==o;)r++,o=n.indexOf(t,o+t.length);return r}n.d(t,{Z:function(){return gn}});var o=n(24345);l(/[A-Za-z]/),l(/[\dA-Za-z]/),l(/[#-'*+\--9=?A-Z^-~]/);l(/\d/),l(/[\dA-Fa-f]/),l(/[!-/:-@[-`{-~]/);const i=l(/\p{P}|\p{S}/u),c=l(/\s/);function l(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}const s=function(e){if(null==e)return a;if("function"==typeof e)return u(e);if("object"==typeof e)return Array.isArray(e)?function(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=s(e[n]);return u(r);function r(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}}(e):function(e){const t=e;return u(n);function n(n){const r=n;let o;for(o in e)if(r[o]!==t[o])return!1;return!0}}(e);if("string"==typeof e)return function(e){return u(t);function t(t){return t&&t.type===e}}(e);throw new Error("Expected function, string, or object as test")};function u(e){return function(t,n,r){return Boolean(f(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function a(){return!0}function f(e){return null!==e&&"object"==typeof e&&"type"in e}const d=[];function h(e,t,n,r){let o;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):o=t;const i=s(o),c=r?-1:1;!function e(o,l,s){const u=o&&"object"==typeof o?o:{};if("string"==typeof u.type){const e="string"==typeof u.tagName?u.tagName:"string"==typeof u.name?u.name:void 0;Object.defineProperty(a,"name",{value:"node ("+o.type+(e?"<"+e+">":"")+")"})}return a;function a(){let u,a,f,h=d;if((!t||i(o,l,s[s.length-1]||void 0))&&(h=function(e){if(Array.isArray(e))return e;if("number"==typeof e)return[true,e];return null==e?d:[e]}(n(o,s)),false===h[0]))return h;if("children"in o&&o.children){const t=o;if(t.children&&"skip"!==h[0])for(a=(r?t.children.length:-1)+c,f=s.concat(t);a>-1&&a<t.children.length;){const n=t.children[a];if(u=e(n,a,f)(),false===u[0])return u;a="number"==typeof u[1]?u[1]:a+c}}return h}}(e,void 0,[])()}function p(e,t,n){const r=s((n||{}).ignore||[]),o=function(e){const t=[];if(!Array.isArray(e))throw new TypeError("Expected find and replace tuple or list of tuples");const n=!e[0]||Array.isArray(e[0])?e:[e];let r=-1;for(;++r<n.length;){const e=n[r];t.push([m(e[0]),g(e[1])])}return t}(t);let i=-1;for(;++i<o.length;)h(e,"text",c);function c(e,t){let n,c=-1;for(;++c<t.length;){const e=t[c],o=n?n.children:void 0;if(r(e,o?o.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){const n=t[t.length-1],r=o[i][0],c=o[i][1];let l=0;const s=n.children.indexOf(e);let u=!1,a=[];r.lastIndex=0;let f=r.exec(e.value);for(;f;){const n=f.index,o={index:f.index,input:f.input,stack:[...t,e]};let i=c(...f,o);if("string"==typeof i&&(i=i.length>0?{type:"text",value:i}:void 0),!1===i?r.lastIndex=n+1:(l!==n&&a.push({type:"text",value:e.value.slice(l,n)}),Array.isArray(i)?a.push(...i):i&&a.push(i),l=n+f[0].length,u=!0),!r.global)break;f=r.exec(e.value)}u?(l<e.value.length&&a.push({type:"text",value:e.value.slice(l)}),n.children.splice(s,1,...a)):a=[e];return s+a.length}(e,t)}}function m(e){return"string"==typeof e?new RegExp(function(e){if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(e),"g"):e}function g(e){return"function"==typeof e?e:function(){return e}}const k="phrasing",b=["autolink","link","image","label"];function v(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function x(e){this.config.enter.autolinkProtocol.call(this,e)}function y(e){this.config.exit.autolinkProtocol.call(this,e)}function w(e){this.config.exit.data.call(this,e);const t=this.stack[this.stack.length-1];(0,o.ok)("link"===t.type),t.url="http://"+this.sliceSerialize(e)}function C(e){this.config.exit.autolinkEmail.call(this,e)}function A(e){this.exit(e)}function F(e){p(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,I],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,S]],{ignore:["link","linkReference"]})}function I(e,t,n,o,i){let c="";if(!E(i))return!1;if(/^w/i.test(t)&&(n=t+n,t="",c="http://"),!function(e){const t=e.split(".");if(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))return!1;return!0}(n))return!1;const l=function(e){const t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],o=n.indexOf(")");const i=r(e,"(");let c=r(e,")");for(;-1!==o&&i>c;)e+=n.slice(0,o+1),n=n.slice(o+1),o=n.indexOf(")"),c++;return[e,n]}(n+o);if(!l[0])return!1;const s={type:"link",title:null,url:c+t+l[0],children:[{type:"text",value:t+l[0]}]};return l[1]?[s,{type:"text",value:l[1]}]:s}function S(e,t,n,r){return!(!E(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function E(e,t){const n=e.input.charCodeAt(e.index-1);return(0===e.index||c(n)||i(n))&&(!t||47!==n)}var L=n(11098);function D(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function T(){this.buffer()}function z(e){const t=this.resume(),n=this.stack[this.stack.length-1];(0,o.ok)("footnoteDefinition"===n.type),n.label=t,n.identifier=(0,L.d)(this.sliceSerialize(e)).toLowerCase()}function O(e){this.exit(e)}function M(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function R(){this.buffer()}function j(e){const t=this.resume(),n=this.stack[this.stack.length-1];(0,o.ok)("footnoteReference"===n.type),n.label=t,n.identifier=(0,L.d)(this.sliceSerialize(e)).toLowerCase()}function P(e){this.exit(e)}function _(e,t,n,r){const o=n.createTracker(r);let i=o.move("[^");const c=n.enter("footnoteReference"),l=n.enter("reference");return i+=o.move(n.safe(n.associationId(e),{...o.current(),before:i,after:"]"})),l(),c(),i+=o.move("]"),i}function B(e,t,n,r){const o=n.createTracker(r);let i=o.move("[^");const c=n.enter("footnoteDefinition"),l=n.enter("label");return i+=o.move(n.safe(n.associationId(e),{...o.current(),before:i,after:"]"})),l(),i+=o.move("]:"+(e.children&&e.children.length>0?" ":"")),o.shift(4),i+=o.move(n.indentLines(n.containerFlow(e,o.current()),$)),c(),i}function $(e,t,n){return 0===t?e:(n?"":"    ")+e}_.peek=function(){return"["};const Z=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function H(e){this.enter({type:"delete",children:[]},e)}function q(e){this.exit(e)}function N(e,t,n,r){const o=n.createTracker(r),i=n.enter("strikethrough");let c=o.move("~~");return c+=n.containerPhrasing(e,{...o.current(),before:c,after:"~"}),c+=o.move("~~"),i(),c}function U(e){return e.length}function V(e){const t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:82===t||114===t?114:0}function W(e,t,n){return">"+(n?"":" ")+e}function Q(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function Y(e,t,n,r){let o=-1;for(;++o<n.unsafe.length;)if("\n"===n.unsafe[o].character&&(i=n.stack,c=n.unsafe[o],Q(i,c.inConstruct,!0)&&!Q(i,c.notInConstruct,!1)))return/[ \t]/.test(r.before)?"":" ";var i,c;return"\\\n"}function G(e,t,n){return(n?"":"    ")+e}function J(e){const t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function K(e){return"&#x"+e.toString(16).toUpperCase()+";"}N.peek=function(){return"~"};var X=n(98788);function ee(e,t,n){const r=(0,X.r)(e),o=(0,X.r)(t);return void 0===r?void 0===o?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===o?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===r?void 0===o?{inside:!1,outside:!1}:1===o?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===o?{inside:!1,outside:!1}:1===o?{inside:!0,outside:!1}:{inside:!1,outside:!1}}function te(e,t,n,r){const o=function(e){const t=e.options.emphasis||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}(n),i=n.enter("emphasis"),c=n.createTracker(r),l=c.move(o);let s=c.move(n.containerPhrasing(e,{after:o,before:l,...c.current()}));const u=s.charCodeAt(0),a=ee(r.before.charCodeAt(r.before.length-1),u,o);a.inside&&(s=K(u)+s.slice(1));const f=s.charCodeAt(s.length-1),d=ee(r.after.charCodeAt(0),f,o);d.inside&&(s=s.slice(0,-1)+K(f));const h=c.move(o);return i(),n.attentionEncodeSurroundingInfo={after:d.outside,before:a.outside},l+s+h}te.peek=function(e,t,n){return n.options.emphasis||"*"};const ne=function(e){if(null==e)return oe;if("function"==typeof e)return re(e);if("object"==typeof e)return Array.isArray(e)?function(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=ne(e[n]);return re(r);function r(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}}(e):function(e){const t=e;return re(n);function n(n){const r=n;let o;for(o in e)if(r[o]!==t[o])return!1;return!0}}(e);if("string"==typeof e)return function(e){return re(t);function t(t){return t&&t.type===e}}(e);throw new Error("Expected function, string, or object as test")};function re(e){return function(t,n,r){return Boolean(ie(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function oe(){return!0}function ie(e){return null!==e&&"object"==typeof e&&"type"in e}const ce=[],le=!1;function se(e,t,n,r){let o;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):o=t;const i=ne(o),c=r?-1:1;!function e(o,l,s){const u=o&&"object"==typeof o?o:{};if("string"==typeof u.type){const e="string"==typeof u.tagName?u.tagName:"string"==typeof u.name?u.name:void 0;Object.defineProperty(a,"name",{value:"node ("+o.type+(e?"<"+e+">":"")+")"})}return a;function a(){let u,a,f,d=ce;if((!t||i(o,l,s[s.length-1]||void 0))&&(d=function(e){if(Array.isArray(e))return e;if("number"==typeof e)return[true,e];return null==e?ce:[e]}(n(o,s)),d[0]===le))return d;if("children"in o&&o.children){const t=o;if(t.children&&"skip"!==d[0])for(a=(r?t.children.length:-1)+c,f=s.concat(t);a>-1&&a<t.children.length;){const n=t.children[a];if(u=e(n,a,f)(),u[0]===le)return u;a="number"==typeof u[1]?u[1]:a+c}}return d}}(e,void 0,[])()}const ue={};function ae(e,t){const n=t||ue;return fe(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function fe(e,t,n){if(function(e){return Boolean(e&&"object"==typeof e)}(e)){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return de(e.children,t,n)}return Array.isArray(e)?de(e,t,n):""}function de(e,t,n){const r=[];let o=-1;for(;++o<e.length;)r[o]=fe(e[o],t,n);return r.join("")}function he(e,t){let n=!1;return function(e,t,n,r){let o,i,c;"function"==typeof t&&"function"!=typeof n?(i=void 0,c=t,o=n):(i=t,c=n,o=r),se(e,i,(function(e,t){const n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return c(e,r,n)}),o)}(e,(function(e){if("value"in e&&/\r?\n|\r/.test(e.value)||"break"===e.type)return n=!0,le})),Boolean((!e.depth||e.depth<3)&&ae(e)&&(t.options.setext||n))}function pe(e){return e.value||""}function me(e,t,n,r){const o=J(n),i='"'===o?"Quote":"Apostrophe",c=n.enter("image");let l=n.enter("label");const s=n.createTracker(r);let u=s.move("![");return u+=s.move(n.safe(e.alt,{before:u,after:"]",...s.current()})),u+=s.move("]("),l(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(l=n.enter("destinationLiteral"),u+=s.move("<"),u+=s.move(n.safe(e.url,{before:u,after:">",...s.current()})),u+=s.move(">")):(l=n.enter("destinationRaw"),u+=s.move(n.safe(e.url,{before:u,after:e.title?" ":")",...s.current()}))),l(),e.title&&(l=n.enter(`title${i}`),u+=s.move(" "+o),u+=s.move(n.safe(e.title,{before:u,after:o,...s.current()})),u+=s.move(o),l()),u+=s.move(")"),c(),u}function ge(e,t,n,r){const o=e.referenceType,i=n.enter("imageReference");let c=n.enter("label");const l=n.createTracker(r);let s=l.move("![");const u=n.safe(e.alt,{before:s,after:"]",...l.current()});s+=l.move(u+"]["),c();const a=n.stack;n.stack=[],c=n.enter("reference");const f=n.safe(n.associationId(e),{before:s,after:"]",...l.current()});return c(),n.stack=a,i(),"full"!==o&&u&&u===f?"shortcut"===o?s=s.slice(0,-1):s+=l.move("]"):s+=l.move(f+"]"),s}function ke(e,t,n){let r=e.value||"",o="`",i=-1;for(;new RegExp("(^|[^`])"+o+"([^`]|$)").test(r);)o+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++i<n.unsafe.length;){const e=n.unsafe[i],t=n.compilePattern(e);let o;if(e.atBreak)for(;o=t.exec(r);){let e=o.index;10===r.charCodeAt(e)&&13===r.charCodeAt(e-1)&&e--,r=r.slice(0,e)+" "+r.slice(o.index+1)}}return o+r+o}function be(e,t){const n=ae(e);return Boolean(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}function ve(e,t,n,r){const o=J(n),i='"'===o?"Quote":"Apostrophe",c=n.createTracker(r);let l,s;if(be(e,n)){const t=n.stack;n.stack=[],l=n.enter("autolink");let r=c.move("<");return r+=c.move(n.containerPhrasing(e,{before:r,after:">",...c.current()})),r+=c.move(">"),l(),n.stack=t,r}l=n.enter("link"),s=n.enter("label");let u=c.move("[");return u+=c.move(n.containerPhrasing(e,{before:u,after:"](",...c.current()})),u+=c.move("]("),s(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),u+=c.move("<"),u+=c.move(n.safe(e.url,{before:u,after:">",...c.current()})),u+=c.move(">")):(s=n.enter("destinationRaw"),u+=c.move(n.safe(e.url,{before:u,after:e.title?" ":")",...c.current()}))),s(),e.title&&(s=n.enter(`title${i}`),u+=c.move(" "+o),u+=c.move(n.safe(e.title,{before:u,after:o,...c.current()})),u+=c.move(o),s()),u+=c.move(")"),l(),u}function xe(e,t,n,r){const o=e.referenceType,i=n.enter("linkReference");let c=n.enter("label");const l=n.createTracker(r);let s=l.move("[");const u=n.containerPhrasing(e,{before:s,after:"]",...l.current()});s+=l.move(u+"]["),c();const a=n.stack;n.stack=[],c=n.enter("reference");const f=n.safe(n.associationId(e),{before:s,after:"]",...l.current()});return c(),n.stack=a,i(),"full"!==o&&u&&u===f?"shortcut"===o?s=s.slice(0,-1):s+=l.move("]"):s+=l.move(f+"]"),s}function ye(e){const t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function we(e){const t=e.options.rule||"*";if("*"!==t&&"-"!==t&&"_"!==t)throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}pe.peek=function(){return"<"},me.peek=function(){return"!"},ge.peek=function(){return"!"},ke.peek=function(){return"`"},ve.peek=function(e,t,n){return be(e,n)?"<":"["},xe.peek=function(){return"["};const Ce=ne(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function Ae(e,t,n,r){const o=function(e){const t=e.options.strong||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}(n),i=n.enter("strong"),c=n.createTracker(r),l=c.move(o+o);let s=c.move(n.containerPhrasing(e,{after:o,before:l,...c.current()}));const u=s.charCodeAt(0),a=ee(r.before.charCodeAt(r.before.length-1),u,o);a.inside&&(s=K(u)+s.slice(1));const f=s.charCodeAt(s.length-1),d=ee(r.after.charCodeAt(0),f,o);d.inside&&(s=s.slice(0,-1)+K(f));const h=c.move(o+o);return i(),n.attentionEncodeSurroundingInfo={after:d.outside,before:a.outside},l+s+h}Ae.peek=function(e,t,n){return n.options.strong||"*"};const Fe={blockquote:function(e,t,n,r){const o=n.enter("blockquote"),i=n.createTracker(r);i.move("> "),i.shift(2);const c=n.indentLines(n.containerFlow(e,i.current()),W);return o(),c},break:Y,code:function(e,t,n,r){const o=function(e){const t=e.options.fence||"`";if("`"!==t&&"~"!==t)throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}(n),i=e.value||"",c="`"===o?"GraveAccent":"Tilde";if(function(e,t){return Boolean(!1===t.options.fences&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}(e,n)){const e=n.enter("codeIndented"),t=n.indentLines(i,G);return e(),t}const l=n.createTracker(r),s=o.repeat(Math.max(function(e,t){const n=String(e);let r=n.indexOf(t),o=r,i=0,c=0;if("string"!=typeof t)throw new TypeError("Expected substring");for(;-1!==r;)r===o?++i>c&&(c=i):i=1,o=r+t.length,r=n.indexOf(t,o);return c}(i,o)+1,3)),u=n.enter("codeFenced");let a=l.move(s);if(e.lang){const t=n.enter(`codeFencedLang${c}`);a+=l.move(n.safe(e.lang,{before:a,after:" ",encode:["`"],...l.current()})),t()}if(e.lang&&e.meta){const t=n.enter(`codeFencedMeta${c}`);a+=l.move(" "),a+=l.move(n.safe(e.meta,{before:a,after:"\n",encode:["`"],...l.current()})),t()}return a+=l.move("\n"),i&&(a+=l.move(i+"\n")),a+=l.move(s),u(),a},definition:function(e,t,n,r){const o=J(n),i='"'===o?"Quote":"Apostrophe",c=n.enter("definition");let l=n.enter("label");const s=n.createTracker(r);let u=s.move("[");return u+=s.move(n.safe(n.associationId(e),{before:u,after:"]",...s.current()})),u+=s.move("]: "),l(),!e.url||/[\0- \u007F]/.test(e.url)?(l=n.enter("destinationLiteral"),u+=s.move("<"),u+=s.move(n.safe(e.url,{before:u,after:">",...s.current()})),u+=s.move(">")):(l=n.enter("destinationRaw"),u+=s.move(n.safe(e.url,{before:u,after:e.title?" ":"\n",...s.current()}))),l(),e.title&&(l=n.enter(`title${i}`),u+=s.move(" "+o),u+=s.move(n.safe(e.title,{before:u,after:o,...s.current()})),u+=s.move(o),l()),c(),u},emphasis:te,hardBreak:Y,heading:function(e,t,n,r){const o=Math.max(Math.min(6,e.depth||1),1),i=n.createTracker(r);if(he(e,n)){const t=n.enter("headingSetext"),r=n.enter("phrasing"),c=n.containerPhrasing(e,{...i.current(),before:"\n",after:"\n"});return r(),t(),c+"\n"+(1===o?"=":"-").repeat(c.length-(Math.max(c.lastIndexOf("\r"),c.lastIndexOf("\n"))+1))}const c="#".repeat(o),l=n.enter("headingAtx"),s=n.enter("phrasing");i.move(c+" ");let u=n.containerPhrasing(e,{before:"# ",after:"\n",...i.current()});return/^[\t ]/.test(u)&&(u=K(u.charCodeAt(0))+u.slice(1)),u=u?c+" "+u:c,n.options.closeAtx&&(u+=" "+c),s(),l(),u},html:pe,image:me,imageReference:ge,inlineCode:ke,link:ve,linkReference:xe,list:function(e,t,n,r){const o=n.enter("list"),i=n.bulletCurrent;let c=e.ordered?function(e){const t=e.options.bulletOrdered||".";if("."!==t&&")"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}(n):ye(n);const l=e.ordered?"."===c?")":".":function(e){const t=ye(e),n=e.options.bulletOther;if(!n)return"*"===t?"-":"*";if("*"!==n&&"+"!==n&&"-"!==n)throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}(n);let s=!(!t||!n.bulletLastUsed)&&c===n.bulletLastUsed;if(!e.ordered){const t=e.children?e.children[0]:void 0;if("*"!==c&&"-"!==c||!t||t.children&&t.children[0]||"list"!==n.stack[n.stack.length-1]||"listItem"!==n.stack[n.stack.length-2]||"list"!==n.stack[n.stack.length-3]||"listItem"!==n.stack[n.stack.length-4]||0!==n.indexStack[n.indexStack.length-1]||0!==n.indexStack[n.indexStack.length-2]||0!==n.indexStack[n.indexStack.length-3]||(s=!0),we(n)===c&&t){let t=-1;for(;++t<e.children.length;){const n=e.children[t];if(n&&"listItem"===n.type&&n.children&&n.children[0]&&"thematicBreak"===n.children[0].type){s=!0;break}}}}s&&(c=l),n.bulletCurrent=c;const u=n.containerFlow(e,r);return n.bulletLastUsed=c,n.bulletCurrent=i,o(),u},listItem:function(e,t,n,r){const o=function(e){const t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n);let i=n.bulletCurrent||ye(n);t&&"list"===t.type&&t.ordered&&(i=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+i);let c=i.length+1;("tab"===o||"mixed"===o&&(t&&"list"===t.type&&t.spread||e.spread))&&(c=4*Math.ceil(c/4));const l=n.createTracker(r);l.move(i+" ".repeat(c-i.length)),l.shift(c);const s=n.enter("listItem"),u=n.indentLines(n.containerFlow(e,l.current()),(function(e,t,n){if(t)return(n?"":" ".repeat(c))+e;return(n?i:i+" ".repeat(c-i.length))+e}));return s(),u},paragraph:function(e,t,n,r){const o=n.enter("paragraph"),i=n.enter("phrasing"),c=n.containerPhrasing(e,r);return i(),o(),c},root:function(e,t,n,r){return(e.children.some((function(e){return Ce(e)}))?n.containerPhrasing:n.containerFlow).call(n,e,r)},strong:Ae,text:function(e,t,n,r){return n.safe(e.value,r)},thematicBreak:function(e,t,n){const r=(we(n)+(n.options.ruleSpaces?" ":"")).repeat(function(e){const t=e.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}(n));return n.options.ruleSpaces?r.slice(0,-1):r}};function Ie(e){const t=e._align;(0,o.ok)(t,"expected `_align` on table"),this.enter({type:"table",align:t.map((function(e){return"none"===e?null:e})),children:[]},e),this.data.inTable=!0}function Se(e){this.exit(e),this.data.inTable=void 0}function Ee(e){this.enter({type:"tableRow",children:[]},e)}function Le(e){this.exit(e)}function De(e){this.enter({type:"tableCell",children:[]},e)}function Te(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,ze));const n=this.stack[this.stack.length-1];(0,o.ok)("inlineCode"===n.type),n.value=t,this.exit(e)}function ze(e,t){return"|"===t?t:e}function Oe(e){const t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,o=t.stringLength,i=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[\t :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=Fe.inlineCode(e,t,n);n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&"));return r},table:function(e,t,n,r){return l(function(e,t,n){const r=e.children;let o=-1;const i=[],c=t.enter("table");for(;++o<r.length;)i[o]=s(r[o],t,n);return c(),i}(e,n,r),e.align)},tableCell:c,tableRow:function(e,t,n,r){const o=s(e,n,r),i=l([o]);return i.slice(0,i.indexOf("\n"))}}};function c(e,t,n,r){const o=n.enter("tableCell"),c=n.enter("phrasing"),l=n.containerPhrasing(e,{...r,before:i,after:i});return c(),o(),l}function l(e,t){return function(e,t){const n=t||{},r=(n.align||[]).concat(),o=n.stringLength||U,i=[],c=[],l=[],s=[];let u=0,a=-1;for(;++a<e.length;){const t=[],r=[];let i=-1;for(e[a].length>u&&(u=e[a].length);++i<e[a].length;){const c=null==(f=e[a][i])?"":String(f);if(!1!==n.alignDelimiters){const e=o(c);r[i]=e,(void 0===s[i]||e>s[i])&&(s[i]=e)}t.push(c)}c[a]=t,l[a]=r}var f;let d=-1;if("object"==typeof r&&"length"in r)for(;++d<u;)i[d]=V(r[d]);else{const e=V(r);for(;++d<u;)i[d]=e}d=-1;const h=[],p=[];for(;++d<u;){const e=i[d];let t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let o=!1===n.alignDelimiters?1:Math.max(1,s[d]-t.length-r.length);const c=t+"-".repeat(o)+r;!1!==n.alignDelimiters&&(o=t.length+o+r.length,o>s[d]&&(s[d]=o),p[d]=o),h[d]=c}c.splice(1,0,h),l.splice(1,0,p),a=-1;const m=[];for(;++a<c.length;){const e=c[a],t=l[a];d=-1;const r=[];for(;++d<u;){const o=e[d]||"";let c="",l="";if(!1!==n.alignDelimiters){const e=s[d]-(t[d]||0),n=i[d];114===n?c=" ".repeat(e):99===n?e%2?(c=" ".repeat(e/2+.5),l=" ".repeat(e/2-.5)):(c=" ".repeat(e/2),l=c):l=" ".repeat(e)}!1===n.delimiterStart||d||r.push("|"),!1===n.padding||!1===n.alignDelimiters&&""===o||!1===n.delimiterStart&&!d||r.push(" "),!1!==n.alignDelimiters&&r.push(c),r.push(o),!1!==n.alignDelimiters&&r.push(l),!1!==n.padding&&r.push(" "),!1===n.delimiterEnd&&d===u-1||r.push("|")}m.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:o})}function s(e,t,n){const r=e.children;let o=-1;const i=[],l=t.enter("tableRow");for(;++o<r.length;)i[o]=c(r[o],0,t,n);return l(),i}}function Me(e,t,n){return">"+(n?"":" ")+e}function Re(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function je(e,t,n,r){let o=-1;for(;++o<n.unsafe.length;)if("\n"===n.unsafe[o].character&&(i=n.stack,c=n.unsafe[o],Re(i,c.inConstruct,!0)&&!Re(i,c.notInConstruct,!1)))return/[ \t]/.test(r.before)?"":" ";var i,c;return"\\\n"}function Pe(e,t,n){return(n?"":"    ")+e}function _e(e){const t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function Be(e){return"&#x"+e.toString(16).toUpperCase()+";"}function $e(e,t,n){const r=(0,X.r)(e),o=(0,X.r)(t);return void 0===r?void 0===o?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===o?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===r?void 0===o?{inside:!1,outside:!1}:1===o?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===o?{inside:!1,outside:!1}:1===o?{inside:!0,outside:!1}:{inside:!1,outside:!1}}function Ze(e,t,n,r){const o=function(e){const t=e.options.emphasis||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}(n),i=n.enter("emphasis"),c=n.createTracker(r),l=c.move(o);let s=c.move(n.containerPhrasing(e,{after:o,before:l,...c.current()}));const u=s.charCodeAt(0),a=$e(r.before.charCodeAt(r.before.length-1),u,o);a.inside&&(s=Be(u)+s.slice(1));const f=s.charCodeAt(s.length-1),d=$e(r.after.charCodeAt(0),f,o);d.inside&&(s=s.slice(0,-1)+Be(f));const h=c.move(o);return i(),n.attentionEncodeSurroundingInfo={after:d.outside,before:a.outside},l+s+h}Ze.peek=function(e,t,n){return n.options.emphasis||"*"};const He=function(e){if(null==e)return Ne;if("function"==typeof e)return qe(e);if("object"==typeof e)return Array.isArray(e)?function(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=He(e[n]);return qe(r);function r(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}}(e):function(e){const t=e;return qe(n);function n(n){const r=n;let o;for(o in e)if(r[o]!==t[o])return!1;return!0}}(e);if("string"==typeof e)return function(e){return qe(t);function t(t){return t&&t.type===e}}(e);throw new Error("Expected function, string, or object as test")};function qe(e){return function(t,n,r){return Boolean(Ue(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function Ne(){return!0}function Ue(e){return null!==e&&"object"==typeof e&&"type"in e}const Ve=[],We=!1;function Qe(e,t,n,r){let o;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):o=t;const i=He(o),c=r?-1:1;!function e(o,l,s){const u=o&&"object"==typeof o?o:{};if("string"==typeof u.type){const e="string"==typeof u.tagName?u.tagName:"string"==typeof u.name?u.name:void 0;Object.defineProperty(a,"name",{value:"node ("+o.type+(e?"<"+e+">":"")+")"})}return a;function a(){let u,a,f,d=Ve;if((!t||i(o,l,s[s.length-1]||void 0))&&(d=function(e){if(Array.isArray(e))return e;if("number"==typeof e)return[true,e];return null==e?Ve:[e]}(n(o,s)),d[0]===We))return d;if("children"in o&&o.children){const t=o;if(t.children&&"skip"!==d[0])for(a=(r?t.children.length:-1)+c,f=s.concat(t);a>-1&&a<t.children.length;){const n=t.children[a];if(u=e(n,a,f)(),u[0]===We)return u;a="number"==typeof u[1]?u[1]:a+c}}return d}}(e,void 0,[])()}const Ye={};function Ge(e,t){const n=t||Ye;return Je(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function Je(e,t,n){if(function(e){return Boolean(e&&"object"==typeof e)}(e)){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return Ke(e.children,t,n)}return Array.isArray(e)?Ke(e,t,n):""}function Ke(e,t,n){const r=[];let o=-1;for(;++o<e.length;)r[o]=Je(e[o],t,n);return r.join("")}function Xe(e,t){let n=!1;return function(e,t,n,r){let o,i,c;"function"==typeof t&&"function"!=typeof n?(i=void 0,c=t,o=n):(i=t,c=n,o=r),Qe(e,i,(function(e,t){const n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return c(e,r,n)}),o)}(e,(function(e){if("value"in e&&/\r?\n|\r/.test(e.value)||"break"===e.type)return n=!0,We})),Boolean((!e.depth||e.depth<3)&&Ge(e)&&(t.options.setext||n))}function et(e){return e.value||""}function tt(e,t,n,r){const o=_e(n),i='"'===o?"Quote":"Apostrophe",c=n.enter("image");let l=n.enter("label");const s=n.createTracker(r);let u=s.move("![");return u+=s.move(n.safe(e.alt,{before:u,after:"]",...s.current()})),u+=s.move("]("),l(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(l=n.enter("destinationLiteral"),u+=s.move("<"),u+=s.move(n.safe(e.url,{before:u,after:">",...s.current()})),u+=s.move(">")):(l=n.enter("destinationRaw"),u+=s.move(n.safe(e.url,{before:u,after:e.title?" ":")",...s.current()}))),l(),e.title&&(l=n.enter(`title${i}`),u+=s.move(" "+o),u+=s.move(n.safe(e.title,{before:u,after:o,...s.current()})),u+=s.move(o),l()),u+=s.move(")"),c(),u}function nt(e,t,n,r){const o=e.referenceType,i=n.enter("imageReference");let c=n.enter("label");const l=n.createTracker(r);let s=l.move("![");const u=n.safe(e.alt,{before:s,after:"]",...l.current()});s+=l.move(u+"]["),c();const a=n.stack;n.stack=[],c=n.enter("reference");const f=n.safe(n.associationId(e),{before:s,after:"]",...l.current()});return c(),n.stack=a,i(),"full"!==o&&u&&u===f?"shortcut"===o?s=s.slice(0,-1):s+=l.move("]"):s+=l.move(f+"]"),s}function rt(e,t,n){let r=e.value||"",o="`",i=-1;for(;new RegExp("(^|[^`])"+o+"([^`]|$)").test(r);)o+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++i<n.unsafe.length;){const e=n.unsafe[i],t=n.compilePattern(e);let o;if(e.atBreak)for(;o=t.exec(r);){let e=o.index;10===r.charCodeAt(e)&&13===r.charCodeAt(e-1)&&e--,r=r.slice(0,e)+" "+r.slice(o.index+1)}}return o+r+o}function ot(e,t){const n=Ge(e);return Boolean(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}function it(e,t,n,r){const o=_e(n),i='"'===o?"Quote":"Apostrophe",c=n.createTracker(r);let l,s;if(ot(e,n)){const t=n.stack;n.stack=[],l=n.enter("autolink");let r=c.move("<");return r+=c.move(n.containerPhrasing(e,{before:r,after:">",...c.current()})),r+=c.move(">"),l(),n.stack=t,r}l=n.enter("link"),s=n.enter("label");let u=c.move("[");return u+=c.move(n.containerPhrasing(e,{before:u,after:"](",...c.current()})),u+=c.move("]("),s(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),u+=c.move("<"),u+=c.move(n.safe(e.url,{before:u,after:">",...c.current()})),u+=c.move(">")):(s=n.enter("destinationRaw"),u+=c.move(n.safe(e.url,{before:u,after:e.title?" ":")",...c.current()}))),s(),e.title&&(s=n.enter(`title${i}`),u+=c.move(" "+o),u+=c.move(n.safe(e.title,{before:u,after:o,...c.current()})),u+=c.move(o),s()),u+=c.move(")"),l(),u}function ct(e,t,n,r){const o=e.referenceType,i=n.enter("linkReference");let c=n.enter("label");const l=n.createTracker(r);let s=l.move("[");const u=n.containerPhrasing(e,{before:s,after:"]",...l.current()});s+=l.move(u+"]["),c();const a=n.stack;n.stack=[],c=n.enter("reference");const f=n.safe(n.associationId(e),{before:s,after:"]",...l.current()});return c(),n.stack=a,i(),"full"!==o&&u&&u===f?"shortcut"===o?s=s.slice(0,-1):s+=l.move("]"):s+=l.move(f+"]"),s}function lt(e){const t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function st(e){const t=e.options.rule||"*";if("*"!==t&&"-"!==t&&"_"!==t)throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}et.peek=function(){return"<"},tt.peek=function(){return"!"},nt.peek=function(){return"!"},rt.peek=function(){return"`"},it.peek=function(e,t,n){return ot(e,n)?"<":"["},ct.peek=function(){return"["};const ut=He(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function at(e,t,n,r){const o=function(e){const t=e.options.strong||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}(n),i=n.enter("strong"),c=n.createTracker(r),l=c.move(o+o);let s=c.move(n.containerPhrasing(e,{after:o,before:l,...c.current()}));const u=s.charCodeAt(0),a=$e(r.before.charCodeAt(r.before.length-1),u,o);a.inside&&(s=Be(u)+s.slice(1));const f=s.charCodeAt(s.length-1),d=$e(r.after.charCodeAt(0),f,o);d.inside&&(s=s.slice(0,-1)+Be(f));const h=c.move(o+o);return i(),n.attentionEncodeSurroundingInfo={after:d.outside,before:a.outside},l+s+h}at.peek=function(e,t,n){return n.options.strong||"*"};const ft={blockquote:function(e,t,n,r){const o=n.enter("blockquote"),i=n.createTracker(r);i.move("> "),i.shift(2);const c=n.indentLines(n.containerFlow(e,i.current()),Me);return o(),c},break:je,code:function(e,t,n,r){const o=function(e){const t=e.options.fence||"`";if("`"!==t&&"~"!==t)throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}(n),i=e.value||"",c="`"===o?"GraveAccent":"Tilde";if(function(e,t){return Boolean(!1===t.options.fences&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}(e,n)){const e=n.enter("codeIndented"),t=n.indentLines(i,Pe);return e(),t}const l=n.createTracker(r),s=o.repeat(Math.max(function(e,t){const n=String(e);let r=n.indexOf(t),o=r,i=0,c=0;if("string"!=typeof t)throw new TypeError("Expected substring");for(;-1!==r;)r===o?++i>c&&(c=i):i=1,o=r+t.length,r=n.indexOf(t,o);return c}(i,o)+1,3)),u=n.enter("codeFenced");let a=l.move(s);if(e.lang){const t=n.enter(`codeFencedLang${c}`);a+=l.move(n.safe(e.lang,{before:a,after:" ",encode:["`"],...l.current()})),t()}if(e.lang&&e.meta){const t=n.enter(`codeFencedMeta${c}`);a+=l.move(" "),a+=l.move(n.safe(e.meta,{before:a,after:"\n",encode:["`"],...l.current()})),t()}return a+=l.move("\n"),i&&(a+=l.move(i+"\n")),a+=l.move(s),u(),a},definition:function(e,t,n,r){const o=_e(n),i='"'===o?"Quote":"Apostrophe",c=n.enter("definition");let l=n.enter("label");const s=n.createTracker(r);let u=s.move("[");return u+=s.move(n.safe(n.associationId(e),{before:u,after:"]",...s.current()})),u+=s.move("]: "),l(),!e.url||/[\0- \u007F]/.test(e.url)?(l=n.enter("destinationLiteral"),u+=s.move("<"),u+=s.move(n.safe(e.url,{before:u,after:">",...s.current()})),u+=s.move(">")):(l=n.enter("destinationRaw"),u+=s.move(n.safe(e.url,{before:u,after:e.title?" ":"\n",...s.current()}))),l(),e.title&&(l=n.enter(`title${i}`),u+=s.move(" "+o),u+=s.move(n.safe(e.title,{before:u,after:o,...s.current()})),u+=s.move(o),l()),c(),u},emphasis:Ze,hardBreak:je,heading:function(e,t,n,r){const o=Math.max(Math.min(6,e.depth||1),1),i=n.createTracker(r);if(Xe(e,n)){const t=n.enter("headingSetext"),r=n.enter("phrasing"),c=n.containerPhrasing(e,{...i.current(),before:"\n",after:"\n"});return r(),t(),c+"\n"+(1===o?"=":"-").repeat(c.length-(Math.max(c.lastIndexOf("\r"),c.lastIndexOf("\n"))+1))}const c="#".repeat(o),l=n.enter("headingAtx"),s=n.enter("phrasing");i.move(c+" ");let u=n.containerPhrasing(e,{before:"# ",after:"\n",...i.current()});return/^[\t ]/.test(u)&&(u=Be(u.charCodeAt(0))+u.slice(1)),u=u?c+" "+u:c,n.options.closeAtx&&(u+=" "+c),s(),l(),u},html:et,image:tt,imageReference:nt,inlineCode:rt,link:it,linkReference:ct,list:function(e,t,n,r){const o=n.enter("list"),i=n.bulletCurrent;let c=e.ordered?function(e){const t=e.options.bulletOrdered||".";if("."!==t&&")"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}(n):lt(n);const l=e.ordered?"."===c?")":".":function(e){const t=lt(e),n=e.options.bulletOther;if(!n)return"*"===t?"-":"*";if("*"!==n&&"+"!==n&&"-"!==n)throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}(n);let s=!(!t||!n.bulletLastUsed)&&c===n.bulletLastUsed;if(!e.ordered){const t=e.children?e.children[0]:void 0;if("*"!==c&&"-"!==c||!t||t.children&&t.children[0]||"list"!==n.stack[n.stack.length-1]||"listItem"!==n.stack[n.stack.length-2]||"list"!==n.stack[n.stack.length-3]||"listItem"!==n.stack[n.stack.length-4]||0!==n.indexStack[n.indexStack.length-1]||0!==n.indexStack[n.indexStack.length-2]||0!==n.indexStack[n.indexStack.length-3]||(s=!0),st(n)===c&&t){let t=-1;for(;++t<e.children.length;){const n=e.children[t];if(n&&"listItem"===n.type&&n.children&&n.children[0]&&"thematicBreak"===n.children[0].type){s=!0;break}}}}s&&(c=l),n.bulletCurrent=c;const u=n.containerFlow(e,r);return n.bulletLastUsed=c,n.bulletCurrent=i,o(),u},listItem:function(e,t,n,r){const o=function(e){const t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n);let i=n.bulletCurrent||lt(n);t&&"list"===t.type&&t.ordered&&(i=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+i);let c=i.length+1;("tab"===o||"mixed"===o&&(t&&"list"===t.type&&t.spread||e.spread))&&(c=4*Math.ceil(c/4));const l=n.createTracker(r);l.move(i+" ".repeat(c-i.length)),l.shift(c);const s=n.enter("listItem"),u=n.indentLines(n.containerFlow(e,l.current()),(function(e,t,n){if(t)return(n?"":" ".repeat(c))+e;return(n?i:i+" ".repeat(c-i.length))+e}));return s(),u},paragraph:function(e,t,n,r){const o=n.enter("paragraph"),i=n.enter("phrasing"),c=n.containerPhrasing(e,r);return i(),o(),c},root:function(e,t,n,r){return(e.children.some((function(e){return ut(e)}))?n.containerPhrasing:n.containerFlow).call(n,e,r)},strong:at,text:function(e,t,n,r){return n.safe(e.value,r)},thematicBreak:function(e,t,n){const r=(st(n)+(n.options.ruleSpaces?" ":"")).repeat(function(e){const t=e.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}(n));return n.options.ruleSpaces?r.slice(0,-1):r}};function dt(e){const t=this.stack[this.stack.length-2];(0,o.ok)("listItem"===t.type),t.checked="taskListCheckValueChecked"===e.type}function ht(e){const t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){const e=this.stack[this.stack.length-1];(0,o.ok)("paragraph"===e.type);const n=e.children[0];if(n&&"text"===n.type){const r=t.children;let o,i=-1;for(;++i<r.length;){const e=r[i];if("paragraph"===e.type){o=e;break}}o===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function pt(e,t,n,r){const o=e.children[0],i="boolean"==typeof e.checked&&o&&"paragraph"===o.type,c="["+(e.checked?"x":" ")+"] ",l=n.createTracker(r);i&&l.move(c);let s=ft.listItem(e,t,n,{...r,...l.current()});return i&&(s=s.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,(function(e){return e+c}))),s}var mt=n(4663);const gt=yt(/[A-Za-z]/),kt=yt(/[\dA-Za-z]/);yt(/[#-'*+\--9=?A-Z^-~]/);yt(/\d/),yt(/[\dA-Fa-f]/),yt(/[!-/:-@[-`{-~]/);function bt(e){return null!==e&&(e<0||32===e)}const vt=yt(/\p{P}|\p{S}/u),xt=yt(/\s/);function yt(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}const wt={tokenize:function(e,t,n){let r=0;return function t(i){if((87===i||119===i)&&r<3)return r++,e.consume(i),t;if(46===i&&3===r)return e.consume(i),o;return n(i)};function o(e){return null===e?n(e):t(e)}},partial:!0},Ct={tokenize:function(e,t,n){let r,o,i;return c;function c(t){return 46===t||95===t?e.check(Ft,s,l)(t):null===t||bt(t)||xt(t)||45!==t&&vt(t)?s(t):(i=!0,e.consume(t),c)}function l(t){return 95===t?r=!0:(o=r,r=void 0),e.consume(t),c}function s(e){return o||r||!i?n(e):t(e)}},partial:!0},At={tokenize:function(e,t){let n=0,r=0;return o;function o(c){return 40===c?(n++,e.consume(c),o):41===c&&r<n?i(c):33===c||34===c||38===c||39===c||41===c||42===c||44===c||46===c||58===c||59===c||60===c||63===c||93===c||95===c||126===c?e.check(Ft,t,i)(c):null===c||bt(c)||xt(c)?t(c):(e.consume(c),o)}function i(t){return 41===t&&r++,e.consume(t),o}},partial:!0},Ft={tokenize:function(e,t,n){return r;function r(c){return 33===c||34===c||39===c||41===c||42===c||44===c||46===c||58===c||59===c||63===c||95===c||126===c?(e.consume(c),r):38===c?(e.consume(c),i):93===c?(e.consume(c),o):60===c||null===c||bt(c)||xt(c)?t(c):n(c)}function o(e){return null===e||40===e||91===e||bt(e)||xt(e)?t(e):r(e)}function i(e){return gt(e)?c(e):n(e)}function c(t){return 59===t?(e.consume(t),r):gt(t)?(e.consume(t),c):n(t)}},partial:!0},It={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return kt(e)?n(e):t(e)}},partial:!0},St={name:"wwwAutolink",tokenize:function(e,t,n){const r=this;return function(t){if(87!==t&&119!==t||!zt.call(r,r.previous)||jt(r.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(wt,e.attempt(Ct,e.attempt(At,o),n),n)(t)};function o(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:zt},Et={name:"protocolAutolink",tokenize:function(e,t,n){const r=this;let o="",i=!1;return function(t){if((72===t||104===t)&&Ot.call(r,r.previous)&&!jt(r.events))return e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),o+=String.fromCodePoint(t),e.consume(t),c;return n(t)};function c(t){if(gt(t)&&o.length<5)return o+=String.fromCodePoint(t),e.consume(t),c;if(58===t){const n=o.toLowerCase();if("http"===n||"https"===n)return e.consume(t),l}return n(t)}function l(t){return 47===t?(e.consume(t),i?s:(i=!0,l)):n(t)}function s(t){return null===t||function(e){return null!==e&&(e<32||127===e)}(t)||bt(t)||xt(t)||vt(t)?n(t):e.attempt(Ct,e.attempt(At,u),n)(t)}function u(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:Ot},Lt={name:"emailAutolink",tokenize:function(e,t,n){const r=this;let o,i;return function(t){if(!Rt(t)||!Mt.call(r,r.previous)||jt(r.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),c(t)};function c(t){return Rt(t)?(e.consume(t),c):64===t?(e.consume(t),l):n(t)}function l(t){return 46===t?e.check(It,u,s)(t):45===t||95===t||kt(t)?(i=!0,e.consume(t),l):u(t)}function s(t){return e.consume(t),o=!0,l}function u(c){return i&&o&&gt(r.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(c)):n(c)}},previous:Mt},Dt={};let Tt=48;for(;Tt<123;)Dt[Tt]=Lt,Tt++,58===Tt?Tt=65:91===Tt&&(Tt=97);function zt(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||bt(e)}function Ot(e){return!gt(e)}function Mt(e){return!(47===e||Rt(e))}function Rt(e){return 43===e||45===e||46===e||95===e||kt(e)}function jt(e){let t=e.length,n=!1;for(;t--;){const r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}Dt[43]=Lt,Dt[45]=Lt,Dt[46]=Lt,Dt[95]=Lt,Dt[72]=[Lt,Et],Dt[104]=[Lt,Et],Dt[87]=[Lt,St],Dt[119]=[Lt,St];var Pt=n(23402);$t(/[A-Za-z]/),$t(/[\dA-Za-z]/),$t(/[#-'*+\--9=?A-Z^-~]/);$t(/\d/),$t(/[\dA-Fa-f]/),$t(/[!-/:-@[-`{-~]/);function _t(e){return null!==e&&(e<0||32===e)}function Bt(e){return-2===e||-1===e||32===e}$t(/\p{P}|\p{S}/u),$t(/\s/);function $t(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function Zt(e,t,n,r){const o=r?r-1:Number.POSITIVE_INFINITY;let i=0;return function(r){if(Bt(r))return e.enter(n),c(r);return t(r)};function c(r){return Bt(r)&&i++<o?(e.consume(r),c):(e.exit(n),t(r))}}const Ht={tokenize:function(e,t,n){const r=this;return Zt(e,(function(e){const o=r.events[r.events.length-1];return o&&"gfmFootnoteDefinitionIndent"===o[1].type&&4===o[2].sliceSerialize(o[1],!0).length?t(e):n(e)}),"gfmFootnoteDefinitionIndent",5)},partial:!0};function qt(e,t,n){const r=this;let o=r.events.length;const i=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let c;for(;o--;){const e=r.events[o][1];if("labelImage"===e.type){c=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(o){if(!c||!c._balanced)return n(o);const l=(0,L.d)(r.sliceSerialize({start:c.end,end:r.now()}));if(94!==l.codePointAt(0)||!i.includes(l.slice(1)))return n(o);return e.enter("gfmFootnoteCallLabelMarker"),e.consume(o),e.exit("gfmFootnoteCallLabelMarker"),t(o)}}function Nt(e,t){let n,r=e.length;for(;r--;)if("labelImage"===e[r][1].type&&"enter"===e[r][0]){n=e[r][1];break}e[r+1][1].type="data",e[r+3][1].type="gfmFootnoteCallLabelMarker";const o={type:"gfmFootnoteCall",start:Object.assign({},e[r+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[r+3][1].end),end:Object.assign({},e[r+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;const c={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},l={type:"chunkString",contentType:"string",start:Object.assign({},c.start),end:Object.assign({},c.end)},s=[e[r+1],e[r+2],["enter",o,t],e[r+3],e[r+4],["enter",i,t],["exit",i,t],["enter",c,t],["enter",l,t],["exit",l,t],["exit",c,t],e[e.length-2],e[e.length-1],["exit",o,t]];return e.splice(r,e.length-r+1,...s),e}function Ut(e,t,n){const r=this,o=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let i,c=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),l};function l(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",s)}function s(l){if(c>999||93===l&&!i||null===l||91===l||_t(l))return n(l);if(93===l){e.exit("chunkString");const i=e.exit("gfmFootnoteCallString");return o.includes((0,L.d)(r.sliceSerialize(i)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(l),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(l)}return _t(l)||(i=!0),c++,e.consume(l),92===l?u:s}function u(t){return 91===t||92===t||93===t?(e.consume(t),c++,s):s(t)}}function Vt(e,t,n){const r=this,o=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let i,c,l=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),s};function s(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",u):n(t)}function u(t){if(l>999||93===t&&!c||null===t||91===t||_t(t))return n(t);if(93===t){e.exit("chunkString");const n=e.exit("gfmFootnoteDefinitionLabelString");return i=(0,L.d)(r.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),f}return _t(t)||(c=!0),l++,e.consume(t),92===t?a:u}function a(t){return 91===t||92===t||93===t?(e.consume(t),l++,u):u(t)}function f(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),o.includes(i)||o.push(i),Zt(e,d,"gfmFootnoteDefinitionWhitespace")):n(t)}function d(e){return t(e)}}function Wt(e,t,n){return e.check(Pt.w,t,e.attempt(Ht,t,n))}function Qt(e){e.exit("gfmFootnoteDefinition")}var Yt=n(62888),Gt=n(63233);function Jt(e){let t=(e||{}).singleTilde;const n={name:"strikethrough",tokenize:function(e,n,r){const o=this.previous,i=this.events;let c=0;return function(t){if(126===o&&"characterEscape"!==i[i.length-1][1].type)return r(t);return e.enter("strikethroughSequenceTemporary"),l(t)};function l(i){const s=(0,X.r)(o);if(126===i)return c>1?r(i):(e.consume(i),c++,l);if(c<2&&!t)return r(i);const u=e.exit("strikethroughSequenceTemporary"),a=(0,X.r)(i);return u._open=!a||2===a&&Boolean(s),u._close=!s||2===s&&Boolean(a),n(i)}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";const o={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},i={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},c=[["enter",o,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",i,t]],l=t.parser.constructs.insideSpan.null;l&&(0,Yt.d)(c,c.length,0,(0,Gt.C)(l,e.slice(r+1,n),t)),(0,Yt.d)(c,c.length,0,[["exit",i,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",o,t]]),(0,Yt.d)(e,r-1,n-r+3,c),n=r+c.length-2;break}}n=-1;for(;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}tn(/[A-Za-z]/),tn(/[\dA-Za-z]/),tn(/[#-'*+\--9=?A-Z^-~]/);tn(/\d/),tn(/[\dA-Fa-f]/),tn(/[!-/:-@[-`{-~]/);function Kt(e){return null!==e&&e<-2}function Xt(e){return null!==e&&(e<0||32===e)}function en(e){return-2===e||-1===e||32===e}tn(/\p{P}|\p{S}/u),tn(/\s/);function tn(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function nn(e,t,n,r){const o=r?r-1:Number.POSITIVE_INFINITY;let i=0;return function(r){if(en(r))return e.enter(n),c(r);return t(r)};function c(r){return en(r)&&i++<o?(e.consume(r),c):(e.exit(n),t(r))}}class rn{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let o=0;if(0===n&&0===r.length)return;for(;o<e.map.length;){if(e.map[o][0]===t)return e.map[o][1]+=n,void e.map[o][2].push(...r);o+=1}e.map.push([t,n,r])}(this,e,t,n)}consume(e){if(this.map.sort((function(e,t){return e[0]-t[0]})),0===this.map.length)return;let t=this.map.length;const n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push([...e]),e.length=0;let r=n.pop();for(;r;)e.push(...r),r=n.pop();this.map.length=0}}function on(e,t){let n=!1;const r=[];for(;t<e.length;){const o=e[t];if(n){if("enter"===o[0])"tableContent"===o[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===o[1].type){if("tableDelimiterMarker"===e[t-1][1].type){const e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===o[1].type)break}else"enter"===o[0]&&"tableDelimiterRow"===o[1].type&&(n=!0);t+=1}return r}function cn(e,t,n){const r=this;let o,i=0,c=0;return function(e){let t=r.events.length-1;for(;t>-1;){const e=r.events[t][1].type;if("lineEnding"!==e&&"linePrefix"!==e)break;t--}const o=t>-1?r.events[t][1].type:null,i="tableHead"===o||"tableRow"===o?x:l;if(i===x&&r.parser.lazy[r.now().line])return n(e);return i(e)};function l(t){return e.enter("tableHead"),e.enter("tableRow"),function(e){if(124===e)return s(e);return o=!0,c+=1,s(e)}(t)}function s(t){return null===t?n(t):Kt(t)?c>1?(c=0,r.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),f):n(t):en(t)?nn(e,s,"whitespace")(t):(c+=1,o&&(o=!1,i+=1),124===t?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),o=!0,s):(e.enter("data"),u(t)))}function u(t){return null===t||124===t||Xt(t)?(e.exit("data"),s(t)):(e.consume(t),92===t?a:u)}function a(t){return 92===t||124===t?(e.consume(t),u):u(t)}function f(t){return r.interrupt=!1,r.parser.lazy[r.now().line]?n(t):(e.enter("tableDelimiterRow"),o=!1,en(t)?nn(e,d,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):d(t))}function d(t){return 45===t||58===t?p(t):124===t?(o=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),h):v(t)}function h(t){return en(t)?nn(e,p,"whitespace")(t):p(t)}function p(t){return 58===t?(c+=1,o=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),m):45===t?(c+=1,m(t)):null===t||Kt(t)?b(t):v(t)}function m(t){return 45===t?(e.enter("tableDelimiterFiller"),g(t)):v(t)}function g(t){return 45===t?(e.consume(t),g):58===t?(o=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),k):(e.exit("tableDelimiterFiller"),k(t))}function k(t){return en(t)?nn(e,b,"whitespace")(t):b(t)}function b(n){return 124===n?d(n):(null===n||Kt(n))&&o&&i===c?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(n)):v(n)}function v(e){return n(e)}function x(t){return e.enter("tableRow"),y(t)}function y(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),y):null===n||Kt(n)?(e.exit("tableRow"),t(n)):en(n)?nn(e,y,"whitespace")(n):(e.enter("data"),w(n))}function w(t){return null===t||124===t||Xt(t)?(e.exit("data"),y(t)):(e.consume(t),92===t?C:w)}function C(t){return 92===t||124===t?(e.consume(t),w):w(t)}}function ln(e,t){let n,r,o,i=-1,c=!0,l=0,s=[0,0,0,0],u=[0,0,0,0],a=!1,f=0;const d=new rn;for(;++i<e.length;){const h=e[i],p=h[1];"enter"===h[0]?"tableHead"===p.type?(a=!1,0!==f&&(un(d,t,f,n,r),r=void 0,f=0),n={type:"table",start:Object.assign({},p.start),end:Object.assign({},p.end)},d.add(i,0,[["enter",n,t]])):"tableRow"===p.type||"tableDelimiterRow"===p.type?(c=!0,o=void 0,s=[0,0,0,0],u=[0,i+1,0,0],a&&(a=!1,r={type:"tableBody",start:Object.assign({},p.start),end:Object.assign({},p.end)},d.add(i,0,[["enter",r,t]])),l="tableDelimiterRow"===p.type?2:r?3:1):!l||"data"!==p.type&&"tableDelimiterMarker"!==p.type&&"tableDelimiterFiller"!==p.type?"tableCellDivider"===p.type&&(c?c=!1:(0!==s[1]&&(u[0]=u[1],o=sn(d,t,s,l,void 0,o)),s=u,u=[s[1],i,0,0])):(c=!1,0===u[2]&&(0!==s[1]&&(u[0]=u[1],o=sn(d,t,s,l,void 0,o),s=[0,0,0,0]),u[2]=i)):"tableHead"===p.type?(a=!0,f=i):"tableRow"===p.type||"tableDelimiterRow"===p.type?(f=i,0!==s[1]?(u[0]=u[1],o=sn(d,t,s,l,i,o)):0!==u[1]&&(o=sn(d,t,u,l,i,o)),l=0):!l||"data"!==p.type&&"tableDelimiterMarker"!==p.type&&"tableDelimiterFiller"!==p.type||(u[3]=i)}for(0!==f&&un(d,t,f,n,r),d.consume(t.events),i=-1;++i<t.events.length;){const e=t.events[i];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=on(t.events,i))}return e}function sn(e,t,n,r,o,i){const c=1===r?"tableHeader":2===r?"tableDelimiter":"tableData";0!==n[0]&&(i.end=Object.assign({},an(t.events,n[0])),e.add(n[0],0,[["exit",i,t]]));const l=an(t.events,n[1]);if(i={type:c,start:Object.assign({},l),end:Object.assign({},l)},e.add(n[1],0,[["enter",i,t]]),0!==n[2]){const o=an(t.events,n[2]),i=an(t.events,n[3]),c={type:"tableContent",start:Object.assign({},o),end:Object.assign({},i)};if(e.add(n[2],0,[["enter",c,t]]),2!==r){const r=t.events[n[2]],o=t.events[n[3]];if(r[1].end=Object.assign({},o[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){const t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",c,t]])}return void 0!==o&&(i.end=Object.assign({},an(t.events,o)),e.add(o,0,[["exit",i,t]]),i=void 0),i}function un(e,t,n,r,o){const i=[],c=an(t.events,n);o&&(o.end=Object.assign({},c),i.push(["exit",o,t])),r.end=Object.assign({},c),i.push(["exit",r,t]),e.add(n+1,0,i)}function an(e,t){const n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}dn(/[A-Za-z]/),dn(/[\dA-Za-z]/),dn(/[#-'*+\--9=?A-Z^-~]/);dn(/\d/),dn(/[\dA-Fa-f]/),dn(/[!-/:-@[-`{-~]/);function fn(e){return-2===e||-1===e||32===e}dn(/\p{P}|\p{S}/u),dn(/\s/);function dn(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}const hn={name:"tasklistCheck",tokenize:function(e,t,n){const r=this;return function(t){if(null!==r.previous||!r._gfmTasklistFirstContentOfListItem)return n(t);return e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),o};function o(t){return function(e){return null!==e&&(e<0||32===e)}(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),i):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),i):n(t)}function i(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),c):n(t)}function c(r){return function(e){return null!==e&&e<-2}(r)?t(r):fn(r)?e.check({tokenize:pn},t,n)(r):n(r)}}};function pn(e,t,n){return function(e,t,n,r){const o=r?r-1:Number.POSITIVE_INFINITY;let i=0;return function(r){return fn(r)?(e.enter(n),c(r)):t(r)};function c(r){return fn(r)&&i++<o?(e.consume(r),c):(e.exit(n),t(r))}}(e,(function(e){return null===e?n(e):t(e)}),"whitespace")}const mn={};function gn(e){const t=e||mn,n=this.data(),r=n.micromarkExtensions||(n.micromarkExtensions=[]),o=n.fromMarkdownExtensions||(n.fromMarkdownExtensions=[]),i=n.toMarkdownExtensions||(n.toMarkdownExtensions=[]);r.push(function(e){return(0,mt.W)([{text:Dt},{document:{91:{name:"gfmFootnoteDefinition",tokenize:Vt,continuation:{tokenize:Wt},exit:Qt}},text:{91:{name:"gfmFootnoteCall",tokenize:Ut},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:qt,resolveTo:Nt}}},Jt(e),{flow:{null:{name:"table",tokenize:cn,resolveAll:ln}}},{text:{91:hn}}])}(t)),o.push([{transforms:[F],enter:{literalAutolink:v,literalAutolinkEmail:x,literalAutolinkHttp:x,literalAutolinkWww:x},exit:{literalAutolink:A,literalAutolinkEmail:C,literalAutolinkHttp:y,literalAutolinkWww:w}},{enter:{gfmFootnoteDefinition:D,gfmFootnoteDefinitionLabelString:T,gfmFootnoteCall:M,gfmFootnoteCallString:R},exit:{gfmFootnoteDefinition:O,gfmFootnoteDefinitionLabelString:z,gfmFootnoteCall:P,gfmFootnoteCallString:j}},{canContainEols:["delete"],enter:{strikethrough:H},exit:{strikethrough:q}},{enter:{table:Ie,tableData:De,tableHeader:De,tableRow:Ee},exit:{codeText:Te,table:Se,tableData:Le,tableHeader:Le,tableRow:Le}},{exit:{taskListCheckValueChecked:dt,taskListCheckValueUnchecked:dt,paragraph:ht}}]),i.push(function(e){return{extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:k,notInConstruct:b},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:k,notInConstruct:b},{character:":",before:"[ps]",after:"\\/",inConstruct:k,notInConstruct:b}]},{unsafe:[{character:"[",inConstruct:["phrasing","label","reference"]}],handlers:{footnoteDefinition:B,footnoteReference:_}},{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:Z}],handlers:{delete:N}},Oe(e),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:pt}}]}}(t))}}}]);