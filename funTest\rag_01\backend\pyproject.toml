[tool]
[tool.poetry]
name = "app"
version = "0.1.0"
description = ""
authors = [ "<PERSON> <<EMAIL>>" ]
readme = "README.md"

[tool.poetry.scripts]
generate = "app.engine.generate:generate_datasource"

[tool.poetry.dependencies]
python = ">=3.11,<3.13"
fastapi = "^0.109.1"
python-dotenv = "^1.0.0"
aiostream = "^0.5.2"
cachetools = "^5.3.3"
llama-index = "^0.11.17"
docx2txt = "^0.8"
duckduckgo-search = "6.1.7"
llama-index-llms-openai = "^0.2.0"
llama-index-embeddings-openai = "^0.2.3"
llama-index-agent-openai = "^0.3.0"

[tool.poetry.dependencies.uvicorn]
extras = [ "standard" ]
version = "^0.23.2"

[tool.poetry.group]
[tool.poetry.group.dev]
[tool.poetry.group.dev.dependencies]
mypy = "^1.8.0"

[tool.mypy]
python_version = "3.11"
plugins = "pydantic.mypy"
exclude = [ "tests", "venv", ".venv", "output", "config" ]
check_untyped_defs = true
warn_unused_ignores = false
show_error_codes = true
namespace_packages = true
ignore_missing_imports = true
follow_imports = "silent"
implicit_optional = true
strict_optional = false
disable_error_code = [ "return-value", "assignment" ]

[[tool.mypy.overrides]]
module = "app.*"
ignore_missing_imports = false

[build-system]
requires = [ "poetry-core" ]
build-backend = "poetry.core.masonry.api"