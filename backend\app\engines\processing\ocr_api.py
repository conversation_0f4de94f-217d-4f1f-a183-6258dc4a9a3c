from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import base64
import cv2
import numpy as np
from typing import List, Optional
import paddle_ocr_local
from layout_detection import LayoutDetectionYOLO
import torch
from config import *

app = FastAPI()

# 加载模型
model_path = '/data/jinxu/file_analyze_v2/model/doclayout_yolo_docstructbench_imgsz1024.pt'
layout_detector = LayoutDetectionYOLO(model_path)
device = 'cuda' if torch.cuda.is_available() else 'cpu'

# 从file_analyze.py引入的布局类型映射
doclayout_names = {
    0: 'title', 
    1: 'plain text', 
    2: 'abandon', 
    3: 'figure', 
    4: 'figure_caption', 
    5: 'table', 
    6: 'table_caption', 
    7: 'table_footnote', 
    8: 'isolate_formula', 
    9: 'formula_caption'
}

# 请求模型
class OCRRequest(BaseModel):
    image: str  # base64编码的图片
    language: Optional[str] = "ch"  # 默认中文
    detect_direction: Optional[bool] = False

class LayoutRequest(BaseModel):
    image: str
    return_text: Optional[bool] = False

# OCR结果模型
class TextLocation(BaseModel):
    top: int 
    left: int
    width: int
    height: int

class TextResult(BaseModel):
    text: str
    confidence: float
    location: TextLocation

class OCRResponse(BaseModel):
    code: int = 0
    message: str = "success"
    data: dict

# 版面分析结果模型  
class Region(BaseModel):
    type: str
    confidence: float
    location: TextLocation
    text: Optional[str] = None

class LayoutResponse(BaseModel):
    code: int = 0
    message: str = "success" 
    data: dict

def base64_to_image(base64_str: str):
    """将base64字符串转换为OpenCV图像"""
    try:
        img_data = base64.b64decode(base64_str)
        nparr = np.frombuffer(img_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        return img
    except Exception as e:
        raise HTTPException(status_code=400, detail="图片解析失败")

@app.post("/api/v1/ocr/general", response_model=OCRResponse)
async def ocr_general(request: OCRRequest):
    try:
        # 解码图片
        img = base64_to_image(request.image)
        
        # OCR识别
        text = paddle_ocr_local.get_ocr(np.ascontiguousarray(img))
        
        # 构造返回结果
        text_list = [{
            "text": text,
            "confidence": 0.95,
            "location": {
                "top": 0,
                "left": 0, 
                "width": img.shape[1],
                "height": img.shape[0]
            }
        }]
        
        return {
            "code": 0,
            "message": "success",
            "data": {
                "text_list": text_list,
                "direction": 0
            }
        }
        
    except Exception as e:
        return {
            "code": 2001,
            "message": f"OCR识别失败: {str(e)}",
            "data": None
        }

@app.post("/api/v1/layout/analyze", response_model=LayoutResponse) 
async def layout_analyze(request: LayoutRequest):
    try:
        # 解码图片
        img = base64_to_image(request.image)
        
        # 版面分析
        layout_result = layout_detector.detect(img, imgsz=1024, conf=0.2, device=device)[0]
        
        # 获取边界框、置信度、类别
        boxes = layout_result.__dict__['boxes'].xyxy
        scores = layout_result.__dict__['boxes'].conf
        classes = layout_result.__dict__['boxes'].cls
        
        # NMS后处理-去除重叠框
        boxes, scores, classes = layout_detector.layout_nms(boxes, scores, classes, 0)
        
        # 使用layout_sorting进行排序
        sorted_indices = layout_sorting.layout_sort_xy(boxes)
        
        # 构造区域列表
        regions = []
        for idx in sorted_indices:
            box = boxes[idx]
            score = scores[idx]
            cls = classes[idx]
            
            # 使用doclayout_names获取类型名称
            region_type = doclayout_names[int(cls)]
            
            # 跳过abandon类型
            if region_type == 'abandon':
                continue
                
            region = {
                "type": region_type,
                "confidence": float(score),
                "location": {
                    "top": int(box[1]),
                    "left": int(box[0]),
                    "width": int(box[2] - box[0]),
                    "height": int(box[3] - box[1])
                }
            }
            
            # 如果需要返回文字内容且不是图片/表格
            if request.return_text and region_type not in ['figure', 'table']:
                # 扩大OCR识别区域
                top = max(0, int(box[1]) - ocr_bounding_box_y_dimension_widen)
                left = max(0, int(box[0]) - ocr_bounding_box_x_dimension_widen)
                bottom = min(img.shape[0], int(box[3]) + ocr_bounding_box_y_dimension_widen)
                right = min(img.shape[1], int(box[2]) + ocr_bounding_box_x_dimension_widen)
                
                cropped = img[top:bottom, left:right]
                text = paddle_ocr_local.get_ocr(np.ascontiguousarray(cropped))
                region["text"] = text
                
            regions.append(region)
            
        return {
            "code": 0,
            "message": "success",
            "data": {
                "regions": regions,
                "page_type": "normal"
            }
        }
        
    except Exception as e:
        return {
            "code": 2002, 
            "message": f"版面分析失败: {str(e)}",
            "data": None
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 