from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional
from ..models.reference import Reference, RAGReference, ChartReference
from ..db.mongodb import db
from ..utils.auth import verify_token
from bson import ObjectId
from datetime import datetime

router = APIRouter(
    prefix="/api",
    tags=["references"]
)

# 创建新的引用
@router.post("/references", response_model=Reference)
async def create_reference(reference: Reference, current_user: dict = Depends(verify_token)):
    new_reference = reference.dict()
    new_reference["created_at"] = datetime.now()
    result = await db["references"].insert_one(new_reference)
    created_reference = await db["references"].find_one({"_id": result.inserted_id})
    return Reference(**created_reference)

# 获取引用列表
@router.get("/references", response_model=List[Reference])
async def read_references(
    conversation_id: Optional[str] = None,
    message_id: Optional[str] = None,
    skip: int = 0,
    limit: int = 10,
    current_user: dict = Depends(verify_token)
):
    query = {}
    if conversation_id:
        query["conversation_id"] = conversation_id
    if message_id:
        query["message_id"] = message_id
    
    references = await db["references"].find(query).skip(skip).limit(limit).to_list(length=limit)
    return [Reference(**ref) for ref in references]


# 获取引用列表
@router.get("/references/conversation/{conversation_id}", response_model=List[Reference])
async def read_references(
    conversation_id: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    query = {conversation_id:conversation_id}
    references = await db["references"].find(query);
    return [Reference(**ref) for ref in references]

# 获取单个引用
@router.get("/references/{reference_id}", response_model=Reference)
async def read_reference(reference_id: str, current_user: dict = Depends(verify_token)):
    reference = await db["references"].find_one({"_id": ObjectId(reference_id)})
    if reference is None:
        raise HTTPException(status_code=404, detail="Reference not found")
    return Reference(**reference)

# 更新引用
@router.put("/references/{reference_id}", response_model=Reference)
async def update_reference(reference_id: str, reference: Reference, current_user: dict = Depends(verify_token)):
    update_data = reference.dict(exclude_unset=True)
    result = await db["references"].update_one(
        {"_id": ObjectId(reference_id)},
        {"$set": update_data}
    )
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Reference not found")
    updated_reference = await db["references"].find_one({"_id": ObjectId(reference_id)})
    return Reference(**updated_reference)

# 删除引用
@router.delete("/references/{reference_id}", response_model=dict)
async def delete_reference(reference_id: str, current_user: dict = Depends(verify_token)):
    result = await db["references"].delete_one({"_id": ObjectId(reference_id)})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Reference not found")
    return {"message": "Reference deleted successfully"}

# 添加 RAG 引用
@router.post("/references/{reference_id}/rag", response_model=Reference)
async def add_rag_reference(reference_id: str, rag_reference: RAGReference, current_user: dict = Depends(verify_token)):
    result = await db["references"].update_one(
        {"_id": ObjectId(reference_id)},
        {"$push": {"rag_references": rag_reference.dict()}}
    )
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Reference not found")
    updated_reference = await db["references"].find_one({"_id": ObjectId(reference_id)})
    return Reference(**updated_reference)

# 添加图表引用
@router.post("/references/{reference_id}/chart", response_model=Reference)
async def add_chart_reference(reference_id: str, chart_reference: ChartReference, current_user: dict = Depends(verify_token)):
    result = await db["references"].update_one(
        {"_id": ObjectId(reference_id)},
        {"$push": {"chart_references": chart_reference.dict()}}
    )
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Reference not found")
    updated_reference = await db["references"].find_one({"_id": ObjectId(reference_id)})
    return Reference(**updated_reference)
