"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2885],{3648:function(e,r,o){o.r(r);var n=o(5574),l=o.n(n),t=o(67294),c=o(71471),a=o(71230),s=o(15746),i=o(4393),d=o(55102),u=o(83622),g=o(66309),h=o(2487),p=o(60520),b=(o(74864),o(85893)),x=c.Z.Title,f=c.Z.Paragraph;r.default=function(){var e=(0,t.useState)("\n    # 会议提纲\n    ## 1. 企业基本信息\n    - 企业名称：示例公司\n    - 行业：信息技术\n    - 位置：北京市\n\n    ## 2. 领导信息\n    - 姓名：张三\n    - 职位：CEO\n\n    ## 3. 企业最新资讯\n    - [2023年10月，公司获得了最佳创新奖。](#)\n    - 类型：奖项\n\n    ## 4. 拜访领导的宣传内容\n    - 张三在2023年被评为年度最佳CEO。\n\n    ## 5. 合作情况\n    - 与本公司签署了长期合作协议。\n\n    ## 6. 潜在产品推荐\n    - 产品A：适合信息技术行业的解决方案\n    - 产品B：企业管理软件\n  "),r=l()(e,2),o=r[0],n=(r[1],(0,t.useRef)(null));return(0,t.useEffect)((function(){if(n.current){var e=new p.cW({element:n.current,placeholder:"请输入谈参方案内容",content:o,toolbarVisible:!0});return function(){e.destroy()}}}),[o]),(0,b.jsxs)("div",{style:{padding:16,minHeight:"100vh",backgroundColor:"#f0f2f5"},children:[(0,b.jsx)(a.Z,{gutter:16,style:{marginBottom:16},children:(0,b.jsx)(s.Z,{span:24,children:(0,b.jsx)(i.Z,{style:{boxShadow:"0 4px 8px rgba(0, 0, 0, 0.1)",borderRadius:8},children:(0,b.jsxs)(d.Z.Group,{compact:!0,style:{display:"flex",alignItems:"center"},children:[(0,b.jsx)(d.Z.Search,{placeholder:"搜索企业",style:{flex:1,marginRight:8,borderRadius:4},enterButton:!0}),(0,b.jsx)(d.Z.TextArea,{placeholder:"输入谈参内容",rows:1,style:{flex:2,marginRight:8,borderRadius:4}}),(0,b.jsx)(u.ZP,{type:"primary",style:{borderRadius:4},children:"生成文档"})]})})})}),(0,b.jsxs)(a.Z,{gutter:16,style:{height:"calc(100vh - 200px)"},children:[(0,b.jsx)(s.Z,{span:16,children:(0,b.jsx)(i.Z,{title:"谈参方案",style:{height:"100%",overflow:"hidden",border:"none",margin:"4px 0",boxShadow:"0 4px 8px rgba(0, 0, 0, 0.1)"},children:(0,b.jsx)("div",{ref:n,style:{height:"calc(100% - 48px)",margin:"0",backgroundColor:"#fff",padding:16,borderRadius:8}})})}),(0,b.jsx)(s.Z,{span:8,children:(0,b.jsxs)(i.Z,{title:"企业信息",style:{marginBottom:4,boxShadow:"0 4px 8px rgba(0, 0, 0, 0.1)"},children:[(0,b.jsx)(x,{level:5,children:"企业基本信息"}),(0,b.jsx)(f,{children:"企业名称：示例公司"}),(0,b.jsx)(f,{children:"行业：信息技术"}),(0,b.jsx)(f,{children:"位置：北京市"}),(0,b.jsxs)(f,{children:["资质：",(0,b.jsx)(g.Z,{color:"green",children:"专精特新"})," ",(0,b.jsx)(g.Z,{color:"blue",children:"高新"})," ",(0,b.jsx)(g.Z,{color:"gold",children:"国央企"})]}),(0,b.jsx)(x,{level:5,children:"领导信息"}),(0,b.jsx)(f,{children:"姓名：张三"}),(0,b.jsx)(f,{children:"部门：技术部"}),(0,b.jsx)(f,{children:"职务：CEO"}),(0,b.jsxs)(f,{children:["个人标签：",(0,b.jsx)(g.Z,{color:"purple",children:"千人计划"})," ",(0,b.jsx)(g.Z,{color:"cyan",children:"清华大学"})]}),(0,b.jsx)(x,{level:5,children:"企业最新资讯"}),(0,b.jsx)(h.Z,{itemLayout:"vertical",dataSource:[{title:"公司获得了最佳创新奖",type:"奖项"},{title:"新产品发布会成功举办",type:"发布会"}],renderItem:function(e){return(0,b.jsx)(h.Z.Item,{children:(0,b.jsx)(h.Z.Item.Meta,{title:(0,b.jsx)("a",{href:"#",children:e.title}),description:(0,b.jsx)(g.Z,{color:"blue",children:e.type})})})}}),(0,b.jsx)(x,{level:5,children:"拜访领导的宣传内容"}),(0,b.jsx)(f,{children:"张三在2023年被评为年度最佳CEO。"}),(0,b.jsx)(x,{level:5,children:"合作情况"}),(0,b.jsx)(f,{children:"与本公司签署了长期合作协议。"}),(0,b.jsxs)(f,{children:["标签：",(0,b.jsx)(g.Z,{color:"red",children:"重要"})," ",(0,b.jsx)(g.Z,{color:"orange",children:"长期"})]}),(0,b.jsx)(x,{level:5,children:"潜在产品推荐"}),(0,b.jsx)(f,{children:"产品A：适合信息技术行业的解决方案"}),(0,b.jsx)(f,{children:"产品B：企业管理软件"})]})})]})]})}},15746:function(e,r,o){var n=o(21584);r.Z=n.Z},71230:function(e,r,o){var n=o(17621);r.Z=n.Z},66309:function(e,r,o){o.d(r,{Z:function(){return B}});var n=o(67294),l=o(93967),t=o.n(l),c=o(98423),a=o(98787),s=o(69760),i=o(96159),d=o(45353),u=o(53124),g=o(11568),h=o(15063),p=o(14747),b=o(83262),x=o(83559);const f=e=>{const{lineWidth:r,fontSizeIcon:o,calc:n}=e,l=e.fontSizeSM;return(0,b.IX)(e,{tagFontSize:l,tagLineHeight:(0,g.bf)(n(e.lineHeightSM).mul(l).equal()),tagIconSize:n(o).sub(n(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},m=e=>({defaultBg:new h.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var C=(0,x.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:o,tagPaddingHorizontal:n,componentCls:l,calc:t}=e,c=t(n).sub(o).equal(),a=t(r).sub(o).equal();return{[l]:Object.assign(Object.assign({},(0,p.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,g.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${l}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${l}-close-icon`]:{marginInlineStart:a,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${l}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${l}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:c}}),[`${l}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(f(e))),m),y=function(e,r){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var l=0;for(n=Object.getOwnPropertySymbols(e);l<n.length;l++)r.indexOf(n[l])<0&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(o[n[l]]=e[n[l]])}return o};const j=n.forwardRef(((e,r)=>{const{prefixCls:o,style:l,className:c,checked:a,onChange:s,onClick:i}=e,d=y(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:g,tag:h}=n.useContext(u.E_),p=g("tag",o),[b,x,f]=C(p),m=t()(p,`${p}-checkable`,{[`${p}-checkable-checked`]:a},null==h?void 0:h.className,c,x,f);return b(n.createElement("span",Object.assign({},d,{ref:r,style:Object.assign(Object.assign({},l),null==h?void 0:h.style),className:m,onClick:e=>{null==s||s(!a),null==i||i(e)}})))}));var v=j,k=o(98719);var $=(0,x.bk)(["Tag","preset"],(e=>(e=>(0,k.Z)(e,((r,o)=>{let{textColor:n,lightBorderColor:l,lightColor:t,darkColor:c}=o;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:n,background:t,borderColor:l,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(f(e))),m);const Z=(e,r,o)=>{const n="string"!=typeof(l=o)?l:l.charAt(0).toUpperCase()+l.slice(1);var l;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${o}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,x.bk)(["Tag","status"],(e=>{const r=f(e);return[Z(r,"success","Success"),Z(r,"processing","Info"),Z(r,"error","Error"),Z(r,"warning","Warning")]}),m),O=function(e,r){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var l=0;for(n=Object.getOwnPropertySymbols(e);l<n.length;l++)r.indexOf(n[l])<0&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(o[n[l]]=e[n[l]])}return o};const w=n.forwardRef(((e,r)=>{const{prefixCls:o,className:l,rootClassName:g,style:h,children:p,icon:b,color:x,onClose:f,bordered:m=!0,visible:y}=e,j=O(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:v,direction:k,tag:Z}=n.useContext(u.E_),[w,E]=n.useState(!0),B=(0,c.Z)(j,["closeIcon","closable"]);n.useEffect((()=>{void 0!==y&&E(y)}),[y]);const I=(0,a.o2)(x),P=(0,a.yT)(x),T=I||P,N=Object.assign(Object.assign({backgroundColor:x&&!T?x:void 0},null==Z?void 0:Z.style),h),R=v("tag",o),[z,H,A]=C(R),L=t()(R,null==Z?void 0:Z.className,{[`${R}-${x}`]:T,[`${R}-has-color`]:x&&!T,[`${R}-hidden`]:!w,[`${R}-rtl`]:"rtl"===k,[`${R}-borderless`]:!m},l,g,H,A),M=e=>{e.stopPropagation(),null==f||f(e),e.defaultPrevented||E(!1)},[,W]=(0,s.Z)((0,s.w)(e),(0,s.w)(Z),{closable:!1,closeIconRender:e=>{const r=n.createElement("span",{className:`${R}-close-icon`,onClick:M},e);return(0,i.wm)(e,r,(e=>({onClick:r=>{var o;null===(o=null==e?void 0:e.onClick)||void 0===o||o.call(e,r),M(r)},className:t()(null==e?void 0:e.className,`${R}-close-icon`)})))}}),_="function"==typeof j.onClick||p&&"a"===p.type,F=b||null,q=F?n.createElement(n.Fragment,null,F,p&&n.createElement("span",null,p)):p,X=n.createElement("span",Object.assign({},B,{ref:r,className:L,style:N}),q,W,I&&n.createElement($,{key:"preset",prefixCls:R}),P&&n.createElement(S,{key:"status",prefixCls:R}));return z(_?n.createElement(d.Z,{component:"Tag"},X):X)})),E=w;E.CheckableTag=v;var B=E}}]);