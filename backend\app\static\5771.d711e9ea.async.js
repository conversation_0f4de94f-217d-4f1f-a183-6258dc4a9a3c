"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5771],{11488:function(n,t,e){function r(n){console.log("🚀 ~ extractKnowledgeReferenceListFromAIChat ~ modules-引用:",n);var t=n.find((function(n){return"AI 对话"===n.moduleName&&n.quoteList}));return console.log("🚀 ~ extractKnowledgeReferenceListFromAIChat ~ aiChatModule:",t),t&&t.quoteList?t.quoteList.map((function(n){return{id:n.id,type:"knowledge",reference:n}})):[]}function a(n){if(n){var t=[];return n.forEach((function(n){n.references&&(t=t.concat(n.references.map((function(t){return t.id=n.message_id+"-"+t.id,t.messageId=n.message_id,t}))))})),t}return[]}e.d(t,{P:function(){return a},n:function(){return r}})},93933:function(n,t,e){e.d(t,{$Z:function(){return T},$o:function(){return h},Db:function(){return m},Mw:function(){return s},SJ:function(){return w},X1:function(){return y},Xw:function(){return f},bk:function(){return C},fx:function(){return S},qP:function(){return N},tn:function(){return k},zl:function(){return j}});var r=e(15009),a=e.n(r),u=e(99289),o=e.n(u),c=e(78158),i=e(10981);function s(n){return p.apply(this,arguments)}function p(){return(p=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,c.N)("/api/myConversations",{method:"GET",params:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function f(n){return l.apply(this,arguments)}function l(){return(l=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,c.N)("/api/conversations",{method:"POST",data:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function h(n,t){return d.apply(this,arguments)}function d(){return(d=o()(a()().mark((function n(t,e){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,c.N)("/api/conversationActive/"+t,{method:"PUT",data:e}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function m(n){return v.apply(this,arguments)}function v(){return(v=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,c.N)("/api/clearConversation/"+t,{method:"PUT"}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function w(n){return b.apply(this,arguments)}function b(){return(b=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,c.N)("/api/conversations/"+t,{method:"DELETE"}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function y(n,t){return x.apply(this,arguments)}function x(){return(x=o()(a()().mark((function n(t,e){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,c.N)("/api/conversations/".concat(t),{method:"PUT",data:e}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function k(n){return g.apply(this,arguments)}function g(){return(g=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,c.N)("/api/message",{method:"POST",data:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function T(n){return P.apply(this,arguments)}function P(){return(P=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,c.N)("/api/messages/".concat(t),{method:"DELETE"}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function N(n){return _.apply(this,arguments)}function _(){return(_=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,c.N)("/api/delete_messages",{method:"DELETE",data:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function C(n){return E.apply(this,arguments)}function E(){return(E=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,c.N)("/api/messages/collected",{method:"POST",data:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function S(n){return Z.apply(this,arguments)}function Z(){return(Z=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,c.N)("/api/app/get_knowledge_bases",{method:"POST",data:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}function j(n){return L.apply(this,arguments)}function L(){return(L=o()(a()().mark((function n(t){var e,r;return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return e=(0,i.bW)(),n.next=3,fetch("/api/app/chat/chat2kb",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});case 3:if((r=n.sent).ok){n.next=6;break}throw new Error("HTTP 错误！状态码：".concat(r.status));case 6:return n.abrupt("return",r);case 7:case"end":return n.stop()}}),n)})))).apply(this,arguments)}},13973:function(n,t,e){e.d(t,{Z:function(){return x}});var r=e(15009),a=e.n(r),u=e(99289),o=e.n(u),c=e(5574),i=e.n(c),s=e(67294),p=e(55102),f=e(2453),l=e(17788),h=e(84567),d=e(78158);function m(n){return v.apply(this,arguments)}function v(){return(v=o()(a()().mark((function n(t){return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,d.N)("/api/feedbacks",{method:"POST",data:t}));case 1:case"end":return n.stop()}}),n)})))).apply(this,arguments)}var w=e(85893),b=p.Z.TextArea,y=[{label:"回答不准确",value:"inaccurate"},{label:"回答不完整",value:"incomplete"},{label:"理解错误",value:"irrelevant"},{label:"生成幻觉",value:"hallucination"},{label:"内容混乱",value:"error"},{label:"有害内容",value:"harmful"},{label:"其他问题",value:"other"}],x=function(n){var t=n.visible,e=n.messageId,r=n.conversationId,u=n.appInfo,c=n.onClose,p=s.useState(""),d=i()(p,2),v=d[0],x=d[1],k=s.useState([]),g=i()(k,2),T=g[0],P=g[1],N=function(){var n=o()(a()().mark((function n(){var t;return a()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(0!==T.length){n.next=3;break}return f.ZP.error("请至少选择一个反馈类型"),n.abrupt("return");case 3:return n.prev=3,t={message_id:e,conversation_id:r,app_info:u,content:v,feedback_types:T},console.log("feedbackData===>",t),n.next=8,m(t);case 8:n.sent.success?(f.ZP.success("感谢您的反馈！"),_()):f.ZP.error("提交反馈失败，请稍后重试"),n.next=16;break;case 12:n.prev=12,n.t0=n.catch(3),console.error("提交反馈失败:",n.t0),f.ZP.error("提交反馈失败，请稍后重试");case 16:case"end":return n.stop()}}),n,null,[[3,12]])})));return function(){return n.apply(this,arguments)}}(),_=function(){x(""),P([]),c()};return(0,w.jsxs)(l.Z,{title:"反馈问题",open:t,onOk:N,onCancel:_,okText:"提交",cancelText:"取消",children:[(0,w.jsxs)("div",{style:{marginBottom:16},children:[(0,w.jsx)("div",{style:{marginBottom:8},children:"请选择存在的问题（可多选）："}),(0,w.jsx)(h.Z.Group,{options:y,value:T,onChange:function(n){return P(n)}})]}),(0,w.jsxs)("div",{children:[(0,w.jsx)("div",{style:{marginBottom:8},children:"详细反馈（选填）："}),(0,w.jsx)(b,{value:v,onChange:function(n){return x(n.target.value)},placeholder:"请输入您的具体反馈意见...",rows:4})]})]})}}}]);