from pymongo import MongoClient
from datetime import datetime
import requests
from elasticsearch import Elasticsearch
from langchain.text_splitter import RecursiveCharacterTextSplitter
import hashlib


collection_files = "source_files"
collection_files_pages = "source_files_pages"
collection_files_pages_chunk = "chunks"
mongo_url= "mongodb://memInterview:<EMAIL>:37017/roardataAiApp_test?authSource=admin"
client = MongoClient(mongo_url)
db = client['roardataAiApp_test']

# embedding
M3_LARGE_EMBDDING_SERVICE = 'http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/bge_m3_finetune_20240710/v1/embeddings'  # 最新（20240710）阿里派平台
access_token = 'ZWY0YzJkZTM4OGI0YmI3YjljOTI4NGE5ZDMxMDVhMzUzZjA3YmYxMw=='  # 派平台调此接口需要先做口令验证

# ES
es = Elasticsearch([
    {'host': '**************', 'port': 9600},
    {'host': '**************', 'port': 9600},
    {'host': '**************', 'port': 9600}
], timeout=3600)
ES_INDEX = "wise_agent_chunking"


def get_text():
    file_list = list(db[collection_files].find({}))
    
    pages_list = []
    for file in file_list:
        file_id = file['_id']
        page_list = list(db[collection_files_pages].find({"file_id": file_id}))
        pages_list.append(page_list)
    
    return pages_list


def get_embedding(text):
    headers = {'Content-Type': 'application/json', 'Authorization': f'{access_token}'}
    req = {
        "input": [text],
        "model": "bge-m3"
    }
    embdd_response = requests.post(url=M3_LARGE_EMBDDING_SERVICE, json=req, headers=headers)
    if embdd_response.status_code == 200:
        return embdd_response.json()['data'][0]['embedding']
    else:
        return None
    

def format_sections(text):
    """
    格式化文本，识别并转换多种类型的章节标题为Markdown格式，并删除目录部分。

    参数:
    text - 原始文本内容

    返回值:
    格式化后的Markdown文本
    """
    # text = text.replace("第四章", "", 1)

    # pattern = re.compile(r'(第[一二三四五六七八九十百千万]+[章]+[ \n])')
    pattern = re.compile(r'(第[一二三四五六七八九十百千万]+[章]+[\n\n]*)')
    matches = pattern.findall(text)

    set_matches = []
    for match in matches:
        if match not in set_matches:
            set_matches.append(match)

    context_list = []
    if len(set_matches) > 0:
        for match in set_matches:
            modified_match = "##&#" + match
            text = text.replace(match, modified_match)
        context_list = text.split("##&#")[1:]

    else:
        pattern = re.compile(r'[一二三四五六七八九十]+[ ]*、')
        matches = pattern.findall(text)

        set_matches = []
        for match in matches:
            if match not in set_matches:
                set_matches.append(match)

        if len(matches) > 0:
            for match in matches:
                modified_match = "##&#" + match
                text = text.replace(match, modified_match)
            context_list = text.split("##&#")[1:]

        else:
            pattern = re.compile(r'(第[一二三四五六七八九十百千万]+[条])')
            matches = pattern.findall(text)

            set_matches = []
            for match in matches:
                if match not in set_matches:
                    set_matches.append(match)

            if len(matches) > 0:
                for match in matches:
                    modified_match = "##&#" + match
                    text = text.replace(match, modified_match)
                context_list = text.split("##&#")[1:]

    if len(set_matches) > 0 and len(matches) / len(set_matches) == 2:
        context_list = context_list[-len(set_matches):]

    return context_list, set_matches

def son_format_sections(text):
    pattern = re.compile(r'(第[一二三四五六七八九十百千万]+[条]+[ \n])')
    matches = pattern.findall(text)

    set_matches = []
    for match in matches:
        if match not in set_matches:
            set_matches.append(match)
    context_list = []
    if len(matches) > 0:
        for match in matches:
            modified_match = "##&#" + match
            text = text.replace(match, modified_match)
        context_list = text.split("##&#")[1:]

    return context_list, set_matches

def combine_strings(string_list, max_length=700):
    combined_strings = []
    combined_index_string_list = []
    current_string = ""
    zi_strings = []
    for string in string_list:
        if len(current_string) + len(string) <= max_length:
            current_string += string
            zi_strings.append(string)
        else:
            combined_strings.append(current_string)
            if len(zi_strings) == 0:
                zi_strings.append(current_string)
            zi_strings = [zi_string for zi_string in zi_strings if len(zi_string) > 0]
            combined_index_string_list.append(zi_strings)
            current_string = string
            zi_strings = [current_string]

    if current_string:
        # print(len(current_string))
        combined_strings.append(current_string)
        zi_strings = [zi_string for zi_string in zi_strings if len(zi_string) > 0]
        combined_index_string_list.append(zi_strings)

    return combined_strings, combined_index_string_list

def format_sections_v2(text, pattern_re):
    pattern = re.compile(pattern_re)
    matches = pattern.findall(text)

    return matches

def txt2json(all_context):
    split_str_use_list = []
    # 识别 章
    charpter_pattern_re = r'(第[一二三四五六七八九十百千万]+[章]+[ \n])'
    charpter_split_str = "###"
    charpter_matches = format_sections_v2(all_context, charpter_pattern_re)
    if len(charpter_matches) > 0:
        for charpter_matche in charpter_matches:
            all_context = all_context.replace(charpter_matche, charpter_split_str+charpter_matche)
        # all_context = re.sub(charpter_pattern_re, charpter_split_str+charpter_matches[0], all_context)
        split_str_use_list.append(charpter_split_str)

        # 识别 节
        jie_pattern_re = r'(第[一二三四五六七八九十百千万]+[节]+[ \n])'
        jie_split_str = "$$$"
        jie_matches = format_sections_v2(all_context, jie_pattern_re)
        if len(jie_matches) > 0:
            for jie_matche in jie_matches:
                all_context = all_context.replace(jie_matche, jie_split_str + jie_matche)
            # all_context = re.sub(jie_pattern_re, jie_split_str+jie_matches[0], all_context)
            split_str_use_list.append(jie_split_str)

            # 识别 条
            tiao_pattern_re = r'(第[一二三四五六七八九十百千万]+[条]+[ \n])'
            tiao_split_str = "&&&"
            tiao_matches = format_sections_v2(all_context, tiao_pattern_re)
            if len(tiao_matches) > 0:
                for tiao_matche in tiao_matches:
                    all_context = all_context.replace(tiao_matche, tiao_split_str + tiao_matche)
                # all_context = re.sub(tiao_pattern_re, tiao_split_str+tiao_matches[0], all_context)
                split_str_use_list.append(tiao_split_str)
        else:
            # 识别 条
            tiao_pattern_re = r'(第[一二三四五六七八九十百千万]+[条]+[ \n])'
            tiao_split_str = "&&&"
            tiao_matches = format_sections_v2(all_context, tiao_pattern_re)
            if len(tiao_matches) > 0:
                for tiao_matche in tiao_matches:
                    all_context = all_context.replace(tiao_matche, tiao_split_str + tiao_matche)
                # all_context = re.sub(tiao_pattern_re, tiao_split_str+tiao_matches[0], all_context)
                split_str_use_list.append(tiao_split_str)
            else:
                # 识别 一、
                title_pattern_re = r'[一二三四五六七八九十]+、'
                title_split_str = "==="
                title_matches = format_sections_v2(all_context, title_pattern_re)
                if len(title_matches) > 0:
                    for title_matche in title_matches:
                        all_context = all_context.replace(title_matche, title_split_str + title_matche)
                    # all_context = re.sub(title_pattern_re, title_split_str+title_matches[0], all_context)
                    split_str_use_list.append(title_split_str)
                    # 识别 （一）
                    subtitle_pattern_re = r'（[一二三四五六七八九十]+）'
                    subtitle_split_str = "¥¥¥"
                    subtitle_matches = format_sections_v2(all_context, subtitle_pattern_re)
                    if len(subtitle_matches) > 0:
                        for subtitle_matche in subtitle_matches:
                            all_context = all_context.replace(subtitle_matche, subtitle_split_str + subtitle_matche)
                        # all_context = re.sub(subtitle_pattern_re, subtitle_split_str+subtitle_matches[0], all_context)
                        split_str_use_list.append(subtitle_split_str)

    else:
        # 识别 一、
        title_pattern_re = r'[一二三四五六七八九十]+、'
        title_split_str = "==="
        title_matches = format_sections_v2(all_context, title_pattern_re)
        if len(title_matches) > 0:
            for title_matche in title_matches:
                all_context = all_context.replace(title_matche, title_split_str + title_matche)
            # all_context = re.sub(title_pattern_re, title_split_str+title_matches[0], all_context)
            split_str_use_list.append(title_split_str)

            # 识别 （一）
            subtitle_pattern_re = r'（[一二三四五六七八九十]+）'
            subtitle_split_str = "¥¥¥"
            subtitle_matches = format_sections_v2(all_context, subtitle_pattern_re)
            if len(subtitle_matches) > 0:
                for subtitle_matche in subtitle_matches:
                    all_context = all_context.replace(subtitle_matche, subtitle_split_str + subtitle_matche)
                # all_context = re.sub(subtitle_pattern_re, subtitle_split_str+subtitle_matches[0], all_context)
                split_str_use_list.append(subtitle_split_str)
        else:
            tiao_pattern_re = r'(第[一二三四五六七八九十百千万]+[条]+[ \n])'
            tiao_split_str = "&&&"
            tiao_matches = format_sections_v2(all_context, tiao_pattern_re)
            if len(tiao_matches) > 0:
                for tiao_matche in tiao_matches:
                    all_context = all_context.replace(tiao_matche, tiao_split_str + tiao_matche)
                # all_context = re.sub(tiao_pattern_re, tiao_split_str+tiao_matches[0], all_context)
                split_str_use_list.append(tiao_split_str)

            else:
                print("切分失败")

    return all_context, split_str_use_list

def split_context_v2(context, split_str_list, pdf_file_name):
    all_split_str_list = [
        "###",  # 第一章
        "===",  # 一、
        "$$$",  # 第一节
        "&&&",  # 第一条
        "¥¥¥"  # （一）
    ]

    json_context_list = []
    if "###" in split_str_list:  # 章
        charpter_split_texts = context.split("###")
        charpter_split_texts = [i for i in charpter_split_texts if len(i) > 0]
        for charpter_split_text in charpter_split_texts:
            if "$$$" in split_str_list:  # 节
                jie_split_texts = charpter_split_text.split("$$$")
                jie_split_texts = [i for i in jie_split_texts if len(i) > 0]
                for jie_split_text in jie_split_texts:
                    if "&&&" in split_str_list:  # 条
                        tiao_split_texts = jie_split_text.split("&&&")
                        tiao_split_texts = [i for i in tiao_split_texts if len(i) > 0]
                        combined_strings, combined_index_string_list = combine_strings(tiao_split_texts, max_length=500)
                        for i in range(len(combined_strings)):
                            json_unit = {
                                "sourceId": "",
                                "context": combined_strings[i],
                                "index": combined_index_string_list[i],
                                "fileName": pdf_file_name
                            }
                            json_context_list.append(json_unit)

            elif "===" in split_str_list:
                title_split_texts = charpter_split_text.split("===")
                title_split_texts = [i for i in title_split_texts if len(i) > 0]
                for title_split_text in title_split_texts:
                    if "¥¥¥" in split_str_list:
                        subtitle_split_texts = title_split_text.split("¥¥¥")
                        subtitle_split_texts = [i for i in subtitle_split_texts if len(i) > 0]
                        combined_strings, combined_index_string_list = combine_strings(subtitle_split_texts, max_length=500)
                        for i in range(len(combined_strings)):
                            json_unit = {
                                "sourceId": "",
                                "context": combined_strings[i],
                                "index": combined_index_string_list[i],
                                "fileName": pdf_file_name
                            }
                            json_context_list.append(json_unit)

            elif "&&&" in split_str_list:
                tiao_split_texts = charpter_split_text.split("&&&")
                tiao_split_texts = [i for i in tiao_split_texts if len(i) > 0]
                combined_strings, combined_index_string_list = combine_strings(tiao_split_texts, max_length=500)
                for i in range(len(combined_strings)):
                    json_unit = {
                        "sourceId": "",
                        "context": combined_strings[i],
                        "index": combined_index_string_list[i],
                        "fileName": pdf_file_name
                    }
                    json_context_list.append(json_unit)

            elif "¥¥¥" in split_str_list:
                subtitle_split_texts = charpter_split_text.split("¥¥¥")
                subtitle_split_texts = [i for i in subtitle_split_texts if len(i) > 0]
                combined_strings, combined_index_string_list = combine_strings(subtitle_split_texts, max_length=500)
                for i in range(len(combined_strings)):
                    json_unit = {
                        "sourceId": "",
                        "context": combined_strings[i],
                        "index": combined_index_string_list[i],
                        "fileName": pdf_file_name
                    }
                    json_context_list.append(json_unit)

            else:
                json_unit = {
                    "sourceId": "",
                    "context": charpter_split_text,
                    "index": [charpter_split_text],
                    "fileName": pdf_file_name
                }
                json_context_list.append(json_unit)

    elif "===" in split_str_list:
        title_split_texts = context.split("===")
        title_split_texts = [i for i in title_split_texts if len(i) > 0]
        for title_split_text in title_split_texts:
            if "¥¥¥" in split_str_list:
                subtitle_split_texts = title_split_text.split("¥¥¥")
                subtitle_split_texts = [i for i in subtitle_split_texts if len(i) > 0]
                combined_strings, combined_index_string_list = combine_strings(subtitle_split_texts, max_length=500)
                for i in range(len(combined_strings)):
                    json_unit = {
                        "sourceId": "",
                        "context": combined_strings[i],
                        "index": combined_index_string_list[i],
                        "fileName": pdf_file_name
                    }
                    json_context_list.append(json_unit)

            elif "&&&" in split_str_list:
                tiao_split_texts = title_split_text.split("&&&")
                tiao_split_texts = [i for i in tiao_split_texts if len(i) > 0]
                combined_strings, combined_index_string_list = combine_strings(tiao_split_texts, max_length=500)
                for i in range(len(combined_strings)):
                    json_unit = {
                        "sourceId": "",
                        "context": combined_strings[i],
                        "index": combined_index_string_list[i],
                        "fileName": pdf_file_name
                    }
                    json_context_list.append(json_unit)

            else:
                json_unit = {
                    "sourceId": "",
                    "context": title_split_text,
                    "index": [title_split_text],
                    "fileName": pdf_file_name
                }
                json_context_list.append(json_unit)

    elif "&&&" in split_str_list:
        tiao_split_texts = context.split("&&&")
        tiao_split_texts = [i for i in tiao_split_texts if len(i) > 0]
        combined_strings, combined_index_string_list = combine_strings(tiao_split_texts, max_length=500)

        for i in range(len(combined_strings)):
            json_unit = {
                "sourceId": "",
                "context": combined_strings[i],
                "index": combined_index_string_list[i],
                "fileName": pdf_file_name
            }
            json_context_list.append(json_unit)

    return json_context_list


def split_content_recursive(content):
    """递归切分"""
    text_splitter = RecursiveCharacterTextSplitter(
        # separators=["\n"],
        separators=["。"],
        chunk_size=500,
        chunk_overlap=50,
        length_function=len,
        is_separator_regex=False,
    )
    texts = text_splitter.create_documents([content])
    texts = [text.page_content for text in texts]
    
    new_texts = []
    for text in texts:
        if text.startswith("。"):
            text = text[1:]
        new_texts.append(text)
    return new_texts


def file_chunking(text):
    context_list, matches = format_sections(text)
    json_context_list = []
    chunk_list = []
    if len(context_list) > 0 and len(context_list) == len(matches) and "" not in context_list:
        # 判断字数
        for context in context_list:
            if len(context) > 700:
                # 继续拆分
                son_context_list, son_matches = son_format_sections(context)
                if len(son_context_list) > 0 and len(son_context_list) == len(son_matches):
                    short_son_context_list = [son_context for son_context in son_context_list if len(son_context) <= 500]
                    combined_strings, combined_index_string_list = combine_strings(short_son_context_list)
                    for i in range(len(combined_strings)):
                        json_unit = {
                            "sourceId": "",
                            "context": combined_strings[i],
                            "index": combined_index_string_list[i],
                            # "fileName": file_name
                        }
                        json_context_list.append(json_unit)
                        chunk_list.extend(combined_index_string_list[i])

                    long_son_context_list = [son_context for son_context in son_context_list if len(son_context) > 500]
                    for long_son_context in long_son_context_list:
                        son_context_list_tri = split_content_recursive(long_son_context)
                        for son_context_recursive in son_context_list_tri:
                            son_context_list = son_context_recursive.split("。")
                            son_context_list = [son_context for son_context in son_context_list if len(son_context) > 0]
                            json_unit = {
                                "sourceId": "",
                                "context": son_context_recursive,
                                "index": son_context_list,
                                # "fileName": file_name
                            }
                            json_context_list.append(json_unit)
                            chunk_list.extend(son_context_list)
                else:
                    # 不能按条切分，则按特殊字符切分
                    son_context_list = split_content_recursive(context)
                    for son_context_recursive in son_context_list:
                        son_context_list = son_context_recursive.split("。")
                        son_context_list = [son_context for son_context in son_context_list if len(son_context) > 0]
                        json_unit = {
                            "sourceId": "",
                            "context": son_context_recursive,
                            "index": son_context_list,
                            # "fileName": file_name
                        }
                        json_context_list.append(json_unit)
                        chunk_list.extend(son_context_list)

                context_list.remove(context)
        all_context = "\n".join(context_list)
        all_context, split_str_use_list = txt2json(all_context)
        # short_json_context_list = split_context_v2(all_context, split_str_use_list, file_name)
        # json_context_list.extend(short_json_context_list)
    else:
        son_context_list = split_content_recursive(text)
        for son_context_recursive in son_context_list:
            son_context_list = son_context_recursive.split("。")
            son_context_list = [son_context for son_context in son_context_list if len(son_context) > 0]
            json_unit = {
                "sourceId": "",
                "context": son_context_recursive,
                "index": son_context_list,
                # "fileName": file_name
            }
            json_context_list.append(json_unit)
            chunk_list.extend(son_context_list)
    
    return chunk_list


def md5(data):
    return hashlib.md5(data.encode(encoding='UTF-8')).hexdigest()


def main():
    pages_list = get_text()
    
    for pages in pages_list:
        for page in pages:
            text = page['content']
            # chunks = chunking(text)
            if len(text) ==0:
                continue
            
            chunks = file_chunking(text)
            if len(chunks) == 0:
                continue
            
            for idx, chunk in enumerate(chunks):
                # 获取每个chunk的embedding
                embedding = get_embedding(chunk)
                if embedding:
                    es_update = {
                        "id": md5(chunk),
                        "file_id": str(page['file_id']),
                        "page_id": str(page['page_num']),
                        "knowledge_base_id": "",
                        "chunk_index": idx+1,
                        "answer": "",
                        "question": "",
                        "embedding": embedding,
                        "metadata": {"context": chunk},
                        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "app_info": "",
                        "is_expired": False
                    }
                    # ES入库
                    es.index(index=ES_INDEX, body=es_update, id=md5(chunk))
                    # MongoDB入库
                    db[collection_files_pages_chunk].insert_one(es_update)
                    

if __name__ == '__main__':
    main()
            
            
                    