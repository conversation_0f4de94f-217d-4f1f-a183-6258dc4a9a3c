"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7948],{49185:function(e,n,t){t.d(n,{D9:function(){return c},IV:function(){return p},eV:function(){return x},ws:function(){return u}});var r=t(15009),a=t.n(r),s=t(99289),i=t.n(s),o=t(78158);function c(e){return l.apply(this,arguments)}function l(){return(l=i()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/knowledge_base",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function u(e){return d.apply(this,arguments)}function d(){return(d=i()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/my_knowledge_bases",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function p(e){return f.apply(this,arguments)}function f(){return(f=i()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/knowledge_bases/".concat(n),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function x(e){return h.apply(this,arguments)}function h(){return(h=i()(a()().mark((function e(n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,o.N)("/api/knowledge_bases/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},14921:function(e,n,t){t.r(n),t.d(n,{default:function(){return Ue}});var r=t(97857),a=t.n(r),s=t(15009),i=t.n(s),o=t(99289),c=t.n(o),l=t(5574),u=t.n(l),d=t(97131),p=t(88372),f=t(26412),x=t(4393),h=t(67294),m=t(34994),g=t(5966),v=t(90672),y=t(64317),j=t(52688),k=t(71471),b=t(2453),w=t(96074),Z=t(2487),P=t(24444),C=(0,P.kc)((function(e){var n=e.token;return{baseView:{maxWidth:"1000px",margin:"0 auto",backgroundColor:n.colorBgContainer},section:{"> h4":{marginBottom:"24px"}},formSection:{padding:"24px 0",".ant-form-item:last-child":{marginBottom:0}},settingsList:{".ant-list-item":{padding:"16px 0",borderBottom:"1px solid ".concat(n.colorBorderSecondary),"&:last-child":{borderBottom:"none"}},".ant-list-item-meta":{marginBottom:0}},settingTitle:{fontSize:"16px",fontWeight:500,color:n.colorTextHeading},settingDesc:{color:n.colorTextSecondary}}})),S=t(58258),I=t(85893),_=k.Z.Title,T=function(e){var n=C().styles,t=(0,h.useState)([]),r=u()(t,2),s=r[0],o=r[1];console.log(e),(0,h.useEffect)((function(){var e=function(){var e=c()(i()().mark((function e(){var n,t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,S.DY)();case 3:(n=e.sent).success&&(t=n.data.map((function(e){return{label:e.name,value:e.embedding_name,id:e.id}})),o(t.map((function(e){return a()(a()({},e),{},{id:Number(e.id)})})))),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),b.ZP.error("获取向量模型列表失败");case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var l=function(){var n=c()(i()().mark((function n(t){var r,o,c;return i()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,r=s.find((function(e){return e.value===t.embedding_model})),o=a()(a()({},t),{},{embedding_model_id:null==r?void 0:r.id.toString()}),n.next=5,(0,S.CH)(e.id,o);case 5:(c=n.sent).success?b.ZP.success("更新知识库信息成功"):b.ZP.error(c.message||"更新知识库信息失败"),n.next=13;break;case 9:n.prev=9,n.t0=n.catch(0),console.error("更新知识库出错:",n.t0),b.ZP.error("更新知识库信息失败");case 13:case"end":return n.stop()}}),n,null,[[0,9]])})));return function(e){return n.apply(this,arguments)}}();return(0,I.jsx)("div",{className:n.baseView,children:(0,I.jsxs)("div",{className:n.section,children:[(0,I.jsx)(_,{level:4,children:"知识库基础信息"}),(0,I.jsxs)(m.A,{layout:"vertical",onFinish:l,submitter:{searchConfig:{submitText:"更新知识库信息"},render:function(e,n){return n[1]}},initialValues:{name:e.name,description:e.description,embedding_model:e.embedding_model,embedding_model_id:e.embedding_model_id,reRank_model:e.reRank_model,reRank_model_id:e.reRank_model_id,basic_index:e.basic_index,graph_index:e.graph_index,semantic_index:e.semantic_index},hideRequiredMark:!0,children:[(0,I.jsxs)("div",{className:n.formSection,children:[(0,I.jsx)(g.Z,{width:"xl",name:"name",label:"知识库名称",rules:[{required:!0,message:"请输入知识库名称!"}]}),(0,I.jsx)(v.Z,{width:"xl",name:"description",label:"知识库描述",rules:[{required:!0,message:"请输入知识库描述!"}],placeholder:"请输入知识库描述"})]}),(0,I.jsx)(w.Z,{}),(0,I.jsxs)("div",{className:n.formSection,children:[(0,I.jsx)(_,{level:4,children:"基础模型"}),(0,I.jsx)(y.Z,{width:"xl",name:"embedding_model",label:"选择向量模型，知识库构建时会使用，构建后会自动使用，但是不建议修改。",options:s,rules:[{required:!0,message:"请选择向量模型!"}],tooltip:"用于文本向量化的预训练模型",placeholder:"请选择向量模型",fieldProps:{loading:0===s.length,disabled:!0},readonly:!0,disabled:!0})]}),(0,I.jsx)(w.Z,{}),(0,I.jsxs)("div",{className:n.formSection,children:[(0,I.jsx)(_,{level:4,children:"索引设置"}),(0,I.jsx)(Z.Z,{className:n.settingsList,itemLayout:"horizontal",dataSource:[{title:"基础索引",description:"构建基础的文本检索索引，支持关键词匹配和语义相似度检索",fieldName:"basic_index"},{title:"Graph索引",description:"构建知识图谱索引，支持实体关系分析和知识推理",fieldName:"graph_index"},{title:"语义索引",description:"构建语义索引，支持语义相似度检索",fieldName:"semantic_index"}],renderItem:function(e){return(0,I.jsx)(Z.Z.Item,{actions:[(0,I.jsx)(j.Z,{name:e.fieldName,noStyle:!0},e.fieldName)],children:(0,I.jsx)(Z.Z.Item.Meta,{title:(0,I.jsx)("span",{className:n.settingTitle,children:e.title}),description:(0,I.jsx)("span",{className:n.settingDesc,children:e.description})})})}})]})]})]})})},M=t(55102),B=function(e){var n=C().styles,t=e.value,r=e.onChange,a=["",""];return t&&(a=t.split("-")),(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)(M.Z,{className:n.area_code,value:a[0],onChange:function(e){r&&r("".concat(e.target.value,"-").concat(a[1]))}}),(0,I.jsx)(M.Z,{className:n.phone_number,onChange:function(e){r&&r("".concat(a[0],"-").concat(e.target.value))},value:a[1]})]})},R=t(72269),F=function(){var e,n=[{title:"账户密码",description:"其他用户的消息将以站内信的形式通知",actions:[e=(0,I.jsx)(R.Z,{checkedChildren:"开",unCheckedChildren:"关",defaultChecked:!0})]},{title:"系统消息",description:"系统消息将以站内信的形式通知",actions:[e]},{title:"待办任务",description:"待办任务将以站内信的形式通知",actions:[e]}];return(0,I.jsx)(h.Fragment,{children:(0,I.jsx)(Z.Z,{itemLayout:"horizontal",dataSource:n,renderItem:function(e){return(0,I.jsx)(Z.Z.Item,{actions:e.actions,children:(0,I.jsx)(Z.Z.Item.Meta,{title:e.title,description:e.description})})}})})},N={strong:(0,I.jsx)("span",{className:"strong",children:"强"}),medium:(0,I.jsx)("span",{className:"medium",children:"中"}),weak:(0,I.jsx)("span",{className:"weak",children:"弱 Weak"})},L=function(){var e=[{title:"账户密码",description:(0,I.jsxs)(I.Fragment,{children:["当前密码强度：",N.strong]}),actions:[(0,I.jsx)("a",{children:"修改"},"Modify")]},{title:"密保手机",description:"已绑定手机：138****8293",actions:[(0,I.jsx)("a",{children:"修改"},"Modify")]},{title:"密保问题",description:"未设置密保问题，密保问题可有效保护账户安全",actions:[(0,I.jsx)("a",{children:"设置"},"Set")]},{title:"备用邮箱",description:"已绑定邮箱：ant***sign.com",actions:[(0,I.jsx)("a",{children:"修改"},"Modify")]},{title:"MFA 设备",description:"未绑定 MFA 设备，绑定后，可以进行二次确认",actions:[(0,I.jsx)("a",{children:"绑定"},"bind")]}];return(0,I.jsx)(I.Fragment,{children:(0,I.jsx)(Z.Z,{itemLayout:"horizontal",dataSource:e,renderItem:function(e){return(0,I.jsx)(Z.Z.Item,{actions:e.actions,children:(0,I.jsx)(Z.Z.Item.Meta,{title:e.title,description:e.description})})}})})},O=t(9783),E=t.n(O),D=t(38703),z=t(17788),A=t(11550),V=t(33914),q=t(51042),W=t(34041),G=t(83622),H=A.Z.Dragger,K=W.default.Option,J=[{id:"default",name:"默认解析器"},{id:"annual",name:"企业年报解析器"},{id:"law",name:"法律法规解析器"},{id:"manual",name:"使用手册解析器"}],X=function(e){var n=e.knowledgeId,t=(0,h.useState)([]),r=u()(t,2),s=r[0],o=r[1],l=(0,h.useState)([]),d=u()(l,2),p=d[0],f=d[1],x=(0,h.useState)({}),g=u()(x,2),v=g[0],y=g[1],j=(0,h.useState)(!1),k=u()(j,2),w=k[0],Z=k[1],P=(0,h.useState)(0),C=u()(P,2),_=C[0],T=C[1],B=(0,h.useState)([]),F=u()(B,2),N=F[0],L=F[1],O=(0,h.useState)(0),X=u()(O,2),U=X[0],Y=X[1],$=(0,h.useState)(0),Q=u()($,2),ee=Q[0],ne=Q[1],te=n,re=(0,h.useState)(""),ae=u()(re,2),se=ae[0],ie=ae[1],oe=(0,h.useState)("default"),ce=u()(oe,2),le=ce[0],ue=ce[1],de=(0,h.useState)(!1),pe=u()(de,2),fe=pe[0],xe=pe[1],he=(0,h.useState)(!1),me=u()(he,2),ge=me[0],ve=me[1],ye=(0,h.useState)(""),je=u()(ye,2),ke=je[0],be=je[1],we={name:"file",multiple:!0,progress:{strokeColor:{"0%":"#108ee9","100%":"#87d068"},size:3,showInfo:!0},maxCount:1e3,fileList:s,beforeUpload:function(e){return[".pdf",".docx",".csv",".xls",".xlsx",".json",".pptx",".html",".md",".jpg",".png",".wav",".mp3"].some((function(n){return e.name.toLowerCase().endsWith(n)}))?(y((function(n){return a()(a()({},n),{},E()({},e.name,0))})),!1):(b.ZP.error("只支持 PDF、CSV、Excel、JSON、DOCX、PPTX、HTML、Markdown、JPG、PNG、WAV、MP3 格式的文件"),A.Z.LIST_IGNORE)},onChange:function(e){o(e.fileList);var n=e.file.status;"uploading"===n?e.file.percent&&y((function(n){return a()(a()({},n),{},E()({},e.file.name,e.file.percent||0))})):"done"===n?(y((function(n){return a()(a()({},n),{},E()({},e.file.name,100))})),b.ZP.success("".concat(e.file.name," 上传成功"))):"error"===n&&b.ZP.error("".concat(e.file.name," 上传失败")),e.fileList.length>50&&b.ZP.warning("最多只能上传50个文件"),console.log(e.file,e.fileList)},onDrop:function(e){console.log("Dropped files",e.dataTransfer.files)},itemRender:function(e,n){return(0,I.jsxs)("div",{style:{marginBottom:8},children:[e,v[n.name]>0&&v[n.name]<100&&(0,I.jsx)(D.Z,{percent:Math.round(v[n.name]),size:"small",status:"error"===n.status?"exception":"active",style:{marginTop:4}})]})},showUploadList:{showDownloadIcon:!1,showRemoveIcon:!0}},Ze=function(){var e=c()(i()().mark((function e(){var n,t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,S.E)(te,"",[],!0);case 3:n=e.sent,console.log("🚀 ~ fetchCategories ~ response:",n),t=n.data||[],r=t.filter((function(e){return"folder"===e.data_type})),f(r),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(0),b.ZP.error("获取分类失败"),console.error("获取分类失败:",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(){return e.apply(this,arguments)}}();(0,h.useEffect)((function(){Ze()}),[]);var Pe=function(){var e=c()(i()().mark((function e(t,r,s,c){var l,u;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,y((function(e){return a()(a()({},e),{},E()({},t.name,10))})),l=setInterval((function(){y((function(e){return e[t.name]&&e[t.name]<90?a()(a()({},e),{},E()({},t.name,Math.min(e[t.name]+Math.floor(15*Math.random())+1,90))):e}))}),500),e.prev=4,e.next=7,(0,S.bV)(n,r,[t],s,c);case 7:if(u=e.sent,clearInterval(l),!u.detail){e.next=11;break}throw new Error(u.detail);case 11:return y((function(e){return a()(a()({},e),{},E()({},t.name,100))})),o((function(e){return e.filter((function(e){return!(e.originFileObj&&e.originFileObj.name===t.name)}))})),b.ZP.success("".concat(t.name," 上传成功")),Y((function(e){return e+1})),e.abrupt("return",!0);case 18:return e.prev=18,e.t0=e.catch(4),clearInterval(l),b.ZP.error("".concat(t.name," 上传失败: ").concat((null===e.t0||void 0===e.t0?void 0:e.t0.message)||"未知错误")),ne((function(e){return e+1})),e.abrupt("return",!1);case 24:return e.prev=24,T((function(e){return e-1})),e.finish(24);case 27:e.next=33;break;case 29:return e.prev=29,e.t1=e.catch(0),console.error("Upload error:",e.t1),e.abrupt("return",!1);case 33:case"end":return e.stop()}}),e,null,[[0,29],[4,18,24,27]])})));return function(n,t,r,a){return e.apply(this,arguments)}}();(0,h.useEffect)((function(){if(N.length>0&&_<2&&w){var e=N[0],n=N.slice(1);L(n),T((function(e){return e+1})),Pe(e,fe?1:0,se,le)}0===N.length&&0===_&&w&&(Z(!1),0===ee&&U>0?b.ZP.success("所有文件上传成功"):ee>0&&b.ZP.warning("上传完成，成功: ".concat(U," 失败: ").concat(ee)),ie(""),ue("default"),xe(!1),y({}),Y(0),ne(0))}),[N,_,w,fe,se,le,U,ee]);var Ce=function(){var e=c()(i()().mark((function e(){var n,t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,0!==s.length){e.next=4;break}return b.ZP.error("请选择要上传的文件"),e.abrupt("return");case 4:console.log("🚀 ~ onFinish ~ fileList:",s),n=s.map((function(e){return e.originFileObj})),t={},n.forEach((function(e){e&&e.name&&(t[e.name]=0)})),y(t),console.log("开始上传文件，总数:",n.length),Z(!0),T(0),Y(0),ne(0),L(n),e.next=22;break;case 17:e.prev=17,e.t0=e.catch(0),Z(!1),b.ZP.error((null===e.t0||void 0===e.t0?void 0:e.t0.message)||"创建数据集失败"),console.error("创建数据集错误:",e.t0);case 22:case"end":return e.stop()}}),e,null,[[0,17]])})));return function(){return e.apply(this,arguments)}}(),Se=function(){ve(!1),be("")},Ie=function(){var e=c()(i()().mark((function e(){var t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,"add",e.next=4,(0,S.l$)("add",ke,n,"","");case 4:(t=e.sent).detail?b.ZP.error(t.detail):(b.ZP.success("分类创建成功"),Ze(),Se()),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("创建分类失败:",e.t0),b.ZP.error("创建分类失败，请重试");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(){return e.apply(this,arguments)}}();return(0,I.jsx)("div",{children:(0,I.jsxs)(m.A,{style:{margin:"auto",marginTop:8,marginBottom:24,maxWidth:600,paddingBottom:24},name:"basic",layout:"vertical",initialValues:{name:"新建数据集",description:"这是一个新的数据集，用于..."},onFinish:Ce,submitter:{searchConfig:{submitText:"上传"},submitButtonProps:{loading:w,disabled:w||0===s.length}},children:[(0,I.jsxs)(H,a()(a()({},we),{},{style:{margin:"auto",marginTop:8,marginBottom:24,maxWidth:600,paddingBottom:24},children:[(0,I.jsx)("p",{className:"ant-upload-drag-icon",children:(0,I.jsx)(V.Z,{})}),(0,I.jsx)("p",{className:"ant-upload-text",children:"点击或拖拽文件到此区域上传"}),(0,I.jsx)("p",{className:"ant-upload-hint",children:"支持单个或批量上传。严格禁止上传公司数据或其他禁止的文件。支持数据格式：PDF、CSV、Excel、JSON、DOCX、PPTX、HTML、Markdown、JPG、PNG、WAV、MP3"})]})),(0,I.jsxs)(m.A.Group,{style:{display:"flex",gap:32},labelCol:{span:8},wrapperCol:{span:16},children:[(0,I.jsx)(m.A.Item,{label:"分类",children:(0,I.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,I.jsx)(W.default,{defaultValue:"",placeholder:"请选择分类",value:se,style:{width:200,marginRight:8},onChange:function(e){console.log("选择的分类: ".concat(e)),ie(e)},children:p.map((function(e){return(0,I.jsx)(K,{value:e.id,children:e.name},e.id)}))}),(0,I.jsx)(G.ZP,{type:"primary",icon:(0,I.jsx)(q.Z,{}),onClick:function(){ve(!0)}})]})}),(0,I.jsx)(m.A.Item,{label:"解析器类型",children:(0,I.jsx)(W.default,{value:le,defaultValue:"default",style:{width:150},onChange:function(e){console.log("选择的解析器类型: ".concat(e)),ue(e)},children:J.map((function(e){return(0,I.jsx)(K,{value:e.id,children:e.name},e.id)}))})}),(0,I.jsx)(m.A.Item,{label:"启用视觉识别",children:(0,I.jsx)(R.Z,{checked:fe,onChange:function(e){console.log("OCR 开关: ".concat(e)),xe(e)}})})]}),(0,I.jsxs)(z.Z,{title:"创建分类",visible:ge,onCancel:Se,footer:null,style:{marginTop:80},width:400,children:[(0,I.jsx)(M.Z,{value:ke,placeholder:"请输入分类名称",onChange:function(e){return be(e.target.value)}}),(0,I.jsx)(G.ZP,{type:"primary",onClick:Ie,style:{marginTop:"10px"},children:"确定"})]})]})})},U=t(19632),Y=t.n(U),$=t(50136),Q=t(85418),ee=t(96974),ne=(A.Z.Dragger,function(e){e.knowledgeId;var n=h.useState([]),t=u()(n,2),r=(t[0],t[1],(0,h.useState)(null)),a=u()(r,2),s=(a[0],a[1]),o=(0,h.useState)(!1),l=u()(o,2),d=l[0],p=l[1],f=(0,h.useState)(!1),x=u()(f,2),m=x[0],g=x[1],v=(0,h.useState)(null),y=u()(v,2),j=y[0],k=y[1],w=(0,h.useState)(""),Z=u()(w,2),P=Z[0],C=Z[1],_=(0,h.useState)([]),T=u()(_,2),B=T[0],R=T[1],F=(0,h.useState)(!1),N=u()(F,2),L=N[0],O=N[1],E=(0,h.useState)(""),D=u()(E,2),A=D[0],V=D[1],q=(0,h.useState)(""),W=u()(q,2),G=W[0],H=W[1],K=(0,ee.TH)(),J=new URLSearchParams(K.search).get("id");console.log("🚀 ~ FileOperations ~ id:",J),(0,h.useEffect)((function(){J&&H(J)}),[J]),(0,h.useEffect)((function(){var e=function(){var e=c()(i()().mark((function e(){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,S.Bp)(G);case 3:n=e.sent,console.log("🚀 ~ fetchTags ~ response:",n),n&&n.data&&Array.isArray(n.data.tags)?R(n.data.tags):(console.warn("未找到标签数据，使用空数组"),R([])),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("获取标签时出错:",e.t0),R([]);case 12:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(){return e.apply(this,arguments)}}();e()}),[G]);var X=function(){var e=c()(i()().mark((function e(){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(""!==P.trim()){e.next=3;break}return b.ZP.error("标签名不能为空"),e.abrupt("return");case 3:return n=B.map((function(e){return e===j?P:e})),e.next=6,(0,S.IV)(G,n);case 6:R(n),p(!1),b.ZP.success("标签重命名成功");case 9:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),U=function(){var e=c()(i()().mark((function e(){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=B.filter((function(e){return e!==j})),e.next=3,(0,S.IV)(G,n);case 3:R(n),g(!1),b.ZP.success("标签删除成功");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ne=function(){p(!1),g(!1)},te=function(){var e=c()(i()().mark((function e(){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!A){e.next=7;break}return n=[].concat(Y()(B),[A]),e.next=4,(0,S.IV)(G,n);case 4:R(n),V(""),O(!1);case 7:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),re=function(e){return(0,I.jsxs)($.Z,{children:[(0,I.jsx)($.Z.Item,{onClick:function(){return function(e){k(e),C(e),p(!0),s(null)}(e)},children:"重命名"},"rename"),(0,I.jsx)($.Z.Item,{onClick:function(){return function(e){k(e),g(!0),s(null)}(e)},children:"删除"},"delete")]})};return(0,I.jsxs)("div",{style:{padding:"16px",borderRadius:"8px"},children:[(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[(0,I.jsx)("h2",{style:{fontSize:"24px",fontWeight:"bold",color:"#333"},children:"标签管理"}),(0,I.jsx)("button",{style:{padding:"8px 12px",border:"none",borderRadius:"4px",backgroundColor:"#108ee9",color:"#fff",cursor:"pointer"},onClick:function(){return O(!0)},children:"创建标签"})]}),(0,I.jsx)("div",{style:{display:"flex",flexWrap:"wrap"},children:B.map((function(e,n){return(0,I.jsx)("div",{style:{position:"relative",display:"inline-block",marginRight:"16px",padding:"8px 12px",border:"1px solid #ccc",borderRadius:"4px",backgroundColor:"#fff",cursor:"pointer"},children:(0,I.jsx)(Q.Z,{overlay:re(e),trigger:["click"],children:(0,I.jsx)("span",{children:e})})},n)}))}),(0,I.jsx)(z.Z,{title:"重命名标签",visible:d,onOk:X,onCancel:ne,children:(0,I.jsx)(M.Z,{value:P,onChange:function(e){return C(e.target.value)}})}),(0,I.jsx)(z.Z,{title:"确认删除",visible:m,onOk:U,onCancel:ne,children:(0,I.jsxs)("p",{children:['您确定要删除标签 "',j,'" 吗？']})}),(0,I.jsx)(z.Z,{title:"创建标签",visible:L,onOk:te,onCancel:function(){return O(!1)},children:(0,I.jsx)(M.Z,{value:A,onChange:function(e){return V(e.target.value)},placeholder:"请输入标签名称"})})]})}),te=t(42075),re=(A.Z.Dragger,function(e){e.knowledgeId;var n=(0,h.useState)(!1),t=u()(n,2),r=(t[0],t[1]);return(0,I.jsxs)("div",{style:{padding:"16px",borderRadius:"8px"},children:[(0,I.jsx)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:(0,I.jsx)("h2",{style:{fontSize:"24px",fontWeight:"bold",color:"#333"},children:"权限控制"})}),(0,I.jsxs)("div",{children:[(0,I.jsx)(Z.Z,{itemLayout:"horizontal",dataSource:[{title:"权限控制",description:"开启后将允许其他用户访问此知识库",fieldName:"permission"}],renderItem:function(e){return(0,I.jsx)(Z.Z.Item,{actions:[(0,I.jsx)(j.Z,{name:e.fieldName,noStyle:!0},e.fieldName)],children:(0,I.jsx)(Z.Z.Item.Meta,{title:(0,I.jsx)("span",{children:e.title}),description:(0,I.jsx)("span",{children:e.description})})})}}),(0,I.jsxs)("div",{style:{marginTop:"16px"},children:[(0,I.jsx)("div",{style:{marginBottom:"8px"},children:(0,I.jsxs)(te.Z,{children:[(0,I.jsx)("span",{children:"选择用户"}),(0,I.jsx)(q.Z,{onClick:function(){return r(!0)},style:{cursor:"pointer"}})]})}),(0,I.jsx)(y.Z,{name:"knowledge_bases",rules:[{required:!0,message:"请选择至少一个用户"}],width:"xl",mode:"multiple",options:[]})]}),(0,I.jsxs)("div",{style:{marginTop:"16px"},children:[(0,I.jsx)("div",{style:{marginBottom:"8px"},children:(0,I.jsxs)(te.Z,{children:[(0,I.jsx)("span",{children:"选择组"}),(0,I.jsx)(q.Z,{onClick:function(){return r(!0)},style:{cursor:"pointer"}})]})}),(0,I.jsx)(y.Z,{name:"knowledge_bases",rules:[{required:!0,message:"请选择至少一个组"}],width:"xl",mode:"multiple",options:[]})]})]})]})}),ae=t(78045),se=t(86125),ie=t(74330),oe=t(40411),ce=t(83062),le=t(66309),ue=t(32983),de=t(11941),pe=t(43471),fe=t(40110),xe=t(13923),he=t(29965),me=t(17044),ge=t(58638),ve=t(15360),ye=t(85175),je=t(55287),ke=t(93162),be=k.Z.Text,we=k.Z.Title,Ze=k.Z.Paragraph,Pe=function(e){return e>=.8?"green":e>=.6?"cyan":e>=.4?"blue":e>=.2?"orange":"red"},Ce=function(){var e=(0,h.useState)(!1),n=u()(e,2),t=n[0],r=n[1],s=(0,h.useState)("1"),o=u()(s,2),l=o[0],d=o[1],p=(0,h.useState)(""),f=u()(p,2),m=f[0],g=f[1],v=(0,h.useState)([]),y=u()(v,2),j=y[0],k=y[1],P=(0,h.useState)(!1),C=u()(P,2),_=C[0],T=C[1],B=(0,h.useState)([]),R=u()(B,2),F=R[0],N=R[1],L=(0,h.useState)("desc"),O=u()(L,2),E=O[0],D=O[1],A=(0,h.useState)({searchMode:"semantic",maxTokens:1e3,minRelevance:.3}),V=u()(A,2),q=V[0],W=V[1],H=(0,h.useState)(1e3),K=u()(H,2),J=K[0],X=K[1],U=(0,h.useState)(.3),$=u()(U,2),Q=$[0],ne=$[1],re=(0,h.useState)(!1),Ce=u()(re,2),Se=Ce[0],Ie=Ce[1],_e=(0,h.useState)(null),Te=u()(_e,2),Me=Te[0],Be=Te[1],Re=(0,h.useRef)(""),Fe=(0,ee.TH)(),Ne=new URLSearchParams(Fe.search).get("id")||"",Le=function(){var e=c()(i()().mark((function e(){var n,t,r,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(m.trim()){e.next=3;break}return b.ZP.error("请输入搜索内容"),e.abrupt("return");case 3:if(Ne){e.next=6;break}return b.ZP.error("知识库ID不能为空"),e.abrupt("return");case 6:return n={knowledge_base_id:Ne,query:m,search_mode:q.searchMode,max_tokens:q.maxTokens,min_relevance:q.minRelevance},console.log("开始搜索，参数:",n),Re.current=m,T(!0),e.prev=10,e.next=13,(0,S.SS)(n);case 13:t=e.sent,console.log("搜索响应:",t),t.success?(k(t.data||[]),r=new Date,a=r.toLocaleTimeString(),N((function(e){return[{query:m,time:a}].concat(Y()(e.slice(0,9)))})),0===t.data.length&&b.ZP.info("未找到相关结果")):b.ZP.error(t.message||"搜索失败"),e.next=23;break;case 18:e.prev=18,e.t0=e.catch(10),console.error("搜索失败:",e.t0),e.t0.response?(console.error("错误响应状态:",e.t0.response.status),console.error("错误响应数据:",e.t0.response.data)):e.t0.request&&console.error("未收到响应，请求信息:",e.t0.request),b.ZP.error("搜索请求失败，请稍后重试");case 23:return e.prev=23,T(!1),e.finish(23);case 26:case"end":return e.stop()}}),e,null,[[10,18,23,26]])})));return function(){return e.apply(this,arguments)}}(),Oe=function(e){navigator.clipboard.writeText(e).then((function(){return b.ZP.success("复制成功")})).catch((function(){return b.ZP.error("复制失败")}))},Ee=[{key:"1",label:(0,I.jsx)("span",{style:{display:"flex",alignItems:"center",gap:"4px"},children:"搜索模式"}),children:(0,I.jsx)("div",{style:{padding:"16px 0"},children:(0,I.jsxs)(ae.ZP.Group,{defaultValue:q.searchMode,value:q.searchMode,onChange:function(e){return W(a()(a()({},q),{},{searchMode:e.target.value}))},children:[(0,I.jsx)("div",{style:{padding:"12px",borderRadius:"8px",background:"semantic"===q.searchMode?"#f5f3ff":"transparent",marginBottom:"12px",cursor:"pointer"},children:(0,I.jsx)(ae.ZP,{value:"semantic",children:(0,I.jsxs)("div",{children:[(0,I.jsx)("div",{children:"语义检索"}),(0,I.jsx)("div",{style:{fontSize:"12px",color:"#666",marginTop:"4px"},children:"使用向量进行文本相关性查询"})]})})}),(0,I.jsx)("div",{style:{padding:"12px",cursor:"pointer",background:"full"===q.searchMode?"#f5f3ff":"transparent",borderRadius:"8px",marginBottom:"12px"},children:(0,I.jsx)(ae.ZP,{value:"full",children:(0,I.jsxs)("div",{children:[(0,I.jsx)("div",{children:"全文检索"}),(0,I.jsx)("div",{style:{fontSize:"12px",color:"#666",marginTop:"4px"},children:"使用传统的全文检索，适合查找一些关键词和主语语境特殊的数据"})]})})}),(0,I.jsx)("div",{style:{padding:"12px",cursor:"pointer",background:"mix"===q.searchMode?"#f5f3ff":"transparent",borderRadius:"8px",marginBottom:"12px"},children:(0,I.jsx)(ae.ZP,{value:"mix",children:(0,I.jsxs)("div",{children:[(0,I.jsx)("div",{children:"混合检索"}),(0,I.jsx)("div",{style:{fontSize:"12px",color:"#666",marginTop:"4px"},children:"使用向量检索与全文检索的综合结果返回，使用RRF算法进行排序。"})]})})}),(0,I.jsx)("div",{style:{borderBottom:"1px solid #e6e6e6",margin:"10px 0"}}),(0,I.jsx)("div",{style:{padding:"12px",cursor:"pointer",background:"reRank"===q.searchMode?"#f5f3ff":"transparent",borderRadius:"8px"},children:(0,I.jsx)(ae.ZP,{value:"reRank",children:(0,I.jsxs)("div",{children:[(0,I.jsx)("div",{children:"结果重排"}),(0,I.jsx)("div",{style:{fontSize:"12px",color:"#666",marginTop:"4px"},children:"使用重排模型来进行二次排序，可增强综合排名"})]})})})]})})},{key:"2",label:(0,I.jsx)("span",{style:{display:"flex",alignItems:"center",gap:"4px"},children:"搜索过滤"}),children:(0,I.jsxs)("div",{style:{padding:"16px 0"},children:[(0,I.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,I.jsxs)("div",{style:{marginBottom:"8px"},children:["引用上限 ",(0,I.jsx)("span",{style:{cursor:"help"},title:"控制检索时返回的最大引用数量",children:"ⓘ"})]}),(0,I.jsx)(se.Z,{min:100,max:2e4,defaultValue:J,value:J,onChange:function(e){return X(e)},style:{width:"100%"}}),(0,I.jsx)("div",{style:{textAlign:"right"},children:J})]}),(0,I.jsxs)("div",{children:[(0,I.jsxs)("div",{style:{marginBottom:"8px"},children:["最低相关度 ",(0,I.jsx)("span",{style:{cursor:"help"},title:"设置检索结果的最低相关度阈值，低于此值的结果将被过滤",children:"ⓘ"})]}),(0,I.jsx)(se.Z,{min:0,max:1,step:.01,defaultValue:Q,value:Q,onChange:function(e){return ne(e)},style:{width:"100%"}}),(0,I.jsx)("div",{style:{textAlign:"right"},children:Q.toFixed(2)})]})]})}];return(0,I.jsxs)("div",{style:{padding:"24px",display:"flex",gap:"20px"},children:[(0,I.jsxs)("div",{style:{flex:"1",border:"1px solid #e6e6e6",borderRadius:"8px",padding:"20px",backgroundColor:"white"},children:[(0,I.jsxs)("div",{style:{display:"flex",gap:"12px",marginBottom:"16px",justifyContent:"space-between"},children:[(0,I.jsx)(G.ZP,{icon:(0,I.jsx)(pe.Z,{}),onClick:function(){g(""),k([])},style:{display:"flex",alignItems:"center"},children:"重置"}),(0,I.jsx)(G.ZP,{icon:(0,I.jsx)(fe.Z,{}),style:{color:"#1677ff",border:"1px solid #1677ff",display:"flex",alignItems:"center",gap:"4px"},onClick:function(){return r(!0)},children:"semantic"===q.searchMode?"语义检索":"full"===q.searchMode?"全文检索":"mix"===q.searchMode?"混合检索":"结果重排"})]}),(0,I.jsx)(M.Z.TextArea,{placeholder:"请输入搜索内容",value:m,onChange:function(e){return g(e.target.value)},style:{height:"200px",marginBottom:"16px",resize:"none"},onPressEnter:function(e){(e.ctrlKey||e.metaKey)&&Le()}}),(0,I.jsx)("div",{style:{display:"flex",justifyContent:"flex-end"},children:(0,I.jsx)(G.ZP,{type:"primary",style:{backgroundColor:"#6B4EFF",borderRadius:"4px"},onClick:Le,loading:_,children:"测试"})}),(0,I.jsxs)("div",{children:[(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginTop:"20px",marginBottom:"10px"},children:[(0,I.jsxs)("h3",{style:{display:"flex",alignItems:"center",gap:"8px",fontSize:"16px",fontWeight:"normal",margin:0},children:[(0,I.jsx)(xe.Z,{}),"测试历史"]}),F.length>0&&(0,I.jsx)(G.ZP,{type:"text",size:"small",onClick:function(){z.Z.confirm({title:"确认清空",content:"确定要清空所有搜索历史吗？",onOk:function(){N([]),b.ZP.success("搜索历史已清空")}})},danger:!0,children:"清空"})]}),(0,I.jsx)(Z.Z,{size:"small",locale:{emptyText:"暂无搜索历史"},dataSource:F,renderItem:function(e){return(0,I.jsx)(Z.Z.Item,{style:{cursor:"pointer",padding:"4px 0"},onClick:function(){return n=e.query,void g(n);var n},children:(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",width:"100%"},children:[(0,I.jsx)(be,{ellipsis:!0,style:{maxWidth:"70%"},children:e.query}),(0,I.jsx)(be,{type:"secondary",children:e.time})]})})}})]})]}),(0,I.jsx)("div",{style:{flex:"1",border:"1px solid #e6e6e6",borderRadius:"8px",padding:"20px",backgroundColor:"white",overflowY:"auto",maxHeight:"calc(100vh - 150px)"},children:_?(0,I.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,I.jsx)(ie.Z,{size:"large",tip:"搜索中..."})}):j.length>0?(0,I.jsxs)("div",{children:[(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[(0,I.jsxs)(we,{level:4,style:{margin:0},children:["搜索结果",(0,I.jsx)(oe.Z,{count:j.length,style:{backgroundColor:"#6B4EFF",marginLeft:"8px"}})]}),(0,I.jsxs)(te.Z,{children:[(0,I.jsx)(ce.Z,{title:"desc"===E?"当前: 相似度降序":"当前: 相似度升序",children:(0,I.jsx)(G.ZP,{icon:"desc"===E?(0,I.jsx)(he.Z,{}):(0,I.jsx)(me.Z,{}),onClick:function(){var e="desc"===E?"asc":"desc";D(e);var n=Y()(j).sort((function(n,t){return"desc"===e?t.similarity-n.similarity:n.similarity-t.similarity}));k(n)}})}),(0,I.jsx)(ce.Z,{title:"导出结果",children:(0,I.jsx)(G.ZP,{icon:(0,I.jsx)(ge.Z,{}),onClick:function(){if(0!==j.length)try{var e="搜索查询: ".concat(Re.current,"\n");e+="搜索时间: ".concat((new Date).toLocaleString(),"\n"),e+="搜索模式: ".concat(q.searchMode,"\n"),e+="结果数量: ".concat(j.length,"\n\n"),j.forEach((function(n,t){e+="--- 结果 ".concat(t+1," ---\n"),e+="相似度: ".concat((100*n.similarity).toFixed(2),"%\n"),n.file_name&&(e+="来源: ".concat(n.file_name,"\n")),n.question&&(e+="问题: ".concat(n.question,"\n")),e+="内容: ".concat(n.answer,"\n"),n.created_at&&(e+="创建时间: ".concat(n.created_at,"\n")),e+="\n"}));var n=new Blob([e],{type:"text/plain;charset=utf-8"});(0,ke.saveAs)(n,"搜索结果_".concat((new Date).toISOString().slice(0,10),".txt")),b.ZP.success("导出成功")}catch(e){console.error("导出失败:",e),b.ZP.error("导出失败")}else b.ZP.warning("没有可导出的结果")}})})]})]}),j.map((function(e,n){return(0,I.jsxs)(x.Z,{style:{marginBottom:"16px"},bodyStyle:{padding:"16px"},hoverable:!0,children:[(0,I.jsxs)("div",{style:{marginBottom:"8px",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,I.jsxs)(le.Z,{color:Pe(e.similarity),children:["相似度: ",(100*e.similarity).toFixed(2),"%"]}),e.file_name&&(0,I.jsxs)(be,{type:"secondary",style:{fontSize:"12px"},children:[(0,I.jsx)(ve.Z,{style:{marginRight:"4px"}}),e.file_name]})]}),e.question&&(0,I.jsxs)("div",{style:{marginBottom:"8px"},children:[(0,I.jsx)(be,{strong:!0,children:"问题: "}),(0,I.jsx)(Ze,{ellipsis:{rows:2,expandable:!0,symbol:"展开"},children:e.question})]}),(0,I.jsxs)("div",{children:[(0,I.jsx)(be,{strong:!0,children:"内容: "}),(0,I.jsx)(Ze,{ellipsis:{rows:3,expandable:!0,symbol:"展开"},children:e.answer})]}),(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",marginTop:"8px",alignItems:"center"},children:[e.created_at&&(0,I.jsxs)(be,{type:"secondary",style:{fontSize:"12px"},children:["创建时间: ",e.created_at]}),(0,I.jsxs)(te.Z,{children:[(0,I.jsx)(G.ZP,{type:"text",size:"small",icon:(0,I.jsx)(ye.Z,{}),onClick:function(){return Oe(e.answer)},children:"复制"}),(0,I.jsx)(G.ZP,{type:"primary",size:"small",icon:(0,I.jsx)(je.Z,{}),onClick:function(){return function(e){Be(e),Ie(!0)}(e)},style:{backgroundColor:"#6B4EFF"},children:"详情"})]})]})]},n)}))]}):(0,I.jsx)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0",color:"#666",height:"100%"},children:(0,I.jsx)(ue.Z,{description:"测试结果将在这里展示",image:ue.Z.PRESENTED_IMAGE_SIMPLE})})}),(0,I.jsx)(z.Z,{title:(0,I.jsx)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:"知识库搜索配置"}),open:t,onCancel:function(){return r(!1)},footer:[(0,I.jsx)(G.ZP,{onClick:function(){return r(!1)},children:"关闭"},"cancel"),(0,I.jsx)(G.ZP,{type:"primary",style:{backgroundColor:"#6B4EFF"},onClick:function(){W({searchMode:q.searchMode,maxTokens:J,minRelevance:Q}),r(!1)},children:"完成"},"submit")],width:500,children:(0,I.jsx)(de.Z,{activeKey:l,onChange:d,items:Ee,style:{marginTop:"16px"}})}),(0,I.jsx)(z.Z,{title:"结果详情",open:Se,onCancel:function(){return Ie(!1)},footer:[(0,I.jsx)(G.ZP,{onClick:function(){return Me&&Oe(Me.answer)},children:"复制内容"},"copy"),(0,I.jsx)(G.ZP,{type:"primary",onClick:function(){return Ie(!1)},children:"关闭"},"close")],width:700,children:Me&&(0,I.jsxs)("div",{children:[(0,I.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,I.jsx)(we,{level:5,children:"基本信息"}),(0,I.jsx)(w.Z,{style:{margin:"8px 0 16px"}}),(0,I.jsxs)("div",{style:{display:"flex",flexWrap:"wrap",gap:"12px"},children:[(0,I.jsxs)("div",{children:[(0,I.jsx)(be,{type:"secondary",children:"相似度:"}),(0,I.jsxs)(le.Z,{color:Pe(Me.similarity),style:{marginLeft:"8px"},children:[(100*Me.similarity).toFixed(2),"%"]})]}),Me.file_name&&(0,I.jsxs)("div",{children:[(0,I.jsx)(be,{type:"secondary",children:"来源文件:"}),(0,I.jsx)(be,{style:{marginLeft:"8px"},children:Me.file_name})]}),Me.created_at&&(0,I.jsxs)("div",{children:[(0,I.jsx)(be,{type:"secondary",children:"创建时间:"}),(0,I.jsx)(be,{style:{marginLeft:"8px"},children:Me.created_at})]})]})]}),Me.question&&(0,I.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,I.jsx)(we,{level:5,children:"问题"}),(0,I.jsx)(w.Z,{style:{margin:"8px 0 16px"}}),(0,I.jsx)(Ze,{children:Me.question})]}),(0,I.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,I.jsx)(we,{level:5,children:"内容"}),(0,I.jsx)(w.Z,{style:{margin:"8px 0 16px"}}),(0,I.jsx)(Ze,{children:Me.answer})]}),(0,I.jsxs)("div",{children:[(0,I.jsx)(we,{level:5,children:"技术信息"}),(0,I.jsx)(w.Z,{style:{margin:"8px 0 16px"}}),(0,I.jsxs)(be,{code:!0,children:["知识库ID: ",Me.knowledge_base_id]}),(0,I.jsx)("br",{}),(0,I.jsxs)(be,{code:!0,children:["分块ID: ",Me.chunk_id]}),(0,I.jsx)("br",{}),(0,I.jsxs)(be,{code:!0,children:["文件ID: ",Me.file_id]})]})]})})]})},Se=t(35183),Ie=t(3159),_e=t(71541),Te=t(78537),Me=t(31825),Be=t(6228);Se.D([_e.N,Te.N,Me.N,Be.N]);var Re=function(){var e=(0,h.useRef)(null),n=(0,h.useRef)(null),t=(0,h.useState)(window.innerHeight),r=u()(t,1)[0],a=(0,h.useState)(!1),s=u()(a,2),o=s[0],l=s[1],d=(0,h.useState)({knowledge_base_id:"",nodes:[],edges:[]}),p=u()(d,2),f=p[0],x=p[1],m=(0,ee.TH)(),g=new URLSearchParams(m.search).get("id")||"default";return(0,h.useEffect)((function(){var e=function(){var e=c()(i()().mark((function e(){var n,t,r,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(g&&"default"!==g){e.next=2;break}return e.abrupt("return");case 2:return l(!0),e.prev=3,console.log("🚀 检查知识库图索引状态..."),e.next=7,(0,S.SJ)(g);case 7:if(n=e.sent,console.log("🚀 知识库信息:",n),n&&n.data.graph_index){e.next=13;break}return console.log("🚀 知识库未开启图索引功能"),x({knowledge_base_id:g,nodes:[],edges:[],error_message:"该数据库并未开启Graph图索引，如需支持图谱，请先开启Graph图索引功能，并重新上传文件"}),e.abrupt("return");case 13:return console.log("🚀 知识库已开启图索引功能，开始获取图谱数据..."),e.next=16,(0,S.$V)(g,{max_depth:2,limit:50,format:"echarts"});case 16:(t=e.sent)&&t.nodes&&t.nodes.length>0?(console.log("🚀 API返回的图谱数据:",t),console.log("🚀 节点数据:",t.nodes),console.log("🚀 边数据:",t.edges),r=t.links||t.edges||[],console.log("🚀 连接数据:",r),console.log("🚀 连接数据长度:",r.length),a={knowledge_base_id:t.knowledge_base_id,nodes:t.nodes,edges:r},console.log("🚀 处理后的数据:",a),x(a)):(console.log("🚀 API返回空数据，显示暂无数据状态"),x({knowledge_base_id:g,nodes:[],edges:[]})),e.next=25;break;case 20:e.prev=20,e.t0=e.catch(3),console.error("获取知识图谱失败:",e.t0),x({knowledge_base_id:g,nodes:[],edges:[]}),b.ZP.error("获取图谱数据失败");case 25:return e.prev=25,l(!1),e.finish(25);case 28:case"end":return e.stop()}}),e,null,[[3,20,25,28]])})));return function(){return e.apply(this,arguments)}}();e()}),[g]),(0,h.useEffect)((function(){if(e.current){var t=n.current;t||(t=Ie.S1(e.current),n.current=t);var r=[{name:"概念",itemStyle:{color:"#d62728"}},{name:"机构",itemStyle:{color:"#ff7f0e"}},{name:"人员",itemStyle:{color:"#2ca02c"}},{name:"地点",itemStyle:{color:"#1f77b4"}},{name:"事件",itemStyle:{color:"#9467bd"}},{name:"未知",itemStyle:{color:"#999999"}}],a=f.nodes.map((function(e){return{id:e.id,name:e.name.length>15?e.name.substring(0,15)+"...":e.name,symbolSize:e.symbolSize,category:e.category,value:e.data.entity_type,label:{show:e.label.show,formatter:"{b}"},itemStyle:e.itemStyle,tooltip:{formatter:e.tooltip.formatter}}})),s=new Set(f.nodes.map((function(e){return e.id})));console.log("🚀 所有节点ID:",Array.from(s));var i=f.edges||[];console.log("🚀 原始边数据:",i);var o=i.map((function(e){return console.log("🚀 处理边: ".concat(e.source," -> ").concat(e.target)),console.log("🚀 source存在: ".concat(s.has(e.source),", target存在: ").concat(s.has(e.target))),{source:e.source,target:e.target,label:e.label?{show:!0,formatter:e.label}:void 0,lineStyle:{color:"#666",width:2}}}));console.log("🚀 转换后的ECharts连接数据:",o);var c={tooltip:{trigger:"item",formatter:function(e){if("node"===e.dataType){var n=f.nodes.find((function(n){return n.id===e.data.id}));return n?n.tooltip.formatter:'<div style="font-weight:bold">'.concat(e.name,"</div>类型: ").concat(e.value)}return e.name||""}},legend:[{data:r.map((function(e){return e.name})),orient:"vertical",right:10,top:20}],series:[{name:"知识图谱",type:"graph",layout:"force",nodes:a,links:o,categories:r,roam:!0,focusNodeAdjacency:!0,label:{show:!0,position:"right",formatter:"{b}",fontSize:12},edgeLabel:{show:!0,fontSize:10,formatter:"{c}"},force:{repulsion:200,edgeLength:[100,200],gravity:.1,layoutAnimation:!0},draggable:!0,emphasis:{focus:"adjacency",lineStyle:{width:3,color:"#409EFF"},label:{show:!0}},lineStyle:{color:"source",curveness:.1,width:2}}]};t.setOption(c,!0);var l=function(){var e;null===(e=t)||void 0===e||e.resize()};return window.addEventListener("resize",l),function(){var e;window.removeEventListener("resize",l),null===(e=t)||void 0===e||e.dispose(),n.current=null}}}),[f]),(0,I.jsx)("div",{style:{width:"100%",height:r-120,padding:"20px",backgroundColor:"#fff",borderRadius:"12px"},children:o?(0,I.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,I.jsx)(ie.Z,{size:"large",tip:"加载中..."})}):f.nodes.length>0?(0,I.jsx)("div",{ref:e,style:{width:"100%",height:"100%",minHeight:"450px",borderRadius:"12px"}}):(0,I.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,I.jsx)(ue.Z,{description:f.error_message||"暂无图谱数据",style:{fontSize:"14px",color:f.error_message?"#ff4d4f":void 0}})})})},Fe=function(){return(0,I.jsx)("div",{style:{padding:"24px",display:"flex",gap:"20px"},children:(0,I.jsxs)("div",{style:{flex:"1",borderRadius:"8px",backgroundColor:"white"},children:[(0,I.jsx)(Z.Z,{itemLayout:"horizontal",dataSource:[{title:"API服务",description:"API服务",fieldName:"apiService"}],renderItem:function(e){return(0,I.jsx)(Z.Z.Item,{actions:[(0,I.jsx)(j.Z,{name:e.fieldName,noStyle:!0},e.fieldName)],children:(0,I.jsx)(Z.Z.Item.Meta,{title:(0,I.jsx)("span",{children:e.title}),description:(0,I.jsx)("span",{children:e.description})})})}}),(0,I.jsx)(M.Z.TextArea,{placeholder:"请输入文本",style:{height:"200px",marginBottom:"16px",resize:"none"}}),(0,I.jsx)("div",{style:{display:"flex",justifyContent:"flex-end"},children:(0,I.jsx)(G.ZP,{type:"primary",style:{backgroundColor:"#6B4EFF",borderRadius:"4px"},children:"测试"})})]})})},Ne=t(12453),Le=t(84567),Oe=t(67839),Ee=function(e){var n=e.record,t=e.onDelete,r=e.onTransfer,s=e.onMoreClick,o=e.currentItemId,l=e.setTableData,d=e.fetchKnowledgeFolderTree,p=e.actionRef,f=(0,h.useState)(""),x=u()(f,2),m=x[0],g=x[1],v=(0,h.useState)(!1),y=u()(v,2),j=y[0],k=y[1],w=(0,h.useState)(!1),P=u()(w,2),C=P[0],_=P[1],T=(0,h.useState)(""),B=u()(T,2),R=B[0],F=B[1],N=(0,h.useState)(new Set),L=u()(N,2),O=L[0],E=L[1],D=(0,h.useState)(""),V=u()(D,2),q=V[0],W=V[1],H=(0,h.useState)([]),K=u()(H,2),J=K[0],X=K[1],U=(0,h.useState)(null),ne=u()(U,2),te=ne[0],re=ne[1],ae=(0,ee.TH)(),se=new URLSearchParams(ae.search).get("id");console.log("🚀 ~ FileOperations ~ id:",se);var ie=(0,h.useState)(n.forbidden),oe=u()(ie,2),ce=oe[0],le=oe[1];(0,h.useEffect)((function(){se&&g(se)}),[se]),(0,h.useEffect)((function(){le(n.forbidden)}),[n.forbidden]);var ue=function(){var e=c()(i()().mark((function e(s){var u,f,x,h,g,v,y,j,w,Z,P;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(u=o){e.next=4;break}return b.ZP.error("请选择一个文件"),e.abrupt("return");case 4:if("unpublish"!==s.key){e.next=29;break}return e.prev=5,e.next=8,(0,S.au)(u,!0);case 8:if(f=e.sent,console.log("下架成功:",f),!f.success){e.next=20;break}return b.ZP.success(f.message),le(!0),e.next=15,d(!0);case 15:h=e.sent,l(h),null==p||null===(x=p.current)||void 0===x||x.reload(),e.next=21;break;case 20:b.ZP.error("下架失败");case 21:e.next=27;break;case 23:e.prev=23,e.t0=e.catch(5),console.error("下架失败:",e.t0),b.ZP.error("下架失败，请重试");case 27:e.next=82;break;case 29:if("publish"!==s.key){e.next=54;break}return e.prev=30,e.next=33,(0,S.au)(u,!1);case 33:if(g=e.sent,console.log("上架成功:",g),!g.success){e.next=45;break}return b.ZP.success(g.message),le(!1),e.next=40,d(!0);case 40:y=e.sent,l(y),null==p||null===(v=p.current)||void 0===v||v.reload(),e.next=46;break;case 45:b.ZP.error(g.message);case 46:e.next=52;break;case 48:e.prev=48,e.t1=e.catch(30),console.error("上架失败:",e.t1),b.ZP.error("上架失败，请重试");case 52:e.next=82;break;case 54:e.t2=s.key,e.next="transfer"===e.t2?57:"tag"===e.t2?59:"update"===e.t2?73:"reset"===e.t2?77:"delete"===e.t2?79:81;break;case 57:return r(n),e.abrupt("break",82);case 59:return k(!0),e.prev=60,e.next=63,(0,S.Bp)(m);case 63:j=e.sent,console.log("🚀 ~ handleMenuClick ~ response:",j),j&&j.data&&j.data.tags&&(X(j.data.tags),E(new Set(null===(w=n.tags)||void 0===w?void 0:w.map((function(e){return j.data.tags.indexOf(e)})).filter((function(e){return-1!==e}))))),e.next=72;break;case 68:e.prev=68,e.t3=e.catch(60),console.error("🚀 ~ handleMenuClick ~ error:",e.t3),b.ZP.error("请求失败，请重试");case 72:return e.abrupt("break",82);case 73:return console.log("🚀 ~ handleMenuClick ~ record:",n),Z=n.id,P={name:"file",multiple:!0,progress:{strokeColor:{"0%":"#108ee9","100%":"#87d068"}},maxCount:1e3,beforeUpload:function(e){return!![".pdf",".docx",".csv",".xls",".xlsx",".json"].some((function(n){return e.name.toLowerCase().endsWith(n)}))||(b.ZP.error("只支持 PDF、Word、CSV、Excel 和 JSON 格式的文件"),A.Z.LIST_IGNORE)},onChange:function(){var e=c()(i()().mark((function e(t){var r,a,s,o;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t.fileList.length>0)){e.next=27;break}if(r=t.fileList.map((function(e){return e.originFileObj})),e.prev=2,!0,!n.knowledge_id||!n.parent_id){e.next=20;break}return a={files:r,knowledgeId:n.knowledge_id,parentId:n.parent_id,oldFileId:Z,ocr:"1"},e.next=8,(0,S.bV)(a.files,a.knowledgeId,a.parentId,a.oldFileId,a.ocr);case 8:if(!(s=e.sent).success){e.next=17;break}return b.ZP.success("文件更新成功"),e.next=13,d();case 13:o=e.sent,l(o),e.next=18;break;case 17:b.ZP.error(s.message||"更新失败");case 18:e.next=21;break;case 20:b.ZP.error("缺少必要的文件信息");case 21:e.next=27;break;case 23:e.prev=23,e.t0=e.catch(2),console.error("更新失败:",e.t0),b.ZP.error("更新失败，请重试");case 27:case"end":return e.stop()}}),e,null,[[2,23]])})));return function(n){return e.apply(this,arguments)}}(),onDrop:function(e){console.log("Dropped files",e.dataTransfer.files)}},e.abrupt("return",(0,I.jsx)(A.Z,a()(a()({},P),{},{children:(0,I.jsx)(G.ZP,{children:"选择文件"})})));case 77:return z.Z.confirm({title:"确认重置",content:"确定要重置文件处理结果并重新处理吗？",okText:"确认",cancelText:"取消",onOk:function(){var e=c()(i()().mark((function e(){var n,t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=b.ZP.loading("正在重置"),e.prev=1,e.next=4,(0,S.L9)(u);case 4:if(t=e.sent,console.log(t.success),!t.success){e.next=13;break}return n(),b.ZP.success("重置成功(知识块："+t.deleted_chunk+",索引："+t.deleted_chunk_index+")，开始重新处理"),null==p||null===(r=p.current)||void 0===r||r.reload(),e.abrupt("return",!0);case 13:return n(),b.ZP.error("重置失败，请重试"),e.abrupt("return",!1);case 16:e.next=23;break;case 18:return e.prev=18,e.t0=e.catch(1),n(),b.ZP.error("重置失败，请重试"),e.abrupt("return",!1);case 23:case"end":return e.stop()}}),e,null,[[1,18]])})));return function(){return e.apply(this,arguments)}}()}),e.abrupt("break",82);case 79:return t(n.id),e.abrupt("break",82);case 81:return e.abrupt("break",82);case 82:case"end":return e.stop()}}),e,null,[[5,23],[30,48],[60,68]])})));return function(n){return e.apply(this,arguments)}}(),de=function(){var e=c()(i()().mark((function e(){var n,t,r,a,s;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("🚀 ~ handleOk ~ currentItemId:",o),n=new Set(O),t=Array.from(n).map((function(e){return J[e]})).filter((function(e){return null!=e})),console.log("🚀 ~ handleTagClick ~ selectedTagValues:",t),e.prev=4,e.next=7,(0,S.ki)(o,t);case 7:if(!(r=e.sent).success){e.next=17;break}return b.ZP.success("标签更新成功"),e.next=12,d(!0);case 12:s=e.sent,l(s),null==p||null===(a=p.current)||void 0===a||a.reload(),e.next=18;break;case 17:b.ZP.error(r.message||"标签更新失败");case 18:e.next=24;break;case 20:e.prev=20,e.t0=e.catch(4),console.error("更新标签失败:",e.t0),b.ZP.error("标签更新失败，请重试");case 24:return e.prev=24,k(!1),e.finish(24);case 27:case"end":return e.stop()}}),e,null,[[4,20,24,27]])})));return function(){return e.apply(this,arguments)}}(),pe=function(e){re(e),W(J[e])},fe=function(){var e=c()(i()().mark((function e(n){var t,r,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("🚀 ~ handleTagClick ~ currentFileId:",o),(t=new Set(O)).has(n)?t.delete(n):t.add(n),E(t),r=Array.from(t).map((function(e){return J[e]})).filter((function(e){return null!=e})),console.log("🚀 ~ handleTagClick ~ selectedTagValues:",r),!o){e.next=13;break}return e.next=9,(0,S.ki)(o,r);case 9:return e.next=11,d();case 11:a=e.sent,l(a);case 13:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),xe=function(){var e=c()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:_(!0);case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),he=function(){_(!1),F("")},me=function(){var e=c()(i()().mark((function e(){var n,t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(X((function(e){return[].concat(Y()(e||[]),[R])})),he(),console.log("----+++====="),console.log("🚀 ~ handleTagModal ~ tags:",J),console.log("🚀 ~ handleTagModal ~ knowledgeId:",m),!m){e.next=26;break}return e.prev=6,e.next=9,(0,S.IV)(m,[].concat(Y()(J||[]),[R]));case 9:if(n=e.sent,console.log("🚀 ~ handleTagModal ~ response:",n),!n.success){e.next=20;break}return n.data&&n.data.tags&&X(n.data.tags||[]),e.next=15,d();case 15:t=e.sent,l(t),b.ZP.success("标签创建成功"),e.next=21;break;case 20:b.ZP.error("标签创建失败");case 21:e.next=26;break;case 23:e.prev=23,e.t0=e.catch(6),b.ZP.error("请求失败，请重试");case 26:case"end":return e.stop()}}),e,null,[[6,23]])})));return function(){return e.apply(this,arguments)}}(),ge=function(){var e=c()(i()().mark((function e(n){var t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(t=Y()(J))[n]=q,e.prev=2,e.next=5,(0,S.IV)(m,t);case 5:if(!e.sent.success){e.next=15;break}return X(t),b.ZP.success("标签重命名成功"),e.next=11,(0,S.Bp)(m);case 11:(r=e.sent)&&r.data&&r.data.tags&&X(r.data.tags||[]),e.next=16;break;case 15:b.ZP.error("标签重命名失败");case 16:e.next=22;break;case 18:e.prev=18,e.t0=e.catch(2),b.ZP.error("请求失败，请重试"),console.error("重命名标签时出错:",e.t0);case 22:return e.prev=22,re(null),W(""),e.finish(22);case 26:case"end":return e.stop()}}),e,null,[[2,18,22,26]])})));return function(n){return e.apply(this,arguments)}}(),ve=(0,I.jsxs)($.Z,{onClick:ue,children:[(0,I.jsx)($.Z.Item,{children:"转移"},"transfer"),(0,I.jsx)($.Z.Item,{children:"标签"},"tag"),(0,I.jsx)($.Z.Item,{children:"重置"},"reset"),0!==n.flg&&(ce?(0,I.jsx)($.Z.Item,{children:"上架"},"publish"):(0,I.jsx)($.Z.Item,{children:"下架"},"unpublish")),(0,I.jsx)($.Z.Item,{children:"删除"},"delete")]});return(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)(Q.Z,{overlay:ve,trigger:["click"],children:(0,I.jsx)(G.ZP,{onClick:function(){return s(n.id)},type:"link",children:"更多"})}),(0,I.jsxs)(z.Z,{title:"标签",visible:j,onCancel:function(){k(!1)},onOk:function(){return de()},okText:"确定",cancelText:"取消",children:[(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",height:"100%",marginBottom:16},children:[(0,I.jsx)("strong",{children:"请选择一个标签"}),(0,I.jsx)(G.ZP,{onClick:xe,type:"primary",children:"创建标签"})]}),(0,I.jsx)(Z.Z,{dataSource:J,renderItem:function(e,n){return(0,I.jsx)(Z.Z.Item,{style:{padding:"8px 16px",borderRadius:"4px",marginBottom:"4px",backgroundColor:O.has(n)?"#e6f7ff":"transparent",cursor:"pointer"},children:(0,I.jsx)(Q.Z,{overlay:(0,I.jsxs)($.Z,{children:[(0,I.jsx)($.Z.Item,{onClick:function(){return pe(n)},children:"重命名"},"rename"),(0,I.jsx)($.Z.Item,{onClick:function(){return function(e){var n;z.Z.confirm({title:"确认删除",content:"确定要删除这个标签吗？",okText:"确认",cancelText:"取消",onOk:(n=c()(i()().mark((function n(){var t;return i()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return t=J.filter((function(n,t){return t!==e})),X(t),n.next=4,(0,S.IV)(m,t);case 4:b.ZP.success("标签删除成功");case 5:case"end":return n.stop()}}),n)}))),function(){return n.apply(this,arguments)})})}(n)},children:"删除"},"delete")]}),trigger:["contextMenu"],children:(0,I.jsx)("span",{children:(0,I.jsx)(Le.Z,{checked:O.has(n),onChange:function(e){e.stopPropagation(),fe(n)},children:te===n?(0,I.jsx)(M.Z,{value:q,onChange:function(e){return W(e.target.value)},onBlur:function(){return re(null)},onKeyPress:function(e){"Enter"===e.key&&ge(n)},autoFocus:!0}):(0,I.jsx)("span",{onDoubleClick:function(){return pe(n)},children:e})})})})})}})]}),(0,I.jsxs)(z.Z,{title:"创建标签",visible:C,onCancel:he,footer:null,style:{marginTop:80},width:400,children:[(0,I.jsx)(M.Z,{value:R,placeholder:"请输入标签名称",onChange:function(e){return F(e.target.value)}}),(0,I.jsx)(G.ZP,{type:"primary",onClick:me,style:{marginTop:"10px"},children:"确定"})]})]})},De=function(){var e=(0,h.useState)(!1),n=u()(e,2),t=n[0],r=n[1],a=(0,h.useState)([]),s=u()(a,2),i=s[0],o=s[1];return(0,I.jsx)(I.Fragment,{children:(0,I.jsx)(z.Z,{title:"请选择标签",visible:t,onOk:function(){console.log("Selected Tags:",i),r(!1)},onCancel:function(){r(!1)},okText:"确定",cancelText:"取消",children:(0,I.jsx)(Le.Z.Group,{options:[],value:i,onChange:function(e){o(e)}})})})},ze=function(e){var n=e.knowledgeBaseId,t=(0,h.useState)(!1),r=u()(t,2),s=r[0],o=r[1],l=(0,h.useState)(""),d=u()(l,2),p=d[0],f=d[1],x=(0,h.useState)(""),m=u()(x,2),g=m[0],v=m[1],y=(0,h.useRef)(),j=(0,h.useState)([]),k=u()(j,2),w=k[0],P=k[1],C=(0,h.useState)(!1),_=u()(C,2),T=_[0],B=_[1],R=(0,h.useState)([]),F=u()(R,1)[0],N=(0,h.useState)(""),L=(u()(N,1)[0],(0,h.useState)(null)),O=u()(L,2),E=O[0],D=O[1],A=(0,h.useState)([]),V=u()(A,2),q=V[0],W=V[1],H=(0,h.useState)(null),K=u()(H,2),J=K[0],X=K[1],U=(0,h.useState)(""),ne=u()(U,2),te=ne[0],re=ne[1],ae=(0,h.useState)(!1),se=u()(ae,2),ie=se[0],oe=se[1],ce=(0,h.useState)([]),ue=u()(ce,2),de=(ue[0],ue[1]),pe=(0,h.useState)([]),fe=u()(pe,2),xe=fe[0],he=fe[1],me=(0,h.useState)(null),ge=u()(me,2),ve=ge[0],ye=ge[1],je=(0,h.useState)(null),ke=u()(je,2),be=ke[0],we=ke[1],Ze=(0,h.useState)(!1),Pe=u()(Ze,2),Ce=Pe[0],Se=Pe[1],Ie=(0,ee.TH)(),_e=new URLSearchParams(Ie.search).get("id");console.log("🚀 ~ FileOperations ~ id:",_e);var Te=(0,ee.s0)(),Me=(0,h.useRef)(),Be=(0,h.useState)({}),Re=u()(Be,2),Fe=Re[0],Le=Re[1];(0,h.useEffect)((function(){_e&&v(_e)}),[_e]);var ze=function(){var e=c()(i()().mark((function e(){var t,r,a,s,o=arguments;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=!(o.length>0&&void 0!==o[0])||o[0],r=o.length>1?o[1]:void 0,e.prev=2,console.log("🚀 ~ fetchKnowledgeFolderTree ~ searchParams:",t?r||Fe:{}),a=t?r||Fe:{},e.next=7,(0,S.E)(n,t&&a.name||"",[],!1,t&&"all"!==a.forbidden?"true"===a.forbidden:void 0,t&&"all"!==a.data_type?a.data_type:void 0,t&&"all"!==a.flg?parseInt(a.flg):void 0);case 7:return s=e.sent,e.abrupt("return",s.data||[]);case 11:return e.prev=11,e.t0=e.catch(2),b.ZP.error("获取文件夹树失败"),e.abrupt("return",[]);case 15:case"end":return e.stop()}}),e,null,[[2,11]])})));return function(){return e.apply(this,arguments)}}();(0,h.useEffect)((function(){var e=function(){var e=c()(i()().mark((function e(){var n,t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,ze(!1);case 3:n=e.sent,t=F.map((function(e){return{id:e.id,name:e.name,isCategory:!0}})),W([].concat(Y()(n),Y()(t))),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),b.ZP.error("获取文件列表失败");case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(){return e.apply(this,arguments)}}();e()}),[F,n]);var Ae=function(){var e=c()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:z.Z.confirm({title:"确认删除",content:"确定要删除这个文件吗？",okText:"确认",cancelText:"取消",onOk:function(){var e=c()(i()().mark((function e(){var t,r,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=b.ZP.loading("正在删除"),e.prev=1,e.next=4,(0,S._I)(n);case 4:if(!e.sent.success){e.next=15;break}return t(),b.ZP.success("删除成功"),null===(r=y.current)||void 0===r||r.reload(),e.next=11,ze(!0);case 11:a=e.sent,W(a),e.next=17;break;case 15:t(),b.ZP.error("删除失败，请重试");case 17:W((function(e){return e.filter((function(e){return e.id!==n}))})),e.next=24;break;case 20:e.prev=20,e.t0=e.catch(1),t(),b.ZP.error("删除失败，请重试");case 24:case"end":return e.stop()}}),e,null,[[1,20]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Ve=function(){var e=c()(i()().mark((function e(n,t){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,S.yN)(n,!0,t);case 3:e.next=9;break;case 5:e.prev=5,e.t0=e.catch(0),b.ZP.error("文件下载失败"),console.error("下载错误:",e.t0);case 9:case"end":return e.stop()}}),e,null,[[0,5]])})));return function(n,t){return e.apply(this,arguments)}}(),qe=function(){var e=c()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(w&&0!==w.length){e.next=3;break}return b.ZP.info("请先选择要删除的文件"),e.abrupt("return");case 3:z.Z.confirm({title:"确认批量删除",content:"确定要删除选中的 ".concat(w.length," 个文件吗？"),okText:"确认",cancelText:"取消",onOk:function(){var e=c()(i()().mark((function e(){var n,t,r,a,s,o;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=b.ZP.loading("正在删除"),e.prev=1,e.next=4,Promise.all(w.map((function(e){return(0,S._I)(e.id)})));case 4:if(t=e.sent,r=t.every((function(e){return e.success})),n(),!r){e.next=19;break}return b.ZP.success("批量删除成功"),W((function(e){return e.filter((function(e){return!w.some((function(n){return n.id===e.id}))}))})),P([]),null===(a=y.current)||void 0===a||a.reload(),e.next=14,ze(!0);case 14:return s=e.sent,W(s),e.abrupt("return",!0);case 19:return b.ZP.error("部分文件删除失败，请重试"),null===(o=y.current)||void 0===o||o.reload(),e.abrupt("return",!1);case 22:e.next=30;break;case 24:return e.prev=24,e.t0=e.catch(1),n(),console.error("批量删除失败:",e.t0),b.ZP.error("批量删除失败，请重试"),e.abrupt("return",!1);case 30:case"end":return e.stop()}}),e,null,[[1,24]])})));return function(){return e.apply(this,arguments)}}()});case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),We=function(){var e=c()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:z.Z.confirm({title:"确认批量重置",content:"确定要重置选中的 ".concat(w.length," 个文件处理结果并重新处理吗？"),okText:"确认",cancelText:"取消",onOk:function(){var e=c()(i()().mark((function e(){var n,t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=b.ZP.loading("正在批量重置"),e.prev=1,t=w.map((function(e){return e.id})),e.next=5,Promise.all(t.map((function(e){return(0,S.L9)(e)})));case 5:if(!e.sent.every((function(e){return e.success}))){e.next=14;break}return n(),b.ZP.success("批量重置成功"),P([]),null===(r=y.current)||void 0===r||r.reload(),e.abrupt("return",!0);case 14:return n(),b.ZP.error("部分重置失败，请重试"),e.abrupt("return",!1);case 17:e.next=24;break;case 19:return e.prev=19,e.t0=e.catch(1),n(),b.ZP.error("批量重置失败，请重试"),e.abrupt("return",!1);case 24:case"end":return e.stop()}}),e,null,[[1,19]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Ge=function(e){B(!0),de([e])},He=function(){o(!1),f("")},Ke=function(){var e=c()(i()().mark((function e(){var n,t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(p&&""!==p.trim()){e.next=3;break}return b.ZP.error("分类名称不能为空"),e.abrupt("return");case 3:return e.prev=3,"add",n=g,"",e.next=9,(0,S.l$)("add",p,n,"","");case 9:return(t=e.sent).detail?b.ZP.error(t.detail):b.ZP.success("分类创建成功"),e.next=13,ze(!0);case 13:r=e.sent,W(r),He(),e.next=22;break;case 18:e.prev=18,e.t0=e.catch(3),console.error("创建分类失败:",e.t0),b.ZP.error("创建分类失败，请重试");case 22:case"end":return e.stop()}}),e,null,[[3,18]])})));return function(){return e.apply(this,arguments)}}(),Je=function(){var e=c()(i()().mark((function e(n,t){var r,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,"update",e.next=4,(0,S.l$)("update",t,g,"",n);case 4:return r=e.sent,console.log("文件夹名称更新成功:",r),ye(null),e.next=9,ze(!0);case 9:a=e.sent,W(a),e.next=17;break;case 13:e.prev=13,e.t0=e.catch(0),console.error("更新文件夹名称失败:",e.t0),b.ZP.error("更新文件夹名称失败");case 17:case"end":return e.stop()}}),e,null,[[0,13]])})));return function(n,t){return e.apply(this,arguments)}}(),Xe=function(){var e=c()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!J){e.next=13;break}return e.prev=1,e.next=4,(0,S.e_)(J,te);case 4:b.ZP.success("重命名成功"),W((function(e){return e.map((function(e){return e.id===J?a()(a()({},e),{},{name:te}):e}))})),D(te),X(null),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(1),b.ZP.error("重命名失败");case 13:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}(),Ue=function(){var e=c()(i()().mark((function e(){var n,t,r,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!be||!E){e.next=31;break}return e.prev=1,n=b.ZP.loading("正在移动文件..."),e.next=5,(0,S.SZ)(be,E);case 5:if(t=e.sent,n(),!t.success){e.next=16;break}return b.ZP.success(t.message||"文件移动成功"),e.next=11,ze(!0);case 11:a=e.sent,W(a),null==y||null===(r=y.current)||void 0===r||r.reload(),e.next=17;break;case 16:b.ZP.error(t.message||"文件移动失败");case 17:B(!1),Se(!1),D(""),we(""),e.next=29;break;case 23:e.prev=23,e.t0=e.catch(1),console.error("移动文件失败:",e.t0),b.ZP.error("移动文件失败，请重试"),B(!1),Se(!1);case 29:e.next=32;break;case 31:b.ZP.error("请选择要移动的文件和目标分类");case 32:case"end":return e.stop()}}),e,null,[[1,23]])})));return function(){return e.apply(this,arguments)}}(),Ye=function(){var e=c()(i()().mark((function e(n){var t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:console.log("🚀 ~ handleCategoryDoubleClick ~ categoryId:",n),ye(n),(t=q.find((function(e){return e.id===n})))&&f(t.name);case 4:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),$e=function(e){console.log("🚀 ~ handleItemClick ~ id:",e),we(e)},Qe=function(e,n){"rename"===e.key?Ye(n):"move"===e.key&&function(e){D(e),Se(!0)}(n)},en=[{title:"文件名",dataIndex:"name",ellipsis:!0,fieldProps:{placeholder:"请输入文件名"},render:function(e,n){return(0,I.jsx)("span",{style:{fontSize:"12px",fontWeight:n.isCategory?"bold":"normal",cursor:n.isCategory?"pointer":"default"},onClick:function(){return n.isCategory&&(he(n.children||[]),void oe(!0))},children:n.isCategory?(0,I.jsxs)("span",{style:{display:"flex",alignItems:"center"},children:[e,"(文件夹)"]}):e})}},{title:"大小",width:100,dataIndex:"size",search:!1,render:function(e,n){return n.isCategory||"folder"===n.data_type?"-":n.size?"".concat((n.size/1024).toFixed(2)," KB"):"-"}},{title:"文件格式",width:120,dataIndex:"data_type",valueType:"select",fieldProps:{placeholder:"选择文件格式"},valueEnum:{all:{text:"全部"},TXT:{text:"TXT"},PDF:{text:"PDF"},DOC:{text:"DOC"},DOCX:{text:"DOCX"},MD:{text:"MD"},CSV:{text:"CSV"},XLS:{text:"XLS"},XLSX:{text:"XLSX"},JSON:{text:"JSON"},JSONL:{text:"JSONL"},PPTX:{text:"PPTX"},HTML:{text:"HTML"},HTM:{text:"HTM"},JPG:{text:"JPG"},JPEG:{text:"JPEG"},PNG:{text:"PNG"},WAV:{text:"WAV"},MP3:{text:"MP3"},Markdown:{text:"Markdown"},folder:{text:"文件夹"}},initialValue:"all",render:function(e,n){if("folder"===n.data_type)return(0,I.jsx)(le.Z,{color:"blue",children:"文件夹"});var t=function(e,n){var t,r=(null===(t=e.split(".").pop())||void 0===t?void 0:t.toLowerCase())||"";return"folder"===n?{label:"文件夹",color:"blue"}:{label:r?r.toUpperCase():n||"未知",color:{pdf:"red",doc:"blue",docx:"blue",txt:"default",md:"green",markdown:"green",xlsx:"green",xls:"green",csv:"orange",json:"purple",jsonl:"purple",pptx:"orange",ppt:"orange",html:"cyan",htm:"cyan",jpg:"magenta",jpeg:"magenta",png:"magenta",gif:"magenta",mp3:"gold",wav:"gold"}[r]||"default"}}(n.name,n.data_type);return(0,I.jsx)(le.Z,{color:t.color,children:t.label})}},{title:"上传时间",dataIndex:"created_at",valueType:"dateTime",search:!1,render:function(e,n){return n.isCategory?"--":e}},{title:"状态",dataIndex:"flg",width:120,valueType:"select",fieldProps:{placeholder:"选择状态"},valueEnum:{all:{text:"全部"},0:{text:"等待处理",status:"default"},1:{text:"识别完成",status:"processing"},2:{text:"索引完成",status:"success"},3:{text:"识别失败",status:"error"},4:{text:"索引失败",status:"error"}},initialValue:"all",render:function(e,n){if("folder"===n.data_type)return"--";switch(n.flg){case 0:return(0,I.jsx)(le.Z,{color:"default",children:"等待处理"});case 1:return(0,I.jsx)(le.Z,{color:"processing",children:"识别完成"});case 2:return(0,I.jsx)(le.Z,{color:"success",children:"索引完成"});case 3:return(0,I.jsx)(le.Z,{color:"error",children:"识别失败"});case 4:return(0,I.jsx)(le.Z,{color:"error",children:"索引失败"});default:return"--"}}},{title:"上线状态",dataIndex:"forbidden",width:80,valueType:"select",fieldProps:{placeholder:"选择上线状态"},valueEnum:{all:{text:"全部"},false:{text:"已上线",status:"Success"},true:{text:"已下线",status:"Error"}},initialValue:"all",render:function(e,n){return"folder"===n.data_type?"--":n.forbidden?(0,I.jsx)(le.Z,{color:"red",children:"已下线"}):(0,I.jsx)(le.Z,{color:"green",children:"已上线"})}},{title:"标签",dataIndex:"tags",search:!1,render:function(e,n){return"folder"===n.data_type?"--":Array.isArray(n.tags)?n.tags.join(", "):"无"}},{title:"视觉",dataIndex:"ocr",width:60,search:!1,render:function(e,n){return"folder"===n.data_type?"--":1===n.ocr?"启用":"-"}},{title:"操作",valueType:"option",render:function(e,n){return"folder"===n.data_type?[(0,I.jsx)(G.ZP,{type:"link",onClick:function(){return function(e){X(e.id),re(e.name),D(e.name)}(n)},children:"重命名"},"rename"),(0,I.jsx)(G.ZP,{type:"link",danger:!0,onClick:function(){return Ae(n.id)},children:"删除"},"delete")]:[(0,I.jsx)(G.ZP,{type:"link",style:{display:"inline-block",width:40,textAlign:"left"},onClick:function(){return Te("/knowledgeManagement/fileInfo?id=".concat(n.id))},children:"查看"},"view"),(0,I.jsx)(G.ZP,{type:"link",style:{display:"inline-block",width:40,textAlign:"left"},onClick:function(){return Ve(n.id,n.name)},children:"下载"},"download"),(0,I.jsx)(Ee,{record:n,onDelete:Ae,onTransfer:Ge,onMoreClick:$e,currentItemId:be,setTableData:W,fetchKnowledgeFolderTree:ze,actionRef:y},"operations")]}}],nn=q.filter((function(e){return"folder"===e.data_type})),tn=function(){var e=c()(i()().mark((function e(n){var t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("提交的表单值:",n),Le(n),e.next=4,ze(!0,n);case 4:t=e.sent,W(t);case 6:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}();return(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)(Ne.Z,{actionRef:y,formRef:Me,columns:en,toolBarRender:!1,rowKey:"id",params:a()({data_type:"all",flg:"all",forbidden:"all"},Fe),request:function(){var e=c()(i()().mark((function e(n){var t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("请求参数:",JSON.stringify(n,null,2)),delete(t=a()({},n)).current,delete t.pageSize,Le(t),e.next=7,ze(!0,t);case 7:return r=e.sent,e.abrupt("return",{data:r,success:!0,total:r.length});case 9:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),options:!1,search:{labelWidth:"auto",layout:"vertical",defaultCollapsed:!1,searchText:"查询",resetText:"重置",optionRender:function(e,n){return[(0,I.jsx)(G.ZP,{type:"primary",onClick:function(){var e,t=null===(e=n.form)||void 0===e?void 0:e.getFieldsValue();tn(t)},children:"查询"},"submit"),(0,I.jsx)(G.ZP,{onClick:function(){var e;null===(e=Me.current)||void 0===e||e.resetFields(),Le({}),ze(!1).then((function(e){W(e)}))},children:"重置"},"reset")]}},rowSelection:{onChange:function(e,n){P(n)},getCheckboxProps:function(e){return{disabled:e.isCategory}}},tableAlertRender:function(e){var n=e.selectedRowKeys,t=e.onCleanSelected;return(0,I.jsxs)("div",{children:["已选择 ",n.length," 项",(0,I.jsx)("a",{style:{marginLeft:8},onClick:t,children:"取消选择"})]})},tableAlertOptionRender:function(){return(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)(G.ZP,{danger:!0,onClick:qe,children:"批量删除"}),(0,I.jsx)(G.ZP,{onClick:We,style:{marginLeft:8},children:"批量重置"},"batchReset")]})},size:"small"}),(0,I.jsx)(z.Z,{title:"转移文件",visible:T,onCancel:function(){return B(!1)},onOk:function(){Se(!0)},okText:"确定",cancelText:"取消",children:(0,I.jsxs)("div",{children:[(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",height:"100%",marginBottom:16},children:[(0,I.jsx)("strong",{children:"请选择一个分类"}),(0,I.jsx)(G.ZP,{onClick:function(){o(!0)},type:"primary",children:"创建分类"})]}),(0,I.jsx)(Z.Z,{dataSource:nn,renderItem:function(e){return(0,I.jsx)(Z.Z.Item,{style:{padding:"8px 16px",borderRadius:"4px",marginBottom:"4px",backgroundColor:E===e.id?"#e6f7ff":"transparent",cursor:"pointer"},onClick:function(){D(e.id)},onContextMenu:function(e){e.preventDefault()},children:(0,I.jsx)(Q.Z,{overlay:(0,I.jsxs)($.Z,{onClick:function(n){return Qe(n,e.id)},children:[(0,I.jsx)($.Z.Item,{children:"重命名"},"rename"),(0,I.jsx)($.Z.Item,{children:"移动文件"},"move")]}),trigger:["contextMenu"],children:(0,I.jsx)("span",{children:ve===e.id?(0,I.jsx)(M.Z,{value:p,onChange:function(e){return f(e.target.value)},onBlur:function(){return ye(null)},onKeyPress:function(n){return function(e,n){"Enter"===e.key&&(Je(n,p),ye(null))}(n,e.id)},style:{flex:1},autoFocus:!0}):(0,I.jsx)("span",{children:e.name})})})})}})]})}),(0,I.jsx)(z.Z,{title:"确认移动",visible:Ce,onOk:Ue,onCancel:function(){return Se(!1)},style:{marginTop:80},width:400,children:(0,I.jsx)("p",{children:"确认移动到选中的分类吗？"})}),(0,I.jsx)(z.Z,{title:"重命名文件夹",visible:!!J,onCancel:function(){return X(null)},onOk:Xe,children:(0,I.jsx)(M.Z,{value:te,onChange:function(e){return re(e.target.value)},placeholder:"输入新名称"})}),(0,I.jsxs)(z.Z,{title:"创建分类",visible:s,onCancel:He,footer:null,style:{marginTop:80},width:400,children:[(0,I.jsx)(M.Z,{value:p,placeholder:"请输入分类名称",onChange:function(e){return f(e.target.value)}}),(0,I.jsx)(G.ZP,{type:"primary",onClick:Ke,style:{marginTop:"10px"},children:"确定"})]}),(0,I.jsx)(z.Z,{title:"文件夹内容",visible:ie,onCancel:function(){return oe(!1)},footer:null,children:(0,I.jsx)(Oe.Z,{dataSource:xe,columns:[{title:"文件名",dataIndex:"name",key:"name"},{title:"大小",dataIndex:"size",key:"size",render:function(e){return"".concat((e/1024).toFixed(2)," KB")}},{title:"上传时间",dataIndex:"created_at",key:"created_at"}],rowKey:"id"})}),(0,I.jsx)(De,{})]})},Ae=t(8232),Ve=t(38925),qe=function(e){var n=e.initialValues,t=e.onChange,r=Ae.Z.useForm(),a=u()(r,1)[0];return(0,h.useEffect)((function(){a.setFieldsValue(n)}),[n]),(0,I.jsxs)(Ae.Z,{form:a,layout:"vertical",onValuesChange:t,children:[(0,I.jsx)(Ae.Z.Item,{label:"问题",name:"question",rules:[{required:!0}],children:(0,I.jsx)(M.Z.TextArea,{rows:3})}),(0,I.jsx)(Ae.Z.Item,{label:"答案",name:"answer",rules:[{required:!0}],children:(0,I.jsx)(M.Z.TextArea,{rows:5})})]})},We=function(e){var n=e.knowledgeBaseId,t=(0,h.useRef)(),r=(0,h.useState)([]),a=u()(r,2),s=a[0],o=a[1],l=(0,h.useState)(!1),d=u()(l,2),p=d[0],f=d[1],x=(0,h.useState)(null),m=u()(x,2),g=m[0],v=m[1],y=(0,h.useState)({answer:"",question:""}),j=u()(y,2),k=j[0],w=j[1],Z=(0,h.useState)(!1),P=u()(Z,2),C=P[0],_=P[1],T=(0,h.useState)(""),M=u()(T,2),B=(M[0],M[1]),R=function(){var e=c()(i()().mark((function e(){var r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(s.length){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,Promise.all(s.map((function(e){return(0,S.Pq)(n,"chunkId_placeholder",e.id)})));case 5:b.ZP.success("批量删除成功"),null===(r=t.current)||void 0===r||r.reload(),o([]),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(2),b.ZP.error("批量删除失败");case 13:case"end":return e.stop()}}),e,null,[[2,10]])})));return function(){return e.apply(this,arguments)}}(),F=function(){v(null),w({answer:"",question:""}),f(!0)},N=function(){var e=c()(i()().mark((function e(){var r,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,a={question:k.question.trim(),answer:k.answer.trim()},!g){e.next=7;break}return e.next=5,updateKnowledgeChunk(g.id,a);case 5:e.next=9;break;case 7:return e.next=9,createKnowledgeChunk(n,a);case 9:b.ZP.success(g?"更新成功":"创建成功"),null===(r=t.current)||void 0===r||r.reload(),f(!1),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(0),b.ZP.error(g?"操作失败":"创建失败");case 17:case"end":return e.stop()}}),e,null,[[0,14]])})));return function(){return e.apply(this,arguments)}}(),L=[{title:"开源文件",dataIndex:"file_name",width:"20%",search:{transform:function(e){return{file_name:e}}},render:function(e){return(0,I.jsx)(ce.Z,{overlayStyle:{maxWidth:800,minWidth:300},title:(0,I.jsx)("pre",{style:{margin:0,whiteSpace:"pre-wrap"},children:e}),children:(0,I.jsx)("span",{style:{display:"inline-block",width:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e})})}},{title:"question",dataIndex:"question",width:"20%",ellipsis:{showTitle:!0,tooltip:{overlayStyle:{width:600}}},search:{transform:function(e){return{question:e}}}},{title:"answer",dataIndex:"answer",width:"20%",search:{transform:function(e){return{file_name:e}}},render:function(e){return(0,I.jsx)(ce.Z,{overlayStyle:{maxWidth:800,minWidth:300},title:(0,I.jsx)("pre",{style:{margin:0,whiteSpace:"pre-wrap"},children:e}),children:(0,I.jsx)("span",{style:{display:"inline-block",width:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e})})}},{title:"创建时间",dataIndex:"created_at",width:"10%",valueType:"dateTime",sorter:!0,search:!1},{title:"操作",valueType:"option",width:120,render:function(e,n){return[(0,I.jsx)(G.ZP,{type:"link",style:{display:"inline-block",width:40,textAlign:"left"},onClick:function(){return function(e){v(e),w({question:e.question,answer:e.answer}),f(!0)}(n)},children:"编辑"},"edit"),(0,I.jsx)(G.ZP,{type:"link",style:{display:"inline-block",width:60,textAlign:"left"},onClick:function(){return e=n.id,B(e),void _(!0);var e},children:"查看索引"},"view-index"),(0,I.jsx)(G.ZP,{type:"link",style:{display:"inline-block",width:40,textAlign:"left"},danger:!0,onClick:c()(i()().mark((function e(){var r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,S.Vk)(n.id);case 3:b.ZP.success("删除成功"),null===(r=t.current)||void 0===r||r.reload(),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),b.ZP.error("删除失败");case 10:case"end":return e.stop()}}),e,null,[[0,7]])}))),children:"删除"},"delete")]}}];return(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)(Ne.Z,{actionRef:t,columns:L,toolBarRender:function(){return[(0,I.jsx)(G.ZP,{type:"primary",onClick:F,children:"新建知识条目"},"create")]},request:function(){var e=c()(i()().mark((function e(t){var r,a,s;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,S.dx)(n,null!==(r=t.current)&&void 0!==r?r:1,null!==(a=t.pageSize)&&void 0!==a?a:10,t.question,t.answer);case 3:return s=e.sent,e.abrupt("return",{data:s.data||[],success:!0,total:s.total||0});case 7:return e.prev=7,e.t0=e.catch(0),b.ZP.error("获取知识索引失败"),e.abrupt("return",{data:[],success:!1,total:0});case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(n){return e.apply(this,arguments)}}(),rowKey:"id",rowSelection:{onChange:function(e,n){o(n)}},tableAlertRender:function(e){var n=e.selectedRowKeys,t=e.onCleanSelected;return(0,I.jsxs)("div",{children:["已选择 ",n.length," 项",(0,I.jsx)("a",{style:{marginLeft:8},onClick:t,children:"取消选择"})]})},tableAlertOptionRender:function(){return(0,I.jsx)(G.ZP,{danger:!0,onClick:R,children:"批量删除"})},size:"small"}),(0,I.jsxs)(z.Z,{title:g?"编辑知识条目":"新建知识条目",visible:p,onOk:N,onCancel:function(){return f(!1)},destroyOnClose:!0,children:[g?(0,I.jsx)(Ve.Z,{message:"编辑提示",description:"修改内容会自动创建一条新的索引，原索引将保留历史版本",type:"info",showIcon:!0,style:{marginBottom:16}}):(0,I.jsx)(Ve.Z,{message:"新建提示",description:"新建索引会基于内容自动创建一条优化后的索引信息",type:"info",showIcon:!0,style:{marginBottom:16}}),(0,I.jsx)(qe,{initialValues:k,onChange:function(e){return w(e)}})]}),(0,I.jsx)(z.Z,{title:"索引详情",visible:C,onCancel:function(){return _(!1)},footer:null,width:800,destroyOnClose:!0,children:(0,I.jsx)("div",{style:{maxHeight:"60vh",overflow:"auto",padding:"16px",backgroundColor:"#f5f5f5",borderRadius:"4px"},children:(0,I.jsx)("pre",{style:{whiteSpace:"pre-wrap"},children:JSON.stringify({},null,2)})})})]})},Ge=t(35312),He=t(49185),Ke=t(27484),Je=t.n(Ke),Xe=(0,P.kc)((function(e){var n=e.token;return{main:{background:n.colorBgContainer,borderRadius:n.borderRadius,"& .ant-card":{borderRadius:0}},headerList:{margin:"20px 0"},moreInfo:{display:"flex",justifyContent:"space-between",width:"200px"}}})),Ue=function(){var e=(0,ee.s0)(),n=Xe().styles,t=(0,Ge.useSearchParams)(),r=u()(t,1)[0].get("id"),s=(0,h.useState)("files"),o=u()(s,2),l=o[0],m=o[1],g=(0,h.useState)(null),v=u()(g,2),y=v[0],j=v[1],k=(0,h.useState)(!0),b=u()(k,2),w=b[0],Z=b[1];(0,h.useEffect)((function(){var e=function(){var e=c()(i()().mark((function e(){var n;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!r){e.next=15;break}return e.prev=1,Z(!0),e.next=5,(0,He.IV)(r);case 5:(n=e.sent).success?j(n.data):console.error("获取知识库数据失败"),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(1),console.error("加载知识库失败:",e.t0);case 12:return e.prev=12,Z(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[1,9,12,15]])})));return function(){return e.apply(this,arguments)}}();e()}),[r]);var P=(0,I.jsxs)(f.Z,{className:n.headerList,size:"small",column:2,children:[(0,I.jsx)(f.Z.Item,{label:"描述",children:(null==y?void 0:y.description)||"-"}),(0,I.jsx)(f.Z.Item,{label:"创建时间",children:null!=y&&y.created_at?Je()(y.created_at).format("YYYY-MM-DD HH:mm:ss"):"-"}),(0,I.jsx)(f.Z.Item,{label:"最后更新时间",children:null!=y&&y.updated_at?Je()(y.updated_at).format("YYYY-MM-DD HH:mm:ss"):"-"})]}),C=(0,I.jsx)("div",{className:n.moreInfo}),S={files:r?(0,I.jsx)(ze,{knowledgeBaseId:r}):null,uploadDatasetFile:r?(0,I.jsx)(X,{knowledgeId:r}):null,base:y?(0,I.jsx)(T,a()({},y)):null,tagManagement:y?(0,I.jsx)(ne,{knowledgeBaseId:r}):null,permissionManagement:y?(0,I.jsx)(re,{knowledgeBaseId:r}):null,searchTest:y?(0,I.jsx)(Ce,{knowledgeBaseId:r}):null,graph:r?(0,I.jsx)(Re,{knowledgeBaseId:r}):null,apiService:y?(0,I.jsx)(Fe,{knowledgeBaseId:r}):null,binding:(0,I.jsx)(B,{}),parse:(0,I.jsx)(F,{}),"search-test":(0,I.jsx)(L,{}),"knowledge-index":r?(0,I.jsx)(We,{knowledgeBaseId:r}):null};return(0,I.jsx)(d._z,{title:(null==y?void 0:y.name)||"加载中...",onBack:function(){return e("/knowledgeManagement/knowledgeBase")},content:w?(0,I.jsx)("div",{children:"加载中..."}):P,extraContent:w?(0,I.jsx)("div",{children:"加载中..."}):C,tabActiveKey:l,onTabChange:function(e){m(e)},tabList:[{key:"files",tab:"文件管理"},{key:"uploadDatasetFile",tab:"上传文件"},{key:"base",tab:"基本设置"},{key:"tagManagement",tab:"文件标签"},{key:"permissionManagement",tab:"权限设置"},{key:"searchTest",tab:"检索测试"},{key:"graph",tab:"图谱"}],children:(0,I.jsx)(p.f,{children:(0,I.jsx)("div",{className:n.main,children:(0,I.jsx)(x.Z,{bordered:!1,style:{padding:0},children:S[l]})})})})}}}]);