
from langgraph.graph import StateGraph, <PERSON><PERSON>
from typing import Dict, List, Any, TypedDict, Optional, AsyncGenerator, Annotated
from langgraph.graph.message import add_messages
import httpx
import asyncio
from app.utils.logging_config import setup_logging, get_logger
import time
import json
import hashlib
import os
from langchain_openai import ChatOpenAI
from app.models.llm import LLMModel
from app.engines.agent.Intelligent_review_prompt import SYSTEM_PROMPT_01

setup_logging()
logger = get_logger(__name__)

class AgentState(TypedDict):
    content_input: str
    rules: Optional[List[Dict[str, str]]] # 数据查询结果
    

class IntelligentReview:
    def __init__(self, config: Dict[str, str], db, evaluation_type: str, llm: LLMModel):
        self.config = config
        self.evaluation_type =evaluation_type
        self.db = db
        self.workflow = StateGraph(AgentState)
        self._setup_workflow()
        self.llm = ChatOpenAI(
                model=llm.get("m_name"),
                temperature=float(llm.get("temperature", 0.1)),
                openai_api_key=llm.get("api_key"),
                openai_api_base= llm.get("service_url"),
                stream = False
            )

    def _setup_workflow(self):
        # 定义三个节点
        # 第一个节点。拼接提示词
        # 第二个节点 调用大模型 推理模型
        # 从结构里抽取结果，并返回
        self.workflow.add_node("get_rules", self.get_rules)
        self.workflow.add_node("build_prompt", self.build_prompt_node)
        self.workflow.add_node("llm_inference", self.llm_inference_node)
        self.workflow.add_node("extract_result", self.extract_result_node)



    async def get_rules(self, state: AgentState) -> AgentState:
         evaluations  = await self.db[ 'consumer_protection_rules'].find({"ruleType":self.evaluation_type}).to_list()
         state.rules = evaluations
         state['rules'] = evaluations
         return state
        
    async def build_prompt_node(self, state: AgentState) -> AgentState:
        """构建提示词节点"""
        logger.info('构建提示词')
        # 从state中获取必要参数

        rules = state.get("rules", [])
        if len(rules)>0:
            rules = [f"规则ID:{rule['ruleId']} 规则名称:{rule['ruleName']} 描述:{rule['description']}" for rule in rules]
        
        # 拼接系统提示词和用户输入
        system_prompt = SYSTEM_PROMPT_01.format(
            roles=table_data_Barthel,
            content=item_config,
        )

    #     '''
    #     for rule in rules:
    #         system_prompt += f"- {rule['description']}\n"
            
    #     return {**state, "messages": [
    #         {"role": "system", "content": system_prompt},
    #         {"role": "user", "content": content_input}
    #     ]}

    # async def llm_inference_node(self, state: AgentState) -> AgentState:
    #     """大模型推理节点"""
    #     logger.info('执行大模型推理')
    #     try:
    #         from app.utils.llmClient import openai_api
    #         response = await openai_api(
    #             api_key=state["llm_params"].get("api_key"),
    #             model=state["llm_params"].get("model", "gpt-4"),
    #             messages=state["messages"],
    #             url=state["llm_params"].get("service_url"),
    #             provider=state["llm_params"].get("provider", "OpenAI")
    #         )
    #         return {**state, "llm_response": response}
    #     except Exception as e:
    #         logger.error(f"大模型调用失败: {str(e)}")
    #         return {**state, "error": str(e)}

    # async def extract_result_node(self, state: AgentState) -> AgentState:
    #     """结果提取节点"""
    #     logger.info('提取结构化结果')
    #     if "error" in state:
    #         return state
            
    #     try:
    #         # 从大模型响应中提取结构化数据
    #         content = state["llm_response"]["choices"][0]["message"]["content"]
    #         # 实现具体的结果解析逻辑
    #         return {**state, "output": self._parse_response(content)}
    #     except Exception as e:
    #         logger.error(f"结果解析失败: {str(e)}")
    #         return {**state, "error": str(e)}

    