# V1.2 此版本目标是完善知识库的管理功能

## 背景更新

- 系统知识库设计为，一个知识库可以有多个文件，一个文件可以有多个chunk，一个chunk可以有多个index。
- 知识库可以手动录入 chunk

相关定义：
```python
# 知识库 knowledge_base.py
class KnowledgeBase(Document):
    meta = {
        'collection': 'knowledge_bases'
    }
    _id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    name = StringField(required=True)
    description = StringField()
    created_at = DateTimeField(default=datetime.now)
    last_updated = DateTimeField(default=datetime.now)
    user_id = IntField(required=True)
    user_name = StringField()
    file_count = IntField(required=True, default=0)  # 存储关联的文件ID列表
    chunk_count = IntField(required=True, default=0)  # 存储关联的文本块数量
    index_count = IntField(required=True, default=0)  # 存储关联的索引数量
    is_active = BooleanField(required=True, default=True)
    embedding_model_id = IntField()
    embedding_model = StringField()
    basic_index = BooleanField(required=True, default=True)# 基础索引-向量检索
    graph_index = BooleanField(required=True, default=False)# 图索引
    semantic_index = BooleanField(required=True, default=False) # 语义索引

# 文件 source_files.py
class SourceFile(Document):
    meta = {
        'collection': 'source_files'
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId)
    knowledge_base_id = ObjectIdField(default=ObjectId)
    name = StringField(required=True)
    storage_path = StringField(required=True)
    storage_type = StringField(required=True, default=FileStorageType.LOCAL)
    data_type = StringField(required=True)  # 文件类型，如 pdf
    processing_status = StringField(default='pending')  # pending, processing, completed,error
    user_id = IntField(required=True)
    deleted_by = IntField(default=None)
    size = IntField(default=None)
    user_name = StringField()
    tags = ListField(StringField(), default=list)
    chunk_count = IntField(default=0)
    flg = IntField(required=True,default='0')  # 标记字段
    orc = IntField(required=True,default='0')  # 标记字段
    created_at = DateTimeField(default=datetime.now)
    basic_index = BooleanField(default=True)
    graph_index = BooleanField(default=False)

class Chunk(Document):
    meta = {
        'collection': 'chunks',
        'indexes': [
            {
                'fields': 'knowledge_base_id',
            },
            {
                'fields': 'file_id',
            },
            {
                'fields': ['answer', 'question', 'chunk_type'],
                'unique': True,
                'sparse': True,
                'background': True,
                'type': 'hashed'  # 使用哈希索引
            }
        ]
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId())
    file_id = ObjectIdField() # 问价名
    file_name = StringField() # 问价名
    page_id = StringField() # 页面id
    knowledge_base_id = ObjectIdField(required=True) # 知识库id
    chunk_index = IntField(required=True) # 排序
    answer = StringField(required=True,default="")
    question = StringField(required=True,default="")
    tokens_count = IntField(required=True,default=0)
    embedding = ListField(FloatField())
    metadata = DictField()
    chunk_type = StringField(required=True)
    created_at = DateTimeField(required=True,default=datetime.now)
    last_updated = DateTimeField(required=True,default=datetime.now)
    app_info = StringField()
    is_expired = BooleanField(required=True,default=False)  # 新增字段

class ChunkIndex(Document):
    meta = {
        'collection': 'chunks_index',
        'indexes': [
            {
                'fields': 'knowledge_base_id',
            },
            {
                'fields': 'file_id',
            },
            {
                'fields': ['file_id', 'index_content', 'chunk_type'],
                'unique': True,
                'sparse': True,
                'background': True,
                'type': 'hashed'  # 使用哈希索引
            }
        ]
    }
    _id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    file_id = ObjectIdField() # 问价名
    knowledge_base_id = ObjectIdField(required=True) # 知识库id
    chunk_id = ObjectIdField(required=True) # 知识库id
    embedding = ListField(FloatField())
    index_content = StringField(required=True)
    chunk_type = StringField(required=True)
    chunk_index = IntField(required=True) # 排序
    is_expired = BooleanField(default=False)  # 新增字段
    created_at = DateTimeField(default=datetime.now)
    combined_hash = StringField()

```


本版本的设计需求

1. 在知识库详情页 增加“手动录入” tab，其中是手动录入chunk。类似“文件管理”，但是是手动录入chunk。
2. 删除知识库详情页中的“知识索引”
3. 增加“检索测试”tab，可以检索测试知识库，
4. 点击“文件管理”中的一个文件的文件“查看”，进入文件详情，里面包括“chunk”和文件预览
5. 手动”录入信息“的chunk 和 文件管理chunk中的页面一致。
6. 
7. chunk功能页面，数据管理参考：
![image](image.png)






