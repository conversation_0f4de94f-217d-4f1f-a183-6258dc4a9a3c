[{"question": "2024年4月平均收益较去年同期增长率", "sql": "SELECT\n  IFNULL (\n    (`t39`.`_avg_profit` - `t29`.`_avg_profit`) / `t29`.`_avg_profit`,\n    0\n  ) * 100 AS `_growth_rate`\nFROM\n  (\n    SELECT\n      SUM(`balance` * `adRate`) / COUNT(DISTINCT `sys_imp_date`) AS `_avg_profit`\n    FROM\n      (\n        SELECT\n          `t21`.`sys_imp_date`,\n          `t21`.`lm_acct_loan_balance_balance` AS `balance`,\n          `t24`.`lm_contract_info_rate` / 36000 AS `adRate`\n        FROM\n          (\n            SELECT\n              `balance` AS `lm_acct_loan_balance_balance`,\n              `balancedate` AS `sys_imp_date`,\n              `CONTRACTCODE`,\n              `subaccountid`,\n              `accountno`\n            FROM\n              gxtest.lm_acct_balance\n            WHERE\n              `accounttype` = '自营贷款'\n              OR `accounttype` = '银团贷款'\n              OR `accounttype` = '委托贷款'\n          ) AS `t21`\n          RIGHT JOIN (\n            SELECT\n              `rate` AS `lm_contract_info_rate`,\n              `contractcode`,\n              `id`,\n              `enddate`\n            FROM\n              gxtest.lm_contract_info\n            GROUP BY\n              `enddate`,\n              `isassure`,\n              `ispledge`,\n              `startdate`,\n              `iscredit`,\n              `isimpawn`,\n              `contractcode`,\n              `consignclientid`,\n              `intervalnum`,\n              `consignclientname`,\n              `borrowclientcode`,\n              `borrowclientname`,\n              `consignclientcode`,\n              `overduerateruntype`,\n              `fixedinterest`,\n              `borrowclientid`,\n              `iscircle`,\n              `loanpurpose`,\n              `loantypeid`,\n              `interestrate`,\n              `statusid`,\n              `applycode`,\n              `rate`,\n              `interestid`,\n              `loansubtypeid`,\n              `name`,\n              `id`,\n              `examineamount`\n          ) AS `t24` ON `t21`.`CONTRACTCODE` = `t24`.`contractcode`\n      ) AS `t25`\n    WHERE\n      `t25`.`sys_imp_date` >= '2023-04-01'\n      AND `t25`.`sys_imp_date` <= '2023-04-30'\n  ) AS `t29`,\n  (\n    SELECT\n      SUM(`balance` * `adRate`) / COUNT(DISTINCT `sys_imp_date`) AS `_avg_profit`\n    FROM\n      (\n        SELECT\n          `t31`.`sys_imp_date`,\n          `t31`.`lm_acct_loan_balance_balance` AS `balance`,\n          `t34`.`lm_contract_info_rate` / 36000 AS `adRate`\n        FROM\n          (\n            SELECT\n              `balance` AS `lm_acct_loan_balance_balance`,\n              `balancedate` AS `sys_imp_date`,\n              `CONTRACTCODE`,\n              `subaccountid`,\n              `accountno`\n            FROM\n              gxtest.lm_acct_balance\n            WHERE\n              `accounttype` = '自营贷款'\n              OR `accounttype` = '银团贷款'\n              OR `accounttype` = '委托贷款'\n          ) AS `t31`\n          RIGHT JOIN (\n            SELECT\n              `rate` AS `lm_contract_info_rate`,\n              `contractcode`,\n              `id`,\n              `enddate`\n            FROM\n              gxtest.lm_contract_info\n            GROUP BY\n              `enddate`,\n              `isassure`,\n              `ispledge`,\n              `startdate`,\n              `iscredit`,\n              `isimpawn`,\n              `contractcode`,\n              `consignclientid`,\n              `intervalnum`,\n              `consignclientname`,\n              `borrowclientcode`,\n              `borrowclientname`,\n              `consignclientcode`,\n              `overduerateruntype`,\n              `fixedinterest`,\n              `borrowclientid`,\n              `iscircle`,\n              `loanpurpose`,\n              `loantypeid`,\n              `interestrate`,\n              `statusid`,\n              `applycode`,\n              `rate`,\n              `interestid`,\n              `loansubtypeid`,\n              `name`,\n              `id`,\n              `examineamount`\n          ) AS `t34` ON `t31`.`CONTRACTCODE` = `t34`.`contractcode`\n      ) AS `t35`\n    WHERE\n      `t35`.`sys_imp_date` >= '2024-04-01'\n      AND `t35`.`sys_imp_date` <= '2024-04-30'\n  ) AS `t39`\nlimit\n  1000\n", "ddl": "CREATE TABLE `lm_acct_balance` (\n  `accountflg` varchar(1) DEFAULT NULL COMMENT '账户类别标识（1内部账户，2银行账户）',\n  `accountid` int DEFAULT NULL COMMENT '账户ID',\n  `accounttype` varchar(50) DEFAULT NULL COMMENT '账户类型',\n  `accountno` varchar(50) DEFAULT NULL COMMENT '账号',\n  `acountname` varchar(200) DEFAULT NULL COMMENT '户名',\n  `bankcode` varchar(50) DEFAULT NULL COMMENT '开户行编号',\n  `bankname` varchar(100) DEFAULT NULL COMMENT '开户行名称',\n  `clientcode` varchar(10) DEFAULT NULL COMMENT '客户编号',\n  `clientname` varchar(200) DEFAULT NULL COMMENT '客户名称',\n  `currencyname` varchar(20) DEFAULT NULL COMMENT '币种',\n  `balancedate` varchar(25) DEFAULT NULL COMMENT '余额日期',\n  `balance` decimal(19,2) DEFAULT NULL COMMENT '余额',\n  `negotiatebalance` decimal(19,2) DEFAULT NULL COMMENT '协定余额',\n  `subaccountid` int DEFAULT NULL COMMENT '子账户ID',\n  `depositno` varchar(50) DEFAULT NULL COMMENT '存单编号',\n  `CONTRACTCODE` varchar(50) DEFAULT NULL COMMENT '合同号',\n  `payformcode` varchar(50) DEFAULT NULL COMMENT '放款通知单编号',\n  KEY `acountno` (`accountno`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='余额表：内部户余额、银行户余额、存单余额、放款单余额、贷款余额'\nCREATE TABLE `lm_contract_info` (\n  `id` int DEFAULT NULL COMMENT '合同ID',\n  `contractcode` varchar(50) DEFAULT NULL COMMENT '合同号',\n  `loantypeid` int DEFAULT NULL COMMENT '贷款类型',\n  `loansubtypeid` int DEFAULT NULL COMMENT '贷款子类型',\n  `name` varchar(20) DEFAULT NULL COMMENT '币种',\n  `applycode` varchar(50) DEFAULT NULL COMMENT '贷款申请编号',\n  `borrowclientid` int DEFAULT NULL COMMENT '借款单位ID',\n  `borrowclientcode` varchar(50) DEFAULT NULL COMMENT '借款单位编号',\n  `borrowclientname` varchar(200) DEFAULT NULL COMMENT '借款单位名称',\n  `consignclientid` varchar(20) DEFAULT NULL COMMENT '委托单位ID',\n  `consignclientcode` varchar(20) DEFAULT NULL COMMENT '委托单位编号',\n  `consignclientname` varchar(200) DEFAULT NULL COMMENT '委托单位名称',\n  `loanpurpose` varchar(500) DEFAULT NULL COMMENT '借款用途',\n  `iscircle` varchar(50) DEFAULT NULL COMMENT '是否循环贷款',\n  `statusid` int DEFAULT NULL COMMENT '合同状态',\n  `iscredit` varchar(5) DEFAULT NULL COMMENT '是否信用',\n  `isassure` varchar(5) DEFAULT NULL COMMENT '是否保证',\n  `ispledge` varchar(5) DEFAULT NULL COMMENT '是否抵押',\n  `isimpawn` varchar(5) DEFAULT NULL COMMENT '是否质押',\n  `examineamount` decimal(19,2) DEFAULT NULL COMMENT '金额',\n  `intervalnum` int DEFAULT NULL COMMENT '期限',\n  `startdate` varchar(20) DEFAULT NULL COMMENT '贷款开始时间',\n  `enddate` varchar(20) DEFAULT NULL COMMENT '贷款结束时间',\n  `interestid` int DEFAULT NULL COMMENT '基准利率ID',\n  `interestrate` decimal(19,4) DEFAULT NULL COMMENT '基准利率',\n  `rate` decimal(19,4) DEFAULT NULL COMMENT '执行利率',\n  `overduerateruntype` varchar(10) DEFAULT NULL COMMENT '利率类型',\n  `fixedinterest` decimal(19,4) DEFAULT NULL COMMENT '固定利率值'\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='合同表：贷款类型、自营贷款、银团贷款、委托贷款、保函、合同期限、担保类型、合同状态'\n"}]