from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.mediaInsightsReport import (
    MediaInsightsReportCreate,
    MediaInsightsReportUpdate,
    MediaInsightsReportResponse
)
from ..db.mongodb import db
from datetime import datetime
from ..utils.auth import verify_token
from pydantic import ValidationError
from bson import ObjectId
from app.utils.logging_config import setup_logging, get_logger
import traceback
import os
import mimetypes
from fastapi.responses import StreamingResponse
import httpx
import aiofiles
from urllib.parse import quote
from ..utils.config import settings
from ..db.miniIO import minio

setup_logging()
logger = get_logger(__name__)

router = APIRouter(
    prefix="/api",
    tags=["media_insights_reports"]
)

# 创建新报告
@router.post("/media_insights_reports", response_model=Dict[str, Any])
async def create_report(report: MediaInsightsReportCreate, current_user: dict = Depends(verify_token)):
    try:
        new_report = report.dict()
        new_id = ObjectId()
        new_report.update({
            "_id": new_id,
            "created_at": datetime.now()
        })
        
        await db["media_insights_reports"].insert_one(new_report)
        created_report = await db["media_insights_reports"].find_one({"_id": new_id})
        created_report["id"] = str(created_report["_id"])
        del created_report["_id"]
        
        logger.info(f"新报告创建成功: {created_report}")
        return {
            "data": MediaInsightsReportResponse(**created_report),
            "success": True,
        }
    except ValidationError as ve:
        logger.error(f"验证错误: {ve.json()}")
        raise HTTPException(status_code=422, detail=ve.errors())
    except Exception as e:
        logger.error(f"创建报告时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

# 获取报告列表
@router.get("/media_insights_reports", response_model=Dict[str, Any])
async def read_reports(
    current: int = Query(1, description="当前页码"),
    pageSize: int = Query(10, description="每页数量"),
    title: Optional[str] = None,
    report_type: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    try:
        skip = (current - 1) * pageSize
        query = {}
        
        if title:
            query["title"] = {"$regex": title, "$options": "i"}
        if report_type:
            query["report_type"] = report_type
        
        reports = await db["media_insights_reports"].find(query).sort("end_time", -1).skip(skip).limit(pageSize).to_list(pageSize)
        total = await db["media_insights_reports"].count_documents(query)
        
        for report in reports:
            report["id"] = str(report["_id"])
            del report["_id"]
        
        return {
            "data": reports,
            "total": total,
            "success": True,
            "pageSize": pageSize,
            "current": current
        }
    except Exception as e:
        logger.error(f"获取报告列表时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

# 获取单个报告
@router.get("/media_insights_reports/{report_id}", response_model=MediaInsightsReportResponse)
async def read_report(report_id: str, current_user: dict = Depends(verify_token)):
    try:
        report = await db["media_insights_reports"].find_one({"_id": ObjectId(report_id)})
        if report is None:
            raise HTTPException(status_code=404, detail="报告未找到")
        
        report["id"] = str(report["_id"])
        del report["_id"]
        return MediaInsightsReportResponse(**report)
    except Exception as e:
        logger.error(f"获取报告时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

# 更新报告
@router.put("/media_insights_reports/{report_id}", response_model=MediaInsightsReportResponse)
async def update_report(report_id: str, report: MediaInsightsReportUpdate, current_user: dict = Depends(verify_token)):
    try:
        update_data = report.dict(exclude_unset=True)
        result = await db["media_insights_reports"].update_one(
            {"_id": ObjectId(report_id)}, 
            {"$set": update_data}
        )
        
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="报告未找到")
        
        updated_report = await db["media_insights_reports"].find_one({"_id": ObjectId(report_id)})
        updated_report["id"] = str(updated_report["_id"])
        del updated_report["_id"]
        return MediaInsightsReportResponse(**updated_report)
    except Exception as e:
        logger.error(f"更新报告时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

# 删除报告
@router.delete("/media_insights_reports/{report_id}", response_model=Dict[str, Any])
async def delete_report(report_id: str, current_user: dict = Depends(verify_token)):
    try:
        result = await db["media_insights_reports"].delete_one({"_id": ObjectId(report_id)})
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="报告未找到")
        
        logger.info(f"报告删除成功: report_id={report_id}")
        return {"success": True, "id": report_id}
    except Exception as e:
        logger.error(f"删除报告时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除报告失败: {str(e)}")

# 批量删除报告
@router.delete("/delete_media_insights_reports", response_model=Dict[str, Any])
async def delete_reports(report_ids: List[str], current_user: dict = Depends(verify_token)):
    try:
        object_ids = [ObjectId(id) for id in report_ids]
        result = await db["media_insights_reports"].delete_many({"_id": {"$in": object_ids}})
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="未找到匹配的报告")
        
        logger.info(f"批量删除报告成功: 数量={result.deleted_count}")
        return {"success": True, "deleted_count": result.deleted_count}
    except Exception as e:
        logger.error(f"批量删除报告时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量删除失败: {str(e)}")

# 下载媒体洞察报告
@router.get("/media_insights_report_download/{report_id}")
async def download_report(
    report_id: str,
    current_user: dict = Depends(verify_token)
):
    try:
        report = await db["media_insights_reports"].find_one({"_id": ObjectId(report_id)})
        if not report:
            raise HTTPException(status_code=404, detail="报告未找到")
        
        storage_path = report.get("storage_path")
        original_filename = report.get("title", "report") + ".pdf"  # 默认使用报告标题作为文件名
        
        if not storage_path:
            # 如果报告没有存储路径，可能是动态生成的报告，需要先生成
            # 这里可以添加动态生成报告的逻辑
            raise HTTPException(status_code=404, detail="报告文件未找到")

        # 获取文件MIME类型
        mime_type = mimetypes.guess_type(original_filename)[0] or "application/pdf"

        # 设置 Content-Disposition，强制下载
        encoded_filename = quote(original_filename.encode('utf-8'))
        content_disposition = f'attachment; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}'
        
        logger.info(f"下载报告: {report_id}, 文件名: {original_filename}")

        if settings.MINIO_ENABLE and storage_path:
            try:
                async with httpx.AsyncClient(timeout=30.0) as client:
                    try:
                        response = await client.get(storage_path)
                        if response.status_code == 403:  # URL已过期
                            object_name = storage_path.split('/')[-1].split('?')[0]
                            storage_path = minio.client.presigned_get_object(
                                settings.MINIO_BUCKET,
                                object_name,
                                expires=3600
                            )
                            response = await client.get(storage_path)
                            
                        response.raise_for_status()
                        
                        return StreamingResponse(
                            content=response.iter_bytes(),
                            media_type=mime_type,
                            headers={
                                "Content-Disposition": content_disposition,
                                "Cache-Control": "no-cache",
                                "X-Content-Type-Options": "nosniff",
                            }
                        )
                    except httpx.HTTPError as http_err:
                        logger.error(f"HTTP error occurred: {str(http_err)}")
                        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(http_err)}")
                        
            except Exception as e:
                logger.error(f"下载错误: {str(e)}")
                raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")
        else:
            # 本地文件处理
            file_path = os.path.join(settings.FILE_UPLOAD_PATH, os.path.basename(storage_path))
            if not os.path.exists(file_path):
                raise HTTPException(status_code=404, detail="文件在磁盘上未找到")
            
            if not os.path.isfile(file_path):
                raise HTTPException(status_code=400, detail="无效的文件路径")
                
            async def file_iterator():
                async with aiofiles.open(file_path, 'rb') as f:
                    while chunk := await f.read(8192):
                        yield chunk
            
            return StreamingResponse(
                content=file_iterator(),
                media_type=mime_type,
                headers={
                    "Content-Disposition": content_disposition,
                    "Cache-Control": "no-cache",
                    "X-Content-Type-Options": "nosniff",
                }
            )
            
    except Exception as e:
        traceback.print_exc()
        logger.error(f"下载报告错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载报告错误: {str(e)}")
