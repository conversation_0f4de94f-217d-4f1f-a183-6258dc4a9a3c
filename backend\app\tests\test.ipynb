{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tiktoken\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "Unknown encoding ../source/o200k_base.tiktoken.\nPlugins found: ['tiktoken_ext.openai_public']\ntiktoken version: 0.8.0 (are you on latest?)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 7\u001b[0m\n\u001b[1;32m      4\u001b[0m file_path \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m../source/o200k_base.tiktoken\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m      6\u001b[0m \u001b[38;5;66;03m# 加载本地分词表\u001b[39;00m\n\u001b[0;32m----> 7\u001b[0m encoder \u001b[38;5;241m=\u001b[39m \u001b[43mtiktoken\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_encoding\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfile_path\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;66;03m# 使用分词器进行分词\u001b[39;00m\n\u001b[1;32m     10\u001b[0m text \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mHello, how are you?\u001b[39m\u001b[38;5;124m\"\u001b[39m\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/tiktoken/registry.py:79\u001b[0m, in \u001b[0;36mget_encoding\u001b[0;34m(encoding_name)\u001b[0m\n\u001b[1;32m     76\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m ENCODING_CONSTRUCTORS \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m     78\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m encoding_name \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ENCODING_CONSTRUCTORS:\n\u001b[0;32m---> 79\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m     80\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnknown encoding \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mencoding_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     81\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPlugins found: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m_available_plugin_modules()\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     82\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtiktoken version: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtiktoken\u001b[38;5;241m.\u001b[39m__version__\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m (are you on latest?)\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     83\u001b[0m     )\n\u001b[1;32m     85\u001b[0m constructor \u001b[38;5;241m=\u001b[39m ENCODING_CONSTRUCTORS[encoding_name]\n\u001b[1;32m     86\u001b[0m enc \u001b[38;5;241m=\u001b[39m Encoding(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mconstructor())\n", "\u001b[0;31mValueError\u001b[0m: Unknown encoding ../source/o200k_base.tiktoken.\nPlugins found: ['tiktoken_ext.openai_public']\ntiktoken version: 0.8.0 (are you on latest?)"]}], "source": ["import tiktoken\n", "\n", "# 指定本地分词表文件路径\n", "file_path = \"../source/o200k_base.tiktoken\"\n", "\n", "# 加载本地分词表\n", "encoder = tiktoken.get_encoding(file_path)\n", "\n", "# 使用分词器进行分词\n", "text = \"Hello, how are you?\"\n", "tokens = encoder.encode(text)\n", "\n", "print(tokens)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import tiktoken\n", "import os\n", "\n", "\n", "enc = tiktoken.encoding_for_model(\"gpt-4o-mini\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["len(enc.encode(\"你好\"))"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["['__class__',\n", " '__delattr__',\n", " '__dict__',\n", " '__dir__',\n", " '__doc__',\n", " '__eq__',\n", " '__format__',\n", " '__ge__',\n", " '__getattribute__',\n", " '__getstate__',\n", " '__gt__',\n", " '__hash__',\n", " '__init__',\n", " '__init_subclass__',\n", " '__le__',\n", " '__lt__',\n", " '__module__',\n", " '__ne__',\n", " '__new__',\n", " '__reduce__',\n", " '__reduce_ex__',\n", " '__repr__',\n", " '__setattr__',\n", " '__setstate__',\n", " '__sizeof__',\n", " '__str__',\n", " '__subclasshook__',\n", " '__weakref__',\n", " '_core_bpe',\n", " '_encode_bytes',\n", " '_encode_only_native_bpe',\n", " '_encode_single_piece',\n", " '_mergeable_ranks',\n", " '_pat_str',\n", " '_special_tokens',\n", " 'decode',\n", " 'decode_batch',\n", " 'decode_bytes',\n", " 'decode_bytes_batch',\n", " 'decode_single_token_bytes',\n", " 'decode_tokens_bytes',\n", " 'decode_with_offsets',\n", " 'encode',\n", " 'encode_batch',\n", " 'encode_ordinary',\n", " 'encode_ordinary_batch',\n", " 'encode_single_token',\n", " 'encode_with_unstable',\n", " 'eot_token',\n", " 'max_token_value',\n", " 'n_vocab',\n", " 'name',\n", " 'special_tokens_set',\n", " 'token_byte_values']"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["dir(enc)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["'o200k_base'"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["<module 'tiktoken' from '/Users/<USER>/miniforge3/envs/py311/lib/python3.11/site-packages/tiktoken/__init__.py'>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["<function tiktoken.registry.list_encoding_names() -> 'list[str]'>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["<module 'tiktoken.load' from '/Users/<USER>/miniforge3/envs/py311/lib/python3.11/site-packages/tiktoken/load.py'>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["import tiktoken.load\n", "\n", "\n", "tiktoken.load"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'gpt-4o': 'o200k_base', 'gpt-4': 'cl100k_base', 'gpt-3.5-turbo': 'cl100k_base', 'gpt-3.5': 'cl100k_base', 'gpt-35-turbo': 'cl100k_base', 'davinci-002': 'cl100k_base', 'babbage-002': 'cl100k_base', 'text-embedding-ada-002': 'cl100k_base', 'text-embedding-3-small': 'cl100k_base', 'text-embedding-3-large': 'cl100k_base', 'text-davinci-003': 'p50k_base', 'text-davinci-002': 'p50k_base', 'text-davinci-001': 'r50k_base', 'text-curie-001': 'r50k_base', 'text-babbage-001': 'r50k_base', 'text-ada-001': 'r50k_base', 'davinci': 'r50k_base', 'curie': 'r50k_base', 'babbage': 'r50k_base', 'ada': 'r50k_base', 'code-davinci-002': 'p50k_base', 'code-davinci-001': 'p50k_base', 'code-cushman-002': 'p50k_base', 'code-cushman-001': 'p50k_base', 'davinci-codex': 'p50k_base', 'cushman-codex': 'p50k_base', 'text-davinci-edit-001': 'p50k_edit', 'code-davinci-edit-001': 'p50k_edit', 'text-similarity-davinci-001': 'r50k_base', 'text-similarity-curie-001': 'r50k_base', 'text-similarity-babbage-001': 'r50k_base', 'text-similarity-ada-001': 'r50k_base', 'text-search-davinci-doc-001': 'r50k_base', 'text-search-curie-doc-001': 'r50k_base', 'text-search-babbage-doc-001': 'r50k_base', 'text-search-ada-doc-001': 'r50k_base', 'code-search-babbage-code-001': 'r50k_base', 'code-search-ada-code-001': 'r50k_base', 'gpt2': 'gpt2', 'gpt-2': 'gpt2'}\n"]}], "source": ["print(tiktoken.model.MODEL_TO_ENCODING)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}