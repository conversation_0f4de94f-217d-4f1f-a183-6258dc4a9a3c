from typing import List, Dict, Any, Optional
from lmnr_flow import Flow, NextTask, TaskOutput
from ..rerank.rerank_utils import rerank_texts, RerankResult
from app.utils.logging_config import setup_logging, get_logger
from concurrent.futures import ThreadPoolExecutor

# 设置日志
setup_logging()
logger = get_logger(__name__)

class FlowRAG:
    def __init__(self, config: Dict[str, Any]):
        """
        初始化 RAG 流程
        
        Args:
            config: 配置信息，包含:
                - vector_search: 是否启用向量搜索
                - graph_search: 是否启用图搜索
                - rerank_config: 重排序配置
                - top_k: 每路召回的数量
                - final_top_k: 最终返回的数量
        """
        self.config = config
        # 创建一个线程池执行器
        self.thread_pool_executor = ThreadPoolExecutor(max_workers=5)
        self.flow = Flow(thread_pool_executor=self.thread_pool_executor)
        self._setup_flow()

    def _setup_flow(self):
        """设置 Flow 任务"""
        # 添加任务节点
        self.flow.add_task("router", self._route_task)
        self.flow.add_task("vector_search", self._vector_search_task)
        self.flow.add_task("graph_search", self._graph_search_task) 
        self.flow.add_task("merge_results", self._merge_results_task)
        self.flow.add_task("rerank", self._rerank_task)

    def _route_task(self, ctx) -> TaskOutput:
        """路由任务，决定使用哪些召回方式"""
        logger.info('调用 ROUTE')
        query = ctx.get("query")
        tasks = []

        if self.config.get("vector_search"):
            tasks.append(NextTask("vector_search", {"query": query}))
            
        if self.config.get("graph_search"):
            tasks.append(NextTask("graph_search", {"query": query}))

        tasks.append(NextTask("merge_results"))
        return TaskOutput(next_tasks=tasks)

    def _vector_search_task(self, ctx) -> TaskOutput:
        """向量搜索任务"""
        logger.info('调用 VECTOR_SEARCH')
        query = ctx.get("query")
        try:
            # 这里实现向量搜索逻辑
            # results = await vector_search(query, top_k=self.config.get("top_k", 10))
            results = []  # 替换为实际的向量搜索结果
            ctx.set("vector_results", results)
            return TaskOutput(next_tasks=[NextTask("merge_results")])
        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}")
            return TaskOutput(error=str(e))

    def _graph_search_task(self, ctx) -> TaskOutput:
        """图搜索任务"""
        logger.info('调用 GRAPH_SEARCH')
        query = ctx.get("query")
        try:
            # 这里实现图搜索逻辑
            # results = await graph_search(query, top_k=self.config.get("top_k", 10))
            results = []  # 替换为实际的图搜索结果
            ctx.set("graph_results", results)
            return TaskOutput(next_tasks=[NextTask("merge_results")])
        except Exception as e:
            logger.error(f"Graph search failed: {str(e)}")
            return TaskOutput(error=str(e))

    def _merge_results_task(self, ctx) -> TaskOutput:
        """合并搜索结果"""
        try:
            logger.info('调用 MERGE_RESULTS')
            all_results = []
            
            # 获取向量搜索结果
            vector_results = ctx.get("vector_results", [])
            all_results.extend(vector_results)
            
            # 获取图搜索结果
            graph_results = ctx.get("graph_results", [])
            all_results.extend(graph_results)
            
            # 去重
            seen = set()
            unique_results = []
            for result in all_results:
                if result.get("id"):
                    if result["id"] not in seen:
                        seen.add(result["id"])
                        unique_results.append(result)
                else:
                    unique_results.append(result)
            
            ctx.set("merged_results", unique_results)
            return TaskOutput(next_tasks=[NextTask("rerank")])
        except Exception as e:
            logger.error(f"Merge results failed: {str(e)}")
            return TaskOutput(error=str(e))

    async def _rerank_task(self, ctx) -> TaskOutput:
        """重排序任务"""
        try:
            logger.info('调用 RERANK')
            query = ctx.get("query")
            merged_results = ctx.get("merged_results", [])
            
            # 准备重排序数据
            texts = [result["content"] for result in merged_results]
            
            # 调用重排序
            rerank_results: List[RerankResult] = await rerank_texts(
                query=query,
                texts=texts,
                config=self.config.get("rerank_config", {}),
                top_k=self.config.get("final_top_k", 5)
            )
            
            # 将重排序分数合并回原始结果
            final_results = []
            for rank_result, orig_result in zip(rerank_results, merged_results):
                final_results.append({
                    **orig_result,
                    "score": rank_result.score
                })
            
            # 按分数排序
            final_results.sort(key=lambda x: x["score"], reverse=True)
            
            # 只返回指定数量的结果
            final_results = final_results[:self.config.get("final_top_k", 5)]
            
            return TaskOutput(output=final_results)
        except Exception as e:
            logger.error(f"Rerank failed: {str(e)}")
            return TaskOutput(error=str(e))

    async def run(self, query: str) -> List[Dict[str, Any]]:
        """
        运行 RAG 流程
        
        Args:
            query: 查询文本
            
        Returns:
            List[Dict[str, Any]]: 排序后的召回结果
        """
        try:
            logger.info('调用 启动 FLOW')
            logger.info(query)
            result = await self.flow.run("router", inputs={"query": query})
            if isinstance(result, dict) and "error" in result:
                logger.error(f"Flow execution failed: {result}")
                return []
            return result
        except Exception as e:
            logger.error(f"Flow execution failed: {str(e)}")
            return []
