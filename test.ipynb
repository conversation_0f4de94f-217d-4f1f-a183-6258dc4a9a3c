{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "# 添加父目录到路径，以便导入 logging_config\n", "current_dir = os.path.dirname(os.path.abspath(__file__))\n", "parent_dir = os.path.dirname(current_dir)\n", "if parent_dir not in sys.path:\n", "    sys.path.append(parent_dir)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>suggestion</th>\n", "      <th>exception</th>\n", "      <th>big_tag</th>\n", "      <th>tag</th>\n", "      <th>use</th>\n", "      <th>z<PERSON>yin</th>\n", "      <th>xizang</th>\n", "      <th>yin<PERSON>xin</th>\n", "      <th>bing<PERSON><PERSON><PERSON></th>\n", "      <th>min<PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>regular</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>践行社会主义荣辱观</td>\n", "      <td>践行(“|‘|\"|')八荣八耻(”|’|\"|')</td>\n", "      <td>政治性</td>\n", "      <td>严重表述错误</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>“村主任”或“村民委员会主任”</td>\n", "      <td>村长</td>\n", "      <td>政治性</td>\n", "      <td>严重表述错误</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>伊斯兰激进组织</td>\n", "      <td>激进伊斯兰组织</td>\n", "      <td>技术性</td>\n", "      <td>表述不规范</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>伊斯兰激进成员</td>\n", "      <td>激进伊斯兰成员</td>\n", "      <td>技术性</td>\n", "      <td>表述不规范</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>The Democratic People s Republic of Korea</td>\n", "      <td>North Korea</td>\n", "      <td>政治性</td>\n", "      <td>严重表述错误</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>外岛</td>\n", "      <td>台湾离岛</td>\n", "      <td>政治性</td>\n", "      <td>严重表述错误</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>穆斯林宰牛羊及家禽</td>\n", "      <td>穆斯林杀牛羊及家禽</td>\n", "      <td>政治性</td>\n", "      <td>严重表述错误</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>新疆</td>\n", "      <td>(?&lt;!(不是|妄想|禁止|不要).{0,5})东突厥斯坦</td>\n", "      <td>政治性</td>\n", "      <td>严重表述错误</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  suggestion                      exception  \\\n", "0                                  践行社会主义荣辱观       践行(“|‘|\"|')八荣八耻(”|’|\"|')   \n", "1                            “村主任”或“村民委员会主任”                             村长   \n", "2                                    伊斯兰激进组织                        激进伊斯兰组织   \n", "3                                    伊斯兰激进成员                        激进伊斯兰成员   \n", "4  The Democratic People s Republic of Korea                    North Korea   \n", "5                                         外岛                           台湾离岛   \n", "6                                  穆斯林宰牛羊及家禽                      穆斯林杀牛羊及家禽   \n", "7                                         新疆  (?<!(不是|妄想|禁止|不要).{0,5})东突厥斯坦   \n", "\n", "  big_tag     tag  use  zhanyin  xizang  yinbaoxin  bing<PERSON>uan  \\\n", "0     政治性  严重表述错误    1        1       1          1             1   \n", "1     政治性  严重表述错误    1        1       1          1             1   \n", "2     技术性   表述不规范    1        1       1          1             1   \n", "3     技术性   表述不规范    1        1       1          1             1   \n", "4     政治性  严重表述错误    1        1       1          1             1   \n", "5     政治性  严重表述错误    1        1       1          1             1   \n", "6     政治性  严重表述错误    1        1       1          1             1   \n", "7     政治性  严重表述错误    1        1       1          1             1   \n", "\n", "   mings<PERSON><PERSON><PERSON>hang  regular  \n", "0                 1      1.0  \n", "1                 1      NaN  \n", "2                 1      NaN  \n", "3                 1      NaN  \n", "4                 1      NaN  \n", "5                 1      NaN  \n", "6                 1      NaN  \n", "7                 1      1.0  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["path = \"./data/add_rules_20230418.xlsx\"  \n", "path = \"./data/修改纠错.xlsx\"\n", "path = \"./data/add_rules_20230531.xlsx\"\n", "path = \"./data/add_rules_20230718.xlsx\"\n", "path = \"./data/modify_rules_20240402.xlsx\"\n", "path = \"./data/add_rules_20240314.xlsx\"\n", "path = \"/Users/<USER>/Desktop/modify_rules_20240524.xlsx\"\n", "# path = \"./data/add_rules0830.xlsx\"\n", "# df = pd.read_excel(path,sheet_name=\"新增\")\n", "# df = df.drop(columns=['Unnamed: 0'])\n", "df = pd.read_excel(path)\n", "# df = pd.read_excel(path,sheet_name=\"add_rules\")\n", "# df = pd.read_excel(path,sheet_name=\"delete_rules\")\n", "# df = df.drop(columns=['序号'])\n", "# df = df.drop(columns=['regular'])\n", "df = df.drop(columns=['Unnamed: 0'])\n", "df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["df['use'] = 0\n", "df['regular'] = 1"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "orient 'record' not understood", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m rule_lists \u001b[38;5;241m=\u001b[39m \u001b[43mdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mto_dict\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mrecord\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/Users/<USER>/program/miniconda3/envs/punish/lib/python3.10/site-packages/pandas/core/frame.py:2039\u001b[0m, in \u001b[0;36mDataFrame.to_dict\u001b[0;34m(self, orient, into, index)\u001b[0m\n\u001b[1;32m   1937\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1938\u001b[0m \u001b[38;5;124;03mConvert the DataFrame to a dictionary.\u001b[39;00m\n\u001b[1;32m   1939\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   2035\u001b[0m \u001b[38;5;124;03m defaultdict(<class 'list'>, {'col1': 2, 'col2': 0.75})]\u001b[39;00m\n\u001b[1;32m   2036\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   2037\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mp<PERSON>s\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmethods\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mto_dict\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m to_dict\n\u001b[0;32m-> 2039\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mto_dict\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43morient\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minto\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindex\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/Users/<USER>/program/miniconda3/envs/punish/lib/python3.10/site-packages/pandas/core/methods/to_dict.py:211\u001b[0m, in \u001b[0;36mto_dict\u001b[0;34m(df, orient, into, index)\u001b[0m\n\u001b[1;32m    206\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m into_c(\n\u001b[1;32m    207\u001b[0m             (t[\u001b[38;5;241m0\u001b[39m], \u001b[38;5;28mdict\u001b[39m(\u001b[38;5;28mzip\u001b[39m(df\u001b[38;5;241m.\u001b[39mcolumns, t[\u001b[38;5;241m1\u001b[39m:]))) \u001b[38;5;28;01mfor\u001b[39;00m t \u001b[38;5;129;01min\u001b[39;00m df\u001b[38;5;241m.\u001b[39mitertuples(name\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[1;32m    208\u001b[0m         )\n\u001b[1;32m    210\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m--> 211\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124morient \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00morient\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m not understood\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>r\u001b[0m: orient 'record' not understood"]}], "source": ["rule_lists = df.to_dict(\"record\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始测试 DeepSeek-R1-Distill-Qwen-32B 模型...\n", "\n", "1. 测试单次请求\n", "单次请求失败：Expecting value: line 1 column 1 (char 0)\n"]}], "source": ["# 测试LLM连接\n", "import requests\n", "import json\n", "import time\n", "\n", "# 配置\n", "name = \"DeepSeek-R1-<PERSON><PERSON><PERSON>-Qwen-32B\"\n", "BASE_URL = \"http://110.80.151.195:1025/v1/chat/completions\"\n", "API_KEY = \"none\"\n", "\n", "def single_request(prompt: str) -> dict:\n", "    \"\"\"单次请求\"\"\"\n", "    try:\n", "        headers = {\n", "            \"Content-Type\": \"application/json\",\n", "            \"Authorization\": f\"Bearer {API_KEY}\"\n", "        }\n", "        \n", "        payload = {\n", "            \"model\": name,\n", "            \"messages\": [{\"role\": \"user\", \"content\": prompt}],\n", "            \"temperature\": 0.7,\n", "            \"max_tokens\": 1024,\n", "            \"stream\": False\n", "        }\n", "        start_time = time.time()\n", "        response = requests.post(\n", "            BASE_URL,\n", "            headers=headers,\n", "            json=payload,\n", "            timeout=30  # 设置超时时间\n", "        )\n", "        end_time = time.time()\n", "        response_time = (end_time - start_time) * 1000\n", "        if response.status_code == 200:\n", "            result = response.json()\n", "            return {\n", "                \"success\": True,\n", "                \"content\": result[\"choices\"][0][\"message\"][\"content\"],\n", "                \"time\": response_time\n", "            }\n", "        else:\n", "            return {\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"error\": f\"HTTP {response.status_code}: {response.text}\"\n", "            }\n", "            \n", "    except Exception as e:\n", "        return {\n", "            \"success\": <PERSON><PERSON><PERSON>,\n", "            \"error\": str(e)\n", "        }\n", "\n", "if __name__ == \"__main__\":\n", "    print(f\"开始测试 {name} 模型...\")\n", "    \n", "    # 测试单次请求\n", "    print(\"\\n1. 测试单次请求\")\n", "    result = single_request(\"你好，请用一句话介绍你自己\")\n", "    if result[\"success\"]:\n", "        print(\"单次请求成功！\")\n", "        print(f\"响应内容：{result['content']}\")\n", "        print(f\"响应时间/毫秒：{result['time']}\")\n", "    else:\n", "        print(f\"单次请求失败：{result['error']}\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["测试首字响应速度...\n", "<think>\n", "首字响应速度: 0.2242 秒\n", "\n", "测试模型并发性能和字符吞吐量，并发请求数量: 20 ...\n", "响应时间 1.2273447513580322\n", "响应时间 1.2183339595794678\n", "响应时间 1.2423436641693115\n", "响应时间 1.3015296459197998\n", "响应时间响应时间 2.1494510173797607\n", " 2.1484508514404297\n", "响应时间 2.1554722785949707\n", "响应时间 2.152452230453491\n", "响应时间 2.1604533195495605\n", "响应时间 2.154451608657837\n", "响应时间 2.170452356338501\n", "响应时间 2.154454469680786\n", "响应时间 2.166452407836914\n", "响应时间 2.1804487705230713\n", "响应时间 2.180464267730713\n", "响应时间 2.221438407897949\n", "响应时间 2.2334625720977783\n", "响应时间 2.239448070526123\n", "响应时间 2.218440532684326\n", "响应时间 2.2324635982513428\n", "总的响应时间: 39.9078 秒\n", "平均响应时间: 1.9954 秒\n", "字符吞吐量: 66.45 字符/秒\n"]}], "source": ["import requests\n", "import concurrent.futures\n", "import time\n", "\n", "# 配置\n", "name = \"DeepSeek-R1-<PERSON><PERSON><PERSON>-Qwen-32B\"\n", "BASE_URL = \"http://110.80.151.195:1025/v1/chat/completions\"\n", "API_KEY = \"none\"\n", "\n", "def measure_first_byte_response_time():\n", "    \"\"\"测试首字响应速度\"\"\"\n", "    headers = {\n", "        \"Content-Type\": \"application/json\",\n", "        \"Authorization\": f\"Bearer {API_KEY}\"\n", "    }\n", "    \n", "    payload = {\n", "        \"model\": name,\n", "        \"messages\": [{\"role\": \"user\", \"content\": \"你好\"}],\n", "        \"max_tokens\": 1024,\n", "        \"stream\": True\n", "    }\n", "    \n", "    start_time = time.time()\n", "    try:\n", "        with requests.post(\n", "            BASE_URL,\n", "            headers=headers,\n", "            json=payload,\n", "            stream=True,\n", "            timeout=30\n", "        ) as response:\n", "            for line in response.iter_lines():\n", "                if line:\n", "                    # 去掉 \"data: \" 前缀并解析 JSON\n", "                    if line.startswith(b\"data: \"):\n", "                        json_str = line.decode('utf-8')[6:]\n", "                        if json_str.strip() == \"[DONE]\":\n", "                            continue\n", "                        chunk = json.loads(json_str)\n", "                        if chunk['choices'][0]['delta'].get('content'):\n", "                            first_byte_time = time.time() - start_time\n", "                            print(chunk['choices'][0]['delta']['content'], end=\"\", flush=True)\n", "                            return first_byte_time\n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        return None\n", "\n", "def send_request():\n", "    \"\"\"发送单个请求并测量响应时间\"\"\"\n", "    headers = {\n", "        \"Content-Type\": \"application/json\",\n", "        \"Authorization\": f\"Bearer {API_KEY}\"\n", "    }\n", "    \n", "    payload = {\n", "        \"model\": name,\n", "        \"messages\": [{\"role\": \"user\", \"content\": content}],\n", "        \"max_tokens\": 1024,\n", "        \"stream\": <PERSON><PERSON><PERSON>,\n", "        \"stop\": []\n", "    }\n", "    \n", "    start_time = time.time()\n", "    try:\n", "        response = requests.post(\n", "            BASE_URL,\n", "            headers=headers,\n", "            json=payload,\n", "            timeout=30\n", "        )\n", "        response_time = time.time() - start_time\n", "        \n", "        if response.status_code == 200:\n", "            result = response.json()\n", "            response_text = result[\"choices\"][0][\"message\"][\"content\"]\n", "            print(\"响应时间\", response_time)\n", "            return {\n", "                \"response_time\": response_time,\n", "                \"response_text\": response_text\n", "            }\n", "        else:\n", "            print(f\"请求失败: {response.status_code}\")\n", "            return {\n", "                \"response_time\": response_time,\n", "                \"response_text\": \"\"\n", "            }\n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        return {\n", "            \"response_time\": time.time() - start_time,\n", "            \"response_text\": \"\"\n", "        }\n", "\n", "def test_model_performance(num_requests):\n", "    \"\"\"测试模型并发性能和字符吞吐量\"\"\"\n", "    with concurrent.futures.ThreadPoolExecutor(max_workers=num_requests) as executor:\n", "        futures = [executor.submit(send_request) for _ in range(num_requests)]\n", "        results = [future.result() for future in concurrent.futures.as_completed(futures)]\n", "    \n", "    total_time = sum(result['response_time'] for result in results)\n", "    total_chars = sum(len(result['response_text']) for result in results)\n", "    \n", "    avg_response_time = total_time / num_requests\n", "    chars_per_second = total_chars / total_time if total_time > 0 else 0\n", "    \n", "    return {\n", "        \"total_time\": total_time,\n", "        \"average_response_time\": avg_response_time,\n", "        \"chars_per_second\": chars_per_second\n", "    }\n", "\n", "if __name__ == \"__main__\":\n", "    num_requests = 20\n", "    content = \"你可以做什么呢？简单介绍一下\"\n", "    \n", "    print(\"测试首字响应速度...\")\n", "    first_byte_response_time = measure_first_byte_response_time()\n", "    if first_byte_response_time:\n", "        print(f\"\\n首字响应速度: {first_byte_response_time:.4f} 秒\")\n", "    \n", "    print(f\"\\n测试模型并发性能和字符吞吐量，并发请求数量: {num_requests} ...\")\n", "    performance_metrics = test_model_performance(num_requests)\n", "    print(f\"总的响应时间: {performance_metrics['total_time']:.4f} 秒\")\n", "    print(f\"平均响应时间: {performance_metrics['average_response_time']:.4f} 秒\")\n", "    print(f\"字符吞吐量: {performance_metrics['chars_per_second']:.2f} 字符/秒\")"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-04-11 10:36:57 - INFO - \n", "    开始LLM自适应压力测试:\n", "    - 模型: deepseek\n", "    - 并发数列表: [30]\n", "    - 单轮测试时间: 30秒\n", "    - 错误率阈值: 10.0%\n", "    - 性能下降阈值: 20.0%\n", "    \n", "2025-04-11 10:36:57 - INFO - 测量首字延迟...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-11 10:36:58 - INFO - 首字延迟: 325.14 ms\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["首个token: 好的\n"]}, {"name": "stderr", "output_type": "stream", "text": ["并发测试进度:   0%|          | 0/1 [00:00<?, ?级别/s]2025-04-11 10:36:58 - INFO - \n", "开始测试并发数: 30\n", "并发数 30: 100%|██████████| 30.0/30.0s [01:00<00:00]\n", "并发测试进度: 100%|██████████| 1/1 [01:00<00:00, 60.01s/级别]2025-04-11 10:37:58 - INFO - \n", "    并发数 30 测试完成:\n", "    - QPS: 0.57\n", "    - 吞吐量: 167.39 tokens/s\n", "    - 平均延迟: 24911.75 ms\n", "    - 错误率: 35.85%\n", "    \n", "2025-04-11 10:37:58 - WARNING - \n", "    错误率超过阈值:\n", "    - 当前错误率: 35.85%\n", "    - 错误阈值: 10.00%\n", "    - 停止测试更高并发\n", "    \n", "并发测试进度: 100%|██████████| 1/1 [01:00<00:00, 60.02s/级别]\n", "2025-04-11 10:37:58 - INFO - \n", "压力测试完成!\n", "\n", "最佳性能指标:\n", "- 最佳并发数: 30\n", "- 最大QPS: 0.57\n", "- 最大吞吐量: 167.39 tokens/s\n", "\n", "系统限制:\n", "- 性能饱和点: 30 并发\n", "- 错误率超标点: 30 并发\n", "\n", "测试结果详情:\n", "2025-04-11 10:37:58 - INFO - \n", "并发数 30:\n", "- QPS: 0.57\n", "- 吞吐量: 167.39 tokens/s\n", "- 平均延迟: 24911.75 ms\n", "- P90延迟: 29268.88 ms\n", "- P99延迟: 29898.37 ms\n", "- 错误率: 35.85%\n", "\n"]}], "source": ["import json\n", "import time\n", "import logging\n", "import requests\n", "# import numpy as np\n", "# import pandas as pd\n", "import concurrent.futures\n", "import matplotlib.pyplot as plt\n", "from typing import Dict, List\n", "from dataclasses import dataclass\n", "from tqdm import tqdm\n", "\n", "# 配置日志\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format='%(asctime)s - %(levelname)s - %(message)s',\n", "    datefmt='%Y-%m-%d %H:%M:%S'\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "@dataclass\n", "class LLMTestConfig:\n", "    \"\"\"LLM测试配置\"\"\"\n", "    concurrency: int               # 并发数\n", "    prompt: str                    # 测试用的提示词\n", "    max_tokens: int = 1024        # 最大token数\n", "    temperature: float = 0.7      # 温度参数\n", "    test_duration: int = 30       # 测试持续时间(秒)\n", "    model_name: str = None        # 模型名称\n", "\n", "@dataclass\n", "class LLMPerformanceMetrics:\n", "    \"\"\"性能指标\"\"\"\n", "    total_requests: int           # 总请求数\n", "    successful_requests: int      # 成功请求数\n", "    failed_requests: int          # 失败请求数\n", "    total_tokens: int            # 总token数\n", "    avg_token_count: float       # 平均每个响应的token数\n", "    qps: float                   # 每秒查询数(QPS)\n", "    tokens_per_second: float     # 每秒token数\n", "    avg_latency: float          # 平均延迟\n", "    p50_latency: float          # P50延迟\n", "    p90_latency: float          # P90延迟\n", "    p99_latency: float          # P99延迟\n", "    first_token_latency: float  # 首字延迟\n", "    error_rate: float           # 错误率\n", "\n", "@dataclass\n", "class StressTestResult:\n", "    \"\"\"压力测试结果\"\"\"\n", "    optimal_concurrency: int        # 最佳并发数\n", "    max_qps: float                 # 最大QPS\n", "    max_tokens_per_second: float   # 最大token吞吐量\n", "    saturation_point: int          # 饱和点（性能开始下降的并发数）\n", "    error_threshold: int           # 错误率超标的并发数\n", "    all_metrics: Dict[int, LLMPerformanceMetrics]  # 所有并发级别的详细指标\n", "\n", "class LLMPerformanceTester:\n", "    \"\"\"LLM性能测试器\"\"\"\n", "    \n", "    def __init__(\n", "        self,\n", "        base_url: str,\n", "        api_key: str,\n", "        model_name: str\n", "    ):\n", "        self.base_url = base_url\n", "        self.api_key = api_key\n", "        self.model_name = model_name\n", "        self.headers = {\n", "            \"Content-Type\": \"application/json\",\n", "            \"Authorization\": f\"Bearer {api_key}\"\n", "        }\n", "\n", "    def measure_first_token_latency(self, prompt: str = \"你好\") -> float:\n", "        \"\"\"测量首字延迟\"\"\"\n", "        payload = {\n", "            \"model\": self.model_name,\n", "            \"messages\": [{\"role\": \"user\", \"content\": prompt}],\n", "            \"max_tokens\": 1024,\n", "            \"stream\": True\n", "        }\n", "        \n", "        start_time = time.time()\n", "        try:\n", "            with requests.post(\n", "                self.base_url,\n", "                headers=self.headers,\n", "                json=payload,\n", "                stream=True,\n", "                timeout=30\n", "            ) as response:\n", "                for line in response.iter_lines():\n", "                    if line:\n", "                        if line.startswith(b\"data: \"):\n", "                            json_str = line.decode('utf-8')[6:]\n", "                            if json_str.strip() == \"[DONE]\":\n", "                                continue\n", "                            chunk = json.loads(json_str)\n", "                            if chunk['choices'][0]['delta'].get('content'):\n", "                                first_token_time = time.time() - start_time\n", "                                print(f\"首个token: {chunk['choices'][0]['delta']['content']}\")\n", "                                return first_token_time\n", "        except Exception as e:\n", "            logger.error(f\"测量首字延迟失败: {e}\")\n", "            return None\n", "\n", "    def single_request(self, prompt: str) -> dict:\n", "        \"\"\"发送单个请求\"\"\"\n", "        try:\n", "            payload = {\n", "                \"model\": self.model_name,\n", "                \"messages\": [{\"role\": \"user\", \"content\": prompt}],\n", "                \"temperature\": 0.7,\n", "                \"max_tokens\": 1024,\n", "                \"stream\": False\n", "            }\n", "            \n", "            start_time = time.time()\n", "            response = requests.post(\n", "                self.base_url,\n", "                headers=self.headers,\n", "                json=payload,\n", "                timeout=30\n", "            )\n", "            latency = time.time() - start_time\n", "            \n", "            if response.status_code == 200:\n", "                result = response.json()\n", "                content = result[\"choices\"][0][\"message\"][\"content\"]\n", "                total_tokens = result.get(\"usage\", {}).get(\"total_tokens\", len(content))\n", "                \n", "                return {\n", "                    \"success\": True,\n", "                    \"content\": content,\n", "                    \"latency\": latency,\n", "                    \"total_tokens\": total_tokens\n", "                }\n", "            else:\n", "                return {\n", "                    \"success\": <PERSON><PERSON><PERSON>,\n", "                    \"error\": f\"HTTP {response.status_code}: {response.text}\",\n", "                    \"latency\": latency\n", "                }\n", "                \n", "        except Exception as e:\n", "            return {\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"error\": str(e),\n", "                \"latency\": time.time() - start_time if 'start_time' in locals() else None\n", "            }\n", "\n", "    def test_llm_performance(self, config: LLMTestConfig,concurrency:int) -> LLMPerformanceMetrics:\n", "        \"\"\"测试LLM性能\"\"\"\n", "        start_time = time.time()\n", "        responses = []\n", "        latencies = []\n", "        total_tokens = 0\n", "        error_count = 0\n", "        \n", "        # 创建当前并发级别的进度条\n", "        with tqdm(total=config.test_duration, \n", "                desc=f\"并发数 {concurrency}\", \n", "                unit=\"秒\",\n", "                bar_format=\"{l_bar}{bar}| {n:.1f}/{total:.1f}s [{elapsed}<{remaining}]\") as pbar:\n", "            \n", "            with concurrent.futures.ThreadPoolExecutor(max_workers=config.concurrency) as executor:\n", "                futures = []\n", "                last_update = start_time\n", "                \n", "                while time.time() - start_time < config.test_duration:\n", "                    # 补充并发任务\n", "                    while len(futures) < config.concurrency:\n", "                        futures.append(executor.submit(self.single_request, config.prompt))\n", "                    \n", "                    # 等待任意一个任务完成\n", "                    done, not_done = concurrent.futures.wait(\n", "                        futures,\n", "                        timeout=0.1,\n", "                        return_when=concurrent.futures.FIRST_COMPLETED\n", "                    )\n", "                    \n", "                    # 处理完成的任务\n", "                    for future in done:\n", "                        result = future.result()\n", "                        if result[\"success\"]:\n", "                            responses.append(result)\n", "                            latencies.append(result[\"latency\"])\n", "                            total_tokens += result[\"total_tokens\"]\n", "                        else:\n", "                            error_count += 1\n", "                    \n", "                    # 更新futures列表\n", "                    futures = list(not_done)\n", "                    \n", "                    # 更新进度条\n", "                    current_time = time.time()\n", "                    if current_time - last_update >= 0.1:  # 每0.1秒更新一次\n", "                        pbar.n = min(current_time - start_time, config.test_duration)\n", "                        pbar.refresh()\n", "                        last_update = current_time\n", "                \n", "            # 等待所有剩余任务完成\n", "            for future in concurrent.futures.as_completed(futures):\n", "                    result = future.result()\n", "                    if result[\"success\"]:\n", "                        responses.append(result)\n", "                        latencies.append(result[\"latency\"])\n", "                        total_tokens += result[\"total_tokens\"]\n", "                    else:\n", "                        error_count += 1\n", "        \n", "        # 计算性能指标\n", "        test_duration = time.time() - start_time\n", "        successful_requests = len(responses)\n", "        total_requests = successful_requests + error_count\n", "        \n", "        if successful_requests == 0:\n", "            return LLMPerformanceMetrics(\n", "                total_requests=total_requests,\n", "                successful_requests=0,\n", "                failed_requests=error_count,\n", "                total_tokens=0,\n", "                avg_token_count=0,\n", "                qps=0,\n", "                tokens_per_second=0,\n", "                avg_latency=float('inf'),\n", "                p50_latency=float('inf'),\n", "                p90_latency=float('inf'),\n", "                p99_latency=float('inf'),\n", "                first_token_latency=float('inf'),\n", "                error_rate=1.0\n", "            )\n", "        \n", "        # 计算延迟分布\n", "        latencies = sorted(latencies)\n", "        avg_latency = sum(latencies) / len(latencies)\n", "        p50_latency = latencies[int(len(latencies) * 0.5)]\n", "        p90_latency = latencies[int(len(latencies) * 0.9)]\n", "        p99_latency = latencies[int(len(latencies) * 0.99)]\n", "        \n", "        return LLMPerformanceMetrics(\n", "            total_requests=total_requests,\n", "            successful_requests=successful_requests,\n", "            failed_requests=error_count,\n", "            total_tokens=total_tokens,\n", "            avg_token_count=total_tokens / successful_requests,\n", "            qps=successful_requests / test_duration,\n", "            tokens_per_second=total_tokens / test_duration,\n", "            avg_latency=avg_latency,\n", "            p50_latency=p50_latency,\n", "            p90_latency=p90_latency,\n", "            p99_latency=p99_latency,\n", "            first_token_latency=min(latencies),\n", "            error_rate=error_count / total_requests\n", "        )\n", "\n", "    def adaptive_stress_test(\n", "        self,\n", "        concurrency_levels: List[int] = [20, 50, 80, 100, 150],\n", "        test_duration: int = 30,\n", "        prompt: str = \"你好，请用100字介绍下自己。\",\n", "        max_tokens: int = 1024,\n", "        error_threshold: float = 0.1,\n", "        performance_degradation_threshold: float = 0.2\n", "    ) -> StressTestResult:\n", "        \"\"\"自适应压力测试\"\"\"\n", "        logger.info(f\"\"\"\n", "    开始LLM自适应压力测试:\n", "    - 模型: {self.model_name}\n", "    - 并发数列表: {concurrency_levels}\n", "    - 单轮测试时间: {test_duration}秒\n", "    - 错误率阈值: {error_threshold*100}%\n", "    - 性能下降阈值: {performance_degradation_threshold*100}%\n", "    \"\"\")\n", "\n", "        # 测量首字延迟\n", "        logger.info(\"测量首字延迟...\")\n", "        first_token_latency = self.measure_first_token_latency()\n", "        if first_token_latency:\n", "            logger.info(f\"首字延迟: {first_token_latency*1000:.2f} ms\\n\")\n", "\n", "        results = {}\n", "        max_qps = 0\n", "        max_tokens_per_second = 0\n", "        optimal_concurrency = concurrency_levels[0]\n", "        saturation_point = max(concurrency_levels)\n", "        error_threshold_point = max(concurrency_levels)\n", "\n", "        last_qps = 0\n", "        last_tokens_per_second = 0\n", "        \n", "        # 使用tqdm创建并发测试的总进度条\n", "        with tqdm(total=len(concurrency_levels), desc=\"并发测试进度\", unit=\"级别\") as pbar:\n", "            # 按并发数列表依次测试\n", "            for concurrency in concurrency_levels:\n", "                logger.info(f\"\\n开始测试并发数: {concurrency}\")\n", "                \n", "                config = LLMTestConfig(\n", "                    concurrency=concurrency,\n", "                    prompt=prompt,\n", "                    max_tokens=max_tokens,\n", "                    test_duration=test_duration,\n", "                    model_name=self.model_name\n", "                )\n", "                \n", "                metrics = self.test_llm_performance(config, concurrency)\n", "                results[concurrency] = metrics\n", "                \n", "                # 更新并发测试总进度条\n", "                pbar.update(1)\n", "                \n", "                # 打印当前并发级别的测试结果\n", "                logger.info(f\"\"\"\n", "    并发数 {concurrency} 测试完成:\n", "    - QPS: {metrics.qps:.2f}\n", "    - 吞吐量: {metrics.tokens_per_second:.2f} tokens/s\n", "    - 平均延迟: {metrics.avg_latency*1000:.2f} ms\n", "    - 错误率: {metrics.error_rate*100:.2f}%\n", "    \"\"\")\n", "                \n", "                current_qps = metrics.qps\n", "                current_tokens_per_second = metrics.tokens_per_second\n", "                \n", "                if current_qps > max_qps:\n", "                    max_qps = current_qps\n", "                    optimal_concurrency = concurrency\n", "                \n", "                if current_tokens_per_second > max_tokens_per_second:\n", "                    max_tokens_per_second = current_tokens_per_second\n", "\n", "                # 检查错误率\n", "                if metrics.error_rate > error_threshold:\n", "                    error_threshold_point = concurrency\n", "                    logger.warning(f\"\"\"\n", "    错误率超过阈值:\n", "    - 当前错误率: {metrics.error_rate:.2%}\n", "    - 错误阈值: {error_threshold:.2%}\n", "    - 停止测试更高并发\n", "    \"\"\")\n", "                    break\n", "\n", "                # 检查性能下降\n", "                if last_qps > 0:\n", "                    qps_degradation = (last_qps - current_qps) / last_qps\n", "                    tokens_degradation = (last_tokens_per_second - current_tokens_per_second) / last_tokens_per_second\n", "                    \n", "                    if max(qps_degradation, tokens_degradation) > performance_degradation_threshold:\n", "                        saturation_point = concurrency\n", "                        logger.warning(f\"\"\"\n", "    性能出现显著下降:\n", "    - QPS下降: {qps_degradation:.2%}\n", "    - 吞吐量下降: {tokens_degradation:.2%}\n", "    - 停止测试更高并发\n", "    \"\"\")\n", "                        break\n", "\n", "                last_qps = current_qps\n", "                last_tokens_per_second = current_tokens_per_second\n", "\n", "        # 汇总测试结果\n", "        result = StressTestResult(\n", "            optimal_concurrency=optimal_concurrency,\n", "            max_qps=max_qps,\n", "            max_tokens_per_second=max_tokens_per_second,\n", "            saturation_point=saturation_point,\n", "            error_threshold=error_threshold_point,\n", "            all_metrics=results\n", "        )\n", "\n", "        self._print_test_report(result)\n", "        # self._plot_stress_test_results(results)\n", "        \n", "        return result\n", "\n", "    def _print_test_report(self, result: StressTestResult):\n", "        \"\"\"打印测试报告\"\"\"\n", "        logger.info(f\"\"\"\n", "压力测试完成!\n", "\n", "最佳性能指标:\n", "- 最佳并发数: {result.optimal_concurrency}\n", "- 最大QPS: {result.max_qps:.2f}\n", "- 最大吞吐量: {result.max_tokens_per_second:.2f} tokens/s\n", "\n", "系统限制:\n", "- 性能饱和点: {result.saturation_point} 并发\n", "- 错误率超标点: {result.error_threshold} 并发\n", "\n", "测试结果详情:\"\"\")\n", "\n", "        for concurrency, metrics in result.all_metrics.items():\n", "            logger.info(f\"\"\"\n", "并发数 {concurrency}:\n", "- QPS: {metrics.qps:.2f}\n", "- 吞吐量: {metrics.tokens_per_second:.2f} tokens/s\n", "- 平均延迟: {metrics.avg_latency*1000:.2f} ms\n", "- P90延迟: {metrics.p90_latency*1000:.2f} ms\n", "- P99延迟: {metrics.p99_latency*1000:.2f} ms\n", "- 错误率: {metrics.error_rate*100:.2f}%\n", "\"\"\")\n", "\n", "    def _plot_stress_test_results(self, results: Dict[int, LLMPerformanceMetrics]):\n", "        \"\"\"绘制压力测试结果图表\"\"\"\n", "        concurrencies = list(results.keys())\n", "        \n", "        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))\n", "        \n", "        # 吞吐量图表\n", "        ax1.plot(concurrencies, [m.qps for m in results.values()], 'b-o', label='QPS')\n", "        ax1_twin = ax1.twinx()\n", "        ax1_twin.plot(concurrencies, [m.tokens_per_second for m in results.values()], 'r--o', label='Tokens/s')\n", "        \n", "        ax1.set_title('吞吐量 vs 并发数')\n", "        ax1.set_xlabel('并发数')\n", "        ax1.set_ylabel('QPS', color='b')\n", "        ax1_twin.set_ylabel('Tokens/s', color='r')\n", "        \n", "        lines1, labels1 = ax1.get_legend_handles_labels()\n", "        lines2, labels2 = ax1_twin.get_legend_handles_labels()\n", "        ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')\n", "        ax1.grid(True)\n", "        \n", "        # 延迟分布图表\n", "        ax2.plot(concurrencies, [m.avg_latency * 1000 for m in results.values()], 'r-o', label='平均延迟')\n", "        ax2.plot(concurrencies, [m.p90_latency * 1000 for m in results.values()], 'g-o', label='P90延迟')\n", "        ax2.plot(concurrencies, [m.p99_latency * 1000 for m in results.values()], 'b-o', label='P99延迟')\n", "        \n", "        ax2.set_title('延迟分布 vs 并发数')\n", "        ax2.set_xlabel('并发数')\n", "        ax2.set_ylabel('延迟 (ms)')\n", "        ax2.legend()\n", "        ax2.grid(True)\n", "        \n", "        # 错误率图表\n", "        ax3.plot(concurrencies, [m.error_rate * 100 for m in results.values()], 'r-o')\n", "        ax3.set_title('错误率 vs 并发数')\n", "        ax3.set_xlabel('并发数')\n", "        ax3.set_ylabel('错误率 (%)')\n", "        ax3.grid(True)\n", "        \n", "        # 平均token数图表\n", "        ax4.plot(concurrencies, [m.avg_token_count for m in results.values()], 'g-o')\n", "        ax4.set_title('平均Token数 vs 并发数')\n", "        ax4.set_xlabel('并发数')\n", "        ax4.set_ylabel('平均Token数')\n", "        ax4.grid(True)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "def main():\n", "    # 配置参数\n", "    # MODEL_NAME = \"DeepSeek-R1-Distill-Qwen-32B\"\n", "    # BASE_URL = \"http://110.80.151.195:1025/v1/chat/completions\"\n", "    MODEL_NAME = \"deepseek\"\n", "    BASE_URL = \"http://10.1.4.1:1025/v1/chat/completions\"\n", "    API_KEY = \"none\"\n", "    \n", "    # 创建测试器实例\n", "    tester = LLMPerformanceTester(\n", "        base_url=BASE_URL,\n", "        api_key=API_KEY,\n", "        model_name=MODEL_NAME\n", "    )\n", "    \n", "    # 运行自适应压力测试\n", "    result = tester.adaptive_stress_test(\n", "        concurrency_levels=[30],  # 自定义并发数列表\n", "        test_duration=30,                       # 每轮测试时间\n", "        prompt=\"你好，请用100字介绍下自己。\",      # 测试提示词\n", "        error_threshold=0.1,                    # 错误率阈值\n", "        performance_degradation_threshold=0.2    # 性能下降阈值\n", "    )\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2025-04-11T08:31:07.136609600Z", "start_time": "2025-04-11T08:31:07.048611700Z"}}, "outputs": [{"ename": "ImportError", "evalue": "C extension: pandas.compat._constants not built. If you want to import pandas from the source directory, you may need to run 'python setup.py build_ext' to build the C extensions first.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "File \u001b[1;32mE:\\soft\\anaconda3\\envs\\wiseagent\\lib\\site-packages\\pandas\\__init__.py:26\u001b[0m\n\u001b[0;32m     25\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_libs\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m hashtable \u001b[38;5;28;01mas\u001b[39;00m _hashtable, lib \u001b[38;5;28;01mas\u001b[39;00m _lib, tslib \u001b[38;5;28;01mas\u001b[39;00m _tslib\n\u001b[1;32m---> 26\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m _err:  \u001b[38;5;66;03m# pragma: no cover\u001b[39;00m\n\u001b[0;32m     27\u001b[0m     _module \u001b[38;5;241m=\u001b[39m _err\u001b[38;5;241m.\u001b[39mname\n", "File \u001b[1;32mE:\\soft\\anaconda3\\envs\\wiseagent\\lib\\site-packages\\pandas\\compat\\__init__.py:17\u001b[0m\n\u001b[0;32m     16\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_typing\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m F\n\u001b[1;32m---> 17\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcompat\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_constants\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     18\u001b[0m     IS64,\n\u001b[0;32m     19\u001b[0m     PY39,\n\u001b[0;32m     20\u001b[0m     PY310,\n\u001b[0;32m     21\u001b[0m     PY311,\n\u001b[0;32m     22\u001b[0m     PYPY,\n\u001b[0;32m     23\u001b[0m )\n\u001b[0;32m     24\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcompat\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcompressors\u001b[39;00m\n", "\u001b[1;31mImportError\u001b[0m: cannot import name 'ISMUSL' from 'pandas.compat._constants' (E:\\soft\\anaconda3\\envs\\wiseagent\\lib\\site-packages\\pandas\\compat\\_constants.py)", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[1;32mIn[14], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mpd\u001b[39;00m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mdatetime\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m datetime\n", "File \u001b[1;32mE:\\soft\\anaconda3\\envs\\wiseagent\\lib\\site-packages\\pandas\\__init__.py:31\u001b[0m\n\u001b[0;32m     26\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m _err:  \u001b[38;5;66;03m# pragma: no cover\u001b[39;00m\n\u001b[0;32m     27\u001b[0m     _module \u001b[38;5;241m=\u001b[39m _err\u001b[38;5;241m.\u001b[39mname\n\u001b[0;32m     28\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m(\n\u001b[0;32m     29\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mC extension: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m_module\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m not built. If you want to import \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m     30\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpandas from the source directory, you may need to run \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m---> 31\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpython setup.py build_ext --force\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m to build the C extensions first.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m     32\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m_err\u001b[39;00m\n\u001b[0;32m     33\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m     34\u001b[0m     \u001b[38;5;28;01mdel\u001b[39;00m _tslib, _lib, _hashtable\n", "\u001b[1;31mImportError\u001b[0m: C extension: pandas.compat._constants not built. If you want to import pandas from the source directory, you may need to run 'python setup.py build_ext' to build the C extensions first."]}], "source": ["import pandas as pd\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-04-11 10:45:26 - INFO - \n", "    开始LLM自适应压力测试:\n", "    - 模型: DeepSeek-R1-<PERSON>st<PERSON>-<PERSON>wen-32B\n", "    - 并发数列表: [20, 50]\n", "    - 单轮测试时间: 30秒\n", "    - 错误率阈值: 10.0%\n", "    - 性能下降阈值: 20.0%\n", "    \n", "2025-04-11 10:45:26 - INFO - 测量首字延迟...\n", "2025-04-11 10:45:26 - INFO - 首字延迟: 160.57 ms\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["首个token: <think>\n"]}, {"name": "stderr", "output_type": "stream", "text": ["并发测试进度:   0%|          | 0/2 [00:00<?, ?级别/s]2025-04-11 10:45:26 - INFO - \n", "开始测试并发数: 20\n", "并发数 20: 100%|██████████| 30.0/30.0s [00:37<00:00]\n", "并发测试进度:  50%|█████     | 1/2 [00:37<00:37, 37.59s/级别]2025-04-11 10:46:04 - INFO - \n", "    并发数 20 测试完成:\n", "    - QPS: 3.43\n", "    - 吞吐量: 631.66 tokens/s\n", "    - 平均延迟: 5241.79 ms\n", "    - 错误率: 0.00%\n", "    \n", "2025-04-11 10:46:04 - INFO - \n", "开始测试并发数: 50\n", "并发数 50: 100%|█████████▉| 30.0/30.0s [00:39<00:00]\n", "并发测试进度: 100%|██████████| 2/2 [01:17<00:00, 39.00s/级别]2025-04-11 10:46:44 - INFO - \n", "    并发数 50 测试完成:\n", "    - QPS: 6.08\n", "    - 吞吐量: 1139.49 tokens/s\n", "    - 平均延迟: 7064.17 ms\n", "    - 错误率: 0.00%\n", "    \n", "并发测试进度: 100%|██████████| 2/2 [01:17<00:00, 38.80s/级别]\n", "2025-04-11 10:46:44 - INFO - \n", "压力测试完成!\n", "\n", "最佳性能指标:\n", "- 最佳并发数: 50\n", "- 最大QPS: 6.08\n", "- 最大吞吐量: 1139.49 tokens/s\n", "\n", "系统限制:\n", "- 性能饱和点: 50 并发\n", "- 错误率超标点: 50 并发\n", "\n", "测试结果详情:\n", "2025-04-11 10:46:44 - INFO - \n", "并发数 20:\n", "- QPS: 3.43\n", "- 吞吐量: 631.66 tokens/s\n", "- 平均延迟: 5241.79 ms\n", "- P90延迟: 9666.02 ms\n", "- P99延迟: 14945.15 ms\n", "- 错误率: 0.00%\n", "\n", "2025-04-11 10:46:44 - INFO - \n", "并发数 50:\n", "- QPS: 6.08\n", "- 吞吐量: 1139.49 tokens/s\n", "- 平均延迟: 7064.17 ms\n", "- P90延迟: 13159.80 ms\n", "- P99延迟: 20167.81 ms\n", "- 错误率: 0.00%\n", "\n"]}, {"ename": "ValueError", "evalue": "numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[11], line 108\u001b[0m\n\u001b[0;32m    102\u001b[0m result \u001b[38;5;241m=\u001b[39m tester\u001b[38;5;241m.\u001b[39madaptive_stress_test(\n\u001b[0;32m    103\u001b[0m     concurrency_levels\u001b[38;5;241m=\u001b[39m[\u001b[38;5;241m20\u001b[39m, \u001b[38;5;241m50\u001b[39m],\n\u001b[0;32m    104\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mtest_config\n\u001b[0;32m    105\u001b[0m )\n\u001b[0;32m    107\u001b[0m \u001b[38;5;66;03m# 导出测试报告\u001b[39;00m\n\u001b[1;32m--> 108\u001b[0m \u001b[43mexport_test_report_to_excel\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    109\u001b[0m \u001b[43m    \u001b[49m\u001b[43mresult\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresult\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    110\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmodel_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mMODEL_NAME\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    111\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtest_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtest_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    112\u001b[0m \u001b[43m    \u001b[49m\u001b[43moutput_path\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mdeepseek-32b.xlsx\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\n\u001b[0;32m    113\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[1;32mIn[11], line 11\u001b[0m, in \u001b[0;36mexport_test_report_to_excel\u001b[1;34m(result, model_name, test_config, output_path)\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mexport_test_report_to_excel\u001b[39m(result: StressTestResult, model_name: \u001b[38;5;28mstr\u001b[39m, test_config: \u001b[38;5;28mdict\u001b[39m, output_path: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mllm_performance_test_report.xlsx\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m      2\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;124;03m    将LLM性能测试结果导出到Excel\u001b[39;00m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;124;03m    \u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m      9\u001b[0m \u001b[38;5;124;03m        output_path: 输出的Excel文件路径\u001b[39;00m\n\u001b[0;32m     10\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m---> 11\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mpd\u001b[39;00m\n\u001b[0;32m     12\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mdatetime\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m datetime\n\u001b[0;32m     14\u001b[0m     \u001b[38;5;66;03m# 创建一个Excel writer对象\u001b[39;00m\n", "File \u001b[1;32me:\\soft\\anaconda3\\envs\\wiseagent\\lib\\site-packages\\pandas\\__init__.py:59\u001b[0m\n\u001b[0;32m     56\u001b[0m \u001b[38;5;66;03m# let init-time option registration happen\u001b[39;00m\n\u001b[0;32m     57\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfig_init\u001b[39;00m  \u001b[38;5;66;03m# pyright: ignore[reportUnusedImport] # noqa: F401\u001b[39;00m\n\u001b[1;32m---> 59\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     60\u001b[0m     \u001b[38;5;66;03m# dtype\u001b[39;00m\n\u001b[0;32m     61\u001b[0m     ArrowDtype,\n\u001b[0;32m     62\u001b[0m     Int8Dtype,\n\u001b[0;32m     63\u001b[0m     Int16Dtype,\n\u001b[0;32m     64\u001b[0m     Int32Dtype,\n\u001b[0;32m     65\u001b[0m     Int64Dtype,\n\u001b[0;32m     66\u001b[0m     UInt8Dtype,\n\u001b[0;32m     67\u001b[0m     UInt16Dtype,\n\u001b[0;32m     68\u001b[0m     UInt32Dtype,\n\u001b[0;32m     69\u001b[0m     UInt64Dtype,\n\u001b[0;32m     70\u001b[0m     Float32Dtype,\n\u001b[0;32m     71\u001b[0m     Float64Dtype,\n\u001b[0;32m     72\u001b[0m     CategoricalDtype,\n\u001b[0;32m     73\u001b[0m     PeriodDtype,\n\u001b[0;32m     74\u001b[0m     IntervalDtype,\n\u001b[0;32m     75\u001b[0m     DatetimeTZDtype,\n\u001b[0;32m     76\u001b[0m     StringDtype,\n\u001b[0;32m     77\u001b[0m     BooleanDtype,\n\u001b[0;32m     78\u001b[0m     \u001b[38;5;66;03m# missing\u001b[39;00m\n\u001b[0;32m     79\u001b[0m     NA,\n\u001b[0;32m     80\u001b[0m     isna,\n\u001b[0;32m     81\u001b[0m     isnull,\n\u001b[0;32m     82\u001b[0m     notna,\n\u001b[0;32m     83\u001b[0m     notnull,\n\u001b[0;32m     84\u001b[0m     \u001b[38;5;66;03m# indexes\u001b[39;00m\n\u001b[0;32m     85\u001b[0m     Index,\n\u001b[0;32m     86\u001b[0m     CategoricalIndex,\n\u001b[0;32m     87\u001b[0m     RangeIndex,\n\u001b[0;32m     88\u001b[0m     MultiIndex,\n\u001b[0;32m     89\u001b[0m     IntervalIndex,\n\u001b[0;32m     90\u001b[0m     TimedeltaIndex,\n\u001b[0;32m     91\u001b[0m     DatetimeIndex,\n\u001b[0;32m     92\u001b[0m     PeriodIndex,\n\u001b[0;32m     93\u001b[0m     IndexSlice,\n\u001b[0;32m     94\u001b[0m     \u001b[38;5;66;03m# tseries\u001b[39;00m\n\u001b[0;32m     95\u001b[0m     NaT,\n\u001b[0;32m     96\u001b[0m     Period,\n\u001b[0;32m     97\u001b[0m     period_range,\n\u001b[0;32m     98\u001b[0m     Timedelta,\n\u001b[0;32m     99\u001b[0m     timedelta_range,\n\u001b[0;32m    100\u001b[0m     Timestamp,\n\u001b[0;32m    101\u001b[0m     date_range,\n\u001b[0;32m    102\u001b[0m     bdate_range,\n\u001b[0;32m    103\u001b[0m     Interval,\n\u001b[0;32m    104\u001b[0m     interval_range,\n\u001b[0;32m    105\u001b[0m     DateOffset,\n\u001b[0;32m    106\u001b[0m     \u001b[38;5;66;03m# conversion\u001b[39;00m\n\u001b[0;32m    107\u001b[0m     to_numeric,\n\u001b[0;32m    108\u001b[0m     to_datetime,\n\u001b[0;32m    109\u001b[0m     to_timedelta,\n\u001b[0;32m    110\u001b[0m     \u001b[38;5;66;03m# misc\u001b[39;00m\n\u001b[0;32m    111\u001b[0m     Flags,\n\u001b[0;32m    112\u001b[0m     Grouper,\n\u001b[0;32m    113\u001b[0m     factorize,\n\u001b[0;32m    114\u001b[0m     unique,\n\u001b[0;32m    115\u001b[0m     value_counts,\n\u001b[0;32m    116\u001b[0m     NamedAgg,\n\u001b[0;32m    117\u001b[0m     array,\n\u001b[0;32m    118\u001b[0m     Categorical,\n\u001b[0;32m    119\u001b[0m     set_eng_float_format,\n\u001b[0;32m    120\u001b[0m     Series,\n\u001b[0;32m    121\u001b[0m     DataFrame,\n\u001b[0;32m    122\u001b[0m )\n\u001b[0;32m    124\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdtypes\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdtypes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m SparseDtype\n\u001b[0;32m    126\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtseries\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mapi\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m infer_freq\n", "File \u001b[1;32me:\\soft\\anaconda3\\envs\\wiseagent\\lib\\site-packages\\pandas\\core\\api.py:1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_libs\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m      2\u001b[0m     NaT,\n\u001b[0;32m      3\u001b[0m     Period,\n\u001b[0;32m      4\u001b[0m     <PERSON>del<PERSON>,\n\u001b[0;32m      5\u001b[0m     Timestamp,\n\u001b[0;32m      6\u001b[0m )\n\u001b[0;32m      7\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_libs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmissing\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m NA\n\u001b[0;32m      9\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdtypes\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdtypes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     10\u001b[0m     ArrowDtype,\n\u001b[0;32m     11\u001b[0m     CategoricalDtype,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     14\u001b[0m     PeriodDtype,\n\u001b[0;32m     15\u001b[0m )\n", "File \u001b[1;32me:\\soft\\anaconda3\\envs\\wiseagent\\lib\\site-packages\\pandas\\_libs\\__init__.py:18\u001b[0m\n\u001b[0;32m     16\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mp<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_libs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpandas_parser\u001b[39;00m  \u001b[38;5;66;03m# noqa: E501 # isort: skip # type: ignore[reportUnusedImport]\u001b[39;00m\n\u001b[0;32m     17\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_libs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpandas_datetime\u001b[39;00m  \u001b[38;5;66;03m# noqa: F401,E501 # isort: skip # type: ignore[reportUnusedImport]\u001b[39;00m\n\u001b[1;32m---> 18\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_libs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01minterval\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Interval\n\u001b[0;32m     19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpandas\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_libs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtslibs\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     20\u001b[0m     NaT,\n\u001b[0;32m     21\u001b[0m     NaTType,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     26\u001b[0m     iNaT,\n\u001b[0;32m     27\u001b[0m )\n", "File \u001b[1;32minterval.pyx:1\u001b[0m, in \u001b[0;36minit pandas._libs.interval\u001b[1;34m()\u001b[0m\n", "\u001b[1;31mValueError\u001b[0m: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject"]}], "source": ["def export_test_report_to_excel(result: StressTestResult, model_name: str, test_config: dict, output_path: str = \"llm_performance_test_report.xlsx\"):\n", "    \"\"\"\n", "    将LLM性能测试结果导出到Excel\n", "    \n", "    Args:\n", "        result: StressTestResult对象，包含测试结果\n", "        model_name: 测试的模型名称\n", "        test_config: 测试配置信息的字典\n", "        output_path: 输出的Excel文件路径\n", "    \"\"\"\n", "    \n", "    # 创建一个Excel writer对象\n", "    with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:\n", "        # 1. 测试概要sheet\n", "        summary_data = {\n", "            \"测试时间\": datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "            \"模型名称\": model_name,\n", "            \"最佳并发数\": result.optimal_concurrency,\n", "            \"最大QPS\": f\"{result.max_qps:.2f}\",\n", "            \"最大吞吐量(tokens/s)\": f\"{result.max_tokens_per_second:.2f}\",\n", "            \"性能饱和点(并发数)\": result.saturation_point,\n", "            \"错误率超标点(并发数)\": result.error_threshold,\n", "            \"测试持续时间(秒)\": test_config.get(\"test_duration\", \"\"),\n", "            \"测试提示词\": test_config.get(\"prompt\", \"\"),\n", "            \"错误率阈值\": f\"{test_config.get('error_threshold', 0)*100}%\",\n", "            \"性能下降阈值\": f\"{test_config.get('performance_degradation_threshold', 0)*100}%\"\n", "        }\n", "        summary_df = pd.DataFrame([summary_data]).T\n", "        summary_df.columns = [\"值\"]\n", "        summary_df.to_excel(writer, sheet_name=\"测试概要\")\n", "        \n", "        # 2. 详细性能指标sheet\n", "        performance_data = []\n", "        for concurrency, metrics in result.all_metrics.items():\n", "            performance_data.append({\n", "                \"并发数\": concurrency,\n", "                \"QPS\": f\"{metrics.qps:.2f}\",\n", "                \"吞吐量(tokens/s)\": f\"{metrics.tokens_per_second:.2f}\",\n", "                \"平均延迟(ms)\": f\"{metrics.avg_latency*1000:.2f}\",\n", "                \"P50延迟(ms)\": f\"{metrics.p50_latency*1000:.2f}\",\n", "                \"P90延迟(ms)\": f\"{metrics.p90_latency*1000:.2f}\",\n", "                \"P99延迟(ms)\": f\"{metrics.p99_latency*1000:.2f}\",\n", "                \"首字延迟(ms)\": f\"{metrics.first_token_latency*1000:.2f}\",\n", "                \"总请求数\": metrics.total_requests,\n", "                \"成功请求数\": metrics.successful_requests,\n", "                \"失败请求数\": metrics.failed_requests,\n", "                \"错误率\": f\"{metrics.error_rate*100:.2f}%\",\n", "                \"平均Token数\": f\"{metrics.avg_token_count:.2f}\",\n", "                \"总Token数\": metrics.total_tokens\n", "            })\n", "        \n", "        perf_df = pd.DataFrame(performance_data)\n", "        perf_df.to_excel(writer, sheet_name=\"性能指标\", index=False)\n", "        \n", "        # 获取workbook和worksheet对象以进行格式设置\n", "        workbook = writer.book\n", "        \n", "        # 设置列宽\n", "        for worksheet in writer.sheets.values():\n", "            worksheet.set_column('A:A', 20)  # 设置第一列宽度\n", "            worksheet.set_column('B:Z', 15)  # 设置其他列宽度\n", "        \n", "        # 创建格式\n", "        header_format = workbook.add_format({\n", "            'bold': True,\n", "            'bg_color': '#D9E1F2',\n", "            'border': 1\n", "        })\n", "        \n", "        # 应用格式到表头\n", "        for worksheet in writer.sheets.values():\n", "            for col_num, value in enumerate(worksheet.table.values()):\n", "                worksheet.write(0, col_num, value, header_format)\n", "    \n", "    logger.info(f\"测试报告已导出到: {output_path}\")\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    # 配置参数\n", "    MODEL_NAME = \"DeepSeek-R1-Distill-Qwen-32B\"\n", "    BASE_URL = \"http://110.80.151.195:1025/v1/chat/completions\"\n", "    API_KEY = \"none\"\n", "    \n", "    # 创建测试器实例\n", "    tester = LLMPerformanceTester(\n", "        base_url=BASE_URL,\n", "        api_key=API_KEY,\n", "        model_name=MODEL_NAME\n", "    )\n", "    \n", "    # 测试配置\n", "    test_config = {\n", "        \"test_duration\": 30,\n", "        \"prompt\": \"你好，请用100字介绍下自己。\",\n", "        \"error_threshold\": 0.1,\n", "        \"performance_degradation_threshold\": 0.2\n", "    }\n", "    \n", "    # 运行自适应压力测试\n", "    result = tester.adaptive_stress_test(\n", "        concurrency_levels=[20, 50],\n", "        **test_config\n", "    )\n", "    \n", "    # 导出测试报告\n", "    export_test_report_to_excel(\n", "        result=result,\n", "        model_name=MODEL_NAME,\n", "        test_config=test_config,\n", "        output_path=\"E:\\workspace\\wangzhitianyuan\\deepseek-32b.xlsx\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "punish", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}