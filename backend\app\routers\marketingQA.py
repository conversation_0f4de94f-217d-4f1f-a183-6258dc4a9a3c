from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
import asyncio  # 导入 asyncio 模块
from ..models.chat import ChatResponse
from ..db.mongodb import db
from ..utils.auth import verify_token
from ..models.system_app_setting import SystemAppSettingModel
from ..utils.llmClient import stream_model_api
from typing import Dict, Any
from ..models.message import Message  # 使用 MongoEngine 模型
from datetime import datetime
from bson import ObjectId
from app.utils.logging_config import setup_logging, get_logger
setup_logging()
logger = get_logger(__name__)

router = APIRouter(
    prefix="/api",
    tags=["marketingQA"]
)


# 创建新对话
@router.post("/app/chat/marketingQA", response_model=Dict[str, Any])
async def chat_marketingQA(conversation: ChatResponse, current_user: dict = Depends(verify_token)):
     # print(conversation)
    system_app_info: SystemAppSettingModel = await db["system_app_settings"].find_one({"app_info": conversation.app_info})
    if not system_app_info:
        logger.error(f"System app setting not found for app_info: {conversation.app_info}")
        raise HTTPException(status_code=404, detail="System app setting not found")
    # 提取最后一条消息
    last_message = conversation.messages[-1]

    # 创建新的用户消息记录
    new_user_message = Message(
        _id=str(ObjectId()),
        conversation_id=conversation.conversation_id,
        message_id=last_message['id'],
        meta_data=last_message.get('meta', {}),
        extra=last_message.get('extra', {}),
        user_id=conversation.user_id,
        user_name=conversation.user_name,
        role="user",
        content=last_message['content'],
        created_at=datetime.now(),
        app_info=conversation.app_info,
        token_count=0,  # 这里可以添加计算token的逻辑
        price=0.0  # 这里可以添加计算价格的逻辑
    )


    # 将 MongoEngine 实例转换为字典
    new_user_message_dict = new_user_message.to_mongo().to_dict()
    await db["messages"].insert_one(new_user_message_dict)

    async def stream_response():
        ai_message_content = ""
        async for chunk in stream_model_api(
            chat_id=conversation.conversation_id,
            user_id=conversation.user_id,
            messages=conversation.messages,
            system_app_info=system_app_info
        ):
            ai_message_content += chunk
            logger.info(chunk)
            yield chunk
            await asyncio.sleep(0)

        # 添加 end 事件类型
        yield "event: end\ndata: Stream has ended\n\n"

    return StreamingResponse(stream_response(), media_type='text/event-stream')
