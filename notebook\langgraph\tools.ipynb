{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["{\n", " \"cells\": [\n", "  {\n", "   \"cell_type\": \"markdown\",\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"# 使用LangGraph实现搜索引擎工具的四种调用场景\\n\",\n", "    \"\\n\",\n", "    \"本notebook展示了四种不同的搜索引擎工具调用场景：\\n\",\n", "    \"1. 直接调用工具\\n\",\n", "    \"2. LLM调用工具，直接返回工具结果\\n\",\n", "    \"3. LLM决定调用工具，并根据调用结果生成回答\\n\",\n", "    \"4. 只提供参数直接调用工具，LLM根据调用结果生成回答\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"# 安装必要的库\\n\",\n", "    \"!pip install langgraph langchain-openai langchain\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"import os\\n\",\n", "    \"from typing import Dict, List, Annotated, TypedDict, Union, Literal\\n\",\n", "    \"import json\\n\",\n", "    \"\\n\",\n", "    \"from langchain_openai import ChatOpenAI\\n\",\n", "    \"from langgraph.graph import StateGraph, END\\n\",\n", "    \"from langgraph.prebuilt import ToolExecutor\\n\",\n", "    \"from langchain_core.tools import tool\\n\",\n", "    \"from langchain_core.messages import HumanMessage, AIMessage, ToolMessage\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"markdown\",\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"## 实现搜索引擎工具\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"@tool\\n\",\n", "    \"def search_engine(query: str) -> str:\\n\",\n", "    \"    \\\"\\\"\\\"使用搜索引擎搜索信息。输入搜索查询，返回搜索结果。\\\"\\\"\\\"\\n\",\n", "    \"    # 这里模拟搜索引擎结果\\n\",\n", "    \"    results = {\\n\",\n", "    \"        \\\"人工智能\\\": \\\"人工智能（AI）是计算机科学的一个分支，致力于创建能够模拟人类智能的系统。\\\",\\n\",\n", "    \"        \\\"机器学习\\\": \\\"机器学习是人工智能的一个子领域，专注于开发能从数据中学习的算法。\\\",\\n\",\n", "    \"        \\\"深度学习\\\": \\\"深度学习是机器学习的一个子集，使用多层神经网络处理复杂模式。\\\",\\n\",\n", "    \"        \\\"大语言模型\\\": \\\"大语言模型（LLM）是基于Transformer架构的深度学习模型，能够理解和生成人类语言。\\\"\\n\",\n", "    \"    }\\n\",\n", "    \"    \\n\",\n", "    \"    # 如果找不到精确匹配，返回模糊匹配结果\\n\",\n", "    \"    if query in results:\\n\",\n", "    \"        return results[query]\\n\",\n", "    \"    else:\\n\",\n", "    \"        for key, value in results.items():\\n\",\n", "    \"            if key in query or query in key:\\n\",\n", "    \"                return f\\\"相关结果 - {key}: {value}\\\"\\n\",\n", "    \"        return f\\\"没有找到与'{query}'相关的搜索结果。\\\"\\n\",\n", "    \"\\n\",\n", "    \"# 创建工具执行器\\n\",\n", "    \"tool_executor = ToolExecutor([search_engine])\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"markdown\",\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"## 场景1：直接调用工具\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"# 定义状态类型\\n\",\n", "    \"class DirectToolState(TypedDict):\\n\",\n", "    \"    query: str\\n\",\n", "    \"    result: str\\n\",\n", "    \"\\n\",\n", "    \"# 工具调用函数\\n\",\n", "    \"def call_tool(state: DirectToolState) -> DirectToolState:\\n\",\n", "    \"    result = search_engine.invoke(state[\\\"query\\\"])\\n\",\n", "    \"    return {\\\"query\\\": state[\\\"query\\\"], \\\"result\\\": result}\\n\",\n", "    \"\\n\",\n", "    \"# 创建图\\n\",\n", "    \"direct_tool_graph = StateGraph(DirectToolState)\\n\",\n", "    \"direct_tool_graph.add_node(\\\"call_tool\\\", call_tool)\\n\",\n", "    \"\\n\",\n", "    \"# 设置边\\n\",\n", "    \"direct_tool_graph.set_entry_point(\\\"call_tool\\\")\\n\",\n", "    \"direct_tool_graph.add_edge(\\\"call_tool\\\", END)\\n\",\n", "    \"\\n\",\n", "    \"# 编译图\\n\",\n", "    \"direct_tool_app = direct_tool_graph.compile()\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"# 测试场景1\\n\",\n", "    \"result1 = direct_tool_app.invoke({\\\"query\\\": \\\"人工智能\\\"})\\n\",\n", "    \"print(\\\"场景1结果：\\\", result1[\\\"result\\\"])\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"markdown\",\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"## 场景2：LLM调用工具，直接返回工具结果\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"# 初始化LLM\\n\",\n", "    \"llm = ChatOpenAI(model=\\\"gpt-3.5-turbo\\\", temperature=0)\\n\",\n", "    \"\\n\",\n", "    \"# 定义状态类型\\n\",\n", "    \"class LLMToolState(TypedDict):\\n\",\n", "    \"    query: str\\n\",\n", "    \"    messages: List\\n\",\n", "    \"    result: str\\n\",\n", "    \"\\n\",\n", "    \"# LLM决定是否调用工具\\n\",\n", "    \"def llm_decide(state: LLMToolState) -> Dict:\\n\",\n", "    \"    messages = [HumanMessage(content=f\\\"用户查询：{state['query']}。请决定是否需要使用搜索引擎工具来回答这个问题。\\\")]\\n\",\n", "    \"    response = llm.invoke(messages)\\n\",\n", "    \"    \\n\",\n", "    \"    # 简单判断LLM是否决定使用工具\\n\",\n", "    \"    if \\\"使用搜索引擎\\\" in response.content or \\\"搜索工具\\\" in response.content:\\n\",\n", "    \"        return {\\\"action\\\": \\\"call_tool\\\"}\\n\",\n", "    \"    else:\\n\",\n", "    \"        return {\\\"action\\\": \\\"no_tool\\\"}\\n\",\n", "    \"\\n\",\n", "    \"# 调用工具\\n\",\n", "    \"def call_search_tool(state: LLMToolState) -> LLMToolState:\\n\",\n", "    \"    result = search_engine.invoke(state[\\\"query\\\"])\\n\",\n", "    \"    return {**state, \\\"result\\\": result}\\n\",\n", "    \"\\n\",\n", "    \"# 不调用工具\\n\",\n", "    \"def no_tool_needed(state: LLMToolState) -> LLMToolState:\\n\",\n", "    \"    return {**state, \\\"result\\\": \\\"LLM决定不需要使用搜索工具。\\\"}\\n\",\n", "    \"\\n\",\n", "    \"# 创建图\\n\",\n", "    \"llm_tool_graph = StateGraph(LLMToolState)\\n\",\n", "    \"llm_tool_graph.add_node(\\\"llm_decide\\\", llm_decide)\\n\",\n", "    \"llm_tool_graph.add_node(\\\"call_tool\\\", call_search_tool)\\n\",\n", "    \"llm_tool_graph.add_node(\\\"no_tool\\\", no_tool_needed)\\n\",\n", "    \"\\n\",\n", "    \"# 设置边\\n\",\n", "    \"llm_tool_graph.set_entry_point(\\\"llm_decide\\\")\\n\",\n", "    \"llm_tool_graph.add_conditional_edges(\\n\",\n", "    \"    \\\"llm_decide\\\",\\n\",\n", "    \"    lambda x: x[\\\"action\\\"],\\n\",\n", "    \"    {\\n\",\n", "    \"        \\\"call_tool\\\": \\\"call_tool\\\",\\n\",\n", "    \"        \\\"no_tool\\\": \\\"no_tool\\\"\\n\",\n", "    \"    }\\n\",\n", "    \")\\n\",\n", "    \"llm_tool_graph.add_edge(\\\"call_tool\\\", END)\\n\",\n", "    \"llm_tool_graph.add_edge(\\\"no_tool\\\", END)\\n\",\n", "    \"\\n\",\n", "    \"# 编译图\\n\",\n", "    \"llm_tool_app = llm_tool_graph.compile()\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"# 测试场景2\\n\",\n", "    \"result2 = llm_tool_app.invoke({\\\"query\\\": \\\"什么是大语言模型？\\\", \\\"messages\\\": []})\\n\",\n", "    \"print(\\\"场景2结果：\\\", result2[\\\"result\\\"])\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"markdown\",\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"## 场景3：LLM决定调用工具，并根据调用结果生成回答\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"# 定义状态类型\\n\",\n", "    \"class LLMWithToolState(TypedDict):\\n\",\n", "    \"    query: str\\n\",\n", "    \"    messages: List\\n\",\n", "    \"    tool_result: str\\n\",\n", "    \"    final_answer: str\\n\",\n", "    \"\\n\",\n", "    \"# LLM决定是否调用工具\\n\",\n", "    \"def llm_router(state: LLMWithToolState) -> Dict:\\n\",\n", "    \"    messages = [HumanMessage(content=f\\\"用户查询：{state['query']}。请决定是否需要使用搜索引擎工具来回答这个问题。\\\")]\\n\",\n", "    \"    response = llm.invoke(messages)\\n\",\n", "    \"    state[\\\"messages\\\"] = messages + [response]\\n\",\n", "    \"    \\n\",\n", "    \"    # 简单判断LLM是否决定使用工具\\n\",\n", "    \"    if \\\"使用搜索引擎\\\" in response.content or \\\"搜索工具\\\" in response.content:\\n\",\n", "    \"        return {\\\"action\\\": \\\"use_tool\\\"}\\n\",\n", "    \"    else:\\n\",\n", "    \"        return {\\\"action\\\": \\\"direct_answer\\\"}\\n\",\n", "    \"\\n\",\n", "    \"# 调用工具\\n\",\n", "    \"def use_search_tool(state: LLMWithToolState) -> LLMWithToolState:\\n\",\n", "    \"    tool_result = search_engine.invoke(state[\\\"query\\\"])\\n\",\n", "    \"    return {**state, \\\"tool_result\\\": tool_result}\\n\",\n", "    \"\\n\",\n", "    \"# LLM直接回答\\n\",\n", "    \"def llm_direct_answer(state: LLMWithToolState) -> LLMWithToolState:\\n\",\n", "    \"    messages = state[\\\"messages\\\"] + [HumanMessage(content=f\\\"请直接回答用户的问题：{state['query']}\\\")]\\n\",\n", "    \"    response = llm.invoke(messages)\\n\",\n", "    \"    return {**state, \\\"final_answer\\\": response.content}\\n\",\n", "    \"\\n\",\n", "    \"# LLM基于工具结果生成回答\\n\",\n", "    \"def llm_answer_with_tool(state: LLMWithToolState) -> LLMWithToolState:\\n\",\n", "    \"    messages = state[\\\"messages\\\"] + [\\n\",\n", "    \"        HumanMessage(content=f\\\"搜索引擎返回的结果是：{state['tool_result']}。请基于这个结果回答用户的问题：{state['query']}\\\")\\n\",\n", "    \"    ]\\n\",\n", "    \"    response = llm.invoke(messages)\\n\",\n", "    \"    return {**state, \\\"final_answer\\\": response.content}\\n\",\n", "    \"\\n\",\n", "    \"# 创建图\\n\",\n", "    \"llm_with_tool_graph = StateGraph(LLMWithToolState)\\n\",\n", "    \"llm_with_tool_graph.add_node(\\\"llm_router\\\", llm_router)\\n\",\n", "    \"llm_with_tool_graph.add_node(\\\"use_tool\\\", use_search_tool)\\n\",\n", "    \"llm_with_tool_graph.add_node(\\\"llm_direct_answer\\\", llm_direct_answer)\\n\",\n", "    \"llm_with_tool_graph.add_node(\\\"llm_answer_with_tool\\\", llm_answer_with_tool)\\n\",\n", "    \"\\n\",\n", "    \"# 设置边\\n\",\n", "    \"llm_with_tool_graph.set_entry_point(\\\"llm_router\\\")\\n\",\n", "    \"llm_with_tool_graph.add_conditional_edges(\\n\",\n", "    \"    \\\"llm_router\\\",\\n\",\n", "    \"    lambda x: x[\\\"action\\\"],\\n\",\n", "    \"    {\\n\",\n", "    \"        \\\"use_tool\\\": \\\"use_tool\\\",\\n\",\n", "    \"        \\\"direct_answer\\\": \\\"llm_direct_answer\\\"\\n\",\n", "    \"    }\\n\",\n", "    \")\\n\",\n", "    \"llm_with_tool_graph.add_edge(\\\"use_tool\\\", \\\"llm_answer_with_tool\\\")\\n\",\n", "    \"llm_with_tool_graph.add_edge(\\\"llm_direct_answer\\\", END)\\n\",\n", "    \"llm_with_tool_graph.add_edge(\\\"llm_answer_with_tool\\\", END)\\n\",\n", "    \"\\n\",\n", "    \"# 编译图\\n\",\n", "    \"llm_with_tool_app = llm_with_tool_graph.compile()\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"# 测试场景3\\n\",\n", "    \"result3 = llm_with_tool_app.invoke({\\\"query\\\": \\\"解释一下深度学习\\\", \\\"messages\\\": []})\\n\",\n", "    \"print(\\\"场景3结果：\\\", result3[\\\"final_answer\\\"])\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"markdown\",\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"## 场景4：只提供参数直接调用工具，LLM根据调用结果生成回答\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"# 定义状态类型\\n\",\n", "    \"class DirectParamToolState(TypedDict):\\n\",\n", "    \"    query: str\\n\",\n", "    \"    messages: List\\n\",\n", "    \"    tool_result: str\\n\",\n", "    \"    final_answer: str\\n\",\n", "    \"\\n\",\n", "    \"# 直接调用工具\\n\",\n", "    \"def direct_tool_call(state: DirectParamToolState) -> DirectParamToolState:\\n\",\n", "    \"    tool_result = search_engine.invoke(state[\\\"query\\\"])\\n\",\n", "    \"    return {**state, \\\"tool_result\\\": tool_result}\\n\",\n", "    \"\\n\",\n", "    \"# LLM基于工具结果生成回答\\n\",\n", "    \"def llm_process_result(state: DirectParamToolState) -> DirectParamToolState:\\n\",\n", "    \"    messages = [HumanMessage(content=f\\\"用户查询：{state['query']}。搜索引擎返回的结果是：{state['tool_result']}。请基于这个结果生成一个有帮助的回答。\\\")]\\n\",\n", "    \"    response = llm.invoke(messages)\\n\",\n", "    \"    return {**state, \\\"final_answer\\\": response.content}\\n\",\n", "    \"\\n\",\n", "    \"# 创建图\\n\",\n", "    \"direct_param_graph = StateGraph(DirectParamToolState)\\n\",\n", "    \"direct_param_graph.add_node(\\\"direct_tool_call\\\", direct_tool_call)\\n\",\n", "    \"direct_param_graph.add_node(\\\"llm_process_result\\\", llm_process_result)\\n\",\n", "    \"\\n\",\n", "    \"# 设置边\\n\",\n", "    \"direct_param_graph.set_entry_point(\\\"direct_tool_call\\\")\\n\",\n", "    \"direct_param_graph.add_edge(\\\"direct_tool_call\\\", \\\"llm_process_result\\\")\\n\",\n", "    \"direct_param_graph.add_edge(\\\"llm_process_result\\\", END)\\n\",\n", "    \"\\n\",\n", "    \"# 编译图\\n\",\n", "    \"direct_param_app = direct_param_graph.compile()\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"# 测试场景4\\n\",\n", "    \"result4 = direct_param_app.invoke({\\\"query\\\": \\\"机器学习\\\", \\\"messages\\\": []})\\n\",\n", "    \"print(\\\"场景4结果：\\\", result4[\\\"final_answer\\\"])\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"markdown\",\n", "   \"metadata\": {},\n", "   \"source\": [\n", "    \"## 总结\\n\",\n", "    \"\\n\",\n", "    \"以上四种场景展示了不同的工具调用方式：\\n\",\n", "    \"\\n\",\n", "    \"1. **直接调用工具**：直接将查询参数传给工具，获取结果。\\n\",\n", "    \"2. **LLM调用工具，直接返回工具结果**：由LLM决定是否需要调用工具，如果需要则调用并直接返回工具结果。\\n\",\n", "    \"3. **LLM决定调用工具，并根据调用结果生成回答**：LLM决定是否调用工具，如果调用则基于工具结果生成回答，否则直接回答。\\n\",\n", "    \"4. **只提供参数直接调用工具，LLM根据调用结果生成回答**：直接使用提供的参数调用工具，然后让LLM基于工具结果生成回答。\"\n", "   ]\n", "  }\n", " ],\n", " \"metadata\": {\n", "  \"kernelspec\": {\n", "   \"display_name\": \"py311\",\n", "   \"language\": \"python\",\n", "   \"name\": \"python3\"\n", "  },\n", "  \"language_info\": {\n", "   \"codemirror_mode\": {\n", "    \"name\": \"i<PERSON>thon\",\n", "    \"version\": 3\n", "   },\n", "   \"file_extension\": \".py\",\n", "   \"mimetype\": \"text/x-python\",\n", "   \"name\": \"python\",\n", "   \"nbconvert_exporter\": \"python\",\n", "   \"pygments_lexer\": \"ipython3\",\n", "   \"version\": \"3.11.10\"\n", "  }\n", " },\n", " \"nbformat\": 4,\n", " \"nbformat_minor\": 2\n", "}"]}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}