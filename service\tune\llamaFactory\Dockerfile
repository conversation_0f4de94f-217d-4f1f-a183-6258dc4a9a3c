# Use an official Python image as the base
FROM python:3.9-slim

# Set the working directory inside the container
WORKDIR /app

# Install dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    procps \
    curl \
    vim \
    && rm -rf /var/lib/apt/lists/*

# Install llama-factory webui
RUN pip install llamafactory pymongo

COPY app.py /app/app.py

# Expose ports for webui (default is 7860)
EXPOSE 7860

# Set the entrypoint to start the webui
CMD ["python", "app.py"]