from typing import TypedDict, List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel
import os
from sqlalchemy import Column, String, DateTime, <PERSON>olean, JSON, <PERSON>UI<PERSON>, <PERSON>teger, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
import uuid
from dotenv import load_dotenv
from sqlalchemy.types import TypeDecorator
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.sql.sqltypes import Float

load_dotenv('../.env')  # 从 .env.dev 改为 .env

class SearchResult(TypedDict):
    role: str
    content: str
    score: float
    id: str
    file_id: str
    knowledge_base_id: str
    chunk_index: int
    answer: str
    question: str
    created_at: datetime

class KnowledgeQAParams(TypedDict):
    knowledge_base_ids: List[str]
    prompt: Optional[str]
    prompt_retrieval: Optional[str]
    config: Dict[str, Any]

class ChatDialogueRequest(BaseModel):
    chatId: str
    datasetId: List[str]
    stream: Optional[bool] = False
    detail: Optional[bool] = False
    variables: Optional[Dict[str, Any]] = None
    messages: List[Dict[str, Any]] = []

class Provider:
    OPENAI = "OPENAI"
    DEEPSEEK = "DEEPSEEK"
    DOUBAO = "DOUBAO"
    LOCAL = "LOCAL"
    OLLAMA = "OLLAMA"
    JIUTIAN = "JIUTIAN"

class DeepSeekConfig:
    """DeepSeek 模型配置"""
    @classmethod
    def get_config(cls) -> Dict[str, str]:
        return {
            "service_url": os.getenv('DEEPSEEK_SERVICE_URL'),
            "api_key": os.getenv('DEEPSEEK_API_KEY'),
            "model": os.getenv('DEEPSEEK_MODEL_NAME'),
            "provider": os.getenv('DEEPSEEK_PROVIDER')
        }

Base = declarative_base()

class Chat(Base):
    __tablename__ = "chats"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    app_id = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)
    stream = Column(Boolean, default=False)
    detail = Column(Boolean, default=False)
    variables = Column(JSON)
    messages = Column(JSON)

class KnowledgeBase(Base):
    __tablename__ = "knowledge_bases"

    id = Column(UUID, primary_key=True, server_default="uuid_generate_v4()")
    parent_id = Column(String(255))
    type = Column(String(255))
    name = Column(String(255), nullable=False)
    intro = Column(Text)
    avatar = Column(String(255))
    vector_model = Column(String(255))
    agent_model = Column(String(255))
    created_at = Column(DateTime, nullable=False)
    last_updated = Column(DateTime, nullable=False)
    user_id = Column(Integer)
    user_name = Column(String(255))
    is_active = Column(Boolean, nullable=False, default=True)

class Collection(Base):
    __tablename__ = "collections"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    knowledge_base_id = Column(UUID(as_uuid=True), ForeignKey('knowledge_bases.id'), nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.now)

# 修改 Vector 类型定义
class Vector(TypeDecorator):
    impl = ARRAY(Float(precision=53))  # 明确指定 Float 精度为 53 (双精度)
    cache_ok = True

    def __init__(self, dimensions=1536):
        super(Vector, self).__init__()
        self.dimensions = dimensions

    def process_bind_param(self, value, dialect):
        if value is not None:
            if not isinstance(value, list):
                value = list(value)
            # 确保所有元素都是 float 类型
            return [float(v) for v in value]
        return value

class KnowledgeData(Base):
    __tablename__ = "knowledge_data"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    collection_id = Column(UUID(as_uuid=True), ForeignKey('collections.id'), nullable=False)
    knowledge_base_id = Column(UUID(as_uuid=True), ForeignKey('knowledge_bases.id'), nullable=False)
    mode = Column(String(50), nullable=False)
    data = Column(String(2000), nullable=False)
    doc_name = Column(String(255))
    prompt = Column(String(50))
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    is_active = Column(Boolean, nullable=False, default=True)
    deleted_at = Column(DateTime)
    embedding_vector = Column(ARRAY(Float(precision=53)))  # 直接使用 ARRAY(Float) 而不是自定义 Vector 类型

class AppInfo(Base):
    __tablename__ = "app_info"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default="uuid_generate_v4()")
    app_name = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now) 