from datetime import datetime
from pydantic import BaseModel
from typing import  List, Dict,Any


class Message(BaseModel):
    content: str
    createAt: int
    id: str
    updateAt: int
    message: str
    role: str
    meta: Dict[str, Any]  # 确保 meta 是一个字典

class ChatResponse(BaseModel):
    app_info: str
    conversation_id: str
    extra: Dict[str, Any]
    user_id: int
    user_name: str
    messages: List[Any]
    kb_id: List[str] | None = None

class ChatRequest(BaseModel):
    app_info: str
    conversation_id: str
    extra: Dict[str, Any]
    user_id: int | None = None
    user_name: str
    messages: List[Any]
    kb_id: List[str] | None = None

class KnowledgeBaseRequest(BaseModel):
    user_id: int


class getContextFromLangflowRequest(BaseModel):
    params: List[str] 
    client_signature: str
    query: str


    