"""
知识图谱检索器 - 纯图结构检索
"""


from typing import Dict, List, Any, Optional, Set
from .llm_client import LLMClient
from .neo4j_client import Neo4jClient
from app.utils.promptStr import QueryEntityExtractionPrompt
from .settings import wisegraph_settings
from loguru import logger


class GraphRetriever:
    """知识图谱检索器 - 实现纯图结构检索"""
    
    def __init__(self):
        self.llm_client = LLMClient()
        self.neo4j_client = Neo4jClient()
        self.is_connected = False
        self.config = wisegraph_settings.graph_query_config
    
    async def initialize(self):
        """初始化连接"""
        try:
            await self.neo4j_client.connect()
            self.is_connected = True
            logger.info("GraphRetriever初始化成功")
        except Exception as e:
            logger.error(f"GraphRetriever初始化失败: {e}")
            raise
    
    async def close(self):
        """关闭连接"""
        if self.is_connected:
            await self.neo4j_client.close()
            self.is_connected = False
            logger.info("GraphRetriever连接已关闭")
    
    async def retrieve(self, query: str, max_depth: int = None, max_results: int = None, knowledge_base_id: str = None) -> Dict[str, Any]:
        """
        纯图结构检索

        Args:
            query: 用户查询
            max_depth: 最大查询深度
            max_results: 最大结果数量
            knowledge_base_id: 知识库ID（可选，用于知识库隔离）

        Returns:
            检索结果，包含上下文信息
        """
        if not self.is_connected:
            raise RuntimeError("GraphRetriever未初始化，请先调用initialize()")
        
        logger.info(f"开始图检索，查询: {query}")
        
        # 使用配置的默认值
        max_depth = max_depth or self.config["max_depth"]
        max_results = max_results or self.config["max_results"]
        
        try:
            # 步骤1: 从查询中提取实体和关系类型
            query_analysis = await self.llm_client.extract_query_entities(
                query, QueryEntityExtractionPrompt
            )
            
            entities = query_analysis.get("entities", [])
            relation_types = query_analysis.get("relation_types", [])
            keywords = query_analysis.get("keywords", [])
            
            logger.info(f"查询分析结果 - 实体: {entities}, 关系类型: {relation_types}, 关键词: {keywords}")
            
            # 步骤2: 在图中搜索相关实体
            relevant_entities = await self._find_relevant_entities(entities, keywords, knowledge_base_id)
            
            if not relevant_entities:
                logger.warning("未找到相关实体")
                return {
                    "query": query,
                    "context": "",
                    "entities_found": [],
                    "subgraph": {"nodes": [], "relationships": []},
                    "chunk_ids": set()
                }
            
            logger.info(f"找到相关实体: {len(relevant_entities)}")
            
            # 步骤3: 获取实体的子图
            entity_ids = [entity["entity_id"] for entity in relevant_entities]
            subgraph = await self.neo4j_client.get_entity_subgraph(entity_ids, max_depth)
            
            logger.info(f"获取子图 - 节点: {len(subgraph['nodes'])}, 关系: {len(subgraph['relationships'])}")
            
            # 步骤4: 构建上下文
            context = self._build_context_from_subgraph(subgraph, query)
            
            # 步骤5: 收集相关的chunk_ids
            chunk_ids = self._collect_chunk_ids(subgraph)
            
            result = {
                "query": query,
                "context": context,
                "entities_found": relevant_entities,
                "subgraph": subgraph,
                "chunk_ids": chunk_ids
            }
            
            logger.info(f"图检索完成，上下文长度: {len(context)}")
            return result
            
        except Exception as e:
            logger.error(f"图检索失败: {e}")
            return {
                "query": query,
                "context": "",
                "entities_found": [],
                "subgraph": {"nodes": [], "relationships": []},
                "chunk_ids": set()
            }
    
    async def _find_relevant_entities(self, entities: List[str], keywords: List[str], knowledge_base_id: str = None) -> List[Dict[str, Any]]:
        """
        查找相关实体（只使用实体名称匹配，避免关键词匹配引入噪音）

        Args:
            entities: 提取的实体列表
            keywords: 关键词列表（已废弃，保留参数兼容性）
            knowledge_base_id: 知识库ID（可选）

        Returns:
            相关实体列表
        """
        try:
            # 只根据实体名称精确匹配（支持知识库过滤）
            if entities:
                relevant_entities = await self.neo4j_client.search_entities_by_names_with_kb(entities, knowledge_base_id)
                logger.info(f"按实体名称匹配到 {len(relevant_entities)} 个实体（知识库: {knowledge_base_id or '全部'}）")

                if relevant_entities:
                    entity_names = [entity["entity_name"] for entity in relevant_entities]
                    logger.debug(f"匹配到的实体: {entity_names}")

                return relevant_entities
            else:
                logger.warning("未提取到任何实体，无法进行图检索")
                return []

        except Exception as e:
            logger.error(f"查找相关实体失败: {e}")
            return []
    
    def _build_context_from_subgraph(self, subgraph: Dict[str, Any], query: str) -> str:
        """
        从子图构建上下文
        
        Args:
            subgraph: 子图数据
            query: 原始查询
            
        Returns:
            构建的上下文字符串
        """
        context_parts = []
        
        # 添加查询信息
        context_parts.append(f"查询: {query}\n")
        
        # 添加实体信息
        nodes = subgraph.get("nodes", [])
        if nodes:
            context_parts.append("相关实体:")
            for node in nodes:
                entity_info = f"- {node['entity_name']} ({node['entity_type']})"
                if node.get('description'):
                    entity_info += f": {node['description']}"
                context_parts.append(entity_info)
            context_parts.append("")
        
        # 添加关系信息
        relationships = subgraph.get("relationships", [])
        if relationships:
            context_parts.append("实体关系:")
            for rel in relationships:
                rel_info = f"- {rel['source']} --[{rel['relation_type']}]--> {rel['target']}"
                if rel.get('description'):
                    rel_info += f": {rel['description']}"
                context_parts.append(rel_info)
            context_parts.append("")
        
        # 组合上下文
        context = "\n".join(context_parts)
        return context
    
    def _collect_chunk_ids(self, subgraph: Dict[str, Any]) -> Set[str]:
        """
        收集子图中的chunk_ids
        
        Args:
            subgraph: 子图数据
            
        Returns:
            chunk_ids集合
        """
        chunk_ids = set()
        
        # 从节点收集chunk_ids
        for node in subgraph.get("nodes", []):
            if node.get("chunk_ids"):
                chunk_ids.update(node["chunk_ids"])
        
        # 从关系收集chunk_ids
        for rel in subgraph.get("relationships", []):
            if rel.get("chunk_ids"):
                chunk_ids.update(rel["chunk_ids"])
        
        return chunk_ids
    
    async def get_entity_details(self, entity_name: str) -> Dict[str, Any]:
        """
        获取特定实体的详细信息
        
        Args:
            entity_name: 实体名称
            
        Returns:
            实体详细信息
        """
        if not self.is_connected:
            raise RuntimeError("GraphRetriever未初始化，请先调用initialize()")
        
        entities = await self.neo4j_client.search_entities_by_names([entity_name])
        if entities:
            entity = entities[0]
            subgraph = await self.neo4j_client.get_entity_subgraph([entity["entity_id"]], 1)
            return {
                "entity": entity,
                "connected_entities": subgraph["nodes"],
                "relationships": subgraph["relationships"]
            }
        else:
            return {"entity": None, "connected_entities": [], "relationships": []}

    async def get_entity_subgraph_by_kb(self, entity_name: str, knowledge_base_id: str, max_depth: int = 2) -> Dict[str, Any]:
        """
        获取知识库内特定实体的子图

        Args:
            entity_name: 实体名称
            knowledge_base_id: 知识库ID
            max_depth: 查询深度

        Returns:
            实体子图数据
        """
        if not self.is_connected:
            raise RuntimeError("GraphRetriever未初始化，请先调用initialize()")

        try:
            # 在指定知识库中查找实体
            entities = await self.neo4j_client.search_entities_by_names_and_kb([entity_name], knowledge_base_id)

            if not entities:
                return {
                    "center_entity": None,
                    "nodes": [],
                    "relationships": [],
                    "depth": max_depth,
                    "stats": {"nodes": 0, "relationships": 0}
                }

            center_entity = entities[0]

            # 获取子图
            subgraph = await self.neo4j_client.get_entity_subgraph_by_kb(
                [center_entity["entity_id"]], knowledge_base_id, max_depth
            )

            return {
                "center_entity": center_entity,
                "nodes": subgraph["nodes"],
                "relationships": subgraph["relationships"],
                "depth": max_depth,
                "stats": {
                    "nodes": len(subgraph["nodes"]),
                    "relationships": len(subgraph["relationships"])
                }
            }

        except Exception as e:
            logger.error(f"获取实体子图失败: {e}")
            return {
                "center_entity": None,
                "nodes": [],
                "relationships": [],
                "depth": max_depth,
                "stats": {"nodes": 0, "relationships": 0}
            }

    async def get_knowledge_base_graph(self, knowledge_base_id: str, max_depth: int = 3, limit: int = 100, entity_types: List[str] = None) -> Dict[str, Any]:
        """
        获取知识库的完整图谱数据

        Args:
            knowledge_base_id: 知识库ID
            max_depth: 查询深度
            limit: 最大返回节点数
            entity_types: 过滤的实体类型

        Returns:
            知识库图谱数据
        """
        if not self.is_connected:
            raise RuntimeError("GraphRetriever未初始化，请先调用initialize()")

        try:
            # 获取知识库图谱概览
            graph_data = await self.neo4j_client.get_kb_graph_overview(
                knowledge_base_id, limit, entity_types
            )

            # 获取统计信息
            stats = await self.neo4j_client.get_kb_stats(knowledge_base_id)

            # 获取实体类型分布
            entity_type_stats = await self.neo4j_client.get_kb_entity_type_stats(knowledge_base_id)

            # 获取关系类型分布
            relation_type_stats = await self.neo4j_client.get_kb_relation_type_stats(knowledge_base_id)

            return {
                "knowledge_base_id": knowledge_base_id,
                "nodes": graph_data["nodes"],
                "relationships": graph_data["relationships"],
                "stats": stats,
                "summary": {
                    "entity_types": entity_type_stats,
                    "relation_types": relation_type_stats,
                    "max_depth": max_depth,
                    "limit": limit
                }
            }

        except Exception as e:
            logger.error(f"获取知识库图谱失败: {e}")
            return {
                "knowledge_base_id": knowledge_base_id,
                "nodes": [],
                "relationships": [],
                "stats": {"nodes": 0, "relationships": 0},
                "summary": {
                    "entity_types": [],
                    "relation_types": [],
                    "max_depth": max_depth,
                    "limit": limit
                }
            }

    def _extract_triplets_from_subgraph(self, subgraph: Dict[str, Any]) -> List[Dict[str, str]]:
        """
        从子图中提取三元组

        Args:
            subgraph: 子图数据

        Returns:
            三元组列表
        """
        triplets = []

        try:
            nodes = subgraph.get("nodes", [])
            relationships = subgraph.get("relationships", [])

            # 创建节点ID到名称的映射
            node_map = {node["entity_id"]: node["entity_name"] for node in nodes}

            # 从关系中提取三元组
            for rel in relationships:
                source_id = rel.get("source")
                target_id = rel.get("target")
                relation_type = rel.get("relation_type", "相关")
                description = rel.get("description", "")

                # 获取实体名称
                subject = node_map.get(source_id, source_id)
                object_name = node_map.get(target_id, target_id)

                triplet = {
                    "subject": subject,
                    "predicate": relation_type,
                    "object": object_name,
                    "description": description
                }
                triplets.append(triplet)

            logger.debug(f"从子图中提取到 {len(triplets)} 个三元组")
            return triplets

        except Exception as e:
            logger.error(f"提取三元组失败: {e}")
            return []

    def _format_triplets_for_llm(self, triplets: List[Dict[str, str]]) -> str:
        """
        将三元组格式化为LLM友好的上下文

        Args:
            triplets: 三元组列表

        Returns:
            格式化的上下文字符串
        """
        if not triplets:
            return ""

        context_lines = ["知识图谱上下文："]

        for triplet in triplets:
            subject = triplet["subject"]
            predicate = triplet["predicate"]
            obj = triplet["object"]

            # 格式：- (主体, 关系, 客体)
            context_lines.append(f"- ({subject}, {predicate}, {obj})")

        return "\n".join(context_lines)

    async def retrieve_for_wiseagent(self, query: str, knowledge_base_id: str = None, max_depth: int = 1) -> Dict[str, Any]:
        """
        为wiseagent项目提供图检索功能

        Args:
            query: 用户查询
            knowledge_base_id: 知识库ID
            max_depth: 查询深度

        Returns:
            包含三元组格式上下文的检索结果
        """
        if max_depth is None:
            max_depth = self.config.get("max_depth", 1)

        logger.info(f"[图检索] 开始检索 - query: {query}, kb_id: {knowledge_base_id}, depth: {max_depth}")

        try:
            # 步骤1: 使用LLM提取查询中的实体和关键词
            query_analysis = await self.llm_client.extract_query_entities(
                query, QueryEntityExtractionPrompt
            )

            entities = query_analysis.get("entities", [])
            keywords = query_analysis.get("keywords", [])  # 保留但不使用

            logger.info(f"[图检索] 提取实体: {entities}（关键词匹配已禁用，避免噪音）")

            # 步骤2: 在图中搜索相关实体（只使用实体名称匹配）
            relevant_entities = await self._find_relevant_entities(entities, keywords, knowledge_base_id)

            if not relevant_entities:
                logger.warning(f"[图检索] 未找到相关实体")
                return {
                    "success": False,
                    "context": "",
                    "triplets": [],
                    "entities_found": [],
                    "stats": {
                        "triplets_count": 0,
                        "entities_count": 0,
                        "depth": max_depth
                    }
                }

            # 步骤3: 获取实体的子图（优先使用知识库专用方法）
            entity_ids = [entity["entity_id"] for entity in relevant_entities]
            if knowledge_base_id:
                subgraph = await self.neo4j_client.get_entity_subgraph_by_kb(entity_ids, knowledge_base_id, max_depth)
            else:
                subgraph = await self.neo4j_client.get_entity_subgraph(entity_ids, max_depth)

            logger.info(f"[图检索] 获取子图 - 节点: {len(subgraph['nodes'])}, 关系: {len(subgraph['relationships'])}")

            # 步骤4: 提取三元组
            triplets = self._extract_triplets_from_subgraph(subgraph)

            # 步骤5: 格式化为LLM上下文
            context = self._format_triplets_for_llm(triplets)

            # 收集chunk_ids用于后续文档检索
            chunk_ids = set()
            for entity in relevant_entities:
                entity_chunk_ids = entity.get("chunk_ids", [])
                if entity_chunk_ids:  # 确保不是None
                    chunk_ids.update(entity_chunk_ids)
            for node in subgraph.get("nodes", []):
                node_chunk_ids = node.get("chunk_ids", [])
                if node_chunk_ids:  # 确保不是None
                    chunk_ids.update(node_chunk_ids)

            result = {
                "success": True,
                "context": context,
                "triplets": triplets,
                "entities_found": [entity["entity_name"] for entity in relevant_entities],
                "chunk_ids": list(chunk_ids),
                "stats": {
                    "triplets_count": len(triplets),
                    "entities_count": len(relevant_entities),
                    "depth": max_depth
                }
            }

            logger.info(f"[图检索] 检索完成 - 三元组: {len(triplets)}, 实体: {len(relevant_entities)}")
            return result

        except Exception as e:
            logger.error(f"[图检索] 检索失败: {e}")
            return {
                "success": False,
                "context": "",
                "triplets": [],
                "entities_found": [],
                "stats": {
                    "triplets_count": 0,
                    "entities_count": 0,
                    "depth": max_depth
                }
            }

neo4j = GraphRetriever()