"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9650],{75573:function(t,n){n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"}},97245:function(t,n,r){var e=r(1413),a=r(67294),u=r(75573),i=r(91146),c=function(t,n){return a.createElement(i.Z,(0,e.Z)((0,e.Z)({},t),{},{ref:n,icon:u.Z}))},s=a.forwardRef(c);n.Z=s},74573:function(t,n,r){r.d(n,{Ak:function(){return p},Sq:function(){return T},_5:function(){return y},s6:function(){return s},vr:function(){return k},xJ:function(){return m},yW:function(){return d}});var e=r(15009),a=r.n(e),u=r(99289),i=r.n(u),c=r(78158);function s(t){return o.apply(this,arguments)}function o(){return(o=i()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,c.N)("/api/auditTask/audit-task-types",{method:"GET",params:n}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function p(t){return f.apply(this,arguments)}function f(){return(f=i()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,c.N)("/api/auditTask/audit-tasks",{method:"GET",params:n}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function d(t){return h.apply(this,arguments)}function h(){return(h=i()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,c.N)("/api/auditTask/audit-tasks/".concat(n),{method:"GET"}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function k(t){return l.apply(this,arguments)}function l(){return(l=i()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,c.N)("/api/auditTask/audit-tasks",{method:"POST",data:n}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function m(t,n){return w.apply(this,arguments)}function w(){return(w=i()(a()().mark((function t(n,r){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,c.N)("/api/auditTask/audit-tasks/".concat(n),{method:"PUT",data:r}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function y(t){return v.apply(this,arguments)}function v(){return(v=i()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,c.N)("/api/auditTask/audit-tasks/".concat(n),{method:"DELETE"}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function T(t){return b.apply(this,arguments)}function b(){return(b=i()(a()().mark((function t(n){return a()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,c.N)("/api/auditTask/audit-tasks/".concat(n,"/files"),{method:"GET"}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}}}]);