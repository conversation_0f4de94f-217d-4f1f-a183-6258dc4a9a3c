本服务用于监控计算资源的使用情况，并提供告警功能。

包括：内容包括：

1. 监控：用于监控计算资源的使用情况，并提供告警功能。
2. 收集：用于采集计算资源的使用情况，并提供告警功能。

技术路线：

1. 存储；influxdb:2.7
2. 数据收集，包括：CPU、内存、磁盘、网络、GPU 卡的使用情况
3. 架构： 3.1. 监控端：用于监控计算资源的使用情况，并提供告警功能。部署在计算资源上。使用 python 提供 API 收集接口 3.2. 收集端：用于采集计算资源的使用情况，存储于 mongodb。使用 python 定时任务执行。
4. 安全控制：监控端进行 key 验证，收集端访问时提供 key 验证。

代码：

1. 翻两个文件夹，一个用于监控端，一个用于收集端。
2. 每个文件夹设置一个.env 文件，用于存储 key。
3. 每个文件夹设置一个 main.py 文件，用于执行主程序。
4. 每个文件夹设置一个 requirements.txt 文件，用于存储依赖。
5. 每个文件夹设置一个 Dockerfile 文件，用于构建 docker 镜像。
6. 每个文件夹设置一个 docker-compose.yml 文件，用于启动服务。
7. 每个文件夹设置一个 README.md 文件，用于描述服务,初始化说明。

收集端功能：

1. 启动定时任务，启动时从 mongodb 中获取服务器信息，包括地址、端口、key、采集频率（时间公式）、然后启动定时任务，定时任务执行时，从监控端获取数据，并存储到 mongodb 中。
2. 提供 API 服务接口，包括，新建、删除、修改、查询服务器信息；检索服务器状态（可根据时间段返回监控信息）。

监控端功能

1. 提供服务器基本信息收集接口，包括操作系统信息、配置信息；
2. 提供监控数据收集接口，包括 CPU、内存、磁盘、网络、GPU 卡的使用情况。
