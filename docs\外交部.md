# 离线数据检索接口
通过此接口可以基于向量相似度进行外交信息检索。
接口信息
URL: /api/wjb/data-query-offline
方法: POST
认证要求: 无需认证
Content-Type: application/json
## 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| app_info | String | 是 | 应用标识符，用于获取相应的应用配置 DiplomaticIntelligentSearch |
| query | String | 是 | 检索查询语句 |
| messages | Array | 否 | 历史对话消息列表，用于优化查询 |
| threshold | Float | 否 | 相似度阈值，默认值为 0.85 |

## 响应内容

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | Boolean | 接口调用是否成功 |
| message | String | 接口调用结果说明 |
| data | Array | 检索结果列表 |
| total | Integer | 结果总数 |

##  结果字段说明
| 字段名 | 类型 | 描述 |
|--------|------|------|
| title | String | 文章标题 |
| url | String | 文章链接 |
| contentSummary | String | 内容摘要 |
| summaryFacts | String | 关键事实总结 |
| authorViewpoint | String | 作者观点 |
| authorAttitude | String | 作者态度 |
| characterEntity | Array | 人物实体 |
| eventInfo | Array | 事件信息 |
| institutionalEntities | Array | 机构实体 |
| locationEntity | Array | 地点实体 |
| score | Float | 相似度分数 |



#  离线数据分析对话接口
基于检索结果进行AI分析和对话的接口。
接口信息
URL: /api/wjb/data-analysis-offline
方法: POST
认证要求: 无需认证
Content-Type: application/json
响应类型: text/event-stream (流式响应)

# 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| app_info | String | 是 | 应用标识符 |
| conversation_id | String | 是 | 会话ID |
| user_id | String | 是 | 用户ID |
| user_name | String | 是 | 用户名称 |
| messages | Array | 是 | 对话消息列表 |

# messages对象结构
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | String | 消息ID |
| role | String | 角色（user/assistant） |
| content | String | 消息内容 |
| references | Array | 参考资料列表 |

响应内容
响应为Server-Sent Events (SSE)格式的流式内容：
ended
# 事件类型
| 事件类型 | 描述 |
|---------|------|
| answer | AI回答的内容片段 |
| end | 流结束标志 |
| error | 错误信息 |


