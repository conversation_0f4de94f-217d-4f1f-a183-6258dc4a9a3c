import os
from functools import cache
from typing import Type<PERSON>lias
from app.db.mongodb import db

from app.utils.config import settings
from langchain_ollama import ChatOllama
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from app.models.llm import LLMModel, Provider
from app.models.embedding import EmbeddingModel
ModelT: TypeAlias = (
    ChatOpenAI
)

@cache
async def get_embedding_model(emb_id) -> OpenAIEmbeddings:
    embedding: EmbeddingModel= await db["embeddings"].find_one({"id": int(emb_id)})
    embedding_name = embedding.get('embedding_name',None)
    api_key = embedding.get('api_key',None)
    service_url = embedding.get('service_url',None)

    return OpenAIEmbeddings(
        model=embedding_name ,
        openai_api_key=api_key,
        openai_api_base=service_url
    )

@cache
async def get_model(model_id, stream:bool = True) -> ModelT:
    """
    获取LLM模型实例

    Args:
        stream: 是否启用流式响应，默认为True

    Returns:
        配置好的LLM模型实例
    """
    llm: LLMModel= await db["llms"].find_one({"id": int(model_id)})
    model_name = llm.get('m_name', None)
    api_key = llm.get('api_key', None)
    temperature = llm.get('temperature', 0.1)
    service_url = llm.get('service_url', None)
    
    return ChatOpenAI(
                model=model_name,
                temperature=temperature,
                openai_api_key=api_key,
                openai_api_base=service_url,
                streaming=stream,  # 使用streaming而不是stream参数名，避免警告
            )


