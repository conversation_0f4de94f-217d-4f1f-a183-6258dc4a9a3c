from motor.motor_asyncio import AsyncIOMotorClient
from ..utils.config import settings
from app.utils.logging_config import setup_logging, get_logger
setup_logging()
logger = get_logger(__name__)

class MongoDBManager:
    _instance = None
    _initialized = False
    _client = None
    _db = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        pass
    
    def _init_sync(self):
        """同步初始化数据库连接"""
        try:
            mongodb_url = settings.MONGODB_URL
            logger.info(f"数据库连接地址: {mongodb_url}")
            self._client = AsyncIOMotorClient(mongodb_url)
            self._db = self._client[settings.DATABASE_NAME]
            self._initialized = True
            logger.info("MongoDB connection initialized")
        except Exception as e:
            logger.error(f"MongoDB connection failed: {str(e)}")
            raise

    async def connect(self):
        """初始化并验证连接"""
        logger.info("开始初始化MongoDB连接")
        if not self._initialized:
            self._init_sync()
        
        try:
            await self._client.admin.command('ping')
            logger.info("MongoDB connection verified")
        except Exception as e:
            logger.error(f"MongoDB connection verification failed: {str(e)}")
            raise

    async def disconnect(self):
        if self._client:
            self._client.close()
            self._client = None
            self._db = None
            self._initialized = False
            logger.info("MongoDB disconnected")

    @property
    def db(self):
        if not self._initialized:
            self._init_sync()
        return self._db

mongodb = MongoDBManager()
db = mongodb.db
