import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import pytest
from httpx import AsyncClient
from unittest.mock import AsyncMock, patch
from app.utils.llmClient import stream_openai_api

@pytest.mark.asyncio
async def test_stream_openai_api():
    # 模拟数据
    api_key = "sk-6e94RWvIwV5TmX3M846529EeCf2b4d9b86A2566a4d4bA242"
    model = "Qwen1.5-14B-Chat"
    messages = [{"role": "user", "content": "你好"}]
    url = "http://api.roardata.cn/v1"

    # 模拟响应内容
    mock_response_content = [
        'data: {"choices":[{"delta":{"content":"你"}}]}\n\n',
        'data: {"choices":[{"delta":{"content":"好"}}]}\n\n',
        'data: {"choices":[{"delta":{"content":"！"}}]}\n\n',
        'data: [DONE]\n\n'
    ]

    # 创建模拟的 AsyncClient
    mock_client = AsyncMock(spec=AsyncClient)
    mock_response = AsyncMock()
    mock_response.aiter_bytes.return_value = (s.encode('utf-8') for s in mock_response_content)
    mock_client.post.return_value = mock_response

    # 使用 patch 替换 httpx.AsyncClient
    with patch('app.utils.llmClient.httpx.AsyncClient', return_value=mock_client):
        # 调用被测试的函数
        result = []
        async for chunk in stream_openai_api(api_key, model, messages, url):
            result.append(chunk)

    # 验证结果
    expected_result = mock_response_content
    assert result == expected_result

    # 验证 API 调用
    mock_client.post.assert_called_once_with(
        f'{url}/chat/completions',
        headers={
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        },
        json={
            "model": model,
            "messages": messages,
            "stream": True
        }
    )
