#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
金融舆情分析智能体
定期从ES中获取微信公众号数据，进行分析并生成报告
基于STORM结构化推理方式改进报告生成过程
"""

import json
import logging
import traceback
import signal
import threading
import platform
import os
import functools
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
from contextlib import contextmanager
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, TypedDict, Callable

import pandas as pd
from elasticsearch import Elasticsearch
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain_core.prompts import ChatPromptTemplate
from langgraph.graph import StateGraph, END

# 自定义超时异常
class TimeoutException(Exception):
    pass

# 跨平台的超时函数装饰器
def timeout(seconds):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 记录开始时间
            start_time = datetime.now()
            func_name = func.__name__
            logger.info(f"开始执行函数 {func_name} 并设置 {seconds} 秒超时...")
            
            # 使用线程池执行函数
            with ThreadPoolExecutor(max_workers=1) as executor:
                # 提交任务到线程池
                future = executor.submit(func, *args, **kwargs)
                try:
                    # 等待任务完成，超时则抛出异常
                    result = future.result(timeout=seconds)
                    # 记录执行时间
                    elapsed = (datetime.now() - start_time).total_seconds()
                    logger.info(f"函数 {func_name} 成功执行完毕，耗时 {elapsed:.2f} 秒")
                    return result
                except concurrent.futures.TimeoutError:
                    # 超时后取消任务（注意：这只是设置取消标志，线程会继续执行）
                    future.cancel()
                    elapsed = (datetime.now() - start_time).total_seconds()
                    logger.error(f"函数 {func_name} 执行超时! 已运行 {elapsed:.2f} 秒，超过了设定的 {seconds} 秒限制")
                    raise TimeoutException(f"操作超时（{seconds}秒）- 函数 {func_name} 执行时间过长")
        return wrapper
    return decorator

# 检测是否为Unix平台（支持SIGALRM）
IS_UNIX = platform.system() in ('Linux', 'Darwin') and os.name == 'posix'

if IS_UNIX:
    # 在Unix平台上使用signal.SIGALRM实现超时
    @contextmanager
    def timeout_context(seconds):
        def handle_timeout(signum, frame):
            raise TimeoutException(f"操作超时（{seconds}秒）")

        # 设置信号处理器
        original_handler = signal.getsignal(signal.SIGALRM)
        signal.signal(signal.SIGALRM, handle_timeout)
        
        try:
            # 设置定时器
            signal.alarm(seconds)
            yield
        finally:
            # 取消定时器
            signal.alarm(0)
            # 恢复原始信号处理器
            signal.signal(signal.SIGALRM, original_handler)
else:
    # 在非Unix平台上使用线程池实现超时
    @contextmanager
    def timeout_context(seconds):
        def do_nothing():
            pass  # 占位函数，用于超时测试
        
        # 使用线程池和Future
        with ThreadPoolExecutor(max_workers=1) as executor:
            # 提交一个简单的任务，仅用于测试超时
            future = executor.submit(do_nothing)
            
            try:
                # 尝试等待任务完成（但我们实际上只是在测试超时机制）
                future.result(timeout=0.001)  # 确保这会立即返回
                # 现在让主任务运行
                yield
                # 注意：我们不能在这里检查是否超时，因为yield可能会长时间运行
            except concurrent.futures.TimeoutError:
                # 这不应该发生，因为do_nothing应该立即返回
                logger.warning("初始化超时上下文出错")
                yield
                
        # 超时上下文结束后，原任务应该已经完成或被取消

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ES配置
TARGET_ES_HOST = "**************"
TARGET_ES_PORT = 9600
TARGET_ES_INDEX = "pro_mcp_data_weixin"

# 大模型参数

# LLM_API_BASE = "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen3_32b_2/v1"
# OPENAI_API_KEY = "ZmYzMmQ1N2E2YWZjMzcwN2Y3NTBjNmQzYjk3Njk3ZWY4Njk5MWQ0ZQ=="
# LLM_MODEL = "Qwen3-32B"





# LLM_MODEL="Qwen/Qwen2.5-72B-Instruct-128K"
# LLM_API_BASE="https://api.siliconflow.cn/v1"
# OPENAI_API_KEY="sk-jexorjtcpbtmhtnxyqcvlzbkxjhsqaqzvkrniotauxuzjlmn"




LLM_MODEL="Qwen/Qwen3-32B"
LLM_API_BASE="https://api.siliconflow.cn/v1"
OPENAI_API_KEY="sk-vkmolqivosbciwqhybrjqcpgopurxdmgmbkiliwznxdgunqn"
# OPENAI_API_KEY="sk-jexorjtcpbtmhtnxyqcvlzbkxjhsqaqzvkrniotauxuzjlmn"

# 公众号ID列表
BIZ_LIST = [
    "MzA4MDY1NTUyMg==",
    "MzA5OTY5MzM4Ng==",
    "Mzg2Mjg1NTg3NA==",
    "Mzg4NTEwMzA5NQ==",
    "Mzg5MjU4MDkyMw==",
    "MzI4NTU0NDE4Mw==",
    "MzI5MzQxOTI0MQ==",
    "MzIwMDI3NjM2Mg==",
    "MzIzMjc3NTYyNQ==",
    "Mzk0NjUwMDIxOQ==",
    "MzkxMDYyNzExMA==",
    "MzkzNTYzMDYxMg==",
    "MzU3MDMwODc2MA==",
    "MzU4NzcwNDcxOA==",
    "MzU4ODM4NzI5Nw==",
    "MzU5MzkzMTY3Mg==",
    "MzUxMDk5NDgwNQ==",
    "MzUxMDkyMzA4Mw==",
    "MzUzMzEyODIyMA==",
    "MzUzNDcxMDgzNg==",
    "MzA3MTIzNDcwMg==",
    "MzA3NjU1NTQwMA==",
    "MzA4MzY2ODYwMw==",
    "MzAwNzMxODYyNg==",
    "Mzg4NDU2MDM3Mw==",
    "MzI1NzAwODc3Nw==",
    "MzU3MDMwODc2MA==",
    "MzU3NTYyNTIyMQ==",
    "MzUzMzYwODI2MA=="
]

# 初始化大模型
llm = ChatOpenAI(
    temperature=0, 
    model=LLM_MODEL, 
    openai_api_base=LLM_API_BASE,
    api_key=OPENAI_API_KEY,
    request_timeout=120  # 设置120秒的请求超时时间
)

def safe_llm_invoke(messages, timeout_seconds=120, default_response=None, description="LLM调用"):
    """安全的LLM调用，带有超时处理和错误恢复"""
    logger.info(f"开始{description}，设置超时时间: {timeout_seconds}秒")
    try:
        # 使用timeout装饰器包装llm.invoke调用
        @timeout(timeout_seconds)
        def invoke_with_timeout(msgs):
            return llm.invoke(msgs)
        
        # 调用包装后的函数
        result = invoke_with_timeout(messages)
        logger.info(f"{description}完成，返回内容长度: {len(result.content)}")
        return result
    except TimeoutException as e:
        logger.error(f"{description}超时: {str(e)}")
        if default_response is not None:
            if isinstance(default_response, str):
                return AIMessage(content=default_response)
            return default_response
        raise
    except Exception as e:
        logger.error(f"{description}失败: {str(e)}")
        traceback.print_exc()
        if default_response is not None:
            if isinstance(default_response, str):
                return AIMessage(content=default_response)
            return default_response
        raise

def get_es_scroll_data_batched(index, query_body, batch_size=1000, es_host=None, es_port=None):
    """滚动查询ES数据，并以批次方式返回"""
    if not es_host:
        es_host = TARGET_ES_HOST
    if not es_port:
        es_port = TARGET_ES_PORT
        
    es = Elasticsearch([f"{es_host}:{es_port}"])
    
    sid = None
    try:
        # 初始搜索
        result = es.search(index=index, scroll='10m', body=query_body, size=batch_size, request_timeout=3600)
        sid = result['_scroll_id']
        scroll_size = result['hits']['total']['value']
        logger.info(f"索引 {index} 总数据量: {scroll_size}")
        
        # 如果有结果，返回第一批数据
        if len(result['hits']['hits']) > 0:
            yield result['hits']['hits']
        
        # 继续滚动直到没有更多数据
        scroll_count = len(result['hits']['hits'])
        while scroll_count > 0:
            result = es.scroll(scroll_id=sid, scroll='10m', request_timeout=3600)
            batch_data = result['hits']['hits']
            scroll_count = len(batch_data)
            if scroll_count == 0:
                break
            yield batch_data
            
    except Exception as e:
        logger.error(f"获取索引 {index} 数据时出错: {str(e)}")
        traceback.print_exc()
    finally:
        if sid:
            try:
                es.clear_scroll(scroll_id=sid)
            except:
                pass
            
        if es:
            try:
                es.close()
            except:
                pass
        logger.info(f"索引 {index} 查询完成")

def build_query_body(start_time, end_time, biz=None):
    """构建ES查询体"""
    body = {
        "query": {
            "bool": {
                "filter": [
                    {
                        "range": {
                            "createTimeES": {
                                "gte": start_time,
                                "lte": end_time
                             }
                        }
                    }
                ]
            }
        },
        "sort": [{"createTimeES": "desc"}]  # 按createTimeES降序排序
    }
    
    # 如果指定了biz参数，则添加biz过滤条件
    if biz:
        # 如果biz是字符串，则使用term查询
        if isinstance(biz, str):
            body["query"]["bool"]["filter"].append({
                "term": {
                    "site_id": {
                        "value": biz
                    }
                }
            })
        # 如果biz是列表，则使用terms查询
        elif isinstance(biz, list) and len(biz) > 0:
            body["query"]["bool"]["filter"].append({
                "terms": {
                    "site_id": biz
                }
            })
        
    return body

def get_recent_reports(hours=24):
    """获取近24小时内的报告数据"""
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=hours)
    
    # 格式化为字符串
    end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
    start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
    
    logger.info(f"获取时间范围: {start_time_str} 至 {end_time_str}")
    
    # 构建查询体
    query_body = build_query_body(start_time_str, end_time_str, BIZ_LIST)
    
    # 获取数据
    all_data = []
    for batch in get_es_scroll_data_batched(TARGET_ES_INDEX, query_body):
        all_data.extend([doc['_source'] for doc in batch])
    
    logger.info(f"获取到 {len(all_data)} 条记录")
    return all_data

def get_latest_report_by_account(days=3):
    """获取每个公众号最近3天的最新一篇报告"""
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)
    
    # 格式化为字符串
    end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
    start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
    
    logger.info(f"获取最新报告时间范围: {start_time_str} 至 {end_time_str}")
    
    latest_reports = {}
    
    for biz in BIZ_LIST:
        # 构建查询体
        query_body = build_query_body(start_time_str, end_time_str, biz)
        
        # 添加分页限制，只获取最新的一篇
        query_body["size"] = 1
        
        try:
            # 直接获取单条记录
            es = Elasticsearch([f"{TARGET_ES_HOST}:{TARGET_ES_PORT}"])
            result = es.search(index=TARGET_ES_INDEX, body=query_body)
            
            if result['hits']['hits']:
                doc = result['hits']['hits'][0]['_source']
                latest_reports[biz] = {
                    'title': doc.get('title', '无标题'),
                    'content': doc.get('content', '无内容'),
                    'author': doc.get('new_author', '未知作者'),
                    'site_name': doc.get('source', '未知公众号'),
                    'publishtime': doc.get('publishtime', '未知时间'),
                    'authorViewpoint': doc.get('authorViewpoint', ''),
                    'characterEntity': doc.get('characterEntity', []),
                    'institutionalEntities': doc.get('institutionalEntities', []),
                    'locationEntity': doc.get('locationEntity', []),
                    'eventInfo': doc.get('eventInfo', []),
                    'summaryFacts': doc.get('summaryFacts', []),
                    'summary': doc.get('summary', '')
                }
                logger.info(f"获取到公众号 {doc.get('source', biz)} 最新文章{doc.get('publishtime', '未知时间')}: {doc.get('title', '无标题')}")
        except Exception as e:
            logger.error(f"获取公众号 {biz} 最新报告时出错: {str(e)}")
        finally:
            if es:
                es.close()
    
    logger.info(f"获取到 {len(latest_reports)} 个公众号的最新报告")
    return latest_reports

def analyze_report_with_reasoning(title, content, account_name):
    """使用STORM结构化推理方式分析单个报告内容"""
    logger.info(f"开始分析来自 '{account_name}' 的报告: {title[:50]}...")
    # 步骤1: 初始提示
    system_msg = SystemMessage(content="""你是一位金融研究报告分析专家。请对以下公众号研究报告进行内容归纳，输出包括：
1. 核心观点（简明扼要）
2. 观点依据（数据、事实、逻辑支撑）
3. 其他重要信息（如市场影响、关键洞见等）
请用结构化JSON格式返回。""")

    human_msg = HumanMessage(content=f"""
请对以下来自"{account_name}"公众号的研究报告内容进行归纳总结，归纳总结的内容包括但不限于核心观点、观点依据等。

标题: {title}
内容: {content[:6000] if content and len(content) > 6000 else content}
""")
    
    # 执行初始思考
    logger.info(f"向LLM发送初始分析请求，内容长度: {len(content[:6000] if content and len(content) > 6000 else content)}")
    messages = [system_msg, human_msg]
    
    # 使用安全的LLM调用
    default_analysis = AIMessage(content="无法在规定时间内完成分析。")
    initial_analysis = safe_llm_invoke(
        messages, 
        timeout_seconds=120, 
        default_response=default_analysis,
        description=f"'{account_name}'报告初始分析"
    )
    
    # 步骤2: 要求生成结构化输出
    logger.info("开始生成结构化分析结果...")
    system_msg2 = SystemMessage(content="现在请基于你的分析，以JSON格式提供最终的结构化结果。")
    
    human_msg2 = HumanMessage(content="""请以JSON格式返回分析结果，包含以下字段:
1. summary: 整体摘要（不超过300字）
2. core_viewpoints: 核心观点列表（数组格式）
3. supporting_evidence: 观点依据列表（数组格式）
4. key_insights: 关键洞见（不超过200字）
5. market_impact: 对市场的潜在影响
6. reasoning_chain: 你的主要推理过程（简洁列表形式）""")
    
    try:
        messages2 = [system_msg2, initial_analysis, human_msg2]
        
        # 使用安全的LLM调用
        default_result = AIMessage(content="""
{
  "summary": "无法在规定时间内完成分析",
  "core_viewpoints": ["无法提取核心观点"],
  "supporting_evidence": ["无法提取支持证据"],
  "key_insights": "无法提取关键洞见",
  "market_impact": "无法分析市场影响",
  "reasoning_chain": ["超时，无法完成分析"]
}
""")
        result = safe_llm_invoke(
            messages2, 
            timeout_seconds=60, 
            default_response=default_result,
            description=f"'{account_name}'报告结构化分析"
        )
        content = result.content
        
        # 尝试解析JSON结果
        start_pos = content.find('{')
        end_pos = content.rfind('}') + 1
        if start_pos >= 0 and end_pos > start_pos:
            json_str = content[start_pos:end_pos]
            analysis = json.loads(json_str)
            # 保存推理过程
            analysis["reasoning_process"] = initial_analysis.content
            logger.info(f"成功解析结构化分析结果，包含 {len(analysis.get('core_viewpoints', []))} 条核心观点")
            return analysis
        else:
            logger.error("无法找到JSON内容")
            return {
                "summary": "无法解析报告内容",
                "core_viewpoints": [],
                "supporting_evidence": [],
                "key_insights": "",
                "market_impact": "无法分析",
                "reasoning_chain": []
            }
    except Exception as e:
        logger.error(f"分析报告内容时出错: {str(e)}")
        return {
            "summary": "分析过程出错",
            "core_viewpoints": [],
            "supporting_evidence": [],
            "key_insights": "",
            "market_impact": "分析出错",
            "reasoning_chain": []
        }

def analyze_all_reports_with_storm(reports_data):
    """使用STORM方法综合分析所有报告的观点"""
    logger.info(f"开始综合分析 {len(reports_data)} 份报告的观点...")
    # 提取所有报告的观点和数据
    all_viewpoints = []
    all_entities = set()
    all_institutions = set()
    all_events = set()
    report_summaries = []
    
    for biz, report in reports_data.items():
        # 收集观点
        if 'analysis' in report and report['analysis'].get('core_viewpoints'):
            for viewpoint in report['analysis']['core_viewpoints']:
                all_viewpoints.append({
                    'site_name': report['site_name'],
                    'viewpoint': viewpoint
                })
        if report.get('authorViewpoint'):
            all_viewpoints.append({
                'site_name': report['site_name'],
                'viewpoint': report['authorViewpoint']
            })
        
        # 收集实体和事件
        all_entities.update(report.get('characterEntity', []))
        all_institutions.update(report.get('institutionalEntities', []))
        all_events.update(report.get('eventInfo', []))
        
        # 收集摘要
        if report.get('summary'):
            report_summaries.append({
                'site_name': report['site_name'],
                'summary': report['summary']
            })
    
    logger.info(f"提取了 {len(all_viewpoints)} 个观点, {len(all_entities)} 个实体, {len(all_institutions)} 个机构, {len(all_events)} 个事件")
    
    # 构建数据摘要
    data_summary = {
        "total_reports": len(reports_data),
        "unique_entities": list(all_entities)[:10],  # 限制数量
        "unique_institutions": list(all_institutions)[:10],
        "unique_events": list(all_events)[:10],
        "viewpoint_count": len(all_viewpoints)
    }
    
    # 观点文本
    viewpoints_text = "\n\n".join([f"{item['site_name']}: {item['viewpoint']}" for item in all_viewpoints])
    logger.info(f"合成的观点文本长度: {len(viewpoints_text)}")
    
    # 步骤1: 初始分析
    logger.info("开始向LLM发送综合分析请求...")
    system_msg = SystemMessage(content="""\"你是一位资深金融市场分析师。请对以下多家机构的公众号研究报告进行多维度综合归纳，分析内容包括：
1. 宏观经济预期（并标注各观点来源）
2. 宏观政策预期（并标注各观点来源）
3. 利率走势预测（并标注各观点来源）
4. 投资建议（并标注各观点来源）
5. 共识观点（哪些机构持有相同观点）
6. 分歧观点（哪些机构观点不同）
7. 主要风险与机会（并标注来源）
请用结构化JSON格式返回，确保每个观点都注明出处。\"""")

    human_msg = HumanMessage(content=f"""
请对以下多家金融机构的研究报告观点进行归纳总结，归纳总结的维度包括但不限于:
1. 机构对宏观经济的预期
2. 对宏观政策的预期
3. 对利率走势的预测
4. 对投资的建议

数据概要:
- 总报告数: {data_summary['total_reports']}
- 涉及机构: {', '.join(data_summary['unique_institutions'][:5])}
- 主要事件: {', '.join(data_summary['unique_events'][:5])}
- 观点数量: {data_summary['viewpoint_count']}

非常重要：请在你的分析中标明每个观点的来源机构，确保读者知道每个观点是由哪家机构提出的。

以下是各家机构的观点:
{viewpoints_text}
""")
    
    # 执行初始思考 - 使用安全的LLM调用
    messages = [system_msg, human_msg]
    default_analysis = AIMessage(content="无法在规定时间内完成综合分析。")
    initial_analysis = safe_llm_invoke(
        messages, 
        timeout_seconds=180, 
        default_response=default_analysis,
        description="多机构报告综合分析"
    )
    
    # 步骤2: 要求生成结构化输出
    logger.info("开始生成结构化综合分析结果...")
    system_msg2 = SystemMessage(content="现在请基于你的分析，以JSON格式提供最终的结构化结果。请确保每个观点都标注其来源机构。")
    
    human_msg2 = HumanMessage(content="""请以JSON格式返回分析结果，包含以下字段:
1. macro_economic_outlook: 宏观经济展望总结（标注各观点来源机构）
2. policy_expectations: 政策预期总结（标注各观点来源机构）
3. interest_rate_forecast: 利率走势预测总结（标注各观点来源机构）
4. investment_recommendations: 投资建议总结（标注各观点来源机构）
5. consensus_points: 机构观点一致之处（数组，每项包含观点内容和支持该观点的机构列表）
6. divergent_points: 机构观点分歧之处（数组，每项包含观点内容和持该观点的机构）
7. overall_summary: 整体市场观点摘要（300字以内，提及主要观点来源）
8. market_risks: 当前市场主要风险点（数组，每项包含风险及提出该风险的机构）
9. market_opportunities: 当前市场主要机会（数组，每项包含机会及提出该机会的机构）
10. reasoning_chain: 你的主要推理链条（简洁列表）

确保在每个字段中都标明信息的来源机构，例如"根据A、B、C三家机构观点，宏观经济..."或使用格式"观点内容（来源：机构A、机构B）"。""")
    
    try:
        messages2 = [system_msg2, initial_analysis, human_msg2]
        
        # 使用安全的LLM调用
        default_result = AIMessage(content="""
{
  "macro_economic_outlook": "无法在规定时间内完成综合分析",
  "policy_expectations": "无法在规定时间内完成综合分析",
  "interest_rate_forecast": "无法在规定时间内完成综合分析",
  "investment_recommendations": "无法在规定时间内完成综合分析",
  "consensus_points": ["无法识别共识观点"],
  "divergent_points": ["无法识别分歧观点"],
  "overall_summary": "无法在规定时间内生成综合分析",
  "market_risks": ["无法识别市场风险"],
  "market_opportunities": ["无法识别市场机会"],
  "reasoning_chain": ["超时，无法完成分析"]
}
""")
        result = safe_llm_invoke(
            messages2, 
            timeout_seconds=120, 
            default_response=default_result,
            description="生成结构化综合分析"
        )
        content = result.content
        
        # 尝试解析JSON结果
        start_pos = content.find('{')
        end_pos = content.rfind('}') + 1
        if start_pos >= 0 and end_pos > start_pos:
            json_str = content[start_pos:end_pos]
            analysis = json.loads(json_str)
            # 保存推理过程
            analysis["reasoning_process"] = initial_analysis.content
            logger.info(f"成功解析综合分析结果，包含 {len(analysis.get('consensus_points', []))} 条共识观点, {len(analysis.get('divergent_points', []))} 条分歧观点")
            return analysis
        else:
            logger.error("无法找到综合分析的JSON内容")
            return {
                "macro_economic_outlook": "无法解析综合观点",
                "policy_expectations": "无法解析综合观点",
                "interest_rate_forecast": "无法解析综合观点",
                "investment_recommendations": "无法解析综合观点",
                "consensus_points": [],
                "divergent_points": [],
                "overall_summary": "无法生成综合分析",
                "market_risks": [],
                "market_opportunities": [],
                "reasoning_chain": []
            }
    except Exception as e:
        logger.error(f"综合分析报告内容时出错: {str(e)}")
        return {
            "macro_economic_outlook": "分析过程出错",
            "policy_expectations": "分析过程出错",
            "interest_rate_forecast": "分析过程出错",
            "investment_recommendations": "分析过程出错",
            "consensus_points": [],
            "divergent_points": [],
            "overall_summary": "分析过程出错",
            "market_risks": [],
            "market_opportunities": [],
            "reasoning_chain": []
        }

def generate_executive_summary(overall_analysis, reports_count):
    """生成执行摘要"""
    logger.info(f"开始为 {reports_count} 家机构的报告生成执行摘要...")
    
    # 格式化复杂结构为字符串，避免类型错误
    def format_complex_item(item):
        if isinstance(item, dict):
            if 'content' in item:
                return item['content']
            if 'point' in item:
                return item['point']
            return str(item)
        return str(item)
    
    # 格式化列表为逗号分隔的字符串
    def format_list_safely(items, max_items=3):
        if not items:
            return '无数据'
        if isinstance(items, str):
            return items
        if isinstance(items, list):
            formatted_items = [format_complex_item(item) for item in items[:max_items]]
            if len(items) > max_items:
                return ', '.join(formatted_items) + '等'
            return ', '.join(formatted_items)
        return str(items)
    
    system_msg = SystemMessage(content="""\"你是一位专业的金融市场分析师，需要为高管层撰写简明扼要的市场观点执行摘要。
请基于提供的综合分析数据，生成一份简短但信息量大的执行摘要。
重点突出最关键的市场趋势、共识观点、分歧点、主要风险和机会。
使用专业但易于理解的语言，避免冗长解释。

非常重要：在执行摘要中，请明确标注各个观点的信息来源（例如"根据A、B、C三家机构的观点..."或"...（来源：机构A、机构B）"）。
这样做的目的是让读者清楚了解各项信息的出处，提高报告的专业性和可信度。\"""")

    try:
        # 安全地获取各个字段值
        macro_economic = format_list_safely(overall_analysis.get('macro_economic_outlook', '无数据'))
        policy_expectations = format_list_safely(overall_analysis.get('policy_expectations', '无数据'))
        interest_rate = format_list_safely(overall_analysis.get('interest_rate_forecast', '无数据'))
        investment = format_list_safely(overall_analysis.get('investment_recommendations', '无数据'))
        consensus = format_list_safely(overall_analysis.get('consensus_points', '无共识'))
        divergent = format_list_safely(overall_analysis.get('divergent_points', '无分歧'))
        risks = format_list_safely(overall_analysis.get('market_risks', '未识别风险'))
        opportunities = format_list_safely(overall_analysis.get('market_opportunities', '未识别机会'))
        
        human_msg = HumanMessage(content=f"""
基于对{reports_count}家金融机构研报的分析，生成一份执行摘要，包含以下要点：

1. 宏观经济展望：{macro_economic}

2. 政策预期：{policy_expectations}

3. 利率预测：{interest_rate}

4. 投资建议：{investment}

5. 共识观点：{consensus}

6. 分歧点：{divergent}

7. 主要风险：{risks}

8. 市场机会：{opportunities}

请生成一份不超过300字的高管层执行摘要，确保标注每个观点的来源机构，使用"（来源：XX、XX机构）"或"据XX、XX机构认为..."等方式明确标注。""")
        
        logger.info("向LLM发送执行摘要生成请求...")
        messages = [system_msg, human_msg]
        
        # 使用安全的LLM调用
        default_summary = "无法在规定时间内生成执行摘要。请参考综合分析部分了解详细情况。"
        result = safe_llm_invoke(
            messages, 
            timeout_seconds=90, 
            default_response=default_summary,
            description="生成执行摘要"
        )
        logger.info(f"执行摘要生成完成，长度: {len(result.content)} 字符")
        return result.content
    except Exception as e:
        logger.error(f"生成执行摘要时出错: {str(e)}")
        traceback.print_exc()
        return "无法生成执行摘要。错误原因：" + str(e)

def generate_report_with_storm():
    """使用STORM方法生成完整的分析报告"""
    logger.info("==================== 开始使用STORM方法生成分析报告 ====================")
    # 1. 获取每个公众号近3天内最新的一篇报告
    logger.info("第1步: 获取每个公众号最新报告...")
    latest_reports = get_latest_report_by_account(days=1)
    logger.info(f"成功获取 {len(latest_reports)} 个公众号的最新报告")
    
    # 2. 分析每篇报告
    errors = []
    total = len(latest_reports)
    for idx, (biz, report) in enumerate(latest_reports.items(), 1):
        logger.info(f"正在分析第 {idx}/{total} 个公众号: {report['site_name']}")
        try:
            # 直接调用，内部已有超时处理
            analysis = analyze_report_with_reasoning(report['title'], report['content'], report['site_name'])
            latest_reports[biz]['analysis'] = analysis
            logger.info(f"完成第 {idx}/{total} 个公众号分析: {report['site_name']}")
        except Exception as e:
            error_msg = f"{report['site_name']} 分析失败: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
    
    # 3. 综合分析
    logger.info("第3步: 开始综合分析所有报告...")
    try:
        # 直接调用，内部已有超时处理
        overall_analysis = analyze_all_reports_with_storm(latest_reports)
        logger.info("综合分析完成")
    except Exception as e:
        error_msg = f"综合分析失败: {str(e)}"
        logger.error(error_msg)
        errors.append(error_msg)
        overall_analysis = {}
    
    # 4. 执行摘要
    logger.info("第4步: 开始生成执行摘要...")
    try:
        # 直接调用，内部已有超时处理
        executive_summary = generate_executive_summary(overall_analysis, len(latest_reports))
        logger.info("执行摘要生成完成")
    except Exception as e:
        error_msg = f"执行摘要生成失败: {str(e)}"
        logger.error(error_msg)
        errors.append(error_msg)
        executive_summary = "执行摘要生成失败。"
    
    # 5. 组装报告
    logger.info("第5步: 组装最终报告...")
    report = {
        "generation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "report_title": f"金融舆情分析报告 - {datetime.now().strftime('%Y年%m月%d日')}",
        "executive_summary": executive_summary,
        "individual_reports": latest_reports,
        "overall_analysis": overall_analysis,
        "metadata": {
            "reports_count": len(latest_reports),
            "generation_method": "STORM结构化推理",
            "time_range": f"近3天 (截至 {datetime.now().strftime('%Y-%m-%d')})"
        },
        "errors": errors
    }
    
    logger.info("==================== 报告生成过程完成 ====================")
    return report

def format_structured_list(items, content_key='content', source_key='sources'):
    lines = []
    for item in items:
        if isinstance(item, dict):
            content = item.get(content_key) or item.get('point') or item.get('risk') or item.get('opportunity') or ''
            sources = item.get(source_key) or item.get('institutions') or item.get('source') or []
            if isinstance(sources, list):
                sources = '、'.join(sources)
            if sources:
                lines.append(f"- {content}（来源：{sources}）")
            else:
                lines.append(f"- {content}")
        elif isinstance(item, str):
            lines.append(f"- {item}")
    return '\n'.join(lines)

def add_heading_numbering(md_text):
    lines = md_text.split('\n')
    h1, h2, h3 = 0, 0, 0
    new_lines = []
    for line in lines:
        if line.startswith('# '):
            h1 += 1; h2 = 0; h3 = 0
            new_lines.append(f"# {h1}. {line[2:]}")
        elif line.startswith('## '):
            h2 += 1; h3 = 0
            new_lines.append(f"## {h1}.{h2} {line[3:]}")
        elif line.startswith('### '):
            h3 += 1
            new_lines.append(f"### {h1}.{h2}.{h3} {line[4:]}")
        else:
            new_lines.append(line)
    return '\n'.join(new_lines)

def format_storm_report(report):
    """将STORM报告格式化为易于展示的Markdown格式"""
    logger.info("开始格式化STORM报告...")
    start_time = datetime.now()
    output = f"# {report['report_title']}\n\n"
    output += f"生成时间: {report['generation_time']}\n"
    output += f"分析报告数: {report['metadata']['reports_count']}\n"
    output += f"时间范围: {report['metadata']['time_range']}\n\n"
    
    # 执行摘要
    logger.info("添加执行摘要部分...")
    output += "## 执行摘要\n\n"
    output += f"{report['executive_summary']}\n\n"
    
    # 综合分析部分
    logger.info("添加市场综合分析部分...")
    output += "## 市场综合分析\n\n"
    overall = report['overall_analysis']
    
    # 市场观点摘要图表
    logger.info("添加市场观点摘要...")
    output += "### 市场观点摘要\n\n"
    output += f"{overall.get('overall_summary', '')}\n\n"
    
    # 核心指标表格
    logger.info("开始生成核心市场指标表格...")
    table_start_time = datetime.now()
    output += "### 核心市场指标\n\n"
    output += "| 指标类别 | 分析结果 |\n"
    output += "|---------|----------|\n"
    output += f"| 宏观经济展望 | {format_structured_list(overall.get('macro_economic_outlook', []))} |\n"
    output += f"| 政策预期 | {format_structured_list(overall.get('policy_expectations', []))} |\n"
    output += f"| 利率走势预测 | {format_structured_list(overall.get('interest_rate_forecast', []))} |\n"
    output += f"| 投资建议 | {format_structured_list(overall.get('investment_recommendations', []))} |\n\n"
    table_end_time = datetime.now()
    table_elapsed_time = (table_end_time - table_start_time).total_seconds()
    logger.info(f"核心市场指标表格生成完成，耗时: {table_elapsed_time:.2f} 秒")
    
    # 观点一致与分歧
    logger.info("添加市场观点分析部分...")
    output += "### 市场观点分析\n\n"
    
    logger.info("处理观点共识信息...")
    output += "#### 观点共识\n"
    consensus_points = overall.get('consensus_points', [])
    for point in consensus_points:
        # 检查point是否为字符串或具有结构（确保兼容性）
        if isinstance(point, str):
            output += f"- {point}\n"
        elif isinstance(point, dict) and 'content' in point and 'sources' in point:
            sources = ', '.join(point['sources']) if isinstance(point['sources'], list) else point['sources']
            output += f"- {point['content']} (来源: {sources})\n"
        else:
            output += f"- {point}\n"
    output += "\n"
    logger.info(f"已添加 {len(consensus_points)} 条共识观点")
    
    logger.info("处理观点分歧信息...")
    output += "#### 观点分歧\n"
    divergent_points = overall.get('divergent_points', [])
    for point in divergent_points:
        # 检查point是否为字符串或具有结构
        if isinstance(point, str):
            output += f"- {point}\n"
        elif isinstance(point, dict) and 'content' in point and 'sources' in point:
            sources = ', '.join(point['sources']) if isinstance(point['sources'], list) else point['sources']
            output += f"- {point['content']} (来源: {sources})\n"
        else:
            output += f"- {point}\n"
    output += "\n"
    logger.info(f"已添加 {len(divergent_points)} 条分歧观点")
    
    # 市场风险与机会
    logger.info("添加风险与机会部分...")
    output += "### 风险与机会\n\n"
    
    logger.info("处理主要风险信息...")
    output += "#### 主要风险\n"
    market_risks = overall.get('market_risks', [])
    for risk in market_risks:
        # 检查risk是否为字符串或具有结构
        if isinstance(risk, str):
            output += f"- {risk}\n"
        elif isinstance(risk, dict) and 'content' in risk and 'sources' in risk:
            sources = ', '.join(risk['sources']) if isinstance(risk['sources'], list) else risk['sources']
            output += f"- {risk['content']} (来源: {sources})\n"
        else:
            output += f"- {risk}\n"
    output += "\n"
    logger.info(f"已添加 {len(market_risks)} 条市场风险")
    
    logger.info("处理主要机会信息...")
    output += "#### 主要机会\n"
    market_opportunities = overall.get('market_opportunities', [])
    for opportunity in market_opportunities:
        # 检查opportunity是否为字符串或具有结构
        if isinstance(opportunity, str):
            output += f"- {opportunity}\n"
        elif isinstance(opportunity, dict) and 'content' in opportunity and 'sources' in opportunity:
            sources = ', '.join(opportunity['sources']) if isinstance(opportunity['sources'], list) else opportunity['sources']
            output += f"- {opportunity['content']} (来源: {sources})\n"
        else:
            output += f"- {opportunity}\n"
    output += "\n"
    logger.info(f"已添加 {len(market_opportunities)} 条市场机会")
    
    # 推理链条（如果存在）
    if overall.get('reasoning_chain'):
        logger.info("添加分析推理过程部分...")
        output += "### 分析推理过程\n\n"
        reasoning_chain = overall.get('reasoning_chain', [])
        for i, step in enumerate(reasoning_chain, 1):
            output += f"{i}. {step}\n"
        output += "\n"
        logger.info(f"已添加 {len(reasoning_chain)} 条推理步骤")
    
    # 各公众号分析
    logger.info("开始添加各机构观点详细分析部分...")
    detailed_start_time = datetime.now()
    output += "## 各机构观点详细分析\n\n"
    report_count = 0
    for biz, report_data in report['individual_reports'].items():
        if 'analysis' not in report_data:
            continue
            
        analysis = report_data['analysis']
        output += f"### {report_data['site_name']}\n\n"
        output += f"**标题**: {report_data['title']}\n\n"
        output += f"**发布时间**: {report_data['publishtime']}\n\n"
        output += f"**摘要**: {analysis.get('summary', '')}\n\n"
        
        output += "**核心观点**:\n"
        for point in analysis.get('core_viewpoints', []):
            output += f"- {point}\n"
        output += "\n"
        
        output += "**观点依据**:\n"
        for evidence in analysis.get('supporting_evidence', []):
            output += f"- {evidence}\n"
        output += "\n"
        
        if analysis.get('market_impact'):
            output += f"**市场影响**: {analysis.get('market_impact')}\n\n"
        
        output += f"**关键洞见**: {analysis.get('key_insights', '')}\n\n"
        output += "---\n\n"
        report_count += 1
    
    detailed_end_time = datetime.now()
    detailed_elapsed_time = (detailed_end_time - detailed_start_time).total_seconds()
    logger.info(f"已添加 {report_count} 个机构的详细分析，耗时: {detailed_elapsed_time:.2f} 秒")
    
    # 报告附录
    output += "## 附录\n\n"
    output += f"- 分析方法: {report['metadata']['generation_method']}\n"
    output += f"- 数据时间范围: {report['metadata']['time_range']}\n"
    output += f"- 报告生成时间: {report['generation_time']}\n"
    output += f"- 分析机构列表: {', '.join([report_data.get('site_name', '未知') for biz, report_data in report['individual_reports'].items() if 'analysis' in report_data])}\n\n"
    
    # 执行情况总结
    output += "## 执行情况总结\n\n"
    errors = report.get("errors", [])
    if errors:
        output += "本次报告生成过程中出现如下异常：\n"
        for err in errors:
            output += f"- {err}\n"
    else:
        output += "本次报告生成过程无异常。\n"
    
    end_time = datetime.now()
    total_elapsed_time = (end_time - start_time).total_seconds()
    logger.info(f"STORM报告格式化完成，总耗时: {total_elapsed_time:.2f} 秒")
    
    output = add_heading_numbering(output)
    return output

# LangGraph智能体定义
class AgentState(TypedDict):
    """智能体状态"""
    report_data: Optional[Dict]
    report_text: Optional[str]
    error: Optional[str]
    es_index: Optional[str]
    es_host: Optional[str]
    es_port: Optional[int]
    fetch_start_time: Optional[str]
    fetch_end_time: Optional[str]
    llm_model: Optional[str]
    llm_api_base: Optional[str]
    llm_api_key: Optional[str]

def fetch_and_analyze_storm(state: AgentState) -> AgentState:
    """使用STORM方法获取数据并分析"""
    try:
        logger.info("====== 智能体工作流: 开始生成金融舆情分析报告 (STORM方法) ======")
        start_time = datetime.now()
        es_index = state.get("es_index")
        es_host = state.get("es_host")
        es_port = state.get("es_port")
        fetch_start_time = state.get("fetch_start_time")
        fetch_end_time = state.get("fetch_end_time")
        llm_model = state.get("llm_model")
        llm_api_base = state.get("llm_api_base")
        llm_api_key = state.get("llm_api_key")
        report_data = generate_report_with_storm()
        end_time = datetime.now()
        elapsed_time = (end_time - start_time).total_seconds()
        logger.info(f"报告生成完成，耗时: {elapsed_time:.2f} 秒")
        state["report_data"] = report_data
        return state
    except Exception as e:
        logger.error(f"生成报告时出错: {str(e)}")
        traceback.print_exc()
        state["error"] = f"生成报告失败: {str(e)}"
        return state

def format_storm_report_node(state: AgentState) -> AgentState:
    """格式化STORM报告"""
    try:
        logger.info("====== 智能体工作流: 开始格式化报告 ======")
        start_time = datetime.now()
        if "report_data" in state and state["report_data"]:
            report_text = format_storm_report(state["report_data"])
            state["report_text"] = report_text
            end_time = datetime.now()
            elapsed_time = (end_time - start_time).total_seconds()
            logger.info(f"报告格式化完成，耗时: {elapsed_time:.2f} 秒")
        return state
    except Exception as e:
        logger.error(f"格式化报告时出错: {str(e)}")
        traceback.print_exc()
        state["error"] = f"格式化报告失败: {str(e)}"
        return state

# 创建工作流
workflow = StateGraph(AgentState)
workflow.add_node("fetch_and_analyze_storm", fetch_and_analyze_storm)
workflow.add_node("format_storm_report", format_storm_report_node)
workflow.add_edge("fetch_and_analyze_storm", "format_storm_report")
workflow.add_edge("format_storm_report", END)
workflow.set_entry_point("fetch_and_analyze_storm")
workflow = workflow.compile()

def run_agent(days=1):
    """运行金融舆情分析智能体"""
    logger.info("====================== 开始运行金融舆情分析智能体 ======================")
    start_time = datetime.now()
    fetch_end_time = datetime.now()
    fetch_start_time = fetch_end_time - timedelta(days=days)
    initial_state = {
        "report_data": None,
        "report_text": None,
        "error": None,
        "es_index": TARGET_ES_INDEX,
        "es_host": TARGET_ES_HOST,
        "es_port": TARGET_ES_PORT,
        "fetch_start_time": fetch_start_time.strftime("%Y-%m-%d %H:%M:%S"),
        "fetch_end_time": fetch_end_time.strftime("%Y-%m-%d %H:%M:%S"),
        "llm_model": LLM_MODEL,
        "llm_api_base": LLM_API_BASE,
        "llm_api_key": OPENAI_API_KEY,
    }
    
    try:
        logger.info("启动LangGraph工作流...")
        result = workflow.invoke(initial_state)
        
        if result.get("error"):
            logger.error(f"智能体运行出错: {result['error']}")
            return {"status": "error", "message": result["error"]}
            
        if result.get("report_text"):
            end_time = datetime.now()
            elapsed_time = (end_time - start_time).total_seconds()
            logger.info(f"金融舆情分析报告生成成功，总耗时: {elapsed_time:.2f} 秒")
            return {
                "status": "success",
                "report_text": result["report_text"],
                "report_data": result["report_data"]
            }
        else:
            logger.error("未能生成报告文本")
            return {"status": "error", "message": "未能生成报告文本"}
            
    except Exception as e:
        logger.error(f"运行智能体时出错: {str(e)}")
        traceback.print_exc()
        return {"status": "error", "message": str(e)}

# 建议添加配置项：
ANALYSIS_CONFIG = {
    "max_days": 3,  # 最大获取天数
    "min_word_count": 500,  # 最小文章字数
    "max_retries": 3,  # 分析失败重试次数
    "timeout": {
        "single_analysis": 120,  # 单篇分析超时时间
        "overall_analysis": 180,  # 综合分析超时时间
    }
}

if __name__ == "__main__":
    result = run_agent()
    
    if result["status"] == "success":
        print("报告生成成功！")
        
        # 保存报告到文件
        report_filename = f"financial_report_storm_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        try:
            with open(report_filename, "w", encoding="utf-8") as f:
                f.write(result["report_text"])
            print(f"报告已保存至: {report_filename}")
        except Exception as e:
            print(f"保存报告失败: {str(e)}")
        
        # 打印报告摘要
        lines = result["report_text"].split("\n")
        preview_lines = lines[:20] + ["...", "（报告内容省略）", "..."] + lines[-5:]
        print("\n".join(preview_lines))
    else:
        print(f"报告生成失败: {result['message']}")
