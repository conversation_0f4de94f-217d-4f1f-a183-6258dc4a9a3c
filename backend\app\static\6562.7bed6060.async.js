"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6562],{47046:function(e,o){o.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},79457:function(e,o,r){r.d(o,{Z:function(){return i}});var n=r(1413),t=r(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656zM484 366h56c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zM302 548h56c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm364 0h56c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-182 0h56c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm0 182h56c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8z"}}]},name:"border-outer",theme:"outlined"},a=r(91146),l=function(e,o){return t.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:o,icon:c}))};var i=t.forwardRef(l)},79090:function(e,o,r){var n=r(1413),t=r(67294),c=r(15294),a=r(91146),l=function(e,o){return t.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:o,icon:c.Z}))},i=t.forwardRef(l);o.Z=i},58831:function(e,o,r){r.d(o,{Z:function(){return i}});var n=r(1413),t=r(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M136 384h56c4.4 0 8-3.6 8-8V200h176c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H196c-37.6 0-68 30.4-68 68v180c0 4.4 3.6 8 8 8zm512-184h176v176c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V196c0-37.6-30.4-68-68-68H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zM376 824H200V648c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v180c0 37.6 30.4 68 68 68h180c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm512-184h-56c-4.4 0-8 3.6-8 8v176H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h180c37.6 0 68-30.4 68-68V648c0-4.4-3.6-8-8-8zm16-164H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"}}]},name:"scan",theme:"outlined"},a=r(91146),l=function(e,o){return t.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:o,icon:c}))};var i=t.forwardRef(l)},87784:function(e,o,r){r.d(o,{Z:function(){return i}});var n=r(1413),t=r(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"},a=r(91146),l=function(e,o){return t.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:o,icon:c}))};var i=t.forwardRef(l)},88484:function(e,o,r){r.d(o,{Z:function(){return i}});var n=r(1413),t=r(67294),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"},a=r(91146),l=function(e,o){return t.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:o,icon:c}))};var i=t.forwardRef(l)},89514:function(e,o,r){var n=r(1413),t=r(67294),c=r(66995),a=r(91146),l=function(e,o){return t.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:o,icon:c.Z}))},i=t.forwardRef(l);o.Z=i},52514:function(e,o,r){var n=r(1413),t=r(67294),c=r(86759),a=r(91146),l=function(e,o){return t.createElement(a.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:o,icon:c.Z}))},i=t.forwardRef(l);o.Z=i},15746:function(e,o,r){var n=r(21584);o.Z=n.Z},71230:function(e,o,r){var n=r(17621);o.Z=n.Z},66309:function(e,o,r){r.d(o,{Z:function(){return H}});var n=r(67294),t=r(93967),c=r.n(t),a=r(98423),l=r(98787),i=r(69760),s=r(96159),d=r(45353),u=r(53124),f=r(11568),g=r(15063),h=r(14747),v=r(83262),p=r(83559);const m=e=>{const{lineWidth:o,fontSizeIcon:r,calc:n}=e,t=e.fontSizeSM;return(0,v.IX)(e,{tagFontSize:t,tagLineHeight:(0,f.bf)(n(e.lineHeightSM).mul(t).equal()),tagIconSize:n(r).sub(n(o).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},b=e=>({defaultBg:new g.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var C=(0,p.I$)("Tag",(e=>(e=>{const{paddingXXS:o,lineWidth:r,tagPaddingHorizontal:n,componentCls:t,calc:c}=e,a=c(n).sub(r).equal(),l=c(o).sub(r).equal();return{[t]:Object.assign(Object.assign({},(0,h.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:a,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,f.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${t}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${t}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${t}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${t}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:a}}),[`${t}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(m(e))),b),Z=function(e,o){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(n=Object.getOwnPropertySymbols(e);t<n.length;t++)o.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]])}return r};const y=n.forwardRef(((e,o)=>{const{prefixCls:r,style:t,className:a,checked:l,onChange:i,onClick:s}=e,d=Z(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:f,tag:g}=n.useContext(u.E_),h=f("tag",r),[v,p,m]=C(h),b=c()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:l},null==g?void 0:g.className,a,p,m);return v(n.createElement("span",Object.assign({},d,{ref:o,style:Object.assign(Object.assign({},t),null==g?void 0:g.style),className:b,onClick:e=>{null==i||i(!l),null==s||s(e)}})))}));var k=y,$=r(98719);var w=(0,p.bk)(["Tag","preset"],(e=>(e=>(0,$.Z)(e,((o,r)=>{let{textColor:n,lightBorderColor:t,lightColor:c,darkColor:a}=r;return{[`${e.componentCls}${e.componentCls}-${o}`]:{color:n,background:c,borderColor:t,"&-inverse":{color:e.colorTextLightSolid,background:a,borderColor:a},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(m(e))),b);const z=(e,o,r)=>{const n="string"!=typeof(t=r)?t:t.charAt(0).toUpperCase()+t.slice(1);var t;return{[`${e.componentCls}${e.componentCls}-${o}`]:{color:e[`color${r}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var x=(0,p.bk)(["Tag","status"],(e=>{const o=m(e);return[z(o,"success","Success"),z(o,"processing","Info"),z(o,"error","Error"),z(o,"warning","Warning")]}),b),S=function(e,o){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(n=Object.getOwnPropertySymbols(e);t<n.length;t++)o.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]])}return r};const O=n.forwardRef(((e,o)=>{const{prefixCls:r,className:t,rootClassName:f,style:g,children:h,icon:v,color:p,onClose:m,bordered:b=!0,visible:Z}=e,y=S(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:k,direction:$,tag:z}=n.useContext(u.E_),[O,E]=n.useState(!0),H=(0,a.Z)(y,["closeIcon","closable"]);n.useEffect((()=>{void 0!==Z&&E(Z)}),[Z]);const B=(0,l.o2)(p),j=(0,l.yT)(p),P=B||j,M=Object.assign(Object.assign({backgroundColor:p&&!P?p:void 0},null==z?void 0:z.style),g),I=k("tag",r),[N,R,T]=C(I),V=c()(I,null==z?void 0:z.className,{[`${I}-${p}`]:P,[`${I}-has-color`]:p&&!P,[`${I}-hidden`]:!O,[`${I}-rtl`]:"rtl"===$,[`${I}-borderless`]:!b},t,f,R,T),L=e=>{e.stopPropagation(),null==m||m(e),e.defaultPrevented||E(!1)},[,_]=(0,i.Z)((0,i.w)(e),(0,i.w)(z),{closable:!1,closeIconRender:e=>{const o=n.createElement("span",{className:`${I}-close-icon`,onClick:L},e);return(0,s.wm)(e,o,(e=>({onClick:o=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,o),L(o)},className:c()(null==e?void 0:e.className,`${I}-close-icon`)})))}}),F="function"==typeof y.onClick||h&&"a"===h.type,W=v||null,q=W?n.createElement(n.Fragment,null,W,h&&n.createElement("span",null,h)):h,X=n.createElement("span",Object.assign({},H,{ref:o,className:V,style:M}),q,_,B&&n.createElement(w,{key:"preset",prefixCls:I}),j&&n.createElement(x,{key:"status",prefixCls:I}));return N(F?n.createElement(d.Z,{component:"Tag"},X):X)})),E=O;E.CheckableTag=k;var H=E},1208:function(e,o,r){var n=r(87462),t=r(67294),c=r(5717),a=r(93771),l=function(e,o){return t.createElement(a.Z,(0,n.Z)({},e,{ref:o,icon:c.Z}))},i=t.forwardRef(l);o.Z=i}}]);