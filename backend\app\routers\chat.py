from fastapi import APIRouter, HTTPException, Depends, Body
from fastapi.responses import StreamingResponse
from ..models.chat import ChatResponse
from ..models.llm import LLMModel
from typing import List, Dict, Any
from ..utils.auth import verify_token
from ..db.mongodb import db  # 确保导入数据库连接
from urllib.parse import urljoin
import asyncio
from ..models.system_app_setting import SystemAppSettingModel
from ..utils.llmClient import stream_model_api, app_model_api_test
from ..models.message import Message
from datetime import datetime
from bson import ObjectId
import traceback
from app.utils.logging_config import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


router = APIRouter(
    prefix="/api",
    tags=["chat"]
)

@router.post("/chat/v1/completions", response_model=Dict[str, Any])
async def chat(conversation: ChatResponse, current_user: dict = Depends(verify_token)):
    try:
        logger.info(f"收到聊天请求: {conversation}")
        logger.info(f"请求路径: /api/chat/v1/completions")
        print(conversation)
        extra = conversation.extra
        m_id = extra['m_id'] if extra and 'm_id' in extra else None
        prompt = extra['prompt'] if extra and 'prompt' in extra else None
        app_info = conversation.app_info
    # model_info: LLMModel = await db["llms"].find_one({"id": m_id}, {"_id": 0})
    # if not model_info:
        # raise HTTPException(status_code=500, detail="Model not found")

        system_app_info = { 
            # "model": model_info["m_name"],
            "extra": extra,
            "id": 10000,
            "name": "模型对话",
            "app_info": app_info,
            "type": "LLM",
            "params": {
                'llm_id': m_id,
                'prompt': prompt
            },
            
        }

        # print(conversation.extra)
        # print(system_app_info)

        if not system_app_info:
            raise HTTPException(status_code=404, detail="System app setting not found")

        # 提取最后一条消息
        last_message = conversation.messages[-1]

        # 创建新的用户消息记录
        new_user_message = Message(
            _id=str(ObjectId()),
            conversation_id=conversation.conversation_id,
            message_id=last_message['id'],
            meta_data= last_message['meta'] if 'meta' in last_message else {},
            extra=extra,
            user_id=conversation.user_id,
            user_name=conversation.user_name,
            role="user",
            content=last_message['content'],
            created_at=datetime.now(),
            app_info=conversation.app_info,
            token_count=0,
            price=0.0
        )

        # 将 MongoEngine 实例转换为字典
        new_user_message_dict = new_user_message.to_mongo().to_dict()
        await db["messages"].insert_one(new_user_message_dict)

        async def stream_response():
            ai_message_content = ""
            async for chunk in stream_model_api(
                chat_id=conversation.conversation_id,
                user_id=conversation.user_id,
                messages=conversation.messages,
                system_app_info=system_app_info
            ):
                ai_message_content += chunk
                yield chunk
                await asyncio.sleep(0)
            # 添加 end 事件类型
            yield "event: end\ndata: Stream has ended\n\n"

        return StreamingResponse(stream_response(), media_type='text/event-stream')
    except Exception as e:
        logger.error(f"处理聊天请求时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"发生错误：{str(e)}")

@router.post("/llms/test", response_model=Dict[str, Any])
async def test_llm(data: Dict[str, Any], current_user: dict = Depends(verify_token)):
    try:
        # 假设 data 包含模型的 ID
        model_id = data.get('id')
        if not model_id:
            raise HTTPException(status_code=400, detail="Model ID is required")
        model_info = await db["llms"].find_one({"id": model_id})
        logger.info(model_info)
        # 在数据库中查找模型
        
        if model_info:
            re = await app_model_api_test(model_id)
            logger.info(re) #  {'error': 'API请求失败: '}

            if re.get('error'):
                test_result = {"status": "failed", "message":re.get('error')}
            else:
                test_result = {"status": "success", "message": "Model test passed"}
        else:
            test_result = {"status": "failed", "message": "Model not found"}

        # 执行测试逻辑
        # 这里可以调用实际的模型测试函数，假设返回一个测试结果
        

        return test_result

    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"发生错误：{str(e)}")


