from langgraph.graph import StateGraph, END
from typing import Dict, List, Any, TypedDict, Optional, AsyncGenerator, Annotated
from langgraph.graph.message import add_messages
import httpx
import asyncio
from app.utils.logging_config import setup_logging, get_logger
import time
import json
import hashlib
import os
from langchain_openai import ChatOpenAI
from app.models.llm import LLMModel
from bson import ObjectId
from fastapi import Request
# from langchain_core.pydantic_v1 import BaseModel, Field
from pydantic import BaseModel, Field
from typing import Literal, List as PydanticList

setup_logging()
logger = get_logger(__name__)

class AgentState(TypedDict):
    content_input: str
    rules: Optional[List[Dict[str, Any]]] # 数据查询结果
    prompt: Optional[Dict[str, str]]
    llm_result: Optional[Dict[str, Any]]
    final_result: Optional[Dict[str, Any]]

class Issue(BaseModel):
    ruleId: str = Field(description="相关规则ID")
    description: str = Field(description="问题描述")
    chunkId: str = Field(description="问题出现的chunkId，全局问题标记为'all'")
    location: str = Field(description="问题在文档中的位置描述")
    severity: Literal["高", "中", "低"] = Field(description="问题严重程度")
    suggestion: str = Field(description="修改建议")

class AssessmentResult(BaseModel):
    assessmentResult: Literal["通过", "不通过", "部分通过"] = Field(description="评估结果")
    analysis: str = Field(description="详细分析每条规则的合规情况")
    issues: PydanticList[Issue] = Field(description="发现的问题列表")
    overallSuggestion: str = Field(description="总体改进建议")

class IntelligentReviewByRule:
    def __init__(self, config: Dict[str, Any], db, evaluation_type: str, llm: LLMModel, rule_id: str, content: List[str]):
        self.config = config
        self.evaluation_type = evaluation_type
        self.db = db
        self.rule_id = rule_id
        self.content = content
        self.workflow = StateGraph(AgentState)
        self.llm = ChatOpenAI(
                model=llm.get("m_name"),
                temperature=float(llm.get("temperature", 0)),
                openai_api_key=llm.get("api_key"),
                openai_api_base= llm.get("service_url"),
                stream = False
            )

        self._setup_workflow()

    def _setup_workflow(self):
        # 定义工作流节点
        # 第一个节点：获取规则
        # 第二个节点：构建提示词
        # 第三个节点：调用大模型推理
        # 第四个节点：解析结果并返回
        self.workflow.add_node("get_rules", self.get_rules)
        self.workflow.add_node("build_prompt", self.build_prompt_node)
        self.workflow.add_node("llm_inference", self.llm_inference_node)
        self.workflow.add_node("extract_result", self.extract_result_node)
        
        # 配置节点之间的连接
        self.workflow.add_edge("get_rules", "build_prompt")
        self.workflow.add_edge("build_prompt", "llm_inference")
        self.workflow.add_edge("llm_inference", "extract_result")
        self.workflow.add_edge("extract_result", END)
        
        # 设置起始节点
        self.workflow.set_entry_point("get_rules")

    async def run(self, content_input: str = None) -> Dict[str, Any]:
        """执行整个工作流程"""
        try:
            # 准备初始状态
            initial_state = AgentState(
                content_input=content_input or "\n".join([f"chunkId: {i+1}\ncontent: {content}" for i, content in enumerate(self.content)]),
                rules=None,
                prompt=None,
                llm_result=None,
                final_result=None
            )
            
            # 执行工作流

            app = self.workflow.compile()
            result = await app.ainvoke(initial_state)
            return {
                "status": "success",
                "message": "规则评估完成",
                "data": result.get("final_result", {})
            }
        except Exception as e:
            logger.error(f"规则评估执行失败: {str(e)}")
            return {
                "status": "error",
                "message": f"规则评估执行失败: {str(e)}",
                "data": {}
            }

    async def get_rules(self, state: AgentState) -> AgentState:
        """获取评估规则"""
        try:
            logger.info(f'获取规则ID: {self.rule_id}')
            
            # 从数据库查询特定规则
            rule = await self.db['consumer_protection_rules'].find_one({"_id": ObjectId(self.rule_id)})
            
            if not rule:
                logger.warning(f"未找到规则ID: {self.rule_id}")
                # 如果找不到特定规则，尝试获取评估类型的所有规则
                evaluations = await self.db['consumer_protection_rules'].find(
                    {"ruleType": self.evaluation_type}
                ).to_list(length=100)
            else:
                # 将规则放入列表中
                evaluations = [rule]
            
            # 处理MongoDB对象ID
            for rule in evaluations:
                if "_id" in rule:
                    rule["id"] = str(rule["_id"])
            
            state["rules"] = evaluations
            logger.info(f'获取到 {len(evaluations)} 条规则')
            logger.info(f'evaluations: {evaluations}')
            return state
        except Exception as e:
            logger.error(f"获取规则失败: {str(e)}")
            state["rules"] = []
            return state
        
    async def build_prompt_node(self, state: AgentState) -> AgentState:
        """构建提示词节点"""
        try:
            logger.info('构建提示词')
            
            # 获取内容和规则
            content = state.get("content_input", "")
            rules = state.get("rules", [])
            
            if not rules:
                logger.warning("没有可用的规则，将使用通用评估提示词")
                rules_text = "未找到特定规则，请执行通用评估"
            else:
                # 格式化规则列表为文本
                rules_text = "\n".join([
                    f"规则 {i+1}:\n"
                    f"- 规则ID: {rule.get('ruleId', '未知')}\n"
                    f"- 规则名称: {rule.get('ruleName', '未知')}\n"
                    # f"- 规则类型: {rule.get('ruleType', '未知')}\n"
                    f"- 描述: {', '.join(rule.get('description', ['未知'])) if isinstance(rule.get('description'), list) else rule.get('description', '未知')}"
                    for i, rule in enumerate(rules)
                ])
            
            # 构建最终提示词
            system_prompt = f"""
你是一位专业的合规审核专家，你需要根据给定的规则评估文档内容是否合规。

## 评估规则
{rules_text}

## 评估任务
1. 仔细阅读文档内容，注意每个内容块都有对应的chunkId
2. 评估内容是否符合上述每条规则的要求
3. 对于每条规则，给出简短分析和评估结果
4. 提供简洁的改进建议（不超过20字）
5. 明确指出问题出现在哪个chunkId中（如果是全局问题，则标记为"all"）
6. 描述语言使用中文，保持简洁，每项不要超过30字

## 输出格式
请严格按照以下JSON格式输出，确保JSON格式有效：
{{
    "assessmentResult": "通过|不通过|部分通过",
    "analysis": "简要分析合规情况",
    "issues": [
        {{
            "ruleId": "相关规则ID",
            "description": "简短问题描述",
            "chunkId": "问题chunkId",
            "location": "简短位置描述",
            "severity": "高|中|低",
            "suggestion": "简短修改建议"
        }}
    ],
    "overallSuggestion": "总体建议"
}}

请保持输出简洁，避免使用特殊字符和换行符。
            """
            
            user_prompt = f"""
请评估以下文档内容：

---
{content}
---
            """
            
            state["prompt"] = {
                "system_prompt": system_prompt,
                "user_prompt": user_prompt
            }
            
            return state
        except Exception as e:
            logger.error(f"构建提示词失败: {str(e)}")
            state["prompt"] = {
                "system_prompt": "评估文档合规性",
                "user_prompt": state.get("content_input", "")
            }
            return state

    async def llm_inference_node(self, state: AgentState) -> AgentState:
        """调用大模型进行推理"""
        try:
            logger.info('调用大模型进行推理')
            
            prompt = state.get("prompt", {})
            system_prompt = prompt.get("system_prompt", "")
            user_prompt = prompt.get("user_prompt", "")
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # 使用普通LLM调用
            response = await self.llm.ainvoke(messages)
            
            # 手动解析模型输出
            text_response = response.content
            logger.info(f'Raw response: {text_response}')
            
            # 尝试提取JSON部分
            result = self._extract_and_parse_json(text_response)
            
            state["llm_result"] = result
            return state
        except Exception as e:
            logger.error(f"大模型推理失败: {str(e)}")
            state["llm_result"] = {
                "assessmentResult": "未知",
                "analysis": f"推理失败: {str(e)}",
                "issues": [],
                "overallSuggestion": "请重试或联系管理员"
            }
            return state

    def _extract_and_parse_json(self, text):
        """尝试从文本中提取和解析JSON"""
        try:
            # 尝试找到JSON部分
            start_idx = text.find('{')
            end_idx = text.rfind('}') + 1
            
            if start_idx >= 0 and end_idx > start_idx:
                json_str = text[start_idx:end_idx]
                result = json.loads(json_str)
                
                # 确保所有必要字段存在
                default_result = {
                    "assessmentResult": "未知",
                    "analysis": "",
                    "issues": [],
                    "overallSuggestion": ""
                }
                
                # 合并解析结果和默认值
                return {**default_result, **result}
        except:
            pass
        
        # 如果解析失败，返回默认结构
        return {
            "assessmentResult": "未知",
            "analysis": f"无法解析模型输出: {text[:200]}...",
            "issues": [],
            "overallSuggestion": "请重试评估"
        }

    async def extract_result_node(self, state: AgentState) -> AgentState:
        """解析模型输出并格式化结果"""
        try:
            logger.info('解析模型输出')
            
            result_data = state.get("llm_result", {})
            
            # 添加元数据
            result_data["evaluationTime"] = time.strftime("%Y-%m-%d %H:%M:%S")
            
            # 添加规则信息
            rules = state.get("rules", [])
            if rules:
                rule = rules[0]  # 使用第一条规则
                result_data["ruleInfo"] = {
                    "id": rule.get("id", ""),
                    "ruleId": rule.get("ruleId", ""),
                    "ruleName": rule.get("ruleName", ""),
                    "ruleType": rule.get("ruleType", "")
                }
            
            # 添加受影响的内容块信息
            affected_chunks = []
            if result_data.get("issues"):
                # 用于跟踪哪些chunkId已经处理过
                processed_chunks = {}
                
                for issue in result_data["issues"]:
                    # 获取问题所属的chunkId，默认为"all"
                    chunk_id = issue.get("chunkId", "all")
                    
                    # 如果这个chunkId之前没处理过，创建新项
                    if chunk_id not in processed_chunks:
                        processed_chunks[chunk_id] = {
                            "chunkId": chunk_id,
                            "issues": []
                        }
                    
                    # 添加问题到对应的chunkId
                    processed_chunks[chunk_id]["issues"].append({
                        "ruleId": issue.get("ruleId", ""),
                        "description": issue.get("description", ""),
                        "severity": issue.get("severity", "低"),
                        "suggestion": issue.get("suggestion", "")
                    })
                
                # 将处理好的数据添加到affected_chunks
                affected_chunks = list(processed_chunks.values())
            
            # 构建最终结果
            final_result = {
                "result": result_data.get("assessmentResult", "未知"),
                "status": "completed",
                "affectedChunks": affected_chunks,
                "analysis": result_data.get("analysis", ""),
                "overallSuggestion": result_data.get("overallSuggestion", ""),
                "evaluationData": result_data
            }
            logger.info(f'final_result: {final_result}')
            
            state["final_result"] = final_result
            logger.info('结果解析完成')
            return state
        except Exception as e:
            logger.error(f"解析结果失败: {str(e)}")
            state["final_result"] = {
                "result": "处理失败",
                "status": "error",
                "message": str(e)
            }
            return state
    