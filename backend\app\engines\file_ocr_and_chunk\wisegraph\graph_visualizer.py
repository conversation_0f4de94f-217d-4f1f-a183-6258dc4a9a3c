"""
知识图谱可视化数据转换器
支持转换为ECharts等前端图表格式
"""

from typing import Dict, List, Any, Optional
import hashlib
from loguru import logger


class GraphVisualizer:
    """知识图谱可视化数据转换器"""
    
    def __init__(self):
        # 实体类型颜色映射
        self.entity_type_colors = {
            "person": "#1f77b4",      # 蓝色 - 人员
            "organization": "#ff7f0e", # 橙色 - 组织
            "location": "#2ca02c",     # 绿色 - 地点
            "concept": "#d62728",      # 红色 - 概念
            "event": "#9467bd",        # 紫色 - 事件
            "object": "#8c564b",       # 棕色 - 物体
            "time": "#e377c2",         # 粉色 - 时间
            "unknown": "#7f7f7f"       # 灰色 - 未知
        }
        
        # 关系类型颜色映射
        self.relation_type_colors = {
            "管理": "#ff6b6b",
            "监管": "#4ecdc4", 
            "负责": "#45b7d1",
            "属于": "#96ceb4",
            "包含": "#ffeaa7",
            "相关": "#dda0dd",
            "RELATED": "#999999"
        }
    
    def to_echarts_format(self, nodes: List[Dict[str, Any]], relationships: List[Dict[str, Any]], 
                         knowledge_base_id: str = None) -> Dict[str, Any]:
        """
        转换为ECharts图表格式
        
        Args:
            nodes: 节点列表
            relationships: 关系列表
            knowledge_base_id: 知识库ID
            
        Returns:
            ECharts格式的图表数据
        """
        try:
            # 转换节点
            echarts_nodes = []
            categories = self._get_categories(nodes)
            category_map = {cat["name"]: i for i, cat in enumerate(categories)}
            
            for node in nodes:
                entity_type = node.get("entity_type")
                # 处理 None 值
                if entity_type is None:
                    entity_type = "unknown"

                echarts_node = {
                    "id": node["entity_id"],
                    "name": node["entity_name"],
                    "category": category_map.get(entity_type, 0),
                    "symbolSize": self._calculate_node_size(node),
                    "itemStyle": {
                        "color": self._get_color_by_type(entity_type)
                    },
                    "label": {
                        "show": True,
                        "fontSize": 12
                    },
                    "tooltip": {
                        "formatter": self._format_node_tooltip(node)
                    },
                    # 原始数据
                    "data": {
                        "entity_type": entity_type,
                        "description": node.get("description", ""),
                        "chunk_ids": node.get("chunk_ids", []),
                        "knowledge_base_id": node.get("knowledge_base_id", knowledge_base_id)
                    }
                }
                echarts_nodes.append(echarts_node)
            
            # 转换关系
            echarts_links = []
            for rel in relationships:
                relation_type = rel.get("relation_type", "RELATED")
                echarts_link = {
                    "source": rel["source"],
                    "target": rel["target"],
                    "name": relation_type,
                    "lineStyle": {
                        "color": self._get_relation_color(relation_type),
                        "width": 2,
                        "opacity": 0.8
                    },
                    "label": {
                        "show": True,
                        "formatter": relation_type,
                        "fontSize": 10
                    },
                    "tooltip": {
                        "formatter": self._format_relation_tooltip(rel)
                    },
                    # 原始数据
                    "data": {
                        "relation_type": relation_type,
                        "description": rel.get("description", ""),
                        "chunk_ids": rel.get("chunk_ids", []),
                        "knowledge_base_id": rel.get("knowledge_base_id", knowledge_base_id)
                    }
                }
                echarts_links.append(echarts_link)
            
            return {
                "nodes": echarts_nodes,
                "links": echarts_links,
                "categories": categories,
                "layout": "force",
                "force": {
                    "repulsion": 1000,
                    "edgeLength": 150,
                    "gravity": 0.1
                },
                "stats": {
                    "total_nodes": len(echarts_nodes),
                    "total_links": len(echarts_links),
                    "knowledge_base_id": knowledge_base_id
                }
            }
            
        except Exception as e:
            logger.error(f"转换ECharts格式失败: {e}")
            return {
                "nodes": [],
                "links": [],
                "categories": [],
                "stats": {"total_nodes": 0, "total_links": 0}
            }
    
    def to_raw_format(self, nodes: List[Dict[str, Any]], relationships: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        转换为原始格式（不做特殊处理）
        
        Args:
            nodes: 节点列表
            relationships: 关系列表
            
        Returns:
            原始格式数据
        """
        return {
            "nodes": nodes,
            "relationships": relationships,
            "stats": {
                "total_nodes": len(nodes),
                "total_relationships": len(relationships)
            }
        }
    
    def _get_categories(self, nodes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """获取节点分类"""
        entity_types = set()
        for node in nodes:
            entity_type = node.get("entity_type")
            # 处理 None 值
            if entity_type is None:
                entity_type = "unknown"
            entity_types.add(entity_type)

        categories = []
        # 安全排序，过滤掉 None 值
        sorted_types = sorted([t for t in entity_types if t is not None])
        for entity_type in sorted_types:
            categories.append({
                "name": entity_type,
                "itemStyle": {
                    "color": self._get_color_by_type(entity_type)
                }
            })

        return categories
    
    def _calculate_node_size(self, node: Dict[str, Any]) -> int:
        """计算节点大小"""
        # 基础大小
        base_size = 30
        
        # 根据chunk_ids数量调整大小
        chunk_count = len(node.get("chunk_ids", []))
        size_bonus = min(chunk_count * 5, 30)  # 最多增加30
        
        # 根据实体名称长度调整
        name_length = len(node.get("entity_name", ""))
        if name_length > 6:
            size_bonus += 10
        
        return base_size + size_bonus
    
    def _get_color_by_type(self, entity_type: str) -> str:
        """根据实体类型获取颜色"""
        return self.entity_type_colors.get(entity_type, self.entity_type_colors["unknown"])
    
    def _get_relation_color(self, relation_type: str) -> str:
        """根据关系类型获取颜色"""
        return self.relation_type_colors.get(relation_type, self.relation_type_colors["RELATED"])
    
    def _format_node_tooltip(self, node: Dict[str, Any]) -> str:
        """格式化节点提示信息"""
        tooltip_parts = [
            f"<b>{node.get('entity_name', 'Unknown')}</b>",
            f"类型: {node.get('entity_type', 'unknown')}",
        ]
        
        if node.get("description"):
            tooltip_parts.append(f"描述: {node['description']}")
        
        chunk_count = len(node.get("chunk_ids", []))
        if chunk_count > 0:
            tooltip_parts.append(f"相关文档: {chunk_count}个")
        
        return "<br/>".join(tooltip_parts)
    
    def _format_relation_tooltip(self, relation: Dict[str, Any]) -> str:
        """格式化关系提示信息"""
        tooltip_parts = [
            f"<b>{relation.get('relation_type', 'RELATED')}</b>",
            f"从: {relation.get('source', '')}",
            f"到: {relation.get('target', '')}"
        ]
        
        if relation.get("description"):
            tooltip_parts.append(f"描述: {relation['description']}")
        
        chunk_count = len(relation.get("chunk_ids", []))
        if chunk_count > 0:
            tooltip_parts.append(f"相关文档: {chunk_count}个")
        
        return "<br/>".join(tooltip_parts)
    
    def get_graph_summary(self, nodes: List[Dict[str, Any]], relationships: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取图谱摘要信息"""
        # 统计实体类型
        entity_type_counts = {}
        for node in nodes:
            entity_type = node.get("entity_type")
            # 处理 None 值
            if entity_type is None:
                entity_type = "unknown"
            entity_type_counts[entity_type] = entity_type_counts.get(entity_type, 0) + 1
        
        # 统计关系类型
        relation_type_counts = {}
        for rel in relationships:
            relation_type = rel.get("relation_type")
            # 处理 None 值
            if relation_type is None:
                relation_type = "RELATED"
            relation_type_counts[relation_type] = relation_type_counts.get(relation_type, 0) + 1
        
        # 计算连接度
        node_connections = {}
        for rel in relationships:
            source = rel.get("source")
            target = rel.get("target")
            if source:
                node_connections[source] = node_connections.get(source, 0) + 1
            if target:
                node_connections[target] = node_connections.get(target, 0) + 1
        
        # 找出连接度最高的节点
        top_connected_nodes = sorted(node_connections.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            "total_nodes": len(nodes),
            "total_relationships": len(relationships),
            "entity_types": [
                {"type": k, "count": v} for k, v in 
                sorted(entity_type_counts.items(), key=lambda x: x[1], reverse=True)
            ],
            "relation_types": [
                {"type": k, "count": v} for k, v in 
                sorted(relation_type_counts.items(), key=lambda x: x[1], reverse=True)
            ],
            "top_connected_nodes": [
                {"entity_id": node_id, "connections": count} 
                for node_id, count in top_connected_nodes
            ]
        }
