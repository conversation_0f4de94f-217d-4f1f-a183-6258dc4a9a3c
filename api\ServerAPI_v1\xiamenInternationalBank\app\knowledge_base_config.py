from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List
from .utils import generate_random_id

# Pydantic 模型
class KnowledgeBaseBase(BaseModel):
    # id: str = Field(default_factory=generate_random_id, description="知识库 ID，必填，主键")
    name: str = Field(..., description="知识库名称，必填")
    description: Optional[str] = Field(None, description="知识库描述，选填")
    file_count: int = Field(0, description="文件数量，默认为0")
    chunk_count: int = Field(0, description="文本块数量，默认为0")
    is_active: bool = Field(True, description="是否激活，默认为True")

class KnowledgeBaseCreate(BaseModel):
    name: str = Field(..., description="知识库名称，必填")
    description: Optional[str] = Field(None, description="知识库描述，选填")
    user_id: Optional[int] = Field(None, description="用户 ID，选填")
    user_name: Optional[str] = Field(None, description="用户名称，选填")

class KnowledgeBaseUpdate(BaseModel):
    name: Optional[str] = Field(None, description="知识库名称，选填")
    description: Optional[str] = Field(None, description="知识库描述，选填")
    is_active: Optional[bool] = Field(None, description="是否激活，选填")

class KnowledgeBaseResponse(KnowledgeBaseBase):
    id: str = Field(..., description="知识库 ID，必填")
    created_at: datetime = Field(..., description="创建时间，必填")
    last_updated: datetime = Field(..., description="最后更新时间，必填")

    class Config:
        orm_mode = True  # 允许从ORM对象创建模型

class KnowledgeBaseCollectionCreate(BaseModel):
    klbId: str = Field(..., description="知识库 ID，必填")

class KnowledgeBaseCollectionResponse(BaseModel):
    collectionId: str = Field(..., description="集合 ID，必填")

class KnowledgeDataAdd(BaseModel):
    collectionId: str = Field(..., description="集合 ID，必填")
    mode: str = Field(..., description="模式，必填，值为 'chunk' 或 'qa'")
    prompt: Optional[str] = Field(None, description="QA 拆分提示词，选填")
    data: List[dict] = Field(..., description="数据列表，必填")

class KnowledgeDataAddResponse(BaseModel):
    insertLen: int = Field(..., description="最终插入成功的数量，必填")
    overToken: List[str] = Field(..., description="超出 token 的，必填")
    repeat: List[str] = Field(..., description="重复的数量，必填")
    error: List[str] = Field(..., description="其他错误，必填")

class KnowledgeDataDeleteRequest(BaseModel):
    dbIds: List[str] = Field(..., description="文档切块后，每一个数据块 ID，必填")

class KnowledgeDataUpdateRequest(BaseModel):
    dbId: str = Field(..., description="数据块 ID，必填")
    data: List[dict] = Field(..., description="更新数据，必填")

class KnowledgeDataUpdateResponse(BaseModel):
    insertLen: int = Field(..., description="最终插入成功数量，必填")
    overToken: List[str] = Field(..., description="超出 token 的，必填")
    repeat: List[str] = Field(..., description="重复的数量，必填")
    error: List[str] = Field(..., description="其他错误，必填")

class KnowledgeFileResponse(BaseModel):
    id: str = Field(..., description="文件 ID，必填")
    name: str = Field(..., description="文件名称，必填")
    url: str = Field(..., description="文件 URL，必填")

