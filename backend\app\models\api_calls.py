from mongoengine import Document, StringField, DateTimeField, DictField, IntField, ObjectIdField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from bson import ObjectId

# MongoEngine 模型
class ApiCall(Document):
    meta = {
        'collection': 'api_calls',
        'indexes': [
            'app_id',
            'api',
            ('app_id', 'api'),
            'timestamp',
            'status'
        ]
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId)
    app_id = StringField(required=True)              # 调用方应用ID
    api = StringField(required=True)                 # API接口名称
    method = StringField(required=True)              # 请求方法 GET/POST/PUT/DELETE
    path = StringField(required=True)                # 请求路径
    timestamp = DateTimeField(default=datetime.now)  # 调用时间
    request_data = DictField()                       # 请求数据
    response_data = DictField()                      # 响应数据
    status = StringField(default='processing')       # 调用状态：processing/success/failed
    status_code = IntField()                         # HTTP状态码
    error = StringField()                           # 错误信息
    execution_time = IntField()                     # 执行时间(毫秒)
    ip_address = StringField()                      # 调用方IP地址
    user_agent = StringField()                      # 用户代理

# Pydantic 模型
class ApiCallBase(BaseModel):
    app_id: str
    api: str
    method: str
    path: str
    request_data: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

class ApiCallCreate(ApiCallBase):
    pass

class ApiCallUpdate(BaseModel):
    response_data: Optional[Dict[str, Any]] = None
    status: Optional[str] = None
    status_code: Optional[int] = None
    error: Optional[str] = None
    execution_time: Optional[int] = None

class ApiCallResponse(ApiCallBase):
    id: str
    timestamp: datetime
    response_data: Optional[Dict[str, Any]] = None
    status: str
    status_code: Optional[int] = None
    error: Optional[str] = None
    execution_time: Optional[int] = None

    class Config:
        from_attributes = True

class ApiCallStats(BaseModel):
    total_calls: int
    success_count: int
    failed_count: int
    avg_execution_time: float
    error_rate: float 