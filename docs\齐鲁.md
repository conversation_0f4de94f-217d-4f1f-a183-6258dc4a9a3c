
```mermaid
graph LR
    subgraph "用户层 (User Layer)"
        U1["业务人员 (Business Users)"]
        U2["技术人员 (Technical Staff)"]
    end
    subgraph "应用与服务层 (Application & Service Layer)"
        AIP["AI 能力平台 (AI Capability Platform)
        面向业务人员
        - 智能问答 (优化后)
        - 智能体超市
        - 提示词超市
        - 平台管理 (业务侧)"]
        ADP["大模型应用开发平台 (Large Model App Dev Platform)
        面向技术人员
        - Agent 开发 & 封装
        - Agent 组件开发
        - 知识库构建与管理
        - OCR/语音模型接入与封装"]
        MMP["大模型管理平台 (Large Model Mgmt Platform)
        基于 Xinference, 面向技术人员
        - 大模型统一管理
        - 嵌入模型管理
        - 模型接口服务 (API Gateway)
        - 对接其他服务等"]
    end
    subgraph "基础模型层 (Foundation Model Layer)"
        LLB["大模型底座 (Large Model Base)
        开源大模型为主
        - DeepSeek-R1-32B (主力)
        - DeepSeek-R1-14B
        - Qwen2.5-32B
        - 嵌入模型 (Embedding Models)
        - (可能) OCR/语音基础模型"]
    end
    subgraph "支撑与集成 (Support & Integration)"
        AUTH["行内统一认证
        (Internal Auth)"]
        DATA["行内数据源
        (Internal Data Sources)
        业务系统、文档库等"]
    end
    U1 -- 使用/交互 --> AIP
    U2 -- 开发/管理 --> ADP
    U2 -- 管理/监控 --> MMP
    U2 -- (底层)管理/部署 --> LLB
    AIP -- 1.调用 Agent 能力 (API) --> ADP
    AIP -- 2.调用通用问答模型 (API) --> MMP
    AIP -- 3.获取可用智能体/模板列表 --> ADP/AIP自身管理模块
    AIP -- 认证 --> AUTH
    ADP -- 4.调用大模型/嵌入模型 (API) --> MMP
    ADP -- 5.构建知识库来源 --> DATA
    ADP -- 6.(可能)调用底层OCR/语音 --> LLB/MMP封装接口
    ADP -- 7.Agent 接口发布至 --> AIP
    ADP -- 认证 --> AUTH
    MMP -- 8.管理/调度模型 --> LLB
    MMP -- 9.对接应用开发平台 --> ADP
    MMP -- 认证 --> AUTH
    style AIP fill:#D6EAF8,stroke:#3498DB,stroke-width:2px
    style ADP fill:#D5F5E3,stroke:#2ECC71,stroke-width:2px
    style MMP fill:#FCF3CF,stroke:#F1C40F,stroke-width:2px
    style LLB fill:#FADBD8,stroke:#E74C3C,stroke-width:2px
    style U1 fill:#E8DAEF,stroke:#8E44AD
    style U2 fill:#E8DAEF,stroke:#8E44AD
    style AUTH fill:#AEB6BF,stroke:#5D6D7E
    style DATA fill:#A9DFBF,stroke:#1E8449
    linkStyle default stroke:#5D6D7E,stroke-width:1px;
    linkStyle 0 stroke-width:1.5px,stroke:blue;
    linkStyle 1 stroke-width:1.5px,stroke:green;
    linkStyle 2 stroke-width:1.5px,stroke:orange;
    linkStyle 3 stroke-width:1.5px,stroke:purple;
    linkStyle 4 stroke-width:1.5px,stroke:red;
    linkStyle 5 stroke-width:1.5px,stroke:brown;
    linkStyle 6 stroke-width:1.5px,stroke:pink;
    linkStyle 7 stroke-width:1.5px,stroke:teal;
    linkStyle 8 stroke-width:1.5px,stroke:gray;
    linkStyle 9 stroke-width:1.5px,stroke:black;
```



```mermaid
graph TD
    subgraph OverallSystem [银行大模型应用开发及AI能力平台 Overall System]
        direction LR

        subgraph AIP [AI 能力平台]
            direction TB
            AIP_Interaction[交互模块]
            AIP_CoreAI[核心 AI 能力]
            AIP_Mgmt[平台管理]

            AIP_Interaction --> AIP_F1[用户界面Vue 3.x ]
            AIP_Interaction --> AIP_F2[用户认证行内认证]

            AIP_CoreAI --> AIP_F3[智能问答：文件上传/历史对话/模型选择]
            AIP_CoreAI --> AIP_F4[智能体超市：浏览/交互/结果展示/对话保持]
            AIP_CoreAI --> AIP_F5[提示词超市：浏览/搜索/应用模板]

            AIP_Mgmt --> AIP_F6[智能体管理:增删查启禁]
            AIP_Mgmt --> AIP_F7[提示词模板管理:增删查启禁]
            AIP_Mgmt --> AIP_F8[模型可用性管理]
        end

        subgraph ADP [大模型应用开发平台]
            direction TB
            ADP_AgentDev[Agent 开发与管理]
            ADP_KB[知识库管理]
            ADP_ModelInt[专用模型集成]
            ADP_PlatformInt[平台集成与接口]
            ADP_Admin[平台配置与管理]

            ADP_AgentDev --> ADP_F1[Agent: 构建]
            ADP_AgentDev --> ADP_F2[Agent: 调试与测试]
            ADP_AgentDev --> ADP_F3[Agent: 组件封装]
            ADP_AgentDev --> ADP_F4[Agent:接口发布]

            ADP_KB --> ADP_F5[知识库创建/配置]
            ADP_KB --> ADP_F6[多源数据接入与处理]
            ADP_KB --> ADP_F7[向量化与索引管理]

            ADP_ModelInt --> ADP_F8[OCR 模型接入管理]
            ADP_ModelInt --> ADP_F9[语音模型接入管理]
            ADP_ModelInt --> ADP_F10[模型能力封装]

            ADP_PlatformInt --> ADP_F11[对接 Xinference]
            ADP_PlatformInt --> ADP_F12[对接 AI 能力平台]

            ADP_Admin --> ADP_F13[配置管理]
            ADP_Admin --> ADP_F14[技术栈适配:Python, DB]
        end

        subgraph MMP [大模型管理平台]
            direction TB
            MMP_ModelMgmt[模型资源管理]
            MMP_Serving[模型服务接口]
            MMP_Orchestration[部署与调度]

            MMP_ModelMgmt --> MMP_F1[大模型注册管理]
            MMP_ModelMgmt --> MMP_F2[嵌入模型注册管理]
            MMP_ModelMgmt --> MMP_F3[模型版本控制]

            MMP_Serving --> MMP_F4[统一模型 API 网关]
            MMP_Serving --> MMP_F5[模型访问控制]

            MMP_Orchestration --> MMP_F6[模型实例部署]
            MMP_Orchestration --> MMP_F7[资源监控]
            MMP_Orchestration --> MMP_F8[对接 RAGFlow/ADP]
        end

        subgraph LLB [大模型底座]
            direction TB
            LLB_LLM[LLM 资源]
            LLB_Emb[Embedding资源]
            LLB_Spec[专用模型资源:OCR/语音]

            LLB_LLM --> LLB_F1[DeepSeek-R1-32B]
            LLB_LLM --> LLB_F2[DeepSeek-R1-14B]
            LLB_LLM --> LLB_F3[Qwen2.5-32B]
            LLB_Emb --> LLB_F4[...]
            LLB_Spec --> LLB_F5[...]
        end
    end

    %% Relationships
    AIP_CoreAI -- Uses Agent --> ADP_PlatformInt
    AIP_CoreAI -- Uses Models --> MMP_Serving
    ADP_AgentDev -- Deploys Agent Interface To --> AIP_Mgmt
    ADP_PlatformInt -- Calls Models Via --> MMP_Serving
    MMP_Orchestration -- Manages/Deploys --> LLB

```



```mermaid
sequenceDiagram
    participant UserBrowser as 用户浏览器
    participant Nginx
    participant AIP_Backend as AIP 后端 (Spring Boot)
    participant SecurityFilter as 安全过滤器
    participant Controller
    participant Service
    participant Repository as 数据访问层 (MyBatis)
    participant DM8 as 达梦数据库
    participant RedisCache as Redis 缓存

    UserBrowser->>+Nginx: GET /protected/resource
    Nginx->>+AIP_Backend: GET /protected/resource
    AIP_Backend->>+SecurityFilter: 拦截请求
    Note over SecurityFilter: 检查 Session/Token 是否有效
    alt Session/Token 无效或过期
        SecurityFilter-->>-AIP_Backend: 返回 401 Unauthorized
        AIP_Backend-->>-Nginx: 401 Unauthorized
        Nginx-->>-UserBrowser: 401 (触发登录跳转)
    else Session/Token 有效
        SecurityFilter->>SecurityFilter: 加载用户身份和权限 (可能查 Redis)
        SecurityFilter->>+Controller: 放行请求 (携带认证信息)
        Controller->>+Service: 调用业务方法 service.getResource()
        Service->>Service: 执行权限校验 (基于用户权限)
        alt 权限不足
            Service-->>-Controller: 抛出 AccessDeniedException
            Controller-->>SecurityFilter: 异常处理 (返回 403 Forbidden)
            SecurityFilter-->>-AIP_Backend: 403 Forbidden
            AIP_Backend-->>-Nginx: 403 Forbidden
            Nginx-->>-UserBrowser: 403 Forbidden
        else 权限足够
            Note over Service: 尝试从 Redis 缓存获取资源
            Service->>+RedisCache: GET resource:id
            alt 缓存命中
                RedisCache-->>-Service: 返回缓存数据
            else 缓存未命中
                RedisCache-->>-Service: 返回 null
                Service->>+Repository: findResourceById(id)
                Repository->>+DM8: 执行 SQL 查询
                DM8-->>-Repository: 返回数据库记录
                Repository-->>-Service: 返回资源对象
                Note over Service: 将资源存入 Redis 缓存
                Service->>+RedisCache: SET resource:id data (带过期时间)
                RedisCache-->>-Service: OK
            end
            Service-->>-Controller: 返回资源数据
            Controller-->>-SecurityFilter: 封装成功响应 (200 OK + JSON Body)
            SecurityFilter-->>-AIP_Backend: 200 OK
            AIP_Backend-->>-Nginx: 200 OK
            Nginx-->>-UserBrowser: 返回页面/数据
        end
    end
```

```mermaid

sequenceDiagram
    participant UserBrowser as 用户浏览器
    participant AIP_Backend as AIP 后端
    participant AgentService as Agent 服务
    participant AgentRepo as Agent 仓库
    participant ADPClient as ADP 客户端
    participant ADP_Backend as ADP 后端 (Agent 执行环境)

    UserBrowser->>+AIP_Backend: POST /agent/invoke (agentId, userInput)
    AIP_Backend->>+AgentService: invokeAgent(agentId, userInput)
    AgentService->>+AgentRepo: findAgentById(agentId)
    AgentRepo->>AgentRepo: 查询 DM8 数据库
    AgentRepo-->>-AgentService: 返回 Agent 信息 (含 API URL)
    alt Agent 不存在或未启用
        AgentService-->>-AIP_Backend: 返回错误信息
    else Agent 存在且启用
        AgentService->>+ADPClient: callAgentApi(apiUrl, userInput, session?)
        Note over ADPClient: 使用 WebClient/RestTemplate 调用动态 URL
        ADPClient->>+ADP_Backend: POST {apiUrl} (请求体含 userInput)
        ADP_Backend->>ADP_Backend: 执行 Agent 逻辑 (可能调用 LLM, 工具, 知识库)
        ADP_Backend-->>-ADPClient: 返回 Agent 执行结果 JSON
        ADPClient-->>-AgentService: 返回处理结果
        AgentService-->>-AIP_Backend: 返回 Agent 的响应
        AIP_Backend-->>-UserBrowser: 返回 Agent 响应 JSON
    end
```






```mermaid
sequenceDiagram
    participant U1 as 业务人员
    participant AIP as AI 能力平台
    participant AUTH as 统一认证
    participant ADP as 大模型应用开发平台
    participant MMP as 大模型管理平台 (Xinference)
    participant LLB as 大模型底座 (Models)
    participant KnowledgeBase as 知识库 (VectorDB/ES @ADP)

    U1->>+AIP: 访问平台 / 请求使用 Agent
    AIP->>+AUTH: 请求用户认证
    AUTH-->>-AIP: 返回认证成功/用户信息
    AIP->>AIP: 验证用户权限

    Note over AIP, ADP: AIP 可能先查询 ADP 获取可用 Agent 列表 (交互 3)

    AIP->>+ADP: 1. 调用 Agent 执行 API (携带用户输入)
    activate ADP
    Note over ADP: 开始执行 Agent 逻辑 (e.g., LangGraph/Langchain)
    ADP->>+MMP: 4. 请求 LLM/Embedding 模型 (e.g., 分析输入 或 RAG 检索)
    activate MMP
    MMP->>+LLB: 8. 加载/调用模型
    activate LLB
    LLB-->>-MMP: 返回模型结果/向量
    deactivate LLB
    MMP-->>-ADP: 返回 LLM 响应 / Embedding 结果
    deactivate MMP

    opt Agent 需要知识库 (RAG)
        ADP->>+KnowledgeBase: 查询相关文档块 (基于 Embedding 结果)
        activate KnowledgeBase
        Note over KnowledgeBase: 知识库数据源于 DATA (交互 5)
        KnowledgeBase-->>-ADP: 返回相关上下文
        deactivate KnowledgeBase
        ADP->>+MMP: 4. 再次请求 LLM 模型 (携带问题+上下文生成答案)
        activate MMP
        MMP->>+LLB: 8. 调用模型
        activate LLB
        LLB-->>-MMP: 返回模型结果
        deactivate LLB
        MMP-->>-ADP: 返回最终答案
        deactivate MMP
    end

    ADP-->>-AIP: 返回 Agent 执行结果
    deactivate ADP
    AIP-->>-U1: 显示 Agent 处理结果
    deactivate AIP
```