from typing import List, Dict, Union, Optional
import numpy as np
import asyncio

import logging
import os
import requests

def setup_logging():
    logging.basicConfig(
        level=os.getenv("LOG_LEVEL", logging.INFO),
        format='%(levelname)s - %(asctime)s - %(name)s:%(lineno)d - %(funcName)s() - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

def get_logger(name):
    logger = logging.getLogger(name)
    return logger


setup_logging()
logger = get_logger(__name__)
def get_embedding(text,embedding_config):
    access_token = embedding_config.get("api_key", "")
    service_url = embedding_config.get("service_url", "")
    model = embedding_config.get("embedding_name", "bge-m3")

    headers = {'Content-Type': 'application/json', 'Authorization': f'{access_token}'}
    req = {
        "input": [text],
        "model": model
    }
    embdd_response = requests.post(url=service_url, json=req, headers=headers)
    if embdd_response.status_code == 200:
        # print("embdd_response.json():", len(embdd_response.json()['data'][0]['embedding']))
        return embdd_response.json()['data'][0]['embedding']
    else:
        logger.error(f"获取embedding失败: {embdd_response.status_code}")
        return None
    


    
# async def get_embedding_vector(
#     text: str,
#     config: Dict[str, str],
#     retry_count: int = 3
# ) -> Optional[List[float]]:
#     """获取文本的embedding向量"""
#     headers = {
#         "Content-Type": "application/json",
#         "Authorization": f"Bearer {config['api_key']}"
#     }
    
#     data = {
#         "input": [text],
#         "model": config.get("model", "bge-m3")
#     }
    
#     # 获取基础 URL
#     base_url = config.get("service_url", "https://api.openai.com/v1/embeddings")
#     # 确保 URL 以 /embeddings 结尾
#     if not base_url.endswith('/embeddings'):
#         base_url = base_url.rstrip('/') + '/embeddings'

    
#     for attempt in range(retry_count):
#         try:
#             async with aiohttp.ClientSession() as session:
#                 async with session.post(
#                     base_url,
#                     headers=headers,
#                     json=data
#                 ) as response:
#                     if response.status == 200:
#                         result = await response.json()
#                         logger.debug(f"API Response: {result}")
                        
#                         # 处理不同的响应结构
#                         if "data" in result and isinstance(result["data"], list):
#                             # OpenAI 格式
#                             return result["data"][0]["embedding"]
#                         elif "embedding" in result:
#                             # 自定义格式 1
#                             return result["embedding"]
#                         elif "embeddings" in result:
#                             # 自定义格式 2
#                             return result["embeddings"][0] if result["embeddings"] else None
#                         elif isinstance(result, list):
#                             # 自定义格式 3
#                             return result[0] if result else None
#                         elif "result" in result and result["result"] == "err":
#                             # 处理错误响应
#                             logger.error(f"API 返回错误: {result.get('message', '未知错误')}")
#                             if attempt == retry_count - 1:
#                                 return None
#                             continue
#                         else:
#                             logger.error(f"未知的响应格式: {result}")
#                             return None
#                     else:
#                         error_text = await response.text()
#                         logger.error(f"Embedding API 请求失败: {error_text}")
#                         if attempt == retry_count - 1:
#                             return None
#         except Exception as e:
#             logger.error(f"获取 embedding 向量时发生错误: {str(e)}")
#             if attempt == retry_count - 1:
#                 return None
            
#         if attempt < retry_count - 1:
#             logger.info(f"第 {attempt + 1} 次重试获取 embedding...")
    
#     return None

# async def get_embeddings_batch(
#     texts: List[str],
#     config: Dict[str, str],
#     batch_size: int = 20,
#     retry_count: int = 3
# ) -> List[Optional[List[float]]]:
#     """批量获取文本的embedding向量"""
#     results = []
#     # 分批处理
#     for i in range(0, len(texts), batch_size):
#         batch_texts = texts[i:i + batch_size]
#         headers = {
#             "Content-Type": "application/json",
#             "Authorization": f"Bearer {config['api_key']}"
#         }
        
#         data = {
#             "input": batch_texts,
#             "model": config.get("model", "bge-m3")
#         }

       
        
#         # 获取基础 URL
#         base_url = config.get("service_url", "https://api.openai.com/v1/embeddings")
#         if not base_url.endswith('/embeddings'):
#             base_url = base_url.rstrip('/') + '/embeddings'
       
#         for attempt in range(retry_count):
#             try:
#                 async with aiohttp.ClientSession() as session:
#                     async with session.post(
#                         base_url,
#                         headers=headers,
#                         json=data
#                     ) as response:
#                         if response.status == 200:
#                             result = await response.json()
#                             logger.debug(f"Batch API Response: {result}")
                            
#                             # 处理不同的响应结构
#                             if "data" in result and isinstance(result["data"], list):
#                                 # OpenAI 格式
#                                 batch_embeddings = [item["embedding"] for item in result["data"]]
#                             elif "embeddings" in result:
#                                 # 自定义格式 1
#                                 batch_embeddings = result["embeddings"]
#                             elif isinstance(result, list):
#                                 # 自定义格式 2
#                                 batch_embeddings = result
#                             elif "result" in result and result["result"] == "err":
#                                 # 处理错误响应
#                                 logger.error(f"批量 API 返回错误: {result.get('message', '未知错误')}")
#                                 if attempt == retry_count - 1:
#                                     batch_embeddings = [None] * len(batch_texts)
#                                 continue
#                             else:
#                                 logger.error(f"未知的批量响应格式: {result}")
#                                 batch_embeddings = [None] * len(batch_texts)
                            
#                             results.extend(batch_embeddings)
#                             break
#                         else:
#                             error_text = await response.text()
#                             logger.error(f"批量 Embedding API 请求失败: {error_text}")
#                             if attempt == retry_count - 1:
#                                 results.extend([None] * len(batch_texts))
#             except Exception as e:
#                 logger.error(f"批量获取 embedding 向量时发生错误: {str(e)}")
#                 if attempt == retry_count - 1:
#                     results.extend([None] * len(batch_texts))
                
#             if attempt < retry_count - 1:
#                 logger.info(f"第 {attempt + 1} 次重试批量获取 embedding...")
    
#     return results

# def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
#     """
#     计算两个向量的余弦相似度
    
#     Args:
#         vec1: 第一个向量
#         vec2: 第二个向量
    
#     Returns:
#         float: 余弦相似度值 (-1 到 1 之间)
#     """
#     if not vec1 or not vec2:
#         return 0.0
        
#     vec1 = np.array(vec1)
#     vec2 = np.array(vec2)
    
#     dot_product = np.dot(vec1, vec2)
#     norm1 = np.linalg.norm(vec1)
#     norm2 = np.linalg.norm(vec2)
    
#     if norm1 == 0 or norm2 == 0:
#         return 0.0
        
#     return dot_product / (norm1 * norm2)

# # 使用示例
# async def example_usage():
#     config = {
#         "api_key": "your-api-key",
#         "model": "text-embedding-ada-002",
#         "service_url": "https://api.openai.com/v1/embeddings"
#     }
    
#     # 单个文本转换
#     text = "这是一个测试文本"
#     embedding = await get_embedding_vector(text, config)
#     if embedding:
#         print(f"向量维度: {len(embedding)}")
    
#     # 批量文本转换
#     texts = ["文本1", "文本2", "文本3"]
#     embeddings = await get_embeddings_batch(texts, config)
#     for i, emb in enumerate(embeddings):
#         if emb:
#             print(f"文本{i+1}向量维度: {len(emb)}")
    
#     # 计算相似度
#     if embeddings[0] and embeddings[1]:
#         similarity = cosine_similarity(embeddings[0], embeddings[1])
#         print(f"文本1和文本2的相似度: {similarity}") 

EMBEDDING_API_URL="http://122.14.231.69:6033/v1/embeddings"
EMBEDDING_API_KEY="sk-aaabbbcccdddeeefffggghhhiiijjjkkk"
headers = {
    'Content-Type': 'application/json',
    'Authorization': f'Bearer {EMBEDDING_API_KEY}'
}

def get_large_embedding(data):
    json = {
        "input": [data],
        "model": "bge-large"
    }
    res = requests.post(EMBEDDING_API_URL, json=json, headers=headers)
    res =res.json().get("data")[0].get("embedding")
    return res

if __name__ == '__main__':
    # config = {
    #     "api_key": "sk-BOstXojjXywbG7QlA76eA282A4724e91915d045512239d9e",
    #     "model": "bge-m3",
    #     "service_url": "http://122.14.231.165:3001/v1"
    # }
    
    # # 单个文本转换
    # text = "这是一个测试文本"
    
    # embedding = asyncio.run(get_embedding_vector(text, config))
    # if embedding:
    #     print(f"向量维度: {len(embedding)}")
    #     print(embedding[:10])
    
    # data = {
    #     "input": ["信托业务分类通知正式下发 涉及三大变化", "第十一条 国务院反洗钱行政主管部门为履行反洗钱资金监测职责"],
    #     "model": "bge-large"
    # }
    # data = "信托业务分类通知正式下发涉及三大变化"
    # response = get_embedding(data)
    # # data=response.get("data")[0].get("embedding")
    # print(response)
    ...