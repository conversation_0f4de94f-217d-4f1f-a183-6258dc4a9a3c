from langgraph.graph import StateGraph, END
from typing import Dict, List, Any, TypedDict, Optional, AsyncGenerator, Annotated
from langgraph.graph.message import add_messages
import httpx
import asyncio
from app.utils.logging_config import setup_logging, get_logger
import time
import json
import hashlib
import os
from langchain_openai import ChatOpenAI
from app.models.llm import LLMModel
from bson import ObjectId
from fastapi import Request
from pydantic import BaseModel, Field
from typing import Literal, List as PydanticList

setup_logging()
logger = get_logger(__name__)

class AgentState(TypedDict):
    content_input: str
    prompt: Optional[Dict[str, str]]
    llm_result: Optional[Dict[str, Any]]
    final_result: Optional[Dict[str, Any]]

class Extraction(BaseModel):
    question: str = Field(description="问题")
    answer: str = Field(description="答案")
    confidence: float = Field(description="置信度")

class ExtractionResult(BaseModel):
    extractions: PydanticList[Extraction] = Field(description="抽取结果列表")


class ScriptMining:
    def __init__(self, config: Dict[str, Any], db, evaluation_type: str, llm: LLMModel, content: List[str]):
        self.config = config
        self.evaluation_type = evaluation_type
        self.db = db
        self.content = content
        self.workflow = StateGraph(AgentState)
        self.llm = ChatOpenAI(
                model=llm.get("m_name"),
                temperature=float(llm.get("temperature", 0)),
                openai_api_key=llm.get("api_key"),
                openai_api_base= llm.get("service_url"),
                stream = False
            )

        self._setup_workflow()

    def _setup_workflow(self):
        # 定义工作流节点
        # 第一个节点：获取规则
        # 第二个节点：构建提示词
        # 第三个节点：调用大模型推理
        # 第四个节点：解析结果并返回
        self.workflow.add_node("build_prompt", self.build_prompt_node)
        self.workflow.add_node("llm_inference", self.llm_inference_node)
        self.workflow.add_node("extract_result", self.extract_result_node)
        
        # 配置节点之间的连接
        self.workflow.add_edge("build_prompt", "llm_inference")
        self.workflow.add_edge("llm_inference", "extract_result")
        self.workflow.add_edge("extract_result", END)
        
        # 设置起始节点
        self.workflow.set_entry_point("build_prompt")

    async def run(self) -> Dict[str, Any]:
        """执行整个工作流程"""
        try:
            # 准备初始状态
            initial_state = AgentState(
                content_input="\n".join(self.content),
                prompt=None,
                llm_result=None,
                final_result=None
            )
            
            # 执行工作流

            app = self.workflow.compile()
            result = await app.ainvoke(initial_state)
            return {
                "status": "success",
                "message": "规则评估完成",
                "data": result.get("final_result", {})
            }
        except Exception as e:
            logger.error(f"规则评估执行失败: {str(e)}")
            return {
                "status": "error",
                "message": f"规则评估执行失败: {str(e)}",
                "data": {}
            }

        
    async def build_prompt_node(self, state: AgentState) -> AgentState:
        """构建提示词节点"""
        try:
            logger.info('构建提示词')
            
            # 获取内容和规则
            content = state.get("content_input", "")

            # 构建最终提示词
            system_prompt = f"""请仔细阅读以下文本内容，并从中提取重要的问答对。每个问答对应包含问题和答案。
        
请按照以下要求提取:
1. 从文本中识别重要信息点,将其转化为问答形式
2. 问题应该简洁明确
3. 答案应该完整准确,直接引用原文相关内容
4. 每个问答对的置信度为0.8-1.0之间

请以JSON格式输出,包含以下字段:
- question: 问题
- answer: 答案
- confidence: 置信度(0.8-1.0)

示例格式:
{{
  "extractions": [
    {{
      "question": "问题1",
      "answer": "答案1", 
      "confidence": 0.9
    }}
  ]
}}"""


            user_prompt = f"""
请从下文中抽取问答对

---
{content}
---
            """
            
            state["prompt"] = {
                "system_prompt": system_prompt,
                "user_prompt": user_prompt
            }
            return state
        except Exception as e:
            logger.error(f"构建提示词失败: {str(e)}")
            state["prompt"] = {
                "system_prompt": "抽取问答对",
                "user_prompt": state.get("content_input", "")
            }
            return state

    async def llm_inference_node(self, state: AgentState) -> AgentState:
        """调用大模型进行推理"""
        try:
            logger.info('调用大模型进行推理')
            
            prompt = state.get("prompt", {})
            system_prompt = prompt.get("system_prompt", "")
            user_prompt = prompt.get("user_prompt", "")
            
            # 使用LangChain调用模型并进行结构化输出
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            logger.info(f'messages: {messages}')
            
            # 使用结构化输出
            structured_llm = self.llm.with_structured_output(ExtractionResult)
            result = await structured_llm.ainvoke(messages)
            logger.info(f'result===========>: {result}')
            state["llm_result"] = result.dict()
            logger.info('大模型推理完成')
            return state
        except Exception as e:
            logger.error(f"大模型推理失败: {str(e)}")
            state["llm_result"] = None
            return state

    async def extract_result_node(self, state: AgentState) -> AgentState:
        """解析模型输出并格式化结果"""
        try:
            logger.info('解析模型输出')
            
            result_data = state.get("llm_result", {})
            
            # 添加元数据
            result_data["evaluationTime"] = time.strftime("%Y-%m-%d %H:%M:%S")
            
            # 构建最终结果
            final_result = {
                "extractions": result_data.get("extractions", []),
                "result": "success",
                "status": "completed"
            }
            
            logger.info(f'final_result: {final_result}')
            
            state["final_result"] = final_result
            logger.info('结果解析完成')
            return state
            
        except Exception as e:
            logger.error(f"解析结果失败: {str(e)}")
            state["final_result"] = {
                "extractions": [],
                "result": "处理失败", 
                "status": "error",
                "message": str(e)
            }
            return state