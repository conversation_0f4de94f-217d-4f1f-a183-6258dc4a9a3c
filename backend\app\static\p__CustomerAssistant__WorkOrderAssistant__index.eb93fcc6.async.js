"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9645],{11319:function(e,t,i){i.r(t),i.d(t,{default:function(){return A}});var r=i(5574),n=i.n(r),a=i(71471),l=i(2487),o=i(4393),s=i(67294),d=i(93967),c=i.n(d),m=i(53124),p=i(35792),g=i(11568),h=i(14747),u=i(83559),b=i(83262);const x=e=>{const{componentCls:t,calc:i}=e;return{[t]:Object.assign(Object.assign({},(0,h.Wf)(e)),{margin:0,padding:0,listStyle:"none",[`${t}-item`]:{position:"relative",margin:0,paddingBottom:e.itemPaddingBottom,fontSize:e.fontSize,listStyle:"none","&-tail":{position:"absolute",insetBlockStart:e.itemHeadSize,insetInlineStart:i(i(e.itemHeadSize).sub(e.tailWidth)).div(2).equal(),height:`calc(100% - ${(0,g.bf)(e.itemHeadSize)})`,borderInlineStart:`${(0,g.bf)(e.tailWidth)} ${e.lineType} ${e.tailColor}`},"&-pending":{[`${t}-item-head`]:{fontSize:e.fontSizeSM,backgroundColor:"transparent"},[`${t}-item-tail`]:{display:"none"}},"&-head":{position:"absolute",width:e.itemHeadSize,height:e.itemHeadSize,backgroundColor:e.dotBg,border:`${(0,g.bf)(e.dotBorderWidth)} ${e.lineType} transparent`,borderRadius:"50%","&-blue":{color:e.colorPrimary,borderColor:e.colorPrimary},"&-red":{color:e.colorError,borderColor:e.colorError},"&-green":{color:e.colorSuccess,borderColor:e.colorSuccess},"&-gray":{color:e.colorTextDisabled,borderColor:e.colorTextDisabled}},"&-head-custom":{position:"absolute",insetBlockStart:i(e.itemHeadSize).div(2).equal(),insetInlineStart:i(e.itemHeadSize).div(2).equal(),width:"auto",height:"auto",marginBlockStart:0,paddingBlock:e.customHeadPaddingVertical,lineHeight:1,textAlign:"center",border:0,borderRadius:0,transform:"translate(-50%, -50%)"},"&-content":{position:"relative",insetBlockStart:i(i(e.fontSize).mul(e.lineHeight).sub(e.fontSize)).mul(-1).add(e.lineWidth).equal(),marginInlineStart:i(e.margin).add(e.itemHeadSize).equal(),marginInlineEnd:0,marginBlockStart:0,marginBlockEnd:0,wordBreak:"break-word"},"&-last":{[`> ${t}-item-tail`]:{display:"none"},[`> ${t}-item-content`]:{minHeight:i(e.controlHeightLG).mul(1.2).equal()}}},[`&${t}-alternate,\n        &${t}-right,\n        &${t}-label`]:{[`${t}-item`]:{"&-tail, &-head, &-head-custom":{insetInlineStart:"50%"},"&-head":{marginInlineStart:i(e.marginXXS).mul(-1).equal(),"&-custom":{marginInlineStart:i(e.tailWidth).div(2).equal()}},"&-left":{[`${t}-item-content`]:{insetInlineStart:`calc(50% - ${(0,g.bf)(e.marginXXS)})`,width:`calc(50% - ${(0,g.bf)(e.marginSM)})`,textAlign:"start"}},"&-right":{[`${t}-item-content`]:{width:`calc(50% - ${(0,g.bf)(e.marginSM)})`,margin:0,textAlign:"end"}}}},[`&${t}-right`]:{[`${t}-item-right`]:{[`${t}-item-tail,\n            ${t}-item-head,\n            ${t}-item-head-custom`]:{insetInlineStart:`calc(100% - ${(0,g.bf)(i(i(e.itemHeadSize).add(e.tailWidth)).div(2).equal())})`},[`${t}-item-content`]:{width:`calc(100% - ${(0,g.bf)(i(e.itemHeadSize).add(e.marginXS).equal())})`}}},[`&${t}-pending\n        ${t}-item-last\n        ${t}-item-tail`]:{display:"block",height:`calc(100% - ${(0,g.bf)(e.margin)})`,borderInlineStart:`${(0,g.bf)(e.tailWidth)} dotted ${e.tailColor}`},[`&${t}-reverse\n        ${t}-item-last\n        ${t}-item-tail`]:{display:"none"},[`&${t}-reverse ${t}-item-pending`]:{[`${t}-item-tail`]:{insetBlockStart:e.margin,display:"block",height:`calc(100% - ${(0,g.bf)(e.margin)})`,borderInlineStart:`${(0,g.bf)(e.tailWidth)} dotted ${e.tailColor}`},[`${t}-item-content`]:{minHeight:i(e.controlHeightLG).mul(1.2).equal()}},[`&${t}-label`]:{[`${t}-item-label`]:{position:"absolute",insetBlockStart:i(i(e.fontSize).mul(e.lineHeight).sub(e.fontSize)).mul(-1).add(e.tailWidth).equal(),width:`calc(50% - ${(0,g.bf)(e.marginSM)})`,textAlign:"end"},[`${t}-item-right`]:{[`${t}-item-label`]:{insetInlineStart:`calc(50% + ${(0,g.bf)(e.marginSM)})`,width:`calc(50% - ${(0,g.bf)(e.marginSM)})`,textAlign:"start"}}},"&-rtl":{direction:"rtl",[`${t}-item-head-custom`]:{transform:"translate(50%, -50%)"}}})}};var f=(0,u.I$)("Timeline",(e=>{const t=(0,b.IX)(e,{itemHeadSize:10,customHeadPaddingVertical:e.paddingXXS,paddingInlineEnd:2});return[x(t)]}),(e=>({tailColor:e.colorSplit,tailWidth:e.lineWidthBold,dotBorderWidth:e.wireframe?e.lineWidthBold:3*e.lineWidth,dotBg:e.colorBgContainer,itemPaddingBottom:1.25*e.padding}))),$=function(e,t){var i={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(i[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(e);n<r.length;n++)t.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(i[r[n]]=e[r[n]])}return i};var S=e=>{var{prefixCls:t,className:i,color:r="blue",dot:n,pending:a=!1,position:l,label:o,children:d}=e,p=$(e,["prefixCls","className","color","dot","pending","position","label","children"]);const{getPrefixCls:g}=s.useContext(m.E_),h=g("timeline",t),u=c()(`${h}-item`,{[`${h}-item-pending`]:a},i),b=/blue|red|green|gray/.test(r||"")?void 0:r,x=c()(`${h}-item-head`,{[`${h}-item-head-custom`]:!!n,[`${h}-item-head-${r}`]:!b});return s.createElement("li",Object.assign({},p,{className:u}),o&&s.createElement("div",{className:`${h}-item-label`},o),s.createElement("div",{className:`${h}-item-tail`}),s.createElement("div",{className:x,style:{borderColor:b,color:b}},n),s.createElement("div",{className:`${h}-item-content`},d))},v=i(74902),y=i(19267),j=function(e,t){var i={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(i[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(e);n<r.length;n++)t.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(i[r[n]]=e[r[n]])}return i};var w=e=>{var{prefixCls:t,className:i,pending:r=!1,children:n,items:a,rootClassName:l,reverse:o=!1,direction:d,hashId:m,pendingDot:p,mode:g=""}=e,h=j(e,["prefixCls","className","pending","children","items","rootClassName","reverse","direction","hashId","pendingDot","mode"]);const u=(e,i)=>"alternate"===g?"right"===e?`${t}-item-right`:"left"===e||i%2==0?`${t}-item-left`:`${t}-item-right`:"left"===g?`${t}-item-left`:"right"===g||"right"===e?`${t}-item-right`:"",b=(0,v.Z)(a||[]),x="boolean"==typeof r?null:r;r&&b.push({pending:!!r,dot:p||s.createElement(y.Z,null),children:x}),o&&b.reverse();const f=b.length,$=`${t}-item-last`,w=b.filter((e=>!!e)).map(((e,t)=>{var i;const n=t===f-2?$:"",a=t===f-1?$:"",{className:l}=e,d=j(e,["className"]);return s.createElement(S,Object.assign({},d,{className:c()([l,!o&&r?n:a,u(null!==(i=null==e?void 0:e.position)&&void 0!==i?i:"",t)]),key:(null==e?void 0:e.key)||t}))})),O=b.some((e=>!!(null==e?void 0:e.label))),C=c()(t,{[`${t}-pending`]:!!r,[`${t}-reverse`]:!!o,[`${t}-${g}`]:!!g&&!O,[`${t}-label`]:O,[`${t}-rtl`]:"rtl"===d},i,l,m);return s.createElement("ul",Object.assign({},h,{className:C}),w)},O=i(50344);var C=function(e,t){return e&&Array.isArray(e)?e:(0,O.Z)(t).map((e=>{var t,i;return Object.assign({children:null!==(i=null===(t=null==e?void 0:e.props)||void 0===t?void 0:t.children)&&void 0!==i?i:""},e.props)}))},I=function(e,t){var i={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(i[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(e);n<r.length;n++)t.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(i[r[n]]=e[r[n]])}return i};const k=e=>{const{getPrefixCls:t,direction:i,timeline:r}=s.useContext(m.E_),{prefixCls:n,children:a,items:l,className:o,style:d}=e,g=I(e,["prefixCls","children","items","className","style"]),h=t("timeline",n);const u=(0,p.Z)(h),[b,x,$]=f(h,u),S=C(l,a);return b(s.createElement(w,Object.assign({},g,{className:c()(null==r?void 0:r.className,o,$,u),style:Object.assign(Object.assign({},null==r?void 0:r.style),d),prefixCls:h,direction:i,items:S,hashId:x})))};k.Item=S;var N=k,z=i(9783),H=i.n(z),B=(0,i(24444).kc)((function(e){var t=e.token;return{card:{".ant-card-meta-title":{marginBottom:"4px","& > a":{display:"inline-block",maxWidth:"100%",color:t.colorTextHeading}},".ant-card-meta-description":{height:"44px",overflow:"hidden",lineHeight:"22px"},"&:hover":{".ant-card-meta-title > a":{color:t.colorPrimary}}},cardItemContent:{display:"flex",height:"20px",marginTop:"16px",marginBottom:"-4px",lineHeight:"20px","& > span":{flex:"1",color:t.colorTextSecondary,fontSize:"12px"}},avatarList:{flex:"0 1 auto"},cardList:{marginTop:"24px"},coverCardList:{".ant-list .ant-list-item-content-single":{maxWidth:"100%"}},tags:{display:"flex",flexWrap:"wrap",gap:"8px"},tag:{padding:"2px 8px",backgroundColor:"#f5f5f5",borderRadius:"4px",fontSize:"12px"},pageHeaderContent:H()({position:"relative"},"@media screen and (max-width: ".concat(t.screenSM,"px)"),{paddingBottom:"30px"}),contentLink:H()(H()({marginTop:"16px",a:{marginRight:"32px",img:{width:"24px"}},img:{marginRight:"8px",verticalAlign:"middle"}},"@media screen and (max-width: ".concat(t.screenLG,"px)"),{a:{marginRight:"16px"}}),"@media screen and (max-width: ".concat(t.screenSM,"px)"),{position:"absolute",bottom:"-4px",left:"0",width:"1000px",a:{marginRight:"16px"},img:{marginRight:"4px"}}),extraImg:H()({width:"155px",marginTop:"-20px",textAlign:"center",img:{width:"100%"}},"@media screen and (max-width: ".concat(t.screenMD,"px)"),{display:"none"})}})),P=i(97131),E=i(85893),W=a.Z.Paragraph,T=[{id:"1",name:"舆情报送流程",description:"舆情信息报送流程",workOrderFlowList:[{title:"舆情报送初报",children:"舆情分析师",url:"/customerAssistant/WorkOrderAssistant/PublicOpinionInitialReport"},{title:"舆情报送结案报",children:"办公室",url:"/customerAssistant/WorkOrderAssistant/PublicOpinionClsureReport"}]},{id:"2",name:"IT问题反馈工作流",description:"工单2描述",workOrderFlowList:[{title:"IT问题反馈",children:"IT工程师",url:"/customerAssistant/WorkOrderAssistant/ITIncidentReporting"},{title:"问题解决",children:"技术人员处理并解决问题",url:"#"},{title:"工单关闭",children:"确认问题解决后关闭工单",url:"#"}]}],A=function(){var e=B().styles,t=(0,s.useState)(1),i=n()(t,2),r=i[0],a=i[1],d=(0,E.jsx)(l.Z,{rowKey:"id",grid:{gutter:16,xs:1,sm:2,md:3,lg:3,xl:4,xxl:4},pagination:{current:r,pageSize:8,total:T.length,onChange:function(e){a(e)}},dataSource:T,renderItem:function(t){return(0,E.jsx)(l.Z.Item,{children:(0,E.jsxs)(o.Z,{className:e.card,hoverable:!0,children:[(0,E.jsx)(o.Z.Meta,{title:t.name,description:(0,E.jsx)(W,{ellipsis:{rows:2},children:t.description})}),(0,E.jsx)(N,{mode:"right",children:t.workOrderFlowList.map((function(e,t){return(0,E.jsx)(N.Item,{label:(0,E.jsx)("a",{href:e.url,rel:"noopener noreferrer",children:e.title}),children:e.children},t)}))})]})})}}),c=(0,E.jsxs)("div",{className:e.pageHeaderContent,children:[(0,E.jsx)("p",{children:"工单助手平台：通过智能化的工单管理流程，帮助企业高效处理客户问题， 提供从问题提交到解决的全流程支持。"}),(0,E.jsxs)("div",{className:e.contentLink,children:[(0,E.jsxs)("a",{children:[(0,E.jsx)("img",{alt:"",src:"https://gw.alipayobjects.com/zos/rmsportal/MjEImQtenlyueSmVEfUD.svg"})," ","开始使用"]}),(0,E.jsxs)("a",{children:[(0,E.jsx)("img",{alt:"",src:"https://gw.alipayobjects.com/zos/rmsportal/NbuDUAuBlIApFuDvWiND.svg"})," ","平台介绍"]}),(0,E.jsxs)("a",{children:[(0,E.jsx)("img",{alt:"",src:"https://gw.alipayobjects.com/zos/rmsportal/ohOEPSYdDTNnyMbGuyLb.svg"})," ","使用文档"]})]})]}),m=(0,E.jsx)("div",{className:e.extraImg,children:(0,E.jsx)("img",{alt:"工单助手",src:"https://gw.alipayobjects.com/zos/rmsportal/RzwpdLnhmvDJToTdfDPe.png"})});return(0,E.jsx)(P._z,{content:c,extraContent:m,fixedHeader:!0,header:{title:"工单工作流",breadcrumb:{routes:[{path:"/",breadcrumbName:"首页"},{path:"/work-order-workflow",breadcrumbName:"工单工作流"}]}},children:(0,E.jsx)("div",{className:e.coverCardList,children:(0,E.jsx)("div",{className:e.cardList,children:d})})})}}}]);