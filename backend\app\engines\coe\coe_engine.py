from typing import List, Dict, Any, Optional, Tuple
from .base_expert import BaseExpert, ExpertResponse
from app.utils.logging_config import setup_logging, get_logger

# 设置日志
setup_logging()
logger = get_logger(__name__)

class COEEngine:
    """Coordinator of Experts 引擎"""
    
    def __init__(self, experts: List[BaseExpert], confidence_threshold: float = 0.7):
        """
        初始化 COE 引擎
        
        Args:
            experts: 专家列表
            confidence_threshold: 置信度阈值，超过该阈值的专家才会被选择
        """
        self.experts = experts
        self.confidence_threshold = confidence_threshold
    
    async def get_best_expert(self, query: str) -> Optional[Tuple[BaseExpert, float]]:
        """
        获取最合适的专家
        
        Args:
            query: 用户查询
            
        Returns:
            Tuple[BaseExpert, float]: (最佳专家, 置信度) 或 None
        """
        expert_scores = []
        for expert in self.experts:
            try:
                confidence = await expert.can_handle(query)
                if confidence >= self.confidence_threshold:
                    expert_scores.append((expert, confidence))
            except Exception as e:
                logger.error(f"专家 {expert.name} 评估失败: {str(e)}")
        
        if not expert_scores:
            return None
        
        # 按置信度降序排序，返回最高的
        return max(expert_scores, key=lambda x: x[1])
    
    async def process_query(
        self,
        query: str,
        context: Optional[Dict[str, Any]] = None,
        fallback_response: Optional[str] = None
    ) -> ExpertResponse:
        """
        处理用户查询
        
        Args:
            query: 用户查询
            context: 上下文信息
            fallback_response: 当没有合适专家时的默认响应
            
        Returns:
            ExpertResponse: 专家响应结果
        """
        best_match = await self.get_best_expert(query)
        
        if best_match:
            expert, confidence = best_match
            logger.info(f"选择专家 {expert.name} 处理查询，置信度: {confidence}")
            try:
                return await expert.handle(query, context)
            except Exception as e:
                logger.error(f"专家 {expert.name} 处理失败: {str(e)}")
                if fallback_response:
                    return ExpertResponse(
                        content=fallback_response,
                        confidence=0.0,
                        metadata={"error": str(e)}
                    )
        
        # 没有合适的专家或处理失败
        return ExpertResponse(
            content=fallback_response or "抱歉，我无法处理这个问题。",
            confidence=0.0,
            metadata={"reason": "no_expert_available"}
        )
    
    def add_expert(self, expert: BaseExpert) -> None:
        """添加新专家"""
        self.experts.append(expert)
    
    def remove_expert(self, expert_name: str) -> bool:
        """移除专家"""
        for expert in self.experts:
            if expert.name == expert_name:
                self.experts.remove(expert)
                return True
        return False
    
    def get_expert(self, expert_name: str) -> Optional[BaseExpert]:
        """获取指定名称的专家"""
        for expert in self.experts:
            if expert.name == expert_name:
                return expert
        return None
    
    def list_experts(self) -> List[Dict[str, str]]:
        """列出所有专家"""
        return [
            {
                "name": expert.name,
                "description": expert.description
            }
            for expert in self.experts
        ] 