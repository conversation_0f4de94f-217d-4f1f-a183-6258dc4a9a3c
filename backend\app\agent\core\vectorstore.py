import os
import traceback
from typing import Any, Dict, List, Optional

import psycopg2
from app.utils.config import settings
from bson import ObjectId
from langchain_community.vectorstores import PGVector
from langchain_core.documents import Document
from langchain_openai import OpenAIEmbeddings
from psycopg2 import sql
from psycopg2.extras import Json
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from ..core.llm import get_embedding_model
from ..db.mongodb import db
from ..utils.logging_config import get_logger, setup_logging

setup_logging()
logger = get_logger(__name__)

async def retrieve_documents_with_sql(
    query: str,
    top_k: int = 5,
    knowledge_base_id: Optional[str] = None,
    embedding_model: Optional[OpenAIEmbeddings] = None,
    similarity_threshold: float = 0.5
) -> List[Dict[str, Any]]:
    """
    使用原生SQL查询直接从PostgreSQL数据库检索文档

    Args:
        query (str): 查询文本
        top_k (int): 返回的最大结果数
        knowledge_base_id (Optional[str]): 知识库ID过滤
        embedding_model (Optional[OpenAIEmbeddings]): 嵌入模型，如果为None则创建新的
        similarity_threshold (float): 相似度阈值，低于此值的结果将被过滤掉

    Returns:
        List[Dict[str, Any]]: 格式化的文档列表
    """
    try:
        # 获取嵌入模型
        if embedding_model is None:
            embedding_model = get_embedding_model()
            logger.info(f"使用默认嵌入模型: {type(embedding_model).__name__}")

        # 生成查询文本的嵌入向量
        query_embedding = embedding_model.embed_query(query)

        # 检查数据库中向量的维度（这里假设是1024）
        DB_VECTOR_DIMENSION = 1536

        # 如果生成的向量维度与数据库不匹配，进行扩充或截断
        if len(query_embedding) != DB_VECTOR_DIMENSION:
            if len(query_embedding) < DB_VECTOR_DIMENSION:
                # 扩充向量维度
                query_embedding = query_embedding + [0.0] * (DB_VECTOR_DIMENSION - len(query_embedding))
                logger.info(f"向量维度已扩充至 {DB_VECTOR_DIMENSION}")
            else:
                # 截断向量维度
                query_embedding = query_embedding[:DB_VECTOR_DIMENSION]
                logger.info(f"向量维度已截断至 {DB_VECTOR_DIMENSION}")

        logger.info(f"最终查询向量维度: {len(query_embedding)}")

        # 建立数据库连接
        conn = psycopg2.connect(
            host=settings.POSTGRES_HOST,
            port=settings.POSTGRES_PORT,
            database=settings.POSTGRES_DB,
            user=settings.POSTGRES_USER,
            password=settings.POSTGRES_PASSWORD
        )
        cursor = conn.cursor()

        logger.info(f"成功连接到数据库: {settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}")

        # 构建SQL查询 - 使用向量相似度搜索
        sql_query = f"""
            SELECT
                id,
                content,
                file_id,
                knowledge_base_id,
                chunk_id,
                1 - (embedding <=> %s::vector) AS similarity
            FROM
                {settings.VECTOR_DATABASE_TABLE}
            WHERE
                1=1
        """

        params = [query_embedding]

        # 添加知识库过滤条件
        if knowledge_base_id:
            sql_query += " AND knowledge_base_id = %s"
            params.append(knowledge_base_id)
            logger.info(f"使用知识库过滤: {knowledge_base_id}")

        # 添加相似度阈值过滤
        sql_query += f" AND (1 - (embedding <=> %s::vector)) >= %s"
        params.append(query_embedding)
        params.append(similarity_threshold)

        # 按相似度排序并限制结果数量
        sql_query += """
            ORDER BY similarity DESC
            LIMIT %s;
        """
        params.append(top_k)

        # 执行查询
        logger.info(f"执行向量相似度查询，查询: '{query}', top_k: {top_k}, 相似度阈值: {similarity_threshold}")
        cursor.execute(sql_query, params)

        # 获取结果
        results = cursor.fetchall()

        # 提取文档内容和元数据
        documents = []
        for row in results:
            # 确保索引与SELECT语句中的列顺序一致
            doc_id = row[0]
            content = row[1]
            file_id = row[2] if len(row) > 2 else None
            kb_id = row[3] if len(row) > 3 else None
            chunk_id = row[4] if len(row) > 4 else None
            similarity = row[5] if len(row) > 5 else 0.0

            documents.append({
                "id": doc_id,
                "content": content,
                "file_id": file_id,
                "knowledge_base_id": kb_id,
                "chunk_id": chunk_id,
                "similarity": similarity,
                "distance": 1.0 - similarity  # 计算距离值
            })

        logger.info(f"向量检索到 {len(documents)} 个文档，相似度阈值: {similarity_threshold}")

        # 关闭连接
        cursor.close()
        conn.close()

        return documents
    except Exception as e:
        logger.error(f"向量文档检索失败: {str(e)}")
        logger.error(traceback.format_exc())
        # 返回空列表而不是抛出异常，以便调用者可以优雅地处理
        return []



async def insert_index_data(index_data, chunk, embedding_model: Optional[OpenAIEmbeddings] = None,):
    conn = None
    try:
        conn = psycopg2.connect(
            host=settings.POSTGRES_HOST,
            port=settings.POSTGRES_PORT,
            database=settings.POSTGRES_DB,
            user=settings.POSTGRES_USER,
            password=settings.POSTGRES_PASSWORD
        )
        if embedding_model is None:
            embedding_model = get_embedding_model()
            logger.info(f"使用默认嵌入模型: {type(embedding_model).__name__}")
        cursor = conn.cursor()
        insert_query = sql.SQL("""
            INSERT INTO knowledge_index
            (id, content, knowledge_base_id, chunk_id, embedding)
            VALUES (%s, %s, %s, %s, %s)
        """)
        logger.info(f"index_data: {index_data}")


        for item in index_data:
            id = str(ObjectId())
            embedding = embedding_model.embed_query(item)
            data = (
                id,
                item,
                str(chunk["knowledge_base_id"]),
                str(chunk["_id"]),
                embedding+ [0.0]*(1536 - len(embedding))
            )
            logger.info(f"插入知识库索引: {data}")
            cursor.execute(insert_query, data)
            conn.commit()
        logger.info("数据插入成功")

    except Exception as e:
        logger.error(f"插入失败: {str(e)}")
        logger.error(traceback.format_exc())
        if conn: conn.rollback()
    finally:
        if conn: conn.close()


async def get_chunks_by_ids(ids: List[str]) -> List[Dict[str, Any]]:
    """
    根据给定的ID列表获取对应的文档内容

    Args:
        ids (List[str]): 文档ID列表

    Returns:
        List[Dict[str, Any]]: 文档内容列表
    """
    try:
        # 使用MongoDB的$in操作符查询多个ID
        chunks = await db.knowledge_chunk.find({"_id": {"$in": [ObjectId(id) for id in ids]}}).to_list(length=None)

        # 转换ObjectId为字符串
        for chunk in chunks:
            chunk["_id"] = str(chunk["_id"])
            if "knowledge_base_id" in chunk and isinstance(chunk["knowledge_base_id"], ObjectId):
                chunk["knowledge_base_id"] = str(chunk["knowledge_base_id"])

        logger.info(f"成功获取 {len(chunks)} 个文档块")
        return chunks
    except Exception as e:
        logger.error(f"获取文档块失败: {str(e)}")
        logger.error(traceback.format_exc())
        return []




async def retrieve_documents_by_chunk_id(
    chunk_id: str,
) -> List[Dict[str, Any]]:
    """
    使用原生SQL查询直接从PostgreSQL数据库检索文档

    Args:
        query (str): 查询文本
        top_k (int): 返回的最大结果数
        knowledge_base_id (Optional[str]): 知识库ID过滤
        embedding_model (Optional[OpenAIEmbeddings]): 嵌入模型，如果为None则创建新的
        similarity_threshold (float): 相似度阈值，低于此值的结果将被过滤掉

    Returns:
        List[Dict[str, Any]]: 格式化的文档列表
    """
    try:
        conn = psycopg2.connect(
            host=settings.POSTGRES_HOST,
            port=settings.POSTGRES_PORT,
            database=settings.POSTGRES_DB,
            user=settings.POSTGRES_USER,
            password=settings.POSTGRES_PASSWORD
        )
        cursor = conn.cursor()

        logger.info(f"成功连接到数据库: {settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}")

        # 构建SQL查询 - 使用向量相似度搜索
        sql_query = f"""
            SELECT
                id,
                content,
                file_id,
                knowledge_base_id,
                chunk_id
            FROM
                {settings.VECTOR_DATABASE_TABLE}
            WHERE
                chunk_id = %s
        """

        params = [chunk_id]
        cursor.execute(sql_query, params)

        # 获取结果
        results = cursor.fetchall()

        # 提取文档内容和元数据
        documents = []
        for row in results:
            # 确保索引与SELECT语句中的列顺序一致
            doc_id = row[0]
            content = row[1]
            file_id = row[2] if len(row) > 2 else None
            kb_id = row[3] if len(row) > 3 else None
            chunk_id = row[4] if len(row) > 4 else None

            documents.append({
                "id": doc_id,
                "content": content,
                "file_id": file_id,
                "knowledge_base_id": kb_id,
                "chunk_id": chunk_id,
            })

        logger.info(f"向量检索到 {len(documents)} 个文档")

        # 关闭连接
        cursor.close()
        conn.close()

        return documents
    except Exception as e:
        logger.error(f"向量文档检索失败: {str(e)}")
        logger.error(traceback.format_exc())
        # 返回空列表而不是抛出异常，以便调用者可以优雅地处理
        return []


async def delete_vector_records(content=None, chunk_id=None, knowledge_base_id=None):
    """
    根据内容、chunk_id和knowledge_base_id删除向量记录

    Args:
        content: 内容
        chunk_id: 知识块ID
        knowledge_base_id: 知识库ID

    Returns:
        int: 删除的记录数量
    """
    try:
        # 至少需要一个条件
        if not any([content, chunk_id, knowledge_base_id]):
            logger.error("删除向量记录需要至少一个条件")
            return 0

        # 连接到数据库
        conn = psycopg2.connect(
            host=settings.POSTGRES_HOST,
            port=settings.POSTGRES_PORT,
            database=settings.POSTGRES_DB,
            user=settings.POSTGRES_USER,
            password=settings.POSTGRES_PASSWORD
        )
        cursor = conn.cursor()

        # 构建SQL查询
        sql_query = f"DELETE FROM {settings.VECTOR_DATABASE_TABLE} WHERE "
        conditions = []
        params = []

        if content:
            conditions.append("content = %s")
            params.append(content)

        if chunk_id:
            conditions.append("chunk_id = %s")
            params.append(chunk_id)

        if knowledge_base_id:
            conditions.append("knowledge_base_id = %s")
            params.append(knowledge_base_id)

        sql_query += " AND ".join(conditions)

        # 执行删除操作
        cursor.execute(sql_query, params)
        deleted_count = cursor.rowcount

        # 提交事务
        conn.commit()

        # 关闭连接
        cursor.close()
        conn.close()

        logger.info(f"成功删除 {deleted_count} 条向量记录")
        return deleted_count

    except Exception as e:
        logger.error(f"删除向量记录失败: {str(e)}")
        logger.error(traceback.format_exc())
        return 0


async def delete_vector_by_id(id):
    """
    根据ID删除向量记录

    Args:
        id: 向量记录ID

    Returns:
        int: 删除的记录数量
    """
    try:
        if not id:
            logger.error("删除向量记录需要提供ID")
            return 0

        # 连接到数据库
        conn = psycopg2.connect(
            host=settings.POSTGRES_HOST,
            port=settings.POSTGRES_PORT,
            database=settings.POSTGRES_DB,
            user=settings.POSTGRES_USER,
            password=settings.POSTGRES_PASSWORD
        )
        cursor = conn.cursor()

        # 构建SQL查询
        sql_query = f"DELETE FROM {settings.VECTOR_DATABASE_TABLE} WHERE id = %s"
        params = [id]

        # 执行删除操作
        cursor.execute(sql_query, params)
        deleted_count = cursor.rowcount

        # 提交事务
        conn.commit()

        # 关闭连接
        cursor.close()
        conn.close()

        logger.info(f"成功删除ID为 {id} 的向量记录")
        return deleted_count

    except Exception as e:
        logger.error(f"删除向量记录失败: {str(e)}")
        logger.error(traceback.format_exc())
        return 0



async def delete_vector_by_chunk_id(chunk_id):
    """
    根据chunk_id删除向量记录

    Args:
        id: 向量记录ID

    Returns:
        int: 删除的记录数量
    """
    try:
        if not id:
            logger.error("删除向量记录需要提供ID")
            return 0

        # 连接到数据库
        conn = psycopg2.connect(
            host=settings.POSTGRES_HOST,
            port=settings.POSTGRES_PORT,
            database=settings.POSTGRES_DB,
            user=settings.POSTGRES_USER,
            password=settings.POSTGRES_PASSWORD
        )
        cursor = conn.cursor()

        # 构建SQL查询
        sql_query = f"DELETE FROM {settings.VECTOR_DATABASE_TABLE} WHERE chunk_id = %s"
        params = [chunk_id]

        # 执行删除操作
        cursor.execute(sql_query, params)
        deleted_count = cursor.rowcount

        # 提交事务
        conn.commit()

        # 关闭连接
        cursor.close()
        conn.close()

        logger.info(f"成功删除chunk_id为 {chunk_id} 的向量记录")
        return deleted_count

    except Exception as e:
        logger.error(f"删除向量记录失败: {str(e)}")
        logger.error(traceback.format_exc())
        return 0
