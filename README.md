# 项目名称

## 项目介绍

项目名称是一个基于 React 和 UmiJS 构建的现代化 Web 应用程序，旨在提供高效的用户体验和强大的功能。该项目集成了多种工具和技术，以实现复杂的业务逻辑和用户界面。

## 文件组织结构

```
项目根目录
├── src
│   ├── pages                  # 页面组件
│   │   ├── LLMmarket          # LLM市场相关页面
│   │   ├── KnowledgeManagement# 知识管理相关页面
│   │   ├── ComplianceAssistant# 合规助手相关页面
│   │   ├── AgentBuilder       # 智能体构建相关页面
│   │   ├── SmartBIManagement  # 智能BI管理相关页面
│   │   ├── PreLoanAssistant   # 贷前助手相关页面
│   │   ├── MidLoanAssistant   # 贷中助手相关页面
│   │   ├── PostLoanAssistant  # 贷后助手相关页面
│   │   └── ...                # 其他页面
│   ├── locales                # 国际化文件
│   │   ├── zh-CN              # 中文翻译
│   │   ├── en-US              # 英文翻译
│   │   ├── fa-IR              # 波斯语翻译
│   │   ├── id-ID              # 印尼语翻译
│   │   ├── ja-JP              # 日语翻译
│   │   └── pt-BR              # 葡萄牙语翻译
│   ├── components             # 通用组件
│   ├── utils                  # 工具函数
│   └── ...                    # 其他目录
├── config                     # 配置文件
│   └── routes.ts              # 路由配置
├── public                     # 静态资源
├── README.md                  # 项目说明文件
└── package.json               # 项目依赖和脚本
```

## 技术栈

- **React**: 用于构建用户界面的 JavaScript 库。
- **UmiJS**: 基于 React 的企业级前端应用框架。
- **Ant Design**: 一套企业级 UI 设计语言和 React 实现。
- **TypeScript**: JavaScript 的超集，提供了静态类型检查。
- **Node.js**: JavaScript 运行时，用于开发工具和构建脚本。

## 启动命令

在开始之前，请确保您的计算机上已安装 [Node.js](https://nodejs.org/) 和 [Yarn](https://yarnpkg.com/)。

### 开发环境

1. **安装依赖**

   ```bash
   npm run dev
   ```

### 生产环境

1. **构建生产版本**

   ```bash
   npm run build:backend
   ```

### 测 �� 环境

#### 前端

在项目根目录下运行：

```bash
npm run dev
```

#### 后端

在 `backend` 目录下运行：

```bash
// 开发环境 链接内网MiniO等
ENV_MODE=dev uvicorn app.main:app --host 0.0.0.0 --port 8800 --reload

// 开发环境 不连接Minio
ENV_MODE=testing uvicorn app.main:app --host 0.0.0.0 --port 8800 --reload
```
$env:ENV_MODE="testing"; uvicorn app.main:app --host 0.0.0.0 --port 8800 --reload

## 贡献指南

欢迎对本项目的贡献！请确保在提交代码之前运行所有测试，并遵循项目的代码风格。

---

请根据项目的具体需求和特点对上述内容进行调整和补充。希望这个模板能帮助您更好地组织和展示项目的相关信息。

提交代码

git commit -m '你的提交信息' --no-verify

## 模型测试

curl http://ai.roardata.cn/v1/chat/completions \
 -H "Content-Type: application/json" \
 -H "Authorization: sk-6e94RWvIwV5TmX3M846529EeCf2b4d9b86A2566a4d4bA242" \
 -d '{ "model": "Qwen2.5-32Bawq-Chat-pai", "messages": [ { "role": "system", "content": "你是一个微博博主" }, { "role": "user", "content": "写一条关于德云社的段子微博" } ] }'

ENV_MODE=dev uvicorn app.main:app --host 0.0.0.0 --port 8800 --reload

pm2 start "uvicorn app.main:app --host 0.0.0.0 --port 8808 --reload" --name fastapi-app --env ENV_MODE=dev

### 生产环境启动命令

使用 pm2 启动服务：

```bash
# 使用开发环境配置启动
pm2 start "uvicorn app.main:app --host 0.0.0.0 --port 8808" --name WiseAgent --env ENV_MODE=dev
pm2 start "uvicorn app.main:app --host 0.0.0.0 --port 8808" --name WiseAgent --env ENV_MODE=production

# 使用生产环境配置启动
pm2 start "uvicorn app.main:app --host 0.0.0.0 --port 8800" --name fastapi-app --env ENV_MODE=prod

# 常用的 pm2 管理命令：
pm2 list                  # 查看所有运行的进程
pm2 stop fastapi-app     # 停止应用
pm2 restart fastapi-app  # 重启应用
pm2 delete fastapi-app   # 删除应用
pm2 logs fastapi-app    # 查看应用日志
```

好的，基于您提供的产品规划，我将补充完善以下部分：

**五、知识库简述**

**1. 知识库格式支持**

- **文本类：**
  - `.txt`：纯文本文件，用于存储原始文本数据。
  - `.csv`：逗号分隔值文件，用于存储结构化数据，如舆情数据列表、企业信息表。
  - `.json`：JSON 格式文件，用于存储半结构化数据，如新闻摘要、风险描述。
  - `.md`：Markdown 文件，用于存储带格式的文档，如风险分析报告模板、知识库文档。
  - `.docx`：Microsoft Word 文档，用于存储较为复杂的报告、政策文件等。
- **文档类:**
  - `.pdf`：PDF 文档，用于存储法律法规、行业标准、企业报告等。
- **其他:**
  - 图片格式：如`.png`，`.jpg`，用于品牌标识、新闻配图等。
  - URL：存储外部链接，用于引用外部新闻报道、社交媒体帖子等。
  - 数据库：支持连接各种类型的数据库，如 MySQL、PostgreSQL 等，用于存储结构化数据。

**2. 知识库数据内容**

- **舆情信息：**
  - **实时舆情数据：** 从探灯数据收集接口获取的实时新闻报道、社交媒体帖子、论坛讨论等，包含发布时间、来源、作者、内容、关键词、情感倾向等信息。
  - **历史舆情数据：** 企业过往的舆情监控数据，包括负面事件、处理结果、相关报告等，用于对比分析和风险预测。
  - **竞品舆情数据：** 竞争对手的舆情信息，用于分析行业动态和自身优劣势。
- **企业信息：**
  - **企业基本信息：** 企业名称、行业、业务范围、组织架构、联系方式等。
  - **品牌信息：** 品牌标识、品牌故事、品牌价值观等。
  - **产品信息：** 产品名称、功能、定价、用户评价等。
  - **合规信息：** 企业规章制度、合规文件、法律法规等。
  - **内部员工信息：** 内部员工信息和发帖习惯。
- **风险知识：**
  - **风险分类：** 按照不同维度对风险进行分类，如财务风险、运营风险、法律风险、声誉风险等。
  - **风险识别方法：** 用于识别风险的指标和方法。
  - **风险评估方法：** 用于评估风险等级和影响的方法。
  - **风险应对措施：** 针对不同风险的应对措施和预案。
  - **行业专家知识：** 行业专家的经验知识、风险分析报告、研究论文等。
  - **行业黑名单：** 行业负面清单、问题企业、人员名单等。

**3. 知识库数据大小**

- **初始阶段：** 建议初期知识库大小为 10GB，包括近一年的舆情数据和企业基本信息、风险知识等。
- **中期阶段：** 随着数据积累，知识库大小预计增长至 50-100GB，包括更长时期的舆情数据、更全面的企业信息和更丰富的风险知识。
- **长期阶段：** 长期知识库大小取决于数据积累速度和数据保留策略，可能达到数百 GB 甚至 TB 级别。

**六、人员配置和资源需求**

**1. 人员配置**

| 角色 | 团队/部门 | 工作量占比 (AI 企业声誉风险分析 Agent) | 工作量占比 (其他 Agent) | 备注 |
| --- | --- | --- | --- | --- |
| 项目经理 | 产品部门 | 100% | 0% | 负责项目整体规划、进度管理、资源协调 |
| AI 算法工程师 | AI 研发团队 | 60% | 40% | 负责自研基模开发、模型训练、模型微调、模型优化，并维护风险分析 Agent 中的风险分析模型，同时参与其他 Agent 的模型训练 |
| 数据工程师 | 数据部门 | 70% | 30% | 负责数据采集、数据清洗、数据存储、数据管理，维护知识库并支持数据接入，同时负责其他 Agent 的数据处理 |
| 前端工程师 | 前端开发团队 | 50% | 50% | 负责用户界面开发、前端逻辑开发，完成 Agent 平台页面、报告展示页面的开发 |
| 后端工程师 | 后端开发团队 | 60% | 40% | 负责 API 开发、数据接口开发、Agent 编排逻辑开发，支持模型和数据的访问，同时参与其他 Agent 的后端开发 |
| 测试工程师 | 测试部门 | 70% | 30% | 负责功能测试、性能测试、安全测试，保证 Agent 的稳定性和可靠性 |
| 行业专家（金融） | 业务部门 | 30% | 0% | 参与需求调研、风险规则制定、模型评估，提供专业知识，支持模型优化，同时提供行业支持 |

- **说明：**
  - 上述人员配置为初期配置，可根据项目进展进行调整。
  - AI 算法工程师和数据工程师可同时支持其他 AI 产品的开发，工作量占比根据实际情况调整。
  - 行业专家为兼职，提供行业知识和经验支持。

**2. 机器资源**

- **训练环境：**
  - **需求：** 2-3 台 8 卡 GPU 服务器（例如 NVIDIA A100 或同等算力的国产 GPU 卡）
  - **用途：**
    - **模型训练：** 自研基模的预训练和微调，以及风险分析模型、决策分析模型的训练。
    - **数据处理：** 大规模文本数据预处理、特征提取、数据清洗、向量化等。
- **推理环境：**
  - **需求：** 1-2 台 4 卡 GPU 服务器（例如 NVIDIA T4 或同等算力的国产 GPU 卡），或云端 GPU 实例
  - **用途：**
    - **模型推理：** 部署训练好的模型，进行在线风险分析、决策分析、报告生成等推理任务。
- **其他资源：**

  - **存储空间：** 至少 100TB 的存储空间，用于存储知识库数据、模型文件、日志数据等。
  - **网络带宽：** 足够带宽，保障数据传输和模型调用效率。

- **说明：**
  - 服务器资源建议申请武清训练池机器资源。
  - 可根据项目实际需求进行资源调整。
  - 根据使用情况，可考虑云端资源和本地资源的混合部署。

**七、问题和风险**

**1. 数据质量问题**

- **问题：**
  - 数据来源多样，数据格式不统一，需要大量清洗工作。
  - 社交媒体数据噪声大，可能包含虚假信息和恶意攻击。
  - 数据存在缺失、错误，可能影响分析结果的准确性。
- **风险：**
  - 分析结果偏差，导致误判或错判。
  - 模型训练效果不佳，无法达到预期性能。
- **应对：**
  - 建立完善的数据清洗流程，采用自动化工具和人工审核相结合的方式。
  - 引入数据质量评估机制，定期检测数据质量。
  - 采用多源数据融合策略，提高数据可靠性。

**2. 模型准确性问题**

- **问题：**
  - 风险事件具有复杂性和多样性，模型难以覆盖所有情况。
  - 中文语境复杂，情感分析准确性有待提高。
  - 新出现的风险类型，模型可能无法识别。
- **风险：**
  - 风险识别不准确，导致未能及时预警或采取有效措施。
  - 模型长期运行可能出现性能下降。
- **应对：**
  - 定期评估模型性能，进行模型优化或重训练。
  - 引入人工审核机制，对模型预测结果进行校对和验证。
  - 持续学习和更新风险知识，并将其融入模型训练中。

**3. Agent 协同问题**

- **问题：**
  - 多 Agent 之间的数据传递和协同可能存在延迟或错误。
  - Agent 的耦合度过高，可能会影响系统的灵活性和可扩展性。
  - Agent 之间的任务分配和调度可能存在冲突。
- **风险：**
  - 系统整体性能下降，无法高效完成任务。
  - 系统维护和升级困难。
  - Agent 之间的协同效率降低，影响整体风险分析效率。
- **应对：**
  - 采用灵活的 Agent 编排机制，降低 Agent 之间的耦合度。
  - 引入任务调度系统，优化 Agent 之间的协同效率。
  - 建立完善的错误处理机制，确保系统稳定运行。

**4. 知识库维护问题**

- **问题：**
  - 知识库内容庞大，维护成本高。
  - 知识更新不及时，导致分析结果过时。
  - 知识管理不规范，可能导致信息混乱。
- **风险：**
  - 分析结果的准确性和时效性降低。
  - 知识库使用效率降低。
- **应对：**
  - 建立完善的知识库管理流程，定期更新和维护知识库。
  - 采用自动化知识提取和更新工具，减少人工维护成本。
  - 引入知识库权限管理机制，确保知识安全性和一致性。

**5. 合规风险**

- **问题：**
  - 数据采集和处理可能涉及隐私信息，存在合规风险。
  - 风险分析结果可能被滥用，造成损失。
  - 报告内容可能存在错误或误导，导致合规问题。
- **风险：**
  - 企业遭受法律处罚，导致声誉受损。
  - 用户隐私泄露，引发信任危机。
- **应对：**
  - 严格遵守数据隐私法规，建立数据安全保护措施。
  - 建立完善的审计机制，定期审查报告内容。
  - 设立合规部门，负责合规风险管理。

**6. 技术风险**

- **问题：**
  - 技术迭代速度快，可能需要不断更新和升级系统。
  - 依赖第三方工具或平台，存在技术风险。
  - 模型训练效果可能不理想，需要重新设计模型结构。
- **风险：**
  - 项目进度延迟，导致成本增加。
  - 技术依赖风险，可能影响系统稳定性。
  - 模型性能不达标，影响产品竞争力。
- **应对：**
  - 采用模块化设计，提高系统的可维护性和可扩展性。
  - 选择成熟可靠的技术方案，降低技术风险。
  - 建立完善的技术支持团队，及时解决技术问题。

**总结**

以上是对产品规划的补充完善，涵盖了知识库、人员配置、资源需求以及潜在的问题和风险。希望这些信息能帮助您更好地进行产品规划和风险管理。请根据实际情况进行调整，祝您的产品顺利落地！

```mermaid
graph LR
    A[开始] --> B(信息收集 - 探灯接口);
    B --> C{数据清洗};
    C --> D(风险分析);
    D --> E(决策分析 - 规则引擎/大模型);
    E --> F(报告生成 - 模板引擎);
    F --> G[结束];

style A fill:#f9f,stroke:#333,stroke-width:2px
style G fill:#f9f,stroke:#333,stroke-width:2px


```

```mermaid
graph LR
    A[开始] --> B(信息收集Agent);
    B --> C{数据预处理};
    C --> D(风险分析Agent);
    D --> E(决策分析Agent);
    E --> F{审计与合规Agent};
    F --> G(报告生成Agent);
   G --> H[结束];

style A fill:#f9f,stroke:#333,stroke-width:2px
style H fill:#f9f,stroke:#333,stroke-width:2px

```

```mermaid
graph LR
    A[开始] --> B(信息收集Agent);
    B -- "抓取 & 初筛" --> C{数据预处理};
    C -- "清洗 & 去重" --> D(风险分析Agent);
    D -- "情感分析 & 事件提取" --> E(决策分析Agent);
    E -- "综合分析 & 建议生成" --> F(审计与合规Agent);
    F -- "合规审查 & 风险评估" --> G(报告生成Agent);
    G -- "生成结构化报告" --> H[结束];

subgraph 信息收集
B
end

subgraph 风险分析
D
end

subgraph 决策分析
E
end

 subgraph 审计与合规
F
end
 subgraph 报告生成
G
end
style A fill:#f9f,stroke:#333,stroke-width:2px
style H fill:#f9f,stroke:#333,stroke-width:2px
```

```mermaid
graph LR
    A[开始] --> B(信息收集Agent);
    B -- "抓取 & 初筛" --> C{数据预处理};
     C -- "清洗 & 去重" --> D(风险分析Agent);
    D -- "情感分析 & 事件提取" --> E{风险级别判断};
    E -- "高风险" --> F(决策分析Agent);
    E -- "中/低风险" --> H{审计与合规Agent};

    F -- "综合分析 & 建议生成" --> H;
     H -- "合规审查 & 风险评估" --> I{报告生成Agent};
    I -- "生成结构化报告" --> J{报告质量评估};
    J -- "报告质量合格" -->K[结束];
    J -- "报告质量不合格" -->L[调整报告生成];
     L --> I;

subgraph 信息收集
B
end

subgraph 风险分析
D
end

subgraph 决策分析
F
end

 subgraph 审计与合规
H
end
 subgraph 报告生成
I
end

style A fill:#f9f,stroke:#333,stroke-width:2px
style K fill:#f9f,stroke:#333,stroke-width:2px
```

```
pip install langgraph

```

```

```


```mermaid
sequenceDiagram
    participant U1 as 业务人员
    participant AIP as AI 能力平台
    participant AUTH as 统一认证
    participant ADP as 大模型应用开发平台
    participant MMP as 大模型管理平台 (Xinference)
    participant LLB as 大模型底座 (Models)
    participant KB as 知识库 (VectorDB/ES @ADP)

    U1->>+AIP: 访问平台 / 请求使用 Agent
    AIP->>+AUTH: 请求用户认证
    AUTH-->>-AIP: 返回认证成功/用户信息
    AIP->>AIP: 验证用户权限

    Note right of AIP: 可选：AIP 查询 ADP 获取 Agent 列表

    AIP->>+ADP: 调用 Agent 执行 API (携带用户输入)
    ADP->>+MMP: 请求 LLM/Embedding 模型
    MMP->>+LLB: 加载/调用模型
    LLB-->>-MMP: 返回模型结果/向量
    MMP-->>-ADP: 返回初步模型响应 / 向量

    alt Agent 需要知识库 (RAG)
        ADP->>+KB: 查询相关文档块 (基于向量)
        KB-->>-ADP: 返回上下文
        ADP->>+MMP: 携带上下文再次请求模型生成答案
        MMP->>+LLB: 再次调用模型
        LLB-->>-MMP: 返回模型答案
        MMP-->>-ADP: 返回最终答案
    end

    ADP-->>-AIP: 返回 Agent 执行结果
    AIP-->>-U1: 显示 Agent 处理结果


```



docker build -t registry.cn-beijing.aliyuncs.com/wiseweb/wiseagent:v20250610_v1.0.1 -f Dockerfile .

docker build -t swr.cn-north-4.myhuaweicloud.com/wiseweb/wiseagent:v20250610_v1.0.2 -f Dockerfile .

swr.cn-north-4.myhuaweicloud.com/{组织名称}/{镜像名称}:{版本名称}


docker save -o wiseagent_v20250610_v1.tar registry.cn-beijing.aliyuncs.com/wiseweb/wiseagent:v20250610_v1.0.1



git push swr.cn-north-4.myhuaweicloud.com/wiseweb/wiseagent:v20250610_v1.0.2