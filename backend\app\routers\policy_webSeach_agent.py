import sys

import os

# 将项目根目录添加到系统路径

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from fastapi import APIRouter, HTTPException, Depends, Query, File, UploadFile, Form, Body
from typing import AsyncGenerator,List, Optional, Dict, Any
from ..models.consumerProtectionFile import (ConsumerProtectionFileResponse, ConsumerProtectionFileUploadRequest)
from ..models.source_files import SourceFileBase
from ..models.chat import ChatResponse
from fastapi.responses import StreamingResponse
from ..utils.llmClient import convert_messages
import asyncio 
from ..models.llm import LLMModel, Provider
from ..db.mongodb import db
from ..utils.auth import verify_token
from bson import ObjectId
from datetime import datetime
import os
from ..models.system_app_setting import SystemAppSettingModel
from werkzeug.utils import secure_filename as _secure_filename
from ..utils.llmClient import stream_openai_api, stream_jiutian_api,openai_api
from pathlib import Path
from pydantic import BaseModel
from app.utils.auth import verify_token
from app.utils.logging_config import setup_logging, get_logger
import traceback # 异常打印
from urllib.parse import quote
from app.engines.embedding.embedding_utils import get_embedding
import json
import requests
from ..models.message import Message
from ..models.system_app_setting import SystemAppSetting
from elasticsearch import Elasticsearch

setup_logging()
logger = get_logger(__name__)
router = APIRouter(
    prefix="/api",
    tags=["financial_webSeach_agent"]
)

# 数据查询请求模型
class DataQueryRequest(BaseModel):
    app_info:str
    query: str
    messages: List[Dict[str, Any]]
    # 加一个默认的相似度阈值
    threshold: float = 0.85
    # filters: Optional[Dict[str, Any]] = None
    # page: int = 1
    # page_size: int = 10
    # sort_by: Optional[str] = None
    # sort_order: Optional[str] = "asc"

# 数据查询响应模型
class DataQueryResponse(BaseModel):
    success: bool
    message: str
    data: List[Dict[str, Any]] = []
    total: int = 0

# LLM数据分析请求模型
class DataAnalysisRequest(BaseModel):
    messages: List[Dict[str, Any]]
    additional_context: Optional[Dict[str, Any]] = None

# LLM数据分析响应模型
class DataAnalysisResponse(BaseModel):
    success: bool
    message: str
    result: Dict[str, Any] = {}
    visualization_data: Optional[Dict[str, Any]] = None

# 评估请求模型
class EvaluateRequest(BaseModel):
    file_id: str
    evaluation_type: str
    chunk_content: str

# 评估响应模型
class EvaluateResponse(BaseModel):
    success: bool
    message: str
    data: Dict[str, Any] = {}

# 对话总结请求模型
class DialogSummaryRequest(BaseModel):
    app_info: str
    history_messages: List[Dict[str, Any]]
    references: List[Dict[str, Any]] = []
    user_id: Optional[str] = None
    summary_type: Optional[str] = "general"  # 可以是general、focused等不同类型的总结

# 对话总结响应模型
class DialogSummaryResponse(BaseModel):
    success: bool
    message: str
    summary: str

# 报告生成请求模型
class ReportGenerationRequest(BaseModel):
    app_info: str
    messages: List[Dict[str, Any]] = []
    references: List[Dict[str, Any]] = []
    
def process_string_array(value):
        """
        测试数值是不是字符串数组，如果是则返回，如果不是则将其转成字符串数组。
        
        处理的情况包括:
        1. JSON格式的字符串: ```json\n[\n    "美国",\n    "中国"]\n```
        2. Python列表字符串: "['美国', '中国']"
        3. 已经是列表的情况: ['美国', '中国']
        4. 嵌套列表的情况: ['['特朗普', '萨尔兹曼']']
        5. 多元素嵌套列表的情况: ['['特朗普', '拜登', 'Niklas Höhne', 'Christoph Bals', 'Javier Milei']']
        
        Args:
            value: 需要处理的值
            
        Returns:
            list: 处理后的字符串数组
        """
        # 如果已经是列表，直接返回
        if isinstance(value, list):
            # 处理嵌套列表的情况，如 ['['特朗普', '萨尔兹曼']'] 或 ['['特朗普', '拜登', 'Niklas Höhne', 'Christoph Bals', 'Javier Milei']']
            if len(value) == 1 and isinstance(value[0], str) and value[0].startswith('[') and value[0].endswith(']'):
                try:
                    import ast
                    return ast.literal_eval(value[0])
                except:
                    logger.error(f"解析嵌套列表字符串失败: {value}")
                    return value
            return value
            
        # 如果是None或空字符串，返回空列表
        if not value:
            return []
            
        # 尝试处理JSON格式的字符串
        if isinstance(value, str) and '```json' in value:
            try:
                # 提取JSON部分
                json_str = value.split('```json')[1].split('```')[0].strip()
                return json.loads(json_str)
            except:
                logger.error(f"解析JSON字符串失败: {value}")
                
        # 尝试处理Python列表字符串
        if isinstance(value, str) and value.startswith('[') and value.endswith(']'):
            try:
                # 使用ast.literal_eval安全地解析Python字面量
                import ast
                return ast.literal_eval(value)
            except:
                logger.error(f"解析Python列表字符串失败: {value}")
                
        # 如果是普通字符串，将其作为单个元素的列表返回
        if isinstance(value, str):
            return [value]
            
        # 其他情况返回空列表
        return []


@router.post("/policy/data-query", response_model=DataQueryResponse)
async def query_data(
    request: DataQueryRequest,
    current_user: dict = Depends(verify_token)
    
):
    """
    基于向量相似度的ES检索接口
    """
    try:
        logger.info(f"数据查询: {request.query}")
        app_info = request.app_info
        query = request.query
        messages = request.messages
        if not query or query == "":
            raise HTTPException(status_code=404, detail="查询内容不能为空")
        if not app_info or app_info == "":
            raise HTTPException(status_code=404, detail="应用信息不能为空")

        app_info_obj = await db["system_app_settings"].find_one({"app_info": app_info})
        if not app_info_obj:
            raise HTTPException(status_code=404, detail="应用信息不存在")
        app_params = app_info_obj.get('params',None)
        if not app_params:  
            raise HTTPException(status_code=404, detail="应用参数不存在")

        logger.info(f"app_params: {app_params}")
        

        embedding_model_id = app_params.get('EMBEDDING_MODEL_ID',None)
        if not embedding_model_id:
            raise HTTPException(status_code=404, detail="Embedding模型ID不存在") 
        
        embedding_model_obj = await db["embeddings"].find_one({"id": int(embedding_model_id)})
        if not embedding_model_obj:
            raise HTTPException(status_code=404, detail="Embedding模型不存在")


        # 2. 调用ES进行向量检索
        es_host = app_params.get('ES_HOST', '')
        es_port = app_params.get('ES_PORT', '9200')
        index_name = app_params.get('ES_INDEX', 'wangsu_jcg_info')
        es_user = app_params.get('ES_USER', None)
        es_password = app_params.get('ES_PASSWORD', None)
        model_id = app_params.get('MODEL_ID', None)
        logger.info(f"==============>>>>>app_params: {app_params}")
        logger.info(f"==============>>>>>index_name: {index_name}")


        if messages and len(messages) > 0 and model_id:
            logger.info(f"==============>>>>>优化查询")
            # 提取用户消息
            user_messages = [msg for msg in messages if msg.get("role") == "user"]

            
            # 如果用户消息大于一条，则调用LLM生成新的查询
            if len(user_messages) > 0:
                logger.info(f"用户消息数量大于1，调用LLM生成新的查询")
                
                # 准备LLM输入
                llm_messages = [
                    {"role": "system", "content": "你是一个专业的金融声誉风险检索助手。请根据用户的对话历史，和新的提问，生成一个更精确的搜索查询语句，以便获取最相关的金融声誉风险信息。只需返回查询语句，不要有任何解释或额外文字。"},
                ]
                prompt_template = """
# 历史对话
{history_messages}

# 新的提问
{new_query}

# 请根据以上对话，生成一个简洁的搜索查询语句，用于检索与'{new_query}'相关的信息。只返回查询语句，不要有任何解释或额外文字。
                """ 
                history_messages = ""
                for msg in user_messages:
                    history_messages += f"用户：{msg.get('content', '')}\n"
                # 添加最后的指令
                llm_messages.append({
                    "role": "user", 
                    "content": prompt_template.format(history_messages=history_messages, new_query=query)
                })
                
                try:
                        
                    refined_query = await model_api(llm_messages,model_id)                    
                    # 使用生成的查询替换原始查询
                    if refined_query :
                        logger.info(f"LLM生成的查询: {refined_query}")
                        query = refined_query
                except Exception as e:
                    logger.error(f"调用LLM生成查询失败: {str(e)}")
                    # 出错时继续使用原始查询
                    pass


        if not es_host or es_host == "":
            raise HTTPException(status_code=404, detail="ES主机不存在")
        if not index_name or index_name == "":
            raise HTTPException(status_code=404, detail="ES索引不存在")
        logger.info(f"执行查询=====》: {query}")
        embedding = get_embedding(query, embedding_model_obj)
        chunks = es_vector_retriever_wjb(
            embedding,
            top_k=5,
            threshold=request.threshold or 0.55,
            es_host=es_host,
            es_port=es_port,
            index_name=index_name,
            es_user=es_user,
            es_password=es_password
        )
        
        # 3. 格式化返回结果
        results = []
        for chunk in chunks:
            results.append({
                "law_title": chunk.get("title", ""),
                "law_url": chunk.get("url", ""),
                "policy_theme": process_string_array(chunk.get("policy_theme", "")),
                "affected_industries": process_string_array(chunk.get("affected_industries", "")),
                "policy_impact": {
                    "regulatory_impact": chunk.get("regulatory_impact", ""),
                    "compliance_requirements": chunk.get("compliance_requirements", ""),
                    "risk_level": chunk.get("risk_level", "")
                },
                "policy_direction": {
                    "trend": chunk.get("trend", ""),
                    "key_points": process_string_array(chunk.get("key_points", "")),
                    "implementation_timeline": chunk.get("implementation_timeline", "")
                },
                "market_impact": {
                    "industry_effect": chunk.get("industry_effect", ""),
                    "business_opportunities": chunk.get("business_opportunities", ""),
                    "market_risk": chunk.get("market_risk", "")
                },
                "area": process_string_array(chunk.get("area", "")),
                "summary": chunk.get("summary", ""),
                "score": chunk.get("score", 0.0)
            })

        return DataQueryResponse(
            success=True,
            message="查询成功",
            total=len(results),
            data=results,
        )
        
    except Exception as e:
        logger.error(f"查询数据失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"查询数据失败: {str(e)}")

    
# 创建新对话
@router.post("/policy/data-analysis", response_model=Dict[str, Any])
async def chat_system_app_completions(conversation: ChatResponse, current_user: dict = Depends(verify_token)):
    # print(conversation)
    logger.debug( conversation)
    
    system_app_info: SystemAppSettingModel = await db["system_app_settings"].find_one({"app_info": conversation.app_info})
    if not system_app_info:
        logger.error(f"没有找到应用信息: {conversation.app_info}")
        raise HTTPException(status_code=404, detail="没有找到应用信息")
    # 提取最后一条消息
    last_message = conversation.messages[-1]
    logger.debug(f"last_message========>: {last_message.get('references',[])}")



    new_user_message = Message(
        _id=str(ObjectId()),
        conversation_id=conversation.conversation_id,
        message_id=last_message['id'],
        meta_data=last_message.get('meta', {}),
        extra=last_message.get('extra', {}),
        user_id=conversation.user_id,
        user_name=conversation.user_name,
        role=last_message['role'],
        content=last_message['content'],
        created_at=datetime.now(),
        app_info=conversation.app_info,
        references=last_message.get('references',[]),
        token_count=0,  # 这里可以添加计算token的逻辑
        price=0.0  # 这里可以添加计算价格的逻辑
    )


    # 将 MongoEngine 实例转换为字典
    new_user_message_dict = new_user_message.to_mongo().to_dict()
    await db["messages"].insert_one(new_user_message_dict)
    references = []
    for reference in last_message.get('references',[]):
        references.append(f"标题：{reference.get('title', '')}\n内容：{reference.get('contentSummary', '')}\n作者观点：{reference.get('authorViewpoint', '')}")

    prompt_template = """
我需要你根据提供的参考资料，从政策分析角度回答用户的问题。请遵循以下要求：
1. 仅使用提供的参考资料中的信息来分析和解读政策
2. 如果参考资料中没有相关政策信息，请明确告知用户
3. 回答要全面、准确，分析政策的背景、内容、影响和实施情况
4. 引用政策文件或解读时，请标明具体来源
5. 保持客观、专业的语气，避免主观评价
6. 必要时可对政策实施的可能效果和影响进行分析预测

# 参考信息
{references}
# 用户问题: 
{user_message}
"""


    conversation.messages[-1]['content'] = prompt_template.format(references=references, user_message=last_message['content'])
    logger.info(f"conversation.messages[-1]['content']========>: {conversation.messages[-1]['content']}")
    async def stream_response():
        ai_message_content = ""
        async for chunk in stream_model_api(
            chat_id=conversation.conversation_id,
            user_id=conversation.user_id,
            messages=conversation.messages,
            system_app_info=system_app_info
        ):
            ai_message_content += chunk
            yield chunk
            await asyncio.sleep(0)

        # 添加 end 事件类型
        yield "event: end\ndata: Stream has ended\n\n"

    return StreamingResponse(stream_response(), media_type='text/event-stream')






async def stream_model_api(
    chat_id: str,
    user_id: str,
    messages: List[Dict[str, Any]],
    system_app_info: SystemAppSetting
) -> AsyncGenerator[str, None]:
    """
    统一的模型流式调用接口，支持 WISERAG、LLM 和 KNOWLEDGE_QA
    """
    logger.info('========stream_model_api')
    logger.info(system_app_info)

    if not system_app_info:
        raise ValueError("System app setting not found")
    
    try:
        messages_list = convert_messages(messages)
        system_app_params = system_app_info.get('params', {})
        
        # 处理 prompt template
        if prompt_template := system_app_params.get('prompt_template', None):
            new_content = prompt_template.replace('{user_message}', messages_list[-1]['content'])
            messages_list[-1]['content'] = new_content

        # app_type = system_app_info["type"]
        
    
        llm: LLMModel = await db["llms"].find_one({"id": int(system_app_params.get('MODEL_ID', None))})
        if not llm:
            raise ValueError("LLM model not found")
            

        logger.info(system_app_info.get('params', {}))
        # 拼接提示词
            # 获取用户的最新消息内容
        user_query = messages_list[-1]['content']
        logger.info(f"用户查询: {user_query}")




        model = llm.get('m_name', None)
        provider = llm.get('provider', Provider.OPENAI)


        # 根据不同的 provider 使用对应的 API 调用方法
        if provider == Provider.DEEPSEEK:
            logger.info('调用 DEEPSEEK')
            stream_api = stream_openai_api
        elif provider == Provider.DOUBAO:
            logger.info('调用 DOUBAO')
            stream_api = stream_openai_api
        elif provider == Provider.LOCAL:
            logger.info('调用 LOCAL')
            stream_api = stream_openai_api
        elif provider == Provider.OLLAMA:
            logger.info('调用 OLLAMA')
            stream_api = stream_openai_api
        elif provider == Provider.JIUTIAN:  # 添加九天模型的处理
            logger.info('调用 JIUTIAN')
            stream_api = stream_jiutian_api
        else:
            logger.info('调用 OpenAI')
            stream_api = stream_openai_api

        

            # event: moduleStatus
            # data: {"status":"running","name":"涉黄涉政识别"}
        system_prompt = '你是一个中国外交AI信息处理机器人。擅长理解用户的信息，并从中国的外交角度给出最合适的回答。'
            
        async for chunk in stream_api(
            llm["api_key"],
            model,
            messages_list,
            llm["service_url"],
            extra=system_app_info.get('extra', None),
            system_prompt=system_prompt,
            provider=provider
        ):
            yield f"event: answer\n{chunk}"

        yield "event: end\ndata: Stream has ended\n\n"
        
    except Exception as e:
        logger.error(f"流式调用失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"评估失败: {str(e)}")
        # yield f"event: error\ndata: {str(e)}\n\n"



async def model_api(
    messages: List[Dict[str, Any]],
    llm_id: int
) -> AsyncGenerator[str, None]:
    """
    统一的模型流式调用接口，支持 WISERAG、LLM 和 KNOWLEDGE_QA
    """
    logger.info('========model_api')
    logger.info(llm_id)


    
    try:
        messages_list = convert_messages(messages)
    
        # app_type = system_app_info["type"]
        
    
        llm: LLMModel = await db["llms"].find_one({"id": int(llm_id)})
        if not llm:
            raise ValueError("LLM model not found")
        
        model = llm.get('m_name', None)
        provider = llm.get('provider', Provider.OPENAI)


        # 根据不同的 provider 使用对应的 API 调用方法
        if provider == Provider.DEEPSEEK:
            logger.info('调用 DEEPSEEK')
            model_api = openai_api
        elif provider == Provider.DOUBAO:
            logger.info('调用 DOUBAO')
            model_api = openai_api
        elif provider == Provider.LOCAL:
            logger.info('调用 LOCAL')
            model_api = openai_api
        elif provider == Provider.OLLAMA:
            logger.info('调用 OLLAMA')
            model_api = openai_api
        elif provider == Provider.JIUTIAN:  # 添加九天模型的处理
            logger.info('调用 JIUTIAN')
            model_api = openai_api
        else:
            logger.info('调用 OpenAI')
            model_api = openai_api
            
        response = await model_api(
                    llm["api_key"],
                    model,
                    messages_list,
                    llm["service_url"],
                    extra=None,  # 使用验证后的参数
                    system_prompt=None,
                    provider=provider  # 添加 provider 参数
                )
        if response:
            return response.get('choices', [{}])[0].get('message', {}).get('content', '')
        else:
            return None
        
    except Exception as e:
        logger.error(f"流式调用失败: {str(e)}")
        logger.error(traceback.format_exc())
        # raise HTTPException(status_code=500, detail=f"评估失败: {str(e)}")
        return None
        # yield f"event: error\ndata: {str(e)}\n\n"




# 外交部向量检索
def es_vector_retriever_wjb(
    query_embedding: List[float], 
    top_k: int = 5, 
    threshold: float = 0.6,
    es_host: str = None, 
    es_port: str = "9200",
    index_name: str = "wangsu_jcg_info_trump",
    es_user: str = None,
    es_password: str = None
) -> List[Dict[str, Any]]:
    """
    基于ES客户端的向量检索
    
    Args:
        query_embedding: 查询向量
        chunk_type: 块类型
        kb_id: 知识库ID
        top_k: 返回结果数量
        threshold: 相似度阈值
        es_host: ES主机配置（可选）
        index_name: 索引名称
    
    Returns:
        List[Dict[str, Any]]: 检索结果列表
    """

    logger.info(f"=========================ip: {es_host}")
    logger.info(f"=========================port: {es_port}")
    logger.info(f"=========================user: {es_user}")
    logger.info(f"=========================password: {es_password}")
    try:
        # 构建查询DSL
        search_query = {
            "track_total_hits": True,
            "query": {
                "script_score": {
                    "query": {
                        "bool": {
                            "must": [
                                {
                                    "exists": {
                                        "field": "embedding"
                                    }
                                }
                            ]
                        }
                    },
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                        "params": {"query_vector": query_embedding}
                    }
                }
            },
            "size": top_k,
            "_source": [
                "law_title",
                "law_url", 
                "policy_theme",
                "affected_industries",
                "policy_impact",
                "policy_direction",
                "market_impact",
                "area",
                "summary"
            ],
            "sort": [
                {"_score": {"order": "desc"}}
            ]
        }

 
        es = Elasticsearch(
            [f"{es_host}:{es_port}"],
            http_auth=(es_user, es_password) if es_user else None,
            timeout=60  # 设置客户端级别的超时时间（秒）
        )
        
            
        # 检查连接
        if es.ping():
            logger.info(f"ES连接成功: {es_host}:{es_port}")
            es_info = es.info()
            logger.info(f"ES版本: {es_info['version']['number']}")
        else:
            logger.error(f"ES连接失败: {es_host}:{es_port}")
            raise Exception(f"ES连接失败: {es_host}:{es_port}")

        logger.info(f'检索=============》{index_name}')
        response = es.search(index=index_name, body=search_query)
        results = []
        hits = response.get("hits", {}).get("hits", [])
        # 在处理结果之前添加日志
        # logger.info(f"ES响应数据: {response}")
        logger.info(f"hits总数: {len(hits)}")

        for hit in hits:
            # 打印原始分数
            original_score = hit["_score"]
            logger.info(f"原始分数: {original_score}")
            
            score = (hit["_score"] - 1.0)  # 恢复原始相似度分数
            # logger.info(f"计算后的分数: {score}")
            
            if score < threshold:
                logger.info(f"分数 {score} 小于阈值 {threshold}，跳过")
                continue
        
            source = hit["_source"]
            # logger.info(f"文档源数据: {source}")
            logger.info(f"文档源数据: {source}")
            
            result = {
                "title": source.get("law_title", ""),
                "url": source.get("law_url", ""), 
                "policy_theme": source.get("policy_theme", ""),
                "affected_industries": source.get("affected_industries", ""),
                "policy_impact": source.get("policy_impact", ""),
                "policy_direction": source.get("policy_direction", ""),
                "market_impact": source.get("market_impact", ""),
                "area": source.get("area", ""),
                "summary": source.get("summary", ""),
                "score": score
            }
            results.append(result)
            # logger.info(f"添加结果: {result}")

        logger.info(f"最终结果数量: {len(results)}")
        return results[:top_k]
    
        
    except Exception as e:
        logger.error(f"向量检索失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise Exception(f"向量检索失败: {str(e)}")
    finally:
        if es:
            es.close()


@router.post("/policy/data-report")
async def generate_report(
    request: ReportGenerationRequest,
):
    """
    基于历史消息和引用资料生成专业报告，流式输出
    """
    try:
        logger.info(f"生成报告请求: {request.app_info}")
        app_info = request.app_info
        messages = request.messages
        references = request.references
   
        
        if not app_info or app_info == "":
            raise HTTPException(status_code=404, detail="应用信息不能为空")

        # 获取应用信息
        app_info_obj = await db["system_app_settings"].find_one({"app_info": app_info})
        if not app_info_obj:
            raise HTTPException(status_code=404, detail="应用信息不存在")
        else:
            logger.info(f"应用信息: {app_info_obj}")
        app_params = app_info_obj.get('params', None)
        if not app_params:  
            raise HTTPException(status_code=404, detail="应用参数不存在")

        # 获取LLM模型ID
        model_id = app_params.get('MODEL_ID', None)
        if not model_id:
            raise HTTPException(status_code=404, detail="模型ID不存在")
        
        # 准备引用信息文本
        references_text = ""
        if references and len(references) > 0:
            for i, reference in enumerate(references):
                references_text += f"参考资料{i+1}：\n"
                references_text += f"标题：{reference.get('title', '')}\n"
                references_text += f"内容：{reference.get('contentSummary', '')}\n"
                if reference.get('authorViewpoint'):
                    references_text += f"作者观点：{reference.get('authorViewpoint', '')}\n"
                if reference.get('authorAttitude'):
                    references_text += f"作者态度：{reference.get('authorAttitude', '')}\n"
                if reference.get('eventInfo'):
                    event_info = process_string_array(reference.get('eventInfo', ''))
                    if event_info:
                        references_text += f"相关事件：{', '.join(event_info)}\n"
                if reference.get('characterEntity'):
                    character_entities = process_string_array(reference.get('characterEntity', ''))
                    if character_entities:
                        references_text += f"相关人物：{', '.join(character_entities)}\n"
                if reference.get('institutionalEntities'):
                    institutional_entities = process_string_array(reference.get('institutionalEntities', ''))
                    if institutional_entities:
                        references_text += f"相关机构：{', '.join(institutional_entities)}\n"
                # if reference.get('locationEntity'):
                #     location_entities = process_string_array(reference.get('locationEntity', ''))
                #     if location_entities:
                #         references_text += f"相关地点：{', '.join(location_entities)}\n"
                references_text += "\n"
        
        # 准备历史对话文本
        history_text = ""
        if messages and len(messages) > 0:
            for msg in messages:
                role = "用户" if msg.get("role") == "user" else "系统"
                history_text += f"{role}：{msg.get('content', '')}\n"
        
        # 构建提示词
        report_prompt = """
你是一个专业的政策分析师。请根据提供的对话历史和参考资料，生成一份全面、专业的政策分析报告。请遵循以下要求：

1. 报告需包含政策背景、政策内容解读、影响分析和未来展望四部分
2. 分析应从用户关注的政策问题入手，保持客观、专业的语气
3. 充分利用参考资料中的信息，分析政策出台的背景、主要内容和可能产生的影响
4. 对政策的不同解读和观点要平衡呈现，必要时指出各方立场
5. 结合中国国情和发展战略，对政策实施的可能效果进行评估

# 对话历史
{history}

# 参考资料
{references}

请生成一份专业的外交信息分析报告：
"""
        
        # 准备提示词
        prompt = report_prompt.format(
            history=history_text if history_text else "无历史对话",
            references=references_text if references_text else "无参考资料"
        )
        
        # 准备LLM输入
        llm_messages = [
            {"role": "system", "content": "你是一个专业的外交信息分析师，擅长撰写分析报告。"},
            {"role": "user", "content": prompt}
        ]
        
        # 获取LLM模型信息
        llm: LLMModel = await db["llms"].find_one({"id": int(model_id)})
        if not llm:
            raise HTTPException(status_code=404, detail="LLM模型不存在")
        
        model = llm.get('m_name', None)
        provider = llm.get('provider', Provider.OPENAI)
        
        # 选择合适的流式API
        if provider == Provider.DEEPSEEK:
            stream_api = stream_openai_api
        elif provider == Provider.DOUBAO:
            stream_api = stream_openai_api
        elif provider == Provider.LOCAL:
            stream_api = stream_openai_api
        elif provider == Provider.OLLAMA:
            stream_api = stream_openai_api
        elif provider == Provider.JIUTIAN:
            stream_api = stream_jiutian_api
        else:
            stream_api = stream_openai_api

        # 创建流式响应
        async def stream_response():
            async for chunk in stream_api(
                llm["api_key"],
                model,
                llm_messages,
                llm["service_url"],
                extra=app_info_obj.get('extra', None),
                system_prompt=None,
                provider=provider
            ):
                yield f"event: answer\n{chunk}"
                await asyncio.sleep(0)
            
            # 添加结束事件
            yield "event: end\ndata: Stream has ended\n\n"

        return StreamingResponse(stream_response(), media_type='text/event-stream')
        
    except Exception as e:
        logger.error(f"生成报告失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"生成报告失败: {str(e)}")




@router.post("/policy/generate-summary", response_model=DialogSummaryResponse)
async def generate_dialog_summary(
    request: DialogSummaryRequest,
):
    """
    基于历史信息和引用信息生成对话总结
    """
    try:
        logger.info(f"生成对话总结请求: {request.app_info}")
        app_info = request.app_info
        history_messages = request.history_messages
        references = request.references
        
        if not app_info or app_info == "":
            raise HTTPException(status_code=404, detail="应用信息不能为空")
        if not history_messages or len(history_messages) == 0:
            raise HTTPException(status_code=404, detail="历史消息不能为空")

        # 获取应用信息
        app_info_obj = await db["system_app_settings"].find_one({"app_info": app_info})
        if not app_info_obj:
            raise HTTPException(status_code=404, detail="应用信息不存在")
        app_params = app_info_obj.get('params', None)
        if not app_params:  
            raise HTTPException(status_code=404, detail="应用参数不存在")

        # 获取LLM模型ID
        model_id = app_params.get('MODEL_ID', None)
        if not model_id:
            raise HTTPException(status_code=404, detail="模型ID不存在")
        
        # 准备引用信息文本
        references_text = ""
        if references and len(references) > 0:
            for i, reference in enumerate(references):
                references_text += f"参考资料{i+1}：\n"
                references_text += f"标题：{reference.get('title', '')}\n"
                references_text += f"内容：{reference.get('contentSummary', '')}\n"
                if reference.get('authorViewpoint'):
                    references_text += f"作者观点：{reference.get('authorViewpoint', '')}\n"
                references_text += "\n"
        
        # 准备历史对话文本
        history_text = ""
        for msg in history_messages:
            role = "用户" if msg.get("role") == "user" else "系统"
            history_text += f"{role}：{msg.get('content', '')}\n"
        
        # 构建提示词
        summary_prompt = """
你是一个专业的外交对话总结助手。请根据提供的对话历史和参考资料，生成一个全面、准确的对话总结。请遵循以下要求：

1. 总结应该简洁明了，突出关键信息和观点
2. 从中国外交立场出发，保持客观、专业的语气
3. 提取对话中的主要问题和解答要点
4. 如果引用了参考资料，请在总结中体现出来

# 对话历史
{history}

# 参考资料
{references}

请生成一份不超过300字的对话总结：
"""
        
        # 准备提示词
        prompt = summary_prompt.format(
            history=history_text,
            references=references_text if references_text else "无"
        )
        
        # 准备LLM输入
        llm_messages = [
            {"role": "system", "content": "你是一个专业的外交对话总结助手。"},
            {"role": "user", "content": prompt}
        ]
        
        # 调用LLM生成总结
        summary = await model_api(llm_messages, model_id)
        
        if not summary:
            return DialogSummaryResponse(
                success=False,
                message="生成总结失败",
                summary=""
            )
        
        return DialogSummaryResponse(
            success=True,
            message="生成总结成功",
            summary=summary
        )
        
    except Exception as e:
        logger.error(f"生成对话总结失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"生成对话总结失败: {str(e)}")



