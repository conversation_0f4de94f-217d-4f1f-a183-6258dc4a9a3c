{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import random\n", "from datetime import datetime, timedelta\n", "from bson import ObjectId\n", "import pymongo"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["client = pymongo.MongoClient(\"mongodb://memInterview:<EMAIL>:37017/roardataAiApp_test?authSource=admin\")\n", "db = client[\"roardataAiApp_test\"]\n", "collection = db[\"structured_datasets\"]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["base_data = [\n", "       {\n", "        \"name\": \"小学语文试题数据集\",\n", "        \"description\": \"包含小学语文试题数据，题型覆盖问答题、单项选择题、判断题等。每条试题数据附带答案解析、题解过程和习题图片。\",\n", "        \"tags\": [\"小学\", \"语文\", \"试题数据\", \"答案解析\", \"习题图片\"],\n", "        \"row_count\": 300000\n", "    },\n", "    {\n", "        \"name\": \"初中数学试题数据集\",\n", "        \"description\": \"包含初中数学试题数据，题型覆盖填空题、多项选择题、判断题等。每条试题数据附带答案解析、题解过程和习题图片。\",\n", "        \"tags\": [\"初中\", \"数学\", \"试题数据\", \"答案解析\", \"习题图片\"],\n", "        \"row_count\": 500000\n", "    },\n", "    {\n", "        \"name\": \"高中地理试题数据集\",\n", "        \"description\": \"包含高中地理试题数据，题型覆盖问答题、单项选择题。每条试题数据附带答案解析、题解过程和习题图片。\",\n", "        \"tags\": [\"高中\", \"地理\", \"试题数据\", \"答案解析\", \"习题图片\"],\n", "        \"row_count\": 200000\n", "    },\n", "    {\n", "        \"name\": \"高中化学试题数据集\",\n", "        \"description\": \"包含高中化学试题数据，题型覆盖填空题、判断题、多项选择题等。每条试题数据附带答案解析、题解过程和习题图片。\",\n", "        \"tags\": [\"高中\", \"化学\", \"试题数据\", \"答案解析\", \"习题图片\"],\n", "        \"row_count\": 200000\n", "    },\n", "    {\n", "        \"name\": \"初中生物教学资料数据集\",\n", "        \"description\": \"包含初中生物教学资料文章，提取了文章中的配图并独立存储，同时标识了配图在文章中的位置。每篇文章至少有1张配图。\",\n", "        \"tags\": [\"初中\", \"生物\", \"教学资料\", \"文章\", \"配图\"],\n", "        \"row_count\": 150000\n", "    },\n", "    {\n", "        \"name\": \"高中物理教学资料数据集\",\n", "        \"description\": \"包含高中物理教学资料文章，提取了文章中的配图并独立存储，同时标识了配图在文章中的位置。每篇文章至少有1张配图。\",\n", "        \"tags\": [\"高中\", \"物理\", \"教学资料\", \"文章\", \"配图\"],\n", "        \"row_count\": 150000\n", "    },\n", "    {\n", "        \"name\": \"大学计算机教学资料数据集\",\n", "        \"description\": \"包含大学计算机专业的教学资料文章，文章中的配图已提取并单独存储，且标识了配图在文章中的位置。\",\n", "        \"tags\": [\"大学\", \"计算机\", \"教学资料\", \"文章\", \"配图\"],\n", "        \"row_count\": 100000\n", "    },\n", "    {\n", "        \"name\": \"唐诗配图数据集\",\n", "        \"description\": \"包含唐朝的古诗词及其配图，配图与诗词内容主旨相符，一一对应。\",\n", "        \"tags\": [\"唐诗\", \"古诗词\", \"配图\", \"唐朝\", \"文学\"],\n", "        \"row_count\": 25000\n", "    },\n", "    {\n", "        \"name\": \"宋词配图数据集\",\n", "        \"description\": \"包含宋朝的古诗词及其配图，配图与诗词内容主旨相符，一一对应。\",\n", "        \"tags\": [\"宋词\", \"古诗词\", \"配图\", \"宋朝\", \"文学\"],\n", "        \"row_count\": 25000\n", "    },\n", "    {\n", "        \"name\": \"清代古诗词配图数据集\",\n", "        \"description\": \"包含清代的古诗词及其配图，配图与诗词内容主旨相符，一一对应。\",\n", "        \"tags\": [\"清代\", \"古诗词\", \"配图\", \"清朝\", \"文学\"],\n", "        \"row_count\": 20000\n", "    },\n", "    {\n", "        \"name\": \"地理杂志文章数据集\",\n", "        \"description\": \"包含来自优质媒体的地理主题中文杂志文章，文章配图已提取并独立存储，并标识了配图在文章中的位置。\",\n", "        \"tags\": [\"地理\", \"杂志文章\", \"配图\", \"优质媒体\"],\n", "        \"row_count\": 400000\n", "    },\n", "    {\n", "        \"name\": \"历史杂志文章数据集\",\n", "        \"description\": \"包含来自优质媒体的历史主题中文杂志文章，文章配图已提取并独立存储，并标识了配图在文章中的位置。\",\n", "        \"tags\": [\"历史\", \"杂志文章\", \"配图\", \"优质媒体\"],\n", "        \"row_count\": 400000\n", "    },\n", "    {\n", "        \"name\": \"医学杂志文章数据集\",\n", "        \"description\": \"包含来自优质媒体的医学主题中文杂志文章，文章配图已提取并独立存储，并标识了配图在文章中的位置。\",\n", "        \"tags\": [\"医学\", \"杂志文章\", \"配图\", \"优质媒体\"],\n", "        \"row_count\": 500000\n", "    },\n", "    {\n", "        \"name\": \"科学杂志文章数据集\",\n", "        \"description\": \"包含来自优质媒体的科学主题中文杂志文章，文章配图已提取并独立存储，并标识了配图在文章中的位置。\",\n", "        \"tags\": [\"科学\", \"杂志文章\", \"配图\", \"优质媒体\"],\n", "        \"row_count\": 300000\n", "    },\n", "    {\n", "        \"name\": \"上市公司财报数据集\",\n", "        \"description\": \"涵盖上市公司对外披露的财报，包括重要财务指标，如利润表、资产负债表等，涉及沪深股市主流报纸新闻、财联社、新浪财经等。\",\n", "        \"tags\": [\"财报\", \"上市公司\", \"财务数据\"],\n", "        \"row_count\": 500000\n", "    },\n", "    {\n", "        \"name\": \"财经杂志数据集\",\n", "        \"description\": \"包括有关金融、股市、债券等各类财经信息的权威报道和分析。涵盖财经杂志如《财联社》、《新财富》等。\",\n", "        \"tags\": [\"财经杂志\", \"金融\", \"市场分析\"],\n", "        \"row_count\": 300000\n", "    },\n", "    {\n", "        \"name\": \"金融产品公告数据集\",\n", "        \"description\": \"涵盖金融产品（包括IPO、债券发行等）的公告数据，涉及IPO招股说明书、发行公告等。\",\n", "        \"tags\": [\"金融产品\", \"IPO公告\", \"债券发行\"],\n", "        \"row_count\": 100000\n", "    },\n", "    {\n", "        \"name\": \"法律意见书数据集\",\n", "        \"description\": \"包括各类公司法务提供的法律意见书，涵盖IPO审核过程中的法律意见、公司股权架构调整、员工持股计划等。\",\n", "        \"tags\": [\"法律意见书\", \"IPO\", \"股权调整\"],\n", "        \"row_count\": 50000\n", "    },\n", "    {\n", "        \"name\": \"三市公告数据集\",\n", "        \"description\": \"包括上海证券交易所、深圳证券交易所和北京证券交易所的公告信息，涵盖季度报告、年度报告及临时公告等。\",\n", "        \"tags\": [\"公告\", \"三市\", \"交易所\"],\n", "        \"row_count\": 200000\n", "    },\n", "    {\n", "        \"name\": \"新三板挂牌公司公告数据集\",\n", "        \"description\": \"包含新三板挂牌公司的公告数据，涵盖中介机构报告、挂牌企业的定期和临时公告。\",\n", "        \"tags\": [\"新三板\", \"挂牌公司\", \"公告\"],\n", "        \"row_count\": 50000\n", "    },\n", "    {\n", "        \"name\": \"债券公告数据集\",\n", "        \"description\": \"涵盖国内外债券市场的公告数据，涉及债券发行、票面利率调整、公司债及国债公告。\",\n", "        \"tags\": [\"债券公告\", \"债券发行\", \"市场公告\"],\n", "        \"row_count\": 150000\n", "    },\n", "    {\n", "        \"name\": \"公募基金公告数据集\",\n", "        \"description\": \"涵盖公募基金的公告数据，包括基金发行、基金经理变更、季度报告等。\",\n", "        \"tags\": [\"公募基金\", \"基金公告\", \"基金经理\"],\n", "        \"row_count\": 80000\n", "    },\n", "    {\n", "        \"name\": \"证券行业监管信息数据集\",\n", "        \"description\": \"包括证监会发布的监管公告、交易所规定、各类行业合规信息等。\",\n", "        \"tags\": [\"证券监管\", \"证监会\", \"合规信息\"],\n", "        \"row_count\": 100000\n", "    },\n", "    {\n", "        \"name\": \"行政处罚数据集\",\n", "        \"description\": \"包括证券行业的行政处罚信息，涵盖违规操作的处罚结果、企业和个人的处罚公告。\",\n", "        \"tags\": [\"行政处罚\", \"证券违规\", \"处罚公告\"],\n", "        \"row_count\": 30000\n", "    },\n", "    {\n", "        \"name\": \"机构调研数据集\",\n", "        \"description\": \"包括券商、基金公司等金融机构对企业的调研报告，涉及行业分析、企业业绩及市场趋势预测等。\",\n", "        \"tags\": [\"机构调研\", \"行业分析\", \"企业业绩\"],\n", "        \"row_count\": 70000\n", "    },\n", "    {\n", "        \"name\": \"投资者互动问答数据集\",\n", "        \"description\": \"包括投资者与上市公司在互动平台上的问答，涵盖公司运营状况、财务状况等问题。\",\n", "        \"tags\": [\"投资者互动\", \"上市公司\", \"问答\"],\n", "        \"row_count\": 120000\n", "    },\n", "    {\n", "        \"name\": \"上市公司回复公告数据集\",\n", "        \"description\": \"包括上市公司对监管部门及交易所的问询函的回复公告，涉及公司运营及财务状况的详细回复。\",\n", "        \"tags\": [\"上市公司回复\", \"监管部门\", \"问询函\"],\n", "        \"row_count\": 60000\n", "    },\n", "    {\n", "        \"name\": \"IPO过程中的反馈问答数据集\",\n", "        \"description\": \"包括IPO审核过程中对企业提出的反馈问题及企业回复情况，涵盖上交所、深交所等的反馈问答。\",\n", "        \"tags\": [\"IPO反馈\", \"企业回复\", \"IPO审核\"],\n", "        \"row_count\": 50000\n", "    },\n", "    {\n", "        \"name\": \"债券发行反馈问答数据集\",\n", "        \"description\": \"包含债券发行过程中的反馈问题及相关回复，涵盖债券市场监管部门的反馈和企业的回复。\",\n", "        \"tags\": [\"债券发行\", \"反馈问答\", \"市场监管\"],\n", "        \"row_count\": 40000\n", "    },\n", "    {\n", "        \"name\": \"金融法规库数据集\",\n", "        \"description\": \"包括金融相关的法律法规、政策解读以及各类监管规定，涵盖金融监管机构的官方发布内容。\",\n", "        \"tags\": [\"金融法规\", \"政策解读\", \"监管规定\"],\n", "        \"row_count\": 100000\n", "    },\n", "    {\n", "        \"name\": \"证券法法规库数据集\",\n", "        \"description\": \"包括证监会、证券交易所发布的证券法及相关实施细则，涵盖行业规则、交易法规等内容。\",\n", "        \"tags\": [\"证券法\", \"行业规则\", \"交易法规\"],\n", "        \"row_count\": 90000\n", "    },\n", "    {\n", "        \"name\": \"研报库数据集\",\n", "        \"description\": \"包括金融机构发布的各类研究报告，涵盖行业研究、公司研究和市场分析，涉及证券、宏观经济等领域。\",\n", "        \"tags\": [\"研报\", \"行业研究\", \"公司研究\"],\n", "        \"row_count\": 80000\n", "    },\n", "    {\n", "        \"name\": \"政府采购招标数据集\",\n", "        \"description\": \"包括政府发布的招标采购信息，涵盖政府部门、公共机构的招标公告和招标结果。\",\n", "        \"tags\": [\"政府采购\", \"招标公告\", \"招标结果\"],\n", "        \"row_count\": 50000\n", "    },\n", "    {\n", "        \"name\": \"金融法规解读数据集\",\n", "        \"description\": \"包括金融领域相关法规、政策的专业解读，涵盖金融监管机构的官方解释和法律条文的细致分析。\",\n", "        \"tags\": [\"金融法规\", \"政策解读\", \"监管解读\"],\n", "        \"row_count\": 70000\n", "    }\n", "    ]\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# 生成随机日期函数，2024年10月之前\n", "def generate_random_date():\n", "    start_date = datetime(2023, 12, 31)\n", "    end_date = datetime(2024, 10, 1)\n", "    delta = end_date - start_date\n", "    random_days = random.randint(0, delta.days)\n", "    random_date = start_date + timedelta(days=random_days)\n", "    return random_date"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# 生成创建数据的函数\n", "def generate_datasets(base_data):\n", "   \n", "    generated_data = []\n", "    \n", "    for data in base_data:\n", "        date = generate_random_date()\n", "        dataset = {\n", "            \"_id\": ObjectId(),\n", "            \"name\": data[\"name\"],\n", "            \"description\": data[\"description\"],\n", "            \"tags\": data[\"tags\"],\n", "            \"created_at\": date,\n", "            \"last_updated\": date,\n", "            \"user_id\": 1,\n", "            \"is_active\": True,\n", "            \"user_name\": \"超级管理员\",\n", "            \"row_count\": 0,\n", "            \"processing_status\": \"completed\"\n", "        }\n", "        generated_data.append(dataset)\n", "\n", "    return generated_data"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["datasets = generate_datasets(base_data)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'_id': ObjectId('671cd8d3291a6d3d6beab144'),\n", "  'name': '小学语文试题数据集',\n", "  'description': '包含小学语文试题数据，题型覆盖问答题、单项选择题、判断题等。每条试题数据附带答案解析、题解过程和习题图片。',\n", "  'tags': ['小学', '语文', '试题数据', '答案解析', '习题图片'],\n", "  'created_at': datetime.datetime(2024, 9, 12, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 9, 12, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab145'),\n", "  'name': '初中数学试题数据集',\n", "  'description': '包含初中数学试题数据，题型覆盖填空题、多项选择题、判断题等。每条试题数据附带答案解析、题解过程和习题图片。',\n", "  'tags': ['初中', '数学', '试题数据', '答案解析', '习题图片'],\n", "  'created_at': datetime.datetime(2024, 1, 2, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 1, 2, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab146'),\n", "  'name': '高中地理试题数据集',\n", "  'description': '包含高中地理试题数据，题型覆盖问答题、单项选择题。每条试题数据附带答案解析、题解过程和习题图片。',\n", "  'tags': ['高中', '地理', '试题数据', '答案解析', '习题图片'],\n", "  'created_at': datetime.datetime(2024, 6, 24, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 24, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab147'),\n", "  'name': '高中化学试题数据集',\n", "  'description': '包含高中化学试题数据，题型覆盖填空题、判断题、多项选择题等。每条试题数据附带答案解析、题解过程和习题图片。',\n", "  'tags': ['高中', '化学', '试题数据', '答案解析', '习题图片'],\n", "  'created_at': datetime.datetime(2024, 4, 26, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 4, 26, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab148'),\n", "  'name': '初中生物教学资料数据集',\n", "  'description': '包含初中生物教学资料文章，提取了文章中的配图并独立存储，同时标识了配图在文章中的位置。每篇文章至少有1张配图。',\n", "  'tags': ['初中', '生物', '教学资料', '文章', '配图'],\n", "  'created_at': datetime.datetime(2024, 9, 7, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 9, 7, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab149'),\n", "  'name': '高中物理教学资料数据集',\n", "  'description': '包含高中物理教学资料文章，提取了文章中的配图并独立存储，同时标识了配图在文章中的位置。每篇文章至少有1张配图。',\n", "  'tags': ['高中', '物理', '教学资料', '文章', '配图'],\n", "  'created_at': datetime.datetime(2024, 6, 3, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 3, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab14a'),\n", "  'name': '大学计算机教学资料数据集',\n", "  'description': '包含大学计算机专业的教学资料文章，文章中的配图已提取并单独存储，且标识了配图在文章中的位置。',\n", "  'tags': ['大学', '计算机', '教学资料', '文章', '配图'],\n", "  'created_at': datetime.datetime(2024, 1, 30, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 1, 30, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab14b'),\n", "  'name': '唐诗配图数据集',\n", "  'description': '包含唐朝的古诗词及其配图，配图与诗词内容主旨相符，一一对应。',\n", "  'tags': ['唐诗', '古诗词', '配图', '唐朝', '文学'],\n", "  'created_at': datetime.datetime(2024, 9, 27, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 9, 27, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab14c'),\n", "  'name': '宋词配图数据集',\n", "  'description': '包含宋朝的古诗词及其配图，配图与诗词内容主旨相符，一一对应。',\n", "  'tags': ['宋词', '古诗词', '配图', '宋朝', '文学'],\n", "  'created_at': datetime.datetime(2024, 3, 30, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 3, 30, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab14d'),\n", "  'name': '清代古诗词配图数据集',\n", "  'description': '包含清代的古诗词及其配图，配图与诗词内容主旨相符，一一对应。',\n", "  'tags': ['清代', '古诗词', '配图', '清朝', '文学'],\n", "  'created_at': datetime.datetime(2024, 4, 9, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 4, 9, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab14e'),\n", "  'name': '地理杂志文章数据集',\n", "  'description': '包含来自优质媒体的地理主题中文杂志文章，文章配图已提取并独立存储，并标识了配图在文章中的位置。',\n", "  'tags': ['地理', '杂志文章', '配图', '优质媒体'],\n", "  'created_at': datetime.datetime(2024, 4, 12, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 4, 12, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab14f'),\n", "  'name': '历史杂志文章数据集',\n", "  'description': '包含来自优质媒体的历史主题中文杂志文章，文章配图已提取并独立存储，并标识了配图在文章中的位置。',\n", "  'tags': ['历史', '杂志文章', '配图', '优质媒体'],\n", "  'created_at': datetime.datetime(2024, 3, 23, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 3, 23, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab150'),\n", "  'name': '医学杂志文章数据集',\n", "  'description': '包含来自优质媒体的医学主题中文杂志文章，文章配图已提取并独立存储，并标识了配图在文章中的位置。',\n", "  'tags': ['医学', '杂志文章', '配图', '优质媒体'],\n", "  'created_at': datetime.datetime(2024, 1, 16, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 1, 16, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab151'),\n", "  'name': '科学杂志文章数据集',\n", "  'description': '包含来自优质媒体的科学主题中文杂志文章，文章配图已提取并独立存储，并标识了配图在文章中的位置。',\n", "  'tags': ['科学', '杂志文章', '配图', '优质媒体'],\n", "  'created_at': datetime.datetime(2024, 8, 8, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 8, 8, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab152'),\n", "  'name': '上市公司财报数据集',\n", "  'description': '涵盖上市公司对外披露的财报，包括重要财务指标，如利润表、资产负债表等，涉及沪深股市主流报纸新闻、财联社、新浪财经等。',\n", "  'tags': ['财报', '上市公司', '财务数据'],\n", "  'created_at': datetime.datetime(2024, 1, 4, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 1, 4, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab153'),\n", "  'name': '财经杂志数据集',\n", "  'description': '包括有关金融、股市、债券等各类财经信息的权威报道和分析。涵盖财经杂志如《财联社》、《新财富》等。',\n", "  'tags': ['财经杂志', '金融', '市场分析'],\n", "  'created_at': datetime.datetime(2024, 4, 11, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 4, 11, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab154'),\n", "  'name': '金融产品公告数据集',\n", "  'description': '涵盖金融产品（包括IPO、债券发行等）的公告数据，涉及IPO招股说明书、发行公告等。',\n", "  'tags': ['金融产品', 'IPO公告', '债券发行'],\n", "  'created_at': datetime.datetime(2024, 6, 29, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 29, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab155'),\n", "  'name': '法律意见书数据集',\n", "  'description': '包括各类公司法务提供的法律意见书，涵盖IPO审核过程中的法律意见、公司股权架构调整、员工持股计划等。',\n", "  'tags': ['法律意见书', 'IPO', '股权调整'],\n", "  'created_at': datetime.datetime(2024, 4, 9, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 4, 9, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab156'),\n", "  'name': '三市公告数据集',\n", "  'description': '包括上海证券交易所、深圳证券交易所和北京证券交易所的公告信息，涵盖季度报告、年度报告及临时公告等。',\n", "  'tags': ['公告', '三市', '交易所'],\n", "  'created_at': datetime.datetime(2024, 6, 5, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 5, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab157'),\n", "  'name': '新三板挂牌公司公告数据集',\n", "  'description': '包含新三板挂牌公司的公告数据，涵盖中介机构报告、挂牌企业的定期和临时公告。',\n", "  'tags': ['新三板', '挂牌公司', '公告'],\n", "  'created_at': datetime.datetime(2024, 3, 26, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 3, 26, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab158'),\n", "  'name': '债券公告数据集',\n", "  'description': '涵盖国内外债券市场的公告数据，涉及债券发行、票面利率调整、公司债及国债公告。',\n", "  'tags': ['债券公告', '债券发行', '市场公告'],\n", "  'created_at': datetime.datetime(2024, 6, 26, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 26, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab159'),\n", "  'name': '公募基金公告数据集',\n", "  'description': '涵盖公募基金的公告数据，包括基金发行、基金经理变更、季度报告等。',\n", "  'tags': ['公募基金', '基金公告', '基金经理'],\n", "  'created_at': datetime.datetime(2024, 8, 16, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 8, 16, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab15a'),\n", "  'name': '证券行业监管信息数据集',\n", "  'description': '包括证监会发布的监管公告、交易所规定、各类行业合规信息等。',\n", "  'tags': ['证券监管', '证监会', '合规信息'],\n", "  'created_at': datetime.datetime(2024, 6, 28, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 28, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab15b'),\n", "  'name': '行政处罚数据集',\n", "  'description': '包括证券行业的行政处罚信息，涵盖违规操作的处罚结果、企业和个人的处罚公告。',\n", "  'tags': ['行政处罚', '证券违规', '处罚公告'],\n", "  'created_at': datetime.datetime(2024, 6, 5, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 5, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab15c'),\n", "  'name': '机构调研数据集',\n", "  'description': '包括券商、基金公司等金融机构对企业的调研报告，涉及行业分析、企业业绩及市场趋势预测等。',\n", "  'tags': ['机构调研', '行业分析', '企业业绩'],\n", "  'created_at': datetime.datetime(2024, 1, 24, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 1, 24, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab15d'),\n", "  'name': '投资者互动问答数据集',\n", "  'description': '包括投资者与上市公司在互动平台上的问答，涵盖公司运营状况、财务状况等问题。',\n", "  'tags': ['投资者互动', '上市公司', '问答'],\n", "  'created_at': datetime.datetime(2024, 5, 12, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 5, 12, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab15e'),\n", "  'name': '上市公司回复公告数据集',\n", "  'description': '包括上市公司对监管部门及交易所的问询函的回复公告，涉及公司运营及财务状况的详细回复。',\n", "  'tags': ['上市公司回复', '监管部门', '问询函'],\n", "  'created_at': datetime.datetime(2024, 9, 21, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 9, 21, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab15f'),\n", "  'name': 'IPO过程中的反馈问答数据集',\n", "  'description': '包括IPO审核过程中对企业提出的反馈问题及企业回复情况，涵盖上交所、深交所等的反馈问答。',\n", "  'tags': ['IPO反馈', '企业回复', 'IPO审核'],\n", "  'created_at': datetime.datetime(2024, 4, 17, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 4, 17, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab160'),\n", "  'name': '债券发行反馈问答数据集',\n", "  'description': '包含债券发行过程中的反馈问题及相关回复，涵盖债券市场监管部门的反馈和企业的回复。',\n", "  'tags': ['债券发行', '反馈问答', '市场监管'],\n", "  'created_at': datetime.datetime(2024, 2, 9, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 2, 9, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab161'),\n", "  'name': '金融法规库数据集',\n", "  'description': '包括金融相关的法律法规、政策解读以及各类监管规定，涵盖金融监管机构的官方发布内容。',\n", "  'tags': ['金融法规', '政策解读', '监管规定'],\n", "  'created_at': datetime.datetime(2024, 5, 25, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 5, 25, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab162'),\n", "  'name': '证券法法规库数据集',\n", "  'description': '包括证监会、证券交易所发布的证券法及相关实施细则，涵盖行业规则、交易法规等内容。',\n", "  'tags': ['证券法', '行业规则', '交易法规'],\n", "  'created_at': datetime.datetime(2024, 2, 11, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 2, 11, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab163'),\n", "  'name': '研报库数据集',\n", "  'description': '包括金融机构发布的各类研究报告，涵盖行业研究、公司研究和市场分析，涉及证券、宏观经济等领域。',\n", "  'tags': ['研报', '行业研究', '公司研究'],\n", "  'created_at': datetime.datetime(2024, 5, 4, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 5, 4, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab164'),\n", "  'name': '政府采购招标数据集',\n", "  'description': '包括政府发布的招标采购信息，涵盖政府部门、公共机构的招标公告和招标结果。',\n", "  'tags': ['政府采购', '招标公告', '招标结果'],\n", "  'created_at': datetime.datetime(2024, 6, 21, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 21, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab165'),\n", "  'name': '金融法规解读数据集',\n", "  'description': '包括金融领域相关法规、政策的专业解读，涵盖金融监管机构的官方解释和法律条文的细致分析。',\n", "  'tags': ['金融法规', '政策解读', '监管解读'],\n", "  'created_at': datetime.datetime(2024, 7, 29, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 7, 29, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'}]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["datasets"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["Insert<PERSON><PERSON><PERSON><PERSON><PERSON>([ObjectId('671cd8d3291a6d3d6beab144'), ObjectId('671cd8d3291a6d3d6beab145'), ObjectId('671cd8d3291a6d3d6beab146'), ObjectId('671cd8d3291a6d3d6beab147'), ObjectId('671cd8d3291a6d3d6beab148'), ObjectId('671cd8d3291a6d3d6beab149'), ObjectId('671cd8d3291a6d3d6beab14a'), ObjectId('671cd8d3291a6d3d6beab14b'), ObjectId('671cd8d3291a6d3d6beab14c'), ObjectId('671cd8d3291a6d3d6beab14d'), ObjectId('671cd8d3291a6d3d6beab14e'), ObjectId('671cd8d3291a6d3d6beab14f'), ObjectId('671cd8d3291a6d3d6beab150'), ObjectId('671cd8d3291a6d3d6beab151'), ObjectId('671cd8d3291a6d3d6beab152'), ObjectId('671cd8d3291a6d3d6beab153'), ObjectId('671cd8d3291a6d3d6beab154'), ObjectId('671cd8d3291a6d3d6beab155'), ObjectId('671cd8d3291a6d3d6beab156'), ObjectId('671cd8d3291a6d3d6beab157'), ObjectId('671cd8d3291a6d3d6beab158'), ObjectId('671cd8d3291a6d3d6beab159'), ObjectId('671cd8d3291a6d3d6beab15a'), ObjectId('671cd8d3291a6d3d6beab15b'), ObjectId('671cd8d3291a6d3d6beab15c'), ObjectId('671cd8d3291a6d3d6beab15d'), ObjectId('671cd8d3291a6d3d6beab15e'), ObjectId('671cd8d3291a6d3d6beab15f'), ObjectId('671cd8d3291a6d3d6beab160'), ObjectId('671cd8d3291a6d3d6beab161'), ObjectId('671cd8d3291a6d3d6beab162'), ObjectId('671cd8d3291a6d3d6beab163'), ObjectId('671cd8d3291a6d3d6beab164'), ObjectId('671cd8d3291a6d3d6beab165')], acknowledged=True)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["collection.insert_many(datasets)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["import datetime"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["datasetList =  [{'_id': ObjectId('671cd8d3291a6d3d6beab144'),\n", "  'name': '小学语文试题数据集',\n", "  'description': '包含小学语文试题数据，题型覆盖问答题、单项选择题、判断题等。每条试题数据附带答案解析、题解过程和习题图片。',\n", "  'tags': ['小学', '语文', '试题数据', '答案解析', '习题图片'],\n", "  'created_at': datetime.datetime(2024, 9, 12, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 9, 12, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab145'),\n", "  'name': '初中数学试题数据集',\n", "  'description': '包含初中数学试题数据，题型覆盖填空题、多项选择题、判断题等。每条试题数据附带答案解析、题解过程和习题图片。',\n", "  'tags': ['初中', '数学', '试题数据', '答案解析', '习题图片'],\n", "  'created_at': datetime.datetime(2024, 1, 2, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 1, 2, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab146'),\n", "  'name': '高中地理试题数据集',\n", "  'description': '包含高中地理试题数据，题型覆盖问答题、单项选择题。每条试题数据附带答案解析、题解过程和习题图片。',\n", "  'tags': ['高中', '地理', '试题数据', '答案解析', '习题图片'],\n", "  'created_at': datetime.datetime(2024, 6, 24, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 24, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab147'),\n", "  'name': '高中化学试题数据集',\n", "  'description': '包含高中化学试题数据，题型覆盖填空题、判断题、多项选择题等。每条试题数据附带答案解析、题解过程和习题图片。',\n", "  'tags': ['高中', '化学', '试题数据', '答案解析', '习题图片'],\n", "  'created_at': datetime.datetime(2024, 4, 26, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 4, 26, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab148'),\n", "  'name': '初中生物教学资料数据集',\n", "  'description': '包含初中生物教学资料文章，提取了文章中的配图并独立存储，同时标识了配图在文章中的位置。每篇文章至少有1张配图。',\n", "  'tags': ['初中', '生物', '教学资料', '文章', '配图'],\n", "  'created_at': datetime.datetime(2024, 9, 7, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 9, 7, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab149'),\n", "  'name': '高中物理教学资料数据集',\n", "  'description': '包含高中物理教学资料文章，提取了文章中的配图并独立存储，同时标识了配图在文章中的位置。每篇文章至少有1张配图。',\n", "  'tags': ['高中', '物理', '教学资料', '文章', '配图'],\n", "  'created_at': datetime.datetime(2024, 6, 3, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 3, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab14a'),\n", "  'name': '大学计算机教学资料数据集',\n", "  'description': '包含大学计算机专业的教学资料文章，文章中的配图已提取并单独存储，且标识了配图在文章中的位置。',\n", "  'tags': ['大学', '计算机', '教学资料', '文章', '配图'],\n", "  'created_at': datetime.datetime(2024, 1, 30, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 1, 30, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab14b'),\n", "  'name': '唐诗配图数据集',\n", "  'description': '包含唐朝的古诗词及其配图，配图与诗词内容主旨相符，一一对应。',\n", "  'tags': ['唐诗', '古诗词', '配图', '唐朝', '文学'],\n", "  'created_at': datetime.datetime(2024, 9, 27, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 9, 27, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab14c'),\n", "  'name': '宋词配图数据集',\n", "  'description': '包含宋朝的古诗词及其配图，配图与诗词内容主旨相符，一一对应。',\n", "  'tags': ['宋词', '古诗词', '配图', '宋朝', '文学'],\n", "  'created_at': datetime.datetime(2024, 3, 30, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 3, 30, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab14d'),\n", "  'name': '清代古诗词配图数据集',\n", "  'description': '包含清代的古诗词及其配图，配图与诗词内容主旨相符，一一对应。',\n", "  'tags': ['清代', '古诗词', '配图', '清朝', '文学'],\n", "  'created_at': datetime.datetime(2024, 4, 9, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 4, 9, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab14e'),\n", "  'name': '地理杂志文章数据集',\n", "  'description': '包含来自优质媒体的地理主题中文杂志文章，文章配图已提取并独立存储，并标识了配图在文章中的位置。',\n", "  'tags': ['地理', '杂志文章', '配图', '优质媒体'],\n", "  'created_at': datetime.datetime(2024, 4, 12, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 4, 12, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab14f'),\n", "  'name': '历史杂志文章数据集',\n", "  'description': '包含来自优质媒体的历史主题中文杂志文章，文章配图已提取并独立存储，并标识了配图在文章中的位置。',\n", "  'tags': ['历史', '杂志文章', '配图', '优质媒体'],\n", "  'created_at': datetime.datetime(2024, 3, 23, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 3, 23, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab150'),\n", "  'name': '医学杂志文章数据集',\n", "  'description': '包含来自优质媒体的医学主题中文杂志文章，文章配图已提取并独立存储，并标识了配图在文章中的位置。',\n", "  'tags': ['医学', '杂志文章', '配图', '优质媒体'],\n", "  'created_at': datetime.datetime(2024, 1, 16, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 1, 16, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab151'),\n", "  'name': '科学杂志文章数据集',\n", "  'description': '包含来自优质媒体的科学主题中文杂志文章，文章配图已提取并独立存储，并标识了配图在文章中的位置。',\n", "  'tags': ['科学', '杂志文章', '配图', '优质媒体'],\n", "  'created_at': datetime.datetime(2024, 8, 8, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 8, 8, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab152'),\n", "  'name': '上市公司财报数据集',\n", "  'description': '涵盖上市公司对外披露的财报，包括重要财务指标，如利润表、资产负债表等，涉及沪深股市主流报纸新闻、财联社、新浪财经等。',\n", "  'tags': ['财报', '上市公司', '财务数据'],\n", "  'created_at': datetime.datetime(2024, 1, 4, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 1, 4, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab153'),\n", "  'name': '财经杂志数据集',\n", "  'description': '包括有关金融、股市、债券等各类财经信息的权威报道和分析。涵盖财经杂志如《财联社》、《新财富》等。',\n", "  'tags': ['财经杂志', '金融', '市场分析'],\n", "  'created_at': datetime.datetime(2024, 4, 11, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 4, 11, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab154'),\n", "  'name': '金融产品公告数据集',\n", "  'description': '涵盖金融产品（包括IPO、债券发行等）的公告数据，涉及IPO招股说明书、发行公告等。',\n", "  'tags': ['金融产品', 'IPO公告', '债券发行'],\n", "  'created_at': datetime.datetime(2024, 6, 29, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 29, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab155'),\n", "  'name': '法律意见书数据集',\n", "  'description': '包括各类公司法务提供的法律意见书，涵盖IPO审核过程中的法律意见、公司股权架构调整、员工持股计划等。',\n", "  'tags': ['法律意见书', 'IPO', '股权调整'],\n", "  'created_at': datetime.datetime(2024, 4, 9, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 4, 9, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab156'),\n", "  'name': '三市公告数据集',\n", "  'description': '包括上海证券交易所、深圳证券交易所和北京证券交易所的公告信息，涵盖季度报告、年度报告及临时公告等。',\n", "  'tags': ['公告', '三市', '交易所'],\n", "  'created_at': datetime.datetime(2024, 6, 5, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 5, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab157'),\n", "  'name': '新三板挂牌公司公告数据集',\n", "  'description': '包含新三板挂牌公司的公告数据，涵盖中介机构报告、挂牌企业的定期和临时公告。',\n", "  'tags': ['新三板', '挂牌公司', '公告'],\n", "  'created_at': datetime.datetime(2024, 3, 26, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 3, 26, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab158'),\n", "  'name': '债券公告数据集',\n", "  'description': '涵盖国内外债券市场的公告数据，涉及债券发行、票面利率调整、公司债及国债公告。',\n", "  'tags': ['债券公告', '债券发行', '市场公告'],\n", "  'created_at': datetime.datetime(2024, 6, 26, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 26, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab159'),\n", "  'name': '公募基金公告数据集',\n", "  'description': '涵盖公募基金的公告数据，包括基金发行、基金经理变更、季度报告等。',\n", "  'tags': ['公募基金', '基金公告', '基金经理'],\n", "  'created_at': datetime.datetime(2024, 8, 16, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 8, 16, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab15a'),\n", "  'name': '证券行业监管信息数据集',\n", "  'description': '包括证监会发布的监管公告、交易所规定、各类行业合规信息等。',\n", "  'tags': ['证券监管', '证监会', '合规信息'],\n", "  'created_at': datetime.datetime(2024, 6, 28, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 28, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab15b'),\n", "  'name': '行政处罚数据集',\n", "  'description': '包括证券行业的行政处罚信息，涵盖违规操作的处罚结果、企业和个人的处罚公告。',\n", "  'tags': ['行政处罚', '证券违规', '处罚公告'],\n", "  'created_at': datetime.datetime(2024, 6, 5, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 5, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab15c'),\n", "  'name': '机构调研数据集',\n", "  'description': '包括券商、基金公司等金融机构对企业的调研报告，涉及行业分析、企业业绩及市场趋势预测等。',\n", "  'tags': ['机构调研', '行业分析', '企业业绩'],\n", "  'created_at': datetime.datetime(2024, 1, 24, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 1, 24, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab15d'),\n", "  'name': '投资者互动问答数据集',\n", "  'description': '包括投资者与上市公司在互动平台上的问答，涵盖公司运营状况、财务状况等问题。',\n", "  'tags': ['投资者互动', '上市公司', '问答'],\n", "  'created_at': datetime.datetime(2024, 5, 12, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 5, 12, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab15e'),\n", "  'name': '上市公司回复公告数据集',\n", "  'description': '包括上市公司对监管部门及交易所的问询函的回复公告，涉及公司运营及财务状况的详细回复。',\n", "  'tags': ['上市公司回复', '监管部门', '问询函'],\n", "  'created_at': datetime.datetime(2024, 9, 21, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 9, 21, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab15f'),\n", "  'name': 'IPO过程中的反馈问答数据集',\n", "  'description': '包括IPO审核过程中对企业提出的反馈问题及企业回复情况，涵盖上交所、深交所等的反馈问答。',\n", "  'tags': ['IPO反馈', '企业回复', 'IPO审核'],\n", "  'created_at': datetime.datetime(2024, 4, 17, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 4, 17, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab160'),\n", "  'name': '债券发行反馈问答数据集',\n", "  'description': '包含债券发行过程中的反馈问题及相关回复，涵盖债券市场监管部门的反馈和企业的回复。',\n", "  'tags': ['债券发行', '反馈问答', '市场监管'],\n", "  'created_at': datetime.datetime(2024, 2, 9, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 2, 9, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab161'),\n", "  'name': '金融法规库数据集',\n", "  'description': '包括金融相关的法律法规、政策解读以及各类监管规定，涵盖金融监管机构的官方发布内容。',\n", "  'tags': ['金融法规', '政策解读', '监管规定'],\n", "  'created_at': datetime.datetime(2024, 5, 25, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 5, 25, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab162'),\n", "  'name': '证券法法规库数据集',\n", "  'description': '包括证监会、证券交易所发布的证券法及相关实施细则，涵盖行业规则、交易法规等内容。',\n", "  'tags': ['证券法', '行业规则', '交易法规'],\n", "  'created_at': datetime.datetime(2024, 2, 11, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 2, 11, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab163'),\n", "  'name': '研报库数据集',\n", "  'description': '包括金融机构发布的各类研究报告，涵盖行业研究、公司研究和市场分析，涉及证券、宏观经济等领域。',\n", "  'tags': ['研报', '行业研究', '公司研究'],\n", "  'created_at': datetime.datetime(2024, 5, 4, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 5, 4, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab164'),\n", "  'name': '政府采购招标数据集',\n", "  'description': '包括政府发布的招标采购信息，涵盖政府部门、公共机构的招标公告和招标结果。',\n", "  'tags': ['政府采购', '招标公告', '招标结果'],\n", "  'created_at': datetime.datetime(2024, 6, 21, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 6, 21, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'},\n", " {'_id': ObjectId('671cd8d3291a6d3d6beab165'),\n", "  'name': '金融法规解读数据集',\n", "  'description': '包括金融领域相关法规、政策的专业解读，涵盖金融监管机构的官方解释和法律条文的细致分析。',\n", "  'tags': ['金融法规', '政策解读', '监管解读'],\n", "  'created_at': datetime.datetime(2024, 7, 29, 0, 0),\n", "  'last_updated': datetime.datetime(2024, 7, 29, 0, 0),\n", "  'user_id': 1,\n", "  'is_active': True,\n", "  'user_name': '超级管理员',\n", "  'row_count': 0,\n", "  'processing_status': 'completed'}]"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["file_list =[\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab201'),\n", "        \"name\": \"小学语文试题数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/小学语文试题数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab144'),\n", "        \"created_at\": \"2024-09-12T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"小学\", \"试题数据\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab202'),\n", "        \"name\": \"小学语文试题数据集_file_4.json\",\n", "        \"storage_path\": \"/dataset/小学语文试题数据集_file_4.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab144'),\n", "        \"created_at\": \"2024-09-12T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"语文\", \"数据集\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab203'),\n", "        \"name\": \"小学语文试题数据集_file_5.csv\",\n", "        \"storage_path\": \"/dataset/小学语文试题数据集_file_5.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab144'),\n", "        \"created_at\": \"2024-09-12T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"小学\", \"语文\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab204'),\n", "        \"name\": \"初中数学试题数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/初中数学试题数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab145'),\n", "        \"created_at\": \"2024-01-02T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"数学\", \"试题数据\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab205'),\n", "        \"name\": \"初中数学试题数据集_file_4.json\",\n", "        \"storage_path\": \"/dataset/初中数学试题数据集_file_4.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab145'),\n", "        \"created_at\": \"2024-01-02T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"数学\", \"数据集\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab206'),\n", "        \"name\": \"初中数学试题数据集_file_5.csv\",\n", "        \"storage_path\": \"/dataset/初中数学试题数据集_file_5.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab145'),\n", "        \"created_at\": \"2024-01-02T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"初中\", \"数学\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab207'),\n", "        \"name\": \"高中地理试题数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/高中地理试题数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab146'),\n", "        \"created_at\": \"2024-06-24T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"高中\", \"地理\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab208'),\n", "        \"name\": \"高中地理试题数据集_file_4.json\",\n", "        \"storage_path\": \"/dataset/高中地理试题数据集_file_4.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab146'),\n", "        \"created_at\": \"2024-06-24T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"地理\", \"试题\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab209'),\n", "        \"name\": \"高中地理试题数据集_file_5.csv\",\n", "        \"storage_path\": \"/dataset/高中地理试题数据集_file_5.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab146'),\n", "        \"created_at\": \"2024-06-24T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"地理\", \"试题数据\"],\n", "        \"row_count\": 0\n", "    },\n", "\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab210'),\n", "        \"name\": \"高中化学试题数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/高中化学试题数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab147'),\n", "        \"created_at\": \"2024-04-26T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"化学\", \"试题\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab211'),\n", "        \"name\": \"高中化学试题数据集_file_4.json\",\n", "        \"storage_path\": \"/dataset/高中化学试题数据集_file_4.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab147'),\n", "        \"created_at\": \"2024-04-26T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"化学\", \"数据集\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab212'),\n", "        \"name\": \"高中化学试题数据集_file_5.csv\",\n", "        \"storage_path\": \"/dataset/高中化学试题数据集_file_5.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab147'),\n", "        \"created_at\": \"2024-04-26T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"化学\", \"试题数据\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab213'),\n", "        \"name\": \"初中生物教学资料数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/初中生物教学资料数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab148'),\n", "        \"created_at\": \"2024-09-07T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"生物\", \"教学资料\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab214'),\n", "        \"name\": \"初中生物教学资料数据集_file_4.json\",\n", "        \"storage_path\": \"/dataset/初中生物教学资料数据集_file_4.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab148'),\n", "        \"created_at\": \"2024-09-07T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"生物\", \"资料\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab215'),\n", "        \"name\": \"初中生物教学资料数据集_file_5.csv\",\n", "        \"storage_path\": \"/dataset/初中生物教学资料数据集_file_5.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab148'),\n", "        \"created_at\": \"2024-09-07T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"教学\", \"生物\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab216'),\n", "        \"name\": \"唐诗配图数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/唐诗配图数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14b'),\n", "        \"created_at\": \"2024-09-27T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"唐诗\", \"配图\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab217'),\n", "        \"name\": \"唐诗配图数据集_file_4.json\",\n", "        \"storage_path\": \"/dataset/唐诗配图数据集_file_4.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14b'),\n", "        \"created_at\": \"2024-09-27T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"唐诗\", \"数据集\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab218'),\n", "        \"name\": \"唐诗配图数据集_file_5.csv\",\n", "        \"storage_path\": \"/dataset/唐诗配图数据集_file_5.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14b'),\n", "        \"created_at\": \"2024-09-27T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"唐诗\", \"文学\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab219'),\n", "        \"name\": \"高中物理教学资料数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/高中物理教学资料数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab149'),\n", "        \"created_at\": \"2024-06-03T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"物理\", \"教学\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab220'),\n", "        \"name\": \"高中物理教学资料数据集_file_4.json\",\n", "        \"storage_path\": \"/dataset/高中物理教学资料数据集_file_4.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab149'),\n", "        \"created_at\": \"2024-06-03T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"物理\", \"教学资料\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab221'),\n", "        \"name\": \"高中物理教学资料数据集_file_5.csv\",\n", "        \"storage_path\": \"/dataset/高中物理教学资料数据集_file_5.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab149'),\n", "        \"created_at\": \"2024-06-03T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"教学\", \"物理\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab222'),\n", "        \"name\": \"大学计算机教学资料数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/大学计算机教学资料数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14a'),\n", "        \"created_at\": \"2024-01-30T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"大学\", \"计算机\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab223'),\n", "        \"name\": \"大学计算机教学资料数据集_file_4.json\",\n", "        \"storage_path\": \"/dataset/大学计算机教学资料数据集_file_4.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14a'),\n", "        \"created_at\": \"2024-01-30T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"计算机\", \"资料\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab224'),\n", "        \"name\": \"大学计算机教学资料数据集_file_5.csv\",\n", "        \"storage_path\": \"/dataset/大学计算机教学资料数据集_file_5.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14a'),\n", "        \"created_at\": \"2024-01-30T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"大学\", \"计算机\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab225'),\n", "        \"name\": \"宋词配图数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/宋词配图数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14c'),\n", "        \"created_at\": \"2024-03-30T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"宋词\", \"配图\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab226'),\n", "        \"name\": \"宋词配图数据集_file_4.json\",\n", "        \"storage_path\": \"/dataset/宋词配图数据集_file_4.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14c'),\n", "        \"created_at\": \"2024-03-30T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"宋词\", \"配图\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab227'),\n", "        \"name\": \"宋词配图数据集_file_5.csv\",\n", "        \"storage_path\": \"/dataset/宋词配图数据集_file_5.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14c'),\n", "        \"created_at\": \"2024-03-30T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"宋词\", \"文学\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab228'),\n", "        \"name\": \"清代古诗词配图数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/清代古诗词配图数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14d'),\n", "        \"created_at\": \"2024-04-09T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"清代\", \"古诗词\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab229'),\n", "        \"name\": \"清代古诗词配图数据集_file_4.json\",\n", "        \"storage_path\": \"/dataset/清代古诗词配图数据集_file_4.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14d'),\n", "        \"created_at\": \"2024-04-09T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"古诗词\", \"配图\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab230'),\n", "        \"name\": \"清代古诗词配图数据集_file_5.csv\",\n", "        \"storage_path\": \"/dataset/清代古诗词配图数据集_file_5.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14d'),\n", "        \"created_at\": \"2024-04-09T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"清代\", \"文学\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab231'),\n", "        \"name\": \"地理杂志文章数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/地理杂志文章数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14e'),\n", "        \"created_at\": \"2024-04-12T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"地理\", \"杂志\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab232'),\n", "        \"name\": \"地理杂志文章数据集_file_4.json\",\n", "        \"storage_path\": \"/dataset/地理杂志文章数据集_file_4.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14e'),\n", "        \"created_at\": \"2024-04-12T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"地理\", \"配图\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab233'),\n", "        \"name\": \"地理杂志文章数据集_file_5.csv\",\n", "        \"storage_path\": \"/dataset/地理杂志文章数据集_file_5.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14e'),\n", "        \"created_at\": \"2024-04-12T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"地理\", \"媒体\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab234'),\n", "        \"name\": \"历史杂志文章数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/历史杂志文章数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab14f'),\n", "        \"created_at\": \"2024-03-23T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"历史\", \"杂志\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab235'),\n", "        \"name\": \"医学杂志文章数据集_file_3.json\",\n", "        \"storage_path\": \"/dataset/医学杂志文章数据集_file_3.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab150'),\n", "        \"created_at\": \"2024-01-16T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"医学\", \"杂志\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab236'),\n", "        \"name\": \"科学杂志文章数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/科学杂志文章数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab151'),\n", "        \"created_at\": \"2024-08-08T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"科学\", \"杂志\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab237'),\n", "        \"name\": \"上市公司财报数据集_file_3.json\",\n", "        \"storage_path\": \"/dataset/上市公司财报数据集_file_3.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab152'),\n", "        \"created_at\": \"2024-01-04T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"财报\", \"上市公司\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab238'),\n", "        \"name\": \"财经杂志数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/财经杂志数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab153'),\n", "        \"created_at\": \"2024-04-11T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"财经\", \"杂志\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab239'),\n", "        \"name\": \"金融产品公告数据集_file_3.json\",\n", "        \"storage_path\": \"/dataset/金融产品公告数据集_file_3.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab154'),\n", "        \"created_at\": \"2024-06-29T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"金融\", \"公告\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab240'),\n", "        \"name\": \"法律意见书数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/法律意见书数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab155'),\n", "        \"created_at\": \"2024-04-09T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"法律\", \"意见书\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab241'),\n", "        \"name\": \"三市公告数据集_file_3.json\",\n", "        \"storage_path\": \"/dataset/三市公告数据集_file_3.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab156'),\n", "        \"created_at\": \"2024-06-05T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"公告\", \"三市\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab242'),\n", "        \"name\": \"新三板挂牌公司公告数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/新三板挂牌公司公告数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab157'),\n", "        \"created_at\": \"2024-03-26T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"新三板\", \"公告\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab243'),\n", "        \"name\": \"债券公告数据集_file_3.json\",\n", "        \"storage_path\": \"/dataset/债券公告数据集_file_3.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab158'),\n", "        \"created_at\": \"2024-06-26T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"债券\", \"公告\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab244'),\n", "        \"name\": \"公募基金公告数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/公募基金公告数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab159'),\n", "        \"created_at\": \"2024-08-16T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"公募基金\", \"公告\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab245'),\n", "        \"name\": \"证券行业监管信息数据集_file_3.json\",\n", "        \"storage_path\": \"/dataset/证券行业监管信息数据集_file_3.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab15a'),\n", "        \"created_at\": \"2024-06-28T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"证券\", \"监管\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab246'),\n", "        \"name\": \"行政处罚数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/行政处罚数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab15b'),\n", "        \"created_at\": \"2024-06-05T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"行政处罚\", \"证券\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab247'),\n", "        \"name\": \"机构调研数据集_file_3.json\",\n", "        \"storage_path\": \"/dataset/机构调研数据集_file_3.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab15c'),\n", "        \"created_at\": \"2024-01-24T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"调研\", \"机构\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab248'),\n", "        \"name\": \"投资者互动问答数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/投资者互动问答数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab15d'),\n", "        \"created_at\": \"2024-05-12T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"投资者\", \"互动\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab249'),\n", "        \"name\": \"上市公司回复公告数据集_file_3.json\",\n", "        \"storage_path\": \"/dataset/上市公司回复公告数据集_file_3.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab15e'),\n", "        \"created_at\": \"2024-09-21T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"上市公司\", \"公告\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab250'),\n", "        \"name\": \"IPO过程中的反馈问答数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/IPO过程中的反馈问答数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab15f'),\n", "        \"created_at\": \"2024-04-17T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"IPO\", \"反馈\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab251'),\n", "        \"name\": \"债券发行反馈问答数据集_file_3.json\",\n", "        \"storage_path\": \"/dataset/债券发行反馈问答数据集_file_3.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab160'),\n", "        \"created_at\": \"2024-02-09T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"债券\", \"反馈\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab252'),\n", "        \"name\": \"金融法规库数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/金融法规库数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab161'),\n", "        \"created_at\": \"2024-05-25T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"金融\", \"法规\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab253'),\n", "        \"name\": \"证券法法规库数据集_file_3.json\",\n", "        \"storage_path\": \"/dataset/证券法法规库数据集_file_3.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab162'),\n", "        \"created_at\": \"2024-02-11T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"证券法\", \"法规\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab254'),\n", "        \"name\": \"研报库数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/研报库数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab163'),\n", "        \"created_at\": \"2024-05-04T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"研报\", \"行业\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab255'),\n", "        \"name\": \"政府采购招标数据集_file_3.json\",\n", "        \"storage_path\": \"/dataset/政府采购招标数据集_file_3.json\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab164'),\n", "        \"created_at\": \"2024-06-21T00:00:00\",\n", "        \"data_type\": \"JSON\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"政府采购\", \"招标\"],\n", "        \"row_count\": 0\n", "    },\n", "    {\n", "        \"_id\": ObjectId('671cd8d3291a6d3d6beab256'),\n", "        \"name\": \"金融法规解读数据集_file_3.csv\",\n", "        \"storage_path\": \"/dataset/金融法规解读数据集_file_3.csv\",\n", "        \"dataset_id\": ObjectId('671cd8d3291a6d3d6beab165'),\n", "        \"created_at\": \"2024-07-29T00:00:00\",\n", "        \"data_type\": \"CSV\",\n", "        \"dataset_type\": \"structured\",\n", "        \"processing_status\": \"completed\",\n", "        \"user_id\": 1,\n", "        \"deleted_by\": None,\n", "        \"user_name\": \"超级管理员\",\n", "        \"tags\": [\"金融法规\", \"解读\"],\n", "        \"row_count\": 0\n", "    }\n", "]"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["def convert_created_at(data):\n", "    for record in data:\n", "        record['created_at'] = datetime.datetime.strptime(record['created_at'], '%Y-%m-%dT%H:%M:%S')\n", "    return data"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["file_collection = db[\"dataset_files\"]"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["converted_data = convert_created_at(file_list)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'_id': ObjectId('671cd8d3291a6d3d6beab201'),\n", " 'name': '小学语文试题数据集_file_3.csv',\n", " 'storage_path': '/dataset/小学语文试题数据集_file_3.csv',\n", " 'dataset_id': ObjectId('671cd8d3291a6d3d6beab144'),\n", " 'created_at': datetime.datetime(2024, 9, 12, 0, 0),\n", " 'data_type': 'CSV',\n", " 'dataset_type': 'structured',\n", " 'processing_status': 'completed',\n", " 'user_id': 1,\n", " 'deleted_by': None,\n", " 'user_name': '超级管理员',\n", " 'tags': ['小学', '试题数据'],\n", " 'row_count': 0}"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["converted_data[0]"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["Insert<PERSON><PERSON><PERSON><PERSON><PERSON>([ObjectId('671cd8d3291a6d3d6beab201'), ObjectId('671cd8d3291a6d3d6beab202'), ObjectId('671cd8d3291a6d3d6beab203'), ObjectId('671cd8d3291a6d3d6beab204'), ObjectId('671cd8d3291a6d3d6beab205'), ObjectId('671cd8d3291a6d3d6beab206'), ObjectId('671cd8d3291a6d3d6beab207'), ObjectId('671cd8d3291a6d3d6beab208'), ObjectId('671cd8d3291a6d3d6beab209'), ObjectId('671cd8d3291a6d3d6beab210'), ObjectId('671cd8d3291a6d3d6beab211'), ObjectId('671cd8d3291a6d3d6beab212'), ObjectId('671cd8d3291a6d3d6beab213'), ObjectId('671cd8d3291a6d3d6beab214'), ObjectId('671cd8d3291a6d3d6beab215'), ObjectId('671cd8d3291a6d3d6beab216'), ObjectId('671cd8d3291a6d3d6beab217'), ObjectId('671cd8d3291a6d3d6beab218'), ObjectId('671cd8d3291a6d3d6beab219'), ObjectId('671cd8d3291a6d3d6beab220'), ObjectId('671cd8d3291a6d3d6beab221'), ObjectId('671cd8d3291a6d3d6beab222'), ObjectId('671cd8d3291a6d3d6beab223'), ObjectId('671cd8d3291a6d3d6beab224'), ObjectId('671cd8d3291a6d3d6beab225'), ObjectId('671cd8d3291a6d3d6beab226'), ObjectId('671cd8d3291a6d3d6beab227'), ObjectId('671cd8d3291a6d3d6beab228'), ObjectId('671cd8d3291a6d3d6beab229'), ObjectId('671cd8d3291a6d3d6beab230'), ObjectId('671cd8d3291a6d3d6beab231'), ObjectId('671cd8d3291a6d3d6beab232'), ObjectId('671cd8d3291a6d3d6beab233'), ObjectId('671cd8d3291a6d3d6beab234'), ObjectId('671cd8d3291a6d3d6beab235'), ObjectId('671cd8d3291a6d3d6beab236'), ObjectId('671cd8d3291a6d3d6beab237'), ObjectId('671cd8d3291a6d3d6beab238'), ObjectId('671cd8d3291a6d3d6beab239'), ObjectId('671cd8d3291a6d3d6beab240'), ObjectId('671cd8d3291a6d3d6beab241'), ObjectId('671cd8d3291a6d3d6beab242'), ObjectId('671cd8d3291a6d3d6beab243'), ObjectId('671cd8d3291a6d3d6beab244'), ObjectId('671cd8d3291a6d3d6beab245'), ObjectId('671cd8d3291a6d3d6beab246'), ObjectId('671cd8d3291a6d3d6beab247'), ObjectId('671cd8d3291a6d3d6beab248'), ObjectId('671cd8d3291a6d3d6beab249'), ObjectId('671cd8d3291a6d3d6beab250'), ObjectId('671cd8d3291a6d3d6beab251'), ObjectId('671cd8d3291a6d3d6beab252'), ObjectId('671cd8d3291a6d3d6beab253'), ObjectId('671cd8d3291a6d3d6beab254'), ObjectId('671cd8d3291a6d3d6beab255'), ObjectId('671cd8d3291a6d3d6beab256')], acknowledged=True)"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["file_collection.insert_many(converted_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 图片"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["file_record = {\n", "    \"_id\" : ObjectId(\"671cd8d3291a6d3d6beab201\"),\n", "    \"name\" : \"小学语文试题数据集_file_3.csv\",\n", "    \"storage_path\" : \"/dataset/小学语文试题数据集_file_3.csv\",\n", "    \"dataset_id\" : ObjectId(\"671cd8d3291a6d3d6beab144\"),\n", "    \"created_at\" :datetime.datetime(2024, 9, 12, 0, 0),\n", "    \"data_type\" : \"CSV\",\n", "    \"dataset_type\" : \"structured\",\n", "    \"processing_status\" : \"completed\",\n", "    \"user_id\" : 1,\n", "    \"deleted_by\" : None,\n", "    \"user_name\" : \"超级管理员\",\n", "    \"tags\" : [ \n", "        \"小学\", \n", "        \"试题数据\"\n", "    ],\n", "    \"row_count\" : 0\n", "}"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["671d7553291a6d3d6beab168\n"]}], "source": ["_id =  ObjectId()\n", "print(_id)"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["image_name ='0000.png'"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": ["newdata_record = {\n", "    \"_id\": _id,\n", "    \"dataset_id\": ObjectId(\"671cd8d3291a6d3d6beab144\"),\n", "    \"file_id\":  ObjectId(\"671cd8d3291a6d3d6beab201\"),\n", "    \"data\": newdata,\n", "    \"images\": [f'/{str(_id)}/{file_record[\"created_at\"].strftime(\"%Y-%m-%d_%H-%M-%S\")}_{image_name}'],\n", "    \"created_at\": file_record[\"created_at\"],\n", "    \"updated_at\": file_record[\"created_at\"]\n", " }\n", "  "]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["newdata = '''  {\n", "    \"typeName\": \"选择题\",\n", "    \"source\": \"\",\n", "    \"subjectId\": 1,\n", "    \"parseContent\": \"<p>此题考查学生对句子及短语知识的理解能力。这要求学生要加强基础知识的掌握，对教材中要求掌握的内容要掌握牢固。本题中，A项中的主干应为：天文学家发现，地球大气会使光线散射（主谓短语作宾语）；B项中的句子只有一层意思，应该是单句；C项中，“神采奕奕”是主谓短语。故选D。​</p>\",\n", "    \"subjectName\": \"语文\",\n", "    \"optionList\": [\n", "      {\n", "        \"correct\": false,\n", "        \"optionNo\": \"A\",\n", "        \"content\": \"天文学家早就发现，地球大气会使光线散射。主干：天文学家发现散射。 <p style=\\\"\\\">   </p>\"\n", "      },\n", "      {\n", "        \"correct\": false,\n", "        \"optionNo\": \"B\",\n", "        \"content\": \"假山的堆叠，全在乎设计者和匠师们生平多阅历，胸中有丘壑。 这个句子是个复句。 <p style=\\\"\\\">   </p>\"\n", "      },\n", "      {\n", "        \"correct\": false,\n", "        \"optionNo\": \"C\",\n", "        \"content\": \"字里行间   神采奕奕  我行我素   多姿多彩 都是并列短语。 <p style=\\\"\\\">   </p>\"\n", "      },\n", "      {\n", "        \"correct\": true,\n", "        \"optionNo\": \"D\",\n", "        \"content\": \"喋喋不休 、无所不为、死灰复燃、恃才放旷都是贬义词。\"\n", "      }\n", "    ],\n", "    \"difficulty\": 5,\n", "    \"answer\": \"D\"\n", "  }'''"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'_id': ObjectId('671d7553291a6d3d6beab168'),\n", " 'dataset_id': ObjectId('671cd8d3291a6d3d6beab144'),\n", " 'file_id': ObjectId('671cd8d3291a6d3d6beab201'),\n", " 'data': '  {\\n    \"typeName\": \"选择题\",\\n    \"source\": \"\",\\n    \"subjectId\": 1,\\n    \"parseContent\": \"<p>此题考查学生对句子及短语知识的理解能力。这要求学生要加强基础知识的掌握，对教材中要求掌握的内容要掌握牢固。本题中，A项中的主干应为：天文学家发现，地球大气会使光线散射（主谓短语作宾语）；B项中的句子只有一层意思，应该是单句；C项中，“神采奕奕”是主谓短语。故选D。\\u200b</p>\",\\n    \"subjectName\": \"语文\",\\n    \"optionList\": [\\n      {\\n        \"correct\": false,\\n        \"optionNo\": \"A\",\\n        \"content\": \"天文学家早就发现，地球大气会使光线散射。主干：天文学家发现散射。 <p style=\"\">   </p>\"\\n      },\\n      {\\n        \"correct\": false,\\n        \"optionNo\": \"B\",\\n        \"content\": \"假山的堆叠，全在乎设计者和匠师们生平多阅历，胸中有丘壑。 这个句子是个复句。 <p style=\"\">   </p>\"\\n      },\\n      {\\n        \"correct\": false,\\n        \"optionNo\": \"C\",\\n        \"content\": \"字里行间   神采奕奕  我行我素   多姿多彩 都是并列短语。 <p style=\"\">   </p>\"\\n      },\\n      {\\n        \"correct\": true,\\n        \"optionNo\": \"D\",\\n        \"content\": \"喋喋不休 、无所不为、死灰复燃、恃才放旷都是贬义词。\"\\n      }\\n    ],\\n    \"difficulty\": 5,\\n    \"answer\": \"D\"\\n  }',\n", " 'images': ['/671d7553291a6d3d6beab168/2024-09-12_00-00-00_0000.png'],\n", " 'created_at': datetime.datetime(2024, 9, 12, 0, 0),\n", " 'updated_at': datetime.datetime(2024, 9, 12, 0, 0)}"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["newdata_record"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [], "source": ["# client = pymongo.MongoClient(\"mongodb://memInterview:<EMAIL>:37017/roardataAiApp_test?authSource=admin\")\n", "db = client[\"roardataAiApp_test\"]\n", "collectionstructured_data_records = db[\"structured_data_records\"]"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/plain": ["InsertManyResult([ObjectId('671d7553291a6d3d6beab168')], acknowledged=True)"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["collectionstructured_data_records.insert_many([newdata_record])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "wisechat", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}