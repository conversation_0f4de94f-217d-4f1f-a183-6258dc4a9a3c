"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3537],{60095:function(e,i,t){t.r(i),t.d(i,{default:function(){return P}});var l=t(5574),n=t.n(l),r=t(71471),s=t(71230),a=t(15746),o=t(66309),d=t(83062),c=t(38703),x=t(4393),g=t(67294),h=t(4628),p=t(78919),f="sender____pgN7",y=t(78677),j=t(64183),u=t(63430),m=t(79611),b=t(52545),v=t(4795),Z=t(56717),_=t(55355),S=t(10524),R=t(61086),k=t(97879),z=t(9783),B=t.n(z),w=t(97857),C=t.n(w),N=t(93967),F=t.n(N),T={chartCard:"chartCard___k2mIu",chartTop:"chartTop___y9Pcz",chartTopMargin:"chartTopMargin___Ot6PP",chartTopHasMargin:"chartTopHasMargin___hGkhi",avatar:"avatar___OCsnN",action:"action___oohF5",total:"total___qlYWC",content:"content___GcuwJ",contentFixed:"contentFixed___IuMbn",footer:"footer___owzND",footerMargin:"footerMargin___q8A7Z"},I=t(85893),H=(r.Z.Text,function(e){var i=e.loading,t=void 0!==i&&i,l=e.bordered,n=void 0!==l&&l,r=e.title,s=e.action,a=e.total,o=e.footer,d=e.children,c=e.contentHeight,g=e.style;return(0,I.jsx)(x.Z,{loading:t,bordered:n,style:C()({},g),bodyStyle:{padding:"20px 24px 8px 24px"},children:(0,I.jsxs)("div",{className:T.chartCard,children:[(0,I.jsxs)("div",{className:F()(T.chartTop,B()({},T.chartTopMargin,!d)),children:[(0,I.jsx)("div",{className:T.avatar,children:r}),(0,I.jsx)("div",{className:T.action,children:s})]}),void 0!==a&&(0,I.jsx)("div",{className:T.total,children:"function"==typeof a?a():a}),d&&(0,I.jsx)("div",{className:T.content,style:{height:c||"auto"},children:(0,I.jsx)("div",{className:c&&T.contentFixed,children:d})}),o&&(0,I.jsx)("div",{className:F()(T.footer,B()({},T.footerMargin,!d)),children:o})]})})}),M=function(e){var i=e.label,t=e.value,l=e.style;return(0,I.jsxs)("div",{className:T.field,style:C()({},l),children:[(0,I.jsx)("span",{className:T.label,children:i}),(0,I.jsx)("span",{className:T.value,children:t})]})},L=r.Z.Title,q=r.Z.Paragraph,P=function(){var e=(0,g.useState)(""),i=n()(e,2),t=i[0],l=i[1];return(0,I.jsx)("div",{style:{padding:"32px 64px",backgroundColor:"#f5f3ff",height:"calc(100vh - 64px)",display:"flex",flexDirection:"column",overflowY:"auto"},children:(0,I.jsxs)(s.Z,{gutter:24,style:{flex:1,overflow:"hidden"},children:[(0,I.jsxs)(a.Z,{span:16,style:{height:"100%",display:"flex",flexDirection:"column"},children:[(0,I.jsxs)("div",{style:{flex:1,overflowY:"auto",paddingRight:"24px",marginBottom:16},children:[(0,I.jsxs)("div",{style:{display:"flex",alignItems:"center",marginBottom:24,padding:"16px 24px",background:"linear-gradient(135deg, #fafafa 0%, #fafafa 100%)",borderRadius:12,borderLeft:"4px solid #722ed1",boxShadow:"0 2px 8px rgba(114,46,209,0.1)"},children:[(0,I.jsx)(b.Z,{style:{fontSize:28,color:"#722ed1",marginRight:16,animation:"pulse 1.5s ease-in-out infinite"}}),(0,I.jsxs)("div",{children:[(0,I.jsxs)(L,{level:2,style:{margin:0,color:"rgba(0,0,0,0.85)",fontSize:24,display:"flex",alignItems:"center",gap:8},children:[(0,I.jsx)("span",{children:"这个季度的大模型项目有哪些"}),(0,I.jsx)(o.Z,{color:"processing",style:{fontSize:12,padding:"2px 8px",borderRadius:12,background:"rgba(114,46,209,0.1)",border:"none"},children:"实时分析中"})]}),(0,I.jsxs)(q,{style:{margin:0,color:"rgba(0,0,0,0.45)",fontSize:14},children:["大模型项目智能分析场景 | 最后更新：",(new Date).toLocaleTimeString()]})]})]}),(0,I.jsxs)(s.Z,{gutter:[24,24],style:{marginBottom:32},children:[(0,I.jsx)(a.Z,{span:8,children:(0,I.jsx)(H,{bordered:!1,style:{background:"linear-gradient(135deg, #f5f3ff 0%, #e6e4ff 100%)",borderRadius:8,padding:"6px 6px 6px",height:"100%",margin:2},title:(0,I.jsxs)("span",{style:{paddingLeft:4,fontSize:14},children:[(0,I.jsx)(v.Z,{style:{marginRight:6}}),"本季度商机总额"]}),action:(0,I.jsx)(d.Z,{title:"包含已发布和进行中的项目总金额",children:(0,I.jsx)(Z.Z,{style:{fontSize:14}})}),total:function(){return(0,I.jsx)("span",{style:{fontSize:20},children:"268500"})},footer:(0,I.jsx)(M,{label:"环比增长",value:"+28%",style:{padding:"4px 0"}}),contentHeight:80,bodyStyle:{padding:"4px 0",background:"linear-gradient(to bottom, rgba(255,255,255,0.2) 0%, rgba(230,244,255,0.1) 100%)"},children:(0,I.jsx)(y.Z,{height:80,data:[{x:"Q1",y:156},{x:"Q2",y:228},{x:"Q3",y:268}],xField:"x",yField:"y",color:"#096dd9",columnStyle:{fillOpacity:.8,lineWidth:0,radius:[4,4,0,0]},axis:!1,label:null,tooltip:!1,interactions:[{type:"element-active"}],animation:{appear:{duration:800}}})})}),(0,I.jsx)(a.Z,{span:8,children:(0,I.jsx)(H,{bordered:!1,style:{background:"linear-gradient(135deg, #f5f3ff 0%, #e6e4ff 100%)",borderRadius:8,padding:"12px 12px 6px",height:"100%",margin:2},title:(0,I.jsxs)("span",{style:{paddingLeft:4,fontSize:14},children:[(0,I.jsx)(_.Z,{style:{marginRight:6}}),"意向客户数"]}),action:(0,I.jsx)(d.Z,{title:"近30天新增意向客户数量",children:(0,I.jsx)(Z.Z,{style:{fontSize:14}})}),total:"386",footer:(0,I.jsx)(M,{label:"转化率",value:"42%",style:{padding:"4px 0"}}),contentHeight:80,bodyStyle:{padding:"4px 0",background:"linear-gradient(to bottom, rgba(255,255,255,0.2) 0%, rgba(230,244,255,0.1) 100%)"},children:(0,I.jsx)(c.Z,{percent:42,strokeColor:{"0%":"#13c2c2","100%":"#52c41a"},strokeWidth:12,trailColor:"#f0f6ff",showInfo:!1,status:"active",style:{margin:"8px 0"}})})}),(0,I.jsx)(a.Z,{span:8,children:(0,I.jsx)(H,{bordered:!1,style:{background:"linear-gradient(135deg, #f5f3ff 0%, #e6e4ff 100%)",borderRadius:8,padding:"12px 12px 6px",height:"100%",margin:2},title:(0,I.jsxs)("span",{style:{paddingLeft:4,fontSize:14},children:[(0,I.jsx)(S.Z,{style:{marginRight:6}}),"商机覆盖区域"]}),action:(0,I.jsx)(d.Z,{title:"已覆盖的目标城市数量",children:(0,I.jsx)(Z.Z,{style:{fontSize:14}})}),total:"28个城市",footer:(0,I.jsx)(M,{label:"重点区域",value:"长三角/珠三角/京津冀",style:{padding:"4px 0"}}),contentHeight:80,bodyStyle:{padding:"4px 0",background:"linear-gradient(to bottom, rgba(255,255,255,0.2) 0%, rgba(230,244,255,0.1) 100%)"},children:(0,I.jsx)(j.Z,{height:80,data:[{x:"1月",y:15},{x:"2月",y:20},{x:"3月",y:25},{x:"4月",y:28}],xField:"x",yField:"y",smooth:!0,color:"#722ed1",areaStyle:{fill:"l(270) 0:rgba(114,46,209,0.2) 1:rgba(114,46,209,0.8)"},line:{size:3},axis:!1,tooltip:!1,animation:{appear:{duration:800}}})})})]}),(0,I.jsxs)("div",{style:{marginBottom:32},children:[(0,I.jsxs)(L,{level:4,style:{display:"flex",alignItems:"center",marginBottom:16,color:"#722ed1"},children:[(0,I.jsx)(R.Z,{style:{marginRight:8}}),"市场趋势分析"]}),(0,I.jsx)(q,{style:{marginBottom:16},children:"根据最新数据分析，大模型项目需求呈现快速增长趋势，主要集中在智慧城市、金融科技和医疗健康等领域。其中："}),(0,I.jsxs)("ul",{style:{marginBottom:16},children:[(0,I.jsx)("li",{children:"智慧城市领域占比最高，达35%"}),(0,I.jsx)("li",{children:"金融科技次之，占比28%"}),(0,I.jsx)("li",{children:"医疗健康快速增长，同比增长85%"})]}),(0,I.jsx)("div",{style:{backgroundColor:"#fafafa",borderRadius:8,padding:24,marginBottom:16},children:(0,I.jsx)(y.Z,{data:[{quarter:"Q1",value:156},{quarter:"Q2",value:228},{quarter:"Q3",value:268},{quarter:"Q4",value:289}],xField:"quarter",yField:"value",color:"#722ed1",height:240,label:{position:"middle",style:{fill:"#fff"}}})})]}),(0,I.jsxs)("div",{style:{marginBottom:32},children:[(0,I.jsxs)(L,{level:4,style:{display:"flex",alignItems:"center",marginBottom:16,color:"#722ed1"},children:[(0,I.jsx)(k.Z,{style:{marginRight:8}}),"行业应用分析"]}),(0,I.jsx)(q,{style:{marginBottom:16},children:"大模型技术应用呈现多领域渗透趋势，建议重点关注："}),(0,I.jsxs)("ul",{style:{marginBottom:16},children:[(0,I.jsx)("li",{children:"智慧城市领域需加强多模态融合能力建设"}),(0,I.jsx)("li",{children:"金融科技领域应提升实时风险预警模型精度"}),(0,I.jsx)("li",{children:"医疗健康领域要突破小样本学习技术瓶颈"})]}),(0,I.jsxs)(s.Z,{gutter:24,children:[(0,I.jsx)(a.Z,{span:12,children:(0,I.jsxs)("div",{style:{backgroundColor:"#fafafa",borderRadius:8,padding:"24px 16px 24px 16px",height:320},children:[(0,I.jsx)("h4",{style:{marginBottom:16},children:"应用场景分布"}),(0,I.jsx)(u.Z,{data:[{type:"智慧城市",value:35},{type:"金融科技",value:28},{type:"医疗健康",value:20},{type:"教育培训",value:12},{type:"其他",value:5}],angleField:"value",colorField:"type",radius:.6,innerRadius:.3,label:{type:"spider",content:"{name} {percentage}",offset:5,style:{fontSize:10,fill:"rgba(0,0,0,0.65)"}},style:{height:220}})]})}),(0,I.jsx)(a.Z,{span:12,children:(0,I.jsxs)("div",{style:{backgroundColor:"#fafafa",borderRadius:8,padding:"24px 16px",height:320},children:[(0,I.jsx)("h4",{style:{marginBottom:16},children:"需求类型分布"}),(0,I.jsx)(m.Z,{data:[{type:"平台建设",value:68},{type:"系统集成",value:52},{type:"技术服务",value:45},{type:"运维支持",value:35}],xField:"type",yField:"value",seriesField:"type",color:function(){return"#722ed1"},columnWidth:1,height:240,label:{position:"right",style:{fill:"#722ed1"},offset:10},axis:{x:{label:{autoRotate:!1}},y:{grid:{line:{style:{stroke:"#f0f0f0"}}}}}})]})})]})]}),(0,I.jsxs)("div",{style:{marginBottom:32},children:[(0,I.jsxs)(L,{level:4,style:{display:"flex",alignItems:"center",marginBottom:16,color:"#722ed1"},children:[(0,I.jsx)(b.Z,{style:{marginRight:8}}),"重点项目进展"]}),(0,I.jsx)(q,{style:{marginBottom:16},children:"基于当前项目进展，大模型实施建议："}),(0,I.jsxs)("ul",{style:{marginBottom:16},children:[(0,I.jsx)("li",{children:"建立跨部门协同的模型迭代机制（建议频率：2周/次）"}),(0,I.jsx)("li",{children:"关键项目需配置专项GPU算力资源（推荐：A100*8节点）"}),(0,I.jsx)("li",{children:"实施数据质量月检制度（合格标准：标注准确率≥98%）"})]}),(0,I.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:["智慧医疗","金融风控","教育平台"].map((function(e,i){return(0,I.jsxs)(x.Z,{size:"small",style:{borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.06)",background:0===i?"#f6ffed":"white",height:"100%"},bodyStyle:{padding:12},children:[(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:8},children:[(0,I.jsxs)("div",{children:[(0,I.jsxs)("div",{style:{fontWeight:600,color:"rgba(0,0,0,0.85)",marginBottom:4,fontSize:14},children:[e,"大模型项目"]}),(0,I.jsxs)("div",{style:{display:"flex",gap:8,color:"rgba(0,0,0,0.45)",fontSize:12},children:[(0,I.jsxs)("span",{children:["负责人：张",0===i?"伟":"敏"]}),(0,I.jsx)("span",{children:"·"}),(0,I.jsxs)("span",{children:["进度 ",25*(i+1),"%"]})]})]}),(0,I.jsx)(o.Z,{color:0===i?"success":"processing",style:{fontSize:14,padding:"2px 8px",borderRadius:4,lineHeight:"18px",height:24},children:0===i?"进行中":"筹备中"})]}),(0,I.jsx)(c.Z,{percent:25*(i+1),showInfo:!1,strokeColor:0===i?"#52c41a":"#722ed1",trailColor:"#f5f5f5"}),(0,I.jsxs)("div",{style:{margin:"8px 0"},children:[(0,I.jsx)(o.Z,{color:"#f0f6ff",style:{color:"#722ed1"},children:"NLP引擎"}),(0,I.jsx)(o.Z,{color:"#f0f6ff",style:{color:"#722ed1"},children:"知识图谱"})]})]},e)}))})]})]}),(0,I.jsx)("div",{style:{padding:"16px",background:"white",boxShadow:"0 -2px 8px rgba(0,0,0,0.06)",borderRadius:"8px"},children:(0,I.jsx)(h.Z,{value:t,onChange:l,onSubmit:function(){console.log(t)},className:f,prompts:(0,I.jsx)(p.Z,{onSelect:function(e){return l(e)},placeholder:"输入问题或使用 / 唤起快捷指令"})})})]}),(0,I.jsxs)(a.Z,{span:8,style:{height:"100%",overflowY:"auto",paddingLeft:24},children:[(0,I.jsxs)(L,{level:4,style:{marginBottom:16,display:"flex",alignItems:"center"},children:[(0,I.jsx)(b.Z,{style:{marginRight:8}}),"推荐项目"]}),(0,I.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:[1,2,3,4,5,6].map((function(e){return(0,I.jsxs)(x.Z,{size:"small",style:{borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.06)",height:"100%"},bodyStyle:{padding:16},children:[(0,I.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:12},children:[(0,I.jsxs)("div",{children:[(0,I.jsx)("div",{style:{fontWeight:600,color:"rgba(0,0,0,0.85)",marginBottom:8,fontSize:14},children:"智慧城市大模型平台建设"}),(0,I.jsxs)("div",{style:{display:"flex",gap:8,color:"rgba(0,0,0,0.45)",fontSize:12},children:[(0,I.jsx)("span",{children:"招标中"}),(0,I.jsx)("span",{children:"·"}),(0,I.jsx)("span",{children:"预算 ¥5,200,000"})]})]}),(0,I.jsx)(o.Z,{color:"processing",style:{fontSize:12,padding:"2px 8px",borderRadius:4,lineHeight:"18px",height:24,marginTop:4},children:"智慧城市"})]}),(0,I.jsxs)("div",{style:{margin:"12px 0"},children:[(0,I.jsx)(o.Z,{color:"#f0f6ff",style:{color:"#722ed1",marginRight:8},children:"NLP"}),(0,I.jsx)(o.Z,{color:"#f0f6ff",style:{color:"#722ed1"},children:"智能客服"})]}),(0,I.jsx)(q,{ellipsis:{rows:2},style:{color:"rgba(0,0,0,0.65)",fontSize:12,lineHeight:1.6,marginBottom:0},children:"建设城市级大模型平台，包含智能问答、数据分析、数字人客服等核心模块，要求支持千亿级参数模型训练..."})]},e)}))})]})]})})}}}]);