from mongoengine import Document, StringField, DateTimeField, IntField, ListField, BooleanField, Dict<PERSON>ield
from datetime import datetime
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from bson import ObjectId

# MongoEngine 模型
class Prompt(Document):
    meta = {
        'collection': 'prompts'
    }
    _id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    title = StringField(required=True)
    content = StringField(required=True)
    category = ListField(StringField(), default=list)  # 分类列表，如 ["investment", "risk", "finance"]
    user = StringField(required=True)
    user_id = IntField(required=True)
    positions = ListField(StringField(), default=list)  # 适用岗位列表
    models = ListField(StringField(), default=list)  # 支持的模型列表
    language = StringField(default="zh-CN")  # 语言字段，默认为中文
    created_at = DateTimeField(default=datetime.now)
    updated_at = DateTimeField(default=datetime.now)
    is_active = BooleanField(default=True)
    tags = ListField(StringField(), default=list)
    usage_count = IntField(default=0)  # 使用次数统计
    is_system = BooleanField(default=False)  # 是否是平台提示词，True为平台提示词，False为个人提示词
    extra = DictField()  # 额外信息字段

# Pydantic 模型
class PromptBase(BaseModel):
    title: str
    content: str
    category: List[str]
    user: str
    user_id: int
    positions: List[str] = []
    models: List[str] = []
    language: str = "zh-CN"
    tags: List[str] = []
    is_active: bool = True
    is_system: bool = False
    extra: Optional[Dict[str, Any]] = None

class PromptCreate(BaseModel):
    title: str
    content: str
    category: List[str]
    positions: List[str] = []
    models: List[str] = []
    language: str = "zh-CN"
    tags: List[str] = []
    is_system: bool = False
    extra: Optional[Dict[str, Any]] = None

class PromptUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    category: Optional[List[str]] = None
    positions: Optional[List[str]] = None
    models: Optional[List[str]] = None
    language: Optional[str] = None
    tags: Optional[List[str]] = None
    is_active: Optional[bool] = None
    is_system: Optional[bool] = None
    extra: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True
        extra = "allow"
        json_encoders = {
            bool: lambda v: bool(v) if v is not None else None
        }

class PromptResponse(BaseModel):
    id: str
    title: str
    content: str
    category: List[str]
    user: str
    user_id: int
    positions: List[str]
    models: List[str]
    language: str
    created_at: datetime
    updated_at: datetime
    is_active: bool
    tags: List[str]
    usage_count: int
    is_system: bool
    extra: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True
