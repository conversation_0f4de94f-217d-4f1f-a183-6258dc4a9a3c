

import requests
import json
from datetime import datetime
import unittest


base_url = "http://192.168.0.69:8808/api/v1/loan-analysis"  # 替换为实际的API地址
headers = {
            "Authorization": "Bearer sk-test-roarAI-123456789",
            "Content-Type": "application/json"
        }

context = '''
| 科目 | 2023年年度 | 2022年年度 | 2021年年度 |
| --- | --- | --- | --- |
| 资产 | 977855.88 | 986333.81 | 1084403.75 |
| 流动资产： | 401016.06 | 405283.77 | 483245.73 |
| 货币资金 | 150489.28 | 134499.12 | 156705.05 |
| 交易性金融资产 | 15.27 | 98.14 | 0 |
| 衍生金融资产 | - | - | - |
| 应收票据 | 41424.37 | 57118.75 | 70592.75 |
| 应收账款 | 74729.01 | 63327.11 | 73254.55 |
| 应收款项融资 | 2184.53 | 6486.54 | 38002.44 |
| 预付款项 | 6908.78 | 8864.32 | 7422.2 |
| 其他应收款 | 4872.13 | 5476.22 | 5471.53 |
| 存货 | 105131.25 | 122087.19 | 118744.37 |
| 合同资产 | 0 | 0 | 0 |
| 持有待售资产 | 0 | 0 | 0 |
| 一年内到期的非流动资产 | 0 | 0 | 0 |
| 其他流动资产 | 15261.43 | 7326.37 | 13052.84 |
| 流动资产合计 | 401016.06 | 405283.77 | 483245.73 |
| 非流动资产： | 576839.81 | 581050.05 | 601158.02 |
| 债权投资 | 0 | 0 | 0 |
| 其他债权投资 | 0 | 0 | 0 |
| 长期应收款 | 0 | 0 | 0 |
| 长期股权投资 | 83815.88 | 71645.61 | 66119.71 |
| 其他权益工具投资 | 60.78 | 1237.14 | 2855.8 |
| 其他非流动金融资产 | 0 | 0 | 0 |
| 投资性房地产 | 0 | 0 | 0 |
| 固定资产 | 370751.64 | 427505.23 | 435971.7 |
| 在建工程 | 60526.01 | 25020.19 | 35611.84 |
| 生产性生物资产 | 0 | 0 | 0 |
| 油气资产 | 0 | 0 | 0 |
| 使用权资产 | 0 | 573.13 | 617.26 |
| 无形资产 | 39912.84 | 38394.37 | 40285.89 |
| 开发支出 | 0 | 0 | 0 |
| 商誉 | 0 | 0 | 0 |
| 长期待摊费用 | 1223.98 | 189.54 | 86.27 |
| 递延所得税资产 | 11219.4 | 13503.89 | 13926.78 |
| 其他非流动资产 | 9329.29 | 2980.96 | 5645.77 |
| 非流动资产合计 | 576839.81 | 581050.05 | 601158.02 |
| 资产总计 | 977855.88 | 986333.81 | 1084403.75 |
| 负债和所有者权益 | - | - | - |
| 流动负债： | 597629.08 | 651399.36 | 638263.05 |
| 短期借款 | 371771.62 | 390369.9 | 375451.75 |
| 交易性金融负债 | 15.89 | 0 | 0 |
| 衍生金融负债 | 0 | 0 | 0 |
| 应付票据 | 20611.31 | 52617.95 | 60182.83 |
| 应付账款 | 92375.93 | 84817.01 | 93013.22 |
| 预收款项 | 192.49 | 181.17 | 334.26 |
| 合同负债 | 11974.06 | 15651.22 | 12138.82 |
| 应付职工薪酬 | 8745.78 | 10651.77 | 7936.52 |
| 应交税费 | 1054.97 | 825.67 | 666.59 |
| 其他应付款 | 17335.62 | 17346.92 | 17389.68 |
| 持有待售负债 | 0 | 0 | 0 |
| 一年内到期的非流动负债 | 13721.24 | 43494.54 | 44570.58 |
| 其他流动负债 | 59830.17 | 35443.23 | 26578.8 |
| 流动负债合计 | 597629.08 | 651399.36 | 638263.05 |
| 非流动负债： | 171913.37 | 96629.74 | 133095.11 |
| 长期借款 | 135775 | 48651.63 | 83654.43 |
| 应付债券 | 0 | 0 | 0 |
| 其中：优先股（应付债券） | - | - | - |
| 永续债（应付债券） | - | - | - |
| 租赁负债 | 0 | 57.45 | 204.04 |
| 长期应付款 | 3525.52 | 5794.13 | 5204.79 |
| 预计负债 | 0 | 0 | 0 |
| 递延收益 | 0 | 0 | 0 |
| 递延所得税负债 | 244.54 | 378.99 | 538.73 |
| 其他非流动负债 | 0 | 0 | 0 |
| 非流动负债合计 | 171913.37 | 96629.74 | 133095.11 |
| 负债合计 | 769542.45 | 748029.1 | 771358.15 |
| 所有者权益（或股东权益）： | - | - | - |
| 实收资本（或股本） | 81675.9 | 81679.25 | 81679.25 |
| 其他权益工具 | 0 | 0 | 0 |
| 其中：优先股（其他权益工具） | 0 | 0 | 0 |
| 永续债（其他权益工具） | 0 | 0 | 0 |
| 资本公积 | 182508.98 | 192077.42 | 193980.27 |
| 减：库存股 | 0 | 10.42 | 10.42 |
| 其他综合收益 | 5940.97 | 4665.03 | -974.37 |
| 专项储备 | 0 | 0 | 0 |
| 盈余公积 | 0 | 0 | 0 |
| 未分配利润 | -63056.83 | -45442.33 | 15567.3 |
| 归属于母公司股东权益合计 | 213442.66 | 239342.59 | 296615.67 |
| 少数股东权益 | -5129.23 | -1037.87 | 16429.93 |
| 所有者权益（或股东权益）合计 | 208313.43 | 238304.71 | 313045.6 |
| 负债和所有者权益（或股东权益）总计 | 977855.88 | 986333.81 | 1084403.75 |
| 一、营业收入 | 465550.02 | 391039.77 | 392452.1 |
| 减：营业成本 | 425350.09 | 385906.18 | 378998.86 |
| 税金及附加 | 3655.44 | 2502.32 | 2580.17 |
| 销售费用 | 17317.43 | 15578.94 | 17657.37 |
| 管理费用 | 17637.38 | 16860.92 | 19951.3 |
| 研发费用 | 20423.88 | 20238.91 | 20303.55 |
| 财务费用 | 17340.18 | 16556.71 | 16219.51 |
| 其中：利息费用 | 19202.04 | 18139.74 | 16753.74 |
| 利息收入 | 0 | 0 | 0 |
| 加：其他收益 | 10561.76 | 6256.43 | 11795.79 |
| 投资收益（损失以“-”号填列） | 16193.47 | 4088.78 | 20751 |
| 其中：对联营企业和合营企业的投资收益 | 13192.89 | 2232.58 | 188.25 |
| 以摊余成本计量的金融资产终止确认收益（损失以“-”号填列） | 0 | 0 | 0 |
| 净敞口套期收益（损失以“-”号填列） | 0 | 0 | 0 |
| 公允价值变动收益（损失以“-”号填列） | -1190.22 | -2808.03 | -666.55 |
| 信用减值损失（损失以“-”号填列） | -4471.29 | -342.45 | -1185.36 |
| 资产减值损失（损失以“-”号填列） | -6505.1 | -9801.88 | -4582.48 |
| 资产处置收益（损失以“-”号填列） | 44.65 | 462.48 | 220.75 |
| 二、营业利润（亏损以“-”号填列） | -21541.1 | -68748.86 | -36925.5 |
| 加：营业外收入 | 403.84 | 91.16 | 42.18 |
| 减：营业外支出 | 188.9 | 34.26 | 148.69 |
| 三、利润总额（亏损总额以“-”号填列） | -21326.16 | -68691.95 | -37032.01 |
| 减：所得税费用 | 2151.69 | 493.15 | 2453.39 |
| 四、净利润（净亏损以“-”号填列） | -23477.85 | -69185.1 | -39485.4 |
| （一）按经营持续性分类： | - | - | - |
| 其中：持续经营净利润（净亏损以“-”号填列） | -23477.85 | -70246.38 | -58283.17 |
| 终止经营净利润（净亏损以“-”号填列） | 0 | 1061.28 | 18797.77 |
| （二）按所有权归属分类： | - | - | - |
| 其中：少数股东损益（净亏损以“-”号填列） | -5863.36 | -8991.87 | -7455.22 |
| 归属于母公司股东的净利润（净亏损以“-”号填列） | -17614.5 | -60193.23 | -32030.18 |
| 五、其他综合收益税后净额 | 1242.58 | 5639.46 | -1016.25 |
| （一）不能重分类进损益的其他综合收益 | - | - | - |
| 1.重新计量设定受益计划变动额 | - | - | - |
| 2.权益法下不能转损益的其他综合收益 | - | - | - |
| 3.其他权益工具投资公允价值变动 | - | - | - |
| 4.企业自身信用风险公允价值变动 | - | - | - |
| （二）将重分类进损益的其他综合收益 | - | - | - |
| 1.权益法下可转损益的其他综合收益 | - | - | - |
| 2.其他债权投资公允价值变动 | - | - | - |
| 3.金融资产重分类计入其他综合收益的金额 | - | - | - |
| 4.其他债权投资信用损失准备 | - | - | - |
| 5.现金流量套期储备 | - | - | - |
| 6.外币财务报表折算差额 | - | - | - |
| 六、综合收益总额 | -22235.28 | -63545.64 | -40501.65 |
| 七、每股收益： | - | - | - |
| （一）基本每股收益 | 0 | 0 | 0 |
| （二）稀释每股收益 | 0 | 0 | 0 |
| 一、经营活动产生的现金流量： | 28033.43 | 33673.64 | -8658.22 |
| 销售商品、提供劳务收到的现金 | 385127.57 | 312084.17 | 246986.44 |
| 收到的税费返还 | 8169.84 | 19861.06 | 20698.91 |
| 收到的其他与经营活动有关的现金 | 20071.12 | 17526.3 | 22538.14 |
| 经营活动现金流入小计 | 413368.53 | 349471.53 | 290223.48 |
| 购买商品、接受劳务支付的现金 | 295317.56 | 233154.26 | 203485.47 |
| 支付给职工以及为职工支付的现金 | 61726.28 | 54653.29 | 66945.49 |
| 支付的各项税费 | 7529.95 | 3227.37 | 4235.11 |
| 支付的其他与经营活动有关的现金 | 20761.3 | 24762.97 | 24215.64 |
| 经营活动现金流出小计 | 385335.1 | 315797.89 | 298881.71 |
| 经营活动产生的现金流量净额 | 28033.43 | 33673.64 | -8658.22 |
| 二、投资活动产生的现金流量： | -40397.6 | -13848.22 | -42030.92 |
| 收回投资所收到的现金 | 1287.08 | 1836.95 | 28114.97 |
| 取得投资收益所收到的现金 | 26.93 | 9.27 | 424.47 |
| 处置固定资产、无形资产和其他长期资产所收回的现金净额 | 1.86 | 533.76 | 5454.87 |
| 处置子公司及其他营业单位收到的现金净额 | 5796.94 | 0 | 32560.48 |
| 收到的其他与投资活动有关的现金 | 0 | 0 | 0 |
| 投资活动现金流入小计 | 7112.81 | 2379.98 | 66554.79 |
| 购建固定资产、无形资产和其他长期资产所支付的现金 | 24263.67 | 16228.19 | 17451.72 |
| 投资所支付的现金 | 21947 | 0 | 91003.52 |
| 取得子公司及其他营业单位支付的现金净额 | 0 | 0 | 130.47 |
| 支付的其他与投资活动有关的现金 | 1299.74 | 0 | 0 |
| 投资活动现金流出小计 | 47510.41 | 16228.19 | 108585.71 |
| 投资活动产生的现金流量净额 | -40397.6 | -13848.22 | -42030.92 |
| 三、筹资活动产生的现金流量： | 3957.1 | -43949.06 | 78046.34 |
| 吸收投资所收到的现金 | 5872.45 | 0 | 5215 |
| 其中：子公司吸收少数股东权益性投资收到的现金 | 5872.45 | 0 | 5215 |
| 取得借款收到的现金 | 495965.81 | 489524.67 | 478244.95 |
| 收到的其他与筹资活动有关的现金 | 2222.68 | 7770.09 | 10985.27 |
| 筹资活动现金流入小计 | 504060.93 | 497294.75 | 494445.22 |
| 偿还债务支付的现金 | 462728.85 | 501729.27 | 392843.27 |
| 分配股利、利润或偿付利息支付的现金 | 14929.04 | 16457.74 | 15848.28 |
| 其中：子公司支付少数股东的现金股利 | 0 | 0 | 0 |
| 支付其他与筹资活动有关的现金 | 22445.94 | 23056.81 | 7707.33 |
| 其中：子公司减资支付给少数股东的现金 | 0 | 0 | 0 |
| 筹资活动现金流出小计 | 500103.83 | 541243.82 | 416398.88 |
| 筹资活动产生的现金流量净额 | 3957.1 | -43949.06 | 78046.34 |
| 四、汇率变动对现金及现金等价物的影响 | 318.41 | 1407.06 | -2074.74 |
| 五、现金及现金等价物净增加额 | -8088.66 | -22716.58 | 25282.45 |
| 加：期初现金及现金等价物余额 | 104632.31 | 127348.89 | 102066.44 |
| 六、期末现金及现金等价物余额 | 96543.65 | 104632.31 | 127348.89 |

'''


# ... existing code ...

# 修改 data 中的 stream 参数为 True
data = {
    'enterpriseId': "913702002646064362",
    'dimensions': ['FINANCIAL_STATUS'],
    'executionMode': "SYNC", 
    'outputFormat': "MARKDOWN",
    'stream': True,  # 改为 True 启用流式输出
    'context': {
        'FINANCIAL_STATUS': context
    }
}

# 修改请求处理部分
response = requests.post(
    base_url,
    headers=headers,
    json=data,
    stream=True  # 启用流式响应
)

# 处理流式响应
if response.status_code == 200:
    for line in response.iter_lines():
        if line:
            # 解码并打印每行数据
            decoded_line = line.decode('utf-8')
            # print(decoded_line)
            try:
                # json_data = json.loads(decoded_line)
                print(decoded_line)
                # 如果是字符串，直接打印
                # if isinstance(json_data, str):
                    # print(json_data.encode().decode('unicode_escape'))
                # 如果是字典，处理其中的字符串值
                # elif isinstance(json_data, dict):
                    # for key, value in json_data.items():
                        # if isinstance(value, str):
                            # json_data[key] = value.encode().decode('unicode_escape')
                    # print(json_data)
            except json.JSONDecodeError:
                # 对于非JSON格式的行，尝试解码Unicode转义序列
                print(decoded_line.encode().decode('unicode_escape'))
else:
    print(f"Error: {response.status_code}")
    print(response.text)












"""无数据"""



import requests
import json
from datetime import datetime
import unittest


base_url = "http://192.168.0.69:8808/api/v1/loan-analysis"  # 替换为实际的API地址
headers = {
            "Authorization": "Bearer sk-test-roarAI-123456789",
            "Content-Type": "application/json"
        }

context = ''


# ... existing code ...

# 修改 data 中的 stream 参数为 True
data = {
    'enterpriseId': "913702002646064362",
    'dimensions': ['FINANCIAL_STATUS'],
    'executionMode': "SYNC", 
    'outputFormat': "MARKDOWN",
    'stream': True,  # 改为 True 启用流式输出
    'context': {
        'FINANCIAL_STATUS': context
    }
}

# 修改请求处理部分
response = requests.post(
    base_url,
    headers=headers,
    json=data,
    stream=True  # 启用流式响应
)

# 处理流式响应
if response.status_code == 200:
    for line in response.iter_lines():
        if line:
            # 解码并打印每行数据
            decoded_line = line.decode('utf-8')
            # print(decoded_line)
            try:
                # json_data = json.loads(decoded_line)
                print(decoded_line)
                # 如果是字符串，直接打印
                # if isinstance(json_data, str):
                    # print(json_data.encode().decode('unicode_escape'))
                # 如果是字典，处理其中的字符串值
                # elif isinstance(json_data, dict):
                    # for key, value in json_data.items():
                        # if isinstance(value, str):
                            # json_data[key] = value.encode().decode('unicode_escape')
                    # print(json_data)
            except json.JSONDecodeError:
                # 对于非JSON格式的行，尝试解码Unicode转义序列
                print(decoded_line.encode().decode('unicode_escape'))
else:
    print(f"Error: {response.status_code}")
    print(response.text)
