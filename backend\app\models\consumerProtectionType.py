from mongoengine import Document, <PERSON><PERSON>ield, ListField, DateTimeField, BooleanField, IntField
from datetime import datetime
from pydantic import BaseModel
from typing import Optional, List

class ConsumerProtectionType(Document):
    meta = {
        'collection': 'consumer_protection_types'
    }
    name = StringField(required=True)               # 类型名称
    description = StringField()                     # 描述
    rules = ListField(StringField())                # 关联的规则ID列表
    is_active = BooleanField(default=True)          # 是否激活
    user_id = IntField(required=True)               # 创建用户ID
    user_name = StringField(required=True)          # 创建用户名
    created_at = DateTimeField(default=datetime.now)  # 创建时间
    updated_at = DateTimeField(default=datetime.now)  # 更新时间
    is_deleted = BooleanField(default=False)        # 是否已删除（软删除标记）

# 定义请求和响应模型
class ConsumerProtectionTypeCreate(BaseModel):
    name: str
    description: Optional[str] = None
    is_active: bool = True
    rules: List[str] = []

class ConsumerProtectionTypeUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    rules: Optional[List[str]] = None

class ConsumerProtectionTypeResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    rules: List[str] = []
    rules_count: int = 0
    is_active: bool = True
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
