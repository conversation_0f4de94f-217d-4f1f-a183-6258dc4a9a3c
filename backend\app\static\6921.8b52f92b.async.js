(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6921],{82947:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"}},52197:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"}}]},name:"star",theme:"filled"}},92287:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"}},36027:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var n=r(1413),o=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M917.7 148.8l-42.4-42.4c-1.6-1.6-3.6-2.3-5.7-2.3s-4.1.8-5.7 2.3l-76.1 76.1a199.27 199.27 0 00-112.1-34.3c-51.2 0-102.4 19.5-141.5 58.6L432.3 308.7a8.03 8.03 0 000 11.3L704 591.7c1.6 1.6 3.6 2.3 5.7 2.3 2 0 4.1-.8 5.7-2.3l101.9-101.9c68.9-69 77-175.7 24.3-253.5l76.1-76.1c3.1-3.2 3.1-8.3 0-11.4zM769.1 441.7l-59.4 59.4-186.8-186.8 59.4-59.4c24.9-24.9 58.1-38.7 93.4-38.7 35.3 0 68.4 13.7 93.4 38.7 24.9 24.9 38.7 58.1 38.7 93.4 0 35.3-13.8 68.4-38.7 93.4zm-190.2 105a8.03 8.03 0 00-11.3 0L501 613.3 410.7 523l66.7-66.7c3.1-3.1 3.1-8.2 0-11.3L441 408.6a8.03 8.03 0 00-11.3 0L363 475.3l-43-43a7.85 7.85 0 00-5.7-2.3c-2 0-4.1.8-5.7 2.3L206.8 534.2c-68.9 69-77 175.7-24.3 253.5l-76.1 76.1a8.03 8.03 0 000 11.3l42.4 42.4c1.6 1.6 3.6 2.3 5.7 2.3s4.1-.8 5.7-2.3l76.1-76.1c33.7 22.9 72.9 34.3 112.1 34.3 51.2 0 102.4-19.5 141.5-58.6l101.9-101.9c3.1-3.1 3.1-8.2 0-11.3l-43-43 66.7-66.7c3.1-3.1 3.1-8.2 0-11.3l-36.6-36.2zM441.7 769.1a131.32 131.32 0 01-93.4 38.7c-35.3 0-68.4-13.7-93.4-38.7a131.32 131.32 0 01-38.7-93.4c0-35.3 13.7-68.4 38.7-93.4l59.4-59.4 186.8 186.8-59.4 59.4z"}}]},name:"api",theme:"outlined"},c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a}))};var s=o.forwardRef(l)},28508:function(e,t,r){"use strict";var n=r(1413),o=r(67294),a=r(89503),c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},s=o.forwardRef(l);t.Z=s},85175:function(e,t,r){"use strict";var n=r(1413),o=r(67294),a=r(48820),c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},s=o.forwardRef(l);t.Z=s},82061:function(e,t,r){"use strict";var n=r(1413),o=r(67294),a=r(47046),c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},s=o.forwardRef(l);t.Z=s},34804:function(e,t,r){"use strict";var n=r(1413),o=r(67294),a=r(66023),c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},s=o.forwardRef(l);t.Z=s},69753:function(e,t,r){"use strict";var n=r(1413),o=r(67294),a=r(49495),c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},s=o.forwardRef(l);t.Z=s},47389:function(e,t,r){"use strict";var n=r(1413),o=r(67294),a=r(27363),c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},s=o.forwardRef(l);t.Z=s},4161:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var n=r(1413),o=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM528.1 472h-32.2c-5.5 0-10.3 3.7-11.6 9.1L434.6 680l-46.1-198.7c-1.3-5.4-6.1-9.3-11.7-9.3h-35.4a12.02 12.02 0 00-11.6 15.1l74.2 276c1.4 5.2 6.2 8.9 11.6 8.9h32c5.4 0 10.2-3.6 11.6-8.9l52.8-197 52.8 197c1.4 5.2 6.2 8.9 11.6 8.9h31.8c5.4 0 10.2-3.6 11.6-8.9l74.4-276a12.04 12.04 0 00-11.6-15.1H647c-5.6 0-10.4 3.9-11.7 9.3l-45.8 199.1-49.8-199.3c-1.3-5.4-6.1-9.1-11.6-9.1z"}}]},name:"file-word",theme:"outlined"},c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a}))};var s=o.forwardRef(l)},12906:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var n=r(1413),o=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM512 533c-85.5 0-155.6 67.3-160 151.6a8 8 0 008 8.4h48.1c4.2 0 7.8-3.2 8.1-7.4C420 636.1 461.5 597 512 597s92.1 39.1 95.8 88.6c.3 4.2 3.9 7.4 8.1 7.4H664a8 8 0 008-8.4C667.6 600.3 597.5 533 512 533z"}}]},name:"frown",theme:"outlined"},c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a}))};var s=o.forwardRef(l)},43471:function(e,t,r){"use strict";var n=r(1413),o=r(67294),a=r(82947),c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},s=o.forwardRef(l);t.Z=s},25820:function(e,t,r){"use strict";var n=r(1413),o=r(67294),a=r(52197),c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},s=o.forwardRef(l);t.Z=s},75750:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var n=r(1413),o=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a}))};var s=o.forwardRef(l)},87784:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var n=r(1413),o=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"},c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a}))};var s=o.forwardRef(l)},98165:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var n=r(1413),o=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"},c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a}))};var s=o.forwardRef(l)},64029:function(e,t,r){"use strict";var n=r(1413),o=r(67294),a=r(92287),c=r(91146),l=function(e,t){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},s=o.forwardRef(l);t.Z=s},26058:function(e,t,r){"use strict";r.d(t,{Z:function(){return x}});var n=r(74902),o=r(67294),a=r(93967),c=r.n(a),l=r(98423),s=r(53124),i=r(82401),u=r(50344),f=r(70985);var d=r(24793),g=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};function m(e){let{suffixCls:t,tagName:r,displayName:n}=e;return e=>o.forwardRef(((n,a)=>o.createElement(e,Object.assign({ref:a,suffixCls:t,tagName:r},n))))}const p=o.forwardRef(((e,t)=>{const{prefixCls:r,suffixCls:n,className:a,tagName:l}=e,i=g(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:u}=o.useContext(s.E_),f=u("layout",r),[m,p,h]=(0,d.ZP)(f),b=n?`${f}-${n}`:f;return m(o.createElement(l,Object.assign({className:c()(r||b,a,p,h),ref:t},i)))})),h=o.forwardRef(((e,t)=>{const{direction:r}=o.useContext(s.E_),[a,m]=o.useState([]),{prefixCls:p,className:h,rootClassName:b,children:v,hasSider:C,tagName:Z,style:y}=e,x=g(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),w=(0,l.Z)(x,["suffixCls"]),{getPrefixCls:S,className:k,style:O}=(0,s.dj)("layout"),$=S("layout",p),E=function(e,t,r){return"boolean"==typeof r?r:!!e.length||(0,u.Z)(t).some((e=>e.type===f.Z))}(a,v,C),[N,z,j]=(0,d.ZP)($),L=c()($,{[`${$}-has-sider`]:E,[`${$}-rtl`]:"rtl"===r},k,h,b,z,j),P=o.useMemo((()=>({siderHook:{addSider:e=>{m((t=>[].concat((0,n.Z)(t),[e])))},removeSider:e=>{m((t=>t.filter((t=>t!==e))))}}})),[]);return N(o.createElement(i.V.Provider,{value:P},o.createElement(Z,Object.assign({ref:t,className:L,style:Object.assign(Object.assign({},O),y)},w),v)))})),b=m({tagName:"div",displayName:"Layout"})(h),v=m({suffixCls:"header",tagName:"header",displayName:"Header"})(p),C=m({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(p),Z=m({suffixCls:"content",tagName:"main",displayName:"Content"})(p);const y=b;y.Header=v,y.Footer=C,y.Content=Z,y.Sider=f.Z,y._InternalSiderContext=f.D;var x=y},66309:function(e,t,r){"use strict";r.d(t,{Z:function(){return z}});var n=r(67294),o=r(93967),a=r.n(o),c=r(98423),l=r(98787),s=r(69760),i=r(96159),u=r(45353),f=r(53124),d=r(11568),g=r(15063),m=r(14747),p=r(83262),h=r(83559);const b=e=>{const{lineWidth:t,fontSizeIcon:r,calc:n}=e,o=e.fontSizeSM;return(0,p.IX)(e,{tagFontSize:o,tagLineHeight:(0,d.bf)(n(e.lineHeightSM).mul(o).equal()),tagIconSize:n(r).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new g.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var C=(0,h.I$)("Tag",(e=>(e=>{const{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:n,componentCls:o,calc:a}=e,c=a(n).sub(r).equal(),l=a(t).sub(r).equal();return{[o]:Object.assign(Object.assign({},(0,m.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,d.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:c}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(b(e))),v),Z=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};const y=n.forwardRef(((e,t)=>{const{prefixCls:r,style:o,className:c,checked:l,onChange:s,onClick:i}=e,u=Z(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:d,tag:g}=n.useContext(f.E_),m=d("tag",r),[p,h,b]=C(m),v=a()(m,`${m}-checkable`,{[`${m}-checkable-checked`]:l},null==g?void 0:g.className,c,h,b);return p(n.createElement("span",Object.assign({},u,{ref:t,style:Object.assign(Object.assign({},o),null==g?void 0:g.style),className:v,onClick:e=>{null==s||s(!l),null==i||i(e)}})))}));var x=y,w=r(98719);var S=(0,h.bk)(["Tag","preset"],(e=>(e=>(0,w.Z)(e,((t,r)=>{let{textColor:n,lightBorderColor:o,lightColor:a,darkColor:c}=r;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:n,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(b(e))),v);const k=(e,t,r)=>{const n="string"!=typeof(o=r)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var O=(0,h.bk)(["Tag","status"],(e=>{const t=b(e);return[k(t,"success","Success"),k(t,"processing","Info"),k(t,"error","Error"),k(t,"warning","Warning")]}),v),$=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};const E=n.forwardRef(((e,t)=>{const{prefixCls:r,className:o,rootClassName:d,style:g,children:m,icon:p,color:h,onClose:b,bordered:v=!0,visible:Z}=e,y=$(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:x,direction:w,tag:k}=n.useContext(f.E_),[E,N]=n.useState(!0),z=(0,c.Z)(y,["closeIcon","closable"]);n.useEffect((()=>{void 0!==Z&&N(Z)}),[Z]);const j=(0,l.o2)(h),L=(0,l.yT)(h),P=j||L,M=Object.assign(Object.assign({backgroundColor:h&&!P?h:void 0},null==k?void 0:k.style),g),R=x("tag",r),[B,I,H]=C(R),A=a()(R,null==k?void 0:k.className,{[`${R}-${h}`]:P,[`${R}-has-color`]:h&&!P,[`${R}-hidden`]:!E,[`${R}-rtl`]:"rtl"===w,[`${R}-borderless`]:!v},o,d,I,H),T=e=>{e.stopPropagation(),null==b||b(e),e.defaultPrevented||N(!1)},[,_]=(0,s.Z)((0,s.w)(e),(0,s.w)(k),{closable:!1,closeIconRender:e=>{const t=n.createElement("span",{className:`${R}-close-icon`,onClick:T},e);return(0,i.wm)(e,t,(e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),T(t)},className:a()(null==e?void 0:e.className,`${R}-close-icon`)})))}}),F="function"==typeof y.onClick||m&&"a"===m.type,W=p||null,q=W?n.createElement(n.Fragment,null,W,m&&n.createElement("span",null,m)):m,D=n.createElement("span",Object.assign({},z,{ref:t,className:A,style:M}),q,_,j&&n.createElement(S,{key:"preset",prefixCls:R}),L&&n.createElement(O,{key:"status",prefixCls:R}));return B(F?n.createElement(u.Z,{component:"Tag"},D):D)})),N=E;N.CheckableTag=x;var z=N},64599:function(e,t,r){var n=r(96263);e.exports=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=n(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var o=0,a=function(){};return{s:a,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var c,l=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){s=!0,c=e},f:function(){try{l||null==r.return||r.return()}finally{if(s)throw c}}}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);