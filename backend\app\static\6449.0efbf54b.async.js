"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6449],{97302:function(e,r,t){var n=t(1413),o=t(67294),a=t(49842),c=t(91146),i=function(e,r){return o.createElement(c.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:a.Z}))},l=o.forwardRef(i);r.Z=l},26544:function(e,r,t){t.d(r,{Jr:function(){return b},Vv:function(){return d},Xf:function(){return g},ao:function(){return u},cO:function(){return v},v5:function(){return l},yB:function(){return y}});var n=t(15009),o=t.n(n),a=t(99289),c=t.n(a),i=t(35312);function l(e){return s.apply(this,arguments)}function s(){return(s=c()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.request)("/api/media_insights_reports",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function u(e){return p.apply(this,arguments)}function p(){return(p=c()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.request)("/api/media_insights_reports",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function d(e){return f.apply(this,arguments)}function f(){return(f=c()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.request)("/api/media_insights_reports/".concat(r),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function g(e,r){return h.apply(this,arguments)}function h(){return(h=c()(o()().mark((function e(r,t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.request)("/api/media_insights_reports/".concat(r),{method:"PUT",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e){return m.apply(this,arguments)}function m(){return(m=c()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.request)("/api/media_insights_reports/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(e){return C.apply(this,arguments)}function C(){return(C=c()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.request)("/api/delete_media_insights_reports",{method:"DELETE",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function v(e,r){return k.apply(this,arguments)}function k(){return(k=c()(o()().mark((function e(r,t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.request)("/api/media_insights_report_download/".concat(r),{method:"GET",responseType:"blob"}).then((function(e){var r,n=null===(r=e.headers)||void 0===r?void 0:r.get("content-disposition"),o=t||"report.pdf";if(!t&&n){var a=n.match(/filename\*=UTF-8''([^;]+)/i);if(a)o=decodeURIComponent(a[1]);else{var c=n.match(/filename="([^"]+)"/i);c&&(o=c[1])}}var i=new Blob([e],{type:e.type}),l=window.URL.createObjectURL(i),s=document.createElement("a");return s.href=l,s.download=o,document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(l),document.body.removeChild(s),e})));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},66309:function(e,r,t){t.d(r,{Z:function(){return j}});var n=t(67294),o=t(93967),a=t.n(o),c=t(98423),i=t(98787),l=t(69760),s=t(96159),u=t(45353),p=t(53124),d=t(11568),f=t(15063),g=t(14747),h=t(83262),b=t(83559);const m=e=>{const{lineWidth:r,fontSizeIcon:t,calc:n}=e,o=e.fontSizeSM;return(0,h.IX)(e,{tagFontSize:o,tagLineHeight:(0,d.bf)(n(e.lineHeightSM).mul(o).equal()),tagIconSize:n(t).sub(n(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},y=e=>({defaultBg:new f.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var C=(0,b.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:t,tagPaddingHorizontal:n,componentCls:o,calc:a}=e,c=a(n).sub(t).equal(),i=a(r).sub(t).equal();return{[o]:Object.assign(Object.assign({},(0,g.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,d.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:i,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:c}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(m(e))),y),v=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)r.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(t[n[o]]=e[n[o]])}return t};const k=n.forwardRef(((e,r)=>{const{prefixCls:t,style:o,className:c,checked:i,onChange:l,onClick:s}=e,u=v(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:d,tag:f}=n.useContext(p.E_),g=d("tag",t),[h,b,m]=C(g),y=a()(g,`${g}-checkable`,{[`${g}-checkable-checked`]:i},null==f?void 0:f.className,c,b,m);return h(n.createElement("span",Object.assign({},u,{ref:r,style:Object.assign(Object.assign({},o),null==f?void 0:f.style),className:y,onClick:e=>{null==l||l(!i),null==s||s(e)}})))}));var w=k,$=t(98719);var x=(0,b.bk)(["Tag","preset"],(e=>(e=>(0,$.Z)(e,((r,t)=>{let{textColor:n,lightBorderColor:o,lightColor:a,darkColor:c}=t;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:n,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(m(e))),y);const O=(e,r,t)=>{const n="string"!=typeof(o=t)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${t}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,b.bk)(["Tag","status"],(e=>{const r=m(e);return[O(r,"success","Success"),O(r,"processing","Info"),O(r,"error","Error"),O(r,"warning","Warning")]}),y),E=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)r.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(t[n[o]]=e[n[o]])}return t};const T=n.forwardRef(((e,r)=>{const{prefixCls:t,className:o,rootClassName:d,style:f,children:g,icon:h,color:b,onClose:m,bordered:y=!0,visible:v}=e,k=E(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:w,direction:$,tag:O}=n.useContext(p.E_),[T,_]=n.useState(!0),j=(0,c.Z)(k,["closeIcon","closable"]);n.useEffect((()=>{void 0!==v&&_(v)}),[v]);const P=(0,i.o2)(b),B=(0,i.yT)(b),I=P||B,N=Object.assign(Object.assign({backgroundColor:b&&!I?b:void 0},null==O?void 0:O.style),f),q=w("tag",t),[L,R,z]=C(q),Z=a()(q,null==O?void 0:O.className,{[`${q}-${b}`]:I,[`${q}-has-color`]:b&&!I,[`${q}-hidden`]:!T,[`${q}-rtl`]:"rtl"===$,[`${q}-borderless`]:!y},o,d,R,z),H=e=>{e.stopPropagation(),null==m||m(e),e.defaultPrevented||_(!1)},[,U]=(0,l.Z)((0,l.w)(e),(0,l.w)(O),{closable:!1,closeIconRender:e=>{const r=n.createElement("span",{className:`${q}-close-icon`,onClick:H},e);return(0,s.wm)(e,r,(e=>({onClick:r=>{var t;null===(t=null==e?void 0:e.onClick)||void 0===t||t.call(e,r),H(r)},className:a()(null==e?void 0:e.className,`${q}-close-icon`)})))}}),F="function"==typeof k.onClick||g&&"a"===g.type,D=h||null,M=D?n.createElement(n.Fragment,null,D,g&&n.createElement("span",null,g)):g,W=n.createElement("span",Object.assign({},j,{ref:r,className:Z,style:N}),M,U,P&&n.createElement(x,{key:"preset",prefixCls:q}),B&&n.createElement(S,{key:"status",prefixCls:q}));return L(F?n.createElement(u.Z,{component:"Tag"},W):W)})),_=T;_.CheckableTag=w;var j=_}}]);