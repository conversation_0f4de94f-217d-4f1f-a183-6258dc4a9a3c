from mongoengine import Document, <PERSON><PERSON><PERSON>,ObjectId<PERSON>ield, DateTimeField, <PERSON>Field
from datetime import datetime
from pydantic import BaseModel
from typing import Optional, List
from bson import ObjectId

class MediaInsightsReport(Document):
    meta = {
        'collection': 'media_insights_reports'
    }
    _id = ObjectIdField(primary_key=True, default=lambda: ObjectId())
    title = StringField(required=True)  # 报告标题
    content = StringField(required=True)  # Markdown格式内容
    storage_path = StringField(required=True)  # 文件保存路径
    created_at = DateTimeField(default=datetime.now)  # 创建时间
    report_type = StringField(required=True)  # 报告类型
    start_time = DateTimeField(required=True)  # 数据开始时间
    end_time = DateTimeField(required=True)  # 数据结束时间
    media_ids = ListField(StringField(), default=list)  # 媒体ID列表

# Pydantic 模型
class MediaInsightsReportBase(BaseModel):
    id: str
    title: str
    content: str
    storage_path: str
    created_at: datetime
    report_type: str
    start_time: datetime
    end_time: datetime
    media_ids: List[str]

class MediaInsightsReportCreate(BaseModel):
    title: str
    content: str
    storage_path: str
    report_type: str
    start_time: datetime
    end_time: datetime
    media_ids: List[str]

class MediaInsightsReportUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    storage_path: Optional[str] = None
    report_type: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    media_ids: Optional[List[str]] = None

class MediaInsightsReportResponse(BaseModel):
    id: str
    title: str
    content: str
    storage_path: str
    created_at: datetime
    report_type: str
    start_time: datetime
    end_time: datetime
    media_ids: List[str]

    class Config:
        from_attributes = True
