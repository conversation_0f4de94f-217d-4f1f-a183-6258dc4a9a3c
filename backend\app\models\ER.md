erDiagram
    KnowledgeBase ||--o{ SourceFile : contains
    KnowledgeBase ||--o{ Chunk : contains
    KnowledgeBase ||--o{ ChunkIndex : contains
    KnowledgeBase ||--o{ EmbeddingModel : uses
    KnowledgeBase ||--o{ RerankModel : uses
    
    SourceFile ||--o{ Chunk : generates
    SourceFile ||--o{ ChunkIndex : generates
    
    Chunk ||--o{ ChunkIndex : has

    KnowledgeBase {
        string _id PK
        string name
        int file_count
        int chunk_count
        int index_count
        int embedding_model_id FK
        boolean basic_index
        boolean graph_index
        boolean semantic_index
    }

    SourceFile {
        ObjectId _id PK
        ObjectId knowledge_base_id FK
        string name
        string storage_path
        string processing_status
        int chunk_count
    }

    Chunk {
        ObjectId _id PK
        ObjectId file_id FK
        ObjectId knowledge_base_id FK
        int chunk_index
        string answer
        string question
        string chunk_type
    }

    ChunkIndex {
        string _id PK
        ObjectId file_id FK
        ObjectId knowledge_base_id FK
        ObjectId chunk_id FK
        string index_content
        string chunk_type
        int chunk_index
    }

    EmbeddingModel {
        int id PK
        string name
        string embedding_name
        int vector_size
    }

    RerankModel {
        int id PK
        string name
        string reRank_model
        string provider
    }
```



| 对比维度 | MoE（混合专家模型） |CoE（专家协同模型） |
|--------|-----------------|-----------|
| 核心架构 | 集成多个子模型，通过门控网络动态激活部分专家，紧密耦合训练 | 独立训练专家模型，通过路由机制按需调用，松散耦合协作 |
| 训练成本 | 需统一训练所有专家，硬件要求高（如GPU集群） | 支持独立训练和渐进扩展，成本降低90% |
| 推理效率 | 稀疏激活减少计算量（如仅激活17B参数），适合高吞吐场景 | 并行调用多模型提升效率，多步推理更精准 |
| 扩展性 | 扩展需重新训练整体模型，参数量接近物理极限 | 动态扩展专家数量（如集成16个模型），开放平台支持多厂商协作 |
| 适用场景 | 企业级多模态任务（如代码生成、SQL解析） | 细分领域专业化任务（如中文诗词、自动驾驶测试） |
| 技术瓶颈 | 硬件故障频发，商业化面临"性能-成本-速度"不可能三角 | 依赖精准路由机制，多模型协同可能引入延迟 |
| 发展趋势 | 巨头企业主导，追求通用性但受限于成本天花板 | 构建多模型协作生态，在金融/医疗等高安全领域潜力突出 |

架构本质：
MoE：集中式架构，参数规模驱动（如Switch Transformers）
CoE：分布式架构，协作效率驱动（如360三阶段推理模型）
典型应用：
MoE：Snowflake Arctic（企业数据分析）
CoE：360智脑（中文复杂推理）、华为自动驾驶方案