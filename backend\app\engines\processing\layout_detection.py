from doclayout_yolo import YOLOv10
import torchvision
import torch
import numpy as np
from PIL import Image
import cv2


class LayoutDetectionYOLO:
    def __init__(self, model_path):
        self.model_path = model_path
        self.model = YOLOv10(self.model_path)
        self.id_to_names = {
            0: 'title', 
            1: 'plain text',
            2: 'abandon', 
            3: 'figure', 
            4: 'figure_caption', 
            5: 'table', 
            6: 'table_caption', 
            7: 'table_footnote', 
            8: 'isolate_formula', 
            9: 'formula_caption'
        }
    
    def detect(self, image_path, imgsz=1024, conf=0.2, device='cpu'):
        """版式分析推理"""
        return self.model.predict(image_path, imgsz=imgsz, conf=conf, device=device)
    
    def layout_nms(self, boxes, scores, classes, iou_threshold=0):
        """
        版式分析后处理，非极大值抑制，删除目标检测生成的重叠边界框。
        原理：选择置信度得分最高的边界框，删除与其显著重叠（iou值大于某个阈值）的任何其他边界框，重复此过程，直到考虑了所有边界框。
        
        boxes: bounding boxes
        scores: 置信度
        classes: 类别序号
        iou_threshold: iou阈值
        """
        iou_threshold = 0
        indices = torchvision.ops.nms(boxes=torch.Tensor(boxes), scores=torch.Tensor(scores),iou_threshold=iou_threshold)  
        boxes, scores, classes = boxes[indices], scores[indices], classes[indices]
        if len(boxes.shape) == 1:
            boxes = np.expand_dims(boxes, 0)
            scores = np.expand_dims(scores, 0)
            classes = np.expand_dims(classes, 0)
            
        return boxes, scores, classes
    
    def save_layout_result(self, img_path, save_image_path, boxes, scores, classes):
        """保存版式分析结果"""
        vis_result = self._visualize_bbox(img_path, boxes, classes, scores, self.id_to_names)
        return cv2.imwrite(save_image_path, vis_result)
        
    def _colormap(self, N=256, normalized=False):
        """
        Generate the color map.
        Args:
            N (int): Number of labels (default is 256).
            normalized (bool): If True, return colors normalized to [0, 1]. Otherwise, return [0, 255].
        Returns:
            np.ndarray: Color map array of shape (N, 3).
        """
        def bitget(byteval, idx):
            """
            Get the bit value at the specified index.
            Args:
                byteval (int): The byte value.
                idx (int): The index of the bit.
            Returns:
                int: The bit value (0 or 1).
            """
            return ((byteval & (1 << idx)) != 0)

        cmap = np.zeros((N, 3), dtype=np.uint8)
        for i in range(N):
            r = g = b = 0
            c = i
            for j in range(8):
                r = r | (bitget(c, 0) << (7 - j))
                g = g | (bitget(c, 1) << (7 - j))
                b = b | (bitget(c, 2) << (7 - j))
                c = c >> 3
            cmap[i] = np.array([r, g, b])
        
        if normalized:
            cmap = cmap.astype(np.float32) / 255.0

        return cmap

    def _visualize_bbox(self, image_path, bboxes, classes, scores, id_to_names, alpha=0.3):
        """
        Visualize layout detection results on an image.

        Args:
            image_path (str): Path to the input image.
            bboxes (list): List of bounding boxes, each represented as [x_min, y_min, x_max, y_max].
            classes (list): List of class IDs corresponding to the bounding boxes.
            id_to_names (dict): Dictionary mapping class IDs to class names.
            alpha (float): Transparency factor for the filled color (default is 0.3).

        Returns:
            np.ndarray: Image with visualized layout detection results.
        """
        # Check if image_path is a PIL.Image.Image object
        if isinstance(image_path, Image.Image) or isinstance(image_path, np.ndarray):
            image = np.array(image_path)
            image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)  # Convert RGB to BGR for OpenCV
        else:
            image = cv2.imread(image_path)

        overlay = image.copy()
        
        cmap = self._colormap(N=len(id_to_names), normalized=False)
        
        # Iterate over each bounding box
        for i, bbox in enumerate(bboxes):
            x_min, y_min, x_max, y_max = map(int, bbox)
            class_id = int(classes[i])
            class_name = id_to_names[class_id]
            
            text = class_name + f":{scores[i]:.3f}"
            
            color = tuple(int(c) for c in cmap[class_id])
            cv2.rectangle(overlay, (x_min, y_min), (x_max, y_max), color, -1)
            cv2.rectangle(image, (x_min, y_min), (x_max, y_max), color, 2)
            
            # Add the class name with a background rectangle
            (text_width, text_height), baseline = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.9, 2)
            cv2.rectangle(image, (x_min, y_min - text_height - baseline), (x_min + text_width, y_min), color, -1)
            cv2.putText(image, text, (x_min, y_min - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 255, 255), 2)
        
        # Blend the overlay with the original image
        cv2.addWeighted(overlay, alpha, image, 1 - alpha, 0, image)
        
        return image
    