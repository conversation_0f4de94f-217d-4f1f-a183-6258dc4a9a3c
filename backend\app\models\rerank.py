# from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>te<PERSON>, String, DateTime, <PERSON><PERSON><PERSON>
# from sqlalchemy.ext.declarative import declarative_base
from mongoengine import Document, StringField, DateTimeField, IntField, ObjectIdField, ListField, DictField, FloatField, <PERSON>olean<PERSON>ield
from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime
from app.utils.enums import RerankProvider

class RerankModel(Document):

    id = IntField(primary_key=True, default=1)
    name = StringField(max_length=255, required=True)
    reRank_model = StringField(max_length=255, required=True)
    service_url = StringField(max_length=255, null=True)
    api_key = StringField(max_length=255, null=True)
    provider = StringField(max_length=50, default=RerankProvider.OPENAI)
    created_at = DateTimeField(default=datetime.now())
    updated_at = DateTimeField(null=True)
    extra = DictField()
    created_by = IntField(required=True)
    is_active = <PERSON><PERSON>anField(default=True)
    meta = {
        'collection': 'reranks',
        'indexes': ['id', 'provider', 'created_by']
    }


class RerankCreate(BaseModel):
    name: str
    reRank_model: str
    service_url: Optional[str] = None
    api_key: Optional[str] = None
    provider: Optional[str] = None
    extra: Optional[Dict[str, Any]] = None
    # created_by: int
    class Config:
        from_attributes = True

class RerankUpdate(BaseModel):
    name: Optional[str] = None
    reRank_model: Optional[str] = None
    service_url: Optional[str] = None
    api_key: Optional[str] = None
    provider: Optional[str] = None
    is_active: Optional[bool] = None
    extra: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True

class RerankResponse(BaseModel):
    id: int
    name: str
    reRank_model: str
    service_url: str
    api_key: str
    provider: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    extra: Optional[Dict[str, Any]] = None
    created_by: int
    is_active: bool

    class Config:
        from_attributes = True

