"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4679],{92479:function(e,t,n){n.r(t),n.d(t,{default:function(){return E}});var r=n(19632),i=n.n(r),a=n(5574),s=n.n(a),o=n(51042),c=n(59257),l=n(40717),d=n(28548),u=n(97131),x=n(88372),h=n(35312),p=n(66309),g=n(55102),f=n(71230),m=n(15746),j=n(4393),v=n(96074),y=n(67294),k=(0,n(24444).kc)((function(e){var t=e.token;return{avatarHolder:{marginBottom:"24px",textAlign:"center","& > img":{width:"104px",height:"104px",marginBottom:"20px"}},name:{marginBottom:"4px",color:t.colorTextHeading,fontWeight:"500",fontSize:"20px",lineHeight:"28px"},detail:{p:{position:"relative",marginBottom:"8px",paddingLeft:"26px","&:last-child":{marginBottom:"0"}},i:{position:"absolute",top:"4px",left:"0",width:"14px",height:"14px"}},tagsTitle:{marginBottom:"12px",color:t.colorTextHeading,fontWeight:"500"},teamTitle:{marginBottom:"12px",color:t.colorTextHeading,fontWeight:"500"},tags:{".ant-tag":{marginBottom:"8px"}},team:{".ant-avatar":{marginRight:"12px"},a:{display:"block",marginBottom:"24px",overflow:"hidden",color:t.colorText,whiteSpace:"nowrap",textOverflow:"ellipsis",wordBreak:"break-all",transition:"color 0.3s","&:hover":{color:t.colorPrimary}}},tabsCard:{".ant-card-head":{padding:"0 16px"}}}})),Z=n(10981),b=n(15009),w=n.n(b),S=n(99289),C=n.n(S),B=n(12453),_=n(2453),T=n(85893),P=function(){var e=(0,y.useState)([]),t=s()(e,2),n=t[0],r=t[1],i=((0,Z.bG)(),function(){var e=C()(w()().mark((function e(){var t,n;return w()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/api/api-keys");case 3:if((t=e.sent).ok){e.next=6;break}throw new Error("获取失败");case 6:return e.next=8,t.json();case 8:n=e.sent,r(Array.isArray(n)?n:[]),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(0),_.ZP.error("获取 API Keys 失败"),r([]);case 16:case"end":return e.stop()}}),e,null,[[0,12]])})));return function(){return e.apply(this,arguments)}}());(0,y.useEffect)((function(){i()}),[]);return(0,T.jsx)(j.Z,{bordered:!1,children:(0,T.jsx)(B.Z,{columns:[{title:"API Key",dataIndex:"key",render:function(e){return"".concat(e.substring(0,8),"...")}},{title:"创建时间",dataIndex:"created_at"}],dataSource:n,search:!1,pagination:!1,rowKey:"id"})})},z=n(93410),I=n(42994),R=n(83622),H=function(){return(0,T.jsx)(z.Z,{children:(0,T.jsx)(I.Rs,{rowKey:"id",dataSource:[{id:"1",title:"用户数据.csv",type:"CSV",size:"2.3MB",createTime:"2024-01-01"}],metas:{title:{dataIndex:"title"},description:{render:function(e,t){return(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)(p.Z,{color:"blue",children:t.type}),(0,T.jsx)(p.Z,{color:"green",children:t.size}),(0,T.jsx)(p.Z,{children:t.createTime})]})}},actions:{render:function(e,t){return[(0,T.jsx)(R.ZP,{type:"link",onClick:function(){return e=t,void _.ZP.success("开始下载 ".concat(e.title));var e},children:"下载"},"download"),(0,T.jsx)(R.ZP,{type:"link",danger:!0,onClick:function(){return e=t,void _.ZP.success("删除成功 ".concat(e.title));var e},children:"删除"},"delete")]}}}})})},N=n(72269),A=n(2487),K=function(){var e,t=[{title:"账户密码",description:"其他用户的消息将以站内信的形式通知",actions:[e=(0,T.jsx)(N.Z,{checkedChildren:"开",unCheckedChildren:"关",defaultChecked:!0})]},{title:"系统消息",description:"系统消息将以站内信的形式通知",actions:[e]},{title:"待办任务",description:"待办任务将以站内信的形式通知",actions:[e]}];return(0,T.jsx)(y.Fragment,{children:(0,T.jsx)(A.Z,{itemLayout:"horizontal",dataSource:t,renderItem:function(e){return(0,T.jsx)(A.Z.Item,{actions:e.actions,children:(0,T.jsx)(A.Z.Item.Meta,{title:e.title,description:e.description})})}})})},L=[{key:"info",tab:(0,T.jsxs)("span",{children:["信息"," ",(0,T.jsx)("span",{style:{fontSize:14},children:"(8)"})]})},{key:"api_key",tab:(0,T.jsxs)("span",{children:["api_key"," ",(0,T.jsx)("span",{style:{fontSize:14},children:"(8)"})]})},{key:"data",tab:(0,T.jsxs)("span",{children:["数据"," ",(0,T.jsx)("span",{style:{fontSize:14},children:"(8)"})]})}],W=function(e){var t=e.tags,n=k().styles,r=(0,y.useRef)(null),a=(0,y.useState)([]),c=s()(a,2),l=c[0],d=c[1],u=(0,y.useState)(!1),x=s()(u,2),h=x[0],f=x[1],m=(0,y.useState)(""),j=s()(m,2),v=j[0],Z=j[1],b=function(){var e=i()(l);v&&0===e.filter((function(e){return e.label===v})).length&&(e=[].concat(i()(e),[{key:"new-".concat(e.length),label:v}])),d(e),f(!1),Z("")};return(0,T.jsxs)("div",{className:n.tags,children:[(0,T.jsx)("div",{className:n.tagsTitle,children:"标签"}),(t||[]).concat(l).map((function(e){return(0,T.jsx)(p.Z,{closable:e.key.startsWith("new-"),onClose:function(){return t=e.key,n=l.filter((function(e){return e.key!==t})),void d(n);var t,n},children:e.label},e.key)})),h&&(0,T.jsx)(g.Z,{ref:r,type:"text",size:"small",style:{width:78},value:v,onChange:function(e){Z(e.target.value)},onBlur:b,onPressEnter:b}),!h&&(0,T.jsx)(p.Z,{onClick:function(){var e;(f(!0),r.current)&&(null===(e=r.current)||void 0===e||e.focus())},style:{borderStyle:"dashed"},children:(0,T.jsx)(o.Z,{})})]})},E=function(){var e,t=k().styles,n=(0,y.useState)("info"),r=s()(n,2),i=r[0],a=r[1],o=(0,h.useRequest)((function(){var e=(0,Z.bG)();return Promise.resolve(e)})),p=o.data,g=o.loading;return(0,T.jsx)(u._z,{children:(0,T.jsx)(x.f,{children:(0,T.jsxs)(f.Z,{gutter:24,children:[(0,T.jsx)(m.Z,{lg:7,md:24,children:(0,T.jsx)(j.Z,{bordered:!1,style:{marginBottom:24},loading:g,children:!g&&p&&(0,T.jsxs)("div",{children:[(0,T.jsxs)("div",{className:t.avatarHolder,children:[(0,T.jsx)("img",{alt:"",src:p.avatar}),(0,T.jsx)("div",{className:t.name,children:p.name}),(0,T.jsxs)("div",{children:["加入时间：",new Date(p.created_at).toLocaleDateString()]})]}),function(e){return(0,T.jsxs)("div",{className:t.detail,children:[(0,T.jsxs)("p",{children:[(0,T.jsx)(c.Z,{style:{marginRight:8}}),"角色：",e.role_name]}),(0,T.jsxs)("p",{children:[(0,T.jsx)(l.Z,{style:{marginRight:8}}),"部门：",e.group_name]}),(0,T.jsxs)("p",{children:[(0,T.jsx)(d.Z,{style:{marginRight:8}}),"手机：",e.phone]})]})}(p),(0,T.jsx)(v.Z,{dashed:!0}),(0,T.jsx)(W,{tags:p.tags||[]})]})})}),(0,T.jsx)(m.Z,{lg:17,md:24,children:(0,T.jsx)(j.Z,{className:t.tabsCard,bordered:!1,tabList:L,activeTabKey:i,onTabChange:function(e){a(e)},children:(e=i,"info"===e?(0,T.jsx)(K,{}):"api_key"===e?(0,T.jsx)(P,{}):"data"===e?(0,T.jsx)(H,{}):null)})})]})})})}}}]);