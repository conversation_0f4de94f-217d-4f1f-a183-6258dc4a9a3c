"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3082],{75668:function(t,e,i){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"DraggableCore",{enumerable:!0,get:function(){return d.default}}),e.default=void 0;var s=function(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var i=p(e);if(i&&i.has(t))return i.get(t);var s={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var r in t)if("default"!==r&&Object.prototype.hasOwnProperty.call(t,r)){var a=n?Object.getOwnPropertyDescriptor(t,r):null;a&&(a.get||a.set)?Object.defineProperty(s,r,a):s[r]=t[r]}s.default=t,i&&i.set(t,s);return s}(i(67294)),n=u(i(45697)),r=u(i(73935)),a=u(i(18946)),o=i(81825),h=i(2849),l=i(9280),d=u(i(80783)),c=u(i(55904));function u(t){return t&&t.__esModule?t:{default:t}}function p(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,i=new WeakMap;return(p=function(t){return t?i:e})(t)}function g(){return g=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(t[s]=i[s])}return t},g.apply(this,arguments)}function f(t,e,i){return(e=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var s=i.call(t,e||"default");if("object"!=typeof s)return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}class m extends s.Component{static getDerivedStateFromProps(t,e){let{position:i}=t,{prevPropsPosition:s}=e;return!i||s&&i.x===s.x&&i.y===s.y?null:((0,c.default)("Draggable: getDerivedStateFromProps %j",{position:i,prevPropsPosition:s}),{x:i.x,y:i.y,prevPropsPosition:{...i}})}constructor(t){super(t),f(this,"onDragStart",((t,e)=>{(0,c.default)("Draggable: onDragStart: %j",e);if(!1===this.props.onStart(t,(0,h.createDraggableData)(this,e)))return!1;this.setState({dragging:!0,dragged:!0})})),f(this,"onDrag",((t,e)=>{if(!this.state.dragging)return!1;(0,c.default)("Draggable: onDrag: %j",e);const i=(0,h.createDraggableData)(this,e),s={x:i.x,y:i.y,slackX:0,slackY:0};if(this.props.bounds){const{x:t,y:e}=s;s.x+=this.state.slackX,s.y+=this.state.slackY;const[n,r]=(0,h.getBoundPosition)(this,s.x,s.y);s.x=n,s.y=r,s.slackX=this.state.slackX+(t-s.x),s.slackY=this.state.slackY+(e-s.y),i.x=s.x,i.y=s.y,i.deltaX=s.x-this.state.x,i.deltaY=s.y-this.state.y}if(!1===this.props.onDrag(t,i))return!1;this.setState(s)})),f(this,"onDragStop",((t,e)=>{if(!this.state.dragging)return!1;if(!1===this.props.onStop(t,(0,h.createDraggableData)(this,e)))return!1;(0,c.default)("Draggable: onDragStop: %j",e);const i={dragging:!1,slackX:0,slackY:0};if(Boolean(this.props.position)){const{x:t,y:e}=this.props.position;i.x=t,i.y=e}this.setState(i)})),this.state={dragging:!1,dragged:!1,x:t.position?t.position.x:t.defaultPosition.x,y:t.position?t.position.y:t.defaultPosition.y,prevPropsPosition:{...t.position},slackX:0,slackY:0,isElementSVG:!1},!t.position||t.onDrag||t.onStop||console.warn("A `position` was applied to this <Draggable>, without drag handlers. This will make this component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the `position` of this element.")}componentDidMount(){void 0!==window.SVGElement&&this.findDOMNode()instanceof window.SVGElement&&this.setState({isElementSVG:!0})}componentWillUnmount(){this.setState({dragging:!1})}findDOMNode(){var t,e;return null!==(t=null===(e=this.props)||void 0===e||null===(e=e.nodeRef)||void 0===e?void 0:e.current)&&void 0!==t?t:r.default.findDOMNode(this)}render(){const{axis:t,bounds:e,children:i,defaultPosition:n,defaultClassName:r,defaultClassNameDragging:l,defaultClassNameDragged:c,position:u,positionOffset:p,scale:f,...m}=this.props;let b={},v=null;const y=!Boolean(u)||this.state.dragging,w=u||n,A={x:(0,h.canDragX)(this)&&y?this.state.x:w.x,y:(0,h.canDragY)(this)&&y?this.state.y:w.y};this.state.isElementSVG?v=(0,o.createSVGTransform)(A,p):b=(0,o.createCSSTransform)(A,p);const x=(0,a.default)(i.props.className||"",r,{[l]:this.state.dragging,[c]:this.state.dragged});return s.createElement(d.default,g({},m,{onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop}),s.cloneElement(s.Children.only(i),{className:x,style:{...i.props.style,...b},transform:v}))}}e.default=m,f(m,"displayName","Draggable"),f(m,"propTypes",{...d.default.propTypes,axis:n.default.oneOf(["both","x","y","none"]),bounds:n.default.oneOfType([n.default.shape({left:n.default.number,right:n.default.number,top:n.default.number,bottom:n.default.number}),n.default.string,n.default.oneOf([!1])]),defaultClassName:n.default.string,defaultClassNameDragging:n.default.string,defaultClassNameDragged:n.default.string,defaultPosition:n.default.shape({x:n.default.number,y:n.default.number}),positionOffset:n.default.shape({x:n.default.oneOfType([n.default.number,n.default.string]),y:n.default.oneOfType([n.default.number,n.default.string])}),position:n.default.shape({x:n.default.number,y:n.default.number}),className:l.dontSetMe,style:l.dontSetMe,transform:l.dontSetMe}),f(m,"defaultProps",{...d.default.defaultProps,axis:"both",bounds:!1,defaultClassName:"react-draggable",defaultClassNameDragging:"react-draggable-dragging",defaultClassNameDragged:"react-draggable-dragged",defaultPosition:{x:0,y:0},scale:1})},80783:function(t,e,i){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=function(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var i=c(e);if(i&&i.has(t))return i.get(t);var s={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var r in t)if("default"!==r&&Object.prototype.hasOwnProperty.call(t,r)){var a=n?Object.getOwnPropertyDescriptor(t,r):null;a&&(a.get||a.set)?Object.defineProperty(s,r,a):s[r]=t[r]}s.default=t,i&&i.set(t,s);return s}(i(67294)),n=d(i(45697)),r=d(i(73935)),a=i(81825),o=i(2849),h=i(9280),l=d(i(55904));function d(t){return t&&t.__esModule?t:{default:t}}function c(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,i=new WeakMap;return(c=function(t){return t?i:e})(t)}function u(t,e,i){return(e=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var s=i.call(t,e||"default");if("object"!=typeof s)return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}const p={start:"touchstart",move:"touchmove",stop:"touchend"},g={start:"mousedown",move:"mousemove",stop:"mouseup"};let f=g;class m extends s.Component{constructor(){super(...arguments),u(this,"dragging",!1),u(this,"lastX",NaN),u(this,"lastY",NaN),u(this,"touchIdentifier",null),u(this,"mounted",!1),u(this,"handleDragStart",(t=>{if(this.props.onMouseDown(t),!this.props.allowAnyClick&&"number"==typeof t.button&&0!==t.button)return!1;const e=this.findDOMNode();if(!e||!e.ownerDocument||!e.ownerDocument.body)throw new Error("<DraggableCore> not mounted on DragStart!");const{ownerDocument:i}=e;if(this.props.disabled||!(t.target instanceof i.defaultView.Node)||this.props.handle&&!(0,a.matchesSelectorAndParentsTo)(t.target,this.props.handle,e)||this.props.cancel&&(0,a.matchesSelectorAndParentsTo)(t.target,this.props.cancel,e))return;"touchstart"===t.type&&t.preventDefault();const s=(0,a.getTouchIdentifier)(t);this.touchIdentifier=s;const n=(0,o.getControlPosition)(t,s,this);if(null==n)return;const{x:r,y:h}=n,d=(0,o.createCoreData)(this,r,h);(0,l.default)("DraggableCore: handleDragStart: %j",d),(0,l.default)("calling",this.props.onStart);!1!==this.props.onStart(t,d)&&!1!==this.mounted&&(this.props.enableUserSelectHack&&(0,a.addUserSelectStyles)(i),this.dragging=!0,this.lastX=r,this.lastY=h,(0,a.addEvent)(i,f.move,this.handleDrag),(0,a.addEvent)(i,f.stop,this.handleDragStop))})),u(this,"handleDrag",(t=>{const e=(0,o.getControlPosition)(t,this.touchIdentifier,this);if(null==e)return;let{x:i,y:s}=e;if(Array.isArray(this.props.grid)){let t=i-this.lastX,e=s-this.lastY;if([t,e]=(0,o.snapToGrid)(this.props.grid,t,e),!t&&!e)return;i=this.lastX+t,s=this.lastY+e}const n=(0,o.createCoreData)(this,i,s);(0,l.default)("DraggableCore: handleDrag: %j",n);if(!1!==this.props.onDrag(t,n)&&!1!==this.mounted)this.lastX=i,this.lastY=s;else try{this.handleDragStop(new MouseEvent("mouseup"))}catch(t){const e=document.createEvent("MouseEvents");e.initMouseEvent("mouseup",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),this.handleDragStop(e)}})),u(this,"handleDragStop",(t=>{if(!this.dragging)return;const e=(0,o.getControlPosition)(t,this.touchIdentifier,this);if(null==e)return;let{x:i,y:s}=e;if(Array.isArray(this.props.grid)){let t=i-this.lastX||0,e=s-this.lastY||0;[t,e]=(0,o.snapToGrid)(this.props.grid,t,e),i=this.lastX+t,s=this.lastY+e}const n=(0,o.createCoreData)(this,i,s);if(!1===this.props.onStop(t,n)||!1===this.mounted)return!1;const r=this.findDOMNode();r&&this.props.enableUserSelectHack&&(0,a.removeUserSelectStyles)(r.ownerDocument),(0,l.default)("DraggableCore: handleDragStop: %j",n),this.dragging=!1,this.lastX=NaN,this.lastY=NaN,r&&((0,l.default)("DraggableCore: Removing handlers"),(0,a.removeEvent)(r.ownerDocument,f.move,this.handleDrag),(0,a.removeEvent)(r.ownerDocument,f.stop,this.handleDragStop))})),u(this,"onMouseDown",(t=>(f=g,this.handleDragStart(t)))),u(this,"onMouseUp",(t=>(f=g,this.handleDragStop(t)))),u(this,"onTouchStart",(t=>(f=p,this.handleDragStart(t)))),u(this,"onTouchEnd",(t=>(f=p,this.handleDragStop(t))))}componentDidMount(){this.mounted=!0;const t=this.findDOMNode();t&&(0,a.addEvent)(t,p.start,this.onTouchStart,{passive:!1})}componentWillUnmount(){this.mounted=!1;const t=this.findDOMNode();if(t){const{ownerDocument:e}=t;(0,a.removeEvent)(e,g.move,this.handleDrag),(0,a.removeEvent)(e,p.move,this.handleDrag),(0,a.removeEvent)(e,g.stop,this.handleDragStop),(0,a.removeEvent)(e,p.stop,this.handleDragStop),(0,a.removeEvent)(t,p.start,this.onTouchStart,{passive:!1}),this.props.enableUserSelectHack&&(0,a.removeUserSelectStyles)(e)}}findDOMNode(){var t,e;return null!==(t=this.props)&&void 0!==t&&t.nodeRef?null===(e=this.props)||void 0===e||null===(e=e.nodeRef)||void 0===e?void 0:e.current:r.default.findDOMNode(this)}render(){return s.cloneElement(s.Children.only(this.props.children),{onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onTouchEnd:this.onTouchEnd})}}e.default=m,u(m,"displayName","DraggableCore"),u(m,"propTypes",{allowAnyClick:n.default.bool,children:n.default.node.isRequired,disabled:n.default.bool,enableUserSelectHack:n.default.bool,offsetParent:function(t,e){if(t[e]&&1!==t[e].nodeType)throw new Error("Draggable's offsetParent must be a DOM Node.")},grid:n.default.arrayOf(n.default.number),handle:n.default.string,cancel:n.default.string,nodeRef:n.default.object,onStart:n.default.func,onDrag:n.default.func,onStop:n.default.func,onMouseDown:n.default.func,scale:n.default.number,className:h.dontSetMe,style:h.dontSetMe,transform:h.dontSetMe}),u(m,"defaultProps",{allowAnyClick:!1,disabled:!1,enableUserSelectHack:!0,onStart:function(){},onDrag:function(){},onStop:function(){},onMouseDown:function(){},scale:1})},61193:function(t,e,i){const{default:s,DraggableCore:n}=i(75668);t.exports=s,t.exports.default=s,t.exports.DraggableCore=n},81825:function(t,e,i){Object.defineProperty(e,"__esModule",{value:!0}),e.addClassName=l,e.addEvent=function(t,e,i,s){if(!t)return;const n={capture:!0,...s};t.addEventListener?t.addEventListener(e,i,n):t.attachEvent?t.attachEvent("on"+e,i):t["on"+e]=i},e.addUserSelectStyles=function(t){if(!t)return;let e=t.getElementById("react-draggable-style-el");e||(e=t.createElement("style"),e.type="text/css",e.id="react-draggable-style-el",e.innerHTML=".react-draggable-transparent-selection *::-moz-selection {all: inherit;}\n",e.innerHTML+=".react-draggable-transparent-selection *::selection {all: inherit;}\n",t.getElementsByTagName("head")[0].appendChild(e));t.body&&l(t.body,"react-draggable-transparent-selection")},e.createCSSTransform=function(t,e){const i=h(t,e,"px");return{[(0,n.browserPrefixToKey)("transform",n.default)]:i}},e.createSVGTransform=function(t,e){return h(t,e,"")},e.getTouch=function(t,e){return t.targetTouches&&(0,s.findInArray)(t.targetTouches,(t=>e===t.identifier))||t.changedTouches&&(0,s.findInArray)(t.changedTouches,(t=>e===t.identifier))},e.getTouchIdentifier=function(t){if(t.targetTouches&&t.targetTouches[0])return t.targetTouches[0].identifier;if(t.changedTouches&&t.changedTouches[0])return t.changedTouches[0].identifier},e.getTranslation=h,e.innerHeight=function(t){let e=t.clientHeight;const i=t.ownerDocument.defaultView.getComputedStyle(t);return e-=(0,s.int)(i.paddingTop),e-=(0,s.int)(i.paddingBottom),e},e.innerWidth=function(t){let e=t.clientWidth;const i=t.ownerDocument.defaultView.getComputedStyle(t);return e-=(0,s.int)(i.paddingLeft),e-=(0,s.int)(i.paddingRight),e},e.matchesSelector=o,e.matchesSelectorAndParentsTo=function(t,e,i){let s=t;do{if(o(s,e))return!0;if(s===i)return!1;s=s.parentNode}while(s);return!1},e.offsetXYFromParent=function(t,e,i){const s=e===e.ownerDocument.body?{left:0,top:0}:e.getBoundingClientRect(),n=(t.clientX+e.scrollLeft-s.left)/i,r=(t.clientY+e.scrollTop-s.top)/i;return{x:n,y:r}},e.outerHeight=function(t){let e=t.clientHeight;const i=t.ownerDocument.defaultView.getComputedStyle(t);return e+=(0,s.int)(i.borderTopWidth),e+=(0,s.int)(i.borderBottomWidth),e},e.outerWidth=function(t){let e=t.clientWidth;const i=t.ownerDocument.defaultView.getComputedStyle(t);return e+=(0,s.int)(i.borderLeftWidth),e+=(0,s.int)(i.borderRightWidth),e},e.removeClassName=d,e.removeEvent=function(t,e,i,s){if(!t)return;const n={capture:!0,...s};t.removeEventListener?t.removeEventListener(e,i,n):t.detachEvent?t.detachEvent("on"+e,i):t["on"+e]=null},e.removeUserSelectStyles=function(t){if(!t)return;try{if(t.body&&d(t.body,"react-draggable-transparent-selection"),t.selection)t.selection.empty();else{const e=(t.defaultView||window).getSelection();e&&"Caret"!==e.type&&e.removeAllRanges()}}catch(t){}};var s=i(9280),n=function(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var i=r(e);if(i&&i.has(t))return i.get(t);var s={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t)if("default"!==a&&Object.prototype.hasOwnProperty.call(t,a)){var o=n?Object.getOwnPropertyDescriptor(t,a):null;o&&(o.get||o.set)?Object.defineProperty(s,a,o):s[a]=t[a]}s.default=t,i&&i.set(t,s);return s}(i(38650));function r(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,i=new WeakMap;return(r=function(t){return t?i:e})(t)}let a="";function o(t,e){return a||(a=(0,s.findInArray)(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"],(function(e){return(0,s.isFunction)(t[e])}))),!!(0,s.isFunction)(t[a])&&t[a](e)}function h(t,e,i){let{x:s,y:n}=t,r="translate(".concat(s).concat(i,",").concat(n).concat(i,")");if(e){const t="".concat("string"==typeof e.x?e.x:e.x+i),s="".concat("string"==typeof e.y?e.y:e.y+i);r="translate(".concat(t,", ").concat(s,")")+r}return r}function l(t,e){t.classList?t.classList.add(e):t.className.match(new RegExp("(?:^|\\s)".concat(e,"(?!\\S)")))||(t.className+=" ".concat(e))}function d(t,e){t.classList?t.classList.remove(e):t.className=t.className.replace(new RegExp("(?:^|\\s)".concat(e,"(?!\\S)"),"g"),"")}},38650:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.browserPrefixToKey=n,e.browserPrefixToStyle=function(t,e){return e?"-".concat(e.toLowerCase(),"-").concat(t):t},e.default=void 0,e.getPrefix=s;const i=["Moz","Webkit","O","ms"];function s(){var t;let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"transform";if("undefined"==typeof window)return"";const s=null===(t=window.document)||void 0===t||null===(t=t.documentElement)||void 0===t?void 0:t.style;if(!s)return"";if(e in s)return"";for(let t=0;t<i.length;t++)if(n(e,i[t])in s)return i[t];return""}function n(t,e){return e?"".concat(e).concat(function(t){let e="",i=!0;for(let s=0;s<t.length;s++)i?(e+=t[s].toUpperCase(),i=!1):"-"===t[s]?i=!0:e+=t[s];return e}(t)):t}e.default=s()},55904:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){0}},2849:function(t,e,i){Object.defineProperty(e,"__esModule",{value:!0}),e.canDragX=function(t){return"both"===t.props.axis||"x"===t.props.axis},e.canDragY=function(t){return"both"===t.props.axis||"y"===t.props.axis},e.createCoreData=function(t,e,i){const n=!(0,s.isNum)(t.lastX),a=r(t);return n?{node:a,deltaX:0,deltaY:0,lastX:e,lastY:i,x:e,y:i}:{node:a,deltaX:e-t.lastX,deltaY:i-t.lastY,lastX:t.lastX,lastY:t.lastY,x:e,y:i}},e.createDraggableData=function(t,e){const i=t.props.scale;return{node:e.node,x:t.state.x+e.deltaX/i,y:t.state.y+e.deltaY/i,deltaX:e.deltaX/i,deltaY:e.deltaY/i,lastX:t.state.x,lastY:t.state.y}},e.getBoundPosition=function(t,e,i){if(!t.props.bounds)return[e,i];let{bounds:a}=t.props;a="string"==typeof a?a:function(t){return{left:t.left,top:t.top,right:t.right,bottom:t.bottom}}(a);const o=r(t);if("string"==typeof a){const{ownerDocument:t}=o,e=t.defaultView;let i;if(i="parent"===a?o.parentNode:t.querySelector(a),!(i instanceof e.HTMLElement))throw new Error('Bounds selector "'+a+'" could not find an element.');const r=i,h=e.getComputedStyle(o),l=e.getComputedStyle(r);a={left:-o.offsetLeft+(0,s.int)(l.paddingLeft)+(0,s.int)(h.marginLeft),top:-o.offsetTop+(0,s.int)(l.paddingTop)+(0,s.int)(h.marginTop),right:(0,n.innerWidth)(r)-(0,n.outerWidth)(o)-o.offsetLeft+(0,s.int)(l.paddingRight)-(0,s.int)(h.marginRight),bottom:(0,n.innerHeight)(r)-(0,n.outerHeight)(o)-o.offsetTop+(0,s.int)(l.paddingBottom)-(0,s.int)(h.marginBottom)}}(0,s.isNum)(a.right)&&(e=Math.min(e,a.right));(0,s.isNum)(a.bottom)&&(i=Math.min(i,a.bottom));(0,s.isNum)(a.left)&&(e=Math.max(e,a.left));(0,s.isNum)(a.top)&&(i=Math.max(i,a.top));return[e,i]},e.getControlPosition=function(t,e,i){const s="number"==typeof e?(0,n.getTouch)(t,e):null;if("number"==typeof e&&!s)return null;const a=r(i),o=i.props.offsetParent||a.offsetParent||a.ownerDocument.body;return(0,n.offsetXYFromParent)(s||t,o,i.props.scale)},e.snapToGrid=function(t,e,i){const s=Math.round(e/t[0])*t[0],n=Math.round(i/t[1])*t[1];return[s,n]};var s=i(9280),n=i(81825);function r(t){const e=t.findDOMNode();if(!e)throw new Error("<DraggableCore>: Unmounted during event!");return e}},9280:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.dontSetMe=function(t,e,i){if(t[e])return new Error("Invalid prop ".concat(e," passed to ").concat(i," - do not set this, set it on the child."))},e.findInArray=function(t,e){for(let i=0,s=t.length;i<s;i++)if(e.apply(e,[t[i],i,t]))return t[i]},e.int=function(t){return parseInt(t,10)},e.isFunction=function(t){return"function"==typeof t||"[object Function]"===Object.prototype.toString.call(t)},e.isNum=function(t){return"number"==typeof t&&!isNaN(t)}},18946:function(t,e,i){function s(t){var e,i,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t))for(e=0;e<t.length;e++)t[e]&&(i=s(t[e]))&&(n&&(n+=" "),n+=i);else for(e in t)t[e]&&(n&&(n+=" "),n+=e);return n}function n(){for(var t,e,i=0,n="";i<arguments.length;)(t=arguments[i++])&&(e=s(t))&&(n&&(n+=" "),n+=e);return n}i.r(e),i.d(e,{clsx:function(){return n}}),e.default=n},28541:function(t,e,i){i.d(e,{Me:function(){return An},Tu:function(){return wn}});var s=i(34155),n=i(48764).lW,r={d:(t,e)=>{for(var i in e)r.o(e,i)&&!r.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)},a=globalThis.pdfjsLib={};r.d(a,{AbortException:()=>vt,AnnotationEditorLayer:()=>vn,AnnotationEditorParamsType:()=>w,AnnotationEditorType:()=>y,AnnotationEditorUIManager:()=>we,AnnotationLayer:()=>an,AnnotationMode:()=>v,CMapCompressionType:()=>Z,ColorPicker:()=>gn,DOMSVGFactory:()=>Xt,DrawLayer:()=>yn,FeatureTest:()=>xt,GlobalWorkerOptions:()=>ci,ImageKind:()=>T,InvalidPDFException:()=>gt,MissingPDFException:()=>ft,OPS:()=>tt,Outliner:()=>ln,PDFDataRangeTransport:()=>us,PDFDateString:()=>ae,PDFWorker:()=>bs,PasswordResponses:()=>et,PermissionFlag:()=>A,PixelsPerInch:()=>$t,RenderingCancelledException:()=>Kt,TextLayer:()=>es,UnexpectedResponseException:()=>mt,Util:()=>St,VerbosityLevel:()=>J,XfaLayer:()=>Ts,build:()=>Ss,createValidAbsoluteUrl:()=>lt,fetchData:()=>Gt,getDocument:()=>ls,getFilenameFromUrl:()=>Zt,getPdfFilenameFromUrl:()=>te,getXfaPageViewport:()=>oe,isDataScheme:()=>Qt,isPdfFile:()=>Jt,noContextMenu:()=>se,normalizeUnicode:()=>Mt,renderTextLayer:()=>is,setLayerDimensions:()=>ce,shadow:()=>dt,updateTextLayer:()=>ss,version:()=>_s});const o=!("object"!=typeof s||s+""!="[object process]"||s.versions.nw||s.versions.electron&&s.type&&"browser"!==s.type),h=[1,0,0,1,0,0],l=[.001,0,0,.001,0,0],d=1.35,c=1,u=2,p=4,g=16,f=32,m=64,b=256,v={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},y={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15},w={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35},A={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},x=0,_=1,S=2,E=3,C=3,M=4,T={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},k=1,P=2,R=3,D=4,L=5,I=6,F=7,N=8,O=9,z=10,H=11,B=12,j=13,W=14,U=15,$=16,G=17,V=20,q=1,X=2,Y=3,K=4,Q=5,J={ERRORS:0,WARNINGS:1,INFOS:5},Z={NONE:0,BINARY:1},tt={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91},et={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let it=J.WARNINGS;function st(t){Number.isInteger(t)&&(it=t)}function nt(){return it}function rt(t){it>=J.INFOS&&console.log(`Info: ${t}`)}function at(t){it>=J.WARNINGS&&console.log(`Warning: ${t}`)}function ot(t){throw new Error(t)}function ht(t,e){t||ot(e)}function lt(t,e=null,i=null){if(!t)return null;try{if(i&&"string"==typeof t){if(i.addDefaultProtocol&&t.startsWith("www.")){t.match(/\./g)?.length>=2&&(t=`http://${t}`)}if(i.tryConvertEncoding)try{t=decodeURIComponent(escape(t))}catch{}}const s=e?new URL(t,e):new URL(t);if(function(t){switch(t?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(s))return s}catch{}return null}function dt(t,e,i,s=!1){return Object.defineProperty(t,e,{value:i,enumerable:!s,configurable:!0,writable:!1}),i}const ct=function(){function t(e,i){this.constructor===t&&ot("Cannot initialize BaseException."),this.message=e,this.name=i}return t.prototype=new Error,t.constructor=t,t}();class ut extends ct{constructor(t,e){super(t,"PasswordException"),this.code=e}}class pt extends ct{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}}class gt extends ct{constructor(t){super(t,"InvalidPDFException")}}class ft extends ct{constructor(t){super(t,"MissingPDFException")}}class mt extends ct{constructor(t,e){super(t,"UnexpectedResponseException"),this.status=e}}class bt extends ct{constructor(t){super(t,"FormatError")}}class vt extends ct{constructor(t){super(t,"AbortException")}}function yt(t){"object"==typeof t&&void 0!==t?.length||ot("Invalid argument for bytesToString");const e=t.length,i=8192;if(e<i)return String.fromCharCode.apply(null,t);const s=[];for(let n=0;n<e;n+=i){const r=Math.min(n+i,e),a=t.subarray(n,r);s.push(String.fromCharCode.apply(null,a))}return s.join("")}function wt(t){"string"!=typeof t&&ot("Invalid argument for stringToBytes");const e=t.length,i=new Uint8Array(e);for(let s=0;s<e;++s)i[s]=255&t.charCodeAt(s);return i}function At(t){const e=Object.create(null);for(const[i,s]of t)e[i]=s;return e}class xt{static get isLittleEndian(){return dt(this,"isLittleEndian",function(){const t=new Uint8Array(4);return t[0]=1,1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return dt(this,"isEvalSupported",function(){try{return new Function(""),!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return dt(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get platform(){return"undefined"!=typeof navigator&&"string"==typeof navigator?.platform?dt(this,"platform",{isMac:navigator.platform.includes("Mac")}):dt(this,"platform",{isMac:!1})}static get isCSSRoundSupported(){return dt(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}const _t=Array.from(Array(256).keys(),(t=>t.toString(16).padStart(2,"0")));class St{static makeHexColor(t,e,i){return`#${_t[t]}${_t[e]}${_t[i]}`}static scaleMinMax(t,e){let i;t[0]?(t[0]<0&&(i=e[0],e[0]=e[2],e[2]=i),e[0]*=t[0],e[2]*=t[0],t[3]<0&&(i=e[1],e[1]=e[3],e[3]=i),e[1]*=t[3],e[3]*=t[3]):(i=e[0],e[0]=e[1],e[1]=i,i=e[2],e[2]=e[3],e[3]=i,t[1]<0&&(i=e[1],e[1]=e[3],e[3]=i),e[1]*=t[1],e[3]*=t[1],t[2]<0&&(i=e[0],e[0]=e[2],e[2]=i),e[0]*=t[2],e[2]*=t[2]),e[0]+=t[4],e[1]+=t[5],e[2]+=t[4],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e){return[t[0]*e[0]+t[1]*e[2]+e[4],t[0]*e[1]+t[1]*e[3]+e[5]]}static applyInverseTransform(t,e){const i=e[0]*e[3]-e[1]*e[2];return[(t[0]*e[3]-t[1]*e[2]+e[2]*e[5]-e[4]*e[3])/i,(-t[0]*e[1]+t[1]*e[0]+e[4]*e[1]-e[5]*e[0])/i]}static getAxialAlignedBoundingBox(t,e){const i=this.applyTransform(t,e),s=this.applyTransform(t.slice(2,4),e),n=this.applyTransform([t[0],t[3]],e),r=this.applyTransform([t[2],t[1]],e);return[Math.min(i[0],s[0],n[0],r[0]),Math.min(i[1],s[1],n[1],r[1]),Math.max(i[0],s[0],n[0],r[0]),Math.max(i[1],s[1],n[1],r[1])]}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t){const e=[t[0],t[2],t[1],t[3]],i=t[0]*e[0]+t[1]*e[2],s=t[0]*e[1]+t[1]*e[3],n=t[2]*e[0]+t[3]*e[2],r=t[2]*e[1]+t[3]*e[3],a=(i+r)/2,o=Math.sqrt((i+r)**2-4*(i*r-n*s))/2,h=a+o||1,l=a-o||1;return[Math.sqrt(h),Math.sqrt(l)]}static normalizeRect(t){const e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){const i=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),s=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(i>s)return null;const n=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),r=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return n>r?null:[i,n,s,r]}static#t(t,e,i,s,n,r,a,o,h,l){if(h<=0||h>=1)return;const d=1-h,c=h*h,u=c*h,p=d*(d*(d*t+3*h*e)+3*c*i)+u*s,g=d*(d*(d*n+3*h*r)+3*c*a)+u*o;l[0]=Math.min(l[0],p),l[1]=Math.min(l[1],g),l[2]=Math.max(l[2],p),l[3]=Math.max(l[3],g)}static#e(t,e,i,s,n,r,a,o,h,l,d,c){if(Math.abs(h)<1e-12)return void(Math.abs(l)>=1e-12&&this.#t(t,e,i,s,n,r,a,o,-d/l,c));const u=l**2-4*d*h;if(u<0)return;const p=Math.sqrt(u),g=2*h;this.#t(t,e,i,s,n,r,a,o,(-l+p)/g,c),this.#t(t,e,i,s,n,r,a,o,(-l-p)/g,c)}static bezierBoundingBox(t,e,i,s,n,r,a,o,h){return h?(h[0]=Math.min(h[0],t,a),h[1]=Math.min(h[1],e,o),h[2]=Math.max(h[2],t,a),h[3]=Math.max(h[3],e,o)):h=[Math.min(t,a),Math.min(e,o),Math.max(t,a),Math.max(e,o)],this.#e(t,i,n,a,e,s,r,o,3*(3*(i-n)-t+a),6*(t-2*i+n),3*(i-t),h),this.#e(t,i,n,a,e,s,r,o,3*(3*(s-r)-e+o),6*(e-2*s+r),3*(s-e),h),h}}let Et=null,Ct=null;function Mt(t){return Et||(Et=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,Ct=new Map([["ﬅ","ſt"]])),t.replaceAll(Et,((t,e,i)=>e?e.normalize("NFKC"):Ct.get(i)))}const Tt="pdfjs_internal_id_",kt=0,Pt=1,Rt=2,Dt=3,Lt=4,It=5,Ft=6,Nt=7,Ot=8;class zt{constructor(){this.constructor===zt&&ot("Cannot initialize BaseFilterFactory.")}addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,i,s,n){return"none"}destroy(t=!1){}}class Ht{#i=!1;constructor({enableHWA:t=!1}={}){this.constructor===Ht&&ot("Cannot initialize BaseCanvasFactory."),this.#i=t}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const i=this._createCanvas(t,e);return{canvas:i,context:i.getContext("2d",{willReadFrequently:!this.#i})}}reset(t,e,i){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||i<=0)throw new Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=i}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){ot("Abstract method `_createCanvas` called.")}}class Bt{constructor({baseUrl:t=null,isCompressed:e=!0}){this.constructor===Bt&&ot("Cannot initialize BaseCMapReaderFactory."),this.baseUrl=t,this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":""),i=this.isCompressed?Z.BINARY:Z.NONE;return this._fetchData(e,i).catch((t=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)}))}_fetchData(t,e){ot("Abstract method `_fetchData` called.")}}class jt{constructor({baseUrl:t=null}){this.constructor===jt&&ot("Cannot initialize BaseStandardFontDataFactory."),this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetchData(e).catch((t=>{throw new Error(`Unable to load font data at: ${e}`)}))}_fetchData(t){ot("Abstract method `_fetchData` called.")}}class Wt{constructor(){this.constructor===Wt&&ot("Cannot initialize BaseSVGFactory.")}create(t,e,i=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const s=this._createSVG("svg:svg");return s.setAttribute("version","1.1"),i||(s.setAttribute("width",`${t}px`),s.setAttribute("height",`${e}px`)),s.setAttribute("preserveAspectRatio","none"),s.setAttribute("viewBox",`0 0 ${t} ${e}`),s}createElement(t){if("string"!=typeof t)throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){ot("Abstract method `_createSVG` called.")}}const Ut="http://www.w3.org/2000/svg";class $t{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}async function Gt(t,e="text"){if(ie(t,document.baseURI)){const i=await fetch(t);if(!i.ok)throw new Error(i.statusText);switch(e){case"arraybuffer":return i.arrayBuffer();case"blob":return i.blob();case"json":return i.json()}return i.text()}return new Promise(((i,s)=>{const n=new XMLHttpRequest;n.open("GET",t,!0),n.responseType=e,n.onreadystatechange=()=>{if(n.readyState===XMLHttpRequest.DONE)if(200!==n.status&&0!==n.status)s(new Error(n.statusText));else{switch(e){case"arraybuffer":case"blob":case"json":return void i(n.response)}i(n.responseText)}},n.send(null)}))}class Vt extends Bt{_fetchData(t,e){return Gt(t,this.isCompressed?"arraybuffer":"text").then((t=>({cMapData:t instanceof ArrayBuffer?new Uint8Array(t):wt(t),compressionType:e})))}}class qt extends jt{_fetchData(t){return Gt(t,"arraybuffer").then((t=>new Uint8Array(t)))}}class Xt extends Wt{_createSVG(t){return document.createElementNS(Ut,t)}}class Yt{constructor({viewBox:t,scale:e,rotation:i,offsetX:s=0,offsetY:n=0,dontFlip:r=!1}){this.viewBox=t,this.scale=e,this.rotation=i,this.offsetX=s,this.offsetY=n;const a=(t[2]+t[0])/2,o=(t[3]+t[1])/2;let h,l,d,c,u,p,g,f;switch((i%=360)<0&&(i+=360),i){case 180:h=-1,l=0,d=0,c=1;break;case 90:h=0,l=1,d=1,c=0;break;case 270:h=0,l=-1,d=-1,c=0;break;case 0:h=1,l=0,d=0,c=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}r&&(d=-d,c=-c),0===h?(u=Math.abs(o-t[1])*e+s,p=Math.abs(a-t[0])*e+n,g=(t[3]-t[1])*e,f=(t[2]-t[0])*e):(u=Math.abs(a-t[0])*e+s,p=Math.abs(o-t[1])*e+n,g=(t[2]-t[0])*e,f=(t[3]-t[1])*e),this.transform=[h*e,l*e,d*e,c*e,u-h*e*a-d*e*o,p-l*e*a-c*e*o],this.width=g,this.height=f}get rawDims(){const{viewBox:t}=this;return dt(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:i=this.offsetX,offsetY:s=this.offsetY,dontFlip:n=!1}={}){return new Yt({viewBox:this.viewBox.slice(),scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:n})}convertToViewportPoint(t,e){return St.applyTransform([t,e],this.transform)}convertToViewportRectangle(t){const e=St.applyTransform([t[0],t[1]],this.transform),i=St.applyTransform([t[2],t[3]],this.transform);return[e[0],e[1],i[0],i[1]]}convertToPdfPoint(t,e){return St.applyInverseTransform([t,e],this.transform)}}class Kt extends ct{constructor(t,e=0){super(t,"RenderingCancelledException"),this.extraDelay=e}}function Qt(t){const e=t.length;let i=0;for(;i<e&&""===t[i].trim();)i++;return"data:"===t.substring(i,i+5).toLowerCase()}function Jt(t){return"string"==typeof t&&/\.pdf$/i.test(t)}function Zt(t){return[t]=t.split(/[#?]/,1),t.substring(t.lastIndexOf("/")+1)}function te(t,e="document.pdf"){if("string"!=typeof t)return e;if(Qt(t))return at('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),e;const i=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,s=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(t);let n=i.exec(s[1])||i.exec(s[2])||i.exec(s[3]);if(n&&(n=n[0],n.includes("%")))try{n=i.exec(decodeURIComponent(n))[0]}catch{}return n||e}class ee{started=Object.create(null);times=[];time(t){t in this.started&&at(`Timer is already running for ${t}`),this.started[t]=Date.now()}timeEnd(t){t in this.started||at(`Timer has not been started for ${t}`),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){const t=[];let e=0;for(const{name:t}of this.times)e=Math.max(t.length,e);for(const{name:i,start:s,end:n}of this.times)t.push(`${i.padEnd(e)} ${n-s}ms\n`);return t.join("")}}function ie(t,e){try{const{protocol:i}=e?new URL(t,e):new URL(t);return"http:"===i||"https:"===i}catch{return!1}}function se(t){t.preventDefault()}function ne(t){console.log("Deprecated API usage: "+t)}let re;class ae{static toDateObject(t){if(!t||"string"!=typeof t)return null;re||=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?");const e=re.exec(t);if(!e)return null;const i=parseInt(e[1],10);let s=parseInt(e[2],10);s=s>=1&&s<=12?s-1:0;let n=parseInt(e[3],10);n=n>=1&&n<=31?n:1;let r=parseInt(e[4],10);r=r>=0&&r<=23?r:0;let a=parseInt(e[5],10);a=a>=0&&a<=59?a:0;let o=parseInt(e[6],10);o=o>=0&&o<=59?o:0;const h=e[7]||"Z";let l=parseInt(e[8],10);l=l>=0&&l<=23?l:0;let d=parseInt(e[9],10)||0;return d=d>=0&&d<=59?d:0,"-"===h?(r+=l,a+=d):"+"===h&&(r-=l,a-=d),new Date(Date.UTC(i,s,n,r,a,o))}}function oe(t,{scale:e=1,rotation:i=0}){const{width:s,height:n}=t.attributes.style,r=[0,0,parseInt(s),parseInt(n)];return new Yt({viewBox:r,scale:e,rotation:i})}function he(t){if(t.startsWith("#")){const e=parseInt(t.slice(1),16);return[(16711680&e)>>16,(65280&e)>>8,255&e]}return t.startsWith("rgb(")?t.slice(4,-1).split(",").map((t=>parseInt(t))):t.startsWith("rgba(")?t.slice(5,-1).split(",").map((t=>parseInt(t))).slice(0,3):(at(`Not a valid color format: "${t}"`),[0,0,0])}function le(t){const{a:e,b:i,c:s,d:n,e:r,f:a}=t.getTransform();return[e,i,s,n,r,a]}function de(t){const{a:e,b:i,c:s,d:n,e:r,f:a}=t.getTransform().invertSelf();return[e,i,s,n,r,a]}function ce(t,e,i=!1,s=!0){if(e instanceof Yt){const{pageWidth:s,pageHeight:n}=e.rawDims,{style:r}=t,a=xt.isCSSRoundSupported,o=`var(--scale-factor) * ${s}px`,h=`var(--scale-factor) * ${n}px`,l=a?`round(${o}, 1px)`:`calc(${o})`,d=a?`round(${h}, 1px)`:`calc(${h})`;i&&e.rotation%180!=0?(r.width=d,r.height=l):(r.width=l,r.height=d)}s&&t.setAttribute("data-main-rotation",e.rotation)}class ue{#s=null;#n=null;#r;#a=null;constructor(t){this.#r=t}render(){const t=this.#s=document.createElement("div");t.className="editToolbar",t.setAttribute("role","toolbar");const e=this.#r._uiManager._signal;t.addEventListener("contextmenu",se,{signal:e}),t.addEventListener("pointerdown",ue.#o,{signal:e});const i=this.#a=document.createElement("div");i.className="buttons",t.append(i);const s=this.#r.toolbarPosition;if(s){const{style:e}=t,i="ltr"===this.#r._uiManager.direction?1-s[0]:s[0];e.insetInlineEnd=100*i+"%",e.top=`calc(${100*s[1]}% + var(--editor-toolbar-vert-offset))`}return this.#h(),t}static#o(t){t.stopPropagation()}#l(t){this.#r._focusEventsAllowed=!1,t.preventDefault(),t.stopPropagation()}#d(t){this.#r._focusEventsAllowed=!0,t.preventDefault(),t.stopPropagation()}#c(t){const e=this.#r._uiManager._signal;t.addEventListener("focusin",this.#l.bind(this),{capture:!0,signal:e}),t.addEventListener("focusout",this.#d.bind(this),{capture:!0,signal:e}),t.addEventListener("contextmenu",se,{signal:e})}hide(){this.#s.classList.add("hidden"),this.#n?.hideDropdown()}show(){this.#s.classList.remove("hidden")}#h(){const t=document.createElement("button");t.className="delete",t.tabIndex=0,t.setAttribute("data-l10n-id",`pdfjs-editor-remove-${this.#r.editorType}-button`),this.#c(t),t.addEventListener("click",(t=>{this.#r._uiManager.delete()}),{signal:this.#r._uiManager._signal}),this.#a.append(t)}get#u(){const t=document.createElement("div");return t.className="divider",t}addAltTextButton(t){this.#c(t),this.#a.prepend(t,this.#u)}addColorPicker(t){this.#n=t;const e=t.renderButton();this.#c(e),this.#a.prepend(e,this.#u)}remove(){this.#s.remove(),this.#n?.destroy(),this.#n=null}}class pe{#a=null;#s=null;#p;constructor(t){this.#p=t}#g(){const t=this.#s=document.createElement("div");t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",se,{signal:this.#p._signal});const e=this.#a=document.createElement("div");return e.className="buttons",t.append(e),this.#f(),t}#m(t,e){let i=0,s=0;for(const n of t){const t=n.y+n.height;if(t<i)continue;const r=n.x+(e?n.width:0);t>i?(s=r,i=t):e?r>s&&(s=r):r<s&&(s=r)}return[e?1-s:s,i]}show(t,e,i){const[s,n]=this.#m(e,i),{style:r}=this.#s||=this.#g();t.append(this.#s),r.insetInlineEnd=100*s+"%",r.top=`calc(${100*n}% + var(--editor-toolbar-vert-offset))`}hide(){this.#s.remove()}#f(){const t=document.createElement("button");t.className="highlightButton",t.tabIndex=0,t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e),e.className="visuallyHidden",e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");const i=this.#p._signal;t.addEventListener("contextmenu",se,{signal:i}),t.addEventListener("click",(()=>{this.#p.highlightSelection("floating_button")}),{signal:i}),this.#a.append(t)}}function ge(t,e,i){for(const s of i)e.addEventListener(s,t[s].bind(t))}class fe{#b=0;get id(){return"pdfjs_internal_editor_"+this.#b++}}class me{#v=function(){if("undefined"!=typeof crypto&&"function"==typeof crypto?.randomUUID)return crypto.randomUUID();const t=new Uint8Array(32);if("undefined"!=typeof crypto&&"function"==typeof crypto?.getRandomValues)crypto.getRandomValues(t);else for(let e=0;e<32;e++)t[e]=Math.floor(255*Math.random());return yt(t)}();#b=0;#y=null;static get _isSVGFittingCanvas(){const t=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),e=new Image;e.src='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>';return dt(this,"_isSVGFittingCanvas",e.decode().then((()=>(t.drawImage(e,0,0,1,1,0,0,1,3),0===new Uint32Array(t.getImageData(0,0,1,1).data.buffer)[0]))))}async#w(t,e){this.#y||=new Map;let i=this.#y.get(t);if(null===i)return null;if(i?.bitmap)return i.refCounter+=1,i;try{let t;if(i||={bitmap:null,id:`image_${this.#v}_${this.#b++}`,refCounter:0,isSvg:!1},"string"==typeof e?(i.url=e,t=await Gt(e,"blob")):t=i.file=e,"image/svg+xml"===t.type){const e=me._isSVGFittingCanvas,s=new FileReader,n=new Image,r=new Promise(((t,r)=>{n.onload=()=>{i.bitmap=n,i.isSvg=!0,t()},s.onload=async()=>{const t=i.svgUrl=s.result;n.src=await e?`${t}#svgView(preserveAspectRatio(none))`:t},n.onerror=s.onerror=r}));s.readAsDataURL(t),await r}else i.bitmap=await createImageBitmap(t);i.refCounter=1}catch(t){console.error(t),i=null}return this.#y.set(t,i),i&&this.#y.set(i.id,i),i}async getFromFile(t){const{lastModified:e,name:i,size:s,type:n}=t;return this.#w(`${e}_${i}_${s}_${n}`,t)}async getFromUrl(t){return this.#w(t,t)}async getFromId(t){this.#y||=new Map;const e=this.#y.get(t);return e?e.bitmap?(e.refCounter+=1,e):e.file?this.getFromFile(e.file):this.getFromUrl(e.url):null}getSvgUrl(t){const e=this.#y.get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){this.#y||=new Map;const e=this.#y.get(t);e&&(e.refCounter-=1,0===e.refCounter&&(e.bitmap=null))}isValidId(t){return t.startsWith(`image_${this.#v}_`)}}class be{#A=[];#x=!1;#_;#S=-1;constructor(t=128){this.#_=t}add({cmd:t,undo:e,post:i,mustExec:s,type:n=NaN,overwriteIfSameType:r=!1,keepUndo:a=!1}){if(s&&t(),this.#x)return;const o={cmd:t,undo:e,post:i,type:n};if(-1===this.#S)return this.#A.length>0&&(this.#A.length=0),this.#S=0,void this.#A.push(o);if(r&&this.#A[this.#S].type===n)return a&&(o.undo=this.#A[this.#S].undo),void(this.#A[this.#S]=o);const h=this.#S+1;h===this.#_?this.#A.splice(0,1):(this.#S=h,h<this.#A.length&&this.#A.splice(h)),this.#A.push(o)}undo(){if(-1===this.#S)return;this.#x=!0;const{undo:t,post:e}=this.#A[this.#S];t(),e?.(),this.#x=!1,this.#S-=1}redo(){if(this.#S<this.#A.length-1){this.#S+=1,this.#x=!0;const{cmd:t,post:e}=this.#A[this.#S];t(),e?.(),this.#x=!1}}hasSomethingToUndo(){return-1!==this.#S}hasSomethingToRedo(){return this.#S<this.#A.length-1}destroy(){this.#A=null}}class ve{constructor(t){this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:e}=xt.platform;for(const[i,s,n={}]of t)for(const t of i){const i=t.startsWith("mac+");e&&i?(this.callbacks.set(t.slice(4),{callback:s,options:n}),this.allKeys.add(t.split("+").at(-1))):e||i||(this.callbacks.set(t,{callback:s,options:n}),this.allKeys.add(t.split("+").at(-1)))}}#E(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);const e=this.buffer.join("+");return this.buffer.length=0,e}exec(t,e){if(!this.allKeys.has(e.key))return;const i=this.callbacks.get(this.#E(e));if(!i)return;const{callback:s,options:{bubbles:n=!1,args:r=[],checker:a=null}}=i;a&&!a(t,e)||(s.bind(t,...r,e)(),n||(e.stopPropagation(),e.preventDefault()))}}class ye{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);return function(t){const e=document.createElement("span");e.style.visibility="hidden",document.body.append(e);for(const i of t.keys()){e.style.color=i;const s=window.getComputedStyle(e).color;t.set(i,he(s))}e.remove()}(t),dt(this,"_colors",t)}convert(t){const e=he(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[t,i]of this._colors)if(i.every(((t,i)=>t===e[i])))return ye._colorsMapping.get(t);return e}getHexCode(t){const e=this._colors.get(t);return e?St.makeHexColor(...e):t}}class we{#C=new AbortController;#M=null;#T=new Map;#k=new Map;#P=null;#R=null;#D=null;#L=new be;#I=0;#F=new Set;#N=null;#O=null;#z=new Set;#H=!1;#B=null;#j=null;#W=null;#U=!1;#$=null;#G=new fe;#V=!1;#q=!1;#X=null;#Y=null;#K=null;#Q=y.NONE;#J=new Set;#Z=null;#tt=null;#et=null;#it=this.blur.bind(this);#st=this.focus.bind(this);#nt=this.copy.bind(this);#rt=this.cut.bind(this);#at=this.paste.bind(this);#ot=this.keydown.bind(this);#ht=this.keyup.bind(this);#lt=this.onEditingAction.bind(this);#dt=this.onPageChanging.bind(this);#ct=this.onScaleChanging.bind(this);#ut=this.onRotationChanging.bind(this);#pt={isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1};#gt=[0,0];#ft=null;#mt=null;#bt=null;static TRANSLATE_SMALL=1;static TRANSLATE_BIG=10;static get _keyboardManager(){const t=we.prototype,e=t=>t.#mt.contains(document.activeElement)&&"BUTTON"!==document.activeElement.tagName&&t.hasSomethingToControl(),i=(t,{target:e})=>{if(e instanceof HTMLInputElement){const{type:t}=e;return"text"!==t&&"number"!==t}return!0},s=this.TRANSLATE_SMALL,n=this.TRANSLATE_BIG;return dt(this,"_keyboardManager",new ve([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:i}],[["ctrl+z","mac+meta+z"],t.undo,{checker:i}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:i}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:i}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#mt.contains(e)&&!t.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#mt.contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-s,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-n,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[s,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[n,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-s],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-n],checker:e}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,s],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,n],checker:e}]]))}constructor(t,e,i,s,n,r,a,o,h){this._signal=this.#C.signal,this.#mt=t,this.#bt=e,this.#P=i,this._eventBus=s,this._eventBus._on("editingaction",this.#lt),this._eventBus._on("pagechanging",this.#dt),this._eventBus._on("scalechanging",this.#ct),this._eventBus._on("rotationchanging",this.#ut),this.#vt(),this.#yt(),this.#wt(),this.#R=n.annotationStorage,this.#B=n.filterFactory,this.#tt=r,this.#W=a||null,this.#H=o,this.#K=h||null,this.viewParameters={realScale:$t.PDF_TO_CSS_UNITS,rotation:0},this.isShiftKeyDown=!1}destroy(){this.#C?.abort(),this.#C=null,this._signal=null,this._eventBus._off("editingaction",this.#lt),this._eventBus._off("pagechanging",this.#dt),this._eventBus._off("scalechanging",this.#ct),this._eventBus._off("rotationchanging",this.#ut);for(const t of this.#k.values())t.destroy();this.#k.clear(),this.#T.clear(),this.#z.clear(),this.#M=null,this.#J.clear(),this.#L.destroy(),this.#P?.destroy(),this.#$?.hide(),this.#$=null,this.#j&&(clearTimeout(this.#j),this.#j=null),this.#ft&&(clearTimeout(this.#ft),this.#ft=null)}async mlGuess(t){return this.#K?.guess(t)||null}get hasMLManager(){return!!this.#K}get hcmFilter(){return dt(this,"hcmFilter",this.#tt?this.#B.addHCMFilter(this.#tt.foreground,this.#tt.background):"none")}get direction(){return dt(this,"direction",getComputedStyle(this.#mt).direction)}get highlightColors(){return dt(this,"highlightColors",this.#W?new Map(this.#W.split(",").map((t=>t.split("=").map((t=>t.trim()))))):null)}get highlightColorNames(){return dt(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,(t=>t.reverse()))):null)}setMainHighlightColorPicker(t){this.#Y=t}editAltText(t){this.#P?.editAltText(this,t)}onPageChanging({pageNumber:t}){this.#I=t-1}focusMainContainer(){this.#mt.focus()}findParent(t,e){for(const i of this.#k.values()){const{x:s,y:n,width:r,height:a}=i.div.getBoundingClientRect();if(t>=s&&t<=s+r&&e>=n&&e<=n+a)return i}return null}disableUserSelect(t=!1){this.#bt.classList.toggle("noUserSelect",t)}addShouldRescale(t){this.#z.add(t)}removeShouldRescale(t){this.#z.delete(t)}onScaleChanging({scale:t}){this.commitOrRemove(),this.viewParameters.realScale=t*$t.PDF_TO_CSS_UNITS;for(const t of this.#z)t.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove(),this.viewParameters.rotation=t}#At({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:i,anchorOffset:s,focusNode:n,focusOffset:r}=e,a=e.toString(),o=this.#At(e).closest(".textLayer"),h=this.getSelectionBoxes(o);if(h){e.empty(),this.#Q===y.NONE&&(this._eventBus.dispatch("showannotationeditorui",{source:this,mode:y.HIGHLIGHT}),this.showAllEditors("highlight",!0,!0));for(const e of this.#k.values())if(e.hasTextLayer(o)){e.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:h,anchorNode:i,anchorOffset:s,focusNode:n,focusOffset:r,text:a});break}}}#xt(){const t=document.getSelection();if(!t||t.isCollapsed)return;const e=this.#At(t).closest(".textLayer"),i=this.getSelectionBoxes(e);i&&(this.#$||=new pe(this),this.#$.show(e,i,"ltr"===this.direction))}addToAnnotationStorage(t){t.isEmpty()||!this.#R||this.#R.has(t.id)||this.#R.setValue(t.id,t)}#_t(){const t=document.getSelection();if(!t||t.isCollapsed)return void(this.#Z&&(this.#$?.hide(),this.#Z=null,this.#St({hasSelectedText:!1})));const{anchorNode:e}=t;if(e===this.#Z)return;if(this.#At(t).closest(".textLayer")){if(this.#$?.hide(),this.#Z=e,this.#St({hasSelectedText:!0}),(this.#Q===y.HIGHLIGHT||this.#Q===y.NONE)&&(this.#Q===y.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0),this.#U=this.isShiftKeyDown,!this.isShiftKeyDown)){const t=this._signal,e=t=>{"pointerup"===t.type&&0!==t.button||(window.removeEventListener("pointerup",e),window.removeEventListener("blur",e),"pointerup"===t.type&&this.#Et("main_toolbar"))};window.addEventListener("pointerup",e,{signal:t}),window.addEventListener("blur",e,{signal:t})}}else this.#Z&&(this.#$?.hide(),this.#Z=null,this.#St({hasSelectedText:!1}))}#Et(t=""){this.#Q===y.HIGHLIGHT?this.highlightSelection(t):this.#H&&this.#xt()}#vt(){document.addEventListener("selectionchange",this.#_t.bind(this),{signal:this._signal})}#Ct(){const t=this._signal;window.addEventListener("focus",this.#st,{signal:t}),window.addEventListener("blur",this.#it,{signal:t})}#Mt(){window.removeEventListener("focus",this.#st),window.removeEventListener("blur",this.#it)}blur(){if(this.isShiftKeyDown=!1,this.#U&&(this.#U=!1,this.#Et("main_toolbar")),!this.hasSelection)return;const{activeElement:t}=document;for(const e of this.#J)if(e.div.contains(t)){this.#X=[e,t],e._focusEventsAllowed=!1;break}}focus(){if(!this.#X)return;const[t,e]=this.#X;this.#X=null,e.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0,signal:this._signal}),e.focus()}#wt(){const t=this._signal;window.addEventListener("keydown",this.#ot,{signal:t}),window.addEventListener("keyup",this.#ht,{signal:t})}#Tt(){window.removeEventListener("keydown",this.#ot),window.removeEventListener("keyup",this.#ht)}#kt(){const t=this._signal;document.addEventListener("copy",this.#nt,{signal:t}),document.addEventListener("cut",this.#rt,{signal:t}),document.addEventListener("paste",this.#at,{signal:t})}#Pt(){document.removeEventListener("copy",this.#nt),document.removeEventListener("cut",this.#rt),document.removeEventListener("paste",this.#at)}#yt(){const t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t}),document.addEventListener("drop",this.drop.bind(this),{signal:t})}addEditListeners(){this.#wt(),this.#kt()}removeEditListeners(){this.#Tt(),this.#Pt()}dragOver(t){for(const{type:e}of t.dataTransfer.items)for(const i of this.#O)if(i.isHandlingMimeForPasting(e))return t.dataTransfer.dropEffect="copy",void t.preventDefault()}drop(t){for(const e of t.dataTransfer.items)for(const i of this.#O)if(i.isHandlingMimeForPasting(e.type))return i.paste(e,this.currentLayer),void t.preventDefault()}copy(t){if(t.preventDefault(),this.#M?.commitOrRemove(),!this.hasSelection)return;const e=[];for(const t of this.#J){const i=t.serialize(!0);i&&e.push(i)}0!==e.length&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t),this.delete()}paste(t){t.preventDefault();const{clipboardData:e}=t;for(const t of e.items)for(const e of this.#O)if(e.isHandlingMimeForPasting(t.type))return void e.paste(t,this.currentLayer);let i=e.getData("application/pdfjs");if(!i)return;try{i=JSON.parse(i)}catch(t){return void at(`paste: "${t.message}".`)}if(!Array.isArray(i))return;this.unselectAll();const s=this.currentLayer;try{const t=[];for(const e of i){const i=s.deserialize(e);if(!i)return;t.push(i)}const e=()=>{for(const e of t)this.#Rt(e);this.#Dt(t)},n=()=>{for(const e of t)e.remove()};this.addCommands({cmd:e,undo:n,mustExec:!0})}catch(t){at(`paste: "${t.message}".`)}}keydown(t){this.isShiftKeyDown||"Shift"!==t.key||(this.isShiftKeyDown=!0),this.#Q===y.NONE||this.isEditorHandlingKeyboard||we._keyboardManager.exec(this,t)}keyup(t){this.isShiftKeyDown&&"Shift"===t.key&&(this.isShiftKeyDown=!1,this.#U&&(this.#U=!1,this.#Et("main_toolbar")))}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu")}}#St(t){Object.entries(t).some((([t,e])=>this.#pt[t]!==e))&&(this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#pt,t)}),this.#Q===y.HIGHLIGHT&&!1===t.hasSelectedEditor&&this.#Lt([[w.HIGHLIGHT_FREE,!0]]))}#Lt(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){t?(this.#Ct(),this.#kt(),this.#St({isEditing:this.#Q!==y.NONE,isEmpty:this.#It(),hasSomethingToUndo:this.#L.hasSomethingToUndo(),hasSomethingToRedo:this.#L.hasSomethingToRedo(),hasSelectedEditor:!1})):(this.#Mt(),this.#Pt(),this.#St({isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(t){if(!this.#O){this.#O=t;for(const t of this.#O)this.#Lt(t.defaultPropertiesToUpdate)}}getId(){return this.#G.id}get currentLayer(){return this.#k.get(this.#I)}getLayer(t){return this.#k.get(t)}get currentPageIndex(){return this.#I}addLayer(t){this.#k.set(t.pageIndex,t),this.#V?t.enable():t.disable()}removeLayer(t){this.#k.delete(t.pageIndex)}updateMode(t,e=null,i=!1){if(this.#Q!==t){if(this.#Q=t,t===y.NONE)return this.setEditingState(!1),void this.#Ft();this.setEditingState(!0),this.#Nt(),this.unselectAll();for(const e of this.#k.values())e.updateMode(t);if(e||!i){if(e)for(const t of this.#T.values())if(t.annotationElementId===e){this.setSelected(t),t.enterInEditMode();break}}else this.addNewEditorFromKeyboard()}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t!==this.#Q&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){if(this.#O){switch(t){case w.CREATE:return void this.currentLayer.addNewEditor();case w.HIGHLIGHT_DEFAULT_COLOR:this.#Y?.updateColor(e);break;case w.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}}),(this.#et||=new Map).set(t,e),this.showAllEditors("highlight",e)}for(const i of this.#J)i.updateParams(t,e);for(const i of this.#O)i.updateDefaultParams(t,e)}}showAllEditors(t,e,i=!1){for(const i of this.#T.values())i.editorType===t&&i.show(e);(this.#et?.get(w.HIGHLIGHT_SHOW_ALL)??!0)!==e&&this.#Lt([[w.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(this.#q!==t){this.#q=t;for(const e of this.#k.values())t?e.disableClick():e.enableClick(),e.div.classList.toggle("waiting",t)}}#Nt(){if(!this.#V){this.#V=!0;for(const t of this.#k.values())t.enable();for(const t of this.#T.values())t.enable()}}#Ft(){if(this.unselectAll(),this.#V){this.#V=!1;for(const t of this.#k.values())t.disable();for(const t of this.#T.values())t.disable()}}getEditors(t){const e=[];for(const i of this.#T.values())i.pageIndex===t&&e.push(i);return e}getEditor(t){return this.#T.get(t)}addEditor(t){this.#T.set(t.id,t)}removeEditor(t){t.div.contains(document.activeElement)&&(this.#j&&clearTimeout(this.#j),this.#j=setTimeout((()=>{this.focusMainContainer(),this.#j=null}),0)),this.#T.delete(t.id),this.unselect(t),t.annotationElementId&&this.#F.has(t.annotationElementId)||this.#R?.remove(t.id)}addDeletedAnnotationElement(t){this.#F.add(t.annotationElementId),this.addChangedExistingAnnotation(t),t.deleted=!0}isDeletedAnnotationElement(t){return this.#F.has(t)}removeDeletedAnnotationElement(t){this.#F.delete(t.annotationElementId),this.removeChangedExistingAnnotation(t),t.deleted=!1}#Rt(t){const e=this.#k.get(t.pageIndex);e?e.addOrRebuild(t):(this.addEditor(t),this.addToAnnotationStorage(t))}setActiveEditor(t){this.#M!==t&&(this.#M=t,t&&this.#Lt(t.propertiesToUpdate))}get#Ot(){let t=null;for(t of this.#J);return t}updateUI(t){this.#Ot===t&&this.#Lt(t.propertiesToUpdate)}toggleSelected(t){if(this.#J.has(t))return this.#J.delete(t),t.unselect(),void this.#St({hasSelectedEditor:this.hasSelection});this.#J.add(t),t.select(),this.#Lt(t.propertiesToUpdate),this.#St({hasSelectedEditor:!0})}setSelected(t){for(const e of this.#J)e!==t&&e.unselect();this.#J.clear(),this.#J.add(t),t.select(),this.#Lt(t.propertiesToUpdate),this.#St({hasSelectedEditor:!0})}isSelected(t){return this.#J.has(t)}get firstSelectedEditor(){return this.#J.values().next().value}unselect(t){t.unselect(),this.#J.delete(t),this.#St({hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==this.#J.size}get isEnterHandled(){return 1===this.#J.size&&this.firstSelectedEditor.isEnterHandled}undo(){this.#L.undo(),this.#St({hasSomethingToUndo:this.#L.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#It()})}redo(){this.#L.redo(),this.#St({hasSomethingToUndo:!0,hasSomethingToRedo:this.#L.hasSomethingToRedo(),isEmpty:this.#It()})}addCommands(t){this.#L.add(t),this.#St({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#It()})}#It(){if(0===this.#T.size)return!0;if(1===this.#T.size)for(const t of this.#T.values())return t.isEmpty();return!1}delete(){if(this.commitOrRemove(),!this.hasSelection)return;const t=[...this.#J];this.addCommands({cmd:()=>{for(const e of t)e.remove()},undo:()=>{for(const e of t)this.#Rt(e)},mustExec:!0})}commitOrRemove(){this.#M?.commitOrRemove()}hasSomethingToControl(){return this.#M||this.hasSelection}#Dt(t){for(const t of this.#J)t.unselect();this.#J.clear();for(const e of t)e.isEmpty()||(this.#J.add(e),e.select());this.#St({hasSelectedEditor:this.hasSelection})}selectAll(){for(const t of this.#J)t.commit();this.#Dt(this.#T.values())}unselectAll(){if((!this.#M||(this.#M.commitOrRemove(),this.#Q===y.NONE))&&this.hasSelection){for(const t of this.#J)t.unselect();this.#J.clear(),this.#St({hasSelectedEditor:!1})}}translateSelectedEditors(t,e,i=!1){if(i||this.commitOrRemove(),!this.hasSelection)return;this.#gt[0]+=t,this.#gt[1]+=e;const[s,n]=this.#gt,r=[...this.#J];this.#ft&&clearTimeout(this.#ft),this.#ft=setTimeout((()=>{this.#ft=null,this.#gt[0]=this.#gt[1]=0,this.addCommands({cmd:()=>{for(const t of r)this.#T.has(t.id)&&t.translateInPage(s,n)},undo:()=>{for(const t of r)this.#T.has(t.id)&&t.translateInPage(-s,-n)},mustExec:!1})}),1e3);for(const i of r)i.translateInPage(t,e)}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),this.#N=new Map;for(const t of this.#J)this.#N.set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!this.#N)return!1;this.disableUserSelect(!1);const t=this.#N;this.#N=null;let e=!1;for(const[{x:i,y:s,pageIndex:n},r]of t)r.newX=i,r.newY=s,r.newPageIndex=n,e||=i!==r.savedX||s!==r.savedY||n!==r.savedPageIndex;if(!e)return!1;const i=(t,e,i,s)=>{if(this.#T.has(t.id)){const n=this.#k.get(s);n?t._setParentAndPosition(n,e,i):(t.pageIndex=s,t.x=e,t.y=i)}};return this.addCommands({cmd:()=>{for(const[e,{newX:s,newY:n,newPageIndex:r}]of t)i(e,s,n,r)},undo:()=>{for(const[e,{savedX:s,savedY:n,savedPageIndex:r}]of t)i(e,s,n,r)},mustExec:!0}),!0}dragSelectedEditors(t,e){if(this.#N)for(const i of this.#N.keys())i.drag(t,e)}rebuild(t){if(null===t.parent){const e=this.getLayer(t.pageIndex);e?(e.changeParent(t),e.addOrRebuild(t)):(this.addEditor(t),this.addToAnnotationStorage(t),t.rebuild())}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){return this.getActive()?.shouldGetKeyboardEvents()||1===this.#J.size&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return this.#M===t}getActive(){return this.#M}getMode(){return this.#Q}get imageManager(){return dt(this,"imageManager",new me)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let i=0,s=e.rangeCount;i<s;i++)if(!t.contains(e.getRangeAt(i).commonAncestorContainer))return null;const{x:i,y:s,width:n,height:r}=t.getBoundingClientRect();let a;switch(t.getAttribute("data-main-rotation")){case"90":a=(t,e,a,o)=>({x:(e-s)/r,y:1-(t+a-i)/n,width:o/r,height:a/n});break;case"180":a=(t,e,a,o)=>({x:1-(t+a-i)/n,y:1-(e+o-s)/r,width:a/n,height:o/r});break;case"270":a=(t,e,a,o)=>({x:1-(e+o-s)/r,y:(t-i)/n,width:o/r,height:a/n});break;default:a=(t,e,a,o)=>({x:(t-i)/n,y:(e-s)/r,width:a/n,height:o/r})}const o=[];for(let t=0,i=e.rangeCount;t<i;t++){const i=e.getRangeAt(t);if(!i.collapsed)for(const{x:t,y:e,width:s,height:n}of i.getClientRects())0!==s&&0!==n&&o.push(a(t,e,s,n))}return 0===o.length?null:o}addChangedExistingAnnotation({annotationElementId:t,id:e}){(this.#D||=new Map).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){this.#D?.delete(t)}renderAnnotationElement(t){const e=this.#D?.get(t.data.id);if(!e)return;const i=this.#R.getRawValue(e);i&&(this.#Q!==y.NONE||i.hasBeenModified)&&i.renderAnnotationElement(t)}}class Ae{#zt="";#Ht=!1;#Bt=null;#jt=null;#Wt=null;#Ut=!1;#r=null;static _l10nPromise=null;constructor(t){this.#r=t}static initialize(t){Ae._l10nPromise||=t}async render(){const t=this.#Bt=document.createElement("button");t.className="altText";const e=await Ae._l10nPromise.get("pdfjs-editor-alt-text-button-label");t.textContent=e,t.setAttribute("aria-label",e),t.tabIndex="0";const i=this.#r._uiManager._signal;t.addEventListener("contextmenu",se,{signal:i}),t.addEventListener("pointerdown",(t=>t.stopPropagation()),{signal:i});const s=t=>{t.preventDefault(),this.#r._uiManager.editAltText(this.#r)};return t.addEventListener("click",s,{capture:!0,signal:i}),t.addEventListener("keydown",(e=>{e.target===t&&"Enter"===e.key&&(this.#Ut=!0,s(e))}),{signal:i}),await this.#$t(),t}finish(){this.#Bt&&(this.#Bt.focus({focusVisible:this.#Ut}),this.#Ut=!1)}isEmpty(){return!this.#zt&&!this.#Ht}get data(){return{altText:this.#zt,decorative:this.#Ht}}set data({altText:t,decorative:e}){this.#zt===t&&this.#Ht===e||(this.#zt=t,this.#Ht=e,this.#$t())}toggle(t=!1){this.#Bt&&(!t&&this.#Wt&&(clearTimeout(this.#Wt),this.#Wt=null),this.#Bt.disabled=!t)}destroy(){this.#Bt?.remove(),this.#Bt=null,this.#jt=null}async#$t(){const t=this.#Bt;if(!t)return;if(!this.#zt&&!this.#Ht)return t.classList.remove("done"),void this.#jt?.remove();t.classList.add("done"),Ae._l10nPromise.get("pdfjs-editor-alt-text-edit-button-label").then((e=>{t.setAttribute("aria-label",e)}));let e=this.#jt;if(!e){this.#jt=e=document.createElement("span"),e.className="tooltip",e.setAttribute("role","tooltip");const i=e.id=`alt-text-tooltip-${this.#r.id}`;t.setAttribute("aria-describedby",i);const s=100,n=this.#r._uiManager._signal;n.addEventListener("abort",(()=>{clearTimeout(this.#Wt),this.#Wt=null}),{once:!0}),t.addEventListener("mouseenter",(()=>{this.#Wt=setTimeout((()=>{this.#Wt=null,this.#jt.classList.add("show"),this.#r._reportTelemetry({action:"alt_text_tooltip"})}),s)}),{signal:n}),t.addEventListener("mouseleave",(()=>{this.#Wt&&(clearTimeout(this.#Wt),this.#Wt=null),this.#jt?.classList.remove("show")}),{signal:n})}e.innerText=this.#Ht?await Ae._l10nPromise.get("pdfjs-editor-alt-text-decorative-tooltip"):this.#zt,e.parentNode||t.append(e);this.#r.getImageForAltText()?.setAttribute("aria-describedby",e.id)}}class xe{#Gt=null;#Vt=null;#zt=null;#qt=!1;#Xt=!1;#Yt=null;#Kt=null;#Qt=this.focusin.bind(this);#Jt=this.focusout.bind(this);#Zt=null;#te="";#ee=!1;#ie=null;#se=!1;#ne=!1;#re=!1;#ae=null;#oe=0;#he=0;#le=null;_initialOptions=Object.create(null);_isVisible=!0;_uiManager=null;_focusEventsAllowed=!0;_l10nPromise=null;#de=!1;#ce=xe._zIndex++;static _borderLineWidth=-1;static _colorManager=new ye;static _zIndex=1;static _telemetryTimeout=1e3;static get _resizerKeyboardManager(){const t=xe.prototype._resizeWithKeyboard,e=we.TRANSLATE_SMALL,i=we.TRANSLATE_BIG;return dt(this,"_resizerKeyboardManager",new ve([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-i,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[i,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-i]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,i]}],[["Escape","mac+Escape"],xe.prototype._stopResizingWithKeyboard]]))}constructor(t){this.constructor===xe&&ot("Cannot initialize AnnotationEditor."),this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null,this._uiManager=t.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=t.isCentered,this._structTreeParentId=null;const{rotation:e,rawDims:{pageWidth:i,pageHeight:s,pageX:n,pageY:r}}=this.parent.viewport;this.rotation=e,this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[i,s],this.pageTranslation=[n,r];const[a,o]=this.parentDimensions;this.x=t.x/a,this.y=t.y/o,this.isAttachedToDOM=!1,this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get _defaultLineColor(){return dt(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new _e({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId,e.deleted=!0,e._uiManager.addToAnnotationStorage(e)}static initialize(t,e,i){if(xe._l10nPromise||=new Map(["pdfjs-editor-alt-text-button-label","pdfjs-editor-alt-text-edit-button-label","pdfjs-editor-alt-text-decorative-tooltip","pdfjs-editor-resizer-label-topLeft","pdfjs-editor-resizer-label-topMiddle","pdfjs-editor-resizer-label-topRight","pdfjs-editor-resizer-label-middleRight","pdfjs-editor-resizer-label-bottomRight","pdfjs-editor-resizer-label-bottomMiddle","pdfjs-editor-resizer-label-bottomLeft","pdfjs-editor-resizer-label-middleLeft"].map((e=>[e,t.get(e.replaceAll(/([A-Z])/g,(t=>`-${t.toLowerCase()}`)))]))),i?.strings)for(const e of i.strings)xe._l10nPromise.set(e,t.get(e));if(-1!==xe._borderLineWidth)return;const s=getComputedStyle(document.documentElement);xe._borderLineWidth=parseFloat(s.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){ot("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return this.#de}set _isDraggable(t){this.#de=t,this.div?.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(2*t),this.y+=this.width*t/(2*e);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*e/(2*t),this.y-=this.width*t/(2*e);break;default:this.x-=this.width/2,this.y-=this.height/2}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#ce}setParent(t){null!==t?(this.pageIndex=t.pageIndex,this.pageDimensions=t.pageDimensions):this.#ue(),this.parent=t}focusin(t){this._focusEventsAllowed&&(this.#ee?this.#ee=!1:this.parent.setSelected(this))}focusout(t){if(!this._focusEventsAllowed)return;if(!this.isAttachedToDOM)return;t.relatedTarget?.closest(`#${this.id}`)||(t.preventDefault(),this.parent?.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,i,s){const[n,r]=this.parentDimensions;[i,s]=this.screenToPageTranslation(i,s),this.x=(t+i)/n,this.y=(e+s)/r,this.fixAndSetPosition()}#pe([t,e],i,s){[i,s]=this.screenToPageTranslation(i,s),this.x+=i/t,this.y+=s/e,this.fixAndSetPosition()}translate(t,e){this.#pe(this.parentDimensions,t,e)}translateInPage(t,e){this.#ie||=[this.x,this.y],this.#pe(this.pageDimensions,t,e),this.div.scrollIntoView({block:"nearest"})}drag(t,e){this.#ie||=[this.x,this.y];const[i,s]=this.parentDimensions;if(this.x+=t/i,this.y+=e/s,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:t,y:e}=this.div.getBoundingClientRect();this.parent.findNewParent(this,t,e)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:n,y:r}=this;const[a,o]=this.getBaseTranslation();n+=a,r+=o,this.div.style.left=`${(100*n).toFixed(2)}%`,this.div.style.top=`${(100*r).toFixed(2)}%`,this.div.scrollIntoView({block:"nearest"})}get _hasBeenMoved(){return!!this.#ie&&(this.#ie[0]!==this.x||this.#ie[1]!==this.y)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:i}=xe,s=i/t,n=i/e;switch(this.rotation){case 90:return[-s,n];case 180:return[s,n];case 270:return[s,-n];default:return[-s,-n]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const[e,i]=this.pageDimensions;let{x:s,y:n,width:r,height:a}=this;if(r*=e,a*=i,s*=e,n*=i,this._mustFixPosition)switch(t){case 0:s=Math.max(0,Math.min(e-r,s)),n=Math.max(0,Math.min(i-a,n));break;case 90:s=Math.max(0,Math.min(e-a,s)),n=Math.min(i,Math.max(r,n));break;case 180:s=Math.min(e,Math.max(r,s)),n=Math.min(i,Math.max(a,n));break;case 270:s=Math.min(e,Math.max(a,s)),n=Math.max(0,Math.min(i-r,n))}this.x=s/=e,this.y=n/=i;const[o,h]=this.getBaseTranslation();s+=o,n+=h;const{style:l}=this.div;l.left=`${(100*s).toFixed(2)}%`,l.top=`${(100*n).toFixed(2)}%`,this.moveInDOM()}static#ge(t,e,i){switch(i){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}screenToPageTranslation(t,e){return xe.#ge(t,e,this.parentRotation)}pageTranslationToScreen(t,e){return xe.#ge(t,e,360-this.parentRotation)}#fe(t){switch(t){case 90:{const[t,e]=this.pageDimensions;return[0,-t/e,e/t,0]}case 180:return[-1,0,0,-1];case 270:{const[t,e]=this.pageDimensions;return[0,t/e,-e/t,0]}default:return[1,0,0,1]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,i]}=this,s=e*t,n=i*t;return xt.isCSSRoundSupported?[Math.round(s),Math.round(n)]:[s,n]}setDims(t,e){const[i,s]=this.parentDimensions;this.div.style.width=`${(100*t/i).toFixed(2)}%`,this.#Xt||(this.div.style.height=`${(100*e/s).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:i}=t,s=i.endsWith("%"),n=!this.#Xt&&e.endsWith("%");if(s&&n)return;const[r,a]=this.parentDimensions;s||(t.width=`${(100*parseFloat(i)/r).toFixed(2)}%`),this.#Xt||n||(t.height=`${(100*parseFloat(e)/a).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}#me(){if(this.#Yt)return;this.#Yt=document.createElement("div"),this.#Yt.classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(const i of t){const t=document.createElement("div");this.#Yt.append(t),t.classList.add("resizer",i),t.setAttribute("data-resizer-name",i),t.addEventListener("pointerdown",this.#be.bind(this,i),{signal:e}),t.addEventListener("contextmenu",se,{signal:e}),t.tabIndex=-1}this.div.prepend(this.#Yt)}#be(t,e){e.preventDefault();const{isMac:i}=xt.platform;if(0!==e.button||e.ctrlKey&&i)return;this.#zt?.toggle(!1);const s=this.#ve.bind(this,t),n=this._isDraggable;this._isDraggable=!1;const r=this._uiManager._signal,a={passive:!0,capture:!0,signal:r};this.parent.togglePointerEvents(!1),window.addEventListener("pointermove",s,a),window.addEventListener("contextmenu",se,{signal:r});const o=this.x,h=this.y,l=this.width,d=this.height,c=this.parent.div.style.cursor,u=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const p=()=>{this.parent.togglePointerEvents(!0),this.#zt?.toggle(!0),this._isDraggable=n,window.removeEventListener("pointerup",p),window.removeEventListener("blur",p),window.removeEventListener("pointermove",s,a),window.removeEventListener("contextmenu",se),this.parent.div.style.cursor=c,this.div.style.cursor=u,this.#ye(o,h,l,d)};window.addEventListener("pointerup",p,{signal:r}),window.addEventListener("blur",p,{signal:r})}#ye(t,e,i,s){const n=this.x,r=this.y,a=this.width,o=this.height;n===t&&r===e&&a===i&&o===s||this.addCommands({cmd:()=>{this.width=a,this.height=o,this.x=n,this.y=r;const[t,e]=this.parentDimensions;this.setDims(t*a,e*o),this.fixAndSetPosition()},undo:()=>{this.width=i,this.height=s,this.x=t,this.y=e;const[n,r]=this.parentDimensions;this.setDims(n*i,r*s),this.fixAndSetPosition()},mustExec:!0})}#ve(t,e){const[i,s]=this.parentDimensions,n=this.x,r=this.y,a=this.width,o=this.height,h=xe.MIN_SIZE/i,l=xe.MIN_SIZE/s,d=t=>Math.round(1e4*t)/1e4,c=this.#fe(this.rotation),u=(t,e)=>[c[0]*t+c[2]*e,c[1]*t+c[3]*e],p=this.#fe(360-this.rotation);let g,f,m=!1,b=!1;switch(t){case"topLeft":m=!0,g=(t,e)=>[0,0],f=(t,e)=>[t,e];break;case"topMiddle":g=(t,e)=>[t/2,0],f=(t,e)=>[t/2,e];break;case"topRight":m=!0,g=(t,e)=>[t,0],f=(t,e)=>[0,e];break;case"middleRight":b=!0,g=(t,e)=>[t,e/2],f=(t,e)=>[0,e/2];break;case"bottomRight":m=!0,g=(t,e)=>[t,e],f=(t,e)=>[0,0];break;case"bottomMiddle":g=(t,e)=>[t/2,e],f=(t,e)=>[t/2,0];break;case"bottomLeft":m=!0,g=(t,e)=>[0,e],f=(t,e)=>[t,0];break;case"middleLeft":b=!0,g=(t,e)=>[0,e/2],f=(t,e)=>[t,e/2]}const v=g(a,o),y=f(a,o);let w=u(...y);const A=d(n+w[0]),x=d(r+w[1]);let _=1,S=1,[E,C]=this.screenToPageTranslation(e.movementX,e.movementY);var M,T;if([E,C]=(M=E/i,T=C/s,[p[0]*M+p[2]*T,p[1]*M+p[3]*T]),m){const t=Math.hypot(a,o);_=S=Math.max(Math.min(Math.hypot(y[0]-v[0]-E,y[1]-v[1]-C)/t,1/a,1/o),h/a,l/o)}else b?_=Math.max(h,Math.min(1,Math.abs(y[0]-v[0]-E)))/a:S=Math.max(l,Math.min(1,Math.abs(y[1]-v[1]-C)))/o;const k=d(a*_),P=d(o*S);w=u(...f(k,P));const R=A-w[0],D=x-w[1];this.width=k,this.height=P,this.x=R,this.y=D,this.setDims(i*k,s*P),this.fixAndSetPosition()}altTextFinish(){this.#zt?.finish()}async addEditToolbar(){return this.#Zt||this.#ne||(this.#Zt=new ue(this),this.div.append(this.#Zt.render()),this.#zt&&this.#Zt.addAltTextButton(await this.#zt.render())),this.#Zt}removeEditToolbar(){this.#Zt&&(this.#Zt.remove(),this.#Zt=null,this.#zt?.destroy())}getClientDimensions(){return this.div.getBoundingClientRect()}async addAltTextButton(){this.#zt||(Ae.initialize(xe._l10nPromise),this.#zt=new Ae(this),this.#Gt&&(this.#zt.data=this.#Gt,this.#Gt=null),await this.addEditToolbar())}get altTextData(){return this.#zt?.data}set altTextData(t){this.#zt&&(this.#zt.data=t)}hasAltText(){return!this.#zt?.isEmpty()}render(){this.div=document.createElement("div"),this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360),this.div.className=this.name,this.div.setAttribute("id",this.id),this.div.tabIndex=this.#qt?-1:0,this._isVisible||this.div.classList.add("hidden"),this.setInForeground();const t=this._uiManager._signal;this.div.addEventListener("focusin",this.#Qt,{signal:t}),this.div.addEventListener("focusout",this.#Jt,{signal:t});const[e,i]=this.parentDimensions;this.parentRotation%180!=0&&(this.div.style.maxWidth=`${(100*i/e).toFixed(2)}%`,this.div.style.maxHeight=`${(100*e/i).toFixed(2)}%`);const[s,n]=this.getInitialTranslation();return this.translate(s,n),ge(this,this.div,["pointerdown"]),this.div}pointerdown(t){const{isMac:e}=xt.platform;0!==t.button||t.ctrlKey&&e?t.preventDefault():(this.#ee=!0,this._isDraggable?this.#we(t):this.#Ae(t))}#Ae(t){const{isMac:e}=xt.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)}#we(t){const e=this._uiManager.isSelected(this);let i,s;this._uiManager.setUpDragSession();const n=this._uiManager._signal;e&&(this.div.classList.add("moving"),i={passive:!0,capture:!0,signal:n},this.#oe=t.clientX,this.#he=t.clientY,s=t=>{const{clientX:e,clientY:i}=t,[s,n]=this.screenToPageTranslation(e-this.#oe,i-this.#he);this.#oe=e,this.#he=i,this._uiManager.dragSelectedEditors(s,n)},window.addEventListener("pointermove",s,i));const r=()=>{window.removeEventListener("pointerup",r),window.removeEventListener("blur",r),e&&(this.div.classList.remove("moving"),window.removeEventListener("pointermove",s,i)),this.#ee=!1,this._uiManager.endDragSession()||this.#Ae(t)};window.addEventListener("pointerup",r,{signal:n}),window.addEventListener("blur",r,{signal:n})}moveInDOM(){this.#ae&&clearTimeout(this.#ae),this.#ae=setTimeout((()=>{this.#ae=null,this.parent?.moveEditorInDOM(this)}),0)}_setParentAndPosition(t,e,i){t.changeParent(this),this.x=e,this.y=i,this.fixAndSetPosition()}getRect(t,e,i=this.rotation){const s=this.parentScale,[n,r]=this.pageDimensions,[a,o]=this.pageTranslation,h=t/s,l=e/s,d=this.x*n,c=this.y*r,u=this.width*n,p=this.height*r;switch(i){case 0:return[d+h+a,r-c-l-p+o,d+h+u+a,r-c-l+o];case 90:return[d+l+a,r-c+h+o,d+l+p+a,r-c+h+u+o];case 180:return[d-h-u+a,r-c+l+o,d-h+a,r-c+l+p+o];case 270:return[d-l-p+a,r-c-h-u+o,d-l+a,r-c-h+o];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[i,s,n,r]=t,a=n-i,o=r-s;switch(this.rotation){case 0:return[i,e-r,a,o];case 90:return[i,e-s,o,a];case 180:return[n,e-s,a,o];case 270:return[n,e-r,o,a];default:throw new Error("Invalid rotation")}}onceAdded(){}isEmpty(){return!1}enableEditMode(){this.#ne=!0}disableEditMode(){this.#ne=!1}isInEditMode(){return this.#ne}shouldGetKeyboardEvents(){return this.#re}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}rebuild(){const t=this._uiManager._signal;this.div?.addEventListener("focusin",this.#Qt,{signal:t}),this.div?.addEventListener("focusout",this.#Jt,{signal:t})}rotate(t){}serialize(t=!1,e=null){ot("An editor must be serializable")}static deserialize(t,e,i){const s=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:i});s.rotation=t.rotation,s.#Gt=t.accessibilityData;const[n,r]=s.pageDimensions,[a,o,h,l]=s.getRectInCurrentCoords(t.rect,r);return s.x=a/n,s.y=o/r,s.width=h/n,s.height=l/r,s}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||null!==this.serialize())}remove(){if(this.div.removeEventListener("focusin",this.#Qt),this.div.removeEventListener("focusout",this.#Jt),this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),this.#ae&&(clearTimeout(this.#ae),this.#ae=null),this.#ue(),this.removeEditToolbar(),this.#le){for(const t of this.#le.values())clearTimeout(t);this.#le=null}this.parent=null}get isResizable(){return!1}makeResizable(){this.isResizable&&(this.#me(),this.#Yt.classList.remove("hidden"),ge(this,this.div,["keydown"]))}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||"Enter"!==t.key)return;this._uiManager.setSelected(this),this.#Kt={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const e=this.#Yt.children;if(!this.#Vt){this.#Vt=Array.from(e);const t=this.#xe.bind(this),i=this.#_e.bind(this),s=this._uiManager._signal;for(const e of this.#Vt){const n=e.getAttribute("data-resizer-name");e.setAttribute("role","spinbutton"),e.addEventListener("keydown",t,{signal:s}),e.addEventListener("blur",i,{signal:s}),e.addEventListener("focus",this.#Se.bind(this,n),{signal:s}),xe._l10nPromise.get(`pdfjs-editor-resizer-label-${n}`).then((t=>e.setAttribute("aria-label",t)))}}const i=this.#Vt[0];let s=0;for(const t of e){if(t===i)break;s++}const n=(360-this.rotation+this.parentRotation)%360/90*(this.#Vt.length/4);if(n!==s){if(n<s)for(let t=0;t<s-n;t++)this.#Yt.append(this.#Yt.firstChild);else if(n>s)for(let t=0;t<n-s;t++)this.#Yt.firstChild.before(this.#Yt.lastChild);let t=0;for(const i of e){const e=this.#Vt[t++].getAttribute("data-resizer-name");xe._l10nPromise.get(`pdfjs-editor-resizer-label-${e}`).then((t=>i.setAttribute("aria-label",t)))}}this.#Ee(0),this.#re=!0,this.#Yt.firstChild.focus({focusVisible:!0}),t.preventDefault(),t.stopImmediatePropagation()}#xe(t){xe._resizerKeyboardManager.exec(this,t)}#_e(t){this.#re&&t.relatedTarget?.parentNode!==this.#Yt&&this.#ue()}#Se(t){this.#te=this.#re?t:""}#Ee(t){if(this.#Vt)for(const e of this.#Vt)e.tabIndex=t}_resizeWithKeyboard(t,e){this.#re&&this.#ve(this.#te,{movementX:t,movementY:e})}#ue(){if(this.#re=!1,this.#Ee(-1),this.#Kt){const{savedX:t,savedY:e,savedWidth:i,savedHeight:s}=this.#Kt;this.#ye(t,e,i,s),this.#Kt=null}}_stopResizingWithKeyboard(){this.#ue(),this.div.focus()}select(){this.makeResizable(),this.div?.classList.add("selectedEditor"),this.#Zt?this.#Zt?.show():this.addEditToolbar().then((()=>{this.div?.classList.contains("selectedEditor")&&this.#Zt?.show()}))}unselect(){this.#Yt?.classList.add("hidden"),this.div?.classList.remove("selectedEditor"),this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0}),this.#Zt?.hide()}updateParams(t,e){}disableEditing(){}enableEditing(){}enterInEditMode(){}getImageForAltText(){return null}get contentDiv(){return this.div}get isEditing(){return this.#se}set isEditing(t){this.#se=t,this.parent&&(t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(t,e){this.#Xt=!0;const i=t/e,{style:s}=this.div;s.aspectRatio=i,s.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){this.#le||=new Map;const{action:e}=t;let i=this.#le.get(e);return i&&clearTimeout(i),i=setTimeout((()=>{this._reportTelemetry(t),this.#le.delete(e),0===this.#le.size&&(this.#le=null)}),xe._telemetryTimeout),void this.#le.set(e,i)}t.type||=this.editorType,this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}show(t=this._isVisible){this.div.classList.toggle("hidden",!t),this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0),this.#qt=!1}disable(){this.div&&(this.div.tabIndex=-1),this.#qt=!0}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(e){if("CANVAS"===e.nodeName){const t=e;e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.before(e)}}else e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.container.prepend(e);return e}resetAnnotationElement(t){const{firstChild:e}=t.container;"DIV"===e.nodeName&&e.classList.contains("annotationContent")&&e.remove()}}class _e extends xe{constructor(t){super(t),this.annotationElementId=t.annotationElementId,this.deleted=!0}serialize(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex}}}const Se=3285377520,Ee=4294901760,Ce=65535;class Me{constructor(t){this.h1=t?4294967295&t:Se,this.h2=t?4294967295&t:Se}update(t){let e,i;if("string"==typeof t){e=new Uint8Array(2*t.length),i=0;for(let s=0,n=t.length;s<n;s++){const n=t.charCodeAt(s);n<=255?e[i++]=n:(e[i++]=n>>>8,e[i++]=255&n)}}else{if(!ArrayBuffer.isView(t))throw new Error("Invalid data format, must be a string or TypedArray.");e=t.slice(),i=e.byteLength}const s=i>>2,n=i-4*s,r=new Uint32Array(e.buffer,0,s);let a=0,o=0,h=this.h1,l=this.h2;const d=3432918353,c=461845907,u=11601,p=13715;for(let t=0;t<s;t++)1&t?(a=r[t],a=a*d&Ee|a*u&Ce,a=a<<15|a>>>17,a=a*c&Ee|a*p&Ce,h^=a,h=h<<13|h>>>19,h=5*h+3864292196):(o=r[t],o=o*d&Ee|o*u&Ce,o=o<<15|o>>>17,o=o*c&Ee|o*p&Ce,l^=o,l=l<<13|l>>>19,l=5*l+3864292196);switch(a=0,n){case 3:a^=e[4*s+2]<<16;case 2:a^=e[4*s+1]<<8;case 1:a^=e[4*s],a=a*d&Ee|a*u&Ce,a=a<<15|a>>>17,a=a*c&Ee|a*p&Ce,1&s?h^=a:l^=a}this.h1=h,this.h2=l}hexdigest(){let t=this.h1,e=this.h2;return t^=e>>>1,t=3981806797*t&Ee|36045*t&Ce,e=4283543511*e&Ee|(2950163797*(e<<16|t>>>16)&Ee)>>>16,t^=e>>>1,t=444984403*t&Ee|60499*t&Ce,e=3301882366*e&Ee|(3120437893*(e<<16|t>>>16)&Ee)>>>16,t^=e>>>1,(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const Te=Object.freeze({map:null,hash:"",transfer:void 0});class ke{#Ce=!1;#Me=new Map;constructor(){this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(t,e){const i=this.#Me.get(t);return void 0===i?e:Object.assign(e,i)}getRawValue(t){return this.#Me.get(t)}remove(t){if(this.#Me.delete(t),0===this.#Me.size&&this.resetModified(),"function"==typeof this.onAnnotationEditor){for(const t of this.#Me.values())if(t instanceof xe)return;this.onAnnotationEditor(null)}}setValue(t,e){const i=this.#Me.get(t);let s=!1;if(void 0!==i)for(const[t,n]of Object.entries(e))i[t]!==n&&(s=!0,i[t]=n);else s=!0,this.#Me.set(t,e);s&&this.#Te(),e instanceof xe&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return this.#Me.has(t)}getAll(){return this.#Me.size>0?At(this.#Me):null}setAll(t){for(const[e,i]of Object.entries(t))this.setValue(e,i)}get size(){return this.#Me.size}#Te(){this.#Ce||(this.#Ce=!0,"function"==typeof this.onSetModified&&this.onSetModified())}resetModified(){this.#Ce&&(this.#Ce=!1,"function"==typeof this.onResetModified&&this.onResetModified())}get print(){return new Pe(this)}get serializable(){if(0===this.#Me.size)return Te;const t=new Map,e=new Me,i=[],s=Object.create(null);let n=!1;for(const[i,r]of this.#Me){const a=r instanceof xe?r.serialize(!1,s):r;a&&(t.set(i,a),e.update(`${i}:${JSON.stringify(a)}`),n||=!!a.bitmap)}if(n)for(const e of t.values())e.bitmap&&i.push(e.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:i}:Te}get editorStats(){let t=null;const e=new Map;for(const i of this.#Me.values()){if(!(i instanceof xe))continue;const s=i.telemetryFinalData;if(!s)continue;const{type:n}=s;e.has(n)||e.set(n,Object.getPrototypeOf(i).constructor),t||=Object.create(null);const r=t[n]||=new Map;for(const[t,e]of Object.entries(s)){if("type"===t)continue;let i=r.get(t);i||(i=new Map,r.set(t,i));const s=i.get(e)??0;i.set(e,s+1)}}for(const[i,s]of e)t[i]=s.computeTelemetryFinalData(t[i]);return t}}class Pe extends ke{#ke;constructor(t){super();const{map:e,hash:i,transfer:s}=t.serializable,n=structuredClone(e,s?{transfer:s}:null);this.#ke={map:n,hash:i,transfer:s}}get print(){ot("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#ke}}class Re{#Pe=new Set;constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){this._document=t,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t),this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t),this._document.fonts.delete(t)}insertRule(t){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear(),this.#Pe.clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont({systemFontInfo:t,_inspectFont:e}){if(t&&!this.#Pe.has(t.loadedName))if(ht(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:i,src:s,style:n}=t,r=new FontFace(i,s,n);this.addNativeFontFace(r);try{await r.load(),this.#Pe.add(i),e?.(t)}catch{at(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(r)}}else ot("Not implemented: loadSystemFont without the Font Loading API.")}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;if(t.attached=!0,t.systemFontInfo)return void await this.loadSystemFont(t);if(this.isFontLoadingAPISupported){const e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(i){throw at(`Failed to load font '${e.family}': '${i}'.`),t.disableFontFace=!0,i}}return}const e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;await new Promise((e=>{const i=this._queueLoadingCallback(e);this._prepareFontLoadEvent(t,i)}))}}get isFontLoadingAPISupported(){return dt(this,"isFontLoadingAPISupported",!!this._document?.fonts)}get isSyncFontLoadingSupported(){let t=!1;return(o||"undefined"!=typeof navigator&&"string"==typeof navigator?.userAgent&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(t=!0),dt(this,"isSyncFontLoadingSupported",t)}_queueLoadingCallback(t){const{loadingRequests:e}=this,i={done:!1,complete:function(){for(ht(!i.done,"completeRequest() cannot be called twice."),i.done=!0;e.length>0&&e[0].done;){const t=e.shift();setTimeout(t.callback,0)}},callback:t};return e.push(i),i}get _loadTestFont(){return dt(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}_prepareFontLoadEvent(t,e){function i(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function s(t,e,i,s){return t.substring(0,e)+s+t.substring(e+i)}let n,r;const a=this._document.createElement("canvas");a.width=1,a.height=1;const o=a.getContext("2d");let h=0;const l=`lt${Date.now()}${this.loadTestFontId++}`;let d=this._loadTestFont;d=s(d,976,l.length,l);const c=1482184792;let u=i(d,16);for(n=0,r=l.length-3;n<r;n+=4)u=u-c+i(l,n)|0;var p;n<l.length&&(u=u-c+i(l+"XXX",n)|0),d=s(d,16,4,(p=u,String.fromCharCode(p>>24&255,p>>16&255,p>>8&255,255&p)));const g=`@font-face {font-family:"${l}";src:${`url(data:font/opentype;base64,${btoa(d)});`}}`;this.insertRule(g);const f=this._document.createElement("div");f.style.visibility="hidden",f.style.width=f.style.height="10px",f.style.position="absolute",f.style.top=f.style.left="0px";for(const e of[t.loadedName,l]){const t=this._document.createElement("span");t.textContent="Hi",t.style.fontFamily=e,f.append(t)}this._document.body.append(f),function t(e,i){if(++h>30)return at("Load test font never loaded."),void i();o.font="30px "+e,o.fillText(".",0,20),o.getImageData(0,0,1,1).data[3]>0?i():setTimeout(t.bind(null,e,i))}(l,(()=>{f.remove(),e.complete()}))}}class De{constructor(t,{disableFontFace:e=!1,inspectFont:i=null}){this.compiledGlyphs=Object.create(null);for(const e in t)this[e]=t[e];this.disableFontFace=!0===e,this._inspectFont=i}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(this.cssFontInfo){const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`),t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});return this._inspectFont?.(this),t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=yt(this.data),e=`url(data:${this.mimetype};base64,${btoa(t)});`;let i;if(this.cssFontInfo){let t=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(t+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),i=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${t}src:${e}}`}else i=`@font-face {font-family:"${this.loadedName}";src:${e}}`;return this._inspectFont?.(this,e),i}getPathGenerator(t,e){if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];let i;try{i=t.get(this.loadedName+"_path_"+e)}catch(t){at(`getPathGenerator - ignoring character: "${t}".`)}if(!Array.isArray(i)||0===i.length)return this.compiledGlyphs[e]=function(t,e){};const s=[];for(let t=0,e=i.length;t<e;)switch(i[t++]){case kt:{const[e,n,r,a,o,h]=i.slice(t,t+6);s.push((t=>t.bezierCurveTo(e,n,r,a,o,h))),t+=6}break;case Pt:{const[e,n]=i.slice(t,t+2);s.push((t=>t.moveTo(e,n))),t+=2}break;case Rt:{const[e,n]=i.slice(t,t+2);s.push((t=>t.lineTo(e,n))),t+=2}break;case Dt:{const[e,n,r,a]=i.slice(t,t+4);s.push((t=>t.quadraticCurveTo(e,n,r,a))),t+=4}break;case Lt:s.push((t=>t.restore()));break;case It:s.push((t=>t.save()));break;case Ft:ht(2===s.length,"Scale command is only valid at the third position.");break;case Nt:{const[e,n,r,a,o,h]=i.slice(t,t+6);s.push((t=>t.transform(e,n,r,a,o,h))),t+=6}break;case Ot:{const[e,n]=i.slice(t,t+2);s.push((t=>t.translate(e,n))),t+=2}}return this.compiledGlyphs[e]=function(t,e){s[0](t),s[1](t),t.scale(e,-e);for(let e=2,i=s.length;e<i;e++)s[e](t)}}}if(o){var Le=Promise.withResolvers(),Ie=null;(async()=>{const t=await import("fs"),e=await import("http"),i=await import("https"),s=await import("url");return new Map(Object.entries({fs:t,http:e,https:i,url:s,canvas:undefined,path2d:undefined}))})().then((t=>{Ie=t,Le.resolve()}),(t=>{at(`loadPackages: ${t}`),Ie=new Map,Le.resolve()}))}class Fe{static get promise(){return Le.promise}static get(t){return Ie?.get(t)}}const Ne=function(t){return Fe.get("fs").promises.readFile(t).then((t=>new Uint8Array(t)))};const Oe="Fill",ze="Stroke",He="Shading";function Be(t,e){if(!e)return;const i=e[2]-e[0],s=e[3]-e[1],n=new Path2D;n.rect(e[0],e[1],i,s),t.clip(n)}class je{constructor(){this.constructor===je&&ot("Cannot initialize BaseShadingPattern.")}getPattern(){ot("Abstract method `getPattern` called.")}}class We extends je{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;"axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const t of this._colorStops)e.addColorStop(t[0],t[1]);return e}getPattern(t,e,i,s){let n;if(s===ze||s===Oe){const r=e.current.getClippedPathBoundingBox(s,le(t))||[0,0,0,0],a=Math.ceil(r[2]-r[0])||1,o=Math.ceil(r[3]-r[1])||1,h=e.cachedCanvases.getCanvas("pattern",a,o,!0),l=h.context;l.clearRect(0,0,l.canvas.width,l.canvas.height),l.beginPath(),l.rect(0,0,l.canvas.width,l.canvas.height),l.translate(-r[0],-r[1]),i=St.transform(i,[1,0,0,1,r[0],r[1]]),l.transform(...e.baseTransform),this.matrix&&l.transform(...this.matrix),Be(l,this._bbox),l.fillStyle=this._createGradient(l),l.fill(),n=t.createPattern(h.canvas,"no-repeat");const d=new DOMMatrix(i);n.setTransform(d)}else Be(t,this._bbox),n=this._createGradient(t);return n}}function Ue(t,e,i,s,n,r,a,o){const h=e.coords,l=e.colors,d=t.data,c=4*t.width;let u;h[i+1]>h[s+1]&&(u=i,i=s,s=u,u=r,r=a,a=u),h[s+1]>h[n+1]&&(u=s,s=n,n=u,u=a,a=o,o=u),h[i+1]>h[s+1]&&(u=i,i=s,s=u,u=r,r=a,a=u);const p=(h[i]+e.offsetX)*e.scaleX,g=(h[i+1]+e.offsetY)*e.scaleY,f=(h[s]+e.offsetX)*e.scaleX,m=(h[s+1]+e.offsetY)*e.scaleY,b=(h[n]+e.offsetX)*e.scaleX,v=(h[n+1]+e.offsetY)*e.scaleY;if(g>=v)return;const y=l[r],w=l[r+1],A=l[r+2],x=l[a],_=l[a+1],S=l[a+2],E=l[o],C=l[o+1],M=l[o+2],T=Math.round(g),k=Math.round(v);let P,R,D,L,I,F,N,O;for(let t=T;t<=k;t++){if(t<m){const e=t<g?0:(g-t)/(g-m);P=p-(p-f)*e,R=y-(y-x)*e,D=w-(w-_)*e,L=A-(A-S)*e}else{let e;e=t>v?1:m===v?0:(m-t)/(m-v),P=f-(f-b)*e,R=x-(x-E)*e,D=_-(_-C)*e,L=S-(S-M)*e}let e;e=t<g?0:t>v?1:(g-t)/(g-v),I=p-(p-b)*e,F=y-(y-E)*e,N=w-(w-C)*e,O=A-(A-M)*e;const i=Math.round(Math.min(P,I)),s=Math.round(Math.max(P,I));let n=c*t+4*i;for(let t=i;t<=s;t++)e=(P-t)/(P-I),e<0?e=0:e>1&&(e=1),d[n++]=R-(R-F)*e|0,d[n++]=D-(D-N)*e|0,d[n++]=L-(L-O)*e|0,d[n++]=255}}function $e(t,e,i){const s=e.coords,n=e.colors;let r,a;switch(e.type){case"lattice":const o=e.verticesPerRow,h=Math.floor(s.length/o)-1,l=o-1;for(r=0;r<h;r++){let e=r*o;for(let r=0;r<l;r++,e++)Ue(t,i,s[e],s[e+1],s[e+o],n[e],n[e+1],n[e+o]),Ue(t,i,s[e+o+1],s[e+1],s[e+o],n[e+o+1],n[e+1],n[e+o])}break;case"triangles":for(r=0,a=s.length;r<a;r+=3)Ue(t,i,s[r],s[r+1],s[r+2],n[r],n[r+1],n[r+2]);break;default:throw new Error("illegal figure")}}class Ge extends je{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[7],this._background=t[8],this.matrix=null}_createMeshCanvas(t,e,i){const s=Math.floor(this._bounds[0]),n=Math.floor(this._bounds[1]),r=Math.ceil(this._bounds[2])-s,a=Math.ceil(this._bounds[3])-n,o=Math.min(Math.ceil(Math.abs(r*t[0]*1.1)),3e3),h=Math.min(Math.ceil(Math.abs(a*t[1]*1.1)),3e3),l=r/o,d=a/h,c={coords:this._coords,colors:this._colors,offsetX:-s,offsetY:-n,scaleX:1/l,scaleY:1/d},u=o+4,p=h+4,g=i.getCanvas("mesh",u,p,!1),f=g.context,m=f.createImageData(o,h);if(e){const t=m.data;for(let i=0,s=t.length;i<s;i+=4)t[i]=e[0],t[i+1]=e[1],t[i+2]=e[2],t[i+3]=255}for(const t of this._figures)$e(m,t,c);f.putImageData(m,2,2);return{canvas:g.canvas,offsetX:s-2*l,offsetY:n-2*d,scaleX:l,scaleY:d}}getPattern(t,e,i,s){let n;if(Be(t,this._bbox),s===He)n=St.singularValueDecompose2dScale(le(t));else if(n=St.singularValueDecompose2dScale(e.baseTransform),this.matrix){const t=St.singularValueDecompose2dScale(this.matrix);n=[n[0]*t[0],n[1]*t[1]]}const r=this._createMeshCanvas(n,s===He?null:this._background,e.cachedCanvases);return s!==He&&(t.setTransform(...e.baseTransform),this.matrix&&t.transform(...this.matrix)),t.translate(r.offsetX,r.offsetY),t.scale(r.scaleX,r.scaleY),t.createPattern(r.canvas,"no-repeat")}}class Ve extends je{getPattern(){return"hotpink"}}const qe=1,Xe=2;class Ye{static MAX_PATTERN_SIZE=3e3;constructor(t,e,i,s,n){this.operatorList=t[2],this.matrix=t[3],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.color=e,this.ctx=i,this.canvasGraphicsFactory=s,this.baseTransform=n}createPatternCanvas(t){const e=this.operatorList,i=this.bbox,s=this.xstep,n=this.ystep,r=this.paintType,a=this.tilingType,o=this.color,h=this.canvasGraphicsFactory;rt("TilingType: "+a);const l=i[0],d=i[1],c=i[2],u=i[3],p=St.singularValueDecompose2dScale(this.matrix),g=St.singularValueDecompose2dScale(this.baseTransform),f=[p[0]*g[0],p[1]*g[1]],m=this.getSizeAndScale(s,this.ctx.canvas.width,f[0]),b=this.getSizeAndScale(n,this.ctx.canvas.height,f[1]),v=t.cachedCanvases.getCanvas("pattern",m.size,b.size,!0),y=v.context,w=h.createCanvasGraphics(y);w.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext(w,r,o);let A=l,x=d,_=c,S=u;return l<0&&(A=0,_+=Math.abs(l)),d<0&&(x=0,S+=Math.abs(d)),y.translate(-m.scale*A,-b.scale*x),w.transform(m.scale,0,0,b.scale,0,0),y.save(),this.clipBbox(w,A,x,_,S),w.baseTransform=le(w.ctx),w.executeOperatorList(e),w.endDrawing(),{canvas:v.canvas,scaleX:m.scale,scaleY:b.scale,offsetX:A,offsetY:x}}getSizeAndScale(t,e,i){t=Math.abs(t);const s=Math.max(Ye.MAX_PATTERN_SIZE,e);let n=Math.ceil(t*i);return n>=s?n=s:i=n/t,{scale:i,size:n}}clipBbox(t,e,i,s,n){const r=s-e,a=n-i;t.ctx.rect(e,i,r,a),t.current.updateRectMinMax(le(t.ctx),[e,i,s,n]),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,i){const s=t.ctx,n=t.current;switch(e){case qe:const t=this.ctx;s.fillStyle=t.fillStyle,s.strokeStyle=t.strokeStyle,n.fillColor=t.fillStyle,n.strokeColor=t.strokeStyle;break;case Xe:const r=St.makeHexColor(i[0],i[1],i[2]);s.fillStyle=r,s.strokeStyle=r,n.fillColor=r,n.strokeColor=r;break;default:throw new bt(`Unsupported paint type: ${e}`)}}getPattern(t,e,i,s){let n=i;s!==He&&(n=St.transform(n,e.baseTransform),this.matrix&&(n=St.transform(n,this.matrix)));const r=this.createPatternCanvas(e);let a=new DOMMatrix(n);a=a.translate(r.offsetX,r.offsetY),a=a.scale(1/r.scaleX,1/r.scaleY);const o=t.createPattern(r.canvas,"repeat");return o.setTransform(a),o}}function Ke({src:t,srcPos:e=0,dest:i,width:s,height:n,nonBlackColor:r=4294967295,inverseDecode:a=!1}){const o=xt.isLittleEndian?4278190080:255,[h,l]=a?[r,o]:[o,r],d=s>>3,c=7&s,u=t.length;i=new Uint32Array(i.buffer);let p=0;for(let s=0;s<n;s++){for(const s=e+d;e<s;e++){const s=e<u?t[e]:255;i[p++]=128&s?l:h,i[p++]=64&s?l:h,i[p++]=32&s?l:h,i[p++]=16&s?l:h,i[p++]=8&s?l:h,i[p++]=4&s?l:h,i[p++]=2&s?l:h,i[p++]=1&s?l:h}if(0===c)continue;const s=e<u?t[e++]:255;for(let t=0;t<c;t++)i[p++]=s&1<<7-t?l:h}return{srcPos:e,destPos:p}}const Qe=16;class Je{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,i){let s;return void 0!==this.cache[t]?(s=this.cache[t],this.canvasFactory.reset(s,e,i)):(s=this.canvasFactory.create(e,i),this.cache[t]=s),s}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function Ze(t,e,i,s,n,r,a,o,h,l){const[d,c,u,p,g,f]=le(t);if(0===c&&0===u){const m=a*d+g,b=Math.round(m),v=o*p+f,y=Math.round(v),w=(a+h)*d+g,A=Math.abs(Math.round(w)-b)||1,x=(o+l)*p+f,_=Math.abs(Math.round(x)-y)||1;return t.setTransform(Math.sign(d),0,0,Math.sign(p),b,y),t.drawImage(e,i,s,n,r,0,0,A,_),t.setTransform(d,c,u,p,g,f),[A,_]}if(0===d&&0===p){const m=o*u+g,b=Math.round(m),v=a*c+f,y=Math.round(v),w=(o+l)*u+g,A=Math.abs(Math.round(w)-b)||1,x=(a+h)*c+f,_=Math.abs(Math.round(x)-y)||1;return t.setTransform(0,Math.sign(c),Math.sign(u),0,b,y),t.drawImage(e,i,s,n,r,0,0,_,A),t.setTransform(d,c,u,p,g,f),[_,A]}t.drawImage(e,i,s,n,r,a,o,h,l);return[Math.hypot(d,c)*h,Math.hypot(u,p)*l]}class ti{constructor(t,e){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=h,this.textMatrixScale=1,this.fontMatrix=l,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=x,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.transferMaps="none",this.startNewPathAndClipBox([0,0,t,e])}clone(){const t=Object.create(this);return t.clipBox=this.clipBox.slice(),t}setCurrentPoint(t,e){this.x=t,this.y=e}updatePathMinMax(t,e,i){[e,i]=St.applyTransform([e,i],t),this.minX=Math.min(this.minX,e),this.minY=Math.min(this.minY,i),this.maxX=Math.max(this.maxX,e),this.maxY=Math.max(this.maxY,i)}updateRectMinMax(t,e){const i=St.applyTransform(e,t),s=St.applyTransform(e.slice(2),t),n=St.applyTransform([e[0],e[3]],t),r=St.applyTransform([e[2],e[1]],t);this.minX=Math.min(this.minX,i[0],s[0],n[0],r[0]),this.minY=Math.min(this.minY,i[1],s[1],n[1],r[1]),this.maxX=Math.max(this.maxX,i[0],s[0],n[0],r[0]),this.maxY=Math.max(this.maxY,i[1],s[1],n[1],r[1])}updateScalingPathMinMax(t,e){St.scaleMinMax(t,e),this.minX=Math.min(this.minX,e[0]),this.minY=Math.min(this.minY,e[1]),this.maxX=Math.max(this.maxX,e[2]),this.maxY=Math.max(this.maxY,e[3])}updateCurvePathMinMax(t,e,i,s,n,r,a,o,h,l){const d=St.bezierBoundingBox(e,i,s,n,r,a,o,h,l);l||this.updateRectMinMax(t,d)}getPathBoundingBox(t=Oe,e=null){const i=[this.minX,this.minY,this.maxX,this.maxY];if(t===ze){e||ot("Stroke bounding box must include transform.");const t=St.singularValueDecompose2dScale(e),s=t[0]*this.lineWidth/2,n=t[1]*this.lineWidth/2;i[0]-=s,i[1]-=n,i[2]+=s,i[3]+=n}return i}updateClipFromPath(){const t=St.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(t){this.clipBox=t,this.minX=1/0,this.minY=1/0,this.maxX=0,this.maxY=0}getClippedPathBoundingBox(t=Oe,e=null){return St.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function ei(t,e){if("undefined"!=typeof ImageData&&e instanceof ImageData)return void t.putImageData(e,0,0);const i=e.height,s=e.width,n=i%Qe,r=(i-n)/Qe,a=0===n?r:r+1,o=t.createImageData(s,Qe);let h,l=0;const d=e.data,c=o.data;let u,p,g,f;if(e.kind===T.GRAYSCALE_1BPP){const e=d.byteLength,i=new Uint32Array(c.buffer,0,c.byteLength>>2),f=i.length,m=s+7>>3,b=4294967295,v=xt.isLittleEndian?4278190080:255;for(u=0;u<a;u++){for(g=u<r?Qe:n,h=0,p=0;p<g;p++){const t=e-l;let n=0;const r=t>m?s:8*t-7,a=-8&r;let o=0,c=0;for(;n<a;n+=8)c=d[l++],i[h++]=128&c?b:v,i[h++]=64&c?b:v,i[h++]=32&c?b:v,i[h++]=16&c?b:v,i[h++]=8&c?b:v,i[h++]=4&c?b:v,i[h++]=2&c?b:v,i[h++]=1&c?b:v;for(;n<r;n++)0===o&&(c=d[l++],o=128),i[h++]=c&o?b:v,o>>=1}for(;h<f;)i[h++]=0;t.putImageData(o,0,u*Qe)}}else if(e.kind===T.RGBA_32BPP){for(p=0,f=s*Qe*4,u=0;u<r;u++)c.set(d.subarray(l,l+f)),l+=f,t.putImageData(o,0,p),p+=Qe;u<a&&(f=s*n*4,c.set(d.subarray(l,l+f)),t.putImageData(o,0,p))}else{if(e.kind!==T.RGB_24BPP)throw new Error(`bad image kind: ${e.kind}`);for(g=Qe,f=s*g,u=0;u<a;u++){for(u>=r&&(g=n,f=s*g),h=0,p=f;p--;)c[h++]=d[l++],c[h++]=d[l++],c[h++]=d[l++],c[h++]=255;t.putImageData(o,0,u*Qe)}}}function ii(t,e){if(e.bitmap)return void t.drawImage(e.bitmap,0,0);const i=e.height,s=e.width,n=i%Qe,r=(i-n)/Qe,a=0===n?r:r+1,o=t.createImageData(s,Qe);let h=0;const l=e.data,d=o.data;for(let e=0;e<a;e++){const i=e<r?Qe:n;({srcPos:h}=Ke({src:l,srcPos:h,dest:d,width:s,height:i,nonBlackColor:0})),t.putImageData(o,0,e*Qe)}}function si(t,e){const i=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of i)void 0!==t[s]&&(e[s]=t[s]);void 0!==t.setLineDash&&(e.setLineDash(t.getLineDash()),e.lineDashOffset=t.lineDashOffset)}function ni(t){if(t.strokeStyle=t.fillStyle="#000000",t.fillRule="nonzero",t.globalAlpha=1,t.lineWidth=1,t.lineCap="butt",t.lineJoin="miter",t.miterLimit=10,t.globalCompositeOperation="source-over",t.font="10px sans-serif",void 0!==t.setLineDash&&(t.setLineDash([]),t.lineDashOffset=0),!o){const{filter:e}=t;"none"!==e&&""!==e&&(t.filter="none")}}function ri(t,e){if(e)return!0;const i=St.singularValueDecompose2dScale(t);i[0]=Math.fround(i[0]),i[1]=Math.fround(i[1]);const s=Math.fround((globalThis.devicePixelRatio||1)*$t.PDF_TO_CSS_UNITS);return i[0]<=s&&i[1]<=s}const ai=["butt","round","square"],oi=["miter","round","bevel"],hi={},li={};class di{constructor(t,e,i,s,n,{optionalContentConfig:r,markedContentStack:a=null},o,h){this.ctx=t,this.current=new ti(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=i,this.canvasFactory=s,this.filterFactory=n,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=a||[],this.optionalContentConfig=r,this.cachedCanvases=new Je(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=o,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=h,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t,e=null){return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:i=!1,background:s=null}){const n=this.ctx.canvas.width,r=this.ctx.canvas.height,a=this.ctx.fillStyle;if(this.ctx.fillStyle=s||"#ffffff",this.ctx.fillRect(0,0,n,r),this.ctx.fillStyle=a,i){const t=this.cachedCanvases.getCanvas("transparent",n,r);this.compositeCtx=this.ctx,this.transparentCanvas=t.canvas,this.ctx=t.context,this.ctx.save(),this.ctx.transform(...le(this.compositeCtx))}this.ctx.save(),ni(this.ctx),t&&(this.ctx.transform(...t),this.outputScaleX=t[0],this.outputScaleY=t[0]),this.ctx.transform(...e.transform),this.viewportScale=e.scale,this.baseTransform=le(this.ctx)}executeOperatorList(t,e,i,s){const n=t.argsArray,r=t.fnArray;let a=e||0;const o=n.length;if(o===a)return a;const h=o-a>10&&"function"==typeof i,l=h?Date.now()+15:0;let d=0;const c=this.commonObjs,u=this.objs;let p;for(;;){if(void 0!==s&&a===s.nextBreakPoint)return s.breakIt(a,i),a;if(p=r[a],p!==tt.dependency)this[p].apply(this,n[a]);else for(const t of n[a]){const e=t.startsWith("g_")?c:u;if(!e.has(t))return e.get(t,i),a}if(a++,a===o)return a;if(h&&++d>10){if(Date.now()>l)return i(),a;d=0}}}#Re(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)}endDrawing(){this.#Re(),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),this.#De()}#De(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if("none"!==t){const e=this.ctx.filter;this.ctx.filter=t,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=e}}}_scaleImage(t,e){const i=t.width,s=t.height;let n,r,a=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),h=i,l=s,d="prescale1";for(;a>2&&h>1||o>2&&l>1;){let e=h,i=l;a>2&&h>1&&(e=h>=16384?Math.floor(h/2)-1||1:Math.ceil(h/2),a/=h/e),o>2&&l>1&&(i=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l)/2,o/=l/i),n=this.cachedCanvases.getCanvas(d,e,i),r=n.context,r.clearRect(0,0,e,i),r.drawImage(t,0,0,h,l,0,0,e,i),t=n.canvas,h=e,l=i,d="prescale1"===d?"prescale2":"prescale1"}return{img:t,paintWidth:h,paintHeight:l}}_createMaskCanvas(t){const e=this.ctx,{width:i,height:s}=t,n=this.current.fillColor,r=this.current.patternFill,a=le(e);let o,h,l,d;if((t.bitmap||t.data)&&t.count>1){const e=t.bitmap||t.data.buffer;h=JSON.stringify(r?a:[a.slice(0,4),n]),o=this._cachedBitmapsMap.get(e),o||(o=new Map,this._cachedBitmapsMap.set(e,o));const i=o.get(h);if(i&&!r){return{canvas:i,offsetX:Math.round(Math.min(a[0],a[2])+a[4]),offsetY:Math.round(Math.min(a[1],a[3])+a[5])}}l=i}l||(d=this.cachedCanvases.getCanvas("maskCanvas",i,s),ii(d.context,t));let c=St.transform(a,[1/i,0,0,-1/s,0,0]);c=St.transform(c,[1,0,0,1,0,-s]);const[u,p,g,f]=St.getAxialAlignedBoundingBox([0,0,i,s],c),m=Math.round(g-u)||1,b=Math.round(f-p)||1,v=this.cachedCanvases.getCanvas("fillCanvas",m,b),y=v.context,w=u,A=p;y.translate(-w,-A),y.transform(...c),l||(l=this._scaleImage(d.canvas,de(y)),l=l.img,o&&r&&o.set(h,l)),y.imageSmoothingEnabled=ri(le(y),t.interpolate),Ze(y,l,0,0,l.width,l.height,0,0,i,s),y.globalCompositeOperation="source-in";const x=St.transform(de(y),[1,0,0,1,-w,-A]);return y.fillStyle=r?n.getPattern(e,this,x,Oe):n,y.fillRect(0,0,i,s),o&&!r&&(this.cachedCanvases.delete("fillCanvas"),o.set(h,v.canvas)),{canvas:v.canvas,offsetX:Math.round(w),offsetY:Math.round(A)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=ai[t]}setLineJoin(t){this.ctx.lineJoin=oi[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const i=this.ctx;void 0!==i.setLineDash&&(i.setLineDash(t),i.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,i]of t)switch(e){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i[0],i[1]);break;case"CA":this.current.strokeAlpha=i;break;case"ca":this.current.fillAlpha=i,this.ctx.globalAlpha=i;break;case"BM":this.ctx.globalCompositeOperation=i;break;case"SMask":this.current.activeSMask=i?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(i)}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,i="smaskGroupAt"+this.groupLevel,s=this.cachedCanvases.getCanvas(i,t,e);this.suspendedCtx=this.ctx,this.ctx=s.context;const n=this.ctx;n.setTransform(...le(this.suspendedCtx)),si(this.suspendedCtx,n),function(t,e){if(t._removeMirroring)throw new Error("Context is already forwarding operations.");t.__originalSave=t.save,t.__originalRestore=t.restore,t.__originalRotate=t.rotate,t.__originalScale=t.scale,t.__originalTranslate=t.translate,t.__originalTransform=t.transform,t.__originalSetTransform=t.setTransform,t.__originalResetTransform=t.resetTransform,t.__originalClip=t.clip,t.__originalMoveTo=t.moveTo,t.__originalLineTo=t.lineTo,t.__originalBezierCurveTo=t.bezierCurveTo,t.__originalRect=t.rect,t.__originalClosePath=t.closePath,t.__originalBeginPath=t.beginPath,t._removeMirroring=()=>{t.save=t.__originalSave,t.restore=t.__originalRestore,t.rotate=t.__originalRotate,t.scale=t.__originalScale,t.translate=t.__originalTranslate,t.transform=t.__originalTransform,t.setTransform=t.__originalSetTransform,t.resetTransform=t.__originalResetTransform,t.clip=t.__originalClip,t.moveTo=t.__originalMoveTo,t.lineTo=t.__originalLineTo,t.bezierCurveTo=t.__originalBezierCurveTo,t.rect=t.__originalRect,t.closePath=t.__originalClosePath,t.beginPath=t.__originalBeginPath,delete t._removeMirroring},t.save=function(){e.save(),this.__originalSave()},t.restore=function(){e.restore(),this.__originalRestore()},t.translate=function(t,i){e.translate(t,i),this.__originalTranslate(t,i)},t.scale=function(t,i){e.scale(t,i),this.__originalScale(t,i)},t.transform=function(t,i,s,n,r,a){e.transform(t,i,s,n,r,a),this.__originalTransform(t,i,s,n,r,a)},t.setTransform=function(t,i,s,n,r,a){e.setTransform(t,i,s,n,r,a),this.__originalSetTransform(t,i,s,n,r,a)},t.resetTransform=function(){e.resetTransform(),this.__originalResetTransform()},t.rotate=function(t){e.rotate(t),this.__originalRotate(t)},t.clip=function(t){e.clip(t),this.__originalClip(t)},t.moveTo=function(t,i){e.moveTo(t,i),this.__originalMoveTo(t,i)},t.lineTo=function(t,i){e.lineTo(t,i),this.__originalLineTo(t,i)},t.bezierCurveTo=function(t,i,s,n,r,a){e.bezierCurveTo(t,i,s,n,r,a),this.__originalBezierCurveTo(t,i,s,n,r,a)},t.rect=function(t,i,s,n){e.rect(t,i,s,n),this.__originalRect(t,i,s,n)},t.closePath=function(){e.closePath(),this.__originalClosePath()},t.beginPath=function(){e.beginPath(),this.__originalBeginPath()}}(n,this.suspendedCtx),this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),si(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,i=this.suspendedCtx;this.composeSMask(i,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}composeSMask(t,e,i,s){const n=s[0],r=s[1],a=s[2]-n,o=s[3]-r;0!==a&&0!==o&&(this.genericComposeSMask(e.context,i,a,o,e.subtype,e.backdrop,e.transferMap,n,r,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(i.canvas,0,0),t.restore())}genericComposeSMask(t,e,i,s,n,r,a,o,h,l,d){let c=t.canvas,u=o-l,p=h-d;if(r)if(u<0||p<0||u+i>c.width||p+s>c.height){const t=this.cachedCanvases.getCanvas("maskExtension",i,s),e=t.context;e.drawImage(c,-u,-p),r.some((t=>0!==t))&&(e.globalCompositeOperation="destination-atop",e.fillStyle=St.makeHexColor(...r),e.fillRect(0,0,i,s),e.globalCompositeOperation="source-over"),c=t.canvas,u=p=0}else if(r.some((t=>0!==t))){t.save(),t.globalAlpha=1,t.setTransform(1,0,0,1,0,0);const e=new Path2D;e.rect(u,p,i,s),t.clip(e),t.globalCompositeOperation="destination-atop",t.fillStyle=St.makeHexColor(...r),t.fillRect(u,p,i,s),t.restore()}e.save(),e.globalAlpha=1,e.setTransform(1,0,0,1,0,0),"Alpha"===n&&a?e.filter=this.filterFactory.addAlphaFilter(a):"Luminosity"===n&&(e.filter=this.filterFactory.addLuminosityFilter(a));const g=new Path2D;g.rect(o,h,i,s),e.clip(g),e.globalCompositeOperation="destination-in",e.drawImage(c,u,p,i,s,o,h,i,s),e.restore()}save(){this.inSMaskMode?(si(this.ctx,this.suspendedCtx),this.suspendedCtx.save()):this.ctx.save();const t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){0===this.stateStack.length&&this.inSMaskMode&&this.endSMaskMode(),0!==this.stateStack.length&&(this.current=this.stateStack.pop(),this.inSMaskMode?(this.suspendedCtx.restore(),si(this.suspendedCtx,this.ctx)):this.ctx.restore(),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null)}transform(t,e,i,s,n,r){this.ctx.transform(t,e,i,s,n,r),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(t,e,i){const s=this.ctx,n=this.current;let r,a,o=n.x,h=n.y;const l=le(s),d=0===l[0]&&0===l[3]||0===l[1]&&0===l[2],c=d?i.slice(0):null;for(let i=0,u=0,p=t.length;i<p;i++)switch(0|t[i]){case tt.rectangle:o=e[u++],h=e[u++];const t=e[u++],i=e[u++],p=o+t,g=h+i;s.moveTo(o,h),0===t||0===i?s.lineTo(p,g):(s.lineTo(p,h),s.lineTo(p,g),s.lineTo(o,g)),d||n.updateRectMinMax(l,[o,h,p,g]),s.closePath();break;case tt.moveTo:o=e[u++],h=e[u++],s.moveTo(o,h),d||n.updatePathMinMax(l,o,h);break;case tt.lineTo:o=e[u++],h=e[u++],s.lineTo(o,h),d||n.updatePathMinMax(l,o,h);break;case tt.curveTo:r=o,a=h,o=e[u+4],h=e[u+5],s.bezierCurveTo(e[u],e[u+1],e[u+2],e[u+3],o,h),n.updateCurvePathMinMax(l,r,a,e[u],e[u+1],e[u+2],e[u+3],o,h,c),u+=6;break;case tt.curveTo2:r=o,a=h,s.bezierCurveTo(o,h,e[u],e[u+1],e[u+2],e[u+3]),n.updateCurvePathMinMax(l,r,a,o,h,e[u],e[u+1],e[u+2],e[u+3],c),o=e[u+2],h=e[u+3],u+=4;break;case tt.curveTo3:r=o,a=h,o=e[u+2],h=e[u+3],s.bezierCurveTo(e[u],e[u+1],o,h,o,h),n.updateCurvePathMinMax(l,r,a,e[u],e[u+1],o,h,o,h,c),u+=4;break;case tt.closePath:s.closePath()}d&&n.updateScalingPathMinMax(l,c),n.setCurrentPoint(o,h)}closePath(){this.ctx.closePath()}stroke(t=!0){const e=this.ctx,i=this.current.strokeColor;e.globalAlpha=this.current.strokeAlpha,this.contentVisible&&("object"==typeof i&&i?.getPattern?(e.save(),e.strokeStyle=i.getPattern(e,this,de(e),ze),this.rescaleAndStroke(!1),e.restore()):this.rescaleAndStroke(!0)),t&&this.consumePath(this.current.getClippedPathBoundingBox()),e.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath(),this.stroke()}fill(t=!0){const e=this.ctx,i=this.current.fillColor;let s=!1;this.current.patternFill&&(e.save(),e.fillStyle=i.getPattern(e,this,de(e),Oe),s=!0);const n=this.current.getClippedPathBoundingBox();this.contentVisible&&null!==n&&(this.pendingEOFill?(e.fill("evenodd"),this.pendingEOFill=!1):e.fill()),s&&e.restore(),t&&this.consumePath(n)}eoFill(){this.pendingEOFill=!0,this.fill()}fillStroke(){this.fill(!1),this.stroke(!1),this.consumePath()}eoFillStroke(){this.pendingEOFill=!0,this.fillStroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=hi}eoClip(){this.pendingClip=li}beginText(){this.current.textMatrix=h,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(void 0!==t){e.save(),e.beginPath();for(const i of t)e.setTransform(...i.transform),e.translate(i.x,i.y),i.addToPath(e,i.fontSize);e.restore(),e.clip(),e.beginPath(),delete this.pendingTextPaths}else e.beginPath()}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const i=this.commonObjs.get(t),s=this.current;if(!i)throw new Error(`Can't find font for ${t}`);if(s.fontMatrix=i.fontMatrix||l,0!==s.fontMatrix[0]&&0!==s.fontMatrix[3]||at("Invalid font matrix for font "+t),e<0?(e=-e,s.fontDirection=-1):s.fontDirection=1,this.current.font=i,this.current.fontSize=e,i.isType3Font)return;const n=i.loadedName||"sans-serif",r=i.systemFontInfo?.css||`"${n}", ${i.fallbackName}`;let a="normal";i.black?a="900":i.bold&&(a="bold");const o=i.italic?"italic":"normal";let h=e;e<16?h=16:e>100&&(h=100),this.current.fontSizeScale=e/h,this.ctx.font=`${o} ${a} ${h}px ${r}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t,e,i,s,n,r){this.current.textMatrix=[t,e,i,s,n,r],this.current.textMatrixScale=Math.hypot(t,e),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,i,s){const n=this.ctx,r=this.current,a=r.font,o=r.textRenderingMode,h=r.fontSize/r.fontSizeScale,l=o&C,d=!!(o&M),c=r.patternFill&&!a.missingFile;let u;if((a.disableFontFace||d||c)&&(u=a.getPathGenerator(this.commonObjs,t)),a.disableFontFace||c?(n.save(),n.translate(e,i),n.beginPath(),u(n,h),s&&n.setTransform(...s),l!==x&&l!==S||n.fill(),l!==_&&l!==S||n.stroke(),n.restore()):(l!==x&&l!==S||n.fillText(t,e,i),l!==_&&l!==S||n.strokeText(t,e,i)),d){(this.pendingTextPaths||=[]).push({transform:le(n),x:e,y:i,fontSize:h,addToPath:u})}}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1),t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let i=!1;for(let t=3;t<e.length;t+=4)if(e[t]>0&&e[t]<255){i=!0;break}return dt(this,"isFontSubpixelAAEnabled",i)}showText(t){const e=this.current,i=e.font;if(i.isType3Font)return this.showType3Text(t);const s=e.fontSize;if(0===s)return;const n=this.ctx,r=e.fontSizeScale,a=e.charSpacing,o=e.wordSpacing,h=e.fontDirection,l=e.textHScale*h,d=t.length,c=i.vertical,u=c?1:-1,p=i.defaultVMetrics,g=s*e.fontMatrix[0],f=e.textRenderingMode===x&&!i.disableFontFace&&!e.patternFill;let m;if(n.save(),n.transform(...e.textMatrix),n.translate(e.x,e.y+e.textRise),h>0?n.scale(l,-1):n.scale(l,1),e.patternFill){n.save();const t=e.fillColor.getPattern(n,this,de(n),Oe);m=le(n),n.restore(),n.fillStyle=t}let b=e.lineWidth;const v=e.textMatrixScale;if(0===v||0===b){const t=e.textRenderingMode&C;t!==_&&t!==S||(b=this.getSinglePixelWidth())}else b/=v;if(1!==r&&(n.scale(r,r),b/=r),n.lineWidth=b,i.isInvalidPDFjsFont){const i=[];let s=0;for(const e of t)i.push(e.unicode),s+=e.width;return n.fillText(i.join(""),0,0),e.x+=s*g*l,n.restore(),void this.compose()}let y,w=0;for(y=0;y<d;++y){const e=t[y];if("number"==typeof e){w+=u*e*s/1e3;continue}let l=!1;const d=(e.isSpace?o:0)+a,b=e.fontChar,v=e.accent;let A,x,_=e.width;if(c){const t=e.vmetric||p,i=-(e.vmetric?t[1]:.5*_)*g,s=t[2]*g;_=t?-t[0]:_,A=i/r,x=(w+s)/r}else A=w/r,x=0;if(i.remeasure&&_>0){const t=1e3*n.measureText(b).width/s*r;if(_<t&&this.isFontSubpixelAAEnabled){const e=_/t;l=!0,n.save(),n.scale(e,1),A/=e}else _!==t&&(A+=(_-t)/2e3*s/r)}if(this.contentVisible&&(e.isInFont||i.missingFile))if(f&&!v)n.fillText(b,A,x);else if(this.paintChar(b,A,x,m),v){const t=A+s*v.offset.x/r,e=x-s*v.offset.y/r;this.paintChar(v.fontChar,t,e,m)}w+=c?_*g-d*h:_*g+d*h,l&&n.restore()}c?e.y-=w:e.x+=w*l,n.restore(),this.compose()}showType3Text(t){const e=this.ctx,i=this.current,s=i.font,n=i.fontSize,r=i.fontDirection,a=s.vertical?1:-1,o=i.charSpacing,h=i.wordSpacing,d=i.textHScale*r,c=i.fontMatrix||l,u=t.length;let p,g,f,m;if(!(i.textRenderingMode===E)&&0!==n){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,e.save(),e.transform(...i.textMatrix),e.translate(i.x,i.y),e.scale(d,r),p=0;p<u;++p){if(g=t[p],"number"==typeof g){m=a*g*n/1e3,this.ctx.translate(m,0),i.x+=m*d;continue}const r=(g.isSpace?h:0)+o,l=s.charProcOperatorList[g.operatorListId];if(!l){at(`Type3 character "${g.operatorListId}" is not available.`);continue}this.contentVisible&&(this.processingType3=g,this.save(),e.scale(n,n),e.transform(...c),this.executeOperatorList(l),this.restore());f=St.applyTransform([g.width,0],c)[0]*n+r,e.translate(f,0),i.x+=f*d}e.restore(),this.processingType3=null}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,i,s,n,r){this.ctx.rect(i,s,n-i,r-s),this.ctx.clip(),this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){const i=t[1],s=this.baseTransform||le(this.ctx),n={createCanvasGraphics:t=>new di(t,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new Ye(t,i,this.ctx,n,s)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t,e,i){const s=St.makeHexColor(t,e,i);this.ctx.strokeStyle=s,this.current.strokeColor=s}setFillRGBColor(t,e,i){const s=St.makeHexColor(t,e,i);this.ctx.fillStyle=s,this.current.fillColor=s,this.current.patternFill=!1}_getPattern(t,e=null){let i;return this.cachedPatterns.has(t)?i=this.cachedPatterns.get(t):(i=function(t){switch(t[0]){case"RadialAxial":return new We(t);case"Mesh":return new Ge(t);case"Dummy":return new Ve}throw new Error(`Unknown IR type: ${t[0]}`)}(this.getObject(t)),this.cachedPatterns.set(t,i)),e&&(i.matrix=e),i}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const i=this._getPattern(t);e.fillStyle=i.getPattern(e,this,de(e),He);const s=de(e);if(s){const{width:t,height:i}=e.canvas,[n,r,a,o]=St.getAxialAlignedBoundingBox([0,0,t,i],s);this.ctx.fillRect(n,r,a-n,o-r)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){ot("Should not call beginInlineImage")}beginImageData(){ot("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),t&&this.transform(...t),this.baseTransform=le(this.ctx),e)){const t=e[2]-e[0],i=e[3]-e[1];this.ctx.rect(e[0],e[1],t,i),this.current.updateRectMinMax(le(this.ctx),e),this.clip(),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const e=this.ctx;t.isolated||rt("TODO: Support non-isolated groups."),t.knockout&&at("Knockout groups not supported.");const i=le(e);if(t.matrix&&e.transform(...t.matrix),!t.bbox)throw new Error("Bounding box is required.");let s=St.getAxialAlignedBoundingBox(t.bbox,le(e));const n=[0,0,e.canvas.width,e.canvas.height];s=St.intersect(s,n)||[0,0,0,0];const r=Math.floor(s[0]),a=Math.floor(s[1]),o=Math.max(Math.ceil(s[2])-r,1),h=Math.max(Math.ceil(s[3])-a,1);this.current.startNewPathAndClipBox([0,0,o,h]);let l="groupAt"+this.groupLevel;t.smask&&(l+="_smask_"+this.smaskCounter++%2);const d=this.cachedCanvases.getCanvas(l,o,h),c=d.context;c.translate(-r,-a),c.transform(...i),t.smask?this.smaskStack.push({canvas:d.canvas,context:c,offsetX:r,offsetY:a,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(r,a),e.save()),si(e,c),this.ctx=c,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,i=this.groupStack.pop();if(this.ctx=i,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const t=le(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...t);const i=St.getAxialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t);this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(i)}}beginAnnotation(t,e,i,s,n){if(this.#Re(),ni(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),e){const s=e[2]-e[0],r=e[3]-e[1];if(n&&this.annotationCanvasMap){(i=i.slice())[4]-=e[0],i[5]-=e[1],(e=e.slice())[0]=e[1]=0,e[2]=s,e[3]=r;const[n,a]=St.singularValueDecompose2dScale(le(this.ctx)),{viewportScale:o}=this,h=Math.ceil(s*this.outputScaleX*o),l=Math.ceil(r*this.outputScaleY*o);this.annotationCanvas=this.canvasFactory.create(h,l);const{canvas:d,context:c}=this.annotationCanvas;this.annotationCanvasMap.set(t,d),this.annotationCanvas.savedCtx=this.ctx,this.ctx=c,this.ctx.save(),this.ctx.setTransform(n,0,0,-a,0,r*a),ni(this.ctx)}else ni(this.ctx),this.ctx.rect(e[0],e[1],s,r),this.ctx.clip(),this.endPath()}this.current=new ti(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...i),this.transform(...s)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),this.#De(),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;(t=this.getObject(t.data,t)).count=e;const i=this.ctx,s=this.processingType3;if(s&&(void 0===s.compiled&&(s.compiled=function(t){const{width:e,height:i}=t;if(e>1e3||i>1e3)return null;const s=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),n=e+1;let r,a,o,h=new Uint8Array(n*(i+1));const l=e+7&-8;let d=new Uint8Array(l*i),c=0;for(const e of t.data){let t=128;for(;t>0;)d[c++]=e&t?0:255,t>>=1}let u=0;for(c=0,0!==d[c]&&(h[0]=1,++u),a=1;a<e;a++)d[c]!==d[c+1]&&(h[a]=d[c]?2:1,++u),c++;for(0!==d[c]&&(h[a]=2,++u),r=1;r<i;r++){c=r*l,o=r*n,d[c-l]!==d[c]&&(h[o]=d[c]?1:8,++u);let t=(d[c]?4:0)+(d[c-l]?8:0);for(a=1;a<e;a++)t=(t>>2)+(d[c+1]?4:0)+(d[c-l+1]?8:0),s[t]&&(h[o+a]=s[t],++u),c++;if(d[c-l]!==d[c]&&(h[o+a]=d[c]?2:4,++u),u>1e3)return null}for(c=l*(i-1),o=r*n,0!==d[c]&&(h[o]=8,++u),a=1;a<e;a++)d[c]!==d[c+1]&&(h[o+a]=d[c]?4:8,++u),c++;if(0!==d[c]&&(h[o+a]=4,++u),u>1e3)return null;const p=new Int32Array([0,n,-1,0,-n,0,0,0,1]),g=new Path2D;for(r=0;u&&r<=i;r++){let t=r*n;const i=t+e;for(;t<i&&!h[t];)t++;if(t===i)continue;g.moveTo(t%n,r);const s=t;let a=h[t];do{const e=p[a];do{t+=e}while(!h[t]);const i=h[t];5!==i&&10!==i?(a=i,h[t]=0):(a=i&51*a>>4,h[t]&=a>>2|a<<2),g.lineTo(t%n,t/n|0),h[t]||--u}while(s!==t);--r}return d=null,h=null,function(t){t.save(),t.scale(1/e,-1/i),t.translate(0,-i),t.fill(g),t.beginPath(),t.restore()}}(t)),s.compiled))return void s.compiled(i);const n=this._createMaskCanvas(t),r=n.canvas;i.save(),i.setTransform(1,0,0,1,0,0),i.drawImage(r,n.offsetX,n.offsetY),i.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e,i=0,s=0,n,r){if(!this.contentVisible)return;t=this.getObject(t.data,t);const a=this.ctx;a.save();const o=le(a);a.transform(e,i,s,n,0,0);const h=this._createMaskCanvas(t);a.setTransform(1,0,0,1,h.offsetX-o[4],h.offsetY-o[5]);for(let t=0,l=r.length;t<l;t+=2){const l=St.transform(o,[e,i,s,n,r[t],r[t+1]]),[d,c]=St.applyTransform([0,0],l);a.drawImage(h.canvas,d,c)}a.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,i=this.current.fillColor,s=this.current.patternFill;for(const n of t){const{data:t,width:r,height:a,transform:o}=n,h=this.cachedCanvases.getCanvas("maskCanvas",r,a),l=h.context;l.save();ii(l,this.getObject(t,n)),l.globalCompositeOperation="source-in",l.fillStyle=s?i.getPattern(l,this,de(e),Oe):i,l.fillRect(0,0,r,a),l.restore(),e.save(),e.transform(...o),e.scale(1,-1),Ze(e,h.canvas,0,0,r,a,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):at("Dependent image isn't ready yet")}paintImageXObjectRepeat(t,e,i,s){if(!this.contentVisible)return;const n=this.getObject(t);if(!n)return void at("Dependent image isn't ready yet");const r=n.width,a=n.height,o=[];for(let t=0,n=s.length;t<n;t+=2)o.push({transform:[e,0,0,i,s[t],s[t+1]],x:0,y:0,w:r,h:a});this.paintInlineImageXObjectGroup(n,o)}applyTransferMapsToCanvas(t){return"none"!==this.current.transferMaps&&(t.filter=this.current.transferMaps,t.drawImage(t.canvas,0,0),t.filter="none"),t.canvas}applyTransferMapsToBitmap(t){if("none"===this.current.transferMaps)return t.bitmap;const{bitmap:e,width:i,height:s}=t,n=this.cachedCanvases.getCanvas("inlineImage",i,s),r=n.context;return r.filter=this.current.transferMaps,r.drawImage(e,0,0),r.filter="none",n.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,i=t.height,s=this.ctx;if(this.save(),!o){const{filter:t}=s;"none"!==t&&""!==t&&(s.filter="none")}let n;if(s.scale(1/e,-1/i),t.bitmap)n=this.applyTransferMapsToBitmap(t);else if("function"==typeof HTMLElement&&t instanceof HTMLElement||!t.data)n=t;else{const s=this.cachedCanvases.getCanvas("inlineImage",e,i).context;ei(s,t),n=this.applyTransferMapsToCanvas(s)}const r=this._scaleImage(n,de(s));s.imageSmoothingEnabled=ri(le(s),t.interpolate),Ze(s,r.img,0,0,r.paintWidth,r.paintHeight,0,-i,e,i),this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const i=this.ctx;let s;if(t.bitmap)s=t.bitmap;else{const e=t.width,i=t.height,n=this.cachedCanvases.getCanvas("inlineImage",e,i).context;ei(n,t),s=this.applyTransferMapsToCanvas(n)}for(const t of e)i.save(),i.transform(...t.transform),i.scale(1,-1),Ze(i,s,t.x,t.y,t.w,t.h,0,-1,1,1),i.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t){const e=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(t);const i=this.ctx;this.pendingClip&&(e||(this.pendingClip===li?i.clip("evenodd"):i.clip()),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox),i.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=le(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),i=Math.hypot(t[0],t[2]),s=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(i,s)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(-1===this._cachedScaleForStroking[0]){const{lineWidth:t}=this.current,{a:e,b:i,c:s,d:n}=this.ctx.getTransform();let r,a;if(0===i&&0===s){const i=Math.abs(e),s=Math.abs(n);if(i===s)if(0===t)r=a=1/i;else{const e=i*t;r=a=e<1?1/e:1}else if(0===t)r=1/i,a=1/s;else{const e=i*t,n=s*t;r=e<1?1/e:1,a=n<1?1/n:1}}else{const o=Math.abs(e*n-i*s),h=Math.hypot(e,i),l=Math.hypot(s,n);if(0===t)r=l/o,a=h/o;else{const e=t*o;r=l>e?l/e:1,a=h>e?h/e:1}}this._cachedScaleForStroking[0]=r,this._cachedScaleForStroking[1]=a}return this._cachedScaleForStroking}rescaleAndStroke(t){const{ctx:e}=this,{lineWidth:i}=this.current,[s,n]=this.getScaleForStroking();if(e.lineWidth=i||1,1===s&&1===n)return void e.stroke();const r=e.getLineDash();if(t&&e.save(),e.scale(s,n),r.length>0){const t=Math.max(s,n);e.setLineDash(r.map((e=>e/t))),e.lineDashOffset/=t}e.stroke(),t&&e.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}for(const t in tt)void 0!==di.prototype[t]&&(di.prototype[tt[t]]=di.prototype[t]);class ci{static#Le=null;static#Ie="";static get workerPort(){return this.#Le}static set workerPort(t){if(!("undefined"!=typeof Worker&&t instanceof Worker)&&null!==t)throw new Error("Invalid `workerPort` type.");this.#Le=t}static get workerSrc(){return this.#Ie}static set workerSrc(t){if("string"!=typeof t)throw new Error("Invalid `workerSrc` type.");this.#Ie=t}}const ui=1,pi=2,gi=1,fi=2,mi=3,bi=4,vi=5,yi=6,wi=7,Ai=8;function xi(t){switch(t instanceof Error||"object"==typeof t&&null!==t||ot('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),t.name){case"AbortException":return new vt(t.message);case"MissingPDFException":return new ft(t.message);case"PasswordException":return new ut(t.message,t.code);case"UnexpectedResponseException":return new mt(t.message,t.status);case"UnknownErrorException":return new pt(t.message,t.details);default:return new pt(t.message,t.toString())}}class _i{constructor(t,e,i){this.sourceName=t,this.targetName=e,this.comObj=i,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),this._onComObjOnMessage=t=>{const e=t.data;if(e.targetName!==this.sourceName)return;if(e.stream)return void this.#Fe(e);if(e.callback){const t=e.callbackId,i=this.callbackCapabilities[t];if(!i)throw new Error(`Cannot resolve callback ${t}`);if(delete this.callbackCapabilities[t],e.callback===ui)i.resolve(e.data);else{if(e.callback!==pi)throw new Error("Unexpected callback case");i.reject(xi(e.reason))}return}const s=this.actionHandler[e.action];if(!s)throw new Error(`Unknown action from worker: ${e.action}`);if(e.callbackId){const t=this.sourceName,n=e.sourceName;new Promise((function(t){t(s(e.data))})).then((function(s){i.postMessage({sourceName:t,targetName:n,callback:ui,callbackId:e.callbackId,data:s})}),(function(s){i.postMessage({sourceName:t,targetName:n,callback:pi,callbackId:e.callbackId,reason:xi(s)})}))}else e.streamId?this.#Ne(e):s(e.data)},i.addEventListener("message",this._onComObjOnMessage)}on(t,e){const i=this.actionHandler;if(i[t])throw new Error(`There is already an actionName called "${t}"`);i[t]=e}send(t,e,i){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},i)}sendWithPromise(t,e,i){const s=this.callbackId++,n=Promise.withResolvers();this.callbackCapabilities[s]=n;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:s,data:e},i)}catch(t){n.reject(t)}return n.promise}sendWithStream(t,e,i,s){const n=this.streamId++,r=this.sourceName,a=this.targetName,o=this.comObj;return new ReadableStream({start:i=>{const h=Promise.withResolvers();return this.streamControllers[n]={controller:i,startCall:h,pullCall:null,cancelCall:null,isClosed:!1},o.postMessage({sourceName:r,targetName:a,action:t,streamId:n,data:e,desiredSize:i.desiredSize},s),h.promise},pull:t=>{const e=Promise.withResolvers();return this.streamControllers[n].pullCall=e,o.postMessage({sourceName:r,targetName:a,stream:yi,streamId:n,desiredSize:t.desiredSize}),e.promise},cancel:t=>{ht(t instanceof Error,"cancel must have a valid reason");const e=Promise.withResolvers();return this.streamControllers[n].cancelCall=e,this.streamControllers[n].isClosed=!0,o.postMessage({sourceName:r,targetName:a,stream:gi,streamId:n,reason:xi(t)}),e.promise}},i)}#Ne(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,n=this.comObj,r=this,a=this.actionHandler[t.action],o={enqueue(t,r=1,a){if(this.isCancelled)return;const o=this.desiredSize;this.desiredSize-=r,o>0&&this.desiredSize<=0&&(this.sinkCapability=Promise.withResolvers(),this.ready=this.sinkCapability.promise),n.postMessage({sourceName:i,targetName:s,stream:bi,streamId:e,chunk:t},a)},close(){this.isCancelled||(this.isCancelled=!0,n.postMessage({sourceName:i,targetName:s,stream:mi,streamId:e}),delete r.streamSinks[e])},error(t){ht(t instanceof Error,"error must have a valid reason"),this.isCancelled||(this.isCancelled=!0,n.postMessage({sourceName:i,targetName:s,stream:vi,streamId:e,reason:xi(t)}))},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};o.sinkCapability.resolve(),o.ready=o.sinkCapability.promise,this.streamSinks[e]=o,new Promise((function(e){e(a(t.data,o))})).then((function(){n.postMessage({sourceName:i,targetName:s,stream:Ai,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:Ai,streamId:e,reason:xi(t)})}))}#Fe(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,n=this.comObj,r=this.streamControllers[e],a=this.streamSinks[e];switch(t.stream){case Ai:t.success?r.startCall.resolve():r.startCall.reject(xi(t.reason));break;case wi:t.success?r.pullCall.resolve():r.pullCall.reject(xi(t.reason));break;case yi:if(!a){n.postMessage({sourceName:i,targetName:s,stream:wi,streamId:e,success:!0});break}a.desiredSize<=0&&t.desiredSize>0&&a.sinkCapability.resolve(),a.desiredSize=t.desiredSize,new Promise((function(t){t(a.onPull?.())})).then((function(){n.postMessage({sourceName:i,targetName:s,stream:wi,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:wi,streamId:e,reason:xi(t)})}));break;case bi:if(ht(r,"enqueue should have stream controller"),r.isClosed)break;r.controller.enqueue(t.chunk);break;case mi:if(ht(r,"close should have stream controller"),r.isClosed)break;r.isClosed=!0,r.controller.close(),this.#Oe(r,e);break;case vi:ht(r,"error should have stream controller"),r.controller.error(xi(t.reason)),this.#Oe(r,e);break;case fi:t.success?r.cancelCall.resolve():r.cancelCall.reject(xi(t.reason)),this.#Oe(r,e);break;case gi:if(!a)break;new Promise((function(e){e(a.onCancel?.(xi(t.reason)))})).then((function(){n.postMessage({sourceName:i,targetName:s,stream:fi,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:fi,streamId:e,reason:xi(t)})})),a.sinkCapability.reject(xi(t.reason)),a.isCancelled=!0,delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async#Oe(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]),delete this.streamControllers[e]}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}class Si{#ze;#He;constructor({parsedData:t,rawData:e}){this.#ze=t,this.#He=e}getRaw(){return this.#He}get(t){return this.#ze.get(t)??null}getAll(){return At(this.#ze)}has(t){return this.#ze.has(t)}}const Ei=Symbol("INTERNAL");class Ci{#Be=!1;#je=!1;#We=!1;#Ue=!0;constructor(t,{name:e,intent:i,usage:s}){this.#Be=!!(t&u),this.#je=!!(t&p),this.name=e,this.intent=i,this.usage=s}get visible(){if(this.#We)return this.#Ue;if(!this.#Ue)return!1;const{print:t,view:e}=this.usage;return this.#Be?"OFF"!==e?.viewState:!this.#je||"OFF"!==t?.printState}_setVisible(t,e,i=!1){t!==Ei&&ot("Internal method `_setVisible` called."),this.#We=i,this.#Ue=e}}class Mi{#$e=null;#Ge=new Map;#Ve=null;#qe=null;constructor(t,e=u){if(this.renderingIntent=e,this.name=null,this.creator=null,null!==t){this.name=t.name,this.creator=t.creator,this.#qe=t.order;for(const i of t.groups)this.#Ge.set(i.id,new Ci(e,i));if("OFF"===t.baseState)for(const t of this.#Ge.values())t._setVisible(Ei,!1);for(const e of t.on)this.#Ge.get(e)._setVisible(Ei,!0);for(const e of t.off)this.#Ge.get(e)._setVisible(Ei,!1);this.#Ve=this.getHash()}}#Xe(t){const e=t.length;if(e<2)return!0;const i=t[0];for(let s=1;s<e;s++){const e=t[s];let n;if(Array.isArray(e))n=this.#Xe(e);else{if(!this.#Ge.has(e))return at(`Optional content group not found: ${e}`),!0;n=this.#Ge.get(e).visible}switch(i){case"And":if(!n)return!1;break;case"Or":if(n)return!0;break;case"Not":return!n;default:return!0}}return"And"===i}isVisible(t){if(0===this.#Ge.size)return!0;if(!t)return rt("Optional content group not defined."),!0;if("OCG"===t.type)return this.#Ge.has(t.id)?this.#Ge.get(t.id).visible:(at(`Optional content group not found: ${t.id}`),!0);if("OCMD"===t.type){if(t.expression)return this.#Xe(t.expression);if(!t.policy||"AnyOn"===t.policy){for(const e of t.ids){if(!this.#Ge.has(e))return at(`Optional content group not found: ${e}`),!0;if(this.#Ge.get(e).visible)return!0}return!1}if("AllOn"===t.policy){for(const e of t.ids){if(!this.#Ge.has(e))return at(`Optional content group not found: ${e}`),!0;if(!this.#Ge.get(e).visible)return!1}return!0}if("AnyOff"===t.policy){for(const e of t.ids){if(!this.#Ge.has(e))return at(`Optional content group not found: ${e}`),!0;if(!this.#Ge.get(e).visible)return!0}return!1}if("AllOff"===t.policy){for(const e of t.ids){if(!this.#Ge.has(e))return at(`Optional content group not found: ${e}`),!0;if(this.#Ge.get(e).visible)return!1}return!0}return at(`Unknown optional content policy ${t.policy}.`),!0}return at(`Unknown group type ${t.type}.`),!0}setVisibility(t,e=!0){const i=this.#Ge.get(t);i?(i._setVisible(Ei,!!e,!0),this.#$e=null):at(`Optional content group not found: ${t}`)}setOCGState({state:t,preserveRB:e}){let i;for(const e of t){switch(e){case"ON":case"OFF":case"Toggle":i=e;continue}const t=this.#Ge.get(e);if(t)switch(i){case"ON":t._setVisible(Ei,!0);break;case"OFF":t._setVisible(Ei,!1);break;case"Toggle":t._setVisible(Ei,!t.visible)}}this.#$e=null}get hasInitialVisibility(){return null===this.#Ve||this.getHash()===this.#Ve}getOrder(){return this.#Ge.size?this.#qe?this.#qe.slice():[...this.#Ge.keys()]:null}getGroups(){return this.#Ge.size>0?At(this.#Ge):null}getGroup(t){return this.#Ge.get(t)||null}getHash(){if(null!==this.#$e)return this.#$e;const t=new Me;for(const[e,i]of this.#Ge)t.update(`${e}:${i.visible}`);return this.#$e=t.hexdigest()}}class Ti{constructor(t,{disableRange:e=!1,disableStream:i=!1}){ht(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:s,initialData:n,progressiveDone:r,contentDispositionFilename:a}=t;if(this._queuedChunks=[],this._progressiveDone=r,this._contentDispositionFilename=a,n?.length>0){const t=n instanceof Uint8Array&&n.byteLength===n.buffer.byteLength?n.buffer:new Uint8Array(n).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=t,this._isStreamingSupported=!i,this._isRangeSupported=!e,this._contentLength=s,this._fullRequestReader=null,this._rangeReaders=[],t.addRangeListener(((t,e)=>{this._onReceiveData({begin:t,chunk:e})})),t.addProgressListener(((t,e)=>{this._onProgress({loaded:t,total:e})})),t.addProgressiveReadListener((t=>{this._onReceiveData({chunk:t})})),t.addProgressiveDoneListener((()=>{this._onProgressiveDone()})),t.transportReady()}_onReceiveData({begin:t,chunk:e}){const i=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(void 0===t)this._fullRequestReader?this._fullRequestReader._enqueue(i):this._queuedChunks.push(i);else{ht(this._rangeReaders.some((function(e){return e._begin===t&&(e._enqueue(i),!0)})),"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){void 0===t.total?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){ht(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;return this._queuedChunks=null,new ki(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new Pi(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}}class ki{constructor(t,e,i=!1,s=null){this._stream=t,this._done=i||!1,this._filename=Jt(s)?s:null,this._queuedChunks=e||[],this._loaded=0;for(const t of this._queuedChunks)this._loaded+=t.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length>0){this._requests.shift().resolve({value:t,done:!1})}else this._queuedChunks.push(t);this._loaded+=t.byteLength}}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0){return{value:this._queuedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class Pi{constructor(t,e,i){this._stream=t,this._begin=e,this._end=i,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}function Ri({getResponseHeader:t,isHttp:e,rangeChunkSize:i,disableRange:s}){const n={allowRangeRequests:!1,suggestedLength:void 0},r=parseInt(t("Content-Length"),10);if(!Number.isInteger(r))return n;if(n.suggestedLength=r,r<=2*i)return n;if(s||!e)return n;if("bytes"!==t("Accept-Ranges"))return n;return"identity"!==(t("Content-Encoding")||"identity")||(n.allowRangeRequests=!0),n}function Di(t){const e=t("Content-Disposition");if(e){let t=function(t){let e=!0,i=s("filename\\*","i").exec(t);if(i){i=i[1];let t=a(i);return t=unescape(t),t=o(t),t=h(t),r(t)}if(i=function(t){const e=[];let i;const n=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(i=n.exec(t));){let[,t,s,n]=i;if(t=parseInt(t,10),t in e){if(0===t)break}else e[t]=[s,n]}const r=[];for(let t=0;t<e.length&&t in e;++t){let[i,s]=e[t];s=a(s),i&&(s=unescape(s),0===t&&(s=o(s))),r.push(s)}return r.join("")}(t),i)return r(h(i));if(i=s("filename","i").exec(t),i){i=i[1];let t=a(i);return t=h(t),r(t)}function s(t,e){return new RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function n(t,i){if(t){if(!/^[\x00-\xFF]+$/.test(i))return i;try{const s=new TextDecoder(t,{fatal:!0}),n=wt(i);i=s.decode(n),e=!1}catch{}}return i}function r(t){return e&&/[\x80-\xff]/.test(t)&&(t=n("utf-8",t),e&&(t=n("iso-8859-1",t))),t}function a(t){if(t.startsWith('"')){const e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){const i=e[t].indexOf('"');-1!==i&&(e[t]=e[t].slice(0,i),e.length=t+1),e[t]=e[t].replaceAll(/\\(.)/g,"$1")}t=e.join('"')}return t}function o(t){const e=t.indexOf("'");return-1===e?t:n(t.slice(0,e),t.slice(e+1).replace(/^[^']*'/,""))}function h(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(t,e,i,s){if("q"===i||"Q"===i)return n(e,s=(s=s.replaceAll("_"," ")).replaceAll(/=([0-9a-fA-F]{2})/g,(function(t,e){return String.fromCharCode(parseInt(e,16))})));try{s=atob(s)}catch{}return n(e,s)}))}return""}(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch{}if(Jt(t))return t}return null}function Li(t,e){return 404===t||0===t&&e.startsWith("file:")?new ft('Missing PDF "'+e+'".'):new mt(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t)}function Ii(t){return 200===t||206===t}function Fi(t,e,i){return{method:"GET",headers:t,signal:i.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function Ni(t){const e=new Headers;for(const i in t){const s=t[i];void 0!==s&&e.append(i,s)}return e}function Oi(t){return t instanceof Uint8Array?t.buffer:t instanceof ArrayBuffer?t:(at(`getArrayBuffer - unexpected data format: ${t}`),new Uint8Array(t).buffer)}class zi{constructor(t){this.source=t,this.isHttp=/^https?:/i.test(t.url),this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return ht(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new Hi(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new Bi(this,t,e);return this._rangeRequestReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class Hi{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._headers=Ni(this._stream.httpHeaders);const i=e.url;fetch(i,Fi(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!Ii(t.status))throw Li(t.status,i);this._reader=t.body.getReader(),this._headersCapability.resolve();const e=e=>t.headers.get(e),{allowRangeRequests:s,suggestedLength:n}=Ri({getResponseHeader:e,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=s,this._contentLength=n||this._contentLength,this._filename=Di(e),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new vt("Streaming is disabled."))})).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:Oi(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class Bi{constructor(t,e,i){this._stream=t,this._reader=null,this._loaded=0;const s=t.source;this._withCredentials=s.withCredentials||!1,this._readCapability=Promise.withResolvers(),this._isStreamingSupported=!s.disableStream,this._abortController=new AbortController,this._headers=Ni(this._stream.httpHeaders),this._headers.append("Range",`bytes=${e}-${i-1}`);const n=s.url;fetch(n,Fi(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!Ii(t.status))throw Li(t.status,n);this._readCapability.resolve(),this._reader=t.body.getReader()})).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded}),{value:Oi(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class ji{constructor(t,e={}){this.url=t,this.isHttp=/^https?:/i.test(t),this.httpHeaders=this.isHttp&&e.httpHeaders||Object.create(null),this.withCredentials=e.withCredentials||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}requestRange(t,e,i){const s={begin:t,end:e};for(const t in i)s[t]=i[t];return this.request(s)}requestFull(t){return this.request(t)}request(t){const e=new XMLHttpRequest,i=this.currXhrId++,s=this.pendingRequests[i]={xhr:e};e.open("GET",this.url),e.withCredentials=this.withCredentials;for(const t in this.httpHeaders){const i=this.httpHeaders[t];void 0!==i&&e.setRequestHeader(t,i)}return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`),s.expectedStatus=206):s.expectedStatus=200,e.responseType="arraybuffer",t.onError&&(e.onerror=function(i){t.onError(e.status)}),e.onreadystatechange=this.onStateChange.bind(this,i),e.onprogress=this.onProgress.bind(this,i),s.onHeadersReceived=t.onHeadersReceived,s.onDone=t.onDone,s.onError=t.onError,s.onProgress=t.onProgress,e.send(null),i}onProgress(t,e){const i=this.pendingRequests[t];i&&i.onProgress?.(e)}onStateChange(t,e){const i=this.pendingRequests[t];if(!i)return;const s=i.xhr;if(s.readyState>=2&&i.onHeadersReceived&&(i.onHeadersReceived(),delete i.onHeadersReceived),4!==s.readyState)return;if(!(t in this.pendingRequests))return;if(delete this.pendingRequests[t],0===s.status&&this.isHttp)return void i.onError?.(s.status);const n=s.status||200;if(!(200===n&&206===i.expectedStatus)&&n!==i.expectedStatus)return void i.onError?.(s.status);const r=function(t){const e=t.response;return"string"!=typeof e?e:wt(e).buffer}(s);if(206===n){const t=s.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);i.onDone({begin:parseInt(e[1],10),chunk:r})}else r?i.onDone({begin:0,chunk:r}):i.onError?.(s.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}class Wi{constructor(t){this._source=t,this._manager=new ji(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials}),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return ht(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new Ui(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){const i=new $i(this._manager,t,e);return i.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class Ui{constructor(t,e){this._manager=t;const i={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=e.url,this._fullRequestId=t.requestFull(i),this._headersReceivedCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t),i=t=>e.getResponseHeader(t),{allowRangeRequests:s,suggestedLength:n}=Ri({getResponseHeader:i,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});s&&(this._isRangeSupported=!0),this._contentLength=n||this._contentLength,this._filename=Di(i),this._isRangeSupported&&this._manager.abortRequest(t),this._headersReceivedCapability.resolve()}_onDone(t){if(t)if(this._requests.length>0){this._requests.shift().resolve({value:t.chunk,done:!1})}else this._cachedChunks.push(t.chunk);if(this._done=!0,!(this._cachedChunks.length>0)){for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=Li(t,this._url),this._headersReceivedCapability.reject(this._storedError);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0){return{value:this._cachedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0,this._headersReceivedCapability.reject(t);for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class $i{constructor(t,e,i){this._manager=t;const s={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=t.url,this._requestId=t.requestRange(e,i,s),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;if(this._requests.length>0){this._requests.shift().resolve({value:e,done:!1})}else this._queuedChunk=e;this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){this._storedError=Li(t,this._url);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){const t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}const Gi=/^file:\/\/\/[a-zA-Z]:\//;class Vi{constructor(t){this.source=t,this.url=function(t){const e=Fe.get("url"),i=e.parse(t);return"file:"===i.protocol||i.host?i:/^[a-z]:[/\\]/i.test(t)?e.parse(`file:///${t}`):(i.host||(i.protocol="file:"),i)}(t.url),this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol,this.isFsUrl="file:"===this.url.protocol,this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return ht(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=this.isFsUrl?new Ji(this):new Ki(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=this.isFsUrl?new Zi(this,t,e):new Qi(this,t,e);return this._rangeRequestReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class qi{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;const e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=Promise.withResolvers(),this._headersCapability=Promise.withResolvers()}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t)return this._readCapability=Promise.withResolvers(),this.read();this._loaded+=t.length,this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",(()=>{this._readCapability.resolve()})),t.on("end",(()=>{t.destroy(),this._done=!0,this._readCapability.resolve()})),t.on("error",(t=>{this._error(t)})),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new vt("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class Xi{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=Promise.withResolvers();const e=t.source;this._isStreamingSupported=!e.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t)return this._readCapability=Promise.withResolvers(),this.read();this._loaded+=t.length,this.onProgress?.({loaded:this._loaded});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",(()=>{this._readCapability.resolve()})),t.on("end",(()=>{t.destroy(),this._done=!0,this._readCapability.resolve()})),t.on("error",(t=>{this._error(t)})),this._storedError&&this._readableStream.destroy(this._storedError)}}function Yi(t,e){return{protocol:t.protocol,auth:t.auth,host:t.hostname,port:t.port,path:t.path,method:"GET",headers:e}}class Ki extends qi{constructor(t){super(t);const e=e=>{if(404===e.statusCode){const t=new ft(`Missing PDF "${this._url}".`);return this._storedError=t,void this._headersCapability.reject(t)}this._headersCapability.resolve(),this._setReadableStream(e);const i=t=>this._readableStream.headers[t.toLowerCase()],{allowRangeRequests:s,suggestedLength:n}=Ri({getResponseHeader:i,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=s,this._contentLength=n||this._contentLength,this._filename=Di(i)};if(this._request=null,"http:"===this._url.protocol){const i=Fe.get("http");this._request=i.request(Yi(this._url,t.httpHeaders),e)}else{const i=Fe.get("https");this._request=i.request(Yi(this._url,t.httpHeaders),e)}this._request.on("error",(t=>{this._storedError=t,this._headersCapability.reject(t)})),this._request.end()}}class Qi extends Xi{constructor(t,e,i){super(t),this._httpHeaders={};for(const e in t.httpHeaders){const i=t.httpHeaders[e];void 0!==i&&(this._httpHeaders[e]=i)}this._httpHeaders.Range=`bytes=${e}-${i-1}`;const s=t=>{if(404!==t.statusCode)this._setReadableStream(t);else{const t=new ft(`Missing PDF "${this._url}".`);this._storedError=t}};if(this._request=null,"http:"===this._url.protocol){const t=Fe.get("http");this._request=t.request(Yi(this._url,this._httpHeaders),s)}else{const t=Fe.get("https");this._request=t.request(Yi(this._url,this._httpHeaders),s)}this._request.on("error",(t=>{this._storedError=t})),this._request.end()}}class Ji extends qi{constructor(t){super(t);let e=decodeURIComponent(this._url.path);Gi.test(this._url.href)&&(e=e.replace(/^\//,""));const i=Fe.get("fs");i.promises.lstat(e).then((t=>{this._contentLength=t.size,this._setReadableStream(i.createReadStream(e)),this._headersCapability.resolve()}),(t=>{"ENOENT"===t.code&&(t=new ft(`Missing PDF "${e}".`)),this._storedError=t,this._headersCapability.reject(t)}))}}class Zi extends Xi{constructor(t,e,i){super(t);let s=decodeURIComponent(this._url.path);Gi.test(this._url.href)&&(s=s.replace(/^\//,""));const n=Fe.get("fs");this._setReadableStream(n.createReadStream(s,{start:e,end:i-1}))}}const ts=30;class es{#Ye=Promise.withResolvers();#mt=null;#Ke=!1;#Qe=!!globalThis.FontInspector?.enabled;#Je=null;#Ze=null;#ti=0;#ei=0;#ii=null;#si=null;#ni=0;#ri=0;#ai=Object.create(null);#oi=[];#hi=null;#li=[];#di=new WeakMap;#ci=null;static#ui=new Map;static#pi=new Map;static#gi=null;static#fi=new Set;constructor({textContentSource:t,container:e,viewport:i}){if(t instanceof ReadableStream)this.#hi=t;else{if("object"!=typeof t)throw new Error('No "textContentSource" parameter specified.');this.#hi=new ReadableStream({start(e){e.enqueue(t),e.close()}})}this.#mt=this.#si=e,this.#ri=i.scale*(globalThis.devicePixelRatio||1),this.#ni=i.rotation,this.#Ze={prevFontSize:null,prevFontFamily:null,div:null,properties:null,ctx:null};const{pageWidth:s,pageHeight:n,pageX:r,pageY:a}=i.rawDims;this.#ci=[1,0,0,-1,-r,a+n],this.#ei=s,this.#ti=n,es.#mi(),ce(e,i),this.#Ye.promise.catch((()=>{})).then((()=>{es.#fi.delete(this),this.#Ze=null,this.#ai=null}))}render(){const t=()=>{this.#ii.read().then((({value:e,done:i})=>{i?this.#Ye.resolve():(this.#Je??=e.lang,Object.assign(this.#ai,e.styles),this.#bi(e.items),t())}),this.#Ye.reject)};return this.#ii=this.#hi.getReader(),es.#fi.add(this),t(),this.#Ye.promise}update({viewport:t,onBefore:e=null}){const i=t.scale*(globalThis.devicePixelRatio||1),s=t.rotation;if(s!==this.#ni&&(e?.(),this.#ni=s,ce(this.#si,{rotation:s})),i!==this.#ri){e?.(),this.#ri=i;const t={prevFontSize:null,prevFontFamily:null,div:null,properties:null,ctx:es.#vi(this.#Je)};for(const e of this.#li)t.properties=this.#di.get(e),t.div=e,this.#yi(t)}}cancel(){const t=new vt("TextLayer task cancelled.");this.#ii?.cancel(t).catch((()=>{})),this.#ii=null,this.#Ye.reject(t)}get textDivs(){return this.#li}get textContentItemsStr(){return this.#oi}#bi(t){if(this.#Ke)return;this.#Ze.ctx??=es.#vi(this.#Je);const e=this.#li,i=this.#oi;for(const s of t){if(e.length>1e5)return at("Ignoring additional textDivs for performance reasons."),void(this.#Ke=!0);if(void 0!==s.str)i.push(s.str),this.#wi(s);else if("beginMarkedContentProps"===s.type||"beginMarkedContent"===s.type){const t=this.#mt;this.#mt=document.createElement("span"),this.#mt.classList.add("markedContent"),null!==s.id&&this.#mt.setAttribute("id",`${s.id}`),t.append(this.#mt)}else"endMarkedContent"===s.type&&(this.#mt=this.#mt.parentNode)}}#wi(t){const e=document.createElement("span"),i={angle:0,canvasWidth:0,hasText:""!==t.str,hasEOL:t.hasEOL,fontSize:0};this.#li.push(e);const s=St.transform(this.#ci,t.transform);let n=Math.atan2(s[1],s[0]);const r=this.#ai[t.fontName];r.vertical&&(n+=Math.PI/2);const a=this.#Qe&&r.fontSubstitution||r.fontFamily,o=Math.hypot(s[2],s[3]),h=o*es.#Ai(a,this.#Je);let l,d;0===n?(l=s[4],d=s[5]-h):(l=s[4]+h*Math.sin(n),d=s[5]-h*Math.cos(n));const c="calc(var(--scale-factor)*",u=e.style;this.#mt===this.#si?(u.left=`${(100*l/this.#ei).toFixed(2)}%`,u.top=`${(100*d/this.#ti).toFixed(2)}%`):(u.left=`${c}${l.toFixed(2)}px)`,u.top=`${c}${d.toFixed(2)}px)`),u.fontSize=`${c}${(es.#gi*o).toFixed(2)}px)`,u.fontFamily=a,i.fontSize=o,e.setAttribute("role","presentation"),e.textContent=t.str,e.dir=t.dir,this.#Qe&&(e.dataset.fontName=r.fontSubstitutionLoadedName||t.fontName),0!==n&&(i.angle=n*(180/Math.PI));let p=!1;if(t.str.length>1)p=!0;else if(" "!==t.str&&t.transform[0]!==t.transform[3]){const e=Math.abs(t.transform[0]),i=Math.abs(t.transform[3]);e!==i&&Math.max(e,i)/Math.min(e,i)>1.5&&(p=!0)}if(p&&(i.canvasWidth=r.vertical?t.height:t.width),this.#di.set(e,i),this.#Ze.div=e,this.#Ze.properties=i,this.#yi(this.#Ze),i.hasText&&this.#mt.append(e),i.hasEOL){const t=document.createElement("br");t.setAttribute("role","presentation"),this.#mt.append(t)}}#yi(t){const{div:e,properties:i,ctx:s,prevFontSize:n,prevFontFamily:r}=t,{style:a}=e;let o="";if(es.#gi>1&&(o=`scale(${1/es.#gi})`),0!==i.canvasWidth&&i.hasText){const{fontFamily:h}=a,{canvasWidth:l,fontSize:d}=i;n===d&&r===h||(s.font=`${d*this.#ri}px ${h}`,t.prevFontSize=d,t.prevFontFamily=h);const{width:c}=s.measureText(e.textContent);c>0&&(o=`scaleX(${l*this.#ri/c}) ${o}`)}0!==i.angle&&(o=`rotate(${i.angle}deg) ${o}`),o.length>0&&(a.transform=o)}static cleanup(){if(!(this.#fi.size>0)){this.#ui.clear();for(const{canvas:t}of this.#pi.values())t.remove();this.#pi.clear()}}static#vi(t=null){let e=this.#pi.get(t||="");if(!e){const i=document.createElement("canvas");i.className="hiddenCanvasElement",i.lang=t,document.body.append(i),e=i.getContext("2d",{alpha:!1,willReadFrequently:!0}),this.#pi.set(t,e)}return e}static#mi(){if(null!==this.#gi)return;const t=document.createElement("div");t.style.opacity=0,t.style.lineHeight=1,t.style.fontSize="1px",t.textContent="X",document.body.append(t),this.#gi=t.getBoundingClientRect().height,t.remove()}static#Ai(t,e){const i=this.#ui.get(t);if(i)return i;const s=this.#vi(e),n=s.font;s.canvas.width=s.canvas.height=ts,s.font=`30px ${t}`;const r=s.measureText("");let a=r.fontBoundingBoxAscent,o=Math.abs(r.fontBoundingBoxDescent);if(a){const e=a/(a+o);return this.#ui.set(t,e),s.canvas.width=s.canvas.height=0,s.font=n,e}s.strokeStyle="red",s.clearRect(0,0,ts,ts),s.strokeText("g",0,0);let h=s.getImageData(0,0,ts,ts).data;o=0;for(let t=h.length-1-3;t>=0;t-=4)if(h[t]>0){o=Math.ceil(t/4/ts);break}s.clearRect(0,0,ts,ts),s.strokeText("A",0,ts),h=s.getImageData(0,0,ts,ts).data,a=0;for(let t=0,e=h.length;t<e;t+=4)if(h[t]>0){a=ts-Math.floor(t/4/ts);break}s.canvas.width=s.canvas.height=0,s.font=n;const l=a?a/(a+o):.8;return this.#ui.set(t,l),l}}function is(){ne("`renderTextLayer`, please use `TextLayer` instead.");const{textContentSource:t,container:e,viewport:i,...s}=arguments[0],n=Object.keys(s);n.length>0&&at("Ignoring `renderTextLayer` parameters: "+n.join(", "));const r=new es({textContentSource:t,container:e,viewport:i}),{textDivs:a,textContentItemsStr:o}=r,h=r.render();return{promise:h,textDivs:a,textContentItemsStr:o}}function ss(){ne("`updateTextLayer`, please use `TextLayer` instead.")}class ns{static textContent(t){const e=[],i={items:e,styles:Object.create(null)};return function t(i){if(!i)return;let s=null;const n=i.name;if("#text"===n)s=i.value;else{if(!ns.shouldBuildText(n))return;i?.attributes?.textContent?s=i.attributes.textContent:i.value&&(s=i.value)}if(null!==s&&e.push({str:s}),i.children)for(const e of i.children)t(e)}(t),i}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}const rs=o?class extends Ht{_createCanvas(t,e){return Fe.get("canvas").createCanvas(t,e)}}:class extends Ht{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}={}){super({enableHWA:e}),this._document=t}_createCanvas(t,e){const i=this._document.createElement("canvas");return i.width=t,i.height=e,i}},as=o?class extends Bt{_fetchData(t,e){return Ne(t).then((t=>({cMapData:t,compressionType:e})))}}:Vt,os=o?class extends zt{}:class extends zt{#xi;#_i;#Si;#Ei;#Ci;#b=0;constructor({docId:t,ownerDocument:e=globalThis.document}={}){super(),this.#Si=t,this.#Ei=e}get#y(){return this.#xi||=new Map}get#Mi(){return this.#Ci||=new Map}get#Ti(){if(!this.#_i){const t=this.#Ei.createElement("div"),{style:e}=t;e.visibility="hidden",e.contain="strict",e.width=e.height=0,e.position="absolute",e.top=e.left=0,e.zIndex=-1;const i=this.#Ei.createElementNS(Ut,"svg");i.setAttribute("width",0),i.setAttribute("height",0),this.#_i=this.#Ei.createElementNS(Ut,"defs"),t.append(i),i.append(this.#_i),this.#Ei.body.append(t)}return this.#_i}#ki(t){if(1===t.length){const e=t[0],i=new Array(256);for(let t=0;t<256;t++)i[t]=e[t]/255;const s=i.join(",");return[s,s,s]}const[e,i,s]=t,n=new Array(256),r=new Array(256),a=new Array(256);for(let t=0;t<256;t++)n[t]=e[t]/255,r[t]=i[t]/255,a[t]=s[t]/255;return[n.join(","),r.join(","),a.join(",")]}addFilter(t){if(!t)return"none";let e=this.#y.get(t);if(e)return e;const[i,s,n]=this.#ki(t),r=1===t.length?i:`${i}${s}${n}`;if(e=this.#y.get(r),e)return this.#y.set(t,e),e;const a=`g_${this.#Si}_transfer_map_${this.#b++}`,o=`url(#${a})`;this.#y.set(t,o),this.#y.set(r,o);const h=this.#Pi(a);return this.#Ri(i,s,n,h),o}addHCMFilter(t,e){const i=`${t}-${e}`,s="base";let n=this.#Mi.get(s);if(n?.key===i)return n.url;if(n?(n.filter?.remove(),n.key=i,n.url="none",n.filter=null):(n={key:i,url:"none",filter:null},this.#Mi.set(s,n)),!t||!e)return n.url;const r=this.#Di(t);t=St.makeHexColor(...r);const a=this.#Di(e);if(e=St.makeHexColor(...a),this.#Ti.style.color="","#000000"===t&&"#ffffff"===e||t===e)return n.url;const o=new Array(256);for(let t=0;t<=255;t++){const e=t/255;o[t]=e<=.03928?e/12.92:((e+.055)/1.055)**2.4}const h=o.join(","),l=`g_${this.#Si}_hcm_filter`,d=n.filter=this.#Pi(l);this.#Ri(h,h,h,d),this.#Li(d);const c=(t,e)=>{const i=r[t]/255,s=a[t]/255,n=new Array(e+1);for(let t=0;t<=e;t++)n[t]=i+t/e*(s-i);return n.join(",")};return this.#Ri(c(0,5),c(1,5),c(2,5),d),n.url=`url(#${l})`,n.url}addAlphaFilter(t){let e=this.#y.get(t);if(e)return e;const[i]=this.#ki([t]),s=`alpha_${i}`;if(e=this.#y.get(s),e)return this.#y.set(t,e),e;const n=`g_${this.#Si}_alpha_map_${this.#b++}`,r=`url(#${n})`;this.#y.set(t,r),this.#y.set(s,r);const a=this.#Pi(n);return this.#Ii(i,a),r}addLuminosityFilter(t){let e,i,s=this.#y.get(t||"luminosity");if(s)return s;if(t?([e]=this.#ki([t]),i=`luminosity_${e}`):i="luminosity",s=this.#y.get(i),s)return this.#y.set(t,s),s;const n=`g_${this.#Si}_luminosity_map_${this.#b++}`,r=`url(#${n})`;this.#y.set(t,r),this.#y.set(i,r);const a=this.#Pi(n);return this.#Fi(a),t&&this.#Ii(e,a),r}addHighlightHCMFilter(t,e,i,s,n){const r=`${e}-${i}-${s}-${n}`;let a=this.#Mi.get(t);if(a?.key===r)return a.url;if(a?(a.filter?.remove(),a.key=r,a.url="none",a.filter=null):(a={key:r,url:"none",filter:null},this.#Mi.set(t,a)),!e||!i)return a.url;const[o,h]=[e,i].map(this.#Di.bind(this));let l=Math.round(.2126*o[0]+.7152*o[1]+.0722*o[2]),d=Math.round(.2126*h[0]+.7152*h[1]+.0722*h[2]),[c,u]=[s,n].map(this.#Di.bind(this));d<l&&([l,d,c,u]=[d,l,u,c]),this.#Ti.style.color="";const p=(t,e,i)=>{const s=new Array(256),n=(d-l)/i,r=t/255,a=(e-t)/(255*i);let o=0;for(let t=0;t<=i;t++){const e=Math.round(l+t*n),i=r+t*a;for(let t=o;t<=e;t++)s[t]=i;o=e+1}for(let t=o;t<256;t++)s[t]=s[o-1];return s.join(",")},g=`g_${this.#Si}_hcm_${t}_filter`,f=a.filter=this.#Pi(g);return this.#Li(f),this.#Ri(p(c[0],u[0],5),p(c[1],u[1],5),p(c[2],u[2],5),f),a.url=`url(#${g})`,a.url}destroy(t=!1){t&&0!==this.#Mi.size||(this.#_i&&(this.#_i.parentNode.parentNode.remove(),this.#_i=null),this.#xi&&(this.#xi.clear(),this.#xi=null),this.#b=0)}#Fi(t){const e=this.#Ei.createElementNS(Ut,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0"),t.append(e)}#Li(t){const e=this.#Ei.createElementNS(Ut,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),t.append(e)}#Pi(t){const e=this.#Ei.createElementNS(Ut,"filter");return e.setAttribute("color-interpolation-filters","sRGB"),e.setAttribute("id",t),this.#Ti.append(e),e}#Ni(t,e,i){const s=this.#Ei.createElementNS(Ut,e);s.setAttribute("type","discrete"),s.setAttribute("tableValues",i),t.append(s)}#Ri(t,e,i,s){const n=this.#Ei.createElementNS(Ut,"feComponentTransfer");s.append(n),this.#Ni(n,"feFuncR",t),this.#Ni(n,"feFuncG",e),this.#Ni(n,"feFuncB",i)}#Ii(t,e){const i=this.#Ei.createElementNS(Ut,"feComponentTransfer");e.append(i),this.#Ni(i,"feFuncA",t)}#Di(t){return this.#Ti.style.color=t,he(getComputedStyle(this.#Ti).getPropertyValue("color"))}},hs=o?class extends jt{_fetchData(t){return Ne(t)}}:qt;function ls(t={}){"string"==typeof t||t instanceof URL?t={url:t}:(t instanceof ArrayBuffer||ArrayBuffer.isView(t))&&(t={data:t});const e=new cs,{docId:i}=e,s=t.url?function(t){if(t instanceof URL)return t.href;try{return new URL(t,window.location).href}catch{if(o&&"string"==typeof t)return t}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}(t.url):null,r=t.data?function(t){if(o&&void 0!==n&&t instanceof n)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(t instanceof Uint8Array&&t.byteLength===t.buffer.byteLength)return t;if("string"==typeof t)return wt(t);if(t instanceof ArrayBuffer||ArrayBuffer.isView(t)||"object"==typeof t&&!isNaN(t?.length))return new Uint8Array(t);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}(t.data):null,a=t.httpHeaders||null,h=!0===t.withCredentials,l=t.password??null,d=t.range instanceof us?t.range:null,c=Number.isInteger(t.rangeChunkSize)&&t.rangeChunkSize>0?t.rangeChunkSize:65536;let u=t.worker instanceof bs?t.worker:null;const p=t.verbosity,g="string"!=typeof t.docBaseUrl||Qt(t.docBaseUrl)?null:t.docBaseUrl,f="string"==typeof t.cMapUrl?t.cMapUrl:null,m=!1!==t.cMapPacked,b=t.CMapReaderFactory||as,v="string"==typeof t.standardFontDataUrl?t.standardFontDataUrl:null,y=t.StandardFontDataFactory||hs,w=!0!==t.stopAtErrors,A=Number.isInteger(t.maxImageSize)&&t.maxImageSize>-1?t.maxImageSize:-1,x=!1!==t.isEvalSupported,_="boolean"==typeof t.isOffscreenCanvasSupported?t.isOffscreenCanvasSupported:!o,S=Number.isInteger(t.canvasMaxAreaInBytes)?t.canvasMaxAreaInBytes:-1,E="boolean"==typeof t.disableFontFace?t.disableFontFace:o,C=!0===t.fontExtraProperties,M=!0===t.enableXfa,T=t.ownerDocument||globalThis.document,k=!0===t.disableRange,P=!0===t.disableStream,R=!0===t.disableAutoFetch,D=!0===t.pdfBug,L=!0===t.enableHWA,I=d?d.length:t.length??NaN,F="boolean"==typeof t.useSystemFonts?t.useSystemFonts:!o&&!E,N="boolean"==typeof t.useWorkerFetch?t.useWorkerFetch:b===Vt&&y===qt&&f&&v&&ie(f,document.baseURI)&&ie(v,document.baseURI),O=t.canvasFactory||new rs({ownerDocument:T,enableHWA:L}),z=t.filterFactory||new os({docId:i,ownerDocument:T});st(p);const H={canvasFactory:O,filterFactory:z};if(N||(H.cMapReaderFactory=new b({baseUrl:f,isCompressed:m}),H.standardFontDataFactory=new y({baseUrl:v})),!u){const t={verbosity:p,port:ci.workerPort};u=t.port?bs.fromPort(t):new bs(t),e._worker=u}const B={docId:i,apiVersion:"4.4.168",data:r,password:l,disableAutoFetch:R,rangeChunkSize:c,length:I,docBaseUrl:g,enableXfa:M,evaluatorOptions:{maxImageSize:A,disableFontFace:E,ignoreErrors:w,isEvalSupported:x,isOffscreenCanvasSupported:_,canvasMaxAreaInBytes:S,fontExtraProperties:C,useSystemFonts:F,cMapUrl:N?f:null,standardFontDataUrl:N?v:null}},j={disableFontFace:E,fontExtraProperties:C,ownerDocument:T,pdfBug:D,styleElement:null,loadingParams:{disableAutoFetch:R,enableXfa:M}};return u.promise.then((function(){if(e.destroyed)throw new Error("Loading aborted");if(u.destroyed)throw new Error("Worker was destroyed");const t=u.messageHandler.sendWithPromise("GetDocRequest",B,r?[r.buffer]:null);let n;if(d)n=new Ti(d,{disableRange:k,disableStream:P});else if(!r){if(!s)throw new Error("getDocument - no `url` parameter provided.");n=(t=>{if(o){return function(){return"undefined"!=typeof fetch&&"undefined"!=typeof Response&&"body"in Response.prototype}()&&ie(t.url)?new zi(t):new Vi(t)}return ie(t.url)?new zi(t):new Wi(t)})({url:s,length:I,httpHeaders:a,withCredentials:h,rangeChunkSize:c,disableRange:k,disableStream:P})}return t.then((t=>{if(e.destroyed)throw new Error("Loading aborted");if(u.destroyed)throw new Error("Worker was destroyed");const s=new _i(i,t,u.port),r=new vs(s,e,n,j,H);e._transport=r,s.send("Ready",null)}))})).catch(e._capability.reject),e}function ds(t){return"object"==typeof t&&Number.isInteger(t?.num)&&t.num>=0&&Number.isInteger(t?.gen)&&t.gen>=0}class cs{static#Si=0;constructor(){this._capability=Promise.withResolvers(),this._transport=null,this._worker=null,this.docId="d"+cs.#Si++,this.destroyed=!1,this.onPassword=null,this.onProgress=null}get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0),await(this._transport?.destroy())}catch(t){throw this._worker?.port&&delete this._worker._pendingDestroy,t}this._transport=null,this._worker&&(this._worker.destroy(),this._worker=null)}}class us{constructor(t,e,i=!1,s=null){this.length=t,this.initialData=e,this.progressiveDone=i,this.contentDispositionFilename=s,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=Promise.withResolvers()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const i of this._rangeListeners)i(t,e)}onDataProgress(t,e){this._readyCapability.promise.then((()=>{for(const i of this._progressListeners)i(t,e)}))}onDataProgressiveRead(t){this._readyCapability.promise.then((()=>{for(const e of this._progressiveReadListeners)e(t)}))}onDataProgressiveDone(){this._readyCapability.promise.then((()=>{for(const t of this._progressiveDoneListeners)t()}))}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){ot("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class ps{constructor(t,e){this._pdfInfo=t,this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return dt(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}class gs{#Oi=null;#zi=!1;constructor(t,e,i,s=!1){this._pageIndex=t,this._pageInfo=e,this._transport=i,this._stats=s?new ee:null,this._pdfBug=s,this.commonObjs=i.commonObjs,this.objs=new ws,this._maybeCleanupAfterRender=!1,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:i=0,offsetY:s=0,dontFlip:n=!1}={}){return new Yt({viewBox:this.view,scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:n})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return dt(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,viewport:e,intent:i="display",annotationMode:s=v.ENABLE,transform:n=null,background:r=null,optionalContentConfigPromise:a=null,annotationCanvasMap:o=null,pageColors:h=null,printAnnotationStorage:l=null}){this._stats?.time("Overall");const d=this._transport.getRenderingIntent(i,s,l),{renderingIntent:c,cacheKey:u}=d;this.#zi=!1,this.#Hi(),a||=this._transport.getOptionalContentConfig(c);let g=this._intentStates.get(u);g||(g=Object.create(null),this._intentStates.set(u,g)),g.streamReaderCancelTimeout&&(clearTimeout(g.streamReaderCancelTimeout),g.streamReaderCancelTimeout=null);const f=!!(c&p);g.displayReadyCapability||(g.displayReadyCapability=Promise.withResolvers(),g.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(d));const m=t=>{g.renderTasks.delete(b),(this._maybeCleanupAfterRender||f)&&(this.#zi=!0),this.#Bi(!f),t?(b.capability.reject(t),this._abortOperatorList({intentState:g,reason:t instanceof Error?t:new Error(t)})):b.capability.resolve(),this._stats&&(this._stats.timeEnd("Rendering"),this._stats.timeEnd("Overall"),globalThis.Stats?.enabled&&globalThis.Stats.add(this.pageNumber,this._stats))},b=new xs({callback:m,params:{canvasContext:t,viewport:e,transform:n,background:r},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:o,operatorList:g.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!f,pdfBug:this._pdfBug,pageColors:h});(g.renderTasks||=new Set).add(b);const y=b.task;return Promise.all([g.displayReadyCapability.promise,a]).then((([t,e])=>{if(this.destroyed)m();else{if(this._stats?.time("Rendering"),!(e.renderingIntent&c))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");b.initializeGraphics({transparency:t,optionalContentConfig:e}),b.operatorListChanged()}})).catch(m),y}getOperatorList({intent:t="display",annotationMode:e=v.ENABLE,printAnnotationStorage:i=null}={}){const s=this._transport.getRenderingIntent(t,e,i,!0);let n,r=this._intentStates.get(s.cacheKey);return r||(r=Object.create(null),this._intentStates.set(s.cacheKey,r)),r.opListReadCapability||(n=Object.create(null),n.operatorListChanged=function(){r.operatorList.lastChunk&&(r.opListReadCapability.resolve(r.operatorList),r.renderTasks.delete(n))},r.opListReadCapability=Promise.withResolvers(),(r.renderTasks||=new Set).add(n),r.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(s)),r.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:!0===t,disableNormalization:!0===e},{highWaterMark:100,size(t){return t.items.length}})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then((t=>ns.textContent(t)));const e=this.streamTextContent(t);return new Promise((function(t,i){const s=e.getReader(),n={items:[],styles:Object.create(null),lang:null};!function e(){s.read().then((function({value:i,done:s}){s?t(n):(n.lang??=i.lang,Object.assign(n.styles,i.styles),n.items.push(...i.items),e())}),i)}()}))}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values())if(this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(const i of e.renderTasks)t.push(i.completed),i.cancel();return this.objs.clear(),this.#zi=!1,this.#Hi(),Promise.all(t)}cleanup(t=!1){this.#zi=!0;const e=this.#Bi(!1);return t&&e&&(this._stats&&=new ee),e}#Bi(t=!1){if(this.#Hi(),!this.#zi||this.destroyed)return!1;if(t)return this.#Oi=setTimeout((()=>{this.#Oi=null,this.#Bi(!1)}),5e3),!1;for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),this.#zi=!1,!0}#Hi(){this.#Oi&&(clearTimeout(this.#Oi),this.#Oi=null)}_startRenderPage(t,e){const i=this._intentStates.get(e);i&&(this._stats?.timeEnd("Page Request"),i.displayReadyCapability?.resolve(t))}_renderPageChunk(t,e){for(let i=0,s=t.length;i<s;i++)e.operatorList.fnArray.push(t.fnArray[i]),e.operatorList.argsArray.push(t.argsArray[i]);e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots;for(const t of e.renderTasks)t.operatorListChanged();t.lastChunk&&this.#Bi(!0)}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:i}){const{map:s,transfer:n}=i,r=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:s},n).getReader(),a=this._intentStates.get(e);a.streamReader=r;const o=()=>{r.read().then((({value:t,done:e})=>{e?a.streamReader=null:this._transport.destroyed||(this._renderPageChunk(t,a),o())}),(t=>{if(a.streamReader=null,!this._transport.destroyed){if(a.operatorList){a.operatorList.lastChunk=!0;for(const t of a.renderTasks)t.operatorListChanged();this.#Bi(!0)}if(a.displayReadyCapability)a.displayReadyCapability.reject(t);else{if(!a.opListReadCapability)throw t;a.opListReadCapability.reject(t)}}}))};o()}_abortOperatorList({intentState:t,reason:e,force:i=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout&&(clearTimeout(t.streamReaderCancelTimeout),t.streamReaderCancelTimeout=null),!i){if(t.renderTasks.size>0)return;if(e instanceof Kt){let i=100;return e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay),void(t.streamReaderCancelTimeout=setTimeout((()=>{t.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:t,reason:e,force:!0})}),i))}}if(t.streamReader.cancel(new vt(e.message)).catch((()=>{})),t.streamReader=null,!this._transport.destroyed){for(const[e,i]of this._intentStates)if(i===t){this._intentStates.delete(e);break}this.cleanup()}}}get stats(){return this._stats}}class fs{#ji=new Set;#Wi=Promise.resolve();postMessage(t,e){const i={data:structuredClone(t,e?{transfer:e}:null)};this.#Wi.then((()=>{for(const t of this.#ji)t.call(this,i)}))}addEventListener(t,e){this.#ji.add(e)}removeEventListener(t,e){this.#ji.delete(e)}terminate(){this.#ji.clear()}}const ms={isWorkerDisabled:!1,fakeWorkerId:0};o&&(ms.isWorkerDisabled=!0,ci.workerSrc||="./pdf.worker.mjs"),ms.isSameOrigin=function(t,e){let i;try{if(i=new URL(t),!i.origin||"null"===i.origin)return!1}catch{return!1}const s=new URL(e,i);return i.origin===s.origin},ms.createCDNWrapper=function(t){const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))};class bs{static#Ui;constructor({name:t=null,port:e=null,verbosity:i=nt()}={}){if(this.name=t,this.destroyed=!1,this.verbosity=i,this._readyCapability=Promise.withResolvers(),this._port=null,this._webWorker=null,this._messageHandler=null,e){if(bs.#Ui?.has(e))throw new Error("Cannot use more than one PDFWorker per port.");return(bs.#Ui||=new WeakMap).set(e,this),void this._initializeFromPort(e)}this._initialize()}get promise(){return o?Promise.all([Fe.promise,this._readyCapability.promise]):this._readyCapability.promise}#$i(){this._readyCapability.resolve(),this._messageHandler.send("configure",{verbosity:this.verbosity})}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t,this._messageHandler=new _i("main","worker",t),this._messageHandler.on("ready",(function(){})),this.#$i()}_initialize(){if(ms.isWorkerDisabled||bs.#Gi)return void this._setupFakeWorker();let{workerSrc:t}=bs;try{ms.isSameOrigin(window.location.href,t)||(t=ms.createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),i=new _i("main","worker",e),s=()=>{n.abort(),i.destroy(),e.terminate(),this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},n=new AbortController;e.addEventListener("error",(()=>{this._webWorker||s()}),{signal:n.signal}),i.on("test",(t=>{n.abort(),!this.destroyed&&t?(this._messageHandler=i,this._port=e,this._webWorker=e,this.#$i()):s()})),i.on("ready",(t=>{if(n.abort(),this.destroyed)s();else try{r()}catch{this._setupFakeWorker()}}));const r=()=>{const t=new Uint8Array;i.send("test",t,[t.buffer])};return void r()}catch{rt("The worker has been disabled.")}this._setupFakeWorker()}_setupFakeWorker(){ms.isWorkerDisabled||(at("Setting up fake worker."),ms.isWorkerDisabled=!0),bs._setupFakeWorkerGlobal.then((t=>{if(this.destroyed)return void this._readyCapability.reject(new Error("Worker was destroyed"));const e=new fs;this._port=e;const i="fake"+ms.fakeWorkerId++,s=new _i(i+"_worker",i,e);t.setup(s,e),this._messageHandler=new _i(i,i+"_worker",e),this.#$i()})).catch((t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))}))}destroy(){this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),bs.#Ui?.delete(this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}static fromPort(t){if(!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");const e=this.#Ui?.get(t.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new bs(t)}static get workerSrc(){if(ci.workerSrc)return ci.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get#Gi(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}}static get _setupFakeWorkerGlobal(){return dt(this,"_setupFakeWorkerGlobal",(async()=>{if(this.#Gi)return this.#Gi;return(await import(this.workerSrc)).WorkerMessageHandler})())}}class vs{#Vi=new Map;#qi=new Map;#Xi=new Map;#Yi=new Map;#Ki=null;constructor(t,e,i,s,n){this.messageHandler=t,this.loadingTask=e,this.commonObjs=new ws,this.fontLoader=new Re({ownerDocument:s.ownerDocument,styleElement:s.styleElement}),this.loadingParams=s.loadingParams,this._params=s,this.canvasFactory=n.canvasFactory,this.filterFactory=n.filterFactory,this.cMapReaderFactory=n.cMapReaderFactory,this.standardFontDataFactory=n.standardFontDataFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=i,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=Promise.withResolvers(),this.setupMessageHandler()}#Qi(t,e=null){const i=this.#Vi.get(t);if(i)return i;const s=this.messageHandler.sendWithPromise(t,e);return this.#Vi.set(t,s),s}get annotationStorage(){return dt(this,"annotationStorage",new ke)}getRenderingIntent(t,e=v.ENABLE,i=null,s=!1){let n=u,r=Te;switch(t){case"any":n=c;break;case"display":break;case"print":n=p;break;default:at(`getRenderingIntent - invalid intent: ${t}`)}switch(e){case v.DISABLE:n+=m;break;case v.ENABLE:break;case v.ENABLE_FORMS:n+=g;break;case v.ENABLE_STORAGE:n+=f;r=(n&p&&i instanceof Pe?i:this.annotationStorage).serializable;break;default:at(`getRenderingIntent - invalid annotationMode: ${e}`)}return s&&(n+=b),{renderingIntent:n,cacheKey:`${n}_${r.hash}`,annotationStorageSerializable:r}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=Promise.withResolvers(),this.#Ki?.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const e of this.#qi.values())t.push(e._destroy());this.#qi.clear(),this.#Xi.clear(),this.#Yi.clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);return t.push(e),Promise.all(t).then((()=>{this.commonObjs.clear(),this.fontLoader.clear(),this.#Vi.clear(),this.filterFactory.destroy(),es.cleanup(),this._networkStream?.cancelAllRequests(new vt("Worker was terminated.")),this.messageHandler&&(this.messageHandler.destroy(),this.messageHandler=null),this.destroyCapability.resolve()}),this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",((t,e)=>{ht(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}},e.onPull=()=>{this._fullReader.read().then((function({value:t,done:i}){i?e.close():(ht(t instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(t),1,[t]))})).catch((t=>{e.error(t)}))},e.onCancel=t=>{this._fullReader.cancel(t),e.ready.catch((t=>{if(!this.destroyed)throw t}))}})),t.on("ReaderHeadersReady",(t=>{const i=Promise.withResolvers(),s=this._fullReader;return s.headersReady.then((()=>{s.isStreamingSupported&&s.isRangeSupported||(this._lastProgress&&e.onProgress?.(this._lastProgress),s.onProgress=t=>{e.onProgress?.({loaded:t.loaded,total:t.total})}),i.resolve({isStreamingSupported:s.isStreamingSupported,isRangeSupported:s.isRangeSupported,contentLength:s.contentLength})}),i.reject),i.promise})),t.on("GetRangeReader",((t,e)=>{ht(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const i=this._networkStream.getRangeReader(t.begin,t.end);i?(e.onPull=()=>{i.read().then((function({value:t,done:i}){i?e.close():(ht(t instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(t),1,[t]))})).catch((t=>{e.error(t)}))},e.onCancel=t=>{i.cancel(t),e.ready.catch((t=>{if(!this.destroyed)throw t}))}):e.close()})),t.on("GetDoc",(({pdfInfo:t})=>{this._numPages=t.numPages,this._htmlForXfa=t.htmlForXfa,delete t.htmlForXfa,e._capability.resolve(new ps(t,this))})),t.on("DocException",(function(t){let i;switch(t.name){case"PasswordException":i=new ut(t.message,t.code);break;case"InvalidPDFException":i=new gt(t.message);break;case"MissingPDFException":i=new ft(t.message);break;case"UnexpectedResponseException":i=new mt(t.message,t.status);break;case"UnknownErrorException":i=new pt(t.message,t.details);break;default:ot("DocException - expected a valid Error.")}e._capability.reject(i)})),t.on("PasswordRequest",(t=>{if(this.#Ki=Promise.withResolvers(),e.onPassword){const i=t=>{t instanceof Error?this.#Ki.reject(t):this.#Ki.resolve({password:t})};try{e.onPassword(i,t.code)}catch(t){this.#Ki.reject(t)}}else this.#Ki.reject(new ut(t.message,t.code));return this.#Ki.promise})),t.on("DataLoaded",(t=>{e.onProgress?.({loaded:t.length,total:t.length}),this.downloadInfoCapability.resolve(t)})),t.on("StartRenderPage",(t=>{if(this.destroyed)return;this.#qi.get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)})),t.on("commonobj",(([e,i,s])=>{if(this.destroyed)return null;if(this.commonObjs.has(e))return null;switch(i){case"Font":const{disableFontFace:n,fontExtraProperties:r,pdfBug:a}=this._params;if("error"in s){const t=s.error;at(`Error during font loading: ${t}`),this.commonObjs.resolve(e,t);break}const o=a&&globalThis.FontInspector?.enabled?(t,e)=>globalThis.FontInspector.fontAdded(t,e):null,h=new De(s,{disableFontFace:n,inspectFont:o});this.fontLoader.bind(h).catch((()=>t.sendWithPromise("FontFallback",{id:e}))).finally((()=>{!r&&h.data&&(h.data=null),this.commonObjs.resolve(e,h)}));break;case"CopyLocalImage":const{imageRef:l}=s;ht(l,"The imageRef must be defined.");for(const t of this.#qi.values())for(const[,i]of t.objs)if(i?.ref===l)return i.dataLen?(this.commonObjs.resolve(e,structuredClone(i)),i.dataLen):null;break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(e,s);break;default:throw new Error(`Got unknown common object type ${i}`)}return null})),t.on("obj",(([t,e,i,s])=>{if(this.destroyed)return;const n=this.#qi.get(e);if(!n.objs.has(t))if(0!==n._intentStates.size)switch(i){case"Image":n.objs.resolve(t,s),s?.dataLen>1e7&&(n._maybeCleanupAfterRender=!0);break;case"Pattern":n.objs.resolve(t,s);break;default:throw new Error(`Got unknown object type ${i}`)}else s?.bitmap?.close()})),t.on("DocProgress",(t=>{this.destroyed||e.onProgress?.({loaded:t.loaded,total:t.total})})),t.on("FetchBuiltInCMap",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.cMapReaderFactory?this.cMapReaderFactory.fetch(t):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter.")))),t.on("FetchStandardFontData",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.standardFontDataFactory?this.standardFontDataFactory.fetch(t):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter."))))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&at("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally((()=>{this.annotationStorage.resetModified()}))}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,i=this.#Xi.get(e);if(i)return i;const s=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then((i=>{if(this.destroyed)throw new Error("Transport destroyed");i.refStr&&this.#Yi.set(i.refStr,t);const s=new gs(e,i,this,this._params.pdfBug);return this.#qi.set(e,s),s}));return this.#Xi.set(e,s),s}getPageIndex(t){return ds(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return this.#Qi("GetFieldObjects")}hasJSActions(){return this.#Qi("HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!=typeof t?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return this.#Qi("GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return this.#Qi("GetOptionalContentConfig").then((e=>new Mi(e,t)))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=this.#Vi.get(t);if(e)return e;const i=this.messageHandler.sendWithPromise(t,null).then((t=>({info:t[0],metadata:t[1]?new Si(t[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null})));return this.#Vi.set(t,i),i}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const t of this.#qi.values()){if(!t.cleanup())throw new Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`)}this.commonObjs.clear(),t||this.fontLoader.clear(),this.#Vi.clear(),this.filterFactory.destroy(!0),es.cleanup()}}cachedPageNumber(t){if(!ds(t))return null;const e=0===t.gen?`${t.num}R`:`${t.num}R${t.gen}`;return this.#Yi.get(e)??null}}const ys=Symbol("INITIAL_DATA");class ws{#Ji=Object.create(null);#Zi(t){return this.#Ji[t]||={...Promise.withResolvers(),data:ys}}get(t,e=null){if(e){const i=this.#Zi(t);return i.promise.then((()=>e(i.data))),null}const i=this.#Ji[t];if(!i||i.data===ys)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return i.data}has(t){const e=this.#Ji[t];return!!e&&e.data!==ys}resolve(t,e=null){const i=this.#Zi(t);i.data=e,i.resolve()}clear(){for(const t in this.#Ji){const{data:e}=this.#Ji[t];e?.bitmap?.close()}this.#Ji=Object.create(null)}*[Symbol.iterator](){for(const t in this.#Ji){const{data:e}=this.#Ji[t];e!==ys&&(yield[t,e])}}}class As{#ts=null;constructor(t){this.#ts=t,this.onContinue=null}get promise(){return this.#ts.capability.promise}cancel(t=0){this.#ts.cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=this.#ts.operatorList;if(!t)return!1;const{annotationCanvasMap:e}=this.#ts;return t.form||t.canvas&&e?.size>0}}class xs{#es=null;static#is=new WeakSet;constructor({callback:t,params:e,objs:i,commonObjs:s,annotationCanvasMap:n,operatorList:r,pageIndex:a,canvasFactory:o,filterFactory:h,useRequestAnimationFrame:l=!1,pdfBug:d=!1,pageColors:c=null}){this.callback=t,this.params=e,this.objs=i,this.commonObjs=s,this.annotationCanvasMap=n,this.operatorListIdx=null,this.operatorList=r,this._pageIndex=a,this.canvasFactory=o,this.filterFactory=h,this._pdfBug=d,this.pageColors=c,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=!0===l&&"undefined"!=typeof window,this.cancelled=!1,this.capability=Promise.withResolvers(),this.task=new As(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch((function(){}))}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(xs.#is.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");xs.#is.add(this._canvas)}this._pdfBug&&globalThis.StepperManager?.enabled&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:i,viewport:s,transform:n,background:r}=this.params;this.gfx=new di(i,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:n,viewport:s,transparency:t,background:r}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback?.()}cancel(t=null,e=0){this.running=!1,this.cancelled=!0,this.gfx?.endDrawing(),this.#es&&(window.cancelAnimationFrame(this.#es),this.#es=null),xs.#is.delete(this._canvas),this.callback(t||new Kt(`Rendering cancelled, page ${this._pageIndex+1}`,e))}operatorListChanged(){this.graphicsReady?(this.stepper?.updateOperatorList(this.operatorList),this.running||this._continue()):this.graphicsReadyCallback||=this._continueBound}_continue(){this.running=!0,this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?this.#es=window.requestAnimationFrame((()=>{this.#es=null,this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),xs.#is.delete(this._canvas),this.callback())))}}const _s="4.4.168",Ss="19fbc8998";function Es(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}function Cs(t){return Math.max(0,Math.min(255,255*t))}class Ms{static CMYK_G([t,e,i,s]){return["G",1-Math.min(1,.3*t+.59*i+.11*e+s)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return[t=Cs(t),t,t]}static G_HTML([t]){const e=Es(t);return`#${e}${e}${e}`}static RGB_G([t,e,i]){return["G",.3*t+.59*e+.11*i]}static RGB_rgb(t){return t.map(Cs)}static RGB_HTML(t){return`#${t.map(Es).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,i,s]){return["RGB",1-Math.min(1,t+s),1-Math.min(1,i+s),1-Math.min(1,e+s)]}static CMYK_rgb([t,e,i,s]){return[Cs(1-Math.min(1,t+s)),Cs(1-Math.min(1,i+s)),Cs(1-Math.min(1,e+s))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,i]){const s=1-t,n=1-e,r=1-i;return["CMYK",s,n,r,Math.min(s,n,r)]}}class Ts{static setupStorage(t,e,i,s,n){const r=s.getValue(e,{value:null});switch(i.name){case"textarea":if(null!==r.value&&(t.textContent=r.value),"print"===n)break;t.addEventListener("input",(t=>{s.setValue(e,{value:t.target.value})}));break;case"input":if("radio"===i.attributes.type||"checkbox"===i.attributes.type){if(r.value===i.attributes.xfaOn?t.setAttribute("checked",!0):r.value===i.attributes.xfaOff&&t.removeAttribute("checked"),"print"===n)break;t.addEventListener("change",(t=>{s.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})}))}else{if(null!==r.value&&t.setAttribute("value",r.value),"print"===n)break;t.addEventListener("input",(t=>{s.setValue(e,{value:t.target.value})}))}break;case"select":if(null!==r.value){t.setAttribute("value",r.value);for(const t of i.children)t.attributes.value===r.value?t.attributes.selected=!0:t.attributes.hasOwnProperty("selected")&&delete t.attributes.selected}t.addEventListener("input",(t=>{const i=t.target.options,n=-1===i.selectedIndex?"":i[i.selectedIndex].value;s.setValue(e,{value:n})}))}}static setAttributes({html:t,element:e,storage:i=null,intent:s,linkService:n}){const{attributes:r}=e,a=t instanceof HTMLAnchorElement;"radio"===r.type&&(r.name=`${r.name}-${s}`);for(const[e,i]of Object.entries(r))if(null!=i)switch(e){case"class":i.length&&t.setAttribute(e,i.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",i);break;case"style":Object.assign(t.style,i);break;case"textContent":t.textContent=i;break;default:(!a||"href"!==e&&"newWindow"!==e)&&t.setAttribute(e,i)}a&&n.addLinkAttributes(t,r.href,r.newWindow),i&&r.dataId&&this.setupStorage(t,r.dataId,e,i)}static render(t){const e=t.annotationStorage,i=t.linkService,s=t.xfaHtml,n=t.intent||"display",r=document.createElement(s.name);s.attributes&&this.setAttributes({html:r,element:s,intent:n,linkService:i});const a="richText"!==n,o=t.div;if(o.append(r),t.viewport){const e=`matrix(${t.viewport.transform.join(",")})`;o.style.transform=e}a&&o.setAttribute("class","xfaLayer xfaFont");const h=[];if(0===s.children.length){if(s.value){const t=document.createTextNode(s.value);r.append(t),a&&ns.shouldBuildText(s.name)&&h.push(t)}return{textDivs:h}}const l=[[s,-1,r]];for(;l.length>0;){const[t,s,r]=l.at(-1);if(s+1===t.children.length){l.pop();continue}const o=t.children[++l.at(-1)[1]];if(null===o)continue;const{name:d}=o;if("#text"===d){const t=document.createTextNode(o.value);h.push(t),r.append(t);continue}const c=o?.attributes?.xmlns?document.createElementNS(o.attributes.xmlns,d):document.createElement(d);if(r.append(c),o.attributes&&this.setAttributes({html:c,element:o,storage:e,intent:n,linkService:i}),o.children?.length>0)l.push([o,-1,c]);else if(o.value){const t=document.createTextNode(o.value);a&&ns.shouldBuildText(d)&&h.push(t),c.append(t)}}for(const t of o.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))t.setAttribute("readOnly",!0);return{textDivs:h}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e,t.div.hidden=!1}}const ks=1e3,Ps=new WeakSet;function Rs(t){return{width:t[2]-t[0],height:t[3]-t[1]}}class Ds{static create(t){switch(t.data.annotationType){case P:return new Is(t);case k:return new Fs(t);case V:switch(t.data.fieldType){case"Tx":return new Os(t);case"Btn":return t.data.radioButton?new Bs(t):t.data.checkBox?new Hs(t):new js(t);case"Ch":return new Ws(t);case"Sig":return new zs(t)}return new Ns(t);case $:return new Us(t);case R:return new Gs(t);case D:return new Vs(t);case L:return new qs(t);case I:return new Xs(t);case N:return new Ys(t);case W:return new Qs(t);case U:return new Js(t);case F:return new Ks(t);case O:return new Zs(t);case z:return new tn(t);case H:return new en(t);case B:return new sn(t);case j:return new nn(t);case G:return new rn(t);default:return new Ls(t)}}}class Ls{#ss=null;#ns=!1;#rs=null;constructor(t,{isRenderable:e=!1,ignoreBorder:i=!1,createQuadrilaterals:s=!1}={}){this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,e&&(this.container=this._createContainer(i)),s&&this._createQuadrilaterals()}static _hasPopupData({titleObj:t,contentsObj:e,richText:i}){return!!(t?.str||e?.str||i?.str)}get hasPopupData(){return Ls._hasPopupData(this.data)}updateEdited(t){if(!this.container)return;this.#ss||={rect:this.data.rect.slice(0)};const{rect:e}=t;e&&this.#as(e),this.#rs?.popup.updateEdited(t)}resetEdited(){this.#ss&&(this.#as(this.#ss.rect),this.#rs?.popup.resetEdited(),this.#ss=null)}#as(t){const{container:{style:e},data:{rect:i,rotation:s},parent:{viewport:{rawDims:{pageWidth:n,pageHeight:r,pageX:a,pageY:o}}}}=this;i?.splice(0,4,...t);const{width:h,height:l}=Rs(t);e.left=100*(t[0]-a)/n+"%",e.top=100*(r-t[3]+o)/r+"%",0===s?(e.width=100*h/n+"%",e.height=100*l/r+"%"):this.setRotation(s)}_createContainer(t){const{data:e,parent:{page:i,viewport:s}}=this,n=document.createElement("section");n.setAttribute("data-annotation-id",e.id),this instanceof Ns||(n.tabIndex=ks);const{style:r}=n;if(r.zIndex=this.parent.zIndex++,e.popupRef&&n.setAttribute("aria-haspopup","dialog"),e.alternativeText&&(n.title=e.alternativeText),e.noRotate&&n.classList.add("norotate"),!e.rect||this instanceof Us){const{rotation:t}=e;return e.hasOwnCanvas||0===t||this.setRotation(t,n),n}const{width:a,height:o}=Rs(e.rect);if(!t&&e.borderStyle.width>0){r.borderWidth=`${e.borderStyle.width}px`;const t=e.borderStyle.horizontalCornerRadius,i=e.borderStyle.verticalCornerRadius;if(t>0||i>0){const e=`calc(${t}px * var(--scale-factor)) / calc(${i}px * var(--scale-factor))`;r.borderRadius=e}else if(this instanceof Bs){const t=`calc(${a}px * var(--scale-factor)) / calc(${o}px * var(--scale-factor))`;r.borderRadius=t}switch(e.borderStyle.style){case q:r.borderStyle="solid";break;case X:r.borderStyle="dashed";break;case Y:at("Unimplemented border style: beveled");break;case K:at("Unimplemented border style: inset");break;case Q:r.borderBottomStyle="solid"}const s=e.borderColor||null;s?(this.#ns=!0,r.borderColor=St.makeHexColor(0|s[0],0|s[1],0|s[2])):r.borderWidth=0}const h=St.normalizeRect([e.rect[0],i.view[3]-e.rect[1]+i.view[1],e.rect[2],i.view[3]-e.rect[3]+i.view[1]]),{pageWidth:l,pageHeight:d,pageX:c,pageY:u}=s.rawDims;r.left=100*(h[0]-c)/l+"%",r.top=100*(h[1]-u)/d+"%";const{rotation:p}=e;return e.hasOwnCanvas||0===p?(r.width=100*a/l+"%",r.height=100*o/d+"%"):this.setRotation(p,n),n}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:i,pageHeight:s}=this.parent.viewport.rawDims,{width:n,height:r}=Rs(this.data.rect);let a,o;t%180==0?(a=100*n/i,o=100*r/s):(a=100*r/i,o=100*n/s),e.style.width=`${a}%`,e.style.height=`${o}%`,e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const t=(t,e,i)=>{const s=i.detail[t],n=s[0],r=s.slice(1);i.target.style[e]=Ms[`${n}_HTML`](r),this.annotationStorage.setValue(this.data.id,{[e]:Ms[`${n}_rgb`](r)})};return dt(this,"_commonActions",{display:t=>{const{display:e}=t.detail,i=e%2==1;this.container.style.visibility=i?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:1===e||2===e})},print:t=>{this.annotationStorage.setValue(this.data.id,{noPrint:!t.detail.print})},hidden:t=>{const{hidden:e}=t.detail;this.container.style.visibility=e?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:e,noView:e})},focus:t=>{setTimeout((()=>t.target.focus({preventScroll:!1})),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.target.disabled=t.detail.readonly},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:t=>{const e=t.detail.rotation;this.setRotation(e),this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){const i=this._commonActions;for(const s of Object.keys(e.detail)){(t[s]||i[s])?.(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const i=this._commonActions;for(const[s,n]of Object.entries(e)){const r=i[s];if(r){r({detail:{[s]:n},target:t}),delete e[s]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,i,s,n]=this.data.rect.map((t=>Math.fround(t)));if(8===t.length){const[r,a,o,h]=t.subarray(2,6);if(s===r&&n===a&&e===o&&i===h)return}const{style:r}=this.container;let a;if(this.#ns){const{borderColor:t,borderWidth:e}=r;r.borderWidth=0,a=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${t}" stroke-width="${e}">`],this.container.classList.add("hasBorder")}const o=s-e,h=n-i,{svgFactory:l}=this,d=l.createElement("svg");d.classList.add("quadrilateralsContainer"),d.setAttribute("width",0),d.setAttribute("height",0);const c=l.createElement("defs");d.append(c);const u=l.createElement("clipPath"),p=`clippath_${this.data.id}`;u.setAttribute("id",p),u.setAttribute("clipPathUnits","objectBoundingBox"),c.append(u);for(let i=2,s=t.length;i<s;i+=8){const s=t[i],r=t[i+1],d=t[i+2],c=t[i+3],p=l.createElement("rect"),g=(d-e)/o,f=(n-r)/h,m=(s-d)/o,b=(r-c)/h;p.setAttribute("x",g),p.setAttribute("y",f),p.setAttribute("width",m),p.setAttribute("height",b),u.append(p),a?.push(`<rect vector-effect="non-scaling-stroke" x="${g}" y="${f}" width="${m}" height="${b}"/>`)}this.#ns&&(a.push("</g></svg>')"),r.backgroundImage=a.join("")),this.container.append(d),this.container.style.clipPath=`url(#${p})`}_createPopup(){const{container:t,data:e}=this;t.setAttribute("aria-haspopup","dialog");const i=this.#rs=new Us({data:{color:e.color,titleObj:e.titleObj,modificationDate:e.modificationDate,contentsObj:e.contentsObj,richText:e.richText,parentRect:e.rect,borderStyle:0,id:`popup_${e.id}`,rotation:e.rotation},parent:this.parent,elements:[this]});this.parent.div.append(i.render())}render(){ot("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const i=[];if(this._fieldObjects){const s=this._fieldObjects[t];if(s)for(const{page:t,id:n,exportValues:r}of s){if(-1===t)continue;if(n===e)continue;const s="string"==typeof r?r:null,a=document.querySelector(`[data-element-id="${n}"]`);!a||Ps.has(a)?i.push({id:n,exportValue:s,domElement:a}):at(`_getElementsByName - element not allowed: ${n}`)}return i}for(const s of document.getElementsByName(t)){const{exportValue:t}=s,n=s.getAttribute("data-element-id");n!==e&&(Ps.has(s)&&i.push({id:n,exportValue:t,domElement:s}))}return i}show(){this.container&&(this.container.hidden=!1),this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0),this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}get _isEditable(){return!1}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",(()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})}))}}class Is extends Ls{constructor(t,e=null){super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0}),this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,i=document.createElement("a");i.setAttribute("data-element-id",t.id);let s=!1;return t.url?(e.addLinkAttributes(i,t.url,t.newWindow),s=!0):t.action?(this._bindNamedAction(i,t.action),s=!0):t.attachment?(this.#os(i,t.attachment,t.attachmentDest),s=!0):t.setOCGState?(this.#hs(i,t.setOCGState),s=!0):t.dest?(this._bindLink(i,t.dest),s=!0):(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(i,t),s=!0),t.resetForm?(this._bindResetFormAction(i,t.resetForm),s=!0):this.isTooltipOnly&&!s&&(this._bindLink(i,""),s=!0)),this.container.classList.add("linkAnnotation"),s&&this.container.append(i),this.container}#ls(){this.container.setAttribute("data-internal-link","")}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e),t.onclick=()=>(e&&this.linkService.goToDestination(e),!1),(e||""===e)&&this.#ls()}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeNamedAction(e),!1),this.#ls()}#os(t,e,i=null){t.href=this.linkService.getAnchorUrl(""),e.description&&(t.title=e.description),t.onclick=()=>(this.downloadManager?.openOrDownloadData(e.content,e.filename,i),!1),this.#ls()}#hs(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeSetOCGState(e),!1),this.#ls()}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const s of Object.keys(e.actions)){const n=i.get(s);n&&(t[n]=()=>(this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:s}}),!1))}t.onclick||(t.onclick=()=>!1),this.#ls()}_bindResetFormAction(t,e){const i=t.onclick;if(i||(t.href=this.linkService.getAnchorUrl("")),this.#ls(),!this._fieldObjects)return at('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),void(i||(t.onclick=()=>!1));t.onclick=()=>{i?.();const{fields:t,refs:s,include:n}=e,r=[];if(0!==t.length||0!==s.length){const e=new Set(s);for(const i of t){const t=this._fieldObjects[i]||[];for(const{id:i}of t)e.add(i)}for(const t of Object.values(this._fieldObjects))for(const i of t)e.has(i.id)===n&&r.push(i)}else for(const t of Object.values(this._fieldObjects))r.push(...t);const a=this.annotationStorage,o=[];for(const t of r){const{id:e}=t;switch(o.push(e),t.type){case"text":{const i=t.defaultValue||"";a.setValue(e,{value:i});break}case"checkbox":case"radiobutton":{const i=t.defaultValue===t.exportValues;a.setValue(e,{value:i});break}case"combobox":case"listbox":{const i=t.defaultValue||"";a.setValue(e,{value:i});break}default:continue}const i=document.querySelector(`[data-element-id="${e}"]`);i&&(Ps.has(i)?i.dispatchEvent(new Event("resetform")):at(`_bindResetFormAction - element not allowed: ${e}`))}return this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:o,name:"ResetForm"}}),!1}}}class Fs extends Ls{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.setAttribute("data-l10n-id","pdfjs-text-annotation-type"),t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name})),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(t),this.container}}class Ns extends Ls{render(){return this.container}showElementAndHideCanvas(t){this.data.hasOwnCanvas&&("CANVAS"===t.previousSibling?.nodeName&&(t.previousSibling.hidden=!0),t.hidden=!1)}_getKeyModifier(t){return xt.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,i,s,n){i.includes("mouse")?t.addEventListener(i,(t=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})})):t.addEventListener(i,(t=>{if("blur"===i){if(!e.focused||!t.relatedTarget)return;e.focused=!1}else if("focus"===i){if(e.focused)return;e.focused=!0}n&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t)}})}))}_setEventListeners(t,e,i,s){for(const[n,r]of i)("Action"===r||this.data.actions?.[r])&&("Focus"!==r&&"Blur"!==r||(e||={focused:!1}),this._setEventListener(t,e,n,r,s),"Focus"!==r||this.data.actions?.Blur?"Blur"!==r||this.data.actions?.Focus||this._setEventListener(t,e,"focus","Focus",null):this._setEventListener(t,e,"blur","Blur",null))}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":St.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:i}=this.data.defaultAppearanceData,s=this.data.defaultAppearanceData.fontSize||9,n=t.style;let r;const a=t=>Math.round(10*t)/10;if(this.data.multiLine){const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2),e=t/(Math.round(t/(d*s))||1);r=Math.min(s,a(e/d))}else{const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2);r=Math.min(s,a(t/d))}n.fontSize=`calc(${r}px * var(--scale-factor))`,n.color=St.makeHexColor(i[0],i[1],i[2]),null!==this.data.textAlignment&&(n.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class Os extends Ns{constructor(t){super(t,{isRenderable:t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,i,s){const n=this.annotationStorage;for(const r of this._getElementsByName(t.name,t.id))r.domElement&&(r.domElement[e]=i),n.setValue(r.id,{[s]:i})}render(){const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let i=null;if(this.renderForms){const s=t.getValue(e,{value:this.data.fieldValue});let n=s.value||"";const r=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;r&&n.length>r&&(n=n.slice(0,r));let a=s.formattedValue||this.data.textContent?.join("\n")||null;a&&this.data.comb&&(a=a.replaceAll(/\s+/g,""));const o={userValue:n,formattedValue:a,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(i=document.createElement("textarea"),i.textContent=a??n,this.data.doNotScroll&&(i.style.overflowY="hidden")):(i=document.createElement("input"),i.type="text",i.setAttribute("value",a??n),this.data.doNotScroll&&(i.style.overflowX="hidden")),this.data.hasOwnCanvas&&(i.hidden=!0),Ps.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,i.name=this.data.fieldName,i.tabIndex=ks,this._setRequired(i,this.data.required),r&&(i.maxLength=r),i.addEventListener("input",(s=>{t.setValue(e,{value:s.target.value}),this.setPropertyOnSiblings(i,"value",s.target.value,"value"),o.formattedValue=null})),i.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue??"";i.value=o.userValue=e,o.formattedValue=null}));let h=t=>{const{formattedValue:e}=o;null!=e&&(t.target.value=e),t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){i.addEventListener("focus",(t=>{if(o.focused)return;const{target:e}=t;o.userValue&&(e.value=o.userValue),o.lastCommittedValue=e.value,o.commitKey=1,this.data.actions?.Focus||(o.focused=!0)})),i.addEventListener("updatefromsandbox",(i=>{this.showElementAndHideCanvas(i.target);const s={value(i){o.userValue=i.detail.value??"",t.setValue(e,{value:o.userValue.toString()}),i.target.value=o.userValue},formattedValue(i){const{formattedValue:s}=i.detail;o.formattedValue=s,null!=s&&i.target!==document.activeElement&&(i.target.value=s),t.setValue(e,{formattedValue:s})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:i=>{const{charLimit:s}=i.detail,{target:n}=i;if(0===s)return void n.removeAttribute("maxLength");n.setAttribute("maxLength",s);let r=o.userValue;!r||r.length<=s||(r=r.slice(0,s),n.value=o.userValue=r,t.setValue(e,{value:r}),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:r,willCommit:!0,commitKey:1,selStart:n.selectionStart,selEnd:n.selectionEnd}}))}};this._dispatchEventFromSandbox(s,i)})),i.addEventListener("keydown",(t=>{o.commitKey=1;let i=-1;if("Escape"===t.key?i=0:"Enter"!==t.key||this.data.multiLine?"Tab"===t.key&&(o.commitKey=3):i=2,-1===i)return;const{value:s}=t.target;o.lastCommittedValue!==s&&(o.lastCommittedValue=s,o.userValue=s,this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,willCommit:!0,commitKey:i,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}}))}));const s=h;h=null,i.addEventListener("blur",(t=>{if(!o.focused||!t.relatedTarget)return;this.data.actions?.Blur||(o.focused=!1);const{value:i}=t.target;o.userValue=i,o.lastCommittedValue!==i&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:i,willCommit:!0,commitKey:o.commitKey,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}}),s(t)})),this.data.actions?.Keystroke&&i.addEventListener("beforeinput",(t=>{o.lastCommittedValue=null;const{data:i,target:s}=t,{value:n,selectionStart:r,selectionEnd:a}=s;let h=r,l=a;switch(t.inputType){case"deleteWordBackward":{const t=n.substring(0,r).match(/\w*[^\w]*$/);t&&(h-=t[0].length);break}case"deleteWordForward":{const t=n.substring(r).match(/^[^\w]*\w*/);t&&(l+=t[0].length);break}case"deleteContentBackward":r===a&&(h-=1);break;case"deleteContentForward":r===a&&(l+=1)}t.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,change:i||"",willCommit:!1,selStart:h,selEnd:l}})})),this._setEventListeners(i,o,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.value))}if(h&&i.addEventListener("blur",h),this.data.comb){const t=(this.data.rect[2]-this.data.rect[0])/r;i.classList.add("comb"),i.style.letterSpacing=`calc(${t}px * var(--scale-factor) - 1ch)`}}else i=document.createElement("div"),i.textContent=this.data.fieldValue,i.style.verticalAlign="middle",i.style.display="table-cell",this.data.hasOwnCanvas&&(i.hidden=!0);return this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class zs extends Ns{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class Hs extends Ns{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,i=e.id;let s=t.getValue(i,{value:e.exportValue===e.fieldValue}).value;"string"==typeof s&&(s="Off"!==s,t.setValue(i,{value:s})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const n=document.createElement("input");return Ps.add(n),n.setAttribute("data-element-id",i),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="checkbox",n.name=e.fieldName,s&&n.setAttribute("checked",!0),n.setAttribute("exportValue",e.exportValue),n.tabIndex=ks,n.addEventListener("change",(s=>{const{name:n,checked:r}=s.target;for(const s of this._getElementsByName(n,i)){const i=r&&s.exportValue===e.exportValue;s.domElement&&(s.domElement.checked=i),t.setValue(s.id,{value:i})}t.setValue(i,{value:r})})),n.addEventListener("resetform",(t=>{const i=e.defaultFieldValue||"Off";t.target.checked=i===e.exportValue})),this.enableScripting&&this.hasJSActions&&(n.addEventListener("updatefromsandbox",(e=>{const s={value(e){e.target.checked="Off"!==e.detail.value,t.setValue(i,{value:e.target.checked})}};this._dispatchEventFromSandbox(s,e)})),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))),this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class Bs extends Ns{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,i=e.id;let s=t.getValue(i,{value:e.fieldValue===e.buttonValue}).value;if("string"==typeof s&&(s=s!==e.buttonValue,t.setValue(i,{value:s})),s)for(const s of this._getElementsByName(e.fieldName,i))t.setValue(s.id,{value:!1});const n=document.createElement("input");if(Ps.add(n),n.setAttribute("data-element-id",i),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="radio",n.name=e.fieldName,s&&n.setAttribute("checked",!0),n.tabIndex=ks,n.addEventListener("change",(e=>{const{name:s,checked:n}=e.target;for(const e of this._getElementsByName(s,i))t.setValue(e.id,{value:!1});t.setValue(i,{value:n})})),n.addEventListener("resetform",(t=>{const i=e.defaultFieldValue;t.target.checked=null!=i&&i===e.buttonValue})),this.enableScripting&&this.hasJSActions){const s=e.buttonValue;n.addEventListener("updatefromsandbox",(e=>{const n={value:e=>{const n=s===e.detail.value;for(const s of this._getElementsByName(e.target.name)){const e=n&&s.id===i;s.domElement&&(s.domElement.checked=e),t.setValue(s.id,{value:e})}}};this._dispatchEventFromSandbox(n,e)})),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}return this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class js extends Is{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",(t=>{this._dispatchEventFromSandbox({},t)}))),t}}class Ws extends Ns{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,i=t.getValue(e,{value:this.data.fieldValue}),s=document.createElement("select");Ps.add(s),s.setAttribute("data-element-id",e),s.disabled=this.data.readOnly,this._setRequired(s,this.data.required),s.name=this.data.fieldName,s.tabIndex=ks;let n=this.data.combo&&this.data.options.length>0;this.data.combo||(s.size=this.data.options.length,this.data.multiSelect&&(s.multiple=!0)),s.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue;for(const t of s.options)t.selected=t.value===e}));for(const t of this.data.options){const e=document.createElement("option");e.textContent=t.displayValue,e.value=t.exportValue,i.value.includes(t.exportValue)&&(e.setAttribute("selected",!0),n=!1),s.append(e)}let r=null;if(n){const t=document.createElement("option");t.value=" ",t.setAttribute("hidden",!0),t.setAttribute("selected",!0),s.prepend(t),r=()=>{t.remove(),s.removeEventListener("input",r),r=null},s.addEventListener("input",r)}const a=t=>{const e=t?"value":"textContent",{options:i,multiple:n}=s;return n?Array.prototype.filter.call(i,(t=>t.selected)).map((t=>t[e])):-1===i.selectedIndex?null:i[i.selectedIndex][e]};let o=a(!1);const h=t=>{const e=t.target.options;return Array.prototype.map.call(e,(t=>({displayValue:t.textContent,exportValue:t.value})))};return this.enableScripting&&this.hasJSActions?(s.addEventListener("updatefromsandbox",(i=>{const n={value(i){r?.();const n=i.detail.value,h=new Set(Array.isArray(n)?n:[n]);for(const t of s.options)t.selected=h.has(t.value);t.setValue(e,{value:a(!0)}),o=a(!1)},multipleSelection(t){s.multiple=!0},remove(i){const n=s.options,r=i.detail.remove;if(n[r].selected=!1,s.remove(r),n.length>0){-1===Array.prototype.findIndex.call(n,(t=>t.selected))&&(n[0].selected=!0)}t.setValue(e,{value:a(!0),items:h(i)}),o=a(!1)},clear(i){for(;0!==s.length;)s.remove(0);t.setValue(e,{value:null,items:[]}),o=a(!1)},insert(i){const{index:n,displayValue:r,exportValue:l}=i.detail.insert,d=s.children[n],c=document.createElement("option");c.textContent=r,c.value=l,d?d.before(c):s.append(c),t.setValue(e,{value:a(!0),items:h(i)}),o=a(!1)},items(i){const{items:n}=i.detail;for(;0!==s.length;)s.remove(0);for(const t of n){const{displayValue:e,exportValue:i}=t,n=document.createElement("option");n.textContent=e,n.value=i,s.append(n)}s.options.length>0&&(s.options[0].selected=!0),t.setValue(e,{value:a(!0),items:h(i)}),o=a(!1)},indices(i){const s=new Set(i.detail.indices);for(const t of i.target.options)t.selected=s.has(t.index);t.setValue(e,{value:a(!0)}),o=a(!1)},editable(t){t.target.disabled=!t.detail.editable}};this._dispatchEventFromSandbox(n,i)})),s.addEventListener("input",(i=>{const s=a(!0),n=a(!1);t.setValue(e,{value:s}),i.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:o,change:n,changeEx:s,willCommit:!1,commitKey:1,keyDown:!1}})})),this._setEventListeners(s,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],(t=>t.target.value))):s.addEventListener("input",(function(i){t.setValue(e,{value:a(!0)})})),this.data.combo&&this._setTextStyle(s),this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class Us extends Ls{constructor(t){const{data:e,elements:i}=t;super(t,{isRenderable:Ls._hasPopupData(e)}),this.elements=i,this.popup=null}render(){this.container.classList.add("popupAnnotation");const t=this.popup=new $s({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const i of this.elements)i.popup=t,e.push(i.data.id),i.addHighlightArea();return this.container.setAttribute("aria-controls",e.map((t=>`${Tt}${t}`)).join(",")),this.container}}class $s{#ds=this.#cs.bind(this);#us=this.#ps.bind(this);#gs=this.#fs.bind(this);#ms=this.#bs.bind(this);#vs=null;#mt=null;#ys=null;#ws=null;#As=null;#xs=null;#_s=null;#Ss=!1;#Es=null;#S=null;#Cs=null;#Ms=null;#Ts=null;#ss=null;#ks=!1;constructor({container:t,color:e,elements:i,titleObj:s,modificationDate:n,contentsObj:r,richText:a,parent:o,rect:h,parentRect:l,open:d}){this.#mt=t,this.#Ts=s,this.#ys=r,this.#Ms=a,this.#xs=o,this.#vs=e,this.#Cs=h,this.#_s=l,this.#As=i,this.#ws=ae.toDateObject(n),this.trigger=i.flatMap((t=>t.getElementsToTriggerPopup()));for(const t of this.trigger)t.addEventListener("click",this.#ms),t.addEventListener("mouseenter",this.#gs),t.addEventListener("mouseleave",this.#us),t.classList.add("popupTriggerArea");for(const t of i)t.container?.addEventListener("keydown",this.#ds);this.#mt.hidden=!0,d&&this.#bs()}render(){if(this.#Es)return;const t=this.#Es=document.createElement("div");if(t.className="popup",this.#vs){const e=t.style.outlineColor=St.makeHexColor(...this.#vs);if(CSS.supports("background-color","color-mix(in srgb, red 30%, white)"))t.style.backgroundColor=`color-mix(in srgb, ${e} 30%, white)`;else{const e=.7;t.style.backgroundColor=St.makeHexColor(...this.#vs.map((t=>Math.floor(e*(255-t)+t))))}}const e=document.createElement("span");e.className="header";const i=document.createElement("h1");if(e.append(i),({dir:i.dir,str:i.textContent}=this.#Ts),t.append(e),this.#ws){const t=document.createElement("span");t.classList.add("popupDate"),t.setAttribute("data-l10n-id","pdfjs-annotation-date-string"),t.setAttribute("data-l10n-args",JSON.stringify({date:this.#ws.toLocaleDateString(),time:this.#ws.toLocaleTimeString()})),e.append(t)}const s=this.#Ps;if(s)Ts.render({xfaHtml:s,intent:"richText",div:t}),t.lastChild.classList.add("richText","popupContent");else{const e=this._formatContents(this.#ys);t.append(e)}this.#mt.append(t)}get#Ps(){const t=this.#Ms,e=this.#ys;return!t?.str||e?.str&&e.str!==t.str?null:this.#Ms.html||null}get#Rs(){return this.#Ps?.attributes?.style?.fontSize||0}get#Ds(){return this.#Ps?.attributes?.style?.color||null}#Ls(t){const e=[],i={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},s={style:{color:this.#Ds,fontSize:this.#Rs?`calc(${this.#Rs}px * var(--scale-factor))`:""}};for(const i of t.split("\n"))e.push({name:"span",value:i,attributes:s});return i}_formatContents({str:t,dir:e}){const i=document.createElement("p");i.classList.add("popupContent"),i.dir=e;const s=t.split(/(?:\r\n?|\n)/);for(let t=0,e=s.length;t<e;++t){const n=s[t];i.append(document.createTextNode(n)),t<e-1&&i.append(document.createElement("br"))}return i}#cs(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||("Enter"===t.key||"Escape"===t.key&&this.#Ss)&&this.#bs()}updateEdited({rect:t,popupContent:e}){this.#ss||={contentsObj:this.#ys,richText:this.#Ms},t&&(this.#S=null),e&&(this.#Ms=this.#Ls(e),this.#ys=null),this.#Es?.remove(),this.#Es=null}resetEdited(){this.#ss&&(({contentsObj:this.#ys,richText:this.#Ms}=this.#ss),this.#ss=null,this.#Es?.remove(),this.#Es=null,this.#S=null)}#Is(){if(null!==this.#S)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:i,pageX:s,pageY:n}}}=this.#xs;let r=!!this.#_s,a=r?this.#_s:this.#Cs;for(const t of this.#As)if(!a||null!==St.intersect(t.data.rect,a)){a=t.data.rect,r=!0;break}const o=St.normalizeRect([a[0],t[3]-a[1]+t[1],a[2],t[3]-a[3]+t[1]]),h=r?a[2]-a[0]+5:0,l=o[0]+h,d=o[1];this.#S=[100*(l-s)/e,100*(d-n)/i];const{style:c}=this.#mt;c.left=`${this.#S[0]}%`,c.top=`${this.#S[1]}%`}#bs(){this.#Ss=!this.#Ss,this.#Ss?(this.#fs(),this.#mt.addEventListener("click",this.#ms),this.#mt.addEventListener("keydown",this.#ds)):(this.#ps(),this.#mt.removeEventListener("click",this.#ms),this.#mt.removeEventListener("keydown",this.#ds))}#fs(){this.#Es||this.render(),this.isVisible?this.#Ss&&this.#mt.classList.add("focused"):(this.#Is(),this.#mt.hidden=!1,this.#mt.style.zIndex=parseInt(this.#mt.style.zIndex)+1e3)}#ps(){this.#mt.classList.remove("focused"),!this.#Ss&&this.isVisible&&(this.#mt.hidden=!0,this.#mt.style.zIndex=parseInt(this.#mt.style.zIndex)-1e3)}forceHide(){this.#ks=this.isVisible,this.#ks&&(this.#mt.hidden=!0)}maybeShow(){this.#ks&&(this.#Es||this.#fs(),this.#ks=!1,this.#mt.hidden=!1)}get isVisible(){return!1===this.#mt.hidden}}class Gs extends Ls{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.textContent=t.data.textContent,this.textPosition=t.data.textPosition,this.annotationEditorType=y.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent"),t.setAttribute("role","comment");for(const e of this.textContent){const i=document.createElement("span");i.textContent=e,t.append(i)}this.container.append(t)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}get _isEditable(){return this.data.hasOwnCanvas}}class Vs extends Ls{#Fs=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("lineAnnotation");const t=this.data,{width:e,height:i}=Rs(t.rect),s=this.svgFactory.create(e,i,!0),n=this.#Fs=this.svgFactory.createElement("svg:line");return n.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]),n.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]),n.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]),n.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]),n.setAttribute("stroke-width",t.borderStyle.width||1),n.setAttribute("stroke","transparent"),n.setAttribute("fill","transparent"),s.append(n),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#Fs}addHighlightArea(){this.container.classList.add("highlightArea")}}class qs extends Ls{#Ns=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("squareAnnotation");const t=this.data,{width:e,height:i}=Rs(t.rect),s=this.svgFactory.create(e,i,!0),n=t.borderStyle.width,r=this.#Ns=this.svgFactory.createElement("svg:rect");return r.setAttribute("x",n/2),r.setAttribute("y",n/2),r.setAttribute("width",e-n),r.setAttribute("height",i-n),r.setAttribute("stroke-width",n||1),r.setAttribute("stroke","transparent"),r.setAttribute("fill","transparent"),s.append(r),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#Ns}addHighlightArea(){this.container.classList.add("highlightArea")}}class Xs extends Ls{#Os=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("circleAnnotation");const t=this.data,{width:e,height:i}=Rs(t.rect),s=this.svgFactory.create(e,i,!0),n=t.borderStyle.width,r=this.#Os=this.svgFactory.createElement("svg:ellipse");return r.setAttribute("cx",e/2),r.setAttribute("cy",i/2),r.setAttribute("rx",e/2-n/2),r.setAttribute("ry",i/2-n/2),r.setAttribute("stroke-width",n||1),r.setAttribute("stroke","transparent"),r.setAttribute("fill","transparent"),s.append(r),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#Os}addHighlightArea(){this.container.classList.add("highlightArea")}}class Ys extends Ls{#zs=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,vertices:e,borderStyle:i,popupRef:s}}=this;if(!e)return this.container;const{width:n,height:r}=Rs(t),a=this.svgFactory.create(n,r,!0);let o=[];for(let i=0,s=e.length;i<s;i+=2){const s=e[i]-t[0],n=t[3]-e[i+1];o.push(`${s},${n}`)}o=o.join(" ");const h=this.#zs=this.svgFactory.createElement(this.svgElementName);return h.setAttribute("points",o),h.setAttribute("stroke-width",i.width||1),h.setAttribute("stroke","transparent"),h.setAttribute("fill","transparent"),a.append(h),this.container.append(a),!s&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#zs}addHighlightArea(){this.container.classList.add("highlightArea")}}class Ks extends Ys{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class Qs extends Ls{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class Js extends Ls{#Hs=[];constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType=y.INK}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,inkLists:e,borderStyle:i,popupRef:s}}=this,{width:n,height:r}=Rs(t),a=this.svgFactory.create(n,r,!0);for(const n of e){let e=[];for(let i=0,s=n.length;i<s;i+=2){const s=n[i]-t[0],r=t[3]-n[i+1];e.push(`${s},${r}`)}e=e.join(" ");const r=this.svgFactory.createElement(this.svgElementName);this.#Hs.push(r),r.setAttribute("points",e),r.setAttribute("stroke-width",i.width||1),r.setAttribute("stroke","transparent"),r.setAttribute("fill","transparent"),!s&&this.hasPopupData&&this._createPopup(),a.append(r)}return this.container.append(a),this.container}getElementsToTriggerPopup(){return this.#Hs}addHighlightArea(){this.container.classList.add("highlightArea")}}class Zs extends Ls{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this.container}}class tn extends Ls{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class en extends Ls{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class sn extends Ls{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class nn extends Ls{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("stampAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class rn extends Ls{#Bs=null;constructor(t){super(t,{isRenderable:!0});const{file:e}=this.data;this.filename=e.filename,this.content=e.content,this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,...e})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:t,data:e}=this;let i;e.hasAppearance||0===e.fillAlpha?i=document.createElement("div"):(i=document.createElement("img"),i.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(e.name)?"paperclip":"pushpin"}.svg`,e.fillAlpha&&e.fillAlpha<1&&(i.style=`filter: opacity(${Math.round(100*e.fillAlpha)}%);`)),i.addEventListener("dblclick",this.#js.bind(this)),this.#Bs=i;const{isMac:s}=xt.platform;return t.addEventListener("keydown",(t=>{"Enter"===t.key&&(s?t.metaKey:t.ctrlKey)&&this.#js()})),!e.popupRef&&this.hasPopupData?this._createPopup():i.classList.add("popupTriggerArea"),t.append(i),t}getElementsToTriggerPopup(){return this.#Bs}addHighlightArea(){this.container.classList.add("highlightArea")}#js(){this.downloadManager?.openOrDownloadData(this.content,this.filename)}}class an{#Ws=null;#Us=null;#$s=new Map;constructor({div:t,accessibilityManager:e,annotationCanvasMap:i,annotationEditorUIManager:s,page:n,viewport:r}){this.div=t,this.#Ws=e,this.#Us=i,this.page=n,this.viewport=r,this.zIndex=0,this._annotationEditorUIManager=s}#Gs(t,e){const i=t.firstChild||t;i.id=`${Tt}${e}`,this.div.append(t),this.#Ws?.moveElementInDOM(this.div,t,i,!1)}async render(t){const{annotations:e}=t,i=this.div;ce(i,this.viewport);const s=new Map,n={data:null,layer:i,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new Xt,annotationStorage:t.annotationStorage||new ke,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const t of e){if(t.noHTML)continue;const e=t.annotationType===$;if(e){const e=s.get(t.id);if(!e)continue;n.elements=e}else{const{width:e,height:i}=Rs(t.rect);if(e<=0||i<=0)continue}n.data=t;const i=Ds.create(n);if(!i.isRenderable)continue;if(!e&&t.popupRef){const e=s.get(t.popupRef);e?e.push(i):s.set(t.popupRef,[i])}const r=i.render();t.hidden&&(r.style.visibility="hidden"),this.#Gs(r,t.id),i.annotationEditorType>0&&(this.#$s.set(i.data.id,i),this._annotationEditorUIManager?.renderAnnotationElement(i))}this.#Vs()}update({viewport:t}){const e=this.div;this.viewport=t,ce(e,{rotation:t.rotation}),this.#Vs(),e.hidden=!1}#Vs(){if(!this.#Us)return;const t=this.div;for(const[e,i]of this.#Us){const s=t.querySelector(`[data-annotation-id="${e}"]`);if(!s)continue;i.className="annotationContent";const{firstChild:n}=s;n?"CANVAS"===n.nodeName?n.replaceWith(i):n.classList.contains("annotationContent")?n.after(i):n.before(i):s.append(i)}this.#Us.clear()}getEditableAnnotations(){return Array.from(this.#$s.values())}getEditableAnnotation(t){return this.#$s.get(t)}}const on=/\r\n?|\n/g;class hn extends xe{#qs=this.editorDivBlur.bind(this);#Xs=this.editorDivFocus.bind(this);#Ys=this.editorDivInput.bind(this);#Ks=this.editorDivKeydown.bind(this);#Qs=this.editorDivPaste.bind(this);#vs;#Js="";#Zs=`${this.id}-editor`;#Rs;#tn=null;static _freeTextDefaultContent="";static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static get _keyboardManager(){const t=hn.prototype,e=t=>t.isEmpty(),i=we.TRANSLATE_SMALL,s=we.TRANSLATE_BIG;return dt(this,"_keyboardManager",new ve([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],t.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],t.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],t._translateEmpty,{args:[-i,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t._translateEmpty,{args:[-s,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t._translateEmpty,{args:[i,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t._translateEmpty,{args:[s,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t._translateEmpty,{args:[0,-i],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t._translateEmpty,{args:[0,-s],checker:e}],[["ArrowDown","mac+ArrowDown"],t._translateEmpty,{args:[0,i],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t._translateEmpty,{args:[0,s],checker:e}]]))}static _type="freetext";static _editorType=y.FREETEXT;constructor(t){super({...t,name:"freeTextEditor"}),this.#vs=t.color||hn._defaultColor||xe._defaultLineColor,this.#Rs=t.fontSize||hn._defaultFontSize}static initialize(t,e){xe.initialize(t,e,{strings:["pdfjs-free-text-default-content"]});const i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case w.FREETEXT_SIZE:hn._defaultFontSize=e;break;case w.FREETEXT_COLOR:hn._defaultColor=e}}updateParams(t,e){switch(t){case w.FREETEXT_SIZE:this.#en(e);break;case w.FREETEXT_COLOR:this.#in(e)}}static get defaultPropertiesToUpdate(){return[[w.FREETEXT_SIZE,hn._defaultFontSize],[w.FREETEXT_COLOR,hn._defaultColor||xe._defaultLineColor]]}get propertiesToUpdate(){return[[w.FREETEXT_SIZE,this.#Rs],[w.FREETEXT_COLOR,this.#vs]]}#en(t){const e=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--scale-factor))`,this.translate(0,-(t-this.#Rs)*this.parentScale),this.#Rs=t,this.#sn()},i=this.#Rs;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:w.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#in(t){const e=t=>{this.#vs=this.editorDiv.style.color=t},i=this.#vs;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:w.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){const t=this.parentScale;return[-hn._internalPadding*t,-(hn._internalPadding+this.#Rs)*t]}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){if(this.isInEditMode())return;this.parent.setEditingState(!1),this.parent.updateToolbar(y.FREETEXT),super.enableEditMode(),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant");const t=this._uiManager._signal;this.editorDiv.addEventListener("keydown",this.#Ks,{signal:t}),this.editorDiv.addEventListener("focus",this.#Xs,{signal:t}),this.editorDiv.addEventListener("blur",this.#qs,{signal:t}),this.editorDiv.addEventListener("input",this.#Ys,{signal:t}),this.editorDiv.addEventListener("paste",this.#Qs,{signal:t})}disableEditMode(){this.isInEditMode()&&(this.parent.setEditingState(!0),super.disableEditMode(),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",this.#Zs),this._isDraggable=!0,this.editorDiv.removeEventListener("keydown",this.#Ks),this.editorDiv.removeEventListener("focus",this.#Xs),this.editorDiv.removeEventListener("blur",this.#qs),this.editorDiv.removeEventListener("input",this.#Ys),this.editorDiv.removeEventListener("paste",this.#Qs),this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freetextEditing"))}focusin(t){this._focusEventsAllowed&&(super.focusin(t),t.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(){this.width||(this.enableEditMode(),this.editorDiv.focus(),this._initialOptions?.isCentered&&this.center(),this._initialOptions=null)}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freetextEditing")),super.remove()}#nn(){const t=[];this.editorDiv.normalize();for(const e of this.editorDiv.childNodes)t.push(hn.#rn(e));return t.join("\n")}#sn(){const[t,e]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:t,div:e}=this,s=e.style.display,n=e.classList.contains("hidden");e.classList.remove("hidden"),e.style.display="hidden",t.div.append(this.div),i=e.getBoundingClientRect(),e.remove(),e.style.display=s,e.classList.toggle("hidden",n)}this.rotation%180==this.parentRotation%180?(this.width=i.width/t,this.height=i.height/e):(this.width=i.height/t,this.height=i.width/e),this.fixAndSetPosition()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const t=this.#Js,e=this.#Js=this.#nn().trimEnd();if(t===e)return;const i=t=>{this.#Js=t,t?(this.#an(),this._uiManager.rebuild(this),this.#sn()):this.remove()};this.addCommands({cmd:()=>{i(e)},undo:()=>{i(t)},mustExec:!1}),this.#sn()}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}dblclick(t){this.enterInEditMode()}keydown(t){t.target===this.div&&"Enter"===t.key&&(this.enterInEditMode(),t.preventDefault())}editorDivKeydown(t){hn._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let t,e;this.width&&(t=this.x,e=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",this.#Zs),this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text"),this.enableEditing(),xe._l10nPromise.get("pdfjs-free-text-default-content").then((t=>this.editorDiv?.setAttribute("default-content",t))),this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;if(i.fontSize=`calc(${this.#Rs}px * var(--scale-factor))`,i.color=this.#vs,this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),ge(this,this.div,["dblclick","keydown"]),this.width){const[i,s]=this.parentDimensions;if(this.annotationElementId){const{position:n}=this.#tn;let[r,a]=this.getInitialTranslation();[r,a]=this.pageTranslationToScreen(r,a);const[o,h]=this.pageDimensions,[l,d]=this.pageTranslation;let c,u;switch(this.rotation){case 0:c=t+(n[0]-l)/o,u=e+this.height-(n[1]-d)/h;break;case 90:c=t+(n[0]-l)/o,u=e-(n[1]-d)/h,[r,a]=[a,-r];break;case 180:c=t-this.width+(n[0]-l)/o,u=e-(n[1]-d)/h,[r,a]=[-r,-a];break;case 270:c=t+(n[0]-l-this.height*h)/o,u=e+(n[1]-d-this.width*o)/h,[r,a]=[-a,r]}this.setAt(c*i,u*s,r,a)}else this.setAt(t*i,e*s,this.width*i,this.height*s);this.#an(),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}static#rn(t){return(t.nodeType===Node.TEXT_NODE?t.nodeValue:t.innerText).replaceAll(on,"")}editorDivPaste(t){const e=t.clipboardData||window.clipboardData,{types:i}=e;if(1===i.length&&"text/plain"===i[0])return;t.preventDefault();const s=hn.#on(e.getData("text")||"").replaceAll(on,"\n");if(!s)return;const n=window.getSelection();if(!n.rangeCount)return;this.editorDiv.normalize(),n.deleteFromDocument();const r=n.getRangeAt(0);if(!s.includes("\n"))return r.insertNode(document.createTextNode(s)),this.editorDiv.normalize(),void n.collapseToStart();const{startContainer:a,startOffset:o}=r,h=[],l=[];if(a.nodeType===Node.TEXT_NODE){const t=a.parentElement;if(l.push(a.nodeValue.slice(o).replaceAll(on,"")),t!==this.editorDiv){let e=h;for(const i of this.editorDiv.childNodes)i!==t?e.push(hn.#rn(i)):e=l}h.push(a.nodeValue.slice(0,o).replaceAll(on,""))}else if(a===this.editorDiv){let t=h,e=0;for(const i of this.editorDiv.childNodes)e++===o&&(t=l),t.push(hn.#rn(i))}this.#Js=`${h.join("\n")}${s}${l.join("\n")}`,this.#an();const d=new Range;let c=h.reduce(((t,e)=>t+e.length),0);for(const{firstChild:t}of this.editorDiv.childNodes)if(t.nodeType===Node.TEXT_NODE){const e=t.nodeValue.length;if(c<=e){d.setStart(t,c),d.setEnd(t,c);break}c-=e}n.removeAllRanges(),n.addRange(d)}#an(){if(this.editorDiv.replaceChildren(),this.#Js)for(const t of this.#Js.split("\n")){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br")),this.editorDiv.append(e)}}#hn(){return this.#Js.replaceAll(" "," ")}static#on(t){return t.replaceAll(" "," ")}get contentDiv(){return this.editorDiv}static deserialize(t,e,i){let s=null;if(t instanceof Gs){const{data:{defaultAppearanceData:{fontSize:e,fontColor:i},rect:n,rotation:r,id:a},textContent:o,textPosition:h,parent:{page:{pageNumber:l}}}=t;if(!o||0===o.length)return null;s=t={annotationType:y.FREETEXT,color:Array.from(i),fontSize:e,value:o.join("\n"),position:h,pageIndex:l-1,rect:n.slice(0),rotation:r,id:a,deleted:!1}}const n=super.deserialize(t,e,i);return n.#Rs=t.fontSize,n.#vs=St.makeHexColor(...t.color),n.#Js=hn.#on(t.value),n.annotationElementId=t.id||null,n.#tn=s,n}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return{pageIndex:this.pageIndex,id:this.annotationElementId,deleted:!0};const e=hn._internalPadding*this.parentScale,i=this.getRect(e,e),s=xe._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:this.#vs),n={annotationType:y.FREETEXT,color:s,fontSize:this.#Rs,value:this.#hn(),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?n:this.annotationElementId&&!this.#ln(n)?null:(n.id=this.annotationElementId,n)}#ln(t){const{value:e,fontSize:i,color:s,pageIndex:n}=this.#tn;return this._hasBeenMoved||t.value!==e||t.fontSize!==i||t.color.some(((t,e)=>t!==s[e]))||t.pageIndex!==n}renderAnnotationElement(t){const e=super.renderAnnotationElement(t);if(this.deleted)return e;const{style:i}=e;i.fontSize=`calc(${this.#Rs}px * var(--scale-factor))`,i.color=this.#vs,e.replaceChildren();for(const t of this.#Js.split("\n")){const i=document.createElement("div");i.append(t?document.createTextNode(t):document.createElement("br")),e.append(i)}const s=hn._internalPadding*this.parentScale;return t.updateEdited({rect:this.getRect(s,s),popupContent:this.#Js}),e}resetAnnotationElement(t){super.resetAnnotationElement(t),t.resetEdited()}}class ln{#dn;#cn=[];#un=[];constructor(t,e=0,i=0,s=!0){let n=1/0,r=-1/0,a=1/0,o=-1/0;const h=10**-4;for(const{x:i,y:s,width:l,height:d}of t){const t=Math.floor((i-e)/h)*h,c=Math.ceil((i+l+e)/h)*h,u=Math.floor((s-e)/h)*h,p=Math.ceil((s+d+e)/h)*h,g=[t,u,p,!0],f=[c,u,p,!1];this.#cn.push(g,f),n=Math.min(n,t),r=Math.max(r,c),a=Math.min(a,u),o=Math.max(o,p)}const l=r-n+2*i,d=o-a+2*i,c=n-i,u=a-i,p=this.#cn.at(s?-1:-2),g=[p[0],p[2]];for(const t of this.#cn){const[e,i,s]=t;t[0]=(e-c)/l,t[1]=(i-u)/d,t[2]=(s-u)/d}this.#dn={x:c,y:u,width:l,height:d,lastPoint:g}}getOutlines(){this.#cn.sort(((t,e)=>t[0]-e[0]||t[1]-e[1]||t[2]-e[2]));const t=[];for(const e of this.#cn)e[3]?(t.push(...this.#pn(e)),this.#gn(e)):(this.#fn(e),t.push(...this.#pn(e)));return this.#mn(t)}#mn(t){const e=[],i=new Set;for(const i of t){const[t,s,n]=i;e.push([t,s,i],[t,n,i])}e.sort(((t,e)=>t[1]-e[1]||t[0]-e[0]));for(let t=0,s=e.length;t<s;t+=2){const s=e[t][2],n=e[t+1][2];s.push(n),n.push(s),i.add(s),i.add(n)}const s=[];let n;for(;i.size>0;){const t=i.values().next().value;let[e,r,a,o,h]=t;i.delete(t);let l=e,d=r;for(n=[e,a],s.push(n);;){let t;if(i.has(o))t=o;else{if(!i.has(h))break;t=h}i.delete(t),[e,r,a,o,h]=t,l!==e&&(n.push(l,d,e,d===r?r:a),l=e),d=d===r?a:r}n.push(l,d)}return new cn(s,this.#dn)}#bn(t){const e=this.#un;let i=0,s=e.length-1;for(;i<=s;){const n=i+s>>1,r=e[n][0];if(r===t)return n;r<t?i=n+1:s=n-1}return s+1}#gn([,t,e]){const i=this.#bn(t);this.#un.splice(i,0,[t,e])}#fn([,t,e]){const i=this.#bn(t);for(let s=i;s<this.#un.length;s++){const[i,n]=this.#un[s];if(i!==t)break;if(i===t&&n===e)return void this.#un.splice(s,1)}for(let s=i-1;s>=0;s--){const[i,n]=this.#un[s];if(i!==t)break;if(i===t&&n===e)return void this.#un.splice(s,1)}}#pn(t){const[e,i,s]=t,n=[[e,i,s]],r=this.#bn(s);for(let t=0;t<r;t++){const[i,s]=this.#un[t];for(let t=0,r=n.length;t<r;t++){const[,a,o]=n[t];if(!(s<=a||o<=i))if(a>=i)if(o>s)n[t][1]=s;else{if(1===r)return[];n.splice(t,1),t--,r--}else n[t][2]=i,o>s&&n.push([e,s,o])}}return n}}class dn{toSVGPath(){throw new Error("Abstract method `toSVGPath` must be implemented.")}get box(){throw new Error("Abstract getter `box` must be implemented.")}serialize(t,e){throw new Error("Abstract method `serialize` must be implemented.")}get free(){return this instanceof pn}}class cn extends dn{#dn;#vn;constructor(t,e){super(),this.#vn=t,this.#dn=e}toSVGPath(){const t=[];for(const e of this.#vn){let[i,s]=e;t.push(`M${i} ${s}`);for(let n=2;n<e.length;n+=2){const r=e[n],a=e[n+1];r===i?(t.push(`V${a}`),s=a):a===s&&(t.push(`H${r}`),i=r)}t.push("Z")}return t.join(" ")}serialize([t,e,i,s],n){const r=[],a=i-t,o=s-e;for(const e of this.#vn){const i=new Array(e.length);for(let n=0;n<e.length;n+=2)i[n]=t+e[n]*a,i[n+1]=s-e[n+1]*o;r.push(i)}return r}get box(){return this.#dn}}class un{#dn;#yn=[];#wn;#An;#xn=[];#_n=new Float64Array(18);#Sn;#En;#Cn;#Mn;#Tn;#kn;#Pn=[];static#Rn=8;static#Dn=2;static#Ln=un.#Rn+un.#Dn;constructor({x:t,y:e},i,s,n,r,a=0){this.#dn=i,this.#kn=n*s,this.#An=r,this.#_n.set([NaN,NaN,NaN,NaN,t,e],6),this.#wn=a,this.#Mn=un.#Rn*s,this.#Cn=un.#Ln*s,this.#Tn=s,this.#Pn.push(t,e)}get free(){return!0}isEmpty(){return isNaN(this.#_n[8])}#In(){const t=this.#_n.subarray(4,6),e=this.#_n.subarray(16,18),[i,s,n,r]=this.#dn;return[(this.#Sn+(t[0]-e[0])/2-i)/n,(this.#En+(t[1]-e[1])/2-s)/r,(this.#Sn+(e[0]-t[0])/2-i)/n,(this.#En+(e[1]-t[1])/2-s)/r]}add({x:t,y:e}){this.#Sn=t,this.#En=e;const[i,s,n,r]=this.#dn;let[a,o,h,l]=this.#_n.subarray(8,12);const d=t-h,c=e-l,u=Math.hypot(d,c);if(u<this.#Cn)return!1;const p=u-this.#Mn,g=p/u,f=g*d,m=g*c;let b=a,v=o;a=h,o=l,h+=f,l+=m,this.#Pn?.push(t,e);const y=f/p,w=-m/p*this.#kn,A=y*this.#kn;if(this.#_n.set(this.#_n.subarray(2,8),0),this.#_n.set([h+w,l+A],4),this.#_n.set(this.#_n.subarray(14,18),12),this.#_n.set([h-w,l-A],16),isNaN(this.#_n[6]))return 0===this.#xn.length&&(this.#_n.set([a+w,o+A],2),this.#xn.push(NaN,NaN,NaN,NaN,(a+w-i)/n,(o+A-s)/r),this.#_n.set([a-w,o-A],14),this.#yn.push(NaN,NaN,NaN,NaN,(a-w-i)/n,(o-A-s)/r)),this.#_n.set([b,v,a,o,h,l],6),!this.isEmpty();this.#_n.set([b,v,a,o,h,l],6);return Math.abs(Math.atan2(v-o,b-a)-Math.atan2(m,f))<Math.PI/2?([a,o,h,l]=this.#_n.subarray(2,6),this.#xn.push(NaN,NaN,NaN,NaN,((a+h)/2-i)/n,((o+l)/2-s)/r),[a,o,b,v]=this.#_n.subarray(14,18),this.#yn.push(NaN,NaN,NaN,NaN,((b+a)/2-i)/n,((v+o)/2-s)/r),!0):([b,v,a,o,h,l]=this.#_n.subarray(0,6),this.#xn.push(((b+5*a)/6-i)/n,((v+5*o)/6-s)/r,((5*a+h)/6-i)/n,((5*o+l)/6-s)/r,((a+h)/2-i)/n,((o+l)/2-s)/r),[h,l,a,o,b,v]=this.#_n.subarray(12,18),this.#yn.push(((b+5*a)/6-i)/n,((v+5*o)/6-s)/r,((5*a+h)/6-i)/n,((5*o+l)/6-s)/r,((a+h)/2-i)/n,((o+l)/2-s)/r),!0)}toSVGPath(){if(this.isEmpty())return"";const t=this.#xn,e=this.#yn,i=this.#_n.subarray(4,6),s=this.#_n.subarray(16,18),[n,r,a,o]=this.#dn,[h,l,d,c]=this.#In();if(isNaN(this.#_n[6])&&!this.isEmpty())return`M${(this.#_n[2]-n)/a} ${(this.#_n[3]-r)/o} L${(this.#_n[4]-n)/a} ${(this.#_n[5]-r)/o} L${h} ${l} L${d} ${c} L${(this.#_n[16]-n)/a} ${(this.#_n[17]-r)/o} L${(this.#_n[14]-n)/a} ${(this.#_n[15]-r)/o} Z`;const u=[];u.push(`M${t[4]} ${t[5]}`);for(let e=6;e<t.length;e+=6)isNaN(t[e])?u.push(`L${t[e+4]} ${t[e+5]}`):u.push(`C${t[e]} ${t[e+1]} ${t[e+2]} ${t[e+3]} ${t[e+4]} ${t[e+5]}`);u.push(`L${(i[0]-n)/a} ${(i[1]-r)/o} L${h} ${l} L${d} ${c} L${(s[0]-n)/a} ${(s[1]-r)/o}`);for(let t=e.length-6;t>=6;t-=6)isNaN(e[t])?u.push(`L${e[t+4]} ${e[t+5]}`):u.push(`C${e[t]} ${e[t+1]} ${e[t+2]} ${e[t+3]} ${e[t+4]} ${e[t+5]}`);return u.push(`L${e[4]} ${e[5]} Z`),u.join(" ")}getOutlines(){const t=this.#xn,e=this.#yn,i=this.#_n,s=i.subarray(4,6),n=i.subarray(16,18),[r,a,o,h]=this.#dn,l=new Float64Array((this.#Pn?.length??0)+2);for(let t=0,e=l.length-2;t<e;t+=2)l[t]=(this.#Pn[t]-r)/o,l[t+1]=(this.#Pn[t+1]-a)/h;l[l.length-2]=(this.#Sn-r)/o,l[l.length-1]=(this.#En-a)/h;const[d,c,u,p]=this.#In();if(isNaN(i[6])&&!this.isEmpty()){const t=new Float64Array(36);return t.set([NaN,NaN,NaN,NaN,(i[2]-r)/o,(i[3]-a)/h,NaN,NaN,NaN,NaN,(i[4]-r)/o,(i[5]-a)/h,NaN,NaN,NaN,NaN,d,c,NaN,NaN,NaN,NaN,u,p,NaN,NaN,NaN,NaN,(i[16]-r)/o,(i[17]-a)/h,NaN,NaN,NaN,NaN,(i[14]-r)/o,(i[15]-a)/h],0),new pn(t,l,this.#dn,this.#Tn,this.#wn,this.#An)}const g=new Float64Array(this.#xn.length+24+this.#yn.length);let f=t.length;for(let e=0;e<f;e+=2)isNaN(t[e])?g[e]=g[e+1]=NaN:(g[e]=t[e],g[e+1]=t[e+1]);g.set([NaN,NaN,NaN,NaN,(s[0]-r)/o,(s[1]-a)/h,NaN,NaN,NaN,NaN,d,c,NaN,NaN,NaN,NaN,u,p,NaN,NaN,NaN,NaN,(n[0]-r)/o,(n[1]-a)/h],f),f+=24;for(let t=e.length-6;t>=6;t-=6)for(let i=0;i<6;i+=2)isNaN(e[t+i])?(g[f]=g[f+1]=NaN,f+=2):(g[f]=e[t+i],g[f+1]=e[t+i+1],f+=2);return g.set([NaN,NaN,NaN,NaN,e[4],e[5]],f),new pn(g,l,this.#dn,this.#Tn,this.#wn,this.#An)}}class pn extends dn{#dn;#Fn=null;#wn;#An;#Pn;#Tn;#Nn;constructor(t,e,i,s,n,r){super(),this.#Nn=t,this.#Pn=e,this.#dn=i,this.#Tn=s,this.#wn=n,this.#An=r,this.#On(r);const{x:a,y:o,width:h,height:l}=this.#Fn;for(let e=0,i=t.length;e<i;e+=2)t[e]=(t[e]-a)/h,t[e+1]=(t[e+1]-o)/l;for(let t=0,i=e.length;t<i;t+=2)e[t]=(e[t]-a)/h,e[t+1]=(e[t+1]-o)/l}toSVGPath(){const t=[`M${this.#Nn[4]} ${this.#Nn[5]}`];for(let e=6,i=this.#Nn.length;e<i;e+=6)isNaN(this.#Nn[e])?t.push(`L${this.#Nn[e+4]} ${this.#Nn[e+5]}`):t.push(`C${this.#Nn[e]} ${this.#Nn[e+1]} ${this.#Nn[e+2]} ${this.#Nn[e+3]} ${this.#Nn[e+4]} ${this.#Nn[e+5]}`);return t.push("Z"),t.join(" ")}serialize([t,e,i,s],n){const r=i-t,a=s-e;let o,h;switch(n){case 0:o=this.#zn(this.#Nn,t,s,r,-a),h=this.#zn(this.#Pn,t,s,r,-a);break;case 90:o=this.#Hn(this.#Nn,t,e,r,a),h=this.#Hn(this.#Pn,t,e,r,a);break;case 180:o=this.#zn(this.#Nn,i,e,-r,a),h=this.#zn(this.#Pn,i,e,-r,a);break;case 270:o=this.#Hn(this.#Nn,i,s,-r,-a),h=this.#Hn(this.#Pn,i,s,-r,-a)}return{outline:Array.from(o),points:[Array.from(h)]}}#zn(t,e,i,s,n){const r=new Float64Array(t.length);for(let a=0,o=t.length;a<o;a+=2)r[a]=e+t[a]*s,r[a+1]=i+t[a+1]*n;return r}#Hn(t,e,i,s,n){const r=new Float64Array(t.length);for(let a=0,o=t.length;a<o;a+=2)r[a]=e+t[a+1]*s,r[a+1]=i+t[a]*n;return r}#On(t){const e=this.#Nn;let i=e[4],s=e[5],n=i,r=s,a=i,o=s,h=i,l=s;const d=t?Math.max:Math.min;for(let t=6,c=e.length;t<c;t+=6){if(isNaN(e[t]))n=Math.min(n,e[t+4]),r=Math.min(r,e[t+5]),a=Math.max(a,e[t+4]),o=Math.max(o,e[t+5]),l<e[t+5]?(h=e[t+4],l=e[t+5]):l===e[t+5]&&(h=d(h,e[t+4]));else{const c=St.bezierBoundingBox(i,s,...e.slice(t,t+6));n=Math.min(n,c[0]),r=Math.min(r,c[1]),a=Math.max(a,c[2]),o=Math.max(o,c[3]),l<c[3]?(h=c[2],l=c[3]):l===c[3]&&(h=d(h,c[2]))}i=e[t+4],s=e[t+5]}const c=n-this.#wn,u=r-this.#wn,p=a-n+2*this.#wn,g=o-r+2*this.#wn;this.#Fn={x:c,y:u,width:p,height:g,lastPoint:[h,l]}}get box(){return this.#Fn}getNewOutline(t,e){const{x:i,y:s,width:n,height:r}=this.#Fn,[a,o,h,l]=this.#dn,d=n*h,c=r*l,u=i*h+a,p=s*l+o,g=new un({x:this.#Pn[0]*d+u,y:this.#Pn[1]*c+p},this.#dn,this.#Tn,t,this.#An,e??this.#wn);for(let t=2;t<this.#Pn.length;t+=2)g.add({x:this.#Pn[t]*d+u,y:this.#Pn[t+1]*c+p});return g.getOutlines()}}class gn{#ds=this.#cs.bind(this);#Bn=this.#o.bind(this);#jn=null;#Wn=null;#Un;#$n=null;#Gn=!1;#Vn=!1;#r=null;#qn;#p=null;#Xn;static get _keyboardManager(){return dt(this,"_keyboardManager",new ve([[["Escape","mac+Escape"],gn.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],gn.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],gn.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],gn.prototype._moveToPrevious],[["Home","mac+Home"],gn.prototype._moveToBeginning],[["End","mac+End"],gn.prototype._moveToEnd]]))}constructor({editor:t=null,uiManager:e=null}){t?(this.#Vn=!1,this.#Xn=w.HIGHLIGHT_COLOR,this.#r=t):(this.#Vn=!0,this.#Xn=w.HIGHLIGHT_DEFAULT_COLOR),this.#p=t?._uiManager||e,this.#qn=this.#p._eventBus,this.#Un=t?.color||this.#p?.highlightColors.values().next().value||"#FFFF98"}renderButton(){const t=this.#jn=document.createElement("button");t.className="colorPicker",t.tabIndex="0",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button"),t.setAttribute("aria-haspopup",!0);const e=this.#p._signal;t.addEventListener("click",this.#Yn.bind(this),{signal:e}),t.addEventListener("keydown",this.#ds,{signal:e});const i=this.#Wn=document.createElement("span");return i.className="swatch",i.setAttribute("aria-hidden",!0),i.style.backgroundColor=this.#Un,t.append(i),t}renderMainDropdown(){const t=this.#$n=this.#Kn();return t.setAttribute("aria-orientation","horizontal"),t.setAttribute("aria-labelledby","highlightColorPickerLabel"),t}#Kn(){const t=document.createElement("div"),e=this.#p._signal;t.addEventListener("contextmenu",se,{signal:e}),t.className="dropdown",t.role="listbox",t.setAttribute("aria-multiselectable",!1),t.setAttribute("aria-orientation","vertical"),t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown");for(const[i,s]of this.#p.highlightColors){const n=document.createElement("button");n.tabIndex="0",n.role="option",n.setAttribute("data-color",s),n.title=i,n.setAttribute("data-l10n-id",`pdfjs-editor-colorpicker-${i}`);const r=document.createElement("span");n.append(r),r.className="swatch",r.style.backgroundColor=s,n.setAttribute("aria-selected",s===this.#Un),n.addEventListener("click",this.#Qn.bind(this,s),{signal:e}),t.append(n)}return t.addEventListener("keydown",this.#ds,{signal:e}),t}#Qn(t,e){e.stopPropagation(),this.#qn.dispatch("switchannotationeditorparams",{source:this,type:this.#Xn,value:t})}_colorSelectFromKeyboard(t){if(t.target===this.#jn)return void this.#Yn(t);const e=t.target.getAttribute("data-color");e&&this.#Qn(e,t)}_moveToNext(t){this.#Jn?t.target!==this.#jn?t.target.nextSibling?.focus():this.#$n.firstChild?.focus():this.#Yn(t)}_moveToPrevious(t){t.target!==this.#$n?.firstChild&&t.target!==this.#jn?(this.#Jn||this.#Yn(t),t.target.previousSibling?.focus()):this.#Jn&&this._hideDropdownFromKeyboard()}_moveToBeginning(t){this.#Jn?this.#$n.firstChild?.focus():this.#Yn(t)}_moveToEnd(t){this.#Jn?this.#$n.lastChild?.focus():this.#Yn(t)}#cs(t){gn._keyboardManager.exec(this,t)}#Yn(t){if(this.#Jn)return void this.hideDropdown();if(this.#Gn=0===t.detail,window.addEventListener("pointerdown",this.#Bn,{signal:this.#p._signal}),this.#$n)return void this.#$n.classList.remove("hidden");const e=this.#$n=this.#Kn();this.#jn.append(e)}#o(t){this.#$n?.contains(t.target)||this.hideDropdown()}hideDropdown(){this.#$n?.classList.add("hidden"),window.removeEventListener("pointerdown",this.#Bn)}get#Jn(){return this.#$n&&!this.#$n.classList.contains("hidden")}_hideDropdownFromKeyboard(){this.#Vn||(this.#Jn?(this.hideDropdown(),this.#jn.focus({preventScroll:!0,focusVisible:this.#Gn})):this.#r?.unselect())}updateColor(t){if(this.#Wn&&(this.#Wn.style.backgroundColor=t),!this.#$n)return;const e=this.#p.highlightColors.values();for(const i of this.#$n.children)i.setAttribute("aria-selected",e.next().value===t)}destroy(){this.#jn?.remove(),this.#jn=null,this.#Wn=null,this.#$n?.remove(),this.#$n=null}}class fn extends xe{#Zn=null;#tr=0;#er;#ir=null;#n=null;#sr=null;#nr=null;#rr=0;#ar=null;#or=null;#b=null;#hr=!1;#ot=this.#lr.bind(this);#dr=null;#cr;#ur=null;#pr="";#kn;#gr="";static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=12;static _l10nPromise;static _type="highlight";static _editorType=y.HIGHLIGHT;static _freeHighlightId=-1;static _freeHighlight=null;static _freeHighlightClipId="";static get _keyboardManager(){const t=fn.prototype;return dt(this,"_keyboardManager",new ve([[["ArrowLeft","mac+ArrowLeft"],t._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],t._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],t._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],t._moveCaret,{args:[3]}]]))}constructor(t){super({...t,name:"highlightEditor"}),this.color=t.color||fn._defaultColor,this.#kn=t.thickness||fn._defaultThickness,this.#cr=t.opacity||fn._defaultOpacity,this.#er=t.boxes||null,this.#gr=t.methodOfCreation||"",this.#pr=t.text||"",this._isDraggable=!1,t.highlightId>-1?(this.#hr=!0,this.#fr(t),this.#mr()):(this.#Zn=t.anchorNode,this.#tr=t.anchorOffset,this.#nr=t.focusNode,this.#rr=t.focusOffset,this.#br(),this.#mr(),this.rotate(this.rotation))}get telemetryInitialData(){return{action:"added",type:this.#hr?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:this.#kn,methodOfCreation:this.#gr}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(t){return{numberOfColors:t.get("color").size}}#br(){const t=new ln(this.#er,.001);this.#or=t.getOutlines(),({x:this.x,y:this.y,width:this.width,height:this.height}=this.#or.box);const e=new ln(this.#er,.0025,.001,"ltr"===this._uiManager.direction);this.#sr=e.getOutlines();const{lastPoint:i}=this.#sr.box;this.#dr=[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height]}#fr({highlightOutlines:t,highlightId:e,clipPathId:i}){this.#or=t;if(this.#sr=t.getNewOutline(this.#kn/2****,.0025),e>=0)this.#b=e,this.#ir=i,this.parent.drawLayer.finalizeLine(e,t),this.#ur=this.parent.drawLayer.highlightOutline(this.#sr);else if(this.parent){const e=this.parent.viewport.rotation;this.parent.drawLayer.updateLine(this.#b,t),this.parent.drawLayer.updateBox(this.#b,fn.#vr(this.#or.box,(e-this.rotation+360)%360)),this.parent.drawLayer.updateLine(this.#ur,this.#sr),this.parent.drawLayer.updateBox(this.#ur,fn.#vr(this.#sr.box,e))}const{x:s,y:n,width:r,height:a}=t.box;switch(this.rotation){case 0:this.x=s,this.y=n,this.width=r,this.height=a;break;case 90:{const[t,e]=this.parentDimensions;this.x=n,this.y=1-s,this.width=r*e/t,this.height=a*t/e;break}case 180:this.x=1-s,this.y=1-n,this.width=r,this.height=a;break;case 270:{const[t,e]=this.parentDimensions;this.x=1-n,this.y=s,this.width=r*e/t,this.height=a*t/e;break}}const{lastPoint:o}=this.#sr.box;this.#dr=[(o[0]-s)/r,(o[1]-n)/a]}static initialize(t,e){xe.initialize(t,e),fn._defaultColor||=e.highlightColors?.values().next().value||"#fff066"}static updateDefaultParams(t,e){switch(t){case w.HIGHLIGHT_DEFAULT_COLOR:fn._defaultColor=e;break;case w.HIGHLIGHT_THICKNESS:fn._defaultThickness=e}}translateInPage(t,e){}get toolbarPosition(){return this.#dr}updateParams(t,e){switch(t){case w.HIGHLIGHT_COLOR:this.#in(e);break;case w.HIGHLIGHT_THICKNESS:this.#yr(e)}}static get defaultPropertiesToUpdate(){return[[w.HIGHLIGHT_DEFAULT_COLOR,fn._defaultColor],[w.HIGHLIGHT_THICKNESS,fn._defaultThickness]]}get propertiesToUpdate(){return[[w.HIGHLIGHT_COLOR,this.color||fn._defaultColor],[w.HIGHLIGHT_THICKNESS,this.#kn||fn._defaultThickness],[w.HIGHLIGHT_FREE,this.#hr]]}#in(t){const e=t=>{this.color=t,this.parent?.drawLayer.changeColor(this.#b,t),this.#n?.updateColor(t)},i=this.color;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:w.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(t)},!0)}#yr(t){const e=this.#kn,i=t=>{this.#kn=t,this.#wr(t)};this.addCommands({cmd:i.bind(this,t),undo:i.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:w.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"thickness_changed",thickness:t},!0)}async addEditToolbar(){const t=await super.addEditToolbar();return t?(this._uiManager.highlightColors&&(this.#n=new gn({editor:this}),t.addColorPicker(this.#n)),t):null}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(this.#Ar())}getBaseTranslation(){return[0,0]}getRect(t,e){return super.getRect(t,e,this.#Ar())}onceAdded(){this.parent.addUndoableEditor(this),this.div.focus()}remove(){this.#xr(),this._reportTelemetry({action:"deleted"}),super.remove()}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.#mr(),this.isAttachedToDOM||this.parent.add(this)))}setParent(t){let e=!1;this.parent&&!t?this.#xr():t&&(this.#mr(t),e=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(t),this.show(this._isVisible),e&&this.select()}#wr(t){if(!this.#hr)return;this.#fr({highlightOutlines:this.#or.getNewOutline(t/2)}),this.fixAndSetPosition();const[e,i]=this.parentDimensions;this.setDims(this.width*e,this.height*i)}#xr(){null!==this.#b&&this.parent&&(this.parent.drawLayer.remove(this.#b),this.#b=null,this.parent.drawLayer.remove(this.#ur),this.#ur=null)}#mr(t=this.parent){null===this.#b&&(({id:this.#b,clipPathId:this.#ir}=t.drawLayer.highlight(this.#or,this.color,this.#cr)),this.#ur=t.drawLayer.highlightOutline(this.#sr),this.#ar&&(this.#ar.style.clipPath=this.#ir))}static#vr({x:t,y:e,width:i,height:s},n){switch(n){case 90:return{x:1-e-s,y:t,width:s,height:i};case 180:return{x:1-t-i,y:1-e-s,width:i,height:s};case 270:return{x:e,y:1-t-i,width:s,height:i}}return{x:t,y:e,width:i,height:s}}rotate(t){const{drawLayer:e}=this.parent;let i;this.#hr?(t=(t-this.rotation+360)%360,i=fn.#vr(this.#or.box,t)):i=fn.#vr(this,t),e.rotate(this.#b,t),e.rotate(this.#ur,t),e.updateBox(this.#b,i),e.updateBox(this.#ur,fn.#vr(this.#sr.box,t))}render(){if(this.div)return this.div;const t=super.render();this.#pr&&(t.setAttribute("aria-label",this.#pr),t.setAttribute("role","mark")),this.#hr?t.classList.add("free"):this.div.addEventListener("keydown",this.#ot,{signal:this._uiManager._signal});const e=this.#ar=document.createElement("div");t.append(e),e.setAttribute("aria-hidden","true"),e.className="internal",e.style.clipPath=this.#ir;const[i,s]=this.parentDimensions;return this.setDims(this.width*i,this.height*s),ge(this,this.#ar,["pointerover","pointerleave"]),this.enableEditing(),t}pointerover(){this.parent.drawLayer.addClass(this.#ur,"hovered")}pointerleave(){this.parent.drawLayer.removeClass(this.#ur,"hovered")}#lr(t){fn._keyboardManager.exec(this,t)}_moveCaret(t){switch(this.parent.unselect(this),t){case 0:case 2:this.#_r(!0);break;case 1:case 3:this.#_r(!1)}}#_r(t){if(!this.#Zn)return;const e=window.getSelection();t?e.setPosition(this.#Zn,this.#tr):e.setPosition(this.#nr,this.#rr)}select(){super.select(),this.#ur&&(this.parent?.drawLayer.removeClass(this.#ur,"hovered"),this.parent?.drawLayer.addClass(this.#ur,"selected"))}unselect(){super.unselect(),this.#ur&&(this.parent?.drawLayer.removeClass(this.#ur,"selected"),this.#hr||this.#_r(!1))}get _mustFixPosition(){return!this.#hr}show(t=this._isVisible){super.show(t),this.parent&&(this.parent.drawLayer.show(this.#b,t),this.parent.drawLayer.show(this.#ur,t))}#Ar(){return this.#hr?this.rotation:0}#Sr(){if(this.#hr)return null;const[t,e]=this.pageDimensions,i=this.#er,s=new Float32Array(8*i.length);let n=0;for(const{x:r,y:a,width:o,height:h}of i){const i=r*t,l=(1-a-h)*e;s[n]=s[n+4]=i,s[n+1]=s[n+3]=l,s[n+2]=s[n+6]=i+o*t,s[n+5]=s[n+7]=l+h*e,n+=8}return s}#Er(t){return this.#or.serialize(t,this.#Ar())}static startHighlighting(t,e,{target:i,x:s,y:n}){const{x:r,y:a,width:o,height:h}=i.getBoundingClientRect(),l=e=>{this.#Cr(t,e)},d=t._signal,c={capture:!0,passive:!1,signal:d},u=t=>{t.preventDefault(),t.stopPropagation()},p=e=>{i.removeEventListener("pointermove",l),window.removeEventListener("blur",p),window.removeEventListener("pointerup",p),window.removeEventListener("pointerdown",u,c),window.removeEventListener("contextmenu",se),this.#Mr(t,e)};window.addEventListener("blur",p,{signal:d}),window.addEventListener("pointerup",p,{signal:d}),window.addEventListener("pointerdown",u,c),window.addEventListener("contextmenu",se,{signal:d}),i.addEventListener("pointermove",l,{signal:d}),this._freeHighlight=new un({x:s,y:n},[r,a,o,h],t.scale,this._defaultThickness/2,e,.001),({id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=t.drawLayer.highlight(this._freeHighlight,this._defaultColor,this._defaultOpacity,!0))}static#Cr(t,e){this._freeHighlight.add(e)&&t.drawLayer.updatePath(this._freeHighlightId,this._freeHighlight)}static#Mr(t,e){this._freeHighlight.isEmpty()?t.drawLayer.removeFreeHighlight(this._freeHighlightId):t.createAndAddNewEditor(e,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"}),this._freeHighlightId=-1,this._freeHighlight=null,this._freeHighlightClipId=""}static deserialize(t,e,i){const s=super.deserialize(t,e,i),{rect:[n,r,a,o],color:h,quadPoints:l}=t;s.color=St.makeHexColor(...h),s.#cr=t.opacity;const[d,c]=s.pageDimensions;s.width=(a-n)/d,s.height=(o-r)/c;const u=s.#er=[];for(let t=0;t<l.length;t+=8)u.push({x:(l[4]-a)/d,y:(o-(1-l[t+5]))/c,width:(l[t+2]-l[t])/d,height:(l[t+5]-l[t+1])/c});return s.#br(),s}serialize(t=!1){if(this.isEmpty()||t)return null;const e=this.getRect(0,0),i=xe._colorManager.convert(this.color);return{annotationType:y.HIGHLIGHT,color:i,opacity:this.#cr,thickness:this.#kn,quadPoints:this.#Sr(),outlines:this.#Er(e),pageIndex:this.pageIndex,rect:e,rotation:this.#Ar(),structTreeParentId:this._structTreeParentId}}static canCreateNewEmptyEditor(){return!1}}class mn extends xe{#Tr=0;#kr=0;#Pr=this.canvasPointermove.bind(this);#Rr=this.canvasPointerleave.bind(this);#Dr=this.canvasPointerup.bind(this);#Lr=this.canvasPointerdown.bind(this);#Ir=null;#Fr=new Path2D;#Nr=!1;#Or=!1;#zr=!1;#Hr=null;#Br=0;#jr=0;#Wr=null;static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=1;static _type="ink";static _editorType=y.INK;constructor(t){super({...t,name:"inkEditor"}),this.color=t.color||null,this.thickness=t.thickness||null,this.opacity=t.opacity||null,this.paths=[],this.bezierPath2D=[],this.allRawPaths=[],this.currentPath=[],this.scaleFactor=1,this.translationX=this.translationY=0,this.x=0,this.y=0,this._willKeepAspectRatio=!0}static initialize(t,e){xe.initialize(t,e)}static updateDefaultParams(t,e){switch(t){case w.INK_THICKNESS:mn._defaultThickness=e;break;case w.INK_COLOR:mn._defaultColor=e;break;case w.INK_OPACITY:mn._defaultOpacity=e/100}}updateParams(t,e){switch(t){case w.INK_THICKNESS:this.#yr(e);break;case w.INK_COLOR:this.#in(e);break;case w.INK_OPACITY:this.#Ur(e)}}static get defaultPropertiesToUpdate(){return[[w.INK_THICKNESS,mn._defaultThickness],[w.INK_COLOR,mn._defaultColor||xe._defaultLineColor],[w.INK_OPACITY,Math.round(100*mn._defaultOpacity)]]}get propertiesToUpdate(){return[[w.INK_THICKNESS,this.thickness||mn._defaultThickness],[w.INK_COLOR,this.color||mn._defaultColor||xe._defaultLineColor],[w.INK_OPACITY,Math.round(100*(this.opacity??mn._defaultOpacity))]]}#yr(t){const e=t=>{this.thickness=t,this.#$r()},i=this.thickness;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:w.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})}#in(t){const e=t=>{this.color=t,this.#Gr()},i=this.color;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:w.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})}#Ur(t){const e=t=>{this.opacity=t,this.#Gr()};t/=100;const i=this.opacity;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:w.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.canvas||(this.#Vr(),this.#qr()),this.isAttachedToDOM||(this.parent.add(this),this.#Xr()),this.#$r()))}remove(){null!==this.canvas&&(this.isEmpty()||this.commit(),this.canvas.width=this.canvas.height=0,this.canvas.remove(),this.canvas=null,this.#Ir&&(clearTimeout(this.#Ir),this.#Ir=null),this.#Hr?.disconnect(),this.#Hr=null,super.remove())}setParent(t){!this.parent&&t?this._uiManager.removeShouldRescale(this):this.parent&&null===t&&this._uiManager.addShouldRescale(this),super.setParent(t)}onScaleChanging(){const[t,e]=this.parentDimensions,i=this.width*t,s=this.height*e;this.setDimensions(i,s)}enableEditMode(){this.#Nr||null===this.canvas||(super.enableEditMode(),this._isDraggable=!1,this.canvas.addEventListener("pointerdown",this.#Lr,{signal:this._uiManager._signal}))}disableEditMode(){this.isInEditMode()&&null!==this.canvas&&(super.disableEditMode(),this._isDraggable=!this.isEmpty(),this.div.classList.remove("editing"),this.canvas.removeEventListener("pointerdown",this.#Lr))}onceAdded(){this._isDraggable=!this.isEmpty()}isEmpty(){return 0===this.paths.length||1===this.paths.length&&0===this.paths[0].length}#Yr(){const{parentRotation:t,parentDimensions:[e,i]}=this;switch(t){case 90:return[0,i,i,e];case 180:return[e,i,e,i];case 270:return[e,0,i,e];default:return[0,0,e,i]}}#Kr(){const{ctx:t,color:e,opacity:i,thickness:s,parentScale:n,scaleFactor:r}=this;t.lineWidth=s*n/r,t.lineCap="round",t.lineJoin="round",t.miterLimit=10,t.strokeStyle=`${e}${function(t){return Math.round(Math.min(255,Math.max(1,255*t))).toString(16).padStart(2,"0")}(i)}`}#Qr(t,e){const i=this._uiManager._signal;this.canvas.addEventListener("contextmenu",se,{signal:i}),this.canvas.addEventListener("pointerleave",this.#Rr,{signal:i}),this.canvas.addEventListener("pointermove",this.#Pr,{signal:i}),this.canvas.addEventListener("pointerup",this.#Dr,{signal:i}),this.canvas.removeEventListener("pointerdown",this.#Lr),this.isEditing=!0,this.#zr||(this.#zr=!0,this.#Xr(),this.thickness||=mn._defaultThickness,this.color||=mn._defaultColor||xe._defaultLineColor,this.opacity??=mn._defaultOpacity),this.currentPath.push([t,e]),this.#Or=!1,this.#Kr(),this.#Wr=()=>{this.#Jr(),this.#Wr&&window.requestAnimationFrame(this.#Wr)},window.requestAnimationFrame(this.#Wr)}#Zr(t,e){const[i,s]=this.currentPath.at(-1);if(this.currentPath.length>1&&t===i&&e===s)return;const n=this.currentPath;let r=this.#Fr;if(n.push([t,e]),this.#Or=!0,n.length<=2)return r.moveTo(...n[0]),void r.lineTo(t,e);3===n.length&&(this.#Fr=r=new Path2D,r.moveTo(...n[0])),this.#ta(r,...n.at(-3),...n.at(-2),t,e)}#ea(){if(0===this.currentPath.length)return;const t=this.currentPath.at(-1);this.#Fr.lineTo(...t)}#ia(t,e){let i;if(this.#Wr=null,t=Math.min(Math.max(t,0),this.canvas.width),e=Math.min(Math.max(e,0),this.canvas.height),this.#Zr(t,e),this.#ea(),1!==this.currentPath.length)i=this.#sa();else{const s=[t,e];i=[[s,s.slice(),s.slice(),s]]}const s=this.#Fr,n=this.currentPath;this.currentPath=[],this.#Fr=new Path2D;this.addCommands({cmd:()=>{this.allRawPaths.push(n),this.paths.push(i),this.bezierPath2D.push(s),this._uiManager.rebuild(this)},undo:()=>{this.allRawPaths.pop(),this.paths.pop(),this.bezierPath2D.pop(),0===this.paths.length?this.remove():(this.canvas||(this.#Vr(),this.#qr()),this.#$r())},mustExec:!0})}#Jr(){if(!this.#Or)return;this.#Or=!1;const t=Math.ceil(this.thickness*this.parentScale),e=this.currentPath.slice(-3),i=e.map((t=>t[0])),s=e.map((t=>t[1])),{ctx:n}=(Math.min(...i),Math.max(...i),Math.min(...s),Math.max(...s),this);n.save(),n.clearRect(0,0,this.canvas.width,this.canvas.height);for(const t of this.bezierPath2D)n.stroke(t);n.stroke(this.#Fr),n.restore()}#ta(t,e,i,s,n,r,a){const o=(e+s)/2,h=(i+n)/2,l=(s+r)/2,d=(n+a)/2;t.bezierCurveTo(o+2*(s-o)/3,h+2*(n-h)/3,l+2*(s-l)/3,d+2*(n-d)/3,l,d)}#sa(){const t=this.currentPath;if(t.length<=2)return[[t[0],t[0],t.at(-1),t.at(-1)]];const e=[];let i,[s,n]=t[0];for(i=1;i<t.length-2;i++){const[r,a]=t[i],[o,h]=t[i+1],l=(r+o)/2,d=(a+h)/2,c=[s+2*(r-s)/3,n+2*(a-n)/3],u=[l+2*(r-l)/3,d+2*(a-d)/3];e.push([[s,n],c,u,[l,d]]),[s,n]=[l,d]}const[r,a]=t[i],[o,h]=t[i+1],l=[s+2*(r-s)/3,n+2*(a-n)/3],d=[o+2*(r-o)/3,h+2*(a-h)/3];return e.push([[s,n],l,d,[o,h]]),e}#Gr(){if(this.isEmpty())return void this.#na();this.#Kr();const{canvas:t,ctx:e}=this;e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,t.width,t.height),this.#na();for(const t of this.bezierPath2D)e.stroke(t)}commit(){this.#Nr||(super.commit(),this.isEditing=!1,this.disableEditMode(),this.setInForeground(),this.#Nr=!0,this.div.classList.add("disabled"),this.#$r(!0),this.select(),this.parent.addInkEditorIfNeeded(!0),this.moveInDOM(),this.div.focus({preventScroll:!0}))}focusin(t){this._focusEventsAllowed&&(super.focusin(t),this.enableEditMode())}canvasPointerdown(t){0===t.button&&this.isInEditMode()&&!this.#Nr&&(this.setInForeground(),t.preventDefault(),this.div.contains(document.activeElement)||this.div.focus({preventScroll:!0}),this.#Qr(t.offsetX,t.offsetY))}canvasPointermove(t){t.preventDefault(),this.#Zr(t.offsetX,t.offsetY)}canvasPointerup(t){t.preventDefault(),this.#ra(t)}canvasPointerleave(t){this.#ra(t)}#ra(t){this.canvas.removeEventListener("pointerleave",this.#Rr),this.canvas.removeEventListener("pointermove",this.#Pr),this.canvas.removeEventListener("pointerup",this.#Dr),this.canvas.addEventListener("pointerdown",this.#Lr,{signal:this._uiManager._signal}),this.#Ir&&clearTimeout(this.#Ir),this.#Ir=setTimeout((()=>{this.#Ir=null,this.canvas.removeEventListener("contextmenu",se)}),10),this.#ia(t.offsetX,t.offsetY),this.addToAnnotationStorage(),this.setInBackground()}#Vr(){this.canvas=document.createElement("canvas"),this.canvas.width=this.canvas.height=0,this.canvas.className="inkEditorCanvas",this.canvas.setAttribute("data-l10n-id","pdfjs-ink-canvas"),this.div.append(this.canvas),this.ctx=this.canvas.getContext("2d")}#qr(){this.#Hr=new ResizeObserver((t=>{const e=t[0].contentRect;e.width&&e.height&&this.setDimensions(e.width,e.height)})),this.#Hr.observe(this.div),this._uiManager._signal.addEventListener("abort",(()=>{this.#Hr?.disconnect(),this.#Hr=null}),{once:!0})}get isResizable(){return!this.isEmpty()&&this.#Nr}render(){if(this.div)return this.div;let t,e;this.width&&(t=this.x,e=this.y),super.render(),this.div.setAttribute("data-l10n-id","pdfjs-ink");const[i,s,n,r]=this.#Yr();if(this.setAt(i,s,0,0),this.setDims(n,r),this.#Vr(),this.width){const[i,s]=this.parentDimensions;this.setAspectRatio(this.width*i,this.height*s),this.setAt(t*i,e*s,this.width*i,this.height*s),this.#zr=!0,this.#Xr(),this.setDims(this.width*i,this.height*s),this.#Gr(),this.div.classList.add("disabled")}else this.div.classList.add("editing"),this.enableEditMode();return this.#qr(),this.div}#Xr(){if(!this.#zr)return;const[t,e]=this.parentDimensions;this.canvas.width=Math.ceil(this.width*t),this.canvas.height=Math.ceil(this.height*e),this.#na()}setDimensions(t,e){const i=Math.round(t),s=Math.round(e);if(this.#Br===i&&this.#jr===s)return;this.#Br=i,this.#jr=s,this.canvas.style.visibility="hidden";const[n,r]=this.parentDimensions;this.width=t/n,this.height=e/r,this.fixAndSetPosition(),this.#Nr&&this.#aa(t,e),this.#Xr(),this.#Gr(),this.canvas.style.visibility="visible",this.fixDims()}#aa(t,e){const i=this.#oa(),s=(t-i)/this.#kr,n=(e-i)/this.#Tr;this.scaleFactor=Math.min(s,n)}#na(){const t=this.#oa()/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+t,this.translationY*this.scaleFactor+t)}static#ha(t){const e=new Path2D;for(let i=0,s=t.length;i<s;i++){const[s,n,r,a]=t[i];0===i&&e.moveTo(...s),e.bezierCurveTo(n[0],n[1],r[0],r[1],a[0],a[1])}return e}static#la(t,e,i){const[s,n,r,a]=e;switch(i){case 0:for(let e=0,i=t.length;e<i;e+=2)t[e]+=s,t[e+1]=a-t[e+1];break;case 90:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=t[e+1]+s,t[e+1]=i+n}break;case 180:for(let e=0,i=t.length;e<i;e+=2)t[e]=r-t[e],t[e+1]+=n;break;case 270:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=r-t[e+1],t[e+1]=a-i}break;default:throw new Error("Invalid rotation")}return t}static#da(t,e,i){const[s,n,r,a]=e;switch(i){case 0:for(let e=0,i=t.length;e<i;e+=2)t[e]-=s,t[e+1]=a-t[e+1];break;case 90:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=t[e+1]-n,t[e+1]=i-s}break;case 180:for(let e=0,i=t.length;e<i;e+=2)t[e]=r-t[e],t[e+1]-=n;break;case 270:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=a-t[e+1],t[e+1]=r-i}break;default:throw new Error("Invalid rotation")}return t}#ca(t,e,i,s){const n=[],r=this.thickness/2,a=t*e+r,o=t*i+r;for(const e of this.paths){const i=[],r=[];for(let s=0,n=e.length;s<n;s++){const[h,l,d,c]=e[s];if(h[0]===c[0]&&h[1]===c[1]&&1===n){const e=t*h[0]+a,s=t*h[1]+o;i.push(e,s),r.push(e,s);break}const u=t*h[0]+a,p=t*h[1]+o,g=t*l[0]+a,f=t*l[1]+o,m=t*d[0]+a,b=t*d[1]+o,v=t*c[0]+a,y=t*c[1]+o;0===s&&(i.push(u,p),r.push(u,p)),i.push(g,f,m,b,v,y),r.push(g,f),s===n-1&&r.push(v,y)}n.push({bezier:mn.#la(i,s,this.rotation),points:mn.#la(r,s,this.rotation)})}return n}#ua(){let t=1/0,e=-1/0,i=1/0,s=-1/0;for(const n of this.paths)for(const[r,a,o,h]of n){const n=St.bezierBoundingBox(...r,...a,...o,...h);t=Math.min(t,n[0]),i=Math.min(i,n[1]),e=Math.max(e,n[2]),s=Math.max(s,n[3])}return[t,i,e,s]}#oa(){return this.#Nr?Math.ceil(this.thickness*this.parentScale):0}#$r(t=!1){if(this.isEmpty())return;if(!this.#Nr)return void this.#Gr();const e=this.#ua(),i=this.#oa();this.#kr=Math.max(xe.MIN_SIZE,e[2]-e[0]),this.#Tr=Math.max(xe.MIN_SIZE,e[3]-e[1]);const s=Math.ceil(i+this.#kr*this.scaleFactor),n=Math.ceil(i+this.#Tr*this.scaleFactor),[r,a]=this.parentDimensions;this.width=s/r,this.height=n/a,this.setAspectRatio(s,n);const o=this.translationX,h=this.translationY;this.translationX=-e[0],this.translationY=-e[1],this.#Xr(),this.#Gr(),this.#Br=s,this.#jr=n,this.setDims(s,n);const l=t?i/this.scaleFactor/2:0;this.translate(o-this.translationX-l,h-this.translationY-l)}static deserialize(t,e,i){if(t instanceof Js)return null;const s=super.deserialize(t,e,i);s.thickness=t.thickness,s.color=St.makeHexColor(...t.color),s.opacity=t.opacity;const[n,r]=s.pageDimensions,a=s.width*n,o=s.height*r,h=s.parentScale,l=t.thickness/2;s.#Nr=!0,s.#Br=Math.round(a),s.#jr=Math.round(o);const{paths:d,rect:c,rotation:u}=t;for(let{bezier:t}of d){t=mn.#da(t,c,u);const e=[];s.paths.push(e);let i=h*(t[0]-l),n=h*(t[1]-l);for(let s=2,r=t.length;s<r;s+=6){const r=h*(t[s]-l),a=h*(t[s+1]-l),o=h*(t[s+2]-l),d=h*(t[s+3]-l),c=h*(t[s+4]-l),u=h*(t[s+5]-l);e.push([[i,n],[r,a],[o,d],[c,u]]),i=c,n=u}const r=this.#ha(e);s.bezierPath2D.push(r)}const p=s.#ua();return s.#kr=Math.max(xe.MIN_SIZE,p[2]-p[0]),s.#Tr=Math.max(xe.MIN_SIZE,p[3]-p[1]),s.#aa(a,o),s}serialize(){if(this.isEmpty())return null;const t=this.getRect(0,0),e=xe._colorManager.convert(this.ctx.strokeStyle);return{annotationType:y.INK,color:e,thickness:this.thickness,opacity:this.opacity,paths:this.#ca(this.scaleFactor/this.parentScale,this.translationX,this.translationY,t),pageIndex:this.pageIndex,rect:t,rotation:this.rotation,structTreeParentId:this._structTreeParentId}}}class bn extends xe{#pa=null;#ga=null;#fa=null;#ma=null;#ba=null;#va="";#ya=null;#Hr=null;#wa=null;#Aa=!1;#xa=!1;static _type="stamp";static _editorType=y.STAMP;constructor(t){super({...t,name:"stampEditor"}),this.#ma=t.bitmapUrl,this.#ba=t.bitmapFile}static initialize(t,e){xe.initialize(t,e)}static get supportedTypes(){return dt(this,"supportedTypes",["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"].map((t=>`image/${t}`)))}static get supportedTypesStr(){return dt(this,"supportedTypesStr",this.supportedTypes.join(","))}static isHandlingMimeForPasting(t){return this.supportedTypes.includes(t)}static paste(t,e){e.pasteEditor(y.STAMP,{bitmapFile:t.getAsFile()})}#_a(t,e=!1){t?(this.#pa=t.bitmap,e||(this.#ga=t.id,this.#Aa=t.isSvg),t.file&&(this.#va=t.file.name),this.#Vr()):this.remove()}#Sa(){this.#fa=null,this._uiManager.enableWaiting(!1),this.#ya&&this.div.focus()}#Ea(){if(this.#ga)return this._uiManager.enableWaiting(!0),void this._uiManager.imageManager.getFromId(this.#ga).then((t=>this.#_a(t,!0))).finally((()=>this.#Sa()));if(this.#ma){const t=this.#ma;return this.#ma=null,this._uiManager.enableWaiting(!0),void(this.#fa=this._uiManager.imageManager.getFromUrl(t).then((t=>this.#_a(t))).finally((()=>this.#Sa())))}if(this.#ba){const t=this.#ba;return this.#ba=null,this._uiManager.enableWaiting(!0),void(this.#fa=this._uiManager.imageManager.getFromFile(t).then((t=>this.#_a(t))).finally((()=>this.#Sa())))}const t=document.createElement("input");t.type="file",t.accept=bn.supportedTypesStr;const e=this._uiManager._signal;this.#fa=new Promise((i=>{t.addEventListener("change",(async()=>{if(t.files&&0!==t.files.length){this._uiManager.enableWaiting(!0);const e=await this._uiManager.imageManager.getFromFile(t.files[0]);this.#_a(e)}else this.remove();i()}),{signal:e}),t.addEventListener("cancel",(()=>{this.remove(),i()}),{signal:e})})).finally((()=>this.#Sa())),t.click()}remove(){this.#ga&&(this.#pa=null,this._uiManager.imageManager.deleteId(this.#ga),this.#ya?.remove(),this.#ya=null,this.#Hr?.disconnect(),this.#Hr=null,this.#wa&&(clearTimeout(this.#wa),this.#wa=null)),super.remove()}rebuild(){this.parent?(super.rebuild(),null!==this.div&&(this.#ga&&null===this.#ya&&this.#Ea(),this.isAttachedToDOM||this.parent.add(this))):this.#ga&&this.#Ea()}onceAdded(){this._isDraggable=!0,this.div.focus()}isEmpty(){return!(this.#fa||this.#pa||this.#ma||this.#ba||this.#ga)}get isResizable(){return!0}render(){if(this.div)return this.div;let t,e;if(this.width&&(t=this.x,e=this.y),super.render(),this.div.hidden=!0,this.addAltTextButton(),this.#pa?this.#Vr():this.#Ea(),this.width){const[i,s]=this.parentDimensions;this.setAt(t*i,e*s,this.width*i,this.height*s)}return this.div}#Vr(){const{div:t}=this;let{width:e,height:i}=this.#pa;const[s,n]=this.pageDimensions,r=.75;if(this.width)e=this.width*s,i=this.height*n;else if(e>r*s||i>r*n){const t=Math.min(r*s/e,r*n/i);e*=t,i*=t}const[a,o]=this.parentDimensions;this.setDims(e*a/s,i*o/n),this._uiManager.enableWaiting(!1);const h=this.#ya=document.createElement("canvas");t.append(h),t.hidden=!1,this.#Ca(e,i),this.#qr(),this.#xa||(this.parent.addUndoableEditor(this),this.#xa=!0),this._reportTelemetry({action:"inserted_image"}),this.#va&&h.setAttribute("aria-label",this.#va)}#Ma(t,e){const[i,s]=this.parentDimensions;this.width=t/i,this.height=e/s,this.setDims(t,e),this._initialOptions?.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,null!==this.#wa&&clearTimeout(this.#wa);this.#wa=setTimeout((()=>{this.#wa=null,this.#Ca(t,e)}),200)}#Ta(t,e){const{width:i,height:s}=this.#pa;let n=i,r=s,a=this.#pa;for(;n>2*t||r>2*e;){const i=n,s=r;n>2*t&&(n=n>=16384?Math.floor(n/2)-1:Math.ceil(n/2)),r>2*e&&(r=r>=16384?Math.floor(r/2)-1:Math.ceil(r/2));const o=new OffscreenCanvas(n,r);o.getContext("2d").drawImage(a,0,0,i,s,0,0,n,r),a=o.transferToImageBitmap()}return a}#Ca(t,e){t=Math.ceil(t),e=Math.ceil(e);const i=this.#ya;if(!i||i.width===t&&i.height===e)return;i.width=t,i.height=e;const s=this.#Aa?this.#pa:this.#Ta(t,e);if(this._uiManager.hasMLManager&&!this.hasAltText()){const i=new OffscreenCanvas(t,e).getContext("2d");i.drawImage(s,0,0,s.width,s.height,0,0,t,e),this._uiManager.mlGuess({service:"image-to-text",request:{data:i.getImageData(0,0,t,e).data,width:t,height:e,channels:4}}).then((t=>{const e=t?.output||"";this.parent&&e&&!this.hasAltText()&&(this.altTextData={altText:e,decorative:!1})}))}const n=i.getContext("2d");n.filter=this._uiManager.hcmFilter,n.drawImage(s,0,0,s.width,s.height,0,0,t,e)}getImageForAltText(){return this.#ya}#ka(t){if(t){if(this.#Aa){const t=this._uiManager.imageManager.getSvgUrl(this.#ga);if(t)return t}const t=document.createElement("canvas");({width:t.width,height:t.height}=this.#pa);return t.getContext("2d").drawImage(this.#pa,0,0),t.toDataURL()}if(this.#Aa){const[t,e]=this.pageDimensions,i=Math.round(this.width*t*$t.PDF_TO_CSS_UNITS),s=Math.round(this.height*e*$t.PDF_TO_CSS_UNITS),n=new OffscreenCanvas(i,s);return n.getContext("2d").drawImage(this.#pa,0,0,this.#pa.width,this.#pa.height,0,0,i,s),n.transferToImageBitmap()}return structuredClone(this.#pa)}#qr(){this._uiManager._signal&&(this.#Hr=new ResizeObserver((t=>{const e=t[0].contentRect;e.width&&e.height&&this.#Ma(e.width,e.height)})),this.#Hr.observe(this.div),this._uiManager._signal.addEventListener("abort",(()=>{this.#Hr?.disconnect(),this.#Hr=null}),{once:!0}))}static deserialize(t,e,i){if(t instanceof nn)return null;const s=super.deserialize(t,e,i),{rect:n,bitmapUrl:r,bitmapId:a,isSvg:o,accessibilityData:h}=t;a&&i.imageManager.isValidId(a)?s.#ga=a:s.#ma=r,s.#Aa=o;const[l,d]=s.pageDimensions;return s.width=(n[2]-n[0])/l,s.height=(n[3]-n[1])/d,h&&(s.altTextData=h),s}serialize(t=!1,e=null){if(this.isEmpty())return null;const i={annotationType:y.STAMP,bitmapId:this.#ga,pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:this.#Aa,structTreeParentId:this._structTreeParentId};if(t)return i.bitmapUrl=this.#ka(!0),i.accessibilityData=this.altTextData,i;const{decorative:s,altText:n}=this.altTextData;if(!s&&n&&(i.accessibilityData={type:"Figure",alt:n}),null===e)return i;e.stamps||=new Map;const r=this.#Aa?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(e.stamps.has(this.#ga)){if(this.#Aa){const t=e.stamps.get(this.#ga);r>t.area&&(t.area=r,t.serialized.bitmap.close(),t.serialized.bitmap=this.#ka(!1))}}else e.stamps.set(this.#ga,{area:r,serialized:i}),i.bitmap=this.#ka(!1);return i}}class vn{#Ws;#Pa=!1;#Ra=null;#Da=null;#La=null;#Ia=null;#Fa=null;#Na=new Map;#Oa=!1;#za=!1;#Ha=!1;#Ba=null;#p;static _initialized=!1;static#O=new Map([hn,mn,bn,fn].map((t=>[t._editorType,t])));constructor({uiManager:t,pageIndex:e,div:i,accessibilityManager:s,annotationLayer:n,drawLayer:r,textLayer:a,viewport:o,l10n:h}){const l=[...vn.#O.values()];if(!vn._initialized){vn._initialized=!0;for(const e of l)e.initialize(h,t)}t.registerEditorTypes(l),this.#p=t,this.pageIndex=e,this.div=i,this.#Ws=s,this.#Ra=n,this.viewport=o,this.#Ba=a,this.drawLayer=r,this.#p.addLayer(this)}get isEmpty(){return 0===this.#Na.size}get isInvisible(){return this.isEmpty&&this.#p.getMode()===y.NONE}updateToolbar(t){this.#p.updateToolbar(t)}updateMode(t=this.#p.getMode()){switch(this.#ja(),t){case y.NONE:return this.disableTextSelection(),this.togglePointerEvents(!1),this.toggleAnnotationLayerPointerEvents(!0),void this.disableClick();case y.INK:this.addInkEditorIfNeeded(!1),this.disableTextSelection(),this.togglePointerEvents(!0),this.disableClick();break;case y.HIGHLIGHT:this.enableTextSelection(),this.togglePointerEvents(!1),this.disableClick();break;default:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const i of vn.#O.values())e.toggle(`${i._type}Editing`,t===i._editorType);this.div.hidden=!1}hasTextLayer(t){return t===this.#Ba?.div}addInkEditorIfNeeded(t){if(this.#p.getMode()!==y.INK)return;if(!t)for(const t of this.#Na.values())if(t.isEmpty())return void t.setInBackground();this.createAndAddNewEditor({offsetX:0,offsetY:0},!1).setInBackground()}setEditingState(t){this.#p.setEditingState(t)}addCommands(t){this.#p.addCommands(t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){this.#Ra?.div.classList.toggle("disabled",!t)}enable(){this.div.tabIndex=0,this.togglePointerEvents(!0);const t=new Set;for(const e of this.#Na.values())e.enableEditing(),e.show(!0),e.annotationElementId&&(this.#p.removeChangedExistingAnnotation(e),t.add(e.annotationElementId));if(!this.#Ra)return;const e=this.#Ra.getEditableAnnotations();for(const i of e){if(i.hide(),this.#p.isDeletedAnnotationElement(i.data.id))continue;if(t.has(i.data.id))continue;const e=this.deserialize(i);e&&(this.addOrRebuild(e),e.enableEditing())}}disable(){this.#Ha=!0,this.div.tabIndex=-1,this.togglePointerEvents(!1);const t=new Map,e=new Map;for(const i of this.#Na.values())i.disableEditing(),i.annotationElementId&&(null===i.serialize()?(e.set(i.annotationElementId,i),this.getEditableAnnotation(i.annotationElementId)?.show(),i.remove()):t.set(i.annotationElementId,i));if(this.#Ra){const i=this.#Ra.getEditableAnnotations();for(const s of i){const{id:i}=s.data;if(this.#p.isDeletedAnnotationElement(i))continue;let n=e.get(i);n?(n.resetAnnotationElement(s),n.show(!1),s.show()):(n=t.get(i),n&&(this.#p.addChangedExistingAnnotation(n),n.renderAnnotationElement(s),n.show(!1)),s.show())}}this.#ja(),this.isEmpty&&(this.div.hidden=!0);const{classList:i}=this.div;for(const t of vn.#O.values())i.remove(`${t._type}Editing`);this.disableTextSelection(),this.toggleAnnotationLayerPointerEvents(!0),this.#Ha=!1}getEditableAnnotation(t){return this.#Ra?.getEditableAnnotation(t)||null}setActiveEditor(t){this.#p.getActive()!==t&&this.#p.setActiveEditor(t)}enableTextSelection(){this.div.tabIndex=-1,this.#Ba?.div&&!this.#Ia&&(this.#Ia=this.#Wa.bind(this),this.#Ba.div.addEventListener("pointerdown",this.#Ia,{signal:this.#p._signal}),this.#Ba.div.classList.add("highlighting"))}disableTextSelection(){this.div.tabIndex=0,this.#Ba?.div&&this.#Ia&&(this.#Ba.div.removeEventListener("pointerdown",this.#Ia),this.#Ia=null,this.#Ba.div.classList.remove("highlighting"))}#Wa(t){if(this.#p.unselectAll(),t.target===this.#Ba.div){const{isMac:e}=xt.platform;if(0!==t.button||t.ctrlKey&&e)return;this.#p.showAllEditors("highlight",!0,!0),this.#Ba.div.classList.add("free"),fn.startHighlighting(this,"ltr"===this.#p.direction,t),this.#Ba.div.addEventListener("pointerup",(()=>{this.#Ba.div.classList.remove("free")}),{once:!0,signal:this.#p._signal}),t.preventDefault()}}enableClick(){if(this.#La)return;const t=this.#p._signal;this.#La=this.pointerdown.bind(this),this.#Da=this.pointerup.bind(this),this.div.addEventListener("pointerdown",this.#La,{signal:t}),this.div.addEventListener("pointerup",this.#Da,{signal:t})}disableClick(){this.#La&&(this.div.removeEventListener("pointerdown",this.#La),this.div.removeEventListener("pointerup",this.#Da),this.#La=null,this.#Da=null)}attach(t){this.#Na.set(t.id,t);const{annotationElementId:e}=t;e&&this.#p.isDeletedAnnotationElement(e)&&this.#p.removeDeletedAnnotationElement(t)}detach(t){this.#Na.delete(t.id),this.#Ws?.removePointerInTextLayer(t.contentDiv),!this.#Ha&&t.annotationElementId&&this.#p.addDeletedAnnotationElement(t)}remove(t){this.detach(t),this.#p.removeEditor(t),t.div.remove(),t.isAttachedToDOM=!1,this.#za||this.addInkEditorIfNeeded(!1)}changeParent(t){t.parent!==this&&(t.parent&&t.annotationElementId&&(this.#p.addDeletedAnnotationElement(t.annotationElementId),xe.deleteAnnotationElement(t),t.annotationElementId=null),this.attach(t),t.parent?.detach(t),t.setParent(this),t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div)))}add(t){if(t.parent!==this||!t.isAttachedToDOM){if(this.changeParent(t),this.#p.addEditor(t),this.attach(t),!t.isAttachedToDOM){const e=t.render();this.div.append(e),t.isAttachedToDOM=!0}t.fixAndSetPosition(),t.onceAdded(),this.#p.addToAnnotationStorage(t),t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;const{activeElement:e}=document;t.div.contains(e)&&!this.#Fa&&(t._focusEventsAllowed=!1,this.#Fa=setTimeout((()=>{this.#Fa=null,t.div.contains(document.activeElement)?t._focusEventsAllowed=!0:(t.div.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0,signal:this.#p._signal}),e.focus())}),0)),t._structTreeParentId=this.#Ws?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?(t.parent||=this,t.rebuild(),t.show()):this.add(t)}addUndoableEditor(t){this.addCommands({cmd:()=>t._uiManager.rebuild(t),undo:()=>{t.remove()},mustExec:!1})}getNextId(){return this.#p.getId()}get#Ua(){return vn.#O.get(this.#p.getMode())}get _signal(){return this.#p._signal}#$a(t){const e=this.#Ua;return e?new e.prototype.constructor(t):null}canCreateNewEmptyEditor(){return this.#Ua?.canCreateNewEmptyEditor()}pasteEditor(t,e){this.#p.updateToolbar(t),this.#p.updateMode(t);const{offsetX:i,offsetY:s}=this.#Ga(),n=this.getNextId(),r=this.#$a({parent:this,id:n,x:i,y:s,uiManager:this.#p,isCentered:!0,...e});r&&this.add(r)}deserialize(t){return vn.#O.get(t.annotationType??t.annotationEditorType)?.deserialize(t,this,this.#p)||null}createAndAddNewEditor(t,e,i={}){const s=this.getNextId(),n=this.#$a({parent:this,id:s,x:t.offsetX,y:t.offsetY,uiManager:this.#p,isCentered:e,...i});return n&&this.add(n),n}#Ga(){const{x:t,y:e,width:i,height:s}=this.div.getBoundingClientRect(),n=Math.max(0,t),r=Math.max(0,e),a=(n+Math.min(window.innerWidth,t+i))/2-t,o=(r+Math.min(window.innerHeight,e+s))/2-e,[h,l]=this.viewport.rotation%180==0?[a,o]:[o,a];return{offsetX:h,offsetY:l}}addNewEditor(){this.createAndAddNewEditor(this.#Ga(),!0)}setSelected(t){this.#p.setSelected(t)}toggleSelected(t){this.#p.toggleSelected(t)}isSelected(t){return this.#p.isSelected(t)}unselect(t){this.#p.unselect(t)}pointerup(t){const{isMac:e}=xt.platform;0!==t.button||t.ctrlKey&&e||t.target===this.div&&this.#Oa&&(this.#Oa=!1,this.#Pa?this.#p.getMode()!==y.STAMP?this.createAndAddNewEditor(t,!1):this.#p.unselectAll():this.#Pa=!0)}pointerdown(t){if(this.#p.getMode()===y.HIGHLIGHT&&this.enableTextSelection(),this.#Oa)return void(this.#Oa=!1);const{isMac:e}=xt.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;this.#Oa=!0;const i=this.#p.getActive();this.#Pa=!i||i.isEmpty()}findNewParent(t,e,i){const s=this.#p.findParent(e,i);return null!==s&&s!==this&&(s.changeParent(t),!0)}destroy(){this.#p.getActive()?.parent===this&&(this.#p.commitOrRemove(),this.#p.setActiveEditor(null)),this.#Fa&&(clearTimeout(this.#Fa),this.#Fa=null);for(const t of this.#Na.values())this.#Ws?.removePointerInTextLayer(t.contentDiv),t.setParent(null),t.isAttachedToDOM=!1,t.div.remove();this.div=null,this.#Na.clear(),this.#p.removeLayer(this)}#ja(){this.#za=!0;for(const t of this.#Na.values())t.isEmpty()&&t.remove();this.#za=!1}render({viewport:t}){this.viewport=t,ce(this.div,t);for(const t of this.#p.getEditors(this.pageIndex))this.add(t),t.rebuild();this.updateMode()}update({viewport:t}){this.#p.commitOrRemove(),this.#ja();const e=this.viewport.rotation,i=t.rotation;if(this.viewport=t,ce(this.div,{rotation:i}),e!==i)for(const t of this.#Na.values())t.rotate(i);this.addInkEditorIfNeeded(!1)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return this.#p.viewParameters.realScale}}class yn{#xs=null;#b=0;#Va=new Map;#qa=new Map;constructor({pageIndex:t}){this.pageIndex=t}setParent(t){if(this.#xs){if(this.#xs!==t){if(this.#Va.size>0)for(const e of this.#Va.values())e.remove(),t.append(e);this.#xs=t}}else this.#xs=t}static get _svgFactory(){return dt(this,"_svgFactory",new Xt)}static#Xa(t,{x:e=0,y:i=0,width:s=1,height:n=1}={}){const{style:r}=t;r.top=100*i+"%",r.left=100*e+"%",r.width=100*s+"%",r.height=100*n+"%"}#Ya(t){const e=yn._svgFactory.create(1,1,!0);return this.#xs.append(e),e.setAttribute("aria-hidden",!0),yn.#Xa(e,t),e}#Ka(t,e){const i=yn._svgFactory.createElement("clipPath");t.append(i);const s=`clip_${e}`;i.setAttribute("id",s),i.setAttribute("clipPathUnits","objectBoundingBox");const n=yn._svgFactory.createElement("use");return i.append(n),n.setAttribute("href",`#${e}`),n.classList.add("clip"),s}highlight(t,e,i,s=!1){const n=this.#b++,r=this.#Ya(t.box);r.classList.add("highlight"),t.free&&r.classList.add("free");const a=yn._svgFactory.createElement("defs");r.append(a);const o=yn._svgFactory.createElement("path");a.append(o);const h=`path_p${this.pageIndex}_${n}`;o.setAttribute("id",h),o.setAttribute("d",t.toSVGPath()),s&&this.#qa.set(n,o);const l=this.#Ka(a,h),d=yn._svgFactory.createElement("use");return r.append(d),r.setAttribute("fill",e),r.setAttribute("fill-opacity",i),d.setAttribute("href",`#${h}`),this.#Va.set(n,r),{id:n,clipPathId:`url(#${l})`}}highlightOutline(t){const e=this.#b++,i=this.#Ya(t.box);i.classList.add("highlightOutline");const s=yn._svgFactory.createElement("defs");i.append(s);const n=yn._svgFactory.createElement("path");s.append(n);const r=`path_p${this.pageIndex}_${e}`;let a;if(n.setAttribute("id",r),n.setAttribute("d",t.toSVGPath()),n.setAttribute("vector-effect","non-scaling-stroke"),t.free){i.classList.add("free");const t=yn._svgFactory.createElement("mask");s.append(t),a=`mask_p${this.pageIndex}_${e}`,t.setAttribute("id",a),t.setAttribute("maskUnits","objectBoundingBox");const n=yn._svgFactory.createElement("rect");t.append(n),n.setAttribute("width","1"),n.setAttribute("height","1"),n.setAttribute("fill","white");const o=yn._svgFactory.createElement("use");t.append(o),o.setAttribute("href",`#${r}`),o.setAttribute("stroke","none"),o.setAttribute("fill","black"),o.setAttribute("fill-rule","nonzero"),o.classList.add("mask")}const o=yn._svgFactory.createElement("use");i.append(o),o.setAttribute("href",`#${r}`),a&&o.setAttribute("mask",`url(#${a})`);const h=o.cloneNode();return i.append(h),o.classList.add("mainOutline"),h.classList.add("secondaryOutline"),this.#Va.set(e,i),e}finalizeLine(t,e){const i=this.#qa.get(t);this.#qa.delete(t),this.updateBox(t,e.box),i.setAttribute("d",e.toSVGPath())}updateLine(t,e){this.#Va.get(t).firstChild.firstChild.setAttribute("d",e.toSVGPath())}removeFreeHighlight(t){this.remove(t),this.#qa.delete(t)}updatePath(t,e){this.#qa.get(t).setAttribute("d",e.toSVGPath())}updateBox(t,e){yn.#Xa(this.#Va.get(t),e)}show(t,e){this.#Va.get(t).classList.toggle("hidden",!e)}rotate(t,e){this.#Va.get(t).setAttribute("data-main-rotation",e)}changeColor(t,e){this.#Va.get(t).setAttribute("fill",e)}changeOpacity(t,e){this.#Va.get(t).setAttribute("fill-opacity",e)}addClass(t,e){this.#Va.get(t).classList.add(e)}removeClass(t,e){this.#Va.get(t).classList.remove(e)}remove(t){null!==this.#xs&&(this.#Va.get(t).remove(),this.#Va.delete(t))}destroy(){this.#xs=null;for(const t of this.#Va.values())t.remove();this.#Va.clear()}}var wn=a.GlobalWorkerOptions,An=a.getDocument},73082:function(t,e,i){i.d(e,{Nb:function(){return O},Hz:function(){return gt}});var s=i(91296),n=i(67294),r=i(20745);const a=(0,n.createContext)(void 0),o=(t,{width:e,height:i})=>({x1:t.left,y1:t.top,x2:t.left+t.width,y2:t.top+t.height,width:e,height:i,pageNumber:t.pageNumber}),h=({boundingRect:t,rects:e},i)=>{const s=t.pageNumber,n=i.getPageView(s-1).viewport,r=t=>o(t,n);return{boundingRect:r(t),rects:(e||[]).map(r)}},l=(t,e,i=!1)=>{const{width:s,height:n}=e;if(i)return((t,e)=>{const[i,s,n,r]=e.convertToViewportRectangle([t.x1,t.y1,t.x2,t.y2]);return{left:Math.min(i,n),top:Math.min(s,r),width:Math.abs(n-i),height:Math.abs(s-r),pageNumber:t.pageNumber}})(t,e);if(void 0===t.x1)throw new Error("You are using old position format, please update");const r=s*t.x1/t.width,a=n*t.y1/t.height;return{left:r,top:a,width:s*t.x2/t.width-r,height:n*t.y2/t.height-a,pageNumber:t.pageNumber}},d=({boundingRect:t,rects:e,usePdfCoordinates:i},s)=>{const n=t.pageNumber,r=s.getPageView(n-1).viewport,a=t=>l(t,r,i);return{boundingRect:a(t),rects:(e||[]).map(a)}};var c=t=>{const e=Array.from(t).map((t=>{const{left:e,top:i,width:s,height:n,pageNumber:r}=t;return{X0:e,X1:e+s,Y0:i,Y1:i+n,pageNumber:r}}));let i=Number.MAX_SAFE_INTEGER;e.forEach((t=>{i=Math.min(i,t.pageNumber??i)}));const s=e.filter((t=>(t.X0>0||t.X1>0||t.Y0>0||t.Y1>0)&&t.pageNumber===i)),n=s.reduce(((t,e)=>({X0:Math.min(t.X0,e.X0),X1:Math.max(t.X1,e.X1),Y0:Math.min(t.Y0,e.Y0),Y1:Math.max(t.Y1,e.Y1),pageNumber:i})),s[0]),{X0:r,X1:a,Y0:o,Y1:h,pageNumber:l}=n;return{left:r,top:o,width:a-r,height:h-o,pageNumber:l}};const u=(t,e)=>t.pageNumber===e.pageNumber&&t.left<=e.left&&e.left<=t.left+t.width,p=(t,e,i=5)=>t.pageNumber===e.pageNumber&&Math.abs(t.top-e.top)<i&&Math.abs(t.height-e.height)<i,g=(t,e,i=10)=>{const s=t.left+t.width,n=e.left+e.width;return t.pageNumber===e.pageNumber&&t.left<=e.left&&s<=n&&e.left-s<=i},f=(t,e)=>{t.width=Math.max(e.width-t.left+e.left,t.width)};var m=t=>{const e=(t=>t.sort(((t,e)=>{const i=(t.pageNumber||0)*t.top-(e.pageNumber||0)*e.top;return 0===i?t.left-e.left:i})))(t),i=new Set,s=e.filter((t=>e.every((e=>{return s=e,!((i=t).pageNumber===s.pageNumber&&i.top>s.top&&i.left>s.left&&i.top+i.height<s.top+s.height&&i.left+i.width<s.left+s.width);var i,s}))));let n=0;for(;n<=2;)s.forEach((t=>{s.forEach((e=>{t===e||i.has(t)||i.has(e)||p(t,e)&&(u(t,e)&&(f(t,e),t.height=Math.max(t.height,e.height),i.add(e)),g(t,e)&&(f(t,e),i.add(e)))}))})),n+=1;return s.filter((t=>!i.has(t)))};const b=(t,e)=>!(t.top<e.top)&&(!(t.bottom>e.bottom)&&(!(t.right>e.right)&&!(t.left<e.left)));var v=(t,e,i=!0)=>{const s=Array.from(t.getClientRects()),n=[];for(const t of s)for(const i of e){const e=i.node.getBoundingClientRect();if(b(t,e)&&t.width>0&&t.height>0&&t.width<e.width&&t.left>e.left&&t.height<e.height){const s={top:t.top+i.node.scrollTop-e.top,left:t.left+i.node.scrollLeft-e.left,width:t.width,height:t.height,pageNumber:i.number};n.push(s)}}return i?m(n):n};var y=t=>t.reduce(((t,e)=>{if(!e)return t;return[e.position.boundingRect.pageNumber,...e.position.rects.map((t=>t.pageNumber||0))].forEach((i=>{t[i]||=[];const s={...e,position:{...e.position,rects:e.position.rects.filter((t=>i===t.pageNumber))}};t[i].push(s)})),t}),{});const w=t=>(t||{}).ownerDocument||document,A=t=>(w(t)||{}).defaultView||window,x=t=>t instanceof HTMLElement||t instanceof A(t).HTMLElement,_=t=>{const e=t.closest(".page");if(!e||!x(e))return null;return{node:e,number:Number(e.dataset.pageNumber)}},S=(0,n.createContext)(void 0),E=(t,e)=>{const{left:i,top:s,width:n,height:r}=e,a=t?t.ownerDocument:null,o=a&&a.createElement("canvas");if(!(o&&(h=o,h instanceof HTMLCanvasElement||h instanceof A(h).HTMLCanvasElement)))return"";var h;o.width=n,o.height=r;const l=o.getContext("2d");if(!l||!t)return"";const d=window.devicePixelRatio;return l.drawImage(t,i*d,s*d,n*d,r*d,0,0,n,r),o.toDataURL("image/png")};var C=(t,e,i)=>E(i.getPageView(e-1).canvas,t);const M=({highlightsByPage:t,pageNumber:e,scrolledToHighlightId:i,viewer:s,highlightBindings:r,children:a})=>{const h=t[e]||[];return n.createElement("div",null,h.map(((t,h)=>{const l={...t,id:"id"in t?t.id:"empty-id",position:d(t.position,s)},c={highlight:l,viewportToScaled:t=>{const i=s.getPageView((t.pageNumber||e)-1).viewport;return o(t,i)},screenshot:t=>C(t,e,s),isScrolledTo:Boolean(i===l.id),highlightBindings:r};return n.createElement(S.Provider,{value:c,key:h},a)})))},T=(t,e)=>({left:Math.min(e.x,t.x),top:Math.min(e.y,t.y),width:Math.abs(e.x-t.x),height:Math.abs(e.y-t.y)}),k=(t,e,i)=>{const s=t.getBoundingClientRect();return{x:e-s.left+t.scrollLeft,y:i-s.top+t.scrollTop-window.scrollY}},P=({viewer:t,onSelection:e,onReset:i,onDragStart:s,enableAreaSelection:r,onChange:a,style:o})=>{const[l,d]=(0,n.useState)(null),[c,u]=(0,n.useState)(null),[p,g]=(0,n.useState)(!1),f=(0,n.useRef)(null),m=(0,n.useRef)(null),b=()=>{i&&i(),d(null),u(null),g(!1)};return(0,n.useEffect)((()=>{if(a&&a(Boolean(l&&c)),!f.current)return;const i=f.current.parentElement,n=s=>{if(!l||!c||!m.current)return;const n=T(l,c),r=n.width>=1&&n.height>=1;if(!i.contains(s.target)||!r)return void b();g(!0);const a=_(m.current);if(!a)return;const o={...n,top:n.top-a.node.offsetTop,left:n.left-a.node.offsetLeft,pageNumber:a.number},d={boundingRect:o,rects:[]},u=h(d,t),p=C(o,o.pageNumber,t);e&&e(d,u,p,b,s)},o=t=>{f.current&&l&&!p&&u(k(i,t.pageX,t.pageY))},v=t=>{(t=>r(t)&&x(t.target)&&Boolean(t.target.closest(".page")))(t)?(m.current=t.target,s&&s(t),d(k(i,t.pageX,t.pageY)),u(null),g(!1)):(t=>l&&!t.target.closest(".PdfHighlighter__tip-container"))(t)&&b()};return i.addEventListener("mousemove",o),i.addEventListener("mousedown",v),document.addEventListener("mouseup",n),()=>{i.removeEventListener("mousemove",o),i.removeEventListener("mousedown",v),document.removeEventListener("mouseup",n)}}),[l,c]),n.createElement("div",{className:"MouseSelection-container",ref:f},l&&c&&n.createElement("div",{className:"MouseSelection",style:{...T(l,c),...o}}))},R=({viewer:t,updateTipPositionRef:e})=>{const[i,s]=(0,n.useState)(0),[r,o]=(0,n.useState)(0),h=(0,n.useRef)(null),l=()=>{if(!h.current)return;const{offsetHeight:t,offsetWidth:e}=h.current;s(t),o(e)};e.current=l,(0,n.useLayoutEffect)((()=>{l()}),[l]);const{getTip:d}=(()=>{const t=(0,n.useContext)(a);if(void 0===t)throw new Error("usePdfHighlighterContext must be used within PdfHighlighter!");return t})(),c=d();if(!c)return null;const{position:u,content:p}=c,{boundingRect:g}=u,f=g.pageNumber,m=t.getPageView(f-1).div,b=m.getBoundingClientRect(),{left:v,width:y}=b,w=t.container.scrollTop,A=m.offsetLeft+g.left+g.width/2,x=g.top+m.offsetTop,_=x+g.height,S=x-i-5<w?_+5:x-i-5,E=((t,e,i)=>Math.min(Math.max(t,e),i))(A-r/2,0,v+y-r);return n.createElement("div",{className:"PdfHighlighter__tip-container",style:{top:S,left:E,height:"max-content",width:"max-content"},ref:h},p)};let D,L,I;(async()=>{const t=await i.e(1264).then(i.bind(i,11264));D=t.EventBus,L=t.PDFLinkService,I=t.PDFViewer})();const F=t=>((t,e)=>{const i=w(t);let s=t.querySelector(`.${e}`);return!s&&t.children.length&&(s=i.createElement("div"),s.className=e,t.appendChild(s)),s})(t,"PdfHighlighter__highlight-layer"),N=(t,e)=>{t.viewer?.classList.toggle("PdfHighlighter--disable-selection",e)},O=({highlights:t,onScrollAway:e,pdfScaleValue:i="auto",onSelection:o,onCreateGhostHighlight:d,onRemoveGhostHighlight:u,selectionTip:p,enableAreaSelection:g,mouseSelectionStyle:f,pdfDocument:m,children:b,textSelectionColor:w="rgba(153,193,218,255)",utilsRef:S,style:E})=>{const[C,T]=(0,n.useState)(null),[k,O]=(0,n.useState)(!1),z=(0,n.useRef)(null),H=(0,n.useRef)({}),B=(0,n.useRef)(null),j=(0,n.useRef)(null),W=(0,n.useRef)(null),U=(0,n.useRef)(!1),$=(0,n.useRef)(!1),G=(0,n.useRef)((()=>{})),V=(0,n.useRef)(new D),q=(0,n.useRef)(new L({eventBus:V.current,externalLinkTarget:2})),X=(0,n.useRef)(null),Y=(0,n.useRef)(null);(0,n.useLayoutEffect)((()=>{if(!z.current)return;const t=s((()=>{Y.current=Y.current||new I({container:z.current,eventBus:V.current,textLayerMode:2,removePageBorders:!0,linkService:q.current}),Y.current.setDocument(m),q.current.setDocument(m),q.current.setViewer(Y.current),O(!0)}),100);return t(),()=>{t.cancel()}}),[document]),(0,n.useLayoutEffect)((()=>{if(!z.current)return;X.current=new ResizeObserver(J),X.current.observe(z.current);const t=z.current.ownerDocument;return V.current.on("textlayerrendered",tt),V.current.on("pagesinit",J),t.addEventListener("keydown",Q),tt(),()=>{V.current.off("pagesinit",J),V.current.off("textlayerrendered",tt),t.removeEventListener("keydown",Q),X.current?.disconnect()}}),[p,t,o]);const K=()=>{e&&e(),W.current=null,tt()},Q=t=>{"Escape"===t.code&&(st(),it(),T(null))},J=()=>{Y.current&&(Y.current.currentScaleValue=i.toString())},Z=(e,i)=>{Y.current&&e.reactRoot.render(n.createElement(a.Provider,{value:nt},n.createElement(M,{highlightsByPage:y([...t,B.current]),pageNumber:i,scrolledToHighlightId:W.current,viewer:Y.current,highlightBindings:e,children:b})))},tt=()=>{if(Y.current)for(let t=1;t<=m.numPages;t++){const e=H.current[t];if(e?.container?.isConnected)Z(e,t);else{const{textLayer:e}=Y.current.getPageView(t-1)||{};if(!e)continue;const i=F(e.div);if(i){const s=(0,r.createRoot)(i);H.current[t]={reactRoot:s,container:i,textLayer:e.div},Z(H.current[t],t)}}}},et=t=>{$.current=void 0!==t?t:!$.current,Y.current&&Y.current.viewer?.classList.toggle("PdfHighlighter--disable-selection",$.current)},it=()=>{u&&B.current&&u(B.current),B.current=null,tt()},st=()=>{j.current=null;const t=z.current,e=A(t).getSelection();t&&e&&e.removeAllRanges()},nt={isEditingOrHighlighting:()=>Boolean(j.current)||Boolean(B.current)||U.current||$.current,getCurrentSelection:()=>j.current,getGhostHighlight:()=>B.current,removeGhostHighlight:it,toggleEditInProgress:et,isEditInProgress:()=>$.current,isSelectionInProgress:()=>Boolean(j.current)||U.current,scrollToHighlight:t=>{const{boundingRect:e,usePdfCoordinates:i}=t.position,s=e.pageNumber;Y.current.container.removeEventListener("scroll",K);const n=Y.current.getPageView(s-1).viewport;Y.current.scrollPageIntoView({pageNumber:s,destArray:[null,{name:"XYZ"},...n.convertToPdfPoint(0,l(e,n,i).top-10),0]}),W.current=t.id,tt(),setTimeout((()=>{Y.current.container.addEventListener("scroll",K,{once:!0})}),100)},getViewer:()=>Y.current,getTip:()=>C,setTip:T,updateTipPosition:G.current};return S(nt),n.createElement(a.Provider,{value:nt},n.createElement("div",{ref:z,className:"PdfHighlighter",onPointerDown:t=>{x(t.target)&&!t.target.closest(".PdfHighlighter__tip-container")&&(T(null),st(),it(),et(!1))},onPointerUp:()=>{const t=z.current,e=A(t).getSelection();if(!t||!e||e.isCollapsed||!Y.current)return;const i=e.rangeCount>0?e.getRangeAt(0):null;if(!i||!t.contains(i.commonAncestorContainer))return;const s=(t=>{const e=t.startContainer.parentElement,i=t.endContainer.parentElement;if(!x(e)||!x(i))return[];const s=_(e),n=_(i);if(!s?.number||!n?.number)return[];if(s.number===n.number)return[s];if(s.number===n.number-1)return[s,n];const r=[];let a=s.number;const o=s.node.ownerDocument;for(;a<=n.number;){const t=_(o.querySelector(`[data-page-number='${a}'`));t&&r.push(t),a++}return r})(i);if(!s||0===s.length)return;const n=v(i,s);if(0===n.length)return;const r={boundingRect:c(n),rects:n},a=h(r,Y.current),l={text:e.toString().split("\n").join(" ")};j.current={content:l,type:"text",position:a,makeGhostHighlight:()=>(B.current={content:l,type:"text",position:a},d&&d(B.current),st(),tt(),B.current)},o&&o(j.current),p&&T({position:r,content:p})},style:E},n.createElement("div",{className:"pdfViewer"}),n.createElement("style",null,`\n          .textLayer ::selection {\n            background: ${w};\n          }\n        `),k&&n.createElement(R,{viewer:Y.current,updateTipPositionRef:G}),k&&g&&n.createElement(P,{viewer:Y.current,onChange:t=>U.current=t,enableAreaSelection:g,style:f,onDragStart:()=>N(Y.current,!0),onReset:()=>{j.current=null,N(Y.current,!1)},onSelection:(t,e,i,s)=>{j.current={content:{image:i},type:"area",position:e,makeGhostHighlight:()=>(B.current={position:e,type:"area",content:{image:i}},d&&d(B.current),s(),tt(),B.current)},o&&o(j.current),p&&T({position:t,content:p})}})))};var z=i(61193),H=i.n(z),B=i(73935),j=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){function s(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(s.prototype=i.prototype,new s)}}(),W=function(){return W=Object.assign||function(t){for(var e,i=1,s=arguments.length;i<s;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},W.apply(this,arguments)},U={width:"100%",height:"10px",top:"0px",left:"0px",cursor:"row-resize"},$={width:"10px",height:"100%",top:"0px",left:"0px",cursor:"col-resize"},G={width:"20px",height:"20px",position:"absolute"},V={top:W(W({},U),{top:"-5px"}),right:W(W({},$),{left:void 0,right:"-5px"}),bottom:W(W({},U),{top:void 0,bottom:"-5px"}),left:W(W({},$),{left:"-5px"}),topRight:W(W({},G),{right:"-10px",top:"-10px",cursor:"ne-resize"}),bottomRight:W(W({},G),{right:"-10px",bottom:"-10px",cursor:"se-resize"}),bottomLeft:W(W({},G),{left:"-10px",bottom:"-10px",cursor:"sw-resize"}),topLeft:W(W({},G),{left:"-10px",top:"-10px",cursor:"nw-resize"})},q=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.onMouseDown=function(t){e.props.onResizeStart(t,e.props.direction)},e.onTouchStart=function(t){e.props.onResizeStart(t,e.props.direction)},e}return j(e,t),e.prototype.render=function(){return n.createElement("div",{className:this.props.className||"",style:W(W({position:"absolute",userSelect:"none"},V[this.props.direction]),this.props.replaceStyles||{}),onMouseDown:this.onMouseDown,onTouchStart:this.onTouchStart},this.props.children)},e}(n.PureComponent),X=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){function s(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(s.prototype=i.prototype,new s)}}(),Y=function(){return Y=Object.assign||function(t){for(var e,i=1,s=arguments.length;i<s;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},Y.apply(this,arguments)},K={width:"auto",height:"auto"},Q=function(t,e,i){return Math.max(Math.min(t,i),e)},J=function(t,e,i){var s=Math.round(t/e);return s*e+i*(s-1)},Z=function(t,e){return new RegExp(t,"i").test(e)},tt=function(t){return Boolean(t.touches&&t.touches.length)},et=function(t,e,i){void 0===i&&(i=0);var s=e.reduce((function(i,s,n){return Math.abs(s-t)<Math.abs(e[i]-t)?n:i}),0),n=Math.abs(e[s]-t);return 0===i||n<i?e[s]:t},it=function(t){return"auto"===(t=t.toString())||t.endsWith("px")||t.endsWith("%")||t.endsWith("vh")||t.endsWith("vw")||t.endsWith("vmax")||t.endsWith("vmin")?t:t+"px"},st=function(t,e,i,s){if(t&&"string"==typeof t){if(t.endsWith("px"))return Number(t.replace("px",""));if(t.endsWith("%"))return e*(Number(t.replace("%",""))/100);if(t.endsWith("vw"))return i*(Number(t.replace("vw",""))/100);if(t.endsWith("vh"))return s*(Number(t.replace("vh",""))/100)}return t},nt=["as","ref","style","className","grid","gridGap","snap","bounds","boundsByDirection","size","defaultSize","minWidth","minHeight","maxWidth","maxHeight","lockAspectRatio","lockAspectRatioExtraWidth","lockAspectRatioExtraHeight","enable","handleStyles","handleClasses","handleWrapperStyle","handleWrapperClass","children","onResizeStart","onResize","onResizeStop","handleComponent","scale","resizeRatio","snapGap"],rt="__resizable_base__",at=function(t){function e(e){var i,s,n,r,a=t.call(this,e)||this;return a.ratio=1,a.resizable=null,a.parentLeft=0,a.parentTop=0,a.resizableLeft=0,a.resizableRight=0,a.resizableTop=0,a.resizableBottom=0,a.targetLeft=0,a.targetTop=0,a.appendBase=function(){if(!a.resizable||!a.window)return null;var t=a.parentNode;if(!t)return null;var e=a.window.document.createElement("div");return e.style.width="100%",e.style.height="100%",e.style.position="absolute",e.style.transform="scale(0, 0)",e.style.left="0",e.style.flex="0 0 100%",e.classList?e.classList.add(rt):e.className+=rt,t.appendChild(e),e},a.removeBase=function(t){var e=a.parentNode;e&&e.removeChild(t)},a.state={isResizing:!1,width:null!==(s=null===(i=a.propsSize)||void 0===i?void 0:i.width)&&void 0!==s?s:"auto",height:null!==(r=null===(n=a.propsSize)||void 0===n?void 0:n.height)&&void 0!==r?r:"auto",direction:"right",original:{x:0,y:0,width:0,height:0},backgroundStyle:{height:"100%",width:"100%",backgroundColor:"rgba(0,0,0,0)",cursor:"auto",opacity:0,position:"fixed",zIndex:9999,top:"0",left:"0",bottom:"0",right:"0"},flexBasis:void 0},a.onResizeStart=a.onResizeStart.bind(a),a.onMouseMove=a.onMouseMove.bind(a),a.onMouseUp=a.onMouseUp.bind(a),a}return X(e,t),Object.defineProperty(e.prototype,"parentNode",{get:function(){return this.resizable?this.resizable.parentNode:null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"window",{get:function(){return this.resizable&&this.resizable.ownerDocument?this.resizable.ownerDocument.defaultView:null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"propsSize",{get:function(){return this.props.size||this.props.defaultSize||K},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"size",{get:function(){var t=0,e=0;if(this.resizable&&this.window){var i=this.resizable.offsetWidth,s=this.resizable.offsetHeight,n=this.resizable.style.position;"relative"!==n&&(this.resizable.style.position="relative"),t="auto"!==this.resizable.style.width?this.resizable.offsetWidth:i,e="auto"!==this.resizable.style.height?this.resizable.offsetHeight:s,this.resizable.style.position=n}return{width:t,height:e}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sizeStyle",{get:function(){var t=this,e=this.props.size,i=function(e){var i;if(void 0===t.state[e]||"auto"===t.state[e])return"auto";if(t.propsSize&&t.propsSize[e]&&(null===(i=t.propsSize[e])||void 0===i?void 0:i.toString().endsWith("%"))){if(t.state[e].toString().endsWith("%"))return t.state[e].toString();var s=t.getParentSize();return Number(t.state[e].toString().replace("px",""))/s[e]*100+"%"}return it(t.state[e])};return{width:e&&void 0!==e.width&&!this.state.isResizing?it(e.width):i("width"),height:e&&void 0!==e.height&&!this.state.isResizing?it(e.height):i("height")}},enumerable:!1,configurable:!0}),e.prototype.getParentSize=function(){if(!this.parentNode)return this.window?{width:this.window.innerWidth,height:this.window.innerHeight}:{width:0,height:0};var t=this.appendBase();if(!t)return{width:0,height:0};var e=!1,i=this.parentNode.style.flexWrap;"wrap"!==i&&(e=!0,this.parentNode.style.flexWrap="wrap"),t.style.position="relative",t.style.minWidth="100%",t.style.minHeight="100%";var s={width:t.offsetWidth,height:t.offsetHeight};return e&&(this.parentNode.style.flexWrap=i),this.removeBase(t),s},e.prototype.bindEvents=function(){this.window&&(this.window.addEventListener("mouseup",this.onMouseUp),this.window.addEventListener("mousemove",this.onMouseMove),this.window.addEventListener("mouseleave",this.onMouseUp),this.window.addEventListener("touchmove",this.onMouseMove,{capture:!0,passive:!1}),this.window.addEventListener("touchend",this.onMouseUp))},e.prototype.unbindEvents=function(){this.window&&(this.window.removeEventListener("mouseup",this.onMouseUp),this.window.removeEventListener("mousemove",this.onMouseMove),this.window.removeEventListener("mouseleave",this.onMouseUp),this.window.removeEventListener("touchmove",this.onMouseMove,!0),this.window.removeEventListener("touchend",this.onMouseUp))},e.prototype.componentDidMount=function(){if(this.resizable&&this.window){var t=this.window.getComputedStyle(this.resizable);this.setState({width:this.state.width||this.size.width,height:this.state.height||this.size.height,flexBasis:"auto"!==t.flexBasis?t.flexBasis:void 0})}},e.prototype.componentWillUnmount=function(){this.window&&this.unbindEvents()},e.prototype.createSizeForCssProperty=function(t,e){var i=this.propsSize&&this.propsSize[e];return"auto"!==this.state[e]||this.state.original[e]!==t||void 0!==i&&"auto"!==i?t:"auto"},e.prototype.calculateNewMaxFromBoundary=function(t,e){var i,s,n=this.props.boundsByDirection,r=this.state.direction,a=n&&Z("left",r),o=n&&Z("top",r);if("parent"===this.props.bounds){var h=this.parentNode;h&&(i=a?this.resizableRight-this.parentLeft:h.offsetWidth+(this.parentLeft-this.resizableLeft),s=o?this.resizableBottom-this.parentTop:h.offsetHeight+(this.parentTop-this.resizableTop))}else"window"===this.props.bounds?this.window&&(i=a?this.resizableRight:this.window.innerWidth-this.resizableLeft,s=o?this.resizableBottom:this.window.innerHeight-this.resizableTop):this.props.bounds&&(i=a?this.resizableRight-this.targetLeft:this.props.bounds.offsetWidth+(this.targetLeft-this.resizableLeft),s=o?this.resizableBottom-this.targetTop:this.props.bounds.offsetHeight+(this.targetTop-this.resizableTop));return i&&Number.isFinite(i)&&(t=t&&t<i?t:i),s&&Number.isFinite(s)&&(e=e&&e<s?e:s),{maxWidth:t,maxHeight:e}},e.prototype.calculateNewSizeFromDirection=function(t,e){var i,s=this.props.scale||1,n=(i=this.props.resizeRatio||1,Array.isArray(i)?i:[i,i]),r=n[0],a=n[1],o=this.state,h=o.direction,l=o.original,d=this.props,c=d.lockAspectRatio,u=d.lockAspectRatioExtraHeight,p=d.lockAspectRatioExtraWidth,g=l.width,f=l.height,m=u||0,b=p||0;return Z("right",h)&&(g=l.width+(t-l.x)*r/s,c&&(f=(g-b)/this.ratio+m)),Z("left",h)&&(g=l.width-(t-l.x)*r/s,c&&(f=(g-b)/this.ratio+m)),Z("bottom",h)&&(f=l.height+(e-l.y)*a/s,c&&(g=(f-m)*this.ratio+b)),Z("top",h)&&(f=l.height-(e-l.y)*a/s,c&&(g=(f-m)*this.ratio+b)),{newWidth:g,newHeight:f}},e.prototype.calculateNewSizeFromAspectRatio=function(t,e,i,s){var n=this.props,r=n.lockAspectRatio,a=n.lockAspectRatioExtraHeight,o=n.lockAspectRatioExtraWidth,h=void 0===s.width?10:s.width,l=void 0===i.width||i.width<0?t:i.width,d=void 0===s.height?10:s.height,c=void 0===i.height||i.height<0?e:i.height,u=a||0,p=o||0;if(r){var g=(d-u)*this.ratio+p,f=(c-u)*this.ratio+p,m=(h-p)/this.ratio+u,b=(l-p)/this.ratio+u,v=Math.max(h,g),y=Math.min(l,f),w=Math.max(d,m),A=Math.min(c,b);t=Q(t,v,y),e=Q(e,w,A)}else t=Q(t,h,l),e=Q(e,d,c);return{newWidth:t,newHeight:e}},e.prototype.setBoundingClientRect=function(){var t=1/(this.props.scale||1);if("parent"===this.props.bounds){var e=this.parentNode;if(e){var i=e.getBoundingClientRect();this.parentLeft=i.left*t,this.parentTop=i.top*t}}if(this.props.bounds&&"string"!=typeof this.props.bounds){var s=this.props.bounds.getBoundingClientRect();this.targetLeft=s.left*t,this.targetTop=s.top*t}if(this.resizable){var n=this.resizable.getBoundingClientRect(),r=n.left,a=n.top,o=n.right,h=n.bottom;this.resizableLeft=r*t,this.resizableRight=o*t,this.resizableTop=a*t,this.resizableBottom=h*t}},e.prototype.onResizeStart=function(t,e){if(this.resizable&&this.window){var i,s=0,n=0;if(t.nativeEvent&&function(t){return Boolean((t.clientX||0===t.clientX)&&(t.clientY||0===t.clientY))}(t.nativeEvent)?(s=t.nativeEvent.clientX,n=t.nativeEvent.clientY):t.nativeEvent&&tt(t.nativeEvent)&&(s=t.nativeEvent.touches[0].clientX,n=t.nativeEvent.touches[0].clientY),this.props.onResizeStart)if(this.resizable)if(!1===this.props.onResizeStart(t,e,this.resizable))return;this.props.size&&(void 0!==this.props.size.height&&this.props.size.height!==this.state.height&&this.setState({height:this.props.size.height}),void 0!==this.props.size.width&&this.props.size.width!==this.state.width&&this.setState({width:this.props.size.width})),this.ratio="number"==typeof this.props.lockAspectRatio?this.props.lockAspectRatio:this.size.width/this.size.height;var r=this.window.getComputedStyle(this.resizable);if("auto"!==r.flexBasis){var a=this.parentNode;if(a){var o=this.window.getComputedStyle(a).flexDirection;this.flexDir=o.startsWith("row")?"row":"column",i=r.flexBasis}}this.setBoundingClientRect(),this.bindEvents();var h={original:{x:s,y:n,width:this.size.width,height:this.size.height},isResizing:!0,backgroundStyle:Y(Y({},this.state.backgroundStyle),{cursor:this.window.getComputedStyle(t.target).cursor||"auto"}),direction:e,flexBasis:i};this.setState(h)}},e.prototype.onMouseMove=function(t){var e=this;if(this.state.isResizing&&this.resizable&&this.window){if(this.window.TouchEvent&&tt(t))try{t.preventDefault(),t.stopPropagation()}catch(t){}var i=this.props,s=i.maxWidth,n=i.maxHeight,r=i.minWidth,a=i.minHeight,o=tt(t)?t.touches[0].clientX:t.clientX,h=tt(t)?t.touches[0].clientY:t.clientY,l=this.state,d=l.direction,c=l.original,u=l.width,p=l.height,g=this.getParentSize(),f=function(t,e,i,s,n,r,a){return s=st(s,t.width,e,i),n=st(n,t.height,e,i),r=st(r,t.width,e,i),a=st(a,t.height,e,i),{maxWidth:void 0===s?void 0:Number(s),maxHeight:void 0===n?void 0:Number(n),minWidth:void 0===r?void 0:Number(r),minHeight:void 0===a?void 0:Number(a)}}(g,this.window.innerWidth,this.window.innerHeight,s,n,r,a);s=f.maxWidth,n=f.maxHeight,r=f.minWidth,a=f.minHeight;var m=this.calculateNewSizeFromDirection(o,h),b=m.newHeight,v=m.newWidth,y=this.calculateNewMaxFromBoundary(s,n);this.props.snap&&this.props.snap.x&&(v=et(v,this.props.snap.x,this.props.snapGap)),this.props.snap&&this.props.snap.y&&(b=et(b,this.props.snap.y,this.props.snapGap));var w=this.calculateNewSizeFromAspectRatio(v,b,{width:y.maxWidth,height:y.maxHeight},{width:r,height:a});if(v=w.newWidth,b=w.newHeight,this.props.grid){var A=J(v,this.props.grid[0],this.props.gridGap?this.props.gridGap[0]:0),x=J(b,this.props.grid[1],this.props.gridGap?this.props.gridGap[1]:0),_=this.props.snapGap||0;v=0===_||Math.abs(A-v)<=_?A:v,b=0===_||Math.abs(x-b)<=_?x:b}var S={width:v-c.width,height:b-c.height};if(u&&"string"==typeof u)if(u.endsWith("%"))v=v/g.width*100+"%";else if(u.endsWith("vw")){v=v/this.window.innerWidth*100+"vw"}else if(u.endsWith("vh")){v=v/this.window.innerHeight*100+"vh"}if(p&&"string"==typeof p)if(p.endsWith("%"))b=b/g.height*100+"%";else if(p.endsWith("vw")){b=b/this.window.innerWidth*100+"vw"}else if(p.endsWith("vh")){b=b/this.window.innerHeight*100+"vh"}var E={width:this.createSizeForCssProperty(v,"width"),height:this.createSizeForCssProperty(b,"height")};"row"===this.flexDir?E.flexBasis=E.width:"column"===this.flexDir&&(E.flexBasis=E.height);var C=this.state.width!==E.width,M=this.state.height!==E.height,T=this.state.flexBasis!==E.flexBasis,k=C||M||T;k&&(0,B.flushSync)((function(){e.setState(E)})),this.props.onResize&&k&&this.props.onResize(t,d,this.resizable,S)}},e.prototype.onMouseUp=function(t){var e,i,s=this.state,n=s.isResizing,r=s.direction,a=s.original;if(n&&this.resizable){var o={width:this.size.width-a.width,height:this.size.height-a.height};this.props.onResizeStop&&this.props.onResizeStop(t,r,this.resizable,o),this.props.size&&this.setState({width:null!==(e=this.props.size.width)&&void 0!==e?e:"auto",height:null!==(i=this.props.size.height)&&void 0!==i?i:"auto"}),this.unbindEvents(),this.setState({isResizing:!1,backgroundStyle:Y(Y({},this.state.backgroundStyle),{cursor:"auto"})})}},e.prototype.updateSize=function(t){var e,i;this.setState({width:null!==(e=t.width)&&void 0!==e?e:"auto",height:null!==(i=t.height)&&void 0!==i?i:"auto"})},e.prototype.renderResizer=function(t){var e=this,i=this.props,s=i.enable,r=i.handleStyles,a=i.handleClasses,o=i.handleWrapperStyle,h=i.handleWrapperClass,l=i.handleComponent;if(!s)return null;var d=t.filter((function(t){return!1!==s[t]})).map((function(t){return!1!==s[t]?n.createElement(q,{key:t,direction:t,onResizeStart:e.onResizeStart,replaceStyles:r&&r[t],className:a&&a[t]},l&&l[t]?l[t]:null):null}));return n.createElement("div",{className:h,style:o},d)},e.prototype.render=function(){var t=this,e=Object.keys(this.props).reduce((function(e,i){return-1!==nt.indexOf(i)||(e[i]=t.props[i]),e}),{}),i=Y(Y(Y({position:"relative",userSelect:this.state.isResizing?"none":"auto"},this.props.style),this.sizeStyle),{maxWidth:this.props.maxWidth,maxHeight:this.props.maxHeight,minWidth:this.props.minWidth,minHeight:this.props.minHeight,boxSizing:"border-box",flexShrink:0});this.state.flexBasis&&(i.flexBasis=this.state.flexBasis);var s=this.props.as||"div";return n.createElement(s,Y({style:i,className:this.props.className},e,{ref:function(e){e&&(t.resizable=e)}}),this.state.isResizing&&n.createElement("div",{style:this.state.backgroundStyle}),this.renderResizer(["topLeft","top","topRight","left"]),this.props.children,this.renderResizer(["right","bottomLeft","bottom","bottomRight"]))},e.defaultProps={as:"div",onResizeStart:function(){},onResize:function(){},onResizeStop:function(){},enable:{top:!0,right:!0,bottom:!0,left:!0,topRight:!0,bottomRight:!0,bottomLeft:!0,topLeft:!0},style:{},grid:[1,1],gridGap:[0,0],lockAspectRatio:!1,lockAspectRatioExtraWidth:0,lockAspectRatioExtraHeight:0,scale:1,resizeRatio:1,snapGap:0},e}(n.PureComponent),ot=function(t,e){return ot=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},ot(t,e)};var ht=function(){return ht=Object.assign||function(t){for(var e,i=1,s=arguments.length;i<s;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},ht.apply(this,arguments)};var lt={width:"auto",height:"auto",display:"inline-block",position:"absolute",top:0,left:0};!function(t){function e(e){var i=t.call(this,e)||this;return i.resizingPosition={x:0,y:0},i.offsetFromParent={left:0,top:0},i.resizableElement={current:null},i.originalPosition={x:0,y:0},i.state={resizing:!1,bounds:{top:0,right:0,bottom:0,left:0},maxWidth:e.maxWidth,maxHeight:e.maxHeight},i.onResizeStart=i.onResizeStart.bind(i),i.onResize=i.onResize.bind(i),i.onResizeStop=i.onResizeStop.bind(i),i.onDragStart=i.onDragStart.bind(i),i.onDrag=i.onDrag.bind(i),i.onDragStop=i.onDragStop.bind(i),i.getMaxSizesFromProps=i.getMaxSizesFromProps.bind(i),i}(function(t,e){function i(){this.constructor=t}ot(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)})(e,t),e.prototype.componentDidMount=function(){this.updateOffsetFromParent();var t=this.offsetFromParent,e=t.left,i=t.top,s=this.getDraggablePosition(),n=s.x,r=s.y;this.draggable.setState({x:n-e,y:r-i}),this.forceUpdate()},e.prototype.getDraggablePosition=function(){var t=this.draggable.state;return{x:t.x,y:t.y}},e.prototype.getParent=function(){return this.resizable&&this.resizable.parentNode},e.prototype.getParentSize=function(){return this.resizable.getParentSize()},e.prototype.getMaxSizesFromProps=function(){return{maxWidth:void 0===this.props.maxWidth?Number.MAX_SAFE_INTEGER:this.props.maxWidth,maxHeight:void 0===this.props.maxHeight?Number.MAX_SAFE_INTEGER:this.props.maxHeight}},e.prototype.getSelfElement=function(){return this.resizable&&this.resizable.resizable},e.prototype.getOffsetHeight=function(t){var e=this.props.scale;switch(this.props.bounds){case"window":return window.innerHeight/e;case"body":return document.body.offsetHeight/e;default:return t.offsetHeight}},e.prototype.getOffsetWidth=function(t){var e=this.props.scale;switch(this.props.bounds){case"window":return window.innerWidth/e;case"body":return document.body.offsetWidth/e;default:return t.offsetWidth}},e.prototype.onDragStart=function(t,e){this.props.onDragStart&&this.props.onDragStart(t,e);var i=this.getDraggablePosition();if(this.originalPosition=i,this.props.bounds){var s,n=this.getParent(),r=this.props.scale;if("parent"===this.props.bounds)s=n;else{if("body"===this.props.bounds){var a=n.getBoundingClientRect(),o=a.left,h=a.top,l=document.body.getBoundingClientRect(),d=-(o-n.offsetLeft*r-l.left)/r,c=-(h-n.offsetTop*r-l.top)/r,u=(document.body.offsetWidth-this.resizable.size.width*r)/r+d,p=(document.body.offsetHeight-this.resizable.size.height*r)/r+c;return this.setState({bounds:{top:c,right:u,bottom:p,left:d}})}if("window"===this.props.bounds){if(!this.resizable)return;var g=n.getBoundingClientRect(),f=g.left,m=g.top,b=-(f-n.offsetLeft*r)/r,v=-(m-n.offsetTop*r)/r;u=(window.innerWidth-this.resizable.size.width*r)/r+b,p=(window.innerHeight-this.resizable.size.height*r)/r+v;return this.setState({bounds:{top:v,right:u,bottom:p,left:b}})}"string"==typeof this.props.bounds?s=document.querySelector(this.props.bounds):this.props.bounds instanceof HTMLElement&&(s=this.props.bounds)}if(s instanceof HTMLElement&&n instanceof HTMLElement){var y=s.getBoundingClientRect(),w=y.left,A=y.top,x=n.getBoundingClientRect(),_=(w-x.left)/r,S=A-x.top;if(this.resizable){this.updateOffsetFromParent();var E=this.offsetFromParent;this.setState({bounds:{top:S-E.top,right:_+(s.offsetWidth-this.resizable.size.width)-E.left/r,bottom:S+(s.offsetHeight-this.resizable.size.height)-E.top,left:_-E.left/r}})}}}},e.prototype.onDrag=function(t,e){if(this.props.onDrag){var i=this.offsetFromParent,s=i.left,n=i.top;return this.props.dragAxis&&"both"!==this.props.dragAxis?"x"===this.props.dragAxis?this.props.onDrag(t,ht(ht({},e),{x:e.x+s,y:this.originalPosition.y+n,deltaY:0})):"y"===this.props.dragAxis?this.props.onDrag(t,ht(ht({},e),{x:this.originalPosition.x+s,y:e.y+n,deltaX:0})):void 0:this.props.onDrag(t,ht(ht({},e),{x:e.x+s,y:e.y+n}))}},e.prototype.onDragStop=function(t,e){if(this.props.onDragStop){var i=this.offsetFromParent,s=i.left,n=i.top;return this.props.dragAxis&&"both"!==this.props.dragAxis?"x"===this.props.dragAxis?this.props.onDragStop(t,ht(ht({},e),{x:e.x+s,y:this.originalPosition.y+n,deltaY:0})):"y"===this.props.dragAxis?this.props.onDragStop(t,ht(ht({},e),{x:this.originalPosition.x+s,y:e.y+n,deltaX:0})):void 0:this.props.onDragStop(t,ht(ht({},e),{x:e.x+s,y:e.y+n}))}},e.prototype.onResizeStart=function(t,e,i){t.stopPropagation(),this.setState({resizing:!0});var s=this.props.scale,n=this.offsetFromParent,r=this.getDraggablePosition();if(this.resizingPosition={x:r.x+n.left,y:r.y+n.top},this.originalPosition=r,this.props.bounds){var a=this.getParent(),o=void 0;"parent"===this.props.bounds?o=a:"body"===this.props.bounds?o=document.body:"window"===this.props.bounds?o=window:"string"==typeof this.props.bounds?o=document.querySelector(this.props.bounds):this.props.bounds instanceof HTMLElement&&(o=this.props.bounds);var h=this.getSelfElement();if(h instanceof Element&&(o instanceof HTMLElement||o===window)&&a instanceof HTMLElement){var l=this.getMaxSizesFromProps(),d=l.maxWidth,c=l.maxHeight,u=this.getParentSize();if(d&&"string"==typeof d)if(d.endsWith("%")){var p=Number(d.replace("%",""))/100;d=u.width*p}else d.endsWith("px")&&(d=Number(d.replace("px","")));if(c&&"string"==typeof c)if(c.endsWith("%")){p=Number(c.replace("%",""))/100;c=u.height*p}else c.endsWith("px")&&(c=Number(c.replace("px","")));var g=h.getBoundingClientRect(),f=g.left,m=g.top,b="window"===this.props.bounds?{left:0,top:0}:o.getBoundingClientRect(),v=b.left,y=b.top,w=this.getOffsetWidth(o),A=this.getOffsetHeight(o),x=e.toLowerCase().endsWith("left"),_=e.toLowerCase().endsWith("right"),S=e.startsWith("top"),E=e.startsWith("bottom");if((x||S)&&this.resizable){var C=(f-v)/s+this.resizable.size.width;this.setState({maxWidth:C>Number(d)?d:C})}if(_||this.props.lockAspectRatio&&!x&&!S){C=w+(v-f)/s;this.setState({maxWidth:C>Number(d)?d:C})}if((S||x)&&this.resizable){C=(m-y)/s+this.resizable.size.height;this.setState({maxHeight:C>Number(c)?c:C})}if(E||this.props.lockAspectRatio&&!S&&!x){C=A+(y-m)/s;this.setState({maxHeight:C>Number(c)?c:C})}}}else this.setState({maxWidth:this.props.maxWidth,maxHeight:this.props.maxHeight});this.props.onResizeStart&&this.props.onResizeStart(t,e,i)},e.prototype.onResize=function(t,e,i,s){var n=this,r={x:this.originalPosition.x,y:this.originalPosition.y},a=-s.width,o=-s.height;["top","left","topLeft","bottomLeft","topRight"].includes(e)&&("bottomLeft"===e?r.x+=a:("topRight"===e||(r.x+=a),r.y+=o));var h=this.draggable.state;r.x===h.x&&r.y===h.y||(0,B.flushSync)((function(){n.draggable.setState(r)})),this.updateOffsetFromParent();var l=this.offsetFromParent,d=this.getDraggablePosition().x+l.left,c=this.getDraggablePosition().y+l.top;this.resizingPosition={x:d,y:c},this.props.onResize&&this.props.onResize(t,e,i,s,{x:d,y:c})},e.prototype.onResizeStop=function(t,e,i,s){this.setState({resizing:!1});var n=this.getMaxSizesFromProps(),r=n.maxWidth,a=n.maxHeight;this.setState({maxWidth:r,maxHeight:a}),this.props.onResizeStop&&this.props.onResizeStop(t,e,i,s,this.resizingPosition)},e.prototype.updateSize=function(t){this.resizable&&this.resizable.updateSize({width:t.width,height:t.height})},e.prototype.updatePosition=function(t){this.draggable.setState(t)},e.prototype.updateOffsetFromParent=function(){var t=this.props.scale,e=this.getParent(),i=this.getSelfElement();if(!e||null===i)return{top:0,left:0};var s=e.getBoundingClientRect(),n=s.left,r=s.top,a=i.getBoundingClientRect(),o=this.getDraggablePosition(),h=e.scrollLeft,l=e.scrollTop;this.offsetFromParent={left:a.left-n+h-o.x*t,top:a.top-r+l-o.y*t}},e.prototype.render=function(){var t=this,e=this.props,i=e.disableDragging,s=e.style,r=e.dragHandleClassName,a=e.position,o=e.onMouseDown,h=e.onMouseUp,l=e.dragAxis,d=e.dragGrid,c=e.bounds,u=e.enableUserSelectHack,p=e.cancel,g=e.children,f=(e.onResizeStart,e.onResize,e.onResizeStop,e.onDragStart,e.onDrag,e.onDragStop,e.resizeHandleStyles),m=e.resizeHandleClasses,b=e.resizeHandleComponent,v=e.enableResizing,y=e.resizeGrid,w=e.resizeHandleWrapperClass,A=e.resizeHandleWrapperStyle,x=e.scale,_=e.allowAnyClick,S=function(t,e){var i={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&e.indexOf(s)<0&&(i[s]=t[s]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(s=Object.getOwnPropertySymbols(t);n<s.length;n++)e.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(t,s[n])&&(i[s[n]]=t[s[n]])}return i}(e,["disableDragging","style","dragHandleClassName","position","onMouseDown","onMouseUp","dragAxis","dragGrid","bounds","enableUserSelectHack","cancel","children","onResizeStart","onResize","onResizeStop","onDragStart","onDrag","onDragStop","resizeHandleStyles","resizeHandleClasses","resizeHandleComponent","enableResizing","resizeGrid","resizeHandleWrapperClass","resizeHandleWrapperStyle","scale","allowAnyClick"]),E=this.props.default?ht({},this.props.default):void 0;delete S.default;var C,M=i||r?{cursor:"auto"}:{cursor:"move"},T=ht(ht(ht({},lt),M),s),k=this.offsetFromParent,P=k.left,R=k.top;a&&(C={x:a.x-P,y:a.y-R});var D,L=this.state.resizing?void 0:C,I=this.state.resizing?"both":l;return(0,n.createElement)(H(),{ref:function(e){e&&(t.draggable=e)},handle:r?".".concat(r):void 0,defaultPosition:E,onMouseDown:o,onMouseUp:h,onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop,axis:I,disabled:i,grid:d,bounds:c?this.state.bounds:void 0,position:L,enableUserSelectHack:u,cancel:p,scale:x,allowAnyClick:_,nodeRef:this.resizableElement},(0,n.createElement)(at,ht({},S,{ref:function(e){e&&(t.resizable=e,t.resizableElement.current=e.resizable)},defaultSize:E,size:this.props.size,enable:"boolean"==typeof v?(D=v,{bottom:D,bottomLeft:D,bottomRight:D,left:D,right:D,top:D,topLeft:D,topRight:D}):v,onResizeStart:this.onResizeStart,onResize:this.onResize,onResizeStop:this.onResizeStop,style:T,minWidth:this.props.minWidth,minHeight:this.props.minHeight,maxWidth:this.state.resizing?this.state.maxWidth:this.props.maxWidth,maxHeight:this.state.resizing?this.state.maxHeight:this.props.maxHeight,grid:y,handleWrapperClass:w,handleWrapperStyle:A,lockAspectRatio:this.props.lockAspectRatio,lockAspectRatioExtraWidth:this.props.lockAspectRatioExtraWidth,lockAspectRatioExtraHeight:this.props.lockAspectRatioExtraHeight,handleStyles:f,handleClasses:m,handleComponent:b,scale:this.props.scale}),g))},e.defaultProps={maxWidth:Number.MAX_SAFE_INTEGER,maxHeight:Number.MAX_SAFE_INTEGER,scale:1,onResizeStart:function(){},onResize:function(){},onResizeStop:function(){},onDragStart:function(){},onDrag:function(){},onDragStop:function(){}}}(n.PureComponent);var dt=i(28541);const ct=t=>n.createElement("div",{style:{color:"black"}},"Loading ",Math.floor(t.loaded/t.total*100),"%"),ut=t=>n.createElement("div",{style:{color:"black"}},t.message),pt=t=>{throw new Error(`Error loading PDF document: ${t.message}!`)},gt=({document:t,beforeLoad:e=ct,errorMessage:i=ut,children:s,onError:r=pt,workerSrc:a="https://unpkg.com/pdfjs-dist@4.4.168/build/pdf.worker.min.mjs"})=>{const o=(0,n.useRef)(null),h=(0,n.useRef)(null),[l,d]=(0,n.useState)(null),[c,u]=(0,n.useState)(null);return(0,n.useEffect)((()=>(dt.Tu.workerSrc=a,o.current=(0,dt.Me)(t),o.current.onProgress=t=>{u(t.loaded>t.total?null:t)},o.current.promise.then((t=>{h.current=t})).catch((t=>{"Worker was destroyed"!=t.message&&(d(t),r(t))})).finally((()=>{u(null)})),()=>{o.current&&o.current.destroy(),h.current&&h.current.destroy()})),[t]),l?i(l):c?e(c):h.current&&s(h.current)}}}]);