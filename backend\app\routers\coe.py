from fastapi import APIRouter, HTTPException
from backend.app.engines.coe.coe_service import COEService
from typing import Optional, Dict
from pydantic import BaseModel

router = APIRouter(prefix="/api/coe", tags=["coe"])

class QueryRequest(BaseModel):
    query: str
    context: Optional[Dict] = None

@router.post("/query")
async def process_query(request: QueryRequest):
    """处理 COE 查询"""
    engine = COEService.get_engine()
    if engine is None:
        raise HTTPException(status_code=503, detail="COE 服务未启用")
    
    try:
        response = await engine.process_query(
            query=request.query,
            context=request.context
        )
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/experts")
async def list_experts():
    """获取所有可用专家列表"""
    engine = COEService.get_engine()
    if engine is None:
        raise HTTPException(status_code=503, detail="COE 服务未启用")
    
    return engine.list_experts()
