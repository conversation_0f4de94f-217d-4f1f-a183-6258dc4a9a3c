from fastapi import Depends, HTTPException, status, Header
from datetime import datetime, timedelta
from ..db.mongodb import db
from dotenv import load_dotenv
import os
from app.utils.logging_config import setup_logging, get_logger
import traceback
setup_logging()
logger = get_logger(__name__)

# 加载 .env 文件
load_dotenv()

# 获取环境变量
SECRET_KEY = os.getenv("SECRET_KEY")
ACCESS_TOKEN_EXPIRE_SECONDS = int(os.getenv("ACCESS_TOKEN_EXPIRE_SECONDS", "360000"))  # 默认24小时

async def verify_token(authorization: str = Header(None)):
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing Authorization header"
        )
    
    token = authorization.split(" ")[-1]  # 获取 Bearer token
    user = await db["users"].find_one({"auth_token": token})
    # logger.info(f"【验证 token】: {token}") 
    # logger.info(f"【验证 user】: {user}")

    if not user:
        logger.info(f"【无效 token】: {token}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )

    if user["token_expiry"] < datetime.now():
        logger.info(f"【token 已过期】: {token}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired"
        )
    else:
        # logger.info(f"【token 有效】: {token}")
        token_expiry = datetime.now() + timedelta(seconds=ACCESS_TOKEN_EXPIRE_SECONDS)
        await db["users"].update_one(
            {"phone": user["phone"]},
            {"$set": {"token_expiry": token_expiry}}
        )
        return user