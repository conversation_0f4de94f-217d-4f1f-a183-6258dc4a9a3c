import asyncio
import asyncpg
from .logging_config import get_logger
from .xmconfig import settings

logger = get_logger(__name__)
conn = None  # 添加全局变量

async def init_db():
    global conn  # 声明使用全局变量
    conn = await asyncpg.connect(
        user=settings.POSTGRES_USER,
        password=settings.POSTGRES_PASSWORD,
        host=settings.POSTGRES_HOST,
        port=settings.POSTGRES_PORT,
        database=settings.POSTGRES_DB
    )
    logger.info("数据库连接成功")

    try:
        # 创建 pgcrypto 扩展
        await conn.execute("""
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        """)

        # 创建应用设置表
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS app_info (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            app_name VARCHAR(50),
            created_at TIMESTAMP  DEFAULT NOW(),
            updated_at TIMESTAMP  DEFAULT NOW()
        );
        COMMENT ON TABLE app_info IS '应用程序配置信息表';
        COMMENT ON COLUMN app_info.id IS '应用ID';
        COMMENT ON COLUMN app_info.app_name IS '应用程序名称';
        COMMENT ON COLUMN app_info.created_at IS '创建时间';
        COMMENT ON COLUMN app_info.updated_at IS '更新时间';
        """)
        logger.info("应用设置表 'app_info' 创建成功或已存在。")
        print("应用设置表 'app_info' 创建成功或已存在。")

        # 创建聊天表
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS chats (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            app_id VARCHAR(255),
            created_at TIMESTAMP DEFAULT NOW(),
            stream BOOLEAN DEFAULT FALSE, 
            detail BOOLEAN DEFAULT FALSE,
            variables JSONB DEFAULT '{}',
            messages JSONB DEFAULT '[]'
        );
        COMMENT ON TABLE chats IS '聊天记录表';
        COMMENT ON COLUMN chats.id IS '主键ID';
        COMMENT ON COLUMN chats.app_id IS '应用程序ID';
        COMMENT ON COLUMN chats.created_at IS '创建时间';
        COMMENT ON COLUMN chats.stream IS '是否为流式响应';
        COMMENT ON COLUMN chats.detail IS '是否包含详细信息';
        COMMENT ON COLUMN chats.variables IS '变量信息';
        COMMENT ON COLUMN chats.messages IS '聊天消息内容';
        """)
        logger.info("聊天表 'chats' 创建成功或已存在。")
        print("聊天表 'chats' 创建成功或已存在。")
        
        # 创建知识库表
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS knowledge_bases (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            parent_id VARCHAR(255),
            type VARCHAR(255),
            name VARCHAR(255) NOT NULL,
            intro TEXT,
            avatar VARCHAR(255),
            vector_model VARCHAR(255),
            agent_model VARCHAR(255),
            created_at TIMESTAMP NOT NULL,
            last_updated TIMESTAMP NOT NULL,
            user_id INTEGER,
            user_name VARCHAR(255),
            is_active BOOLEAN NOT NULL DEFAULT TRUE
        );
        COMMENT ON TABLE knowledge_bases IS '知识库主表';
        COMMENT ON COLUMN knowledge_bases.id IS '主键ID';
        COMMENT ON COLUMN knowledge_bases.parent_id IS '父级ID';
        COMMENT ON COLUMN knowledge_bases.type IS '类型';
        COMMENT ON COLUMN knowledge_bases.name IS '知识库名称';
        COMMENT ON COLUMN knowledge_bases.intro IS '知识库描述';
        COMMENT ON COLUMN knowledge_bases.avatar IS '知识库头像地址';
        COMMENT ON COLUMN knowledge_bases.vector_model IS '向量模型';
        COMMENT ON COLUMN knowledge_bases.agent_model IS '文本处理模型';
        COMMENT ON COLUMN knowledge_bases.created_at IS '创建时间';
        COMMENT ON COLUMN knowledge_bases.last_updated IS '最后更新时间';
        COMMENT ON COLUMN knowledge_bases.user_id IS '用户ID';
        COMMENT ON COLUMN knowledge_bases.user_name IS '用户名称';
        COMMENT ON COLUMN knowledge_bases.is_active IS '是否激活';
        """)
        logger.info("知识库表 'knowledge_bases' 创建成功或已存在。")
        print("知识库表 'knowledge_bases' 创建成功或已存在。")
        
        # 创建集合表
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS collections (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            knowledge_base_id UUID NOT NULL REFERENCES knowledge_bases(id),
            parent_id VARCHAR(255),
            created_at TIMESTAMP 
        );
        COMMENT ON TABLE collections IS '知识库集合表';
        COMMENT ON COLUMN collections.id IS '主键ID';
        COMMENT ON COLUMN collections.knowledge_base_id IS '关联的知识库ID';
        COMMENT ON COLUMN collections.parent_id IS '父级ID';
        COMMENT ON COLUMN collections.created_at IS '创建时间';
        """)
        logger.info("集合表 'collections' 创建成功或已存在。")
        print("集合表 'collections' 创建成功或已存在。")
        
        # 创建知识数据表
        await conn.execute("""
        CREATE TABLE IF NOT EXISTS knowledge_data (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            collection_id UUID REFERENCES collections(id),
            knowledge_base_id UUID REFERENCES knowledge_bases(id),
            mode VARCHAR(50),
            data VARCHAR(2000),
            doc_name VARCHAR(255),
            prompt VARCHAR(50),
            created_at TIMESTAMP,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            deleted_at TIMESTAMP,
            embedding_vector "public"."vector"
        );
        COMMENT ON TABLE knowledge_data IS '知识库数据表';
        COMMENT ON COLUMN knowledge_data.id IS '主键ID';
        COMMENT ON COLUMN knowledge_data.collection_id IS '关联的集合ID';
        COMMENT ON COLUMN knowledge_data.knowledge_base_id IS '关联的知识库ID';
        COMMENT ON COLUMN knowledge_data.mode IS '模式';
        COMMENT ON COLUMN knowledge_data.data IS '知识内容';
        COMMENT ON COLUMN knowledge_data.prompt IS '提示词';
        COMMENT ON COLUMN knowledge_data.doc_name IS '文档名称';
        COMMENT ON COLUMN knowledge_data.created_at IS '创建时间';
        COMMENT ON COLUMN knowledge_data.is_active IS '是否激活';
        COMMENT ON COLUMN knowledge_data.deleted_at IS '删除时间';
        COMMENT ON COLUMN knowledge_data.embedding_vector IS '向量嵌入';
        """)
        logger.info("知识数据表 'knowledge_data' 创建成功或已存在。")
        print("知识数据表 'knowledge_data' 创建成功或已存在。")
        
        
    except Exception as e:
        logger.error(f"初始化数据库时发生错误: {e}")
        print(f"初始化数据库时发生错误: {e}")
    finally:
        await conn.close()

async def close_db():
    """关闭数据库连接"""
    global conn  # 声明使用全局变量
    if conn:
        await conn.close()
        conn = None
        logger.info("数据库连接已关闭")


# 在应用程序启动时调用初始化函数
if __name__ == "__main__":
    asyncio.run(init_db())