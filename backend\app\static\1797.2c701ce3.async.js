"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1797],{8751:function(e,t,n){n.d(t,{Z:function(){return c}});var o=n(1413),r=n(67294),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"},a=n(91146),i=function(e,t){return r.createElement(a.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:l}))};var c=r.forwardRef(i)},18429:function(e,t,n){n.d(t,{Z:function(){return c}});var o=n(1413),r=n(67294),l={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"},a=n(91146),i=function(e,t){return r.createElement(a.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:l}))};var c=r.forwardRef(i)},82061:function(e,t,n){var o=n(1413),r=n(67294),l=n(47046),a=n(91146),i=function(e,t){return r.createElement(a.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:l.Z}))},c=r.forwardRef(i);t.Z=c},47389:function(e,t,n){var o=n(1413),r=n(67294),l=n(27363),a=n(91146),i=function(e,t){return r.createElement(a.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:l.Z}))},c=r.forwardRef(i);t.Z=c},11475:function(e,t,n){n.d(t,{Z:function(){return c}});var o=n(1413),r=n(67294),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"},a=n(91146),i=function(e,t){return r.createElement(a.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:l}))};var c=r.forwardRef(i)},51042:function(e,t,n){var o=n(1413),r=n(67294),l=n(42110),a=n(91146),i=function(e,t){return r.createElement(a.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:l.Z}))},c=r.forwardRef(i);t.Z=c},21179:function(e,t,n){n.d(t,{Z:function(){return c}});var o=n(1413),r=n(67294),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M483.2 790.3L861.4 412c1.7-1.7 2.5-4 2.3-6.3l-25.5-301.4c-.7-7.8-6.8-13.9-14.6-14.6L522.2 64.3c-2.3-.2-4.7.6-6.3 2.3L137.7 444.8a8.03 8.03 0 000 11.3l334.2 334.2c3.1 3.2 8.2 3.2 11.3 0zm62.6-651.7l224.6 19 19 224.6L477.5 694 233.9 450.5l311.9-311.9zm60.16 186.23a48 48 0 1067.88-67.89 48 48 0 10-67.88 67.89zM889.7 539.8l-39.6-39.5a8.03 8.03 0 00-11.3 0l-362 361.3-237.6-237a8.03 8.03 0 00-11.3 0l-39.6 39.5a8.03 8.03 0 000 11.3l243.2 242.8 39.6 39.5c3.1 3.1 8.2 3.1 11.3 0l407.3-406.6c3.1-3.1 3.1-8.2 0-11.3z"}}]},name:"tags",theme:"outlined"},a=n(91146),i=function(e,t){return r.createElement(a.Z,(0,o.Z)((0,o.Z)({},e),{},{ref:t,icon:l}))};var c=r.forwardRef(i)},81643:function(e,t,n){n.d(t,{Z:function(){return o}});const o=e=>e?"function"==typeof e?e():e:null},86738:function(e,t,n){n.d(t,{Z:function(){return w}});var o=n(67294),r=n(29950),l=n(93967),a=n.n(l),i=n(21770),c=n(98423),s=n(53124),d=n(55241),p=n(86743),u=n(81643),g=n(83622),m=n(33671),f=n(10110),b=n(24457),v=n(66330),y=n(83559);var h=(0,y.I$)("Popconfirm",(e=>(e=>{const{componentCls:t,iconCls:n,antCls:o,zIndexPopup:r,colorText:l,colorWarning:a,marginXXS:i,marginXS:c,fontSize:s,fontWeightStrong:d,colorTextHeading:p}=e;return{[t]:{zIndex:r,[`&${o}-popover`]:{fontSize:s},[`${t}-message`]:{marginBottom:c,display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${t}-message-icon ${n}`]:{color:a,fontSize:s,lineHeight:1,marginInlineEnd:c},[`${t}-title`]:{fontWeight:d,color:p,"&:only-child":{fontWeight:"normal"}},[`${t}-description`]:{marginTop:i,color:l}},[`${t}-buttons`]:{textAlign:"end",whiteSpace:"nowrap",button:{marginInlineStart:c}}}}})(e)),(e=>{const{zIndexPopupBase:t}=e;return{zIndexPopup:t+60}}),{resetStyle:!1}),C=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const x=e=>{const{prefixCls:t,okButtonProps:n,cancelButtonProps:l,title:a,description:i,cancelText:c,okText:d,okType:v="primary",icon:y=o.createElement(r.Z,null),showCancel:h=!0,close:C,onConfirm:x,onCancel:O,onPopupClick:$}=e,{getPrefixCls:E}=o.useContext(s.E_),[w]=(0,f.Z)("Popconfirm",b.Z.Popconfirm),Z=(0,u.Z)(a),k=(0,u.Z)(i);return o.createElement("div",{className:`${t}-inner-content`,onClick:$},o.createElement("div",{className:`${t}-message`},y&&o.createElement("span",{className:`${t}-message-icon`},y),o.createElement("div",{className:`${t}-message-text`},Z&&o.createElement("div",{className:`${t}-title`},Z),k&&o.createElement("div",{className:`${t}-description`},k))),o.createElement("div",{className:`${t}-buttons`},h&&o.createElement(g.ZP,Object.assign({onClick:O,size:"small"},l),c||(null==w?void 0:w.cancelText)),o.createElement(p.Z,{buttonProps:Object.assign(Object.assign({size:"small"},(0,m.nx)(v)),n),actionFn:x,close:C,prefixCls:E("btn"),quitOnNullishReturnValue:!0,emitEvent:!0},d||(null==w?void 0:w.okText))))};var O=e=>{const{prefixCls:t,placement:n,className:r,style:l}=e,i=C(e,["prefixCls","placement","className","style"]),{getPrefixCls:c}=o.useContext(s.E_),d=c("popconfirm",t),[p]=h(d);return p(o.createElement(v.ZP,{placement:n,className:a()(d,r),style:l,content:o.createElement(x,Object.assign({prefixCls:d},i))}))},$=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const E=o.forwardRef(((e,t)=>{var n,l;const{prefixCls:p,placement:u="top",trigger:g="click",okType:m="primary",icon:f=o.createElement(r.Z,null),children:b,overlayClassName:v,onOpenChange:y,onVisibleChange:C,overlayStyle:O,styles:E,classNames:w}=e,Z=$(e,["prefixCls","placement","trigger","okType","icon","children","overlayClassName","onOpenChange","onVisibleChange","overlayStyle","styles","classNames"]),{getPrefixCls:k,className:j,style:S,classNames:P,styles:N}=(0,s.dj)("popconfirm"),[z,B]=(0,i.Z)(!1,{value:null!==(n=e.open)&&void 0!==n?n:e.visible,defaultValue:null!==(l=e.defaultOpen)&&void 0!==l?l:e.defaultVisible}),I=(e,t)=>{B(e,!0),null==C||C(e),null==y||y(e,t)},T=k("popconfirm",p),W=a()(T,j,v,P.root,null==w?void 0:w.root),L=a()(P.body,null==w?void 0:w.body),[R]=h(T);return R(o.createElement(d.Z,Object.assign({},(0,c.Z)(Z,["title"]),{trigger:g,placement:u,onOpenChange:(t,n)=>{const{disabled:o=!1}=e;o||I(t,n)},open:z,ref:t,classNames:{root:W,body:L},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},N.root),S),O),null==E?void 0:E.root),body:Object.assign(Object.assign({},N.body),null==E?void 0:E.body)},content:o.createElement(x,Object.assign({okType:m,icon:f},e,{prefixCls:T,close:e=>{I(!1,e)},onConfirm:t=>{var n;return null===(n=e.onConfirm)||void 0===n?void 0:n.call(void 0,t)},onCancel:t=>{var n;I(!1,t),null===(n=e.onCancel)||void 0===n||n.call(void 0,t)}})),"data-popover-inject":!0}),b))}));E._InternalPanelDoNotUseOrYouWillBeFired=O;var w=E},66330:function(e,t,n){n.d(t,{aV:function(){return p}});var o=n(67294),r=n(93967),l=n.n(r),a=n(92419),i=n(81643),c=n(53124),s=n(20136),d=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const p=e=>{let{title:t,content:n,prefixCls:r}=e;return t||n?o.createElement(o.Fragment,null,t&&o.createElement("div",{className:`${r}-title`},t),n&&o.createElement("div",{className:`${r}-inner-content`},n)):null},u=e=>{const{hashId:t,prefixCls:n,className:r,style:c,placement:s="top",title:d,content:u,children:g}=e,m=(0,i.Z)(d),f=(0,i.Z)(u),b=l()(t,n,`${n}-pure`,`${n}-placement-${s}`,r);return o.createElement("div",{className:b,style:c},o.createElement("div",{className:`${n}-arrow`}),o.createElement(a.G,Object.assign({},e,{className:t,prefixCls:n}),g||o.createElement(p,{prefixCls:n,title:m,content:f})))};t.ZP=e=>{const{prefixCls:t,className:n}=e,r=d(e,["prefixCls","className"]),{getPrefixCls:a}=o.useContext(c.E_),i=a("popover",t),[p,g,m]=(0,s.Z)(i);return p(o.createElement(u,Object.assign({},r,{prefixCls:i,hashId:g,className:l()(n,m)})))}},55241:function(e,t,n){var o=n(67294),r=n(93967),l=n.n(r),a=n(21770),i=n(15105),c=n(81643),s=n(33603),d=n(96159),p=n(83062),u=n(66330),g=n(53124),m=n(20136),f=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const b=o.forwardRef(((e,t)=>{var n,r;const{prefixCls:b,title:v,content:y,overlayClassName:h,placement:C="top",trigger:x="hover",children:O,mouseEnterDelay:$=.1,mouseLeaveDelay:E=.1,onOpenChange:w,overlayStyle:Z={},styles:k,classNames:j}=e,S=f(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:P,className:N,style:z,classNames:B,styles:I}=(0,g.dj)("popover"),T=P("popover",b),[W,L,R]=(0,m.Z)(T),M=P(),_=l()(h,L,R,N,B.root,null==j?void 0:j.root),H=l()(B.body,null==j?void 0:j.body),[D,V]=(0,a.Z)(!1,{value:null!==(n=e.open)&&void 0!==n?n:e.visible,defaultValue:null!==(r=e.defaultOpen)&&void 0!==r?r:e.defaultVisible}),F=(e,t)=>{V(e,!0),null==w||w(e,t)},X=(0,c.Z)(v),A=(0,c.Z)(y);return W(o.createElement(p.Z,Object.assign({placement:C,trigger:x,mouseEnterDelay:$,mouseLeaveDelay:E},S,{prefixCls:T,classNames:{root:_,body:H},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},I.root),z),Z),null==k?void 0:k.root),body:Object.assign(Object.assign({},I.body),null==k?void 0:k.body)},ref:t,open:D,onOpenChange:e=>{F(e)},overlay:X||A?o.createElement(u.aV,{prefixCls:T,title:X,content:A}):null,transitionName:(0,s.m)(M,"zoom-big",S.transitionName),"data-popover-inject":!0}),(0,d.Tm)(O,{onKeyDown:e=>{var t,n;o.isValidElement(O)&&(null===(n=null==O?void 0:(t=O.props).onKeyDown)||void 0===n||n.call(t,e)),(e=>{e.keyCode===i.Z.ESC&&F(!1,e)})(e)}})))}));b._InternalPanelDoNotUseOrYouWillBeFired=u.ZP,t.Z=b},20136:function(e,t,n){var o=n(14747),r=n(50438),l=n(97414),a=n(79511),i=n(8796),c=n(83559),s=n(83262);const d=e=>{const{componentCls:t,popoverColor:n,titleMinWidth:r,fontWeightStrong:a,innerPadding:i,boxShadowSecondary:c,colorTextHeading:s,borderRadiusLG:d,zIndexPopup:p,titleMarginBottom:u,colorBgElevated:g,popoverBg:m,titleBorderBottom:f,innerContentPadding:b,titlePadding:v}=e;return[{[t]:Object.assign(Object.assign({},(0,o.Wf)(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:p,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":g,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{backgroundColor:m,backgroundClip:"padding-box",borderRadius:d,boxShadow:c,padding:i},[`${t}-title`]:{minWidth:r,marginBottom:u,color:s,fontWeight:a,borderBottom:f,padding:v},[`${t}-inner-content`]:{color:n,padding:b}})},(0,l.ZP)(e,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${t}-content`]:{display:"inline-block"}}}]},p=e=>{const{componentCls:t}=e;return{[t]:i.i.map((n=>{const o=e[`${n}6`];return{[`&${t}-${n}`]:{"--antd-arrow-background-color":o,[`${t}-inner`]:{backgroundColor:o},[`${t}-arrow`]:{background:"transparent"}}}}))}};t.Z=(0,c.I$)("Popover",(e=>{const{colorBgElevated:t,colorText:n}=e,o=(0,s.IX)(e,{popoverBg:t,popoverColor:n});return[d(o),p(o),(0,r._y)(o,"zoom-big")]}),(e=>{const{lineWidth:t,controlHeight:n,fontHeight:o,padding:r,wireframe:i,zIndexPopupBase:c,borderRadiusLG:s,marginXS:d,lineType:p,colorSplit:u,paddingSM:g}=e,m=n-o,f=m/2,b=m/2-t,v=r;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:c+30},(0,a.w)(e)),(0,l.wZ)({contentRadius:s,limitVerticalRadius:!0})),{innerPadding:i?0:12,titleMarginBottom:i?0:d,titlePadding:i?`${f}px ${v}px ${b}px`:0,titleBorderBottom:i?`${t}px ${p} ${u}`:"none",innerContentPadding:i?`${g}px ${v}px`:0})}),{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]})},66309:function(e,t,n){n.d(t,{Z:function(){return P}});var o=n(67294),r=n(93967),l=n.n(r),a=n(98423),i=n(98787),c=n(69760),s=n(96159),d=n(45353),p=n(53124),u=n(11568),g=n(15063),m=n(14747),f=n(83262),b=n(83559);const v=e=>{const{lineWidth:t,fontSizeIcon:n,calc:o}=e,r=e.fontSizeSM;return(0,f.IX)(e,{tagFontSize:r,tagLineHeight:(0,u.bf)(o(e.lineHeightSM).mul(r).equal()),tagIconSize:o(n).sub(o(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},y=e=>({defaultBg:new g.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var h=(0,b.I$)("Tag",(e=>(e=>{const{paddingXXS:t,lineWidth:n,tagPaddingHorizontal:o,componentCls:r,calc:l}=e,a=l(o).sub(n).equal(),i=l(t).sub(n).equal();return{[r]:Object.assign(Object.assign({},(0,m.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:a,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,u.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${r}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${r}-close-icon`]:{marginInlineStart:i,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${r}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${r}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:a}}),[`${r}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(v(e))),y),C=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const x=o.forwardRef(((e,t)=>{const{prefixCls:n,style:r,className:a,checked:i,onChange:c,onClick:s}=e,d=C(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:u,tag:g}=o.useContext(p.E_),m=u("tag",n),[f,b,v]=h(m),y=l()(m,`${m}-checkable`,{[`${m}-checkable-checked`]:i},null==g?void 0:g.className,a,b,v);return f(o.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},r),null==g?void 0:g.style),className:y,onClick:e=>{null==c||c(!i),null==s||s(e)}})))}));var O=x,$=n(98719);var E=(0,b.bk)(["Tag","preset"],(e=>(e=>(0,$.Z)(e,((t,n)=>{let{textColor:o,lightBorderColor:r,lightColor:l,darkColor:a}=n;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:o,background:l,borderColor:r,"&-inverse":{color:e.colorTextLightSolid,background:a,borderColor:a},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(v(e))),y);const w=(e,t,n)=>{const o="string"!=typeof(r=n)?r:r.charAt(0).toUpperCase()+r.slice(1);var r;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${n}`],background:e[`color${o}Bg`],borderColor:e[`color${o}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var Z=(0,b.bk)(["Tag","status"],(e=>{const t=v(e);return[w(t,"success","Success"),w(t,"processing","Info"),w(t,"error","Error"),w(t,"warning","Warning")]}),y),k=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const j=o.forwardRef(((e,t)=>{const{prefixCls:n,className:r,rootClassName:u,style:g,children:m,icon:f,color:b,onClose:v,bordered:y=!0,visible:C}=e,x=k(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:O,direction:$,tag:w}=o.useContext(p.E_),[j,S]=o.useState(!0),P=(0,a.Z)(x,["closeIcon","closable"]);o.useEffect((()=>{void 0!==C&&S(C)}),[C]);const N=(0,i.o2)(b),z=(0,i.yT)(b),B=N||z,I=Object.assign(Object.assign({backgroundColor:b&&!B?b:void 0},null==w?void 0:w.style),g),T=O("tag",n),[W,L,R]=h(T),M=l()(T,null==w?void 0:w.className,{[`${T}-${b}`]:B,[`${T}-has-color`]:b&&!B,[`${T}-hidden`]:!j,[`${T}-rtl`]:"rtl"===$,[`${T}-borderless`]:!y},r,u,L,R),_=e=>{e.stopPropagation(),null==v||v(e),e.defaultPrevented||S(!1)},[,H]=(0,c.Z)((0,c.w)(e),(0,c.w)(w),{closable:!1,closeIconRender:e=>{const t=o.createElement("span",{className:`${T}-close-icon`,onClick:_},e);return(0,s.wm)(e,t,(e=>({onClick:t=>{var n;null===(n=null==e?void 0:e.onClick)||void 0===n||n.call(e,t),_(t)},className:l()(null==e?void 0:e.className,`${T}-close-icon`)})))}}),D="function"==typeof x.onClick||m&&"a"===m.type,V=f||null,F=V?o.createElement(o.Fragment,null,V,m&&o.createElement("span",null,m)):m,X=o.createElement("span",Object.assign({},P,{ref:t,className:M,style:I}),F,H,N&&o.createElement(E,{key:"preset",prefixCls:T}),z&&o.createElement(Z,{key:"status",prefixCls:T}));return W(D?o.createElement(d.Z,{component:"Tag"},X):X)})),S=j;S.CheckableTag=O;var P=S},64019:function(e,t,n){n.d(t,{Z:function(){return r}});var o=n(73935);function r(e,t,n,r){var l=o.unstable_batchedUpdates?function(e){o.unstable_batchedUpdates(n,e)}:n;return null!=e&&e.addEventListener&&e.addEventListener(t,l,r),{remove:function(){null!=e&&e.removeEventListener&&e.removeEventListener(t,l,r)}}}}}]);