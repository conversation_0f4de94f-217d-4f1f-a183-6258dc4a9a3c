提示词
```
本页面将介绍如何在本项目中添加一个新的功能，以“营销助手”功能为例，告诉初学者如何添加一个新的功能。

基本步骤如下：
确定组件名称中因为，比如营销助手，则组件名称为 marketingAssistant
## 后端操作
1 添加后端权限：在 backend/src/app/role_tree.py 中添加权限项
2 添加后端接口：在 backend/src/app/routes/marketingAssistant.py 中添加接口文件，新建时可以参考其他接口文件
3 如果需要数据库：在 backend/src/app/models/marketingAssistant.py 中添加数据库模型，新建时可以参考其他模型文件
4.在启动程序中引入接口文件 backend/src/app/main.py

## 前端操作
1.添加前端的菜单 在项目目录  config/router.ts 中添加路由,如果菜单不显示（ hideInMenu: true）
2.添加前端功能页面 在项目目录 src/pages/ 中添加页面文件。目录结构如下：
    src/pages/ 
        - 目录名：功能名 marketingAssistant
            - index.tsx 主页面
            - service.ts 请求函数
            - types.ts 类型文件
            - styles.less 样式文件
            - components 组件目录
                - 目录名：组件名
                    - index.tsx 组件
                    - types.ts 类型文件
                    - styles.less 样式文件

## 集成
1. 前端service.ts 中添加请求函数 要和后端接口文件名一致
2. 前端types.ts 中添加类型 要和后端接口返回类型一致
```

根据上面步骤，早此文档下面补充详细的文档


