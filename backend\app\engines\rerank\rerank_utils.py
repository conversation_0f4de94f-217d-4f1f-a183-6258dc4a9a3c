from dataclasses import dataclass
from typing import List, Dict, Tuple
from app.utils.logging_config import get_logger, setup_logging


# 设置日志
setup_logging()
logger = get_logger(__name__)

from typing import List, Dict
from dataclasses import dataclass
import requests

@dataclass
class RerankResult:
    """重排序结果"""
    score: float
    id: any

def rerank_texts(
    query: str,
    texts: List[Dict[str, str]],
    config: Dict[str, str],
    top_k: int = None,
    score_threshold: float = 0.0000000000001,
    retry_count: int = 3
) -> List[RerankResult]:
    """
    对文本列表进行重排序
    
    Args:
        query: 查询文本
        texts: 待重排序的文本列表
        config: 配置信息，格式如下：
            {
                "api_key": "your-api-key",
                "model": "bge-rerank-m3",  # 模型名称
                "service_url": "http://api.roardata.cn/v1"  # 服务地址
            }
        top_k: 返回前k个结果，如果为None则返回所有结果
        score_threshold: 分数阈值，只返回分数高于阈值的结果
        retry_count: 重试次数
    
    Returns:
        List[RerankResult]: 重排序结果列表，按相关性分数从高到低排序
    """
    logger.info(query)
    logger.info(texts)
    logger.info(config)

    if not texts:
        return []

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {config.get('api_key', 'OTEzZWQ2OTc4YTBlZWY2MDBjYjc5ZGNjMDViYzg1NmIyNThkODg5MQ==')}"
    }

    # inputs = [{'id':i['id'],'text':i['a']} for i in texts]
    


    data = {
        "query": query,
        "inputs": texts
    }
    # 获取基础 URL
    base_url = config.get("service_url", "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/reranker_base_finetune/api/v1/rerank")
    if not base_url.endswith('/rerank'):
        base_url = base_url.rstrip('/') + '/rerank'

    for attempt in range(retry_count):
        try:
            # 使用 requests 进行同步请求
            logger.info(headers)
            logger.info(data)
            logger.info(base_url)
            response = requests.post(base_url, headers=headers, json=data)

            if response.status_code == 200:
                result = response.json()
                logger.info(f"Rerank API Response: {result}")

                

                # 创建结果对象列表
                rerank_results = [
                    RerankResult( score=i['score'], id=i['id'])  # 假设 id 为索引
                    for i in result.get('data',[])
                ]

                # 按分数降序排序
                rerank_results.sort(key=lambda x: x.score, reverse=True)
                logger.info(f"重拍结果: {rerank_results}")
                # 应用分数阈值过滤
                if score_threshold is not None:
                    rerank_results = [
                        r for r in rerank_results
                        if r.score >= score_threshold
                    ]

                # 应用 top_k 限制
                if top_k is not None:
                    rerank_results = rerank_results[:top_k]

                logger.info(f"返回 重拍结果: {rerank_results}")
                return rerank_results
            else:
                error_text = response.text
                logger.error(f"Rerank API 请求失败: {error_text}")
                if attempt == retry_count - 1:
                    return []

        except Exception as e:
            logger.error(f"重排序过程发生错误: {str(e)}")
            if attempt == retry_count - 1:
                return []

        if attempt < retry_count - 1:
            logger.info(f"第 {attempt + 1} 次重试重排序...")

    return []


def rerank_texts_with_metadata(
        query: str,
        texts_with_metadata: List[Tuple[str, Dict]],
        config: Dict[str, str],
        top_k: int = None,
        score_threshold: float = None,
        retry_count: int = 3
    ) -> List[RerankResult]:
        """
        对带元数据的文本列表进行重排序
        
        Args:
            query: 查询文本
            texts_with_metadata: 待重排序的文本和元数据列表，格式为 [(text, metadata), ...]
            config: 配置信息
            top_k: 返回前k个结果
            score_threshold: 分数阈值
            retry_count: 重试次数
        
        Returns:
            List[RerankResult]: 重排序结果列表
        """
        texts = [text for text, _ in texts_with_metadata]
        metadata_list = [metadata for _, metadata in texts_with_metadata]
        
        rerank_results = rerank_texts(
            query=query,
            texts=texts,
            config=config,
            top_k=top_k,
            score_threshold=score_threshold,
            retry_count=retry_count
        )
        
        # 将元数据添加到结果中
        for result, metadata in zip(rerank_results, metadata_list):
            result.metadata = metadata
        
        return rerank_results 