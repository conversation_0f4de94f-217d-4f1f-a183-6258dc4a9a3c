# 系统文件处理切块策略

本文档主要用于记录系统文件处理切块策略，包括文件的切块、存储、检索等。


## 1. 主要概念
主要涵盖了文件的知识库、文件、页面、切块、检索等。

1. 知识库：
  - 文件的知识库，分为系统知识库和用户知识库。系统知识库有系统管理员管理，用户知识库由用户管理。
  - 知识库内容具备审核机制，确保内容的安全性和准确性。

2. 文件：用户上传的文件。
  - 文件名：基础支持、csv、pdf、docx、txt、md等。
3. 页面：文件的页面。
  - 将pdf、docx、txt、md等文件切分为页面。主要用户展示定位和切块
3. 切块：文件的切块。
  - chunk 信息，用来做相关的索引
  - 
## 2 实体关系
- 知识库 1:n 文件 (一个知识库包含多个文件)
- 文件 1:n 页面 (一个文件包含多个页面)
- 页面 1:n 切块 (一个页面包含多个切块)
- 知识库 1:n 切块 (多个切块属于一个知识库)

## 3 处理流程
1. 用户新建知识库
2. 用户上传文件
   这里可能有什么逻辑
3. 对部分具有页面的属性的文件极性分页处理，例如pdf、docx、excle(sheet)等
4. 对文件进行切块处理，若果已经分页，则基于分页结果进行处理。如果无需分页的直接分块处理。
   这里可能有审核逻辑
5. 将切块信息写入数据库，切入es进行搜索。




