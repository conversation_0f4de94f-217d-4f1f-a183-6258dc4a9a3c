
# PAI 自带vLLM部署的接口

| 路由路径                  | 请求方法      | 中文说明                      |
|--------------------------|-------------|-----------------------------|
| /openapi.json            | GET, HEAD   | 获取OpenAPI规范文档           |
| /docs                    | GET, HEAD   | 交互式API文档页面              |
| /docs/oauth2-redirect    | GET, HEAD   | OAuth2重定向端点              |
| /redoc                   | GET, HEAD   | Redoc文档页面                |
| /health                  | GET         | 服务健康检查端点              |
| /tokenize                | POST        | 文本分词处理接口              |
| /detokenize              | POST        | 分词还原文本接口              |
| /v1/models               | GET         | 获取可用模型列表              |
| /version                 | GET         | 获取服务版本信息              |
| /v1/chat/completions     | POST        | 聊天补全接口                 |
| /v1/completions          | POST        | 文本补全接口                 |
| /v1/embeddings           | POST        | 生成文本嵌入向量              |
| /pooling                 | POST        | 池化操作接口                 |
| /score                   | POST        | 文本评分接口                 |
| /v1/score                | POST        | 评分接口(v1版本)             |

# 阿里pai部署：
Qwen2-14B-Instruct
http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_14b_instruct
ZjI1MDkzMDMyYmQ0MzA2MGYxNTQ5MGIxNWU3NDIxODk0MWMxMWFhYw==


qwen2.5-7b-instruct
http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen2_5_7b_instrcut
ODc5YzllNzk1ZjRmZmVhMTkzMGFjZWQyNzg4MjNjNDY0ZDE3MjA3Mw==

DeepSeek-R1-Distill-Qwen-32B
http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/deepseek_r1_distill_qwen_32b
OGMzOGY0NWFmYmFlMDZiNGQ3MTg2OGU5ZjBiZmU3NzNkNDE4N2I1MA==



DeepSeek-R1-Distill-Qwen-14B
http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/deepseek_r1_distill_qwen_14b
MjA3ZDcxYTI3NzJlYTA0ZjY5MDk3OWQ5MGI1YTE2YzNjOWI1ZjUyNA==