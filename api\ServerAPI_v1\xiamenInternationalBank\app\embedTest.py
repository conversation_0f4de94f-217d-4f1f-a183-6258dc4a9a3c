from dotenv import load_dotenv
import os
import requests
from pathlib import Path

# 获取当前文件的目录
current_dir = Path(__file__).parent
# 获取 .env 文件的绝对路径
env_path = (current_dir.parent / '.env').resolve()

# 加载环境变量
load_dotenv(env_path)

# 从环境变量中获取配置
access_token = os.getenv('EMBEDDING_API_KEY')
url = os.getenv('EMBEDDING_API_URL')

if not url or not access_token:
    raise ValueError(f"Missing configuration - URL: {url}, Token: {'exists' if access_token else 'missing'}")

headers = {
    'Content-Type': 'application/json',
    'Authorization': f'Bearer {access_token}'
}

def embedding(data):
    json = {
        "input": [data],
        "model": "bge-large"
    }
    res = requests.post(url, json=json, headers=headers)
    return res.json()

# 备注："input"字段的值为待向量化内容列表，建议一次调用最多传十条内容
# data = {
#         "input": ["信托业务分类通知正式下发 涉及三大变化", "第十一条 国务院反洗钱行政主管部门为履行反洗钱资金监测职责"],
#         "model": "bge-large"
#     }
# data = "信托业务分类通知正式下发涉及三大变化"
# response = embedding(data)
# data=response.get("data")[0].get("embedding")
# print(data)
