{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 导入所需的库\n", "from datetime import datetime, timedelta\n", "from bson import ObjectId\n", "from mongoengine import connect\n", "import json\n", "\n", "# 连接MongoDB数据库\n", "connect('roardataAiApp_test')  # 替换为你的数据库名称\n", "\n", "# 定义ApiToken模型类\n", "from mongoengine import Document, ObjectIdField, StringField, IntField, DateTimeField, BooleanField\n", "\n", "class ApiToken(Document):\n", "    meta = {\n", "        'collection': 'api_tokens'\n", "    }\n", "    _id = ObjectIdField(primary_key=True, default=ObjectId)\n", "    token = StringField(required=True, unique=True)    \n", "    app_id = StringField(required=True)                \n", "    app_name = StringField(required=True)              \n", "    user_id = IntField(required=True)                  \n", "    created_at = DateTimeField(default=datetime.now)   \n", "    expires_at = DateTimeField(required=True)          \n", "    last_used_at = DateTimeField()                     \n", "    is_active = BooleanField(default=True)            \n", "    use_count = IntField(default=0)                   \n", "    permissions = StringField(default='read')          \n", "    description = StringField()                        \n", "    ip_whitelist = StringField()                      \n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MongoDB连接失败：A different connection with alias `default` was already registered. Use disconnect() first\n"]}], "source": ["# 导入所需的库\n", "from datetime import datetime, timedelta\n", "from bson import ObjectId\n", "from mongoengine import connect\n", "import json\n", "\n", "# 连接MongoDB数据库\n", "MONGODB_URI = \"*********************************************************************\"\n", "try:\n", "    connect(host=MONGODB_URI)\n", "    print(\"MongoDB连接成功！\")\n", "except Exception as e:\n", "    print(f\"MongoDB连接失败：{str(e)}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 保存数据\n", "test_token.save()\n", "\n", "# 验证数据\n", "result = ApiToken.objects(token=\"test_token_12345\").first()\n", "print(\"插入的数据:\")\n", "print(json.loads(result.to_json()))\n", "\n", "# 查询总数\n", "total_count = ApiToken.objects.count()\n", "print(f\"\\n数据库中的总记录数: {total_count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建测试数据\n", "test_token = ApiToken(\n", "    token=\"test_token_12345\",\n", "    app_id=\"test_app_001\",\n", "    app_name=\"测试应用\",\n", "    user_id=1001,\n", "    expires_at=datetime.now() + timed<PERSON>ta(days=30),\n", "    description=\"这是一个测试用的API Token\",\n", "    ip_whitelist=\"127.0.0.1,***********\"\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["{\n", "    \"token\": \"sk-test-roarAI-123456789\",\n", "    \"app_id\": \"roar_app_001\",\n", "    \"app_name\": \"RoarAI测试应用\",\n", "    \"user_id\": 1,\n", "    \"created_at\": {\"$date\": \"2024-03-19T10:00:00Z\"},\n", "    \"expires_at\": {\"$date\": \"2024-04-19T10:00:00Z\"},\n", "    \"last_used_at\": null,\n", "    \"is_active\": true,\n", "    \"use_count\": 0,\n", "    \"permissions\": \"read\",\n", "    \"description\": \"这是一个用于测试的API Token\",\n", "    \"ip_whitelist\": \"127.0.0.1,***********\"\n", "}"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["| 科目 | 2023年年度 | 2022年年度 | 2021年年度 |\n", "| --- | --- | --- | --- |\n", "| 资产 | 977855.88 | 986333.81 | 1084403.75 |\n", "| 流动资产： | 401016.06 | 405283.77 | 483245.73 |\n", "| 货币资金 | 150489.28 | 134499.12 | 156705.05 |\n", "| 交易性金融资产 | 15.27 | 98.14 | 0 |\n", "| 衍生金融资产 | - | - | - |\n", "| 应收票据 | 41424.37 | 57118.75 | 70592.75 |\n", "| 应收账款 | 74729.01 | 63327.11 | 73254.55 |\n", "| 应收款项融资 | 2184.53 | 6486.54 | 38002.44 |\n", "| 预付款项 | 6908.78 | 8864.32 | 7422.2 |\n", "| 其他应收款 | 4872.13 | 5476.22 | 5471.53 |\n", "| 存货 | 105131.25 | 122087.19 | 118744.37 |\n", "| 合同资产 | 0 | 0 | 0 |\n", "| 持有待售资产 | 0 | 0 | 0 |\n", "| 一年内到期的非流动资产 | 0 | 0 | 0 |\n", "| 其他流动资产 | 15261.43 | 7326.37 | 13052.84 |\n", "| 流动资产合计 | 401016.06 | 405283.77 | 483245.73 |\n", "| 非流动资产： | 576839.81 | 581050.05 | 601158.02 |\n", "| 债权投资 | 0 | 0 | 0 |\n", "| 其他债权投资 | 0 | 0 | 0 |\n", "| 长期应收款 | 0 | 0 | 0 |\n", "| 长期股权投资 | 83815.88 | 71645.61 | 66119.71 |\n", "| 其他权益工具投资 | 60.78 | 1237.14 | 2855.8 |\n", "| 其他非流动金融资产 | 0 | 0 | 0 |\n", "| 投资性房地产 | 0 | 0 | 0 |\n", "| 固定资产 | 370751.64 | 427505.23 | 435971.7 |\n", "| 在建工程 | 60526.01 | 25020.19 | 35611.84 |\n", "| 生产性生物资产 | 0 | 0 | 0 |\n", "| 油气资产 | 0 | 0 | 0 |\n", "| 使用权资产 | 0 | 573.13 | 617.26 |\n", "| 无形资产 | 39912.84 | 38394.37 | 40285.89 |\n", "| 开发支出 | 0 | 0 | 0 |\n", "| 商誉 | 0 | 0 | 0 |\n", "| 长期待摊费用 | 1223.98 | 189.54 | 86.27 |\n", "| 递延所得税资产 | 11219.4 | 13503.89 | 13926.78 |\n", "| 其他非流动资产 | 9329.29 | 2980.96 | 5645.77 |\n", "| 非流动资产合计 | 576839.81 | 581050.05 | 601158.02 |\n", "| 资产总计 | 977855.88 | 986333.81 | 1084403.75 |\n", "| 负债和所有者权益 | - | - | - |\n", "| 流动负债： | 597629.08 | 651399.36 | 638263.05 |\n", "| 短期借款 | 371771.62 | 390369.9 | 375451.75 |\n", "| 交易性金融负债 | 15.89 | 0 | 0 |\n", "| 衍生金融负债 | 0 | 0 | 0 |\n", "| 应付票据 | 20611.31 | 52617.95 | 60182.83 |\n", "| 应付账款 | 92375.93 | 84817.01 | 93013.22 |\n", "| 预收款项 | 192.49 | 181.17 | 334.26 |\n", "| 合同负债 | 11974.06 | 15651.22 | 12138.82 |\n", "| 应付职工薪酬 | 8745.78 | 10651.77 | 7936.52 |\n", "| 应交税费 | 1054.97 | 825.67 | 666.59 |\n", "| 其他应付款 | 17335.62 | 17346.92 | 17389.68 |\n", "| 持有待售负债 | 0 | 0 | 0 |\n", "| 一年内到期的非流动负债 | 13721.24 | 43494.54 | 44570.58 |\n", "| 其他流动负债 | 59830.17 | 35443.23 | 26578.8 |\n", "| 流动负债合计 | 597629.08 | 651399.36 | 638263.05 |\n", "| 非流动负债： | 171913.37 | 96629.74 | 133095.11 |\n", "| 长期借款 | 135775 | 48651.63 | 83654.43 |\n", "| 应付债券 | 0 | 0 | 0 |\n", "| 其中：优先股（应付债券） | - | - | - |\n", "| 永续债（应付债券） | - | - | - |\n", "| 租赁负债 | 0 | 57.45 | 204.04 |\n", "| 长期应付款 | 3525.52 | 5794.13 | 5204.79 |\n", "| 预计负债 | 0 | 0 | 0 |\n", "| 递延收益 | 0 | 0 | 0 |\n", "| 递延所得税负债 | 244.54 | 378.99 | 538.73 |\n", "| 其他非流动负债 | 0 | 0 | 0 |\n", "| 非流动负债合计 | 171913.37 | 96629.74 | 133095.11 |\n", "| 负债合计 | 769542.45 | 748029.1 | 771358.15 |\n", "| 所有者权益（或股东权益）： | - | - | - |\n", "| 实收资本（或股本） | 81675.9 | 81679.25 | 81679.25 |\n", "| 其他权益工具 | 0 | 0 | 0 |\n", "| 其中：优先股（其他权益工具） | 0 | 0 | 0 |\n", "| 永续债（其他权益工具） | 0 | 0 | 0 |\n", "| 资本公积 | 182508.98 | 192077.42 | 193980.27 |\n", "| 减：库存股 | 0 | 10.42 | 10.42 |\n", "| 其他综合收益 | 5940.97 | 4665.03 | -974.37 |\n", "| 专项储备 | 0 | 0 | 0 |\n", "| 盈余公积 | 0 | 0 | 0 |\n", "| 未分配利润 | -63056.83 | -45442.33 | 15567.3 |\n", "| 归属于母公司股东权益合计 | 213442.66 | 239342.59 | 296615.67 |\n", "| 少数股东权益 | -5129.23 | -1037.87 | 16429.93 |\n", "| 所有者权益（或股东权益）合计 | 208313.43 | 238304.71 | 313045.6 |\n", "| 负债和所有者权益（或股东权益）总计 | 977855.88 | 986333.81 | 1084403.75 |\n", "| 一、营业收入 | 465550.02 | 391039.77 | 392452.1 |\n", "| 减：营业成本 | 425350.09 | 385906.18 | 378998.86 |\n", "| 税金及附加 | 3655.44 | 2502.32 | 2580.17 |\n", "| 销售费用 | 17317.43 | 15578.94 | 17657.37 |\n", "| 管理费用 | 17637.38 | 16860.92 | 19951.3 |\n", "| 研发费用 | 20423.88 | 20238.91 | 20303.55 |\n", "| 财务费用 | 17340.18 | 16556.71 | 16219.51 |\n", "| 其中：利息费用 | 19202.04 | 18139.74 | 16753.74 |\n", "| 利息收入 | 0 | 0 | 0 |\n", "| 加：其他收益 | 10561.76 | 6256.43 | 11795.79 |\n", "| 投资收益（损失以“-”号填列） | 16193.47 | 4088.78 | 20751 |\n", "| 其中：对联营企业和合营企业的投资收益 | 13192.89 | 2232.58 | 188.25 |\n", "| 以摊余成本计量的金融资产终止确认收益（损失以“-”号填列） | 0 | 0 | 0 |\n", "| 净敞口套期收益（损失以“-”号填列） | 0 | 0 | 0 |\n", "| 公允价值变动收益（损失以“-”号填列） | -1190.22 | -2808.03 | -666.55 |\n", "| 信用减值损失（损失以“-”号填列） | -4471.29 | -342.45 | -1185.36 |\n", "| 资产减值损失（损失以“-”号填列） | -6505.1 | -9801.88 | -4582.48 |\n", "| 资产处置收益（损失以“-”号填列） | 44.65 | 462.48 | 220.75 |\n", "| 二、营业利润（亏损以“-”号填列） | -21541.1 | -68748.86 | -36925.5 |\n", "| 加：营业外收入 | 403.84 | 91.16 | 42.18 |\n", "| 减：营业外支出 | 188.9 | 34.26 | 148.69 |\n", "| 三、利润总额（亏损总额以“-”号填列） | -21326.16 | -68691.95 | -37032.01 |\n", "| 减：所得税费用 | 2151.69 | 493.15 | 2453.39 |\n", "| 四、净利润（净亏损以“-”号填列） | -23477.85 | -69185.1 | -39485.4 |\n", "| （一）按经营持续性分类： | - | - | - |\n", "| 其中：持续经营净利润（净亏损以“-”号填列） | -23477.85 | -70246.38 | -58283.17 |\n", "| 终止经营净利润（净亏损以“-”号填列） | 0 | 1061.28 | 18797.77 |\n", "| （二）按所有权归属分类： | - | - | - |\n", "| 其中：少数股东损益（净亏损以“-”号填列） | -5863.36 | -8991.87 | -7455.22 |\n", "| 归属于母公司股东的净利润（净亏损以“-”号填列） | -17614.5 | -60193.23 | -32030.18 |\n", "| 五、其他综合收益税后净额 | 1242.58 | 5639.46 | -1016.25 |\n", "| （一）不能重分类进损益的其他综合收益 | - | - | - |\n", "| 1.重新计量设定受益计划变动额 | - | - | - |\n", "| 2.权益法下不能转损益的其他综合收益 | - | - | - |\n", "| 3.其他权益工具投资公允价值变动 | - | - | - |\n", "| 4.企业自身信用风险公允价值变动 | - | - | - |\n", "| （二）将重分类进损益的其他综合收益 | - | - | - |\n", "| 1.权益法下可转损益的其他综合收益 | - | - | - |\n", "| 2.其他债权投资公允价值变动 | - | - | - |\n", "| 3.金融资产重分类计入其他综合收益的金额 | - | - | - |\n", "| 4.其他债权投资信用损失准备 | - | - | - |\n", "| 5.现金流量套期储备 | - | - | - |\n", "| 6.外币财务报表折算差额 | - | - | - |\n", "| 六、综合收益总额 | -22235.28 | -63545.64 | -40501.65 |\n", "| 七、每股收益： | - | - | - |\n", "| （一）基本每股收益 | 0 | 0 | 0 |\n", "| （二）稀释每股收益 | 0 | 0 | 0 |\n", "| 一、经营活动产生的现金流量： | 28033.43 | 33673.64 | -8658.22 |\n", "| 销售商品、提供劳务收到的现金 | 385127.57 | 312084.17 | 246986.44 |\n", "| 收到的税费返还 | 8169.84 | 19861.06 | 20698.91 |\n", "| 收到的其他与经营活动有关的现金 | 20071.12 | 17526.3 | 22538.14 |\n", "| 经营活动现金流入小计 | 413368.53 | 349471.53 | 290223.48 |\n", "| 购买商品、接受劳务支付的现金 | 295317.56 | 233154.26 | 203485.47 |\n", "| 支付给职工以及为职工支付的现金 | 61726.28 | 54653.29 | 66945.49 |\n", "| 支付的各项税费 | 7529.95 | 3227.37 | 4235.11 |\n", "| 支付的其他与经营活动有关的现金 | 20761.3 | 24762.97 | 24215.64 |\n", "| 经营活动现金流出小计 | 385335.1 | 315797.89 | 298881.71 |\n", "| 经营活动产生的现金流量净额 | 28033.43 | 33673.64 | -8658.22 |\n", "| 二、投资活动产生的现金流量： | -40397.6 | -13848.22 | -42030.92 |\n", "| 收回投资所收到的现金 | 1287.08 | 1836.95 | 28114.97 |\n", "| 取得投资收益所收到的现金 | 26.93 | 9.27 | 424.47 |\n", "| 处置固定资产、无形资产和其他长期资产所收回的现金净额 | 1.86 | 533.76 | 5454.87 |\n", "| 处置子公司及其他营业单位收到的现金净额 | 5796.94 | 0 | 32560.48 |\n", "| 收到的其他与投资活动有关的现金 | 0 | 0 | 0 |\n", "| 投资活动现金流入小计 | 7112.81 | 2379.98 | 66554.79 |\n", "| 购建固定资产、无形资产和其他长期资产所支付的现金 | 24263.67 | 16228.19 | 17451.72 |\n", "| 投资所支付的现金 | 21947 | 0 | 91003.52 |\n", "| 取得子公司及其他营业单位支付的现金净额 | 0 | 0 | 130.47 |\n", "| 支付的其他与投资活动有关的现金 | 1299.74 | 0 | 0 |\n", "| 投资活动现金流出小计 | 47510.41 | 16228.19 | 108585.71 |\n", "| 投资活动产生的现金流量净额 | -40397.6 | -13848.22 | -42030.92 |\n", "| 三、筹资活动产生的现金流量： | 3957.1 | -43949.06 | 78046.34 |\n", "| 吸收投资所收到的现金 | 5872.45 | 0 | 5215 |\n", "| 其中：子公司吸收少数股东权益性投资收到的现金 | 5872.45 | 0 | 5215 |\n", "| 取得借款收到的现金 | 495965.81 | 489524.67 | 478244.95 |\n", "| 收到的其他与筹资活动有关的现金 | 2222.68 | 7770.09 | 10985.27 |\n", "| 筹资活动现金流入小计 | 504060.93 | 497294.75 | 494445.22 |\n", "| 偿还债务支付的现金 | 462728.85 | 501729.27 | 392843.27 |\n", "| 分配股利、利润或偿付利息支付的现金 | 14929.04 | 16457.74 | 15848.28 |\n", "| 其中：子公司支付少数股东的现金股利 | 0 | 0 | 0 |\n", "| 支付其他与筹资活动有关的现金 | 22445.94 | 23056.81 | 7707.33 |\n", "| 其中：子公司减资支付给少数股东的现金 | 0 | 0 | 0 |\n", "| 筹资活动现金流出小计 | 500103.83 | 541243.82 | 416398.88 |\n", "| 筹资活动产生的现金流量净额 | 3957.1 | -43949.06 | 78046.34 |\n", "| 四、汇率变动对现金及现金等价物的影响 | 318.41 | 1407.06 | -2074.74 |\n", "| 五、现金及现金等价物净增加额 | -8088.66 | -22716.58 | 25282.45 |\n", "| 加：期初现金及现金等价物余额 | 104632.31 | 127348.89 | 102066.44 |\n", "| 六、期末现金及现金等价物余额 | 96543.65 | 104632.31 | 127348.89 |\n"]}], "source": ["import pandas as pd\n", "\n", "def excel_to_markdown(excel_file, sheet_name=0):\n", "    \"\"\"\n", "    将Excel文件转换为Markdown表格格式\n", "    \n", "    参数:\n", "        excel_file (str): Excel文件路径\n", "        sheet_name (str|int): 工作表名称或索引，默认为第一个工作表\n", "        \n", "    返回:\n", "        str: Markdown格式的表格字符串\n", "    \"\"\"\n", "    try:\n", "        # 读取Excel文件\n", "        df = pd.read_excel(excel_file, sheet_name=sheet_name)\n", "        \n", "        # 获取列名\n", "        headers = df.columns.tolist()\n", "        \n", "        # 构建markdown表格\n", "        markdown_table = []\n", "        \n", "        # 添加表头\n", "        markdown_table.append('| ' + ' | '.join(str(col) for col in headers) + ' |')\n", "        \n", "        # 添加分隔行\n", "        markdown_table.append('| ' + ' | '.join(['---'] * len(headers)) + ' |')\n", "        \n", "        # 添加数据行\n", "        for _, row in df.iterrows():\n", "            markdown_table.append('| ' + ' | '.join(str(cell) for cell in row) + ' |')\n", "        \n", "        # 合并所有行\n", "        return '\\n'.join(markdown_table)\n", "    \n", "    except Exception as e:\n", "        return f\"转换失败: {str(e)}\"\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    # 基本使用\n", "    excel_file = \"./参数数据.xlsx\"\n", "    markdown = excel_to_markdown(excel_file)\n", "    print(markdown)\n", "    \n", "    # 保存到文件\n", "    with open('output.md', 'w', encoding='utf-8') as f:\n", "        f.write(markdown)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["| 科目 | 2023年年度 | 2022年年度 | 2021年年度 |\n", "| --- | --- | --- | --- |\n", "| 资产 | 977855.88 | 986333.81 | 1084403.75 |\n", "| 流动资产： | 401016.06 | 405283.77 | 483245.73 |\n", "| 货币资金 | 150489.28 | 134499.12 | 156705.05 |\n", "| 交易性金融资产 | 15.27 | 98.14 | 0 |\n", "| 衍生金融资产 | - | - | - |\n", "| 应收票据 | 41424.37 | 57118.75 | 70592.75 |\n", "| 应收账款 | 74729.01 | 63327.11 | 73254.55 |\n", "| 应收款项融资 | 2184.53 | 6486.54 | 38002.44 |\n", "| 预付款项 | 6908.78 | 8864.32 | 7422.2 |\n", "| 其他应收款 | 4872.13 | 5476.22 | 5471.53 |\n", "| 存货 | 105131.25 | 122087.19 | 118744.37 |\n", "| 合同资产 | 0 | 0 | 0 |\n", "| 持有待售资产 | 0 | 0 | 0 |\n", "| 一年内到期的非流动资产 | 0 | 0 | 0 |\n", "| 其他流动资产 | 15261.43 | 7326.37 | 13052.84 |\n", "| 流动资产合计 | 401016.06 | 405283.77 | 483245.73 |\n", "| 非流动资产： | 576839.81 | 581050.05 | 601158.02 |\n", "| 债权投资 | 0 | 0 | 0 |\n", "| 其他债权投资 | 0 | 0 | 0 |\n", "| 长期应收款 | 0 | 0 | 0 |\n", "| 长期股权投资 | 83815.88 | 71645.61 | 66119.71 |\n", "| 其他权益工具投资 | 60.78 | 1237.14 | 2855.8 |\n", "| 其他非流动金融资产 | 0 | 0 | 0 |\n", "| 投资性房地产 | 0 | 0 | 0 |\n", "| 固定资产 | 370751.64 | 427505.23 | 435971.7 |\n", "| 在建工程 | 60526.01 | 25020.19 | 35611.84 |\n", "| 生产性生物资产 | 0 | 0 | 0 |\n", "| 油气资产 | 0 | 0 | 0 |\n", "| 使用权资产 | 0 | 573.13 | 617.26 |\n", "| 无形资产 | 39912.84 | 38394.37 | 40285.89 |\n", "| 开发支出 | 0 | 0 | 0 |\n", "| 商誉 | 0 | 0 | 0 |\n", "| 长期待摊费用 | 1223.98 | 189.54 | 86.27 |\n", "| 递延所得税资产 | 11219.4 | 13503.89 | 13926.78 |\n", "| 其他非流动资产 | 9329.29 | 2980.96 | 5645.77 |\n", "| 非流动资产合计 | 576839.81 | 581050.05 | 601158.02 |\n", "| 资产总计 | 977855.88 | 986333.81 | 1084403.75 |\n", "| 负债和所有者权益 | - | - | - |\n", "| 流动负债： | 597629.08 | 651399.36 | 638263.05 |\n", "| 短期借款 | 371771.62 | 390369.9 | 375451.75 |\n", "| 交易性金融负债 | 15.89 | 0 | 0 |\n", "| 衍生金融负债 | 0 | 0 | 0 |\n", "| 应付票据 | 20611.31 | 52617.95 | 60182.83 |\n", "| 应付账款 | 92375.93 | 84817.01 | 93013.22 |\n", "| 预收款项 | 192.49 | 181.17 | 334.26 |\n", "| 合同负债 | 11974.06 | 15651.22 | 12138.82 |\n", "| 应付职工薪酬 | 8745.78 | 10651.77 | 7936.52 |\n", "| 应交税费 | 1054.97 | 825.67 | 666.59 |\n", "| 其他应付款 | 17335.62 | 17346.92 | 17389.68 |\n", "| 持有待售负债 | 0 | 0 | 0 |\n", "| 一年内到期的非流动负债 | 13721.24 | 43494.54 | 44570.58 |\n", "| 其他流动负债 | 59830.17 | 35443.23 | 26578.8 |\n", "| 流动负债合计 | 597629.08 | 651399.36 | 638263.05 |\n", "| 非流动负债： | 171913.37 | 96629.74 | 133095.11 |\n", "| 长期借款 | 135775 | 48651.63 | 83654.43 |\n", "| 应付债券 | 0 | 0 | 0 |\n", "| 其中：优先股（应付债券） | - | - | - |\n", "| 永续债（应付债券） | - | - | - |\n", "| 租赁负债 | 0 | 57.45 | 204.04 |\n", "| 长期应付款 | 3525.52 | 5794.13 | 5204.79 |\n", "| 预计负债 | 0 | 0 | 0 |\n", "| 递延收益 | 0 | 0 | 0 |\n", "| 递延所得税负债 | 244.54 | 378.99 | 538.73 |\n", "| 其他非流动负债 | 0 | 0 | 0 |\n", "| 非流动负债合计 | 171913.37 | 96629.74 | 133095.11 |\n", "| 负债合计 | 769542.45 | 748029.1 | 771358.15 |\n", "| 所有者权益（或股东权益）： | - | - | - |\n", "| 实收资本（或股本） | 81675.9 | 81679.25 | 81679.25 |\n", "| 其他权益工具 | 0 | 0 | 0 |\n", "| 其中：优先股（其他权益工具） | 0 | 0 | 0 |\n", "| 永续债（其他权益工具） | 0 | 0 | 0 |\n", "| 资本公积 | 182508.98 | 192077.42 | 193980.27 |\n", "| 减：库存股 | 0 | 10.42 | 10.42 |\n", "| 其他综合收益 | 5940.97 | 4665.03 | -974.37 |\n", "| 专项储备 | 0 | 0 | 0 |\n", "| 盈余公积 | 0 | 0 | 0 |\n", "| 未分配利润 | -63056.83 | -45442.33 | 15567.3 |\n", "| 归属于母公司股东权益合计 | 213442.66 | 239342.59 | 296615.67 |\n", "| 少数股东权益 | -5129.23 | -1037.87 | 16429.93 |\n", "| 所有者权益（或股东权益）合计 | 208313.43 | 238304.71 | 313045.6 |\n", "| 负债和所有者权益（或股东权益）总计 | 977855.88 | 986333.81 | 1084403.75 |\n", "| 一、营业收入 | 465550.02 | 391039.77 | 392452.1 |\n", "| 减：营业成本 | 425350.09 | 385906.18 | 378998.86 |\n", "| 税金及附加 | 3655.44 | 2502.32 | 2580.17 |\n", "| 销售费用 | 17317.43 | 15578.94 | 17657.37 |\n", "| 管理费用 | 17637.38 | 16860.92 | 19951.3 |\n", "| 研发费用 | 20423.88 | 20238.91 | 20303.55 |\n", "| 财务费用 | 17340.18 | 16556.71 | 16219.51 |\n", "| 其中：利息费用 | 19202.04 | 18139.74 | 16753.74 |\n", "| 利息收入 | 0 | 0 | 0 |\n", "| 加：其他收益 | 10561.76 | 6256.43 | 11795.79 |\n", "| 投资收益（损失以“-”号填列） | 16193.47 | 4088.78 | 20751 |\n", "| 其中：对联营企业和合营企业的投资收益 | 13192.89 | 2232.58 | 188.25 |\n", "| 以摊余成本计量的金融资产终止确认收益（损失以“-”号填列） | 0 | 0 | 0 |\n", "| 净敞口套期收益（损失以“-”号填列） | 0 | 0 | 0 |\n", "| 公允价值变动收益（损失以“-”号填列） | -1190.22 | -2808.03 | -666.55 |\n", "| 信用减值损失（损失以“-”号填列） | -4471.29 | -342.45 | -1185.36 |\n", "| 资产减值损失（损失以“-”号填列） | -6505.1 | -9801.88 | -4582.48 |\n", "| 资产处置收益（损失以“-”号填列） | 44.65 | 462.48 | 220.75 |\n", "| 二、营业利润（亏损以“-”号填列） | -21541.1 | -68748.86 | -36925.5 |\n", "| 加：营业外收入 | 403.84 | 91.16 | 42.18 |\n", "| 减：营业外支出 | 188.9 | 34.26 | 148.69 |\n", "| 三、利润总额（亏损总额以“-”号填列） | -21326.16 | -68691.95 | -37032.01 |\n", "| 减：所得税费用 | 2151.69 | 493.15 | 2453.39 |\n", "| 四、净利润（净亏损以“-”号填列） | -23477.85 | -69185.1 | -39485.4 |\n", "| （一）按经营持续性分类： | - | - | - |\n", "| 其中：持续经营净利润（净亏损以“-”号填列） | -23477.85 | -70246.38 | -58283.17 |\n", "| 终止经营净利润（净亏损以“-”号填列） | 0 | 1061.28 | 18797.77 |\n", "| （二）按所有权归属分类： | - | - | - |\n", "| 其中：少数股东损益（净亏损以“-”号填列） | -5863.36 | -8991.87 | -7455.22 |\n", "| 归属于母公司股东的净利润（净亏损以“-”号填列） | -17614.5 | -60193.23 | -32030.18 |\n", "| 五、其他综合收益税后净额 | 1242.58 | 5639.46 | -1016.25 |\n", "| （一）不能重分类进损益的其他综合收益 | - | - | - |\n", "| 1.重新计量设定受益计划变动额 | - | - | - |\n", "| 2.权益法下不能转损益的其他综合收益 | - | - | - |\n", "| 3.其他权益工具投资公允价值变动 | - | - | - |\n", "| 4.企业自身信用风险公允价值变动 | - | - | - |\n", "| （二）将重分类进损益的其他综合收益 | - | - | - |\n", "| 1.权益法下可转损益的其他综合收益 | - | - | - |\n", "| 2.其他债权投资公允价值变动 | - | - | - |\n", "| 3.金融资产重分类计入其他综合收益的金额 | - | - | - |\n", "| 4.其他债权投资信用损失准备 | - | - | - |\n", "| 5.现金流量套期储备 | - | - | - |\n", "| 6.外币财务报表折算差额 | - | - | - |\n", "| 六、综合收益总额 | -22235.28 | -63545.64 | -40501.65 |\n", "| 七、每股收益： | - | - | - |\n", "| （一）基本每股收益 | 0 | 0 | 0 |\n", "| （二）稀释每股收益 | 0 | 0 | 0 |\n", "| 一、经营活动产生的现金流量： | 28033.43 | 33673.64 | -8658.22 |\n", "| 销售商品、提供劳务收到的现金 | 385127.57 | 312084.17 | 246986.44 |\n", "| 收到的税费返还 | 8169.84 | 19861.06 | 20698.91 |\n", "| 收到的其他与经营活动有关的现金 | 20071.12 | 17526.3 | 22538.14 |\n", "| 经营活动现金流入小计 | 413368.53 | 349471.53 | 290223.48 |\n", "| 购买商品、接受劳务支付的现金 | 295317.56 | 233154.26 | 203485.47 |\n", "| 支付给职工以及为职工支付的现金 | 61726.28 | 54653.29 | 66945.49 |\n", "| 支付的各项税费 | 7529.95 | 3227.37 | 4235.11 |\n", "| 支付的其他与经营活动有关的现金 | 20761.3 | 24762.97 | 24215.64 |\n", "| 经营活动现金流出小计 | 385335.1 | 315797.89 | 298881.71 |\n", "| 经营活动产生的现金流量净额 | 28033.43 | 33673.64 | -8658.22 |\n", "| 二、投资活动产生的现金流量： | -40397.6 | -13848.22 | -42030.92 |\n", "| 收回投资所收到的现金 | 1287.08 | 1836.95 | 28114.97 |\n", "| 取得投资收益所收到的现金 | 26.93 | 9.27 | 424.47 |\n", "| 处置固定资产、无形资产和其他长期资产所收回的现金净额 | 1.86 | 533.76 | 5454.87 |\n", "| 处置子公司及其他营业单位收到的现金净额 | 5796.94 | 0 | 32560.48 |\n", "| 收到的其他与投资活动有关的现金 | 0 | 0 | 0 |\n", "| 投资活动现金流入小计 | 7112.81 | 2379.98 | 66554.79 |\n", "| 购建固定资产、无形资产和其他长期资产所支付的现金 | 24263.67 | 16228.19 | 17451.72 |\n", "| 投资所支付的现金 | 21947 | 0 | 91003.52 |\n", "| 取得子公司及其他营业单位支付的现金净额 | 0 | 0 | 130.47 |\n", "| 支付的其他与投资活动有关的现金 | 1299.74 | 0 | 0 |\n", "| 投资活动现金流出小计 | 47510.41 | 16228.19 | 108585.71 |\n", "| 投资活动产生的现金流量净额 | -40397.6 | -13848.22 | -42030.92 |\n", "| 三、筹资活动产生的现金流量： | 3957.1 | -43949.06 | 78046.34 |\n", "| 吸收投资所收到的现金 | 5872.45 | 0 | 5215 |\n", "| 其中：子公司吸收少数股东权益性投资收到的现金 | 5872.45 | 0 | 5215 |\n", "| 取得借款收到的现金 | 495965.81 | 489524.67 | 478244.95 |\n", "| 收到的其他与筹资活动有关的现金 | 2222.68 | 7770.09 | 10985.27 |\n", "| 筹资活动现金流入小计 | 504060.93 | 497294.75 | 494445.22 |\n", "| 偿还债务支付的现金 | 462728.85 | 501729.27 | 392843.27 |\n", "| 分配股利、利润或偿付利息支付的现金 | 14929.04 | 16457.74 | 15848.28 |\n", "| 其中：子公司支付少数股东的现金股利 | 0 | 0 | 0 |\n", "| 支付其他与筹资活动有关的现金 | 22445.94 | 23056.81 | 7707.33 |\n", "| 其中：子公司减资支付给少数股东的现金 | 0 | 0 | 0 |\n", "| 筹资活动现金流出小计 | 500103.83 | 541243.82 | 416398.88 |\n", "| 筹资活动产生的现金流量净额 | 3957.1 | -43949.06 | 78046.34 |\n", "| 四、汇率变动对现金及现金等价物的影响 | 318.41 | 1407.06 | -2074.74 |\n", "| 五、现金及现金等价物净增加额 | -8088.66 | -22716.58 | 25282.45 |\n", "| 加：期初现金及现金等价物余额 | 104632.31 | 127348.89 | 102066.44 |\n", "| 六、期末现金及现金等价物余额 | 96543.65 | 104632.31 | 127348.89 |\n"]}], "source": ["print(markdown"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["\n", "\n", "data_rule = [\n", "  {\n", "    \"ruleId\": \"ANL_CPR_001\",\n", "    \"ruleName\": \"利率与费用透明度审查\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"检查贷款合同中是否明确披露借款利率、服务费、管理费、逾期费等所有费用，且条款表述清晰易懂。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"费用说明文件\"],\n", "    \"triggerCondition\": \"当已完成对合同条款和费用信息提取后触发\",\n", "    \"dependencies\": [\"EXT_001_核心合同字段提取\", \"EXT_008_费用明细提取（示例）\"],\n", "    \"analysisLogic\": \"若在合同中能找到所有必需费用条款，且披露完整且清晰，则合规；若缺漏或模糊不清则不合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"费用信息披露合规\",\n", "        \"condition\": \"费用明细字段全部存在 && 条款表述明确\"\n", "      },\n", "      {\n", "        \"resultValue\": \"费用信息披露不合规\",\n", "        \"condition\": \"否则\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_002\",\n", "    \"ruleName\": \"不公平格式条款：排除消费者主要权利\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"识别并审查合同中是否存在排除或限制消费者依法享有的主要权利的条款（如知情权、赔偿请求权等）。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"补充协议\"],\n", "    \"triggerCondition\": \"当已完成对合同条款文本提取后触发\",\n", "    \"dependencies\": [\"EXT_009_合同条款文本提取（示例）\"],\n", "    \"analysisLogic\": \"若检测到“放弃索赔”“消费者无权…”等明显排除消费者权利的表述，则判定为不合规；否则合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"未排除消费者主要权利\",\n", "        \"condition\": \"未检出此类不公平表述\"\n", "      },\n", "      {\n", "        \"resultValue\": \"存在排除消费者权利条款\",\n", "        \"condition\": \"检出不公平关键词/句式\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_003\",\n", "    \"ruleName\": \"不公平格式条款：额外强加损失或不合理义务\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"识别合同中是否有超出正常交易要求的条款，对消费者施加额外或不合理的义务和损失承担。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"补充协议\"],\n", "    \"triggerCondition\": \"当已完成对合同条款文本提取后触发\",\n", "    \"dependencies\": [\"EXT_009_合同条款文本提取（示例）\"],\n", "    \"analysisLogic\": \"若检测到消费者需承担本不应由其承担的损失或不合理义务（如因第三方过错却要求消费者赔偿），则不合规；否则合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"无额外强加义务\",\n", "        \"condition\": \"未检出此类不公平表述\"\n", "      },\n", "      {\n", "        \"resultValue\": \"存在额外强加义务\",\n", "        \"condition\": \"检出此类表述\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_004\",\n", "    \"ruleName\": \"不公平格式条款：超出法律或行业惯例的高额违约金\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"检查合同中是否约定明显超出法定或行业允许范围的违约金、罚息等费用。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"补充协议\"],\n", "    \"triggerCondition\": \"当已完成对违约金、罚息条款提取后触发\",\n", "    \"dependencies\": [\"EXT_009_合同条款文本提取（示例）\"],\n", "    \"analysisLogic\": \"若违约金或罚息费率超过监管或行业惯例上限，则判定为不合规；否则合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"未发现高额违约金\",\n", "        \"condition\": \"违约金 <= 合理上限\"\n", "      },\n", "      {\n", "        \"resultValue\": \"存在高额违约金\",\n", "        \"condition\": \"违约金 > 合理上限\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_005\",\n", "    \"ruleName\": \"不公平格式条款：责任转嫁\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"识别经营者将本应由自身承担的义务或风险转嫁给消费者的条款（如系统故障、不可抗力导致的损失等）。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"补充协议\"],\n", "    \"triggerCondition\": \"当已完成对合同文本提取后触发\",\n", "    \"dependencies\": [\"EXT_009_合同条款文本提取（示例）\"],\n", "    \"analysisLogic\": \"若发现将经营者风险/过失完全转嫁给消费者的描述，则判定为不合规；否则合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"未发现责任转嫁条款\",\n", "        \"condition\": \"未检出此类不公平表述\"\n", "      },\n", "      {\n", "        \"resultValue\": \"存在责任转嫁条款\",\n", "        \"condition\": \"检出此类表述\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_006\",\n", "    \"ruleName\": \"不公平格式条款：减轻经营者责任\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"识别合同中是否存在单方面减轻或免除经营者（贷款机构）自己应承担的责任，如重大过失免责等。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"补充协议\"],\n", "    \"triggerCondition\": \"当已完成对合同文本提取后触发\",\n", "    \"dependencies\": [\"EXT_009_合同条款文本提取（示例）\"],\n", "    \"analysisLogic\": \"若合同文本出现“机构不承担任何责任”“出现重大过失也不赔偿”等内容，则判定为不合规；否则合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"未发现减轻经营者责任条款\",\n", "        \"condition\": \"无此类不公平表述\"\n", "      },\n", "      {\n", "        \"resultValue\": \"存在减轻经营者责任条款\",\n", "        \"condition\": \"检出相关描述\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_007\",\n", "    \"ruleName\": \"个人信息保护合规性审查\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"检查合同或相关授权文件中是否明确说明个人信息的采集范围、用途、存储与保护措施，且获得消费者明示授权。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"隐私授权书\"],\n", "    \"triggerCondition\": \"当完成对隐私条款提取后触发\",\n", "    \"dependencies\": [\"EXT_010_隐私条款提取（示例）\"],\n", "    \"analysisLogic\": \"若授权条款明示消费者同意，且使用范围符合相关法律法规，则合规；否则不合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"个人信息保护合规\",\n", "        \"condition\": \"信息采集和使用有明确授权 && 符合法规\"\n", "      },\n", "      {\n", "        \"resultValue\": \"个人信息保护不合规\",\n", "        \"condition\": \"未获明示授权或授权过度\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_008\",\n", "    \"ruleName\": \"信息披露及时性分析\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"审查金融机构是否在签约前向消费者披露了所有关键费率、风险、还款方式等信息，并留有合理时间供消费者决策。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"风险告知书\", \"费用清单\"],\n", "    \"triggerCondition\": \"当完成对风险告知/费用清单的提取后触发\",\n", "    \"dependencies\": [\"EXT_008_费用明细提取（示例）\"],\n", "    \"analysisLogic\": \"若签署时间上显示合同前已提供重要信息，并由消费者确认知悉，则合规；否则不合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"披露及时\",\n", "        \"condition\": \"签署前已确认风险与费用\"\n", "      },\n", "      {\n", "        \"resultValue\": \"披露不及时\",\n", "        \"condition\": \"否则\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_009\",\n", "    \"ruleName\": \"强制搭售或捆绑销售检查\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"检查是否存在只要办理贷款就被迫购买保险、理财等不必要附加服务的情况。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"附加协议\", \"保险单据\"],\n", "    \"triggerCondition\": \"当提取到保险或其他附加产品条款后触发\",\n", "    \"dependencies\": [\"EXT_009_合同条款文本提取（示例）\"],\n", "    \"analysisLogic\": \"若检测到与放贷审批绑定的强制购买描述，则不合规；否则合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"无强制搭售\",\n", "        \"condition\": \"未发现强制购买字眼或实际绑定条款\"\n", "      },\n", "      {\n", "        \"resultValue\": \"存在强制搭售\",\n", "        \"condition\": \"检测到强制购买或捆绑描述\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_010\",\n", "    \"ruleName\": \"提前还款条款合理性分析\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"检测合同中是否允许消费者提前还款，及提前还款违约金或手续费是否处于合理范围。\",\n", "    \"applicableAttachments\": [\"贷款合同\"],\n", "    \"triggerCondition\": \"当提取到合同中的提前还款相关条款后触发\",\n", "    \"dependencies\": [\"EXT_009_合同条款文本提取（示例）\"],\n", "    \"analysisLogic\": \"若条款明确允许提前还款，且手续费不畸高，则合规；否则不合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"提前还款条款合规\",\n", "        \"condition\": \"可提前还款 && 违约金在合理范围\"\n", "      },\n", "      {\n", "        \"resultValue\": \"提前还款条款不合规\",\n", "        \"condition\": \"否则\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_011\",\n", "    \"ruleName\": \"逾期违约责任公平性分析\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"检查合同对逾期违约金或罚息的设定是否合理，并未过度惩罚消费者。\",\n", "    \"applicableAttachments\": [\"贷款合同\"],\n", "    \"triggerCondition\": \"当提取到逾期罚息/违约金条款后触发\",\n", "    \"dependencies\": [\"EXT_009_合同条款文本提取（示例）\"],\n", "    \"analysisLogic\": \"若逾期罚息或违约金不超过法定或行业允许的区间，则合规；否则不合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"逾期违约条款合规\",\n", "        \"condition\": \"违约金/罚息 <= 行业或法律规定\"\n", "      },\n", "      {\n", "        \"resultValue\": \"逾期违约条款不合规\",\n", "        \"condition\": \"否则\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_012\",\n", "    \"ruleName\": \"消费者撤销权或冷静期分析\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"判断合同中是否载明消费者享有一定期限内的撤销权或冷静期，以便对贷款业务作二次确认。\",\n", "    \"applicableAttachments\": [\"贷款合同\"],\n", "    \"triggerCondition\": \"当提取到相关条款后触发\",\n", "    \"dependencies\": [\"EXT_009_合同条款文本提取（示例）\"],\n", "    \"analysisLogic\": \"若合同包含冷静期或撤销权条款，则合规；若缺失且法规要求必须有则不合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"包含消费者冷静期/撤销权\",\n", "        \"condition\": \"匹配到相关条款\"\n", "      },\n", "      {\n", "        \"resultValue\": \"未包含或不达法规要求\",\n", "        \"condition\": \"否则\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_013\",\n", "    \"ruleName\": \"消费者投诉渠道和救济途径审查\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"检查合同中是否明确提供消费者投诉渠道、争议解决机制及法律救济方式。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"补充协议\"],\n", "    \"triggerCondition\": \"当完成对争议解决条款及投诉方式提取后触发\",\n", "    \"dependencies\": [\"EXT_009_合同条款文本提取（示例）\"],\n", "    \"analysisLogic\": \"若合同中列明有效投诉电话/地址、仲裁或诉讼方式，则合规；否则不合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"救济途径合规\",\n", "        \"condition\": \"列出投诉渠道、仲裁/诉讼方式\"\n", "      },\n", "      {\n", "        \"resultValue\": \"救济途径不合规\",\n", "        \"condition\": \"缺少或不明确\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_014\",\n", "    \"ruleName\": \"自动续贷或自动扣款条款审查\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"识别合同中是否存在自动续贷或自动扣款的约定，并审查是否在签署前充分告知并征得消费者同意。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"授权协议\"],\n", "    \"triggerCondition\": \"当检测到自动扣款或自动续贷相关表述后触发\",\n", "    \"dependencies\": [\"EXT_009_合同条款文本提取（示例）\"],\n", "    \"analysisLogic\": \"若合同明确说明自动续贷/自动扣款机制并获得消费者同意，则合规；否则不合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"自动续贷/扣款条款合规\",\n", "        \"condition\": \"条款明示且消费者知情\"\n", "      },\n", "      {\n", "        \"resultValue\": \"自动续贷/扣款条款不合规\",\n", "        \"condition\": \"隐藏或模糊表述\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_015\",\n", "    \"ruleName\": \"催收方式合规性审查\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"检查合同或附属文件中对逾期催收方式的约定，是否符合合法合规及保护消费者尊严的要求。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"催收说明文件\"],\n", "    \"triggerCondition\": \"当提取到催收相关条款后触发\",\n", "    \"dependencies\": [\"EXT_009_合同条款文本提取（示例）\"],\n", "    \"analysisLogic\": \"若约定的催收方式符合监管规定，不涉及骚扰、暴力或侵犯隐私，则合规；否则不合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"催收方式合规\",\n", "        \"condition\": \"无非法/骚扰/暴力催收表述\"\n", "      },\n", "      {\n", "        \"resultValue\": \"催收方式不合规\",\n", "        \"condition\": \"检出不合规催收相关描述\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_016\",\n", "    \"ruleName\": \"多头借贷或借贷叠加提示审查\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"在合同或风险提示中是否明确告知消费者多头借贷的潜在风险，并非鼓励叠加借款。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"风险告知书\"],\n", "    \"triggerCondition\": \"当检测到风险提示或相关条款后触发\",\n", "    \"dependencies\": [\"EXT_009_合同条款文本提取（示例）\"],\n", "    \"analysisLogic\": \"若存在明确的借贷叠加风险告知条款，则合规；若完全无提及且有明显多头借贷引导，则不合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"已进行多头借贷风险提示\",\n", "        \"condition\": \"条款中明确警示多头借贷风险\"\n", "      },\n", "      {\n", "        \"resultValue\": \"无多头借贷风险提示\",\n", "        \"condition\": \"无任何提醒或反而鼓励叠加贷款\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_017\",\n", "    \"ruleName\": \"金融广告与宣传材料真实合法性审查\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"若提供了宣传/广告材料，检查是否存在虚假宣传、夸大收益、隐瞒费用等误导消费者的内容。\",\n", "    \"applicableAttachments\": [\"宣传广告材料\", \"营销文案\"],\n", "    \"triggerCondition\": \"当上传营销或宣传材料并完成文本提取后触发\",\n", "    \"dependencies\": [\"EXT_011_广告文案提取（示例）\"],\n", "    \"analysisLogic\": \"若发现虚假或误导性陈述，如零利率但实际有费用，则不合规；否则合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"广告宣传合规\",\n", "        \"condition\": \"未检出虚假或误导信息\"\n", "      },\n", "      {\n", "        \"resultValue\": \"广告宣传不合规\",\n", "        \"condition\": \"检测到夸大宣传/隐瞒费用\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_018\",\n", "    \"ruleName\": \"风险告知充分性分析\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"综合评估是否对贷款逾期、利息叠加、违约后果等关键风险进行充分告知。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"风险告知书\"],\n", "    \"triggerCondition\": \"当完成对风险条款提取后触发\",\n", "    \"dependencies\": [\"EXT_009_合同条款文本提取（示例）\"],\n", "    \"analysisLogic\": \"若贷款核心风险在合同或单独文件中明示，并有消费者签字确认，则合规；否则不合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"风险告知充分\",\n", "        \"condition\": \"包含核心风险并有确认\"\n", "      },\n", "      {\n", "        \"resultValue\": \"风险告知不充分\",\n", "        \"condition\": \"缺少或未明确披露关键风险\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_019\",\n", "    \"ruleName\": \"信息安全承诺条款审查\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"检查合同或附加条款中对用户数据保护和信息安全责任的承诺，是否符合相应安全管理要求。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"信息安全承诺书\"],\n", "    \"triggerCondition\": \"当检测到信息安全相关条款后触发\",\n", "    \"dependencies\": [\"EXT_010_隐私条款提取（示例）\"],\n", "    \"analysisLogic\": \"若经营者承诺采用适当的数据加密、存储防护措施，并对泄露事件承担责任，则合规；否则不合规。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"信息安全承诺合规\",\n", "        \"condition\": \"承诺措施完善 && 责任明确\"\n", "      },\n", "      {\n", "        \"resultValue\": \"信息安全承诺不合规\",\n", "        \"condition\": \"缺失或不明确\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"ANL_CPR_020\",\n", "    \"ruleName\": \"消费者权益保护综合评估\",\n", "    \"ruleType\": \"分析\",\n", "    \"description\": \"综合所有拆分规则结果（利率费用、隐私、格式条款、催收方式等），得出整体合规性判断。\",\n", "    \"applicableAttachments\": [\"贷款合同\", \"费用说明文件\", \"补充协议\", \"隐私授权书\"],\n", "    \"triggerCondition\": \"当前面各项分析均已完成后触发\",\n", "    \"dependencies\": [\n", "      \"ANL_CPR_001\",\n", "      \"ANL_CPR_002\",\n", "      \"ANL_CPR_003\",\n", "      \"ANL_CPR_004\",\n", "      \"ANL_CPR_005\",\n", "      \"ANL_CPR_006\",\n", "      \"ANL_CPR_007\",\n", "      \"ANL_CPR_008\",\n", "      \"ANL_CPR_009\",\n", "      \"ANL_CPR_010\",\n", "      \"ANL_CPR_011\",\n", "      \"ANL_CPR_012\",\n", "      \"ANL_CPR_013\",\n", "      \"ANL_CPR_014\",\n", "      \"ANL_CPR_015\",\n", "      \"ANL_CPR_016\",\n", "      \"ANL_CPR_017\",\n", "      \"ANL_CPR_018\",\n", "      \"ANL_CPR_019\"\n", "    ],\n", "    \"analysisLogic\": \"若各项拆分规则均显示合规或可接受，则整体判定为合规；若任意关键维度不合规，则判定不合格并提示整改。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"整体合规\",\n", "        \"condition\": \"所有关键子规则均满足要求\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不合规/需整改\",\n", "        \"condition\": \"存在至少一项不符合消费者权益保护要求\"\n", "      }\n", "    ]\n", "  }\n", "]"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["xxpl = [\n", "  {\n", "    \"ruleId\": \"R001\",\n", "    \"ruleName\": \"附件内容完整性检查\",\n", "    \"ruleType\": \"信息披露申请书审核\",\n", "    \"description\": \"附件文本需包含完整的信息，如合同应有合同编号、签署方信息、日期等。\",\n", "    \"applicableAttachments\": [\"合同文件\", \"保险单据\", \"授权书\"],\n", "    \"triggerCondition\": \"附件文本缺少应有的关键字段\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"扫描文本，检查是否包含所有必要字段，如合同编号、签署方、日期等。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"文本中包含所有必需信息\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"文本中缺少合同编号、签署方或其他关键信息\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R002\",\n", "    \"ruleName\": \"文本格式正确\",\n", "    \"ruleType\": \"信息披露申请书审核\",\n", "    \"description\": \"附件内容需按照规定格式书写，如合同应以正式条款书写，并包含关键条目。\",\n", "    \"applicableAttachments\": [\"合同文件\", \"业务协议\"],\n", "    \"triggerCondition\": \"文本格式不符合合同或协议的标准结构\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检测文本格式是否包含标题、条款编号、正式用语等格式要求。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"文本格式符合合同或协议的标准结构\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"文本未按标准格式书写，如缺少条款编号或正式语言\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R003\",\n", "    \"ruleName\": \"授权书内容明确\",\n", "    \"ruleType\": \"信息披露申请书审核\",\n", "    \"description\": \"授权书文本应清晰表述授权人、被授权人、授权范围及有效期限。\",\n", "    \"applicableAttachments\": [\"授权书\"],\n", "    \"triggerCondition\": \"授权书文本未明确授权人、被授权人、授权范围或有效期\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"扫描文本，提取授权人、被授权人、授权范围及有效期等信息，判断是否完整。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"文本明确包含授权人、被授权人、授权范围及有效期\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"文本缺少授权人、被授权人、授权范围或有效期中的任意一项\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R004\",\n", "    \"ruleName\": \"签署信息完整\",\n", "    \"ruleType\": \"信息披露申请书审核\",\n", "    \"description\": \"合同、授权书等文件文本应包含签署人姓名及签署日期。\",\n", "    \"applicableAttachments\": [\"合同文件\", \"授权书\"],\n", "    \"triggerCondition\": \"附件文本缺少签署人姓名或签署日期\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检测文本中是否包含‘签署人’、‘签名’或‘日期’等关键词及相关内容。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"文本包含签署人姓名及签署日期\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"文本缺少签署人姓名或签署日期\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R005\",\n", "    \"ruleName\": \"保险单据有效性\",\n", "    \"ruleType\": \"信息披露申请书审核\",\n", "    \"description\": \"保险单据文本需包含保单号、保险公司名称、投保人信息及有效期限。\",\n", "    \"applicableAttachments\": [\"保险单据\"],\n", "    \"triggerCondition\": \"附件文本缺少保单号、保险公司名称、投保人信息或有效期限\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检查文本是否包含‘保单号’、‘保险公司’、‘投保人’及‘有效期’等关键词。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"文本包含完整的保单信息\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"文本缺少保单号、保险公司名称、投保人信息或有效期中的任意一项\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R006\",\n", "    \"ruleName\": \"金额信息一致性\",\n", "    \"ruleType\": \"信息披露申请书审核\",\n", "    \"description\": \"保险单、合同等文件应明确显示金额，并确保数字格式正确。\",\n", "    \"applicableAttachments\": [\"保险单据\", \"合同文件\"],\n", "    \"triggerCondition\": \"文本中金额表达不清或数字格式错误\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检测文本是否包含金额数值，并判断格式是否符合标准（如千分位、货币单位）。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"文本金额信息完整，格式正确\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"文本金额缺失或格式错误\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R007\",\n", "    \"ruleName\": \"文本中不得有涂改痕迹\",\n", "    \"ruleType\": \"信息披露申请书审核\",\n", "    \"description\": \"文本内容应完整清晰，不应存在涂改、删除或不自然的文本缺失。\",\n", "    \"applicableAttachments\": [\"所有附件\"],\n", "    \"triggerCondition\": \"附件文本存在涂改、删除或不自然缺失的内容\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检测文本是否有‘删除’标记、字符缺失或异常空白区域。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"文本内容完整，无涂改或异常缺失\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"文本存在涂改、删除或缺失内容\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R008\",\n", "    \"ruleName\": \"合同条款完整\",\n", "    \"ruleType\": \"信息披露申请书审核\",\n", "    \"description\": \"合同文本应包含完整的权利义务、违约责任和争议解决条款。\",\n", "    \"applicableAttachments\": [\"合同文件\"],\n", "    \"triggerCondition\": \"合同文本未包含权利义务、违约责任或争议解决条款\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"扫描文本是否包含‘权利义务’、‘违约责任’、‘争议解决’等条款。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"文本包含所有合同关键条款\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"文本缺少权利义务、违约责任或争议解决条款\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R009\",\n", "    \"ruleName\": \"有效期限明确\",\n", "    \"ruleType\": \"信息披露申请书审核\",\n", "    \"description\": \"合同、保险单等文件应明确列出有效期限或起止日期。\",\n", "    \"applicableAttachments\": [\"合同文件\", \"保险单据\"],\n", "    \"triggerCondition\": \"文本缺少有效期限或起止日期\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检查文本中是否有‘有效期至’、‘起止日期’等关键词及对应内容。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"文本包含有效期限或起止日期\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"文本缺少有效期限或起止日期\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R010\",\n", "    \"ruleName\": \"文本语言规范\",\n", "    \"ruleType\": \"信息披露申请书审核\",\n", "    \"description\": \"文本语言应使用正式、准确、无歧义的表述，不应包含错别字或非正式语言。\",\n", "    \"applicableAttachments\": [\"所有附件\"],\n", "    \"triggerCondition\": \"文本存在错别字、非正式用语或歧义表述\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检测文本是否包含非正式用语、错别字或可能导致误解的表达。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"文本语言规范、准确\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"文本包含错别字或非正式表述\"\n", "      }\n", "    ]\n", "  }\n", "]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 创建信息过度披露规则\n", "xxpl = [\n", "  {\n", "    \"ruleId\": \"R011\",\n", "    \"ruleName\": \"个人敏感信息保护\",\n", "    \"ruleType\": \"信息披露申请书审核\",\n", "    \"description\": \"文件中不应过度披露个人敏感信息，如身份证号码、银行账号、手机号码等应适当脱敏处理。\",\n", "    \"applicableAttachments\": [\"所有附件\"],\n", "    \"triggerCondition\": \"文本中包含未脱敏的个人敏感信息\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检查文本中是否存在完整的身份证号码、银行卡号、手机号码等敏感信息，判断是否进行了适当脱敏处理。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"敏感信息已适当脱敏或不存在过度披露\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"存在未脱敏的敏感个人信息\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R012\",\n", "    \"ruleName\": \"商业秘密保护\",\n", "    \"ruleType\": \"信息披露申请书审核\",\n", "    \"description\": \"文件中不应过度披露商业秘密或内部敏感信息，如具体定价策略、内部流程细节等。\",\n", "    \"applicableAttachments\": [\"合同文件\", \"商业计划书\", \"财务报表\"],\n", "    \"triggerCondition\": \"文本中包含商业秘密或内部敏感信息\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检查文本中是否包含商业秘密、内部运营数据、未公开的财务数据等敏感商业信息。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"不存在商业秘密过度披露\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"存在商业秘密或内部敏感信息过度披露\"\n", "      }\n", "    ]\n", "  }\n", "]\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# 创建保险产品消保审核规则\n", "bxcp = [\n", "  {\n", "    \"ruleId\": \"R021\",\n", "    \"ruleName\": \"保险责任明确性\",\n", "    \"ruleType\": \"保险产品消保审核\",\n", "    \"description\": \"保险产品宣传材料中应明确说明保险责任范围，避免使用模糊或误导性表述。\",\n", "    \"applicableAttachments\": [\"产品说明书\", \"宣传手册\", \"保险条款\"],\n", "    \"triggerCondition\": \"文本中包含保险责任相关描述\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检查保险责任描述是否清晰、准确，是否存在模糊表述或夸大承诺。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"保险责任描述清晰明确，无误导性表述\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"保险责任描述模糊、夸大或存在误导性表述\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R022\",\n", "    \"ruleName\": \"免责条款显著提示\",\n", "    \"ruleType\": \"保险产品消保审核\",\n", "    \"description\": \"保险产品宣传材料中的免责条款应当以显著方式提示，如加粗、放大字体或特殊颜色标注。\",\n", "    \"applicableAttachments\": [\"产品说明书\", \"保险条款\"],\n", "    \"triggerCondition\": \"文本中包含免责条款\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检查免责条款是否以显著方式提示，是否易于消费者识别和理解。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"免责条款以显著方式提示\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"免责条款未以显著方式提示或隐藏在细则中\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R023\",\n", "    \"ruleName\": \"收益示例真实性\",\n", "    \"ruleType\": \"保险产品消保审核\",\n", "    \"description\": \"保险产品宣传材料中的收益示例应当真实、客观，不得使用绝对化语言或承诺固定收益。\",\n", "    \"applicableAttachments\": [\"产品说明书\", \"宣传手册\"],\n", "    \"triggerCondition\": \"文本中包含收益相关描述或示例\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检查收益示例是否客观真实，是否存在夸大收益或承诺固定回报的表述。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"收益示例客观真实，无夸大或承诺固定收益\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"存在夸大收益或承诺固定回报的表述\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R024\",\n", "    \"ruleName\": \"费用透明度\",\n", "    \"ruleType\": \"保险产品消保审核\",\n", "    \"description\": \"保险产品宣传材料应当清晰列明各项费用，包括保费、管理费、退保费用等，避免隐藏收费项目。\",\n", "    \"applicableAttachments\": [\"产品说明书\", \"费用说明\"],\n", "    \"triggerCondition\": \"文本中包含费用相关描述\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检查费用说明是否完整、透明，是否存在隐藏费用或模糊表述。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"费用说明完整透明，无隐藏费用\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"费用说明不完整或存在隐藏费用\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R025\",\n", "    \"ruleName\": \"理赔流程清晰性\",\n", "    \"ruleType\": \"保险产品消保审核\",\n", "    \"description\": \"保险产品宣传材料应当清晰说明理赔流程、所需材料和时限，避免消费者理赔困难。\",\n", "    \"applicableAttachments\": [\"产品说明书\", \"理赔指南\"],\n", "    \"triggerCondition\": \"文本中包含理赔相关描述\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检查理赔流程说明是否清晰、完整，是否便于消费者理解和操作。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"理赔流程说明清晰完整\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"理赔流程说明不清晰或不完整\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R026\",\n", "    \"ruleName\": \"风险提示充分性\",\n", "    \"ruleType\": \"保险产品消保审核\",\n", "    \"description\": \"保险产品宣传材料应当充分提示消费者可能面临的风险，包括投资风险、市场风险等。\",\n", "    \"applicableAttachments\": [\"产品说明书\", \"风险提示\"],\n", "    \"triggerCondition\": \"文本中包含风险相关描述\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检查风险提示是否充分、明确，是否涵盖主要风险点。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"风险提示充分明确\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"风险提示不充分或不明确\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R027\",\n", "    \"ruleName\": \"产品适用人群明确性\",\n", "    \"ruleType\": \"保险产品消保审核\",\n", "    \"description\": \"保险产品宣传材料应当明确说明适用人群和不适用人群，避免误导消费者购买不适合的产品。\",\n", "    \"applicableAttachments\": [\"产品说明书\", \"投保须知\"],\n", "    \"triggerCondition\": \"文本中包含适用人群相关描述\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检查适用人群说明是否明确、准确，是否存在模糊表述。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"适用人群说明明确准确\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"适用人群说明不明确或不准确\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"ruleId\": \"R028\",\n", "    \"ruleName\": \"免责条款显著性\",\n", "    \"ruleType\": \"保险产品消保审核\",\n", "    \"description\": \"保险产品宣传材料中的免责条款应当以显著方式提示，避免以小字体或不易察觉的方式呈现。\",\n", "    \"applicableAttachments\": [\"产品说明书\", \"条款\"],\n", "    \"triggerCondition\": \"文本中包含免责相关描述\",\n", "    \"dependencies\": [],\n", "    \"analysisLogic\": \"检查免责条款是否以显著方式提示，是否易于消费者察觉。\",\n", "    \"analysisResultDefinition\": [\n", "      {\n", "        \"resultValue\": \"满足\",\n", "        \"condition\": \"免责条款以显著方式提示\"\n", "      },\n", "      {\n", "        \"resultValue\": \"不满足\",\n", "        \"condition\": \"免责条款未以显著方式提示\"\n", "      }\n", "    ]\n", "  }\n", "\n", "]\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "from pymongo import MongoClient\n", "\n", "# MongoDB 连接设置\n", "client = MongoClient('mongodb://kgweb.roardata.cn:37017', username='memInterview', password='memInterview@202408', authSource='admin')\n", "db = client['roardataAiApp_test']\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'ruleId': 'R021', 'ruleName': '保险责任明确性', 'ruleType': '保险产品消保审核', 'description': '保险产品宣传材料中应明确说明保险责任范围，避免使用模糊或误导性表述。', 'applicableAttachments': ['产品说明书', '宣传手册', '保险条款'], 'triggerCondition': '文本中包含保险责任相关描述', 'dependencies': [], 'analysisLogic': '检查保险责任描述是否清晰、准确，是否存在模糊表述或夸大承诺。', 'analysisResultDefinition': [{'resultValue': '满足', 'condition': '保险责任描述清晰明确，无误导性表述'}, {'resultValue': '不满足', 'condition': '保险责任描述模糊、夸大或存在误导性表述'}], 'created_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 550055), 'updated_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 550065)}\n", "{'ruleId': 'R022', 'ruleName': '免责条款显著提示', 'ruleType': '保险产品消保审核', 'description': '保险产品宣传材料中的免责条款应当以显著方式提示，如加粗、放大字体或特殊颜色标注。', 'applicableAttachments': ['产品说明书', '保险条款'], 'triggerCondition': '文本中包含免责条款', 'dependencies': [], 'analysisLogic': '检查免责条款是否以显著方式提示，是否易于消费者识别和理解。', 'analysisResultDefinition': [{'resultValue': '满足', 'condition': '免责条款以显著方式提示'}, {'resultValue': '不满足', 'condition': '免责条款未以显著方式提示或隐藏在细则中'}], 'created_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 645642), 'updated_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 645643)}\n", "{'ruleId': 'R023', 'ruleName': '收益示例真实性', 'ruleType': '保险产品消保审核', 'description': '保险产品宣传材料中的收益示例应当真实、客观，不得使用绝对化语言或承诺固定收益。', 'applicableAttachments': ['产品说明书', '宣传手册'], 'triggerCondition': '文本中包含收益相关描述或示例', 'dependencies': [], 'analysisLogic': '检查收益示例是否客观真实，是否存在夸大收益或承诺固定回报的表述。', 'analysisResultDefinition': [{'resultValue': '满足', 'condition': '收益示例客观真实，无夸大或承诺固定收益'}, {'resultValue': '不满足', 'condition': '存在夸大收益或承诺固定回报的表述'}], 'created_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 664786), 'updated_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 664787)}\n", "{'ruleId': 'R024', 'ruleName': '费用透明度', 'ruleType': '保险产品消保审核', 'description': '保险产品宣传材料应当清晰列明各项费用，包括保费、管理费、退保费用等，避免隐藏收费项目。', 'applicableAttachments': ['产品说明书', '费用说明'], 'triggerCondition': '文本中包含费用相关描述', 'dependencies': [], 'analysisLogic': '检查费用说明是否完整、透明，是否存在隐藏费用或模糊表述。', 'analysisResultDefinition': [{'resultValue': '满足', 'condition': '费用说明完整透明，无隐藏费用'}, {'resultValue': '不满足', 'condition': '费用说明不完整或存在隐藏费用'}], 'created_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 683411), 'updated_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 683412)}\n", "{'ruleId': 'R025', 'ruleName': '理赔流程清晰性', 'ruleType': '保险产品消保审核', 'description': '保险产品宣传材料应当清晰说明理赔流程、所需材料和时限，避免消费者理赔困难。', 'applicableAttachments': ['产品说明书', '理赔指南'], 'triggerCondition': '文本中包含理赔相关描述', 'dependencies': [], 'analysisLogic': '检查理赔流程说明是否清晰、完整，是否便于消费者理解和操作。', 'analysisResultDefinition': [{'resultValue': '满足', 'condition': '理赔流程说明清晰完整'}, {'resultValue': '不满足', 'condition': '理赔流程说明不清晰或不完整'}], 'created_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 702461), 'updated_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 702462)}\n", "{'ruleId': 'R026', 'ruleName': '风险提示充分性', 'ruleType': '保险产品消保审核', 'description': '保险产品宣传材料应当充分提示消费者可能面临的风险，包括投资风险、市场风险等。', 'applicableAttachments': ['产品说明书', '风险提示'], 'triggerCondition': '文本中包含风险相关描述', 'dependencies': [], 'analysisLogic': '检查风险提示是否充分、明确，是否涵盖主要风险点。', 'analysisResultDefinition': [{'resultValue': '满足', 'condition': '风险提示充分明确'}, {'resultValue': '不满足', 'condition': '风险提示不充分或不明确'}], 'created_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 721185), 'updated_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 721186)}\n", "{'ruleId': 'R027', 'ruleName': '产品适用人群明确性', 'ruleType': '保险产品消保审核', 'description': '保险产品宣传材料应当明确说明适用人群和不适用人群，避免误导消费者购买不适合的产品。', 'applicableAttachments': ['产品说明书', '投保须知'], 'triggerCondition': '文本中包含适用人群相关描述', 'dependencies': [], 'analysisLogic': '检查适用人群说明是否明确、准确，是否存在模糊表述。', 'analysisResultDefinition': [{'resultValue': '满足', 'condition': '适用人群说明明确准确'}, {'resultValue': '不满足', 'condition': '适用人群说明不明确或不准确'}], 'created_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 739805), 'updated_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 739806)}\n", "{'ruleId': 'R028', 'ruleName': '免责条款显著性', 'ruleType': '保险产品消保审核', 'description': '保险产品宣传材料中的免责条款应当以显著方式提示，避免以小字体或不易察觉的方式呈现。', 'applicableAttachments': ['产品说明书', '条款'], 'triggerCondition': '文本中包含免责相关描述', 'dependencies': [], 'analysisLogic': '检查免责条款是否以显著方式提示，是否易于消费者察觉。', 'analysisResultDefinition': [{'resultValue': '满足', 'condition': '免责条款以显著方式提示'}, {'resultValue': '不满足', 'condition': '免责条款未以显著方式提示'}], 'created_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 758287), 'updated_at': datetime.datetime(2025, 3, 24, 20, 54, 50, 758289)}\n"]}], "source": ["\n", "def insert_data_rule(data_rule):\n", "    collection = db['consumer_protection_rules']\n", "    data_rule['created_at'] = datetime.now()\n", "    data_rule['updated_at'] = datetime.now()\n", "    print(data_rule)\n", "    result = collection.insert_one(data_rule)\n", "    return result.inserted_id\n", "\n", "# 示例数据\n", "# 插入数据\n", "\n", "for data_rule in bxcp:\n", "    insert_data_rule(data_rule)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["<bound method MongoClient.close of MongoClient(host=['kgweb.roardata.cn:37017'], document_class=dict, tz_aware=False, connect=True, authsource='admin')>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["client.close"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "# 超级安心人寿保险计划 - 您最明智的选择!\n", "\n", "尊敬的客户，感谢您关注我们的超级安心人寿保险计划，这是市场上**绝对最好**的保险产品，没有之一！我们的产品保证让您高枕无忧，永远不会有任何风险和损失。\n", "\n", "## 产品亮点\n", "\n", "- **100%安全保障**：投保后，您的资金将获得全方位保护，绝对不会有任何损失风险。\n", "- **超高回报率**：我们的产品年化收益率高达15%，远超市场平均水平，这是其他任何保险产品都无法比拟的！\n", "- **无条件理赔**：无论发生什么情况，我们都会进行理赔，没有任何例外。\n", "- **即刻生效**：签约后立即生效，不需要等待期，为您提供即时保障。\n", "\n", "## 收益展示\n", "\n", "根据我们的历史数据，投保本产品的客户平均获得了以下收益：\n", "- 5年期：本金翻倍\n", "- 10年期：本金增长3倍\n", "- 20年期：本金增长10倍以上\n", "\n", "这些数据充分证明了我们产品的卓越性能和稳定回报。投资我们的产品，您将永远不会后悔！\n", "\n", "## 客户见证\n", "\n", "张先生（北京）：\"投保超级安心人寿保险后，我的资产在短短3年内就增长了80%，这是我做过的最明智的投资决定！\"\n", "\n", "李女士（上海）：\"这是市场上最好的保险产品，没有之一！我已经向所有亲友推荐了。\"\n", "\n", "## 如何购买\n", "\n", "现在拨打我们的热线电话，前100名客户还将获得额外5%的首年收益奖励！机不可失，立即行动！\n", "\n", "*免责声明：本产品可能存在一定投资风险，收益率会受到市场波动影响，理赔需符合相关条件，部分情况下可能存在等待期，详情请咨询客服。*\n", "\n"]}], "source": ["# 创建一个违反4个保险消费者保护规则的产品介绍材料示例\n", "# 这个示例将违反以下规则：\n", "# 1. 未如实告知产品风险\n", "# 2. 夸大产品收益\n", "# 3. 使用绝对化用语\n", "# 4. 免责条款未以显著方式提示\n", "\n", "violation_example = \"\"\"\n", "# 超级安心人寿保险计划 - 您最明智的选择!\n", "\n", "尊敬的客户，感谢您关注我们的超级安心人寿保险计划，这是市场上**绝对最好**的保险产品，没有之一！我们的产品保证让您高枕无忧，永远不会有任何风险和损失。\n", "\n", "## 产品亮点\n", "\n", "- **100%安全保障**：投保后，您的资金将获得全方位保护，绝对不会有任何损失风险。\n", "- **超高回报率**：我们的产品年化收益率高达15%，远超市场平均水平，这是其他任何保险产品都无法比拟的！\n", "- **无条件理赔**：无论发生什么情况，我们都会进行理赔，没有任何例外。\n", "- **即刻生效**：签约后立即生效，不需要等待期，为您提供即时保障。\n", "\n", "## 收益展示\n", "\n", "根据我们的历史数据，投保本产品的客户平均获得了以下收益：\n", "- 5年期：本金翻倍\n", "- 10年期：本金增长3倍\n", "- 20年期：本金增长10倍以上\n", "\n", "这些数据充分证明了我们产品的卓越性能和稳定回报。投资我们的产品，您将永远不会后悔！\n", "\n", "## 客户见证\n", "\n", "张先生（北京）：\"投保超级安心人寿保险后，我的资产在短短3年内就增长了80%，这是我做过的最明智的投资决定！\"\n", "\n", "李女士（上海）：\"这是市场上最好的保险产品，没有之一！我已经向所有亲友推荐了。\"\n", "\n", "## 如何购买\n", "\n", "现在拨打我们的热线电话，前100名客户还将获得额外5%的首年收益奖励！机不可失，立即行动！\n", "\n", "*免责声明：本产品可能存在一定投资风险，收益率会受到市场波动影响，理赔需符合相关条件，部分情况下可能存在等待期，详情请咨询客服。*\n", "\"\"\"\n", "\n", "print(violation_example)\n", "\n", "# 分析违规点：\n", "# 1. 未如实告知产品风险：宣称\"绝对不会有任何风险和损失\"，\"100%安全保障\"\n", "# 2. 夸大产品收益：宣称\"年化收益率高达15%\"，\"5年期本金翻倍\"等\n", "# 3. 使用绝对化用语：\"绝对最好\"，\"永远不会有任何风险\"，\"永远不会后悔\"等\n", "# 4. 免责条款未以显著方式提示：免责声明字体小，放在最后，且与前面的宣传内容矛盾\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 超级安心人寿保险计划 - 您最明智的选择!\n", "\n", "尊敬的客户，感谢您关注我们的超级安心人寿保险计划，这是市场上**绝对最好**的保险产品，没有之一！我们的产品保证让您高枕无忧，永远不会有任何风险和损失。\n", "\n", "## 产品亮点\n", "\n", "- **100%安全保障**：投保后，您的资金将获得全方位保护，绝对不会有任何损失风险。\n", "- **超高回报率**：我们的产品年化收益率高达15%，远超市场平均水平，这是其他任何保险产品都无法比拟的！\n", "- **无条件理赔**：无论发生什么情况，我们都会进行理赔，没有任何例外。\n", "- **即刻生效**：签约后立即生效，不需要等待期，为您提供即时保障。\n", "\n", "## 收益展示\n", "\n", "根据我们的历史数据，投保本产品的客户平均获得了以下收益：\n", "- 5年期：本金翻倍\n", "- 10年期：本金增长3倍\n", "- 20年期：本金增长10倍以上\n", "\n", "这些数据充分证明了我们产品的卓越性能和稳定回报。投资我们的产品，您将永远不会后悔！\n", "\n", "## 客户见证\n", "\n", "张先生（北京）：\"投保超级安心人寿保险后，我的资产在短短3年内就增长了80%，这是我做过的最明智的投资决定！\"\n", "\n", "李女士（上海）：\"这是市场上最好的保险产品，没有之一！我已经向所有亲友推荐了。\"\n", "\n", "## 如何购买\n", "\n", "现在拨打我们的热线电话，前100名客户还将获得额外5%的首年收益奖励！机不可失，立即行动！\n", "\n", "*免责声明：本产品可能存在一定投资风险，收益率会受到市场波动影响，理赔需符合相关条件，部分情况下可能存在等待期，详情请咨询客服。*"]}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}