"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1391],{35605:function(e,t,r){r.r(t),r.d(t,{default:function(){return W}});var s=r(15009),n=r.n(s),l=r(99289),i=r.n(l),o=r(5574),d=r.n(o),a=r(42119),c=r(4393),u=r(27808),x=r(42075),p=r(32983),h=r(71471),g=r(11550),y=r(83622),f=r(71230),j=r(15746),m=r(66309),b=r(2453),v=r(74330),Z=r(38703),R=r(67294),w=r(55287),k=r(88484),C=r(79457),T=r(87784),S=r(58831),_=r(89514),P=r(52514),z=r(79090),A=r(78158);function O(e){return F.apply(this,arguments)}function F(){return(F=i()(n()().mark((function e(t){return n()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,A.N)("/api/ai-content-recognition/ocr-recognition",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var I=r(85893),U=a.Z.Step,L=R.memo((function(e){var t=e.imageUrl,r=e.onUpload,s=e.fileList,n=e.zoom;return(0,I.jsx)(c.Z,{bordered:!1,className:"image-preview-card",style:{height:"100%",display:"flex",flexDirection:"column"},children:(0,I.jsx)("div",{style:{flex:1,overflow:"hidden",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"#ffffff",borderRadius:"8px"},children:t?(0,I.jsx)("div",{style:{width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",overflow:"auto"},children:(0,I.jsx)(u.Z,{src:t,style:{maxWidth:n?"".concat(100*n,"%"):"90%",maxHeight:n?"".concat(100*n,"%"):"90%",objectFit:"contain"},preview:{mask:(0,I.jsxs)(x.Z,{children:[(0,I.jsx)(w.Z,{}),(0,I.jsx)("span",{children:"查看原图"})]})}})}):(0,I.jsxs)("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",backgroundColor:"#fff",padding:"20px"},children:[(0,I.jsx)(p.Z,{image:p.Z.PRESENTED_IMAGE_SIMPLE,description:(0,I.jsx)(h.Z.Text,{type:"secondary",children:"请上传OCR识别图片"})}),(0,I.jsx)(g.Z,{accept:"image/*",showUploadList:!1,fileList:s,beforeUpload:r,style:{marginTop:"16px"},children:(0,I.jsx)(y.ZP,{type:"primary",icon:(0,I.jsx)(k.Z,{}),children:"上传图片"})})]})})})})),N=function(e){return{0:"标题 (title)",1:"正文 (plain text)",2:"废弃区域 (abandon)",3:"图形 (figure)",4:"图形标题 (figure_caption)",5:"表格 (table)",6:"表格标题 (table_caption)",7:"表格脚注 (table_footnote)",8:"独立公式 (isolate_formula)",9:"公式标题 (formula_caption)"}[e]||"类别".concat(e)},E=function(e){return{0:"magenta",1:"blue",2:"gray",3:"cyan",4:"geekblue",5:"purple",6:"volcano",7:"orange",8:"gold",9:"lime"}[e]||"default"},D=function(e){var t,r,s,n,l,i=e.imageContent;return i&&i.isAnalyzed?(0,I.jsx)(c.Z,{title:(0,I.jsxs)(x.Z,{children:[(0,I.jsx)(C.Z,{}),(0,I.jsx)("span",{children:"OCR分析结果"})]}),bordered:!1,style:{height:"100%",overflow:"auto"},children:(0,I.jsx)(f.Z,{gutter:[16,16],children:(0,I.jsxs)(j.Z,{span:24,children:[null!==(t=i.layoutResult)&&void 0!==t&&t.image?(0,I.jsxs)("div",{style:{textAlign:"center"},children:[(0,I.jsx)(u.Z,{src:i.layoutResult.image.startsWith("data:")?i.layoutResult.image:"data:image/png;base64,".concat(i.layoutResult.image),alt:"OCR结果",style:{maxWidth:"100%"},preview:{mask:(0,I.jsxs)(x.Z,{children:[(0,I.jsx)(w.Z,{}),(0,I.jsx)("span",{children:"查看详情"})]})}}),(null===(r=i.layoutResult)||void 0===r?void 0:r.boxes)&&i.layoutResult.boxes.length>0&&i.layoutResult.scores&&i.layoutResult.classes&&(0,I.jsxs)("div",{style:{marginTop:"8px",textAlign:"left"},children:[(0,I.jsxs)(h.Z.Text,{type:"secondary",children:["识别到 ",(null===(s=i.layoutResult)||void 0===s||null===(s=s.boxes)||void 0===s?void 0:s.length)||0," 个区域",(null===(n=i.layoutResult)||void 0===n?void 0:n.scores)&&", 置信度: ".concat(i.layoutResult.scores.map((function(e){return(100*e).toFixed(0)+"%"})).join(", "))]}),(null===(l=i.layoutResult)||void 0===l?void 0:l.classes)&&(0,I.jsxs)("div",{style:{marginTop:"8px",marginBottom:"16px"},children:[(0,I.jsx)(h.Z.Text,{strong:!0,children:"类别统计："}),(0,I.jsx)("div",{style:{marginTop:"8px",display:"flex",flexWrap:"wrap",gap:"8px"},children:Array.from(new Set(i.layoutResult.classes)).map((function(e){var t=i.layoutResult.classes.filter((function(t){return t===e})).length;return(0,I.jsxs)(m.Z,{color:E(e),children:[N(e),": ",t,"个"]},e)}))})]}),(0,I.jsxs)("div",{style:{marginTop:"16px",overflow:"auto"},children:[(0,I.jsxs)("div",{style:{marginTop:"0px"},children:[(0,I.jsx)(h.Z.Text,{strong:!0,children:"区域明细："}),(0,I.jsxs)("table",{style:{width:"100%",borderCollapse:"collapse",marginTop:"8px",fontSize:"12px"},children:[(0,I.jsx)("thead",{children:(0,I.jsxs)("tr",{style:{backgroundColor:"#f0f0f0"},children:[(0,I.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"序号"}),(0,I.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"区域坐标 [x1, y1, x2, y2]"}),(0,I.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"置信度"}),(0,I.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"类别"})]})}),(0,I.jsx)("tbody",{children:i.layoutResult.boxes.map((function(e,t){var r,s;return(0,I.jsxs)("tr",{children:[(0,I.jsx)("td",{style:{padding:"4px",border:"1px solid #d9d9d9",textAlign:"center"},children:t+1}),(0,I.jsxs)("td",{style:{padding:"4px",border:"1px solid #d9d9d9",fontFamily:"monospace"},children:["[",e.map((function(e){return e.toFixed(1)})).join(", "),"]"]}),(0,I.jsx)("td",{style:{padding:"4px",border:"1px solid #d9d9d9",textAlign:"center"},children:null!==(r=i.layoutResult)&&void 0!==r&&r.scores?(100*i.layoutResult.scores[t]).toFixed(2)+"%":"未知"}),(0,I.jsx)("td",{style:{padding:"4px",border:"1px solid #d9d9d9",textAlign:"center"},children:null!==(s=i.layoutResult)&&void 0!==s&&s.classes?(0,I.jsx)(m.Z,{color:E(i.layoutResult.classes[t]),children:N(i.layoutResult.classes[t])}):"未知"})]},t)}))})]})]}),(0,I.jsx)(h.Z.Text,{strong:!0,style:{display:"block",marginTop:"16px"},children:"识别详情："}),(0,I.jsx)("div",{style:{backgroundColor:"#f9f9f9",padding:"8px",borderRadius:"4px",marginTop:"8px",maxHeight:"200px",overflow:"auto"},children:(0,I.jsxs)(h.Z.Text,{code:!0,children:["{",'code: 0, message: "success", data: ',"{",(0,I.jsx)("br",{}),"  layout_result: ","{"," ",(0,I.jsx)("br",{}),"    boxes: ",JSON.stringify(i.layoutResult.boxes,null,2),", ",(0,I.jsx)("br",{}),"    scores: ",JSON.stringify(i.layoutResult.scores,null,2),", ",(0,I.jsx)("br",{}),"    classes: ",JSON.stringify(i.layoutResult.classes,null,2)," ",(0,I.jsx)("br",{}),"  ","}","}","}"]})})]})]})]}):(0,I.jsx)(I.Fragment,{}),i.ocrResult&&(0,I.jsxs)("div",{style:{marginTop:"24px"},children:[(0,I.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,I.jsx)(h.Z.Text,{strong:!0,children:"识别文本："}),(0,I.jsx)("div",{style:{backgroundColor:"#f9f9f9",padding:"12px",borderRadius:"4px",marginTop:"8px",maxHeight:"200px",overflow:"auto",whiteSpace:"pre-wrap",fontSize:"14px"},children:i.ocrResult.text})]}),i.ocrResult.results&&i.ocrResult.results.length>0&&i.ocrResult.results[0]&&(0,I.jsxs)("div",{children:[(0,I.jsxs)(h.Z.Text,{strong:!0,children:["识别详情（共 ",i.ocrResult.results[0].length," 项）："]}),(0,I.jsx)("div",{style:{marginTop:"8px",overflow:"auto",maxHeight:"400px"},children:(0,I.jsxs)("table",{style:{width:"100%",borderCollapse:"collapse",fontSize:"12px"},children:[(0,I.jsx)("thead",{children:(0,I.jsxs)("tr",{style:{backgroundColor:"#f0f0f0"},children:[(0,I.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"序号"}),(0,I.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"文本内容"}),(0,I.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"置信度"}),(0,I.jsx)("th",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:"坐标 [x1,y1,x2,y2,x3,y3,x4,y4]"})]})}),(0,I.jsx)("tbody",{children:i.ocrResult.results[0].map((function(e,t){var r=e[0],s=e[1],n=s[0],l=s[1];return(0,I.jsxs)("tr",{children:[(0,I.jsx)("td",{style:{padding:"4px",border:"1px solid #d9d9d9",textAlign:"center"},children:t+1}),(0,I.jsx)("td",{style:{padding:"4px",border:"1px solid #d9d9d9"},children:n}),(0,I.jsxs)("td",{style:{padding:"4px",border:"1px solid #d9d9d9",textAlign:"center"},children:[(100*l).toFixed(2),"%"]}),(0,I.jsx)("td",{style:{padding:"4px",border:"1px solid #d9d9d9",fontFamily:"monospace",fontSize:"10px"},children:r.map((function(e,t){return"[".concat(e[0],",").concat(e[1],"]").concat(t<r.length-1?",":"")}))})]},t)}))})]})})]})]})]})})}):(0,I.jsx)(c.Z,{bordered:!1,style:{height:"100%"},children:(0,I.jsx)(p.Z,{description:"暂无分析结果"})})},W=function(){var e=(0,R.useState)(null),t=d()(e,2),r=t[0],s=t[1],l=(0,R.useState)([]),o=d()(l,2),u=o[0],p=o[1],m=(0,R.useState)(null),w=d()(m,2),C=w[0],A=w[1],F=(0,R.useState)(!1),N=d()(F,2),E=N[0],W=N[1],B=(0,R.useRef)(!0),H=(0,R.useState)(0),J=d()(H,2),M=J[0],G=J[1],q=(0,R.useState)(0),K=d()(q,2),Q=K[0],V=K[1],X=(0,R.useState)(null),Y=d()(X,2),$=Y[0],ee=Y[1],te=function(){var e=i()(n()().mark((function e(t){var r;return n()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("开始上传文件:",t.name),t.type.startsWith("image/")){e.next=4;break}return b.ZP.error("只接受图片文件"),e.abrupt("return",!1);case 4:if(t.size/1024/1024<10){e.next=8;break}return b.ZP.error("图片大小不能超过 10MB"),e.abrupt("return",!1);case 8:return e.prev=8,r=URL.createObjectURL(t),s(r),p([{uid:"-1",name:t.name,status:"done",url:r,originFileObj:t}]),ee(null),V(1),b.ZP.success("图片上传成功"),console.log("图片上传成功，URL:",r),e.abrupt("return",!1);case 19:return e.prev=19,e.t0=e.catch(8),b.ZP.error("图片处理失败，请重试"),console.error("图片上传或处理失败:",e.t0),e.abrupt("return",!1);case 24:case"end":return e.stop()}}),e,null,[[8,19]])})));return function(t){return e.apply(this,arguments)}}(),re=function(e){return new Promise((function(t,r){var s=new FileReader;s.readAsDataURL(e),s.onload=function(){if("string"==typeof s.result){var e=s.result.split(",")[1];t(e)}else r(new Error("无法读取文件"))},s.onerror=function(e){return r(e)}}))},se=function(){var e=i()(n()().mark((function e(){var t,s,l,i,o,d,a;return n()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.info("=======>handleRecognize"),r){e.next=4;break}return b.ZP.warning("请先上传图片！"),e.abrupt("return");case 4:if(W(!0),G(0),V(2),B.current=!0,e.prev=8,s=null===(t=u[0])||void 0===t?void 0:t.originFileObj){e.next=12;break}throw new Error("文件对象不存在");case 12:return e.next=14,re(s);case 14:return l=e.sent,i=setInterval((function(){B.current?G((function(e){return e>=90?(clearInterval(i),e):e+5})):clearInterval(i)}),200),e.next=18,O({base64Image:l,fileName:s.name});case 18:o=e.sent,clearInterval(i),G(100),console.log("布局识别API返回结果:",o),o.success?(d=null,a=null,o.data&&o.data.data?(o.data.data.layout_result&&(d=o.data.data.layout_result),o.data.data.ocr_result&&(a=o.data.data.ocr_result)):o.data&&(o.data.layout_result&&(d=o.data.layout_result),o.data.ocr_result&&(a=o.data.ocr_result)),ee({fileName:s.name,isAnalyzed:!0,layoutResult:d,ocrResult:a}),b.ZP.success("OCR成功！"),V(3)):(b.ZP.error(o.message||"识别失败"),ee(null)),e.next=30;break;case 25:e.prev=25,e.t0=e.catch(8),b.ZP.error("识别过程中出现错误"),console.error(e.t0),ee(null);case 30:return e.prev=30,setTimeout((function(){W(!1),G(0)}),500),e.finish(30);case 33:case"end":return e.stop()}}),e,null,[[8,25,30,33]])})));return function(){return e.apply(this,arguments)}}();return(0,I.jsxs)("div",{style:{display:"flex",flexDirection:"column",height:"100vh",width:"100%",backgroundColor:"#f5f5f5",padding:"16px"},children:[(0,I.jsxs)(c.Z,{bordered:!1,style:{marginBottom:"16px"},children:[(0,I.jsxs)(f.Z,{align:"middle",justify:"space-between",gutter:[16,16],children:[(0,I.jsx)(j.Z,{xs:24,md:8,children:(0,I.jsx)(h.Z.Title,{level:4,style:{margin:0},children:"OCR识别"})}),(0,I.jsx)(j.Z,{xs:24,md:16,children:(0,I.jsx)(f.Z,{justify:"end",align:"middle",gutter:[16,16],children:(0,I.jsx)(j.Z,{children:(0,I.jsxs)(x.Z,{size:"middle",children:[(0,I.jsx)(g.Z,{accept:"image/*",showUploadList:!1,fileList:u,beforeUpload:te,children:(0,I.jsx)(y.ZP,{icon:(0,I.jsx)(k.Z,{}),children:"上传图片"})}),(0,I.jsx)(y.ZP,{type:"primary",icon:E?(0,I.jsx)(T.Z,{}):(0,I.jsx)(S.Z,{}),onClick:E?function(){B.current=!1}:se,disabled:!r,danger:E,loading:E,children:E?"停止":"分析"}),(0,I.jsxs)(x.Z,{children:[(0,I.jsx)(y.ZP,{icon:(0,I.jsx)(_.Z,{}),onClick:function(){C?C<4&&A(C+.1):A(1)},disabled:!r}),(0,I.jsx)(y.ZP,{icon:(0,I.jsx)(P.Z,{}),onClick:function(){C?C>.2&&A(C-.1):A(1)},disabled:!r}),(0,I.jsx)("span",{style:{opacity:C?1:.5},children:C?"".concat((100*C).toFixed(0),"%"):"自动"})]})]})})})})]}),(Q>0||E)&&(0,I.jsx)(f.Z,{style:{marginTop:"16px"},children:(0,I.jsx)(j.Z,{span:24,children:(0,I.jsxs)(a.Z,{current:Q,size:"small",progressDot:!0,children:[(0,I.jsx)(U,{title:"上传"}),(0,I.jsx)(U,{title:"准备"}),(0,I.jsx)(U,{title:E?"分析中 ".concat(M,"%"):"处理"}),(0,I.jsx)(U,{title:"完成"})]})})})]}),(0,I.jsxs)(f.Z,{gutter:[16,16],style:{flex:1,overflow:"hidden"},children:[(0,I.jsx)(j.Z,{xs:24,md:12,style:{height:"100%"},children:(0,I.jsx)(L,{imageUrl:r,onUpload:te,fileList:u,zoom:C})}),(0,I.jsx)(j.Z,{xs:24,md:12,style:{height:"100%",overflow:"auto"},children:E?(0,I.jsx)(c.Z,{bordered:!1,style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},bodyStyle:{width:"100%",textAlign:"center"},children:(0,I.jsxs)(x.Z,{direction:"vertical",size:"large",style:{width:"100%"},children:[(0,I.jsx)(v.Z,{size:"large",indicator:(0,I.jsx)(z.Z,{style:{fontSize:36},spin:!0})}),(0,I.jsx)(h.Z.Title,{level:4,children:"正在进行OCR识别..."}),(0,I.jsx)(Z.Z,{percent:M,status:"active",style:{width:"80%",margin:"0 auto"}})]})}):(0,I.jsx)(D,{imageContent:$})})]})]})}}}]);