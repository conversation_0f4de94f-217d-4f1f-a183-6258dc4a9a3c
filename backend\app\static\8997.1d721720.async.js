"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8997],{81643:function(e,t,n){n.d(t,{Z:function(){return o}});const o=e=>e?"function"==typeof e?e():e:null},68997:function(e,t,n){n.d(t,{Z:function(){return P}});var o=n(67294),r=n(93967),a=n.n(r),i=n(9220),l=n(42550),s=n(74443),c=n(53124),d=n(35792),p=n(98675),u=n(25378);var g=o.createContext({}),m=n(11568),f=n(14747),v=n(83559),b=n(83262);const y=e=>{const{antCls:t,componentCls:n,iconCls:o,avatarBg:r,avatarColor:a,containerSize:i,containerSizeLG:l,containerSizeSM:s,textFontSize:c,textFontSizeLG:d,textFontSizeSM:p,borderRadius:u,borderRadiusLG:g,borderRadiusSM:v,lineWidth:b,lineType:y}=e,h=(e,t,r)=>({width:e,height:e,borderRadius:"50%",[`&${n}-square`]:{borderRadius:r},[`&${n}-icon`]:{fontSize:t,[`> ${o}`]:{margin:0}}});return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,f.Wf)(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:a,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:r,border:`${(0,m.bf)(b)} ${y} transparent`,"&-image":{background:"transparent"},[`${t}-image-img`]:{display:"block"}}),h(i,c,u)),{"&-lg":Object.assign({},h(l,d,g)),"&-sm":Object.assign({},h(s,p,v)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},h=e=>{const{componentCls:t,groupBorderColor:n,groupOverlapping:o,groupSpace:r}=e;return{[`${t}-group`]:{display:"inline-flex",[t]:{borderColor:n},"> *:not(:first-child)":{marginInlineStart:o}},[`${t}-group-popover`]:{[`${t} + ${t}`]:{marginInlineStart:r}}}};var x=(0,v.I$)("Avatar",(e=>{const{colorTextLightSolid:t,colorTextPlaceholder:n}=e,o=(0,b.IX)(e,{avatarBg:n,avatarColor:t});return[y(o),h(o)]}),(e=>{const{controlHeight:t,controlHeightLG:n,controlHeightSM:o,fontSize:r,fontSizeLG:a,fontSizeXL:i,fontSizeHeading3:l,marginXS:s,marginXXS:c,colorBorderBg:d}=e;return{containerSize:t,containerSizeLG:n,containerSizeSM:o,textFontSize:Math.round((a+i)/2),textFontSizeLG:l,textFontSizeSM:r,groupSpace:c,groupOverlapping:-s,groupBorderColor:d}})),O=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const S=(e,t)=>{const[n,r]=o.useState(1),[m,f]=o.useState(!1),[v,b]=o.useState(!0),y=o.useRef(null),h=o.useRef(null),S=(0,l.sQ)(t,y),{getPrefixCls:C,avatar:$}=o.useContext(c.E_),j=o.useContext(g),E=()=>{if(!h.current||!y.current)return;const t=h.current.offsetWidth,n=y.current.offsetWidth;if(0!==t&&0!==n){const{gap:o=4}=e;2*o<n&&r(n-2*o<t?(n-2*o)/t:1)}};o.useEffect((()=>{f(!0)}),[]),o.useEffect((()=>{b(!0),r(1)}),[e.src]),o.useEffect(E,[e.gap]);const w=()=>{const{onError:t}=e;!1!==(null==t?void 0:t())&&b(!1)},{prefixCls:z,shape:N,size:P,src:k,srcSet:Z,icon:B,className:W,rootClassName:I,alt:M,draggable:R,children:L,crossOrigin:T}=e,_=O(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","alt","draggable","children","crossOrigin"]),G=(0,p.Z)((e=>{var t,n;return null!==(n=null!==(t=null!=P?P:null==j?void 0:j.size)&&void 0!==t?t:e)&&void 0!==n?n:"default"})),D=Object.keys("object"==typeof G&&G||{}).some((e=>["xs","sm","md","lg","xl","xxl"].includes(e))),F=(0,u.Z)(D),H=o.useMemo((()=>{if("object"!=typeof G)return{};const e=s.c4.find((e=>F[e])),t=G[e];return t?{width:t,height:t,fontSize:t&&(B||L)?t/2:18}:{}}),[F,G]);const V=C("avatar",z),X=(0,d.Z)(V),[A,K,q]=x(V,X),Q=a()({[`${V}-lg`]:"large"===G,[`${V}-sm`]:"small"===G}),U=o.isValidElement(k),Y=N||(null==j?void 0:j.shape)||"circle",J=a()(V,Q,null==$?void 0:$.className,`${V}-${Y}`,{[`${V}-image`]:U||k&&v,[`${V}-icon`]:!!B},q,X,W,I,K),ee="number"==typeof G?{width:G,height:G,fontSize:B?G/2:18}:{};let te;if("string"==typeof k&&v)te=o.createElement("img",{src:k,draggable:R,srcSet:Z,onError:w,alt:M,crossOrigin:T});else if(U)te=k;else if(B)te=B;else if(m||1!==n){const e=`scale(${n})`,t={msTransform:e,WebkitTransform:e,transform:e};te=o.createElement(i.Z,{onResize:E},o.createElement("span",{className:`${V}-string`,ref:h,style:Object.assign({},t)},L))}else te=o.createElement("span",{className:`${V}-string`,style:{opacity:0},ref:h},L);return delete _.onError,delete _.gap,A(o.createElement("span",Object.assign({},_,{style:Object.assign(Object.assign(Object.assign(Object.assign({},ee),H),null==$?void 0:$.style),_.style),className:J,ref:S}),te))};var C=o.forwardRef(S),$=n(50344),j=n(96159),E=n(55241);const w=e=>{const{size:t,shape:n}=o.useContext(g),r=o.useMemo((()=>({size:e.size||t,shape:e.shape||n})),[e.size,e.shape,t,n]);return o.createElement(g.Provider,{value:r},e.children)};var z=e=>{var t,n,r,i;const{getPrefixCls:l,direction:s}=o.useContext(c.E_),{prefixCls:p,className:u,rootClassName:g,style:m,maxCount:f,maxStyle:v,size:b,shape:y,maxPopoverPlacement:h,maxPopoverTrigger:O,children:S,max:z}=e;const N=l("avatar",p),P=`${N}-group`,k=(0,d.Z)(N),[Z,B,W]=x(N,k),I=a()(P,{[`${P}-rtl`]:"rtl"===s},W,k,u,g,B),M=(0,$.Z)(S).map(((e,t)=>(0,j.Tm)(e,{key:`avatar-key-${t}`}))),R=(null==z?void 0:z.count)||f,L=M.length;if(R&&R<L){const e=M.slice(0,R),l=M.slice(R,L),s=(null==z?void 0:z.style)||v,c=(null===(t=null==z?void 0:z.popover)||void 0===t?void 0:t.trigger)||O||"hover",d=(null===(n=null==z?void 0:z.popover)||void 0===n?void 0:n.placement)||h||"top",p=Object.assign(Object.assign({content:l},null==z?void 0:z.popover),{classNames:{root:a()(`${P}-popover`,null===(i=null===(r=null==z?void 0:z.popover)||void 0===r?void 0:r.classNames)||void 0===i?void 0:i.root)},placement:d,trigger:c});return e.push(o.createElement(E.Z,Object.assign({key:"avatar-popover-key",destroyTooltipOnHide:!0},p),o.createElement(C,{style:s},"+"+(L-R)))),Z(o.createElement(w,{shape:y,size:b},o.createElement("div",{className:I,style:m},e)))}return Z(o.createElement(w,{shape:y,size:b},o.createElement("div",{className:I,style:m},M)))};const N=C;N.Group=z;var P=N},66330:function(e,t,n){n.d(t,{aV:function(){return p}});var o=n(67294),r=n(93967),a=n.n(r),i=n(92419),l=n(81643),s=n(53124),c=n(20136),d=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const p=e=>{let{title:t,content:n,prefixCls:r}=e;return t||n?o.createElement(o.Fragment,null,t&&o.createElement("div",{className:`${r}-title`},t),n&&o.createElement("div",{className:`${r}-inner-content`},n)):null},u=e=>{const{hashId:t,prefixCls:n,className:r,style:s,placement:c="top",title:d,content:u,children:g}=e,m=(0,l.Z)(d),f=(0,l.Z)(u),v=a()(t,n,`${n}-pure`,`${n}-placement-${c}`,r);return o.createElement("div",{className:v,style:s},o.createElement("div",{className:`${n}-arrow`}),o.createElement(i.G,Object.assign({},e,{className:t,prefixCls:n}),g||o.createElement(p,{prefixCls:n,title:m,content:f})))};t.ZP=e=>{const{prefixCls:t,className:n}=e,r=d(e,["prefixCls","className"]),{getPrefixCls:i}=o.useContext(s.E_),l=i("popover",t),[p,g,m]=(0,c.Z)(l);return p(o.createElement(u,Object.assign({},r,{prefixCls:l,hashId:g,className:a()(n,m)})))}},55241:function(e,t,n){var o=n(67294),r=n(93967),a=n.n(r),i=n(21770),l=n(15105),s=n(81643),c=n(33603),d=n(96159),p=n(83062),u=n(66330),g=n(53124),m=n(20136),f=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const v=o.forwardRef(((e,t)=>{var n,r;const{prefixCls:v,title:b,content:y,overlayClassName:h,placement:x="top",trigger:O="hover",children:S,mouseEnterDelay:C=.1,mouseLeaveDelay:$=.1,onOpenChange:j,overlayStyle:E={},styles:w,classNames:z}=e,N=f(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:P,className:k,style:Z,classNames:B,styles:W}=(0,g.dj)("popover"),I=P("popover",v),[M,R,L]=(0,m.Z)(I),T=P(),_=a()(h,R,L,k,B.root,null==z?void 0:z.root),G=a()(B.body,null==z?void 0:z.body),[D,F]=(0,i.Z)(!1,{value:null!==(n=e.open)&&void 0!==n?n:e.visible,defaultValue:null!==(r=e.defaultOpen)&&void 0!==r?r:e.defaultVisible}),H=(e,t)=>{F(e,!0),null==j||j(e,t)},V=(0,s.Z)(b),X=(0,s.Z)(y);return M(o.createElement(p.Z,Object.assign({placement:x,trigger:O,mouseEnterDelay:C,mouseLeaveDelay:$},N,{prefixCls:I,classNames:{root:_,body:G},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},W.root),Z),E),null==w?void 0:w.root),body:Object.assign(Object.assign({},W.body),null==w?void 0:w.body)},ref:t,open:D,onOpenChange:e=>{H(e)},overlay:V||X?o.createElement(u.aV,{prefixCls:I,title:V,content:X}):null,transitionName:(0,c.m)(T,"zoom-big",N.transitionName),"data-popover-inject":!0}),(0,d.Tm)(S,{onKeyDown:e=>{var t,n;o.isValidElement(S)&&(null===(n=null==S?void 0:(t=S.props).onKeyDown)||void 0===n||n.call(t,e)),(e=>{e.keyCode===l.Z.ESC&&H(!1,e)})(e)}})))}));v._InternalPanelDoNotUseOrYouWillBeFired=u.ZP,t.Z=v},20136:function(e,t,n){var o=n(14747),r=n(50438),a=n(97414),i=n(79511),l=n(8796),s=n(83559),c=n(83262);const d=e=>{const{componentCls:t,popoverColor:n,titleMinWidth:r,fontWeightStrong:i,innerPadding:l,boxShadowSecondary:s,colorTextHeading:c,borderRadiusLG:d,zIndexPopup:p,titleMarginBottom:u,colorBgElevated:g,popoverBg:m,titleBorderBottom:f,innerContentPadding:v,titlePadding:b}=e;return[{[t]:Object.assign(Object.assign({},(0,o.Wf)(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:p,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":g,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{backgroundColor:m,backgroundClip:"padding-box",borderRadius:d,boxShadow:s,padding:l},[`${t}-title`]:{minWidth:r,marginBottom:u,color:c,fontWeight:i,borderBottom:f,padding:b},[`${t}-inner-content`]:{color:n,padding:v}})},(0,a.ZP)(e,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${t}-content`]:{display:"inline-block"}}}]},p=e=>{const{componentCls:t}=e;return{[t]:l.i.map((n=>{const o=e[`${n}6`];return{[`&${t}-${n}`]:{"--antd-arrow-background-color":o,[`${t}-inner`]:{backgroundColor:o},[`${t}-arrow`]:{background:"transparent"}}}}))}};t.Z=(0,s.I$)("Popover",(e=>{const{colorBgElevated:t,colorText:n}=e,o=(0,c.IX)(e,{popoverBg:t,popoverColor:n});return[d(o),p(o),(0,r._y)(o,"zoom-big")]}),(e=>{const{lineWidth:t,controlHeight:n,fontHeight:o,padding:r,wireframe:l,zIndexPopupBase:s,borderRadiusLG:c,marginXS:d,lineType:p,colorSplit:u,paddingSM:g}=e,m=n-o,f=m/2,v=m/2-t,b=r;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:s+30},(0,i.w)(e)),(0,a.wZ)({contentRadius:c,limitVerticalRadius:!0})),{innerPadding:l?0:12,titleMarginBottom:l?0:d,titlePadding:l?`${f}px ${b}px ${v}px`:0,titleBorderBottom:l?`${t}px ${p} ${u}`:"none",innerContentPadding:l?`${g}px ${b}px`:0})}),{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]})}}]);