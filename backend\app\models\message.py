from mongoengine import Document, StringField, IntField, DateTimeField, FloatField, DictField,ListField, ObjectIdField,BooleanField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, Dict, List
from bson import ObjectId

# MongoEngine 模型
class Message(Document):
    meta = {
        'collection': 'messages',
        'indexes': [
            'conversation_id',
            'app_info',
            ('conversation_id', 'app_info'),
            'created_at'
        ]
    }
    _id =ObjectIdField (primary_key=True)
    conversation_id = StringField(required=True)  # 确保是必选字段
    message_id = StringField(required=True)  # 设置为必选字段
    meta_data = DictField()  # 使用不同的名称以避免与 meta 冲突
    extra = DictField()
    user_id = IntField(required=True)  # 确保是必选字段
    user_name = StringField()
    role = StringField(required=True)  # 确保是必选字段
    m_id = IntField()
    m_name = StringField()
    created_at = DateTimeField(default=datetime.now)
    content = StringField(required=True)  # 确保是必选字段
    token_count = IntField()
    references = ListField()
    price = FloatField()
    app_info = StringField()
    collected = BooleanField(default=False)
    knowledge_ids = ListField(StringField(), required=True)
    

# Pydantic 模型
class MessageBase(BaseModel):
    message_id: str  # 确保在请求中提供
    meta_data: Dict  # 确保在请求中提供
    extra: Dict
    conversation_id: Optional[str] = None
    user_id: int
    user_name: Optional[str] = None
    role: str
    content: str
    m_id: Optional[int] = None
    m_name: Optional[str] = None
    token_count: Optional[int] = None
    price: Optional[float] = None
    app_info: Optional[str] = None
    references: Optional[List] = None
    collected: Optional[bool] = False
    query: Optional[List] = None
    knowledge_ids: Optional[List] = None  

class MessageCreate(BaseModel):
    message_id: str
    role: str
    content: str
    conversation_id: str
    app_info: str
    user_id: int
    user_name: str
    meta_data: Optional[dict] = Field(default_factory=dict)
    extra: Optional[dict] = Field(default_factory=dict)
    references: Optional[List] = []
    collected: Optional[bool] = False
    query: Optional[List] = None
    knowledge_ids: Optional[List] = None 
class MessageUpdate(BaseModel):
    content: Optional[str] = None
    role: Optional[str] = None
    token_count: Optional[int] = None
    price: Optional[float] = None
    references: Optional[List] = None
    
class MessageResponse(MessageBase):
    created_at: datetime

    class Config:
        from_attributes = True

class MessageConversationResponse(BaseModel):
    conversation_id: str
    user_id: int
    user_name: Optional[str] = None
    role: str
    content: str
    m_id: Optional[int] = None
    m_name: Optional[str] = None
    token_count: Optional[int] = None
    price: Optional[float] = None
    app_info: Optional[str] = None
    references: Optional[List] = None
    collected: Optional[bool] = False



class MessageCollectedResponse(BaseModel):
    message_id: str
    collected: bool
