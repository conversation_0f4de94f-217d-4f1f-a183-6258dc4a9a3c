import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from main import app
import platform
import psutil
import subprocess

client = TestClient(app)

# 模拟环境变量
@pytest.fixture(autouse=True)
def mock_env(monkeypatch):
    monkeypatch.setenv("API_KEY", "363326947")

# 测试认证
def test_invalid_api_key():
    response = client.get("/system/info", headers={"api-key": "wrong_key"})
    assert response.status_code == 403
    assert response.json()["detail"] == "Invalid API key"

def test_missing_api_key():
    response = client.get("/system/info")
    assert response.status_code == 422

# 模拟系统信息
@pytest.fixture
def mock_system_info(mocker):
    mocker.patch('platform.system', return_value='Linux')
    mocker.patch('platform.version', return_value='5.4.0-1018-aws')
    mocker.patch('platform.machine', return_value='x86_64')
    mocker.patch('psutil.cpu_count', return_value=4)
    mocker.patch('psutil.virtual_memory', return_value=MagicMock(total=16777216))
    mocker.patch('psutil.disk_partitions', return_value=[
        MagicMock(mountpoint='/'),
        MagicMock(mountpoint='/boot')
    ])

# 模拟GPU信息
@pytest.fixture
def mock_gpu_info(mocker):
    mock_gpu = MagicMock()
    mock_gpu.id = 0
    mock_gpu.name = "NVIDIA GeForce RTX 3080"
    mock_gpu.memoryTotal = 10240
    mock_gpu.uuid = "GPU-123456789"
    mock_gpu.load = 45.6
    mock_gpu.memoryUsed = 4096

    mocker.patch('GPUtil.getGPUs', return_value=[mock_gpu])
    mocker.patch('subprocess.check_output', side_effect=[
        b'nvcc: NVIDIA (R) Cuda compiler driver\nRelease 11.4, V11.4.100\n',
        b'NVIDIA-SMI 470.57.02\nDriver Version: 470.57.02\nCUDA Version: 11.4\n'
    ])

# 测试系统信息接口
def test_get_system_info(mock_system_info, mock_gpu_info):
    response = client.get("/system/info", headers={"api-key": "363326947"})
    assert response.status_code == 200
    data = response.json()
    
    # 验证基本系统信息
    assert data["os"] == "Linux"
    assert data["architecture"] == "x86_64"
    assert data["cpu_count"] == 4
    assert data["memory_total"] == 16777216
    assert len(data["disk_partitions"]) == 2
    
    # 验证GPU信息
    assert len(data["gpu_devices"]) == 1
    gpu = data["gpu_devices"][0]
    assert gpu["name"] == "NVIDIA GeForce RTX 3080"
    assert gpu["memory_total"] == 10240

# 模拟系统指标
@pytest.fixture
def mock_system_metrics():
    with patch('psutil.cpu_percent', side_effect=[25.6, [20.1, 30.2, 25.3, 26.8]]), \
         patch('psutil.cpu_freq', return_value=MagicMock(
             current=3600, min=800, max=4000
         )), \
         patch('psutil.virtual_memory', return_value=MagicMock(
             total=16777216, used=8388608, free=8388608, percent=50.0,
             _asdict=lambda: {
                 "total": 16777216, "used": 8388608,
                 "free": 8388608, "percent": 50.0
             }
         )), \
         patch('psutil.disk_usage', return_value=MagicMock(
             _asdict=lambda: {
                 "total": 256060514304,
                 "used": 121634816000,
                 "free": 134425698304,
                 "percent": 47.5
             }
         )), \
         patch('psutil.net_io_counters', return_value=MagicMock(
             _asdict=lambda: {
                 "bytes_sent": 1024,
                 "bytes_recv": 2048
             }
         )):
        yield

# 测试系统指标接口
def test_get_system_metrics(mock_system_metrics, mock_gpu_info):
    response = client.get("/system/metrics", headers={"api-key": "363326947"})
    assert response.status_code == 200
    data = response.json()
    
    # 验证CPU信息
    assert data["cpu"]["percent"] == 25.6
    assert len(data["cpu"]["per_cpu"]) == 4
    assert data["cpu"]["freq"]["current"] == 3600
    
    # 验证内存信息
    assert data["memory"]["total"] == 16777216
    assert data["memory"]["used"] == 8388608
    
    # 验证磁盘信息
    assert data["disk"]["/"]["total"] == 256060514304
    assert data["disk"]["/"]["percent"] == 47.5
    
    # 验证网络信息
    assert data["network"]["bytes_sent"] == 1024
    assert data["network"]["bytes_recv"] == 2048
    
    # 验证GPU信息
    assert len(data["gpu"]) == 1
    assert data["gpu"][0]["name"] == "NVIDIA GeForce RTX 3080"
    assert data["gpu"][0]["memory_total"] == 10240

# 模拟GPU详细信息
@pytest.fixture
def mock_gpu_detailed_info(mocker):
    nvidia_smi_output = """
    0, NVIDIA GeForce RTX 3080, GPU-123456789, 1234567890, 94.02.42.00.0F, 10240 MiB, Default, 16
    """
    mocker.patch('subprocess.check_output', return_value=nvidia_smi_output.encode())
    mocker.patch('main.get_cuda_version', return_value="11.4")

# 测试GPU详细信息接口
def test_get_gpu_info(mock_gpu_detailed_info, mock_gpu_info):
    response = client.get("/gpu/info", headers={"api-key": "363326947"})
    assert response.status_code == 200
    data = response.json()
    
    assert data["cuda_version"] == "11.4"
    assert len(data["gpus"]) == 1
    gpu = data["gpus"][0]
    assert gpu["name"] == "NVIDIA GeForce RTX 3080"
    assert gpu["uuid"] == "GPU-123456789"
    assert gpu["memory_total"] == "10240 MiB"

# 测试错误处理
def test_gpu_info_no_gpu():
    with patch('GPUtil.getGPUs', return_value=[]), \
         patch('subprocess.check_output', side_effect=subprocess.CalledProcessError(1, 'cmd')):
        response = client.get("/gpu/info", headers={"api-key": "363326947"})
        assert response.status_code == 200
        data = response.json()
        assert data["gpus"] == []

def test_system_metrics_partial_failure(mock_system_metrics):
    with patch('GPUtil.getGPUs', side_effect=Exception("GPU Error")):
        response = client.get("/system/metrics", headers={"api-key": "363326947"})
        assert response.status_code == 200
        data = response.json()
        assert "cpu" in data
        assert "memory" in data
        assert "gpu" in data
        assert data["gpu"] == [] 