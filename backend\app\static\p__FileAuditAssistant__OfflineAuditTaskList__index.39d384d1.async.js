"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3746],{47046:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},82061:function(e,t,r){var n=r(1413),o=r(67294),a=r(47046),l=r(91146),c=function(e,t){return o.createElement(l.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},s=o.forwardRef(c);t.Z=s},11475:function(e,t,r){r.d(t,{Z:function(){return s}});var n=r(1413),o=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"},l=r(91146),c=function(e,t){return o.createElement(l.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a}))};var s=o.forwardRef(c)},37446:function(e,t,r){r.d(t,{Z:function(){return s}});var n=r(1413),o=r(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"},l=r(91146),c=function(e,t){return o.createElement(l.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a}))};var s=o.forwardRef(c)},51042:function(e,t,r){var n=r(1413),o=r(67294),a=r(42110),l=r(91146),c=function(e,t){return o.createElement(l.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:t,icon:a.Z}))},s=o.forwardRef(c);t.Z=s},95753:function(e,t,r){r.r(t);var n=r(15009),o=r.n(n),a=r(99289),l=r.n(a),c=r(5574),s=r.n(c),i=r(67294),d=r(34041),u=r(71471),p=r(8232),f=r(2453),g=r(17788),x=r(74330),h=r(55102),m=r(83622),v=r(2487),y=r(32983),b=r(4393),C=r(85418),k=r(66309),j=r(11475),Z=r(40110),w=r(51042),S=r(55287),P=r(82061),$=r(37446),z=r(97245),I=r(96974),O=r(74573),E=r(97131),T=r(85893),B=d.default.Option,R=u.Z.Text,N=u.Z.Paragraph,_={pending_upload:{text:"待上传文件",color:"orange"},analyzing:{text:"分析中",color:"blue"},completed:{text:"已完成",color:"green"},failed:{text:"失败",color:"red"}},L={cardList:{margin:"-24px",padding:"24px"},card:{height:"100%",transition:"all 0.3s",borderRadius:"8px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},avatarContainer:{display:"flex",alignItems:"center",marginBottom:"16px"},avatarIcon:{display:"flex",justifyContent:"center",alignItems:"center",width:"40px",height:"40px",borderRadius:"8px",backgroundColor:"#e6f7ff",color:"#1890ff",fontSize:"20px",marginRight:"10px"},moreButton:{position:"absolute",top:"12px",right:"12px",fontSize:"18px",color:"#8c8c8c",cursor:"pointer",zIndex:10},cardStats:{display:"flex",justifyContent:"space-between",marginTop:"16px",fontSize:"13px",color:"#8c8c8c"},searchContainer:{padding:"16px 0",marginBottom:"16px",display:"flex",justifyContent:"space-between",alignItems:"center"},loadingContainer:{display:"flex",justifyContent:"center",alignItems:"center",height:"60vh"},emptyContainer:{margin:"40px 0"}};t.default=function(){var e=(0,I.s0)(),t=(0,i.useState)([]),r=s()(t,2),n=r[0],a=r[1],c=(0,i.useState)([]),u=s()(c,2),H=u[0],M=u[1],A=(0,i.useState)([]),F=s()(A,2),W=F[0],q=F[1],D=(0,i.useState)(!1),X=s()(D,2),G=X[0],K=X[1],Q=p.Z.useForm(),U=s()(Q,1)[0],V=(0,i.useState)(""),J=s()(V,2),Y=J[0],ee=J[1],te=(0,i.useState)(!0),re=s()(te,2),ne=re[0],oe=re[1],ae=(0,i.useState)(!0),le=s()(ae,2),ce=le[0],se=le[1],ie=function(){var e=l()(o()().mark((function e(){var t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,se(!0),e.next=4,(0,O.s6)();case 4:if(!(t=e.sent).success||!t.data){e.next=10;break}return e.next=8,q(t.data);case 8:e.next=12;break;case 10:f.ZP.error(t.error||"获取任务类型列表失败"),q([]);case 12:e.next=19;break;case 14:e.prev=14,e.t0=e.catch(0),console.error("获取任务类型列表失败:",e.t0),f.ZP.error("获取任务类型列表失败，请检查网络连接"),q([]);case 19:return e.prev=19,se(!1),e.finish(19);case 22:case"end":return e.stop()}}),e,null,[[0,14,19,22]])})));return function(){return e.apply(this,arguments)}}(),de=function(){var e=l()(o()().mark((function e(){var t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,oe(!0),e.next=4,(0,O.Ak)();case 4:(t=e.sent).success&&t.data?(a(t.data),M(t.data)):(f.ZP.error(t.error||"获取任务列表失败"),a([]),M([])),e.next=14;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("获取任务列表失败:",e.t0),f.ZP.error("获取任务列表失败，请检查网络连接"),a([]),M([]);case 14:return e.prev=14,oe(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[0,8,14,17]])})));return function(){return e.apply(this,arguments)}}();(0,i.useEffect)((function(){Promise.all([de(),ie()])}),[]);var ue=function(){var e=l()(o()().mark((function e(){var t,r;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,U.validateFields();case 3:return t=e.sent,e.next=6,(0,O.vr)({name:t.taskName,type:t.taskType});case 6:(r=e.sent).success&&r.data?(f.ZP.success("任务创建成功!"),U.resetFields(),K(!1),de()):f.ZP.error(r.error||"创建任务失败"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.log("表单验证失败:",e.t0);case 13:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(){return e.apply(this,arguments)}}(),pe=function(){var e=l()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:g.Z.confirm({title:"确认删除",icon:(0,T.jsx)(j.Z,{}),content:"确定要删除这个任务吗？相关文件和结果将一并删除。",okText:"确认",cancelText:"取消",onOk:function(){var e=l()(o()().mark((function e(){var r;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,O._5)(t);case 3:(r=e.sent).success?(f.ZP.success("任务删除成功!"),de()):f.ZP.error(r.error||"删除任务失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("删除任务失败:",e.t0),f.ZP.error("删除任务失败，请检查网络连接");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),fe=function(t){e("/fileAuditAssistant/offlineAuditTask?task=".concat(t))};return ne||ce?(0,T.jsx)(E._z,{children:(0,T.jsx)("div",{style:L.loadingContainer,children:(0,T.jsx)(x.Z,{size:"large",tip:"加载数据中..."})})}):(0,T.jsxs)(E._z,{children:[(0,T.jsxs)("div",{style:{marginBottom:"24px",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,T.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,T.jsx)(h.Z,{placeholder:"搜索任务名称或类型",prefix:(0,T.jsx)(Z.Z,{style:{color:"#1890ff"}}),allowClear:!0,value:Y,onChange:function(e){return function(e){ee(e);var t=e.toLowerCase(),r=n.filter((function(e){var r,n=(null===(r=W.find((function(t){return t.code===e.type})))||void 0===r?void 0:r.name)||e.type;return e.name.toLowerCase().includes(t)||n.toLowerCase().includes(t)}));M(r)}(e.target.value)},style:{width:"300px"}}),Y&&(0,T.jsxs)("span",{style:{marginLeft:"8px",fontSize:"13px",color:"#888"},children:["找到 ",H.length," 个匹配的任务"]})]}),(0,T.jsx)(m.ZP,{type:"primary",icon:(0,T.jsx)(w.Z,{}),onClick:function(){0!==W.length?K(!0):ce?f.ZP.warning("正在加载任务类型，请稍候..."):f.ZP.warning("当前没有可用的任务类型，请先在管理界面创建并激活任务类型！")},children:"新建任务"})]}),(0,T.jsx)("div",{style:L.cardList,children:(0,T.jsx)(v.Z,{rowKey:"id",loading:ne,grid:{gutter:24,xs:1,sm:2,md:2,lg:3,xl:4,xxl:4},dataSource:H,pagination:{pageSize:12,style:{textAlign:"center",marginTop:"16px"}},locale:{emptyText:(0,T.jsx)(y.Z,{description:(0,T.jsxs)("span",{children:[Y?"未找到匹配的任务":"暂无审核任务",(0,T.jsx)("br",{}),!Y&&(0,T.jsx)(R,{type:"secondary",children:'点击右上角"新建任务"按钮来创建'})]}),image:y.Z.PRESENTED_IMAGE_SIMPLE,style:L.emptyContainer})},renderItem:function(e){var t,r,n,o=[{key:"view",label:(0,T.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,T.jsx)(S.Z,{style:{marginRight:8}}),(0,T.jsx)("span",{children:"查看详情"})]}),onClick:function(t){t.domEvent.stopPropagation(),fe(e.id)}},{key:"delete",label:(0,T.jsxs)("div",{style:{display:"flex",alignItems:"center",color:"#ff4d4f"},children:[(0,T.jsx)(P.Z,{style:{marginRight:8,color:"#ff4d4f"}}),(0,T.jsx)("span",{children:"删除"})]}),onClick:function(t){t.domEvent.stopPropagation(),pe(e.id)}}];return(0,T.jsx)(v.Z.Item,{children:(0,T.jsxs)(b.Z,{hoverable:!0,style:L.card,onClick:function(){return fe(e.id)},bodyStyle:{paddingRight:"40px"},children:[(0,T.jsx)("div",{style:L.moreButton,onClick:function(e){e.stopPropagation()},children:(0,T.jsx)(C.Z,{menu:{items:o},trigger:["click"],placement:"bottomRight",getPopupContainer:function(e){return e.parentNode},overlayStyle:{minWidth:"120px",boxShadow:"0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",borderRadius:"8px"},children:(0,T.jsx)($.Z,{})})}),(0,T.jsxs)("div",{style:L.avatarContainer,children:[(0,T.jsx)("div",{style:L.avatarIcon,children:(0,T.jsx)(z.Z,{})}),(0,T.jsx)("span",{style:{fontWeight:"bold",fontSize:"16px"},children:e.name})]}),(0,T.jsx)(b.Z.Meta,{title:null,description:(0,T.jsxs)(N,{ellipsis:{rows:2},children:[(null===(t=W.find((function(t){return t.code===e.type})))||void 0===t?void 0:t.name)||e.type,(0,T.jsx)(k.Z,{color:(null===(r=_[e.status])||void 0===r?void 0:r.color)||"default",style:{marginLeft:"8px"},children:(null===(n=_[e.status])||void 0===n?void 0:n.text)||e.status})]})}),(0,T.jsxs)("div",{style:L.cardStats,children:[(0,T.jsxs)("span",{children:["文件数量：",e.file_count||0]}),(0,T.jsxs)("span",{children:["创建时间：",new Date(e.created_at).toLocaleDateString()]})]})]})})}})}),(0,T.jsx)(g.Z,{title:"新建审核任务",open:G,onOk:ue,onCancel:function(){U.resetFields(),K(!1)},okText:"创建",cancelText:"取消",destroyOnClose:!0,children:(0,T.jsxs)(p.Z,{form:U,layout:"vertical",name:"create_task_form",children:[(0,T.jsx)(p.Z.Item,{name:"taskName",label:"任务名称",rules:[{required:!0,message:"请输入任务名称!"}],children:(0,T.jsx)(h.Z,{placeholder:"例如：年度报告审核"})}),(0,T.jsx)(p.Z.Item,{name:"taskType",label:"审核规则类型",rules:[{required:!0,message:"请选择审核规则类型!"}],children:(0,T.jsx)(d.default,{placeholder:"选择对应的审核规则类型",children:W.map((function(e){return(0,T.jsxs)(B,{value:e.code,children:[e.name,e.description?" (".concat(e.description,")"):""]},e.id)}))})})]})})]})}},66309:function(e,t,r){r.d(t,{Z:function(){return I}});var n=r(67294),o=r(93967),a=r.n(o),l=r(98423),c=r(98787),s=r(69760),i=r(96159),d=r(45353),u=r(53124),p=r(11568),f=r(15063),g=r(14747),x=r(83262),h=r(83559);const m=e=>{const{lineWidth:t,fontSizeIcon:r,calc:n}=e,o=e.fontSizeSM;return(0,x.IX)(e,{tagFontSize:o,tagLineHeight:(0,p.bf)(n(e.lineHeightSM).mul(o).equal()),tagIconSize:n(r).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new f.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var y=(0,h.I$)("Tag",(e=>(e=>{const{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:n,componentCls:o,calc:a}=e,l=a(n).sub(r).equal(),c=a(t).sub(r).equal();return{[o]:Object.assign(Object.assign({},(0,g.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:c,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(m(e))),v),b=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};const C=n.forwardRef(((e,t)=>{const{prefixCls:r,style:o,className:l,checked:c,onChange:s,onClick:i}=e,d=b(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:f}=n.useContext(u.E_),g=p("tag",r),[x,h,m]=y(g),v=a()(g,`${g}-checkable`,{[`${g}-checkable-checked`]:c},null==f?void 0:f.className,l,h,m);return x(n.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},o),null==f?void 0:f.style),className:v,onClick:e=>{null==s||s(!c),null==i||i(e)}})))}));var k=C,j=r(98719);var Z=(0,h.bk)(["Tag","preset"],(e=>(e=>(0,j.Z)(e,((t,r)=>{let{textColor:n,lightBorderColor:o,lightColor:a,darkColor:l}=r;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:n,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:l,borderColor:l},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(m(e))),v);const w=(e,t,r)=>{const n="string"!=typeof(o=r)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,h.bk)(["Tag","status"],(e=>{const t=m(e);return[w(t,"success","Success"),w(t,"processing","Info"),w(t,"error","Error"),w(t,"warning","Warning")]}),v),P=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};const $=n.forwardRef(((e,t)=>{const{prefixCls:r,className:o,rootClassName:p,style:f,children:g,icon:x,color:h,onClose:m,bordered:v=!0,visible:b}=e,C=P(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:k,direction:j,tag:w}=n.useContext(u.E_),[$,z]=n.useState(!0),I=(0,l.Z)(C,["closeIcon","closable"]);n.useEffect((()=>{void 0!==b&&z(b)}),[b]);const O=(0,c.o2)(h),E=(0,c.yT)(h),T=O||E,B=Object.assign(Object.assign({backgroundColor:h&&!T?h:void 0},null==w?void 0:w.style),f),R=k("tag",r),[N,_,L]=y(R),H=a()(R,null==w?void 0:w.className,{[`${R}-${h}`]:T,[`${R}-has-color`]:h&&!T,[`${R}-hidden`]:!$,[`${R}-rtl`]:"rtl"===j,[`${R}-borderless`]:!v},o,p,_,L),M=e=>{e.stopPropagation(),null==m||m(e),e.defaultPrevented||z(!1)},[,A]=(0,s.Z)((0,s.w)(e),(0,s.w)(w),{closable:!1,closeIconRender:e=>{const t=n.createElement("span",{className:`${R}-close-icon`,onClick:M},e);return(0,i.wm)(e,t,(e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),M(t)},className:a()(null==e?void 0:e.className,`${R}-close-icon`)})))}}),F="function"==typeof C.onClick||g&&"a"===g.type,W=x||null,q=W?n.createElement(n.Fragment,null,W,g&&n.createElement("span",null,g)):g,D=n.createElement("span",Object.assign({},I,{ref:t,className:H,style:B}),q,A,O&&n.createElement(Z,{key:"preset",prefixCls:R}),E&&n.createElement(S,{key:"status",prefixCls:R}));return N(F?n.createElement(d.Z,{component:"Tag"},D):D)})),z=$;z.CheckableTag=k;var I=z}}]);