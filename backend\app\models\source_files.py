from mongoengine import Document, <PERSON>Field, DateTimeField, IntField, ObjectIdField, ListField, <PERSON>oleanField
from datetime import datetime
from pydantic import BaseModel
from typing import Optional, List
from bson import ObjectId
from app.utils.enums import FileStorageType, FileParserType

# MongoEngine 模型
class SourceFile(Document):
    meta = {
        'collection': 'source_files'
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId)
    knowledge_base_id = ObjectIdField(default=ObjectId)
    name = StringField(required=True)
    storage_path = StringField(required=True)
    storage_type = StringField(required=True, default=FileStorageType.LOCAL)
    data_type = StringField(required=True)  # 文件类型，如 pdf
    processing_status = StringField(default='pending')  # pending, processing, completed,error
    user_id = IntField(required=True)
    deleted_by = IntField(default=None)
    size = IntField(default=None)
    user_name = StringField()
    tags = ListField(StringField(), default=list)
    chunk_count = IntField(default=0)
    flg = IntField(required=True,default='0')  # 标记字段
    orc = IntField(required=True,default='0')  # 标记字段
    created_at = DateTimeField(default=datetime.now)
    basic_index = BooleanField(default=True)
    graph_index = BooleanField(default=False)
    parser_type = StringField(default=FileParserType.DEFAULT)
    forbidden = BooleanField(default=False)  # 上下线状态，False为已上线，True为已下线

# Pydantic 模型
class SourceFileBase(BaseModel):
    id: str
    name: str
    storage_path: str
    size: Optional[int] = 0
    data_type: str
    processing_status: Optional[str] = 'pending'
    chunk_count: Optional[int] = 0
    user_id: int
    created_at: Optional[datetime] = None
    user_name: Optional[str] = None
    tags: Optional[List[str]] = []
    flg: Optional[int] = 0

class SourceFileCreate(SourceFileBase):
    pass

class SourceFileUpdate(BaseModel):
    name: Optional[str] = None
    storage_path: Optional[str] = None
    data_type: Optional[str] = None
    processing_status: Optional[str] = None
    tags: Optional[List[str]] = None
    flg: Optional[int] = None

class SourceFileResponse(BaseModel):
    id: str
    name: str
    data_type: str
    size: int
    url: str
    deleteUrl: str
    deleteType: str

    class Config:
        from_attributes = True

class SourceFileInfoResponse(BaseModel):
    id: str
    name: str
    storage_path: str
    size: int = 0
    data_type: str
    processing_status: str = 'pending'
    chunk_count: int = 0
    created_at: datetime = None
    knowledge_base_id: str = None
    tags: Optional[List[str]] = []
    
    class Config:
        from_attributes = True