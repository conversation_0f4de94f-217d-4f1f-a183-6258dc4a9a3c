"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9440],{93933:function(e,n,t){t.d(n,{$Z:function(){return w},$o:function(){return f},Db:function(){return h},Mw:function(){return l},SJ:function(){return m},X1:function(){return y},Xw:function(){return d},bk:function(){return _},fx:function(){return B},qP:function(){return S},tn:function(){return k},zl:function(){return I}});var r=t(15009),o=t.n(r),a=t(99289),i=t.n(a),s=t(78158),c=t(10981);function l(e){return u.apply(this,arguments)}function u(){return(u=i()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/myConversations",{method:"GET",params:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function d(e){return p.apply(this,arguments)}function p(){return(p=i()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/conversations",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function f(e,n){return x.apply(this,arguments)}function x(){return(x=i()(o()().mark((function e(n,t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/conversationActive/"+n,{method:"PUT",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(e){return g.apply(this,arguments)}function g(){return(g=i()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/clearConversation/"+n,{method:"PUT"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function m(e){return v.apply(this,arguments)}function v(){return(v=i()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/conversations/"+n,{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(e,n){return b.apply(this,arguments)}function b(){return(b=i()(o()().mark((function e(n,t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/conversations/".concat(n),{method:"PUT",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function k(e){return j.apply(this,arguments)}function j(){return(j=i()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/message",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function w(e){return Z.apply(this,arguments)}function Z(){return(Z=i()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/messages/".concat(n),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function S(e){return C.apply(this,arguments)}function C(){return(C=i()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/delete_messages",{method:"DELETE",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function _(e){return P.apply(this,arguments)}function P(){return(P=i()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/messages/collected",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function B(e){return z.apply(this,arguments)}function z(){return(z=i()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/app/get_knowledge_bases",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function I(e){return T.apply(this,arguments)}function T(){return(T=i()(o()().mark((function e(n){var t,r;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=(0,c.bW)(),e.next=3,fetch("/api/app/chat/chat2kb",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify(n)});case 3:if((r=e.sent).ok){e.next=6;break}throw new Error("HTTP 错误！状态码：".concat(r.status));case 6:return e.abrupt("return",r);case 7:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},13973:function(e,n,t){t.d(n,{Z:function(){return b}});var r=t(15009),o=t.n(r),a=t(99289),i=t.n(a),s=t(5574),c=t.n(s),l=t(67294),u=t(55102),d=t(2453),p=t(17788),f=t(84567),x=t(78158);function h(e){return g.apply(this,arguments)}function g(){return(g=i()(o()().mark((function e(n){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,x.N)("/api/feedbacks",{method:"POST",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var m=t(85893),v=u.Z.TextArea,y=[{label:"回答不准确",value:"inaccurate"},{label:"回答不完整",value:"incomplete"},{label:"理解错误",value:"irrelevant"},{label:"生成幻觉",value:"hallucination"},{label:"内容混乱",value:"error"},{label:"有害内容",value:"harmful"},{label:"其他问题",value:"other"}],b=function(e){var n=e.visible,t=e.messageId,r=e.conversationId,a=e.appInfo,s=e.onClose,u=l.useState(""),x=c()(u,2),g=x[0],b=x[1],k=l.useState([]),j=c()(k,2),w=j[0],Z=j[1],S=function(){var e=i()(o()().mark((function e(){var n;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==w.length){e.next=3;break}return d.ZP.error("请至少选择一个反馈类型"),e.abrupt("return");case 3:return e.prev=3,n={message_id:t,conversation_id:r,app_info:a,content:g,feedback_types:w},console.log("feedbackData===>",n),e.next=8,h(n);case 8:e.sent.success?(d.ZP.success("感谢您的反馈！"),C()):d.ZP.error("提交反馈失败，请稍后重试"),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(3),console.error("提交反馈失败:",e.t0),d.ZP.error("提交反馈失败，请稍后重试");case 16:case"end":return e.stop()}}),e,null,[[3,12]])})));return function(){return e.apply(this,arguments)}}(),C=function(){b(""),Z([]),s()};return(0,m.jsxs)(p.Z,{title:"反馈问题",open:n,onOk:S,onCancel:C,okText:"提交",cancelText:"取消",children:[(0,m.jsxs)("div",{style:{marginBottom:16},children:[(0,m.jsx)("div",{style:{marginBottom:8},children:"请选择存在的问题（可多选）："}),(0,m.jsx)(f.Z.Group,{options:y,value:w,onChange:function(e){return Z(e)}})]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("div",{style:{marginBottom:8},children:"详细反馈（选填）："}),(0,m.jsx)(v,{value:g,onChange:function(e){return b(e.target.value)},placeholder:"请输入您的具体反馈意见...",rows:4})]})]})}},57257:function(e,n,t){t.r(n),t.d(n,{default:function(){return Ne}});var r=t(64599),o=t.n(r),a=t(19632),i=t.n(a),s=t(15009),c=t.n(s),l=t(99289),u=t.n(l),d=t(97857),p=t.n(d),f=t(9783),x=t.n(f),h=t(13769),g=t.n(h),m=t(5574),v=t.n(m),y=t(93461),b=t(34114),k=t(78205),j=t(78919),w=t(4628),Z=t(24495),S=t(9502),C=t(37864),_=t(26058),P=t(71471),B=t(42075),z=t(83622),I=t(2453),T=t(17788),R=t(74330),D=t(86250),E=t(83062),N=t(55102),K=t(85265),W=t(2487),F=t(11941),M=t(66309),A=t(67294),H=t(10048),O=t(10981),q=t(78404),Y=t(1832),L=t(14079),U=t(66513),G=t(93045),J=t(71255),X=t(51042),$=t(82061),V=t(43425),Q=t(64029),ee=t(34804),ne=t(47389),te=t(87784),re=t(25820),oe=t(75750),ae=t(12906),ie=t(85175),se=t(43471),ce=t(16596),le=t(28508),ue=t(29158),de=t(36027),pe=t(4161),fe=t(98165),xe=t(69753),he=t(27484),ge=t.n(he),me=(0,t(24444).kc)((function(e){var n=e.token;return{reference:{background:"".concat(n.colorBgLayout,"80"),minWidth:"400px",maxWidth:"720px",width:"50%"},layout:{width:"100%",minWidth:"800px",borderRadius:n.borderRadius,display:"flex",background:n.colorBgContainer,fontFamily:"AlibabaPuHuiTi, ".concat(n.fontFamily,", sans-serif"),".ant-prompts":{color:n.colorText},border:"3px solid",borderImage:"linear-gradient(45deg, ".concat(n.colorPrimary,", ").concat(n.colorSplit,") 1")},menu:{height:"100%",display:"flex",flexDirection:"column"},conversations:{padding:"0 12px",flex:1,overflowY:"auto"},chat:{height:"100%",width:"50%",maxWidth:"700px",margin:"0 auto",boxSizing:"border-box",display:"flex",flexDirection:"column",padding:n.paddingLG,gap:"16px"},messages:{flex:1},placeholder:{paddingTop:"32px"},sender:{boxShadow:n.boxShadow},logo:{display:"flex",height:"72px",alignItems:"center",justifyContent:"start",padding:"0 24px",boxSizing:"border-box",img:{width:"24px",height:"24px",display:"inline-block"},span:{display:"inline-block",margin:"0 8px",fontWeight:"bold",color:n.colorText,fontSize:"16px"}},addBtn:{background:"#1677ff0f",border:"1px solid #1677ff34",width:"calc(100% - 24px)",margin:"0 12px 24px 12px"},wiseAnswer:{display:"block",padding:"12px",background:"#e6f7ff",borderRadius:"8px",borderLeft:"4px solid #1890ff",margin:"8px 0"}}})),ve=t(93933),ye=t(58258),be=t(13973),ke=t(45435),je=t(85893),we=["href","children"],Ze=["href","children"],Se=(_.Z.Header,_.Z.Content,_.Z.Sider);P.Z.Title,P.Z.Paragraph,new H.Z({html:!0,breaks:!0,linkify:!0});function Ce(e){return e+"-"+Date.now()}var _e=function(e,n){return(0,je.jsxs)(B.Z,{align:"start",children:[e,(0,je.jsx)("span",{children:n})]})},Pe=[{key:"1",label:_e((0,je.jsx)(Y.Z,{style:{color:"#FF4D4F"}}),"热门合规主题"),description:"您想了解哪些合规问题？",children:[{key:"1-1",description:"如何确保金融产品合规销售？"},{key:"1-2",description:"反洗钱相关规定有哪些？"},{key:"1-3",description:"客户信息保护要求有哪些？"}]},{key:"2",label:_e((0,je.jsx)(L.Z,{style:{color:"#1890FF"}}),"合规风控"),description:"了解金融业务合规要求",children:[{key:"2-1",icon:(0,je.jsx)(U.Z,{}),description:"业务操作合规指南"},{key:"2-2",icon:(0,je.jsx)(G.Z,{}),description:"风险控制要点解析"},{key:"2-3",icon:(0,je.jsx)(J.Z,{}),description:"合规案例分析"}]},{key:"3",label:_e((0,je.jsx)(L.Z,{style:{color:"#1890FF"}}),"监管政策"),description:"了解最新监管要求",children:[{key:"3-1",icon:(0,je.jsx)(U.Z,{}),description:"最新监管政策解读"},{key:"3-2",icon:(0,je.jsx)(G.Z,{}),description:"合规检查重点提示"},{key:"3-3",icon:(0,je.jsx)(J.Z,{}),description:"违规处罚案例警示"}]}],Be=[{key:"historyConversation",description:"历史对话",icon:(0,je.jsx)(L.Z,{style:{color:"#FF4D4F"}})},{key:"newConversation",description:"新建对话",icon:(0,je.jsx)(X.Z,{style:{color:"#1890FF"}})},{key:"clearConversation",description:"清空对话",icon:(0,je.jsx)($.Z,{style:{color:"#1890FF"}})},{key:"knowledgeBaseSetting",description:"知识库设置",icon:(0,je.jsx)(V.Z,{style:{color:"#1890FF"}})}],ze=(0,O.bG)(),Ie=(0,q.kH)(),Te="chat2kb",Re=function(e){return"string"!=typeof e?"":e.replace(/\[\[citation:(\d+)\]\]/g,(function(e,n){return"[".concat(n,"](#citation-").concat(n,")")}))},De=function(e){var n=e.content,t=e.messageId,r=(0,A.useState)(!1),o=v()(r,2),a=o[0],i=o[1],s=function(e){if("string"!=typeof e)return{processedContent:"",thinkBlocks:[]};var n=[];return{processedContent:e.replace(/<think>([\s\S]*?)<\/think>/g,(function(e,t){return n.push(t.trim()),""})).trim(),thinkBlocks:n}}(n),c=s.processedContent,l=s.thinkBlocks,u=A.useContext(Ee),d=(u.expandedRefs,u.setExpandedRefs);return(0,je.jsxs)("div",{style:{position:"relative"},children:[function(){try{if(!c)return null;var e=Re(c),n={a:function(e){var n=e.href,r=e.children,o=g()(e,we);if(n&&n.startsWith("#citation-")){var a=n.replace("#citation-",""),i=parseInt(a)-1;return(0,je.jsx)("span",{style:{color:"#1890ff",fontWeight:"bold",fontSize:"0.9em",cursor:"pointer",textDecoration:"underline",margin:"0 2px"},onClick:function(){var e="".concat(t,"-").concat(i);d((function(n){return n[e]?{}:x()({},e,!0)})),setTimeout((function(){var n=document.querySelector('[data-ref-key="'.concat(e,'"]'));n&&n.scrollIntoView({behavior:"smooth",block:"center"})}),100)},children:r})}return(0,je.jsx)("a",p()(p()({href:n},o),{},{children:r}))},p:function(e){var n=e.children;return(0,je.jsx)("p",{style:{marginBottom:"0.6em",marginTop:"0.6em"},children:n})}};return(0,je.jsx)("div",{className:"markdown-content",style:{lineHeight:1.5},children:(0,je.jsx)(ke.UG,{components:n,children:e})})}catch(e){return console.error("渲染内容时出错:",e),(0,je.jsx)("div",{style:{color:"red"},children:"内容渲染失败"})}}(),l.length>0&&(0,je.jsxs)("div",{style:{marginTop:12},children:[(0,je.jsx)(z.ZP,{type:"text",size:"small",icon:a?(0,je.jsx)(Q.Z,{}):(0,je.jsx)(ee.Z,{}),onClick:function(){return i(!a)},style:{color:"#666",fontSize:12,padding:0,height:"auto",background:"transparent"},children:(0,je.jsxs)("span",{style:{marginLeft:4},children:[a?"收起":"展开","思考过程 (",l.length,")"]})}),a&&(0,je.jsx)("div",{style:{marginTop:8,padding:12,backgroundColor:"#f8f9fa",border:"1px solid #e9ecef",borderRadius:6,fontSize:"13px",lineHeight:1.4,color:"#495057"},children:l.map((function(e,n){return(0,je.jsx)("div",{style:{marginBottom:n<l.length-1?8:0},children:(0,je.jsx)(ke.UG,{components:{p:function(e){var n=e.children;return(0,je.jsx)("p",{style:{marginBottom:"0.6em",marginTop:"0.6em"},children:n})}},children:e})},n)}))})]})]})},Ee=A.createContext({expandedRefs:{},setExpandedRefs:function(){}}),Ne=function(){var e=(0,A.useState)({}),n=v()(e,2),t=n[0],r=n[1],a=(0,A.useState)(!1),s=v()(a,2),l=s[0],d=s[1],f=(0,A.useState)(null),h=v()(f,2),m=h[0],_=h[1],H=me().styles,q=(0,A.useState)(window.innerHeight),Y=v()(q,1)[0],U=A.useRef(),G=A.useState(""),J=v()(G,2),Q=J[0],ee=J[1],he=A.useState([]),we=v()(he,2),_e=we[0],Ne=we[1],Ke=A.useState(),We=v()(Ke,2),Fe=We[0],Me=We[1],Ae=A.useState(void 0),He=v()(Ae,2),Oe=He[0],qe=He[1],Ye=(0,A.useState)(!1),Le=v()(Ye,2),Ue=Le[0],Ge=Le[1],Je=(0,A.useState)(!1),Xe=v()(Je,2),$e=Xe[0],Ve=Xe[1],Qe=(0,A.useState)(!1),en=v()(Qe,2),nn=en[0],tn=en[1],rn=(0,A.useState)(""),on=v()(rn,2),an=on[0],sn=on[1],cn=(0,A.useState)(""),ln=v()(cn,2),un=ln[0],dn=ln[1],pn=(0,A.useState)([]),fn=v()(pn,2),xn=fn[0],hn=fn[1],gn=(0,A.useRef)(null),mn=(0,A.useState)(!1),vn=v()(mn,2),yn=vn[0],bn=vn[1],kn=(0,A.useState)(""),jn=v()(kn,2),wn=jn[0],Zn=jn[1],Sn=(0,A.useState)([]),Cn=v()(Sn,2),_n=(Cn[0],Cn[1],(0,A.useState)([])),Pn=v()(_n,2),Bn=Pn[0],zn=Pn[1],In=(0,A.useState)([]),Tn=v()(In,2),Rn=Tn[0],Dn=Tn[1],En=(0,A.useState)(!1),Nn=v()(En,2),Kn=Nn[0],Wn=Nn[1],Fn=function(e){Zn(e),bn(!0)},Mn=function(e){var n=xn.find((function(n){return n.message_id===e}));n&&navigator.clipboard.writeText(n.content).then((function(){I.ZP.success("复制成功")})).catch((function(){I.ZP.error("复制失败")}))},An=(0,A.useRef)({selectedKnowledgeBases:Rn,availableKnowledgeBases:Bn,activeConversationKey:Fe,conversationKnowledgeBaseId:Oe});(0,A.useEffect)((function(){An.current={selectedKnowledgeBases:Rn,availableKnowledgeBases:Bn,activeConversationKey:Fe,conversationKnowledgeBaseId:Oe}}),[Rn,Bn,Fe,Oe]);var Hn,On=function(){var e=u()(c()().mark((function e(){var n,t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=(0,O.bG)(),console.log("🚀 ~ fetchKnowledgeBases ~ userInfo:",n),e.prev=2,e.next=5,(0,ve.fx)({user_id:parseInt((null==n?void 0:n.id)||"0")});case 5:return t=e.sent,console.log("🚀 ~ fetchKnowledgeBases ~ response:",t),zn(t.data||[]),e.abrupt("return",t.data||[]);case 11:return e.prev=11,e.t0=e.catch(2),console.error("Error fetching knowledge bases:",e.t0),e.abrupt("return",[]);case 15:case"end":return e.stop()}}),e,null,[[2,11]])})));return function(){return e.apply(this,arguments)}}(),qn=(0,y.Z)({request:(Hn=u()(c()().mark((function e(n,t){var r,a,s,l,u,d,p,f,x,h,g,m,v,y,b,k,j,w,Z,S,C,_,P,B,z,T,R,D,E,N,K,W,F,M,A,H,q,Y,L,G,J;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.messages,a=n.message,s=t.onSuccess,l=t.onUpdate,u=t.onError,e.prev=2,p=An.current,f=p.selectedKnowledgeBases,x=p.availableKnowledgeBases,h=p.conversationKnowledgeBaseId,console.log("🚀 ~ request中的最新状态:",{selectedKnowledgeBases:f,availableKnowledgeBases:x,conversationKnowledgeBaseId:h}),!$e){e.next=8;break}return I.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 8:if(Ve(!0),(0,O.bW)(),g=a?a.id:Ce(U.current),a){e.next=15;break}return s({content:"出现了异常: 消息为空",role:"assistant",id:g,references:[],collected:!1,query:[]}),Ve(!1),e.abrupt("return");case 15:if(c=f,console.log("🚀 ~ getIdsByNames 开始执行"),console.log("🚀 ~ 输入的names:",c),console.log("🚀 ~ names长度:",null==c?void 0:c.length),m=c&&Array.isArray(c)&&0!==c.length?x&&Array.isArray(x)?c.map((function(e,n){var t=x.find((function(n){return n&&n.name&&n.name===e}));return t?t._id:null})).filter((function(e){return null!=e})):(console.log("🚀 ~ availableKnowledgeBases为空或不是数组"),[]):(console.log("🚀 ~ names为空或不是数组"),[]),h&&(m=[].concat(i()(m),[h])),console.log("🚀 ~ 最终的selectedIds:",m),v={conversation_id:U.current||"",message_id:g,meta_data:{},extra:{},role:a?a.role:"user",content:a?a.content:"",app_info:Te,user_id:null==ze?void 0:ze.id,user_name:null==ze?void 0:ze.name,references:[],token_count:null,price:null,collected:!1,created_at:ge()().format("YYYY-MM-DD HH:mm:ss"),knowledge_ids:m,contextData:[],query:[]},hn((function(e){var n=[].concat(i()(e),[v]);return console.log("更新后的消息列表:",n),n})),U.current){e.next=24;break}throw I.ZP.error("No active conversation selected"),new Error("No active conversation selected");case 24:return console.log("activeKey===>",U.current),y={conversation_id:U.current,app_info:Te,user_id:parseInt(null==ze?void 0:ze.id),user_name:null==ze?void 0:ze.name,extra:{},messages:r,kb_id:m},b={id:Ce(U.current),role:"assistant",content:"",references:[],collected:!1,query:[]},k=!1,j="",w=[],l(b),e.next=33,(0,ve.zl)(y);case 33:if(Z=e.sent,console.log("response===>",Z),Z.ok){e.next=37;break}throw new Error("HTTP 错误！状态码：".concat(Z.status));case 37:if(S=null===(d=Z.body)||void 0===d?void 0:d.getReader()){e.next=40;break}throw new Error("当前浏览器不支持 ReadableStream。");case 40:C=new TextDecoder("utf-8"),console.log("userInfo===>",ze),_={conversation_id:U.current||"",message_id:b.id,meta_data:{},extra:{},role:"assistant",content:"",app_info:Te,user_id:null==ze?void 0:ze.id,user_name:null==ze?void 0:ze.name,references:[],token_count:null,price:null,collected:!1,created_at:ge()().format("YYYY-MM-DD HH:mm:ss"),knowledge_ids:m};case 43:if(k){e.next=114;break}return e.next=46,S.read();case 46:P=e.sent,B=P.value,P.done&&(k=!0),j+=C.decode(B,{stream:!0}),z=j.split("\n\n"),j=z.pop()||"",T=o()(z),e.prev=54,T.s();case 56:if((R=T.n()).done){e.next=104;break}if(""!==(D=R.value).trim()){e.next=60;break}return e.abrupt("continue",102);case 60:E=D.split("\n"),N=null,K=null,W=o()(E);try{for(W.s();!(F=W.n()).done;)(M=F.value).startsWith("event: ")?N=M.substring(7).trim():M.startsWith("data: ")&&(K=M.substring(6))}catch(e){W.e(e)}finally{W.f()}if(!K){e.next=102;break}e.t0=N,e.next="answer"===e.t0?69:"moduleStatus"===e.t0?81:"appStreamResponse"===e.t0?83:"flowResponses"===e.t0?85:"end"===e.t0?87:"error"===e.t0?89:102;break;case 69:if("[DONE]"===K){e.next=80;break}e.prev=70,H=JSON.parse(K),(q=(null===(A=H.choices[0])||void 0===A||null===(A=A.delta)||void 0===A?void 0:A.content)||"")&&(b.content+=q,l(b)),e.next=80;break;case 76:return e.prev=76,e.t1=e.catch(70),console.error("Error parsing answer data:",e.t1),e.abrupt("return",s({content:"出现了异常:"+K,role:"assistant",id:Ce(U.current),references:[],query:[],collected:!1}));case 80:return e.abrupt("break",102);case 81:try{Y=JSON.parse(K),console.log("模块状态：",Y)}catch(e){console.error("Error parsing moduleStatus data:",e)}return e.abrupt("break",102);case 83:try{L=JSON.parse(K),console.log("appStreamData===>",L),console.log("appStreamData[0].context===>",L[0].context),w=L[0].context,b.references=w}catch(e){console.error("Error parsing appStreamResponse data:",e)}return e.abrupt("break",102);case 85:try{console.log("flowResponsesData",K)}catch(e){console.error("Error parsing flowResponses data:",e)}return e.abrupt("break",102);case 87:return k=!0,e.abrupt("break",102);case 89:e.prev=89,k=!0,G=JSON.parse(K),b.content=G.message,_.role="assistant",l(b),e.next=101;break;case 97:throw e.prev=97,e.t2=e.catch(89),console.error("Error event received:",e.t2),e.t2;case 101:return e.abrupt("break",102);case 102:e.next=56;break;case 104:e.next=109;break;case 106:e.prev=106,e.t3=e.catch(54),T.e(e.t3);case 109:return e.prev=109,T.f(),e.finish(109);case 112:e.next=43;break;case 114:if(console.info(b),s(b),!b.content||""===b.content.trim()){e.next=123;break}return _.content=b.content,_.references=w,e.next=121,(0,ve.tn)(_);case 121:(J=e.sent).success?(_.message_id=J.data.message_id,console.log("创建消息成功，返回数据:",J.data),hn((function(e){var n=[].concat(i()(e),[J.data]);return console.log("更新后的消息列表:",n),n}))):I.ZP.error("消息上报失败");case 123:e.next=130;break;case 125:e.prev=125,e.t4=e.catch(2),console.log("error===>",e.t4),s({content:"出现了异常，系统正在处理其他对话。请稍后重试",role:"assistant",id:Ce(U.current),references:[],collected:!1,query:[]}),u(e.t4 instanceof Error?e.t4:new Error("Unknown error"));case 130:return e.prev=130,Ve(!1),e.finish(130);case 133:case"end":return e.stop()}var c}),e,null,[[2,125,130,133],[54,106,109,112],[70,76],[89,97]])}))),function(e,n){return Hn.apply(this,arguments)})}),Yn=v()(qn,1)[0],Ln=(0,b.Z)({agent:Yn}),Un=Ln.onRequest,Gn=Ln.messages,Jn=Ln.setMessages,Xn=function(e){console.log("activeKey 设置",e),U.current=e,Me(e)},$n=function(e){var n=e.filter((function(e){return e.pinned})).sort((function(e,n){return new Date(n.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return p()(p()({},e),{},{key:e.id||"",label:e.conversation_name||e.id,group:"置顶"})})),t=e.filter((function(e){return!e.pinned})).sort((function(e,n){return new Date(n.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return p()(p()({},e),{},{key:e.id||"",label:e.conversation_name||"",group:"对话"})}));Ne([].concat(i()(n),i()(t)))},Vn=function(e){return 0===e.length?null:e.reduce((function(e,n){return new Date(n.active_at)>new Date(e.active_at)?n:e}))},Qn=function(){var e=u()(c()().mark((function e(n){var t,r,o,a,i;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,Ve(!0),console.info("获取对话信息",n),t=ge()().format("YYYY-MM-DD HH:mm:ss"),e.next=6,(0,ve.$o)(n,{conversation_name:null,active_at:t,pinned_at:null,pinned:null});case 6:if(null==(r=e.sent)||!r.messages){e.next=30;break}if(console.info("设置对话信息",r.messages),o=r.messages.map((function(e){return{id:e.id||e.message_id,message:{id:e.id||e.message_id,content:e.content,role:e.role,references:e.references||[],collected:e.collected||!1,query:e.query||[]},status:"assistant"===e.role?"success":"local",meta:e.meta||{avatar:"assistant"===e.role?(null==Ie?void 0:Ie.logo)||"/static/logo.png":(null==ze?void 0:ze.avatar)||"/avatar/default.jpeg"}}})),hn(r.messages),Jn(o),Xn(n),!(r.messages.length>0)){e.next=28;break}if(!((a=r.messages[r.messages.length-1]).knowledge_ids&&Array.isArray(a.knowledge_ids)&&a.knowledge_ids.length>0)){e.next=26;break}if(console.log("🚀 ~ 获取到最后一条消息的知识库IDs:",a.knowledge_ids),Bn&&0!==Bn.length){e.next=21;break}return console.log("🚀 ~ 可用知识库列表为空，尝试获取"),e.next=21,On();case 21:i=a.knowledge_ids.map((function(e){var n=Bn.find((function(n){return n._id===e}));return n?(console.log("🚀 ~ 找到知识库: ID=".concat(e,", 名称=").concat(n.name)),n.name):(console.log("🚀 ~ 未找到知识库: ID=".concat(e)),null)})).filter((function(e){return null!==e})),console.log("🚀 ~ 转换后的知识库名称:",i),Dn(i),e.next=28;break;case 26:console.log("🚀 ~ 最后一条消息没有知识库IDs"),Dn([]);case 28:e.next=31;break;case 30:I.ZP.error("获取对话信息失败");case 31:e.next=36;break;case 33:e.prev=33,e.t0=e.catch(0),console.error("切换对话时出错：",e.t0);case 36:return e.prev=36,Ve(!1),Me(n),e.finish(36);case 40:case"end":return e.stop()}}),e,null,[[0,33,36,40]])})));return function(n){return e.apply(this,arguments)}}(),et=function(){var e=u()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(U.current){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,(0,ve.Db)(U.current);case 4:e.sent.success?(hn([]),Jn([]),gn.current&&gn.current.updateReferenceList([])):I.ZP.error("清空对话失败");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),nt=function(){var e=u()(c()().mark((function e(){var n,t,r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!$e){e.next=3;break}return I.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(!(n=(0,O.bG)())){e.next=20;break}return e.prev=5,Dn([]),t=(new Date).toLocaleString(),r="对话-".concat(t),e.next=11,(0,ve.Xw)({user_id:parseInt(n.id),user_name:n.name,conversation_name:r,app_info:Te});case 11:o=e.sent,$n([].concat(i()(_e),[{key:o.id||"",id:o.id||"",label:o.conversation_name||"",conversation_name:o.conversation_name||"",active_at:o.active_at||"",pinned_at:o.pinned_at,pinned:o.pinned||!1,messages:[]}])),Xn(o.id||""),et(),e.next=20;break;case 17:e.prev=17,e.t0=e.catch(5),console.error("创建新对话时出错：",e.t0);case 20:case"end":return e.stop()}}),e,null,[[5,17]])})));return function(){return e.apply(this,arguments)}}(),tt=function(){var e=u()(c()().mark((function e(n){var t,r,o,a,i;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=_e.find((function(e){return e.key===n}))){e.next=3;break}return e.abrupt("return");case 3:return r=t.pinned,o=!r,e.prev=6,a=ge()().format("YYYY-MM-DD HH:mm:ss"),e.next=10,(0,ve.X1)(n,{conversation_name:null,active_at:null,pinned:o,pinned_at:a});case 10:i=_e.map((function(e){return e.key===n?p()(p()({},e),{},{pinned:o}):e})),$n(i),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),console.error("更新置顶状态时出错：",e.t0);case 17:case"end":return e.stop()}}),e,null,[[6,14]])})));return function(n){return e.apply(this,arguments)}}(),rt=function(){var e=u()(c()().mark((function e(n){var t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,ve.SJ)(n);case 3:if(t=_e.filter((function(e){return e.key!==n})),console.log("🚀 ~ handleDeleteConversation ~ updatedItems:",t),$n(t),console.log("activeKeyRef.current===>",U.current),console.log("deleteConversation===>",n),U.current!==n){e.next=14;break}if(!(t.length>0)){e.next=14;break}return e.next=12,Xn(t[0].key||"");case 12:return e.next=14,Qn(t[0].key||"");case 14:e.next=19;break;case 16:e.prev=16,e.t0=e.catch(0),console.error("删除对话时出错：",e.t0);case 19:case"end":return e.stop()}}),e,null,[[0,16]])})));return function(n){return e.apply(this,arguments)}}(),ot=function(){var e=u()(c()().mark((function e(n,t){var r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,_e.find((function(e){return e.key===n}))){e.next=4;break}return e.abrupt("return");case 4:return r={conversation_name:t,active_at:null,pinned_at:null,pinned:null},e.next=7,(0,ve.X1)(n,r);case 7:null!=(o=e.sent)&&o.success?Ne((function(e){return e.map((function(e){return e.key===n?p()(p()({},e),{},{label:t}):e}))})):I.ZP.error("更新对话标题失败"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("更新对话标题时出错：",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(n,t){return e.apply(this,arguments)}}(),at=function(){var e=u()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!$e){e.next=3;break}return I.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:return e.next=5,Qn(n);case 5:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}();(0,A.useEffect)((function(){var e=function(){var e=u()(c()().mark((function e(){var n,t,r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(n=(0,O.bG)())){e.next=30;break}return e.prev=2,Ve(!0),e.next=6,(0,ve.Mw)({user_id:n.id,app_info:Te});case 6:if(!(t=e.sent).success||!Array.isArray(t.data)){e.next=22;break}if(0!==t.data.length){e.next=13;break}return e.next=11,nt();case 11:e.next=22;break;case 13:if(r=Vn(t.data),$n(t.data),!r){e.next=20;break}return e.next=18,Qn(r.id);case 18:e.next=22;break;case 20:return e.next=22,Qn(t.data[0].id||"");case 22:e.next=27;break;case 24:e.prev=24,e.t0=e.catch(2),console.error("初始化对话时出错：",e.t0);case 27:return e.prev=27,Ve(!1),e.finish(27);case 30:case"end":return e.stop()}}),e,null,[[2,24,27,30]])})));return function(){return e.apply(this,arguments)}}();e()}),[Te]);var it=function(){var e=u()(c()().mark((function e(n){var t,r,o,a;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("重新生成消息:",n),e.prev=1,t=xn.findIndex((function(e){return e.message_id===n})),console.log("currentIndex===>",t),-1!==t){e.next=7;break}return I.ZP.error("未找到指定消息"),e.abrupt("return");case 7:return r=xn[t],o=xn.slice(t),console.log("将要删除的消息:",o),e.next=12,(0,ve.qP)(o.map((function(e){return e.message_id})));case 12:e.sent.success||I.ZP.error("删除消息失败"),hn((function(e){return e.slice(0,t)})),Jn((function(e){return e.slice(0,t)})),"assistant"===r.role?(a=xn.slice(0,t).reverse().find((function(e){return"user"===e.role})))&&Un({id:n,role:"user",content:a.content,references:[],query:[],collected:!1}):Un({id:n,role:"user",content:r.content,references:[],query:[],collected:!1}),I.ZP.success("正在重新生成回复..."),e.next=24;break;case 20:e.prev=20,e.t0=e.catch(1),console.error("重新生成消息时出错：",e.t0),I.ZP.error("重新生成消息失败");case 24:case"end":return e.stop()}}),e,null,[[1,20]])})));return function(n){return e.apply(this,arguments)}}(),st=function(){var e=u()(c()().mark((function e(n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:T.Z.confirm({title:"确认删除",content:"确定要删除这条消息吗？删除后不可恢复。",okText:"确认",cancelText:"取消",onOk:function(){return u()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("delete messageId===>",n),e.next=4,(0,ve.$Z)(n);case 4:e.sent.success?(hn((function(e){return e.filter((function(e){return e.message_id!==n}))})),console.log("delete currentConversationMessages===>",xn),Jn((function(e){return e.filter((function(e){return e.message.id!==n}))})),I.ZP.success("消息及相关引用已删除")):I.ZP.error("删除消息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除消息时出错：",e.t0),I.ZP.error("删除消息失败");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),ct=function(){var e=u()(c()().mark((function e(n,t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("收藏状态切换:",n,t),e.next=3,(0,ve.bk)({message_id:n,collected:!t});case 3:e.sent.success?(I.ZP.success(t?"取消收藏成功":"收藏成功"),Jn((function(e){return e.map((function(e){return e.id===n?p()(p()({},e),{},{message:p()(p()({},e.message),{},{collected:!t})}):e}))})),hn((function(e){return e.map((function(e){return e.message_id===n?p()(p()({},e),{},{collected:!t}):e}))}))):I.ZP.error(t?"取消收藏失败":"收藏失败");case 5:case"end":return e.stop()}}),e)})));return function(n,t){return e.apply(this,arguments)}}(),lt=function(){var e=u()(c()().mark((function e(n){var t,r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("nextContent===>","11111111----------+++++++++++"),console.log("nextContent===>",n),n){e.next=4;break}return e.abrupt("return");case 4:if(t=Rn&&Rn.length>0,r=bt&&bt.length>0,o=!!Oe,t||r||o){e.next=10;break}return I.ZP.warning("您还尚未选择知识库，请先选择知识库！"),e.abrupt("return");case 10:Un({id:Ce(U.current),role:"user",content:n,references:[],collected:!1,query:[]}),kt([]),ee("");case 13:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),ut=function(){var e=u()(c()().mark((function e(n){var t,r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!$e){e.next=3;break}return I.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(t=n.data,r=t.key,o=t.description,"historyConversation"!==r){e.next=8;break}Ge(!0),e.next=19;break;case 8:if("newConversation"!==r){e.next=13;break}return e.next=11,nt();case 11:e.next=19;break;case 13:if("clearConversation"!==r){e.next=18;break}return e.next=16,et();case 16:e.next=19;break;case 18:"knowledgeBaseSetting"===r?(On(),Wn(!0)):Un({id:Ce(U.current),role:"user",content:o,references:[],collected:!1,query:[]});case 19:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),dt=function(e,n){n&&n.stopPropagation(),_(e),d(!0)},pt=(0,je.jsxs)(B.Z,{direction:"vertical",size:16,className:H.placeholder,children:[(0,je.jsx)(k.Z,{variant:"borderless",icon:(0,je.jsx)("img",{src:(null==Ie?void 0:Ie.logo)||"/static/logo.png",alt:"logo"}),title:"你好，我是金融合规助手.",description:"基于智能知识库和监管规则，为您提供专业的金融合规咨询和指导服务"}),(0,je.jsx)(j.Z,{title:"您想了解哪些合规问题？",items:Pe,styles:{list:{width:"100%"},item:{backgroundImage:"linear-gradient(137deg, #e5f4ff 0%, #efe7ff 100%)",border:0,flex:1}},onItemClick:ut})]}),ft=Gn.length>0?Gn.map((function(e){var n=e.id,o=e.message,a=e.status;return{key:U.current+"_"+n,loadingRender:function(){return(0,je.jsxs)(B.Z,{children:[(0,je.jsx)(R.Z,{size:"small"}),"模型思考中..."]})},loading:"loading"===a&&o.content.length<1,content:o.content,messageRender:function(e){return function(e,n){if("string"!=typeof e)return String(e);if(!e.includes("<think>")){var t=Re(e),o={a:function(e){var t=e.href,o=e.children,a=g()(e,Ze);if(t&&t.startsWith("#citation-")){var i=t.replace("#citation-",""),s=parseInt(i)-1;return(0,je.jsx)("span",{style:{color:"#1890ff",fontWeight:"bold",fontSize:"0.9em",cursor:"pointer",textDecoration:"underline",margin:"0 2px"},onClick:function(){var e="".concat(n,"-").concat(s);r((function(n){return n[e]?{}:x()({},e,!0)})),setTimeout((function(){var n=document.querySelector('[data-ref-key="'.concat(e,'"]'));n&&n.scrollIntoView({behavior:"smooth",block:"center"})}),100)},children:o})}return(0,je.jsx)("a",p()(p()({href:t},a),{},{children:o}))},p:function(e){var n=e.children;return(0,je.jsx)("p",{style:{marginBottom:"0.6em",marginTop:"0.6em"},children:n})}};return(0,je.jsx)("div",{className:"markdown-content",style:{lineHeight:1.5},children:(0,je.jsx)(ke.UG,{components:o,children:t})})}return(0,je.jsx)(De,{content:e,messageId:n})}(e,o.id)},shape:"local"===a?"corner":"round",variant:"local"===a?"filled":"borderless",avatar:"local"===a?{src:(null==ze?void 0:ze.avatar)||"/avatar/default.jpeg"}:{src:(null==Ie?void 0:Ie.logo)||"/static/logo.png"},placement:"local"!==a?"start":"end",footer:"local"!==a?(0,je.jsxs)(D.Z,{children:[o.references&&o.references.length>0&&(0,je.jsxs)("div",{style:{marginTop:8,width:"100%"},children:[(0,je.jsxs)("div",{style:{fontWeight:"bold",marginBottom:8,fontSize:13},children:["引用来源 (",o.references.length,")"]}),(0,je.jsx)("div",{children:o.references.map((function(e,n){return(0,je.jsxs)("div",{style:{marginBottom:8},"data-ref-index":n,"data-ref-key":"".concat(o.id,"-").concat(n),children:[(0,je.jsxs)("div",{style:{display:"flex",alignItems:"center",cursor:"pointer",padding:"8px 13px",border:"1px solid #f0f0f0",borderRadius:4,backgroundColor:"#fafafa",justifyContent:"space-between"},onClick:function(n){n.stopPropagation(),dt(e,n)},children:[(0,je.jsxs)("div",{style:{fontWeight:"bold",fontSize:12,color:"#666"},children:[n+1,". ",e.source_name&&e.source_name.length>25?e.source_name.slice(0,25)+"...":e.source_name||"来源 ".concat(n+1)]}),(0,je.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:8},children:[(0,je.jsx)(z.ZP,{type:"text",size:"small",icon:(0,je.jsx)(L.Z,{}),onClick:function(n){return dt(e,n)},style:{fontSize:12,color:"#1890ff",padding:"0 4px",height:20,border:"none"},title:"查看详情"}),(0,je.jsx)("span",{style:{fontSize:12,color:"#999",cursor:"pointer"},onClick:function(e){e.stopPropagation();var t="".concat(o.id,"-").concat(n);r((function(e){return e[t]?{}:x()({},t,!0)}))},children:t["".concat(o.id,"-").concat(n)]?"▲":"▼"})]})]}),t["".concat(o.id,"-").concat(n)]&&(0,je.jsx)("div",{style:{padding:8,border:"1px solid #f0f0f0",borderTop:"none",borderRadius:"0 0 4px 4px",backgroundColor:"#fff",fontSize:12,lineHeight:1.4,color:"#333"},children:e.content})]},n)}))})]}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:(0,je.jsx)($.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),st(o.id)}}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:o.collected?(0,je.jsx)(re.Z,{style:{color:"#FFD700"}}):(0,je.jsx)(oe.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),ct(o.id,o.collected)}}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:(0,je.jsx)(ae.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),Fn(o.id)}}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:(0,je.jsx)(ie.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),Mn(o.id)}})]}):(0,je.jsxs)(D.Z,{children:[(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:(0,je.jsx)(se.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),it(o.id)}}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:(0,je.jsx)($.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),st(o.id)}}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:o.collected?(0,je.jsx)(re.Z,{style:{color:"#FFD700"}}):(0,je.jsx)(oe.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),ct(o.id,o.collected)}}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:(0,je.jsx)(ae.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),Fn(o.id)}}),(0,je.jsx)(z.ZP,{size:"small",type:"text",icon:(0,je.jsx)(ie.Z,{style:{color:"#ccc"}}),onClick:function(e){e.stopPropagation(),Mn(o.id)}})]})}})):[{content:pt,variant:"borderless"}],xt=A.useState(!1),ht=v()(xt,2),gt=ht[0],mt=ht[1],vt=A.useState([]),yt=v()(vt,2),bt=yt[0],kt=yt[1],jt=A.useState(""),wt=v()(jt,2),Zt=(wt[0],wt[1],A.useRef(null)),St=A.useRef(null),Ct=(0,je.jsx)(w.Z.Header,{title:"Attachments",styles:{content:{padding:0}},open:gt,onOpenChange:mt,forceRender:!0,children:(0,je.jsx)(Z.Z,{ref:Zt,beforeUpload:function(){return!1},items:bt,onChange:function(e){var n=e.fileList,t=n.filter((function(e){return!bt.some((function(n){return n.uid===e.uid}))}));kt(n),t.forEach(function(){var e=u()(c()().mark((function e(n){var t,r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=U.current,1,r=[n.originFileObj].filter((function(e){return e})),"","","chat",e.next=8,(0,ye.pj)(t,1,r,"","","chat");case 8:o=e.sent,console.log("🚀 ~ newFiles.forEach ~ response:",o),o.detail?I.ZP.error("上传文件失败"):(I.ZP.success("上传文件成功"),qe(U.current));case 11:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}())},placeholder:function(e){return"drop"===e?{title:"Drop file here"}:{icon:(0,je.jsx)(ce.Z,{}),title:"Upload files",description:"Click or drag files to this area to upload"}},getDropContainer:function(){var e;return null===(e=St.current)||void 0===e?void 0:e.nativeElement}})}),_t=(0,je.jsxs)("div",{className:H.logo,children:[(0,je.jsx)("span",{children:"对话记录"}),(0,je.jsx)(E.Z,{title:"新对话",children:(0,je.jsx)(z.ZP,{type:"text",icon:(0,je.jsx)(X.Z,{}),onClick:nt,style:{fontSize:"16px"}})})]}),Pt=(0,je.jsx)(T.Z,{title:"修改对话标题",open:nn,onOk:function(){un&&an.trim()&&(ot(un,an.trim()),tn(!1))},onCancel:function(){tn(!1),sn(""),dn("")},children:(0,je.jsx)(N.Z,{value:an,onChange:function(e){return sn(e.target.value)},placeholder:"请输入新的对话标题"})}),Bt=(0,je.jsx)(K.Z,{title:"历史对话",placement:"right",width:400,onClose:function(){return Ge(!1)},open:Ue,children:(0,je.jsxs)("div",{className:H.menu,children:[_t,(0,je.jsx)(S.Z,{items:_e,activeKey:Fe,onActiveChange:at,menu:function(e){return{items:[{label:"重命名",key:"edit",icon:(0,je.jsx)(ne.Z,{})},{label:"置顶",key:"pin",icon:(0,je.jsx)(te.Z,{})},{label:"删除",key:"delete",icon:(0,je.jsx)($.Z,{}),danger:!0}],onClick:function(n){switch(console.log("menuInfo","Click ".concat(e.key," - ").concat(n.key)),n.key){case"edit":dn(e.key),sn(e.label),tn(!0);break;case"pin":tt(e.key);break;case"delete":if($e)return void I.ZP.error("系统正在处理其他对话。请稍😊");rt(e.key)}}}},groupable:!0})]})}),zt=A.memo((function(e){var n=e.isOpen,t=e.onClose,r=e.availableKnowledgeBases,o=e.selectedKnowledgeBases,a=e.setSelectedKnowledgeBases,s=(0,A.useState)(o),c=v()(s,2),l=c[0],u=c[1];(0,A.useEffect)((function(){n&&u(o)}),[n,o]);var d=(0,A.useCallback)((function(e,n){n&&(n.stopPropagation(),n.preventDefault()),u((function(n){return n.includes(e)?n.filter((function(n){return n!==e})):[].concat(i()(n),[e])}))}),[]);return(0,je.jsx)(T.Z,{title:"知识库设置",open:n,onCancel:t,width:1e3,bodyStyle:{padding:"12px 16px"},footer:[(0,je.jsx)(z.ZP,{onClick:t,children:"关闭"},"close"),(0,je.jsx)(z.ZP,{type:"primary",onClick:function(){a(l),t()},children:"保存"},"save")],children:(0,je.jsxs)("div",{style:{display:"flex",height:"500px"},children:[(0,je.jsxs)("div",{style:{width:"50%",paddingRight:"12px",overflowY:"auto"},children:[(0,je.jsx)("h3",{style:{marginBottom:"16px",fontSize:"16px",fontWeight:500},children:"可选知识库"}),r.length>0?(0,je.jsx)(W.Z,{grid:{gutter:16,column:2},dataSource:r,renderItem:function(e){var n=l.includes(e.name);return(0,je.jsx)(W.Z.Item,{onClick:function(n){return d(e.name,n)},style:{border:n?"1px solid #1890ff":"1px solid #e0e0e0",padding:"12px",backgroundColor:n?"#e6f7ff":"#fff",color:n?"#1890ff":"inherit",borderRadius:"4px",transition:"all 0.3s ease",boxShadow:n?"0 2px 8px rgba(24, 144, 255, 0.15)":"none",cursor:"pointer",marginBottom:"8px"},children:(0,je.jsx)(W.Z.Item.Meta,{title:(0,je.jsx)("span",{style:{color:n?"#1890ff":"inherit",fontWeight:n?500:400,fontSize:"14px"},children:e.name}),description:(0,je.jsx)("div",{style:{fontSize:"12px",color:"#999",marginTop:"4px"},children:e.description||"暂无描述"})})})}}):(0,je.jsx)("div",{style:{textAlign:"center",padding:"40px 0",color:"#999"},children:"暂无可用知识库"})]}),(0,je.jsxs)("div",{style:{width:"50%",paddingLeft:"12px",borderLeft:"1px solid #f0f0f0",overflowY:"auto"},children:[(0,je.jsx)("h3",{style:{marginBottom:"16px",fontSize:"16px",fontWeight:500},children:"已选知识库"}),l.length>0?(0,je.jsx)(W.Z,{grid:{gutter:16,column:2},dataSource:l,renderItem:function(e){var n=r.find((function(n){return n.name===e}));return(0,je.jsx)(W.Z.Item,{onClick:function(n){return d(e,n)},style:{border:"1px solid #1890ff",padding:"12px",backgroundColor:"#e6f7ff",color:"#1890ff",borderRadius:"4px",transition:"all 0.3s ease",boxShadow:"0 2px 8px rgba(24, 144, 255, 0.15)",cursor:"pointer",marginBottom:"8px"},children:(0,je.jsx)(W.Z.Item.Meta,{title:(0,je.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,je.jsx)("span",{style:{color:"#1890ff",fontWeight:500,fontSize:"14px"},children:e}),(0,je.jsx)(z.ZP,{type:"text",size:"small",icon:(0,je.jsx)(le.Z,{}),onClick:function(n){n.stopPropagation(),u((function(n){return n.filter((function(n){return n!==e}))}))},style:{color:"#1890ff"}})]}),description:(0,je.jsx)("div",{style:{fontSize:"12px",color:"#666",marginTop:"4px"},children:(null==n?void 0:n.description)||"暂无描述"})})})}}):(0,je.jsx)("div",{style:{textAlign:"center",padding:"40px 0",color:"#999"},children:"暂未选择知识库"})]})]})})}));(0,A.useEffect)((function(){console.log("currentConversationMessages 更新了:",xn)}),[xn]),(0,A.useEffect)((function(){On()}),[]);var It=(0,je.jsx)(K.Z,{title:"引用来源详情",placement:"right",width:500,onClose:function(){return d(!1)},open:l,children:m&&(0,je.jsxs)("div",{style:{padding:"16px 0"},children:[(0,je.jsxs)("div",{style:{marginBottom:24},children:[(0,je.jsx)("h3",{style:{fontSize:16,fontWeight:"bold",marginBottom:12,color:"#333"},children:"文档标题"}),(0,je.jsx)("div",{style:{padding:12,backgroundColor:"#f5f5f5",borderRadius:6,fontSize:14,color:"#666"},children:m.source_name||"未知来源"})]}),(0,je.jsxs)("div",{style:{marginBottom:24},children:[(0,je.jsx)("h3",{style:{fontSize:16,fontWeight:"bold",marginBottom:12,color:"#333"},children:"引用内容"}),(0,je.jsx)("div",{style:{padding:16,backgroundColor:"#fafafa",border:"1px solid #e8e8e8",borderRadius:6,fontSize:14,lineHeight:1.6,color:"#333",maxHeight:500,overflowY:"auto"},children:m.content||"暂无内容"})]}),m.metadata&&(0,je.jsxs)("div",{style:{marginBottom:24},children:[(0,je.jsx)("h3",{style:{fontSize:16,fontWeight:"bold",marginBottom:12,color:"#333"},children:"其他信息"}),(0,je.jsx)("div",{style:{fontSize:12,color:"#888"},children:Object.entries(m.metadata).map((function(e){var n=v()(e,2),t=n[0],r=n[1];return(0,je.jsxs)("div",{style:{marginBottom:4},children:[(0,je.jsxs)("span",{style:{fontWeight:"bold"},children:[t,":"]})," ",String(r)]},t)}))})]})]})}),Tt=((0,A.useRef)({}),(0,A.useState)("knowledge")),Rt=v()(Tt,2),Dt=Rt[0],Et=Rt[1],Nt=(0,A.useState)(""),Kt=v()(Nt,2),Wt=Kt[0],Ft=Kt[1],Mt=(0,A.useState)(!1),At=v()(Mt,2),Ht=At[0],Ot=At[1],qt=(0,A.useState)({nodes:[],edges:[]}),Yt=v()(qt,2),Lt=Yt[0],Ut=Yt[1],Gt=function(){var e=u()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==xn.length){e.next=3;break}return I.ZP.warning("没有对话内容可供总结"),e.abrupt("return");case 3:try{Ot(!0),setTimeout((function(){var e,n="# ".concat(Fe?null===(e=_e.find((function(e){return e.key===Fe})))||void 0===e?void 0:e.label:"当前对话"," 总结报告\n\n## 对话概览\n- 对话时间：").concat((new Date).toLocaleString(),"\n- 消息数量：").concat(xn.length,"\n- 知识库：").concat(Rn.join(", "),"\n\n## 主要问题和回答\n").concat(xn.filter((function(e){return"user"===e.role})).slice(0,3).map((function(e,n){return"\n### 问题 ".concat(n+1,"：\n").concat(e.content,"\n\n#### 回答摘要：\n").concat(xn.filter((function(n){return"assistant"===n.role&&n.created_at>e.created_at})).slice(0,1).map((function(e){return e.content.length>200?e.content.substring(0,200)+"...":e.content})).join("")||"无回答","\n")})).join("\n"),"\n\n## 关键引用资料\n").concat(xn.filter((function(e){return"assistant"===e.role&&e.references&&e.references.length>0})).flatMap((function(e){return e.references||[]})).slice(0,5).map((function(e,n){var t;return"\n- **引用 ".concat(n+1,"**: ").concat(e.source_name||"未命名来源"," \n  > ").concat(null===(t=e.content)||void 0===t?void 0:t.substring(0,100),"...\n")})).join("\n")||"无引用资料","\n\n## 总结建议\n基于以上对话内容，建议进一步探讨以下方向：\n1. 深入了解相关政策法规\n2. 结合实际业务场景分析\n3. 考虑潜在风险并制定应对策略\n");Ft(n),Ot(!1)}),2e3)}catch(e){console.error("生成总结报告时出错：",e),I.ZP.error("生成总结报告失败"),Ot(!1)}case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Jt=(0,A.useCallback)((function(){var e=[{id:"query",label:"当前对话",type:"query",size:50}].concat(i()(Rn.map((function(e,n){return{id:"kb-".concat(n),label:e,type:"document",size:40}})))),n=Rn.flatMap((function(e,n){return Array(Math.floor(3*Math.random())+2).fill(0).map((function(e,t){return{id:"doc-".concat(n,"-").concat(t),label:"文档 ".concat(t+1),type:"organization",size:30}}))})),t=n.flatMap((function(e,n){return Array(Math.floor(4*Math.random())+1).fill(0).map((function(e,t){return{id:"chunk-".concat(n,"-").concat(t),label:"片段 ".concat(t+1),type:"person",size:25}}))})),r=[];Rn.forEach((function(e,n){r.push({source:"query",target:"kb-".concat(n)})})),n.forEach((function(e,n){var t=Math.floor(n/3);t<Rn.length&&r.push({source:"kb-".concat(t),target:e.id})})),t.forEach((function(e,t){var o=Math.floor(t/4);o<n.length&&r.push({source:n[o].id,target:e.id})})),Ut({nodes:[].concat(i()(e),i()(n),i()(t)),edges:r})}),[Rn]);return(0,A.useEffect)((function(){Jt()}),[Jt]),(0,je.jsx)(Ee.Provider,{value:{expandedRefs:t,setExpandedRefs:r},children:(0,je.jsxs)("div",{className:H.layout,style:{height:Y-56},children:[(0,je.jsxs)("div",{className:H.chat,children:[(0,je.jsx)(C.Z.List,{roles:{assistant:{placement:"start",typing:{step:5,interval:20},style:{maxWidth:600}}},items:ft,className:H.messages}),(0,je.jsx)(j.Z,{items:Be,onItemClick:ut}),(0,je.jsx)(w.Z,{ref:St,header:Ct,prefix:(0,je.jsx)(z.ZP,{type:"text",icon:(0,je.jsx)(ue.Z,{}),onClick:function(){mt(!gt)}}),onPasteFile:function(e,n){var t,r=o()(n);try{for(r.s();!(t=r.n()).done;){var a,i=t.value;null===(a=Zt.current)||void 0===a||a.upload(i)}}catch(e){r.e(e)}finally{r.f()}mt(!0)},onSubmit:lt,value:Q,onChange:ee,loading:Yn.isRequesting(),className:H.sender})]}),(0,je.jsx)("div",{children:(0,je.jsx)(Se,{width:350,style:{background:"#fff",padding:"12px",borderLeft:"1px solid #d9d9d9",height:"100%",overflow:"auto"},children:(0,je.jsx)(F.Z,{activeKey:Dt,onChange:Et,style:{height:"100%"},items:[{key:"knowledge",label:(0,je.jsxs)("span",{children:[(0,je.jsx)(L.Z,{})," 知识资源"]}),children:(0,je.jsxs)("div",{style:{padding:"8px 0"},children:[(0,je.jsxs)("div",{style:{marginBottom:"16px",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,je.jsx)("div",{style:{fontWeight:500},children:"已选知识库"}),(0,je.jsx)(z.ZP,{type:"primary",size:"small",icon:(0,je.jsx)(V.Z,{}),onClick:function(){On(),Wn(!0)},children:"设置"})]}),Rn.length>0?(0,je.jsx)(W.Z,{dataSource:Rn,renderItem:function(e){var n;return(0,je.jsx)(W.Z.Item,{style:{marginBottom:"8px",padding:"12px",borderRadius:"6px",border:"1px solid #e0e0e0",backgroundColor:"#f5f5f5"},children:(0,je.jsx)(W.Z.Item.Meta,{title:e,description:(0,je.jsx)(B.Z,{size:"small",children:(0,je.jsx)(M.Z,{color:"blue",children:(null===(n=Bn.find((function(n){return n.name===e})))||void 0===n?void 0:n.type)||"文档库"})})})})},locale:{emptyText:"暂未选择知识库"}}):(0,je.jsxs)("div",{style:{textAlign:"center",padding:"40px 0",color:"#999",backgroundColor:"#f9f9f9",borderRadius:"6px"},children:[(0,je.jsx)(L.Z,{style:{fontSize:"32px",marginBottom:"16px"}}),(0,je.jsx)("div",{children:"暂未选择知识库"}),(0,je.jsx)("div",{style:{marginTop:"12px"},children:(0,je.jsx)(z.ZP,{type:"primary",size:"small",onClick:function(){On(),Wn(!0)},children:"选择知识库"})})]}),bt&&bt.length>0&&(0,je.jsxs)("div",{style:{marginTop:"24px"},children:[(0,je.jsx)("div",{style:{fontWeight:500,marginBottom:"12px"},children:"上传文件"}),(0,je.jsx)(W.Z,{dataSource:bt,renderItem:function(e){return(0,je.jsx)(W.Z.Item,{style:{marginBottom:"8px",padding:"8px 12px",borderRadius:"6px",border:"1px solid #e0e0e0"},children:(0,je.jsx)(W.Z.Item.Meta,{avatar:(0,je.jsx)(ce.Z,{style:{fontSize:"20px",color:"#1890ff"}}),title:e.name,description:"".concat((e.size/1024).toFixed(2)," KB")})})}})]})]})},{key:"graph",label:(0,je.jsxs)("span",{children:[(0,je.jsx)(de.Z,{})," 关联关系"]}),children:(0,je.jsxs)("div",{style:{padding:"8px 0",height:Y-156},children:[(0,je.jsxs)("div",{style:{marginBottom:"16px",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,je.jsx)("div",{style:{fontWeight:500},children:"知识库关联关系图"}),(0,je.jsx)(z.ZP,{size:"small",icon:(0,je.jsx)(se.Z,{}),onClick:Jt,children:"刷新"})]}),Rn.length>0?(0,je.jsx)("div",{id:"relation-graph-container",style:{height:Y-200,backgroundColor:"#f9f9f9",borderRadius:"6px",overflow:"hidden",border:"1px solid #e0e0e0"},children:(0,je.jsxs)("div",{style:{height:"100%",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",color:"#666"},children:[(0,je.jsx)(de.Z,{style:{fontSize:"32px",marginBottom:"16px"}}),(0,je.jsx)("div",{children:"知识库关联关系图"}),(0,je.jsxs)("div",{style:{fontSize:"12px",color:"#999",marginTop:"8px"},children:["查询与",Lt.nodes.length,"个节点的关联"]})]})}):(0,je.jsxs)("div",{style:{textAlign:"center",padding:"40px 0",color:"#999",backgroundColor:"#f9f9f9",borderRadius:"6px",height:Y-200},children:[(0,je.jsx)(de.Z,{style:{fontSize:"32px",marginBottom:"16px"}}),(0,je.jsx)("div",{children:"暂无关联数据"}),(0,je.jsx)("div",{style:{marginTop:"12px"},children:"请先选择知识库"})]})]})},{key:"summary",label:(0,je.jsxs)("span",{children:[(0,je.jsx)(pe.Z,{})," 对话总结"]}),children:(0,je.jsxs)("div",{style:{padding:"8px 0"},children:[(0,je.jsxs)("div",{style:{marginBottom:"16px",display:"flex",justifyContent:"flex-start",gap:"8px"},children:[(0,je.jsx)(z.ZP,{type:"primary",icon:(0,je.jsx)(pe.Z,{}),onClick:Gt,loading:Ht,disabled:0===xn.length,children:"生成报告"}),(0,je.jsx)(z.ZP,{icon:(0,je.jsx)(fe.Z,{}),onClick:Gt,loading:Ht,disabled:0===xn.length,children:"重新生成"}),(0,je.jsx)(z.ZP,{icon:(0,je.jsx)(ie.Z,{}),onClick:function(){Wt&&navigator.clipboard.writeText(Wt).then((function(){I.ZP.success("复制成功")})).catch((function(){I.ZP.error("复制失败")}))},disabled:!Wt,children:"复制内容"}),(0,je.jsx)(z.ZP,{icon:(0,je.jsx)(xe.Z,{}),onClick:function(){var e;if(Wt){var n=new Blob([Wt],{type:"text/markdown"}),t=URL.createObjectURL(n),r=document.createElement("a");r.href=t,r.download="".concat(Fe?null===(e=_e.find((function(e){return e.key===Fe})))||void 0===e?void 0:e.label:"对话","_总结报告_").concat((new Date).toISOString().split("T")[0],".md"),document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(t)}},disabled:!Wt,children:"下载"})]}),(0,je.jsx)("div",{style:{border:"1px solid #e0e0e0",borderRadius:"6px",padding:"16px",minHeight:"300px",backgroundColor:"#fff",overflowY:"auto",maxHeight:Y-300},children:Ht&&!Wt?(0,je.jsx)("div",{style:{textAlign:"center",padding:"100px 0"},children:(0,je.jsx)(R.Z,{tip:"正在生成对话总结报告..."})}):Wt?(0,je.jsxs)("div",{style:{position:"relative"},children:[(0,je.jsx)("div",{className:"markdown-content",children:(0,je.jsx)(ke.UG,{children:Wt})}),Ht&&(0,je.jsxs)("div",{style:{position:"absolute",bottom:"10px",right:"10px",background:"rgba(24, 144, 255, 0.1)",padding:"4px 12px",borderRadius:"12px",display:"flex",alignItems:"center",gap:"8px"},children:[(0,je.jsx)(R.Z,{size:"small"}),(0,je.jsx)("span",{children:"正在生成中..."})]})]}):(0,je.jsxs)("div",{style:{textAlign:"center",padding:"100px 0",color:"#888"},children:[(0,je.jsx)(pe.Z,{style:{fontSize:"48px",color:"#bbb"}}),(0,je.jsx)(P.Z.Paragraph,{style:{marginTop:"16px"},children:'点击"生成报告"按钮，基于当前对话内容生成总结'})]})})]})}]})})}),Pt,Bt,It,(0,je.jsx)(zt,{isOpen:Kn,onClose:function(){return Wn(!1)},availableKnowledgeBases:Bn,selectedKnowledgeBases:Rn,setSelectedKnowledgeBases:Dn}),(0,je.jsx)(be.Z,{visible:yn,messageId:wn,conversationId:Fe,appInfo:Te,onClose:function(){return bn(!1)}})]})})}}}]);