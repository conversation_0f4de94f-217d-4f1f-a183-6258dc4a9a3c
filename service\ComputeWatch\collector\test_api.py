import requests
import json
from typing import Dict, Any
import os
from dotenv import load_dotenv
import time
from colorama import init, Fore, Style
from datetime import datetime

# 初始化colorama
init()

# 加载环境变量
load_dotenv()

class APITester:
    def __init__(self):
        self.base_url = "http://127.0.0.1:8000"
        # 使用cron表达式的测试服务器
        self.test_server_cron = {
            "name": "test-server-cron",
            "hostname": "************",
            "port": 10086,
            "api_key": "363326947",
            "collect_interval": 60,  # 默认间隔，当使用cron时不生效
            "cron_expression": "*/5 * * * *",  # 每5分钟执行一次
            "description": "测试服务器(Cron)",
            "enabled": True
        }
        # 使用固定间隔的测试服务器
        self.test_server_interval = {
            "name": "test-server-interval",
            "hostname": "************",
            "port": 10086,
            "api_key": "363326947",
            "collect_interval": 60,
            "cron_expression": None,  # 不使用cron表达式
            "description": "测试服务器(固定间隔)",
            "enabled": True
        }
        # 默认使用cron版本
        self.test_server = self.test_server_cron

    def print_json(self, data: Dict):
        """格式化打印JSON数据"""
        print(json.dumps(data, indent=2, ensure_ascii=False))

    def print_colored(self, text: str, color: str = Fore.WHITE, style: str = Style.NORMAL):
        """打印彩色文本"""
        print(f"{style}{color}{text}{Style.RESET_ALL}")

    def validate_response(self, response: Dict) -> bool:
        """验证响应格式"""
        assert "success" in response, "响应缺少success字段"
        assert "message" in response, "响应缺少message字段"
        assert "data" in response, "响应缺少data字段"
        assert "error" in response, "响应缺少error字段"
        return response["success"]

    def test_add_server(self):
        """测试添加服务器"""
        self.print_colored("\n" + "="*50, Fore.CYAN)
        self.print_colored("测试添加服务器", Fore.CYAN)
        self.print_colored("="*50, Fore.CYAN)
        
        url = f"{self.base_url}/servers"
        try:
            response = requests.post(url, json=self.test_server)
            result = response.json()
            self.print_json(result)
            
            # 验证响应格式
            assert self.validate_response(result), "添加服务器失败"
            
            # 验证返回数据
            data = result["data"]
            assert "server_id" in data, "响应缺少server_id"
            assert "details" in data, "响应缺少details"
            
            details = data["details"]
            assert details["hostname"] == self.test_server["hostname"], "主机名不匹配"
            assert details["port"] == self.test_server["port"], "端口不匹配"
            
            return result
        except Exception as e:
            self.print_colored(f"添加服务器失败: {str(e)}", Fore.RED)
            return None

    def test_update_server(self, server_id: str):
        """测试更新服务器"""
        self.print_colored("\n" + "="*50, Fore.CYAN)
        self.print_colored("测试更新服务器", Fore.CYAN)
        self.print_colored("="*50, Fore.CYAN)
        
        url = f"{self.base_url}/servers/{server_id}"
        update_data = self.test_server.copy()
        update_data["name"] = server_id
        update_data["collect_interval"] = 120
        update_data["description"] = "更新后的测试服务器"
        
        try:
            response = requests.put(url, json=update_data)
            result = response.json()
            self.print_json(result)
            
            assert self.validate_response(result), "更新服务器失败"
            return result
        except Exception as e:
            self.print_colored(f"更新服务器失败: {str(e)}", Fore.RED)
            return None

    def test_get_server_metrics(self, server_id: str):
        """测试获取服务器监控数据"""
        self.print_colored("\n" + "="*50, Fore.CYAN)
        self.print_colored("测试获取服务器监控数据", Fore.CYAN)
        self.print_colored("="*50, Fore.CYAN)
        
        url = f"{self.base_url}/servers/{server_id}/metrics"
        params = {
            "start_time": (datetime.now().isoformat()),
            "limit": 10
        }
        
        try:
            response = requests.get(url, params=params)
            result = response.json()
            self.print_json(result)
            
            assert self.validate_response(result), "获取监控数据失败"
            assert "metrics" in result["data"], "响应缺少metrics字段"
            return result
        except Exception as e:
            self.print_colored(f"获取监控数据失败: {str(e)}", Fore.RED)
            return None

    def test_list_servers(self):
        """测试获取服务器列表"""
        self.print_colored("\n" + "="*50, Fore.CYAN)
        self.print_colored("测试获取服务器列表", Fore.CYAN)
        self.print_colored("="*50, Fore.CYAN)
        
        url = f"{self.base_url}/servers"
        try:
            response = requests.get(url)
            result = response.json()
            self.print_json(result)
            
            assert self.validate_response(result), "获取服务器列表失败"
            assert "servers" in result["data"], "响应缺少servers字段"
            return result
        except Exception as e:
            self.print_colored(f"获取服务器列表失败: {str(e)}", Fore.RED)
            return None

    def test_reload_tasks(self):
        """测试重新加载任务"""
        self.print_colored("\n" + "="*50, Fore.CYAN)
        self.print_colored("测试重新加载任务", Fore.CYAN)
        self.print_colored("="*50, Fore.CYAN)
        
        url = f"{self.base_url}/reload"
        try:
            response = requests.post(url)
            result = response.json()
            self.print_json(result)
            
            assert self.validate_response(result), "重新加载任务失败"
            assert "stats" in result["data"], "响应缺少stats字段"
            stats = result["data"]["stats"]
            assert all(k in stats for k in ["total", "enabled", "disabled"]), "统计信息不完整"
            return result
        except Exception as e:
            self.print_colored(f"重新加载任务失败: {str(e)}", Fore.RED)
            return None

    def test_delete_server(self, server_id: str):
        """测试删除服务器"""
        self.print_colored("\n" + "="*50, Fore.CYAN)
        self.print_colored("测试删除服务器", Fore.CYAN)
        self.print_colored("="*50, Fore.CYAN)
        
        url = f"{self.base_url}/servers/{server_id}"
        try:
            response = requests.delete(url)
            result = response.json()
            self.print_json(result)
            
            assert self.validate_response(result), "删除服务器失败"
            data = result["data"]
            assert data["server_id"] == server_id, "服务器ID不匹配"
            
            # 验证删除结果
            self.print_colored(f"已删除监控记录: {data['deleted_metrics']}条", Fore.GREEN)
            
            # 验证服务器确实被删除
            list_response = requests.get(f"{self.base_url}/servers")
            servers = list_response.json()["data"]["servers"]
            assert not any(s["server_id"] == server_id for s in servers), "服务器未被完全删除"
            
            return result
        except Exception as e:
            self.print_colored(f"删除服务器失败: {str(e)}", Fore.RED)
            return None

    def generate_report(self, results: list):
        """生成测试报告"""
        print("\n")
        self.print_colored("="*50, Fore.YELLOW)
        self.print_colored("API测试报告", Fore.YELLOW)
        self.print_colored("="*50, Fore.YELLOW)
        print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        success_count = len([r for r in results if r and r.get("success", False)])
        total_count = len(results)
        
        self.print_colored(f"测试用例总数: {total_count}", Fore.WHITE)
        self.print_colored(f"成功: {success_count}", Fore.GREEN)
        self.print_colored(f"失败: {total_count - success_count}", Fore.RED)

    def test_add_server_both(self):
        """测试添加两种调度方式的服务器"""
        # self.print_colored("\n" + "="*50, Fore.CYAN)
        # self.print_colored("测试添加Cron调度服务器", Fore.CYAN)
        # self.print_colored("="*50, Fore.CYAN)
        
        # # 先测试添加cron版本
        # self.test_server = self.test_server_cron
        # cron_result = self.test_add_server()
        
        self.print_colored("\n" + "="*50, Fore.CYAN)
        self.print_colored("测试添加固定间隔服务器", Fore.CYAN)
        self.print_colored("="*50, Fore.CYAN)
        
        # 再测试添加interval版本
        self.test_server = self.test_server_interval
        interval_result = self.test_add_server()
        
        return None, interval_result

def main():
    tester = APITester()
    results = []
    
    # # 测试获取服务器列表
    # results.append(tester.test_list_servers())
    
    # # 测试添加两种服务器
    # cron_result, interval_result = tester.test_add_server_both()
    # results.extend([cron_result, interval_result])
    
    # if cron_result and cron_result["success"]:
    #     server_id = cron_result["data"]["server_id"]
        
    #     # 测试更新服务器
    #     results.append(tester.test_update_server(server_id))
        
    #     # 等待一些监控数据收集
    #     time.sleep(5)
        
    #     # 测试获取监控数据
    #     results.append(tester.test_get_server_metrics(server_id))
    
    # if interval_result and interval_result["success"]:
    #     server_id = interval_result["data"]["server_id"]
        
    #     # 测试获取监控数据
    #     results.append(tester.test_get_server_metrics(server_id))
    
    # 测试重新加载任务
    results.append(tester.test_reload_tasks())
    
    # # 清理测试服务器
    # if cron_result and cron_result["success"]:
    #     results.append(tester.test_delete_server(cron_result["data"]["server_id"]))
    # if interval_result and interval_result["success"]:
    #     results.append(tester.test_delete_server(interval_result["data"]["server_id"]))
    
    # 生成测试报告
    tester.generate_report(results)

if __name__ == "__main__":
    main() 