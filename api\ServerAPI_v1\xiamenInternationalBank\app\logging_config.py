import logging
import os

def setup_logging():
    logging.basicConfig(
        level=os.getenv("LOG_LEVEL", logging.INFO),
        format='%(levelname)s - %(asctime)s - %(name)s:%(lineno)d - %(funcName)s() - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

def get_logger(name):
    logger = logging.getLogger(name)
    return logger

def get_logger_debug(name):
    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)
    return logger

def get_logger_info(name):
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)
    return logger

def get_logger_warning(name):
    logger = logging.getLogger(name)
    logger.setLevel(logging.WARNING)
