"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9450],{26766:function(e,t,n){n.r(t);var i=n(19632),r=n.n(i),l=n(15009),s=n.n(l),a=n(99289),c=n.n(a),o=n(5574),d=n.n(o),u=n(67294),h=n(26058),f=n(71471),x=n(55102),m=n(2453),p=n(71230),j=n(15746),y=n(4393),Z=n(42119),g=n(83062),v=n(42075),w=n(11550),S=n(83622),b=n(2487),z=n(66309),k=n(38703),P=n(32983),C=n(16596),T=n(88484),L=n(15360),I=n(56717),q=n(27496),A=n(85893),B=h.Z.Content,R=f.Z.Title,_=f.Z.Text,D=x.Z.TextArea;t.default=function(){var e=(0,u.useState)([]),t=d()(e,2),n=t[0],i=t[1],l=(0,u.useState)([]),a=d()(l,2),o=a[0],f=a[1],x=(0,u.useState)([]),N=d()(x,2),Y=N[0],E=N[1],H=(0,u.useState)(""),K=d()(H,2),O=K[0],U=K[1],W=(0,u.useState)([]),F=d()(W,2),G=F[0],J=F[1],M=(0,u.useState)(0),Q=d()(M,2),V=Q[0],X=Q[1],$=function(){var e=c()(s()().mark((function e(t){var n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,f(n=[{name:"客户基本信息",type:"客户信息",value:"35岁，已婚，有子女",importance:"关键",detail:"客户为35岁已婚人士，育有一子，家庭责任较重。"},{name:"保障需求",type:"保障需求",value:"重疾+医疗+意外",importance:"重要",detail:"客户主要关注重疾、医疗和意外保障，希望获得全面的健康保障。"},{name:"预算范围",type:"预算范围",value:"2-3万/年",importance:"重要",detail:"客户可接受的年保费预算在2-3万元之间。"},{name:"风险偏好",type:"风险偏好",value:"稳健型",importance:"关键",detail:"客户风险承受能力中等，偏好稳健的保险产品。"}]),e.next=5,ee(n);case 5:X(2),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),m.ZP.error("文件解析失败");case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(t){return e.apply(this,arguments)}}(),ee=function(){var e=c()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:E([{name:"全面健康保障计划",matchLevel:95,features:{planType:"重疾险+医疗险+意外险",coverage:"全面保障",premium:"2.5万/年",benefits:["重疾保障","医疗报销","意外保障","住院津贴"]},details:{totalPremium:"2.5万/年",coveragePeriod:"终身",companyName:"平安保险",successRate:100},matchingPoints:[{feature:"保障范围",match:"full",detail:"完全匹配客户需求",required:!0},{feature:"保费预算",match:"full",detail:"在客户预算范围内",required:!0},{feature:"产品类型",match:"full",detail:"符合客户风险偏好",required:!0},{feature:"公司实力",match:"full",detail:"大型保险公司，服务有保障",required:!0}]},{name:"基础健康保障计划",matchLevel:85,features:{planType:"重疾险+医疗险",coverage:"基础保障",premium:"1.8万/年",benefits:["重疾保障","医疗报销"]},details:{totalPremium:"1.8万/年",coveragePeriod:"终身",companyName:"中国人寿",successRate:100},matchingPoints:[{feature:"保障范围",match:"partial",detail:"缺少意外保障"},{feature:"保费预算",match:"full",detail:"低于客户预算"},{feature:"产品类型",match:"full",detail:"符合客户风险偏好"}]}]);case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),te=function(){if(O.trim()){var e={role:"user",content:O};J([].concat(r()(G),[e])),U(""),setTimeout((function(){var e={role:"assistant",content:'根据分析，第一个方案"全面健康保障计划"的匹配度更高(95%)，主要原因是：\n1. 保障范围完全匹配客户需求\n2. 保费在客户预算范围内\n3. 产品类型符合客户风险偏好\n4. 提供全面的健康保障'};J((function(t){return[].concat(r()(t),[e])}))}),1e3)}};return(0,A.jsx)(h.Z,{style:{minHeight:"100vh",background:"#fff"},children:(0,A.jsx)(B,{style:{padding:12},children:(0,A.jsxs)(p.Z,{gutter:12,wrap:!1,children:[(0,A.jsx)(j.Z,{flex:"auto",children:(0,A.jsxs)("div",{style:{height:"calc(100vh - 24px)",overflowY:"auto"},children:[(0,A.jsx)(y.Z,{style:{marginBottom:12},bodyStyle:{padding:"8px 16px"},children:(0,A.jsx)(Z.Z,{current:V,items:[{title:(0,A.jsx)(g.Z,{title:"上传客户资料",children:"上传文件"})},{title:(0,A.jsx)(g.Z,{title:"分析客户需求",children:"需求分析"})},{title:(0,A.jsx)(g.Z,{title:"推荐保险方案",children:"方案推荐"})}],size:"small"})}),(0,A.jsx)(y.Z,{bodyStyle:{padding:12},children:(0,A.jsxs)(v.Z,{direction:"vertical",style:{width:"100%"},size:16,children:[(0,A.jsxs)("div",{children:[(0,A.jsx)(p.Z,{align:"middle",justify:"space-between",style:{background:"#f6f8fa",padding:"12px 16px",borderRadius:"4px"},children:(0,A.jsxs)(v.Z,{size:8,children:[(0,A.jsx)(C.Z,{style:{fontSize:16,color:"#1890ff"}}),(0,A.jsx)(R,{level:5,style:{margin:0,fontSize:14,color:"#1890ff"},children:"客户资料上传"}),(0,A.jsx)(w.Z,{fileList:n,onChange:function(e){i(e.fileList.slice(-1)),"done"===e.file.status?(m.ZP.success("".concat(e.file.name," 上传成功")),$(e.file),X(1)):"error"===e.file.status&&m.ZP.error("".concat(e.file.name," 上传失败"))},maxCount:1,accept:".pdf,.doc,.docx",showUploadList:!1,children:(0,A.jsx)(S.ZP,{type:"primary",ghost:!0,icon:(0,A.jsx)(T.Z,{}),size:"small",children:"选择文件"})})]})}),n.length>0&&(0,A.jsx)(y.Z,{size:"small",bodyStyle:{padding:"4px 8px",marginTop:8},children:(0,A.jsxs)(v.Z,{size:4,children:[(0,A.jsx)(L.Z,{style:{fontSize:12,color:"#1890ff"}}),(0,A.jsx)(_,{style:{fontSize:12},children:n[0].name})]})})]}),o.length>0&&(0,A.jsxs)("div",{children:[(0,A.jsx)(R,{level:5,style:{fontSize:14,marginBottom:12},children:"客户需求分析"}),(0,A.jsx)(b.Z,{grid:{gutter:16,column:2},dataSource:o,renderItem:function(e){return(0,A.jsx)(b.Z.Item,{children:(0,A.jsx)(y.Z,{size:"small",bodyStyle:{padding:12},style:{background:"#f6f8fa",border:"1px solid #e6f4ff"},children:(0,A.jsx)(v.Z,{direction:"vertical",style:{width:"100%"},size:4,children:(0,A.jsxs)(v.Z,{align:"center",style:{width:"100%",justifyContent:"space-between"},children:[(0,A.jsxs)(v.Z,{children:[(0,A.jsx)(z.Z,{color:"关键"===e.importance?"red":"重要"===e.importance?"blue":"default",children:e.type}),(0,A.jsxs)(_,{children:[e.name,": "]}),(0,A.jsx)(_,{strong:!0,children:e.value})]}),(0,A.jsx)(g.Z,{title:e.detail,children:(0,A.jsx)(I.Z,{style:{color:"#1890ff",cursor:"pointer"}})})]})})})})}})]}),Y.length>0&&(0,A.jsxs)("div",{children:[(0,A.jsx)(R,{level:5,style:{fontSize:14,marginBottom:12},children:"推荐保险方案"}),(0,A.jsx)(b.Z,{dataSource:Y,renderItem:function(e){return(0,A.jsx)(b.Z.Item,{children:(0,A.jsx)(y.Z,{style:{width:"100%"},bodyStyle:{padding:16},children:(0,A.jsxs)(p.Z,{gutter:16,children:[(0,A.jsx)(j.Z,{span:18,children:(0,A.jsxs)(v.Z,{direction:"vertical",size:8,style:{width:"100%"},children:[(0,A.jsxs)(v.Z,{align:"center",children:[(0,A.jsx)(_,{strong:!0,style:{fontSize:14},children:e.name}),(0,A.jsx)(k.Z,{percent:e.matchLevel,size:"small",status:"active",style:{width:80}})]}),(0,A.jsx)(v.Z,{wrap:!0,children:Object.entries(e.features).map((function(e,t){var n=d()(e,2),i=n[0],r=n[1];return(0,A.jsx)(z.Z,{children:"".concat({planType:"方案类型",coverage:"保障范围",premium:"年缴保费",benefits:"保障内容"}[i],": ").concat(Array.isArray(r)?r.join(", "):r)},t)}))}),(0,A.jsx)(b.Z,{size:"small",dataSource:e.matchingPoints,renderItem:function(e){return(0,A.jsx)(b.Z.Item,{children:(0,A.jsxs)(v.Z,{children:[(0,A.jsxs)(z.Z,{color:"full"===e.match?"success":"partial"===e.match?"warning":"error",children:[e.required?"必须":"可选"," | ",e.feature]}),(0,A.jsx)(_,{children:e.detail})]})})}})]})}),(0,A.jsx)(j.Z,{span:6,style:{textAlign:"center"},children:(0,A.jsx)(k.Z,{type:"circle",percent:e.matchLevel,width:80})})]})})})}})]})]})})]})}),(0,A.jsx)(j.Z,{flex:"360px",children:(0,A.jsxs)(y.Z,{title:(0,A.jsx)(_,{style:{fontSize:14},children:"智能对话"}),style:{height:"100%"},bodyStyle:{padding:12,display:"flex",flexDirection:"column",height:"calc(100% - 46px)"},children:[(0,A.jsx)("div",{style:{flex:1,overflowY:"auto",marginBottom:12},children:G.length>0?G.map((function(e,t){return(0,A.jsx)("div",{style:{textAlign:"user"===e.role?"right":"left",marginBottom:8},children:(0,A.jsx)(y.Z,{size:"small",style:{display:"inline-block",maxWidth:"80%",background:"user"===e.role?"#e6f7ff":"#f0f0f0"},children:(0,A.jsx)(_,{children:e.content})})},t)})):(0,A.jsx)(P.Z,{description:"可以询问我关于保险方案的问题"})}),(0,A.jsxs)(v.Z.Compact,{style:{width:"100%"},children:[(0,A.jsx)(D,{value:O,onChange:function(e){return U(e.target.value)},placeholder:"输入您的问题...",autoSize:{minRows:1,maxRows:3},onPressEnter:function(e){e.shiftKey||(e.preventDefault(),te())}}),(0,A.jsx)(S.ZP,{type:"primary",icon:(0,A.jsx)(q.Z,{}),onClick:te})]})]})})]})})})}}}]);