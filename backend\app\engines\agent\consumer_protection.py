
from langgraph.graph import StateGraph, <PERSON><PERSON>
from typing import Dict, List, Any, TypedDict, Optional, AsyncGenerator, Annotated
from langgraph.graph.message import add_messages
import httpx
import asyncio
from app.utils.logging_config import setup_logging, get_logger
import time
import json
import hashlib
import os

setup_logging()
logger = get_logger(__name__)

class AgentState(TypedDict):
    user_input: str
    classification: Optional[str]  # 分类结果：data_query 或 chat
    query_result: Optional[Dict]  # 数据查询结果
    query_data: Optional[str]
    chat_response: Optional[str]  # 大模型聊天结果
    chat_chunk: Optional[str] 
    error: Optional[str]
    llm_params: Optional[Dict[str, Any]]  # 大模型参数，包含api_key、model、url等信息
    messages: Optional[List[Dict[str, str]]]  # 对话消息列表
    

class ConsumerProtectionAgent:
    def __init__(self):
        self.workflow = StateGraph(AgentState)
        self._setup_workflow()

    def _setup_workflow(self):
        # 定义三个节点
        self.workflow.add_node("classify", self.classify_node)
        self.workflow.add_node("data_query", self.data_query_node)
        # self.workflow.add_node("chat", self.chat_node)
        # 构建流程
        self.workflow.set_entry_point("classify")
        # 根据分类结果指向不同节点
        self.workflow.add_conditional_edges(
            "classify",
            self.route_based_on_classification,
            {
                "data_query": "data_query",
                # "chat": "chat",
                "end": END
            }
        )
        
        self.workflow.add_edge("data_query", END)
        # self.workflow.add_edge("chat", END)

    async def classify_node(self, state: AgentState) -> AgentState:
        """分类节点：调用大模型判断用户问题是数据查询还是直接聊天"""
        try:
            # 从initial_state中获取LLM参数
            llm_params = state.get("llm_params", {})
            # 构建大模型输入
            messages = []
            messagesystem = [{"role": "system", "content": "你是一个问题分类助手，任务是根据用户的聊天记录对用户本次的问题进行分析提炼，按照如下的json格式派发到后面不同的处理节点，注意你不需要处理问题，且必须按照如下json格式回答问题：{'classification':(data_query/end),'user_input':(分析对话得到的本次用户提问的内容，若本次用户提问是完整的问题，则只需重复用户问题内容即可，不要自以为是的丢掉用户问题的任何内容！)}1. 如果是消费者保护相关的数据查询或统计问题，返回 classification='data_query'2. 其他情况返回 classification='end'。"}]
            messages.extend(messagesystem)
            messages.extend(state.get("messages", []))

            from app.utils.llmClient import openai_api
            response = await openai_api(
                api_key=llm_params.get("api_key", ""),
                model=llm_params.get("m_name", "gpt-4"),
                messages=messages,
                url=llm_params.get("service_url", "https://api.openai.com/v1"),
                extra=llm_params.get("extra", None),
                system_prompt=llm_params.get("system_prompt", None),
                provider=llm_params.get("provider", "OpenAI")
            )

            # 解析大模型返回的结果
            if "error" in response:
                raise Exception(response["error"])
            # 解析大模型返回的JSON格式内容
            content = response.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
            try:
                # 处理可能包含的<think>标签
                if '<think>' in content:
                    content = content.split('</think>')[-1].strip()
                # 处理可能包含的```json标记
                if content.startswith('```json'):
                    content = content[7:].strip()
                if content.endswith('```'):
                    content = content[:-3].strip()
                
                # 尝试将内容解析为JSON
                # 处理单引号问题，将单引号替换为双引号
                content = content.replace("'", '"')
                # 处理可能的换行符和多余空格
                content = content.replace('\n', '').strip()
                # 尝试解析JSON
                result = json.loads(content)
                
                classification = result.get("classification", "").lower()
                user_input = result.get("user_input", "")
                
                # 根据分类结果返回
                if classification == "data_query":
                    return {**state, "classification": "data_query", "user_input": user_input}
                else:
                    return {**state, "classification": "end", "user_input": user_input}
            except json.JSONDecodeError:
                # 如果解析失败，默认返回chat类型
                return {**state, "classification": "end"}
        except Exception as e:
            logger.error(f"分类节点出错: {str(e)}")
            return {**state, "error": str(e), "classification": "end"}

    async def data_query_node(self, state: AgentState) -> AgentState:
        """数据查询节点：调用HTTP接口查询数据"""
        try:
            # 准备请求参数
            params = {"queryText": state["user_input"]}
            # 获取认证信息
            app_id = os.getenv('BI_APP_ID','wisebi_653a86b5e')
            app_secret = os.getenv('BI_APP_SECRET',"1cf3cfa89466419baa1cf2e03b0eeee6")
            app_url =os.getenv('BI_APP_URL','http://218.77.35.16:9199/openapi/query')
            # 生成时间戳和签名
            timestamp = str(int(time.time() * 1000))
            psw = json.dumps(params, ensure_ascii=False).replace(" ", "") + app_secret + timestamp
            signature = hashlib.sha1(psw.encode()).hexdigest()
            # 设置请求头
            headers = {
                "Content-Type": "application/json; charset=utf-8",
                "Accept": "application/json",
                "timestamp": timestamp,
                "signature": signature,
                "appId": app_id
            }
            # 发送请求
            async with httpx.AsyncClient(timeout=httpx.Timeout(120.0)) as client:
                response = await client.post(
                    url=app_url,
                    json=params,
                    headers=headers
                )
                response.raise_for_status()
                
                # 解析返回的JSON数据
                response_data = response.json()
                if response_data.get("code") != 200:
                    return {**state, "error": response_data.get("msg")}
                
                # 提取data部分作为query_result
                query_result = response_data.get("data", {})
                # 从data中提取textResult作为query_data
                query_data = query_result.get("textResult", "")
                
                return {
                    **state,
                    "query_result": query_result,  # 包含完整的data部分
                    "query_data": query_data      # 单独提取的textResult
                }
        except Exception as e:
            return {**state, "error": str(e)}

    async def chat_node(self, state: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """聊天节点：调用大模型进行对话，流式返回结果"""
        try:
            
            # 构建对话消息
            messages = [{"role": "system", "content": "你是一个智能助手，请根据用户输入进行对话。"}]
            if state.get("user_input") and state.get("query_data"):
                # 如果有用户输入和查询结果，构建新的提示词
                user_input = state["user_input"]
                query_data = json.dumps(state["query_data"], ensure_ascii=False)
                messages.append({
                    "role": "user",
                    "content": f"用户问题：{user_input}\n查询智能BI系统得到的相关数据：{query_data}\n请根据以上信息回答用户问题。"
                })
            else:
                # 否则使用原始消息
                messages.extend(state.get("messages", []))
            # 从state中获取LLM参数
            llm_params = state.get("llm_params", {})
            # 调用大模型API进行对话
            from app.utils.llmClient import stream_openai_api
            # 流式处理大模型响应
            chat_response = ""  # 初始化chat_response变量
            async for chunk in stream_openai_api(
                api_key=llm_params.get("api_key", ""),
                model=llm_params.get("m_name", "gpt-4"),
                messages=messages,
                url=llm_params.get("service_url", "https://api.openai.com/v1"),
                extra=llm_params.get("extra", None),
                system_prompt=llm_params.get("system_prompt", None),
                provider=llm_params.get("provider", "OpenAI")
            ):
                # 解析chunk中的content内容
                if chunk.startswith("data: "):
                    chunk_data = chunk[6:]  # 去掉"data: "前缀
                    try:
                        if "[DONE]" in chunk_data:
                            break
                        chunk_json = json.loads(chunk_data)
                        content = chunk_json.get("choices", [{}])[0].get("delta", {}).get("content", "")
                        if content:
                            chat_response += content 
                            yield f"{json.dumps({'chat_chunk': content}, ensure_ascii=False)}"
                    except json.JSONDecodeError:
                        # 如果解析失败，直接返回原始chunk
                        yield f"{json.dumps({'chat_chunk': chunk}, ensure_ascii=False)}"
                
            # 最终返回完整状态
            # yield f"{json.dumps({'chat_response': chat_response}, ensure_ascii=False)}"
            
        except Exception as e:
            yield f"{json.dumps({'error': str(e)}, ensure_ascii=False)}"

    def route_based_on_classification(self, state: AgentState) -> str:
        """根据分类结果路由到不同节点"""
        return state["classification"]

    async def run(self, messages: List[Dict[str, Any]], llm_params: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """运行agent流程
        Args:
            messages: 用户输入内容
            llm_params: 外部传入的openai_api调用参数，包含：
                - api_key: OpenAI API密钥
                - model: 使用的模型名称
                - url: API地址
                - extra: 参数
                - system_prompt: 系统提示词
                - provider: 服务提供商
        """
        max_token = llm_params.get("max_tokens", 32000)*0.8
        # 处理历史消息，从后往前累加，确保不超过max_token
        total_length = 0
        # 先进行字符长度校验
        valid_messages = []
        for i, msg in enumerate(reversed(messages)):
            if i >= 50:  # 最多处理50条消息
                break
            content = msg.get("content", "")[:100]  # 每个content最多取前100个字符
            content_length = len(content)
            if total_length + content_length > max_token:
                break
            total_length += content_length
            valid_messages.append(msg)  # 将符合长度要求的消息加入列表
        # 从前向后拼接字符串
        combined_content = ""
        for msg in reversed(valid_messages):  # 将消息顺序还原
            role = msg.get("role", "")
            content = msg.get("content", "")
            if role == "user":
                # 如果是用户消息，保留全部内容
                combined_content += f"{role}: {content}\n\n"
            else:
                # 如果是助手消息，截取前100个字符
                truncated_content = content[:100]
                # 如果内容超过100字符才添加省略号
                if len(content) > 100:
                    truncated_content += "..."
                combined_content += f"{role}: {truncated_content}\n\n"
            
        # 将合并后的字符串作为单条消息
        messages = [{
            "role": "user",
            "content": combined_content
        }]

        initial_state = {
            "messages": messages,  # 获取最新用户输入
            "llm_params": llm_params  # 将外部传入的参数添加到初始状态
        }
        
        yield f"event: answer\ndata:  {json.dumps({'status': '执行代理工作流'}, ensure_ascii=False)}"
        
        app = self.workflow.compile()
        workflow_state = {}
        # 使用astream进行流式处理
        async for state in app.astream(initial_state, stream_mode="values"):
            workflow_state = state
            # 根据当前状态返回不同事件
            if state.get("classification"):
                status_msg = '执行步骤：' + state['classification']
                if state['classification'] == 'data_query' and state.get('user_input'):
                    status_msg += f"，解析到用户输入：{state['user_input']}"
                yield f"event: answer\ndata: {json.dumps({'flow': status_msg}, ensure_ascii=False)}"
            if state.get("query_result"):
                yield f"event: query\ndata: {json.dumps({'query': state['query_result']}, ensure_ascii=False)}"
                
            if state.get("error"):
                yield f"event: answer\ndata: {json.dumps({'error': state['error']}, ensure_ascii=False)}"
        
        # 直接调用类实例的chat_node方法，传入workflow_state作为参数
        async for chunk in self.chat_node(state=workflow_state):
            # 将聊天节点的输出以JSON格式流式返回
            yield f"event: answer\ndata: {json.dumps({'stream': chunk}, ensure_ascii=False)}"


        yield f"event: answer\ndata: {json.dumps({'status': '执行代理工作流完成'}, ensure_ascii=False)}"

