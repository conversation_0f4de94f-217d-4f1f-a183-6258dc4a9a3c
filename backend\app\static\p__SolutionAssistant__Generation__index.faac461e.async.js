"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2922],{47046:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},42003:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"}},{tag:"path",attrs:{d:"M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"}}]},name:"eye-invisible",theme:"outlined"}},88484:function(e,t,n){n.d(t,{Z:function(){return c}});var r=n(1413),o=n(67294),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"},a=n(91146),l=function(e,t){return o.createElement(a.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:i}))};var c=o.forwardRef(l)},16439:function(e,t,n){n.r(t),n.d(t,{default:function(){return S}});var r=n(97857),o=n.n(r),i=n(5574),a=n.n(i),l=n(67294),c=n(71471),s=n(2453),d=n(71230),u=n(15746),f=n(4393),h=n(83622),p=n(11550),b=n(63496),m=n(1413),g=n(42003),x=n(91146),y=function(e,t){return l.createElement(x.Z,(0,m.Z)((0,m.Z)({},e),{},{ref:t,icon:g.Z}))};var v=l.forwardRef(y),k=n(55287),$=n(88484),w=n(15360),Z=n(60520),C=(n(74864),n(85893)),j=c.Z.Title,E=c.Z.Text,S=function(){var e=(0,l.useState)([{id:"1",title:"1. 客户需求分析",content:"根据客户的基本信息、财务状况、风险承受能力等，分析客户的保险需求。包括：\n1. 客户基本信息\n2. 财务状况分析\n3. 风险承受能力评估\n4. 保险需求总结"},{id:"2",title:"2. 保险方案设计",content:"基于客户需求，设计合适的保险方案：\n1. 主险选择\n2. 附加险配置\n3. 保额设计\n4. 缴费方式建议\n5. 保障期限规划"},{id:"3",title:"3. 产品对比分析",content:"对推荐的产品进行详细对比分析：\n1. 产品基本信息\n2. 保障范围对比\n3. 保费对比\n4. 特色功能分析\n5. 理赔服务对比"},{id:"4",title:"4. 方案优势说明",content:"详细说明推荐方案的优势：\n1. 保障全面性\n2. 性价比分析\n3. 服务特色\n4. 理赔便利性\n5. 增值服务"},{id:"5",title:"5. 投保建议",content:"提供具体的投保建议：\n1. 投保流程说明\n2. 注意事项提醒\n3. 后续服务承诺\n4. 联系方式"}]),t=a()(e,2),n=t[0],r=(t[1],(0,l.useState)([{id:"1",name:"客户需求问卷.pdf",url:"#"},{id:"2",name:"产品条款说明.docx",url:"#"}])),i=a()(r,2),c=(i[0],i[1],(0,l.useState)(!0)),m=a()(c,2),g=m[0],x=m[1],y=(0,l.useRef)([]),S={name:"file",action:"/api/upload-document",onChange:function(e){"done"===e.file.status?s.ZP.success("".concat(e.file.name," 上传成功")):"error"===e.file.status&&s.ZP.error("".concat(e.file.name," 上传失败"))}};(0,l.useEffect)((function(){var e=y.current.map((function(e,t){return e?new Z.cW({element:e,placeholder:"请输入".concat(n[t].title,"内容"),content:n[t].content,toolbarVisible:g}):null}));return function(){e.forEach((function(e){return null==e?void 0:e.destroy()}))}}),[n,g]);var O=function(){x(!g)};return(0,C.jsxs)("div",{style:{padding:16,height:"calc(100vh - 100px)"},children:[(0,C.jsx)(j,{level:2,style:{marginBottom:24},children:"保险计划书推荐"}),(0,C.jsxs)(d.Z,{gutter:16,style:{height:"calc(100% - 60px)"},children:[(0,C.jsx)(u.Z,{span:16,children:(0,C.jsx)(f.Z,{title:"计划书编辑",style:{height:"100%",overflow:"auto",border:"none",margin:"4px 0"},children:n.map((function(e,t){return(0,C.jsx)(f.Z,{title:(0,C.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.title,(0,C.jsx)(h.ZP,{icon:g?(0,C.jsx)(v,{}):(0,C.jsx)(k.Z,{}),onClick:O})]}),style:{width:"100%",marginBottom:4,border:"none",boxShadow:"none"},children:(0,C.jsx)("div",{ref:function(e){return y.current[t]=e},style:{height:"300px",margin:"0 auto"}})},e.id)}))})}),(0,C.jsxs)(u.Z,{span:8,children:[(0,C.jsxs)(f.Z,{title:"资料上传",style:{marginBottom:4},children:[(0,C.jsx)(p.Z,o()(o()({},S),{},{children:(0,C.jsx)(h.ZP,{icon:(0,C.jsx)($.Z,{}),children:"上传客户资料"})})),(0,C.jsx)(E,{type:"secondary",style:{display:"block",marginTop:8},children:"支持上传客户需求问卷、产品条款等资料"})]}),(0,C.jsx)(f.Z,{title:"文档目录",style:{height:"calc(100% - 120px)",overflow:"auto",margin:"4px 0"},children:(0,C.jsx)(b.Z,{treeData:[{title:"1. 客户需求分析",key:"1",children:[{title:"客户需求问卷.pdf",key:"1-1"},{title:"客户访谈记录.docx",key:"1-2"}]},{title:"2. 保险方案设计",key:"2",children:[{title:"产品条款说明.docx",key:"2-1"},{title:"费率表.pdf",key:"2-2"}]},{title:"3. 产品对比分析",key:"3",children:[{title:"竞品分析报告.pdf",key:"3-1"},{title:"产品对比表.xlsx",key:"3-2"}]},{title:"4. 方案优势说明",key:"4",children:[{title:"理赔案例.pdf",key:"4-1"},{title:"客户评价.docx",key:"4-2"}]},{title:"5. 投保建议",key:"5",children:[{title:"投保流程说明.pdf",key:"5-1"},{title:"服务承诺书.docx",key:"5-2"}]}],defaultExpandAll:!0,icon:(0,C.jsx)(w.Z,{})})})]})]})]})}},63185:function(e,t,n){n.d(t,{C2:function(){return c}});var r=n(11568),o=n(14747),i=n(83262),a=n(83559);const l=e=>{const{checkboxCls:t}=e,n=`${t}-wrapper`;return[{[`${t}-group`]:Object.assign(Object.assign({},(0,o.Wf)(e)),{display:"inline-flex",flexWrap:"wrap",columnGap:e.marginXS,[`> ${e.antCls}-row`]:{flex:1}}),[n]:Object.assign(Object.assign({},(0,o.Wf)(e)),{display:"inline-flex",alignItems:"baseline",cursor:"pointer","&:after":{display:"inline-block",width:0,overflow:"hidden",content:"'\\a0'"},[`& + ${n}`]:{marginInlineStart:0},[`&${n}-in-form-item`]:{'input[type="checkbox"]':{width:14,height:14}}}),[t]:Object.assign(Object.assign({},(0,o.Wf)(e)),{position:"relative",whiteSpace:"nowrap",lineHeight:1,cursor:"pointer",borderRadius:e.borderRadiusSM,alignSelf:"center",[`${t}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0,margin:0,[`&:focus-visible + ${t}-inner`]:Object.assign({},(0,o.oN)(e))},[`${t}-inner`]:{boxSizing:"border-box",display:"block",width:e.checkboxSize,height:e.checkboxSize,direction:"ltr",backgroundColor:e.colorBgContainer,border:`${(0,r.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,borderCollapse:"separate",transition:`all ${e.motionDurationSlow}`,"&:after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"25%",display:"table",width:e.calc(e.checkboxSize).div(14).mul(5).equal(),height:e.calc(e.checkboxSize).div(14).mul(8).equal(),border:`${(0,r.bf)(e.lineWidthBold)} solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`}},"& + span":{paddingInlineStart:e.paddingXS,paddingInlineEnd:e.paddingXS}})},{[`\n        ${n}:not(${n}-disabled),\n        ${t}:not(${t}-disabled)\n      `]:{[`&:hover ${t}-inner`]:{borderColor:e.colorPrimary}},[`${n}:not(${n}-disabled)`]:{[`&:hover ${t}-checked:not(${t}-disabled) ${t}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"},[`&:hover ${t}-checked:not(${t}-disabled):after`]:{borderColor:e.colorPrimaryHover}}},{[`${t}-checked`]:{[`${t}-inner`]:{backgroundColor:e.colorPrimary,borderColor:e.colorPrimary,"&:after":{opacity:1,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`}}},[`\n        ${n}-checked:not(${n}-disabled),\n        ${t}-checked:not(${t}-disabled)\n      `]:{[`&:hover ${t}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"}}},{[t]:{"&-indeterminate":{[`${t}-inner`]:{backgroundColor:`${e.colorBgContainer} !important`,borderColor:`${e.colorBorder} !important`,"&:after":{top:"50%",insetInlineStart:"50%",width:e.calc(e.fontSizeLG).div(2).equal(),height:e.calc(e.fontSizeLG).div(2).equal(),backgroundColor:e.colorPrimary,border:0,transform:"translate(-50%, -50%) scale(1)",opacity:1,content:'""'}},[`&:hover ${t}-inner`]:{backgroundColor:`${e.colorBgContainer} !important`,borderColor:`${e.colorPrimary} !important`}}}},{[`${n}-disabled`]:{cursor:"not-allowed"},[`${t}-disabled`]:{[`&, ${t}-input`]:{cursor:"not-allowed",pointerEvents:"none"},[`${t}-inner`]:{background:e.colorBgContainerDisabled,borderColor:e.colorBorder,"&:after":{borderColor:e.colorTextDisabled}},"&:after":{display:"none"},"& + span":{color:e.colorTextDisabled},[`&${t}-indeterminate ${t}-inner::after`]:{background:e.colorTextDisabled}}}]};function c(e,t){const n=(0,i.IX)(t,{checkboxCls:`.${e}`,checkboxSize:t.controlInteractiveSize});return[l(n)]}t.ZP=(0,a.I$)("Checkbox",((e,t)=>{let{prefixCls:n}=t;return[c(n,e)]}))},15746:function(e,t,n){var r=n(21584);t.Z=r.Z},71230:function(e,t,n){var r=n(17621);t.Z=r.Z},63496:function(e,t,n){n.d(t,{Z:function(){return R}});var r=n(70593),o=n(74902),i=n(67294),a=n(41018),l=n(87462),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"},s=n(93771),d=function(e,t){return i.createElement(s.Z,(0,l.Z)({},e,{ref:t,icon:c}))};var u=i.forwardRef(d),f=n(85118),h=function(e,t){return i.createElement(s.Z,(0,l.Z)({},e,{ref:t,icon:f.Z}))};var p=i.forwardRef(h),b=n(93967),m=n.n(b),g=n(10225),x=n(1089),y=n(53124),v={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z"}}]},name:"holder",theme:"outlined"},k=function(e,t){return i.createElement(s.Z,(0,l.Z)({},e,{ref:t,icon:v}))};var $=i.forwardRef(k),w=n(33603),Z=n(29691),C=n(40561);var j=function(e){const{dropPosition:t,dropLevelOffset:n,prefixCls:r,indent:o,direction:a="ltr"}=e,l="ltr"===a?"left":"right",c={[l]:-n*o+4,["ltr"===a?"right":"left"]:0};switch(t){case-1:c.top=-3;break;case 1:c.bottom=-3;break;default:c.bottom=-3,c[l]=o+4}return i.createElement("div",{style:c,className:`${r}-drop-indicator`})},E=n(77632);const S=i.forwardRef(((e,t)=>{var n;const{getPrefixCls:o,direction:a,virtual:l,tree:c}=i.useContext(y.E_),{prefixCls:s,className:d,showIcon:u=!1,showLine:f,switcherIcon:h,switcherLoadingIcon:p,blockNode:b=!1,children:g,checkable:x=!1,selectable:v=!0,draggable:k,motion:S,style:O}=e,z=o("tree",s),I=o(),K=null!=S?S:Object.assign(Object.assign({},(0,w.Z)(I)),{motionAppear:!1}),P=Object.assign(Object.assign({},e),{checkable:x,selectable:v,showIcon:u,motion:K,blockNode:b,showLine:Boolean(f),dropIndicatorRender:j}),[N,B,H]=(0,C.ZP)(z),[,L]=(0,Z.ZP)(),M=L.paddingXS/2+((null===(n=L.Tree)||void 0===n?void 0:n.titleHeight)||L.controlHeightSM),R=i.useMemo((()=>{if(!k)return!1;let e={};switch(typeof k){case"function":e.nodeDraggable=k;break;case"object":e=Object.assign({},k)}return!1!==e.icon&&(e.icon=e.icon||i.createElement($,null)),e}),[k]);return N(i.createElement(r.ZP,Object.assign({itemHeight:M,ref:t,virtual:l},P,{style:Object.assign(Object.assign({},null==c?void 0:c.style),O),prefixCls:z,className:m()({[`${z}-icon-hide`]:!u,[`${z}-block-node`]:b,[`${z}-unselectable`]:!v,[`${z}-rtl`]:"rtl"===a},null==c?void 0:c.className,d,B,H),direction:a,checkable:x?i.createElement("span",{className:`${z}-checkbox-inner`}):x,selectable:v,switcherIcon:e=>i.createElement(E.Z,{prefixCls:z,switcherIcon:h,switcherLoadingIcon:p,treeNodeProps:e,showLine:f}),draggable:R}),g))}));var O=S;function z(e,t,n){const{key:r,children:o}=n;e.forEach((function(e){const i=e[r],a=e[o];!1!==t(i,e)&&z(a||[],t,n)}))}function I(e){let{treeData:t,expandedKeys:n,startKey:r,endKey:o,fieldNames:i}=e;const a=[];let l=0;if(r&&r===o)return[r];if(!r||!o)return[];return z(t,(e=>{if(2===l)return!1;if(function(e){return e===r||e===o}(e)){if(a.push(e),0===l)l=1;else if(1===l)return l=2,!1}else 1===l&&a.push(e);return n.includes(e)}),(0,x.w$)(i)),a}function K(e,t,n){const r=(0,o.Z)(t),i=[];return z(e,((e,t)=>{const n=r.indexOf(e);return-1!==n&&(i.push(t),r.splice(n,1)),!!r.length}),(0,x.w$)(n)),i}var P=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function N(e){const{isLeaf:t,expanded:n}=e;return t?i.createElement(a.Z,null):n?i.createElement(u,null):i.createElement(p,null)}function B(e){let{treeData:t,children:n}=e;return t||(0,x.zn)(n)}const H=(e,t)=>{var{defaultExpandAll:n,defaultExpandParent:r,defaultExpandedKeys:a}=e,l=P(e,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);const c=i.useRef(null),s=i.useRef(null),[d,u]=i.useState(l.selectedKeys||l.defaultSelectedKeys||[]),[f,h]=i.useState((()=>(()=>{const{keyEntities:e}=(0,x.I8)(B(l));let t;return t=n?Object.keys(e):r?(0,g.r7)(l.expandedKeys||a||[],e):l.expandedKeys||a||[],t})()));i.useEffect((()=>{"selectedKeys"in l&&u(l.selectedKeys)}),[l.selectedKeys]),i.useEffect((()=>{"expandedKeys"in l&&h(l.expandedKeys)}),[l.expandedKeys]);const{getPrefixCls:p,direction:b}=i.useContext(y.E_),{prefixCls:v,className:k,showIcon:$=!0,expandAction:w="click"}=l,Z=P(l,["prefixCls","className","showIcon","expandAction"]),C=p("tree",v),j=m()(`${C}-directory`,{[`${C}-directory-rtl`]:"rtl"===b},k);return i.createElement(O,Object.assign({icon:N,ref:t,blockNode:!0},Z,{showIcon:$,expandAction:w,prefixCls:C,className:j,expandedKeys:f,selectedKeys:d,onSelect:(e,t)=>{var n;const{multiple:r,fieldNames:i}=l,{node:a,nativeEvent:d}=t,{key:h=""}=a,p=B(l),b=Object.assign(Object.assign({},t),{selected:!0}),m=(null==d?void 0:d.ctrlKey)||(null==d?void 0:d.metaKey),g=null==d?void 0:d.shiftKey;let x;r&&m?(x=e,c.current=h,s.current=x,b.selectedNodes=K(p,x,i)):r&&g?(x=Array.from(new Set([].concat((0,o.Z)(s.current||[]),(0,o.Z)(I({treeData:p,expandedKeys:f,startKey:h,endKey:c.current,fieldNames:i}))))),b.selectedNodes=K(p,x,i)):(x=[h],c.current=h,s.current=x,b.selectedNodes=K(p,x,i)),null===(n=l.onSelect)||void 0===n||n.call(l,x,b),"selectedKeys"in l||u(x)},onExpand:(e,t)=>{var n;return"expandedKeys"in l||h(e),null===(n=l.onExpand)||void 0===n?void 0:n.call(l,e,t)}}))};var L=i.forwardRef(H);const M=O;M.DirectoryTree=L,M.TreeNode=r.OF;var R=M},1208:function(e,t,n){var r=n(87462),o=n(67294),i=n(5717),a=n(93771),l=function(e,t){return o.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:i.Z}))},c=o.forwardRef(l);t.Z=c}}]);