from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.structured_dataset import StructuredDataset, StructuredDatasetCreate, StructuredDatasetUpdate, StructuredDatasetResponse
from ..db.mongodb import db
from ..utils.auth import verify_token
from bson import ObjectId
from datetime import datetime
import logging
logger = logging.getLogger(__name__)


router = APIRouter(
    prefix="/api",
    tags=["structured_datasets"]
)

# 创建新的结构化数据集
@router.post("/structured-datasets", response_model=StructuredDatasetResponse)
async def create_structured_dataset(dataset: StructuredDatasetCreate, current_user: dict = Depends(verify_token)):
    logger.info(f"Creating structured dataset: {dataset}")
    logger.info(f"current_user: {current_user}")
    new_dataset = dataset.dict()
    new_dataset["created_at"] = datetime.now()
    new_dataset["last_updated"] = datetime.now()
    new_dataset["user_id"] = current_user["id"]
    new_dataset["is_active"] = True
    new_dataset["user_name"] = current_user["name"]
    result = await db["structured_datasets"].insert_one(new_dataset)
    created_dataset = await db["structured_datasets"].find_one({"_id": result.inserted_id})
    return StructuredDatasetResponse(**created_dataset)

# 获取结构化数据集列表
@router.get("/structured-datasets", response_model=Dict[str, Any])
async def read_structured_datasets(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    name: Optional[str] = None,
    description: Optional[str] = None,
    processing_status: Optional[str] = None,  # 新增状态参数
    tags: Optional[str] = None,  # 新增状态参数
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"is_active": True}
    if name:
        query["name"] = {"$regex": name, "$options": "i"}  # 使用正则表达式进行模糊匹配，忽略大小写
    if description:
        query["description"] = {"$regex": description, "$options": "i"}  # 支持描述的模糊匹配
    if processing_status:  # 添加状态筛选条件
        query["processing_status"] = processing_status
    if tags:  # 添加状态筛选条件
        query["tags"] = tags

    # 计算 row_count 总和
    sum_pipeline = [
        {"$match": query},
        {"$group": {
            "_id": None,
            "total_rows": {"$sum": "$row_count"},
            "dataset_count": {"$sum": 1}
        }}
    ]
    sum_result = await db["structured_datasets"].aggregate(sum_pipeline).to_list(length=1)
    total_rows = sum_result[0]["total_rows"] if sum_result else 0
    
    # 获取数据集列表
    datasets = await db["structured_datasets"].find(query).sort("created_at", -1).skip(skip).limit(pageSize).to_list(length=pageSize)
    for dataset in datasets:
        dataset["id"] = str(dataset["_id"])
        del dataset["_id"]
    
    total = await db["structured_datasets"].count_documents(query)
    return {
        "data": [StructuredDatasetResponse(**dataset) for dataset in datasets],
        "total": total,
        "total_rows": total_rows,  # 添加行数总和到返回结果中
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取单个结构化数据集
@router.get("/structured-datasets/{dataset_id}", response_model=StructuredDatasetResponse)
async def read_structured_dataset(dataset_id: str, current_user: dict = Depends(verify_token)):
    dataset = await db["structured_datasets"].find_one({"_id": ObjectId(dataset_id)})
    if dataset is None:
        raise HTTPException(status_code=404, detail="Structured dataset not found")
    dataset["id"] = str(dataset["_id"])
    del dataset["_id"]
    return StructuredDatasetResponse(**dataset)

# 更新结构化数据集
@router.put("/structured-datasets/{dataset_id}", response_model=StructuredDatasetResponse)
async def update_structured_dataset(dataset_id: str, dataset: StructuredDatasetUpdate, current_user: dict = Depends(verify_token)):
    update_data = dataset.dict(exclude_unset=True)
    update_data["last_updated"] = datetime.now()
    result = await db["structured_datasets"].update_one(
        {"_id": ObjectId(dataset_id)},
        {"$set": update_data}
    )
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Structured dataset not found")
    updated_dataset = await db["structured_datasets"].find_one({"_id": ObjectId(dataset_id)})
    return StructuredDatasetResponse(**updated_dataset)

# 删除结构化数据集（软删除）
@router.delete("/structured-datasets/{dataset_id}", response_model=dict)
async def delete_structured_dataset(dataset_id: str, current_user: dict = Depends(verify_token)):
    result = await db["structured_datasets"].update_one(
        {"_id": ObjectId(dataset_id)},
        {
            "$set": {
                "is_active": False,
                "deleted_at": datetime.now(),
                "deleted_by": current_user["id"]
            }
        }
    )
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Structured dataset not found")
    return {"message": "Structured dataset deleted successfully"}
