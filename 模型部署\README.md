# PAI部署
## 基于 OLLAMA
可以参考这种方式在dsw跑推理
https://pai.console.aliyun.com/#/dsw-gallery/preview/deepLearning/nlp/ollama_modelscope


2025-01-23 时候的状态
![alt text](image.png)


参考的demo
```json
{
    "containers": [
        {
            "image": "registry.cn-beijing.aliyuncs.com/wiseweb/roardata-nlp-api:vllm-openai-v053",
            "port": 9019,
            "script": "python3 -m vllm.entrypoints.openai.api_server --served-model-name Qwen1.5-14B-Chat --host 0.0.0.0 --port 9019 --max-model-len 32768  --model /mnt/model/Qwen1.5-32B-Chat-AWQ/ --trust-remote-code --quantization awq --gpu-memory-utilization 0.95 --tensor-parallel-size 1 --disable-log-stats"
        }
    ],
    "credential": {
        "token": "********************************************************"
    },
    "dockerAuth": "****************************************",
    "metadata": {
        "cpu": 14,
        "gpu": 1,
        "instance": 1,
        "memory": 100000,
        "name": "qwen1_5_32b_chat1",
        "resource": "eas-r-vzupqjh980f3lv4dg2",
        "resource_burstable": false
    },
    "name": "qwen1_5_32b_chat",
    "storage": [
        {
            "mount_path": "/mnt/model/Qwen1.5-32B-Chat-AWQ/",
            "oss": {
                "path": "oss://ai-middle-bj-platform/model/qwen/Qwen1.5-32B-Chat-AWQ/",
                "readOnly": true
            },
            "properties": {
                "resource_type": "model"
            }
        }
    ]
}
```

### Vllm 
vllm serve deepseek-ai/DeepSeek-R1-Distill-Qwen-32B --tensor-parallel-size 2 --max-model-len 32768 --enforce-eager







# 命令记录



oss://ai-middle-bj-platform/model/llama-3-sqlcoder-8b/

ossutil cp /data/model/deepseek-ai/DeepSeek-R1-Distill-Qwen-32B  oss://ai-middle-bj-platform/model/ -r --ignore-existing


ossutil cp D:/localpath/example.iso oss://examplebucket/desfolder/


# deepseek Pai部署

## 下载参数，从魔塔社区下载到阿里云的服务器
上传参数：

### 下载到服务器（阿里云）
服务器终端运行python
python
from modelscope.hub.snapshot_download import snapshot_download
model_dir = snapshot_download('deepseek-ai/DeepSeek-R1-Distill-Qwen-32B', cache_dir='/data/model/')
model_dir = snapshot_download('deepseek-ai/DeepSeek-R1-Distill-Qwen-14B', cache_dir='/data/model/')
model_dir = snapshot_download('deepseek-ai/DeepSeek-R1-Distill-Qwen-7B', cache_dir='/data/model/')



model_dir = snapshot_download('Qwen/Qwen3-4B', cache_dir='/data/qwen3/')


## 上传到OSS
### 按照装 OSS linux上传工具文档

安装ossutil
Linux
安装ossutil。

下载ossutil压缩包。

放大查看复制代码
curl -o ossutil-2.0.6-beta.01091200-linux-amd64.zip https://gosspublic.alicdn.com/ossutil/v2-beta/2.0.6-beta.01091200/ossutil-2.0.6-beta.01091200-linux-amd64.zip
说明
此处以Linux x86 64bit为例进行介绍。其他系统下载地址，请参见下载ossutil。

在下载压缩包的所在目录执行以下解压命令。

放大查看复制代码
unzip ossutil-2.0.6-beta.01091200-linux-amd64.zip
进入ossutil-2.0.6-beta.01091200-linux-amd64目录。

放大查看复制代码
cd ossutil-2.0.6-beta.01091200-linux-amd64
在当前目录执行以下命令。

放大查看复制代码
chmod 755 ossutil
执行以下命令，实现ossutil的全局调用。

放大查看复制代码
sudo mv ossutil /usr/local/bin/ && sudo ln -s /usr/local/bin/ossutil /usr/bin/ossutil
验证是否成功安装ossutil。

放大查看复制代码
ossutil
返回ossutil的帮助信息即表示安装成功。

配置ossutil。

输入配置命令。

放大查看复制代码
ossutil config
根据提示设置配置文件路径。您可以直接回车使用默认的配置文件路径。

放大查看复制代码
Please enter the config file name,the file name can include path(default /root/.ossutilconfig, carriage return will use the default file. If you specified this option to other file, you should specify --config-file option to the file when you use other commands):
ossutil默认使用/root/.ossutilconfig作为配置文件，若您设置了配置文件的路径，则每次使用命令时需增加-c选项指定配置文件。例如配置文件保存为/home/<USER>

放大查看复制代码
ossutil ls oss://examplebucket -c /home/<USER>
根据提示分别设置AccessKey ID、AccessKey Secret、地域等信息。

输入您创建的AccessKey ID。

放大查看复制代码
Please enter Access Key ID [****************id]:LTAI****************
输入您创建的AccessKey Secret。

放大查看复制代码
Please enter Access Key Secret [****************sk]:R6vg*********************
输入您的OSS的数据中心所在的地域，如无任何输入，默认值为cn-hangzhou。

放大查看复制代码
Please enter Region [cn-hangzhou]:cn-hangzhou
如果您不需要自定义 Endpoint，可以直接按回车跳过该参数的配置。在上一步配置完地域信息后，将默认使用该地域 ID 对应的外网 Endpoint。例如，如果您设置的 region-id 为 cn-hangzhou，默认使用的外网 Endpoint 是 https://oss-cn-hangzhou.aliyuncs.com。如果您需要自定义 OSS 数据中心所在地域的 Endpoint，请输入您的 Endpoint 信息。

放大查看复制代码
Please enter Endpoint (optional, use public endpoint by default) [None]: https://oss-cn-hangzhou.aliyuncs.com

### 参数
第一步默认
AccessKeyID：LTAIN08l4poLiJDx      
AccessKeySecret：oKGyLdD4KIOuvVZTYZ2SAysRqkQD41
cn-beijing 
https://oss-cn-beijing.aliyuncs.com

内网地址 使用地址会非常快
cn-beijing 
https://oss-cn-beijing-internal.aliyuncs.com
### 上传命令
#### 32B 千问蒸馏
ossutil cp /data/model/deepseek-ai/DeepSeek-R1-Distill-Qwen-32B  oss://ai-middle-bj-platform/model/DeepSeek-R1-Distill-Qwen-32B/ -r --ignore-existing

#### 14B千问蒸馏
ossutil cp /data/model/deepseek-ai/DeepSeek-R1-Distill-Qwen-14B  oss://ai-middle-bj-platform/model/DeepSeek-R1-Distill-Qwen-14B/ -r --ignore-existing
#### 7B千问蒸馏
ossutil cp /data/model/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B   oss://ai-middle-bj-platform/model/DeepSeek-R1-Distill-Qwen-7B/ -r --ignore-existing


注意目录：/data/model/deepseek-ai/DeepSeek-R1-Distill-Qwen-32B下面的所有文件会上传到
 oss://ai-middle-bj-platform/model/DeepSeek-R1-Distill-Qwen-32B/目录下



#### 重新配置参数可以使用 ossutil config



## PAI 部署 
![alt text](image-1.png)

![alt text](image-2.png)

![alt text](image-3.png)

![alt text](image-4.png)
在这个地方 直接带最下面 编辑json 直接部署

官方命令 vllm serve deepseek-ai/DeepSeek-R1-Distill-Qwen-32B --tensor-parallel-size 2 --max-model-len 32768 --enforce-eager


### 32B
```json
{
    "containers": [
        {
            "image": "registry.cn-beijing.aliyuncs.com/wiseweb/roardata-nlp-api:vllm-openai-v061-telechat",
            "port": 9020,
            "script": "python3 -m vllm.entrypoints.openai.api_server --served-model-name DeepSeek-R1-Distill-Qwen-14B --host 0.0.0.0 --port 9019 --max-model-len 32768 --model /mnt/model/DeepSeek-R1-Distill-Qwen-14B/ --trust-remote-code --gpu-memory-utilization 0.95 --tensor-parallel-size 2 --disable-log-stats"
        }
    ],
    "credential": {
        "token": "ZjI1MDkzMDMyYmQ0MzA2MGYxNTQ5MGIxNWU3NDIxODk0MWMxMWFhYw=="
    },
    "dockerAuth": "d2lzZXdlYi1qY2JAd2lzZXdlYjoxcWF6QFdTWA==",
    "metadata": {
        "cpu": 16,
        "gpu": 1,
        "instance": 1,
        "memory": 100000,
        "name": "deepseek_r1_distill_qwen_14b",
        "resource": "eas-r-vzupqjh980f3lv4dg2",
        "resource_burstable": false
    },
    "name": "deepseek_r1_distill_qwen_14b",
    "storage": [
        {
            "mount_path": "/mnt/model/DeepSeek-R1-Distill-Qwen-14B/",
            "oss": {
                "path": "oss://ai-middle-bj-platform/model/DeepSeek-R1-Distill-Qwen-14B/",
                "readOnly": true
            },
            "properties": {
                "resource_type": "model"
            }
        }
    ]
}
```
换镜像
```json
{
    "containers": [
        {
            "image": "registry-vpc.cn-beijing.aliyuncs.com/wiseweb/roardata-nlp-api:vllm-openai-v061-2501",
            "port": 9020,
            "script": "python3 -m vllm.entrypoints.openai.api_server --served-model-name DeepSeek-R1-Distill-Qwen-14B --host 0.0.0.0 --port 9019 --max-model-len 32768 --model /mnt/model/DeepSeek-R1-Distill-Qwen-14B/ --trust-remote-code --gpu-memory-utilization 0.95 --tensor-parallel-size 1 --disable-log-stats"
        }
    ],
    "credential": {
        "token": "ZjI1MDkzMDMyYmQ0MzA2MGYxNTQ5MGIxNWU3NDIxODk0MWMxMWFhYw=="
    },
    "dockerAuth": "d2lzZXdlYi1qY2JAd2lzZXdlYjoxcWF6QFdTWA==",
    "metadata": {
        "cpu": 16,
        "gpu": 1,
        "instance": 1,
        "memory": 100000,
        "name": "deepseek_r1_distill_qwen_14b",
        "resource": "eas-r-vzupqjh980f3lv4dg2",
        "resource_burstable": false
    },
    "name": "deepseek_r1_distill_qwen_14b",
    "storage": [
        {
            "mount_path": "/mnt/model/DeepSeek-R1-Distill-Qwen-14B/",
            "oss": {
                "path": "oss://ai-middle-bj-platform/model/DeepSeek-R1-Distill-Qwen-14B/",
                "readOnly": true
            },
            "properties": {
                "resource_type": "model"
            }
        }
    ]
}
```

![alt text](image-5.png)
需要注意 name 和 metadata.name 只需要小写、数字、下划线


### 32B


```json
{
    "containers": [
        {
            "image": "registry.cn-beijing.aliyuncs.com/wiseweb/roardata-nlp-api:vllm-openai-v061",
            "port": 9021,
            "script": "python3 -m vllm.entrypoints.openai.api_server --served-model-name DeepSeek-R1-Distill-Qwen-32B --host 0.0.0.0 --port 9019 --max-model-len 32768 --model /mnt/model/DeepSeek-R1-Distill-Qwen-32B/ --trust-remote-code --gpu-memory-utilization 0.95 --tensor-parallel-size 1 --disable-log-stats"
        }
    ],
    "credential": {
        "token": "ZjI1MDkzMDMyYmQ0MzA2MGYxNTQ5MGIxNWU3NDIxODk0MWMxMWFhYw=="
    },
    "dockerAuth": "d2lzZXdlYi1qY2JAd2lzZXdlYjoxcWF6QFdTWA==",
    "metadata": {
        "cpu": 16,
        "gpu": 1,
        "instance": 1,
        "memory": 100000,
        "name": "deepseek_r1_distill_qwen_32b",
        "resource": "eas-r-vzupqjh980f3lv4dg2",
        "resource_burstable": false
    },
    "name": "deepseek_r1_distill_qwen_32b",
    "storage": [
        {
            "mount_path": "/mnt/model/DeepSeek-R1-Distill-Qwen-32B/",
            "oss": {
                "path": "oss://ai-middle-bj-platform/model/DeepSeek-R1-Distill-Qwen-32B/",
                "readOnly": true
            },
            "properties": {
                "resource_type": "model"
            }
        }
    ]
}
```

换内网镜像
```json
{
    "containers": [
        {
            "image": "registry-vpc.cn-beijing.aliyuncs.com/wiseweb/roardata-nlp-api:vllm-openai-v061-2501",
            "port": 9021,
            "script": "PYTHONPATH=/ PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:32 CUDA_VISIBLE_DEVICES=2,3 python3 -m torch.multiprocessing.spawn -m vllm.entrypoints.openai.api_server --served-model-name DeepSeek-R1-Distill-Qwen-32B --host 0.0.0.0 --port 9019 --max-model-len 32768 --model /mnt/model/DeepSeek-R1-Distill-Qwen-32B/ --trust-remote-code --gpu-memory-utilization 0.95 --tensor-parallel-size 1 --disable-log-stats",
            "env": {
                "PYTHONPATH": "/",
                "PYTORCH_CUDA_ALLOC_CONF": "max_split_size_mb:32",
                "PYTHONUNBUFFERED": "1",
                "PYTHON_MULTIPROCESSING_START_METHOD": "spawn"
            }
        }
    ],
    "credential": {
        "token": "ZjI1MDkzMDMyYmQ0MzA2MGYxNTQ5MGIxNWU3NDIxODk0MWMxMWFhYw=="
    },
    "dockerAuth": "d2lzZXdlYi1qY2JAd2lzZXdlYjoxcWF6QFdTWA==",
    "metadata": {
        "cpu": 32,
        "gpu": 2,
        "instance": 1,
        "memory": 240000,
        "name": "deepseek_r1_distill_qwen_32b",
        "resource": "eas-r-vzupqjh980f3lv4dg2",
        "resource_burstable": false
    },
    "name": "deepseek_r1_distill_qwen_32b",
    "storage": [
        {
            "mount_path": "/mnt/model/DeepSeek-R1-Distill-Qwen-32B/",
            "oss": {
                "path": "oss://ai-middle-bj-platform/model/DeepSeek-R1-Distill-Qwen-32B/",
                "readOnly": true
            },
            "properties": {
                "resource_type": "model"
            }
        }
    ]
}
```

## 测试

## 注意事项
1. 模型名称 需要小写、数字、下划线
2. 模型名称 需要和 镜像名称 一致
3. 当部署太长时间等待可以查看部署事件
4. --tensor-parallel-size 1  与 1个GPU 对应
![alt text](image-9.png)


docker login --username=wiseweb-jcb@wiseweb registry.cn-beijing.aliyuncs.com

1qaz@WSX

docker login --username=wiseweb-jcb@wiseweb registry-vpc.cn-beijing.aliyuncs.com



## PAI 部署 自带模型

![alt text](image-10.png)
![alt text](image-11.png)
![alt text](image-14.png)


![alt text](image-13.png)