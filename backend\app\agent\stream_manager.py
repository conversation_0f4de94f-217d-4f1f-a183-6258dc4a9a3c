"""
管理流式响应的辅助模块
"""
import asyncio
import logging
import uuid
from collections import defaultdict
from typing import Any, AsyncGenerator, Dict, List, Optional

logger = logging.getLogger(__name__)

class StreamManager:
    """
    流式响应管理器 - 使用内存存储流式生成器的引用
    """
    _streams: Dict[str, Any] = {}
    _callbacks: Dict[str, asyncio.Queue] = defaultdict(asyncio.Queue)
    _content_cache: Dict[str, List[str]] = {}

    @classmethod
    def register_stream(cls, stream_id: Optional[str] = None) -> str:
        """注册一个新的流ID"""
        if not stream_id:
            stream_id = str(uuid.uuid4())
        return stream_id

    @classmethod
    def set_stream(cls, stream_id: str, stream: Any) -> None:
        """设置流对象"""
        cls._streams[stream_id] = stream

    @classmethod
    def get_stream(cls, stream_id: str) -> Optional[Any]:
        """获取流对象并增加引用计数"""
        if stream_id in cls._streams:
            return cls._streams.get(stream_id)
        return None

    @classmethod
    def remove_stream(cls, stream_id: str) -> None:
        """移除流对象"""
        if stream_id in cls._streams:
            del cls._streams[stream_id]

    @classmethod
    async def stream_content(cls, stream_id: str) -> AsyncGenerator[str, None]:
        """从流中生成内容，支持缓存重放"""
        # 检查是否已有缓存
        if stream_id in cls._content_cache:
            logger.info(f"使用缓存内容重放流: {stream_id}")
            for chunk in cls._content_cache[stream_id]:
                yield chunk
            return

        # 没有缓存时正常处理
        stream = cls.get_stream(stream_id)
        if not stream:
            logger.warning(f"找不到流ID: {stream_id}")
            yield "错误：找不到指定的流"
            return

        # 创建新的缓存
        cache = []
        cls._content_cache[stream_id] = cache

        try:
            queue = cls._callbacks[stream_id]
            async for chunk in stream:
                await queue.put(chunk)
                cache.append(chunk)  # 缓存内容
                yield chunk

            # 标记流结束
            await queue.put(None)
        except Exception as e:
            logger.error(f"流式处理出错: {e}")
            error_msg = f"流式处理出错: {str(e)}"
            cache.append(error_msg)  # 缓存错误信息
            yield error_msg
        finally:
            cls.release_stream(stream_id)

    @classmethod
    async def consume_stream(cls, stream_id: str) -> str:
        """消费整个流并返回完整内容"""
        queue = cls._callbacks[stream_id]
        result = []

        while True:
            chunk = await queue.get()
            if chunk is None:  # 流结束
                break
            result.append(chunk)

        return "".join(result)

    @classmethod
    def release_stream(cls, stream_id: str) -> None:
        """减少引用计数，当计数为0时移除流"""
        cls.remove_stream(stream_id)

    @classmethod
    def clear_cache(cls, stream_id: Optional[str] = None):
        """清除缓存"""
        if stream_id:
            if stream_id in cls._content_cache:
                del cls._content_cache[stream_id]
        else:
            cls._content_cache.clear()

# 单例实例
stream_manager = StreamManager()
