# from sqlalchemy import <PERSON><PERSON><PERSON>, Inte<PERSON>, String, DateTime, <PERSON><PERSON><PERSON>
# from sqlalchemy.ext.declarative import declarative_base
from mongoengine import Document
from mongoengine.fields import (
    StringField,
    IntField,
    DateTimeField,
    BooleanField,
    DictField
)
from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime

class EmbeddingModel(Document):
    id = IntField(required=True, primary_key=True)
    name = StringField(required=True, max_length=255)
    embedding_name = StringField(required=True, max_length=255)
    vector_size = IntField(required=True)  # 向量大小
    distance_metric = StringField(default='cosine', max_length=50)  # 距离度量
    service_url = StringField(max_length=255)
    api_key = StringField(max_length=255)
    provider = StringField(max_length=50)
    created_at = DateTimeField(default=datetime.now)
    updated_at = DateTimeField(auto_now=True)
    extra = DictField()
    created_by = IntField(required=True)
    is_active = BooleanField(default=True)
    meta = {
        'collection': 'embeddings',
        'indexes': ['name', 'embedding_name']
    }


class EmbeddingCreate(BaseModel):
    name: str
    embedding_name: str
    vector_size: int
    distance_metric: Optional[str] = 'cosine'
    service_url: Optional[str] = None
    api_key: Optional[str] = None
    provider: Optional[str] = None
    extra: Optional[Dict[str, Any]] = None
    # created_by: int
    class Config:
        from_attributes = True

class EmbeddingUpdate(BaseModel):
    name: Optional[str] = None
    embedding_name: Optional[str] = None
    vector_size: Optional[int] = None
    distance_metric: Optional[str] = None
    service_url: Optional[str] = None
    api_key: Optional[str] = None
    provider: Optional[str] = None
    is_active: Optional[bool] = None
    extra: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True

class EmbeddingResponse(BaseModel):
    id: int
    name: str
    embedding_name: str
    vector_size: int
    distance_metric: str
    service_url: str
    api_key: str
    provider: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    extra: Optional[Dict[str, Any]] = None
    created_by: int
    is_active: bool

    class Config:
        from_attributes = True

