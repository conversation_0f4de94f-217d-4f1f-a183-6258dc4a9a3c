{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[31mERROR: Could not find a version that satisfies the requirement caj2pdf (from versions: none)\u001b[0m\u001b[31m\n", "\u001b[0m\u001b[31mERROR: No matching distribution found for caj2pdf\u001b[0m\u001b[31m\n", "\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m23.3.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["# !pip install caj2pdf"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from langchain_mcp_adapters.client import MultiServerMCPClient\n", "from langgraph.prebuilt import create_react_agent\n", "\n", "from langchain_openai import ChatOpenAI"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "model = ChatOpenAI(\n", "                model=\"Qwen/Qwen3-32B\",\n", "                openai_api_key=\"sk-jexorjtcpbtmhtnxyqcvlzbkxjhsqaqzvkrniotauxuzjlmn\",\n", "                openai_api_base=\"https://api.siliconflow.cn/v1\",\n", "\n", "            )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async with MultiServerMCPClient(\n", "    {\n", "        \"markitdown\": {\n", "            # make sure you start your weather server on port 8000\n", "            \"url\": \"http://localhost:3001/sse\",\n", "            \"transport\": \"sse\",\n", "        }\n", "    }\n", ") as client:\n", "    agent = create_react_agent(model, client.get_tools())\n", "    math_response = await agent.ainvoke({\"messages\": \"what's (3 + 5) x 12?\"})\n", "    weather_response = await agent.ainvoke({\"messages\": \"what is the weather in nyc?\"})"]}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}