
## 政策 /pro_mcp_data_zhengce_v2 



## 知识库索引 wise_agent_chunk_index
## 海峡金融舆情，过滤网址的，pro_mcp_data_financial_info_v1
## 投诉信息 pro_mcp_data_complaint_v1



```shell
# 创建索引


PUT /wise_agent_chunk_index
{
  "mappings": {
    "properties": {
      "id": {"type": "keyword"},
      "file_id": {"type": "keyword"},
      "chunk_id": {"type": "keyword"},
      "knowledge_base_id": {"type": "keyword"},
      "chunk_index": {"type": "integer"},
      "index_content": {
        "type": "text",
        "analyzer": "ik_max_word",  
        "search_analyzer": "ik_smart"  
      },
      "embedding": {
        "type": "dense_vector",
        "dims": 1536
        
      },
      "chunk_type": {"type": "keyword"},
      "created_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss"
      }
    }
  }
}


PUT /pro_data_1358
{
  "mappings": {
    "properties": {
      "author":              { "type": "keyword" },
      "area":                { "type": "keyword" },
      "source":              { "type": "keyword" },
      "publishtime":         { "type": "date", "format": "yyyy-MM-dd HH:mm:ss" },
      "site_media_nature":   { "type": "keyword" },
      "site_id":             { "type": "keyword" },
      "site_level":          { "type": "keyword" },
      "new_author":          { "type": "keyword" },
      "reply":               { "type": "keyword" },
      "imgs":                { "type": "text" },
      "site_area_code":      { "type": "keyword" },
      "site_name":           { "type": "text" },
      "content":             { "type": "text" },     
      "new_site_name":       { "type": "keyword" },
      "agg_domain_1":        { "type": "keyword" },
      "summary":             { "type": "text" },
      "correlationOrg": {
        "type": "nested",
        "properties": {
          "score":       { "type": "integer" },
          "creditCode":  { "type": "keyword" },
          "area_code":   { "type": "keyword" },
          "name":        { "type": "text" },
          "id":          { "type": "keyword" },
          "keyword":     { "type": "keyword" },
          "type":        { "type": "keyword" }
        }
      },
      "tag": {
        "type": "keyword"
      },
       "authorAttitude" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "authorViewpoint" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "characterEntity" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "createTimeES" : {
          "type" : "date",
          "format" : "yyyy-MM-dd HH:mm:ss"
        },
        "embedding" : {
          "type" : "dense_vector",
          "dims" : 1536
        },
        "eventInfo" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "institutionalEntities" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "locationEntity" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "summaryFacts" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "title" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "url" : {
          "type" : "keyword",
          "ignore_above" : 256
        }
    }
  }
}

````


{
  "wangsu_jcg_info_trump" : {
    "mappings" : {
      "properties" : {
        "authorAttitude" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "authorViewpoint" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "characterEntity" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "content" : {
          "type" : "text",
          "analyzer" : "ik_max_word"
        },
        "contentSummary" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "createTimeES" : {
          "type" : "date",
          "format" : "yyyy-MM-dd HH:mm:ss"
        },
        "embedding" : {
          "type" : "dense_vector",
          "dims" : 1536
        },
        "eventInfo" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "institutionalEntities" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "locationEntity" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "publishtime" : {
          "type" : "date",
          "format" : "yyyy-MM-dd HH:mm:ss"
        },
        "summaryFacts" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "title" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "url" : {
          "type" : "keyword",
          "ignore_above" : 256
        }
      }
    }
  }
}




{
  "pro_mcp_data_zhengce": {
    "mappings": {
      "properties": {
        "affected_industries": {
          "type": "keyword",
          "fields": {
            "text": {
              "type": "text",
              "analyzer": "ik_max_word"
            }
          }
        },
        "area": { "type": "keyword" },
        "embedding": { "type": "dense_vector", "dims": 1536 },
        "id": { "type": "integer" },
        "law_doc_is_download": { "type": "integer", "null_value": 0 },
        "laws_ageing": { "type": "keyword" },
        "laws_approving_authority": { "type": "text", "analyzer": "ik_max_word" },
        "laws_area": { "type": "keyword" },
        "laws_area_address": { "type": "text", "analyzer": "ik_max_word" },
        "laws_area_code": { "type": "long", "null_value": 0 },
        "laws_brief": { "type": "text", "analyzer": "ik_max_word" },
        "laws_change_excute_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
        "laws_change_pass_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
        "laws_change_public_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
        "laws_clean_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
        "laws_code": { "type": "keyword" },
        "laws_crawler_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
        "laws_doc_local_url": { "type": "keyword", "index": false },
        "laws_doc_url": { "type": "keyword", "index": false },
        "laws_excute_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
        "laws_expiry_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
        "laws_formulate_authority": { "type": "text", "analyzer": "ik_max_word" },
        "laws_formulate_way": { "type": "keyword" },
        "laws_level": { "type": "keyword" },
        "laws_level_file": { "type": "keyword" },
        "laws_level_two": { "type": "keyword" },
        "laws_object": { "type": "long", "null_value": 0 },
        "laws_pass_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
        "laws_public_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
        "laws_relevant_datas": { "type": "keyword", "index": false },
        "laws_relevant_docs": { "type": "keyword", "index": false },
        "laws_site_name": { "type": "text", "analyzer": "ik_max_word" },
        "laws_text": { "type": "text", "index": false },
        "laws_text_from": { "type": "integer" },
        "laws_text_search": { "type": "text", "analyzer": "ik_max_word" },
        "laws_title": { "type": "text", "analyzer": "ik_max_word" },
        "laws_type": { "type": "keyword" },
        "laws_update_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
        "laws_url": { "type": "keyword" },
        "laws_written_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
        "lid": { "type": "keyword" },
        "policy_theme": {
          "type": "keyword",
          "fields": {
            "text": {
              "type": "text",
              "analyzer": "ik_max_word"
            }
          }
        },
        "summary": { "type": "text" },

        // 以下为原嵌套字段平铺
        "market_impact_business_opportunities": { "type": "text", "analyzer": "ik_max_word" },
        "market_impact_industry_effect": { "type": "text", "analyzer": "ik_max_word" },
        "market_impact_market_risk": { "type": "text", "analyzer": "ik_max_word" },

        "policy_direction_implementation_timeline": { "type": "text", "analyzer": "ik_max_word" },
        "policy_direction_key_points": { "type": "text", "analyzer": "ik_max_word" },
        "policy_direction_trend": { "type": "text", "analyzer": "ik_max_word" },

        "policy_impact_compliance_requirements": { "type": "text", "analyzer": "ik_max_word" },
        "policy_impact_regulatory_impact": { "type": "text", "analyzer": "ik_max_word" },
        "policy_impact_risk_level": { "type": "keyword" },

        // 新增关联法规字段
        "related_laws": {
          "type": "keyword"
        }
      }
    }
  }
}




PUT /pro_mcp_data_zhengce_v2
{
  "mappings": {
    "properties": {
      "affected_industries": {
        "type": "keyword",
        "fields": {
          "text": {
            "type": "text",
            "analyzer": "ik_max_word"
          }
        }
      },
      "area": { "type": "keyword" },
      "embedding": { "type": "dense_vector", "dims": 1536 },
      "id": { "type": "integer" },
      "law_doc_is_download": { "type": "integer", "null_value": 0 },
      "laws_ageing": { "type": "keyword" },
      "laws_approving_authority": { "type": "text", "analyzer": "ik_max_word" },
      "laws_area": { "type": "keyword" },
      "laws_area_address": { "type": "text", "analyzer": "ik_max_word" },
      "laws_area_code": { "type": "long", "null_value": 0 },
      "laws_brief": { "type": "text", "analyzer": "ik_max_word" },
      "laws_change_excute_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
      "laws_change_pass_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
      "laws_change_public_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
      "laws_clean_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
      "laws_code": { "type": "keyword" },
      "laws_crawler_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
      "laws_doc_local_url": { "type": "keyword", "index": false },
      "laws_doc_url": { "type": "keyword", "index": false },
      "laws_excute_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
      "laws_expiry_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
      "laws_formulate_authority": { "type": "text", "analyzer": "ik_max_word" },
      "laws_formulate_way": { "type": "keyword" },
      "laws_level": { "type": "keyword" },
      "laws_level_file": { "type": "keyword" },
      "laws_level_two": { "type": "keyword" },
      "laws_object": { "type": "long", "null_value": 0 },
      "laws_pass_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
      "laws_public_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
      "laws_relevant_datas": { "type": "keyword", "index": false },
      "laws_relevant_docs": { "type": "keyword", "index": false },
      "laws_site_name": { "type": "text", "analyzer": "ik_max_word" },
      "laws_text": { "type": "text", "index": false },
      "laws_text_from": { "type": "integer" },
      "laws_text_search": { "type": "text", "analyzer": "ik_max_word" },
      "laws_title": { "type": "text", "analyzer": "ik_max_word" },
      "laws_type": { "type": "keyword" },
      "laws_update_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
      "laws_url": { "type": "keyword" },
      "laws_written_time": { "type": "date", "null_value": "1970-01-01 00:00:00", "format": "yyyy-MM-dd HH:mm:ss" },
      "lid": { "type": "keyword" },
      "policy_theme": {
        "type": "keyword",
        "fields": {
          "text": {
            "type": "text",
            "analyzer": "ik_max_word"
          }
        }
      },
      "summary": { "type": "text" },

      "market_impact_business_opportunities": { "type": "text", "analyzer": "ik_max_word" },
      "market_impact_industry_effect": { "type": "text", "analyzer": "ik_max_word" },
      "market_impact_market_risk": { "type": "text", "analyzer": "ik_max_word" },

      "policy_direction_implementation_timeline": { "type": "text", "analyzer": "ik_max_word" },
      "policy_direction_key_points": { "type": "text", "analyzer": "ik_max_word" },
      "policy_direction_trend": { "type": "text", "analyzer": "ik_max_word" },

      "policy_impact_compliance_requirements": { "type": "text", "analyzer": "ik_max_word" },
      "policy_impact_regulatory_impact": { "type": "text", "analyzer": "ik_max_word" },
      "policy_impact_risk_level": { "type": "keyword" },

      "related_laws": {
        "type": "keyword"
      }
    }
  }
}




  PUT /pro_mcp_data_financial_info_v1
  {
    "mappings" : {
      "properties" : {
        "agg_domain_1" : {
          "type" : "keyword"
        },
        "area" : {
          "type" : "keyword"
        },
        "author" : {
          "type" : "keyword"
        },
        "authorAttitude" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "authorViewpoint" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "characterEntity" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "content" : {
          "type" : "text"
        },
        "correlationOrg" : {
          "type" : "nested",
          "properties" : {
            "area_code" : {
              "type" : "keyword"
            },
            "creditCode" : {
              "type" : "keyword"
            },
            "id" : {
              "type" : "keyword"
            },
            "keyword" : {
              "type" : "keyword"
            },
            "name" : {
              "type" : "text"
            },
            "score" : {
              "type" : "integer"
            },
            "type" : {
              "type" : "keyword"
            }
          }
        },
        "createTimeES" : {
          "type" : "date",
          "format" : "yyyy-MM-dd HH:mm:ss"
        },
        "embedding" : {
          "type" : "dense_vector",
          "dims" : 1536
        },
        "eventInfo" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "imgs" : {
          "type" : "text"
        },
        "institutionalEntities" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "locationEntity" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "new_author" : {
          "type" : "keyword"
        },
        "new_site_name" : {
          "type" : "keyword"
        },
        "publishtime" : {
          "type" : "date",
          "format" : "yyyy-MM-dd HH:mm:ss"
        },
        "reply" : {
          "type" : "keyword"
        },
        "site_area_code" : {
          "type" : "keyword"
        },
        "site_id" : {
          "type" : "keyword"
        },
        "site_level" : {
          "type" : "keyword"
        },
        "site_media_nature" : {
          "type" : "keyword"
        },
        "site_name" : {
          "type" : "text"
        },
        "source" : {
          "type" : "keyword"
        },
        "summary" : {
          "type" : "text"
        },
        "summaryFacts" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "tag" : {
          "type" : "keyword"
        },
        "title" : {
          "type" : "keyword",
          "ignore_above" : 256
        },
        "url" : {
          "type" : "keyword",
          "ignore_above" : 256
        }
      }
    }
  }





pro_mcp_data_complaint_v1
  {
    "mappings": {
      "dynamic": "strict",
      "properties": {
        "appeal": {
          "type": "text",
          "analyzer": "ik_max_word"
        },
        "applbasques": {
          "type": "keyword"
        },
        "area": {
          "type": "text",
          "analyzer": "ik_max_word"
        },
        "author": {
          "type": "text",
          "analyzer": "ik_max_word"
        },
        "author_avatar": {
          "type": "keyword",
          "index": false
        },
        "collective_amount": {
          "type": "integer",
          "null_value": 0
        },
        "collective_per": {
          "type": "keyword",
          "index": false
        },
        "collective_sn": {
          "type": "keyword"
        },
        "comment_amount": {
          "type": "integer",
          "null_value": 0
        },
        "comment_id": {
          "type": "text",
          "analyzer": "ik_max_word"
        },
        "cost": {
          "type": "double",
          "null_value": 0.0
        },
        "cotitle": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 2000
            }
          },
          "analyzer": "ik_max_word"
        },
        "couid": {
          "type": "keyword"
        },
        "cp_type": {
          "type": "integer",
          "null_value": 0
        },
        "crawler_interval": {
          "type": "long"
        },
        "crawler_time": {
          "type": "date",
          "format": "yyyy-MM-dd HH:mm:ss",
          "null_value": "9999-01-10 00:00:00"
        },
        "created_at": {
          "type": "integer",
          "null_value": 0
        },
        "handle_depart": {
          "type": "text",
          "analyzer": "ik_max_word"
        },
        "handle_result": {
          "type": "text",
          "analyzer": "ik_max_word"
        },
        "id": {
          "type": "integer"
        },
        "indtypes": {
          "type": "keyword"
        },
        "industry": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "issue": {
          "type": "text",
          "analyzer": "ik_max_word"
        },
        "last_gather_comment_time": {
          "type": "date",
          "format": "yyyy-MM-dd HH:mm:ss",
          "null_value": "9999-01-10 00:00:00"
        },
        "last_gather_default_time": {
          "type": "date",
          "format": "yyyy-MM-dd HH:mm:ss",
          "null_value": "9999-01-10 00:00:00"
        },
        "last_gather_jiti_time": {
          "type": "date",
          "format": "yyyy-MM-dd HH:mm:ss",
          "null_value": "9999-01-10 00:00:00"
        },
        "last_gather_time": {
          "type": "date",
          "format": "yyyy-MM-dd HH:mm:ss",
          "null_value": "9999-01-10 00:00:00"
        },
        "last_gather_time _2": {
          "type": "date",
          "format": "yyyy-MM-dd HH:mm:ss",
          "null_value": "9999-01-10 00:00:00"
        },
        "meger_data_time": {
          "type": "date",
          "format": "yyyy-MM-dd HH:mm:ss",
          "null_value": "9999-01-10 00:00:00"
        },
        "obj_id": {
          "type": "keyword"
        },
        "product_model": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 2000
            }
          },
          "analyzer": "ik_max_word"
        },
        "product_type": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 2000
            }
          },
          "analyzer": "ik_max_word"
        },
        "satisfaction_level": {
          "type": "integer"
        },
        "service_used": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 2000
            }
          },
          "analyzer": "ik_max_word"
        },
        "share_amount": {
          "type": "integer",
          "null_value": 0
        },
        "site_name": {
          "type": "text",
          "analyzer": "ik_max_word"
        },
        "sn": {
          "type": "keyword"
        },
        "status": {
          "type": "integer",
          "null_value": 0
        },
        "summary": {
          "type": "text",
          "analyzer": "ik_max_word"
        },
        "title": {
          "type": "text",
          "analyzer": "ik_max_word"
        },
        "update_time": {
          "type": "date",
          "format": "yyyy-MM-dd HH:mm:ss",
          "null_value": "9999-01-10 00:00:00"
        },
        "upvote_amount": {
          "type": "integer",
          "null_value": 0
        },
        "url": {
          "type": "keyword",
          "index": false
        },

        "core_issue": {
          "type": "text",
          "analyzer": "ik_max_word",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 1024 
            }
          }
        },
        "product_service_involved": {
          "type": "text",
          "analyzer": "ik_max_word",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "user_demand": {
          "type": "text",
          "analyzer": "ik_max_word",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 512
            }
          }
        },
        "sentiment": { 
          "type": "keyword"
        },
        "severity": {
          "type": "keyword"
        },
        "potential_risks": {
          "type": "keyword"
        },
        "potential_causes": { 
          "type": "keyword"
        },
        "improvement_suggestions": {
          "type": "text",
          "analyzer": "ik_max_word"
        },
        "complaint_category": {
          "type": "keyword"
        },
        "keywords": { 
          "type": "keyword"
        },
        "embedding" : {
          "type" : "dense_vector",
          "dims" : 1536
        }
      }
    }
  }