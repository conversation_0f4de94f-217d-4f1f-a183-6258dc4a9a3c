from mongoengine import Document, <PERSON><PERSON>ield, <PERSON><PERSON>ield, DateT<PERSON>Field
from datetime import datetime
from pydantic import BaseModel
from typing import Optional, List

class ConsumerProtectionRule(Document):
    meta = {
        'collection': 'consumer_protection_rules'
    }
    ruleName = StringField(required=True)         # 规则名称
    ruleType = StringField(required=True)         # 规则类型
    description = StringField(required=True)      # 描述
    source = StringField(required=True)           # 来源
    logic = StringField(required=True)            # 分析逻辑
    result_definition = ListField(StringField())  # 分析结果定义
    created_at = DateTimeField(default=datetime.now)  # 创建时间
    updated_at = DateTimeField(default=datetime.now)  # 更新时间

# 定义请求和响应模型
class ConsumerProtectionRuleCreate(BaseModel):
    ruleName: str
    ruleType: str
    description: str
    source: str
    logic: str
    result_definition: List[str]

class ConsumerProtectionRuleUpdate(BaseModel):
    ruleName: Optional[str] = None
    ruleType: Optional[str] = None
    description: Optional[str] = None
    source: Optional[str] = None
    logic: Optional[str] = None
    result_definition: Optional[List[str]] = None

class ConsumerProtectionRuleResponse(BaseModel):
    id: str
    ruleName: str
    ruleType: str
    description: str
    source: str
    logic: str
    result_definition: List[str]
    created_at: Optional[str] = None
    updated_at: Optional[str] = None