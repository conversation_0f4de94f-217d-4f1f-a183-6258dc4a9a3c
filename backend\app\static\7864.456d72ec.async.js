"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7864],{37864:function(e,t,n){n.d(t,{Z:function(){return M}});var a=n(87462),r=n(93967),o=n.n(r),i=n(67294),l=n(68997),s=n(21450),c=n(36158),d=n(8410);function u(e){return"string"==typeof e}var m=(e,t,n,a)=>{const[r,o]=i.useState(""),[l,s]=i.useState(1),c=t&&u(e);(0,d.Z)((()=>{o(e),!c&&u(e)?s(e.length):u(e)&&u(r)&&0!==e.indexOf(r)&&s(1)}),[e]),i.useEffect((()=>{if(c&&l<e.length){const e=setTimeout((()=>{s((e=>e+n))}),a);return()=>{clearTimeout(e)}}}),[l,t,e]);return[c?e.slice(0,l):e,c&&l<e.length]};var f=function(e){return i.useMemo((()=>{if(!e)return[!1,0,0,null];let t={step:1,interval:50,suffix:null};return"object"==typeof e&&(t={...t,...e}),[!0,t.step,t.interval,t.suffix]}),[e])};var p=({prefixCls:e})=>i.createElement("span",{className:`${e}-dot`},i.createElement("i",{className:`${e}-dot-item`,key:"item-1"}),i.createElement("i",{className:`${e}-dot-item`,key:"item-2"}),i.createElement("i",{className:`${e}-dot-item`,key:"item-3"})),g=n(11568),h=n(83262),y=n(43495);const v=e=>{const{componentCls:t,paddingSM:n,padding:a}=e;return{[t]:{[`${t}-content`]:{"&-filled,&-outlined,&-shadow":{padding:`${(0,g.bf)(n)} ${(0,g.bf)(a)}`,borderRadius:e.borderRadiusLG},"&-filled":{backgroundColor:e.colorFillContent},"&-outlined":{border:`1px solid ${e.colorBorderSecondary}`},"&-shadow":{boxShadow:e.boxShadowTertiary}}}}},b=e=>{const{componentCls:t,fontSize:n,lineHeight:a,paddingSM:r,padding:o,calc:i}=e,l=`${t}-content`;return{[t]:{[l]:{"&-round":{borderRadius:{_skip_check_:!0,value:i(n).mul(a).div(2).add(r).equal()},paddingInline:i(o).mul(1.25).equal()}},[`&-start ${l}-corner`]:{borderStartStartRadius:e.borderRadiusXS},[`&-end ${l}-corner`]:{borderStartEndRadius:e.borderRadiusXS}}}};var $=e=>{const{componentCls:t,padding:n}=e;return{[`${t}-list`]:{display:"flex",flexDirection:"column",gap:n,overflowY:"auto"}}};const x=new g.E4("loadingMove",{"0%":{transform:"translateY(0)"},"10%":{transform:"translateY(4px)"},"20%":{transform:"translateY(0)"},"30%":{transform:"translateY(-4px)"},"40%":{transform:"translateY(0)"}}),E=new g.E4("cursorBlink",{"0%":{opacity:1},"50%":{opacity:0},"100%":{opacity:1}}),S=e=>{const{componentCls:t,fontSize:n,lineHeight:a,paddingSM:r,colorText:o,calc:i}=e;return{[t]:{display:"flex",columnGap:r,[`&${t}-end`]:{justifyContent:"end",flexDirection:"row-reverse",[`& ${t}-content-wrapper`]:{alignItems:"flex-end"}},[`&${t}-rtl`]:{direction:"rtl"},[`&${t}-typing ${t}-content:last-child::after`]:{content:'"|"',fontWeight:900,userSelect:"none",opacity:1,marginInlineStart:"0.1em",animationName:E,animationDuration:"0.8s",animationIterationCount:"infinite",animationTimingFunction:"linear"},[`& ${t}-avatar`]:{display:"inline-flex",justifyContent:"center",alignSelf:"flex-start"},[`& ${t}-header, & ${t}-footer`]:{fontSize:n,lineHeight:a,color:e.colorText},[`& ${t}-header`]:{marginBottom:e.paddingXXS},[`& ${t}-footer`]:{marginTop:r},[`& ${t}-content-wrapper`]:{flex:"auto",display:"flex",flexDirection:"column",alignItems:"flex-start",minWidth:0,maxWidth:"100%"},[`& ${t}-content`]:{position:"relative",boxSizing:"border-box",minWidth:0,maxWidth:"100%",color:o,fontSize:e.fontSize,lineHeight:e.lineHeight,minHeight:i(r).mul(2).add(i(a).mul(n)).equal(),wordBreak:"break-word",[`& ${t}-dot`]:{position:"relative",height:"100%",display:"flex",alignItems:"center",columnGap:e.marginXS,padding:`0 ${(0,g.bf)(e.paddingXXS)}`,"&-item":{backgroundColor:e.colorPrimary,borderRadius:"100%",width:4,height:4,animationName:x,animationDuration:"2s",animationIterationCount:"infinite",animationTimingFunction:"linear","&:nth-child(1)":{animationDelay:"0s"},"&:nth-child(2)":{animationDelay:"0.2s"},"&:nth-child(3)":{animationDelay:"0.4s"}}}}}}};var k=(0,y.I$)("Bubble",(e=>{const t=(0,h.IX)(e,{});return[S(t),$(t),v(t),b(t)]}),(()=>({})));const C=i.createContext({}),N=(e,t)=>{const{prefixCls:n,className:r,rootClassName:d,style:u,classNames:g={},styles:h={},avatar:y,placement:v="start",loading:b=!1,loadingRender:$,typing:x,content:E="",messageRender:S,variant:N="filled",shape:w,onTypingComplete:R,header:T,footer:I,...H}=e,{onUpdate:M}=i.useContext(C),X=i.useRef(null);i.useImperativeHandle(t,(()=>({nativeElement:X.current})));const{direction:Z,getPrefixCls:z}=(0,c.Z)(),D=z("bubble",n),_=(0,s.Z)("bubble"),[B,Y,W,F]=f(x),[P,j]=m(E,B,Y,W);i.useEffect((()=>{M?.()}),[P]);const q=i.useRef(!1);i.useEffect((()=>{j||b?q.current=!1:q.current||(q.current=!0,R?.())}),[j,b]);const[G,L,U]=k(D),V=o()(D,d,_.className,r,L,U,`${D}-${v}`,{[`${D}-rtl`]:"rtl"===Z,[`${D}-typing`]:j&&!b&&!S&&!F}),O=i.isValidElement(y)?y:i.createElement(l.Z,y),A=S?S(P):P;let J;J=b?$?$():i.createElement(p,{prefixCls:D}):i.createElement(i.Fragment,null,A,j&&F);let K=i.createElement("div",{style:{..._.styles.content,...h.content},className:o()(`${D}-content`,`${D}-content-${N}`,w&&`${D}-content-${w}`,_.classNames.content,g.content)},J);return(T||I)&&(K=i.createElement("div",{className:`${D}-content-wrapper`},T&&i.createElement("div",{className:o()(`${D}-header`,_.classNames.header,g.header),style:{..._.styles.header,...h.header}},T),K,I&&i.createElement("div",{className:o()(`${D}-footer`,_.classNames.footer,g.footer),style:{..._.styles.footer,...h.footer}},I))),G(i.createElement("div",(0,a.Z)({style:{..._.style,...u},className:V},H,{ref:X}),y&&i.createElement("div",{style:{..._.styles.avatar,...h.avatar},className:o()(`${D}-avatar`,_.classNames.avatar,g.avatar)},O),K))};var w=i.forwardRef(N),R=n(56790),T=n(64217);const I=(e,t)=>{const{prefixCls:n,rootClassName:r,className:l,items:s,autoScroll:d=!0,roles:u,...m}=e,f=(0,T.Z)(m,{attr:!0,aria:!0}),p=i.useRef(null),g=i.useRef({}),{getPrefixCls:h}=(0,c.Z)(),y=h("bubble",n),v=`${y}-list`,[b,$,x]=k(y),[E,S]=i.useState(!1);i.useEffect((()=>(S(!0),()=>{S(!1)})),[]);const N=function(e,t){const n=i.useCallback((e=>"function"==typeof t?t(e):t&&t[e.role]||{}),[t]);return i.useMemo((()=>(e||[]).map(((e,t)=>{const a=e.key??`preset_${t}`;return{...n(e),...e,key:a}}))),[e,n])}(s,u),[I,H]=function(e){const[t,n]=i.useState(e.length),a=i.useMemo((()=>e.slice(0,t)),[e,t]),r=i.useMemo((()=>{const e=a[a.length-1];return e?e.key:null}),[a]);i.useEffect((()=>{if(!a.length||!a.every(((t,n)=>t.key===e[n]?.key)))if(0===a.length)n(1);else for(let t=0;t<a.length;t+=1)if(a[t].key!==e[t]?.key){n(t);break}}),[e]);const o=(0,R.zX)((e=>{e===r&&n(t+1)}));return[a,o]}(N),[M,X]=i.useState(!0),[Z,z]=i.useState(0);i.useEffect((()=>{d&&p.current&&M&&p.current.scrollTo({top:p.current.scrollHeight})}),[Z]),i.useEffect((()=>{if(d){const e=I[I.length-2]?.key,t=g.current[e];if(t){const{nativeElement:e}=t,{top:n,bottom:a}=e.getBoundingClientRect(),{top:r,bottom:o}=p.current.getBoundingClientRect();n<o&&a>r&&(z((e=>e+1)),X(!0))}}}),[I.length]),i.useImperativeHandle(t,(()=>({nativeElement:p.current,scrollTo:({key:e,offset:t,behavior:n="smooth",block:a})=>{if("number"==typeof t)p.current.scrollTo({top:t,behavior:n});else if(void 0!==e){const t=g.current[e];if(t){const r=I.findIndex((t=>t.key===e));X(r===I.length-1),t.nativeElement.scrollIntoView({behavior:n,block:a})}}}})));const D=(0,R.zX)((()=>{d&&z((e=>e+1))})),_=i.useMemo((()=>({onUpdate:D})),[]);return b(i.createElement(C.Provider,{value:_},i.createElement("div",(0,a.Z)({},f,{className:o()(v,r,l,$,x,{[`${v}-reach-end`]:M}),ref:p,onScroll:e=>{const t=e.target;X(t.scrollHeight-Math.abs(t.scrollTop)-t.clientHeight<=1)}}),I.map((({key:e,...t})=>i.createElement(w,(0,a.Z)({},t,{key:e,ref:t=>{t?g.current[e]=t:delete g.current[e]},typing:!!E&&t.typing,onTypingComplete:()=>{t.onTypingComplete?.(),H(e)}})))))))};var H=i.forwardRef(I);w.List=H;var M=w}}]);