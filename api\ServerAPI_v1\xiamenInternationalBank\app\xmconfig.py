import os
from pathlib import Path
from urllib.parse import quote_plus
from dotenv import load_dotenv
from .logging_config import get_logger

logger = get_logger(__name__)

class Settings:
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._initialize()
    
    def _find_env_file(self):
        """查找环境配置文件"""
        env_mode = os.getenv('ENV_MODE', 'production')
        
        # 获取项目根目录（从当前文件向上查找到包含 docker-compose.yml 的目录）
        current_path = Path(__file__).resolve()
        root_dir = current_path.parent.parent  # 项目根目录应该是 xiamenInternationalBank
        
        # 按优先级依次查找配置文件
        env_files = [
            root_dir / f'.env.{env_mode}',  # 1. 项目根目录下的环境特定配置
            root_dir / '.env',              # 2. 项目根目录下的默认配置
            current_path.parent / f'.env.{env_mode}',  # 3. app目录下的环境特定配置
            current_path.parent / '.env',    # 4. app目录下的默认配置
        ]
        
        for env_file in env_files:
            if env_file.exists():
                logger.info(f"找到配置文件: {env_file}")
                return str(env_file)
        
        # 如果找不到任何配置文件，使用默认配置
        logger.warning("未找到配置文件，使用默认配置")
        return None
    
    def _initialize(self):
        """初始化配置"""
        logger.info("开始加载环境配置")
        
        try:
            env_file = self._find_env_file()
            if env_file:
                load_dotenv(env_file)
            
            # PostgreSQL 配置
            self.POSTGRES_USER = os.getenv('POSTGRES_USER', 'username')
            self.POSTGRES_PASSWORD = os.getenv('POSTGRES_PASSWORD', 'password')
            self.POSTGRES_HOST = os.getenv('POSTGRES_HOST', 'localhost')
            self.POSTGRES_PORT = int(os.getenv('POSTGRES_PORT', '5432'))
            self.POSTGRES_DB = os.getenv('POSTGRES_DB', 'postgres')
            self.POSTGRES_SCHEMA = os.getenv('POSTGRES_SCHEMA', 'public')
            
            # 连接池配置
            self.POSTGRES_POOL_MIN_SIZE = int(os.getenv('POSTGRES_POOL_MIN_SIZE', '10'))
            self.POSTGRES_POOL_MAX_SIZE = int(os.getenv('POSTGRES_POOL_MAX_SIZE', '100'))
            self.POSTGRES_COMMAND_TIMEOUT = int(os.getenv('POSTGRES_COMMAND_TIMEOUT', '60'))
            self.POSTGRES_TIMEOUT = int(os.getenv('POSTGRES_TIMEOUT', '60'))
            
            # API 配置
            self.API_HOST = os.getenv('API_HOST', '0.0.0.0')
            self.API_PORT = int(os.getenv('API_PORT', '8000'))
            self.API_DEBUG = os.getenv('API_DEBUG', 'true').lower() == 'true'
            
            # 向量数据库配置
            self.VECTOR_DIMENSION = int(os.getenv('VECTOR_DIMENSION', '1536'))  # OpenAI 默认维度
            self.VECTOR_SIMILARITY_THRESHOLD = float(os.getenv('VECTOR_SIMILARITY_THRESHOLD', '0.8'))
            
            self._initialized = True
            logger.info("环境配置加载完成")
            
            # 打印当前配置（仅在调试模式）
            if self.API_DEBUG:
                self._log_config()
                
        except Exception as e:
            logger.error(f"加载配置时发生错误: {e}")
            raise
    
    def _log_config(self):
        """打印配置信息（敏感信息会被遮掩）"""
        logger.debug("当前配置:")
        for key, value in self.__dict__.items():
            if not key.startswith('_'):  # 跳过私有属性
                if 'PASSWORD' in key:
                    logger.debug(f"{key}: ****")
                else:
                    logger.debug(f"{key}: {value}")
    
    @property
    def DATABASE_URL(self) -> str:
        """构建 PostgreSQL 连接 URL"""
        return f"postgresql://{self.POSTGRES_USER}:{quote_plus(self.POSTGRES_PASSWORD)}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"

# 创建全局配置实例
settings = Settings()
