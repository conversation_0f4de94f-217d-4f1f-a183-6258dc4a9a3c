from mongoengine import Document, <PERSON><PERSON><PERSON>, DateTimeField, <PERSON><PERSON>an<PERSON>ield, IntField, ObjectIdField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional
from bson import ObjectId

# MongoEngine 模型
class Doc(Document):
    meta = {
        'collection': 'docs'
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId)
    title = StringField(required=True)     # 文档标题
    content = StringField(required=True)    # 文档内容
    password = StringField()               # 访问密码
    created_at = DateTimeField(default=datetime.now)
    updated_at = DateTimeField(default=datetime.now)
    user_id = IntField(required=True)      # 创建者ID
    user_name = StringField()              # 创建者名称
    is_active = BooleanField(default=True) # 是否可用
    view_count = IntField(default=0)       # 查看次数
    deleted_by = IntField()                # 删除者ID

# Pydantic 模型
class DocBase(BaseModel):
    title: str
    content: str
    password: Optional[str] = None

class DocCreate(DocBase):
    pass

class DocUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None

class DocResponse(DocBase):
    id: str
    created_at: datetime
    updated_at: datetime
    user_name: Optional[str] = None
    view_count: int = 0

    class Config:
        from_attributes = True 