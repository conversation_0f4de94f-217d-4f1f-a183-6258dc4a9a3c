from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from typing import TypedDict
from enum import Enum
from datetime import datetime
import asyncio
from concurrent.futures import ThreadPoolExecutor
import traceback
from app.utils.logging_config import setup_logging, get_logger
from app.models.embedding import Embedding<PERSON>odel, EmbeddingCreate, EmbeddingUpdate, EmbeddingResponse
from app.db.mongodb import db
from bson import ObjectId
from app.engines.embedding.embedding_utils import get_embedding
from app.engines.retrieval.base_retriever import es_vector_retriever
from app.utils.config import settings
from app.utils.enums import chunk_type, RAGSearchType
from app.engines.wisegraph.example import graph_retrieval_for_wiseagent_sync, graph_retrieval_for_wiseagent
from app.engines.wisegraph.graph_retriever import neo4j

# from dotenv import load_dotenv

# load_dotenv()
import os

# 设置日志
setup_logging()
logger = get_logger(__name__)



class SearchRetrievalResult(TypedDict):
     score: float
     chunk_id: str
     search_type: RAGSearchType

class GraphRetrievalResult(TypedDict):
    """图检索专用结果类型"""
    content: str
    search_type: RAGSearchType
    entities_found: List[str]
    triplets_count: int
    chunk_ids: List[str]

class SearchResult(TypedDict):
    # index_content: str # index_content
    score: float
    # rerank_score: float
    id: str
    source_id: str
    source_name: str
    knowledge_base_id: str
    chunk_index: int
    answer: str
    question: str
    created_at: datetime
    search_type: RAGSearchType  # 新增检索类型字段

class SearchConfig(TypedDict):
    top_k: int
    final_top_k: int
    rerank_config: Dict[str, Any]

async def parallel_knowledge_search(
    query: str,
    knowledge_base_ids: List[str],
    config: SearchConfig,
    thread_pool_executor: Optional[ThreadPoolExecutor] = None
) -> Dict[str, List[SearchResult]]:
    """
    并行从多个知识库中检索知识
    
    Args:
        query: 查询文本
        knowledge_base_ids: 知识库ID列表
        config: 检索配置
        thread_pool_executor: 线程池执行器，如果为None则创建新的
    
    Returns:
        Dict[str, List[SearchResult]]: 包含向量搜索和图搜索结果的字典
    """
    logger.info('执行并行知识库检索')
    logger.info(f"查询: {query}")
    logger.info(f"知识库IDs: {knowledge_base_ids}")
    
    # 如果没有提供线程池，创建一个新的
    if thread_pool_executor is None:
        thread_pool_executor = ThreadPoolExecutor(max_workers=len(knowledge_base_ids))
    
    try:
        knowledge_base_ids = [ObjectId(kb_id) for kb_id in knowledge_base_ids]
        # 获取知识库配置信息
        knowledge_bases = await db.knowledge_bases.find({
            "_id": {"$in": knowledge_base_ids}
        }).to_list(length=None)
        logger.info(f"知识库配置信息: {knowledge_bases}")
        # 构建知识库ID到配置的映射
        # kb_config_map = {str(kb["_id"]): kb for kb in knowledge_bases}
        
        loop = asyncio.get_running_loop()
        tasks = []
        
        for kb in knowledge_bases:            
            # 根据知识库配置决定检索类型
            if kb.get("basic_index"):
                embedding_id = kb["embedding_model_id"]
                if not embedding_id:
                    logger.error(f"知识库没有设置embedding_model_id")
                    continue
                embedding_id = int(embedding_id)
                embedding_config = await db.embeddings.find_one({"id": embedding_id})
                if not embedding_config:
                    logger.error(f"知识库的embedding_model_id {embedding_id} 不存在")
                    continue
                else:
                    logger.info(f"---------------->embedding_config: {embedding_config}")

                tasks.append(
                    loop.run_in_executor(
                        thread_pool_executor,
                        vector_search_for_kb,
                        query,
                        kb,
                        embedding_config,
                        config.get("top_k", 10)
                    )
                )
            if kb.get("graph_index"):
                # 直接添加异步任务，避免线程池和事件循环冲突
                tasks.append(
                    graph_search_for_kb_async(
                        query,
                        kb,
                        config.get("top_k", 10)
                    )
                )
            if kb.get("semantic_index"):
                tasks.append(
                    loop.run_in_executor(
                        thread_pool_executor,
                        semantic_search_for_kb,
                        query,
                        str(kb["_id"]),
                        config.get("top_k", 10)
                    )
                )
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks)
        logger.info("========检索结果===========")
        logger.info(f"results: {results}")
        
        # 将嵌套结果拉平为一维列表
        flattened_results = [item for sublist in results for item in sublist]
        # logger.info(f"flattened_results: {flattened_results}")
        
        # # 合并结果时根据实际任务类型分类
        vector_results: List[SearchRetrievalResult] = []
        graph_results: List[GraphRetrievalResult] = []
        semantic_results: List[SearchRetrievalResult] = []
        
        for item in flattened_results:
            if not item:
                continue
            # 根据每个条目的search_type判断类型
            if item.get("search_type") == RAGSearchType.VECTOR:
                vector_results.append(item)
            elif item.get("search_type") == RAGSearchType.GRAPH:
                graph_results.append(item)
            elif item.get("search_type") == RAGSearchType.SEMANTIC:
                semantic_results.append(item)

        logger.info(f"vector_results: {vector_results}")
        logger.info(f"graph_results: {graph_results}")
        logger.info(f"semantic_results: {semantic_results}")
        re_list = []
        graph_list = []
        if len(vector_results) > 0:
            # 创建chunk_id到score的映射字典
            score_map = {chunk["chunk_id"]: chunk["score"] for chunk in vector_results}
            # 使用集合去重
            ids = list({ObjectId(chunk["chunk_id"]) for chunk in vector_results})
            chunks_info = await db.chunks.find({"_id": {"$in": ids}}).to_list(length=None)
            logger.info(f"chunks_info: {chunks_info}")

            re_list = []
            for chunk in chunks_info:
                # 从映射字典中获取对应的score
                source_name = ''
                if "metadata" in chunk and "file_name" in chunk["metadata"]:
                    source_name = chunk["metadata"]["file_name"]
                score = score_map.get(str(chunk["_id"]), 0.0)  # 默认值设为0.0防止找不到
                re_list.append({
                    "role": "system",
                    "content": f"知识片段：{chunk['chunk_index']}",
                    "score": score,    
                    # "rerank_score": chunk["rerank_score"],
                    "id": str(chunk["_id"]),
                    "sourceId": str(chunk["file_id"]) if "file_id" in chunk else "",
                    "collectionId": str(chunk["file_id"]) if "file_id" in chunk else "",
                    "source_name": str(chunk["file_name"]) if "file_name" in chunk else source_name,
                    "datasetId": str(chunk["knowledge_base_id"]) if "knowledge_base_id" in chunk else "",
                    "a": chunk["answer"] if "answer" in chunk else "",
                    "q": chunk["question"] if "question" in chunk else "",
                    # "created_at": chunk["created_at"],
                    "search_type": RAGSearchType.VECTOR,
                    "tokens_count": chunk["tokens_count"] if "tokens_count" in chunk else len(chunk["answer"]) + len(chunk["question"])
                })
        logger.info(f"图检索实现")
        if len(graph_results) > 0:
            # 处理图检索结果，将所有图检索结果放在一个集合中
            graph_list.append({
                "graph_context": graph_results,
                "role":"system",
                "content":"testgraph"
            })
            
        logger.info(f"语义检索待实现")
        logger.info(f"返回结果: {re_list,graph_list}")
        return re_list,graph_list
        
    except Exception as e:
        traceback.print_exc()
        logger.error(f"并行检索失败: {str(e)}")
        raise e
        # return {
        #     "vector_results": [],
        #     "graph_results": [],
        #     "semantic_results": [],
        #     "error": str(e)
        # }

def vector_search_for_kb(query: str, kb_info=None, embedding_config=None, top_k: int=5) -> List[SearchRetrievalResult]:
    """向量搜索实现 基于每个知识库的向量搜索"""
    logger.info(f"---------------->kb_info: {kb_info}")
    try:
        embedding = get_embedding(query, embedding_config)
        
        if embedding and len(embedding) < 1536:
            embedding.extend([0] * (1536 - len(embedding)))
            logger.info(f"embedding 长度: {len(embedding)}")
        logger.info(f"embedding: {len(embedding)}")
        # 检索es 
        # TODO 检索es 
        es_host = settings.ES_HOST
        index_name = settings.ES_INDEX
        logger.info(f"es_host: {es_host}, index_name: {index_name}")

        chunks = es_vector_retriever(embedding,chunk_type.BASE, str(kb_info["_id"]), top_k, threshold=0.65, es_host=es_host, index_name=index_name)
        
        return chunks
        
    except Exception as e:
        traceback.print_exc()
        logger.error(f"向量搜索失败: {str(e)}")
        return []

async def graph_search_for_kb_async(query: str, kb_info=None, top_k: int=5) -> List[GraphRetrievalResult]:
    """图搜索异步实现（直接调用异步版本，避免事件循环冲突）"""
    logger.info(f'执行图搜索（异步），知识库ID: {kb_info}')
    try:
        kb_id = kb_info["_id"] if isinstance(kb_info, dict) else kb_info

        # 直接调用异步版本，在同一事件循环中运行，传入全局retriever实例
        graph_result = await graph_retrieval_for_wiseagent(neo4j, query, str(kb_id))

        # 处理返回结果，确保符合GraphRetrievalResult类型
        if graph_result and graph_result.get("success"):
            context = graph_result.get("context", "")
            entities_found = graph_result.get("entities_found", [])
            triplets_count = graph_result.get("stats", {}).get("triplets_count", 0)
            chunk_ids = graph_result.get("chunk_ids", [])

            # 返回GraphRetrievalResult格式
            graph_retrieval_result: GraphRetrievalResult = {
                "content": context,
                "search_type": RAGSearchType.GRAPH,
                "entities_found": entities_found,
                "triplets_count": triplets_count,
                "chunk_ids": chunk_ids
            }

            return [graph_retrieval_result]
        else:
            logger.warning(f"图搜索未找到相关内容，知识库ID: {kb_id}")
            return []

    except Exception as e:
        logger.error(f"图搜索失败: {str(e)}")
        return []

def semantic_search_for_kb(query: str, kb_info=None, top_k: int=5) -> List[SearchRetrievalResult]:
    """语义搜索实现"""
    logger.info(f'执行语义搜索，知识库ID: {kb_info}')
    try:
        # TODO: 实现实际的语义搜索逻辑
        return []
    except Exception as e:
        logger.error(f"语义搜索失败: {str(e)}")
        return []




