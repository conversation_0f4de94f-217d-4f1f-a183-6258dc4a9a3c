"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3752],{93933:function(e,t,n){n.d(t,{$Z:function(){return Z},$o:function(){return f},Db:function(){return m},Mw:function(){return u},SJ:function(){return v},X1:function(){return y},Xw:function(){return p},bk:function(){return C},fx:function(){return T},qP:function(){return _},tn:function(){return b},zl:function(){return E}});var r=n(15009),a=n.n(r),o=n(99289),c=n.n(o),s=n(78158),i=n(10981);function u(e){return l.apply(this,arguments)}function l(){return(l=c()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/myConversations",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function p(e){return d.apply(this,arguments)}function d(){return(d=c()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/conversations",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function f(e,t){return x.apply(this,arguments)}function x(){return(x=c()(a()().mark((function e(t,n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/conversationActive/"+t,{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function m(e){return h.apply(this,arguments)}function h(){return(h=c()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/clearConversation/"+t,{method:"PUT"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function v(e){return g.apply(this,arguments)}function g(){return(g=c()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/conversations/"+t,{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(e,t){return k.apply(this,arguments)}function k(){return(k=c()(a()().mark((function e(t,n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/conversations/".concat(t),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e){return w.apply(this,arguments)}function w(){return(w=c()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/message",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Z(e){return j.apply(this,arguments)}function j(){return(j=c()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/messages/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function _(e){return P.apply(this,arguments)}function P(){return(P=c()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/delete_messages",{method:"DELETE",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function C(e){return S.apply(this,arguments)}function S(){return(S=c()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/messages/collected",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function T(e){return D.apply(this,arguments)}function D(){return(D=c()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.N)("/api/app/get_knowledge_bases",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function E(e){return I.apply(this,arguments)}function I(){return(I=c()(a()().mark((function e(t){var n,r;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=(0,i.bW)(),e.next=3,fetch("/api/app/chat/chat2kb",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(n)},body:JSON.stringify(t)});case 3:if((r=e.sent).ok){e.next=6;break}throw new Error("HTTP 错误！状态码：".concat(r.status));case 6:return e.abrupt("return",r);case 7:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},13973:function(e,t,n){n.d(t,{Z:function(){return k}});var r=n(15009),a=n.n(r),o=n(99289),c=n.n(o),s=n(5574),i=n.n(s),u=n(67294),l=n(55102),p=n(2453),d=n(17788),f=n(84567),x=n(78158);function m(e){return h.apply(this,arguments)}function h(){return(h=c()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,x.N)("/api/feedbacks",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var v=n(85893),g=l.Z.TextArea,y=[{label:"回答不准确",value:"inaccurate"},{label:"回答不完整",value:"incomplete"},{label:"理解错误",value:"irrelevant"},{label:"生成幻觉",value:"hallucination"},{label:"内容混乱",value:"error"},{label:"有害内容",value:"harmful"},{label:"其他问题",value:"other"}],k=function(e){var t=e.visible,n=e.messageId,r=e.conversationId,o=e.appInfo,s=e.onClose,l=u.useState(""),x=i()(l,2),h=x[0],k=x[1],b=u.useState([]),w=i()(b,2),Z=w[0],j=w[1],_=function(){var e=c()(a()().mark((function e(){var t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==Z.length){e.next=3;break}return p.ZP.error("请至少选择一个反馈类型"),e.abrupt("return");case 3:return e.prev=3,t={message_id:n,conversation_id:r,app_info:o,content:h,feedback_types:Z},console.log("feedbackData===>",t),e.next=8,m(t);case 8:e.sent.success?(p.ZP.success("感谢您的反馈！"),P()):p.ZP.error("提交反馈失败，请稍后重试"),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(3),console.error("提交反馈失败:",e.t0),p.ZP.error("提交反馈失败，请稍后重试");case 16:case"end":return e.stop()}}),e,null,[[3,12]])})));return function(){return e.apply(this,arguments)}}(),P=function(){k(""),j([]),s()};return(0,v.jsxs)(d.Z,{title:"反馈问题",open:t,onOk:_,onCancel:P,okText:"提交",cancelText:"取消",children:[(0,v.jsxs)("div",{style:{marginBottom:16},children:[(0,v.jsx)("div",{style:{marginBottom:8},children:"请选择存在的问题（可多选）："}),(0,v.jsx)(f.Z.Group,{options:y,value:Z,onChange:function(e){return j(e)}})]}),(0,v.jsxs)("div",{children:[(0,v.jsx)("div",{style:{marginBottom:8},children:"详细反馈（选填）："}),(0,v.jsx)(g,{value:h,onChange:function(e){return k(e.target.value)},placeholder:"请输入您的具体反馈意见...",rows:4})]})]})}},95993:function(e,t,n){n.r(t),n.d(t,{default:function(){return we}});var r=n(97857),a=n.n(r),o=n(15009),c=n.n(o),s=n(64599),i=n.n(s),u=n(19632),l=n.n(u),p=n(99289),d=n.n(p),f=n(5574),x=n.n(f),m=n(88310),h=n(17598),v=n(15525),g=n(19050),y=n(19669),k=n(42075),b=n(83062),w=n(83622),Z=n(2487),j=n(67294),_=n(24444),P=(0,_.kc)((function(e){var t=e.token;return{referenceList:{".ant-list-item:first-child":{paddingTop:"0"}},card:{listItemMetaTitle:{color:t.colorTextHeading},".ant-card-meta-title":{marginBottom:"4px","& > a":{display:"inline-block",maxWidth:"100%",color:t.colorTextHeading}},".ant-card-meta-description":{height:"44px",overflow:"hidden",lineHeight:"22px"},"&:hover":{".ant-card-meta-title > a":{color:t.colorPrimary}}},cardItemContent:{display:"flex",height:"20px",marginTop:"16px",marginBottom:"-4px",lineHeight:"20px","& > span":{flex:"1",color:t.colorTextSecondary,fontSize:"12px"}},avatarList:{flex:"0 1 auto"},cardList:{marginTop:"24px"},coverCardList:{".ant-list .ant-list-item-content-single":{maxWidth:"100%"}},toolbar:{boxShadow:"0 1px 4px rgba(0,0,0,0.1)"},listItem:{transition:"all 0.3s ease"}}})),C=n(85893),S=(0,j.forwardRef)((function(e,t){var n=e.messages,r=P().styles,a=(0,j.useState)([]),o=x()(a,2),c=o[0],s=o[1],i=(0,j.useState)(""),u=x()(i,2),l=u[0],p=u[1];(0,j.useEffect)((function(){n&&n.length>0?s(n):s([])}),[n]);var d=j.useState(!1),f=x()(d,2),_=(f[0],f[1]),S=j.useState("all"),T=x()(S,2),D=T[0],E=(T[1],(null==c?void 0:c.filter((function(e){var t="all"===D||e.type===D,n=!l||e.messageId===l;return t&&n})))||[]),I=j.useRef(null),N=(j.useRef(new Map),function(e){console.log("scrollToItem--",e);var t=document.getElementById("content-".concat(e));t&&t.scrollIntoView({behavior:"smooth",block:"center"})});return(0,j.useImperativeHandle)(t,(function(){return{scrollToItem:N,updateReferenceList:function(e){setReferenceList(e)},filterByMessageId:function(e){p(e)},clearFilter:function(){p("")},getFilterMessageId:function(){return l}}})),(0,C.jsxs)("div",{children:[(0,C.jsx)("div",{style:{position:"sticky",top:0,padding:"8px 12px 8px 12px",zIndex:1,borderBottom:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between",alignItems:"center"},children:(0,C.jsxs)(k.Z,{children:[(0,C.jsx)(b.Z,{title:"展开全部",mouseEnterDelay:2,children:(0,C.jsx)(w.ZP,{type:"text",icon:(0,C.jsx)(m.Z,{}),onClick:function(){return _(!0)}})}),(0,C.jsx)(b.Z,{title:"收起全部",mouseEnterDelay:2,children:(0,C.jsx)(w.ZP,{type:"text",icon:(0,C.jsx)(h.Z,{}),onClick:function(){return _(!1)}})}),(0,C.jsx)(b.Z,{title:"滚动到顶部",mouseEnterDelay:2,children:(0,C.jsx)(w.ZP,{type:"text",icon:(0,C.jsx)(v.Z,{}),onClick:function(){I.current&&I.current.scrollTo({top:0,behavior:"smooth"})}})}),(0,C.jsx)(b.Z,{title:"滚动到底部",mouseEnterDelay:2,children:(0,C.jsx)(w.ZP,{type:"text",icon:(0,C.jsx)(g.Z,{}),onClick:function(){I.current&&I.current.scrollTo({top:I.current.scrollHeight,behavior:"smooth"})}})}),l&&(0,C.jsx)(b.Z,{title:"清除筛选",mouseEnterDelay:2,children:(0,C.jsx)(w.ZP,{type:"primary",icon:(0,C.jsx)(y.Z,{}),onClick:function(){return p("")}})})]})}),(0,C.jsx)("div",{ref:I,style:{height:"calc(100vh - 130px)",overflowY:"auto"},children:(0,C.jsx)(Z.Z,{size:"large",className:r.referenceList,rowKey:"id",itemLayout:"vertical",dataSource:E})})]})}));S.displayName="WorkOrderReference";var T=S,D=n(93461),E=n(34114),I=n(78205),N=n(78919),F=n(4628),R=n(9502),z=n(37864),H=n(71471),M=n(2453),O=n(17788),Y=n(74330),L=n(86250),B=n(66309),A=n(55102),W=n(85265),J=n(10048),G=n(10981),q=n(78404),U=n(1832),X=n(14079),K=n(66513),$=n(93045),V=n(71255),Q=n(51042),ee=n(82061),te=n(47389),ne=n(87784),re=n(25820),ae=n(75750),oe=n(12906),ce=n(85175),se=n(43471),ie=n(27484),ue=n.n(ie),le=(0,_.kc)((function(e){var t=e.token;return{reference:{background:"".concat(t.colorBgLayout,"80"),minWidth:"400px",maxWidth:"720px",width:"30%"},layout:{width:"100%",minWidth:"800px",borderRadius:t.borderRadius,display:"flex",background:t.colorBgContainer,fontFamily:"AlibabaPuHuiTi, ".concat(t.fontFamily,", sans-serif"),".ant-prompts":{color:t.colorText},border:"3px solid",borderImage:"linear-gradient(45deg, ".concat(t.colorPrimary,", ").concat(t.colorSplit,") 1")},menu:{height:"100%",display:"flex",flexDirection:"column"},conversations:{padding:"0 12px",flex:1,overflowY:"auto"},chat:{height:"100%",width:"70%",maxWidth:"700px",margin:"0 auto",boxSizing:"border-box",display:"flex",flexDirection:"column",padding:t.paddingLG,gap:"16px"},messages:{flex:1},placeholder:{paddingTop:"32px"},sender:{boxShadow:t.boxShadow},logo:{display:"flex",height:"72px",alignItems:"center",justifyContent:"start",padding:"0 24px",boxSizing:"border-box",img:{width:"24px",height:"24px",display:"inline-block"},span:{display:"inline-block",margin:"0 8px",fontWeight:"bold",color:t.colorText,fontSize:"16px"}},addBtn:{background:"#1677ff0f",border:"1px solid #1677ff34",width:"calc(100% - 24px)",margin:"0 12px 24px 12px"}}})),pe=n(93933),de=n(13973);function fe(e){return e+"-"+Date.now()}var xe=function(e,t){return(0,C.jsxs)(k.Z,{align:"start",children:[e,(0,C.jsx)("span",{children:t})]})},me=[{key:"1",label:xe((0,C.jsx)(U.Z,{style:{color:"#FF4D4F"}}),"常见工单"),description:"快速处理常见工单问题",children:[{key:"1-1",description:"如何处理客户投诉工单？"},{key:"1-2",description:"账户异常工单处理流程？"},{key:"1-3",description:"系统故障工单如何处理？"}]},{key:"2",label:xe((0,C.jsx)(X.Z,{style:{color:"#52C41A"}}),"工单指南"),description:"工单处理标准流程",children:[{key:"2-1",icon:(0,C.jsx)(K.Z,{style:{color:"#722ED1"}}),description:"工单分类与优先级判断"},{key:"2-2",icon:(0,C.jsx)($.Z,{style:{color:"#1890FF"}}),description:"工单回复规范与模板"},{key:"2-3",icon:(0,C.jsx)(V.Z,{style:{color:"#13C2C2"}}),description:"工单升级与转派流程"}]},{key:"3",label:xe((0,C.jsx)(X.Z,{style:{color:"#FA8C16"}}),"业务知识"),description:"快速查找业务知识",children:[{key:"3-1",icon:(0,C.jsx)(K.Z,{style:{color:"#EB2F96"}}),description:"产品功能与使用说明"},{key:"3-2",icon:(0,C.jsx)($.Z,{style:{color:"#F5222D"}}),description:"常见问题解决方案"},{key:"3-3",icon:(0,C.jsx)(V.Z,{style:{color:"#A0D911"}}),description:"业务流程与规范说明"}]}],he=[{key:"historyConversation",description:"历史对话",icon:(0,C.jsx)(X.Z,{style:{color:"#FF4D4F"}})},{key:"newConversation",description:"新建对话",icon:(0,C.jsx)(Q.Z,{style:{color:"#1890FF"}})},{key:"clearConversation",description:"清空对话",icon:(0,C.jsx)(ee.Z,{style:{color:"#1890FF"}})}],ve=(0,G.bG)(),ge=(0,q.kH)(),ye=(0,J.Z)({html:!0,breaks:!0}),ke=function(e){return(0,C.jsx)(H.Z,{style:{marginBottom:0},children:(0,C.jsx)("div",{dangerouslySetInnerHTML:{__html:ye.render(e)}})})},be="workOrderAssistant",we=function(){var e,t=le().styles,n=(0,j.useState)(window.innerHeight),r=x()(n,1)[0],o=j.useRef(),s=j.useState(!1),u=x()(s,2),p=u[0],f=u[1],m=j.useState(""),h=x()(m,2),v=h[0],g=h[1],y=j.useState([]),Z=x()(y,2),_=Z[0],P=Z[1],S=j.useState(),H=x()(S,2),J=H[0],q=H[1],U=(0,j.useState)(!1),X=x()(U,2),K=X[0],$=X[1],V=(0,j.useState)(!1),ie=x()(V,2),xe=ie[0],ye=ie[1],we=(0,j.useState)(!1),Ze=x()(we,2),je=Ze[0],_e=Ze[1],Pe=(0,j.useState)(""),Ce=x()(Pe,2),Se=Ce[0],Te=Ce[1],De=(0,j.useState)(""),Ee=x()(De,2),Ie=Ee[0],Ne=Ee[1],Fe=(0,j.useState)([]),Re=x()(Fe,2),ze=Re[0],He=Re[1],Me=(0,j.useRef)(null),Oe=(0,j.useState)(!1),Ye=x()(Oe,2),Le=Ye[0],Be=Ye[1],Ae=(0,j.useState)(""),We=x()(Ae,2),Je=We[0],Ge=We[1],qe=function(e){Ge(e),Be(!0)},Ue=function(e){var t=ze.find((function(t){return t.message_id===e}));t&&navigator.clipboard.writeText(t.content).then((function(){M.ZP.success("复制成功")})).catch((function(){M.ZP.error("复制失败")}))},Xe=(0,D.Z)({request:(e=d()(c()().mark((function e(t,n){var r,a,s,u,p,d,f,x,m,h,v,g,y,k,b,w,Z,j,_,P,C,S,T,D,E,I,N,F,R,z,H,O,Y,L,B,A,W;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.messages,a=t.message,s=n.onSuccess,u=n.onUpdate,p=n.onError,e.prev=2,!xe){e.next=6;break}return M.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 6:if(ye(!0),f=(0,G.bW)(),x=a?a.id:fe(o.current),a||s({content:"出现了异常:",role:"assistant",id:x,references:[],collected:!1}),m={conversation_id:o.current||"",message_id:x,meta_data:{},extra:{},role:a?a.role:"user",content:a?a.content:"",app_info:be,user_id:null==ve?void 0:ve.id,user_name:null==ve?void 0:ve.name,references:[],token_count:null,price:null,collected:!1,created_at:ue()().format("YYYY-MM-DD HH:mm:ss")},He((function(e){var t=[].concat(l()(e),[m]);return console.log("更新后的消息列表:",t),t})),o.current){e.next=15;break}throw M.ZP.error("No active conversation selected"),new Error("No active conversation selected");case 15:return console.log("activeKey===>",o.current),h={conversation_id:o.current,app_info:be,user_id:null==ve?void 0:ve.id,user_name:null==ve?void 0:ve.name,extra:{},messages:r},v={id:fe(o.current),role:"user",content:"",references:[],collected:!1},g=!1,y="",k=[],u(v),e.next=24,fetch("/api/app/chat/system_app_completions",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(f)},body:JSON.stringify(h)});case 24:if((b=e.sent).ok){e.next=27;break}throw new Error("HTTP 错误！状态码：".concat(b.status));case 27:if(w=null===(d=b.body)||void 0===d?void 0:d.getReader()){e.next=30;break}throw new Error("当前浏览器不支持 ReadableStream。");case 30:Z=new TextDecoder("utf-8"),j={conversation_id:o.current||"",message_id:v.id,meta_data:{},extra:{},role:"assistant",content:"",app_info:be,user_id:null==ve?void 0:ve.id,user_name:null==ve?void 0:ve.name,references:[],token_count:null,price:null,collected:!1,created_at:ue()().format("YYYY-MM-DD HH:mm:ss")};case 32:if(g){e.next=100;break}return e.next=35,w.read();case 35:_=e.sent,P=_.value,_.done&&(g=!0),y+=Z.decode(P,{stream:!0}),C=y.split("\n\n"),y=C.pop()||"",S=i()(C),e.prev=43,S.s();case 45:if((T=S.n()).done){e.next=90;break}if(""!==(D=T.value).trim()){e.next=49;break}return e.abrupt("continue",88);case 49:E=D.split("\n"),I=null,N=null,F=i()(E);try{for(F.s();!(R=F.n()).done;)(z=R.value).startsWith("event: ")?I=z.substring(7).trim():z.startsWith("data: ")&&(N=z.substring(6))}catch(e){F.e(e)}finally{F.f()}if(!N){e.next=88;break}e.t0=I,e.next="answer"===e.t0?58:"moduleStatus"===e.t0?70:"appStreamResponse"===e.t0?72:"flowResponses"===e.t0?74:"end"===e.t0?76:"error"===e.t0?78:88;break;case 58:if("[DONE]"===N){e.next=69;break}e.prev=59,O=JSON.parse(N),(Y=(null===(H=O.choices[0])||void 0===H||null===(H=H.delta)||void 0===H?void 0:H.content)||"")&&(v.content+=Y,u(v)),e.next=69;break;case 65:return e.prev=65,e.t1=e.catch(59),console.error("Error parsing answer data:",e.t1),e.abrupt("return",s({content:"出现了异常:"+N,role:"assistant",id:fe(o.current),references:[],collected:!1}));case 69:return e.abrupt("break",88);case 70:try{L=JSON.parse(N),console.log("模块状态：",L)}catch(e){console.error("Error parsing moduleStatus data:",e)}return e.abrupt("break",88);case 72:try{B=JSON.parse(N),console.log("appStreamData===>",B),k=B,v.references=k}catch(e){console.error("Error parsing appStreamResponse data:",e)}return e.abrupt("break",88);case 74:try{console.log("flowResponsesData",N)}catch(e){console.error("Error parsing flowResponses data:",e)}return e.abrupt("break",88);case 76:return g=!0,e.abrupt("break",88);case 78:e.prev=78,A=JSON.parse(N),u(A),e.next=87;break;case 83:throw e.prev=83,e.t2=e.catch(78),console.error("Error event received:",e.t2),e.t2;case 87:return e.abrupt("break",88);case 88:e.next=45;break;case 90:e.next=95;break;case 92:e.prev=92,e.t3=e.catch(43),S.e(e.t3);case 95:return e.prev=95,S.f(),e.finish(95);case 98:e.next=32;break;case 100:if(s(v),!v.content||""===v.content.trim()){e.next=108;break}return j.content=v.content,j.references=k,e.next=106,(0,pe.tn)(j);case 106:(W=e.sent).success?(j.message_id=W.data.message_id,console.log("创建消息成功，返回数据:",W.data),He((function(e){var t=[].concat(l()(e),[W.data]);return console.log("更新后的消息列表:",t),t}))):M.ZP.error("消息上报失败");case 108:e.next=115;break;case 110:e.prev=110,e.t4=e.catch(2),console.log("error===>",e.t4),s({content:"出现了，系统正在处理其他对话。请稍后重试",role:"assistant",id:fe(o.current),references:[],collected:!1}),p(e.t4 instanceof Error?e.t4:new Error("Unknown error"));case 115:return e.prev=115,ye(!1),e.finish(115);case 118:case"end":return e.stop()}}),e,null,[[2,110,115,118],[43,92,95,98],[59,65],[78,83]])}))),function(t,n){return e.apply(this,arguments)})}),Ke=x()(Xe,1)[0],$e=(0,E.Z)({agent:Ke}),Ve=$e.onRequest,Qe=$e.messages,et=$e.setMessages,tt=function(e){q(e),console.log("activeKey 设置",e),o.current=e},nt=function(e){var t=e.filter((function(e){return e.pinned})).sort((function(e,t){return new Date(t.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return a()(a()({},e),{},{key:e.id||"",label:e.conversation_name||e.id,group:"置顶"})})),n=e.filter((function(e){return!e.pinned})).sort((function(e,t){return new Date(t.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return a()(a()({},e),{},{key:e.id||"",label:e.conversation_name||"",group:"对话"})}));P([].concat(l()(t),l()(n)))},rt=function(e){return 0===e.length?null:e.reduce((function(e,t){return new Date(t.active_at)>new Date(e.active_at)?t:e}))},at=function(){var e=d()(c()().mark((function e(t){var n,r,a;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,ye(!0),console.info("获取对话信息",t),n=ue()().format("YYYY-MM-DD HH:mm:ss"),e.next=6,(0,pe.$o)(t,{conversation_name:null,active_at:n,pinned_at:null,pinned:null});case 6:null!=(r=e.sent)&&r.messages?(console.info("设置对话信息",r.messages),a=r.messages.map((function(e){return{id:e.id||e.message_id,message:{id:e.id||e.message_id,content:e.content,role:e.role,references:e.references||[],collected:e.collected||!1},status:"assistant"===e.role?"success":"local",meta:e.meta||{avatar:"assistant"===e.role?(null==ge?void 0:ge.logo)||"/static/logo.png":(null==ve?void 0:ve.avatar)||"/avatar/default.jpeg"}}})),He(r.messages),et(a),tt(t)):M.ZP.error("获取对话信息失败"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("切换对话时出错：",e.t0);case 13:return e.prev=13,ye(!1),q(t),e.finish(13);case 17:case"end":return e.stop()}}),e,null,[[0,10,13,17]])})));return function(t){return e.apply(this,arguments)}}(),ot=function(){var e=d()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o.current){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,(0,pe.Db)(o.current);case 4:e.sent.success?(He([]),et([]),Me.current&&Me.current.updateReferenceList([])):M.ZP.error("清空对话失败");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ct=function(){var e=d()(c()().mark((function e(){var t,n,r,a;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!xe){e.next=3;break}return M.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(!(t=(0,G.bG)())){e.next=19;break}return e.prev=5,n=(new Date).toLocaleString(),r="对话-".concat(n),e.next=10,(0,pe.Xw)({user_id:t.id,user_name:t.name,conversation_name:r,app_info:be});case 10:a=e.sent,nt([].concat(l()(_),[{key:a.id||"",id:a.id||"",label:a.conversation_name||"",conversation_name:a.conversation_name||"",active_at:a.active_at||"",pinned_at:a.pinned_at,pinned:a.pinned||!1,messages:[]}])),tt(a.id||""),ot(),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(5),console.error("创建新对话时出错：",e.t0);case 19:case"end":return e.stop()}}),e,null,[[5,16]])})));return function(){return e.apply(this,arguments)}}(),st=function(){var e=d()(c()().mark((function e(t){var n,r,o,s,i;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=_.find((function(e){return e.key===t}))){e.next=3;break}return e.abrupt("return");case 3:return r=n.pinned,o=!r,e.prev=6,s=ue()().format("YYYY-MM-DD HH:mm:ss"),e.next=10,(0,pe.X1)(t,{conversation_name:null,active_at:null,pinned:o,pinned_at:s});case 10:i=_.map((function(e){return e.key===t?a()(a()({},e),{},{pinned:o}):e})),nt(i),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),console.error("更新置顶状态时出错：",e.t0);case 17:case"end":return e.stop()}}),e,null,[[6,14]])})));return function(t){return e.apply(this,arguments)}}(),it=function(){var e=d()(c()().mark((function e(t){var n;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,pe.SJ)(t);case 3:n=_.filter((function(e){return e.key!==t})),nt(n),o.current===t&&n.length>0&&at(n[0].key||""),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除对话时出错：",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(t){return e.apply(this,arguments)}}(),ut=function(){var e=d()(c()().mark((function e(t,n){var r,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,_.find((function(e){return e.key===t}))){e.next=4;break}return e.abrupt("return");case 4:return r={conversation_name:n,active_at:null,pinned_at:null,pinned:null},e.next=7,(0,pe.X1)(t,r);case 7:null!=(o=e.sent)&&o.success?P((function(e){return e.map((function(e){return e.key===t?a()(a()({},e),{},{label:n}):e}))})):M.ZP.error("更新对话标题失败"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("更新对话标题时出错：",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t,n){return e.apply(this,arguments)}}(),lt=function(){var e=d()(c()().mark((function e(t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!xe){e.next=3;break}return M.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:return e.next=5,at(t);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();(0,j.useEffect)((function(){var e=function(){var e=d()(c()().mark((function e(){var t,n,r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t=(0,G.bG)())){e.next=20;break}return e.prev=2,e.next=5,(0,pe.Mw)({user_id:t.id,app_info:be});case 5:if(!(n=e.sent).success||!Array.isArray(n.data)){e.next=15;break}if(0!==n.data.length){e.next=12;break}return e.next=10,ct();case 10:e.next=15;break;case 12:r=rt(n.data),nt(n.data),at(r?r.id:n.data[0].id||"");case 15:e.next=20;break;case 17:e.prev=17,e.t0=e.catch(2),console.error("初始化对话时出错：",e.t0);case 20:case"end":return e.stop()}}),e,null,[[2,17]])})));return function(){return e.apply(this,arguments)}}();e()}),[be]);var pt=function(){var e=d()(c()().mark((function e(t){var n,r,a,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("重新生成消息:",t),e.prev=1,console.info(ze),n=ze.findIndex((function(e){return e.message_id===t})),console.log("currentIndex===>",n),-1!==n){e.next=8;break}return M.ZP.error("未找到指定消息"),e.abrupt("return");case 8:return r=ze[n],a=ze.slice(n),console.log("将要删除的消息:",a),e.next=13,(0,pe.qP)(a.map((function(e){return e.message_id})));case 13:e.sent.success||M.ZP.error("删除消息失败"),He((function(e){return e.slice(0,n)})),et((function(e){return e.slice(0,n)})),"assistant"===r.role?(o=ze.slice(0,n).reverse().find((function(e){return"user"===e.role})))&&Ve({id:t,role:"user",content:o.content,references:[],collected:!1}):Ve({id:t,role:"user",content:r.content,references:[],collected:!1}),M.ZP.success("正在重新生成回复..."),e.next=25;break;case 21:e.prev=21,e.t0=e.catch(1),console.error("重新生成消息时出错：",e.t0),M.ZP.error("重新生成消息失败");case 25:case"end":return e.stop()}}),e,null,[[1,21]])})));return function(t){return e.apply(this,arguments)}}(),dt=function(){var e=d()(c()().mark((function e(t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:O.Z.confirm({title:"确认删除",content:"确定要删除这条消息吗？删除后不可恢复。",okText:"确认",cancelText:"取消",onOk:function(){return d()(c()().mark((function e(){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("delete messageId===>",t),e.next=4,(0,pe.$Z)(t);case 4:e.sent.success?(He((function(e){return e.filter((function(e){return e.message_id!==t}))})),console.log("delete currentConversationMessages===>",ze),et((function(e){return e.filter((function(e){return e.message.id!==t}))})),console.log("delete messages===>",Qe),M.ZP.success("消息及相关引用已删除")):M.ZP.error("删除消息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除消息时出错：",e.t0),M.ZP.error("删除消息失败");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ft=function(){var e=d()(c()().mark((function e(t,n){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("收藏状态切换:",t,n),e.next=3,(0,pe.bk)({message_id:t,collected:!n});case 3:e.sent.success?(M.ZP.success(n?"取消收藏成功":"收藏成功"),et((function(e){return e.map((function(e){return e.id===t?a()(a()({},e),{},{message:a()(a()({},e.message),{},{collected:!n})}):e}))})),He((function(e){return e.map((function(e){return e.message_id===t?a()(a()({},e),{},{collected:!n}):e}))}))):M.ZP.error(n?"取消收藏失败":"收藏失败");case 5:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),xt=function(){var e=d()(c()().mark((function e(t){var n,r,a;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!xe){e.next=3;break}return M.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(n=t.data,r=n.key,a=n.description,"historyConversation"!==r){e.next=8;break}$(!0),e.next=19;break;case 8:if("newConversation"!==r){e.next=13;break}return e.next=11,ct();case 11:e.next=19;break;case 13:if("clearConversation"!==r){e.next=18;break}return e.next=16,ot();case 16:e.next=19;break;case 18:Ve({id:fe(o.current),role:"user",content:a,references:[],collected:!1});case 19:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),mt=(0,C.jsxs)(k.Z,{direction:"vertical",size:16,className:t.placeholder,children:[(0,C.jsx)(I.Z,{variant:"borderless",icon:(0,C.jsx)("img",{src:(null==ge?void 0:ge.logo)||"/static/logo.png",alt:"logo"}),title:"您好，我是智能客服助手",description:"我可以为您解答产品咨询、业务办理等相关问题，请问有什么可以帮您？"}),(0,C.jsx)(N.Z,{title:"以下是常见问题，您可以直接点击进行咨询",items:me,styles:{list:{width:"100%"},item:{backgroundImage:"linear-gradient(137deg, #f0f7ff 0%, #fff1f0 100%)",border:0,flex:1}},onItemClick:xt})]}),ht=Qe.length>0?Qe.map((function(e){var t=e.id,n=e.message,r=e.status;return{key:o.current+"_"+t,loadingRender:function(){return(0,C.jsxs)(k.Z,{children:[(0,C.jsx)(Y.Z,{size:"small"}),"模型思考中..."]})},loading:"loading"===r&&n.content.length<1,content:n.content,shape:"local"===r?"corner":"round",variant:"local"===r?"filled":"borderless",rols:n.role,messageRender:ke,avatar:"local"===r?{src:(null==ve?void 0:ve.avatar)||"/avatar/default.jpeg"}:{src:(null==ge?void 0:ge.logo)||"/static/logo.png"},placement:"local"!==r?"start":"end",footer:"local"!==r?(0,C.jsxs)(L.Z,{children:[n.references.length>0&&(0,C.jsxs)(B.Z,{bordered:!1,color:"blue",onClick:function(){return e=n.id,console.log("filterMessageReference===>",e),void(Me.current&&(Me.current.getFilterMessageId()===e?Me.current.clearFilter():Me.current.filterByMessageId(e)));var e},children:["引用:",n.references.length]}),(0,C.jsx)(w.ZP,{size:"small",type:"text",icon:(0,C.jsx)(ee.Z,{style:{color:"#ccc"}}),onClick:function(){return dt(n.id)}}),(0,C.jsx)(w.ZP,{size:"small",type:"text",icon:n.collected?(0,C.jsx)(re.Z,{style:{color:"#FFD700"}}):(0,C.jsx)(ae.Z,{style:{color:"#ccc"}}),onClick:function(){return ft(n.id,n.collected)}}),(0,C.jsx)(w.ZP,{size:"small",type:"text",icon:(0,C.jsx)(oe.Z,{style:{color:"#ccc"}}),onClick:function(){return qe(n.id)}}),(0,C.jsx)(w.ZP,{size:"small",type:"text",icon:(0,C.jsx)(ce.Z,{style:{color:"#ccc"}}),onClick:function(){return Ue(n.id)}})]}):(0,C.jsxs)(L.Z,{children:[(0,C.jsx)(w.ZP,{size:"small",type:"text",icon:(0,C.jsx)(se.Z,{style:{color:"#ccc"}}),onClick:function(){return pt(n.id)}}),(0,C.jsx)(w.ZP,{size:"small",type:"text",icon:(0,C.jsx)(ee.Z,{style:{color:"#ccc"}}),onClick:function(){return dt(n.id)}}),(0,C.jsx)(w.ZP,{size:"small",type:"text",icon:n.collected?(0,C.jsx)(re.Z,{style:{color:"#FFD700"}}):(0,C.jsx)(ae.Z,{style:{color:"#ccc"}}),onClick:function(){return ft(n.id,n.collected)}}),(0,C.jsx)(w.ZP,{size:"small",type:"text",icon:(0,C.jsx)(oe.Z,{style:{color:"#ccc"}}),onClick:function(){return qe(n.id)}}),(0,C.jsx)(w.ZP,{size:"small",type:"text",icon:(0,C.jsx)(ce.Z,{style:{color:"#ccc"}}),onClick:function(){return Ue(n.id)}})]})}})):[{content:mt,variant:"borderless"}],vt=(0,C.jsx)(F.Z.Header,{title:"Attachments",open:p,onOpenChange:f,styles:{content:{padding:0}}}),gt=(0,C.jsxs)("div",{className:t.logo,children:[(0,C.jsx)("span",{children:"对话记录"}),(0,C.jsx)(b.Z,{title:"新对话",children:(0,C.jsx)(w.ZP,{type:"text",icon:(0,C.jsx)(Q.Z,{}),onClick:ct,style:{fontSize:"16px"}})})]}),yt=(0,C.jsx)(O.Z,{title:"修改对话标题",open:je,onOk:function(){Ie&&Se.trim()&&(ut(Ie,Se.trim()),_e(!1))},onCancel:function(){_e(!1),Te(""),Ne("")},children:(0,C.jsx)(A.Z,{value:Se,onChange:function(e){return Te(e.target.value)},placeholder:"请输入新的对话标题"})}),kt=(0,C.jsx)(W.Z,{title:"历史对话",placement:"right",width:400,onClose:function(){return $(!1)},open:K,children:(0,C.jsxs)("div",{className:t.menu,children:[gt,(0,C.jsx)(R.Z,{items:_,activeKey:J,onActiveChange:lt,menu:function(e){return{items:[{label:"重命名",key:"edit",icon:(0,C.jsx)(te.Z,{})},{label:"置顶",key:"pin",icon:(0,C.jsx)(ne.Z,{})},{label:"删除",key:"delete",icon:(0,C.jsx)(ee.Z,{}),danger:!0}],onClick:function(t){switch(console.log("menuInfo","Click ".concat(e.key," - ").concat(t.key)),t.key){case"edit":Ne(e.key),Te(e.label),_e(!0);break;case"pin":st(e.key);break;case"delete":if(xe)return void M.ZP.error("系统正在处理其他对话。请稍😊");it(e.key)}}}},groupable:!0})]})});return(0,j.useEffect)((function(){console.log("currentConversationMessages 更新了:",ze)}),[ze]),(0,C.jsxs)("div",{className:t.layout,style:{height:r-56},children:[(0,C.jsxs)("div",{className:t.chat,children:[(0,C.jsx)(z.Z.List,{items:ht,className:t.messages}),(0,C.jsx)(N.Z,{items:he,onItemClick:xt}),(0,C.jsx)(F.Z,{value:v,header:vt,onSubmit:function(e){console.log("nextContent===>",e),e&&(Ve({id:fe(o.current),role:"user",content:e,references:[],collected:!1}),g(""))},onChange:g,loading:Ke.isRequesting(),className:t.sender})]}),(0,C.jsx)("div",{className:t.reference,children:(0,C.jsx)(T,{ref:Me,messages:ze})}),yt,kt,(0,C.jsx)(de.Z,{visible:Le,messageId:Je,conversationId:J,appInfo:be,onClose:function(){return Be(!1)}})]})}}}]);