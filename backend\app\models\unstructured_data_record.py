from mongoengine import Document, <PERSON><PERSON>ield, DateTimeField, FileField, ObjectIdField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional
from bson import ObjectId

# MongoEngine 模型
class UnstructuredDataRecord(Document):
    meta = {
        'collection': 'unstructured_data_records'
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId)
    dataset_id = ObjectIdField(required=True)  # 关联的非结构化数据集ID
    file_name = StringField(required=True)
    file_data = FileField(required=True)  # 存储文件数据
    content = StringField()  # 新增字段：数据集内容
    created_at = DateTimeField(default=datetime.now)
    updated_at = DateTimeField(default=datetime.now)

# Pydantic 模型
class UnstructuredDataRecordBase(BaseModel):
    dataset_id: str
    file_name: str
    content: Optional[str] = None  # 新增字段：数据集内容
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class UnstructuredDataRecordCreate(UnstructuredDataRecordBase):
    file_data: bytes

class UnstructuredDataRecordUpdate(BaseModel):
    file_name: Optional[str] = None
    content: Optional[str] = None  # 新增字段：数据集内容

class UnstructuredDataRecordResponse(UnstructuredDataRecordBase):
    id: str

    class Config:
        from_attributes = True
