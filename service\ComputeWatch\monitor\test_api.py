import requests
import json
from typing import Dict, Any
import os
from dotenv import load_dotenv
import time
from colorama import init, Fore, Style

# 初始化colorama
init()

# 加载环境变量
load_dotenv()

class APITester:
    def __init__(self):
        self.base_url = "http://218.77.35.16:10086"
        self.api_key = os.getenv("API_KEY", "363326947")
        self.headers = {"api-key": self.api_key}

    def print_json(self, data: Dict):
        """格式化打印JSON数据"""
        print(json.dumps(data, indent=2, ensure_ascii=False))

    def print_colored(self, text: str, color: str = Fore.WHITE, style: str = Style.NORMAL):
        """打印彩色文本"""
        print(f"{style}{color}{text}{Style.RESET_ALL}")

    def test_endpoint(self, endpoint: str, expected_fields: list) -> Dict[str, Any]:
        """测试API端点并验证返回字段"""
        url = f"{self.base_url}{endpoint}"
        try:
            response = requests.get(url, headers=self.headers)
            
            # 检查状态码
            assert response.status_code == 200, f"接口 {endpoint} 返回态码 {response.status_code}"
            
            # 检查返回数据格式
            data = response.json()
            assert isinstance(data, dict), f"接口 {endpoint} 返回数据不是JSON格式"
            
            # 验证基本响应格式
            assert "success" in data, "响应缺少success字段"
            assert "message" in data, "响应缺少message字段"
            assert "data" in data, "响应缺少data字段"
            
            # 如果请求失败，直接返回错误信息
            if not data["success"]:
                return {
                    "status": "error",
                    "endpoint": endpoint,
                    "error": data.get("error", "Unknown error")
                }
            
            # 验证数据字段
            response_data = data["data"]
            for field in expected_fields:
                assert field in response_data, f"接口 {endpoint} 缺少必需字段 {field}"
            
            return {
                "status": "success",
                "endpoint": endpoint,
                "response_time": response.elapsed.total_seconds(),
                "data": data
            }
        except Exception as e:
            return {
                "status": "error",
                "endpoint": endpoint,
                "error": str(e)
            }

    def validate_system_info(self, data: Dict):
        """验证系统信息数据格式"""
        response_data = data["data"]
        required_fields = [
            "os", "os_version", "architecture", "cpu_count",
            "cpu_physical_count", "memory_total", "disk_partitions"
        ]
        for field in required_fields:
            assert field in response_data, f"系统信息缺少字段: {field}"

    def validate_gpu_info(self, data: Dict):
        """验证GPU信息数据格式"""
        response_data = data["data"]
        required_fields = ["available", "devices", "cuda_version", "driver_info"]
        for field in required_fields:
            assert field in response_data, f"GPU信息缺少字段: {field}"
        
        if response_data["available"] and response_data["devices"]:
            gpu = response_data["devices"][0]
            assert all(k in gpu for k in ["id", "name", "memory_total", "uuid"]), "GPU设备信息格式错误"

    def validate_npu_info(self, data: Dict):
        """验证NPU信息数据格式"""
        response_data = data["data"]
        required_fields = ["available", "devices", "driver_version"]
        for field in required_fields:
            assert field in response_data, f"NPU信息缺少字段: {field}"
        
        if response_data["available"] and response_data["devices"]:
            npu = response_data["devices"][0]
            assert all(k in npu for k in ["id", "name", "memory_total"]), "NPU设备信息格式错误"

    def validate_metrics(self, data: Dict):
        """验证系统指标数据格式"""
        response_data = data["data"]
        required_fields = ["cpu", "memory", "disk", "network"]
        for field in required_fields:
            assert field in response_data, f"监控指标缺少字段: {field}"
        
        # 验证CPU信息格式
        assert "percent" in response_data["cpu"], "CPU使用率信息缺失"
        assert "per_cpu" in response_data["cpu"], "每核CPU信息缺失"
        
        # 验证内存信息格式
        assert all(k in response_data["memory"] for k in ["total", "used", "free", "percent"]), "内存信息格式错误"

    def validate_gpu_metrics(self, data: Dict):
        """验证GPU监控指标数据格式"""
        response_data = data["data"]
        required_fields = ["available", "devices"]
        for field in required_fields:
            assert field in response_data, f"GPU指标缺少字段: {field}"
        
        if response_data["available"] and response_data["devices"]:
            gpu = response_data["devices"][0]
            assert all(k in gpu for k in [
                "id", "name", "memory_total", "uuid", "temperature",
                "utilization", "memory_used"
            ]), "GPU指标格式错误"

        # 验证每个字段的数据类型
        if response_data["available"] and response_data["devices"]:
            for gpu in response_data["devices"]:
                assert isinstance(gpu["id"], int), "id必须是整数"
                assert isinstance(gpu["name"], str), "name必须是字符串"
                assert isinstance(gpu["memory_total"], (int, float)), "memory_total必须是数字"
                assert isinstance(gpu["uuid"], str), "uuid必须是字符串"
                assert isinstance(gpu["temperature"], (int, float)), "temperature必须是数字"
                assert isinstance(gpu["utilization"], (int, float)), "utilization必须是数字"
                assert isinstance(gpu["memory_used"], (int, float)), "memory_used必须是数字"
                
                # 验证数值范围
                assert 0 <= gpu["utilization"] <= 100, "utilization必须在0-100之间"
                assert gpu["memory_used"] <= gpu["memory_total"], "已用显存不能超过总显存"

    def validate_npu_metrics(self, data: Dict):
        """验证NPU监控指标数据格式"""
        response_data = data["data"]
        required_fields = ["available", "devices"]
        for field in required_fields:
            assert field in response_data, f"NPU指标缺少字段: {field}"
        
        if response_data["available"] and response_data["devices"]:
            npu = response_data["devices"][0]
            assert all(k in npu for k in [
                "id", "utilization", "memory_used", "memory_total",
                "power", "temperature"
            ]), "NPU指标格式错误"

    def run_all_tests(self):
        """运行所有API测试"""
        test_cases = [
            {
                "endpoint": "/system/info",
                "expected_fields": [
                    "os", "os_version", "architecture", "cpu_count",
                    "cpu_physical_count", "memory_total", "disk_partitions"
                ],
                "validator": self.validate_system_info
            },
            {
                "endpoint": "/system/metrics",
                "expected_fields": ["cpu", "memory", "disk", "network"],
                "validator": self.validate_metrics
            },
            {
                "endpoint": "/gpu/info",
                "expected_fields": ["available", "devices", "cuda_version", "driver_info"],
                "validator": self.validate_gpu_info
            },
            {
                "endpoint": "/gpu/metrics",
                "expected_fields": ["available", "devices"],
                "validator": self.validate_gpu_metrics
            },
            {
                "endpoint": "/npu/info",
                "expected_fields": ["available", "devices", "driver_version"],
                "validator": self.validate_npu_info
            },
            {
                "endpoint": "/npu/metrics",
                "expected_fields": ["available", "devices"],
                "validator": self.validate_npu_metrics
            }
        ]

        results = []
        for test in test_cases:
            self.print_colored("\n" + "="*50, Fore.CYAN)
            self.print_colored(f"测试接口: {test['endpoint']}", Fore.CYAN)
            self.print_colored("="*50, Fore.CYAN)
            
            result = self.test_endpoint(test["endpoint"], test["expected_fields"])
            
            if result["status"] == "success":
                self.print_colored("✓ 状态码检查通过", Fore.GREEN)
                self.print_colored(f"✓ 响应时间: {result['response_time']:.3f}秒", Fore.GREEN)
                
                print("\n返回数据:")
                self.print_json(result["data"])
                
                # 如果有自定义验证器，执行验证
                if "validator" in test:
                    try:
                        test["validator"](result["data"])
                        self.print_colored("\n✓ 数据格式验证通过", Fore.GREEN)
                    except AssertionError as e:
                        self.print_colored(f"\n✗ 数据格式验证失败: {str(e)}", Fore.RED)
                        result["status"] = "format_error"
                        result["error"] = str(e)
            else:
                self.print_colored(f"✗ 测试失败: {result['error']}", Fore.RED)
            
            results.append(result)
        
        return results

    def generate_report(self, results: list):
        """生成测试报告"""
        print("\n")
        self.print_colored("="*50, Fore.YELLOW)
        self.print_colored("API测试报告", Fore.YELLOW)
        self.print_colored("="*50, Fore.YELLOW)
        print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试��口数量: {len(results)}")
        
        success_count = len([r for r in results if r["status"] == "success"])
        self.print_colored(f"成功: {success_count}", Fore.GREEN)
        self.print_colored(f"失败: {len(results) - success_count}", Fore.RED)
        
        print("\n详细结果:")
        for result in results:
            if result["status"] == "success":
                self.print_colored(f"\n✓ {result['endpoint']}", Fore.GREEN)
                self.print_colored(f"  响应时间: {result['response_time']:.3f}秒", Fore.GREEN)
            else:
                self.print_colored(f"\n✗ {result['endpoint']}", Fore.RED)
                self.print_colored(f"  错误: {result['error']}", Fore.RED)

def main():
    tester = APITester()
    results = tester.run_all_tests()
    tester.generate_report(results)

if __name__ == "__main__":
    main() 