from mongoengine import Document, StringField, IntField, DateTimeField, ListField, BooleanField, ReferenceField, ObjectIdField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List
from bson import ObjectId
# MongoEngine 模型
class AuditTaskFile(Document):
    meta = {
        'collection': 'audit_task_files'
    }
    _id = ObjectIdField(primary_key=True, default=lambda: ObjectId())
    name = StringField(required=True)  # 文件名称
    task_id = ObjectIdField(required=True)  # 关联的审计任务ID
    storage_path = StringField(required=True)  # 存储路径
    storage_type = StringField(required=True)  # 存储类型：MINIO或LOCAL
    data_type = StringField()  # 文件格式：PDF, Word等
    size = IntField()  # 文件大小（字节）
    status = StringField(default="pending")  # 文件状态：pending, processing, completed, failed
    uploaded_at = DateTimeField(default=datetime.now)  # 上传时间
    processed_at = DateTimeField()  # 处理完成时间
    user_id = IntField(required=True)  # 上传用户ID
    user_name = StringField()  # 上传用户名
    is_deleted = BooleanField(default=False)  # 软删除标记
    # processing_status = StringField(default="pending")  # 处理状态
    url = StringField()  # 文件访问URL

# Pydantic 基础模型
class AuditTaskFileBase(BaseModel):
    name: str
    task_id: str
    storage_path: str
    storage_type: str
    data_type: Optional[str] = None
    size: Optional[int] = None

# 创建任务文件请求模型
class AuditTaskFileCreate(AuditTaskFileBase):
    user_id: int
    user_name: Optional[str] = None

# 更新任务文件请求模型
class AuditTaskFileUpdate(BaseModel):
    status: Optional[str] = None
    processed_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        extra = "allow"

# 任务文件响应模型
class AuditTaskFileResponse(AuditTaskFileBase):
    id: str
    status: str
    uploaded_at: datetime
    processed_at: Optional[datetime] = None
    user_name: Optional[str] = None
    url: Optional[str] = None

    class Config:
        from_attributes = True

# 任务文件列表响应模型
class AuditTaskFileListResponse(BaseModel):
    id: str
    name: str
    status: str
    data_type: str
    size: int
    uploaded_at: datetime
    url: Optional[str] = None

    class Config:
        from_attributes = True
