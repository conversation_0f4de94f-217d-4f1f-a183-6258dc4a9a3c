from typing import Dict, Any, List, AsyncGenerator
from datetime import datetime
from ..db.mongodb import db
import asyncio
import json
from ..utils.llmClient import stream_openai_api
from app.utils.logging_config import setup_logging, get_logger
# 设置日志配置
setup_logging()
logger = get_logger(__name__)


 # 定义支持的分析维度
SUPPORTED_DIMENSIONS = {
    "LEGAL_COMPLIANCE": "法律合规性审查",
    "FINANCIAL_STATUS": "财务状况分析",
    "OPERATION_ANALYSIS": "经营情况分析",
    "CREDIT_HISTORY": "信用记录分析",
    "MARKET_POSITION": "市场地位分析",
    "RISK_ASSESSMENT": "风险评估",
    "MANAGEMENT_TEAM": "管理团队评估",
    "INDUSTRY_ANALYSIS": "行业分析",
    "CREDIT_ASSIST": "信贷助手"
}

    # @staticmethod
    # async def analyze_enterprise(
    #     enterprise_id: str,
    #     dimensions: List[str],
    #     context: Dict[str, Any] = None
    # ) -> Dict[str, Any]:
    #     """
    #     企业尽调分析服务 - 同步模式
    #     """
    #     sections = []
    #     unsupported_dimensions = []
        
    #     # 检查不支持的维度
    #     for dimension in dimensions:
    #         if dimension not in LoanAnalysisService.SUPPORTED_DIMENSIONS:
    #             unsupported_dimensions.append(dimension)
    #             continue

    #         # 根据请求的维度进行分析
    #         if dimension == "LEGAL_COMPLIANCE":
    #             sections.append({
    #                 "title": "法律合规性审查",
    #                 "content": await LoanAnalysisService.analyze_legal_compliance(enterprise_id, context),
    #                 "type": "markdown"
    #             })
            
    #         elif dimension == "FINANCIAL_STATUS":
    #             sections.append({
    #                 "title": "财务状况分析",
    #                 "content": await LoanAnalysisService.analyze_financial_status(enterprise_id, context),
    #                 "type": "markdown"
    #             })
            
    #         elif dimension == "OPERATION_ANALYSIS":
    #             sections.append({
    #                 "title": "经营情况分析",
    #                 "content": await LoanAnalysisService.analyze_operation(enterprise_id, context),
    #                 "type": "markdown"
    #             })
    #         # ... 其他维度的处理保持不变

    #     result = {
    #         "analysisId": f"analysis_{datetime.now().strftime('%Y%m%d%H%M%S')}",
    #         "sections": sections,
    #     }

    #     # 如果有不支持的维度，添加到结果中
    #     if unsupported_dimensions:
    #         result["unsupported_dimensions"] = unsupported_dimensions
    #         result["warning"] = f"以下分析维度暂不支持: {', '.join(unsupported_dimensions)}"

    #     return result


async def analyze_legal_compliance(enterprise_id: str, context: Dict[str, Any] = None) -> AsyncGenerator[str, None]:
    """法律合规性审查\n\n1. 许可证情况: 经营许可齐全，无违规记录\n2. 合规状况: 近三年无重大违法违规记录"""
    try:
        # 1. 获取模型配置信息
        app_setting = await db["system_app_settings"].find_one({"app_info": "LEGAL_COMPLIANCE"})
        if not app_setting:
            yield json.dumps({
                "event": "error",
                "data": {"message": "未找到模型配置信息"}
            })
            return

        # 2. 发送模块开始状态
        yield f"event: moduleStatus\ndata: {json.dumps({'status': 'running', 'name': 'LEGAL_COMPLIANCE'})}\n\n"

        # 3. 获取法律合规数据
        if not context:
            context = await db["enterprise_info"].find_one(
                {"enterprise_id": enterprise_id}
            )
            if context:
                context.pop("_id", None)    # 删除 _id 字段
            if not context:
                yield json.dumps({
                    "event": "error",
                    "data": {"message": "未找到企业法律合规数据"}
                })
                return

        # 4. 构建分析提示词
        messages = [
            {"role": "system", "content": "你是一个专业的法律合规分析师，擅长企业合规性评估和风险识别。"},
            {"role": "user", "content": f"""
            请对以下企业法律合规数据进行全面分析，重点关注以下方面：
            1. 经营许可证分析
                - 必要许可证持有情况
                - 许可证有效期评估
                - 许可证续期风险
                - 经营范围合规性
            2. 违法违规记录
                - 近三年违法违规情况
                - 处罚记录严重程度
                - 整改落实情况
                - 合规风险趋势
            3. 合规管理体系
                - 内部合规制度建设
                - 合规管理机制
                - 合规培训情况
            4. 诉讼与仲裁情况
                - 重大诉讼案件
                - 执行情况分析
                - 法律风险评估
            5. 监管合规情况
                - 行业监管要求达标情况
                - 日常监管检查记录
                - 特殊监管指标完成度
            6. 合规风险防控
                - 主要合规风险点
                - 风险防控措施
                - 应对预案建议

            法律合规数据如下：
            {json.dumps(context, ensure_ascii=False, indent=2)}

            请以Markdown格式输出分析结果，包含关键指标解读和风险提示。
            注意：请直接给出分析结果，无需展示计算公式。
            """}
        ]

        # 5. 准备API调用参数
        logger.info(f"app_setting: {app_setting}")
        api_key = app_setting.get("token_key")
        model = app_setting.get("model_name", "gpt-3.5-turbo")
        url = app_setting.get("service_url", "https://api.openai.com/v1/chat/completions")
        extra = {
            "temperature": 0.7,
            "stream": True
        }

        # 6. 调用大模型进行分析
        async for chunk in stream_openai_api(
            api_key=api_key,
            model=model,
            messages=messages,
            url=url,
            extra=extra
        ):
            logger.info(f"chunk: {chunk}")
            
            if isinstance(chunk, str):
                yield f"event: answer\n{chunk}\n\n"
            else:
                logger.error(f"分析维度 LEGAL_COMPLIANCE 时发生错误: {str(chunk)}")
                yield json.dumps({
                    "event": "error",
                    "data": {"message": str(chunk)}
                })

    except Exception as e:
        logger.error(f"分析维度 LEGAL_COMPLIANCE 时发生错误: {str(e)}")
        yield json.dumps({
            "event": "error",
            "data": {"message": f"分析过程发生错误: {str(e)}"}
        })


async def analyze_financial_status(enterprise_id: str, context:str = None) -> AsyncGenerator[str, None]:
    try:
        # 1. 获取模型配置信息
        app_setting = await db["system_app_settings"].find_one({"app_info": "FINANCIAL_STATUS"})
        if not app_setting:
            yield json.dumps({
                "event": "error",
                "data": {"message": "未找到模型配置信息"}
            })
            return

        # 2. 发送模块开始状态
        yield f"event: moduleStatus\ndata: {json.dumps({'status': 'running', 'name': 'FINANCIAL_STATUS'})}\n\n"
            

        # 3. 获取财务数据
        # logger.info(f"context: {context}")
        # financial_data = context.get('FINANCIAL_STATUS',None) if context else None
        # logger.info(f"financial_data: {financial_data}")
        if not context:
            context = await db["enterprise"].find_one(
                {"enterprise_id": enterprise_id}
            )
            if context:
                context.pop("_id", None)    # 删除 _id 字段
            if not context:
                yield json.dumps({
                    "event": "error",
                    "data": {"message": "未找到企业财务数据"}
                })
                return
        print("==========测试==============",context)
        # 4. 构建分析提示词
        messages = [
            {"role": "system", "content": "你是一个专业的财务分析师，擅长企业财务报表分析。"},
            {"role": "user", "content": f"""
            请对以下企业财务数据进行全面分析，重点关注以下方面：
            1. 盈利能力分析
            2. 偿债能力分析
            3. 运营效率分析
            4. 现金流状况分析
            5. 财务风险评估

            财数据如下：
            {json.dumps(context, ensure_ascii=False, indent=2)}

            请以Markdown格式输出分析结果，包含关键指标解读和风险提示。
            注意：请直接给出分析结果，无需展示计算公式。
            """}
        ]
        # logger.info(f"messages: {messages}")

        # 5. 准备API调用参数
        logger.info(f"app_setting: {app_setting}")
        api_key = app_setting.get("token_key")
        model = app_setting.get("model_name", "gpt-3.5-turbo")
        url = app_setting.get("service_url", "https://api.openai.com/v1/chat/completions")
        extra = {
            "temperature": 0.7,
            "stream": True,
            # "enterprise_id": enterprise_id
        }

        logger.info(f"api_key: {api_key}")
        logger.info(f"model: {model}")
        logger.info(f"url: {url}")
        logger.info(f"extra: {extra}")
        logger.info(f"messages: {messages}")

        # 6. 调用大模型进行分析
        async for chunk in stream_openai_api(
            api_key=api_key,
            model=model,
            messages=messages,
            url=url,
            extra=extra
        ):
            logger.info(f"chunk: {chunk}")
            
            if isinstance(chunk, str):
                yield f"event: answer\n{chunk}\n\n"
                # try:
                #     # 尝试解析JSON字符串
                #     chunk_data = json.loads(chunk)
                #     # 处理完成标志
                #     if chunk_data.get("finish_reason") == "stop":
                #         yield f"event: answer\ndata: [DONE]\n\n"
                #     else:
                #         yield f"event: answer\n{chunk_data}\n\n"
                # except json.JSONDecodeError as e:
                #     logger.error(f"分析维度 FINANCIAL_STATUS 时发生错误: {str(e)}")
                #     # 如果不是JSON格式，直接返回原始字符串
                #     yield f"event: error\n{chunk}\n\n"
                #     # yield json.dumps({
                    #     "event": "answer",
                    #     "data": chunk
                    # })
                    # yield json.dumps({
                    #     "event": "error",
                    #     "data": {"message": 2}
                    # })
            else:
                # 处理错误情况
                logger.error(f"分析维度 FINANCIAL_STATUS 时发生错误: {str(chunk)}")
                yield json.dumps({
                    "event": "error",
                    "data": {"message": str(chunk)}
                })

    except Exception as e:
        logger.error(f"分析维度 FINANCIAL_STATUS 时发生错误: {str(e)}")
        # 处理异常情况
        yield json.dumps({
            "event": "error",
            "data": {"message": f"分析过程发生错误: {str(e)}"}
        })

    # finally:
    #    发送模块完成状态
    #    yield f"event: moduleStatus\ndata: {json.dumps({'status': 'completed', 'name': 'FINANCIAL_STATUS'})}\n\n"
               
async def analyze_credit_assist(enterprise_id: str, context:str = None) -> AsyncGenerator[str, None]:
    try:
        # 1. 获取模型配置信息
        app_setting = await db["system_app_settings"].find_one({"app_info": "CREDIT_ASSIST"})
        if not app_setting:
            yield json.dumps({
                "event": "error",
                "data": {"message": "未找到模型配置信息"}
            })
            return
    
        # 2. 发送模块开始状态
        yield f"event: moduleStatus\ndata: {json.dumps({'status': 'running', 'name': 'CREDIT_ASSIST'})}\n\n"
            

        # 3. 获取财务数据
        # logger.info(f"context: {context}")
        # financial_data = context.get('FINANCIAL_STATUS',None) if context else None
        # logger.info(f"financial_data: {financial_data}")
        if not context:
            context = await db["enterprise_info"].find_one(
                {"enterprise_id": enterprise_id}
            )
            if context:
                context.pop("_id", None)    # 删除 _id 字段
            if not context:
                yield json.dumps({
                    "event": "error",
                    "data": {"message": "未找到企业财务数据"}
                })
                return

        # 4. 构建分析提示词
        messages = [
            {"role": "system", "content": "你是一个专业的信贷审批专家，擅长企业资产分析。"},
            {"role": "user", "content": f"""
            请对以下企业智能信贷文件，竞品系信息数据进行全面分析，重点关注以下方面：
            1. 尽调报告
            2. 决策建议


            参考数据如下：
            {json.dumps(context, ensure_ascii=False, indent=2)}

            请以Markdown格式输出分析结果，包含关键指标解读和风险提示。
            注意：请直接给出分析结果，无需展示计算公式。
            """}
        ]
        # logger.info(f"messages: {messages}")

        # 5. 准备API调用参数
        logger.info(f"app_setting: {app_setting}")
        api_key = app_setting.get("token_key")
        model = app_setting.get("model_name", "gpt-3.5-turbo")
        url = app_setting.get("service_url", "https://api.openai.com/v1/chat/completions")
        logger.info(f"url: {url}")
        extra = {
            "temperature": 0.7,
            "stream": True,
            # "enterprise_id": enterprise_id
        }

        # 6. 调用大模型进行分析
        async for chunk in stream_openai_api(
            api_key=api_key,
            model=model,
            messages=messages,
            url=url,
            extra=extra
        ):
            logger.info(f"chunk: {chunk}")
            
            if isinstance(chunk, str):
                yield f"event: answer\n{chunk}\n\n"
                # try:
                #     # 尝试解析JSON字符串
                #     chunk_data = json.loads(chunk)
                #     # 处理完成标志
                #     if chunk_data.get("finish_reason") == "stop":
                #         yield f"event: answer\ndata: [DONE]\n\n"
                #     else:
                #         yield f"event: answer\n{chunk_data}\n\n"
                # except json.JSONDecodeError as e:
                #     logger.error(f"分析维度 FINANCIAL_STATUS 时发生错误: {str(e)}")
                #     # 如果不是JSON格式，直接返回原始字符串
                #     yield f"event: error\n{chunk}\n\n"
                #     # yield json.dumps({
                    #     "event": "answer",
                    #     "data": chunk
                    # })
                    # yield json.dumps({
                    #     "event": "error",
                    #     "data": {"message": 2}
                    # })
            else:
                # 处理错误情况
                logger.error(f"分析维度 CREDIT_ASSIST 时发生错误: {str(chunk)}")
                yield json.dumps({
                    "event": "error",
                    "data": {"message": str(chunk)}
                })

    except Exception as e:
        logger.error(f"分析维度 CREDIT_ASSIST 时发生错误: {str(e)}")
        # 处理异常情况
        yield json.dumps({
            "event": "error",
            "data": {"message": f"分析过程发生错误: {str(e)}"}
        })

    # finally:
    #    发送模块完成状态
    #    yield f"event: moduleStatus\ndata: {json.dumps({'status': 'completed', 'name': 'FINANCIAL_STATUS'})}\n\n"

async def analyze_operation(enterprise_id: str, context: Dict[str, Any] = None) -> AsyncGenerator[str, None]:
    """经营情况分析## 经营情况分析\n\n1. 市场份额: 在细分市场占有率稳定\n2. 运营效率: 各项运营指标良好"""
    try:
        # 1. 获取模型配置信息
        app_setting = await db["system_app_settings"].find_one({"app_info": "OPERATION_ANALYSIS"})
        if not app_setting:
            yield json.dumps({
                "event": "error",
                "data": {"message": "未找到模型配置信息"}
            })
            return

        # 2. 发送模块开始状态
        yield f"event: moduleStatus\ndata: {json.dumps({'status': 'running', 'name': 'OPERATION_ANALYSIS'})}\n\n"

        # 3. 获取经营数据
        if not context:
            context = await db["enterprise_info"].find_one(
                {"enterprise_id": enterprise_id}
            )
            if context:
                context.pop("_id", None)    # 删除 _id 字段
            if not context:
                yield json.dumps({
                    "event": "error",
                    "data": {"message": "未找到企业市场经营数据"}
                })
                return

        # 4. 构建分析提示词
        messages = [
            {"role": "system", "content": "你是一个专业的场分析师，擅长企业市场经营状况分析。"},
            {"role": "user", "content": f"""
            请对以下企业市场经营数据进行全面分析，重点关注以下方面：
            1. 市场份额分析
                - 市场占有率及变化趋势
                - 与主要竞争对手的对比
            2. 销售业绩表现
                - 销售收入增长情况
                - 产品/服务定价策略
            3. 客户结构分析
                - 客户集中度
                - 客户稳定性
            4. 市场竞争力评估
                - 产品/服务竞争优势
                - 品牌影响力
            5. 市场拓展能力
                - 新市场开发情况
                - 营销渠道效率
            6. 经营风险评估
                - 市场波动风险
                - 竞争风险
                - 客户依赖风险

            经营数据如下：
            {json.dumps(context, ensure_ascii=False, indent=2)}

            请以Markdown格式输出分析结果，包含关键指标解读和风险提示。
            注意：请直接给出分析结果，无需展示计算公式。
            """}
        ]

        # 5. 准备API调用参数
        logger.info(f"app_setting: {app_setting}")
        api_key = app_setting.get("token_key")
        model = app_setting.get("model_name", "gpt-3.5-turbo")
        url = app_setting.get("service_url", "https://api.openai.com/v1/chat/completions")
        extra = {
            "temperature": 0.7,
            "stream": True
        }

        # 6. 调用大模型进行分析
        async for chunk in stream_openai_api(
            api_key=api_key,
            model=model,
            messages=messages,
            url=url,
            extra=extra
        ):
            logger.info(f"chunk: {chunk}")
            
            if isinstance(chunk, str):
                yield f"event: answer\n{chunk}\n\n"
            else:
                logger.error(f"分析维度 OPERATION_ANALYSIS 时发生错误: {str(chunk)}")
                yield json.dumps({
                    "event": "error",
                    "data": {"message": str(chunk)}
                })

    except Exception as e:
        logger.error(f"分析维度 OPERATION_ANALYSIS 时发生错误: {str(e)}")
        yield json.dumps({
            "event": "error",
            "data": {"message": f"分析过程发生错误: {str(e)}"}
        })

async def analyze_credit_history(enterprise_id: str, context: Dict[str, Any] = None) -> AsyncGenerator[str, None]:
    """信用记录分析## 信用记录分析\n\n1. 信用评分: 良好\n2. 历史违约: 无重大违约记录"""
    try:
        # 1. 获取模型配置信息
        app_setting = await db["system_app_settings"].find_one({"app_info": "CREDIT_HISTORY"})
        if not app_setting:
            yield json.dumps({
                "event": "error",
                "data": {"message": "未找到模型配置信息"}
            })
            return

        # 2. 发送模块开始状态
        yield f"event: moduleStatus\ndata: {json.dumps({'status': 'running', 'name': 'CREDIT_HISTORY'})}\n\n"

        # 3. 获取信用记录数据
        if not context:
            context = await db["enterprise_info"].find_one(
                {"enterprise_id": enterprise_id}
            )
            if context:
                context.pop("_id", None)    # 删除 _id 字段
            if not context:
                yield json.dumps({
                    "event": "error",
                    "data": {"message": "未找到企业信用记录数据"}
                })
                return

        # 4. 构建分析提示词
        messages = [
            {"role": "system", "content": "你是一个专的信用分析师，擅长企业信用记录分析和风险评估。"},
            {"role": "user", "content": f"""
            请对以下企业信用记录数据进行全面分析，重点关注以下方面：
            1. 信用评分分析
                - 总体信用评分及变化趋势
                - 各维度评分详情
                - 与行业平均水平对比
            2. 历史违约记录分析
                - 违约事件统计及严重程度
                - 违约原因分析
                - 后续整改情况
            3. 信用交易表现
                - 贷款还款记录
                - 商业信用履约情况
                - 供应商合作评价
            4. 司法诉讼记录
                - 涉诉情况统计
                - 案件性质分析
                - 执行情况
            5. 信用风险评估
                - 主要风险点识别
                - 风险等级评定
                - 风险趋势预判
            6. 信用管理建议
                - 信用提升建议
                - 风险防控措施
                - 信用监测要点

            信用记录数据如下：
            {json.dumps(context, ensure_ascii=False, indent=2)}

            请以Markdown格式输出分结果，需要：
            1. 对每个维度进行详细分析
            2. 提供具体的数据支持
            3. 突出关键风险点
            4. 给出明确的信用评级建议
            """}
        ]

        # 5. 准备API调用参数
        logger.info(f"app_setting: {app_setting}")
        api_key = app_setting.get("token_key")
        model = app_setting.get("model_name", "gpt-3.5-turbo")
        url = app_setting.get("service_url", "https://api.openai.com/v1/chat/completions")
        extra = {
            "temperature": 0.7,
            "stream": True
        }

        # 6. 调用大模型进行分析
        async for chunk in stream_openai_api(
            api_key=api_key,
            model=model,
            messages=messages,
            url=url,
            extra=extra
        ):
            logger.info(f"chunk: {chunk}")
            
            if isinstance(chunk, str):
                yield f"event: answer\n{chunk}\n\n"
            else:
                logger.error(f"分析维度 CREDIT_HISTORY 时发生错误: {str(chunk)}")
                yield json.dumps({
                    "event": "error",
                    "data": {"message": str(chunk)}
                })

    except Exception as e:
        logger.error(f"分析维度 CREDIT_HISTORY 时发生错误: {str(e)}")
        yield json.dumps({
            "event": "error",
            "data": {"message": f"分析过程发生错误: {str(e)}"}
        })

async def analyze_market_position(enterprise_id: str, context: Dict[str, Any] = None) -> AsyncGenerator[str, None]:
    """市场地位分析## 市场地位分析\n\n1. 行业排名: 前20%\n2. 市场份额: 稳定增长"""
    try:
        # 1. 获取模型配置信息
        app_setting = await db["system_app_settings"].find_one({"app_info": "MARKET_POSITION"})
        if not app_setting:
            yield json.dumps({
                "event": "error",
                "data": {"message": "未找到模型配置信息"}
            })
            return

        # 2. 发送模块开始状态
        yield f"event: moduleStatus\ndata: {json.dumps({'status': 'running', 'name': 'MARKET_POSITION'})}\n\n"

        # 3. 获取市场地位数据
        if not context:
            context = await db["enterprise_info"].find_one(
                {"enterprise_id": enterprise_id}
            )
            if context:
                context.pop("_id", None)    # 删除 _id 字段
            if not context:
                yield json.dumps({
                    "event": "error",
                    "data": {"message": "未找到企业市场地位数据"}
                })
                return

        # 4. 构建分析提示词
        messages = [
            {"role": "system", "content": "你是一个专业的市场分析师，长企业市场地位和竞争力分析。"},
            {"role": "user", "content": f"""
            请对以下企业市场地位数据进行全面分析，重点关注以下方面：
            1. 行业排名分析
                - 整体行业排名及变化趋势
                - 细分市场排名情况
                - 与主要竞争对手的排名对比
            2. 市场份额分析
                - 整体市场份额及增长情况
                - 各区域市场占有率
                - 重点产品市场份额
            3. 竞争优势分析
                - 核心竞争力识别
                - 品牌影响力评估
                - 技术创新能力
            4. 市场影响力
                - 行业地位评估
                - 定价能力分析
                - 上下游议价能力
            5. 发展潜力评估
                - 市场扩张能力
                - 增长机会分析
                - 未来发展预测
            6. 风险因素
                - 市场竞争风险
                - 行业变革风险
                - 市场份额稳定性

            市场地位数据如下：
            {json.dumps(context, ensure_ascii=False, indent=2)}

            请以Markdown格式输出分析结果，包含关键指标解读和风险提示。
            注意：请直接给出分析结果，无需展示计算公式。
            """}
        ]

        # 5. 准备API调用参数
        logger.info(f"app_setting: {app_setting}")
        api_key = app_setting.get("token_key")
        model = app_setting.get("model_name", "gpt-3.5-turbo")
        url = app_setting.get("service_url", "https://api.openai.com/v1/chat/completions")
        extra = {
            "temperature": 0.7,
            "stream": True
        }

        # 6. 调用大模型进行分析
        async for chunk in stream_openai_api(
            api_key=api_key,
            model=model,
            messages=messages,
            url=url,
            extra=extra
        ):
            logger.info(f"chunk: {chunk}")
            
            if isinstance(chunk, str):
                yield f"event: answer\n{chunk}\n\n"
            else:
                logger.error(f"分析维度 MARKET_POSITION 时发生错误: {str(chunk)}")
                yield json.dumps({
                    "event": "error",
                    "data": {"message": str(chunk)}
                })

    except Exception as e:
        logger.error(f"分析维度 MARKET_POSITION 时发生错误: {str(e)}")
        yield json.dumps({
            "event": "error",
            "data": {"message": f"分析过程发生错误: {str(e)}"}
        })

async def analyze_risk(enterprise_id: str, context: Dict[str, Any] = None) -> AsyncGenerator[str, None]:
    """风险评估## 风险评估\n\n1. 整体风险: 中低风险\n2. 主要风险点: 市场竞争加剧"""
    try:
        # 1. 获取模型配置信息
        app_setting = await db["system_app_settings"].find_one({"app_info": "RISK_ASSESSMENT"})
        if not app_setting:
            yield json.dumps({
                "event": "error",
                "data": {"message": "未找到模型配置信息"}
            })
            return

        # 2. 发送模块开始状态
        yield f"event: moduleStatus\ndata: {json.dumps({'status': 'running', 'name': 'RISK_ASSESSMENT'})}\n\n"

        # 3. 获取风险评估数据
        if not context:
            context = await db["enterprise_info"].find_one(
                {"enterprise_id": enterprise_id}
            )
            if context:
                context.pop("_id", None)    # 删除 _id 字段
            if not context:
                yield json.dumps({
                    "event": "error",
                    "data": {"message": "未找到企业风险评估数据"}
                })
                return

        # 4. 构建分析提示词
        messages = [
            {"role": "system", "content": "你是一个专业的风险评估分析师，擅长企业整体风险评估和风险点识��。"},
            {"role": "user", "content": f"""
            请对以下企业风险数据进行全面分析，重点关注以下方面：
            1. 整体风险评估
                - 风险等级判定
                - 风险评分分析
                - 风险变化趋势
            2. 财务风险
                - 资产负债风险
                - 现金流风险
                - 盈利能力风险
            3. 经营风险
                - 市场竞争风险
                - 运营管理风险
                - 供应链风险
            4. 外部环境风险
                - 政策法规风险
                - 行业周期风险
                - 宏观经济风险
            5. 信用风险
                - 违约风险评估
                - 信用记录分析
                - 债务履约能力
            6. 风险防控建议
                - 主要风险点清单
                - 风险管理措施
                - 风险监测点

            风险评估数据如下：
            {json.dumps(context, ensure_ascii=False, indent=2)}

            请以Markdown格式输出分析结果，包含关键指标解读和风险提示。
            注意：请直接给出分析结果，无需展示计算公式。
            """}
        ]

        # 5. 准备API调用参数
        logger.info(f"app_setting: {app_setting}")
        api_key = app_setting.get("token_key")
        model = app_setting.get("model_name", "gpt-3.5-turbo")
        url = app_setting.get("service_url", "https://api.openai.com/v1/chat/completions")
        extra = {
            "temperature": 0.7,
            "stream": True
        }

        # 6. 调用大模型进行分析
        async for chunk in stream_openai_api(
            api_key=api_key,
            model=model,
            messages=messages,
            url=url,
            extra=extra
        ):
            logger.info(f"chunk: {chunk}")
            
            if isinstance(chunk, str):
                yield f"event: answer\n{chunk}\n\n"
            else:
                logger.error(f"分析维度 RISK_ASSESSMENT 时发生错误: {str(chunk)}")
                yield json.dumps({
                    "event": "error",
                    "data": {"message": str(chunk)}
                })

    except Exception as e:
        logger.error(f"分析维度 RISK_ASSESSMENT 时发生错误: {str(e)}")
        yield json.dumps({
            "event": "error",
            "data": {"message": f"分析过程发生错误: {str(e)}"}
        })

async def analyze_management(enterprise_id: str, context: Dict[str, Any] = None) -> AsyncGenerator[str, None]:
    "管理团队评估 ## 管理团队评估\n\n1. 团队构成: 经验丰富\n2. 管理能力: 专业稳定"
    try:
        # 1. 获取模型配置信息
        app_setting = await db["system_app_settings"].find_one({"app_info": "MANAGEMENT_TEAM"})
        if not app_setting:
            yield json.dumps({
                "event": "error",
                "data": {"message": "未找到模型配置信息"}
            })
            return

        # 2. 发送模块开始状态
        yield f"event: moduleStatus\ndata: {json.dumps({'status': 'running', 'name': 'MANAGEMENT_TEAM'})}\n\n"

        # 3. 获取管理团队数据
        if not context:
            context = await db["enterprise_info"].find_one(
                {"enterprise_id": enterprise_id}
            )
            if context:
                context.pop("_id", None)    # 删除 _id 字段
            if not context:
                yield json.dumps({
                    "event": "error",
                    "data": {"message": "未找到企业管理团队数据"}
                })
                return

        # 4. 构建分析提示词
        messages = [
            {"role": "system", "content": "你是一个专业的企业管理分析师，擅长管理团队评估和能力分析。"},
            {"role": "user", "content": f"""
            请对以下企业管理团队数据进行全面分析，重点关注以下方面：
            1. 团队构成分析
                - 团队架构完整性
                - 人员配置合理性
                - 核心岗位覆盖情况
            2. 管理层背景评估
                - 教育背景和专业匹配度
                - 行业从业经验
                - 过往业绩表现
            3. 管理能力评估
                - 战略规划能力
                - 执行力和决策效率
                - 创新管理能力
                - 风险管理意识
            4. 团队稳定性分析
                - 核心团队稳定性
                - 人员流动情况
                - 继任者计划
            5. 管理机制评估
                - 公司治理结构
                - 激励约束机制
                - 内部控制体系
            6. 团队协作效能
                - 部门协同情况
                - 沟通效率
                - 团队凝聚力

            管理团队数据如下：
            {json.dumps(context, ensure_ascii=False, indent=2)}

            请以Markdown格式输出分析结果，包含关键指标解读和风险提示。
            注意：请直接给出分析结果，无需展示计算公式。
            """}
        ]

        # 5. 准备API调用参数
        logger.info(f"app_setting: {app_setting}")
        api_key = app_setting.get("token_key")
        model = app_setting.get("model_name", "gpt-3.5-turbo")
        url = app_setting.get("service_url", "https://api.openai.com/v1/chat/completions")
        extra = {
            "temperature": 0.7,
            "stream": True
        }

        # 6. 调用大模型进行分析
        async for chunk in stream_openai_api(
            api_key=api_key,
            model=model,
            messages=messages,
            url=url,
            extra=extra
        ):
            logger.info(f"chunk: {chunk}")
            
            if isinstance(chunk, str):
                yield f"event: answer\n{chunk}\n\n"
            else:
                logger.error(f"分析维度 MANAGEMENT_TEAM 时发生错误: {str(chunk)}")
                yield json.dumps({
                    "event": "error",
                    "data": {"message": str(chunk)}
                })

    except Exception as e:
        logger.error(f"分析维度 MANAGEMENT_TEAM 时发生错误: {str(e)}")
        yield json.dumps({
            "event": "error",
            "data": {"message": f"分析过程发生错误: {str(e)}"}
        })

async def analyze_industry(enterprise_id: str, context: Dict[str, Any] = None) -> AsyncGenerator[str, None]:
    """行业分析\n\n1. 行业趋势: 稳定增长\n2. 竞争格局: 竞争适中"""
    try:
        # 1. 获取模型配置信息
        app_setting = await db["system_app_settings"].find_one({"app_info": "INDUSTRY_ANALYSIS"})
        if not app_setting:
            yield json.dumps({
                "event": "error",
                "data": {"message": "未找到模型配置信息"}
            })
            return

        # 2. 发送模块开始状态
        yield f"event: moduleStatus\ndata: {json.dumps({'status': 'running', 'name': 'INDUSTRY_ANALYSIS'})}\n\n"

        # 3. 获取行业分析数据
        if not context:
            context = await db["enterprise_info"].find_one(
                {"enterprise_id": enterprise_id}
            )
            if context:
                context.pop("_id", None)    # 删除 _id 字段
            if not context:
                yield json.dumps({
                    "event": "error",
                    "data": {"message": "未找到企业行业分析数据"}
                })
                return

        # 4. 构建分析提示词
        messages = [
            {"role": "system", "content": "你是一个专业的行业分析师，擅长行业趋势分析和竞争格局研判。"},
            {"role": "user", "content": f"""
            请对以下企业所处行业数据进行全面分析，重点关注以下方面：
            1. 行业发展阶段与趋势
                - 行业生命周期判断
                - 市场规模及增长趋势
                - 技术发展趋势
                - 未来发展前景
            2. 行业竞争格局
                - 市场集中度分析
                - 主要竞争者分析
                - 进入壁垒评估
                - 替代品威胁
            3. 产业链分析
                - 上下游议价能力
                - 产业链整合趋势
                - 价值链分布特点
            4. 政策环境分析
                - 行业政策导向
                - 监管环境变化
                - 政策机遇与风险
            5. 行业特征指标
                - 盈利能力指标
                - 行业周期性
                - 资金密集度
                - 技术密集度
            6. 企业在行业中的地位
                - 相对竞争优势
                - 市场地位评估
                - 发展机会分析

            行业分析数据如下：
            {json.dumps(context, ensure_ascii=False, indent=2)}

            请以Markdown格式输出分析结果，包含关键指标解读和风险提示。
            注意：请直接给出分析结果，无需展示计算公式。
            """}
        ]

        # 5. 准备API调用参api
        logger.info(f"app_setting: {app_setting}")
        api_key = app_setting.get("token_key")
        model = app_setting.get("model_name", "gpt-3.5-turbo")
        url = app_setting.get("service_url", "https://api.openai.com/v1/chat/completions")
        extra = {
            "temperature": 0.7,
            "stream": True
        }

        # 6. 调用大模型进行分析
        async for chunk in stream_openai_api(
            api_key=api_key,
            model=model,
            messages=messages,
            url=url,
            extra=extra
        ):
            logger.info(f"chunk: {chunk}")
            
            if isinstance(chunk, str):
                yield f"event: answer\n{chunk}\n\n"
            else:
                logger.error(f"分析维度 INDUSTRY_ANALYSIS 时发生错误: {str(chunk)}")
                yield json.dumps({
                    "event": "error",
                    "data": {"message": str(chunk)}
                })

    except Exception as e:
        logger.error(f"分析维度 INDUSTRY_ANALYSIS 时发生错误: {str(e)}")
        yield json.dumps({
            "event": "error",
            "data": {"message": f"分析过程发生错误: {str(e)}"}
        })


async def analyze_enterprise_stream(
    enterprise_id: str,
    dimensions: List[str],
    context: Dict[str, Any] = None
) -> AsyncGenerator[Dict[str, Any], None]:
    """
    企业尽调分析服务 - 流式模式
    """
    analysis_id = f"analysis_{datetime.now().strftime('%Y%m%d%H%M%S')}"
    unsupported_dimensions = []

    # 首先输出不支持的维度警告
    for dimension in dimensions:
        if dimension not in SUPPORTED_DIMENSIONS:
            unsupported_dimensions.append(dimension)

    if unsupported_dimensions:
        yield json.dumps({
            "analysisId": analysis_id,
            "warning": f"以下分析维度暂不支持: {', '.join(unsupported_dimensions)}",
            "type": "warning"
        })
        await asyncio.sleep(0)

    # 处理支持的维度
    for dimension in dimensions:
        if dimension not in SUPPORTED_DIMENSIONS:
            continue
            
        try:
            if dimension == "LEGAL_COMPLIANCE":
                async for chunk in analyze_legal_compliance(enterprise_id, context.get('LEGAL_COMPLIANCE', None)):
                # print(f" analyze_financial_status chunk: {chunk}")
                    yield chunk
                    
            elif dimension == "FINANCIAL_STATUS":
                async for chunk in analyze_financial_status(enterprise_id, context.get('FINANCIAL_STATUS', None)):
                    yield chunk
                    
            elif dimension == "OPERATION_ANALYSIS":
                async for chunk in analyze_operation(enterprise_id, context.get('OPERATION_ANALYSIS', None)):
                    yield chunk
                    
            elif dimension == "CREDIT_HISTORY":
                async for chunk in analyze_credit_history(enterprise_id, context.get('CREDIT_HISTORY', None)):
                    yield chunk
                    
            elif dimension == "MARKET_POSITION":
                async for chunk in analyze_market_position(enterprise_id, context.get('MARKET_POSITION', None)):
                    yield chunk
                    
            elif dimension == "RISK_ASSESSMENT":
                async for chunk in analyze_risk(enterprise_id, context.get('RISK_ASSESSMENT', None)):
                    yield chunk
                    
            elif dimension == "MANAGEMENT_TEAM":
                async for chunk in analyze_management(enterprise_id, context.get('MANAGEMENT_TEAM', None)):
                    yield chunk
                    
            elif dimension == "INDUSTRY_ANALYSIS":
                async for chunk in analyze_industry(enterprise_id, context.get('INDUSTRY_ANALYSIS', None)):
                    yield chunk
                    
            elif dimension == "CREDIT_ASSIST":
                async for chunk in analyze_credit_assist(enterprise_id, context.get('CREDIT_ASSIST', None)):
                    yield chunk
            else:
                pass

            yield chunk

        except Exception as e:
            logger.error(f"分析维度 {dimension} 时发生错误: {str(e)}")
            yield json.dumps({
                "analysisId": analysis_id,
                "error": f"分析维度 {dimension} 时发生错误: {str(e)}",
                "type": "error"
            })
            await asyncio.sleep(0)

