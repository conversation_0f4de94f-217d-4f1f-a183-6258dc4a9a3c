# 尽调报告分析助手
基于LLM 实现对于企业尽调报告的分析，给出分析建议

## 1.解决方案

### 1.1 需求描述

本接口基于大语言模型(LLM)，用于智能分析企业尽职调查报告，为信贷决策提供专业支持。主要功能特点：

1. 双模式执行：
   - 同步模式：适用于实时分析场景
   - 异步模式：适用于大规模批量处理

2. 智能分析能力：
   - 自动提取关键信息
   - 识别潜在风险点
   - 生成结构化分析建议

3. 灵活配置选项：
   - 支持流式输出，适用于大规模数据分析
   - 自定义分析维度，便于功能扩展
   - 异步任务回调通知

4. 标准化输出：
   - 采用Markdown格式
   - 结构化内容组织
   - 便于系统解析和界面展示

###  1.2 实现方案

#### 1.2.1 服务概述
基于大语言模型的尽调报告分析服务，提供同步/异步两种调用方式，支持实时分析和批量处理场景。

1. 服务方式
    系统提供API方式提供服务，服务通过输入企业ID，分析维度，分析方式，补充信息等参数，调用大模型模型，输出分析结果。
    输出结果为基于LLM的企业尽调报告分析建议。
2. 数据对齐方式
    系统以企业ID为分析对象唯一标识，建议使用社会统一信用代码。
    系统可接受补充上下文信息，用于细化分析结果。
3. 集成方式
    系统提供API方式提供服务，支持异步和同步两种方式。
    同步方式：适用于报告内容较小，需要实时响应的场景
    异步方式：适用于报告内容较大，需要长时间处理的场景


#### 1.2.2 调用时序图

1. 同步调用时序
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant LLM
    
    Client->>API: POST /api/v1/loan-analysis
    Note over Client,API: executionMode: SYNC
    API->>LLM: 调用模型分析
    LLM-->>API: 返回分析结果
    API-->>Client: 返回分析内容
```

2. 同步流式调用时序
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant LLM
    
    Client->>API: POST /api/v1/loan-analysis
    Note over Client,API: executionMode: SYNC, stream: true
    API->>LLM: 调用模型分析
    loop 流式响应
        LLM-->>API: 返回部分结果
        API-->>Client: 流式返回数据块
    end
```

3. 异步回调调用时序
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant LLM
    participant Callback
    
    Client->>API: POST /api/v1/loan-analysis
    Note over Client,API: executionMode: ASYNC
    API-->>Client: 返回taskId
    API->>LLM: 异步调用模型分析
    LLM-->>API: 返回分析结果
    API->>Callback: POST 回调结果
    Callback-->>API: 确认接收 (200 OK)
```

4. 主动查询调用时序
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant LLM
    
    Client->>API: POST /api/v1/loan-analysis
    Note over Client,API: executionMode: ASYNC
    API-->>Client: 返回taskId
    API->>LLM: 异步调用模型分析
    
    loop 轮询查询
        Client->>API: GET /api/v1/loan-analysis/task/{taskId}
        API-->>Client: 返回任务状态和结果
    end
```


这些时序图清晰地展示了三种不同的调用方式：
1. 同步调用：客户端等待完整结果
2. 同步流式：客户端接收流式结果
3. 异步回调：通过回调接口获取结果
4. 异步回调：支持主动查询

## 2.服务接口说明

### 2.1 分析接口
```
POST /api/v1/loan-analysis
```


#### 2.1.1 请求参数
```json
{
    "enterpriseId": "string",     // 企业ID，必填
    "dimensions": [],             // 分析维度，拓展字段，用于后续拓展
    "executionMode": "SYNC",     // 执行方式：SYNC(同步) / ASYNC(异步)
    "outputFormat": "MARKDOWN",   // 输出格式，当前仅支持 MARKDOWN
    "stream": false,             // 是否流式输出，仅在同步模式下有效
    "callbackUrl": "https://api.example.com/callback",  // 回调地址，异步模式下可选
    "context": {                 // 可选的辅助内容。用于细化分析结果
        "markdown": "markdown 内容"
    }
} 
# 分析维度
"dimensions": [               // 分析维度，可选多个维度进行分析
        "LEGAL_COMPLIANCE",       // 法律合规性分析==》法律合规性==》许可证情况、法律诉讼记录
        "FINANCIAL_STATUS",       // 财务状况分析 ==》财务表格==》资产负债表、利润表、现金流量表
        "OPERATION_ANALYSIS",     // 经营情况分析==》经营数据==》收入、成本、利润、现金流
        "CREDIT_HISTORY",         // 信用记录分析==》信用记录==》贷款记录、还款记录、逾期记录
        "MARKET_POSITION",        // 市场地位分析==》市场地位==》市场份额、市场竞争力、市场风险
        "RISK_ASSESSMENT",        // 风险评估==》风险评估==》信用风险、市场风险、操作风险
        "MANAGEMENT_TEAM",        // 管理团队评估==》管理团队==》团队结构、团队能力、团队稳定性
        "INDUSTRY_ANALYSIS"       // 行业分析==》行业分析==》行业现状、行业发展趋势、行业风险
    ],

```
#### 2.1.2 响应参数
##### 1. 同步模式 (stream=false)
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "analysisId": "analysis_2024031512345",
        "sections": [
            {
                "title": "法律合规性审查",
                "content": "## 法律合规性审查\n\n1. 许可证情况...",
                "type": "markdown"
            },
            {
                "title": "诉讼记录分析",
                "content": "## 诉讼记录分析\n\n1. 未决诉讼情况...",
                "type": "markdown"
            }
        ]
    }
}
```


##### 2. 同步模式 (stream=true)
说明：
    流式输出，每部分包裹：包括标题和内容，类型（markdown）

2024-11-14 12:00:00 修改，改成“文本”流式输出
```json
 "## 法律合规性审查\n\n1. 许可证情况...",
// ... 流式返回后续内容
```

#####  3.  异步模式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "taskId": "task_2024031512345",
        "status": "PROCESSING"
    }
}
```

##### 4.回调模式
当使用异步模式并配置了回调地址时，分析完成后系统将向指定的回调地址发送 POST 请求：

```json
{
    "taskId": "task_2024031512345",
    "status": "COMPLETED",    // 任务状态：COMPLETED(完成), FAILED(失败)
    "result": {
        "analysisId": "analysis_2024031512345",
        "sections": [
            {
                "title": "法律合规性审查",
                "content": "## 法律合规性审查\n\n1. 许可证情况...",
                "type": "markdown"
            }
            // ... 其他分析部分
        ]
    },
    "error": {               // 仅在失败时返回
        "code": "500",
        "message": "处理超时"
    },
    "timestamp": "2024-03-15T12:34:56Z"
}
```

回调请求说明：
1. 请求方式：POST
2. Content-Type: application/json
3. 回调接口需返回 HTTP 200 状态码表示接收成功


## 2.2 异步结果查询接口

```
GET /api/v1/loan-analysis/task/{taskId}
```


#### 2.2.1 响应格式：
```
{
    "code": 200,
    "message": "success",
    "data": {
        "taskId": "task_2024031512345",
        "status": "COMPLETED",    // PROCESSING, COMPLETED, FAILED
        "result": {
            "analysisId": "analysis_2024031512345",
            "sections": [
                {
                    "title": "法律合规性审查",
                    "content": "## 法律合规性审查\n\n1. 许可证情况...",
                    "type": "markdown"
                }
                // ... 其他分析部分
            ]
        }
    }
}

```

## 2.3 错误码说明
| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 404 | 企业信息未找到 |
| 500 | 服务器内部错误 |




## 2.4 认证方式

系统采用 Bearer Token 认证机制，确保API调用的安全性。

### 2.4.1 认证流程

1. 获取访问令牌（Access Token）
   - 联系系统管理员获取 Token）

2. 使用访问令牌
   - 在请求头中添加 `Authorization: Bearer <token>`
   - token 有效期为24小时

### 2.4.2 请求头格式
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

### 2.4.3 错误响应
当认证失败时，系统将返回以下错误：

```json
{
    "code": 401,
    "message": "未授权访问",
    "data": null
}
```




## 2.5 Python 示例
创建同步调用
```python
import requests

url = "https://api.example.com/api/v1/loan-analysis"
headers = {
    "Authorization": "Bearer your_token",
    "Content-Type": "application/json"
}

payload = {
    "enterpriseId": "社会信用代码",
    "dimensions": [],
    "executionMode": "SYNC",
    "outputFormat": "MARKDOWN",
    "stream": False,
    "context": {
       "markdown": "markdown 内容"
    }
}

response = requests.post(url, json=payload, headers=headers)
print(response.json())
```

创建同步流式调用
```python
import requests

url = "https://api.example.com/api/v1/loan-analysis"
headers = {
    "Authorization": "Bearer your_token",
    "Content-Type": "application/json"
}

payload = {
    "enterpriseId": "社会信用代码",
    "dimensions": [],
    "executionMode": "SYNC",
    "outputFormat": "MARKDOWN",
    "stream": True
}

with requests.post(url, json=payload, headers=headers, stream=True) as response:
    for line in response.iter_lines():
        if line:
            print(line.decode())



```
创建异步调用

``` python
# 异步调用示例
import requests
import time

def create_async_analysis():
    url = "https://api.example.com/api/v1/loan-analysis"
    headers = {
        "Authorization": "Bearer your_token",
        "Content-Type": "application/json"
    }
    
    payload = {
        "enterpriseId": "社会信用代码",
        "dimensions": [],
        "executionMode": "ASYNC",
        "outputFormat": "MARKDOWN",
        "callbackUrl": "https://your-callback-url.com/webhook",
        "context": {
            "markdown": "markdown 内容"
        }
    }
    
    # 创建异步任务
    response = requests.post(url, json=payload, headers=headers)
    return response.json()

def check_task_status(task_id):
    url = f"https://api.example.com/api/v1/loan-analysis/task/{task_id}"
    headers = {
        "Authorization": "Bearer your_token"
    }
    
    response = requests.get(url, headers=headers)
    return response.json()

# 使用示例
def main():
    # 1. 创建异步任务
    result = create_async_analysis()
    task_id = result['data']['taskId']
    print(f"任务已创建: {task_id}")
    
    # 2. 轮询检查任务状态
    while True:
        status = check_task_status(task_id)
        if status['data']['status'] == 'COMPLETED':
            print("分析完成:")
            print(status['data']['result'])
            break
        elif status['data']['status'] == 'FAILED':
            print("分析失败:")
            print(status['data'].get('error'))
            break
        else:
            print("任务处理中...")
            time.sleep(5)  # 等待5秒后重试

if __name__ == "__main__":
    main()

```




请求
```
event: moduleStatus
data: {"status":"running","name":"FINANCIAL_STATUS"} 

event: answer
data: {"id":"","object":"","created":0,"model":"","choices":[{"delta":{"role":"assistant","content":""},"index":0,"finish_reason":null}]}


answer
event: answer
data: [DONE]

event: end
data: Stream has ended

```