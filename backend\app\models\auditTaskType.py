from mongoengine import Document, <PERSON>Field, IntField, DateTimeField, ListField, BooleanField, ReferenceField, ObjectIdField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List
from bson import ObjectId

# MongoEngine 模型
class AuditTaskType(Document):
    meta = {
        'collection': 'audit_task_types'
    }
    _id = ObjectIdField(primary_key=True, default=lambda:ObjectId())
    name = StringField(required=True)  # 任务类型名称
    code = StringField(required=True, unique=True)  # 任务类型代码：contract_extraction, resume_parsing, pii_detection, other
    description = StringField()  # 类型描述
    rules = ListField(StringField(), default=[])  # 关联的规则ID列表
    created_at = DateTimeField(default=datetime.now)  # 创建时间
    updated_at = DateTimeField(default=datetime.now)  # 更新时间
    is_active = BooleanField(default=True)  # 是否激活
    is_deleted = BooleanField(default=False)  # 软删除标记
    user_id = IntField(required=True)

# Pydantic 基础模型
class AuditTaskTypeBase(BaseModel):
    name: str
    code: str
    description: Optional[str] = None

# 创建任务类型请求模型
class AuditTaskTypeCreate(BaseModel):
    name: str
    code: str
    description: Optional[str] = None
    rules: Optional[List[str]] = []

# 更新任务类型请求模型
class AuditTaskTypeUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    rules: Optional[List[str]] = None
    is_active: Optional[bool] = None

    class Config:
        from_attributes = True
        extra = "allow"

# 任务类型响应模型
class AuditTaskTypeResponse(AuditTaskTypeBase):
    id: str
    rules: List[str] = []
    created_at: datetime
    updated_at: datetime
    is_active: bool

    class Config:
        from_attributes = True

# 任务类型列表响应模型
class AuditTaskTypeListResponse(BaseModel):
    id: str
    name: str
    code: str
    description: Optional[str] = None
    rules_count: int = 0
    is_active: bool

    class Config:
        from_attributes = True
