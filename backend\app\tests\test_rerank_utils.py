import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import pytest
from engines.rerank.rerank_utils import rerank_texts, rerank_texts_with_metadata, RerankResult

@pytest.fixture
def rerank_config():
    """测试用的 rerank 配置"""
    return {
        "api_key": "sk-BOstXojjXywbG7QlA76eA282A4724e91915d045512239d9e",
        "model": "bge-rerank-m3",
        "service_url": "http://api.roardata.cn/v1"
    }

@pytest.fixture
def sample_texts():
    """测试用的文本列表"""
    return [
        "北京是中国的首都",
        "上海是中国最大的城市",
        "广州是中国南方重要的经济中心",
        "深圳是中国重要的科技创新中心"
    ]

@pytest.fixture
def sample_texts_with_metadata():
    """测试用的带元数据的文本列表"""
    return [
        ("北京是中国的首都", {"city": "北京", "type": "capital"}),
        ("上海是中国最大的城市", {"city": "上海", "type": "metropolis"}),
        ("广州是中国南方重要的经济中心", {"city": "广州", "type": "economic"}),
        ("深圳是中国重要的科技创新中心", {"city": "深圳", "type": "tech"})
    ]

@pytest.mark.asyncio
async def test_rerank_texts_basic(rerank_config, sample_texts):
    """测试基本的重排序功能"""
    query = "中国的首都在哪里"
    results = await rerank_texts(
        query=query,
        texts=sample_texts,
        config=rerank_config
    )
    
    # 基本检查
    assert isinstance(results, list)
    assert len(results) > 0
    assert all(isinstance(r, RerankResult) for r in results)
    assert all(hasattr(r, 'text') and hasattr(r, 'score') for r in results)
    
    # 检查分数排序
    scores = [r.score for r in results]
    assert scores == sorted(scores, reverse=True)  # 确保分数是降序的

@pytest.mark.asyncio
async def test_rerank_texts_with_top_k(rerank_config, sample_texts):
    """测试 top_k 参数"""
    query = "中国的大城市"
    top_k = 2
    results = await rerank_texts(
        query=query,
        texts=sample_texts,
        config=rerank_config,
        top_k=top_k
    )
    
    assert len(results) == top_k

@pytest.mark.asyncio
async def test_rerank_texts_with_threshold(rerank_config, sample_texts):
    """测试分数阈值过滤"""
    query = "中国的科技城市"
    threshold = 0.5
    results = await rerank_texts(
        query=query,
        texts=sample_texts,
        config=rerank_config,
        score_threshold=threshold
    )
    
    assert all(r.score >= threshold for r in results)

@pytest.mark.asyncio
async def test_rerank_texts_with_metadata(rerank_config, sample_texts_with_metadata):
    """测试带元数据的重排序"""
    query = "中国的首都"
    results = await rerank_texts_with_metadata(
        query=query,
        texts_with_metadata=sample_texts_with_metadata,
        config=rerank_config
    )
    
    assert all(hasattr(r, 'metadata') for r in results)
    assert all(isinstance(r.metadata, dict) for r in results)
    assert all('city' in r.metadata for r in results)
    assert all('type' in r.metadata for r in results)

@pytest.mark.asyncio
async def test_empty_input():
    """测试空输入的处理"""
    query = "测试查询"
    results = await rerank_texts(
        query=query,
        texts=[],
        config=rerank_config
    )
    assert results == []

@pytest.mark.asyncio
async def test_error_handling(rerank_config):
    """测试错误处理"""
    # 测试无效的 API key
    invalid_config = rerank_config.copy()
    invalid_config["api_key"] = "invalid_key"
    
    results = await rerank_texts(
        query="测试查询",
        texts=["测试文本"],
        config=invalid_config
    )
    assert results == []
    
    # 测试无效的服务 URL
    invalid_url_config = rerank_config.copy()
    invalid_url_config["service_url"] = "https://invalid.url"
    
    results = await rerank_texts(
        query="测试查询",
        texts=["测试文本"],
        config=invalid_url_config
    )
    assert results == []

@pytest.mark.asyncio
async def test_rerank_texts_with_metadata_and_top_k(rerank_config, sample_texts_with_metadata):
    """测试带元数据和 top_k 的重排序"""
    query = "中国的城市"
    top_k = 2
    results = await rerank_texts_with_metadata(
        query=query,
        texts_with_metadata=sample_texts_with_metadata,
        config=rerank_config,
        top_k=top_k
    )
    
    assert len(results) == top_k
    assert all(hasattr(r, 'metadata') for r in results)
    scores = [r.score for r in results]
    assert scores == sorted(scores, reverse=True)

if __name__ == "__main__":
    pytest.main(["-v", "test_rerank_utils.py"]) 