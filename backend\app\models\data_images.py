from mongoengine import Document, <PERSON>Field, DateTimeField, DictField, ObjectIdField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional
from bson import ObjectId

class StructuredDataRecord(Document):
    meta = {
        'collection': 'data_images'
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId)
    file_name = StringField(required=True)     # 文件名
    created_at = DateTimeField(default=datetime.now)
    storage_path = StringField(required=True)
    