#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import time
from datetime import datetime, timedelta
import traceback

# 导入金融舆情分析智能体
from financialPublicOpinionReport_weixin_V5_4 import run_agent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s'
)
logger = logging.getLogger(__name__)

def print_banner():
    """打印横幅"""
    banner = """
============================================================
                金融舆情分析智能体 - 历史报告生成
                     版本: V5.4
            启动时间: {}
============================================================
""".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print(banner)

def run_historical_reports(days=1):
    """循环生成近30天（每天一份）的金融舆情分析报告"""
    print_banner()
    logger.info("+" + "="*60 + "+")
    logger.info("|" + " "*20 + "开始批量运行金融舆情分析智能体" + " "*19 + "|")
    logger.info("+" + "="*60 + "+")
    
    all_results = []
    total_reports = 27  # 从3到30天，共27天
    success_count = 0
    error_count = 0
    
    for i in range(3,30):
        # 计算本次循环的时间区间（都是从0点开始）
        fetch_end_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=i)
        fetch_start_time = fetch_end_time - timedelta(days=days)
        
        try:
            start_time = datetime.now()
            progress = f"[{i-2:2d}/{total_reports}]"
            date_range = f"{fetch_start_time.strftime('%Y-%m-%d')} 至 {fetch_end_time.strftime('%Y-%m-%d')}"
            
            logger.info("-" * 70)
            logger.info(f"{progress} 开始生成报告...")
            logger.info(f"日期区间: {date_range}")
            
            result = run_agent(fetch_start_time, fetch_end_time)
            
            if result.get("status") == "success":
                end_time = datetime.now()
                elapsed_time = (end_time - start_time).total_seconds()
                success_count += 1
                
                logger.info(f"{progress} [成功] 金融舆情分析报告生成完成")
                logger.info(f"        耗时: {elapsed_time:.2f} 秒")
                logger.info(f"        存储路径: {result.get('storage_path', '未知')}")
                
                result["date"] = fetch_end_time.strftime("%Y-%m-%d")
                all_results.append(result)
            else:
                error_count += 1
                logger.error(f"{progress} [失败] {result.get('message', '未知错误')}")
                all_results.append({
                    "status": "error",
                    "message": result.get("message", "未能生成报告文本"),
                    "date": fetch_end_time.strftime("%Y-%m-%d")
                })
            
            # 显示进度
            percent = (i - 2) / total_reports * 100
            progress_bar = "=" * int(percent/2) + ">" + " " * (50 - int(percent/2))
            logger.info(f"进度: [{progress_bar}] {percent:3.1f}%")
            
            # 添加间隔，避免请求过于频繁
            if i < 29:  # 不是最后一个
                logger.info("等待5秒后继续...")
                time.sleep(5)
            
        except Exception as e:
            error_count += 1
            logger.error(f"{progress} [错误] {str(e)}")
            traceback.print_exc()
            all_results.append({
                "status": "error",
                "message": str(e),
                "date": fetch_end_time.strftime("%Y-%m-%d")
            })
    
    # 输出总结信息
    logger.info("+" + "="*60 + "+")
    logger.info("|" + " "*25 + "生成报告完成" + " "*24 + "|")
    logger.info("+" + "="*60 + "+")
    logger.info(f"总报告数: {total_reports}  成功: {success_count}  失败: {error_count}")
    
    return all_results

if __name__ == "__main__":
    run_historical_reports()
