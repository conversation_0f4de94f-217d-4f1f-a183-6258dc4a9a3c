import asyncpg
from typing import List, Dict, Any
import json
from .embedTest import embedding
from dotenv import load_dotenv
from pathlib import Path
import os

# 获取 .env 文件路径
current_dir = Path(__file__).parent
env_path = (current_dir.parent / '.env').resolve()
load_dotenv(env_path)

# 添加常量配置
MAX_CONTENT_LENGTH = 500  # 最大内容长度
SIMILARITY_THRESHOLD = 0.8  # 相似度阈值（越小表示越相似）

def truncate_content(content: str, max_length: int = MAX_CONTENT_LENGTH) -> str:
    """
    截断内容到指定长度，保持完整句子
    """
    if len(content) <= max_length:
        return content
        
    # 在最大长度位置查找最近的句子结束符
    end_markers = ['.', '。', '!', '！', '?', '？']
    truncated = content[:max_length]
    
    # 找到最后一个句子结束符的位置
    last_marker_pos = max(truncated.rfind(marker) for marker in end_markers)
    
    if last_marker_pos > 0:
        return content[:last_marker_pos + 1]
    return truncated + "..."

async def get_pg_pool():
    return await asyncpg.create_pool(
        user=os.getenv('POSTGRES_USER'),
        password=os.getenv('POSTGRES_PASSWORD'),
        host=os.getenv('POSTGRES_HOST'),
        port=os.getenv('POSTGRES_PORT', '5433'),
        database=os.getenv('POSTGRES_DB')
    )

async def parallel_knowledge_search(
    query: str,
    knowledge_base_ids: List[str]
) -> List[Dict[str, Any]]:
    """
    使用 pgvector 进行并行知识检索
    """
    pool = await get_pg_pool()
    async with pool.acquire() as conn:
        # 1. 创建 vector 扩展（如果还没创建）
        await conn.execute('CREATE EXTENSION IF NOT EXISTS vector;')
        
        # 2. 获取 embedding
        res = embedding(query)
        vector_data = res.get("data")[0].get("embedding")
        
        # 3. 将 Python 列表转换为 PostgreSQL 向量
        vector_str = f"[{','.join(map(str, vector_data))}]"
        
        # 4. 修改 SQL 查询，使用 vector 类型转换
        results = await conn.fetch("""
            SELECT 
                id,
                data,
                mode,
                knowledge_base_id, 
                collection_id,
                (embedding_vector <=> $1::vector) as score
            FROM knowledge_data 
            WHERE knowledge_base_id = ANY($2)
            AND is_active = TRUE
            AND (embedding_vector <=> $1::vector) < $4
            ORDER BY embedding_vector <=> $1::vector
            LIMIT $3
        """, vector_str, knowledge_base_ids, 5, SIMILARITY_THRESHOLD)
        
        # 5. 转换结果格式
        return [{
            'id': str(r['id']),
            'content': truncate_content(r['data']),
            'answer': truncate_content(r['data']),
            'question': query,
            'knowledge_base_id': str(r['knowledge_base_id']),
            'collection_id': str(r['collection_id']),
            'score': float(r['score']),
            'mode': r['mode']
        } for r in results]