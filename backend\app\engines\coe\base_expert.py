from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

@dataclass
class ExpertResponse:
    """专家响应结果"""
    content: str  # 响应内容
    confidence: float  # 置信度
    metadata: Optional[Dict[str, Any]] = None  # 元数据
    source_documents: Optional[List[Dict]] = None  # 源文档

class BaseExpert(ABC):
    """基础专家类"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        
    @abstractmethod
    async def can_handle(self, query: str) -> float:
        """
        判断是否能处理该查询
        
        Args:
            query: 用户查询
            
        Returns:
            float: 置信度分数 (0-1)
        """
        pass
    
    @abstractmethod
    async def handle(self, query: str, context: Optional[Dict] = None) -> ExpertResponse:
        """
        处理查询
        
        Args:
            query: 用户查询
            context: 上下文信息
            
        Returns:
            ExpertResponse: 专家响应结果
        """
        pass
    
    def __str__(self) -> str:
        return f"{self.name} - {self.description}" 