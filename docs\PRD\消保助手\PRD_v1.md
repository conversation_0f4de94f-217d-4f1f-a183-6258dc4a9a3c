# 消保 V1.

目标：
1. 第一个版本的目的是基于黑猫投诉实现mvp版本的消保信息问答。
2. 跑通基于agnet的数据问答数据交互、检索、分析数据及可视化。
3. 作为后续版本的基础，迭代来发；迭代的目标是从检索问答、但交互分析--》报告生成（报告不在此版本里）。
4. 此按本主要基于数据团队的5个表。
5. 参考产品： 秘塔（左侧）


## 数据处理
为了满足交互需求在数据基础上进加工。
目前知识文本，文本交互
1. 原始数据字典 :消保库数据字典.xlsx
2. 数据需要打标签（这两个需要小唐在调研下，是个二级分类）需要在数据字段上家二级标签字段。使用llm识别打标签
   1. 内部分类：按照内部业务：催收类、费用类、征信类、协商还款、规则及操作类、其他类型、欺诈类等（）

| 类别名称               | 分类依据                                                                 | 典型问题                                                                 |
|------------------------|--------------------------------------------------------------------------|--------------------------------------------------------------------------|
| **催收类投诉**          | 涉及债务追讨过程中采取的不当行为，违反法律法规或道德规范                   | 恶意电话轰炸；骚扰亲属；泄露隐私；言语威胁                               |
| **费用类投诉**          | 涉及收费环节的透明度或合理性争议                                           | 信用卡年费争议；高额服务费/利息；支付机构无故扣费；理财产品不合理收费    |
| **征信类投诉**          | 涉及个人信用记录的不当处理                                                 | 错误上传逾期记录；未及时更新还款状态；结清后仍占用授信额度               |
| **协商还款类投诉**      | 涉及协商还款计划的障碍                                                     | 拒绝个性化分期；协商流程冗长；客服推诿                                   |
| **规则及操作类投诉**    | 涉及业务流程或系统操作中的不规范行为                                       | 擅自开通分期；自动扣款失败导致逾期；APP功能异常                          |
| **欺诈类投诉**          | 涉及虚假宣传、诱导销售、非法代理维权等欺诈行为                             | 理财虚假宣传；保险夸大收益；代理退保骗局；AI新型诈骗                     |
| **数据安全与隐私类投诉**| 涉及用户个人信息泄露或滥用                                                 | 泄露客户信息；违规使用数据；账户无故冻结                                 |
| **服务态度与质量类投诉**| 涉及服务人员的态度或效率问题                                               | 客服响应迟缓；服务态度恶劣；投诉处理周期长                               |

### 新的二级分类
| 一级分类 | 二级分类 | 定义 | 示例 |
|---------|---------|------|------|
| 催收类投诉 | 暴力/威胁催收 | 使用恐吓、辱骂、人身威胁等非法手段催收 | 短信/电话威胁、冒充公检法、上门暴力催收 |
| | 骚扰性催收 | 超出合理频率或范围的催收行为 | 高频电话轰炸（每日超5次）、骚扰无关第三人 |
| | 隐私泄露催收 | 非法获取或公开借款人隐私信息 | 未经授权查询通讯录、公开借款人身份证/住址信息 |
| 费用及息费类投诉 | 隐性收费争议 | 未明确告知的附加费用或条款 | 信用卡分期手续费未提前说明、贷款服务费隐藏于合同 |
| | 高利贷/息费过高 | 综合年化利率超出法定上限或显著高于市场水平 | 网贷年化利率超36%、信用卡循环利息计算争议 |
| | 自动扣费争议 | 未经用户授权或未及时关闭的自动扣款 | 免密支付自动续费、贷款提前还款仍被扣息 |
| 征信与授信类投诉 | 征信记录错误 | 信用报告中存在不实信息 | 未逾期却被标记逾期、还款记录未及时更新 |
| | 授信管理问题 | 授信额度或账户状态管理不当 | 已结清贷款仍显示占用额度、账户关闭后仍影响征信 |
| | 征信修复障碍 | 对征信异议处理流程的投诉 | 银行拖延更正错误记录、缺乏有效申诉渠道 |
| 服务流程类投诉 | 协商还款障碍 | 还款困难时协商渠道受阻 | 拒绝个性化分期、客服推诿不转接专员 |
| | 系统操作故障 | 因技术问题导致用户权益受损 | APP还款失败仍计逾期、自动扣款系统错误 |
| | 业务规则不透明 | 业务流程或条款解释不清晰 | 信用卡积分规则模糊、贷款提前还款违约金标准不明 |
| 欺诈与诱导类投诉 | 虚假宣传 | 夸大产品收益或隐瞒风险 | 理财"保本保息"承诺、信用卡开卡奖励未兑现 |
| | 诱导销售 | 误导性话术促成非理性消费 | 电话推销隐瞒贷款真实利率、诱导老年人购买高风险保险 |
| | 非法代理维权 | 第三方机构以维权名义实施诈骗 | 收费"征信修复"骗局、伪造材料代理退保 |
| 数据安全与账户类投诉 | 信息泄露 | 用户数据被非法获取或使用 | 客户信息被倒卖、内部员工泄露交易记录 |
| | 账户异常操作 | 非用户本人发起的账户变动 | 银行卡盗刷、支付账户被冒名绑定 |
| | 风控误判 | 系统错误限制用户正常使用 | 无故冻结账户、误标记为风险交易 |
| 服务质量类投诉 | 响应效率低下 | 未在承诺时间内处理问题 | 投诉工单超7天未回复、电话客服长期排队 |
| | 服务态度恶劣 | 工作人员语言或行为失当 | 客服嘲讽用户、故意拖延解决问题 |
| | 处理结果不合理 | 解决方案未满足基本诉求 | 仅道歉不赔偿实际损失、重复投诉无升级机制 |

   2. 外部分类：按照监管需求：其他、国库、债务催收、其他中间业务、银行代理业务、个人金融信息、贵金属、外汇、人民币管理、支付结算、自营理财、银行卡、贷款、人民币储蓄




3. 实体：
   1. 需要产品/服务分类，类型、产品名称。
   2. 企业实体，可以通过简称模糊搜索（这个靳许有接口，从简称到全程）
4. 情感分类（正中负）：需要将情感映射具体的维度，例如对于服务态度是“正“，产品质量”负“
5. 涉及到的消费者权利。
6. 紧急程度分级
7. 目前数据在朱超峰的大es里，靳许有访问权限应该。需要增加的打打标签，需要同步到我们的存储里。

```
法律与政策框架
   《中华人民共和国消费者权益保护法》
   《关于加强金融消费者权益保护工作的指导意见》
   《金融消费者权益保护实施办法》
   银保监会监管评价办法
消费者基本权益
   财产安全权
   知情权
   自主选择权
   公平交易权
   依法求偿权
   受教育权
   受尊重权
   信息安全权
消保工作核心
   维护金融消费者权利
   提升获得感、幸福感、安全感
桥梁作用
   建立信任关系
全流程管理
   销售行为与服务体验管控
文化理念
   “人人消保”文化
消保审查
   监管要求与行业现状
宣传教育
   普及金融知识
投诉处理
   客户投诉机制优化
```



## 问答功能

| 分类       | 说明    | 示例题     |
|-------|-------------|------|
| **检索类** | 基于模糊或精确条件查询具体数据，支持多维度筛选                       | 1. 查询“XX商家”近30天的投诉总量及处理状态<br>2. 根据投诉编号SN-20230901获取该投诉的详细内容及处理进度<br>3. 搜索标题包含“退款问题”的投诉记录<br>4. 列出“黑榜”中行业为“电商”的商家名称和排名<br>5. 查找2023年9月1日至10月1日期间新增的集体投诉案例<br>6. 显示用户ID为“UID-123”发布的所有评论内容<br>7. 获取“红榜”中回复率高于90%的商家列表<br>8. 查询投诉类型为“集体投诉”且涉及金额大于5000元的记录<br>9. 检索“XX产品型号”相关的所有投诉问题摘要<br>10. 查找最近7天投诉量增长超过50%的商家名称及增长量          |
| **统计类** | 按条件输出数值统计结果，支持可视化展示（如柱状图、折线图、饼图等）   | 1. 统计各行业（如电商、金融）的月均投诉量排名<br>2. 计算“XX商家”近一年的投诉回复率趋势图<br>3. 展示红黑榜中不同类型榜单的商家数量分布（饼图）<br>4. 统计各地区的投诉量占比及涉诉金额平均值<br>5. 输出“集体投诉”与“个人投诉”的解决时长对比（箱线图）<br>6. 分析不同产品类型的投诉问题数量TOP5<br>7. 统计商家“XX公司”的投诉处理效率（响应时间中位数）<br>8. 展示最近30天投诉量、回复量、完成量的每日变化折线图<br>9. 计算各行业投诉量环比增长率（表格+柱状图）<br>10. 统计“一周投诉飙升榜”中商家投诉量的历史波动情况          |
| **对比类** | 多维度对比不同实体或时间段的数据差异                                 | 1. 对比“A公司”与“B公司”近半年的投诉量、回复率、完成率<br>2. 比较红榜与黑榜商家的平均响应时间差异<br>3. 分析2023年Q3与Q4的集体投诉量变化趋势<br>4. 对比“电商”与“金融”行业的投诉类型分布差异<br>5. 同一商家在不同季度的红黑榜排名变化对比<br>6. 不同产品型号的投诉解决时长对比（如型号X vs 型号Y）<br>7. 对比个人投诉与集体投诉的平均涉诉金额<br>8. 同一问题在不同地区的处理效率差异（如北京 vs 上海）<br>9. 商家“XX”在“周榜”与“月榜”中的投诉量排名对比<br>10. 对比不同投诉状态（处理中/已关闭）的平均处理周期          |
| **分析类** | 挖掘数据规律、原因或趋势，提供深层洞察                               | 1. 分析“XX商家”投诉量激增的可能原因（如特定产品/服务问题）<br>2. 探究投诉回复率与商家红黑榜排名的相关性<br>3. 预测下季度“电商行业”投诉量的潜在增长点<br>4. 识别投诉处理效率低的商家的共性特征（如行业/规模）<br>5. 分析集体投诉处理率与用户满意度的关联性<br>6. 挖掘投诉问题高频关键词（如“退款延迟”“质量缺陷”）的分布规律<br>7. 评估“涉诉金额”对投诉解决时长的影响程度<br>8. 分析不同统计方式（周榜/月榜）对红黑榜排名稳定性的影响<br>9. 探究商家Logo更新频率与用户评价的正向关联性<br>10. 识别投诉处理流程中耗时最长的环节（如分配商家→首次回复）          |
| **实体类** | 围绕具体公司、产品或投诉案例的详细信息或动态                         | 1. 展示“XX公司”在红黑榜中的历史排名变化及当前行业位置<br>2. 查询“产品型号Y”的所有投诉记录及解决状态<br>3. 获取投诉编号SN-20231001的处理流程时间线及责任人<br>4. 分析“XX商家”的投诉类型分布（如服务/质量/物流）<br>5. 查看用户“UID-456”发布的所有投诉及评论互动详情<br>6. 列出“XX行业”Top3投诉商家的关键指标（投诉量/回复率/完成率）<br>7. 追踪“集体投诉编号C-1001”的当前处理进度及参与用户数<br>8. 获取“XX产品”的投诉高频问题及对应解决方案建议库<br>9. 展示“XX公司”近一年各季度的用户满意度得分趋势<br>10. 查询“红榜”第1名商家的详细运营指标（如响应时间/投诉闭环率）          |
| **辅助类** | 基于数据规则或字段含义的解释性问答                                   | 1. 解释“红黑榜”排名算法的具体影响因子及权重<br>2. 说明“投诉状态”字段中数值3/4/6/7/8分别代表什么含义<br>3. 定义“集体处理率”的计算公式及数据来源<br>4. 如何区分“stat_way”中1/2/3的统计方式？<br>5. “ES查询匹配方式”中的“关键词+完全”具体如何应用？<br>6. “涉诉金额”字段是否包含未解决的投诉案例？<br>7. 说明“crawler_time”与“update_time”的数据更新逻辑差异<br>8. “stat_class”中类别1/2/3的具体覆盖范围是什么？<br>9. 如何理解“rank_type”为0时的榜单生成规则？<br>10. 解释“valid30d”与“valid_amt”字段的统计周期差异          |

**设计说明**：  
1. **覆盖多表关联**：问题设计整合了商家列表、投诉问题表、红黑榜等核心表的关键字段（如`valid30d`、`rank_type`、`stat_way`）。  
2. **贴近实际场景**：例如消费者查询商家信誉、企业监控自身投诉处理效率、监管机构分析行业风险等。  
3. **支持复杂条件**：结合时间范围、数值比较（>5000）、多字段组合（行业+投诉类型）等筛选逻辑。  
4. **动态扩展性**：预留字段解释类问题，帮助用户理解数据底层逻辑，增强问答系统的透明性。


## 其他要求
1. 多轮对话：同一个会话中可以多轮对话
2. 可以展示图表：image copy 3.png等
3. 支持 ```<think>``` 的支持展示
4. 功能也是”系统应用“可以在系统里选择替换llm服务。
5. 数据不一定入系统库。可以封装成api 包装成 agent tool
6. 页面包括问题结果，分析结果、引用数据、图表等。需要小唐按照秘塔简答还几个图
7. 涉及到多个结果或者中间结果输入的，需要将执行状态尽量输出到前端。采用目前的输出格式。


## 分工
- 产品：贾、清伟
- agent及后台开发，少伟
- 前端交互：青雯、少伟
- 数据处理：靳许
- 验收：清伟、贾
技术栈：
- 数据分析llm + agent工作流
- 前端：antd 、ant-x
- agent：langgraph