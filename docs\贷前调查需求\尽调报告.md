
## 贷前分析

## 合规及法律风险维度

| 合规及法律风险维度           | 目的                                       | 数据来源                                                                                       | 分析方案 / 关键指标与参考标准                                          |
|------------------------------|--------------------------------------------|------------------------------------------------------------------------------------------------|------------------------------------------------------------------------|
| **法律合规性审查**           | 确认公司是否符合法律法规要求                | - 国家企业信用信息公示系统<br>- 行业主管部门网站<br>- 商业查询平台（企查查、天眼查等）           | - **许可证齐备性**：是否具备经营所需的全部许可证<br>- **合规率** = 实际合规记录 / 应合规要求（参考值：100%） |
| **诉讼和争议记录调查**       | 识别未决诉讼、历史纠纷，评估潜在法律风险    | - 中国裁判文书网<br>- 信用中国<br>- 商业查询平台                                                | - **未决诉讼数量** = 当前未决诉讼案件数量（参考值：越少越好）<br>- **诉讼金额占比** = 未决诉讼金额 / 净资产（参考值：<5%） |
| **知识产权风险调查**         | 保护核心知识产权，避免侵权风险              | - 国家知识产权局专利和商标查询系统<br>- 商业查询平台                                           | - **专利/商标数量** = 注册专利/商标数量<br>- **知识产权诉讼数量**（参考：无知识产权诉讼为佳）<br>- **技术依赖度** = 关键技术依赖程度（参考：单一技术依赖<50%） |
| **环境合规性**               | 检查环境违规情况，确保环保合规              | - 生态环境部监管平台<br>- 信用中国<br>- 公司环境报告                                            | - **环保违规次数** = 环保处罚次数（参考：无违规记录）<br>- **环境罚金金额** = 罚金总额 / 净利润（参考值：<1%） |
| **数据保护和隐私合规性**     | 确保数据安全和隐私合规，避免数据泄露风险    | - 公司官网隐私政策<br>- 网信办发布信息<br>- 行业内新闻和媒体报道                                | - **隐私政策合规性**：政策是否符合《个人信息保护法》要求<br>- **数据泄露事件数量**（参考：0） |
| **劳动法合规性**             | 保障员工权益，避免劳动争议                  | - 劳动法合规审查<br>- 人社部处罚记录<br>- 公司员工手册和合同                                   | - **劳动争议次数** = 劳动争议案件数量（参考：0）<br>- **福利合规性**：员工福利是否符合《劳动法》规定 |
| **政策与监管变动风险评估**   | 识别新法规对业务的潜在影响                  | - 政府官网<br>- 行业协会<br>- 国家法律法规数据库                                              | - **法规变动频率** = 近5年法规变更次数（参考值：频率越低越好）<br>- **合规成本变化**：法规更新前后合规成本的变化率 |                                               | - 相关法规变动信息<br>- 新法规影响预估<br>- 风险适应性 |


	1.	法律合规性审查：通过许可证的齐备性和合规率判断公司是否符合行业监管要求。
	2.	诉讼和争议记录调查：关注未决诉讼和历史纠纷，未决诉讼金额占比用于评估可能的财务影响。
	3.	知识产权风险调查：专利、商标数量及知识产权诉讼情况判断公司知识产权的稳固性，避免技术或商标纠纷。
	4.	环境合规性：环保违规次数和罚金金额的占比用于评估公司的环境合规情况，尤其是涉及制造业、能源业的企业。
	5.	数据保护和隐私合规性：隐私政策合规性和数据泄露事件数量反映公司的数据安全和隐私保护措施。
	6.	劳动法合规性：劳动争议次数和福利合规性检查公司对员工权益的保障，降低潜在的劳动纠纷风险。
	7.	政策与监管变动风险评估：法规变动频率和合规成本变化率用于评估公司对政策更新的适应性，尤其是高监管行业。

## 风险分析表

| 风险分析维度                 | 目的                                   | 数据来源                                                                                   | 分析方案 / 关键指标与参考标准                                    |
|------------------------------|----------------------------------------|--------------------------------------------------------------------------------------------|-------------------------------------------------------------------|
| **财务风险**                | 评估财务健康状况，识别资金链风险       | - 公司财务报表<br>- 信用评级报告<br>- 财务分析工具                                         | - **流动比率** = 流动资产 / 流动负债（参考值：1.5-2）<br>- **速动比率** = （流动资产 - 存货） / 流动负债（参考值：1）<br>- **负债比率** = 总负债 / 总资产（参考值：50%以内）<br>- **现金流健康度** = 经营活动现金流 / 短期负债（参考值：>1） |
| **运营风险**                | 评估供应链、生产及技术风险             | - 供应链合作商调查<br>- 生产和库存报告<br>- 技术研发文档                                    | - **供应链依赖度** = 主要供应商占采购总额比例（参考值：单一供应商<30%）<br>- **库存周转率** = 销售成本 / 平均库存（参考值：制造业一般为4-6次/年）<br>- **技术依赖风险** = 单一技术收入占比或单一产品收入占比（参考值：<50%） |
| **市场风险**                | 识别市场需求变化和竞争压力             | - 行业市场报告<br>- 市场份额数据<br>- 竞争对手分析                                         | - **市场份额变化率** = （期末市场份额 - 期初市场份额） / 期初市场份额<br>- **需求增长率** = （期末需求 - 期初需求） / 期初需求（行业标准根据具体市场定）<br>- **竞争对手数量和份额** = 市场份额前三竞争对手的集中度（CR3），>60%表示高度集中，<40%为低集中度 |
| **法律及合规风险**          | 评估因法规变化导致的合规风险           | - 政策法规更新<br>- 行业协会报告<br>- 公司法律顾问意见                                     | - **新法规影响**：分析新法规对合规成本及收入的影响（例如增加合规成本或新费用）<br>- **合规成本变化**：新法规实施前后合规成本占比<br>- **违约可能性**：是否因法规不符而可能遭遇诉讼或处罚，增加法律费用预算比例 |
| **ESG（环境、社会和治理）风险** | 评估环境合规、社会责任和治理风险       | - ESG评级报告<br>- 环保部门处罚记录<br>- 社会责任报告                                      | - **环境违规记录** = 环保处罚次数（参考：无违规记录）<br>- **社会责任合规度** = 实际支出社会责任金额 / 营业收入（参考值：行业标准0.1%-1%）<br>- **治理结构健全度** = 是否建立董事会、监事会和股东大会等治理结构（合规标准） |
| **信用风险**                | 评估公司偿债能力及信用状况             | - 信用评级<br>- 历史信用记录<br>- 银行贷款记录                                             | - **信用评分**：根据信用评级机构打分（例如AAA、AA，行业内常规标准）<br>- **债务负担率** = 有息负债 / 净资产（参考值：<50%）<br>- **利息保障倍数** = 息税前利润 / 利息费用（参考值：>3） |
| **外部环境风险**            | 识别因宏观经济、政策环境变化带来的风险 | - 宏观经济数据<br>- 经济政策动态<br>- 外部经济环境报告                                     | - **汇率变化率** = （期末汇率 - 期初汇率） / 期初汇率（对于出口公司，波动越小越稳定）<br>- **利率变化率** = 当前利率 - 基准利率（影响融资成本）<br>- **通货膨胀率** = 消费者物价指数CPI变化率（高通胀对原材料成本和消费有压力） |


	1.	财务风险指标：流动比率和速动比率用于衡量短期偿债能力，负债比率和现金流健康度反映财务结构是否稳健。
	2.	运营风险指标：库存周转率等反映运营效率，供应链和技术依赖度反映业务稳定性。
	3.	市场风险指标：通过市场份额、需求增长率等衡量公司在市场中的竞争力和需求变化。
	4.	合规性指标：评估法规变化对公司的影响，避免因不合规导致的风险。
	5.	ESG风险：聚焦环境保护、社会责任及公司治理结构的健全程度。
	6.	信用风险：信用评分和利息保障倍数反映公司偿债能力和信用状况。
	7.	外部环境风险：关注汇率、利率和通胀变化，以评估经济环境对业务的潜在影响。





    我有个需求，是根据一个企业的基础信息进行贷前分析，分析维度包括：
    1.	法律合规性审查
    2.	诉讼和争议记录调查
    3.	知识产权风险调查
    4.	环境合规性
    5.	数据保护和隐私合规性
    6.	劳动法合规性
    7.	政策与监管变动风险评估
    

开发一个贷前分析的API进行服务，
API的输入：
    1. 企业的基础ID
    2. 分析维度，可以多选，分析维度英文列表
    3. 执行方式：同步执行（立即返回），异步执行（返回任务ID，通过任务ID查询结果）
    4. 分析报告的格式：markdown
    5. 输出方式：当是同步执行时，stream为true 流式输出，否则为false，返回完整的结果
    6. 辅助材料：辅助内容生成的上下文信息，使用json格式，key为

API的输出：
    同步 stream为 false时：
        格式json，包括是一个json对象列表，每个item是报告的一部分：
        每部分包裹：包括标题和内容，类型（markdown）
    当stream为true时：
        每部分包裹：包括标题和内容，类型（markdown）


    当为异步时：
        返回任务ID，通过任务ID查询结果




# 接口地址
```
POST /api/v1/loan-analysis
```


### 请求参数
```json
{
    "enterpriseId": "string",     // 企业ID，必填
    "dimensions": [               // 分析维度，必填，可多选
        "LEGAL_COMPLIANCE",       // 法律合规性审查
        "LITIGATION_RECORD",      // 诉讼和争议记录调查
        "IP_RISK",               // 知识产权风险调查
        "ENVIRONMENTAL",         // 环境合规性
        "DATA_PRIVACY",          // 数据保护和隐私合规性
        "LABOR_LAW",            // 劳动法合规性
        "REGULATORY_RISK"        // 政策与监管变动风险评估
    ],
    "executionMode": "SYNC",     // 执行方式：SYNC(同步) / ASYNC(异步)
    "outputFormat": "MARKDOWN",   // 输出格式，当前仅支持 MARKDOWN
    "stream": false,             // 是否流式输出，仅在同步模式下有效
    "context": {                 // 可选的辅助内容
        "industry": "制造业",
        "location": "浙江省",
        "establishedYear": 2010
        // ... 其他辅助信息
    }
}
```
### 响应参数
#### 1. 同步模式 (stream=false)
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "analysisId": "analysis_2024031512345",
        "sections": [
            {
                "title": "法律合规性审查",
                "content": "## 法律合规性审查\n\n1. 许可证情况...",
                "type": "markdown"
            },
            {
                "title": "诉讼记录分析",
                "content": "## 诉讼记录分析\n\n1. 未决诉讼情况...",
                "type": "markdown"
            }
        ]
    }
}
```


#### 2. 同步模式 (stream=true)
```json
{
    "title": "法律合规性审查",
    "content": "## 法律合规性审查\n\n1. 许可证情况...",
    "type": "markdown"
}
// ... 流式返回后续内容
```

#### 3. 异步模式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "taskId": "task_2024031512345",
        "status": "PROCESSING"
    }
}
```




# 异步结果查询接口

```
GET /api/v1/loan-analysis/task/{taskId}
```


### 响应格式：
```
{
    "code": 200,
    "message": "success",
    "data": {
        "taskId": "task_2024031512345",
        "status": "COMPLETED",    // PROCESSING, COMPLETED, FAILED
        "result": {
            "analysisId": "analysis_2024031512345",
            "sections": [
                {
                    "title": "法律合规性审查",
                    "content": "## 法律合规性审查\n\n1. 许可证情况...",
                    "type": "markdown"
                }
                // ... 其他分析部分
            ]
        }
    }
}

```

## 错误码说明
| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 404 | 企业信息未找到 |
| 500 | 服务器内部错误 |



### Python 示例
```python
import requests

url = "https://api.example.com/api/v1/loan-analysis"
headers = {
    "Authorization": "Bearer your_token",
    "Content-Type": "application/json"
}

payload = {
    "enterpriseId": "E123456789",
    "dimensions": ["LEGAL_COMPLIANCE", "LITIGATION_RECORD"],
    "executionMode": "SYNC",
    "outputFormat": "MARKDOWN",
    "stream": False,
    "context": {
        "industry": "制造业",
        "location": "浙江省"
    }
}

response = requests.post(url, json=payload, headers=headers)
print(response.json())
```
```
import requests

url = "https://api.example.com/api/v1/loan-analysis"
headers = {
    "Authorization": "Bearer your_token",
    "Content-Type": "application/json"
}

payload = {
    "enterpriseId": "E123456789",
    "dimensions": ["LEGAL_COMPLIANCE"],
    "executionMode": "SYNC",
    "outputFormat": "MARKDOWN",
    "stream": True
}

with requests.post(url, json=payload, headers=headers, stream=True) as response:
    for line in response.iter_lines():
        if line:
            print(line.decode())





```flowchart TD
    A[构建自动化评测工具] --> B[标准化测试数据集]
    A --> C[评估指标体系]
    B --> D[问答准确性]
    B --> E[语义理解]
    B --> F[业务适配性]
    C --> G[自动化脚本执行]
    C --> H[评分机制]
    C --> I[报告生成]
    G --> J[测试数据集金融行业定制]
    F --> K[业务关联性评估]
    I --> L[自动生成评测报告]
    L --> M[性能分析图表]
    M --> N[人工测试与深度审查]
    N --> O[人工反馈及问题记录]
    O --> P[评测数据反馈至调优团队]
    P --> Q[启动模型调优]
    Q --> R[Prompt优化]
    Q --> S[数据精调]
    Q --> T[参数调整]
    R --> U[模型迭代]
    S --> U
    T --> U
    U --> V[人工测试复检]
    V --> W[应用效果持续提升]
```