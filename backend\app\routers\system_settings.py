from fastapi import APIRouter, HTTPException
from typing import Optional
from ..models.system_settings import SystemSettings, SystemSettingsUpdate
from ..db.mongodb import db
from pathlib import Path
import aiofiles
from typing import List, Optional, Dict, Any
from ..utils.auth import verify_token
from app.utils.logging_config import setup_logging, get_logger
from fastapi import Depends, File, UploadFile
from datetime import datetime
from app.utils.config import settings
setup_logging()
logger = get_logger(__name__)
router = APIRouter()

@router.get("/api/system/config")
async def get_system_config():
    """获取系统配置"""
    settings_info = await db.system_settings.find_one()
    if settings_info:
        if "_id" in settings_info:
            del settings_info["_id"]
        if settings.system_name:
            settings_info["title"] = settings.system_name
            settings_info['name_locked'] = True  # 添加名称锁定参数
        return {"status": "ok", "data": settings_info}

    else:
        return {"status": "error", "message": "系统配置不存在"}



@router.post("/api/system/config")
async def update_system_config(logo: str, title: str, colorPrimary: str, navTheme: str, enable_coe: bool, current_user: dict = Depends(verify_token)):

    update_data = {}
    if logo:
        update_data["logo"] = logo
    if title:
        update_data["title"] = title
    if colorPrimary:
        update_data["colorPrimary"] = colorPrimary
    if navTheme:
        update_data["navTheme"] = navTheme
    if enable_coe is not None:
        update_data["enable_coe"] = enable_coe

    result = await db["system_settings"].update_one({}, {"$set": update_data})
    if result.matched_count == 0:
        return {
            "success": False,
            "message": "系统配置不存在"
        }
    else:
        updated_message = await db["system_settings"].find_one({})
        del updated_message["_id"]
        return {
        "success": True,
        "data": updated_message
    }



@router.post("/api/uploadLogo")
async def upload_logo(files: List[UploadFile] = File(...)):
    file_upload_path = Path("./static")
    if not file_upload_path.exists():
        file_upload_path.mkdir(parents=True, exist_ok=True)

    try:
        for file in files:
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{file.filename}"
            file_location = file_upload_path / filename
            
            async with aiofiles.open(file_location, "wb") as f:
                await f.write(await file.read())
            return {
                "success": True,
                "url": f'/{str(file_location)}'  # 转换为字符串以确保可以序列化
            }
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


