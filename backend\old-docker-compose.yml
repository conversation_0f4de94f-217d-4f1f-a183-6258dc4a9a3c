version: '3.10'

services:
  api:
    build: 
      context: .
      dockerfile: Dockerfile
    image: wiseagent-app
    extra_hosts:
      - "host.docker.internal:host-gateway"
    container_name: wiseagent-app
    restart: always
    environment:
      - ENV_MODE=prod
      - MONGODB_HOST=mongodb
      - MONGODB_PORT=27017
      - MONGODB_USER=wiseagent
      - MONGODB_PASSWORD= 1qaz@WSX
      - MONGODB_DATABASE=wiseagent
      - MONGODB_AUTH_SOURCE=${MONGODB_AUTH_SOURCE:-admin}
    command: uvicorn app.main:app --host 0.0.0.0 --port 8800
    ports:
      - "9700:8800"
    env_file:
      - .env.prod
    volumes:
      - /data/wiseagent/static:/app/static
    depends_on:
      - mongodb
    networks:
      - app-network

  mongodb:
    image: mongo:4.4
    container_name: wiseagent-app-mongodb
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: wiseagent
      MONGO_INITDB_ROOT_PASSWORD: 1qaz@WSX
      MONGO_INITDB_DATABASE: wiseagent
    volumes:
      - /data/wiseagent/mongodb_data:/data/db
    networks:
      - app-network

volumes:
  mongodb_data:

networks:
  app-network:
    driver: bridge 