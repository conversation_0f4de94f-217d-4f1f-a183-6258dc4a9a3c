from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.docs import (
    Doc,
    DocCreate,
    DocUpdate,
    DocResponse
)
from ..db.mongodb import db
from ..utils.auth import verify_token
from bson import ObjectId
from datetime import datetime

router = APIRouter(
    prefix="/api/docs",
    tags=["docs"]
)

# 创建文档
@router.post("/", response_model=Dict[str, Any])
async def create_doc(
    doc: DocCreate,
    current_user: dict = Depends(verify_token)
):
    new_doc = doc.dict()
    new_doc["created_at"] = datetime.now()
    new_doc["updated_at"] = datetime.now()
    new_doc["user_id"] = current_user["id"]
    new_doc["user_name"] = current_user["name"]
    new_doc["is_active"] = True
    new_doc["view_count"] = 0
    
    result = await db["docs"].insert_one(new_doc)
    created_doc = await db["docs"].find_one({"_id": result.inserted_id})
    
    return {
        "data": DocResponse(**created_doc),
        "success": True
    }

# 获取文档列表
@router.get("/", response_model=Dict[str, Any])
async def get_docs(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    title: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"is_active": True}
    
    if title:
        query["title"] = {"$regex": title, "$options": "i"}

    docs = await db["docs"].find(query).sort(
        "created_at", -1
    ).skip(skip).limit(pageSize).to_list(length=pageSize)
    
    total = await db["docs"].count_documents(query)
    
    return {
        "data": [DocResponse(**doc) for doc in docs],
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取单个文档（公开访问，需要密码验证）
@router.get("/{doc_id}", response_model=Dict[str, Any])
async def get_doc(
    doc_id: str,
    password: Optional[str] = None
):
    doc = await db["docs"].find_one({"_id": ObjectId(doc_id), "is_active": True})
    if not doc:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # 验证密码
    if doc.get("password") and doc["password"] != password:
        raise HTTPException(status_code=403, detail="Invalid password")
    
    # 更新查看次数
    await db["docs"].update_one(
        {"_id": ObjectId(doc_id)},
        {"$inc": {"view_count": 1}}
    )
    
    return {
        "data": DocResponse(**doc),
        "success": True
    }

# 更新文档
@router.put("/{doc_id}", response_model=DocResponse)
async def update_doc(
    doc_id: str,
    doc: DocUpdate,
    current_user: dict = Depends(verify_token)
):
    update_data = doc.dict(exclude_unset=True)
    update_data["updated_at"] = datetime.now()
    
    result = await db["docs"].update_one(
        {"_id": ObjectId(doc_id)},
        {"$set": update_data}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Document not found")
        
    updated_doc = await db["docs"].find_one({"_id": ObjectId(doc_id)})
    return DocResponse(**updated_doc)

# 删除文档（软删除）
@router.delete("/{doc_id}", response_model=dict)
async def delete_doc(
    doc_id: str,
    current_user: dict = Depends(verify_token)
):
    result = await db["docs"].update_one(
        {"_id": ObjectId(doc_id)},
        {
            "$set": {
                "is_active": False,
                "deleted_by": current_user["id"]
            }
        }
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Document not found")
        
    return {"message": "Document deleted successfully"} 