数据处理需求：

## 假设源文件包括
1. 标题
2. 正文
3. url
4. 发布时间

## 增加一个es索引，检索数据。可使用大模型抽取
0. 包括元字段
   
新增字段：
1. 内容摘要：内容摘要300字以内，可以用大模型生成
2. 事实摘要：时间、地点、人物、事件，可以用大模型生成
3. 作者观点：作者观点，可以用大模型生成
4. 作者态度：正向、负向、中性（针对事件而言）
5. 人物实体：例如["特朗普","拜登"]
6. 事件标签：例如["中美关系","俄乌战争"]
7. 机构实体：例如["白宫","国务院"]
8. 地点实体：例如["华盛顿","北京"]
9. 摘要的向量：
   字段名称和类型
   
   "embedding": {
        "type": "dense_vector",
        "dims": 1536
      }

## 筛选条件
包含特朗普的信息








