# import sys
# import os

# # 添加项目根目录到 Python 路径
# sys.path.insert(0, os.path.abspath(os.path.dirname(__file__))) 





import asyncio

from engines.embedding.embedding_utils import get_embedding_vector
from engines.rag.flow_rag_new import FlowRAG
from engines.retrieval.base_retriever import ESKeywordRetriever
from engines.rerank.rerank_utils import rerank_texts


config = {
        "api_key": "sk-BOstXojjXywbG7QlA76eA282A4724e91915d045512239d9e",
        "model": "bge-m3",
        "service_url": "http://**************:3001/v1"
    }
    
        # 单个文本转换
text = "这是一个测试文本"

embedding = asyncio.run(get_embedding_vector(text, config))
if embedding:
    print(f"向量维度: {len(embedding)}")
    print(embedding[:10])



test = FlowRAG(config={'vector_search':True,'graph_search':False,'rerank_config':{'api_key':'OTEzZWQ2OTc4YTBlZWY2MDBjYjc5ZGNjMDViYzg1NmIyNThkODg5MQ=='}})
result = test.run('实际控制')
print(result)
    


# test = ESKeywordRetriever(index_name='wise_agent_chunking',es_host='**************:9600')

# print(test.retrieve('实际控制人及'))
    


# data = {
#     "query":"国际贸易中的信用证是如何运作的？",
#     "inputs":[{"id":2,"text":  "信用证主要通过银行为买卖双方提供交易保障，确保卖方在履行合同后获得付款。"
# },{"id":1,"text":"买家通过银行支付，银行确保交易的安全"},{"id":3,"text":"信用证保证卖方在交货后立即收到货款。"},{"id":4,"text":"信用证是一种银行间的支付保障机制。"}]
# }
    
# data = {'query': '实际控制', 'inputs': [{'id': '3da6b46f158d4a88fe7ef8662f1563b9', 'text': '\n\n （一）实际控制人及其控制本公司情况的简要说明;\n\n （三）公司住所和营业场所;\n\n （五）经营范围和经营区域;\n\n （二）持股比例在5%以上的股东及其持股情况;\n\n （六）法定代表人;\n\n （四）成立时间;\n\n （八）重大事项信息;\n\n （九）中国银行保险监督管理委员会规定的其他信息'}, {'id': '82bcbb508b0a6a38dc17cc2b547e727c', 'text': ' |\n| 第十条保险公司披露的公司治理概要应当包括下列内容： |\n| （一）实际控制人及其控制本公司情况的简要说明; |\n| （二）持股比例在5%以上的股东及其持股情况; |\n| （三）近3年股东大会（股东会）主要决议，至少包括会 |\n| 开的时间、地点、出席情况、主要议题以及表决情况等； |\n| （四）董事和监事简历; （五）高级管理人员简历、职责及其履职情况; |\n\n （五）高级管理人员简历、职责及其履职情况;\n\n 第九条保险公司披露的公司概况应当包括下列内容：\n\n （八）各分支机构营业场所和联系电话'}, {'id': '5d3be4bbfe3011d62fcd3e7f50530e3a', 'text': '中国银行保险监督管理委员会规章\n\n股票交易的，该证券经营机构应当符合下列条件：\n\n （一）财务状况良好，经营稳健，净资本在10亿元人民币以上;\n\n （二）内部控制制度健全;\n\n （二）内部控制制度健全；\n\n （三）客户交易结算资金全额存入具有从事证券交易结算资金存管业务资格的商业银行：\n\n （四）在中国证券登记结算有限公司分别设立自营结算备付金账户和客户结算备付金账户；\n\n （五）其上海、深圳两个交易所的自营业务席位和非自营业务席位分别设立；\n\n （六）通讯条件和交易设施高效、安全，符合股票交易的要求，信息服务全面；\n\n （七）具备证券市场研究实力，能及时提供咨询服务；\n\n （八）最近3年无重大违法、违规记录，未受中国证监会处罚，且未处于立案调查过程中；\n\n （九）诚信方面无不良记录，最近1年无占用或者挪用客户保证金和证券的行为；\n\n （十）书面承诺接受中国保监会对保险机构投资者股票交易情况的检查，并向中国保监会如实提供保险机构投资者股票交易的各种资料；\n\n （十一）当地的营业部管理规范、经营良好、服务功能齐全;'}, {'id': '0faf025ef7381e93ddb7b8fd336ed005', 'text': ' 中国银行保险监督管理委员会规章\n\n名的保险公司应当在筹建阶段向银保监会提交下列材料：\n\n （一）更名申请书，其中应当载明拟更名公司的名称、组织形式、注册资本、住所（营业场所）、业务范围、筹备组织情况、联系人及联系方式等；\n\n （二）可行性研究报告，包括可行性分析、更名方式、公司治理和组织机构框架、发展战略、风险管理和内部控制体系、保险公司更名前后偿付能力评估等；\n\n （三）更名方案，包括拟设立的保险集团公司及其子公司的股权结构，理顺股权关系的总体规划和操作流程，子公司的名称和业务类别等；\n\n （四）筹备负责人材料，包括投资人关于认可筹备组负责人和拟任董事长、总经理任职的确认书，筹备组负责人基本情况本人认可证明，拟任董事长、总经理任职资格申请表，身份证明和学历学位证书复印件；\n\n （五）保险集团公司章程草案;\n\n （六）保险公司股东（大）会同意更名设立保险集团公司的决议；\n\n （七）保险公司最近3年经审计的财务报告、偿付能力报告；（八）更名后的营业执照；\n\n （七）保险公司最近3年经审计的财务报告、偿付能力报告；\n\n （八）更名后的营业执照；\n\n （九）住所（营业场所）所有权或者使用权的证明文件;6\n\n （九）住所（营业场所）所有权或者使用权的证明文件;\n\n'}]}

# print(rerank_texts(query=data['query'],texts=data['inputs'],config={}))


