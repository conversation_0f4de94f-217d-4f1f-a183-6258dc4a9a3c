# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
**/node_modules
# roadhog-api-doc ignore
/src/utils/request-temp.js
_roadhog-api-doc

# production
/dist
/backend/static/uploads/*.pdf

# misc
.DS_Store
npm-debug.log*
yarn-error.log

/coverage
.idea
yarn.lock
package-lock.json
*bak
.vscode


# visual studio code
.history
*.log
functions/*
.temp/**

# umi
.umi
.umi-production
.umi-test

# screenshot
screenshot
.firebase
.eslintcache

build


# Ignore all .pyc files
*.pyc

# Ignore __pycache__ directories
__pycache__/

# Specifically ignore .pyc files in the backend directory
backend/**/*.pyc

# Specifically ignore __pycache__ directories in the backend directory
backend/**/__pycache__/
notebook/data/
public/data_images/
