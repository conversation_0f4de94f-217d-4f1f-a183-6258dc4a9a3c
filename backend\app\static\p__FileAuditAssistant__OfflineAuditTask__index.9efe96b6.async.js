"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2600],{99011:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"}}]},name:"clock-circle",theme:"outlined"}},71879:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M885.2 446.3l-.2-.8-112.2-285.1c-5-16.1-19.9-27.2-36.8-27.2H281.2c-17 0-32.1 11.3-36.9 27.6L139.4 443l-.3.7-.2.8c-1.3 4.9-1.7 9.9-1 14.8-.1 1.6-.2 3.2-.2 4.8V830a60.9 60.9 0 0060.8 60.8h627.2c33.5 0 60.8-27.3 60.9-60.8V464.1c0-1.3 0-2.6-.1-3.7.4-4.9 0-9.6-1.3-14.1zm-295.8-43l-.3 15.7c-.8 44.9-31.8 75.1-77.1 75.1-22.1 0-41.1-7.1-54.8-20.6S436 441.2 435.6 419l-.3-15.7H229.5L309 210h399.2l81.7 193.3H589.4zm-375 76.8h157.3c24.3 57.1 76 90.8 140.4 90.8 33.7 0 65-9.4 90.3-27.2 22.2-15.6 39.5-37.4 50.7-63.6h156.5V814H214.4V480.1z"}}]},name:"inbox",theme:"outlined"}},82947:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"}},82826:function(e,t,n){n.d(t,{Z:function(){return o}});var r=n(1413),a=n(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"},c=n(91146),i=function(e,t){return a.createElement(c.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:s}))};var o=a.forwardRef(i)},8751:function(e,t,n){n.d(t,{Z:function(){return o}});var r=n(1413),a=n(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"},c=n(91146),i=function(e,t){return a.createElement(c.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:s}))};var o=a.forwardRef(i)},66212:function(e,t,n){var r=n(1413),a=n(67294),s=n(99011),c=n(91146),i=function(e,t){return a.createElement(c.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:s.Z}))},o=a.forwardRef(i);t.Z=o},18429:function(e,t,n){n.d(t,{Z:function(){return o}});var r=n(1413),a=n(67294),s={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"},c=n(91146),i=function(e,t){return a.createElement(c.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:s}))};var o=a.forwardRef(i)},4161:function(e,t,n){n.d(t,{Z:function(){return o}});var r=n(1413),a=n(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM528.1 472h-32.2c-5.5 0-10.3 3.7-11.6 9.1L434.6 680l-46.1-198.7c-1.3-5.4-6.1-9.3-11.7-9.3h-35.4a12.02 12.02 0 00-11.6 15.1l74.2 276c1.4 5.2 6.2 8.9 11.6 8.9h32c5.4 0 10.2-3.6 11.6-8.9l52.8-197 52.8 197c1.4 5.2 6.2 8.9 11.6 8.9h31.8c5.4 0 10.2-3.6 11.6-8.9l74.4-276a12.04 12.04 0 00-11.6-15.1H647c-5.6 0-10.4 3.9-11.7 9.3l-45.8 199.1-49.8-199.3c-1.3-5.4-6.1-9.1-11.6-9.1z"}}]},name:"file-word",theme:"outlined"},c=n(91146),i=function(e,t){return a.createElement(c.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:s}))};var o=a.forwardRef(i)},33914:function(e,t,n){var r=n(1413),a=n(67294),s=n(71879),c=n(91146),i=function(e,t){return a.createElement(c.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:s.Z}))},o=a.forwardRef(i);t.Z=o},74842:function(e,t,n){n.d(t,{Z:function(){return o}});var r=n(1413),a=n(67294),s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"},c=n(91146),i=function(e,t){return a.createElement(c.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:s}))};var o=a.forwardRef(i)},43471:function(e,t,n){var r=n(1413),a=n(67294),s=n(82947),c=n(91146),i=function(e,t){return a.createElement(c.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:s.Z}))},o=a.forwardRef(i);t.Z=o},48066:function(e,t,n){n.r(t),n.d(t,{default:function(){return me}});var r=n(52677),a=n.n(r),s=n(19632),c=n.n(s),i=n(97857),o=n.n(i),l=n(15009),d=n.n(l),u=n(99289),p=n.n(u),f=n(5574),x=n.n(f),h=n(67294),g=n(26058),m=n(71471),v=n(55102),j=n(11550),Z=n(42119),b=n(11941),y=n(8232),k=n(2453),w=n(4393),z=n(38925),_=n(42075),P=n(83622),S=n(34041),C=n(66309),B=n(2487),T=n(74330),L=n(32983),R=n(17788),M=n(8751),H=n(33914),O=n(15360),E=n(43471),I=n(82826),V=n(1413),A={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM514.1 580.1l-61.8-102.4c-2.2-3.6-6.1-5.8-10.3-5.8h-38.4c-2.3 0-4.5.6-6.4 1.9-5.6 3.5-7.3 10.9-3.7 16.6l82.3 130.4-83.4 132.8a12.04 12.04 0 0010.2 18.4h34.5c4.2 0 8-2.2 10.2-5.7L510 664.8l62.3 101.4c2.2 3.6 6.1 5.7 10.2 5.7H620c2.3 0 4.5-.7 6.5-1.9 5.6-3.6 7.2-11 3.6-16.6l-84-130.4 85.3-132.5a12.04 12.04 0 00-10.1-18.5h-35.7c-4.2 0-8.1 2.2-10.3 5.8l-61.2 102.3z"}}]},name:"file-excel",theme:"outlined"},F=n(91146),N=function(e,t){return h.createElement(F.Z,(0,V.Z)((0,V.Z)({},e),{},{ref:t,icon:A}))};var W=h.forwardRef(N),D=n(49354),J=n(4161),q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M553.1 509.1l-77.8 99.2-41.1-52.4a8 8 0 00-12.6 0l-99.8 127.2a7.98 7.98 0 006.3 12.9H696c6.7 0 10.4-7.7 6.3-12.9l-136.5-174a8.1 8.1 0 00-12.7 0zM360 442a40 40 0 1080 0 40 40 0 10-80 0zm494.6-153.4L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file-image",theme:"outlined"},K=function(e,t){return h.createElement(F.Z,(0,V.Z)((0,V.Z)({},e),{},{ref:t,icon:q}))};var G=h.forwardRef(K),U=n(97245),Q=n(82061),X=n(66212),Y=n(18429),$=n(74842),ee=n(47389),te=n(10981),ne=n(96974),re=n(97131),ae=n(88372),se=n(34994),ce=n(74573),ie=n(85893),oe=(g.Z.Content,m.Z.Title,m.Z.Text,m.Z.Paragraph,v.Z.TextArea,j.Z.Dragger),le=Z.Z.Step,de=b.Z.TabPane,ue={pending_upload:"default",analyzing:"processing",completed:"success",failed:"error"},pe={pending_upload:"待上传文件",analyzing:"分析中",completed:"已完成",failed:"失败"},fe="default",xe="processing",he="success",ge="error",me=function(){var e,t=(0,ne.TH)(),n=(0,ne.s0)(),r=(0,h.useState)(null),s=x()(r,2),i=s[0],l=s[1],u=(0,h.useState)([]),f=x()(u,2),g=f[0],V=f[1],A=y.Z.useForm(),F=(x()(A,1)[0],(0,h.useState)("uploaded_at")),N=x()(F,2),q=N[0],K=N[1],me=(0,h.useState)("desc"),ve=x()(me,2),je=ve[0],Ze=ve[1],be=(0,h.useState)([]),ye=x()(be,2),ke=ye[0],we=ye[1],ze=(0,h.useState)(!0),_e=x()(ze,2),Pe=(_e[0],_e[1]),Se=(0,h.useState)(""),Ce=x()(Se,2),Be=Ce[0],Te=Ce[1],Le=(0,h.useState)(""),Re=x()(Le,2),Me=Re[0],He=Re[1],Oe=(0,h.useState)("results"),Ee=x()(Oe,2),Ie=Ee[0],Ve=Ee[1],Ae=(0,h.useState)(!0),Fe=x()(Ae,2),Ne=Fe[0],We=Fe[1],De=(0,h.useState)(null),Je=x()(De,2),qe=Je[0],Ke=Je[1],Ge=(0,h.useState)(!1),Ue=x()(Ge,2),Qe=Ue[0],Xe=Ue[1],Ye=(0,h.useState)(""),$e=x()(Ye,2),et=$e[0],tt=$e[1],nt=(0,h.useState)(""),rt=x()(nt,2),at=rt[0],st=rt[1],ct=(0,h.useState)(!1),it=x()(ct,2),ot=it[0],lt=it[1],dt=(0,h.useState)([]),ut=x()(dt,2),pt=ut[0],ft=ut[1],xt=(0,h.useState)(!1),ht=x()(xt,2),gt=ht[0],mt=ht[1],vt={name:"file",multiple:!0,progress:{strokeColor:{"0%":"#108ee9","100%":"#87d068"}},maxCount:1e3,fileList:pt,beforeUpload:function(e){return![".pdf",".docx",".doc",".csv",".xls",".xlsx",".json",".txt",".jpg",".jpeg",".png"].some((function(t){return e.name.toLowerCase().endsWith(t)}))&&(k.ZP.error("仅支持PDF、Word、CSV、Excel、JSON和图片格式的文件"),j.Z.LIST_IGNORE)},onChange:function(e){ft(e.fileList),e.fileList.length>50&&k.ZP.warning("最多只能上传50个文件")},onDrop:function(e){console.log("拖拽文件",e.dataTransfer.files)}},jt=function(){var e=p()(d()().mark((function e(){var t,n;return d()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,Pe(!0),e.next=4,(0,ce.s6)();case 4:(t=e.sent).success&&t.data?(n=t.data.filter((function(e){return e.is_active})),we(n)):(k.ZP.error(t.error||"获取任务类型列表失败"),we([])),e.next=13;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("获取任务类型列表失败:",e.t0),k.ZP.error("获取任务类型列表失败，请检查网络连接"),we([]);case 13:return e.prev=13,Pe(!1),e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[0,8,13,16]])})));return function(){return e.apply(this,arguments)}}(),Zt=function(){var e=p()(d()().mark((function e(t){var n;return d()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,We(!0),e.next=4,(0,ce.yW)(t);case 4:if(!(n=e.sent).success||!n.data){e.next=17;break}if(l(n.data),Te(n.data.name),He(n.data.type),!n.data.files){e.next=13;break}V(n.data.files),e.next=15;break;case 13:return e.next=15,bt(t);case 15:e.next=18;break;case 17:k.ZP.error(n.error||"获取任务详情失败");case 18:e.next=24;break;case 20:e.prev=20,e.t0=e.catch(0),console.error("获取任务详情失败:",e.t0),k.ZP.error("获取任务详情失败，请检查网络连接");case 24:return e.prev=24,We(!1),e.finish(24);case 27:case"end":return e.stop()}}),e,null,[[0,20,24,27]])})));return function(t){return e.apply(this,arguments)}}(),bt=function(){var e=p()(d()().mark((function e(t){var n;return d()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,ce.Sq)(t);case 3:(n=e.sent).success&&n.data?V(n.data):k.ZP.error(n.error||"获取任务文件列表失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取任务文件列表失败:",e.t0),k.ZP.error("获取任务文件列表失败，请检查网络连接");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}(),yt=function(){var e=p()(d()().mark((function e(){return d()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(qe){e.next=3;break}return k.ZP.warning("无法识别当前任务ID，请刷新页面重试"),e.abrupt("return");case 3:if(0!==g.length){e.next=6;break}return k.ZP.warning("请先上传文件"),e.abrupt("return");case 6:return e.prev=6,k.ZP.loading({content:"正在启动任务...",key:"startTask"}),e.next=10,fetch("/api/chat/v1/completions",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat((0,te.bW)())},body:JSON.stringify({taskId:qe,files:g.map((function(e){return{id:e.id,name:e.name,size:e.size,data_type:e.data_type,status:e.status}}))})});case 10:if(!e.sent.ok){e.next=20;break}return k.ZP.success({content:"任务已启动，系统正在分析文件...",key:"startTask",icon:(0,ie.jsx)(M.Z,{style:{color:"#52c41a"}}),duration:3}),i&&l(o()(o()({},i),{},{status:"analyzing"})),Ve("status"),e.next=17,Zt(qe);case 17:setTimeout(p()(d()().mark((function e(){return d()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Zt(qe);case 2:case"end":return e.stop()}}),e)}))),5e3),e.next=21;break;case 20:k.ZP.error({content:"启动任务失败",key:"startTask",duration:3});case 21:e.next=27;break;case 23:e.prev=23,e.t0=e.catch(6),console.error("启动任务失败:",e.t0),k.ZP.error({content:"启动任务失败，请检查网络连接",key:"startTask",duration:3});case 27:case"end":return e.stop()}}),e,null,[[6,23]])})));return function(){return e.apply(this,arguments)}}(),kt=function(){var e=p()(d()().mark((function e(){var t;return d()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(qe){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,lt(!0),e.next=6,(0,ce.xJ)(qe,{name:et,type:at});case 6:(t=e.sent).success?(k.ZP.success("任务信息更新成功"),i&&l(o()(o()({},i),{},{name:et,type:at})),Te(et),He(at),Xe(!1)):k.ZP.error(t.error||"更新任务信息失败"),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(2),console.error("更新任务信息失败:",e.t0),k.ZP.error("更新任务信息失败，请检查网络连接");case 14:return e.prev=14,lt(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[2,10,14,17]])})));return function(){return e.apply(this,arguments)}}();(0,h.useEffect)((function(){jt();var e=new URLSearchParams(t.search).get("task");e?(Ke(e),Zt(e)):We(!1)}),[t]),(0,h.useEffect)((function(){"status"===Ie&&qe&&bt(qe)}),[Ie,qe]);var wt,zt,_t,Pt,St=function(){var e=p()(d()().mark((function e(){return d()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(qe){e.next=3;break}return k.ZP.warning("无法识别当前任务ID，请刷新页面重试"),e.abrupt("return");case 3:return k.ZP.loading({content:"正在刷新任务状态...",key:"refreshTask"}),e.prev=4,e.next=7,Zt(qe);case 7:k.ZP.success({content:"刷新成功",key:"refreshTask"}),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(4),k.ZP.error({content:"刷新失败，请重试",key:"refreshTask"});case 13:case"end":return e.stop()}}),e,null,[[4,10]])})));return function(){return e.apply(this,arguments)}}(),Ct=function(){var e=p()(d()().mark((function e(){var t,n,r,a;return d()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(qe){e.next=3;break}return k.ZP.warning("无法识别当前任务ID，请刷新页面重试"),e.abrupt("return");case 3:if(0!==pt.length){e.next=6;break}return k.ZP.warning("请选择要上传的文件"),e.abrupt("return");case 6:return e.prev=6,mt(!0),(t=new FormData).append("taskId",qe),pt.forEach((function(e){e.originFileObj&&t.append("files",e.originFileObj)})),n=(0,te.bW)(),e.next=14,fetch("/api/auditTask/upload_task_file",{method:"POST",headers:{Authorization:"Bearer ".concat(n)},body:t});case 14:return r=e.sent,e.next=17,r.json();case 17:if(!(a=e.sent).success){e.next=27;break}if(k.ZP.success({content:"文件上传成功，请点击页面顶部【开始分析】按钮启动任务",duration:5}),!qe){e.next=23;break}return e.next=23,Zt(qe);case 23:ft([]),Ve("status"),e.next=28;break;case 27:k.ZP.error(a.error||"文件上传失败");case 28:e.next=34;break;case 30:e.prev=30,e.t0=e.catch(6),console.error("上传文件过程中发生错误:",e.t0),k.ZP.error("上传文件失败，请检查网络连接");case 34:return e.prev=34,mt(!1),e.finish(34);case 37:case"end":return e.stop()}}),e,null,[[6,30,34,37]])})));return function(){return e.apply(this,arguments)}}(),Bt=function(){var e=p()(d()().mark((function e(t){var n,r;return d()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,i&&"pending_upload"===i.status){e.next=4;break}return k.ZP.warning("只有在待上传状态下才能删除文件"),e.abrupt("return");case 4:return e.next=6,fetch("/api/auditTask/delete_task_file/".concat(t),{method:"DELETE",headers:{Authorization:"Bearer ".concat((0,te.bW)())}});case 6:return n=e.sent,e.next=9,n.json();case 9:(r=e.sent).success?(V(g.filter((function(e){return e.id!==t}))),k.ZP.success("文件删除成功"),g.length<=1&&i&&l(o()(o()({},i),{},{file_count:0}))):k.ZP.error(r.error||"删除文件失败"),e.next=17;break;case 13:e.prev=13,e.t0=e.catch(0),console.error("删除文件时出错:",e.t0),k.ZP.error("删除文件失败，请检查网络连接");case 17:case"end":return e.stop()}}),e,null,[[0,13]])})));return function(t){return e.apply(this,arguments)}}(),Tt=function(e){return{scene_1:"场景1: 价格一致性检查",scene_2:"场景2: 坐落位置一致性检查",scene_3:"场景3: 房产证号一致性检查",scene_4:"场景4: 建筑面积一致性检查",scene_5:"场景5: 使用年限一致性检查",scene_6:"场景6: 用途一致性检查",scene_7:"场景7: 抵押信息一致性检查",scene_8:"场景8: 租赁信息完整性检查",scene_9:"场景9: 决策文件检查"}[e]||"场景".concat(e.split("_")[1],": 未知检查项")};return(0,ie.jsxs)(re._z,{title:(null==i?void 0:i.name)||"任务详情",onBack:function(){return n("/fileAuditAssistant/offlineAuditTaskList")},content:(0,ie.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,ie.jsxs)(_.Z,{children:[(null==i?void 0:i.status)&&(0,ie.jsx)(C.Z,{color:ue[i.status],children:pe[i.status]}),(null==i?void 0:i.type)&&(0,ie.jsx)(C.Z,{color:"blue",children:(null===(e=ke.find((function(e){return e.code===i.type})))||void 0===e?void 0:e.name)||i.type})]}),(0,ie.jsx)("div",{style:{fontSize:"12px"},children:null==i?void 0:i.description})]}),header:{backIcon:(0,ie.jsx)(I.Z,{})},tabActiveKey:Ie,onTabChange:Ve,tabList:[{key:"results",tab:(0,ie.jsxs)("span",{children:[(0,ie.jsx)(M.Z,{}),"审核结果"]})},{key:"status",tab:(0,ie.jsxs)("span",{children:[(0,ie.jsx)(X.Z,{}),"执行状态"]})},{key:"upload",tab:(0,ie.jsxs)("span",{children:[(0,ie.jsx)(H.Z,{}),"上传文件"]})}],extra:[(0,ie.jsx)(P.ZP,{type:"link",icon:(0,ie.jsx)(E.Z,{}),onClick:St,size:"small",children:"刷新状态"},"refresh"),(0,ie.jsx)(P.ZP,{type:"link",icon:(0,ie.jsx)($.Z,{}),onClick:yt,disabled:!i||"pending_upload"!==i.status,style:{color:"pending_upload"===(null==i?void 0:i.status)&&g.length>0?"#52c41a":void 0,fontWeight:"pending_upload"===(null==i?void 0:i.status)&&g.length>0?"bold":void 0},title:"启动任务后，系统将开始分析文件内容",size:"small",children:"开始分析"},"start"),(0,ie.jsx)(P.ZP,{type:"link",icon:(0,ie.jsx)(ee.Z,{}),onClick:function(){tt(Be),st(Me),Xe(!0)},size:"small",children:"编辑任务"},"edit")],children:[Ne?(0,ie.jsx)(w.Z,{bordered:!1,loading:!0,children:(0,ie.jsx)(T.Z,{tip:"加载任务信息...",size:"large",children:(0,ie.jsx)("div",{style:{height:"400px",display:"flex",justifyContent:"center",alignItems:"center"},children:"正在加载任务信息..."})})}):(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsxs)(ae.f,{children:["results"===Ie&&function(){if(null==i||!i.result_data||0===i.result_data.length)return(0,ie.jsx)(w.Z,{title:(0,ie.jsxs)("div",{children:[(0,ie.jsx)(M.Z,{style:{color:"#52c41a"}})," 审核结果"]}),style:{borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.05)"},children:(0,ie.jsx)(L.Z,{description:"暂无审核结果数据"})});var e=i.result_data[0];return(0,ie.jsx)(w.Z,{title:(0,ie.jsxs)("div",{children:[(0,ie.jsx)(M.Z,{style:{color:"#52c41a"}})," 审核结果"]}),style:{borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.05)"},children:(0,ie.jsxs)(b.Z,{defaultActiveKey:"file_info",children:[(0,ie.jsx)(de,{tab:"文件信息",children:(0,ie.jsx)("div",{className:"result-container",style:{background:"#f9f9f9",padding:20,borderRadius:8},children:Object.entries(e.file_info).map((function(e){var t=x()(e,2),n=t[0],r=t[1];return(0,ie.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,ie.jsx)(m.Z.Title,{level:5,style:{marginBottom:"8px"},children:n}),r.map((function(e,t){return(0,ie.jsx)("div",{style:{backgroundColor:"#fff",border:"1px solid #e8e8e8",borderRadius:"8px",padding:"16px",marginBottom:"8px"},children:(0,ie.jsx)("table",{style:{width:"100%",borderCollapse:"collapse"},children:(0,ie.jsx)("tbody",{children:Object.entries(e).map((function(t,n){var r=x()(t,2),s=r[0],c=r[1];return(0,ie.jsxs)("tr",{style:{borderBottom:n===Object.entries(e).length-1?"none":"1px solid #f0f0f0"},children:[(0,ie.jsxs)("td",{style:{padding:"12px 8px",width:"30%",fontWeight:"bold"},children:[s,":"]}),(0,ie.jsx)("td",{style:{padding:"12px 8px"},children:"object"===a()(c)?JSON.stringify(c):String(c)})]},s)}))})})},t)}))]},n)}))})},"file_info"),(0,ie.jsx)(de,{tab:"审核判断",children:(0,ie.jsx)("div",{className:"result-container",style:{background:"#f9f9f9",padding:20,borderRadius:8},children:Object.entries(e.scene_info).map((function(e){var t=x()(e,2),n=t[0],r=t[1];return(0,ie.jsxs)("div",{style:{backgroundColor:"#fff",border:"1px solid #e8e8e8",borderRadius:"8px",padding:"16px",marginBottom:"16px"},children:[(0,ie.jsx)(m.Z.Title,{level:5,children:Tt(n)}),Object.entries(r).map((function(e){var t=x()(e,2),n=t[0],r=t[1];return n.startsWith("judge")&&"object"===a()(r)&&r&&"value"in r?(0,ie.jsxs)("div",{style:{backgroundColor:r.value?"#f6ffed":"#fff2f0",border:"1px solid ".concat(r.value?"#b7eb8f":"#ffccc7"),borderRadius:"4px",padding:"12px",marginBottom:"8px"},children:[r.value?(0,ie.jsx)(M.Z,{style:{color:"#52c41a",marginRight:"8px"}}):(0,ie.jsx)(Y.Z,{style:{color:"#ff4d4f",marginRight:"8px"}}),(0,ie.jsx)(m.Z.Text,{children:r.desc})]},n):(0,ie.jsxs)("div",{style:{backgroundColor:"#f5f5f5",padding:"12px",borderRadius:"4px",marginBottom:"8px"},children:[(0,ie.jsxs)(m.Z.Text,{strong:!0,children:[n,": "]}),(0,ie.jsx)(m.Z.Text,{children:"object"===a()(r)?JSON.stringify(r):String(r)})]},n)}))]},n)}))})},"scene_info")]})})}(),"status"===Ie&&(_t=function(){if(!i)return 0;switch(i.status){case"pending_upload":default:return 0;case"analyzing":case"failed":return 1;case"completed":return 2}}(),Pt=c()(g).sort((function(e,t){if("status"===q){var n={failed:0,processing:1,completed:2,pending:3},r=n[e.status||"pending"]||4,a=n[t.status||"pending"]||4;return"asc"===je?r-a:a-r}if("size"===q)return"asc"===je?e.size-t.size:t.size-e.size;if("name"===q)return"asc"===je?e.name.localeCompare(t.name):t.name.localeCompare(e.name);if("type"===q)return"asc"===je?(e.data_type||"").localeCompare(t.data_type||""):(t.data_type||"").localeCompare(e.data_type||"");var s=e.uploaded_at||e.uploadTime||"",c=t.uploaded_at||t.uploadTime||"";return"asc"===je?s.localeCompare(c):c.localeCompare(s)})),(0,ie.jsxs)("div",{children:[g.length>0&&(0,ie.jsxs)(w.Z,{title:(0,ie.jsxs)("div",{children:[(0,ie.jsx)(O.Z,{})," 已上传文件列表 (",g.length,")"]}),style:{marginBottom:16,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.05)"},extra:(0,ie.jsxs)(_.Z,{children:[(0,ie.jsx)(P.ZP,{icon:(0,ie.jsx)(E.Z,{}),size:"small",onClick:St,title:"刷新任务状态",children:"刷新"}),(0,ie.jsx)("span",{children:"排序:"}),(0,ie.jsxs)(S.default,{value:q,style:{width:120},onChange:function(e){var t;q===(t=e)?Ze("asc"===je?"desc":"asc"):(K(t),Ze("desc"))},children:[(0,ie.jsx)(S.default.Option,{value:"uploaded_at",children:"上传时间"}),(0,ie.jsx)(S.default.Option,{value:"status",children:"处理状态"}),(0,ie.jsx)(S.default.Option,{value:"name",children:"文件名"}),(0,ie.jsx)(S.default.Option,{value:"size",children:"文件大小"}),(0,ie.jsx)(S.default.Option,{value:"type",children:"文件类型"})]}),(0,ie.jsx)(P.ZP,{type:"text",icon:"asc"===je?(0,ie.jsx)(I.Z,{rotate:90}):(0,ie.jsx)(I.Z,{rotate:-90}),onClick:function(){return Ze("asc"===je?"desc":"asc")}})]}),children:[g.length>0&&(0,ie.jsx)("div",{style:{marginBottom:16,padding:"0 12px"},children:(0,ie.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:8},children:(zt={pending:0,processing:0,completed:0,failed:0,unknown:0},g.forEach((function(e){e.status?zt[e.status]=(zt[e.status]||0)+1:zt.unknown++})),(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsxs)(C.Z,{color:"default",children:["总文件: ",g.length]}),zt.pending>0&&(0,ie.jsxs)(C.Z,{color:fe,children:["待处理: ",zt.pending]}),zt.processing>0&&(0,ie.jsxs)(C.Z,{color:xe,children:["处理中: ",zt.processing]}),zt.completed>0&&(0,ie.jsxs)(C.Z,{color:he,children:["已完成: ",zt.completed]}),zt.failed>0&&(0,ie.jsxs)(C.Z,{color:ge,children:["失败: ",zt.failed]}),zt.unknown>0&&(0,ie.jsxs)(C.Z,{children:["未知状态: ",zt.unknown]})]}))})}),(0,ie.jsx)(B.Z,{size:"small",dataSource:Pt,renderItem:function(e){var t,n=O.Z,r="#1890ff";if(e.data_type){var a=e.data_type.toLowerCase();a.includes("excel")||a.includes("csv")||a.includes("xls")?(n=W,r="#52c41a"):a.includes("pdf")?(n=D.Z,r="#f5222d"):a.includes("word")||a.includes("doc")?(n=J.Z,r="#1890ff"):a.includes("json")||a.includes("txt")?(n=O.Z,r="#fa8c16"):a.includes("jpg")||a.includes("jpeg")||a.includes("png")?(n=G,r="#722ed1"):(n=U.Z,r="#8c8c8c")}return(0,ie.jsx)(B.Z.Item,{actions:["pending_upload"===(null==i?void 0:i.status)&&(0,ie.jsx)(P.ZP,{type:"link",icon:(0,ie.jsx)(Q.Z,{}),danger:!0,size:"small",onClick:function(){return Bt(e.id)},children:"移除"})],children:(0,ie.jsx)(B.Z.Item.Meta,{avatar:(0,ie.jsx)(n,{style:{fontSize:20,color:r}}),title:(0,ie.jsxs)(_.Z,{children:[(0,ie.jsx)("span",{children:e.name}),e.data_type&&(0,ie.jsx)(C.Z,{color:"blue",children:e.data_type})]}),description:(0,ie.jsxs)("div",{children:[(0,ie.jsx)("div",{style:{display:"flex",justifyContent:"space-between"},children:(0,ie.jsx)("span",{children:"大小: ".concat((t=e.size,t<1024?t+" B":t<1048576?(t/1024).toFixed(2)+" KB":t<1073741824?(t/1048576).toFixed(2)+" MB":(t/1073741824).toFixed(2)+" GB"))})}),"processing"===e.status&&(0,ie.jsx)("div",{style:{marginTop:8},children:(0,ie.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,ie.jsx)("span",{style:{marginRight:8},children:"处理中"}),(0,ie.jsx)(T.Z,{size:"small"})]})}),"completed"===e.status&&(0,ie.jsxs)("div",{style:{marginTop:8,color:"#52c41a"},children:[(0,ie.jsx)(M.Z,{style:{marginRight:8}}),"处理完成"]}),"failed"===e.status&&(0,ie.jsxs)("div",{style:{marginTop:8,color:"#f5222d"},children:["处理失败: ",e.processing_status||"未知错误"]})]})})})}})]}),(0,ie.jsxs)(w.Z,{title:(0,ie.jsxs)("div",{children:[(0,ie.jsx)(X.Z,{})," 执行状态"]}),style:{borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.05)"},extra:(0,ie.jsx)(P.ZP,{icon:(0,ie.jsx)(E.Z,{}),size:"small",onClick:St,title:"刷新任务状态",children:"刷新"}),children:["pending_upload"===(null==i?void 0:i.status)&&g.length>0&&(0,ie.jsx)(z.Z,{type:"info",message:"文件已上传，等待启动",description:"您的文件已上传成功，请点击页面顶部的【开始分析】按钮启动分析任务。",style:{marginBottom:16},showIcon:!0}),(0,ie.jsxs)(Z.Z,{progressDot:!0,current:_t,style:{padding:"0 20px"},children:[(0,ie.jsx)(le,{title:"文件上传",description:_t>=0?"已完成":"等待中"}),(0,ie.jsx)(le,{title:"任务处理",description:1===_t?"进行中":_t>1?"已完成":"等待中"}),(0,ie.jsx)(le,{title:"结果生成",description:2===_t?"已完成":"等待中"})]})]})]})),"upload"===Ie&&(wt=ke.find((function(e){return e.code===Me})),(0,ie.jsx)("div",{children:(0,ie.jsxs)(w.Z,{title:(0,ie.jsxs)("div",{children:[(0,ie.jsx)(H.Z,{})," 文件上传"]}),style:{marginBottom:16,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.05)"},children:[(0,ie.jsx)(z.Z,{type:"warning",message:"重要提示",description:(0,ie.jsxs)("div",{children:[(0,ie.jsx)("p",{children:(0,ie.jsx)("strong",{children:"操作步骤："})}),(0,ie.jsx)("p",{children:"1. 上传所有需要分析的文件"}),(0,ie.jsx)("p",{children:"2. 点击页面顶部的【开始分析】按钮手动启动任务"}),(0,ie.jsxs)("p",{children:[(0,ie.jsx)("strong",{children:"注意："}),"上传完文件后，需要手动启动分析任务"]})]}),style:{marginBottom:16}}),(0,ie.jsx)(z.Z,{type:"info",message:"文件要求",description:(0,ie.jsx)("div",{children:null!=wt&&wt.description?(0,ie.jsx)("div",{dangerouslySetInnerHTML:{__html:wt.description}}):(0,ie.jsxs)(ie.Fragment,{children:[(0,ie.jsxs)("p",{children:[(0,ie.jsx)("strong",{children:"支持格式："}),"PDF、Word、CSV、Excel、JSON、图片等"]}),(0,ie.jsxs)("p",{children:[(0,ie.jsx)("strong",{children:"命名建议："}),"建议使用有意义的文件名，方便后续识别"]}),(0,ie.jsxs)("p",{children:[(0,ie.jsx)("strong",{children:"大小限制："}),"单个文件不超过100MB"]})]})}),style:{marginBottom:16}}),(0,ie.jsx)(se.A,{style:{margin:"auto",marginTop:8,marginBottom:24,width:"100%",paddingBottom:24},name:"basic",layout:"vertical",onFinish:Ct,submitter:{searchConfig:{submitText:"上传文件"},submitButtonProps:{loading:gt,disabled:0===pt.length}},children:(0,ie.jsxs)(oe,o()(o()({},vt),{},{style:{margin:"auto",marginTop:8,marginBottom:24,width:"100%",paddingBottom:24},children:[(0,ie.jsx)("p",{className:"ant-upload-drag-icon",children:(0,ie.jsx)(H.Z,{})}),(0,ie.jsx)("p",{className:"ant-upload-text",children:"点击或拖拽文件到此区域上传"}),(0,ie.jsx)("p",{className:"ant-upload-hint",children:"支持单个或批量上传。严格禁止上传公司数据或其他禁止的文件。支持数据格式：PDF、Word、CSV、Excel、JSON、图片等"})]}))})]})}))]}),(0,ie.jsx)(R.Z,{title:"编辑任务信息",open:Qe,onCancel:function(){return Xe(!1)},footer:[(0,ie.jsx)(P.ZP,{onClick:function(){return Xe(!1)},children:"取消"},"cancel"),(0,ie.jsx)(P.ZP,{type:"primary",loading:ot,onClick:kt,children:"保存"},"submit")],children:(0,ie.jsxs)(y.Z,{layout:"vertical",children:[(0,ie.jsx)(y.Z.Item,{label:"任务名称",required:!0,tooltip:"为当前审核任务起一个名称，便于后续识别",children:(0,ie.jsx)(v.Z,{placeholder:"请输入任务名称",value:et,onChange:function(e){return tt(e.target.value)}})}),(0,ie.jsx)(y.Z.Item,{label:"任务类型",required:!0,tooltip:"不同的任务类型会使用不同的内容抽取策略",children:(0,ie.jsx)(S.default,{placeholder:"请选择任务类型",value:at||void 0,onChange:function(e){return st(e)},style:{width:"100%"},children:ke.map((function(e){return(0,ie.jsxs)(S.default.Option,{value:e.code,children:[e.name,e.description?" (".concat(e.description,")"):""]},e.id)}))})})]})})]}),(0,ie.jsx)("style",{children:"\n        .file-audit-tabs .ant-tabs-nav {\n          margin-bottom: 24px;\n        }\n        \n        .task-list-table .ant-table-tbody > tr {\n          cursor: pointer;\n          transition: all 0.3s;\n        }\n        \n        .task-list-table .ant-table-tbody > tr:hover {\n          background-color: rgba(24, 144, 255, 0.05);\n        }\n        \n        .ant-card-head {\n          border-bottom: 1px solid #f0f0f0;\n          padding: 0 16px;\n        }\n        \n        .upload-card .ant-upload-drag {\n          border: 2px dashed #d9d9d9;\n          transition: all 0.3s;\n        }\n        \n        .upload-card .ant-upload-drag:hover {\n          border-color: #1890ff;\n        }\n        \n        .chat-container::-webkit-scrollbar {\n          width: 6px;\n        }\n        \n        .chat-container::-webkit-scrollbar-thumb {\n          background-color: rgba(0, 0, 0, 0.2);\n          border-radius: 3px;\n        }\n        \n        .chat-container::-webkit-scrollbar-track {\n          background-color: transparent;\n        }\n      "})]})}}}]);