"""金融风险分析代理的客户端示例。

展示如何通过API调用传入参数，如数据库连接信息和文件路径。
"""

import requests
import json
import os
from dotenv import load_dotenv

# 加载环境变量（用于API密钥等敏感信息）
load_dotenv()

# LangGraph Server的基础URL
BASE_URL = "http://localhost:8000"

def invoke_agent(query: str, config: dict = None) -> dict:
    """调用风险分析代理"""
    
    endpoint = f"{BASE_URL}/agent/invoke"
    
    # 准备请求数据
    payload = {
        "inputs": {
            "query": query,
            "messages": []
        },
        "config": {
            "configurable": config or {}
        }
    }
    
    # 发送请求
    try:
        response = requests.post(
            endpoint,
            headers={"Content-Type": "application/json"},
            data=json.dumps(payload)
        )
        
        # 检查响应状态
        response.raise_for_status()
        
        # 返回JSON结果
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return {"error": str(e)}


def main():
    """主函数"""
    
    # 获取OpenAI API密钥
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key:
        print("警告: 未找到OPENAI_API_KEY环境变量")
    
    # 示例1: 传入数据库连接信息和文件路径
    print("\n=== 示例1: 带数据库和文件路径的请求 ===")
    config1 = {
        "db_url": "postgres://localhost:5432/finance_db",
        "db_username": "financial_analyst",
        "db_password": "secure_password",
        "risk_policy_path": "/path/to/risk_policy.txt",
        "model_name": "gpt-3.5-turbo",
        "temperature": 0.3,
        "api_key": openai_api_key
    }
    
    result1 = invoke_agent(
        "分析我们数据库中客户的信用风险，特别关注贷款额度超过10万的客户", 
        config1
    )
    
    print(f"结果:\n{json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 示例2: 仅传入API密钥，不提供数据库和文件信息
    print("\n=== 示例2: 仅提供API密钥的请求 ===")
    config2 = {
        "api_key": openai_api_key
    }
    
    result2 = invoke_agent(
        "评估张三、李四和王五的信用风险", 
        config2
    )
    
    print(f"结果:\n{json.dumps(result2, ensure_ascii=False, indent=2)}")


if __name__ == "__main__":
    main() 