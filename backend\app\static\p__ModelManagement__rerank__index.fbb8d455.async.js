"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8202],{51042:function(e,r,n){var t=n(1413),a=n(67294),u=n(42110),s=n(91146),i=function(e,r){return a.createElement(s.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:r,icon:u.Z}))},l=a.forwardRef(i);r.Z=l},42844:function(e,r,n){n.r(r),n.d(r,{default:function(){return T}});var t=n(15009),a=n.n(t),u=n(97857),s=n.n(u),i=n(99289),l=n.n(i),c=n(5574),o=n.n(c),p=n(67294),d=n(97131),f=n(12453),h=n(8232),m=n(2453),x=n(83622),v=n(17788),Z=n(55102),j=n(34041),b=n(51042),y=n(78158);function k(e){return I.apply(this,arguments)}function I(){return(I=l()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,y.N)("/api/reranks",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function O(e){return P.apply(this,arguments)}function P(){return(P=l()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,y.N)("/api/reranks",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function w(e){return A.apply(this,arguments)}function A(){return(A=l()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,y.N)("/api/reranks/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function g(e){return L.apply(this,arguments)}function L(){return(L=l()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,y.N)("/api/reranks/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var C=n(77885),E=n(85893),T=function(){var e=(0,p.useState)(!1),r=o()(e,2),n=r[0],t=r[1],u=(0,p.useState)(!1),i=o()(u,2),c=i[0],y=i[1],I=(0,p.useState)(void 0),P=o()(I,2),A=P[0],L=P[1],T=(0,p.useRef)(),_=h.Z.useForm(),q=o()(_,1)[0],M=function(){var e=l()(a()().mark((function e(r){var n,u;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=m.ZP.loading("正在添加"),e.prev=1,e.next=4,O(s()({},r));case 4:return n(),m.ZP.success("添加成功"),t(!1),null===(u=T.current)||void 0===u||u.reload(),q.resetFields(),e.abrupt("return",!0);case 12:return e.prev=12,e.t0=e.catch(1),n(),m.ZP.error("添加失败，请重试"),e.abrupt("return",!1);case 17:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(r){return e.apply(this,arguments)}}(),R=function(){var e=l()(a()().mark((function e(r){var n,t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r.id){e.next=3;break}return m.ZP.error("更新失败，缺少嵌入 ID"),e.abrupt("return",!1);case 3:return n=m.ZP.loading("正在更新"),e.prev=4,e.next=7,w(r);case 7:return n(),m.ZP.success("更新成功"),y(!1),L(void 0),null===(t=T.current)||void 0===t||t.reload(),q.resetFields(),e.abrupt("return",!0);case 16:return e.prev=16,e.t0=e.catch(4),n(),m.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 21:case"end":return e.stop()}}),e,null,[[4,16]])})));return function(r){return e.apply(this,arguments)}}(),N=function(){var e=l()(a()().mark((function e(r){var n,t;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=m.ZP.loading("正在删除"),e.prev=1,e.next=4,g(r.id);case 4:return n(),m.ZP.success("删除成功"),null===(t=T.current)||void 0===t||t.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),n(),m.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(r){return e.apply(this,arguments)}}(),S=[{title:"名称",dataIndex:"name",valueType:"text"},{title:"模型名称",dataIndex:"reRank_model",valueType:"text"},{title:"接口类型",dataIndex:"provider",valueType:"text",search:!1},{title:"状态",dataIndex:"is_active",valueType:"boolean",render:function(e){return e?"激活":"未激活"},search:!1},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"操作",dataIndex:"option",valueType:"option",render:function(e,r){return[(0,E.jsx)(x.ZP,{type:"link",onClick:function(){y(!0),L(r)},children:"编辑"},"edit-".concat(r.id)),(0,E.jsx)(x.ZP,{type:"link",danger:!0,onClick:function(){return N(r)},children:"删除"},"delete-".concat(r.id))]}}];return(0,E.jsxs)(d._z,{children:[(0,E.jsx)(f.Z,{headerTitle:"重排序管理",actionRef:T,rowKey:"id",search:{labelWidth:120,defaultCollapsed:!0},toolBarRender:function(){return[(0,E.jsxs)(x.ZP,{type:"primary",onClick:function(){t(!0)},children:[(0,E.jsx)(b.Z,{})," 新建"]},"primary")]},request:function(){var e=l()(a()().mark((function e(r){var n;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,k(s()({current:r.current,pageSize:r.pageSize},r));case 2:return n=e.sent,e.abrupt("return",{data:n.data,success:n.success,total:n.total});case 4:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),columns:S}),(0,E.jsx)(v.Z,{visible:n,title:"新建重排序",onCancel:function(){return t(!1)},onOk:function(){return q.submit()},children:(0,E.jsxs)(h.Z,{form:q,layout:"horizontal",onFinish:M,initialValues:{provider:"openai"},labelCol:{span:6},wrapperCol:{span:18},children:[(0,E.jsx)(h.Z.Item,{name:"name",label:"名称",rules:[{required:!0,message:"请输入名称"}],children:(0,E.jsx)(Z.Z,{})}),(0,E.jsx)(h.Z.Item,{name:"reRank_model",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:(0,E.jsx)(Z.Z,{})}),(0,E.jsx)(h.Z.Item,{name:"service_url",label:"服务地址",rules:[{required:!0,message:"请输入服务地址"},{pattern:/^(http|https):\/\/[^ "]+$/,message:"请输入有效的 URL，必须以 http 或 https 开头"}],children:(0,E.jsx)(Z.Z,{})}),(0,E.jsx)(h.Z.Item,{name:"api_key",label:"API Key",rules:[{required:!0,message:"请输入 API Key"}],children:(0,E.jsx)(Z.Z,{})}),(0,E.jsx)(h.Z.Item,{name:"provider",label:"接口标准",rules:[{required:!0,message:"请选择接口调用标准"}],children:(0,E.jsxs)(j.default,{children:[(0,E.jsx)(j.default.Option,{value:C.M.LOACL,children:"本地化"}),(0,E.jsx)(j.default.Option,{value:C.M.OLLAMA,children:"Ollama"}),(0,E.jsx)(j.default.Option,{value:C.M.OPENAI,children:"OpenAI"})]})})]})}),A&&(0,E.jsx)(v.Z,{visible:c,title:"更新嵌入",onCancel:function(){return y(!1)},onOk:function(){return q.submit()},children:(0,E.jsxs)(h.Z,{form:q,layout:"horizontal",initialValues:A,onFinish:R,labelCol:{span:6},wrapperCol:{span:18},children:[(0,E.jsx)(h.Z.Item,{name:"id",hidden:!0,children:(0,E.jsx)(Z.Z,{})}),(0,E.jsx)(h.Z.Item,{name:"name",label:"名称",rules:[{required:!0,message:"请输入名称"}],children:(0,E.jsx)(Z.Z,{})}),(0,E.jsx)(h.Z.Item,{name:"reRank_model",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:(0,E.jsx)(Z.Z,{})}),(0,E.jsx)(h.Z.Item,{name:"service_url",label:"服务地址",rules:[{required:!0,message:"请输入服务地址"},{pattern:/^(http|https):\/\/[^ "]+$/,message:"请输入有效的 URL，必须以 http 或 https 开头"}],children:(0,E.jsx)(Z.Z,{})}),(0,E.jsx)(h.Z.Item,{name:"api_key",label:"API Key",rules:[{required:!0,message:"请输入 API Key"}],children:(0,E.jsx)(Z.Z,{})}),(0,E.jsx)(h.Z.Item,{name:"provider",label:"接口标准",rules:[{required:!0,message:"请选择接口调用标准"}],children:(0,E.jsxs)(j.default,{children:[(0,E.jsx)(j.default.Option,{value:C.M.LOACL,children:"本地化"}),(0,E.jsx)(j.default.Option,{value:C.M.OLLAMA,children:"Ollama"}),(0,E.jsx)(j.default.Option,{value:C.M.OPENAI,children:"OpenAI"})]})})]})})]})}},77885:function(e,r,n){n.d(r,{M:function(){return t}});var t={LOACL:"local",OPENAI:"openai",DEEPSEEK:"deepseek",DOUBAO:"doubao",TELEAI:"teleai",JIUTIAN:"jiutian",OLLAMA:"ollama"}}}]);