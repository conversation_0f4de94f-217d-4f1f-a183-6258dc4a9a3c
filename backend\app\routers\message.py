from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.message import MessageC<PERSON>, MessageUpdate, MessageResponse,MessageCollectedResponse
from ..db.mongodb import db
from datetime import datetime
from ..utils.auth import verify_token
from pydantic import ValidationError
from bson import ObjectId
from app.utils.logging_config import setup_logging, get_logger
# from ..utils.minio_client import get_presigned_url

import traceback
import logging

setup_logging()
logger = get_logger(__name__)

router = APIRouter(
    prefix="/api",
    tags=["messages"]
)

# 创建新消息
@router.post("/message", response_model=Dict[str, Any])
async def create_message(message: MessageCreate, current_user: dict = Depends(verify_token)):
    try:
        new_message = message.dict()
        new_id =  ObjectId()
        new_message.update({
            "created_at": datetime.now() if new_message.get("created_at") is None else datetime.strptime(new_message.get("created_at"),'%Y-%m-%d %H:%M:%S'),
            "_id": new_id  # 使用 ObjectId 生成新的 _id
        })
        
        await db["messages"].insert_one(new_message)
        created_message = await db["messages"].find_one({"_id": new_id})
        del created_message["_id"]
        logger.info(f"新消息创建成功: {created_message}")
        return {
            "data": MessageResponse(**created_message),
            "success": True,
        }
    except ValidationError as ve:
        logger.error(f"验证错误: {ve.json()}")
        raise HTTPException(status_code=422, detail=ve.errors())
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

# 获取消息列表
@router.get("/messages", response_model=Dict[str, Any])
async def read_messages(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    content: Optional[str] = None,
    user_name: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {}
    if user_name:
        query["user_name"] = {"$regex": user_name, "$options": "i"}  # 
    if content:
        query["content"] = {"$regex": content, "$options": "i"} 

    messages = await db["messages"].find(query,{
        "conversation_id": 1,
        "user_id": 1,
        "user_name": 1,
        "content": 1,
        "role": 1,
        "created_at": 1,
        "references": 1,
        "app_info": 1
    }).sort("created_at", -1).skip(skip).limit(pageSize).to_list(pageSize)
    total = await db["messages"].count_documents(query)

    for msg in messages:
        msg["id"] = str(msg["_id"])
        del msg["_id"]

    return {
        "data": messages,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取单个消息
@router.get("/messages/{message_id}", response_model=MessageResponse)
async def read_message(message_id: str, current_user: dict = Depends(verify_token)):
    message = await db["messages"].find_one({"_id": ObjectId(message_id)})
    if message is None:
        raise HTTPException(status_code=404, detail="Message not found")
    message["id"] = str(message["_id"])
    del message["_id"]
    return MessageResponse(**message)

# 更新消息
@router.put("/messages/{message_id}", response_model=MessageResponse)
async def update_message(message_id: str, message: MessageUpdate, current_user: dict = Depends(verify_token)):
    update_data = message.dict(exclude_unset=True)
    result = await db["messages"].update_one({"_id": ObjectId(message_id)}, {"$set": update_data})
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Message not found")
    
    updated_message = await db["messages"].find_one({"_id": ObjectId(message_id)})
    updated_message["id"] = str(updated_message["_id"])
    del updated_message["_id"]
    return MessageResponse(**updated_message)

# 删除消息
@router.delete("/messages/{message_id}", response_model=Dict[str, Any])
async def delete_message(message_id: str, current_user: dict = Depends(verify_token)):
    try:
        logger.info(f'删除 message_id===>{message_id}')
        result = await db["messages"].delete_one({"message_id": message_id})
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Message not found")
        return {"success": True, "id": message_id}
    except Exception as e:
        logger.error(f"删除消息时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除消息失败: {str(e)}")
    
# 批量删除
@router.delete("/delete_messages", response_model=Dict[str, Any])
async def delete_messages(message_ids: List[str], current_user: dict = Depends(verify_token)):
    try:
        logger.info(f'删除 message_id===>{message_ids}')
        result = await db["messages"].delete_many({"message_id": {"$in": message_ids}})
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Message not found")
        return {"success": True, "id": message_ids}
    except Exception as e:
        logger.error(f"删除消息时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除消息失败: {str(e)}")

@router.post("/messages/collected", response_model=Dict[str, Any])
async def update_message_collected(message: MessageCollectedResponse, current_user: dict = Depends(verify_token)):
    try:
        logger.info(f"收藏消息: message_id={message.message_id}, collected={message.collected}")
        update_data = message.dict(exclude_unset=True)
        # 使用 _id 或 message_id 查询
        result = await db["messages"].update_one(
            {"message_id": update_data["message_id"]}, 
            {"$set": {"collected": update_data["collected"]}}
        )
        print('result',result)

        if result.acknowledged:
            return {"success": True, "id": message.message_id}
        else:
            return {"success": False, "id": message.message_id}
    except Exception as e:
        traceback.print_exc()
        logger.error(f"收藏消息时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"收藏消息失败: {str(e)}")
    
# @router.post("/messages/pdfHighlight", response_model=Dict[str, Any])
# async def pdf_highlight(bucket_name: str, file_name: str, current_user: dict = Depends(verify_token)):
#     try:
#         # 获取预签名的URL
#         file_stream = await get_presigned_url(bucket_name, file_name)
#         logger.info(f"获取文件流成功: {file_stream}")

#         # 处理URL
#         minio_url = file_stream.split('/')[0]
#         new_file_stream = file_stream.replace(minio_url, '/reviewfile/')
#         logger.info(f"处理后的文件流: {new_file_stream}")

#         return {
#             "data": new_file_stream,
#             "success": True
#         }
#     except Exception as e:
#         logger.error(f"获取文件流失败: {str(e)}")
        # raise HTTPException(status_code=500, detail="获取文件流失败")