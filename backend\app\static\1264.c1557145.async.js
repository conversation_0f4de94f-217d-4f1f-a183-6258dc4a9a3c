"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1264],{11264:function(e,t,i){i.r(t),i.d(t,{AnnotationLayerBuilder:function(){return Ji},DownloadManager:function(){return qi},EventBus:function(){return Ki},FindState:function(){return Yi},GenericL10n:function(){return en},LinkTarget:function(){return tn},PDFFindController:function(){return nn},PDFHistory:function(){return sn},PDFLinkService:function(){return rn},PDFPageView:function(){return an},PDFScriptingManager:function(){return on},PDFSinglePageViewer:function(){return ln},PDFViewer:function(){return hn},ProgressBar:function(){return dn},RenderingStates:function(){return cn},ScrollMode:function(){return un},SimpleLinkService:function(){return pn},SpreadMode:function(){return gn},StructTreeLayerBuilder:function(){return fn},TextLayerBuilder:function(){return mn},XfaLayerBuilder:function(){return bn},parseQueryString:function(){return yn}});var n={d:(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)},s=globalThis.pdfjsViewer={};n.d(s,{AnnotationLayerBuilder:()=>Xe,DownloadManager:()=>Je,EventBus:()=>Ye,FindState:()=>O,GenericL10n:()=>fi,LinkTarget:()=>Y,PDFFindController:()=>K,PDFHistory:()=>bi,PDFLinkService:()=>ee,PDFPageView:()=>Ri,PDFScriptingManager:()=>$i,PDFSinglePageViewer:()=>Qi,PDFViewer:()=>Xi,ProgressBar:()=>L,RenderingStates:()=>r,ScrollMode:()=>p,SimpleLinkService:()=>te,SpreadMode:()=>g,StructTreeLayerBuilder:()=>ki,TextLayerBuilder:()=>Ni,XfaLayerBuilder:()=>Fi,parseQueryString:()=>b});const r={INITIAL:0,RUNNING:1,PAUSED:2,FINISHED:3},a=0,o=1,l=2,h=3,d=0,c=1,u=2,p={UNKNOWN:-1,VERTICAL:0,HORIZONTAL:1,WRAPPED:2,PAGE:3},g={UNKNOWN:-1,NONE:0,ODD:1,EVEN:2};class f{constructor(){const e=window.devicePixelRatio||1;this.sx=e,this.sy=e}get scaled(){return 1!==this.sx||1!==this.sy}}function m(e,t,i=!1){let n=e.offsetParent;if(!n)return void console.error("offsetParent is not set -- cannot scroll");let s=e.offsetTop+e.clientTop,r=e.offsetLeft+e.clientLeft;for(;n.clientHeight===n.scrollHeight&&n.clientWidth===n.scrollWidth||i&&(n.classList.contains("markedContent")||"hidden"===getComputedStyle(n).overflow);)if(s+=n.offsetTop,r+=n.offsetLeft,n=n.offsetParent,!n)return;t&&(void 0!==t.top&&(s+=t.top),void 0!==t.left&&(r+=t.left,n.scrollLeft=r)),n.scrollTop=s}function b(e){const t=new Map;for(const[i,n]of new URLSearchParams(e))t.set(i.toLowerCase(),n);return t}const y=/[\x00-\x1F]/g;function v(e,t=!1){return y.test(e)?t?e.replaceAll(y,(e=>"\0"===e?"":" ")):e.replaceAll("\0",""):e}function w(e,t,i=0){let n=i,s=e.length-1;if(s<0||!t(e[s]))return e.length;if(t(e[n]))return n;for(;n<s;){const i=n+s>>1;t(e[i])?s=i:n=i+1}return n}function P(e){if(Math.floor(e)===e)return[e,1];const t=1/e;if(t>8)return[1,8];if(Math.floor(t)===t)return[1,t];const i=e>1?t:e;let n,s=0,r=1,a=1,o=1;for(;;){const e=s+a,t=r+o;if(t>8)break;i<=e/t?(a=e,o=t):(s=e,r=t)}return n=i-s/r<a/o-i?i===e?[s,r]:[r,s]:i===e?[a,o]:[o,a],n}function _(e,t){return e-e%t}function x(e){return Number.isInteger(e)&&e%90==0}function S(e){return e.width<=e.height}new Promise((function(e){window.requestAnimationFrame(e)}));const C=document.documentElement.style;class L{#e=null;#t=null;#i=0;#n=null;#s=!0;constructor(e){this.#e=e.classList,this.#n=e.style}get percent(){return this.#i}set percent(e){var t,i,n;this.#i=(t=e,i=0,n=100,Math.min(Math.max(t,i),n)),isNaN(e)?this.#e.add("indeterminate"):(this.#e.remove("indeterminate"),this.#n.setProperty("--progressBar-percent",`${this.#i}%`))}setWidth(e){if(!e)return;const t=e.parentNode.offsetWidth-e.offsetWidth;t>0&&this.#n.setProperty("--progressBar-end-offset",`${t}px`)}setDisableAutoFetch(e=5e3){isNaN(this.#i)||(this.#t&&clearTimeout(this.#t),this.show(),this.#t=setTimeout((()=>{this.#t=null,this.hide()}),e))}hide(){this.#s&&(this.#s=!1,this.#e.add("hidden"))}show(){this.#s||(this.#s=!0,this.#e.remove("hidden"))}}const M=0,E=1,A=2,I=3,k=4,T=5,D=6,N=7;function F(e){return function(e){return e<11904}(e)?function(e){return 0==(65408&e)}(e)?function(e){return 32===e||9===e||13===e||10===e}(e)?M:function(e){return e>=97&&e<=122||e>=65&&e<=90}(e)||function(e){return e>=48&&e<=57}(e)||95===e?E:A:function(e){return 3584==(65408&e)}(e)?N:160===e?M:E:function(e){return e>=13312&&e<=40959||e>=63744&&e<=64255}(e)?I:function(e){return e>=12448&&e<=12543}(e)?k:function(e){return e>=12352&&e<=12447}(e)?T:function(e){return e>=65376&&e<=65439}(e)?D:E}let j;const O={FOUND:0,NOT_FOUND:1,WRAPPED:2,PENDING:3},R={"‐":"-","‘":"'","’":"'","‚":"'","‛":"'","“":'"',"”":'"',"„":'"',"‟":'"',"¼":"1/4","½":"1/2","¾":"3/4"},V=new Set([12441,12442,2381,2509,2637,2765,2893,3021,3149,3277,3387,3388,3405,3530,3642,3770,3972,4153,4154,5908,5940,6098,6752,6980,7082,7083,7154,7155,11647,43014,43052,43204,43347,43456,43766,44013,3158,3953,3954,3962,3963,3964,3965,3968,3956]);let B;const $=/\p{M}+/gu,H=/([.*+?^${}()|[\]\\])|(\p{P})|(\s+)|(\p{M})|(\p{L})/gu,z=/([^\p{M}])\p{M}*$/u,U=/^\p{M}*([^\p{M}])/u,W=/[\uAC00-\uD7AF\uFA6C\uFACF-\uFAD1\uFAD5-\uFAD7]+/g,Z=new Map,G=new Map;let X=null,Q=null;function J(e){const t=[];let i,n;for(;null!==(i=W.exec(e));){let{index:e}=i;for(const n of i[0]){let i=Z.get(n);i||(i=n.normalize("NFD").length,Z.set(n,i)),t.push([i,e++])}}if(0===t.length&&X)n=X;else if(t.length>0&&Q)n=Q;else{const e=`([${Object.keys(R).join("")}])|([${j||=" ¨ª¯²-µ¸-º¼-¾Ĳ-ĳĿ-ŀŉſǄ-ǌǱ-ǳʰ-ʸ˘-˝ˠ-ˤʹͺ;΄-΅·ϐ-ϖϰ-ϲϴ-ϵϹևٵ-ٸक़-य़ড়-ঢ়য়ਲ਼ਸ਼ਖ਼-ਜ਼ਫ਼ଡ଼-ଢ଼ำຳໜ-ໝ༌གྷཌྷདྷབྷཛྷཀྵჼᴬ-ᴮᴰ-ᴺᴼ-ᵍᵏ-ᵪᵸᶛ-ᶿẚ-ẛάέήίόύώΆ᾽-῁ΈΉ῍-῏ΐΊ῝-῟ΰΎ῭-`ΌΏ´-῾ - ‑‗․-… ″-‴‶-‷‼‾⁇-⁉⁗ ⁰-ⁱ⁴-₎ₐ-ₜ₨℀-℃℅-ℇ℉-ℓℕ-№ℙ-ℝ℠-™ℤΩℨK-ℭℯ-ℱℳ-ℹ℻-⅀ⅅ-ⅉ⅐-ⅿ↉∬-∭∯-∰〈-〉①-⓪⨌⩴-⩶⫝̸ⱼ-ⱽⵯ⺟⻳⼀-⿕　〶〸-〺゛-゜ゟヿㄱ-ㆎ㆒-㆟㈀-㈞㈠-㉇㉐-㉾㊀-㏿ꚜ-ꚝꝰꟲ-ꟴꟸ-ꟹꭜ-ꭟꭩ豈-嗀塚晴凞-羽蘒諸逸-都飯-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-זּטּ-לּמּנּ-סּףּ-פּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-﷼︐-︙︰-﹄﹇-﹒﹔-﹦﹨-﹫ﹰ-ﹲﹴﹶ-ﻼ！-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ￠-￦",j}])|(${"(?:゙|゚)"}\\n)|(\\p{M}+(?:-\\n)?)|(\\S-\\n)|(${"(?:\\p{Ideographic}|[぀-ヿ])"}\\n)|(\\n)`;n=0===t.length?X=new RegExp(e+"|(\\u0000)","gum"):Q=new RegExp(e+"|([\\u1100-\\u1112\\ud7a4-\\ud7af\\ud84a\\ud84c\\ud850\\ud854\\ud857\\ud85f])","gum")}const s=[];for(;null!==(i=$.exec(e));)s.push([i[0].length,i.index]);let r=e.normalize("NFD");const a=[[0,0]];let o=0,l=0,h=0,d=0,c=0,u=!1;return r=r.replace(n,((e,i,n,r,p,g,f,m,b,y)=>{if(y-=d,i){const e=R[i],t=e.length;for(let e=1;e<t;e++)a.push([y-h+e,h-e]);return h-=t-1,e}if(n){let e=G.get(n);e||(e=n.normalize("NFKC"),G.set(n,e));const t=e.length;for(let e=1;e<t;e++)a.push([y-h+e,h-e]);return h-=t-1,e}if(r)return u=!0,y+c===s[o]?.[1]?++o:(a.push([y-1-h+1,h-1]),h-=1,d+=1),a.push([y-h+1,h]),d+=1,c+=1,r.charAt(0);if(p){const e=p.endsWith("\n"),t=e?p.length-2:p.length;u=!0;let i=t;y+c===s[o]?.[1]&&(i-=s[o][0],++o);for(let e=1;e<=i;e++)a.push([y-1-h+e,h-e]);return h-=i,d+=i,e?(y+=t-1,a.push([y-h+1,1+h]),h+=1,d+=1,c+=1,p.slice(0,t)):p}if(g){const e=g.length-2;return a.push([y-h+e,1+h]),h+=1,d+=1,c+=1,g.slice(0,-2)}if(f){const e=f.length-1;return a.push([y-h+e,h]),d+=1,c+=1,f.slice(0,-1)}if(m)return a.push([y-h+1,h-1]),h-=1,d+=1,c+=1," ";if(y+c===t[l]?.[1]){const e=t[l][0]-1;++l;for(let t=1;t<=e;t++)a.push([y-(h-t),h-t]);h-=e,d+=e}return b})),a.push([r.length,h]),[r,a,u]}function q(e,t,i){if(!e)return[t,i];const n=t,s=t+i-1;let r=w(e,(e=>e[0]>=n));e[r][0]>n&&--r;let a=w(e,(e=>e[0]>=s),r);e[a][0]>s&&--a;const o=n+e[r][1];return[o,s+e[a][1]+1-o]}class K{#r=null;#a=!0;#o=0;constructor({linkService:e,eventBus:t,updateMatchesCountOnProgress:i=!0}){this._linkService=e,this._eventBus=t,this.#a=i,this.onIsPageVisible=null,this.#l(),t._on("find",this.#h.bind(this)),t._on("findbarclose",this.#d.bind(this))}get highlightMatches(){return this._highlightMatches}get pageMatches(){return this._pageMatches}get pageMatchesLength(){return this._pageMatchesLength}get selected(){return this._selected}get state(){return this.#r}setDocument(e){this._pdfDocument&&this.#l(),e&&(this._pdfDocument=e,this._firstPageCapability.resolve())}#h(e){if(!e)return;const t=this._pdfDocument,{type:i}=e;(null===this.#r||this.#c(e))&&(this._dirtyMatch=!0),this.#r=e,"highlightallchange"!==i&&this.#u(O.PENDING),this._firstPageCapability.promise.then((()=>{if(!this._pdfDocument||t&&this._pdfDocument!==t)return;this.#p();const e=!this._highlightMatches,n=!!this._findTimeout;this._findTimeout&&(clearTimeout(this._findTimeout),this._findTimeout=null),i?this._dirtyMatch?this.#g():"again"===i?(this.#g(),e&&this.#r.highlightAll&&this.#f()):"highlightallchange"===i?(n?this.#g():this._highlightMatches=!0,this.#f()):this.#g():this._findTimeout=setTimeout((()=>{this.#g(),this._findTimeout=null}),250)}))}scrollMatchIntoView({element:e=null,selectedLeft:t=0,pageIndex:i=-1,matchIndex:n=-1}){if(!this._scrollMatches||!e)return;if(-1===n||n!==this._selected.matchIdx)return;if(-1===i||i!==this._selected.pageIdx)return;this._scrollMatches=!1;m(e,{top:-50,left:t+-400},!0)}#l(){this._highlightMatches=!1,this._scrollMatches=!1,this._pdfDocument=null,this._pageMatches=[],this._pageMatchesLength=[],this.#o=0,this.#r=null,this._selected={pageIdx:-1,matchIdx:-1},this._offset={pageIdx:null,matchIdx:null,wrapped:!1},this._extractTextPromises=[],this._pageContents=[],this._pageDiffs=[],this._hasDiacritics=[],this._matchesCountTotal=0,this._pagesToSearch=null,this._pendingFindMatches=new Set,this._resumePageIdx=null,this._dirtyMatch=!1,clearTimeout(this._findTimeout),this._findTimeout=null,this._firstPageCapability=Promise.withResolvers()}get#m(){const{query:e}=this.#r;return"string"==typeof e?(e!==this._rawQuery&&(this._rawQuery=e,[this._normalizedQuery]=J(e)),this._normalizedQuery):(e||[]).filter((e=>!!e)).map((e=>J(e)[0]))}#c(e){const t=e.query,i=this.#r.query,n=typeof t;if(n!==typeof i)return!0;if("string"===n){if(t!==i)return!0}else if(JSON.stringify(t)!==JSON.stringify(i))return!0;switch(e.type){case"again":const e=this._selected.pageIdx+1,t=this._linkService;return e>=1&&e<=t.pagesCount&&e!==t.page&&!(this.onIsPageVisible?.(e)??1);case"highlightallchange":return!1}return!0}#b(e,t,i){let n=e.slice(0,t).match(z);if(n){const i=e.charCodeAt(t),s=n[1].charCodeAt(0);if(F(i)===F(s))return!1}if(n=e.slice(t+i).match(U),n){const s=e.charCodeAt(t+i-1),r=n[1].charCodeAt(0);if(F(s)===F(r))return!1}return!0}#y(e,t,i,n){const s=this._pageMatches[i]=[],r=this._pageMatchesLength[i]=[];if(!e)return;const a=this._pageDiffs[i];let o;for(;null!==(o=e.exec(n));){if(t&&!this.#b(n,o.index,o[0].length))continue;const[e,i]=q(a,o.index,o[0].length);i&&(s.push(e),r.push(i))}}#v(e,t){const{matchDiacritics:i}=this.#r;let n=!1;const s="[ ]*";return(e=e.replaceAll(H,((e,s,r,a,o,l)=>s?`[ ]*\\${s}[ ]*`:r?`[ ]*${r}[ ]*`:a?"[ ]+":i?o||l:o?V.has(o.charCodeAt(0))?o:"":t?(n=!0,`${l}\\p{M}*`):l))).endsWith(s)&&(e=e.slice(0,e.length-s.length)),i&&t&&(B||=String.fromCharCode(...V),n=!0,e=`${e}(?=[${B}]|[^\\p{M}]|$)`),[n,e]}#w(e){let t=this.#m;if(0===t.length)return;const{caseSensitive:i,entireWord:n}=this.#r,s=this._pageContents[e],r=this._hasDiacritics[e];let a=!1;"string"==typeof t?[a,t]=this.#v(t,r):t=t.sort().reverse().map((e=>{const[t,i]=this.#v(e,r);return a||=t,`(${i})`})).join("|");t=t?new RegExp(t,`g${a?"u":""}${i?"":"i"}`):null,this.#y(t,n,e,s),this.#r.highlightAll&&this.#P(e),this._resumePageIdx===e&&(this._resumePageIdx=null,this.#_());const o=this._pageMatches[e].length;this._matchesCountTotal+=o,this.#a?o>0&&this.#x():++this.#o===this._linkService.pagesCount&&this.#x()}#p(){if(this._extractTextPromises.length>0)return;let e=Promise.resolve();const t={disableNormalization:!0};for(let i=0,n=this._linkService.pagesCount;i<n;i++){const{promise:n,resolve:s}=Promise.withResolvers();this._extractTextPromises[i]=n,e=e.then((()=>this._pdfDocument.getPage(i+1).then((e=>e.getTextContent(t))).then((e=>{const t=[];for(const i of e.items)t.push(i.str),i.hasEOL&&t.push("\n");[this._pageContents[i],this._pageDiffs[i],this._hasDiacritics[i]]=J(t.join("")),s()}),(e=>{console.error(`Unable to get text content for page ${i+1}`,e),this._pageContents[i]="",this._pageDiffs[i]=null,this._hasDiacritics[i]=!1,s()}))))}}#P(e){this._scrollMatches&&this._selected.pageIdx===e&&(this._linkService.page=e+1),this._eventBus.dispatch("updatetextlayermatches",{source:this,pageIndex:e})}#f(){this._eventBus.dispatch("updatetextlayermatches",{source:this,pageIndex:-1})}#g(){const e=this.#r.findPrevious,t=this._linkService.page-1,i=this._linkService.pagesCount;if(this._highlightMatches=!0,this._dirtyMatch){this._dirtyMatch=!1,this._selected.pageIdx=this._selected.matchIdx=-1,this._offset.pageIdx=t,this._offset.matchIdx=null,this._offset.wrapped=!1,this._resumePageIdx=null,this._pageMatches.length=0,this._pageMatchesLength.length=0,this.#o=0,this._matchesCountTotal=0,this.#f();for(let e=0;e<i;e++)this._pendingFindMatches.has(e)||(this._pendingFindMatches.add(e),this._extractTextPromises[e].then((()=>{this._pendingFindMatches.delete(e),this.#w(e)})))}if(0===this.#m.length)return void this.#u(O.FOUND);if(this._resumePageIdx)return;const n=this._offset;if(this._pagesToSearch=i,null!==n.matchIdx){const t=this._pageMatches[n.pageIdx].length;if(!e&&n.matchIdx+1<t||e&&n.matchIdx>0)return n.matchIdx=e?n.matchIdx-1:n.matchIdx+1,void this.#S(!0);this.#C(e)}this.#_()}#L(e){const t=this._offset,i=e.length,n=this.#r.findPrevious;return i?(t.matchIdx=n?i-1:0,this.#S(!0),!0):(this.#C(n),!!(t.wrapped&&(t.matchIdx=null,this._pagesToSearch<0))&&(this.#S(!1),!0))}#_(){null!==this._resumePageIdx&&console.error("There can only be one pending page.");let e=null;do{const t=this._offset.pageIdx;if(e=this._pageMatches[t],!e){this._resumePageIdx=t;break}}while(!this.#L(e))}#C(e){const t=this._offset,i=this._linkService.pagesCount;t.pageIdx=e?t.pageIdx-1:t.pageIdx+1,t.matchIdx=null,this._pagesToSearch--,(t.pageIdx>=i||t.pageIdx<0)&&(t.pageIdx=e?i-1:0,t.wrapped=!0)}#S(e=!1){let t=O.NOT_FOUND;const i=this._offset.wrapped;if(this._offset.wrapped=!1,e){const e=this._selected.pageIdx;this._selected.pageIdx=this._offset.pageIdx,this._selected.matchIdx=this._offset.matchIdx,t=i?O.WRAPPED:O.FOUND,-1!==e&&e!==this._selected.pageIdx&&this.#P(e)}this.#u(t,this.#r.findPrevious),-1!==this._selected.pageIdx&&(this._scrollMatches=!0,this.#P(this._selected.pageIdx))}#d(e){const t=this._pdfDocument;this._firstPageCapability.promise.then((()=>{!this._pdfDocument||t&&this._pdfDocument!==t||(this._findTimeout&&(clearTimeout(this._findTimeout),this._findTimeout=null),this._resumePageIdx&&(this._resumePageIdx=null,this._dirtyMatch=!0),this.#u(O.FOUND),this._highlightMatches=!1,this.#f())}))}#M(){const{pageIdx:e,matchIdx:t}=this._selected;let i=0,n=this._matchesCountTotal;if(-1!==t){for(let t=0;t<e;t++)i+=this._pageMatches[t]?.length||0;i+=t+1}return(i<1||i>n)&&(i=n=0),{current:i,total:n}}#x(){this._eventBus.dispatch("updatefindmatchescount",{source:this,matchesCount:this.#M()})}#u(e,t=!1){(this.#a||this.#o===this._linkService.pagesCount&&e!==O.PENDING)&&this._eventBus.dispatch("updatefindcontrolstate",{source:this,state:e,previous:t,entireWord:this.#r?.entireWord??null,matchesCount:this.#M(),rawQuery:this.#r?.query??null})}}const Y={NONE:0,SELF:1,BLANK:2,PARENT:3,TOP:4};class ee{externalLinkEnabled=!0;constructor({eventBus:e,externalLinkTarget:t=null,externalLinkRel:i=null,ignoreDestinationZoom:n=!1}={}){this.eventBus=e,this.externalLinkTarget=t,this.externalLinkRel=i,this._ignoreDestinationZoom=n,this.baseUrl=null,this.pdfDocument=null,this.pdfViewer=null,this.pdfHistory=null}setDocument(e,t=null){this.baseUrl=t,this.pdfDocument=e}setViewer(e){this.pdfViewer=e}setHistory(e){this.pdfHistory=e}get pagesCount(){return this.pdfDocument?this.pdfDocument.numPages:0}get page(){return this.pdfDocument?this.pdfViewer.currentPageNumber:1}set page(e){this.pdfDocument&&(this.pdfViewer.currentPageNumber=e)}get rotation(){return this.pdfDocument?this.pdfViewer.pagesRotation:0}set rotation(e){this.pdfDocument&&(this.pdfViewer.pagesRotation=e)}get isInPresentationMode(){return!!this.pdfDocument&&this.pdfViewer.isInPresentationMode}async goToDestination(e){if(!this.pdfDocument)return;let t,i,n;if("string"==typeof e?(t=e,i=await this.pdfDocument.getDestination(e)):(t=null,i=await e),!Array.isArray(i))return void console.error(`goToDestination: "${i}" is not a valid destination array, for dest="${e}".`);const[s]=i;if(s&&"object"==typeof s){if(n=this.pdfDocument.cachedPageNumber(s),!n)try{n=await this.pdfDocument.getPageIndex(s)+1}catch{return void console.error(`goToDestination: "${s}" is not a valid page reference, for dest="${e}".`)}}else Number.isInteger(s)&&(n=s+1);!n||n<1||n>this.pagesCount?console.error(`goToDestination: "${n}" is not a valid page number, for dest="${e}".`):(this.pdfHistory&&(this.pdfHistory.pushCurrentPosition(),this.pdfHistory.push({namedDest:t,explicitDest:i,pageNumber:n})),this.pdfViewer.scrollPageIntoView({pageNumber:n,destArray:i,ignoreDestinationZoom:this._ignoreDestinationZoom}))}goToPage(e){if(!this.pdfDocument)return;const t="string"==typeof e&&this.pdfViewer.pageLabelToPageNumber(e)||0|e;Number.isInteger(t)&&t>0&&t<=this.pagesCount?(this.pdfHistory&&(this.pdfHistory.pushCurrentPosition(),this.pdfHistory.pushPage(t)),this.pdfViewer.scrollPageIntoView({pageNumber:t})):console.error(`PDFLinkService.goToPage: "${e}" is not a valid page.`)}addLinkAttributes(e,t,i=!1){if(!t||"string"!=typeof t)throw new Error('A valid "url" parameter must provided.');const n=i?Y.BLANK:this.externalLinkTarget,s=this.externalLinkRel;this.externalLinkEnabled?e.href=e.title=t:(e.href="",e.title=`Disabled: ${t}`,e.onclick=()=>!1);let r="";switch(n){case Y.NONE:break;case Y.SELF:r="_self";break;case Y.BLANK:r="_blank";break;case Y.PARENT:r="_parent";break;case Y.TOP:r="_top"}e.target=r,e.rel="string"==typeof s?s:"noopener noreferrer nofollow"}getDestinationHash(e){if("string"==typeof e){if(e.length>0)return this.getAnchorUrl("#"+escape(e))}else if(Array.isArray(e)){const t=JSON.stringify(e);if(t.length>0)return this.getAnchorUrl("#"+escape(t))}return this.getAnchorUrl("")}getAnchorUrl(e){return this.baseUrl?this.baseUrl+e:e}setHash(e){if(!this.pdfDocument)return;let t,i;if(e.includes("=")){const n=b(e);if(n.has("search")){const e=n.get("search").replaceAll('"',""),t="true"===n.get("phrase");this.eventBus.dispatch("findfromurlhash",{source:this,query:t?e:e.match(/\S+/g)})}if(n.has("page")&&(t=0|n.get("page")||1),n.has("zoom")){const e=n.get("zoom").split(","),t=e[0],s=parseFloat(t);t.includes("Fit")?"Fit"===t||"FitB"===t?i=[null,{name:t}]:"FitH"===t||"FitBH"===t||"FitV"===t||"FitBV"===t?i=[null,{name:t},e.length>1?0|e[1]:null]:"FitR"===t?5!==e.length?console.error('PDFLinkService.setHash: Not enough parameters for "FitR".'):i=[null,{name:t},0|e[1],0|e[2],0|e[3],0|e[4]]:console.error(`PDFLinkService.setHash: "${t}" is not a valid zoom value.`):i=[null,{name:"XYZ"},e.length>1?0|e[1]:null,e.length>2?0|e[2]:null,s?s/100:t]}return i?this.pdfViewer.scrollPageIntoView({pageNumber:t||this.page,destArray:i,allowNegativeOffset:!0}):t&&(this.page=t),n.has("pagemode")&&this.eventBus.dispatch("pagemode",{source:this,mode:n.get("pagemode")}),void(n.has("nameddest")&&this.goToDestination(n.get("nameddest")))}i=unescape(e);try{i=JSON.parse(i),Array.isArray(i)||(i=i.toString())}catch{}"string"==typeof i||ee.#E(i)?this.goToDestination(i):console.error(`PDFLinkService.setHash: "${unescape(e)}" is not a valid destination.`)}executeNamedAction(e){if(this.pdfDocument){switch(e){case"GoBack":this.pdfHistory?.back();break;case"GoForward":this.pdfHistory?.forward();break;case"NextPage":this.pdfViewer.nextPage();break;case"PrevPage":this.pdfViewer.previousPage();break;case"LastPage":this.page=this.pagesCount;break;case"FirstPage":this.page=1}this.eventBus.dispatch("namedaction",{source:this,action:e})}}async executeSetOCGState(e){if(!this.pdfDocument)return;const t=this.pdfDocument,i=await this.pdfViewer.optionalContentConfigPromise;t===this.pdfDocument&&(i.setOCGState(e),this.pdfViewer.optionalContentConfigPromise=Promise.resolve(i))}static#E(e){if(!Array.isArray(e)||e.length<2)return!1;const[t,i,...n]=e;if(!("object"==typeof t&&Number.isInteger(t?.num)&&Number.isInteger(t?.gen)||Number.isInteger(t)))return!1;if("object"!=typeof i||"string"!=typeof i?.name)return!1;let s=!0;switch(i.name){case"XYZ":if(3!==n.length)return!1;break;case"Fit":case"FitB":return 0===n.length;case"FitH":case"FitBH":case"FitV":case"FitBV":if(1!==n.length)return!1;break;case"FitR":if(4!==n.length)return!1;s=!1;break;default:return!1}for(const e of n)if(!("number"==typeof e||s&&null===e))return!1;return!0}}class te extends ee{setDocument(e,t=null){}}const{AbortException:ie,AnnotationEditorLayer:ne,AnnotationEditorParamsType:se,AnnotationEditorType:re,AnnotationEditorUIManager:ae,AnnotationLayer:oe,AnnotationMode:le,build:he,CMapCompressionType:de,ColorPicker:ce,createValidAbsoluteUrl:ue,DOMSVGFactory:pe,DrawLayer:ge,FeatureTest:fe,fetchData:me,getDocument:be,getFilenameFromUrl:ye,getPdfFilenameFromUrl:ve,getXfaPageViewport:we,GlobalWorkerOptions:Pe,ImageKind:_e,InvalidPDFException:xe,isDataScheme:Se,isPdfFile:Ce,MissingPDFException:Le,noContextMenu:Me,normalizeUnicode:Ee,OPS:Ae,Outliner:Ie,PasswordResponses:ke,PDFDataRangeTransport:Te,PDFDateString:De,PDFWorker:Ne,PermissionFlag:Fe,PixelsPerInch:je,RenderingCancelledException:Oe,renderTextLayer:Re,setLayerDimensions:Ve,shadow:Be,TextLayer:$e,UnexpectedResponseException:He,updateTextLayer:ze,Util:Ue,VerbosityLevel:We,version:Ze,XfaLayer:Ge}=globalThis.pdfjsLib;class Xe{#A=null;#I=null;constructor({pdfPage:e,linkService:t,downloadManager:i,annotationStorage:n=null,imageResourcesPath:s="",renderForms:r=!0,enableScripting:a=!1,hasJSActionsPromise:o=null,fieldObjectsPromise:l=null,annotationCanvasMap:h=null,accessibilityManager:d=null,annotationEditorUIManager:c=null,onAppend:u=null}){this.pdfPage=e,this.linkService=t,this.downloadManager=i,this.imageResourcesPath=s,this.renderForms=r,this.annotationStorage=n,this.enableScripting=a,this._hasJSActionsPromise=o||Promise.resolve(!1),this._fieldObjectsPromise=l||Promise.resolve(null),this._annotationCanvasMap=h,this._accessibilityManager=d,this._annotationEditorUIManager=c,this.#A=u,this.annotationLayer=null,this.div=null,this._cancelled=!1,this._eventBus=t.eventBus}async render(e,t="display"){if(this.div){if(this._cancelled||!this.annotationLayer)return;return void this.annotationLayer.update({viewport:e.clone({dontFlip:!0})})}const[i,n,s]=await Promise.all([this.pdfPage.getAnnotations({intent:t}),this._hasJSActionsPromise,this._fieldObjectsPromise]);if(this._cancelled)return;const r=this.div=document.createElement("div");r.className="annotationLayer",this.#A?.(r),0!==i.length?(this.annotationLayer=new oe({div:r,accessibilityManager:this._accessibilityManager,annotationCanvasMap:this._annotationCanvasMap,annotationEditorUIManager:this._annotationEditorUIManager,page:this.pdfPage,viewport:e.clone({dontFlip:!0})}),await this.annotationLayer.render({annotations:i,imageResourcesPath:this.imageResourcesPath,renderForms:this.renderForms,linkService:this.linkService,downloadManager:this.downloadManager,annotationStorage:this.annotationStorage,enableScripting:this.enableScripting,hasJSActions:n,fieldObjects:s}),this.linkService.isInPresentationMode&&this.#k(h),this.#I||(this.#I=new AbortController,this._eventBus?._on("presentationmodechanged",(e=>{this.#k(e.state)}),{signal:this.#I.signal}))):this.hide()}cancel(){this._cancelled=!0,this.#I?.abort(),this.#I=null}hide(){this.div&&(this.div.hidden=!0)}#k(e){if(!this.div)return;let t=!1;switch(e){case h:t=!0;break;case o:break;default:return}for(const e of this.div.childNodes)e.hasAttribute("data-internal-link")||(e.inert=t)}}function Qe(e,t){const i=document.createElement("a");if(!i.click)throw new Error('DownloadManager: "a.click()" is not supported.');i.href=e,i.target="_parent","download"in i&&(i.download=t),(document.body||document.documentElement).append(i),i.click(),i.remove()}class Je{#T=new WeakMap;downloadData(e,t,i){Qe(URL.createObjectURL(new Blob([e],{type:i})),t)}openOrDownloadData(e,t,i=null){const n=Ce(t)?"application/pdf":"";return this.downloadData(e,t,n),!1}download(e,t,i,n){let s;if(e)s=URL.createObjectURL(new Blob([e],{type:"application/pdf"}));else{if(!ue(t,"http://example.com"))return void console.error(`download - not a valid URL: ${t}`);s=t+"#pdfjs.action=download"}Qe(s,i)}}const qe="event",Ke="timeout";class Ye{#D=Object.create(null);on(e,t,i=null){this._on(e,t,{external:!0,once:i?.once,signal:i?.signal})}off(e,t,i=null){this._off(e,t)}dispatch(e,t){const i=this.#D[e];if(!i||0===i.length)return;let n;for(const{listener:s,external:r,once:a}of i.slice(0))a&&this._off(e,s),r?(n||=[]).push(s):s(t);if(n){for(const e of n)e(t);n=null}}_on(e,t,i=null){let n=null;if(i?.signal instanceof AbortSignal){const{signal:s}=i;if(s.aborted)return void console.error("Cannot use an `aborted` signal.");const r=()=>this._off(e,t);n=()=>s.removeEventListener("abort",r),s.addEventListener("abort",r)}(this.#D[e]||=[]).push({listener:t,external:!0===i?.external,once:!0===i?.once,rmAbort:n})}_off(e,t,i=null){const n=this.#D[e];if(n)for(let e=0,i=n.length;e<i;e++){const i=n[e];if(i.listener===t)return i.rmAbort?.(),void n.splice(e,1)}}}class et{constructor(e){this.value=e}valueOf(){return this.value}}class tt extends et{constructor(e="???"){super(e)}toString(e){return`{${this.value}}`}}class it extends et{constructor(e,t={}){super(e),this.opts=t}toString(e){try{return e.memoizeIntlObject(Intl.NumberFormat,this.opts).format(this.value)}catch(t){return e.reportError(t),this.value.toString(10)}}}class nt extends et{constructor(e,t={}){super(e),this.opts=t}toString(e){try{return e.memoizeIntlObject(Intl.DateTimeFormat,this.opts).format(this.value)}catch(t){return e.reportError(t),new Date(this.value).toISOString()}}}function st(e,t,i){if(i===t)return!0;if(i instanceof it&&t instanceof it&&i.value===t.value)return!0;if(t instanceof it&&"string"==typeof i){if(i===e.memoizeIntlObject(Intl.PluralRules,t.opts).select(t.value))return!0}return!1}function rt(e,t,i){return t[i]?ht(e,t[i].value):(e.reportError(new RangeError("No default")),new tt)}function at(e,t){const i=[],n=Object.create(null);for(const s of t)"narg"===s.type?n[s.name]=ot(e,s.value):i.push(ot(e,s));return{positional:i,named:n}}function ot(e,t){switch(t.type){case"str":return t.value;case"num":return new it(t.value,{minimumFractionDigits:t.precision});case"var":return function(e,{name:t}){let i;if(e.params){if(!Object.prototype.hasOwnProperty.call(e.params,t))return new tt(`$${t}`);i=e.params[t]}else{if(!e.args||!Object.prototype.hasOwnProperty.call(e.args,t))return e.reportError(new ReferenceError(`Unknown variable: $${t}`)),new tt(`$${t}`);i=e.args[t]}if(i instanceof et)return i;switch(typeof i){case"string":return i;case"number":return new it(i);case"object":if(i instanceof Date)return new nt(i.getTime());default:return e.reportError(new TypeError(`Variable type not supported: $${t}, ${typeof i}`)),new tt(`$${t}`)}}(e,t);case"mesg":return function(e,{name:t,attr:i}){const n=e.bundle._messages.get(t);if(!n)return e.reportError(new ReferenceError(`Unknown message: ${t}`)),new tt(t);if(i){const s=n.attributes[i];return s?ht(e,s):(e.reportError(new ReferenceError(`Unknown attribute: ${i}`)),new tt(`${t}.${i}`))}if(n.value)return ht(e,n.value);return e.reportError(new ReferenceError(`No value: ${t}`)),new tt(t)}(e,t);case"term":return function(e,{name:t,attr:i,args:n}){const s=`-${t}`,r=e.bundle._terms.get(s);if(!r)return e.reportError(new ReferenceError(`Unknown term: ${s}`)),new tt(s);if(i){const t=r.attributes[i];if(t){e.params=at(e,n).named;const i=ht(e,t);return e.params=null,i}return e.reportError(new ReferenceError(`Unknown attribute: ${i}`)),new tt(`${s}.${i}`)}e.params=at(e,n).named;const a=ht(e,r.value);return e.params=null,a}(e,t);case"func":return function(e,{name:t,args:i}){let n=e.bundle._functions[t];if(!n)return e.reportError(new ReferenceError(`Unknown function: ${t}()`)),new tt(`${t}()`);if("function"!=typeof n)return e.reportError(new TypeError(`Function ${t}() is not callable`)),new tt(`${t}()`);try{let t=at(e,i);return n(t.positional,t.named)}catch(i){return e.reportError(i),new tt(`${t}()`)}}(e,t);case"select":return function(e,{selector:t,variants:i,star:n}){let s=ot(e,t);if(s instanceof tt)return rt(e,i,n);for(const t of i){if(st(e,s,ot(e,t.key)))return ht(e,t.value)}return rt(e,i,n)}(e,t);default:return new tt}}function lt(e,t){if(e.dirty.has(t))return e.reportError(new RangeError("Cyclic reference")),new tt;e.dirty.add(t);const i=[],n=e.bundle._useIsolating&&t.length>1;for(const s of t)if("string"!=typeof s){if(e.placeables++,e.placeables>100)throw e.dirty.delete(t),new RangeError(`Too many placeables expanded: ${e.placeables}, max allowed is 100`);n&&i.push("⁨"),i.push(ot(e,s).toString(e)),n&&i.push("⁩")}else i.push(e.bundle._transform(s));return e.dirty.delete(t),i.join("")}function ht(e,t){return"string"==typeof t?e.bundle._transform(t):lt(e,t)}class dt{constructor(e,t,i){this.dirty=new WeakSet,this.params=null,this.placeables=0,this.bundle=e,this.errors=t,this.args=i}reportError(e){if(!(this.errors&&e instanceof Error))throw e;this.errors.push(e)}memoizeIntlObject(e,t){let i=this.bundle._intls.get(e);i||(i={},this.bundle._intls.set(e,i));let n=JSON.stringify(t);return i[n]||(i[n]=new e(this.bundle.locales,t)),i[n]}}function ct(e,t){const i=Object.create(null);for(const[n,s]of Object.entries(e))t.includes(n)&&(i[n]=s.valueOf());return i}const ut=["unitDisplay","currencyDisplay","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits"];function pt(e,t){let i=e[0];if(i instanceof tt)return new tt(`NUMBER(${i.valueOf()})`);if(i instanceof it)return new it(i.valueOf(),{...i.opts,...ct(t,ut)});if(i instanceof nt)return new it(i.valueOf(),{...ct(t,ut)});throw new TypeError("Invalid argument to NUMBER")}const gt=["dateStyle","timeStyle","fractionalSecondDigits","dayPeriod","hour12","weekday","era","year","month","day","hour","minute","second","timeZoneName"];function ft(e,t){let i=e[0];if(i instanceof tt)return new tt(`DATETIME(${i.valueOf()})`);if(i instanceof nt)return new nt(i.valueOf(),{...i.opts,...ct(t,gt)});if(i instanceof it)return new nt(i.valueOf(),{...ct(t,gt)});throw new TypeError("Invalid argument to DATETIME")}const mt=new Map;class bt{constructor(e,{functions:t,useIsolating:i=!0,transform:n=(e=>e)}={}){this._terms=new Map,this._messages=new Map,this.locales=Array.isArray(e)?e:[e],this._functions={NUMBER:pt,DATETIME:ft,...t},this._useIsolating=i,this._transform=n,this._intls=function(e){const t=Array.isArray(e)?e.join(" "):e;let i=mt.get(t);return void 0===i&&(i=new Map,mt.set(t,i)),i}(e)}hasMessage(e){return this._messages.has(e)}getMessage(e){return this._messages.get(e)}addResource(e,{allowOverrides:t=!1}={}){const i=[];for(let n=0;n<e.body.length;n++){let s=e.body[n];if(s.id.startsWith("-")){if(!1===t&&this._terms.has(s.id)){i.push(new Error(`Attempt to override an existing term: "${s.id}"`));continue}this._terms.set(s.id,s)}else{if(!1===t&&this._messages.has(s.id)){i.push(new Error(`Attempt to override an existing message: "${s.id}"`));continue}this._messages.set(s.id,s)}}return i}formatPattern(e,t=null,i=null){if("string"==typeof e)return this._transform(e);let n=new dt(this,i,t);try{return lt(n,e).toString(n)}catch(e){if(n.errors&&e instanceof Error)return n.errors.push(e),(new tt).toString(n);throw e}}}const yt=/^(-?[a-zA-Z][\w-]*) *= */gm,vt=/\.([a-zA-Z][\w-]*) *= */y,wt=/\*?\[/y,Pt=/(-?[0-9]+(?:\.([0-9]+))?)/y,_t=/([a-zA-Z][\w-]*)/y,xt=/([$-])?([a-zA-Z][\w-]*)(?:\.([a-zA-Z][\w-]*))?/y,St=/^[A-Z][A-Z0-9_-]*$/,Ct=/([^{}\n\r]+)/y,Lt=/([^\\"\n\r]*)/y,Mt=/\\([\\"])/y,Et=/\\u([a-fA-F0-9]{4})|\\U([a-fA-F0-9]{6})/y,At=/^\n+/,It=/ +$/,kt=/ *\r?\n/g,Tt=/( *)$/,Dt=/{\s*/y,Nt=/\s*}/y,Ft=/\[\s*/y,jt=/\s*] */y,Ot=/\s*\(\s*/y,Rt=/\s*->\s*/y,Vt=/\s*:\s*/y,Bt=/\s*,?\s*/y,$t=/\s+/y;class Ht{constructor(e){this.body=[],yt.lastIndex=0;let t=0;for(;;){let i=yt.exec(e);if(null===i)break;t=yt.lastIndex;try{this.body.push(o(i[1]))}catch(e){if(e instanceof SyntaxError)continue;throw e}}function i(i){return i.lastIndex=t,i.test(e)}function n(i,n){if(e[t]===i)return t++,!0;if(n)throw new n(`Expected ${i}`);return!1}function s(e,n){if(i(e))return t=e.lastIndex,!0;if(n)throw new n(`Expected ${e.toString()}`);return!1}function r(i){i.lastIndex=t;let n=i.exec(e);if(null===n)throw new SyntaxError(`Expected ${i.toString()}`);return t=i.lastIndex,n}function a(e){return r(e)[1]}function o(e){let t=l(),n=function(){let e=Object.create(null);for(;i(vt);){let t=a(vt),i=l();if(null===i)throw new SyntaxError("Expected attribute value");e[t]=i}return e}();if(null===t&&0===Object.keys(n).length)throw new SyntaxError("Expected message value or attributes");return{id:e,value:t,attributes:n}}function l(){let n;if(i(Ct)&&(n=a(Ct)),"{"===e[t]||"}"===e[t])return h(n?[n]:[],1/0);let s=b();return s?n?h([n,s],s.length):(s.value=y(s.value,At),h([s],s.length)):n?y(n,It):null}function h(n=[],s){for(;;){if(i(Ct)){n.push(a(Ct));continue}if("{"===e[t]){n.push(d());continue}if("}"===e[t])throw new SyntaxError("Unbalanced closing brace");let r=b();if(!r)break;n.push(r),s=Math.min(s,r.length)}let r=n.length-1,o=n[r];"string"==typeof o&&(n[r]=y(o,It));let l=[];for(let e of n)e instanceof zt&&(e=e.value.slice(0,e.value.length-s)),e&&l.push(e);return l}function d(){s(Dt,SyntaxError);let e=c();if(s(Nt))return e;if(s(Rt)){let t=function(){let e,t=[],s=0;for(;i(wt);){n("*")&&(e=s);let i=p(),r=l();if(null===r)throw new SyntaxError("Expected variant value");t[s++]={key:i,value:r}}if(0===s)return null;if(void 0===e)throw new SyntaxError("Expected default variant");return{variants:t,star:e}}();return s(Nt,SyntaxError),{type:"select",selector:e,...t}}throw new SyntaxError("Unclosed placeable")}function c(){if("{"===e[t])return d();if(i(xt)){let[,i,n,a=null]=r(xt);if("$"===i)return{type:"var",name:n};if(s(Ot)){let r=function(){let i=[];for(;;){switch(e[t]){case")":return t++,i;case void 0:throw new SyntaxError("Unclosed argument list")}i.push(u()),s(Bt)}}();if("-"===i)return{type:"term",name:n,attr:a,args:r};if(St.test(n))return{type:"func",name:n,args:r};throw new SyntaxError("Function names must be all upper-case")}return"-"===i?{type:"term",name:n,attr:a,args:[]}:{type:"mesg",name:n,attr:a}}return g()}function u(){let e=c();return"mesg"!==e.type?e:s(Vt)?{type:"narg",name:e.name,value:g()}:e}function p(){let e;return s(Ft,SyntaxError),e=i(Pt)?f():{type:"str",value:a(_t)},s(jt,SyntaxError),e}function g(){if(i(Pt))return f();if('"'===e[t])return function(){n('"',SyntaxError);let i="";for(;;){if(i+=a(Lt),"\\"!==e[t]){if(n('"'))return{type:"str",value:i};throw new SyntaxError("Unclosed string literal")}i+=m()}}();throw new SyntaxError("Invalid expression")}function f(){let[,e,t=""]=r(Pt),i=t.length;return{type:"num",value:parseFloat(e),precision:i}}function m(){if(i(Mt))return a(Mt);if(i(Et)){let[,e,t]=r(Et),i=parseInt(e||t,16);return i<=55295||57344<=i?String.fromCodePoint(i):"�"}throw new SyntaxError("Unknown escape sequence")}function b(){let i=t;switch(s($t),e[t]){case".":case"[":case"*":case"}":case void 0:return!1;case"{":return v(e.slice(i,t))}return" "===e[t-1]&&v(e.slice(i,t))}function y(e,t){return e.replace(t,"")}function v(e){let t=e.replace(kt,"\n"),i=Tt.exec(e)[1].length;return new zt(t,i)}}}class zt{constructor(e,t){this.value=e,this.length=t}}const Ut=/<|&#?\w+;/,Wt={"http://www.w3.org/1999/xhtml":["em","strong","small","s","cite","q","dfn","abbr","data","time","code","var","samp","kbd","sub","sup","i","b","u","mark","bdi","bdo","span","br","wbr"]},Zt={"http://www.w3.org/1999/xhtml":{global:["title","aria-label","aria-valuetext"],a:["download"],area:["download","alt"],input:["alt","placeholder"],menuitem:["label"],menu:["label"],optgroup:["label"],option:["label"],track:["label"],img:["alt"],textarea:["placeholder"],th:["abbr"]},"http://www.mozilla.org/keymaster/gatekeeper/there.is.only.xul":{global:["accesskey","aria-label","aria-valuetext","label","title","tooltiptext"],description:["value"],key:["key","keycode"],label:["value"],textbox:["placeholder","value"]}};function Gt(e,t){const{value:i}=t;if("string"==typeof i)if("title"===e.localName&&"http://www.w3.org/1999/xhtml"===e.namespaceURI)e.textContent=i;else if(Ut.test(i)){const t=e.ownerDocument.createElementNS("http://www.w3.org/1999/xhtml","template");t.innerHTML=i,function(e,t){for(const i of e.childNodes)if(i.nodeType!==i.TEXT_NODE)if(i.hasAttribute("data-l10n-name")){const n=Jt(t,i);e.replaceChild(n,i)}else if(Yt(i)){const t=qt(i);e.replaceChild(t,i)}else console.warn(`An element of forbidden type "${i.localName}" was found in the translation. Only safe text-level elements and elements with data-l10n-name are allowed.`),e.replaceChild(Kt(i),i);t.textContent="",t.appendChild(e)}(t.content,e)}else e.textContent=i;Qt(t,e)}function Xt(e,t){if(!e)return!1;for(let i of e)if(i.name===t)return!0;return!1}function Qt(e,t){const i=t.hasAttribute("data-l10n-attrs")?t.getAttribute("data-l10n-attrs").split(",").map((e=>e.trim())):null;for(const n of Array.from(t.attributes))ei(n.name,t,i)&&!Xt(e.attributes,n.name)&&t.removeAttribute(n.name);if(e.attributes)for(const n of Array.from(e.attributes))ei(n.name,t,i)&&t.getAttribute(n.name)!==n.value&&t.setAttribute(n.name,n.value)}function Jt(e,t){const i=t.getAttribute("data-l10n-name"),n=e.querySelector(`[data-l10n-name="${i}"]`);if(!n)return console.warn(`An element named "${i}" wasn't found in the source.`),Kt(t);if(n.localName!==t.localName)return console.warn(`An element named "${i}" was found in the translation but its type ${t.localName} didn't match the element found in the source (${n.localName}).`),Kt(t);e.removeChild(n);return ti(t,n.cloneNode(!1))}function qt(e){const t=e.ownerDocument.createElement(e.localName);return ti(e,t)}function Kt(e){return e.ownerDocument.createTextNode(e.textContent)}function Yt(e){const t=Wt[e.namespaceURI];return t&&t.includes(e.localName)}function ei(e,t,i=null){if(i&&i.includes(e))return!0;const n=Zt[t.namespaceURI];if(!n)return!1;const s=e.toLowerCase(),r=t.localName;if(n.global.includes(s))return!0;if(!n[r])return!1;if(n[r].includes(s))return!0;if("http://www.w3.org/1999/xhtml"===t.namespaceURI&&"input"===r&&"value"===s){const e=t.type.toLowerCase();if("submit"===e||"button"===e||"reset"===e)return!0}return!1}function ti(e,t){return t.textContent=e.textContent,Qt(e,t),t}class ii extends Array{static from(e){return e instanceof this?e:new this(e)}}class ni extends ii{constructor(e){if(super(),!(Symbol.iterator in Object(e)))throw new TypeError("Argument must implement the iteration protocol.");this.iterator=e[Symbol.iterator]()}[Symbol.iterator](){const e=this;let t=0;return{next(){return e.length<=t&&e.push(e.iterator.next()),e[t++]}}}touchNext(e=1){let t=0;for(;t++<e;){const e=this[this.length-1];if(e&&e.done)break;this.push(this.iterator.next())}return this[this.length-1]}}class si extends ii{constructor(e){if(super(),Symbol.asyncIterator in Object(e))this.iterator=e[Symbol.asyncIterator]();else{if(!(Symbol.iterator in Object(e)))throw new TypeError("Argument must implement the iteration protocol.");this.iterator=e[Symbol.iterator]()}}[Symbol.asyncIterator](){const e=this;let t=0;return{async next(){return e.length<=t&&e.push(e.iterator.next()),e[t++]}}}async touchNext(e=1){let t=0;for(;t++<e;){const e=this[this.length-1];if(e&&(await e).done)break;this.push(this.iterator.next())}return this[this.length-1]}}class ri{constructor(e=[],t){this.resourceIds=e,this.generateBundles=t,this.onChange(!0)}addResourceIds(e,t=!1){return this.resourceIds.push(...e),this.onChange(t),this.resourceIds.length}removeResourceIds(e){return this.resourceIds=this.resourceIds.filter((t=>!e.includes(t))),this.onChange(),this.resourceIds.length}async formatWithFallback(e,t){const i=[];let n=!1;for await(const s of this.bundles){n=!0;const r=li(t,s,e,i);if(0===r.size)break;if("undefined"!=typeof console){const e=s.locales[0],t=Array.from(r).join(", ");console.warn(`[fluent] Missing translations in ${e}: ${t}`)}}return n||"undefined"==typeof console||console.warn(`[fluent] Request for keys failed because no resource bundles got generated.\n  keys: ${JSON.stringify(e)}.\n  resourceIds: ${JSON.stringify(this.resourceIds)}.`),i}formatMessages(e){return this.formatWithFallback(e,oi)}formatValues(e){return this.formatWithFallback(e,ai)}async formatValue(e,t){const[i]=await this.formatValues([{id:e,args:t}]);return i}handleEvent(){this.onChange()}onChange(e=!1){this.bundles=si.from(this.generateBundles(this.resourceIds)),e&&this.bundles.touchNext(2)}}function ai(e,t,i,n){return i.value?e.formatPattern(i.value,n,t):null}function oi(e,t,i,n){const s={value:null,attributes:null};i.value&&(s.value=e.formatPattern(i.value,n,t));let r=Object.keys(i.attributes);if(r.length>0){s.attributes=new Array(r.length);for(let[a,o]of r.entries()){let r=e.formatPattern(i.attributes[o],n,t);s.attributes[a]={name:o,value:r}}}return s}function li(e,t,i,n){const s=[],r=new Set;return i.forEach((({id:i,args:a},o)=>{if(void 0!==n[o])return;let l=t.getMessage(i);if(l){if(s.length=0,n[o]=e(t,s,l,a),s.length>0&&"undefined"!=typeof console){const e=t.locales[0],n=s.join(", ");console.warn(`[fluent][resolver] errors in ${e}/${i}: ${n}.`)}}else r.add(i)})),r}const hi="data-l10n-id",di="data-l10n-args",ci=`[${hi}]`;class ui extends ri{constructor(e,t){super(e,t),this.roots=new Set,this.pendingrAF=null,this.pendingElements=new Set,this.windowElement=null,this.mutationObserver=null,this.observerConfig={attributes:!0,characterData:!1,childList:!0,subtree:!0,attributeFilter:[hi,di]}}onChange(e=!1){super.onChange(e),this.roots&&this.translateRoots()}setAttributes(e,t,i){return e.setAttribute(hi,t),i?e.setAttribute(di,JSON.stringify(i)):e.removeAttribute(di),e}getAttributes(e){return{id:e.getAttribute(hi),args:JSON.parse(e.getAttribute(di)||null)}}connectRoot(e){for(const t of this.roots)if(t===e||t.contains(e)||e.contains(t))throw new Error("Cannot add a root that overlaps with existing root.");if(this.windowElement){if(this.windowElement!==e.ownerDocument.defaultView)throw new Error("Cannot connect a root:\n          DOMLocalization already has a root from a different window.")}else this.windowElement=e.ownerDocument.defaultView,this.mutationObserver=new this.windowElement.MutationObserver((e=>this.translateMutations(e)));this.roots.add(e),this.mutationObserver.observe(e,this.observerConfig)}disconnectRoot(e){return this.roots.delete(e),this.pauseObserving(),0===this.roots.size?(this.mutationObserver=null,this.windowElement&&this.pendingrAF&&this.windowElement.cancelAnimationFrame(this.pendingrAF),this.windowElement=null,this.pendingrAF=null,this.pendingElements.clear(),!0):(this.resumeObserving(),!1)}translateRoots(){const e=Array.from(this.roots);return Promise.all(e.map((e=>this.translateFragment(e))))}pauseObserving(){this.mutationObserver&&(this.translateMutations(this.mutationObserver.takeRecords()),this.mutationObserver.disconnect())}resumeObserving(){if(this.mutationObserver)for(const e of this.roots)this.mutationObserver.observe(e,this.observerConfig)}translateMutations(e){for(const t of e)switch(t.type){case"attributes":t.target.hasAttribute("data-l10n-id")&&this.pendingElements.add(t.target);break;case"childList":for(const e of t.addedNodes)if(e.nodeType===e.ELEMENT_NODE)if(e.childElementCount)for(const t of this.getTranslatables(e))this.pendingElements.add(t);else e.hasAttribute(hi)&&this.pendingElements.add(e)}this.pendingElements.size>0&&null===this.pendingrAF&&(this.pendingrAF=this.windowElement.requestAnimationFrame((()=>{this.translateElements(Array.from(this.pendingElements)),this.pendingElements.clear(),this.pendingrAF=null})))}translateFragment(e){return this.translateElements(this.getTranslatables(e))}async translateElements(e){if(!e.length)return;const t=e.map(this.getKeysForElement),i=await this.formatMessages(t);return this.applyTranslations(e,i)}applyTranslations(e,t){this.pauseObserving();for(let i=0;i<e.length;i++)void 0!==t[i]&&Gt(e[i],t[i]);this.resumeObserving()}getTranslatables(e){const t=Array.from(e.querySelectorAll(ci));return"function"==typeof e.hasAttribute&&e.hasAttribute(hi)&&t.push(e),t}getKeysForElement(e){return{id:e.getAttribute(hi),args:JSON.parse(e.getAttribute(di)||null)}}}class pi{#N;#F=new Set;#j;#O;constructor({lang:e,isRTL:t},i=null){this.#j=pi.#R(e),this.#O=i,this.#N=t??pi.#V(this.#j)?"rtl":"ltr"}_setL10n(e){this.#O=e}getLanguage(){return this.#j}getDirection(){return this.#N}async get(e,t=null,i){if(Array.isArray(e)){e=e.map((e=>({id:e})));return(await this.#O.formatMessages(e)).map((e=>e.value))}return(await this.#O.formatMessages([{id:e,args:t}]))?.[0].value||i}async translate(e){this.#F.add(e);try{this.#O.connectRoot(e),await this.#O.translateRoots()}catch{}}async destroy(){for(const e of this.#F)this.#O.disconnectRoot(e);this.#F.clear(),this.#O.pauseObserving()}pause(){this.#O.pauseObserving()}resume(){this.#O.resumeObserving()}static#R(e){return{en:"en-us",es:"es-es",fy:"fy-nl",ga:"ga-ie",gu:"gu-in",hi:"hi-in",hy:"hy-am",nb:"nb-no",ne:"ne-np",nn:"nn-no",pa:"pa-in",pt:"pt-pt",sv:"sv-se",zh:"zh-cn"}[e=e?.toLowerCase()||"en-us"]||e}static#V(e){const t=e.split("-",1)[0];return["ar","he","fa","ps","ur"].includes(t)}}function gi(e,t){const i=new Ht(t),n=new bt(e),s=n.addResource(i);return s.length&&console.error("L10n errors",s),n}class fi extends pi{constructor(e){super({lang:e});const t=e?fi.#B.bind(fi,"en-us",this.getLanguage()):fi.#$.bind(fi,this.getLanguage());this._setL10n(new ui([],t))}static async*#B(e,t){const{baseURL:i,paths:n}=await this.#H(),s=[t];if(e!==t){const i=t.split("-",1)[0];i!==t&&s.push(i),s.push(e)}for(const e of s){const t=await this.#z(e,i,n);t?yield t:"en-us"===e&&(yield this.#U(e))}}static async#z(e,t,i){const n=i[e];if(!n)return null;const s=new URL(n,t);return gi(e,await me(s,"text"))}static async#H(){try{const{href:e}=document.querySelector('link[type="application/l10n"]'),t=await me(e,"json");return{baseURL:e.replace(/[^/]*$/,"")||"./",paths:t}}catch{}return{baseURL:"./",paths:Object.create(null)}}static async*#$(e){yield this.#U(e)}static async#U(e){return gi(e,"pdfjs-previous-button =\n    .title = Previous Page\npdfjs-previous-button-label = Previous\npdfjs-next-button =\n    .title = Next Page\npdfjs-next-button-label = Next\npdfjs-page-input =\n    .title = Page\npdfjs-of-pages = of { $pagesCount }\npdfjs-page-of-pages = ({ $pageNumber } of { $pagesCount })\npdfjs-zoom-out-button =\n    .title = Zoom Out\npdfjs-zoom-out-button-label = Zoom Out\npdfjs-zoom-in-button =\n    .title = Zoom In\npdfjs-zoom-in-button-label = Zoom In\npdfjs-zoom-select =\n    .title = Zoom\npdfjs-presentation-mode-button =\n    .title = Switch to Presentation Mode\npdfjs-presentation-mode-button-label = Presentation Mode\npdfjs-open-file-button =\n    .title = Open File\npdfjs-open-file-button-label = Open\npdfjs-print-button =\n    .title = Print\npdfjs-print-button-label = Print\npdfjs-save-button =\n    .title = Save\npdfjs-save-button-label = Save\npdfjs-download-button =\n    .title = Download\npdfjs-download-button-label = Download\npdfjs-bookmark-button =\n    .title = Current Page (View URL from Current Page)\npdfjs-bookmark-button-label = Current Page\npdfjs-tools-button =\n    .title = Tools\npdfjs-tools-button-label = Tools\npdfjs-first-page-button =\n    .title = Go to First Page\npdfjs-first-page-button-label = Go to First Page\npdfjs-last-page-button =\n    .title = Go to Last Page\npdfjs-last-page-button-label = Go to Last Page\npdfjs-page-rotate-cw-button =\n    .title = Rotate Clockwise\npdfjs-page-rotate-cw-button-label = Rotate Clockwise\npdfjs-page-rotate-ccw-button =\n    .title = Rotate Counterclockwise\npdfjs-page-rotate-ccw-button-label = Rotate Counterclockwise\npdfjs-cursor-text-select-tool-button =\n    .title = Enable Text Selection Tool\npdfjs-cursor-text-select-tool-button-label = Text Selection Tool\npdfjs-cursor-hand-tool-button =\n    .title = Enable Hand Tool\npdfjs-cursor-hand-tool-button-label = Hand Tool\npdfjs-scroll-page-button =\n    .title = Use Page Scrolling\npdfjs-scroll-page-button-label = Page Scrolling\npdfjs-scroll-vertical-button =\n    .title = Use Vertical Scrolling\npdfjs-scroll-vertical-button-label = Vertical Scrolling\npdfjs-scroll-horizontal-button =\n    .title = Use Horizontal Scrolling\npdfjs-scroll-horizontal-button-label = Horizontal Scrolling\npdfjs-scroll-wrapped-button =\n    .title = Use Wrapped Scrolling\npdfjs-scroll-wrapped-button-label = Wrapped Scrolling\npdfjs-spread-none-button =\n    .title = Do not join page spreads\npdfjs-spread-none-button-label = No Spreads\npdfjs-spread-odd-button =\n    .title = Join page spreads starting with odd-numbered pages\npdfjs-spread-odd-button-label = Odd Spreads\npdfjs-spread-even-button =\n    .title = Join page spreads starting with even-numbered pages\npdfjs-spread-even-button-label = Even Spreads\npdfjs-document-properties-button =\n    .title = Document Properties…\npdfjs-document-properties-button-label = Document Properties…\npdfjs-document-properties-file-name = File name:\npdfjs-document-properties-file-size = File size:\npdfjs-document-properties-kb = { $size_kb } KB ({ $size_b } bytes)\npdfjs-document-properties-mb = { $size_mb } MB ({ $size_b } bytes)\npdfjs-document-properties-title = Title:\npdfjs-document-properties-author = Author:\npdfjs-document-properties-subject = Subject:\npdfjs-document-properties-keywords = Keywords:\npdfjs-document-properties-creation-date = Creation Date:\npdfjs-document-properties-modification-date = Modification Date:\npdfjs-document-properties-date-string = { $date }, { $time }\npdfjs-document-properties-creator = Creator:\npdfjs-document-properties-producer = PDF Producer:\npdfjs-document-properties-version = PDF Version:\npdfjs-document-properties-page-count = Page Count:\npdfjs-document-properties-page-size = Page Size:\npdfjs-document-properties-page-size-unit-inches = in\npdfjs-document-properties-page-size-unit-millimeters = mm\npdfjs-document-properties-page-size-orientation-portrait = portrait\npdfjs-document-properties-page-size-orientation-landscape = landscape\npdfjs-document-properties-page-size-name-a-three = A3\npdfjs-document-properties-page-size-name-a-four = A4\npdfjs-document-properties-page-size-name-letter = Letter\npdfjs-document-properties-page-size-name-legal = Legal\npdfjs-document-properties-page-size-dimension-string = { $width } × { $height } { $unit } ({ $orientation })\npdfjs-document-properties-page-size-dimension-name-string = { $width } × { $height } { $unit } ({ $name }, { $orientation })\npdfjs-document-properties-linearized = Fast Web View:\npdfjs-document-properties-linearized-yes = Yes\npdfjs-document-properties-linearized-no = No\npdfjs-document-properties-close-button = Close\npdfjs-print-progress-message = Preparing document for printing…\npdfjs-print-progress-percent = { $progress }%\npdfjs-print-progress-close-button = Cancel\npdfjs-printing-not-supported = Warning: Printing is not fully supported by this browser.\npdfjs-printing-not-ready = Warning: The PDF is not fully loaded for printing.\npdfjs-toggle-sidebar-button =\n    .title = Toggle Sidebar\npdfjs-toggle-sidebar-notification-button =\n    .title = Toggle Sidebar (document contains outline/attachments/layers)\npdfjs-toggle-sidebar-button-label = Toggle Sidebar\npdfjs-document-outline-button =\n    .title = Show Document Outline (double-click to expand/collapse all items)\npdfjs-document-outline-button-label = Document Outline\npdfjs-attachments-button =\n    .title = Show Attachments\npdfjs-attachments-button-label = Attachments\npdfjs-layers-button =\n    .title = Show Layers (double-click to reset all layers to the default state)\npdfjs-layers-button-label = Layers\npdfjs-thumbs-button =\n    .title = Show Thumbnails\npdfjs-thumbs-button-label = Thumbnails\npdfjs-current-outline-item-button =\n    .title = Find Current Outline Item\npdfjs-current-outline-item-button-label = Current Outline Item\npdfjs-findbar-button =\n    .title = Find in Document\npdfjs-findbar-button-label = Find\npdfjs-additional-layers = Additional Layers\npdfjs-thumb-page-title =\n    .title = Page { $page }\npdfjs-thumb-page-canvas =\n    .aria-label = Thumbnail of Page { $page }\npdfjs-find-input =\n    .title = Find\n    .placeholder = Find in document…\npdfjs-find-previous-button =\n    .title = Find the previous occurrence of the phrase\npdfjs-find-previous-button-label = Previous\npdfjs-find-next-button =\n    .title = Find the next occurrence of the phrase\npdfjs-find-next-button-label = Next\npdfjs-find-highlight-checkbox = Highlight All\npdfjs-find-match-case-checkbox-label = Match Case\npdfjs-find-match-diacritics-checkbox-label = Match Diacritics\npdfjs-find-entire-word-checkbox-label = Whole Words\npdfjs-find-reached-top = Reached top of document, continued from bottom\npdfjs-find-reached-bottom = Reached end of document, continued from top\npdfjs-find-match-count =\n    { $total ->\n        [one] { $current } of { $total } match\n       *[other] { $current } of { $total } matches\n    }\npdfjs-find-match-count-limit =\n    { $limit ->\n        [one] More than { $limit } match\n       *[other] More than { $limit } matches\n    }\npdfjs-find-not-found = Phrase not found\npdfjs-page-scale-width = Page Width\npdfjs-page-scale-fit = Page Fit\npdfjs-page-scale-auto = Automatic Zoom\npdfjs-page-scale-actual = Actual Size\npdfjs-page-scale-percent = { $scale }%\npdfjs-page-landmark =\n    .aria-label = Page { $page }\npdfjs-loading-error = An error occurred while loading the PDF.\npdfjs-invalid-file-error = Invalid or corrupted PDF file.\npdfjs-missing-file-error = Missing PDF file.\npdfjs-unexpected-response-error = Unexpected server response.\npdfjs-rendering-error = An error occurred while rendering the page.\npdfjs-annotation-date-string = { $date }, { $time }\npdfjs-text-annotation-type =\n    .alt = [{ $type } Annotation]\npdfjs-password-label = Enter the password to open this PDF file.\npdfjs-password-invalid = Invalid password. Please try again.\npdfjs-password-ok-button = OK\npdfjs-password-cancel-button = Cancel\npdfjs-web-fonts-disabled = Web fonts are disabled: unable to use embedded PDF fonts.\npdfjs-editor-free-text-button =\n    .title = Text\npdfjs-editor-free-text-button-label = Text\npdfjs-editor-ink-button =\n    .title = Draw\npdfjs-editor-ink-button-label = Draw\npdfjs-editor-stamp-button =\n    .title = Add or edit images\npdfjs-editor-stamp-button-label = Add or edit images\npdfjs-editor-highlight-button =\n    .title = Highlight\npdfjs-editor-highlight-button-label = Highlight\npdfjs-highlight-floating-button1 =\n    .title = Highlight\n    .aria-label = Highlight\npdfjs-highlight-floating-button-label = Highlight\npdfjs-editor-remove-ink-button =\n    .title = Remove drawing\npdfjs-editor-remove-freetext-button =\n    .title = Remove text\npdfjs-editor-remove-stamp-button =\n    .title = Remove image\npdfjs-editor-remove-highlight-button =\n    .title = Remove highlight\npdfjs-editor-free-text-color-input = Color\npdfjs-editor-free-text-size-input = Size\npdfjs-editor-ink-color-input = Color\npdfjs-editor-ink-thickness-input = Thickness\npdfjs-editor-ink-opacity-input = Opacity\npdfjs-editor-stamp-add-image-button =\n    .title = Add image\npdfjs-editor-stamp-add-image-button-label = Add image\npdfjs-editor-free-highlight-thickness-input = Thickness\npdfjs-editor-free-highlight-thickness-title =\n    .title = Change thickness when highlighting items other than text\npdfjs-free-text =\n    .aria-label = Text Editor\npdfjs-free-text-default-content = Start typing…\npdfjs-ink =\n    .aria-label = Draw Editor\npdfjs-ink-canvas =\n    .aria-label = User-created image\npdfjs-editor-alt-text-button-label = Alt text\npdfjs-editor-alt-text-edit-button-label = Edit alt text\npdfjs-editor-alt-text-dialog-label = Choose an option\npdfjs-editor-alt-text-dialog-description = Alt text (alternative text) helps when people can’t see the image or when it doesn’t load.\npdfjs-editor-alt-text-add-description-label = Add a description\npdfjs-editor-alt-text-add-description-description = Aim for 1-2 sentences that describe the subject, setting, or actions.\npdfjs-editor-alt-text-mark-decorative-label = Mark as decorative\npdfjs-editor-alt-text-mark-decorative-description = This is used for ornamental images, like borders or watermarks.\npdfjs-editor-alt-text-cancel-button = Cancel\npdfjs-editor-alt-text-save-button = Save\npdfjs-editor-alt-text-decorative-tooltip = Marked as decorative\npdfjs-editor-alt-text-textarea =\n    .placeholder = For example, “A young man sits down at a table to eat a meal”\npdfjs-editor-resizer-label-top-left = Top left corner — resize\npdfjs-editor-resizer-label-top-middle = Top middle — resize\npdfjs-editor-resizer-label-top-right = Top right corner — resize\npdfjs-editor-resizer-label-middle-right = Middle right — resize\npdfjs-editor-resizer-label-bottom-right = Bottom right corner — resize\npdfjs-editor-resizer-label-bottom-middle = Bottom middle — resize\npdfjs-editor-resizer-label-bottom-left = Bottom left corner — resize\npdfjs-editor-resizer-label-middle-left = Middle left — resize\npdfjs-editor-highlight-colorpicker-label = Highlight color\npdfjs-editor-colorpicker-button =\n    .title = Change color\npdfjs-editor-colorpicker-dropdown =\n    .aria-label = Color choices\npdfjs-editor-colorpicker-yellow =\n    .title = Yellow\npdfjs-editor-colorpicker-green =\n    .title = Green\npdfjs-editor-colorpicker-blue =\n    .title = Blue\npdfjs-editor-colorpicker-pink =\n    .title = Pink\npdfjs-editor-colorpicker-red =\n    .title = Red\npdfjs-editor-highlight-show-all-button-label = Show all\npdfjs-editor-highlight-show-all-button =\n    .title = Show all")}}function mi(){return document.location.hash}class bi{#I=null;constructor({linkService:e,eventBus:t}){this.linkService=e,this.eventBus=t,this._initialized=!1,this._fingerprint="",this.reset(),this.eventBus._on("pagesinit",(()=>{this._isPagesLoaded=!1,this.eventBus._on("pagesloaded",(e=>{this._isPagesLoaded=!!e.pagesCount}),{once:!0})}))}initialize({fingerprint:e,resetHistory:t=!1,updateUrl:i=!1}){if(!e||"string"!=typeof e)return void console.error('PDFHistory.initialize: The "fingerprint" must be a non-empty string.');this._initialized&&this.reset();const n=""!==this._fingerprint&&this._fingerprint!==e;this._fingerprint=e,this._updateUrl=!0===i,this._initialized=!0,this.#W();const s=window.history.state;if(this._popStateInProgress=!1,this._blockHashChange=0,this._currentHash=mi(),this._numPositionUpdates=0,this._uid=this._maxUid=0,this._destination=null,this._position=null,!this.#Z(s,!0)||t){const{hash:e,page:i,rotation:s}=this.#G(!0);return!e||n||t?void this.#X(null,!0):void this.#X({hash:e,page:i,rotation:s},!0)}const r=s.destination;this.#Q(r,s.uid,!0),void 0!==r.rotation&&(this._initialRotation=r.rotation),r.dest?(this._initialBookmark=JSON.stringify(r.dest),this._destination.page=null):r.hash?this._initialBookmark=r.hash:r.page&&(this._initialBookmark=`page=${r.page}`)}reset(){this._initialized&&(this.#J(),this._initialized=!1,this.#q()),this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),this._initialBookmark=null,this._initialRotation=null}push({namedDest:e=null,explicitDest:t,pageNumber:i}){if(!this._initialized)return;if(e&&"string"!=typeof e)return void console.error(`PDFHistory.push: "${e}" is not a valid namedDest parameter.`);if(!Array.isArray(t))return void console.error(`PDFHistory.push: "${t}" is not a valid explicitDest parameter.`);if(!this.#K(i)&&(null!==i||this._destination))return void console.error(`PDFHistory.push: "${i}" is not a valid pageNumber parameter.`);const n=e||JSON.stringify(t);if(!n)return;let s=!1;if(this._destination&&(function(e,t){if("string"!=typeof e||"string"!=typeof t)return!1;if(e===t)return!0;if(b(e).get("nameddest")===t)return!0;return!1}(this._destination.hash,n)||function(e,t){function i(e,t){if(typeof e!=typeof t)return!1;if(Array.isArray(e)||Array.isArray(t))return!1;if(null!==e&&"object"==typeof e&&null!==t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!i(e[n],t[n]))return!1;return!0}return e===t||Number.isNaN(e)&&Number.isNaN(t)}if(!Array.isArray(e)||!Array.isArray(t))return!1;if(e.length!==t.length)return!1;for(let n=0,s=e.length;n<s;n++)if(!i(e[n],t[n]))return!1;return!0}(this._destination.dest,t))){if(this._destination.page)return;s=!0}this._popStateInProgress&&!s||(this.#X({dest:t,hash:n,page:i,rotation:this.linkService.rotation},s),this._popStateInProgress||(this._popStateInProgress=!0,Promise.resolve().then((()=>{this._popStateInProgress=!1}))))}pushPage(e){this._initialized&&(this.#K(e)?this._destination?.page!==e&&(this._popStateInProgress||(this.#X({dest:null,hash:`page=${e}`,page:e,rotation:this.linkService.rotation}),this._popStateInProgress||(this._popStateInProgress=!0,Promise.resolve().then((()=>{this._popStateInProgress=!1}))))):console.error(`PDFHistory.pushPage: "${e}" is not a valid page number.`))}pushCurrentPosition(){this._initialized&&!this._popStateInProgress&&this.#Y()}back(){if(!this._initialized||this._popStateInProgress)return;const e=window.history.state;this.#Z(e)&&e.uid>0&&window.history.back()}forward(){if(!this._initialized||this._popStateInProgress)return;const e=window.history.state;this.#Z(e)&&e.uid<this._maxUid&&window.history.forward()}get popStateInProgress(){return this._initialized&&(this._popStateInProgress||this._blockHashChange>0)}get initialBookmark(){return this._initialized?this._initialBookmark:null}get initialRotation(){return this._initialized?this._initialRotation:null}#X(e,t=!1){const i=t||!this._destination,n={fingerprint:this._fingerprint,uid:i?this._uid:this._uid+1,destination:e};let s;if(this.#Q(e,n.uid),this._updateUrl&&e?.hash){const t=document.location.href.split("#",1)[0];t.startsWith("file://")||(s=`${t}#${e.hash}`)}i?window.history.replaceState(n,"",s):window.history.pushState(n,"",s)}#Y(e=!1){if(!this._position)return;let t=this._position;if(e&&(t=Object.assign(Object.create(null),this._position),t.temporary=!0),!this._destination)return void this.#X(t);if(this._destination.temporary)return void this.#X(t,!0);if(this._destination.hash===t.hash)return;if(!this._destination.page&&this._numPositionUpdates<=50)return;let i=!1;if(this._destination.page>=t.first&&this._destination.page<=t.page){if(void 0!==this._destination.dest||!this._destination.first)return;i=!0}this.#X(t,i)}#K(e){return Number.isInteger(e)&&e>0&&e<=this.linkService.pagesCount}#Z(e,t=!1){if(!e)return!1;if(e.fingerprint!==this._fingerprint){if(!t)return!1;{if("string"!=typeof e.fingerprint||e.fingerprint.length!==this._fingerprint.length)return!1;const[t]=performance.getEntriesByType("navigation");if("reload"!==t?.type)return!1}}return!(!Number.isInteger(e.uid)||e.uid<0)&&(null!==e.destination&&"object"==typeof e.destination)}#Q(e,t,i=!1){this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),i&&e?.temporary&&delete e.temporary,this._destination=e,this._uid=t,this._maxUid=Math.max(this._maxUid,t),this._numPositionUpdates=0}#G(e=!1){const t=unescape(mi()).substring(1),i=b(t),n=i.get("nameddest")||"";let s=0|i.get("page");return(!this.#K(s)||e&&n.length>0)&&(s=null),{hash:t,page:s,rotation:this.linkService.rotation}}#ee({location:e}){this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),this._position={hash:e.pdfOpenParams.substring(1),page:this.linkService.page,first:e.pageNumber,rotation:e.rotation},this._popStateInProgress||(this._isPagesLoaded&&this._destination&&!this._destination.page&&this._numPositionUpdates++,this._updateViewareaTimeout=setTimeout((()=>{this._popStateInProgress||this.#Y(!0),this._updateViewareaTimeout=null}),1e3))}#te({state:e}){const t=mi(),i=this._currentHash!==t;if(this._currentHash=t,!e){this._uid++;const{hash:e,page:t,rotation:i}=this.#G();return void this.#X({hash:e,page:t,rotation:i},!0)}if(!this.#Z(e))return;this._popStateInProgress=!0,i&&(this._blockHashChange++,async function({target:e,name:t,delay:i=0}){if("object"!=typeof e||!t||"string"!=typeof t||!(Number.isInteger(i)&&i>=0))throw new Error("waitOnEventOrTimeout - invalid parameters.");const{promise:n,resolve:s}=Promise.withResolvers(),r=new AbortController;function a(e){r.abort(),clearTimeout(o),s(e)}e[e instanceof Ye?"_on":"addEventListener"](t,a.bind(null,qe),{signal:r.signal});const o=setTimeout(a.bind(null,Ke),i);return n}({target:window,name:"hashchange",delay:1e3}).then((()=>{this._blockHashChange--})));const n=e.destination;this.#Q(n,e.uid,!0),x(n.rotation)&&(this.linkService.rotation=n.rotation),n.dest?this.linkService.goToDestination(n.dest):n.hash?this.linkService.setHash(n.hash):n.page&&(this.linkService.page=n.page),Promise.resolve().then((()=>{this._popStateInProgress=!1}))}#J(){this._destination&&!this._destination.temporary||this.#Y()}#W(){if(this.#I)return;this.#I=new AbortController;const{signal:e}=this.#I;this.eventBus._on("updateviewarea",this.#ee.bind(this),{signal:e}),window.addEventListener("popstate",this.#te.bind(this),{signal:e}),window.addEventListener("pagehide",this.#J.bind(this),{signal:e})}#q(){this.#I?.abort(),this.#I=null}}class yi{#ie=null;#ne=null;#A=null;#se=null;#re;constructor(e){this.pdfPage=e.pdfPage,this.accessibilityManager=e.accessibilityManager,this.l10n=e.l10n,this.l10n||=new fi,this.annotationEditorLayer=null,this.div=null,this._cancelled=!1,this.#re=e.uiManager,this.#ie=e.annotationLayer||null,this.#se=e.textLayer||null,this.#ne=e.drawLayer||null,this.#A=e.onAppend||null}async render(e,t="display"){if("display"!==t)return;if(this._cancelled)return;const i=e.clone({dontFlip:!0});if(this.div)return this.annotationEditorLayer.update({viewport:i}),void this.show();const n=this.div=document.createElement("div");n.className="annotationEditorLayer",n.hidden=!0,n.dir=this.#re.direction,this.#A?.(n),this.annotationEditorLayer=new ne({uiManager:this.#re,div:n,accessibilityManager:this.accessibilityManager,pageIndex:this.pdfPage.pageNumber-1,l10n:this.l10n,viewport:i,annotationLayer:this.#ie,textLayer:this.#se,drawLayer:this.#ne});const s={viewport:i,div:n,annotations:null,intent:t};this.annotationEditorLayer.render(s),this.show()}cancel(){this._cancelled=!0,this.div&&this.annotationEditorLayer.destroy()}hide(){this.div&&(this.div.hidden=!0)}show(){this.div&&!this.annotationEditorLayer.isInvisible&&(this.div.hidden=!1)}}{var vi=Object.create(null);const e=navigator.userAgent||"",t=navigator.platform||"",i=navigator.maxTouchPoints||1,n=/Android/.test(e);(/\b(iPad|iPhone|iPod)(?=;)/.test(e)||"MacIntel"===t&&i>1||n)&&(vi.maxCanvasPixels=5242880)}const wi=1,Pi=2,_i=4,xi=8,Si=128,Ci={canvasMaxAreaInBytes:{value:-1,kind:wi+_i},isInAutomation:{value:!1,kind:wi},supportsCaretBrowsingMode:{value:!1,kind:wi},supportsDocumentFonts:{value:!0,kind:wi},supportsIntegratedFind:{value:!1,kind:wi},supportsMouseWheelZoomCtrlKey:{value:!0,kind:wi},supportsMouseWheelZoomMetaKey:{value:!0,kind:wi},supportsPinchToZoom:{value:!0,kind:wi},annotationEditorMode:{value:0,kind:Pi+Si},annotationMode:{value:2,kind:Pi+Si},cursorToolOnLoad:{value:0,kind:Pi+Si},debuggerSrc:{value:"./debugger.mjs",kind:Pi},defaultZoomDelay:{value:400,kind:Pi+Si},defaultZoomValue:{value:"",kind:Pi+Si},disableHistory:{value:!1,kind:Pi},disablePageLabels:{value:!1,kind:Pi+Si},enableHighlightEditor:{value:!1,kind:Pi+Si},enableHighlightFloatingButton:{value:!1,kind:Pi+Si},enableML:{value:!1,kind:Pi+Si},enablePermissions:{value:!1,kind:Pi+Si},enablePrintAutoRotate:{value:!0,kind:Pi+Si},enableScripting:{value:!0,kind:Pi+Si},enableStampEditor:{value:!0,kind:Pi+Si},externalLinkRel:{value:"noopener noreferrer nofollow",kind:Pi},externalLinkTarget:{value:0,kind:Pi+Si},highlightEditorColors:{value:"yellow=#FFFF98,green=#53FFBC,blue=#80EBFF,pink=#FFCBE6,red=#FF4F5F",kind:Pi+Si},historyUpdateUrl:{value:!1,kind:Pi+Si},ignoreDestinationZoom:{value:!1,kind:Pi+Si},imageResourcesPath:{value:"./images/",kind:Pi},maxCanvasPixels:{value:2**25,kind:Pi},forcePageColors:{value:!1,kind:Pi+Si},pageColorsBackground:{value:"Canvas",kind:Pi+Si},pageColorsForeground:{value:"CanvasText",kind:Pi+Si},pdfBugEnabled:{value:!1,kind:Pi+Si},printResolution:{value:150,kind:Pi},sidebarViewOnLoad:{value:-1,kind:Pi+Si},scrollModeOnLoad:{value:-1,kind:Pi+Si},spreadModeOnLoad:{value:-1,kind:Pi+Si},textLayerMode:{value:1,kind:Pi+Si},viewOnLoad:{value:0,kind:Pi+Si},cMapPacked:{value:!0,kind:_i},cMapUrl:{value:"../web/cmaps/",kind:_i},disableAutoFetch:{value:!1,kind:_i+Si},disableFontFace:{value:!1,kind:_i+Si},disableRange:{value:!1,kind:_i+Si},disableStream:{value:!1,kind:_i+Si},docBaseUrl:{value:"",kind:_i},enableHWA:{value:!0,kind:_i+Pi+Si},enableXfa:{value:!0,kind:_i+Si},fontExtraProperties:{value:!1,kind:_i},isEvalSupported:{value:!0,kind:_i},isOffscreenCanvasSupported:{value:!0,kind:_i},maxImageSize:{value:-1,kind:_i},pdfBug:{value:!1,kind:_i},standardFontDataUrl:{value:"../web/standard_fonts/",kind:_i},verbosity:{value:1,kind:_i},workerPort:{value:null,kind:xi},workerSrc:{value:"../build/pdf.worker.mjs",kind:xi}};Ci.defaultUrl={value:"compressed.tracemonkey-pldi-09.pdf",kind:Pi},Ci.sandboxBundleSrc={value:"../build/pdf.sandbox.mjs",kind:Pi},Ci.viewerCssTheme={value:0,kind:Pi+Si},Ci.disablePreferences={value:!1,kind:Pi},Ci.locale={value:navigator.language||"en-US",kind:Pi};const Li=Object.create(null);for(const e in vi)Li[e]=vi[e];class Mi{constructor(){throw new Error("Cannot initialize AppOptions.")}static get(e){return Li[e]??Ci[e]?.value??void 0}static getAll(e=null,t=!1){const i=Object.create(null);for(const n in Ci){const s=Ci[n];(!e||e&s.kind)&&(i[n]=t?s.value:Li[n]??s.value)}return i}static set(e,t){Li[e]=t}static setAll(e,t=!1){if(t){if(this.get("disablePreferences"))return;for(const e in Li)if(void 0===vi[e]){console.warn('setAll: The Preferences may override manually set AppOptions; please use the "disablePreferences"-option in order to prevent that.');break}}for(const t in e)Li[t]=e[t]}static remove(e){delete Li[e];const t=vi[e];void 0!==t&&(Li[e]=t)}}class Ei{#ne=null;constructor(e){this.pageIndex=e.pageIndex}async render(e="display"){"display"!==e||this.#ne||this._cancelled||(this.#ne=new ge({pageIndex:this.pageIndex}))}cancel(){this._cancelled=!0,this.#ne&&(this.#ne.destroy(),this.#ne=null)}setParent(e){this.#ne?.setParent(e)}getDrawLayer(){return this.#ne}}const Ai={Document:null,DocumentFragment:null,Part:"group",Sect:"group",Div:"group",Aside:"note",NonStruct:"none",P:null,H:"heading",Title:null,FENote:"note",Sub:"group",Lbl:null,Span:null,Em:null,Strong:null,Link:"link",Annot:"note",Form:"form",Ruby:null,RB:null,RT:null,RP:null,Warichu:null,WT:null,WP:null,L:"list",LI:"listitem",LBody:null,Table:"table",TR:"row",TH:"columnheader",TD:"cell",THead:"columnheader",TBody:null,TFoot:null,Caption:null,Figure:"figure",Formula:null,Artifact:null},Ii=/^H(\d+)$/;class ki{#ae=void 0;get renderingDone(){return void 0!==this.#ae}render(e){if(void 0!==this.#ae)return this.#ae;const t=this.#oe(e);return t?.classList.add("structTree"),this.#ae=t}hide(){this.#ae&&!this.#ae.hidden&&(this.#ae.hidden=!0)}show(){this.#ae?.hidden&&(this.#ae.hidden=!1)}#le(e,t){const{alt:i,id:n,lang:s}=e;void 0!==i&&t.setAttribute("aria-label",v(i)),void 0!==n&&t.setAttribute("aria-owns",n),void 0!==s&&t.setAttribute("lang",v(s,!0))}#oe(e){if(!e)return null;const t=document.createElement("span");if("role"in e){const{role:i}=e,n=i.match(Ii);n?(t.setAttribute("role","heading"),t.setAttribute("aria-level",n[1])):Ai[i]&&t.setAttribute("role",Ai[i])}if(this.#le(e,t),e.children)if(1===e.children.length&&"id"in e.children[0])this.#le(e.children[0],t);else for(const i of e.children)t.append(this.#oe(i));return t}}class Ti{#he=!1;#de=null;#ce=new Map;#ue=new Map;setTextMapping(e){this.#de=e}static#pe(e,t){const i=e.getBoundingClientRect(),n=t.getBoundingClientRect();if(0===i.width&&0===i.height)return 1;if(0===n.width&&0===n.height)return-1;const s=i.y,r=i.y+i.height,a=i.y+i.height/2,o=n.y,l=n.y+n.height,h=n.y+n.height/2;if(a<=o&&h>=r)return-1;if(h<=s&&a>=l)return 1;return i.x+i.width/2-(n.x+n.width/2)}enable(){if(this.#he)throw new Error("TextAccessibilityManager is already enabled.");if(!this.#de)throw new Error("Text divs and strings have not been set.");if(this.#he=!0,this.#de=this.#de.slice(),this.#de.sort(Ti.#pe),this.#ce.size>0){const e=this.#de;for(const[t,i]of this.#ce){document.getElementById(t)?this.#ge(t,e[i]):this.#ce.delete(t)}}for(const[e,t]of this.#ue)this.addPointerInTextLayer(e,t);this.#ue.clear()}disable(){this.#he&&(this.#ue.clear(),this.#de=null,this.#he=!1)}removePointerInTextLayer(e){if(!this.#he)return void this.#ue.delete(e);const t=this.#de;if(!t||0===t.length)return;const{id:i}=e,n=this.#ce.get(i);if(void 0===n)return;const s=t[n];this.#ce.delete(i);let r=s.getAttribute("aria-owns");r?.includes(i)&&(r=r.split(" ").filter((e=>e!==i)).join(" "),r?s.setAttribute("aria-owns",r):(s.removeAttribute("aria-owns"),s.setAttribute("role","presentation")))}#ge(e,t){const i=t.getAttribute("aria-owns");i?.includes(e)||t.setAttribute("aria-owns",i?`${i} ${e}`:e),t.removeAttribute("role")}addPointerInTextLayer(e,t){const{id:i}=e;if(!i)return null;if(!this.#he)return this.#ue.set(e,t),null;t&&this.removePointerInTextLayer(e);const n=this.#de;if(!n||0===n.length)return null;const s=w(n,(t=>Ti.#pe(e,t)<0)),r=Math.max(0,s-1),a=n[r];this.#ge(i,a),this.#ce.set(i,r);const o=a.parentNode;return o?.classList.contains("markedContent")?o.id:null}moveElementInDOM(e,t,i,n){const s=this.addPointerInTextLayer(i,n);if(!e.hasChildNodes())return e.append(t),s;const r=Array.from(e.childNodes).filter((e=>e!==t));if(0===r.length)return s;const a=i||t,o=w(r,(e=>Ti.#pe(a,e)<0));return 0===o?r[0].before(t):r[o-1].after(t),s}}class Di{#I=null;constructor({findController:e,eventBus:t,pageIndex:i}){this.findController=e,this.matches=[],this.eventBus=t,this.pageIdx=i,this.textDivs=null,this.textContentItemsStr=null,this.enabled=!1}setTextMapping(e,t){this.textDivs=e,this.textContentItemsStr=t}enable(){if(!this.textDivs||!this.textContentItemsStr)throw new Error("Text divs and strings have not been set.");if(this.enabled)throw new Error("TextHighlighter is already enabled.");this.enabled=!0,this.#I||(this.#I=new AbortController,this.eventBus._on("updatetextlayermatches",(e=>{e.pageIndex!==this.pageIdx&&-1!==e.pageIndex||this._updateMatches()}),{signal:this.#I.signal})),this._updateMatches()}disable(){this.enabled&&(this.enabled=!1,this.#I?.abort(),this.#I=null,this._updateMatches(!0))}_convertMatches(e,t){if(!e)return[];const{textContentItemsStr:i}=this;let n=0,s=0;const r=i.length-1,a=[];for(let o=0,l=e.length;o<l;o++){let l=e[o];for(;n!==r&&l>=s+i[n].length;)s+=i[n].length,n++;n===i.length&&console.error("Could not find a matching mapping");const h={begin:{divIdx:n,offset:l-s}};for(l+=t[o];n!==r&&l>s+i[n].length;)s+=i[n].length,n++;h.end={divIdx:n,offset:l-s},a.push(h)}return a}_renderMatches(e){if(0===e.length)return;const{findController:t,pageIdx:i}=this,{textContentItemsStr:n,textDivs:s}=this,r=i===t.selected.pageIdx,a=t.selected.matchIdx;let o=null;const l={divIdx:-1,offset:void 0};function h(e,t){const i=e.divIdx;return s[i].textContent="",d(i,0,e.offset,t)}function d(e,t,i,r){let a=s[e];if(a.nodeType===Node.TEXT_NODE){const t=document.createElement("span");a.before(t),t.append(a),s[e]=t,a=t}const o=n[e].substring(t,i),l=document.createTextNode(o);if(r){const e=document.createElement("span");return e.className=`${r} appended`,e.append(l),a.append(e),r.includes("selected")?e.offsetLeft:0}return a.append(l),0}let c=a,u=c+1;if(t.state.highlightAll)c=0,u=e.length;else if(!r)return;let p=-1,g=-1;for(let n=c;n<u;n++){const c=e[n],u=c.begin;if(u.divIdx===p&&u.offset===g)continue;p=u.divIdx,g=u.offset;const f=c.end,m=r&&n===a,b=m?" selected":"";let y=0;if(o&&u.divIdx===o.divIdx?d(o.divIdx,o.offset,u.offset):(null!==o&&d(o.divIdx,o.offset,l.offset),h(u)),u.divIdx===f.divIdx)y=d(u.divIdx,u.offset,f.offset,"highlight"+b);else{y=d(u.divIdx,u.offset,l.offset,"highlight begin"+b);for(let e=u.divIdx+1,t=f.divIdx;e<t;e++)s[e].className="highlight middle"+b;h(f,"highlight end"+b)}o=f,m&&t.scrollMatchIntoView({element:s[u.divIdx],selectedLeft:y,pageIndex:i,matchIndex:a})}o&&d(o.divIdx,o.offset,l.offset)}_updateMatches(e=!1){if(!this.enabled&&!e)return;const{findController:t,matches:i,pageIdx:n}=this,{textContentItemsStr:s,textDivs:r}=this;let a=-1;for(const e of i){for(let t=Math.max(a,e.begin.divIdx),i=e.end.divIdx;t<=i;t++){const e=r[t];e.textContent=s[t],e.className=""}a=e.end.divIdx+1}if(!t?.highlightMatches||e)return;const o=t.pageMatches[n]||null,l=t.pageMatchesLength[n]||null;this.matches=this._convertMatches(o,l),this._renderMatches(this.matches)}}class Ni{#fe=!1;#A=null;#me=!1;#se=null;static#be=new Map;static#ye=null;constructor({pdfPage:e,highlighter:t=null,accessibilityManager:i=null,enablePermissions:n=!1,onAppend:s=null}){this.pdfPage=e,this.highlighter=t,this.accessibilityManager=i,this.#fe=!0===n,this.#A=s,this.div=document.createElement("div"),this.div.tabIndex=0,this.div.className="textLayer"}async render(e,t=null){if(this.#me&&this.#se)return this.#se.update({viewport:e,onBefore:this.hide.bind(this)}),void this.show();this.cancel(),this.#se=new $e({textContentSource:this.pdfPage.streamTextContent(t||{includeMarkedContent:!0,disableNormalization:!0}),container:this.div,viewport:e});const{textDivs:i,textContentItemsStr:n}=this.#se;this.highlighter?.setTextMapping(i,n),this.accessibilityManager?.setTextMapping(i),await this.#se.render(),this.#me=!0;const s=document.createElement("div");s.className="endOfContent",this.div.append(s),this.#ve(s),this.#A?.(this.div),this.highlighter?.enable(),this.accessibilityManager?.enable()}hide(){!this.div.hidden&&this.#me&&(this.highlighter?.disable(),this.div.hidden=!0)}show(){this.div.hidden&&this.#me&&(this.div.hidden=!1,this.highlighter?.enable())}cancel(){this.#se?.cancel(),this.#se=null,this.highlighter?.disable(),this.accessibilityManager?.disable(),Ni.#we(this.div)}#ve(e){const{div:t}=this;t.addEventListener("mousedown",(t=>{e.classList.add("active")})),t.addEventListener("copy",(e=>{if(!this.#fe){const t=document.getSelection();e.clipboardData.setData("text/plain",v(Ee(t.toString())))}e.preventDefault(),e.stopPropagation()})),Ni.#be.set(t,e),Ni.#Pe()}static#we(e){this.#be.delete(e),0===this.#be.size&&(this.#ye?.abort(),this.#ye=null)}static#Pe(){if(this.#ye)return;this.#ye=new AbortController;const{signal:e}=this.#ye,t=(e,t)=>{t.append(e),e.style.width="",e.style.height="",e.classList.remove("active")};var i,n;document.addEventListener("pointerup",(()=>{this.#be.forEach(t)}),{signal:e}),document.addEventListener("selectionchange",(()=>{const e=document.getSelection();if(0===e.rangeCount)return void this.#be.forEach(t);const s=new Set;for(let t=0;t<e.rangeCount;t++){const i=e.getRangeAt(t);for(const e of this.#be.keys())!s.has(e)&&i.intersectsNode(e)&&s.add(e)}for(const[e,i]of this.#be)s.has(e)?i.classList.add("active"):t(i,e);if(i??="none"===getComputedStyle(this.#be.values().next().value).getPropertyValue("-moz-user-select"),i)return;const r=e.getRangeAt(0),a=n&&(0===r.compareBoundaryPoints(Range.END_TO_END,n)||0===r.compareBoundaryPoints(Range.START_TO_END,n));let o=a?r.startContainer:r.endContainer;o.nodeType===Node.TEXT_NODE&&(o=o.parentNode);const l=o.parentElement.closest(".textLayer"),h=this.#be.get(l);h&&(h.style.width=l.style.width,h.style.height=l.style.height,o.parentElement.insertBefore(h,a?o:o.nextSibling)),n=r.cloneRange()}),{signal:e})}}class Fi{constructor({pdfPage:e,annotationStorage:t=null,linkService:i,xfaHtml:n=null}){this.pdfPage=e,this.annotationStorage=t,this.linkService=i,this.xfaHtml=n,this.div=null,this._cancelled=!1}async render(e,t="display"){if("print"===t){const i={viewport:e.clone({dontFlip:!0}),div:this.div,xfaHtml:this.xfaHtml,annotationStorage:this.annotationStorage,linkService:this.linkService,intent:t};return this.div=document.createElement("div"),i.div=this.div,Ge.render(i)}const i=await this.pdfPage.getXfa();if(this._cancelled||!i)return{textDivs:[]};const n={viewport:e.clone({dontFlip:!0}),div:this.div,xfaHtml:i,annotationStorage:this.annotationStorage,linkService:this.linkService,intent:t};return this.div?Ge.update(n):(this.div=document.createElement("div"),n.div=this.div,Ge.render(n))}cancel(){this._cancelled=!0}hide(){this.div&&(this.div.hidden=!0)}}const ji={annotationEditorUIManager:null,annotationStorage:null,downloadManager:null,enableScripting:!1,fieldObjectsPromise:null,findController:null,hasJSActionsPromise:null,get linkService(){return new te}},Oi=new Map([["canvasWrapper",0],["textLayer",1],["annotationLayer",2],["annotationEditorLayer",3],["xfaLayer",3]]);class Ri{#_e=le.ENABLE_FORMS;#xe=!1;#Se=!1;#Ce=null;#Le=null;#Me=null;#Ee=null;#Ae=r.INITIAL;#Ie=c;#ke={directDrawing:!0,initialOptionalContent:!0,regularAnnotations:!0};#Te=new WeakMap;#De=[null,null,null,null];constructor(e){const t=e.container,i=e.defaultViewport;this.id=e.id,this.renderingId="page"+this.id,this.#Ce=e.layerProperties||ji,this.pdfPage=null,this.pageLabel=null,this.rotation=0,this.scale=e.scale||1,this.viewport=i,this.pdfPageRotate=i.rotation,this._optionalContentConfigPromise=e.optionalContentConfigPromise||null,this.#Ie=e.textLayerMode??c,this.#_e=e.annotationMode??le.ENABLE_FORMS,this.imageResourcesPath=e.imageResourcesPath||"",this.maxCanvasPixels=e.maxCanvasPixels??Mi.get("maxCanvasPixels"),this.pageColors=e.pageColors||null,this.#xe=e.enableHWA||!1,this.eventBus=e.eventBus,this.renderingQueue=e.renderingQueue,this.l10n=e.l10n,this.l10n||=new fi,this.renderTask=null,this.resume=null,this._isStandalone=!this.renderingQueue?.hasViewer(),this._container=t,this._annotationCanvasMap=null,this.annotationLayer=null,this.annotationEditorLayer=null,this.textLayer=null,this.zoomLayer=null,this.xfaLayer=null,this.structTreeLayer=null,this.drawLayer=null;const n=document.createElement("div");if(n.className="page",n.setAttribute("data-page-number",this.id),n.setAttribute("role","region"),n.setAttribute("data-l10n-id","pdfjs-page-landmark"),n.setAttribute("data-l10n-args",JSON.stringify({page:this.id})),this.div=n,this.#Ne(),t?.append(n),this._isStandalone){t?.style.setProperty("--scale-factor",this.scale*je.PDF_TO_CSS_UNITS);const{optionalContentConfigPromise:i}=e;i&&i.then((e=>{i===this._optionalContentConfigPromise&&(this.#ke.initialOptionalContent=e.hasInitialVisibility)})),e.l10n||this.l10n.translate(this.div)}}#Fe(e,t){const i=Oi.get(t),n=this.#De[i];if(this.#De[i]=e,n)n.replaceWith(e);else{for(let t=i-1;t>=0;t--){const i=this.#De[t];if(i)return void i.after(e)}this.div.prepend(e)}}get renderingState(){return this.#Ae}set renderingState(e){if(e!==this.#Ae)switch(this.#Ae=e,this.#Le&&(clearTimeout(this.#Le),this.#Le=null),e){case r.PAUSED:this.div.classList.remove("loading");break;case r.RUNNING:this.div.classList.add("loadingIcon"),this.#Le=setTimeout((()=>{this.div.classList.add("loading"),this.#Le=null}),0);break;case r.INITIAL:case r.FINISHED:this.div.classList.remove("loadingIcon","loading")}}#Ne(){const{viewport:e}=this;if(this.pdfPage){if(this.#Me===e.rotation)return;this.#Me=e.rotation}Ve(this.div,e,!0,!1)}setPdfPage(e){!this._isStandalone||"CanvasText"!==this.pageColors?.foreground&&"Canvas"!==this.pageColors?.background||(this._container?.style.setProperty("--hcm-highlight-filter",e.filterFactory.addHighlightHCMFilter("highlight","CanvasText","Canvas","HighlightText","Highlight")),this._container?.style.setProperty("--hcm-highlight-selected-filter",e.filterFactory.addHighlightHCMFilter("highlight_selected","CanvasText","Canvas","HighlightText","Highlight"))),this.pdfPage=e,this.pdfPageRotate=e.rotate;const t=(this.rotation+this.pdfPageRotate)%360;this.viewport=e.getViewport({scale:this.scale*je.PDF_TO_CSS_UNITS,rotation:t}),this.#Ne(),this.reset()}destroy(){this.reset(),this.pdfPage?.cleanup()}get _textHighlighter(){return Be(this,"_textHighlighter",new Di({pageIndex:this.id-1,eventBus:this.eventBus,findController:this.#Ce.findController}))}#je(e,t){this.eventBus.dispatch(e,{source:this,pageNumber:this.id,error:t})}async#Oe(){let e=null;try{await this.annotationLayer.render(this.viewport,"display")}catch(t){console.error(`#renderAnnotationLayer: "${t}".`),e=t}finally{this.#je("annotationlayerrendered",e)}}async#Re(){let e=null;try{await this.annotationEditorLayer.render(this.viewport,"display")}catch(t){console.error(`#renderAnnotationEditorLayer: "${t}".`),e=t}finally{this.#je("annotationeditorlayerrendered",e)}}async#Ve(){try{await this.drawLayer.render("display")}catch(e){console.error(`#renderDrawLayer: "${e}".`)}}async#Be(){let e=null;try{const t=await this.xfaLayer.render(this.viewport,"display");t?.textDivs&&this._textHighlighter&&this.#$e(t.textDivs)}catch(t){console.error(`#renderXfaLayer: "${t}".`),e=t}finally{this.xfaLayer?.div&&(this.l10n.pause(),this.#Fe(this.xfaLayer.div,"xfaLayer"),this.l10n.resume()),this.#je("xfalayerrendered",e)}}async#He(){if(!this.textLayer)return;let e=null;try{await this.textLayer.render(this.viewport)}catch(t){if(t instanceof ie)return;console.error(`#renderTextLayer: "${t}".`),e=t}this.#je("textlayerrendered",e),this.#ze()}async#ze(){if(!this.textLayer)return;this.structTreeLayer||=new ki;const e=await(this.structTreeLayer.renderingDone?null:this.pdfPage.getStructTree()),t=this.structTreeLayer?.render(e);t&&(this.l10n.pause(),this.canvas?.append(t),this.l10n.resume()),this.structTreeLayer?.show()}async#$e(e){const t=await this.pdfPage.getTextContent(),i=[];for(const e of t.items)i.push(e.str);this._textHighlighter.setTextMapping(e,i),this._textHighlighter.enable()}_resetZoomLayer(e=!1){if(!this.zoomLayer)return;const t=this.zoomLayer.firstChild;this.#Te.delete(t),t.width=0,t.height=0,e&&this.zoomLayer.remove(),this.zoomLayer=null}reset({keepZoomLayer:e=!1,keepAnnotationLayer:t=!1,keepAnnotationEditorLayer:i=!1,keepXfaLayer:n=!1,keepTextLayer:s=!1}={}){this.cancelRendering({keepAnnotationLayer:t,keepAnnotationEditorLayer:i,keepXfaLayer:n,keepTextLayer:s}),this.renderingState=r.INITIAL;const a=this.div,o=a.childNodes,l=e&&this.zoomLayer||null,h=t&&this.annotationLayer?.div||null,d=i&&this.annotationEditorLayer?.div||null,c=n&&this.xfaLayer?.div||null,u=s&&this.textLayer?.div||null;for(let e=o.length-1;e>=0;e--){const t=o[e];switch(t){case l:case h:case d:case c:case u:continue}t.remove();const i=this.#De.indexOf(t);i>=0&&(this.#De[i]=null)}a.removeAttribute("data-loaded"),h&&this.annotationLayer.hide(),d&&this.annotationEditorLayer.hide(),c&&this.xfaLayer.hide(),u&&this.textLayer.hide(),this.structTreeLayer?.hide(),l||(this.canvas&&(this.#Te.delete(this.canvas),this.canvas.width=0,this.canvas.height=0,delete this.canvas),this._resetZoomLayer())}update({scale:e=0,rotation:t=null,optionalContentConfigPromise:i=null,drawingDelay:n=-1}){this.scale=e||this.scale,"number"==typeof t&&(this.rotation=t),i instanceof Promise&&(this._optionalContentConfigPromise=i,i.then((e=>{i===this._optionalContentConfigPromise&&(this.#ke.initialOptionalContent=e.hasInitialVisibility)}))),this.#ke.directDrawing=!0;const s=(this.rotation+this.pdfPageRotate)%360;if(this.viewport=this.viewport.clone({scale:this.scale*je.PDF_TO_CSS_UNITS,rotation:s}),this.#Ne(),this._isStandalone&&this._container?.style.setProperty("--scale-factor",this.viewport.scale),this.canvas){let e=!1;if(this.#Se)if(0===this.maxCanvasPixels)e=!0;else if(this.maxCanvasPixels>0){const{width:t,height:i}=this.viewport,{sx:n,sy:s}=this.outputScale;e=(Math.floor(t)*n|0)*(Math.floor(i)*s|0)>this.maxCanvasPixels}const t=n>=0&&n<1e3;if(t||e){if(t&&!e&&this.renderingState!==r.FINISHED&&(this.cancelRendering({keepZoomLayer:!0,keepAnnotationLayer:!0,keepAnnotationEditorLayer:!0,keepXfaLayer:!0,keepTextLayer:!0,cancelExtraDelay:n}),this.renderingState=r.FINISHED,this.#ke.directDrawing=!1),this.cssTransform({target:this.canvas,redrawAnnotationLayer:!0,redrawAnnotationEditorLayer:!0,redrawXfaLayer:!0,redrawTextLayer:!t,hideTextLayer:t}),t)return;return void this.eventBus.dispatch("pagerendered",{source:this,pageNumber:this.id,cssTransform:!0,timestamp:performance.now(),error:this.#Ee})}this.zoomLayer||this.canvas.hidden||(this.zoomLayer=this.canvas.parentNode,this.zoomLayer.style.position="absolute")}this.zoomLayer&&this.cssTransform({target:this.zoomLayer.firstChild}),this.reset({keepZoomLayer:!0,keepAnnotationLayer:!0,keepAnnotationEditorLayer:!0,keepXfaLayer:!0,keepTextLayer:!0})}cancelRendering({keepAnnotationLayer:e=!1,keepAnnotationEditorLayer:t=!1,keepXfaLayer:i=!1,keepTextLayer:n=!1,cancelExtraDelay:s=0}={}){this.renderTask&&(this.renderTask.cancel(s),this.renderTask=null),this.resume=null,!this.textLayer||n&&this.textLayer.div||(this.textLayer.cancel(),this.textLayer=null),this.structTreeLayer&&!this.textLayer&&(this.structTreeLayer=null),!this.annotationLayer||e&&this.annotationLayer.div||(this.annotationLayer.cancel(),this.annotationLayer=null,this._annotationCanvasMap=null),!this.annotationEditorLayer||t&&this.annotationEditorLayer.div||(this.drawLayer&&(this.drawLayer.cancel(),this.drawLayer=null),this.annotationEditorLayer.cancel(),this.annotationEditorLayer=null),!this.xfaLayer||i&&this.xfaLayer.div||(this.xfaLayer.cancel(),this.xfaLayer=null,this._textHighlighter?.disable())}cssTransform({target:e,redrawAnnotationLayer:t=!1,redrawAnnotationEditorLayer:i=!1,redrawXfaLayer:n=!1,redrawTextLayer:s=!1,hideTextLayer:r=!1}){if(!e.hasAttribute("zooming")){e.setAttribute("zooming",!0);const{style:t}=e;t.width=t.height=""}const a=this.#Te.get(e);if(this.viewport!==a){const t=this.viewport.rotation-a.rotation,i=Math.abs(t);let n=1,s=1;if(90===i||270===i){const{width:e,height:t}=this.viewport;n=t/e,s=e/t}e.style.transform=`rotate(${t}deg) scale(${n}, ${s})`}t&&this.annotationLayer&&this.#Oe(),i&&this.annotationEditorLayer&&(this.drawLayer&&this.#Ve(),this.#Re()),n&&this.xfaLayer&&this.#Be(),this.textLayer&&(r?(this.textLayer.hide(),this.structTreeLayer?.hide()):s&&this.#He())}get width(){return this.viewport.width}get height(){return this.viewport.height}getPagePoint(e,t){return this.viewport.convertToPdfPoint(e,t)}async#Ue(e,t=null){if(e===this.renderTask&&(this.renderTask=null),t instanceof Oe)this.#Ee=null;else if(this.#Ee=t,this.renderingState=r.FINISHED,this._resetZoomLayer(!0),this.#ke.regularAnnotations=!e.separateAnnots,this.eventBus.dispatch("pagerendered",{source:this,pageNumber:this.id,cssTransform:!1,timestamp:performance.now(),error:this.#Ee}),t)throw t}async draw(){this.renderingState!==r.INITIAL&&(console.error("Must be in new state before drawing"),this.reset());const{div:e,l10n:t,pageColors:i,pdfPage:n,viewport:s}=this;if(!n)throw this.renderingState=r.FINISHED,new Error("pdfPage is not loaded");this.renderingState=r.RUNNING;const a=document.createElement("div");if(a.classList.add("canvasWrapper"),this.#Fe(a,"canvasWrapper"),this.textLayer||this.#Ie===d||n.isPureXfa||(this._accessibilityManager||=new Ti,this.textLayer=new Ni({pdfPage:n,highlighter:this._textHighlighter,accessibilityManager:this._accessibilityManager,enablePermissions:this.#Ie===u,onAppend:e=>{this.l10n.pause(),this.#Fe(e,"textLayer"),this.l10n.resume()}})),!this.annotationLayer&&this.#_e!==le.DISABLE){const{annotationStorage:e,annotationEditorUIManager:t,downloadManager:i,enableScripting:s,fieldObjectsPromise:r,hasJSActionsPromise:a,linkService:o}=this.#Ce;this._annotationCanvasMap||=new Map,this.annotationLayer=new Xe({pdfPage:n,annotationStorage:e,imageResourcesPath:this.imageResourcesPath,renderForms:this.#_e===le.ENABLE_FORMS,linkService:o,downloadManager:i,enableScripting:s,hasJSActionsPromise:a,fieldObjectsPromise:r,annotationCanvasMap:this._annotationCanvasMap,accessibilityManager:this._accessibilityManager,annotationEditorUIManager:t,onAppend:e=>{this.#Fe(e,"annotationLayer")}})}const{width:o,height:l}=s,h=document.createElement("canvas");h.setAttribute("role","presentation"),h.hidden=!0;const c=!(!i?.background||!i?.foreground);let p=e=>{c&&!e||(h.hidden=!1,p=null)};a.append(h),this.canvas=h;const g=h.getContext("2d",{alpha:!1,willReadFrequently:!this.#xe}),m=this.outputScale=new f;if(0===this.maxCanvasPixels){const e=1/this.scale;m.sx*=e,m.sy*=e,this.#Se=!0}else if(this.maxCanvasPixels>0){const e=o*l,t=Math.sqrt(this.maxCanvasPixels/e);m.sx>t||m.sy>t?(m.sx=t,m.sy=t,this.#Se=!0):this.#Se=!1}const b=P(m.sx),y=P(m.sy);h.width=_(o*m.sx,b[0]),h.height=_(l*m.sy,y[0]);const{style:v}=h;v.width=_(o,b[1])+"px",v.height=_(l,y[1])+"px",this.#Te.set(h,s);const w={canvasContext:g,transform:m.scaled?[m.sx,0,0,m.sy,0,0]:null,viewport:s,annotationMode:this.#_e,optionalContentConfigPromise:this._optionalContentConfigPromise,annotationCanvasMap:this._annotationCanvasMap,pageColors:i},x=this.renderTask=n.render(w);x.onContinue=e=>{if(p?.(!1),this.renderingQueue&&!this.renderingQueue.isHighestPriority(this))return this.renderingState=r.PAUSED,void(this.resume=()=>{this.renderingState=r.RUNNING,e()});e()};const S=x.promise.then((async()=>{p?.(!0),await this.#Ue(x),this.#He(),this.annotationLayer&&await this.#Oe();const{annotationEditorUIManager:e}=this.#Ce;e&&(this.drawLayer||=new Ei({pageIndex:this.id}),await this.#Ve(),this.drawLayer.setParent(a),this.annotationEditorLayer||(this.annotationEditorLayer=new yi({uiManager:e,pdfPage:n,l10n:t,accessibilityManager:this._accessibilityManager,annotationLayer:this.annotationLayer?.annotationLayer,textLayer:this.textLayer,drawLayer:this.drawLayer.getDrawLayer(),onAppend:e=>{this.#Fe(e,"annotationEditorLayer")}})),this.#Re())}),(e=>(e instanceof Oe||p?.(!0),this.#Ue(x,e))));if(n.isPureXfa){if(!this.xfaLayer){const{annotationStorage:e,linkService:t}=this.#Ce;this.xfaLayer=new Fi({pdfPage:n,annotationStorage:e,linkService:t})}this.#Be()}return e.setAttribute("data-loaded",!0),this.eventBus.dispatch("pagerender",{source:this,pageNumber:this.id}),S}setPageLabel(e){this.pageLabel="string"==typeof e?e:null,this.div.setAttribute("data-l10n-args",JSON.stringify({page:this.pageLabel??this.id})),null!==this.pageLabel?this.div.setAttribute("data-page-label",this.pageLabel):this.div.removeAttribute("data-page-label")}get thumbnailCanvas(){const{directDrawing:e,initialOptionalContent:t,regularAnnotations:i}=this.#ke;return e&&t&&i?this.canvas:null}}class Vi{constructor(e){this._ready=new Promise(((t,i)=>{import(e).then((e=>{t(e.QuickJSSandbox())})).catch(i)}))}async createSandbox(e){(await this._ready).create(e)}async dispatchEventInSandbox(e){const t=await this._ready;setTimeout((()=>t.dispatchEvent(e)),0)}async destroySandbox(){(await this._ready).nukeSandbox()}}class Bi{#We=null;#Ze=null;#Ge=null;#I=null;#Xe=null;#Qe=null;#Je=null;#qe=null;#Ke=!1;#Ye=null;#et=null;constructor({eventBus:e,externalServices:t=null,docProperties:i=null}){this.#Xe=e,this.#Qe=t,this.#Ge=i}setViewer(e){this.#qe=e}async setDocument(e){if(this.#Je&&await this.#tt(),this.#Je=e,!e)return;const[t,i,n]=await Promise.all([e.getFieldObjects(),e.getCalculationOrderIds(),e.getJSActions()]);if(!t&&!n)return void await this.#tt();if(e!==this.#Je)return;try{this.#Ye=this.#it()}catch(e){return console.error(`setDocument: "${e.message}".`),void await this.#tt()}const s=this.#Xe;this.#I=new AbortController;const{signal:r}=this.#I;s._on("updatefromsandbox",(e=>{e?.source===window&&this.#nt(e.detail)}),{signal:r}),s._on("dispatcheventinsandbox",(e=>{this.#Ye?.dispatchEventInSandbox(e.detail)}),{signal:r}),s._on("pagechanging",(({pageNumber:e,previous:t})=>{e!==t&&(this.#st(t),this.#rt(e))}),{signal:r}),s._on("pagerendered",(({pageNumber:e})=>{this._pageOpenPending.has(e)&&e===this.#qe.currentPageNumber&&this.#rt(e)}),{signal:r}),s._on("pagesdestroy",(async()=>{await this.#st(this.#qe.currentPageNumber),await(this.#Ye?.dispatchEventInSandbox({id:"doc",name:"WillClose"})),this.#We?.resolve()}),{signal:r});try{const r=await this.#Ge(e);if(e!==this.#Je)return;await this.#Ye.createSandbox({objects:t,calculationOrder:i,appInfo:{platform:navigator.platform,language:navigator.language},docInfo:{...r,actions:n}}),s.dispatch("sandboxcreated",{source:this})}catch(e){return console.error(`setDocument: "${e.message}".`),void await this.#tt()}await(this.#Ye?.dispatchEventInSandbox({id:"doc",name:"Open"})),await this.#rt(this.#qe.currentPageNumber,!0),Promise.resolve().then((()=>{e===this.#Je&&(this.#Ke=!0)}))}async dispatchWillSave(){return this.#Ye?.dispatchEventInSandbox({id:"doc",name:"WillSave"})}async dispatchDidSave(){return this.#Ye?.dispatchEventInSandbox({id:"doc",name:"DidSave"})}async dispatchWillPrint(){if(this.#Ye){await(this.#et?.promise),this.#et=Promise.withResolvers();try{await this.#Ye.dispatchEventInSandbox({id:"doc",name:"WillPrint"})}catch(e){throw this.#et.resolve(),this.#et=null,e}await this.#et.promise}}async dispatchDidPrint(){return this.#Ye?.dispatchEventInSandbox({id:"doc",name:"DidPrint"})}get destroyPromise(){return this.#Ze?.promise||null}get ready(){return this.#Ke}get _pageOpenPending(){return Be(this,"_pageOpenPending",new Set)}get _visitedPages(){return Be(this,"_visitedPages",new Map)}async#nt(e){const t=this.#qe,i=t.isInPresentationMode||t.isChangingPresentationMode,{id:n,siblings:s,command:r,value:a}=e;if(!n){switch(r){case"clear":console.clear();break;case"error":console.error(a);break;case"layout":if(!i){const e=function(e){let t=p.VERTICAL,i=g.NONE;switch(e){case"SinglePage":t=p.PAGE;break;case"OneColumn":break;case"TwoPageLeft":t=p.PAGE;case"TwoColumnLeft":i=g.ODD;break;case"TwoPageRight":t=p.PAGE;case"TwoColumnRight":i=g.EVEN}return{scrollMode:t,spreadMode:i}}(a);t.spreadMode=e.spreadMode}break;case"page-num":t.currentPageNumber=a+1;break;case"print":await t.pagesPromise,this.#Xe.dispatch("print",{source:this});break;case"println":console.log(a);break;case"zoom":i||(t.currentScaleValue=a);break;case"SaveAs":this.#Xe.dispatch("download",{source:this});break;case"FirstPage":t.currentPageNumber=1;break;case"LastPage":t.currentPageNumber=t.pagesCount;break;case"NextPage":t.nextPage();break;case"PrevPage":t.previousPage();break;case"ZoomViewIn":i||t.increaseScale();break;case"ZoomViewOut":i||t.decreaseScale();break;case"WillPrintFinished":this.#et?.resolve(),this.#et=null}return}if(i&&e.focus)return;delete e.id,delete e.siblings;const o=s?[n,...s]:[n];for(const t of o){const i=document.querySelector(`[data-element-id="${t}"]`);i?i.dispatchEvent(new CustomEvent("updatefromsandbox",{detail:e})):this.#Je?.annotationStorage.setValue(t,e)}}async#rt(e,t=!1){const i=this.#Je,n=this._visitedPages;if(t&&(this.#We=Promise.withResolvers()),!this.#We)return;const s=this.#qe.getPageView(e-1);if(s?.renderingState!==r.FINISHED)return void this._pageOpenPending.add(e);this._pageOpenPending.delete(e);const a=(async()=>{const t=await(n.has(e)?null:s.pdfPage?.getJSActions());i===this.#Je&&await(this.#Ye?.dispatchEventInSandbox({id:"page",name:"PageOpen",pageNumber:e,actions:t}))})();n.set(e,a)}async#st(e){const t=this.#Je,i=this._visitedPages;if(!this.#We)return;if(this._pageOpenPending.has(e))return;const n=i.get(e);n&&(i.set(e,null),await n,t===this.#Je&&await(this.#Ye?.dispatchEventInSandbox({id:"page",name:"PageClose",pageNumber:e})))}#it(){if(this.#Ze=Promise.withResolvers(),this.#Ye)throw new Error("#initScripting: Scripting already exists.");return this.#Qe.createScripting()}async#tt(){if(!this.#Ye)return this.#Je=null,void this.#Ze?.resolve();this.#We&&(await Promise.race([this.#We.promise,new Promise((e=>{setTimeout(e,1e3)}))]).catch((()=>{})),this.#We=null),this.#Je=null;try{await this.#Ye.destroySandbox()}catch{}this.#et?.reject(new Error("Scripting destroyed.")),this.#et=null,this.#I?.abort(),this.#I=null,this._pageOpenPending.clear(),this._visitedPages.clear(),this.#Ye=null,this.#Ke=!1,this.#Ze?.resolve()}}class $i extends Bi{constructor(e){e.externalServices||window.addEventListener("updatefromsandbox",(t=>{e.eventBus.dispatch("updatefromsandbox",{source:window,detail:t.detail})})),e.externalServices||={createScripting:()=>new Vi(e.sandboxBundleSrc)},e.docProperties||=e=>async function(e){const t="".split("#",1)[0];let{info:i,metadata:n,contentDispositionFilename:s,contentLength:r}=await e.getMetadata();if(!r){const{length:t}=await e.getDownloadInfo();r=t}return{...i,baseURL:t,filesize:r,filename:s||ve(""),metadata:n?.getRaw(),authors:n?.get("dc:creator"),numPages:e.numPages,URL:""}}(e),super(e)}}class Hi{constructor(){this.pdfViewer=null,this.pdfThumbnailViewer=null,this.onIdle=null,this.highestPriorityPage=null,this.idleTimeout=null,this.printing=!1,this.isThumbnailViewEnabled=!1,Object.defineProperty(this,"hasViewer",{value:()=>!!this.pdfViewer})}setViewer(e){this.pdfViewer=e}setThumbnailViewer(e){this.pdfThumbnailViewer=e}isHighestPriority(e){return this.highestPriorityPage===e.renderingId}renderHighestPriority(e){this.idleTimeout&&(clearTimeout(this.idleTimeout),this.idleTimeout=null),this.pdfViewer.forceRendering(e)||this.isThumbnailViewEnabled&&this.pdfThumbnailViewer?.forceRendering()||this.printing||this.onIdle&&(this.idleTimeout=setTimeout(this.onIdle.bind(this),3e4))}getHighestPriority(e,t,i,n=!1){const s=e.views,r=s.length;if(0===r)return null;for(let e=0;e<r;e++){const t=s[e].view;if(!this.isViewFinished(t))return t}const a=e.first.id,o=e.last.id;if(o-a+1>r){const n=e.ids;for(let e=1,s=o-a;e<s;e++){const s=i?a+e:o-e;if(n.has(s))continue;const r=t[s-1];if(!this.isViewFinished(r))return r}}let l=i?o:a-2,h=t[l];return h&&!this.isViewFinished(h)||n&&(l+=i?1:-1,h=t[l],h&&!this.isViewFinished(h))?h:null}isViewFinished(e){return e.renderingState===r.FINISHED}renderView(e){switch(e.renderingState){case r.FINISHED:return!1;case r.PAUSED:this.highestPriorityPage=e.renderingId,e.resume();break;case r.RUNNING:this.highestPriorityPage=e.renderingId;break;case r.INITIAL:this.highestPriorityPage=e.renderingId,e.draw().finally((()=>{this.renderHighestPriority()})).catch((e=>{e instanceof Oe||console.error(`renderView: "${e}"`)}))}return!0}}const zi=1e4,Ui=5e3,Wi=250;function Zi(e){return Object.values(re).includes(e)&&e!==re.DISABLE}class Gi{#at=new Set;#ot=0;constructor(e){this.#ot=e}push(e){const t=this.#at;t.has(e)&&t.delete(e),t.add(e),t.size>this.#ot&&this.#lt()}resize(e,t=null){this.#ot=e;const i=this.#at;if(t){const e=i.size;let n=1;for(const s of i)if(t.has(s.id)&&(i.delete(s),i.add(s)),++n>e)break}for(;i.size>this.#ot;)this.#lt()}has(e){return this.#at.has(e)}[Symbol.iterator](){return this.#at.keys()}#lt(){const e=this.#at.keys().next().value;e?.destroy(),this.#at.delete(e)}}class Xi{#ht=null;#dt=null;#ct=null;#ut=re.NONE;#pt=null;#_e=le.ENABLE_FORMS;#gt=null;#xe=!1;#ft=!1;#fe=!1;#I=null;#mt=null;#bt=!1;#yt=null;#vt=!1;#wt=0;#Pt=new ResizeObserver(this.#_t.bind(this));#xt=null;#St=null;#Ie=c;constructor(e){const t="4.4.168";if(Ze!==t)throw new Error(`The API version "${Ze}" does not match the Viewer version "${t}".`);if(this.container=e.container,this.viewer=e.viewer||e.container.firstElementChild,"DIV"!==this.container?.tagName||"DIV"!==this.viewer?.tagName)throw new Error("Invalid `container` and/or `viewer` option.");if(this.container.offsetParent&&"absolute"!==getComputedStyle(this.container).position)throw new Error("The `container` must be absolutely positioned.");this.#Pt.observe(this.container),this.eventBus=e.eventBus,this.linkService=e.linkService||new te,this.downloadManager=e.downloadManager||null,this.findController=e.findController||null,this.#dt=e.altTextManager||null,this.findController&&(this.findController.onIsPageVisible=e=>this._getVisiblePages().ids.has(e)),this._scriptingManager=e.scriptingManager||null,this.#Ie=e.textLayerMode??c,this.#_e=e.annotationMode??le.ENABLE_FORMS,this.#ut=e.annotationEditorMode??re.NONE,this.#ct=e.annotationEditorHighlightColors||null,this.#ft=!0===e.enableHighlightFloatingButton,this.imageResourcesPath=e.imageResourcesPath||"",this.enablePrintAutoRotate=e.enablePrintAutoRotate||!1,this.removePageBorders=e.removePageBorders||!1,this.maxCanvasPixels=e.maxCanvasPixels,this.l10n=e.l10n,this.l10n||=new fi,this.#fe=e.enablePermissions||!1,this.pageColors=e.pageColors||null,this.#mt=e.mlManager||null,this.#xe=e.enableHWA||!1,this.defaultRenderingQueue=!e.renderingQueue,this.defaultRenderingQueue?(this.renderingQueue=new Hi,this.renderingQueue.setViewer(this)):this.renderingQueue=e.renderingQueue;const{abortSignal:i}=e;i?.addEventListener("abort",(()=>{this.#Pt.disconnect(),this.#Pt=null}),{once:!0}),this.scroll=function(e,t,i){const n=function(i){r||(r=window.requestAnimationFrame((function(){r=null;const i=e.scrollLeft,n=s.lastX;i!==n&&(s.right=i>n),s.lastX=i;const a=e.scrollTop,o=s.lastY;a!==o&&(s.down=a>o),s.lastY=a,t(s)})))},s={right:!0,down:!0,lastX:e.scrollLeft,lastY:e.scrollTop,_eventHandler:n};let r=null;return e.addEventListener("scroll",n,{useCapture:!0,signal:i}),i?.addEventListener("abort",(()=>window.cancelAnimationFrame(r)),{once:!0}),s}(this.container,this._scrollUpdate.bind(this),i),this.presentationModeState=a,this._resetView(),this.removePageBorders&&this.viewer.classList.add("removePageBorders"),this.#Ct(),this.eventBus._on("thumbnailrendered",(({pageNumber:e,pdfPage:t})=>{const i=this._pages[e-1];this.#ht.has(i)||t?.cleanup()})),e.l10n||this.l10n.translate(this.container)}get pagesCount(){return this._pages.length}getPageView(e){return this._pages[e]}getCachedPageViews(){return new Set(this.#ht)}get pageViewsReady(){return this._pages.every((e=>e?.pdfPage))}get renderForms(){return this.#_e===le.ENABLE_FORMS}get enableScripting(){return!!this._scriptingManager}get currentPageNumber(){return this._currentPageNumber}set currentPageNumber(e){if(!Number.isInteger(e))throw new Error("Invalid page number.");this.pdfDocument&&(this._setCurrentPageNumber(e,!0)||console.error(`currentPageNumber: "${e}" is not a valid page.`))}_setCurrentPageNumber(e,t=!1){if(this._currentPageNumber===e)return t&&this.#Lt(),!0;if(!(0<e&&e<=this.pagesCount))return!1;const i=this._currentPageNumber;return this._currentPageNumber=e,this.eventBus.dispatch("pagechanging",{source:this,pageNumber:e,pageLabel:this._pageLabels?.[e-1]??null,previous:i}),t&&this.#Lt(),!0}get currentPageLabel(){return this._pageLabels?.[this._currentPageNumber-1]??null}set currentPageLabel(e){if(!this.pdfDocument)return;let t=0|e;if(this._pageLabels){const i=this._pageLabels.indexOf(e);i>=0&&(t=i+1)}this._setCurrentPageNumber(t,!0)||console.error(`currentPageLabel: "${e}" is not a valid page.`)}get currentScale(){return 0!==this._currentScale?this._currentScale:1}set currentScale(e){if(isNaN(e))throw new Error("Invalid numeric scale.");this.pdfDocument&&this.#Mt(e,{noScroll:!1})}get currentScaleValue(){return this._currentScaleValue}set currentScaleValue(e){this.pdfDocument&&this.#Mt(e,{noScroll:!1})}get pagesRotation(){return this._pagesRotation}set pagesRotation(e){if(!x(e))throw new Error("Invalid pages rotation angle.");if(!this.pdfDocument)return;if((e%=360)<0&&(e+=360),this._pagesRotation===e)return;this._pagesRotation=e;const t=this._currentPageNumber;this.refresh(!0,{rotation:e}),this._currentScaleValue&&this.#Mt(this._currentScaleValue,{noScroll:!0}),this.eventBus.dispatch("rotationchanging",{source:this,pagesRotation:e,pageNumber:t}),this.defaultRenderingQueue&&this.update()}get firstPagePromise(){return this.pdfDocument?this._firstPageCapability.promise:null}get onePageRendered(){return this.pdfDocument?this._onePageRenderedCapability.promise:null}get pagesPromise(){return this.pdfDocument?this._pagesCapability.promise:null}get _layerProperties(){const e=this;return Be(this,"_layerProperties",{get annotationEditorUIManager(){return e.#pt},get annotationStorage(){return e.pdfDocument?.annotationStorage},get downloadManager(){return e.downloadManager},get enableScripting(){return!!e._scriptingManager},get fieldObjectsPromise(){return e.pdfDocument?.getFieldObjects()},get findController(){return e.findController},get hasJSActionsPromise(){return e.pdfDocument?.hasJSActions()},get linkService(){return e.linkService}})}#Et(e){const t={annotationEditorMode:this.#ut,annotationMode:this.#_e,textLayerMode:this.#Ie};return e?(e.includes(Fe.COPY)||this.#Ie!==c||(t.textLayerMode=u),e.includes(Fe.MODIFY_CONTENTS)||(t.annotationEditorMode=re.DISABLE),e.includes(Fe.MODIFY_ANNOTATIONS)||e.includes(Fe.FILL_INTERACTIVE_FORMS)||this.#_e!==le.ENABLE_FORMS||(t.annotationMode=le.ENABLE),t):t}async#At(e){if("hidden"===document.visibilityState||!this.container.offsetParent||0===this._getVisiblePages().views.length)return;const t=Promise.withResolvers();function i(){"hidden"===document.visibilityState&&t.resolve()}document.addEventListener("visibilitychange",i,{signal:e}),await Promise.race([this._onePageRenderedCapability.promise,t.promise]),document.removeEventListener("visibilitychange",i)}async getAllText(){const e=[],t=[];for(let i=1,n=this.pdfDocument.numPages;i<=n;++i){if(this.#vt)return null;t.length=0;const n=await this.pdfDocument.getPage(i),{items:s}=await n.getTextContent();for(const e of s)e.str&&t.push(e.str),e.hasEOL&&t.push("\n");e.push(v(t.join("")))}return e.join("\n")}#It(e,t){const i=document.getSelection(),{focusNode:n,anchorNode:s}=i;if(s&&n&&i.containsNode(this.#yt)){if(this.#bt||e===u)return t.preventDefault(),void t.stopPropagation();this.#bt=!0;const{classList:i}=this.viewer;i.add("copyAll");const n=new AbortController;window.addEventListener("keydown",(e=>this.#vt="Escape"===e.key),{signal:n.signal}),this.getAllText().then((async e=>{null!==e&&await navigator.clipboard.writeText(e)})).catch((e=>{console.warn(`Something goes wrong when extracting the text: ${e.message}`)})).finally((()=>{this.#bt=!1,this.#vt=!1,n.abort(),i.remove("copyAll")})),t.preventDefault(),t.stopPropagation()}}setDocument(e){if(this.pdfDocument&&(this.eventBus.dispatch("pagesdestroy",{source:this}),this._cancelRendering(),this._resetView(),this.findController?.setDocument(null),this._scriptingManager?.setDocument(null),this.#pt&&(this.#pt.destroy(),this.#pt=null)),this.pdfDocument=e,!e)return;const t=e.numPages,i=e.getPage(1),n=e.getOptionalContentConfig({intent:"display"}),s=this.#fe?e.getPermissions():Promise.resolve(),{eventBus:r,pageColors:a,viewer:o}=this;this.#I=new AbortController;const{signal:l}=this.#I;if(t>zi){console.warn("Forcing PAGE-scrolling for performance reasons, given the length of the document.");const e=this._scrollMode=p.PAGE;r.dispatch("scrollmodechanged",{source:this,mode:e})}this._pagesCapability.promise.then((()=>{r.dispatch("pagesloaded",{source:this,pagesCount:t})}),(()=>{}));r._on("pagerender",(e=>{const t=this._pages[e.pageNumber-1];t&&this.#ht.push(t)}),{signal:l});const h=e=>{e.cssTransform||(this._onePageRenderedCapability.resolve({timestamp:e.timestamp}),r._off("pagerendered",h))};r._on("pagerendered",h,{signal:l}),Promise.all([i,s]).then((([i,s])=>{if(e!==this.pdfDocument)return;this._firstPageCapability.resolve(i),this._optionalContentConfigPromise=n;const{annotationEditorMode:h,annotationMode:c,textLayerMode:u}=this.#Et(s);if(u!==d){const e=this.#yt=document.createElement("div");e.id="hiddenCopyElement",o.before(e)}if(h!==re.DISABLE){const t=h;e.isPureXfa?console.warn("Warning: XFA-editing is not implemented."):Zi(t)?(this.#pt=new ae(this.container,o,this.#dt,r,e,a,this.#ct,this.#ft,this.#mt),r.dispatch("annotationeditoruimanager",{source:this,uiManager:this.#pt}),t!==re.NONE&&this.#pt.updateMode(t)):console.error(`Invalid AnnotationEditor mode: ${t}`)}const f=this._scrollMode===p.PAGE?null:o,m=this.currentScale,b=i.getViewport({scale:m*je.PDF_TO_CSS_UNITS});o.style.setProperty("--scale-factor",b.scale),"CanvasText"!==a?.foreground&&"Canvas"!==a?.background||(o.style.setProperty("--hcm-highlight-filter",e.filterFactory.addHighlightHCMFilter("highlight","CanvasText","Canvas","HighlightText","Highlight")),o.style.setProperty("--hcm-highlight-selected-filter",e.filterFactory.addHighlightHCMFilter("highlight_selected","CanvasText","Canvas","HighlightText","ButtonText")));for(let e=1;e<=t;++e){const t=new Ri({container:f,eventBus:r,id:e,scale:m,defaultViewport:b.clone(),optionalContentConfigPromise:n,renderingQueue:this.renderingQueue,textLayerMode:u,annotationMode:c,imageResourcesPath:this.imageResourcesPath,maxCanvasPixels:this.maxCanvasPixels,pageColors:a,l10n:this.l10n,layerProperties:this._layerProperties,enableHWA:this.#xe});this._pages.push(t)}this._pages[0]?.setPdfPage(i),this._scrollMode===p.PAGE?this.#kt():this._spreadMode!==g.NONE&&this._updateSpreadMode(),this.#At(l).then((async()=>{if(e!==this.pdfDocument)return;if(this.findController?.setDocument(e),this._scriptingManager?.setDocument(e),this.#yt&&document.addEventListener("copy",this.#It.bind(this,u),{signal:l}),this.#pt&&r.dispatch("annotationeditormodechanged",{source:this,mode:this.#ut}),e.loadingParams.disableAutoFetch||t>Ui)return void this._pagesCapability.resolve();let i=t-1;if(i<=0)this._pagesCapability.resolve();else for(let n=2;n<=t;++n){const t=e.getPage(n).then((e=>{const t=this._pages[n-1];t.pdfPage||t.setPdfPage(e),0==--i&&this._pagesCapability.resolve()}),(e=>{console.error(`Unable to get page ${n} to initialize viewer`,e),0==--i&&this._pagesCapability.resolve()}));n%Wi==0&&await t}})),r.dispatch("pagesinit",{source:this}),e.getMetadata().then((({info:t})=>{e===this.pdfDocument&&t.Language&&(o.lang=t.Language)})),this.defaultRenderingQueue&&this.update()})).catch((e=>{console.error("Unable to initialize viewer",e),this._pagesCapability.reject(e)}))}setPageLabels(e){if(this.pdfDocument){e?Array.isArray(e)&&this.pdfDocument.numPages===e.length?this._pageLabels=e:(this._pageLabels=null,console.error("setPageLabels: Invalid page labels.")):this._pageLabels=null;for(let e=0,t=this._pages.length;e<t;e++)this._pages[e].setPageLabel(this._pageLabels?.[e]??null)}}_resetView(){this._pages=[],this._currentPageNumber=1,this._currentScale=0,this._currentScaleValue=null,this._pageLabels=null,this.#ht=new Gi(10),this._location=null,this._pagesRotation=0,this._optionalContentConfigPromise=null,this._firstPageCapability=Promise.withResolvers(),this._onePageRenderedCapability=Promise.withResolvers(),this._pagesCapability=Promise.withResolvers(),this._scrollMode=p.VERTICAL,this._previousScrollMode=p.UNKNOWN,this._spreadMode=g.NONE,this.#xt={previousPageNumber:1,scrollDown:!0,pages:[]},this.#I?.abort(),this.#I=null,this.viewer.textContent="",this._updateScrollMode(),this.viewer.removeAttribute("lang"),this.#yt?.remove(),this.#yt=null}#kt(){if(this._scrollMode!==p.PAGE)throw new Error("#ensurePageViewVisible: Invalid scrollMode value.");const e=this._currentPageNumber,t=this.#xt,i=this.viewer;if(i.textContent="",t.pages.length=0,this._spreadMode!==g.NONE||this.isInPresentationMode){const n=new Set,s=this._spreadMode-1;-1===s?n.add(e-1):e%2!==s?(n.add(e-1),n.add(e)):(n.add(e-2),n.add(e-1));const r=document.createElement("div");if(r.className="spread",this.isInPresentationMode){const e=document.createElement("div");e.className="dummyPage",r.append(e)}for(const e of n){const i=this._pages[e];i&&(r.append(i.div),t.pages.push(i))}i.append(r)}else{const n=this._pages[e-1];i.append(n.div),t.pages.push(n)}t.scrollDown=e>=t.previousPageNumber,t.previousPageNumber=e}_scrollUpdate(){0!==this.pagesCount&&this.update()}#Tt(e,t=null){const{div:i,id:n}=e;if(this._currentPageNumber!==n&&this._setCurrentPageNumber(n),this._scrollMode===p.PAGE&&(this.#kt(),this.update()),!t&&!this.isInPresentationMode){const e=i.offsetLeft+i.clientLeft,n=e+i.clientWidth,{scrollLeft:s,clientWidth:r}=this.container;(this._scrollMode===p.HORIZONTAL||e<s||n>s+r)&&(t={left:0,top:0})}m(i,t),!this._currentScaleValue&&this._location&&(this._location=null)}#Dt(e){return e===this._currentScale||Math.abs(e-this._currentScale)<1e-15}#Nt(e,t,{noScroll:i=!1,preset:n=!1,drawingDelay:s=-1,origin:r=null}){if(this._currentScaleValue=t.toString(),this.#Dt(e))return void(n&&this.eventBus.dispatch("scalechanging",{source:this,scale:e,presetValue:t}));this.viewer.style.setProperty("--scale-factor",e*je.PDF_TO_CSS_UNITS);const a=s>=0&&s<1e3;this.refresh(!0,{scale:e,drawingDelay:a?s:-1}),a&&(this.#St=setTimeout((()=>{this.#St=null,this.refresh()}),s));const o=this._currentScale;if(this._currentScale=e,!i){let t,i=this._currentPageNumber;if(!this._location||this.isInPresentationMode||this.isChangingPresentationMode||(i=this._location.pageNumber,t=[null,{name:"XYZ"},this._location.left,this._location.top,null]),this.scrollPageIntoView({pageNumber:i,destArray:t,allowNegativeOffset:!0}),Array.isArray(r)){const t=e/o-1,[i,n]=this.containerTopLeft;this.container.scrollLeft+=(r[0]-n)*t,this.container.scrollTop+=(r[1]-i)*t}}this.eventBus.dispatch("scalechanging",{source:this,scale:e,presetValue:n?t:void 0}),this.defaultRenderingQueue&&this.update()}get#Ft(){return this._spreadMode!==g.NONE&&this._scrollMode!==p.HORIZONTAL?2:1}#Mt(e,t){let i=parseFloat(e);if(i>0)t.preset=!1,this.#Nt(i,e,t);else{const n=this._pages[this._currentPageNumber-1];if(!n)return;let s=40,r=5;this.isInPresentationMode?(s=r=4,this._spreadMode!==g.NONE&&(s*=2)):this.removePageBorders?s=r=0:this._scrollMode===p.HORIZONTAL&&([s,r]=[r,s]);const a=(this.container.clientWidth-s)/n.width*n.scale/this.#Ft,o=(this.container.clientHeight-r)/n.height*n.scale;switch(e){case"page-actual":i=1;break;case"page-width":i=a;break;case"page-height":i=o;break;case"page-fit":i=Math.min(a,o);break;case"auto":const t=S(n)?a:Math.min(o,a);i=Math.min(1.25,t);break;default:return void console.error(`#setScale: "${e}" is an unknown zoom value.`)}t.preset=!0,this.#Nt(i,e,t)}}#Lt(){const e=this._pages[this._currentPageNumber-1];this.isInPresentationMode&&this.#Mt(this._currentScaleValue,{noScroll:!0}),this.#Tt(e)}pageLabelToPageNumber(e){if(!this._pageLabels)return null;const t=this._pageLabels.indexOf(e);return t<0?null:t+1}scrollPageIntoView({pageNumber:e,destArray:t=null,allowNegativeOffset:i=!1,ignoreDestinationZoom:n=!1}){if(!this.pdfDocument)return;const s=Number.isInteger(e)&&this._pages[e-1];if(!s)return void console.error(`scrollPageIntoView: "${e}" is not a valid pageNumber parameter.`);if(this.isInPresentationMode||!t)return void this._setCurrentPageNumber(e,!0);let r,a,o=0,l=0,h=0,d=0;const c=s.rotation%180!=0,u=(c?s.height:s.width)/s.scale/je.PDF_TO_CSS_UNITS,p=(c?s.width:s.height)/s.scale/je.PDF_TO_CSS_UNITS;let g=0;switch(t[1].name){case"XYZ":o=t[2],l=t[3],g=t[4],o=null!==o?o:0,l=null!==l?l:p;break;case"Fit":case"FitB":g="page-fit";break;case"FitH":case"FitBH":l=t[2],g="page-width",null===l&&this._location?(o=this._location.left,l=this._location.top):("number"!=typeof l||l<0)&&(l=p);break;case"FitV":case"FitBV":o=t[2],h=u,d=p,g="page-height";break;case"FitR":o=t[2],l=t[3],h=t[4]-o,d=t[5]-l;let e=40,i=5;this.removePageBorders&&(e=i=0),r=(this.container.clientWidth-e)/h/je.PDF_TO_CSS_UNITS,a=(this.container.clientHeight-i)/d/je.PDF_TO_CSS_UNITS,g=Math.min(Math.abs(r),Math.abs(a));break;default:return void console.error(`scrollPageIntoView: "${t[1].name}" is not a valid destination type.`)}if(n||(g&&g!==this._currentScale?this.currentScaleValue=g:0===this._currentScale&&(this.currentScaleValue="auto")),"page-fit"===g&&!t[4])return void this.#Tt(s);const f=[s.viewport.convertToViewportPoint(o,l),s.viewport.convertToViewportPoint(o+h,l+d)];let m=Math.min(f[0][0],f[1][0]),b=Math.min(f[0][1],f[1][1]);i||(m=Math.max(m,0),b=Math.max(b,0)),this.#Tt(s,{left:m,top:b})}_updateLocation(e){const t=this._currentScale,i=this._currentScaleValue,n=parseFloat(i)===t?Math.round(1e4*t)/100:i,s=e.id,r=this._pages[s-1],a=this.container,o=r.getPagePoint(a.scrollLeft-e.x,a.scrollTop-e.y),l=Math.round(o[0]),h=Math.round(o[1]);let d=`#page=${s}`;this.isInPresentationMode||(d+=`&zoom=${n},${l},${h}`),this._location={pageNumber:s,scale:n,top:h,left:l,rotation:this._pagesRotation,pdfOpenParams:d}}update(){const e=this._getVisiblePages(),t=e.views,i=t.length;if(0===i)return;const n=Math.max(10,2*i+1);this.#ht.resize(n,e.ids),this.renderingQueue.renderHighestPriority(e);const s=this._spreadMode===g.NONE&&(this._scrollMode===p.PAGE||this._scrollMode===p.VERTICAL),r=this._currentPageNumber;let a=!1;for(const e of t){if(e.percent<100)break;if(e.id===r&&s){a=!0;break}}this._setCurrentPageNumber(a?r:t[0].id),this._updateLocation(e.first),this.eventBus.dispatch("updateviewarea",{source:this,location:this._location})}containsElement(e){return this.container.contains(e)}focus(){this.container.focus()}get _isContainerRtl(){return"rtl"===getComputedStyle(this.container).direction}get isInPresentationMode(){return this.presentationModeState===h}get isChangingPresentationMode(){return this.presentationModeState===l}get isHorizontalScrollbarEnabled(){return!this.isInPresentationMode&&this.container.scrollWidth>this.container.clientWidth}get isVerticalScrollbarEnabled(){return!this.isInPresentationMode&&this.container.scrollHeight>this.container.clientHeight}_getVisiblePages(){const e=this._scrollMode===p.PAGE?this.#xt.pages:this._pages,t=this._scrollMode===p.HORIZONTAL,i=t&&this._isContainerRtl;return function({scrollEl:e,views:t,sortByVisibility:i=!1,horizontal:n=!1,rtl:s=!1}){const r=e.scrollTop,a=r+e.clientHeight,o=e.scrollLeft,l=o+e.clientWidth,h=[],d=new Set,c=t.length;let u=w(t,n?function(e){const t=e.div,i=t.offsetLeft+t.clientLeft,n=i+t.clientWidth;return s?i<l:n>o}:function(e){const t=e.div;return t.offsetTop+t.clientTop+t.clientHeight>r});u>0&&u<c&&!n&&(u=function(e,t,i){if(e<2)return e;let n=t[e].div,s=n.offsetTop+n.clientTop;s>=i&&(n=t[e-1].div,s=n.offsetTop+n.clientTop);for(let i=e-2;i>=0&&(n=t[i].div,!(n.offsetTop+n.clientTop+n.clientHeight<=s));--i)e=i;return e}(u,t,r));let p=n?l:-1;for(let e=u;e<c;e++){const i=t[e],s=i.div,c=s.offsetLeft+s.clientLeft,u=s.offsetTop+s.clientTop,g=s.clientWidth,f=s.clientHeight,m=c+g,b=u+f;if(-1===p)b>=a&&(p=b);else if((n?c:u)>p)break;if(b<=r||u>=a||m<=o||c>=l)continue;const y=Math.max(0,r-u)+Math.max(0,b-a),v=(g-(Math.max(0,o-c)+Math.max(0,m-l)))/g,w=(f-y)/f*v*100|0;h.push({id:i.id,x:c,y:u,view:i,percent:w,widthPercent:100*v|0}),d.add(i.id)}const g=h[0],f=h.at(-1);return i&&h.sort((function(e,t){const i=e.percent-t.percent;return Math.abs(i)>.001?-i:e.id-t.id})),{first:g,last:f,views:h,ids:d}}({scrollEl:this.container,views:e,sortByVisibility:!0,horizontal:t,rtl:i})}cleanup(){for(const e of this._pages)e.renderingState!==r.FINISHED&&e.reset()}_cancelRendering(){for(const e of this._pages)e.cancelRendering()}async#jt(e){if(e.pdfPage)return e.pdfPage;try{const t=await this.pdfDocument.getPage(e.id);return e.pdfPage||e.setPdfPage(t),t}catch(e){return console.error("Unable to get page for page view",e),null}}#Ot(e){if(1===e.first?.id)return!0;if(e.last?.id===this.pagesCount)return!1;switch(this._scrollMode){case p.PAGE:return this.#xt.scrollDown;case p.HORIZONTAL:return this.scroll.right}return this.scroll.down}forceRendering(e){const t=e||this._getVisiblePages(),i=this.#Ot(t),n=this._spreadMode!==g.NONE&&this._scrollMode!==p.HORIZONTAL,s=this.renderingQueue.getHighestPriority(t,this._pages,i,n);return!!s&&(this.#jt(s).then((()=>{this.renderingQueue.renderView(s)})),!0)}get hasEqualPageSizes(){const e=this._pages[0];for(let t=1,i=this._pages.length;t<i;++t){const i=this._pages[t];if(i.width!==e.width||i.height!==e.height)return!1}return!0}getPagesOverview(){let e;return this._pages.map((t=>{const i=t.pdfPage.getViewport({scale:1}),n=S(i);if(void 0===e)e=n;else if(this.enablePrintAutoRotate&&n!==e)return{width:i.height,height:i.width,rotation:(i.rotation-90)%360};return{width:i.width,height:i.height,rotation:i.rotation}}))}get optionalContentConfigPromise(){return this.pdfDocument?this._optionalContentConfigPromise?this._optionalContentConfigPromise:(console.error("optionalContentConfigPromise: Not initialized yet."),this.pdfDocument.getOptionalContentConfig({intent:"display"})):Promise.resolve(null)}set optionalContentConfigPromise(e){if(!(e instanceof Promise))throw new Error(`Invalid optionalContentConfigPromise: ${e}`);this.pdfDocument&&this._optionalContentConfigPromise&&(this._optionalContentConfigPromise=e,this.refresh(!1,{optionalContentConfigPromise:e}),this.eventBus.dispatch("optionalcontentconfigchanged",{source:this,promise:e}))}get scrollMode(){return this._scrollMode}set scrollMode(e){if(this._scrollMode!==e){if(!function(e){return Number.isInteger(e)&&Object.values(p).includes(e)&&e!==p.UNKNOWN}(e))throw new Error(`Invalid scroll mode: ${e}`);this.pagesCount>zi||(this._previousScrollMode=this._scrollMode,this._scrollMode=e,this.eventBus.dispatch("scrollmodechanged",{source:this,mode:e}),this._updateScrollMode(this._currentPageNumber))}}_updateScrollMode(e=null){const t=this._scrollMode,i=this.viewer;i.classList.toggle("scrollHorizontal",t===p.HORIZONTAL),i.classList.toggle("scrollWrapped",t===p.WRAPPED),this.pdfDocument&&e&&(t===p.PAGE?this.#kt():this._previousScrollMode===p.PAGE&&this._updateSpreadMode(),this._currentScaleValue&&isNaN(this._currentScaleValue)&&this.#Mt(this._currentScaleValue,{noScroll:!0}),this._setCurrentPageNumber(e,!0),this.update())}get spreadMode(){return this._spreadMode}set spreadMode(e){if(this._spreadMode!==e){if(!function(e){return Number.isInteger(e)&&Object.values(g).includes(e)&&e!==g.UNKNOWN}(e))throw new Error(`Invalid spread mode: ${e}`);this._spreadMode=e,this.eventBus.dispatch("spreadmodechanged",{source:this,mode:e}),this._updateSpreadMode(this._currentPageNumber)}}_updateSpreadMode(e=null){if(!this.pdfDocument)return;const t=this.viewer,i=this._pages;if(this._scrollMode===p.PAGE)this.#kt();else if(t.textContent="",this._spreadMode===g.NONE)for(const e of this._pages)t.append(e.div);else{const e=this._spreadMode-1;let n=null;for(let s=0,r=i.length;s<r;++s)null===n?(n=document.createElement("div"),n.className="spread",t.append(n)):s%2===e&&(n=n.cloneNode(!1),t.append(n)),n.append(i[s].div)}e&&(this._currentScaleValue&&isNaN(this._currentScaleValue)&&this.#Mt(this._currentScaleValue,{noScroll:!0}),this._setCurrentPageNumber(e,!0),this.update())}_getPageAdvance(e,t=!1){switch(this._scrollMode){case p.WRAPPED:{const{views:i}=this._getVisiblePages(),n=new Map;for(const{id:e,y:t,percent:s,widthPercent:r}of i){if(0===s||r<100)continue;let i=n.get(t);i||n.set(t,i||=[]),i.push(e)}for(const i of n.values()){const n=i.indexOf(e);if(-1===n)continue;const s=i.length;if(1===s)break;if(t)for(let t=n-1,s=0;t>=s;t--){const n=i[t],s=i[t+1]-1;if(n<s)return e-s}else for(let t=n+1,r=s;t<r;t++){const n=i[t],s=i[t-1]+1;if(n>s)return s-e}if(t){const t=i[0];if(t<e)return e-t+1}else{const t=i[s-1];if(t>e)return t-e+1}break}break}case p.HORIZONTAL:break;case p.PAGE:case p.VERTICAL:{if(this._spreadMode===g.NONE)break;const i=this._spreadMode-1;if(t&&e%2!==i)break;if(!t&&e%2===i)break;const{views:n}=this._getVisiblePages(),s=t?e-1:e+1;for(const{id:e,percent:t,widthPercent:i}of n)if(e===s){if(t>0&&100===i)return 2;break}break}}return 1}nextPage(){const e=this._currentPageNumber,t=this.pagesCount;if(e>=t)return!1;const i=this._getPageAdvance(e,!1)||1;return this.currentPageNumber=Math.min(e+i,t),!0}previousPage(){const e=this._currentPageNumber;if(e<=1)return!1;const t=this._getPageAdvance(e,!0)||1;return this.currentPageNumber=Math.max(e-t,1),!0}updateScale({drawingDelay:e,scaleFactor:t=null,steps:i=null,origin:n}){if(null===i&&null===t)throw new Error("Invalid updateScale options: either `steps` or `scaleFactor` must be provided.");if(!this.pdfDocument)return;let s=this._currentScale;if(t>0&&1!==t)s=Math.round(s*t*100)/100;else if(i){const e=i>0?1.1:1/1.1,t=i>0?Math.ceil:Math.floor;i=Math.abs(i);do{s=t(10*(s*e).toFixed(2))/10}while(--i>0)}s=Math.max(.1,Math.min(10,s)),this.#Mt(s,{noScroll:!1,drawingDelay:e,origin:n})}increaseScale(e={}){this.updateScale({...e,steps:e.steps??1})}decreaseScale(e={}){this.updateScale({...e,steps:-(e.steps??1)})}#Ct(e=this.container.clientHeight){e!==this.#wt&&(this.#wt=e,C.setProperty("--viewer-container-height",`${e}px`))}#_t(e){for(const t of e)if(t.target===this.container){this.#Ct(Math.floor(t.borderBoxSize[0].blockSize)),this.#gt=null;break}}get containerTopLeft(){return this.#gt||=[this.container.offsetTop,this.container.offsetLeft]}get annotationEditorMode(){return this.#pt?this.#ut:re.DISABLE}set annotationEditorMode({mode:e,editId:t=null,isFromKeyboard:i=!1}){if(!this.#pt)throw new Error("The AnnotationEditor is not enabled.");if(this.#ut!==e){if(!Zi(e))throw new Error(`Invalid AnnotationEditor mode: ${e}`);this.pdfDocument&&(this.#ut=e,this.eventBus.dispatch("annotationeditormodechanged",{source:this,mode:e}),this.#pt.updateMode(e,t,i))}}set annotationEditorParams({type:e,value:t}){if(!this.#pt)throw new Error("The AnnotationEditor is not enabled.");this.#pt.updateParams(e,t)}refresh(e=!1,t=Object.create(null)){if(this.pdfDocument){for(const e of this._pages)e.update(t);null!==this.#St&&(clearTimeout(this.#St),this.#St=null),e||this.update()}}}class Qi extends Xi{_resetView(){super._resetView(),this._scrollMode=p.PAGE,this._spreadMode=g.NONE}set scrollMode(e){}_updateScrollMode(){}set spreadMode(e){}_updateSpreadMode(){}}var Ji=s.AnnotationLayerBuilder,qi=s.DownloadManager,Ki=s.EventBus,Yi=s.FindState,en=s.GenericL10n,tn=s.LinkTarget,nn=s.PDFFindController,sn=s.PDFHistory,rn=s.PDFLinkService,an=s.PDFPageView,on=s.PDFScriptingManager,ln=s.PDFSinglePageViewer,hn=s.PDFViewer,dn=s.ProgressBar,cn=s.RenderingStates,un=s.ScrollMode,pn=s.SimpleLinkService,gn=s.SpreadMode,fn=s.StructTreeLayerBuilder,mn=s.TextLayerBuilder,bn=s.XfaLayerBuilder,yn=s.parseQueryString}}]);