from typing import List, Dict, Any

class PromptBuilder:
    """提示词构建器"""
    
    async def build_prompt(
        self,
        query: str,
        context: List[Dict[str, Any]],
        system_prompt: str = None
    ) -> str:
        """
        构建完整的提示词
        Args:
            query: 用户查询
            context: 检索到的相关文档
            system_prompt: 系统提示词
        Returns:
            完整的提示词
        """
        # 构建提示词的具体实现
        pass