"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5990],{75381:function(e,t,r){r.r(t),r.d(t,{default:function(){return D}});var a=r(15009),o=r.n(a),n=r(99289),i=r.n(n),l=r(5574),s=r.n(l),c=r(67294),d=r(97131),p=r(2453),g=r(74330),u=r(71230),m=r(15746),f=r(78158);function x(e){return h.apply(this,arguments)}function h(){return(h=i()(o()().mark((function e(t){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,f.N)("/api/llmMarket",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var v=r(9783),b=r.n(v),y=(0,r(24444).kc)((function(e){var t=e.token;return{item:{height:"64px"},extraImg:b()({width:"155px",marginTop:"-20px",textAlign:"center",img:{width:"100%"}},"@media screen and (max-width: ".concat(t.screenMD,"px)"),{display:"none"}),newButton:{width:"100%",height:"201px",color:t.colorTextSecondary,backgroundColor:t.colorBgContainer,borderColor:t.colorBorder},cardDescription:{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",wordBreak:"break-all"},pageHeaderContent:b()({position:"relative"},"@media screen and (max-width: ".concat(t.screenSM,"px)"),{paddingBottom:"30px"}),contentLink:b()(b()({marginTop:"16px",a:{marginRight:"32px",img:{width:"24px"}},img:{marginRight:"8px",verticalAlign:"middle"}},"@media screen and (max-width: ".concat(t.screenLG,"px)"),{a:{marginRight:"16px"}}),"@media screen and (max-width: ".concat(t.screenSM,"px)"),{position:"absolute",bottom:"-4px",left:"0",width:"1000px",a:{marginRight:"16px"},img:{marginRight:"4px"}}),cardList:{display:"flex",flexWrap:"wrap",gap:"16px",justifyContent:"space-between"},card:{background:"#fff",border:"1px solid #f0f0f0"},cardHeader:{display:"flex",alignItems:"center",padding:"16px",background:"linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%)"},cardAvatar:{width:"40px",height:"40px",borderRadius:"50%",marginRight:"12px"},cardHeaderContent:{display:"flex",flexDirection:"column"},title:{fontWeight:"bold",fontSize:"16px"},subtitle:{fontSize:"12px",color:"#888"},cardContent:{padding:"16px"},description:{marginBottom:"8px",color:"#555"},info:{fontSize:"14px",marginBottom:"4px"},price:{fontSize:"14px",marginBottom:"4px"},tags:{marginTop:"8px"},tag:{display:"inline-block",background:"#f0f0f0",color:"#555",padding:"2px 8px",borderRadius:"4px",fontSize:"12px",marginRight:"4px"}}})),C=r(71471),k=r(4393),j=r(68997),S=r(66309),w=r(83622),z=r(1413),$={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"message",theme:"outlined"},B=r(91146),I=function(e,t){return c.createElement(B.Z,(0,z.Z)((0,z.Z)({},e),{},{ref:t,icon:$}))};var O=c.forwardRef(I),T=r(35312),Z=r(85893),E=C.Z.Text,N=C.Z.Paragraph,P=function(e){var t=e.id,r=e.name,a=e.shortName,o=e.price,n=e.unit,i=e.description,l=e.tags,s=e.logoUrl,c=e.logoFallback;return(0,Z.jsxs)(k.Z,{hoverable:!0,style:{height:"100%",borderRadius:"12px",background:"#ffffff",boxShadow:"0 1px 2px rgba(0, 0, 0, 0.06)",border:"1px solid #f0f0f0"},bodyStyle:{padding:"16px",display:"flex",flexDirection:"column",gap:"12px"},children:[(0,Z.jsxs)("div",{style:{display:"flex",gap:"12px",alignItems:"flex-start"},children:[(0,Z.jsx)(j.Z,{size:64,src:s,style:{backgroundColor:"#f5f5f5",borderRadius:"8px"},children:c}),(0,Z.jsxs)("div",{style:{flex:1},children:[(0,Z.jsxs)(C.Z.Title,{level:5,style:{marginBottom:"4px",fontSize:"15px",fontWeight:500},children:[r,l.map((function(e){return(0,Z.jsx)(S.Z,{style:{margin:0,color:"#722ED1",backgroundColor:"#f9f0ff",border:"none",borderRadius:"4px",padding:"2px 6px",fontSize:"12px"},children:e},e)}))]}),(0,Z.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px",marginBottom:"8px"},children:[(0,Z.jsx)(E,{style:{color:"#722ED1",fontSize:"13px"},children:a}),(0,Z.jsx)(E,{type:"secondary",style:{fontSize:"13px"},children:" | "}),(0,Z.jsxs)(E,{style:{fontFamily:"monospace",fontSize:"13px"},children:["¥ ",o]}),(0,Z.jsxs)(E,{type:"secondary",style:{fontSize:"13px"},children:[" / ",n]})]})]})]}),(0,Z.jsx)(N,{ellipsis:{rows:2},style:{margin:0,fontSize:"13px",color:"#666",lineHeight:"1.5"},children:i}),(0,Z.jsx)("div",{style:{marginTop:"auto",textAlign:"right"},children:(0,Z.jsx)(w.ZP,{type:"primary",icon:(0,Z.jsx)(O,{}),size:"small",onClick:function(){var e=new URLSearchParams;e.append("modelId",String(t)),T.history.push("/LLMmarket/llmChat?".concat(e.toString()))},style:{backgroundColor:"#722ED1",borderColor:"#722ED1",fontSize:"13px",height:"28px",borderRadius:"6px"},children:"开始对话"})})]})},R={openai:"/static/avatar/openai.png",deepseek:"/static/avatar/DeepSeek.svg",doubao:"/static/avatar/doubao.png",teleai:"/static/avatar/teleAI.png",jiutian:"/static/avatar/jiutian.png",ollama:"/static/avatar/default.jpeg",local:"/static/logo.png",zhipu:"/static/avatar/Zhipu.svg",hunyuan:"/static/avatar/hunyuan.svg",tongyi:"/static/avatar/Tongyi.svg",yi:"/static/avatar/Yi.svg",baai:"/static/avatar/BAAI.svg",google:"/static/avatar/Google.svg",meta:"/static/avatar/Meta.svg",mistral:"/static/avatar/Mistral.svg",nvidia:"/static/avatar/NVIDIA.svg",blackforestlabs:"/static/avatar/blackforestlabs.svg",instantx:"/static/avatar/InstantX.svg",bytedance:"/static/avatar/ByteDance.svg",tencentarc:"/static/avatar/TencentARC.svg",internlm:"/static/avatar/internlm.svg",qwen:"/static/avatar/qwen.png"},D=function(){var e=y().styles,t=(0,c.useState)([]),r=s()(t,2),a=r[0],n=r[1],l=(0,c.useState)(!0),f=s()(l,2),h=f[0],v=f[1];(0,c.useEffect)((function(){var e=function(){var e=i()(o()().mark((function e(){var t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,x({current:1,pageSize:8});case 3:(t=e.sent).success?n(t.data||[]):p.ZP.error("获取数据失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error(e.t0),p.ZP.error("获取数据失败");case 11:return e.prev=11,v(!1),e.finish(11);case 14:case"end":return e.stop()}}),e,null,[[0,7,11,14]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var b=(0,Z.jsxs)("div",{className:e.pageHeaderContent,children:[(0,Z.jsx)("p",{children:"金融大模型平台：通过先进的人工智能技术，助力金融行业实现智能化转型， 提供从数据分析到决策支持的全方位解决方案。"}),(0,Z.jsxs)("div",{className:e.contentLink,children:[(0,Z.jsxs)("a",{href:"/LLMmarket/llmChat",children:[(0,Z.jsx)("img",{alt:"",src:"https://gw.alipayobjects.com/zos/rmsportal/MjEImQtenlyueSmVEfUD.svg"})," ","开始对话"]}),(0,Z.jsxs)("a",{href:"/LLMmarket/llmComparison",children:[(0,Z.jsx)("img",{alt:"",src:"https://gw.alipayobjects.com/zos/rmsportal/NbuDUAuBlIApFuDvWiND.svg"})," ","模型对比"]})]})]}),C=(0,Z.jsx)("div",{className:e.extraImg,children:(0,Z.jsx)("img",{alt:"金融大模型",src:"https://gw.alipayobjects.com/zos/rmsportal/RzwpdLnhmvDJToTdfDPe.png"})});return(0,Z.jsx)(d._z,{content:b,extraContent:C,children:(0,Z.jsx)("div",{style:{padding:"24px",minHeight:"100vh"},children:(0,Z.jsx)(g.Z,{spinning:h,children:(0,Z.jsx)(u.Z,{gutter:[24,24],children:a.map((function(e){var t,r,a;return(0,Z.jsx)(m.Z,{xs:24,sm:24,md:12,lg:12,xl:12,children:(0,Z.jsx)(P,{id:e.id,name:e.name,shortName:e.m_name,description:e.description||"暂无描述",tags:[e.provider],logoUrl:(r=e.provider,a=r.toLowerCase(),R[a]||"/static/avatar/default.jpeg"),logoFallback:(null===(t=e.provider)||void 0===t||null===(t=t.substring(0,2))||void 0===t?void 0:t.toUpperCase())||"LM",price:e.price||0,unit:"kTokens"})},e.id)}))})})})})}},15746:function(e,t,r){var a=r(21584);t.Z=a.Z},71230:function(e,t,r){var a=r(17621);t.Z=a.Z},66309:function(e,t,r){r.d(t,{Z:function(){return O}});var a=r(67294),o=r(93967),n=r.n(o),i=r(98423),l=r(98787),s=r(69760),c=r(96159),d=r(45353),p=r(53124),g=r(11568),u=r(15063),m=r(14747),f=r(83262),x=r(83559);const h=e=>{const{lineWidth:t,fontSizeIcon:r,calc:a}=e,o=e.fontSizeSM;return(0,f.IX)(e,{tagFontSize:o,tagLineHeight:(0,g.bf)(a(e.lineHeightSM).mul(o).equal()),tagIconSize:a(r).sub(a(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new u.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var b=(0,x.I$)("Tag",(e=>(e=>{const{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:a,componentCls:o,calc:n}=e,i=n(a).sub(r).equal(),l=n(t).sub(r).equal();return{[o]:Object.assign(Object.assign({},(0,m.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:i,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,g.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:i}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(h(e))),v),y=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(r[a[o]]=e[a[o]])}return r};const C=a.forwardRef(((e,t)=>{const{prefixCls:r,style:o,className:i,checked:l,onChange:s,onClick:c}=e,d=y(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:g,tag:u}=a.useContext(p.E_),m=g("tag",r),[f,x,h]=b(m),v=n()(m,`${m}-checkable`,{[`${m}-checkable-checked`]:l},null==u?void 0:u.className,i,x,h);return f(a.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},o),null==u?void 0:u.style),className:v,onClick:e=>{null==s||s(!l),null==c||c(e)}})))}));var k=C,j=r(98719);var S=(0,x.bk)(["Tag","preset"],(e=>(e=>(0,j.Z)(e,((t,r)=>{let{textColor:a,lightBorderColor:o,lightColor:n,darkColor:i}=r;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:a,background:n,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:i,borderColor:i},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(h(e))),v);const w=(e,t,r)=>{const a="string"!=typeof(o=r)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${a}Bg`],borderColor:e[`color${a}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var z=(0,x.bk)(["Tag","status"],(e=>{const t=h(e);return[w(t,"success","Success"),w(t,"processing","Info"),w(t,"error","Error"),w(t,"warning","Warning")]}),v),$=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(r[a[o]]=e[a[o]])}return r};const B=a.forwardRef(((e,t)=>{const{prefixCls:r,className:o,rootClassName:g,style:u,children:m,icon:f,color:x,onClose:h,bordered:v=!0,visible:y}=e,C=$(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:k,direction:j,tag:w}=a.useContext(p.E_),[B,I]=a.useState(!0),O=(0,i.Z)(C,["closeIcon","closable"]);a.useEffect((()=>{void 0!==y&&I(y)}),[y]);const T=(0,l.o2)(x),Z=(0,l.yT)(x),E=T||Z,N=Object.assign(Object.assign({backgroundColor:x&&!E?x:void 0},null==w?void 0:w.style),u),P=k("tag",r),[R,D,L]=b(P),M=n()(P,null==w?void 0:w.className,{[`${P}-${x}`]:E,[`${P}-has-color`]:x&&!E,[`${P}-hidden`]:!B,[`${P}-rtl`]:"rtl"===j,[`${P}-borderless`]:!v},o,g,D,L),A=e=>{e.stopPropagation(),null==h||h(e),e.defaultPrevented||I(!1)},[,H]=(0,s.Z)((0,s.w)(e),(0,s.w)(w),{closable:!1,closeIconRender:e=>{const t=a.createElement("span",{className:`${P}-close-icon`,onClick:A},e);return(0,c.wm)(e,t,(e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),A(t)},className:n()(null==e?void 0:e.className,`${P}-close-icon`)})))}}),F="function"==typeof C.onClick||m&&"a"===m.type,W=f||null,_=W?a.createElement(a.Fragment,null,W,m&&a.createElement("span",null,m)):m,U=a.createElement("span",Object.assign({},O,{ref:t,className:M,style:N}),_,H,T&&a.createElement(S,{key:"preset",prefixCls:P}),Z&&a.createElement(z,{key:"status",prefixCls:P}));return R(F?a.createElement(d.Z,{component:"Tag"},U):U)})),I=B;I.CheckableTag=k;var O=I}}]);