"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[908],{11475:function(e,t,n){n.d(t,{Z:function(){return i}});var r=n(1413),a=n(67294),u={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"},s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:u}))};var i=a.forwardRef(c)},51042:function(e,t,n){var r=n(1413),a=n(67294),u=n(42110),s=n(91146),c=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:u.Z}))},i=a.forwardRef(c);t.Z=i},57595:function(e,t,n){n.r(t),n.d(t,{default:function(){return E}});var r=n(97857),a=n.n(r),u=n(15009),s=n.n(u),c=n(99289),i=n.n(c),o=n(5574),p=n.n(o),l=n(67294),f=n(97131),d=n(12453),h=n(17788),m=n(2453),x=n(83622),y=n(69992),v=n(35312),Z=n(11475),w=n(51042),g=n(8232),k=n(55102),b=n(34041),j=n(85893),_=function(e){var t=e.modalVisible,n=e.onCancel,r=e.onSubmit,a=g.Z.useForm(),u=p()(a,1)[0];return(0,j.jsx)(h.Z,{title:"新建系统应用",open:t,onCancel:n,onOk:function(){u.validateFields().then((function(e){u.resetFields(),r(e)}))},destroyOnClose:!0,children:(0,j.jsxs)(g.Z,{form:u,layout:"vertical",children:[(0,j.jsx)(g.Z.Item,{name:"name",label:"应用名称",rules:[{required:!0,message:"请输入应用名称"}],children:(0,j.jsx)(k.Z,{placeholder:"请输入应用名称"})}),(0,j.jsx)(g.Z.Item,{name:"app_info",label:"应用ID",rules:[{required:!0,message:"请输入应用ID"}],children:(0,j.jsx)(k.Z,{placeholder:"请输入应用ID"})}),(0,j.jsx)(g.Z.Item,{name:"type",label:"应用类型",rules:[{required:!0,message:"请选择应用类型"}],children:(0,j.jsxs)(b.default,{placeholder:"请选择应用类型",children:[(0,j.jsx)(b.default.Option,{value:"KNOWLEDGE_QA",children:"知识库问答"}),(0,j.jsx)(b.default.Option,{value:"LLM",children:"大语言模型"}),(0,j.jsx)(b.default.Option,{value:"OTHER",children:"其他"})]})}),(0,j.jsx)(g.Z.Item,{name:"tags",label:"标签",children:(0,j.jsx)(b.default,{mode:"tags",placeholder:"请输入标签（可选，多个标签请用回车分隔）"})}),(0,j.jsx)(g.Z.Item,{name:"description",label:"应用描述",children:(0,j.jsx)(k.Z.TextArea,{rows:4,placeholder:"请输入应用描述（可选）"})})]})})},T=h.Z.confirm,E=function(){var e=(0,l.useRef)(),t=(0,l.useState)(!1),n=p()(t,2),r=n[0],u=n[1],c=function(){var t=i()(s()().mark((function t(n){var r,a,c;return s()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,r={name:n.name,app_info:n.app_info,type:n.type,tags:n.tags||[],description:n.description||"",params:{}},a=m.ZP.loading("正在添加..."),t.next=5,(0,y.C3)(r);case 5:c=t.sent,a(),c&&c.success?(m.ZP.success("添加成功"),u(!1),e.current&&e.current.reload()):m.ZP.error((null==c?void 0:c.message)||"添加失败，请重试"),t.next=13;break;case 10:t.prev=10,t.t0=t.catch(0),m.ZP.error("添加失败，请重试");case 13:case"end":return t.stop()}}),t,null,[[0,10]])})));return function(e){return t.apply(this,arguments)}}(),o=[{title:"应用名称",dataIndex:"name",valueType:"text"},{title:"应用id",dataIndex:"app_info",valueType:"text"},{title:"类型",dataIndex:"type",valueType:"select",valueEnum:{KNOWLEDGE_QA:{text:"知识库问答"},LLM:{text:"大语言模型"},OTHER:{text:"其他"}}},{title:"操作",dataIndex:"option",valueType:"option",render:function(t,n){return[(0,j.jsx)(x.ZP,{type:"link",onClick:function(){v.history.push("/admin/system-app-info?id=".concat(n.id))},children:"设置"},"edit"),(0,j.jsx)(x.ZP,{type:"link",danger:!0,onClick:function(){return function(t){var n;T({title:"确认删除",icon:(0,j.jsx)(Z.Z,{}),content:'确定要删除应用 "'.concat(t.name,'" 吗？此操作不可恢复。'),okText:"确认",okType:"danger",cancelText:"取消",onOk:(n=i()(s()().mark((function n(){var r,a;return s()().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,r=m.ZP.loading("正在删除..."),n.next=4,(0,y.j6)(t.id);case 4:a=n.sent,r(),a&&a.success?(m.ZP.success("删除成功"),e.current&&e.current.reload()):m.ZP.error((null==a?void 0:a.message)||"删除失败，请重试"),n.next=12;break;case 9:n.prev=9,n.t0=n.catch(0),m.ZP.error("删除失败，请重试");case 12:case"end":return n.stop()}}),n,null,[[0,9]])}))),function(){return n.apply(this,arguments)})})}(n)},children:"删除"},"delete")]}}];return(0,j.jsxs)(f._z,{children:[(0,j.jsx)(d.Z,{headerTitle:"系统应用设置",actionRef:e,rowKey:"id",toolBarRender:function(){return[(0,j.jsxs)(x.ZP,{type:"primary",onClick:function(){u(!0)},children:[(0,j.jsx)(w.Z,{})," 新建"]},"primary")]},request:function(){var e=i()(s()().mark((function e(t){var n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,y.vQ)(a()({current:t.current||1,pageSize:t.pageSize||10},t));case 2:return n=e.sent,e.abrupt("return",{data:n.data,success:n.success,total:n.total});case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),columns:o}),(0,j.jsx)(_,{modalVisible:r,onCancel:function(){return u(!1)},onSubmit:c})]})}},69992:function(e,t,n){n.d(t,{BN:function(){return i},C3:function(){return j},VB:function(){return h},YS:function(){return f},aD:function(){return v},j6:function(){return T},tv:function(){return k},uG:function(){return x},vQ:function(){return p},xQ:function(){return w}});var r=n(15009),a=n.n(r),u=n(99289),s=n.n(u),c=n(78158);function i(e){return o.apply(this,arguments)}function o(){return(o=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/system-app-info/".concat(t),{method:"GET",id:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function p(e){return l.apply(this,arguments)}function l(){return(l=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/system_app_settings",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function f(e,t){return d.apply(this,arguments)}function d(){return(d=s()(a()().mark((function e(t,n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/system-app-settings/".concat(t),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(){return m.apply(this,arguments)}function m(){return(m=s()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api//system_app_settings/llms",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function x(){return y.apply(this,arguments)}function y(){return(y=s()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/rerankModelList",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function v(){return Z.apply(this,arguments)}function Z(){return(Z=s()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/system_app_settings/knowledge_bases",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function w(e,t,n,r,a){return g.apply(this,arguments)}function g(){return(g=s()(a()().mark((function e(t,n,r,u,s){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/system-app-setting/logs",{method:"GET",params:{app_id:t,current:n,pageSize:r,operator:u,type:s}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function k(e,t,n,r){return b.apply(this,arguments)}function b(){return(b=s()(a()().mark((function e(t,n,r,u){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/system-app-setting/chat",{method:"GET",params:{app_info:t,current:n,pageSize:r,question:u}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function j(e){return _.apply(this,arguments)}function _(){return(_=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/system_app_settings",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function T(e){return E.apply(this,arguments)}function E(){return(E=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/system_app_settings/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}}]);