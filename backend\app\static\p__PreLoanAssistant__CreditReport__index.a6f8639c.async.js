"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1214],{8648:function(e,t,s){s.r(t);s(67294);var a=s(26058),r=s(4393),n=s(83622),l=s(85893),i=a.Z.Content,o=a.<PERSON>.Sider;t.default=function(){return(0,l.jsxs)(a.Z,{children:[(0,l.jsx)(o,{width:400,style:{background:"#fff"}}),(0,l.jsxs)(i,{style:{padding:"0 24px",minHeight:280},children:[(0,l.jsx)("h1",{children:"信贷报告"}),(0,l.jsxs)(r.Z,{title:"信贷报告",style:{width:300},children:[(0,l.jsx)("p",{children:"这里是信贷报告页面的内容。"}),(0,l.jsx)(n.ZP,{type:"primary",onClick:function(){console.log("生成报告中...")},children:"生成报告"})]})]})]})}},26058:function(e,t,s){s.d(t,{Z:function(){return b}});var a=s(74902),r=s(67294),n=s(93967),l=s.n(n),i=s(98423),o=s(53124),f=s(82401),c=s(50344),d=s(70985);var u=s(24793),m=function(e,t){var s={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(s[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(s[a[r]]=e[a[r]])}return s};function p(e){let{suffixCls:t,tagName:s,displayName:a}=e;return e=>r.forwardRef(((a,n)=>r.createElement(e,Object.assign({ref:n,suffixCls:t,tagName:s},a))))}const y=r.forwardRef(((e,t)=>{const{prefixCls:s,suffixCls:a,className:n,tagName:i}=e,f=m(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:c}=r.useContext(o.E_),d=c("layout",s),[p,y,g]=(0,u.ZP)(d),x=a?`${d}-${a}`:d;return p(r.createElement(i,Object.assign({className:l()(s||x,n,y,g),ref:t},f)))})),g=r.forwardRef(((e,t)=>{const{direction:s}=r.useContext(o.E_),[n,p]=r.useState([]),{prefixCls:y,className:g,rootClassName:x,children:C,hasSider:N,tagName:h,style:j}=e,b=m(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),O=(0,i.Z)(b,["suffixCls"]),{getPrefixCls:Z,className:v,style:w}=(0,o.dj)("layout"),S=Z("layout",y),P=function(e,t,s){return"boolean"==typeof s?s:!!e.length||(0,c.Z)(t).some((e=>e.type===d.Z))}(n,C,N),[k,E,_]=(0,u.ZP)(S),H=l()(S,{[`${S}-has-sider`]:P,[`${S}-rtl`]:"rtl"===s},v,g,x,E,_),$=r.useMemo((()=>({siderHook:{addSider:e=>{p((t=>[].concat((0,a.Z)(t),[e])))},removeSider:e=>{p((t=>t.filter((t=>t!==e))))}}})),[]);return k(r.createElement(f.V.Provider,{value:$},r.createElement(h,Object.assign({ref:t,className:H,style:Object.assign(Object.assign({},w),j)},O),C)))})),x=p({tagName:"div",displayName:"Layout"})(g),C=p({suffixCls:"header",tagName:"header",displayName:"Header"})(y),N=p({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(y),h=p({suffixCls:"content",tagName:"main",displayName:"Content"})(y);const j=x;j.Header=C,j.Footer=N,j.Content=h,j.Sider=d.Z,j._InternalSiderContext=d.D;var b=j}}]);