# 文档处理引擎

这个目录包含了一系列用于文档处理、OCR识别和版面分析的工具。

## 核心文件说明

### 文件分析和处理
- `file_analyze.py` - 主要的文件分析处理模块
  - 支持 PDF、Word、PPT 等文档格式
  - 实现文档到 Markdown 的转换
  - 包含版面分析和 OCR 识别功能

- `file_analyze_v2.py` - 文件分析处理模块的升级版
  - 基于 API 调用的方式重构
  - 支持更多文档格式
  - 优化了处理流程和性能

### OCR 和版面分析
- `ocr_api.py` - OCR 服务 API 实现
  - 提供 OCR 文字识别接口
  - 支持版面分析功能
  - 基于 FastAPI 实现 RESTful API

- `layout_detection.py` - 文档版面检测模块
  - 使用 YOLO 模型进行版面分析
  - 支持多种版面元素的识别（标题、正文、图表等）
  - 提供版面可视化功能

- `layout_sorting.py` - 版面元素排序模块
  - 实现版面元素的空间排序
  - 处理文档元素的逻辑顺序

### 文本处理
- `text_splitter.py` - 文本分割工具
  - 实现文本的智能分段
  - 支持多种分割策略

### 数据上传和存储
- `upload_json.py` - 数据上传工具
  - 处理文档解析结果的上传
  - 支持多种文件格式的元数据处理

- `mini_io.py` - MinIO 存储接口
  - 提供对象存储功能
  - 处理文件的上传和下载

### 配置文件
- `config.py` - 配置管理模块
  - 包含系统配置参数
  - 定义处理参数和阈值

## 功能特点

1. **文档处理**
   - 支持多种文档格式的转换和解析
   - 智能识别文档结构和内容
   - 生成标准化的 Markdown 输出

2. **版面分析**
   - 基于深度学习的版面识别
   - 支持复杂文档布局的分析
   - 精确的元素定位和分类

3. **OCR 功能**
   - 多语言文字识别
   - 高精度表格识别
   - 图文混排处理

4. **数据处理**
   - 智能文本分割
   - 结构化数据提取
   - 支持批量处理

## 使用说明

1. 确保安装了所有必要的依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. 配置相关参数（在 config.py 中）：
   - OCR 服务配置
   - 存储路径设置
   - 处理参数调整

3. 运行服务：
   ```bash
   python ocr_api.py  # 启动 OCR 服务
   ```

4. 处理文档：
   ```python
   from file_analyze import main
   main("path/to/your/document")
   ```
