from typing import List, Dict, Any, Optional, TypedDict, Annotated
from langgraph.graph import StateGraph, END
from langgraph.graph.message import  add_messages
import asyncio
from concurrent.futures import ThreadPoolExecutor
from app.engines.rerank.rerank_utils import rerank_texts, RerankResult
from app.engines.retrieval.retriever_utils import parallel_knowledge_search
from app.models.system_app_setting import KnowledgeQAParams
import traceback
from app.db.mongodb import db
from datetime import datetime
from app.utils.enums import RAGSearchType
from app.utils.logging_config import setup_logging, get_logger
from app.utils.promptStr import RagQueryPrompt
setup_logging()
logger = get_logger(__name__)


# 新增检索类型枚举


class SearchResult(TypedDict):
    role: str
    content: str # index_content
    # index_content: str
    score: float
    rerank_score: float
    id: str
    file_id: str
    knowledge_base_id: str
    chunk_index: int
    answer: str
    question: str
    created_at: datetime
    search_type: RAGSearchType  # 新增检索类型字段

class RAGState(TypedDict):
    query: str
    config: Dict[str, Any]
    # vector_results: Annotated[List[Dict], add_messages]
    # graph_results: Annotated[List[Dict], add_messages]
    merged_results: Annotated[List[Dict], add_messages]
    rerank_results: Annotated[List[Dict], add_messages]
    error: Optional[str]
    output: Optional[Dict]

class FlowRAG:
    def __init__(self, config: KnowledgeQAParams,user_id: str):
        logger.info(f"config==》{config}")
        self.config = config
        self.user_id = user_id
        self.thread_pool_executor = ThreadPoolExecutor(max_workers=5)
        self.workflow = StateGraph(RAGState)
        self._setup_workflow()

    def _setup_workflow(self):
        # 定义节点
        self.workflow.add_node("search", self.search_node)
        # self.workflow.add_node("merge", self.merge_node)
        self.workflow.add_node("rerank", self.rerank_node)
        self.workflow.add_node("build_answer", self.build_prompt)
        
        # 构建流程
        self.workflow.set_entry_point("search")
        self.workflow.add_edge("search", "rerank")
        # self.workflow.add_edge("merge", "rerank")
        self.workflow.add_edge("rerank", "build_answer")
        self.workflow.add_edge("build_answer", END)

    async def search_node(self, state: RAGState) -> RAGState:
        """并行执行搜索任务"""
        logger.info('执行搜索任务')
        logger.info(state)
        query = state["query"]
        config = state["config"]
        # loop = asyncio.get_running_loop()

        try:
            results = await parallel_knowledge_search(
                query,
                config.knowledge_base_ids or [],
                config.config or {}
            )
            
            return {**state, "merged_results": results}  # 直接返回列表格式
        except Exception as e:
            traceback.print_exc()
            logger.error(f"搜索失败: {str(e)}")
            return {**state, "error": str(e)}

    async def rerank_node(self, state: RAGState) -> RAGState:
        """重排序结果"""
        logger.info('执行重排序==state')
        logger.info(state)
        logger.info("================")

        if state.get("error") or not state.get("merged_results"):
            return state
        
        rerank_id = self.config.rerank_id
        rerank_config = await db["reranks"].find_one({"id": int(rerank_id)})
        logger.info(f"rerank_config==》{rerank_config}")
        if not rerank_config:
            raise ValueError("Rerank model not found")
        try:
            # 修改调用方式：使用同步执行上下文
            merged_results = state["merged_results"]
            loop = asyncio.get_running_loop()
            final_top_k = self.config.config.get("final_top_k", 5) if self.config.config else 5

            rerank_results: List[RerankResult] = await loop.run_in_executor(
                self.thread_pool_executor,
                lambda: rerank_texts(
                    query=state["query"],
                    texts=[{'id': res.id, 'text': f"{res.additional_kwargs['q']} {res.additional_kwargs['a']}"} for res in merged_results],
                    config=rerank_config,
                    top_k=final_top_k
                )
            )
            # logger.info(f"==》重拍结果: {rerank_results}")

            # 创建ID到原始结果的映射
            id_to_res = {res.id: res for res in merged_results}
            final_results = []
            
            # 按照rerank_results的顺序过滤和排序
            for rank in rerank_results:
                if res := id_to_res.get(rank.id):
                    # 仅保留必要字段
                    final_results.append(res)

            logger.info(f"final_results==》重拍结果: {final_results}")
            return {**state, "rerank_results": final_results}
        except Exception as e:
            traceback.print_exc()
            logger.error(f"重排序失败: {str(e)}")
            return {**state, "error": str(e)}
    async def build_prompt(self, state: RAGState) -> RAGState:
        """构建答案"""
        logger.info('构建答案')
        logger.info(state)
        
        # 从配置中获取提示词模板
        logger.info(f"config==》{state['config']}")
        config:KnowledgeQAParams = state["config"]
        logger.info(f"config==》{config}")
        prompt = "{{quote}}\n请根据以上参考内容回答：{{question}}"
        retrieval_template = "【参考资料{{index}}】{{a}}"
        # logger.info(f"prompt==》{prompt}")
        # logger.info(f"retrieval_template==》{retrieval_template}")

        
        final_top_k = 5
        if config.prompt:
            prompt = config.prompt
        if config.prompt_retrieval:
            retrieval_template = config.prompt_retrieval
        if hasattr(config, 'final_top_k'):
            final_top_k = config.final_top_k

        # logger.info(f"prompt==》{prompt}")
        # logger.info(f"retrieval_template==》{retrieval_template}")
        
        # 处理重排序结果
        quote_strs = []
        for idx, res in enumerate(state["rerank_results"][:final_top_k], 1):  # 取前5个结果
            # 安全获取字段
            logger.info(f"res==》{res}")
            metadata = res.additional_kwargs or {}
            source_name = metadata.get("source_name", "未知来源")
            sourceId = metadata.get("sourceId", "无")
            
            # 格式化单个检索结果（修改此处）
            quote_strs.append(retrieval_template.replace("{{a}}", metadata.get('a', ''))
                                             .replace("{{q}}", metadata.get("q", ""))
                                             .replace("{{source}}", source_name)
                                             .replace("{{sourceId}}", sourceId)
                                             .replace("{{index}}", str(idx))
                                             .replace("{{time}}", datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
        # logger.info(f"quote_strs==》{quote_strs}")
        
        # 合并所有检索结果（新增quote参数处理）
        quote_block = "\n\n".join(quote_strs)
        logger.info(f"quote_block=============》{quote_block}")
        # 生成最终提示词（修改模板参数）
        final_prompt = prompt.replace("{{quote}}", quote_block)\
                             .replace("{{question}}", state["query"])  # 确保使用原始问题
        # logger.info(f"final_prompt==》{final_prompt}")
        # 更新输出结果
        return {**state, "output": {"role": "user", "content": final_prompt}}
    async def run(self, query: str) -> List[Dict[str, Any]]:
        """执行流程"""
        try:
            logger.info('启动流程')
            initial_state = RAGState(
                query=query,
                config=self.config,
                user_id=self.user_id,
                error=None,
                output=None
            )
            app = self.workflow.compile()
            result = await app.ainvoke(initial_state)
            return result
        except Exception as e:
            traceback.print_exc()
            logger.error(f"流程执行失败: {str(e)}")
            return []
        

class FlowRAGforchat2kb:  
    def __init__(self, config: KnowledgeQAParams,user_id: str):
        logger.info(f"config==》{config}")
        self.config = config
        self.user_id = user_id
        self.thread_pool_executor = ThreadPoolExecutor(max_workers=5)
        self.workflow = StateGraph(RAGState)
        self._setup_workflow()

    def _setup_workflow(self):
        # 定义节点
        self.workflow.add_node("search", self.search_node)
        # self.workflow.add_node("merge", self.merge_node)
        self.workflow.add_node("rerank", self.rerank_node)
        self.workflow.add_node("build_answer", self.build_prompt)
        
        # 构建流程
        self.workflow.set_entry_point("search")
        self.workflow.add_edge("search", "rerank")
        # self.workflow.add_edge("merge", "rerank")
        self.workflow.add_edge("rerank", "build_answer")
        self.workflow.add_edge("build_answer", END)

    async def search_node(self, state: RAGState) -> RAGState:
        """并行执行搜索任务"""
        logger.info('执行搜索任务')
        logger.info(state)
        query = state["query"]
        config = state["config"]
        # loop = asyncio.get_running_loop()

        try:
            results = await parallel_knowledge_search(
                query,
                config.knowledge_base_ids or [],
                config.config or {}
            )
            
            return {**state, "merged_results": results}  # 直接返回列表格式
        except Exception as e:
            traceback.print_exc()
            logger.error(f"搜索失败: {str(e)}")
            return {**state, "error": str(e)}

    async def rerank_node(self, state: RAGState) -> RAGState:
        """重排序结果"""
        logger.info('执行重排序==state')
        logger.info(state)
        logger.info("================")

        if state.get("error") or not state.get("merged_results"):
            return state
        
        rerank_id = self.config.rerank_id
        rerank_config = await db["reranks"].find_one({"id": int(rerank_id)})
        logger.info(f"rerank_config==》{rerank_config}")
        if not rerank_config:
            raise ValueError("Rerank model not found")
        try:
            # 修改调用方式：使用同步执行上下文
            merged_results = state["merged_results"]
            loop = asyncio.get_running_loop()
            final_top_k = self.config.config.get("final_top_k", 5) if self.config.config else 5

            rerank_results: List[RerankResult] = await loop.run_in_executor(
                self.thread_pool_executor,
                lambda: rerank_texts(
                    query=state["query"],
                    texts=[{'id': res.id, 'text': f"{res.additional_kwargs['q']} {res.additional_kwargs['a']}"} for res in merged_results],
                    config=rerank_config,
                    top_k=final_top_k
                )
            )
            # logger.info(f"==》重拍结果: {rerank_results}")

            # 创建ID到原始结果的映射
            id_to_res = {res.id: res for res in merged_results}
            final_results = []
            
            # 按照rerank_results的顺序过滤和排序
            for rank in rerank_results:
                if res := id_to_res.get(rank.id):
                    # 仅保留必要字段
                    final_results.append(res)

            logger.info(f"final_results==》重拍结果: {final_results}")
            return {**state, "rerank_results": final_results}
        except Exception as e:
            traceback.print_exc()
            logger.error(f"重排序失败: {str(e)}")
            return {**state, "error": str(e)}
    async def build_prompt(self, state: RAGState) -> RAGState:
        """构建答案"""
        logger.info('构建答案')
        logger.info(state)
        
        # 从配置中获取提示词模板
        logger.info(f"config==》{state['config']}")
        config:KnowledgeQAParams = state["config"]
        logger.info(f"config==》{config}")
        prompt = "{{quote}}\n"+RagQueryPrompt+"\n{{question}}"
        retrieval_template = "【参考资料{{index}}】{{a}}"
        logger.info(f"prompt==》{prompt}")
        logger.info(f"retrieval_template==》{retrieval_template}")

        
        final_top_k = 5
        if config.prompt:
            prompt = config.prompt
        if config.prompt_retrieval:
            retrieval_template = config.prompt_retrieval
        if hasattr(config, 'final_top_k'):
            final_top_k = config.final_top_k

        logger.info(f"prompt==》{prompt}")
        logger.info(f"retrieval_template==》{retrieval_template}")
        
        # 处理重排序结果
        quote_strs = []
        context_strs = []
        query = state["query"]
        for idx, res in enumerate(state["rerank_results"][:final_top_k], 1):  # 取前5个结果
            # 安全获取字段
            logger.info(f"res==》{res}")
            metadata = res.additional_kwargs or {}
            source_name = metadata.get("source_name", "未知来源")
            sourceId = metadata.get("sourceId", "无")
            citation = f"[[citation:{idx}]]"
            content = metadata.get('a', '')
            formatted_text = f"{citation} {content}"
            # 格式化单个检索结果（修改此处）
            quote_strs.append(retrieval_template.replace("{{a}}", formatted_text)
                                             .replace("{{q}}", metadata.get("q", ""))
                                             .replace("{{source}}", source_name)
                                             .replace("{{sourceId}}", sourceId)
                                             .replace("{{index}}", str(idx))
                                             .replace("{{time}}", datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
        # logger.info(f"quote_strs==》{quote_strs}")
            #把citation、content以及source_name、sourceId、index、time以字典形式添加到context_strs中
            context_strs.append({"citation":citation,"content":content,"source_name":source_name,"sourceId":sourceId,"index":idx,"time":datetime.now().strftime("%Y-%m-%d %H:%M:%S")})
        # print(f"context_strs==》{context_strs}")
        # 合并所有检索结果（新增quote参数处理）
        quote_block = "\n\n".join(quote_strs)
        logger.info(f"quote_block==》{quote_block}")
        # 生成最终提示词（修改模板参数）
        final_prompt = prompt.replace("{{quote}}", quote_block)\
                             .replace("{{question}}", state["query"])  # 确保使用原始问题
        # logger.info(f"final_prompt==》{final_prompt}")
        # print(f"final_prompt==》{final_prompt}")
        # 更新输出结果
        return {**state, "output": {"role": "user", "content": final_prompt,"context": context_strs,"query":query}}
    async def run(self, query: str) -> List[Dict[str, Any]]:
        """执行流程"""
        try:
            logger.info('启动流程')
            initial_state = RAGState(
                query=query,
                config=self.config,
                user_id=self.user_id,
                error=None,
                output=None
            )
            app = self.workflow.compile()
            result = await app.ainvoke(initial_state)
            return result
        except Exception as e:
            traceback.print_exc()
            logger.error(f"流程执行失败: {str(e)}")
            return []
        

class FlowRAGforgeContext:  
    def __init__(self, config: KnowledgeQAParams):
        logger.info(f"config==》{config}")
        self.config = config
        self.thread_pool_executor = ThreadPoolExecutor(max_workers=5)
        self.workflow = StateGraph(RAGState)
        self._setup_workflow()

    def _setup_workflow(self):
        # 定义节点
        self.workflow.add_node("search", self.search_node)
        # self.workflow.add_node("merge", self.merge_node)
        self.workflow.add_node("rerank", self.rerank_node)
        # self.workflow.add_node("build_answer", self.build_prompt)
        
        # 构建流程
        self.workflow.set_entry_point("search")
        self.workflow.add_edge("search", "rerank")
        # self.workflow.add_edge("merge", "rerank")
        # self.workflow.add_edge("rerank", "build_answer")
        self.workflow.add_edge("rerank", END)

    async def search_node(self, state: RAGState) -> RAGState:
        """并行执行搜索任务"""
        logger.info('执行搜索任务')
        logger.info(state)
        query = state["query"]
        config = state["config"]
        # loop = asyncio.get_running_loop()

        try:
            results = await parallel_knowledge_search(
                query,
                config.knowledge_base_ids or [],
                config.config or {}
            )
            
            return {**state, "merged_results": results}  # 直接返回列表格式
        except Exception as e:
            traceback.print_exc()
            logger.error(f"搜索失败: {str(e)}")
            return {**state, "error": str(e)}

    async def rerank_node(self, state: RAGState) -> RAGState:
        """重排序结果"""
        logger.info('执行重排序==state')
        logger.info(state)
        logger.info("================")

        if state.get("error") or not state.get("merged_results"):
            return state
        
        rerank_id = self.config.rerank_id
        rerank_config = await db["reranks"].find_one({"id": int(rerank_id)})
        logger.info(f"rerank_config==》{rerank_config}")
        if not rerank_config:
            raise ValueError("Rerank model not found")
        try:
            # 修改调用方式：使用同步执行上下文
            merged_results = state["merged_results"]
            loop = asyncio.get_running_loop()
            final_top_k = self.config.config.get("final_top_k", 5) if self.config.config else 5

            rerank_results: List[RerankResult] = await loop.run_in_executor(
                self.thread_pool_executor,
                lambda: rerank_texts(
                    query=state["query"],
                    texts=[{'id': res.id, 'text': f"{res.additional_kwargs['q']} {res.additional_kwargs['a']}"} for res in merged_results],
                    config=rerank_config,
                    top_k=final_top_k
                )
            )
            # logger.info(f"==》重拍结果: {rerank_results}")

            # 创建ID到原始结果的映射
            id_to_res = {res.id: res for res in merged_results}
            final_results = []
            
            # 按照rerank_results的顺序过滤和排序
            for rank in rerank_results:
                if res := id_to_res.get(rank.id):
                    # 仅保留必要字段
                    final_results.append(res)

            logger.info(f"final_results==》重拍结果: {final_results}")
            return {**state, "rerank_results": final_results}
        except Exception as e:
            traceback.print_exc()
            logger.error(f"重排序失败: {str(e)}")
            return {**state, "error": str(e)}
    
        # """构建答案"""
        # logger.info('构建答案')
        # logger.info(state)
        
        # # 从配置中获取提示词模板
        # logger.info(f"config==》{state['config']}")
        # config:KnowledgeQAParams = state["config"]
        # logger.info(f"config==》{config}")
        # prompt = "{{quote}}\n"+RagQueryPrompt+"\n{{question}}"
        # retrieval_template = "【参考资料{{index}}】{{a}}"
        # logger.info(f"prompt==》{prompt}")
        # logger.info(f"retrieval_template==》{retrieval_template}")

        
        # final_top_k = 5
        # if config.prompt:
        #     prompt = config.prompt
        # if config.prompt_retrieval:
        #     retrieval_template = config.prompt_retrieval
        # if hasattr(config, 'final_top_k'):
        #     final_top_k = config.final_top_k

        # logger.info(f"prompt==》{prompt}")
        # logger.info(f"retrieval_template==》{retrieval_template}")
        
        # # 处理重排序结果
        # quote_strs = []
        # context_strs = []
        # for idx, res in enumerate(state["rerank_results"][:final_top_k], 1):  # 取前5个结果
        #     # 安全获取字段
        #     logger.info(f"res==》{res}")
        #     metadata = res.additional_kwargs or {}
        #     source_name = metadata.get("source_name", "未知来源")
        #     sourceId = metadata.get("sourceId", "无")
        #     citation = f"[[citation:{idx}]]"
        #     content = metadata.get('a', '')
        #     formatted_text = f"{citation} {content}"
        #     # 格式化单个检索结果（修改此处）
        #     quote_strs.append(retrieval_template.replace("{{a}}", formatted_text)
        #                                      .replace("{{q}}", metadata.get("q", ""))
        #                                      .replace("{{source}}", source_name)
        #                                      .replace("{{sourceId}}", sourceId)
        #                                      .replace("{{index}}", str(idx))
        #                                      .replace("{{time}}", datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
        # # logger.info(f"quote_strs==》{quote_strs}")
        #     #把citation、content以及source_name、sourceId、index、time以字典形式添加到context_strs中
        #     context_strs.append({"citation":citation,"content":content,"source_name":source_name,"sourceId":sourceId,"index":idx,"time":datetime.now().strftime("%Y-%m-%d %H:%M:%S")})
        # print(f"context_strs==》{context_strs}")
        # # 合并所有检索结果（新增quote参数处理）
        # quote_block = "\n\n".join(quote_strs)
        # logger.info(f"quote_block==》{quote_block}")
        # # 生成最终提示词（修改模板参数）
        # final_prompt = prompt.replace("{{quote}}", quote_block)\
        #                      .replace("{{question}}", state["query"])  # 确保使用原始问题
        # # logger.info(f"final_prompt==》{final_prompt}")
        # # 更新输出结果
        # return {**state, "output": {"role": "user", "content": final_prompt,"context": context_strs}}
    async def run(self, query: str) -> List[Dict[str, Any]]:
        """执行流程"""
        try:
            logger.info('启动流程')
            initial_state = RAGState(
                query=query,
                config=self.config,
                error=None,
                output=None
            )
            app = self.workflow.compile()
            result = await app.ainvoke(initial_state)
            return result
        except Exception as e:
            traceback.print_exc()
            logger.error(f"流程执行失败: {str(e)}")
            return []