import pytest
from fastapi.testclient import TestClient
from app.main import app
from unittest.mock import patch, AsyncMock
import json

client = TestClient(app)

# 模拟 token
MOCK_TOKEN = "test-token"
MOCK_TOKEN_INFO = {
    "app_id": "test-app",
    "permissions": ["write"]
}

# 测试数据
TEST_ENTERPRISE_ID = "test-enterprise-123"
TEST_DIMENSIONS = ["financial", "credit"]

@pytest.fixture
def mock_verify_token():
    with patch("app.api.preLoanAssistant_api.verify_api_token", return_value=MOCK_TOKEN_INFO):
        yield

@pytest.fixture
def mock_db():
    with patch("app.api.preLoanAssistant_api.db") as mock_db:
        mock_db["api_calls"].insert_one = AsyncMock()
        mock_db["api_calls"].update_one = AsyncMock()
        yield mock_db

@pytest.mark.asyncio
async def test_analyze_loan_stream_success(mock_verify_token, mock_db):
    """测试流式分析接口成功场景"""
    request_data = {
        "enterpriseId": TEST_ENTERPRISE_ID,
        "dimensions": TEST_DIMENSIONS,
        "executionMode": "SYNC",
        "outputFormat": "MARKDOWN",
        "stream": True,
        "context": {}
    }

    headers = {
        "Authorization": f"Bearer {MOCK_TOKEN}",
        "Content-Type": "application/json"
    }

    with patch("app.api.preLoanAssistant_api.analyze_enterprise_stream") as mock_analyze:
        mock_analyze.return_value = AsyncMock()
        response = client.post(
            "/api/v1/loan-analysis",
            headers=headers,
            json=request_data
        )

        assert response.status_code == 200
        assert response.headers["content-type"] == "text/event-stream"
        mock_db["api_calls"].insert_one.assert_called_once()

@pytest.mark.asyncio
async def test_analyze_loan_invalid_mode(mock_verify_token, mock_db):
    """测试无效的执行模式"""
    request_data = {
        "enterpriseId": TEST_ENTERPRISE_ID,
        "dimensions": TEST_DIMENSIONS,
        "executionMode": "ASYNC",  # 不支持的模式
        "stream": True
    }

    headers = {
        "Authorization": f"Bearer {MOCK_TOKEN}",
        "Content-Type": "application/json"
    }

    response = client.post(
        "/api/v1/loan-analysis",
        headers=headers,
        json=request_data
    )

    assert response.status_code == 400
    assert "Only synchronous mode is supported" in response.json()["detail"]

@pytest.mark.asyncio
async def test_analyze_loan_invalid_format(mock_verify_token, mock_db):
    """测试无效的输出格式"""
    request_data = {
        "enterpriseId": TEST_ENTERPRISE_ID,
        "dimensions": TEST_DIMENSIONS,
        "outputFormat": "JSON",  # 不支持的格式
        "stream": True
    }

    headers = {
        "Authorization": f"Bearer {MOCK_TOKEN}",
        "Content-Type": "application/json"
    }

    response = client.post(
        "/api/v1/loan-analysis",
        headers=headers,
        json=request_data
    )

    assert response.status_code == 400
    assert "Only MARKDOWN format is supported" in response.json()["detail"]

@pytest.mark.asyncio
async def test_analyze_loan_missing_enterprise_id(mock_verify_token, mock_db):
    """测试缺少企业ID"""
    request_data = {
        "dimensions": TEST_DIMENSIONS,
        "stream": True
    }

    headers = {
        "Authorization": f"Bearer {MOCK_TOKEN}",
        "Content-Type": "application/json"
    }

    response = client.post(
        "/api/v1/loan-analysis",
        headers=headers,
        json=request_data
    )

    assert response.status_code == 422  # FastAPI 的验证错误

@pytest.mark.asyncio
async def test_analyze_loan_server_error(mock_verify_token, mock_db):
    """测试服务器错误场景"""
    request_data = {
        "enterpriseId": TEST_ENTERPRISE_ID,
        "dimensions": TEST_DIMENSIONS,
        "stream": True
    }

    headers = {
        "Authorization": f"Bearer {MOCK_TOKEN}",
        "Content-Type": "application/json"
    }

    with patch("app.api.preLoanAssistant_api.analyze_enterprise_stream", 
              side_effect=Exception("Internal server error")):
        response = client.post(
            "/api/v1/loan-analysis",
            headers=headers,
            json=request_data
        )

        assert response.status_code == 500
        mock_db["api_calls"].update_one.assert_called_once() 