# WiseGraph - 知识图谱构建与纯图结构检索系统

WiseGraph 是一个独立的知识图谱解决方案，专注于从文本数据构建知识图谱并提供纯图结构检索功能。

## 🎯 核心特性

- **知识图谱构建**: 从文本块字典构建结构化知识图谱
- **纯图结构检索**: 不依赖向量检索，仅使用图结构进行查询
- **实体关系抽取**: 基于LLM的智能实体和关系识别
- **Neo4j存储**: 使用Neo4j图数据库进行高效存储和查询
- **异步支持**: 全异步设计，支持高并发处理

## 📋 系统要求

- Python 3.8+
- Neo4j 数据库
- OpenAI API 访问权限

## 🛠️ 安装依赖

```bash
pip install neo4j openai asyncio
```

## ⚙️ 配置

在使用前，请在 `config.py` 中配置以下信息：

```python
# Neo4j 配置
NEO4J_CONFIG = {
    "uri": "bolt://localhost:7687",
    "username": "neo4j", 
    "password": "your_password",
    "database": "neo4j"
}

# OpenAI 配置
OPENAI_CONFIG = {
    "api_key": "your_openai_api_key",
    "base_url": "https://api.openai.com/v1",
    "model": "gpt-4o-mini"
}
```

## 🚀 快速开始

### 1. 知识图谱构建

```python
import asyncio
from wisegraph import GraphBuilder

async def build_graph():
    # 准备文本数据（字典格式）
    chunks = {
        "chunk1": "余春明是某公司的董事长...",
        "chunk2": "李华是知名的保荐人...",
        # 更多文本块...
    }
    
    # 创建图构建器
    builder = GraphBuilder()
    
    try:
        # 初始化连接
        await builder.initialize()
        
        # 构建知识图谱
        result = await builder.build_graph_from_chunks(chunks, clear_existing=True)
        
        print(f"构建完成: {result}")
        
    finally:
        await builder.close()

# 运行
asyncio.run(build_graph())
```

### 2. 纯图结构检索

```python
import asyncio
from wisegraph import GraphRetriever

async def retrieve_info():
    # 创建图检索器
    retriever = GraphRetriever()
    
    try:
        # 初始化连接
        await retriever.initialize()
        
        # 执行查询
        result = await retriever.retrieve("余春明是谁？")
        
        print("查询结果:")
        print(f"上下文: {result['context']}")
        print(f"找到实体: {len(result['entities_found'])}")
        print(f"相关文本块: {result['chunk_ids']}")
        
    finally:
        await retriever.close()

# 运行
asyncio.run(retrieve_info())
```

## 📊 API 文档

### GraphBuilder

#### `build_graph_from_chunks(chunks, clear_existing=False)`
从文本块字典构建知识图谱

**参数:**
- `chunks`: Dict[str, str] - 文本块字典，格式为 {"chunk_id": "text_content"}
- `clear_existing`: bool - 是否清空现有数据

**返回:**
- Dict - 构建结果统计信息

#### `get_graph_summary()`
获取图谱摘要信息

**返回:**
- Dict - 包含实体类型、关系类型等统计信息

### GraphRetriever

#### `retrieve(query, max_depth=None, max_results=None)`
执行纯图结构检索

**参数:**
- `query`: str - 用户查询
- `max_depth`: int - 最大查询深度（默认3）
- `max_results`: int - 最大结果数量（默认50）

**返回:**
- Dict - 包含上下文、实体、子图等信息

#### `get_entity_details(entity_name)`
获取特定实体的详细信息

**参数:**
- `entity_name`: str - 实体名称

**返回:**
- Dict - 实体详细信息和连接关系

## 🔧 工作原理

### 图构建流程
1. **文本分析**: 使用LLM从每个文本块中提取实体和关系
2. **实体识别**: 识别人物、组织、地点、事件等不同类型的实体
3. **关系抽取**: 提取实体间的语义关系
4. **图存储**: 将实体和关系存储到Neo4j图数据库

### 检索流程
1. **查询分析**: 使用LLM从用户查询中提取关键实体和关系类型
2. **实体匹配**: 在图中搜索相关实体（精确匹配+模糊匹配）
3. **子图提取**: 获取相关实体的连接子图
4. **上下文构建**: 从子图信息构建结构化上下文

## 📝 示例

运行完整示例：

```bash
cd wisegraph
python example.py
```

这将演示：
- 知识图谱构建过程
- 多种查询的检索结果
- 实体详情查询

## 🔍 支持的查询类型

- **实体查询**: "余春明是谁？"
- **关系查询**: "余春明和李华的关系"
- **属性查询**: "公司的股东情况"
- **事件查询**: "股票发行的相关信息"

## 📈 性能特点

- **纯图检索**: 不依赖向量计算，查询速度快
- **结构化结果**: 返回明确的实体关系结构
- **可解释性**: 查询路径清晰可追踪
- **扩展性**: 支持大规模图数据

## 🤝 与LightRAG的区别

| 特性 | WiseGraph | LightRAG |
|------|-----------|----------|
| 检索方式 | 纯图结构检索 | 混合向量+图检索 |
| 依赖 | Neo4j + LLM | 多种存储后端 |
| 复杂度 | 简单专注 | 功能丰富 |
| 适用场景 | 结构化知识查询 | 通用RAG应用 |

## 📄 许可证

MIT License
