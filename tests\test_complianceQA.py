import pytest
import httpx
from datetime import datetime

# 使用 pytest-asyncio 提供的装饰器
@pytest.mark.asyncio
async def test_streaming_response():
    # 创建一个有效的 JWT 令牌
    token = "b90a26c8170bb79be0fa881f0b71bb98"

    # 准备请求数据
    data = {
        "app_info": "complianceQA",
        "conversation_id": "670cc0712c22f6e196aa7179",
        "user_id": 1,
        "messages": [
            {
                "content": "asdasd",
                "createAt": 1728902527716,
                "id": "RAxQRlLQ",
                "updateAt": 1728902527716,
                "message": "asdasd",
                "role": "user",
                "meta": {"avatar": "😀"}
            }
        ]
    }

    # 使用 httpx 发送 POST 请求
    async with httpx.AsyncClient(timeout=30.0) as client:  # 增加超时时间
        response = await client.post(
            "http://127.0.0.1:8000/api/app/chat/complianceQA",
            json=data,
            headers={"Authorization": f"Bearer {token}"}
        )

        # 断言响应状态码
        assert response.status_code == 200

        # 断言响应是流式的
        async for chunk in response.aiter_bytes():
            assert chunk  # 确保每个块都有数据
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"[{current_time}] Received chunk: {chunk.decode('utf-8')}")  # 打印每个块的数据和时间
