from mongoengine import Document,ObjectIdField, StringField, IntField, DateTimeField, FloatField, DictField, ListField, BooleanField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, Dict, List
from bson import ObjectId
import hashlib

# MongoEngine 模型
class ChunkIndex(Document):
    meta = {
        'collection': 'chunks_index',
        'indexes': [
            {
                'fields': 'knowledge_base_id',
            },
            {
                'fields': 'file_id',
            },
            {
                'fields': ['file_id', 'index_content', 'chunk_type'],
                'unique': True,
                'sparse': True,
                'background': True,
                'type': 'hashed'  # 使用哈希索引
            }
        ]
    }
    _id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    file_id = ObjectIdField() # 问价名
    knowledge_base_id = ObjectIdField(required=True) # 知识库id
    chunk_id = ObjectIdField(required=True) # 知识库id
    embedding = ListField(FloatField())
    index_content = StringField(required=True)
    chunk_type = StringField(required=True)
    chunk_index = IntField(required=True) # 排序
    is_expired = BooleanField(default=False)  # 新增字段
    created_at = DateTimeField(default=datetime.now)
    combined_hash = StringField()



# Pydantic 模型
class ChunkIndexBase(BaseModel):
    file_id: str
    knowledge_base_id: str
    chunk_index: int
    index_content: str
    index_type: str
    embedding: Optional[List[float]] = None

class ChunkCreate(ChunkBase):
    pass

class ChunkUpdate(BaseModel):
    content: Optional[str] = None
    embedding: Optional[List[float]] = None
    metadata: Optional[Dict] = None
    is_expired: Optional[bool] = None  # 新增字段

class ChunkResponse(ChunkBase):
    id: str
    created_at: datetime
    last_updated: datetime

    class Config:
        from_attributes = True
