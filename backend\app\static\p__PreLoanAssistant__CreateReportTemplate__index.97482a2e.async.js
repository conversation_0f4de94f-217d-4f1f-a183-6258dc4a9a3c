"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8010],{74078:function(e,t,n){n.r(t),n.d(t,{default:function(){return X}});var r=n(15009),i=n.n(r),s=n(64599),o=n.n(s),l=n(99289),a=n.n(l),c=n(19632),d=n.n(c),p=n(5574),x=n.n(p),u=n(97857),m=n.n(u),h=n(67294),f=n(97131),g=n(55102),j=n(71471),v=n(8232),y=n(2453),Z=n(42075),b=n(83622),_=n(86738),S=n(96074),k=n(66309),w=n(71230),C=n(15746),B=n(63496),P=n(17788),I=n(72269),N=n(2487),z=n(51042),T=n(15360),R=n(47389),q=n(82061),L=n(64789),A=n(82826),F=n(60219),D=n(35312),H=n(78158);function E(e){return W.apply(this,arguments)}function W(){return(W=a()(i()().mark((function e(t){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,H.N)("/api/reportTemplate/sections",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Y(e){return M.apply(this,arguments)}function M(){return(M=a()(i()().mark((function e(t){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,H.N)("/api/reportTemplate/sections",{method:"POST",data:m()(m()({},t),{},{parent_id:null,level:1})}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var G=(0,n(24444).kc)((function(e){var t=e.token;return{leftPanel:{flex:3,margin:0,padding:0},rightPanel:{flex:7,margin:0,padding:0},formLabel:{fontWeight:500,marginBottom:"6px"},uploadBox:{border:"1px dashed ".concat(t.colorBorder),borderRadius:"8px",padding:"12px",textAlign:"center",marginTop:"6px"},nodeTitle:{display:"flex",alignItems:"center",height:"28px"},nodeActions:{visibility:"hidden",marginLeft:"8px"},nodeItem:{"&:hover .node-actions":{visibility:"visible"}},sectionForm:{marginTop:"12px"},sectionHeader:{display:"flex",justifyContent:"space-between",alignItems:"center"},tagInput:{marginBottom:"6px",width:"100%"},tagsContainer:{marginBottom:"12px"},formActions:{display:"flex",justifyContent:"flex-end",gap:"8px"},noSectionSelected:{textAlign:"center",padding:"30px 0"},sectionList:{height:"100%",overflowY:"auto",padding:"0 4px"},sectionDetail:{height:"100%",overflowY:"auto",padding:"0 4px",borderLeft:"1px solid #f0f0f0"},mainCard:{borderRadius:"4px",boxShadow:"none",border:"1px solid #f0f0f0"},infoCard:{boxShadow:"none",marginBottom:"8px",border:"1px solid #f0f0f0",backgroundColor:"#ffffff",padding:"16px",borderRadius:"4px"},sectionCard:{boxShadow:"none",height:"100%",border:"1px solid #f0f0f0",backgroundColor:"#ffffff",padding:"16px",borderRadius:"4px",display:"flex",flexDirection:"column"},formSection:{marginBottom:"12px",paddingBottom:"12px",borderBottom:"1px solid #f0f0f0"},contentBox:{backgroundColor:"rgba(0,0,0,0.02)",padding:"10px",borderRadius:"4px",marginTop:"6px"},templateItem:{cursor:"pointer",padding:"12px",borderRadius:"4px",transition:"all 0.3s","&:hover":{backgroundColor:t.colorBgTextHover}},templateListContainer:{padding:"0 16px"},templateHeader:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},templateAvatar:{backgroundColor:t.colorPrimary,color:"#fff"},selectedTemplate:{backgroundColor:t.colorPrimaryBg,borderLeft:"3px solid ".concat(t.colorPrimary)}}})),O=n(85893),J=g.Z.TextArea,Q=j.Z.Text,V=j.Z.Title,K=[],U=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return t.filter((function(e){return null===n?!e.parent_id:e.parent_id===n})).map((function(n){return m()(m()({},n),{},{children:e(t,n.id)})}))},X=function(){var e=G().styles,t=v.Z.useForm(),n=x()(t,1)[0],r=(0,h.useState)([]),s=x()(r,2),l=(s[0],s[1],(0,h.useState)([])),c=x()(l,2),p=c[0],u=c[1],j=(0,h.useState)(!1),H=x()(j,2),W=H[0],M=H[1],X=v.Z.useForm(),$=x()(X,1)[0],ee=(0,h.useState)(null),te=x()(ee,2),ne=te[0],re=te[1],ie=(0,h.useState)(!1),se=x()(ie,2),oe=se[0],le=se[1],ae=(0,h.useState)(null),ce=x()(ae,2),de=ce[0],pe=ce[1],xe=(0,h.useState)(null),ue=x()(xe,2),me=ue[0],he=ue[1],fe=(0,h.useState)(""),ge=x()(fe,2),je=ge[0],ve=ge[1],ye=(0,h.useState)([]),Ze=x()(ye,2),be=Ze[0],_e=Ze[1],Se=(0,h.useState)([]),ke=x()(Se,2),we=ke[0],Ce=ke[1],Be=(0,h.useState)(null),Pe=x()(Be,2),Ie=Pe[0],Ne=Pe[1],ze=(0,h.useState)(null),Te=x()(ze,2),Re=Te[0],qe=Te[1],Le=(0,h.useState)(!1),Ae=x()(Le,2),Fe=Ae[0],De=Ae[1],He=(0,h.useState)(!1),Ee=x()(He,2),We=Ee[0],Ye=Ee[1],Me=(0,h.useState)([]),Ge=x()(Me,2),Oe=Ge[0],Je=Ge[1],Qe=(0,h.useState)(0),Ve=x()(Qe,2),Ke=Ve[0],Ue=Ve[1],Xe=(0,h.useState)(!1),$e=x()(Xe,2),et=$e[0],tt=$e[1],nt=(0,h.useState)({current:1,pageSize:10}),rt=x()(nt,2),it=rt[0],st=rt[1],ot=function(){je&&!be.includes(je)&&(_e([].concat(d()(be),[je])),ve(""))},lt=function(){je&&!we.includes(je)&&(Ce([].concat(d()(we),[je])),ve(""))},at=function(){pe(null),le(!1),Ce([]),Ye(!1),$.resetFields(),M(!0)},ct=function(){var e=a()(i()().mark((function e(){var t,n,r,s,l,a,c,x;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,$.validateFields();case 3:if(t=e.sent,!oe||!ne){e.next=11;break}n=function e(n){return n.map((function(n){return n.id===ne.id?m()(m()({},n),{},{title:t.title,section_type:t.section_type,tags:we,prompt_text:t.prompt_text,data_requirements:t.data_requirements,content_template:t.content_template}):(n.children&&(n.children=e(n.children)),n)}))}(p),u(n),y.ZP.success("章节已更新"),e.next=30;break;case 11:if(r="section-".concat(Date.now()),s=1,de&&(l=function e(t,n){var r,i=o()(t);try{for(i.s();!(r=i.n()).done;){var s=r.value;if(s.id===n)return s;if(s.children){var l=e(s.children,n);if(l)return l}}}catch(e){i.e(e)}finally{i.f()}return null}(U(p),de))&&(s=l.level+1),a={id:r,title:t.title,section_type:t.section_type,tags:we,section_order:p.length+1,parent_id:de||void 0,level:s,prompt_text:t.prompt_text,data_requirements:t.data_requirements,content_template:t.content_template,children:[]},u([].concat(d()(p),[a])),y.ZP.success("章节已添加到模板"),!We){e.next=30;break}return e.prev=18,c={title:t.title,section_type:t.section_type,tags:we,prompt_text:t.prompt_text,data_requirements:t.data_requirements,content_template:t.content_template,parent_id:null,level:1},e.next=22,Y(c);case 22:(x=e.sent)&&x.success?y.ZP.success('章节 "'.concat(t.title,'" 已同时保存到库')):y.ZP.error('章节 "'.concat(t.title,'" 保存到库失败: ').concat((null==x?void 0:x.message)||"未知错误")),e.next=30;break;case 26:e.prev=26,e.t0=e.catch(18),console.error("保存到章节库失败:",e.t0),y.ZP.error('章节 "'.concat(t.title,'" 保存到库时发生错误'));case 30:M(!1),Ye(!1),e.next=37;break;case 34:e.prev=34,e.t1=e.catch(0),console.log("Failed:",e.t1);case 37:case"end":return e.stop()}}),e,null,[[0,34],[18,26]])})));return function(){return e.apply(this,arguments)}}(),dt=function(e){e.length>0?he(e[0]):he(null)},pt=function(){var e=a()(i()().mark((function e(){var t,n,r,s=arguments;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=s.length>0&&void 0!==s[0]?s[0]:1,n=s.length>1&&void 0!==s[1]?s[1]:10,tt(!0),e.prev=3,e.next=6,E({current:t,pageSize:n});case 6:(r=e.sent).success?(Je(r.data),Ue(r.total),st({current:r.current,pageSize:r.pageSize})):y.ZP.error("获取模板章节列表失败"),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(3),console.error("获取模板章节列表出错:",e.t0),y.ZP.error("获取模板章节列表失败");case 14:return e.prev=14,tt(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[3,10,14,17]])})));return function(){return e.apply(this,arguments)}}();(0,h.useEffect)((function(){Fe&&pt(it.current,it.pageSize)}),[Fe]);var xt=function(){pe(null),De(!0)},ut=function(){De(!1),Ne(null),qe(null)},mt=function(){if(Re){var e="section-".concat(Date.now()),t=m()(m()({},Re),{},{id:e,section_order:p.length+1,parent_id:de||void 0});u([].concat(d()(p),[t])),y.ZP.success('已添加"'.concat(Re.title,'"章节')),ut()}},ht=function t(n){return n.map((function(n){var r=(0,O.jsxs)("div",{className:"".concat(e.nodeTitle," ").concat(e.nodeItem),children:[(0,O.jsx)("span",{children:n.title}),(0,O.jsx)("span",{className:"".concat(e.nodeActions," node-actions"),children:(0,O.jsxs)(Z.Z,{children:[(0,O.jsx)(b.ZP,{type:"text",size:"small",icon:(0,O.jsx)(z.Z,{}),onClick:function(e){var t;e.stopPropagation(),t=n.id,pe(t),le(!1),Ce([]),Ye(!1),$.resetFields(),M(!0)},title:"添加子章节"}),(0,O.jsx)(b.ZP,{type:"text",size:"small",icon:(0,O.jsx)(T.Z,{}),onClick:function(e){e.stopPropagation(),function(e,t){t.stopPropagation(),pe(e),De(!0)}(n.id,e)},title:"从模板库添加子章节"})]})})]});return n.children&&n.children.length>0?{title:r,key:n.id,children:t(n.children)}:{title:r,key:n.id}}))},ft=function(){if(!me)return(0,O.jsxs)("div",{className:e.noSectionSelected,children:[(0,O.jsx)(T.Z,{style:{fontSize:"48px",color:"#d9d9d9",marginBottom:"16px"}}),(0,O.jsx)(Q,{type:"secondary",children:"请选择一个章节查看详情"})]});var t=p.find((function(e){return e.id===me}));return t?(0,O.jsxs)("div",{children:[(0,O.jsxs)("div",{className:e.sectionHeader,children:[(0,O.jsx)(V,{level:4,children:t.title}),(0,O.jsxs)(Z.Z,{children:[(0,O.jsx)(b.ZP,{icon:(0,O.jsx)(R.Z,{}),onClick:function(){return function(e){re(e),le(!0),Ce(e.tags||[]),$.setFieldsValue({title:e.title,section_type:e.section_type,prompt_text:e.prompt_text,data_requirements:e.data_requirements,content_template:e.content_template}),M(!0)}(t)},children:"编辑"}),(0,O.jsx)(_.Z,{title:"确定要删除此章节吗？",description:"删除后将无法恢复，且会删除所有子章节",onConfirm:function(){return e=t.id,n=function e(t,n){return t.filter((function(t){return t.id!==n&&(t.children&&(t.children=e(t.children,n)),!0)}))}(p,e),u(n),me===e&&he(null),void y.ZP.success("章节已删除");var e,n},okText:"确定",cancelText:"取消",children:(0,O.jsx)(b.ZP,{danger:!0,icon:(0,O.jsx)(q.Z,{}),children:"删除"})})]})]}),(0,O.jsx)(S.Z,{}),t.section_type&&(0,O.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,O.jsx)(Q,{strong:!0,children:"章节类型："}),(0,O.jsx)(Q,{children:t.section_type})]}),t.tags&&t.tags.length>0&&(0,O.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,O.jsx)(Q,{strong:!0,children:"标签："}),(0,O.jsx)("div",{style:{marginTop:"8px"},children:t.tags.map((function(e){return(0,O.jsx)(k.Z,{color:"blue",children:e},e)}))})]}),t.prompt_text&&(0,O.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,O.jsx)(Q,{strong:!0,children:"提示词："}),(0,O.jsx)("div",{className:e.contentBox,children:(0,O.jsx)(Q,{children:t.prompt_text})})]}),t.data_requirements&&(0,O.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,O.jsx)(Q,{strong:!0,children:"数据需求："}),(0,O.jsx)("div",{className:e.contentBox,children:(0,O.jsx)(Q,{children:t.data_requirements})})]}),t.content_template&&(0,O.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,O.jsx)(Q,{strong:!0,children:"内容模板："}),(0,O.jsx)("div",{className:e.contentBox,children:(0,O.jsx)(Q,{children:t.content_template})})]})]}):null},gt=function(){var e=a()(i()().mark((function e(){var t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,n.validateFields();case 3:t=e.sent,r=m()(m()({},t),{},{tags:be,sections:p}),console.log("提交模板数据:",r),y.ZP.success("模板创建成功"),D.history.push("/preLoanAssistant/reportTemplateManagement"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.log("Failed:",e.t0);case 13:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(){return e.apply(this,arguments)}}(),jt=function(){D.history.push("/preLoanAssistant/reportTemplateManagement")},vt=U(p);return(0,O.jsxs)(f._z,{title:"创建报告模板",onBack:jt,backIcon:(0,O.jsx)(A.Z,{}),extra:[(0,O.jsx)(b.ZP,{onClick:jt,style:{marginRight:4},children:"取消"},"cancel"),(0,O.jsx)(b.ZP,{type:"primary",icon:(0,O.jsx)(F.Z,{}),onClick:gt,children:"保存模板"},"save")],children:[(0,O.jsx)("div",{style:{padding:"0",display:"flex",gap:"8px"},children:(0,O.jsxs)(O.Fragment,{children:[(0,O.jsx)("div",{className:e.leftPanel,children:(0,O.jsxs)("div",{className:e.infoCard,children:[(0,O.jsx)("h3",{style:{fontSize:"16px",fontWeight:500,margin:"0 0 12px 0",paddingBottom:"8px",borderBottom:"1px solid #f0f0f0"},children:"基本信息"}),(0,O.jsxs)(v.Z,{form:n,layout:"vertical",size:"small",colon:!1,children:[(0,O.jsx)(v.Z.Item,{name:"title",label:"模板名称",rules:[{required:!0,message:"请输入模板名称"}],children:(0,O.jsx)(g.Z,{placeholder:"请输入模板名称"})}),(0,O.jsx)(v.Z.Item,{name:"description",label:"模板描述",children:(0,O.jsx)(J,{placeholder:"请输入模板描述",autoSize:{minRows:2,maxRows:4}})}),(0,O.jsx)(v.Z.Item,{name:"template_type",label:"模板类型",children:(0,O.jsx)(g.Z,{placeholder:"请输入模板类型，如：贷前调查、授信审批等"})}),(0,O.jsxs)(v.Z.Item,{label:"标签",children:[(0,O.jsx)("div",{className:e.tagsContainer,children:be.map((function(e){return(0,O.jsx)(k.Z,{closable:!0,onClose:function(){return t=e,n=be.filter((function(e){return e!==t})),void _e(n);var t,n},style:{marginBottom:"6px"},children:e},e)}))}),(0,O.jsx)(g.Z,{placeholder:"输入标签后按回车添加",value:je,onChange:function(e){return ve(e.target.value)},onPressEnter:ot,onBlur:ot,className:e.tagInput})]})]})]})}),(0,O.jsx)("div",{className:e.rightPanel,children:(0,O.jsxs)("div",{className:e.sectionCard,children:[(0,O.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",margin:"0 0 12px 0",paddingBottom:"8px",borderBottom:"1px solid #f0f0f0"},children:[(0,O.jsx)("h3",{style:{fontSize:"16px",fontWeight:500,margin:0},children:"章节管理"}),(0,O.jsxs)(Z.Z,{children:[(0,O.jsx)(b.ZP,{type:"primary",icon:(0,O.jsx)(z.Z,{}),size:"small",onClick:at,children:"添加章节"}),(0,O.jsx)(b.ZP,{onClick:xt,icon:(0,O.jsx)(T.Z,{}),size:"small",children:"从模板库添加"})]})]}),(0,O.jsxs)(w.Z,{gutter:16,style:{flexGrow:1,minHeight:0},children:[(0,O.jsx)(C.Z,{span:10,children:(0,O.jsx)("div",{className:e.sectionList,children:p.length>0?(0,O.jsx)(B.Z,{showLine:{showLeafIcon:!1},onSelect:dt,treeData:ht(vt),blockNode:!0,defaultExpandAll:!0}):(0,O.jsx)("div",{style:{textAlign:"center",padding:"15px 0"},children:(0,O.jsx)(Q,{type:"secondary",children:"暂无章节，请点击添加章节或从模板选择"})})})}),(0,O.jsx)(C.Z,{span:14,children:(0,O.jsx)("div",{className:e.sectionDetail,children:ft()})})]})]})})]})}),(0,O.jsx)(P.Z,{title:oe?"编辑章节":"添加章节",open:W,onCancel:function(){return M(!1)},onOk:ct,width:700,children:(0,O.jsxs)(v.Z,{form:$,layout:"vertical",size:"small",colon:!1,children:[(0,O.jsx)(v.Z.Item,{name:"title",label:"章节标题",rules:[{required:!0,message:"请输入章节标题"}],children:(0,O.jsx)(g.Z,{placeholder:"请输入章节标题"})}),!oe&&(0,O.jsx)(v.Z.Item,{label:"同时保存到章节库",name:"saveToLibrarySwitch",children:(0,O.jsx)(I.Z,{checked:We,onChange:Ye})}),(0,O.jsx)(v.Z.Item,{name:"section_type",label:"章节类型",children:(0,O.jsx)(g.Z,{placeholder:"请输入章节类型，如：概述、财务分析、风险评估等"})}),(0,O.jsxs)(v.Z.Item,{label:"标签",children:[(0,O.jsx)("div",{className:e.tagsContainer,children:we.map((function(e){return(0,O.jsx)(k.Z,{closable:!0,onClose:function(){return t=e,n=we.filter((function(e){return e!==t})),void Ce(n);var t,n},style:{marginBottom:"6px"},children:e},e)}))}),(0,O.jsx)(g.Z,{placeholder:"输入标签后按回车添加",value:je,onChange:function(e){return ve(e.target.value)},onPressEnter:lt,onBlur:lt,className:e.tagInput})]}),(0,O.jsx)(v.Z.Item,{name:"prompt_text",label:"提示词",children:(0,O.jsx)(J,{placeholder:"请输入生成内容的提示词",autoSize:{minRows:2,maxRows:4}})}),(0,O.jsx)(v.Z.Item,{name:"data_requirements",label:"数据需求",children:(0,O.jsx)(J,{placeholder:"请输入该章节所需的数据要求",autoSize:{minRows:2,maxRows:4}})}),(0,O.jsx)(v.Z.Item,{name:"content_template",label:"内容模板",children:(0,O.jsx)(J,{placeholder:"请输入内容模板",autoSize:{minRows:2,maxRows:4}})})]})}),(0,O.jsx)(P.Z,{title:"模板章节库",open:Fe,onCancel:ut,width:1200,bodyStyle:{padding:"12px 16px"},footer:[(0,O.jsx)(b.ZP,{onClick:ut,children:"关闭"},"cancel"),(0,O.jsx)(b.ZP,{type:"primary",disabled:!Re,onClick:mt,children:"添加到我的章节"},"add")],children:(0,O.jsxs)("div",{style:{display:"flex",height:"560px"},children:[(0,O.jsx)("div",{style:{width:"50%",paddingRight:"12px",overflowY:"auto"},children:(0,O.jsx)(N.Z,{loading:et,grid:{gutter:16,column:2},dataSource:Oe.length>0?Oe:K,pagination:{total:Ke||K.length,current:it.current,pageSize:it.pageSize,onChange:function(e,t){pt(e,t)},showSizeChanger:!0,showQuickJumper:!0},renderItem:function(t){var n=Ie===t.id;return(0,O.jsx)(N.Z.Item,{className:"".concat(e.templateItem," ").concat(n?e.selectedTemplate:""),onClick:function(){return function(e){console.log("选中章节:",e),Ne(e);var t=(Oe.length>0?Oe:K).find((function(t){return t.id===e}));console.log("找到章节:",t),qe(t||null)}(t.id)},style:{border:n?"1px solid #1890ff":"1px solid #e0e0e0",padding:"12px",backgroundColor:n?"#e6f7ff":"#fff",color:n?"#1890ff":"inherit",borderRadius:"4px",transition:"all 0.3s ease",boxShadow:n?"0 2px 8px rgba(24, 144, 255, 0.15)":"none"},children:(0,O.jsx)(N.Z.Item.Meta,{title:(0,O.jsx)("span",{style:{color:n?"#1890ff":"inherit",fontWeight:n?500:400},children:t.title}),description:(0,O.jsx)(Z.Z,{children:(0,O.jsx)(k.Z,{children:t.section_type})})})})}})}),(0,O.jsx)("div",{style:{width:"50%",paddingLeft:"12px",borderLeft:"1px solid #f0f0f0",overflowY:"auto"},children:(console.log("当前选中的章节详情:",Re),Re?(0,O.jsxs)("div",{style:{padding:"8px"},children:[(0,O.jsxs)("div",{className:e.sectionHeader,children:[(0,O.jsx)(V,{level:4,children:Re.title}),(0,O.jsx)(b.ZP,{type:"primary",icon:(0,O.jsx)(L.Z,{}),onClick:mt,children:"添加到我的章节"})]}),(0,O.jsx)(S.Z,{}),Re.section_type&&(0,O.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,O.jsx)(Q,{strong:!0,children:"章节类型："}),(0,O.jsx)(Q,{children:Re.section_type})]}),Re.tags&&Re.tags.length>0&&(0,O.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,O.jsx)(Q,{strong:!0,children:"标签："}),(0,O.jsx)("div",{style:{marginTop:"8px"},children:Re.tags.map((function(e){return(0,O.jsx)(k.Z,{color:"blue",children:e},e)}))})]}),Re.prompt_text&&(0,O.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,O.jsx)(Q,{strong:!0,children:"提示词："}),(0,O.jsx)("div",{className:e.contentBox,children:(0,O.jsx)(Q,{children:Re.prompt_text})})]}),Re.data_requirements&&(0,O.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,O.jsx)(Q,{strong:!0,children:"数据需求："}),(0,O.jsx)("div",{className:e.contentBox,children:(0,O.jsx)(Q,{children:Re.data_requirements})})]}),Re.content_template&&(0,O.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,O.jsx)(Q,{strong:!0,children:"内容模板："}),(0,O.jsx)("div",{className:e.contentBox,children:(0,O.jsx)(Q,{children:Re.content_template})})]})]}):(0,O.jsxs)("div",{className:e.noSectionSelected,children:[(0,O.jsx)(T.Z,{style:{fontSize:"48px",color:"#d9d9d9",marginBottom:"16px"}}),(0,O.jsx)(Q,{type:"secondary",children:"请选择一个模板章节查看详情"})]}))})]})})]})}}}]);