import subprocess
import json
import os
from typing import Optional, Dict, Any
import asyncio
from modelcontextprotocol import MultiServerMCPClient

class MarkdownConverter:
    def __init__(self, use_docker: bool = False, mount_dir: Optional[str] = None):
        """
        初始化 MarkdownConverter
        
        Args:
            use_docker (bool): 是否使用 Docker 运行
            mount_dir (Optional[str]): 如果需要挂载本地目录，指定目录路径
        """
        self.use_docker = use_docker
        self.mount_dir = mount_dir
        self._process = None
        
    async def start_server(self, port: int = 3001) -> None:
        """
        启动 markitdown-mcp 服务器
        
        Args:
            port (int): SSE 服务器端口
        """
        if self.use_docker:
            cmd = ["docker", "run", "--rm", "-i"]
            if self.mount_dir:
                cmd.extend(["-v", f"{self.mount_dir}:/workdir"])
            cmd.extend(["-p", f"{port}:{port}"])
            cmd.extend(["markitdown-mcp:latest", "--sse", "--host", "0.0.0.0", "--port", str(port)])
        else:
            cmd = ["markitdown-mcp", "--sse", "--host", "0.0.0.0", "--port", str(port)]
            
        self._process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        # 等待服务器启动
        await asyncio.sleep(2)
        
    async def stop_server(self) -> None:
        """停止 markitdown-mcp 服务器"""
        if self._process:
            self._process.terminate()
            await self._process.wait()
            
    async def convert_to_markdown(self, uri: str) -> str:
        """
        将指定 URI 的内容转换为 Markdown 格式
        
        Args:
            uri (str): 要转换的 URI，可以是 http、https、file 或 data URI
            
        Returns:
            str: 转换后的 Markdown 内容
        """
        try:
            # 准备输入数据
            input_data = {
                "tool": "convert_to_markdown",
                "parameters": {
                    "uri": uri
                }
            }
            
            # 使用 MultiServerMCPClient 调用服务
            async with MultiServerMCPClient({
                "markitdown": {
                    "url": "http://localhost:3001/sse",
                    "transport": "sse"
                }
            }) as client:
                tools = client.get_tools()
                result = await tools["markitdown.convert_to_markdown"].invoke(uri)
                return result
                
        except Exception as e:
            raise Exception(f"执行转换时出错: {str(e)}")

# 使用示例
async def main():
    # 创建转换器实例
    converter = MarkdownConverter(use_docker=True, mount_dir="/path/to/your/data")
    
    try:
        # 启动服务器
        await converter.start_server()
        
        # 示例：转换本地文件
        result = await converter.convert_to_markdown("file:///workdir/example.txt")
        print("转换结果:")
        print(result)
        
    except Exception as e:
        print(f"错误: {str(e)}")
    finally:
        # 停止服务器
        await converter.stop_server()

if __name__ == "__main__":
    asyncio.run(main()) 