(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2331],{95985:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880.1 154H143.9c-24.5 0-39.8 26.7-27.5 48L349 597.4V838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V597.4L907.7 202c12.2-21.3-3.1-48-27.6-48zM603.4 798H420.6V642h182.9v156zm9.6-236.6l-9.5 16.6h-183l-9.5-16.6L212.7 226h598.6L613 561.4z"}}]},name:"filter",theme:"outlined"}},42110:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"}},82947:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"}},52197:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"}}]},name:"star",theme:"filled"}},76853:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M859.9 780H164.1c-4.5 0-8.1 3.6-8.1 8v60c0 4.4 3.6 8 8.1 8h695.8c4.5 0 8.1-3.6 8.1-8v-60c0-4.4-3.6-8-8.1-8zM505.7 669a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V176c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8z"}}]},name:"vertical-align-bottom",theme:"outlined"}},44039:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M859.9 168H164.1c-4.5 0-8.1 3.6-8.1 8v60c0 4.4 3.6 8 8.1 8h695.8c4.5 0 8.1-3.6 8.1-8v-60c0-4.4-3.6-8-8.1-8zM518.3 355a8 8 0 00-12.6 0l-112 141.7a7.98 7.98 0 006.3 12.9h73.9V848c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V509.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 355z"}}]},name:"vertical-align-top",theme:"outlined"}},82061:function(e,t,n){"use strict";var r=n(1413),o=n(67294),a=n(47046),i=n(91146),l=function(e,t){return o.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a.Z}))},c=o.forwardRef(l);t.Z=c},47389:function(e,t,n){"use strict";var r=n(1413),o=n(67294),a=n(27363),i=n(91146),l=function(e,t){return o.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a.Z}))},c=o.forwardRef(l);t.Z=c},88310:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(1413),o=n(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M855 160.1l-189.2 23.5c-6.6.8-9.3 8.8-4.7 13.5l54.7 54.7-153.5 153.5a8.03 8.03 0 000 11.3l45.1 45.1c3.1 3.1 8.2 3.1 11.3 0l153.6-153.6 54.7 54.7a7.94 7.94 0 0013.5-4.7L863.9 169a7.9 7.9 0 00-8.9-8.9zM416.6 562.3a8.03 8.03 0 00-11.3 0L251.8 715.9l-54.7-54.7a7.94 7.94 0 00-13.5 4.7L160.1 855c-.6 5.2 3.7 9.5 8.9 8.9l189.2-23.5c6.6-.8 9.3-8.8 4.7-13.5l-54.7-54.7 153.6-153.6c3.1-3.1 3.1-8.2 0-11.3l-45.2-45z"}}]},name:"expand-alt",theme:"outlined"},i=n(91146),l=function(e,t){return o.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a}))};var c=o.forwardRef(l)},19669:function(e,t,n){"use strict";var r=n(1413),o=n(67294),a=n(95985),i=n(91146),l=function(e,t){return o.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a.Z}))},c=o.forwardRef(l);t.Z=c},12906:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(1413),o=n(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM512 533c-85.5 0-155.6 67.3-160 151.6a8 8 0 008 8.4h48.1c4.2 0 7.8-3.2 8.1-7.4C420 636.1 461.5 597 512 597s92.1 39.1 95.8 88.6c.3 4.2 3.9 7.4 8.1 7.4H664a8 8 0 008-8.4C667.6 600.3 597.5 533 512 533z"}}]},name:"frown",theme:"outlined"},i=n(91146),l=function(e,t){return o.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a}))};var c=o.forwardRef(l)},43471:function(e,t,n){"use strict";var r=n(1413),o=n(67294),a=n(82947),i=n(91146),l=function(e,t){return o.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a.Z}))},c=o.forwardRef(l);t.Z=c},17598:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(1413),o=n(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M881.7 187.4l-45.1-45.1a8.03 8.03 0 00-11.3 0L667.8 299.9l-54.7-54.7a7.94 7.94 0 00-13.5 4.7L576.1 439c-.6 5.2 3.7 9.5 8.9 8.9l189.2-23.5c6.6-.8 9.3-8.8 4.7-13.5l-54.7-54.7 157.6-157.6c3-3 3-8.1-.1-11.2zM439 576.1l-189.2 23.5c-6.6.8-9.3 8.9-4.7 13.5l54.7 54.7-157.5 157.5a8.03 8.03 0 000 11.3l45.1 45.1c3.1 3.1 8.2 3.1 11.3 0l157.6-157.6 54.7 54.7a7.94 7.94 0 0013.5-4.7L447.9 585a7.9 7.9 0 00-8.9-8.9z"}}]},name:"shrink",theme:"outlined"},i=n(91146),l=function(e,t){return o.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a}))};var c=o.forwardRef(l)},25820:function(e,t,n){"use strict";var r=n(1413),o=n(67294),a=n(52197),i=n(91146),l=function(e,t){return o.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a.Z}))},c=o.forwardRef(l);t.Z=c},75750:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(1413),o=n(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},i=n(91146),l=function(e,t){return o.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a}))};var c=o.forwardRef(l)},87784:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(1413),o=n(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"},i=n(91146),l=function(e,t){return o.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a}))};var c=o.forwardRef(l)},19050:function(e,t,n){"use strict";var r=n(1413),o=n(67294),a=n(76853),i=n(91146),l=function(e,t){return o.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a.Z}))},c=o.forwardRef(l);t.Z=c},15525:function(e,t,n){"use strict";var r=n(1413),o=n(67294),a=n(44039),i=n(91146),l=function(e,t){return o.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a.Z}))},c=o.forwardRef(l);t.Z=c},64599:function(e,t,n){var r=n(96263);e.exports=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=r(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var o=0,a=function(){};return{s:a,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,l=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return l=e.done,e},e:function(e){c=!0,i=e},f:function(){try{l||null==n.return||n.return()}finally{if(c)throw i}}}},e.exports.__esModule=!0,e.exports.default=e.exports},22424:function(e,t,n){"use strict";n.d(t,{Z:function(){return ce}});const{entries:r,setPrototypeOf:o,isFrozen:a,getPrototypeOf:i,getOwnPropertyDescriptor:l}=Object;let{freeze:c,seal:s,create:u}=Object,{apply:f,construct:m}="undefined"!=typeof Reflect&&Reflect;c||(c=function(e){return e}),s||(s=function(e){return e}),f||(f=function(e,t,n){return e.apply(t,n)}),m||(m=function(e,t){return new e(...t)});const p=L(Array.prototype.forEach),d=L(Array.prototype.lastIndexOf),h=L(Array.prototype.pop),g=L(Array.prototype.push),T=L(Array.prototype.splice),y=L(String.prototype.toLowerCase),E=L(String.prototype.toString),A=L(String.prototype.match),b=L(String.prototype.replace),v=L(String.prototype.indexOf),_=L(String.prototype.trim),S=L(Object.prototype.hasOwnProperty),w=L(RegExp.prototype.test),N=(R=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return m(R,t)});var R;function L(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return f(e,t,r)}}function x(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:y;o&&o(e,null);let r=t.length;for(;r--;){let o=t[r];if("string"==typeof o){const e=n(o);e!==o&&(a(t)||(t[r]=e),o=e)}e[o]=!0}return e}function C(e){for(let t=0;t<e.length;t++){S(e,t)||(e[t]=null)}return e}function M(e){const t=u(null);for(const[n,o]of r(e)){S(e,n)&&(Array.isArray(o)?t[n]=C(o):o&&"object"==typeof o&&o.constructor===Object?t[n]=M(o):t[n]=o)}return t}function O(e,t){for(;null!==e;){const n=l(e,t);if(n){if(n.get)return L(n.get);if("function"==typeof n.value)return L(n.value)}e=i(e)}return function(){return null}}const D=c(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),k=c(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),I=c(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),z=c(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Z=c(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),U=c(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),P=c(["#text"]),H=c(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),F=c(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),B=c(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),W=c(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),G=s(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Y=s(/<%[\w\W]*|[\w\W]*%>/gm),j=s(/\$\{[\w\W]*/gm),q=s(/^data-[\-\w.\u00B7-\uFFFF]+$/),V=s(/^aria-[\-\w]+$/),X=s(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),$=s(/^(?:\w+script|data):/i),K=s(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),J=s(/^html$/i),Q=s(/^[a-z][.\w]*(-[.\w]+)+$/i);var ee=Object.freeze({__proto__:null,ARIA_ATTR:V,ATTR_WHITESPACE:K,CUSTOM_ELEMENT:Q,DATA_ATTR:q,DOCTYPE_NAME:J,ERB_EXPR:Y,IS_ALLOWED_URI:X,IS_SCRIPT_OR_DATA:$,MUSTACHE_EXPR:G,TMPLIT_EXPR:j});const te=1,ne=3,re=7,oe=8,ae=9,ie=function(){return"undefined"==typeof window?null:window},le=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const r="data-tt-policy-suffix";t&&t.hasAttribute(r)&&(n=t.getAttribute(r));const o="dompurify"+(n?"#"+n:"");try{return e.createPolicy(o,{createHTML(e){return e},createScriptURL(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+o+" could not be created."),null}};var ce=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ie();const n=t=>e(t);if(n.version="3.2.5",n.removed=[],!t||!t.document||t.document.nodeType!==ae||!t.Element)return n.isSupported=!1,n;let{document:o}=t;const a=o,i=a.currentScript,{DocumentFragment:l,HTMLTemplateElement:s,Node:f,Element:m,NodeFilter:R,NamedNodeMap:L=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:C,DOMParser:G,trustedTypes:Y}=t,j=m.prototype,q=O(j,"cloneNode"),V=O(j,"remove"),$=O(j,"nextSibling"),K=O(j,"childNodes"),Q=O(j,"parentNode");if("function"==typeof s){const e=o.createElement("template");e.content&&e.content.ownerDocument&&(o=e.content.ownerDocument)}let ce,se="";const{implementation:ue,createNodeIterator:fe,createDocumentFragment:me,getElementsByTagName:pe}=o,{importNode:de}=a;let he={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};n.isSupported="function"==typeof r&&"function"==typeof Q&&ue&&void 0!==ue.createHTMLDocument;const{MUSTACHE_EXPR:ge,ERB_EXPR:Te,TMPLIT_EXPR:ye,DATA_ATTR:Ee,ARIA_ATTR:Ae,IS_SCRIPT_OR_DATA:be,ATTR_WHITESPACE:ve,CUSTOM_ELEMENT:_e}=ee;let{IS_ALLOWED_URI:Se}=ee,we=null;const Ne=x({},[...D,...k,...I,...Z,...P]);let Re=null;const Le=x({},[...H,...F,...B,...W]);let xe=Object.seal(u(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Ce=null,Me=null,Oe=!0,De=!0,ke=!1,Ie=!0,ze=!1,Ze=!0,Ue=!1,Pe=!1,He=!1,Fe=!1,Be=!1,We=!1,Ge=!0,Ye=!1;const je="user-content-";let qe=!0,Ve=!1,Xe={},$e=null;const Ke=x({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Je=null;const Qe=x({},["audio","video","img","source","image","track"]);let et=null;const tt=x({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),nt="http://www.w3.org/1998/Math/MathML",rt="http://www.w3.org/2000/svg",ot="http://www.w3.org/1999/xhtml";let at=ot,it=!1,lt=null;const ct=x({},[nt,rt,ot],E);let st=x({},["mi","mo","mn","ms","mtext"]),ut=x({},["annotation-xml"]);const ft=x({},["title","style","font","a","script"]);let mt=null;const pt=["application/xhtml+xml","text/html"],dt="text/html";let ht=null,gt=null;const Tt=o.createElement("form"),yt=function(e){return e instanceof RegExp||e instanceof Function},Et=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!gt||gt!==e){if(e&&"object"==typeof e||(e={}),e=M(e),mt=-1===pt.indexOf(e.PARSER_MEDIA_TYPE)?dt:e.PARSER_MEDIA_TYPE,ht="application/xhtml+xml"===mt?E:y,we=S(e,"ALLOWED_TAGS")?x({},e.ALLOWED_TAGS,ht):Ne,Re=S(e,"ALLOWED_ATTR")?x({},e.ALLOWED_ATTR,ht):Le,lt=S(e,"ALLOWED_NAMESPACES")?x({},e.ALLOWED_NAMESPACES,E):ct,et=S(e,"ADD_URI_SAFE_ATTR")?x(M(tt),e.ADD_URI_SAFE_ATTR,ht):tt,Je=S(e,"ADD_DATA_URI_TAGS")?x(M(Qe),e.ADD_DATA_URI_TAGS,ht):Qe,$e=S(e,"FORBID_CONTENTS")?x({},e.FORBID_CONTENTS,ht):Ke,Ce=S(e,"FORBID_TAGS")?x({},e.FORBID_TAGS,ht):{},Me=S(e,"FORBID_ATTR")?x({},e.FORBID_ATTR,ht):{},Xe=!!S(e,"USE_PROFILES")&&e.USE_PROFILES,Oe=!1!==e.ALLOW_ARIA_ATTR,De=!1!==e.ALLOW_DATA_ATTR,ke=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Ie=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,ze=e.SAFE_FOR_TEMPLATES||!1,Ze=!1!==e.SAFE_FOR_XML,Ue=e.WHOLE_DOCUMENT||!1,Fe=e.RETURN_DOM||!1,Be=e.RETURN_DOM_FRAGMENT||!1,We=e.RETURN_TRUSTED_TYPE||!1,He=e.FORCE_BODY||!1,Ge=!1!==e.SANITIZE_DOM,Ye=e.SANITIZE_NAMED_PROPS||!1,qe=!1!==e.KEEP_CONTENT,Ve=e.IN_PLACE||!1,Se=e.ALLOWED_URI_REGEXP||X,at=e.NAMESPACE||ot,st=e.MATHML_TEXT_INTEGRATION_POINTS||st,ut=e.HTML_INTEGRATION_POINTS||ut,xe=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&yt(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(xe.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&yt(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(xe.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(xe.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),ze&&(De=!1),Be&&(Fe=!0),Xe&&(we=x({},P),Re=[],!0===Xe.html&&(x(we,D),x(Re,H)),!0===Xe.svg&&(x(we,k),x(Re,F),x(Re,W)),!0===Xe.svgFilters&&(x(we,I),x(Re,F),x(Re,W)),!0===Xe.mathMl&&(x(we,Z),x(Re,B),x(Re,W))),e.ADD_TAGS&&(we===Ne&&(we=M(we)),x(we,e.ADD_TAGS,ht)),e.ADD_ATTR&&(Re===Le&&(Re=M(Re)),x(Re,e.ADD_ATTR,ht)),e.ADD_URI_SAFE_ATTR&&x(et,e.ADD_URI_SAFE_ATTR,ht),e.FORBID_CONTENTS&&($e===Ke&&($e=M($e)),x($e,e.FORBID_CONTENTS,ht)),qe&&(we["#text"]=!0),Ue&&x(we,["html","head","body"]),we.table&&(x(we,["tbody"]),delete Ce.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw N('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw N('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ce=e.TRUSTED_TYPES_POLICY,se=ce.createHTML("")}else void 0===ce&&(ce=le(Y,i)),null!==ce&&"string"==typeof se&&(se=ce.createHTML(""));c&&c(e),gt=e}},At=x({},[...k,...I,...z]),bt=x({},[...Z,...U]),vt=function(e){let t=Q(e);t&&t.tagName||(t={namespaceURI:at,tagName:"template"});const n=y(e.tagName),r=y(t.tagName);return!!lt[e.namespaceURI]&&(e.namespaceURI===rt?t.namespaceURI===ot?"svg"===n:t.namespaceURI===nt?"svg"===n&&("annotation-xml"===r||st[r]):Boolean(At[n]):e.namespaceURI===nt?t.namespaceURI===ot?"math"===n:t.namespaceURI===rt?"math"===n&&ut[r]:Boolean(bt[n]):e.namespaceURI===ot?!(t.namespaceURI===rt&&!ut[r])&&(!(t.namespaceURI===nt&&!st[r])&&(!bt[n]&&(ft[n]||!At[n]))):!("application/xhtml+xml"!==mt||!lt[e.namespaceURI]))},_t=function(e){g(n.removed,{element:e});try{Q(e).removeChild(e)}catch(t){V(e)}},St=function(e,t){try{g(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){g(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(Fe||Be)try{_t(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},wt=function(e){let t=null,n=null;if(He)e="<remove></remove>"+e;else{const t=A(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===mt&&at===ot&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const r=ce?ce.createHTML(e):e;if(at===ot)try{t=(new G).parseFromString(r,mt)}catch(e){}if(!t||!t.documentElement){t=ue.createDocument(at,"template",null);try{t.documentElement.innerHTML=it?se:r}catch(e){}}const a=t.body||t.documentElement;return e&&n&&a.insertBefore(o.createTextNode(n),a.childNodes[0]||null),at===ot?pe.call(t,Ue?"html":"body")[0]:Ue?t.documentElement:a},Nt=function(e){return fe.call(e.ownerDocument||e,e,R.SHOW_ELEMENT|R.SHOW_COMMENT|R.SHOW_TEXT|R.SHOW_PROCESSING_INSTRUCTION|R.SHOW_CDATA_SECTION,null)},Rt=function(e){return e instanceof C&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof L)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Lt=function(e){return"function"==typeof f&&e instanceof f};function xt(e,t,r){p(e,(e=>{e.call(n,t,r,gt)}))}const Ct=function(e){let t=null;if(xt(he.beforeSanitizeElements,e,null),Rt(e))return _t(e),!0;const r=ht(e.nodeName);if(xt(he.uponSanitizeElement,e,{tagName:r,allowedTags:we}),e.hasChildNodes()&&!Lt(e.firstElementChild)&&w(/<[/\w!]/g,e.innerHTML)&&w(/<[/\w!]/g,e.textContent))return _t(e),!0;if(e.nodeType===re)return _t(e),!0;if(Ze&&e.nodeType===oe&&w(/<[/\w]/g,e.data))return _t(e),!0;if(!we[r]||Ce[r]){if(!Ce[r]&&Ot(r)){if(xe.tagNameCheck instanceof RegExp&&w(xe.tagNameCheck,r))return!1;if(xe.tagNameCheck instanceof Function&&xe.tagNameCheck(r))return!1}if(qe&&!$e[r]){const t=Q(e)||e.parentNode,n=K(e)||e.childNodes;if(n&&t){for(let r=n.length-1;r>=0;--r){const o=q(n[r],!0);o.__removalCount=(e.__removalCount||0)+1,t.insertBefore(o,$(e))}}}return _t(e),!0}return e instanceof m&&!vt(e)?(_t(e),!0):"noscript"!==r&&"noembed"!==r&&"noframes"!==r||!w(/<\/no(script|embed|frames)/i,e.innerHTML)?(ze&&e.nodeType===ne&&(t=e.textContent,p([ge,Te,ye],(e=>{t=b(t,e," ")})),e.textContent!==t&&(g(n.removed,{element:e.cloneNode()}),e.textContent=t)),xt(he.afterSanitizeElements,e,null),!1):(_t(e),!0)},Mt=function(e,t,n){if(Ge&&("id"===t||"name"===t)&&(n in o||n in Tt))return!1;if(De&&!Me[t]&&w(Ee,t));else if(Oe&&w(Ae,t));else if(!Re[t]||Me[t]){if(!(Ot(e)&&(xe.tagNameCheck instanceof RegExp&&w(xe.tagNameCheck,e)||xe.tagNameCheck instanceof Function&&xe.tagNameCheck(e))&&(xe.attributeNameCheck instanceof RegExp&&w(xe.attributeNameCheck,t)||xe.attributeNameCheck instanceof Function&&xe.attributeNameCheck(t))||"is"===t&&xe.allowCustomizedBuiltInElements&&(xe.tagNameCheck instanceof RegExp&&w(xe.tagNameCheck,n)||xe.tagNameCheck instanceof Function&&xe.tagNameCheck(n))))return!1}else if(et[t]);else if(w(Se,b(n,ve,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==v(n,"data:")||!Je[e]){if(ke&&!w(be,b(n,ve,"")));else if(n)return!1}else;return!0},Ot=function(e){return"annotation-xml"!==e&&A(e,_e)},Dt=function(e){xt(he.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||Rt(e))return;const r={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Re,forceKeepAttr:void 0};let o=t.length;for(;o--;){const a=t[o],{name:i,namespaceURI:l,value:c}=a,s=ht(i);let u="value"===i?c:_(c);if(r.attrName=s,r.attrValue=u,r.keepAttr=!0,r.forceKeepAttr=void 0,xt(he.uponSanitizeAttribute,e,r),u=r.attrValue,!Ye||"id"!==s&&"name"!==s||(St(i,e),u=je+u),Ze&&w(/((--!?|])>)|<\/(style|title)/i,u)){St(i,e);continue}if(r.forceKeepAttr)continue;if(St(i,e),!r.keepAttr)continue;if(!Ie&&w(/\/>/i,u)){St(i,e);continue}ze&&p([ge,Te,ye],(e=>{u=b(u,e," ")}));const f=ht(e.nodeName);if(Mt(f,s,u)){if(ce&&"object"==typeof Y&&"function"==typeof Y.getAttributeType)if(l);else switch(Y.getAttributeType(f,s)){case"TrustedHTML":u=ce.createHTML(u);break;case"TrustedScriptURL":u=ce.createScriptURL(u)}try{l?e.setAttributeNS(l,i,u):e.setAttribute(i,u),Rt(e)?_t(e):h(n.removed)}catch(e){}}}xt(he.afterSanitizeAttributes,e,null)},kt=function e(t){let n=null;const r=Nt(t);for(xt(he.beforeSanitizeShadowDOM,t,null);n=r.nextNode();)xt(he.uponSanitizeShadowNode,n,null),Ct(n),Dt(n),n.content instanceof l&&e(n.content);xt(he.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=null,o=null,i=null,c=null;if(it=!e,it&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Lt(e)){if("function"!=typeof e.toString)throw N("toString is not a function");if("string"!=typeof(e=e.toString()))throw N("dirty is not a string, aborting")}if(!n.isSupported)return e;if(Pe||Et(t),n.removed=[],"string"==typeof e&&(Ve=!1),Ve){if(e.nodeName){const t=ht(e.nodeName);if(!we[t]||Ce[t])throw N("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof f)r=wt("\x3c!----\x3e"),o=r.ownerDocument.importNode(e,!0),o.nodeType===te&&"BODY"===o.nodeName||"HTML"===o.nodeName?r=o:r.appendChild(o);else{if(!Fe&&!ze&&!Ue&&-1===e.indexOf("<"))return ce&&We?ce.createHTML(e):e;if(r=wt(e),!r)return Fe?null:We?se:""}r&&He&&_t(r.firstChild);const s=Nt(Ve?e:r);for(;i=s.nextNode();)Ct(i),Dt(i),i.content instanceof l&&kt(i.content);if(Ve)return e;if(Fe){if(Be)for(c=me.call(r.ownerDocument);r.firstChild;)c.appendChild(r.firstChild);else c=r;return(Re.shadowroot||Re.shadowrootmode)&&(c=de.call(a,c,!0)),c}let u=Ue?r.outerHTML:r.innerHTML;return Ue&&we["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&w(J,r.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+u),ze&&p([ge,Te,ye],(e=>{u=b(u,e," ")})),ce&&We?ce.createHTML(u):u},n.setConfig=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Et(e),Pe=!0},n.clearConfig=function(){gt=null,Pe=!1},n.isValidAttribute=function(e,t,n){gt||Et({});const r=ht(e),o=ht(t);return Mt(r,o,n)},n.addHook=function(e,t){"function"==typeof t&&g(he[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=d(he[e],t);return-1===n?void 0:T(he[e],n,1)[0]}return h(he[e])},n.removeHooks=function(e){he[e]=[]},n.removeAllHooks=function(){he={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}()}}]);