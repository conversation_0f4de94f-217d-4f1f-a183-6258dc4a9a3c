from mongoengine import Document, <PERSON><PERSON>ield, DateTimeField, BooleanField, IntField, Dict<PERSON><PERSON>
from datetime import datetime
from pydantic import BaseModel, ConfigDict
from typing import Optional, Dict

# MongoEngine 模型
class Role(Document):
    meta = {
        'collection': 'roles'
    }
    id = IntField(primary_key=True)                    # 主键ID
    name = StringField(required=True, unique=True)      # 角色名称
    description = StringField()                         # 角色描述
    created_at = DateTimeField(default=datetime.now)    # 创建时间
    created_by = IntField()                            # 创建者ID
    deletable = BooleanField(default=True)             # 是否可删除
    access = DictField(default={})                     # 权限对象

# Pydantic 模型基类配置
class BaseConfig:
    model_config = ConfigDict(
        from_attributes=True,  # 替换 orm_mode
        protected_namespaces=()  # 禁用保护命名空间
    )

# Pydantic 模型
class RoleBase(BaseModel):
    name: str
    description: Optional[str] = None
    access: Dict[str, bool] = {}
    
    model_config = ConfigDict(protected_namespaces=())

class RoleCreate(RoleBase):
    pass

class RoleUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    deletable: Optional[bool] = None
    access: Optional[Dict[str, bool]] = None
    
    model_config = ConfigDict(protected_namespaces=())

class RoleResponse(RoleBase):
    id: int
    created_at: datetime
    created_by: Optional[int] = None
    deletable: bool = True
    
    model_config = ConfigDict(
        from_attributes=True,
        protected_namespaces=()
    )
