from mongoengine import Document, <PERSON><PERSON>ield, DateTimeField, Dict<PERSON><PERSON>, ObjectIdField, <PERSON>Field
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List
from bson import ObjectId

# MongoEngine 模型
class StructuredDataRecord(Document):
    meta = {
        'collection': 'structured_data_records'
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId)
    dataset_id = ObjectIdField(required=True)  # 数据集di
    file_id = ObjectIdField()     # 文件id
    file_name = StringField()     # 文件名
    data = StringField(required=True)  # json字符换
    images = ListField(StringField(), default=list)
    created_at = DateTimeField(default=datetime.now)
    updated_at = DateTimeField(default=datetime.now)

# Pydantic 模型
class StructuredDataRecordBase(BaseModel):
    dataset_id: Optional[str] = None
    file_id: Optional[str] = None      # 新增字段
    data: str
    images: List[str] = []
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class StructuredDataRecordCreate(StructuredDataRecordBase):
    pass

class StructuredDataRecordUpdate(BaseModel):
    data: Optional[str] = None
    images: Optional[List[str]] = None

class StructuredDataRecordResponse(StructuredDataRecordBase):
    id: str

    class Config:
        from_attributes = True


class StructuredDataRecord(Document):
    meta = {
        'collection': 'structured_data_images'
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId)
    file_name = StringField(required=True)     # 文件名
    created_at = DateTimeField(default=datetime.now)
    storage_path = StringField(required=True)