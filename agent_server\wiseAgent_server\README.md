# LangGraph 项目

[![CI](https://github.com/langchain-ai/new-langgraph-project/actions/workflows/unit-tests.yml/badge.svg)](https://github.com/langchain-ai/new-langgraph-project/actions/workflows/unit-tests.yml)
[![Integration Tests](https://github.com/langchain-ai/new-langgraph-project/actions/workflows/integration-tests.yml/badge.svg)](https://github.com/langchain-ai/new-langgraph-project/actions/workflows/integration-tests.yml)

此模板展示了一个使用 [LangGraph](https://github.com/langchain-ai/langgraph) 实现的简单应用程序，旨在展示如何开始使用 [LangGraph Server](https://langchain-ai.github.io/langgraph/concepts/langgraph_server/#langgraph-server) 以及使用 [LangGraph Studio](https://langchain-ai.github.io/langgraph/concepts/langgraph_studio/)（一个可视化调试IDE）。

<div align="center">
  <img src="./static/studio_ui.png" alt="LangGraph studio UI中的图表视图" width="75%" />
</div>

`src/agent/graph.py` 中定义的核心逻辑展示了一个单步应用程序，该程序以固定字符串和提供的配置进行响应。

您可以扩展此图表来编排更复杂的代理工作流，这些工作流可以在 LangGraph Studio 中进行可视化和调试。

## 开始使用

<!--
Setup instruction auto-generated by `langgraph template lock`. DO NOT EDIT MANUALLY.
-->

<!--
End setup instructions
-->

1. 安装依赖项，以及 [LangGraph CLI](https://langchain-ai.github.io/langgraph/concepts/langgraph_cli/)，它将用于运行服务器。

```bash
cd path/to/your/app
pip install -e . "langgraph-cli[inmem]"
```

2. （可选）根据需要自定义代码和项目。如果需要使用密钥，请创建一个 `.env` 文件。

```bash
cp .env.example .env
```

如果您想启用 LangSmith 跟踪，请将您的 LangSmith API 密钥添加到 `.env` 文件中。

```text
# .env
LANGSMITH_API_KEY=lsv2...
```

3. 启动 LangGraph 服务器。

```shell
langgraph dev
```

有关开始使用 LangGraph Server 的更多信息，[请参见此处](https://langchain-ai.github.io/langgraph/tutorials/langgraph-platform/local-server/)。

## 如何自定义

1. **定义可配置参数**：修改 `graph.py` 文件中的 `Configuration` 类，以暴露您想要配置的参数。例如，在聊天机器人应用程序中，您可能想要定义动态系统提示或要使用的 LLM。有关 LangGraph 中配置的更多信息，[请参见此处](https://langchain-ai.github.io/langgraph/concepts/low_level/?h=configuration#configuration)。

2. **扩展图表**：应用程序的核心逻辑在 [graph.py](./src/agent/graph.py) 中定义。您可以修改此文件以添加新节点、边缘或更改信息流。

## 开发

在 LangGraph Studio 中迭代您的图表时，您可以编辑过去的状态，并从之前的状态重新运行您的应用程序以调试特定节点。本地更改将通过热重载自动应用。

后续请求会扩展相同的线程。您可以使用右上角的 `+` 按钮创建一个全新的线程，清除之前的历史记录。

有关更高级的功能和示例，请参阅 [LangGraph 文档](https://langchain-ai.github.io/langgraph/)。这些资源可以帮助您根据特定用例调整此模板，并构建更复杂的对话代理。

LangGraph Studio 还与 [LangSmith](https://smith.langchain.com/) 集成，用于更深入的跟踪和与团队成员的协作，允许您分析和优化聊天机器人的性能。

<!--
Configuration auto-generated by `langgraph template lock`. DO NOT EDIT MANUALLY.
{
  "config_schemas": {
    "agent": {
      "type": "object",
      "properties": {}
    }
  }
}
-->
