"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[7535],{51042:function(e,r,n){var t=n(1413),a=n(67294),u=n(42110),s=n(91146),i=function(e,r){return a.createElement(s.Z,(0,t.Z)((0,t.Z)({},e),{},{ref:r,icon:u.Z}))},o=a.forwardRef(i);r.Z=o},5966:function(e,r,n){var t=n(97685),a=n(1413),u=n(91),s=n(21770),i=n(8232),o=n(55241),c=n(98423),p=n(67294),l=n(62633),f=n(85893),d=["fieldProps","proFieldProps"],h=["fieldProps","proFieldProps"],v="text",m=function(e){var r=(0,s.Z)(e.open||!1,{value:e.open,onChange:e.onOpenChange}),n=(0,t.Z)(r,2),u=n[0],c=n[1];return(0,f.jsx)(i.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(r){var n,t=r.getFieldValue(e.name||[]);return(0,f.jsx)(o.Z,(0,a.Z)((0,a.Z)({getPopupContainer:function(e){return e&&e.parentNode?e.parentNode:e},onOpenChange:function(e){return c(e)},content:(0,f.jsxs)("div",{style:{padding:"4px 0"},children:[null===(n=e.statusRender)||void 0===n?void 0:n.call(e,t),e.strengthText?(0,f.jsx)("div",{style:{marginTop:10},children:(0,f.jsx)("span",{children:e.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},e.popoverProps),{},{open:u,children:e.children}))}})},x=function(e){var r=e.fieldProps,n=e.proFieldProps,t=(0,u.Z)(e,d);return(0,f.jsx)(l.Z,(0,a.Z)({valueType:v,fieldProps:r,filedConfig:{valueType:v},proFieldProps:n},t))};x.Password=function(e){var r=e.fieldProps,n=e.proFieldProps,s=(0,u.Z)(e,h),i=(0,p.useState)(!1),o=(0,t.Z)(i,2),d=o[0],x=o[1];return null!=r&&r.statusRender&&s.name?(0,f.jsx)(m,{name:s.name,statusRender:null==r?void 0:r.statusRender,popoverProps:null==r?void 0:r.popoverProps,strengthText:null==r?void 0:r.strengthText,open:d,onOpenChange:x,children:(0,f.jsx)("div",{children:(0,f.jsx)(l.Z,(0,a.Z)({valueType:"password",fieldProps:(0,a.Z)((0,a.Z)({},(0,c.Z)(r,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(e){var n;null==r||null===(n=r.onBlur)||void 0===n||n.call(r,e),x(!1)},onClick:function(e){var n;null==r||null===(n=r.onClick)||void 0===n||n.call(r,e),x(!0)}}),proFieldProps:n,filedConfig:{valueType:v}},s))})}):(0,f.jsx)(l.Z,(0,a.Z)({valueType:"password",fieldProps:r,proFieldProps:n,filedConfig:{valueType:v}},s))},x.displayName="ProFormComponent",r.Z=x},27504:function(e,r,n){n.r(r),n.d(r,{default:function(){return C}});var t=n(9783),a=n.n(t),u=n(15009),s=n.n(u),i=n(97857),o=n.n(i),c=n(99289),p=n.n(c),l=n(5574),f=n.n(l),d=n(67294),h=n(97131),v=n(12453),m=n(2453),x=n(17788),y=n(63496),g=n(83622),w=n(51042),k=n(69044),b=n(37476),Z=n(5966),P=n(85893),T=function(e){var r=e.formRef,n=e.onSubmit,t=e.modalVisible,u=e.values,i=e.treeData,c=e.checkedKeys,l=e.onTreeCheck,f=e.onCancel;return(0,P.jsxs)(b.Y,{formRef:r,title:null!=u&&u.id?"编辑角色":"新建角色",visible:t,onVisibleChange:function(e){e||f()},onFinish:function(){var e=p()(s()().mark((function e(r){var t,i;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=o()(o()({},r),{},{id:null==u?void 0:u.id,access:c.reduce((function(e,r){return o()(o()({},e),{},a()({},r,!0))}),{})}),e.next=3,n(t);case 3:return i=e.sent,e.abrupt("return",i);case 5:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),initialValues:u,children:[(0,P.jsx)(Z.Z,{name:"id",hidden:!0}),(0,P.jsx)(Z.Z,{name:"name",label:"角色名称",placeholder:"请输入角色名称",rules:[{required:!0,message:"角色名称为必填项"}]}),(0,P.jsx)(Z.Z,{name:"description",label:"描述",placeholder:"请输入角色描述"}),(0,P.jsx)(y.Z,{checkable:!0,onCheck:function(e){return l(e)},checkedKeys:c,treeData:i})]})},M=n(35312),C=function(){var e=(0,M.useIntl)(),r=(0,d.useState)(!1),n=f()(r,2),t=n[0],u=n[1],i=(0,d.useState)(!1),c=f()(i,2),l=c[0],y=c[1],b=(0,d.useState)(void 0),Z=f()(b,2),C=Z[0],j=Z[1],E=(0,d.useState)([]),N=f()(E,2),R=N[0],S=N[1],F=(0,d.useRef)(),O=(0,d.useRef)(),D=(0,d.useState)([]),I=f()(D,2),_=I[0],G=I[1];(0,d.useEffect)((function(){var r=function(){var r=p()(s()().mark((function r(){var n;return s()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,k.CW)();case 3:n=r.sent,G(function r(n){return n.map((function(n){return o()(o()({},n),{},{title:e.formatMessage({id:"permission.".concat(n.key),defaultMessage:n.title}),children:n.children?r(n.children):void 0})}))}(n)),r.next=11;break;case 8:r.prev=8,r.t0=r.catch(0),m.ZP.error("获取权限树据失败，请重试");case 11:case"end":return r.stop()}}),r,null,[[0,8]])})));return function(){return r.apply(this,arguments)}}();r()}),[e]),(0,d.useEffect)((function(){var e;t||(null===(e=O.current)||void 0===e||e.resetFields(),S([]))}),[t]);var V=function(){var r=p()(s()().mark((function r(n){var t,i;return s()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return t=m.ZP.loading(e.formatMessage({id:"adding",defaultMessage:"正在添加"})),r.prev=1,r.next=4,(0,k._d)(o()(o()({},n),{},{access:R.reduce((function(e,r){return o()(o()({},e),{},a()({},r,!0))}),{})}));case 4:return t(),m.ZP.success(e.formatMessage({id:"add.success",defaultMessage:"添加成功"})),u(!1),null===(i=F.current)||void 0===i||i.reload(),r.abrupt("return",!0);case 11:return r.prev=11,r.t0=r.catch(1),t(),m.ZP.error(e.formatMessage({id:"add.fail",defaultMessage:"添加失败，请重试"})),r.abrupt("return",!1);case 16:case"end":return r.stop()}}),r,null,[[1,11]])})));return function(e){return r.apply(this,arguments)}}(),K=function(){var r=p()(s()().mark((function r(n){var t,u,i;return s()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(t=m.ZP.loading(e.formatMessage({id:"updating",defaultMessage:"正在更新"})),r.prev=1,n.id){r.next=4;break}throw new Error("Role ID is undefined");case 4:return i=R.reduce((function(e,r){return o()(o()({},e),{},a()({},r,!0))}),{}),r.next=7,(0,k.ul)(n.id,o()(o()({},n),{},{access:i}));case 7:return t(),m.ZP.success(e.formatMessage({id:"update.success",defaultMessage:"更新成功"})),y(!1),j(void 0),null===(u=F.current)||void 0===u||u.reload(),r.abrupt("return",!0);case 15:return r.prev=15,r.t0=r.catch(1),t(),m.ZP.error(e.formatMessage({id:"update.fail",defaultMessage:"更新失败，请重试"})),r.abrupt("return",!1);case 20:case"end":return r.stop()}}),r,null,[[1,15]])})));return function(e){return r.apply(this,arguments)}}(),U=function(){var e=p()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:x.Z.confirm({title:"确认删除",content:"确定要删除这个角色吗？",okText:"确认",cancelText:"取消",onOk:function(){var e=p()(s()().mark((function e(){var n,t;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=m.ZP.loading("正在删除"),e.prev=1,e.next=4,(0,k.Rd)(r.id);case 4:return n(),m.ZP.success("删除成功"),null===(t=F.current)||void 0===t||t.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),n(),m.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}()});case 1:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),L=function(){var e=p()(s()().mark((function e(r){var n,t,a;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,k.cY)(r.id);case 3:t=e.sent,y(!0),j(t),a=Object.entries(t.access||{}).filter((function(e){var r=f()(e,2);r[0];return r[1]})).map((function(e){var r=f()(e,2),n=r[0];r[1];return n})),S(a),null===(n=O.current)||void 0===n||n.setFieldsValue({name:t.name,description:t.description}),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),m.ZP.error("获取角色详情失败，请重试");case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(r){return e.apply(this,arguments)}}(),q=[{title:e.formatMessage({id:"role.name",defaultMessage:"名称"}),dataIndex:"name",valueType:"text"},{title:e.formatMessage({id:"role.description",defaultMessage:"描述"}),dataIndex:"description",valueType:"textarea"},{title:e.formatMessage({id:"role.createdAt",defaultMessage:"创建时间"}),dataIndex:"created_at",valueType:"dateTime",search:!1},{title:e.formatMessage({id:"role.deletable",defaultMessage:"可删除"}),dataIndex:"deletable",valueType:"text",search:!1,render:function(e){return e?"是":"否"}},{title:e.formatMessage({id:"role.action",defaultMessage:"操作"}),dataIndex:"option",valueType:"option",render:function(r,n){return[(0,P.jsx)(g.ZP,{type:"link",onClick:function(){return L(n)},children:e.formatMessage({id:"edit",defaultMessage:"编辑"})},"edit-".concat(n.id)),(0,P.jsx)(g.ZP,{type:"link",danger:!0,onClick:function(){return U(n)},disabled:!n.deletable,children:e.formatMessage({id:"delete",defaultMessage:"删除"})},"delete-".concat(n.id))]}}];return(0,P.jsxs)(h._z,{children:[(0,P.jsx)(v.Z,{headerTitle:e.formatMessage({id:"menu.roleManagement",defaultMessage:"角色管理"}),actionRef:F,rowKey:"id",search:{labelWidth:120},toolBarRender:function(){return[(0,P.jsxs)(g.ZP,{type:"primary",onClick:function(){u(!0),S([])},children:[(0,P.jsx)(w.Z,{})," ",e.formatMessage({id:"new",defaultMessage:"新建"})]},"primary")]},request:function(){var e=p()(s()().mark((function e(r){var n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,k.F3)(r);case 2:return n=e.sent,e.abrupt("return",{data:n.data,success:n.success,total:n.total});case 4:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),columns:q,pagination:{pageSize:10}}),(0,P.jsx)(T,{modalVisible:t,onCancel:function(){return u(!1)},onSubmit:V,formRef:O,values:{},treeData:_,checkedKeys:R,onTreeCheck:S}),C&&(0,P.jsx)(T,{modalVisible:l,onCancel:function(){y(!1),j(void 0)},onSubmit:K,formRef:O,values:C,treeData:_,checkedKeys:R,onTreeCheck:S})]})}},69044:function(e,r,n){n.d(r,{CW:function(){return I},F3:function(){return j},Nq:function(){return h},Rd:function(){return O},Rf:function(){return p},Rp:function(){return b},_d:function(){return N},az:function(){return y},cY:function(){return G},cn:function(){return f},h8:function(){return m},iE:function(){return M},jA:function(){return w},mD:function(){return P},ul:function(){return S},w1:function(){return K},wG:function(){return L}});var t=n(5574),a=n.n(t),u=n(15009),s=n.n(u),i=n(99289),o=n.n(i),c=n(78158);function p(e){return l.apply(this,arguments)}function l(){return(l=o()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/users",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function f(e){return d.apply(this,arguments)}function d(){return(d=o()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/users",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(e){return v.apply(this,arguments)}function v(){return(v=o()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/users/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function m(e){return x.apply(this,arguments)}function x(){return(x=o()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/users/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(e,r){return g.apply(this,arguments)}function g(){return(g=o()(s()().mark((function e(r,n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/users/changeStatus",{method:"POST",data:{id:r,status:n}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function w(e){return k.apply(this,arguments)}function k(){return(k=o()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/groups",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e){return Z.apply(this,arguments)}function Z(){return(Z=o()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/groups",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function P(e){return T.apply(this,arguments)}function T(){return(T=o()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/groups/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function M(e){return C.apply(this,arguments)}function C(){return(C=o()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/groups/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function j(e){return E.apply(this,arguments)}function E(){return(E=o()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/roles",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function N(e){return R.apply(this,arguments)}function R(){return(R=o()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/roles",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function S(e,r){return F.apply(this,arguments)}function F(){return(F=o()(s()().mark((function e(r,n){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/roles/".concat(r),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function O(e){return D.apply(this,arguments)}function D(){return(D=o()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/roles/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function I(){return _.apply(this,arguments)}function _(){return(_=o()(s()().mark((function e(){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/role/tree",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function G(e){return V.apply(this,arguments)}function V(){return(V=o()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/roles/".concat(r),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function K(e){return U.apply(this,arguments)}function U(){return(U=o()(s()().mark((function e(r){var n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=new URLSearchParams,Object.entries(r).forEach((function(e){var r=a()(e,2),t=r[0],u=r[1];n.append(t,u)})),e.abrupt("return",(0,c.N)("/api/system/config?".concat(n.toString()),{method:"POST"}));case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function L(e){return q.apply(this,arguments)}function q(){return(q=o()(s()().mark((function e(r){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/useActiveCases",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}}]);