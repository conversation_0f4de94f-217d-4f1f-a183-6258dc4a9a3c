import asyncio
from asyncpg import create_pool, Pool
from app.xmconfig import settings
from app.logging_config import get_logger
from typing import Optional

logger = get_logger(__name__)

class PostgreSQLManager:
    _instance = None
    _initialized = False
    _pool: Optional[Pool] = None
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化方法"""
        pass
    
    async def _init_pool(self):
        """初始化连接池"""
        try:
            logger.info(f"正在初始化 PostgreSQL 连接池，地址: {settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}")
            
            # 使用 settings 中的配置创建连接池
            self._pool = await create_pool(
                user=settings.POSTGRES_USER,
                password=settings.POSTGRES_PASSWORD,
                host=settings.POSTGRES_HOST,
                port=settings.POSTGRES_PORT,
                database=settings.POSTGRES_DB,
                min_size=settings.POSTGRES_POOL_MIN_SIZE,
                max_size=settings.POSTGRES_POOL_MAX_SIZE,
                command_timeout=settings.POSTGRES_COMMAND_TIMEOUT,
                timeout=settings.POSTGRES_TIMEOUT
            )
            
            # # 设置默认 schema
            # async with self._pool.acquire() as conn:
            #     await conn.execute(f"SET search_path TO {settings.POSTGRES_SCHEMA}")
            
            self._initialized = True
            logger.info("PostgreSQL 连接池初始化成功")
            print("PostgreSQL 连接池初始化成功")
            
            
        except Exception as e:
            logger.error(f"PostgreSQL 连接池初始化失败: {str(e)}")
            raise

    async def connect(self):
        """连接数据库"""
        if not self._initialized:
            await self._init_pool()
            
        try:
            # 验证连接
            async with self._pool.acquire() as conn:
                await conn.execute('SELECT 1')
            logger.info("PostgreSQL 连接验证成功")
        except Exception as e:
            logger.error(f"PostgreSQL 连接验证失败: {str(e)}")
            raise

    async def disconnect(self):
        """断开连接"""
        if self._pool:
            await self._pool.close()
            self._pool = None
            self._initialized = False
            logger.info("PostgreSQL 连接已断开")

    @property
    def pool(self) -> Pool:
        """获取连接池"""
        if not self._initialized:
            raise RuntimeError("PostgreSQL 连接池未初始化")
        return self._pool

# 创建全局实例
postgresql = PostgreSQLManager()

# 在应用启动时连接数据库
async def initialize_db():
    await postgresql.connect()  # 确保连接池被初始化
    return postgresql.pool  # 返回连接池

# 确保 db 是连接池
async def get_db():
    await initialize_db()  # 确保连接池已初始化
    return postgresql.pool  # 返回连接池

# 在其他地方使用时，确保调用 initialize_db() 来获取连接池
