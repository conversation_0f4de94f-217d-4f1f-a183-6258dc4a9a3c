from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from datetime import datetime
import json
from typing import List, Dict, Any
from .database import get_db
from .models import KnowledgeBase, Collection, KnowledgeData, AppInfo
from .embedTest import embedding
from pydantic import BaseModel, Field
from .logging_config import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/api/core/dataset",
    tags=["knowledge"]
)

# 请求模型
class KnowledgeBaseCreate(BaseModel):
    name: str
    parent_id: str | None = Field(default=None, alias="parentId")
    type: str 
    intro: str | None = None
    avatar: str | None = None
    vector_model: str = Field(alias="vectorModel")
    agent_model: str = Field(alias="agentModel")

    class Config:
        populate_by_alias = True
        validate_by_name = True

class AppCreate(BaseModel):
    app_name: str

# 创建应用
@router.post("/create/app", response_model=Dict[str, Any])
async def create_app(
    app: AppCreate,
    db: AsyncSession = Depends(get_db)
):
    try:
        # 使用字典形式创建
        new_app = AppInfo(**{
            "app_name": app.app_name,
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        })
        
        db.add(new_app)
        await db.commit()
        await db.refresh(new_app)
        
        return {
            "data": str(new_app.id),
            "code": 200,
            "statusText": "success",
            "message": "创建应用成功"
        }
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to create app: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建应用失败: {str(e)}")

# 1. 创建知识库
@router.post("/create", response_model=Dict[str, Any])
async def create_knowledge_base(
    knowledge_base: KnowledgeBaseCreate,
    db: AsyncSession = Depends(get_db)
):
    try:
        new_kb = KnowledgeBase(
            name=knowledge_base.name,
            parent_id=knowledge_base.parent_id,
            type=knowledge_base.type,
            intro=knowledge_base.intro,
            avatar=knowledge_base.avatar,
            vector_model=knowledge_base.vector_model,
            agent_model=knowledge_base.agent_model,
            created_at=datetime.now(),
            last_updated=datetime.now(),
            is_active=True
        )
        db.add(new_kb)
        await db.commit()
        await db.refresh(new_kb)
        
        return {
            "code": 200,
            "statusText": "success",
            "message": "创建知识库成功",
            "datasetId": str(new_kb.id)
        }
    except Exception as e:
        return {
            "code": 500,
            "statusText": "error",
            "message": f"创建知识库失败: {str(e)}",
            "datasetId": None
        }

# 2. 创建集合
@router.post("/collection/create", response_model=Dict[str, Any])
async def create_collection(
    request: Dict[str, Any],
    db: AsyncSession = Depends(get_db)
):
    try:
        # 检查知识库是否存在
        stmt = select(KnowledgeBase).where(
            KnowledgeBase.id == request["datasetId"],
            KnowledgeBase.is_active == True
        )
        result = await db.execute(stmt)
        kb = result.scalar_one_or_none()
        
        if not kb:
            raise HTTPException(status_code=404, detail="Knowledge base not found")
        
        # 创建新集合
        new_collection = Collection(
            knowledge_base_id=request["datasetId"],
            created_at=datetime.now()
        )
        db.add(new_collection)
        await db.commit()
        await db.refresh(new_collection)
        
        return {
            "collectionId": str(new_collection.id),
            "code": 200
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

# 3. 知识库添加数据
@router.post("/data/pushData", response_model=Dict[str, Any])
async def add_knowledge_data(
    request: Dict[str, Any],
    db: AsyncSession = Depends(get_db)
):
    try:
        result = {
            "insertLen": 0,
            "overToken": [],
            "repeat": [],
            "error": [],
            "insertIds": []
        }
        
        # 使用事务包装所有数据插入操作
        async with db.begin():
            for data in request["data"]:
                try:
                    embedding_result = embedding(data)
                    if not embedding_result or "data" not in embedding_result:
                        result["error"].append(f"获取 embedding 失败: {data[:100]}...")
                        continue
                    
                    embedding_vector = embedding_result.get("data")[0].get("embedding")
                    if not embedding_vector:
                        result["error"].append(f"embedding 向量为空: {data[:100]}...")
                        continue

                    embedding_vector = [float(x) for x in embedding_vector]

                    new_data = KnowledgeData(
                        collection_id=request["collectionId"],
                        knowledge_base_id=request["datasetId"],
                        mode=request["mode"],
                        data=data,
                        prompt=request.get("prompt"),
                        doc_name=request.get("docName"),
                        created_at=datetime.now(),
                        embedding_vector=embedding_vector
                    )
                    db.add(new_data)
                    # 移除这里的 commit
                    await db.flush()  # 使用flush替代单独的commit
                    result["insertIds"].append(str(new_data.id))
                    result["insertLen"] += 1
                except Exception as e:
                    logger.error(f"Error processing data item: {str(e)}")
                    result["error"].append(str(e))
                    continue
        
        return {
            "code": 200,
            "statusText": "success",
            "message": "",
            "data": {
                "insertIds": result["insertIds"],
                "insertLen": result["insertLen"],
                "overToken": result["overToken"],
                "repeat": result["repeat"],
                "error": result["error"]
            }
        }
    except Exception as e:
        logger.error(f"Failed to add knowledge data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 4. 知识库删除数据
@router.delete("/data/delete", response_model=Dict[str, Any])
async def delete_knowledge_data(
    request: Dict[str, Any],
    db: AsyncSession = Depends(get_db)
):
    try:
        stmt = update(KnowledgeData).where(
            KnowledgeData.id.in_(request["dbId"])
        ).values(
            is_active=False,
            deleted_at=datetime.now()
        )
        result = await db.execute(stmt)
        await db.commit()
        
        return {
            "data": "success",
            "code": 200
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

# 5. 知识库更新数据
@router.put("/data/update", response_model=Dict[str, Any])
async def update_knowledge_data(
    request: Dict[str, Any],
    db: AsyncSession = Depends(get_db)
):
    try:
        # 1. 首先获取新数据的向量嵌入
        embedding_result = embedding(request["data"])
        if not embedding_result or "data" not in embedding_result:
            raise HTTPException(status_code=500, detail="获取 embedding 失败")
            
        embedding_vector = embedding_result.get("data")[0].get("embedding")
        if not embedding_vector:
            raise HTTPException(status_code=500, detail="embedding 向量为空")
        
        # 2. 更新数据和向量
        stmt = update(KnowledgeData).where(
            KnowledgeData.id == request["dbId"]
        ).values(
            data=request["data"],
            embedding_vector=embedding_vector  # 同时更新向量字段
        )
        
        # 3. 执行更新
        result = await db.execute(stmt)
        await db.commit()
        
        # 4. 返回结果
        return {
            "data": {
                "insertLen": result.rowcount,
                "overToken": [],
                "repeat": [],
                "error": []
            },
            "code": 200
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
