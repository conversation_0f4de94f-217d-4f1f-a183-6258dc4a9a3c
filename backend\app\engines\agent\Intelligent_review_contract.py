from langgraph.graph import StateGraph, END
from typing import Dict, List, Any, TypedDict, Optional, AsyncGenerator, Annotated
from langgraph.graph.message import add_messages
import httpx
import asyncio
from app.utils.logging_config import setup_logging, get_logger
import time
import json
import hashlib
import os
from langchain_openai import Chat<PERSON><PERSON>AI
from app.models.llm import LLMModel
from app.engines.agent.Intelligent_review_prompt import (
    CONTRACT_REVIEW_SYSTEM_PROMPT,
    CONTRACT_CLAUSE_ANALYSIS_PROMPT,
    CONTRACT_ISSUES_PROMPT,
    CONTRACT_CITATION_PROMPT,
    CONTRACT_RECOMMENDATIONS_PROMPT
)
from app.engines.retrieval.retriever_utils import parallel_knowledge_search, SearchConfig

setup_logging()
logger = get_logger(__name__)

class AgentState(TypedDict):
    content_input: str
    knowledge_base_ids: List[str]
    rules: Optional[List[Dict[str, str]]] # 数据查询结果
    content: Optional[List[str]]
    kb_search_results: Optional[List[Dict[str, Any]]]
    analyzed_clauses: Optional[Dict[str, Any]]
    issues_identified: Optional[List[Dict[str, Any]]]
    cited_clauses: Optional[List[Dict[str, Any]]]
    analysis_result: Optional[Dict[str, Any]]
    key_points: Optional[List[Dict[str, Any]]]
    recommendations: Optional[List[Dict[str, Any]]]
    final_output: Optional[Dict[str, Any]]
    error: Optional[str]
    app_info: Optional[str]  # 应用信息
    

class ContractIntelligentReview:
    def __init__(self, config: Dict[str, str], db, content: List, llm: LLMModel, knowledge_base_ids: List[str] = None, app_info: str = None):
        self.config = config
        self.content = content
        self.db = db
        self.knowledge_base_ids = knowledge_base_ids or []
        self.app_info = app_info
        self.workflow = StateGraph(AgentState)
        self._setup_workflow()
        self.llm = ChatOpenAI(
                model=llm.m_name,
                temperature=llm.temperature | 0.1,
                openai_api_key=llm.api_key,
                openai_api_base= llm.service_url,
                stream = False
            )

    def _setup_workflow(self):
        # 添加工作流节点
        self.workflow.add_node("get_rules", self.get_rules_node)
        self.workflow.add_node("kb_search", self.kb_search_node)
        self.workflow.add_node("analyze_clauses", self.analyze_clauses_node)
        self.workflow.add_node("identify_issues", self.identify_issues_node)
        self.workflow.add_node("cite_clauses", self.cite_clauses_node)
        self.workflow.add_node("generate_results", self.generate_results_node)
        
        # 设置工作流入口点
        self.workflow.set_entry_point("get_rules")
        
        # 添加工作流边
        self.workflow.add_edge("get_rules", "kb_search")
        self.workflow.add_edge("kb_search", "analyze_clauses")
        self.workflow.add_edge("analyze_clauses", "identify_issues")
        self.workflow.add_edge("identify_issues", "cite_clauses")
        self.workflow.add_edge("cite_clauses", "generate_results")
        self.workflow.add_edge("generate_results", END)

    async def get_rules_node(self, state: AgentState) -> AgentState:
        """获取相关保护规则"""
        logger.info('获取相关保护规则')
        try:
            # 构建查询条件
            query = {"ruleType": "合同审核"}
            if self.app_info:
                query["relatedApps"] = self.app_info
            
            # 执行查询
            rules = await self.db['consumer_protection_rules'].find(query).to_list(length=1000)
            
            # 转换规则为可用格式
            formatted_rules = []
            for rule in rules:
                formatted_rule = {
                    "ruleId": rule.get("ruleId", ""),
                    "ruleName": rule.get("ruleName", ""),
                    "description": rule.get("description", ""),
                    "triggerCondition": rule.get("triggerCondition", ""),
                    "analysisLogic": rule.get("analysisLogic", "")
                }
                formatted_rules.append(formatted_rule)
            
            logger.info(f"获取到 {len(formatted_rules)} 条保护规则")
            return {
                **state,
                "rules": formatted_rules,
                "app_info": self.app_info
            }
        except Exception as e:
            logger.error(f"获取保护规则失败: {str(e)}")
            return {
                **state,
                "error": f"获取保护规则失败: {str(e)}",
                "app_info": self.app_info
            }

    async def kb_search_node(self, state: AgentState) -> AgentState:
        """知识库搜索节点：搜索与合同相关的内容"""
        logger.info('执行知识库搜索')
        try:
            query = "\n".join(self.content) if isinstance(self.content, list) else self.content
            
            # 准备搜索配置
            search_config = SearchConfig(top_k=10)
            
            # 执行并行知识库搜索
            search_results = await parallel_knowledge_search(
                query=query,
                knowledge_base_ids=self.knowledge_base_ids,
                config=search_config
            )
            
            logger.info(f"知识库搜索结果数量: {len(search_results)}")
            return {
                **state, 
                "content_input": query,
                "knowledge_base_ids": self.knowledge_base_ids,
                "kb_search_results": search_results
            }
        except Exception as e:
            logger.error(f"知识库搜索失败: {str(e)}")
            return {
                **state, 
                "content_input": "\n".join(self.content) if isinstance(self.content, list) else self.content,
                "error": f"知识库搜索失败: {str(e)}"
            }

    async def analyze_clauses_node(self, state: AgentState) -> AgentState:
        """合同条款分析节点：分析关键合同条款"""
        logger.info('分析合同条款')
        try:
            # 准备合同原文和知识库搜索结果
            contract_text = state.get("content_input", "")
            kb_results = state.get("kb_search_results", [])
            rules = state.get("rules", [])
            
            # 构建合同分析提示词，添加规则内容
            rules_text = ""
            if rules:
                rules_text = "## 适用规则:\n"
                for rule in rules:
                    rules_text += f"- 规则ID: {rule.get('ruleId', '')}, 名称: {rule.get('ruleName', '')}\n"
                    rules_text += f"  描述: {rule.get('description', '')}\n"
                    rules_text += f"  触发条件: {rule.get('triggerCondition', '')}\n"
            
            enhanced_prompt = CONTRACT_CLAUSE_ANALYSIS_PROMPT.format(
                contract_text=contract_text,
                kb_results=json.dumps(kb_results, ensure_ascii=False)
            )
            
            if rules_text:
                enhanced_prompt += "\n" + rules_text
            
            # 调用LLM进行分析
            messages = [
                {"role": "system", "content": "你是一位专业的合同分析师，擅长分析合同条款、权利义务和法律风险。"},
                {"role": "user", "content": enhanced_prompt}
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # 解析LLM响应
            analysis_content = response.content
            analyzed_clauses = self._extract_json_from_text(analysis_content)
            
            return {**state, "analyzed_clauses": analyzed_clauses}
        except Exception as e:
            logger.error(f"合同条款分析失败: {str(e)}")
            return {**state, "error": f"合同条款分析失败: {str(e)}"}

    async def identify_issues_node(self, state: AgentState) -> AgentState:
        """问题识别节点：标记模糊或有争议的条款"""
        logger.info('识别合同问题')
        try:
            # 获取合同内容和分析结果
            contract_text = state.get("content_input", "")
            analyzed_clauses = state.get("analyzed_clauses", {})
            rules = state.get("rules", [])
            
            # 构建问题识别提示词，添加规则参考
            rules_text = ""
            if rules:
                rules_text = "## 参考规则:\n"
                for rule in rules:
                    rules_text += f"- 规则ID: {rule.get('ruleId', '')}, 名称: {rule.get('ruleName', '')}\n"
                    rules_text += f"  描述: {rule.get('description', '')}\n"
                    rules_text += f"  分析逻辑: {rule.get('analysisLogic', '')}\n"
            
            enhanced_prompt = CONTRACT_ISSUES_PROMPT.format(
                contract_text=contract_text,
                analyzed_clauses=json.dumps(analyzed_clauses, ensure_ascii=False)
            )
            
            if rules_text:
                enhanced_prompt += "\n" + rules_text
            
            # 调用LLM进行问题识别
            messages = [
                {"role": "system", "content": "你是一位专业的合同审核师，擅长识别合同中的问题和风险。"},
                {"role": "user", "content": enhanced_prompt}
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # 解析LLM响应
            issues_content = response.content
            identified_issues = self._extract_json_from_text(issues_content)
            
            return {**state, "issues_identified": identified_issues}
        except Exception as e:
            logger.error(f"问题识别失败: {str(e)}")
            return {**state, "error": f"问题识别失败: {str(e)}"}

    async def cite_clauses_node(self, state: AgentState) -> AgentState:
        """引用具体条款节点：在分析中直接引用合同的具体章节和条款"""
        logger.info('引用具体条款')
        try:
            # 获取合同内容、分析结果和已识别问题
            contract_text = state.get("content_input", "")
            analyzed_clauses = state.get("analyzed_clauses", {})
            identified_issues = state.get("issues_identified", [])
            rules = state.get("rules", [])
            
            # 构建条款引用提示词，添加规则引用提示
            rules_text = ""
            if rules:
                rules_text = "## 引用规则:\n"
                for rule in rules:
                    rules_text += f"- 规则ID: {rule.get('ruleId', '')}, 名称: {rule.get('ruleName', '')}\n"
                    
            enhanced_prompt = CONTRACT_CITATION_PROMPT.format(
                contract_text=contract_text,
                analyzed_clauses=json.dumps(analyzed_clauses, ensure_ascii=False),
                identified_issues=json.dumps(identified_issues, ensure_ascii=False)
            )
            
            if rules_text:
                enhanced_prompt += "\n" + rules_text + "\n还请同时引用相关保护规则，标明规则ID。"
            
            # 调用LLM进行条款引用
            messages = [
                {"role": "system", "content": "你是一位专业的合同分析师，擅长精确引用合同条款并提供分析。"},
                {"role": "user", "content": enhanced_prompt}
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # 解析LLM响应
            citation_content = response.content
            cited_clauses = self._extract_json_from_text(citation_content)
            
            return {**state, "cited_clauses": cited_clauses}
        except Exception as e:
            logger.error(f"条款引用失败: {str(e)}")
            return {**state, "error": f"条款引用失败: {str(e)}"}

    async def generate_results_node(self, state: AgentState) -> AgentState:
        """结果生成节点：生成分析、关键点和建议"""
        logger.info('生成分析结果')
        try:
            # 获取分析结果、问题和引用
            analyzed_clauses = state.get("analyzed_clauses", {})
            identified_issues = state.get("issues_identified", [])
            cited_clauses = state.get("cited_clauses", [])
            rules = state.get("rules", [])
            
            # 生成详细分析结果
            analysis_result = {
                "title": "合同详细分析",
                "key_clauses_analysis": analyzed_clauses.get("key_clauses", []),
                "rights_obligations_analysis": analyzed_clauses.get("rights_obligations", []),
                "legal_risks_analysis": analyzed_clauses.get("legal_risks", []),
                "issues_details": identified_issues,
                "citations": cited_clauses,
                "applied_rules": [{"ruleId": rule.get("ruleId", ""), "ruleName": rule.get("ruleName", "")} for rule in rules]
            }
            
            # 生成关键点摘要
            key_points = []
            
            # 添加关键条款点
            for clause in analyzed_clauses.get("key_clauses", []):
                key_points.append({
                    "type": "key_clause",
                    "title": clause.get("clause_name", ""),
                    "description": f"位置: {clause.get('location', '未知')}",
                    "importance": "high"
                })
            
            # 添加主要风险点
            for risk in analyzed_clauses.get("legal_risks", []):
                if risk.get("severity", "") in ["high", "medium"]:
                    key_points.append({
                        "type": "risk",
                        "title": risk.get("risk_type", ""),
                        "description": risk.get("description", ""),
                        "importance": "high" if risk.get("severity") == "high" else "medium"
                    })
            
            # 添加主要问题点
            for issue in identified_issues:
                if issue.get("risk_level", "") in ["high", "medium"]:
                    key_points.append({
                        "type": "issue",
                        "title": f"{issue.get('issue_type', '')} 问题",
                        "description": issue.get("description", ""),
                        "importance": "high" if issue.get("risk_level") == "high" else "medium"
                    })
            
            # 生成建议
            # 添加规则引用至建议生成提示中
            rules_text = ""
            if rules:
                rules_text = "\n## 参考规则:\n"
                for rule in rules:
                    rules_text += f"- 规则ID: {rule.get('ruleId', '')}, 名称: {rule.get('ruleName', '')}\n"
                    rules_text += f"  描述: {rule.get('description', '')}\n"
            
            # 构建建议生成提示词
            recommendations_prompt = CONTRACT_RECOMMENDATIONS_PROMPT.format(
                analysis_result=json.dumps(analysis_result, ensure_ascii=False),
                key_points=json.dumps(key_points, ensure_ascii=False)
            )
            
            if rules_text:
                recommendations_prompt += rules_text
            
            # 调用LLM生成建议
            messages = [
                {"role": "system", "content": "你是一位专业的合同顾问，擅长提供合同修改和谈判建议。"},
                {"role": "user", "content": recommendations_prompt}
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # 解析LLM响应
            recommendations_content = response.content
            recommendations = self._extract_json_from_text(recommendations_content)
            
            # 生成最终输出
            final_output = {
                "analysis": analysis_result,
                "key_points": key_points,
                "recommendations": recommendations,
                "applied_rules": [{"ruleId": rule.get("ruleId", ""), "ruleName": rule.get("ruleName", "")} for rule in rules]
            }
            
            return {
                **state, 
                "analysis_result": analysis_result,
                "key_points": key_points,
                "recommendations": recommendations,
                "final_output": final_output
            }
        except Exception as e:
            logger.error(f"结果生成失败: {str(e)}")
            return {**state, "error": f"结果生成失败: {str(e)}"}

    def _extract_json_from_text(self, text: str) -> Dict:
        """从文本中提取JSON内容"""
        try:
            # 查找文本中的JSON部分
            start_index = text.find('{')
            end_index = text.rfind('}') + 1
            
            if start_index != -1 and end_index != 0:
                json_str = text[start_index:end_index]
                return json.loads(json_str)
            else:
                logger.warning("未找到有效的JSON内容")
                return {}
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {str(e)}")
            return {}

    async def run(self) -> Dict[str, Any]:
        """运行智能合同审查工作流"""
        try:
            logger.info("启动智能合同审查工作流")
            
            # 准备初始状态
            initial_state = {
                "content_input": "\n".join(self.content) if isinstance(self.content, list) else self.content,
                "knowledge_base_ids": self.knowledge_base_ids,
                "app_info": self.app_info
            }
            
            # 编译并执行工作流
            app = self.workflow.compile()
            result = await app.ainvoke(initial_state)
            
            # 检查是否有错误
            if "error" in result and result["error"]:
                logger.error(f"工作流执行出错: {result['error']}")
                return {"status": "error", "message": result["error"]}
            
            # 返回最终结果
            if "final_output" in result:
                return {
                    "status": "success", 
                    "data": result["final_output"]
                }
            else:
                return {
                    "status": "incomplete", 
                    "message": "工作流执行未完成"
                }
        except Exception as e:
            logger.error(f"工作流执行失败: {str(e)}")
            return {"status": "error", "message": str(e)}

    