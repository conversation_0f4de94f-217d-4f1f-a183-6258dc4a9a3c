from fastapi import APIRouter, HTTPException, Depends, Query, File, UploadFile, Form, Body
from typing import List, Optional, Dict, Any
from ..models.consumerProtectionFile import (ConsumerProtectionFileResponse, ConsumerProtectionFileUploadRequest)
from ..models.source_files import SourceFileBase
from ..db.mongodb import db
from ..utils.auth import verify_token
from bson import ObjectId
from datetime import datetime
from ..models.system_app_setting import SystemAppSettingModel
import os
from werkzeug.utils import secure_filename as _secure_filename
from pathlib import Path
from pydantic import BaseModel
from ..models.llm import LLMModel
from ..engines.agent import Intelligent_review
import json

from ..models.source_files import SourceFileResponse
from app.utils.logging_config import setup_logging, get_logger
from ..db.miniIO import minio
import uuid
from ..utils.config import settings
import traceback # 异常打印
from urllib.parse import quote
from app.utils.enums import FileStorageType
from app.engines.embedding.embedding_utils import get_embedding
from app.engines.retrieval.base_retriever import es_data_ingester, update_by_query
from difflib import SequenceMatcher
from ..models.consumerProtectionFile import ConsumerProtectionFileResponse
from app.engines.indexing.content_parser import parse_file_content
import time

setup_logging()
logger = get_logger(__name__)
router = APIRouter(
    prefix="/api/customerAssistant",
    tags=["customerAssistant"]
)





@router.post("/evaluate_by_rule")
async def evaluate_by_rule(
    request_data: Dict[str, Any] = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    基于特定规则评估文档内容
    参数:
    - evaluation_type: 评估类型
    - rule_id: 规则ID
    - chunk_contents: 文档内容块
    - app_info: 应用信息
    """
    try:
        evaluation_type = request_data.get("evaluation_type", "")
        app_info = request_data.get("app_info", "")
        chunk_contents = request_data.get("chunk_contents", [])
        logger.info(f"evaluation_type: {evaluation_type}")
        logger.info(f"app_info: {app_info}")
        logger.info(f"chunk_contents: {chunk_contents}")
        
    
        # 获取系统应用设置
        system_app_info = await db["system_app_settings"].find_one({"app_info": app_info})
        if not system_app_info:
            return {
                "success": False,
                "message": "未找到应用配置信息",
                "data": {"result": "", "status": "failed"}
            }
        
        system_app_params = system_app_info.get('params', {})
        
        # 获取LLM配置
        llm: LLMModel = await db["llms"].find_one({"id": int(system_app_params.get('llm_id', 1))})
        if not llm:
            return {
                "success": False,
                "message": "未找到LLM配置",
                "data": {"result": "", "status": "failed"}
            }
        

        
        # 提取所有文档内容
        contents = [chunk.get("content", "") for chunk in chunk_contents]

        logger.info(f"contents: {contents}")
        
        # 使用智能评估引擎
        from app.engines.agent.ScriptMining import ScriptMining
        
        review_agent = ScriptMining(
            config=system_app_params,
            db=db,
            evaluation_type=evaluation_type,
            llm=llm,
            content=contents
        )
         
        # 执行评估
        result = await review_agent.run()
        
        if result.get("status") == "success":
            logger.info(f'result: {result}')
            return {
                "success": True,
                "message": "抽取完成",
                "data": result.get("data", {})
            }
        else:
            return {
                "success": False,
                "message": result.get("message", "抽取失败"),
                "data": {"result": "评估失败", "status": "failed"}
            }
            
    except Exception as e:
        logger.error(f"规则评估失败: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "success": False,
            "message": f"规则评估失败: {str(e)}",
            "data": {"result": "", "status": "error"}
        }
