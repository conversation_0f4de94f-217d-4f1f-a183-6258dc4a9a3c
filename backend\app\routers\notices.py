from fastapi import APIRouter, Depends
from typing import List
from pydantic import BaseModel
from datetime import datetime

router = APIRouter()

class Notice(BaseModel):
    id: str
    avatar: str
    title: str
    datetime: str
    type: str
    description: str = None
    read: bool = None
    clickClose: bool = None
    extra: str = None
    status: str = None

# 模拟通知数据
notices_data = [
    {
        "id": "000000001",
        "avatar": "https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/MSbDR4FR2MUAAAAAAAAAAAAAFl94AQBr",
        "title": "你收到了 14 份新周报",
        "datetime": "2017-08-09",
        "type": "notification",
    },
    {
        "id": "000000002",
        "avatar": "https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/hX-PTavYIq4AAAAAAAAAAAAAFl94AQBr",
        "title": "你推荐的 曲妮妮 已通过第三轮面试",
        "datetime": "2017-08-08",
        "type": "notification",
    },
    # ... 添加其他通知数据 ...
]

@router.get("/api/notices", response_model=dict)
async def get_notices():
    return {"data": notices_data}