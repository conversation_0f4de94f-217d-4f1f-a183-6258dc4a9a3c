from fastapi import FastAPI
from fastapi.responses import StreamingResponse
import asyncio
import time

app = FastAPI()

@app.get("/stream-sse")
async def stream():
    async def generate():
        for i in range(10):
            yield f"data: {time.time()}\n\n"
            await asyncio.sleep(1)
    return StreamingResponse(generate(), media_type='text/event-stream')

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8001)