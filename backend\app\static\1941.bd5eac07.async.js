"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1941],{42110:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"}},11941:function(e,t,n){n.d(t,{Z:function(){return Re}});var a=n(67294),o=n(62208),r=n(48001),i=n(87462),l=n(42110),c=n(93771),d=function(e,t){return a.createElement(c.Z,(0,i.Z)({},e,{ref:t,icon:l.Z}))};var s=a.forwardRef(d),u=n(93967),f=n.n(u),v=n(4942),b=n(1413),p=n(97685),m=n(71002),h=n(91),g=n(21770),$=n(31131),k=(0,a.createContext)(null),y=n(74902),w=n(9220),x=n(66680),_=n(42550),S=n(75164),C=function(e){var t=e.activeTabOffset,n=e.horizontal,o=e.rtl,r=e.indicator,i=void 0===r?{}:r,l=i.size,c=i.align,d=void 0===c?"center":c,s=(0,a.useState)(),u=(0,p.Z)(s,2),f=u[0],v=u[1],b=(0,a.useRef)(),m=a.useCallback((function(e){return"function"==typeof l?l(e):"number"==typeof l?l:e}),[l]);function h(){S.Z.cancel(b.current)}return(0,a.useEffect)((function(){var e={};if(t)if(n){e.width=m(t.width);var a=o?"right":"left";"start"===d&&(e[a]=t[a]),"center"===d&&(e[a]=t[a]+t.width/2,e.transform=o?"translateX(50%)":"translateX(-50%)"),"end"===d&&(e[a]=t[a]+t.width,e.transform="translateX(-100%)")}else e.height=m(t.height),"start"===d&&(e.top=t.top),"center"===d&&(e.top=t.top+t.height/2,e.transform="translateY(-50%)"),"end"===d&&(e.top=t.top+t.height,e.transform="translateY(-100%)");return h(),b.current=(0,S.Z)((function(){v(e)})),h}),[t,n,o,d,m]),{style:f}},E={width:0,height:0,left:0,top:0};function Z(e,t){var n=a.useRef(e),o=a.useState({}),r=(0,p.Z)(o,2)[1];return[n.current,function(e){var a="function"==typeof e?e(n.current):e;a!==n.current&&t(a,n.current),n.current=a,r({})}]}var P=Math.pow(.995,20);var R=n(8410);function I(e){var t=(0,a.useState)(0),n=(0,p.Z)(t,2),o=n[0],r=n[1],i=(0,a.useRef)(0),l=(0,a.useRef)();return l.current=e,(0,R.o)((function(){var e;null===(e=l.current)||void 0===e||e.call(l)}),[o]),function(){i.current===o&&(i.current+=1,r(i.current))}}var T={width:0,height:0,left:0,top:0,right:0};function M(e){var t;return e instanceof Map?(t={},e.forEach((function(e,n){t[n]=e}))):t=e,JSON.stringify(t)}function L(e){return String(e).replace(/"/g,"TABS_DQ")}function z(e,t,n,a){return!(!n||a||!1===e||void 0===e&&(!1===t||null===t))}var B=a.forwardRef((function(e,t){var n=e.prefixCls,o=e.editable,r=e.locale,i=e.style;return o&&!1!==o.showAdd?a.createElement("button",{ref:t,type:"button",className:"".concat(n,"-nav-add"),style:i,"aria-label":(null==r?void 0:r.addAriaLabel)||"Add tab",onClick:function(e){o.onEdit("add",{event:e})}},o.addIcon||"+"):null})),D=B;var O=a.forwardRef((function(e,t){var n,o=e.position,r=e.prefixCls,i=e.extra;if(!i)return null;var l={};return"object"!==(0,m.Z)(i)||a.isValidElement(i)?l.right=i:l=i,"right"===o&&(n=l.right),"left"===o&&(n=l.left),n?a.createElement("div",{className:"".concat(r,"-extra-content"),ref:t},n):null})),N=n(29171),j=n(72512),G=n(15105),A=a.forwardRef((function(e,t){var n=e.prefixCls,o=e.id,r=e.tabs,l=e.locale,c=e.mobile,d=e.more,s=void 0===d?{}:d,u=e.style,b=e.className,m=e.editable,h=e.tabBarGutter,g=e.rtl,$=e.removeAriaLabel,k=e.onTabClick,y=e.getPopupContainer,w=e.popupClassName,x=(0,a.useState)(!1),_=(0,p.Z)(x,2),S=_[0],C=_[1],E=(0,a.useState)(null),Z=(0,p.Z)(E,2),P=Z[0],R=Z[1],I=s.icon,T=void 0===I?"More":I,M="".concat(o,"-more-popup"),L="".concat(n,"-dropdown"),B=null!==P?"".concat(M,"-").concat(P):null,O=null==l?void 0:l.dropdownAriaLabel;var A=a.createElement(j.ZP,{onClick:function(e){var t=e.key,n=e.domEvent;k(t,n),C(!1)},prefixCls:"".concat(L,"-menu"),id:M,tabIndex:-1,role:"listbox","aria-activedescendant":B,selectedKeys:[P],"aria-label":void 0!==O?O:"expanded dropdown"},r.map((function(e){var t=e.closable,n=e.disabled,r=e.closeIcon,i=e.key,l=e.label,c=z(t,r,m,n);return a.createElement(j.sN,{key:i,id:"".concat(M,"-").concat(i),role:"option","aria-controls":o&&"".concat(o,"-panel-").concat(i),disabled:n},a.createElement("span",null,l),c&&a.createElement("button",{type:"button","aria-label":$||"remove",tabIndex:0,className:"".concat(L,"-menu-item-remove"),onClick:function(e){e.stopPropagation(),function(e,t){e.preventDefault(),e.stopPropagation(),m.onEdit("remove",{key:t,event:e})}(e,i)}},r||m.removeIcon||"×"))})));function H(e){for(var t=r.filter((function(e){return!e.disabled})),n=t.findIndex((function(e){return e.key===P}))||0,a=t.length,o=0;o<a;o+=1){var i=t[n=(n+e+a)%a];if(!i.disabled)return void R(i.key)}}(0,a.useEffect)((function(){var e=document.getElementById(B);e&&e.scrollIntoView&&e.scrollIntoView(!1)}),[P]),(0,a.useEffect)((function(){S||R(null)}),[S]);var W=(0,v.Z)({},g?"marginRight":"marginLeft",h);r.length||(W.visibility="hidden",W.order=1);var X=f()((0,v.Z)({},"".concat(L,"-rtl"),g)),K=c?null:a.createElement(N.Z,(0,i.Z)({prefixCls:L,overlay:A,visible:!!r.length&&S,onVisibleChange:C,overlayClassName:f()(X,w),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:y},s),a.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:W,"aria-haspopup":"listbox","aria-controls":M,id:"".concat(o,"-more"),"aria-expanded":S,onKeyDown:function(e){var t=e.which;if(S)switch(t){case G.Z.UP:H(-1),e.preventDefault();break;case G.Z.DOWN:H(1),e.preventDefault();break;case G.Z.ESC:C(!1);break;case G.Z.SPACE:case G.Z.ENTER:null!==P&&k(P,e)}else[G.Z.DOWN,G.Z.SPACE,G.Z.ENTER].includes(t)&&(C(!0),e.preventDefault())}},T));return a.createElement("div",{className:f()("".concat(n,"-nav-operations"),b),style:u,ref:t},K,a.createElement(D,{prefixCls:n,locale:l,editable:m}))})),H=a.memo(A,(function(e,t){return t.tabMoving})),W=function(e){var t=e.prefixCls,n=e.id,o=e.active,r=e.focus,i=e.tab,l=i.key,c=i.label,d=i.disabled,s=i.closeIcon,u=i.icon,b=e.closable,p=e.renderWrapper,m=e.removeAriaLabel,h=e.editable,g=e.onClick,$=e.onFocus,k=e.onBlur,y=e.onKeyDown,w=e.onMouseDown,x=e.onMouseUp,_=e.style,S=e.tabCount,C=e.currentPosition,E="".concat(t,"-tab"),Z=z(b,s,h,d);function P(e){d||g(e)}var R=a.useMemo((function(){return u&&"string"==typeof c?a.createElement("span",null,c):c}),[c,u]),I=a.useRef(null);a.useEffect((function(){r&&I.current&&I.current.focus()}),[r]);var T=a.createElement("div",{key:l,"data-node-key":L(l),className:f()(E,(0,v.Z)((0,v.Z)((0,v.Z)((0,v.Z)({},"".concat(E,"-with-remove"),Z),"".concat(E,"-active"),o),"".concat(E,"-disabled"),d),"".concat(E,"-focus"),r)),style:_,onClick:P},a.createElement("div",{ref:I,role:"tab","aria-selected":o,id:n&&"".concat(n,"-tab-").concat(l),className:"".concat(E,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(l),"aria-disabled":d,tabIndex:d?null:o?0:-1,onClick:function(e){e.stopPropagation(),P(e)},onKeyDown:y,onMouseDown:w,onMouseUp:x,onFocus:$,onBlur:k},r&&a.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(C," of ").concat(S)),u&&a.createElement("span",{className:"".concat(E,"-icon")},u),c&&R),Z&&a.createElement("button",{type:"button",role:"tab","aria-label":m||"remove",tabIndex:o?0:-1,className:"".concat(E,"-remove"),onClick:function(e){var t;e.stopPropagation(),(t=e).preventDefault(),t.stopPropagation(),h.onEdit("remove",{key:l,event:t})}},s||h.removeIcon||"×"));return p?p(T):T},X=function(e){var t=e.current||{},n=t.offsetWidth,a=void 0===n?0:n,o=t.offsetHeight,r=void 0===o?0:o;if(e.current){var i=e.current.getBoundingClientRect(),l=i.width,c=i.height;if(Math.abs(l-a)<1)return[l,c]}return[a,r]},K=function(e,t){return e[t?0:1]},q=a.forwardRef((function(e,t){var n=e.className,o=e.style,r=e.id,l=e.animated,c=e.activeKey,d=e.rtl,s=e.extra,u=e.editable,m=e.locale,h=e.tabPosition,g=e.tabBarGutter,$=e.children,S=e.onTabClick,R=e.onTabScroll,B=e.indicator,N=a.useContext(k),j=N.prefixCls,G=N.tabs,A=(0,a.useRef)(null),q=(0,a.useRef)(null),F=(0,a.useRef)(null),V=(0,a.useRef)(null),Y=(0,a.useRef)(null),U=(0,a.useRef)(null),Q=(0,a.useRef)(null),J="top"===h||"bottom"===h,ee=Z(0,(function(e,t){J&&R&&R({direction:e>t?"left":"right"})})),te=(0,p.Z)(ee,2),ne=te[0],ae=te[1],oe=Z(0,(function(e,t){!J&&R&&R({direction:e>t?"top":"bottom"})})),re=(0,p.Z)(oe,2),ie=re[0],le=re[1],ce=(0,a.useState)([0,0]),de=(0,p.Z)(ce,2),se=de[0],ue=de[1],fe=(0,a.useState)([0,0]),ve=(0,p.Z)(fe,2),be=ve[0],pe=ve[1],me=(0,a.useState)([0,0]),he=(0,p.Z)(me,2),ge=he[0],$e=he[1],ke=(0,a.useState)([0,0]),ye=(0,p.Z)(ke,2),we=ye[0],xe=ye[1],_e=function(e){var t=(0,a.useRef)([]),n=(0,a.useState)({}),o=(0,p.Z)(n,2)[1],r=(0,a.useRef)("function"==typeof e?e():e),i=I((function(){var e=r.current;t.current.forEach((function(t){e=t(e)})),t.current=[],r.current=e,o({})}));return[r.current,function(e){t.current.push(e),i()}]}(new Map),Se=(0,p.Z)(_e,2),Ce=Se[0],Ee=Se[1],Ze=function(e,t,n){return(0,a.useMemo)((function(){for(var n,a=new Map,o=t.get(null===(n=e[0])||void 0===n?void 0:n.key)||E,r=o.left+o.width,i=0;i<e.length;i+=1){var l,c=e[i].key,d=t.get(c);d||(d=t.get(null===(l=e[i-1])||void 0===l?void 0:l.key)||E);var s=a.get(c)||(0,b.Z)({},d);s.right=r-s.left-s.width,a.set(c,s)}return a}),[e.map((function(e){return e.key})).join("_"),t,n])}(G,Ce,be[0]),Pe=K(se,J),Re=K(be,J),Ie=K(ge,J),Te=K(we,J),Me=Math.floor(Pe)<Math.floor(Re+Ie),Le=Me?Pe-Te:Pe-Ie,ze="".concat(j,"-nav-operations-hidden"),Be=0,De=0;function Oe(e){return e<Be?Be:e>De?De:e}J&&d?(Be=0,De=Math.max(0,Re-Le)):(Be=Math.min(0,Le-Re),De=0);var Ne=(0,a.useRef)(null),je=(0,a.useState)(),Ge=(0,p.Z)(je,2),Ae=Ge[0],He=Ge[1];function We(){He(Date.now())}function Xe(){Ne.current&&clearTimeout(Ne.current)}!function(e,t){var n=(0,a.useState)(),o=(0,p.Z)(n,2),r=o[0],i=o[1],l=(0,a.useState)(0),c=(0,p.Z)(l,2),d=c[0],s=c[1],u=(0,a.useState)(0),f=(0,p.Z)(u,2),v=f[0],b=f[1],m=(0,a.useState)(),h=(0,p.Z)(m,2),g=h[0],$=h[1],k=(0,a.useRef)(),y=(0,a.useRef)(),w=(0,a.useRef)(null);w.current={onTouchStart:function(e){var t=e.touches[0],n=t.screenX,a=t.screenY;i({x:n,y:a}),window.clearInterval(k.current)},onTouchMove:function(e){if(r){var n=e.touches[0],a=n.screenX,o=n.screenY;i({x:a,y:o});var l=a-r.x,c=o-r.y;t(l,c);var u=Date.now();s(u),b(u-d),$({x:l,y:c})}},onTouchEnd:function(){if(r&&(i(null),$(null),g)){var e=g.x/v,n=g.y/v,a=Math.abs(e),o=Math.abs(n);if(Math.max(a,o)<.1)return;var l=e,c=n;k.current=window.setInterval((function(){Math.abs(l)<.01&&Math.abs(c)<.01?window.clearInterval(k.current):t(20*(l*=P),20*(c*=P))}),20)}},onWheel:function(e){var n=e.deltaX,a=e.deltaY,o=0,r=Math.abs(n),i=Math.abs(a);r===i?o="x"===y.current?n:a:r>i?(o=n,y.current="x"):(o=a,y.current="y"),t(-o,-o)&&e.preventDefault()}},a.useEffect((function(){function t(e){w.current.onTouchMove(e)}function n(e){w.current.onTouchEnd(e)}return document.addEventListener("touchmove",t,{passive:!1}),document.addEventListener("touchend",n,{passive:!0}),e.current.addEventListener("touchstart",(function(e){w.current.onTouchStart(e)}),{passive:!0}),e.current.addEventListener("wheel",(function(e){w.current.onWheel(e)}),{passive:!1}),function(){document.removeEventListener("touchmove",t),document.removeEventListener("touchend",n)}}),[])}(V,(function(e,t){function n(e,t){e((function(e){return Oe(e+t)}))}return!!Me&&(J?n(ae,e):n(le,t),Xe(),We(),!0)})),(0,a.useEffect)((function(){return Xe(),Ae&&(Ne.current=setTimeout((function(){He(0)}),100)),Xe}),[Ae]);var Ke=function(e,t,n,o,r,i,l){var c,d,s,u=l.tabs,f=l.tabPosition,v=l.rtl;return["top","bottom"].includes(f)?(c="width",d=v?"right":"left",s=Math.abs(n)):(c="height",d="top",s=-n),(0,a.useMemo)((function(){if(!u.length)return[0,0];for(var n=u.length,a=n,o=0;o<n;o+=1){var r=e.get(u[o].key)||T;if(Math.floor(r[d]+r[c])>Math.floor(s+t)){a=o-1;break}}for(var i=0,l=n-1;l>=0;l-=1)if((e.get(u[l].key)||T)[d]<s){i=l+1;break}return i>=a?[0,0]:[i,a]}),[e,t,o,r,i,s,f,u.map((function(e){return e.key})).join("_"),v])}(Ze,Le,J?ne:ie,Re,Ie,Te,(0,b.Z)((0,b.Z)({},e),{},{tabs:G})),qe=(0,p.Z)(Ke,2),Fe=qe[0],Ve=qe[1],Ye=(0,x.Z)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:c,t=Ze.get(e)||{width:0,height:0,left:0,right:0,top:0};if(J){var n=ne;d?t.right<ne?n=t.right:t.right+t.width>ne+Le&&(n=t.right+t.width-Le):t.left<-ne?n=-t.left:t.left+t.width>-ne+Le&&(n=-(t.left+t.width-Le)),le(0),ae(Oe(n))}else{var a=ie;t.top<-ie?a=-t.top:t.top+t.height>-ie+Le&&(a=-(t.top+t.height-Le)),ae(0),le(Oe(a))}})),Ue=(0,a.useState)(),Qe=(0,p.Z)(Ue,2),Je=Qe[0],et=Qe[1],tt=(0,a.useState)(!1),nt=(0,p.Z)(tt,2),at=nt[0],ot=nt[1],rt=G.filter((function(e){return!e.disabled})).map((function(e){return e.key})),it=function(e){var t=rt.indexOf(Je||c),n=rt.length,a=rt[(t+e+n)%n];et(a)},lt=function(e){var t=e.code,n=d&&J,a=rt[0],o=rt[rt.length-1];switch(t){case"ArrowLeft":J&&it(n?1:-1);break;case"ArrowRight":J&&it(n?-1:1);break;case"ArrowUp":e.preventDefault(),J||it(-1);break;case"ArrowDown":e.preventDefault(),J||it(1);break;case"Home":e.preventDefault(),et(a);break;case"End":e.preventDefault(),et(o);break;case"Enter":case"Space":e.preventDefault(),S(Je,e);break;case"Backspace":case"Delete":var r=rt.indexOf(Je),i=G.find((function(e){return e.key===Je}));z(null==i?void 0:i.closable,null==i?void 0:i.closeIcon,u,null==i?void 0:i.disabled)&&(e.preventDefault(),e.stopPropagation(),u.onEdit("remove",{key:Je,event:e}),r===rt.length-1?it(-1):it(1))}},ct={};J?ct[d?"marginRight":"marginLeft"]=g:ct.marginTop=g;var dt=G.map((function(e,t){var n=e.key;return a.createElement(W,{id:r,prefixCls:j,key:n,tab:e,style:0===t?void 0:ct,closable:e.closable,editable:u,active:n===c,focus:n===Je,renderWrapper:$,removeAriaLabel:null==m?void 0:m.removeAriaLabel,tabCount:rt.length,currentPosition:t+1,onClick:function(e){S(n,e)},onKeyDown:lt,onFocus:function(){at||et(n),Ye(n),We(),V.current&&(d||(V.current.scrollLeft=0),V.current.scrollTop=0)},onBlur:function(){et(void 0)},onMouseDown:function(){ot(!0)},onMouseUp:function(){ot(!1)}})})),st=function(){return Ee((function(){var e,t=new Map,n=null===(e=Y.current)||void 0===e?void 0:e.getBoundingClientRect();return G.forEach((function(e){var a,o=e.key,r=null===(a=Y.current)||void 0===a?void 0:a.querySelector('[data-node-key="'.concat(L(o),'"]'));if(r){var i=function(e,t){var n=e.offsetWidth,a=e.offsetHeight,o=e.offsetTop,r=e.offsetLeft,i=e.getBoundingClientRect(),l=i.width,c=i.height,d=i.left,s=i.top;return Math.abs(l-n)<1?[l,c,d-t.left,s-t.top]:[n,a,r,o]}(r,n),l=(0,p.Z)(i,4),c=l[0],d=l[1],s=l[2],u=l[3];t.set(o,{width:c,height:d,left:s,top:u})}})),t}))};(0,a.useEffect)((function(){st()}),[G.map((function(e){return e.key})).join("_")]);var ut=I((function(){var e=X(A),t=X(q),n=X(F);ue([e[0]-t[0]-n[0],e[1]-t[1]-n[1]]);var a=X(Q);$e(a);var o=X(U);xe(o);var r=X(Y);pe([r[0]-a[0],r[1]-a[1]]),st()})),ft=G.slice(0,Fe),vt=G.slice(Ve+1),bt=[].concat((0,y.Z)(ft),(0,y.Z)(vt)),pt=Ze.get(c),mt=C({activeTabOffset:pt,horizontal:J,indicator:B,rtl:d}).style;(0,a.useEffect)((function(){Ye()}),[c,Be,De,M(pt),M(Ze),J]),(0,a.useEffect)((function(){ut()}),[d]);var ht,gt,$t,kt,yt=!!bt.length,wt="".concat(j,"-nav-wrap");return J?d?(gt=ne>0,ht=ne!==De):(ht=ne<0,gt=ne!==Be):($t=ie<0,kt=ie!==Be),a.createElement(w.Z,{onResize:ut},a.createElement("div",{ref:(0,_.x1)(t,A),role:"tablist","aria-orientation":J?"horizontal":"vertical",className:f()("".concat(j,"-nav"),n),style:o,onKeyDown:function(){We()}},a.createElement(O,{ref:q,position:"left",extra:s,prefixCls:j}),a.createElement(w.Z,{onResize:ut},a.createElement("div",{className:f()(wt,(0,v.Z)((0,v.Z)((0,v.Z)((0,v.Z)({},"".concat(wt,"-ping-left"),ht),"".concat(wt,"-ping-right"),gt),"".concat(wt,"-ping-top"),$t),"".concat(wt,"-ping-bottom"),kt)),ref:V},a.createElement(w.Z,{onResize:ut},a.createElement("div",{ref:Y,className:"".concat(j,"-nav-list"),style:{transform:"translate(".concat(ne,"px, ").concat(ie,"px)"),transition:Ae?"none":void 0}},dt,a.createElement(D,{ref:Q,prefixCls:j,locale:m,editable:u,style:(0,b.Z)((0,b.Z)({},0===dt.length?void 0:ct),{},{visibility:yt?"hidden":null})}),a.createElement("div",{className:f()("".concat(j,"-ink-bar"),(0,v.Z)({},"".concat(j,"-ink-bar-animated"),l.inkBar)),style:mt}))))),a.createElement(H,(0,i.Z)({},e,{removeAriaLabel:null==m?void 0:m.removeAriaLabel,ref:U,prefixCls:j,tabs:bt,className:!yt&&ze,tabMoving:!!Ae})),a.createElement(O,{ref:F,position:"right",extra:s,prefixCls:j})))})),F=q,V=a.forwardRef((function(e,t){var n=e.prefixCls,o=e.className,r=e.style,i=e.id,l=e.active,c=e.tabKey,d=e.children;return a.createElement("div",{id:i&&"".concat(i,"-panel-").concat(c),role:"tabpanel",tabIndex:l?0:-1,"aria-labelledby":i&&"".concat(i,"-tab-").concat(c),"aria-hidden":!l,style:r,className:f()(n,l&&"".concat(n,"-active"),o),ref:t},d)}));var Y=V,U=["renderTabBar"],Q=["label","key"];var J=function(e){var t=e.renderTabBar,n=(0,h.Z)(e,U),o=a.useContext(k).tabs;return t?t((0,b.Z)((0,b.Z)({},n),{},{panes:o.map((function(e){var t=e.label,n=e.key,o=(0,h.Z)(e,Q);return a.createElement(Y,(0,i.Z)({tab:t,key:n,tabKey:n},o))}))}),F):a.createElement(F,n)},ee=n(29372),te=["key","forceRender","style","className","destroyInactiveTabPane"],ne=function(e){var t=e.id,n=e.activeKey,o=e.animated,r=e.tabPosition,l=e.destroyInactiveTabPane,c=a.useContext(k),d=c.prefixCls,s=c.tabs,u=o.tabPane,p="".concat(d,"-tabpane");return a.createElement("div",{className:f()("".concat(d,"-content-holder"))},a.createElement("div",{className:f()("".concat(d,"-content"),"".concat(d,"-content-").concat(r),(0,v.Z)({},"".concat(d,"-content-animated"),u))},s.map((function(e){var r=e.key,c=e.forceRender,d=e.style,s=e.className,v=e.destroyInactiveTabPane,m=(0,h.Z)(e,te),g=r===n;return a.createElement(ee.ZP,(0,i.Z)({key:r,visible:g,forceRender:c,removeOnLeave:!(!l&&!v),leavedClassName:"".concat(p,"-hidden")},o.tabPaneMotion),(function(e,n){var o=e.style,l=e.className;return a.createElement(Y,(0,i.Z)({},m,{prefixCls:p,id:t,tabKey:r,animated:u,active:g,style:(0,b.Z)((0,b.Z)({},d),o),className:f()(s,l),ref:n}))}))}))))};n(80334);var ae=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],oe=0,re=a.forwardRef((function(e,t){var n=e.id,o=e.prefixCls,r=void 0===o?"rc-tabs":o,l=e.className,c=e.items,d=e.direction,s=e.activeKey,u=e.defaultActiveKey,y=e.editable,w=e.animated,x=e.tabPosition,_=void 0===x?"top":x,S=e.tabBarGutter,C=e.tabBarStyle,E=e.tabBarExtraContent,Z=e.locale,P=e.more,R=e.destroyInactiveTabPane,I=e.renderTabBar,T=e.onChange,M=e.onTabClick,L=e.onTabScroll,z=e.getPopupContainer,B=e.popupClassName,D=e.indicator,O=(0,h.Z)(e,ae),N=a.useMemo((function(){return(c||[]).filter((function(e){return e&&"object"===(0,m.Z)(e)&&"key"in e}))}),[c]),j="rtl"===d,G=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{inkBar:!0,tabPane:!1};return(e=!1===t?{inkBar:!1,tabPane:!1}:!0===t?{inkBar:!0,tabPane:!1}:(0,b.Z)({inkBar:!0},"object"===(0,m.Z)(t)?t:{})).tabPaneMotion&&void 0===e.tabPane&&(e.tabPane=!0),!e.tabPaneMotion&&e.tabPane&&(e.tabPane=!1),e}(w),A=(0,a.useState)(!1),H=(0,p.Z)(A,2),W=H[0],X=H[1];(0,a.useEffect)((function(){X((0,$.Z)())}),[]);var K=(0,g.Z)((function(){var e;return null===(e=N[0])||void 0===e?void 0:e.key}),{value:s,defaultValue:u}),q=(0,p.Z)(K,2),F=q[0],V=q[1],Y=(0,a.useState)((function(){return N.findIndex((function(e){return e.key===F}))})),U=(0,p.Z)(Y,2),Q=U[0],ee=U[1];(0,a.useEffect)((function(){var e,t=N.findIndex((function(e){return e.key===F}));-1===t&&(t=Math.max(0,Math.min(Q,N.length-1)),V(null===(e=N[t])||void 0===e?void 0:e.key));ee(t)}),[N.map((function(e){return e.key})).join("_"),F,Q]);var te=(0,g.Z)(null,{value:n}),re=(0,p.Z)(te,2),ie=re[0],le=re[1];(0,a.useEffect)((function(){n||(le("rc-tabs-".concat(oe)),oe+=1)}),[]);var ce={id:ie,activeKey:F,animated:G,tabPosition:_,rtl:j,mobile:W},de=(0,b.Z)((0,b.Z)({},ce),{},{editable:y,locale:Z,more:P,tabBarGutter:S,onTabClick:function(e,t){null==M||M(e,t);var n=e!==F;V(e),n&&(null==T||T(e))},onTabScroll:L,extra:E,style:C,panes:null,getPopupContainer:z,popupClassName:B,indicator:D});return a.createElement(k.Provider,{value:{tabs:N,prefixCls:r}},a.createElement("div",(0,i.Z)({ref:t,id:n,className:f()(r,"".concat(r,"-").concat(_),(0,v.Z)((0,v.Z)((0,v.Z)({},"".concat(r,"-mobile"),W),"".concat(r,"-editable"),y),"".concat(r,"-rtl"),j),l)},O),a.createElement(J,(0,i.Z)({},de,{renderTabBar:I})),a.createElement(ne,(0,i.Z)({destroyInactiveTabPane:R},ce,{animated:G}))))}));var ie=re,le=n(53124),ce=n(35792),de=n(98675),se=n(33603);const ue={motionAppear:!1,motionEnter:!0,motionLeave:!0};var fe=n(50344),ve=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n};var be=n(11568),pe=n(14747),me=n(83559),he=n(83262),ge=n(67771);var $e=e=>{const{componentCls:t,motionDurationSlow:n}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${n}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${n}`}}}}},[(0,ge.oN)(e,"slide-up"),(0,ge.oN)(e,"slide-down")]]};const ke=e=>{const{componentCls:t,tabsCardPadding:n,cardBg:a,cardGutter:o,colorBorderSecondary:r,itemSelectedColor:i}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:n,background:a,border:`${(0,be.bf)(e.lineWidth)} ${e.lineType} ${r}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:i,background:e.colorBgContainer},[`${t}-tab-focus`]:Object.assign({},(0,pe.oN)(e,-3)),[`${t}-ink-bar`]:{visibility:"hidden"},[`& ${t}-tab${t}-tab-focus ${t}-tab-btn`]:{outline:"none"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:(0,be.bf)(o)}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${(0,be.bf)(e.borderRadiusLG)} ${(0,be.bf)(e.borderRadiusLG)} 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${(0,be.bf)(e.borderRadiusLG)} ${(0,be.bf)(e.borderRadiusLG)}`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:(0,be.bf)(o)}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,be.bf)(e.borderRadiusLG)} 0 0 ${(0,be.bf)(e.borderRadiusLG)}`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,be.bf)(e.borderRadiusLG)} ${(0,be.bf)(e.borderRadiusLG)} 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},ye=e=>{const{componentCls:t,itemHoverColor:n,dropdownEdgeChildVerticalPadding:a}=e;return{[`${t}-dropdown`]:Object.assign(Object.assign({},(0,pe.Wf)(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${(0,be.bf)(a)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},pe.vS),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${(0,be.bf)(e.paddingXXS)} ${(0,be.bf)(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},we=e=>{const{componentCls:t,margin:n,colorBorderSecondary:a,horizontalMargin:o,verticalItemPadding:r,verticalItemMargin:i,calc:l}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${(0,be.bf)(e.lineWidth)} ${e.lineType} ${a}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},\n            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,\n        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:l(e.controlHeight).mul(1.25).equal(),[`${t}-tab`]:{padding:r,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:i},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:(0,be.bf)(l(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${(0,be.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:l(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${(0,be.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},xe=e=>{const{componentCls:t,cardPaddingSM:n,cardPaddingLG:a,horizontalItemPaddingSM:o,horizontalItemPaddingLG:r}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:o,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:r,fontSize:e.titleFontSizeLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:n}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${(0,be.bf)(e.borderRadius)} ${(0,be.bf)(e.borderRadius)}`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${(0,be.bf)(e.borderRadius)} ${(0,be.bf)(e.borderRadius)} 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,be.bf)(e.borderRadius)} ${(0,be.bf)(e.borderRadius)} 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,be.bf)(e.borderRadius)} 0 0 ${(0,be.bf)(e.borderRadius)}`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:a}}}}}},_e=e=>{const{componentCls:t,itemActiveColor:n,itemHoverColor:a,iconCls:o,tabsHorizontalItemMargin:r,horizontalItemPadding:i,itemSelectedColor:l,itemColor:c}=e,d=`${t}-tab`;return{[d]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:i,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:c,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${d}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},(0,pe.Qy)(e)),"&:hover":{color:a},[`&${d}-active ${d}-btn`]:{color:l,textShadow:e.tabsActiveTextShadow},[`&${d}-focus ${d}-btn`]:Object.assign({},(0,pe.oN)(e)),[`&${d}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${d}-disabled ${d}-btn, &${d}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${d}-remove ${o}`]:{margin:0},[`${o}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${d} + ${d}`]:{margin:{_skip_check_:!0,value:r}}}},Se=e=>{const{componentCls:t,tabsHorizontalItemMarginRTL:n,iconCls:a,cardGutter:o,calc:r}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:n},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[a]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,be.bf)(e.marginSM)}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:(0,be.bf)(e.marginXS)},marginLeft:{_skip_check_:!0,value:(0,be.bf)(r(e.marginXXS).mul(-1).equal())},[a]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},Ce=e=>{const{componentCls:t,tabsCardPadding:n,cardHeight:a,cardGutter:o,itemHoverColor:r,itemActiveColor:i,colorBorderSecondary:l}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,pe.Wf)(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:n,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:Object.assign({minWidth:a,marginLeft:{_skip_check_:!0,value:o},padding:(0,be.bf)(e.paddingXS),background:"transparent",border:`${(0,be.bf)(e.lineWidth)} ${e.lineType} ${l}`,borderRadius:`${(0,be.bf)(e.borderRadiusLG)} ${(0,be.bf)(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:r},"&:active, &:focus:not(:focus-visible)":{color:i}},(0,pe.Qy)(e,-3))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),_e(e)),{[`${t}-content`]:{position:"relative",width:"100%"},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:Object.assign(Object.assign({},(0,pe.Qy)(e)),{"&-hidden":{display:"none"}})}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping']) > ${t}-nav-list`]:{margin:"auto"}}}}}};var Ee=(0,me.I$)("Tabs",(e=>{const t=(0,he.IX)(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${(0,be.bf)(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${(0,be.bf)(e.horizontalItemGutter)}`});return[xe(t),Se(t),we(t),ye(t),ke(t),Ce(t),$e(t)]}),(e=>{const t=e.controlHeightLG;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:t,cardPadding:`${(t-Math.round(e.fontSize*e.lineHeight))/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${1.5*e.paddingXXS}px ${e.padding}px`,cardPaddingLG:`${e.paddingXS}px ${e.padding}px ${1.5*e.paddingXXS}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}}));var Ze=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n};const Pe=e=>{var t,n,i,l,c,d,u,v,b,p,m;const{type:h,className:g,rootClassName:$,size:k,onEdit:y,hideAdd:w,centered:x,addIcon:_,removeIcon:S,moreIcon:C,more:E,popupClassName:Z,children:P,items:R,animated:I,style:T,indicatorSize:M,indicator:L}=e,z=Ze(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator"]),{prefixCls:B}=z,{direction:D,tabs:O,getPrefixCls:N,getPopupContainer:j}=a.useContext(le.E_),G=N("tabs",B),A=(0,ce.Z)(G),[H,W,X]=Ee(G,A);let K;"editable-card"===h&&(K={onEdit:(e,t)=>{let{key:n,event:a}=t;null==y||y("add"===e?a:n,e)},removeIcon:null!==(t=null!=S?S:null==O?void 0:O.removeIcon)&&void 0!==t?t:a.createElement(o.Z,null),addIcon:(null!=_?_:null==O?void 0:O.addIcon)||a.createElement(s,null),showAdd:!0!==w});const q=N();const F=(0,de.Z)(k),V=function(e,t){return e||function(e){return e.filter((e=>e))}((0,fe.Z)(t).map((e=>{if(a.isValidElement(e)){const{key:t,props:n}=e,a=n||{},{tab:o}=a,r=ve(a,["tab"]);return Object.assign(Object.assign({key:String(t)},r),{label:o})}return null})))}(R,P),Y=function(e){let t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{inkBar:!0,tabPane:!1};return t=!1===n?{inkBar:!1,tabPane:!1}:!0===n?{inkBar:!0,tabPane:!0}:Object.assign({inkBar:!0},"object"==typeof n?n:{}),t.tabPane&&(t.tabPaneMotion=Object.assign(Object.assign({},ue),{motionName:(0,se.m)(e,"switch")})),t}(G,I),U=Object.assign(Object.assign({},null==O?void 0:O.style),T),Q={align:null!==(n=null==L?void 0:L.align)&&void 0!==n?n:null===(i=null==O?void 0:O.indicator)||void 0===i?void 0:i.align,size:null!==(u=null!==(c=null!==(l=null==L?void 0:L.size)&&void 0!==l?l:M)&&void 0!==c?c:null===(d=null==O?void 0:O.indicator)||void 0===d?void 0:d.size)&&void 0!==u?u:null==O?void 0:O.indicatorSize};return H(a.createElement(ie,Object.assign({direction:D,getPopupContainer:j},z,{items:V,className:f()({[`${G}-${F}`]:F,[`${G}-card`]:["card","editable-card"].includes(h),[`${G}-editable-card`]:"editable-card"===h,[`${G}-centered`]:x},null==O?void 0:O.className,g,$,W,X,A),popupClassName:f()(Z,W,X,A),style:U,editable:K,more:Object.assign({icon:null!==(m=null!==(p=null!==(b=null===(v=null==O?void 0:O.more)||void 0===v?void 0:v.icon)&&void 0!==b?b:null==O?void 0:O.moreIcon)&&void 0!==p?p:C)&&void 0!==m?m:a.createElement(r.Z,null),transitionName:`${q}-slide-up`},E),prefixCls:G,animated:Y,indicator:Q})))};Pe.TabPane=()=>null;var Re=Pe}}]);