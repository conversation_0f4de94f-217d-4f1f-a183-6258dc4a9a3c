import tempfile
import tarfile
import shutil
import tempfile
import tarfile
import shutil
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
from ..utils.auth import verify_token
from typing import List, Optional
import os
import base64
from datetime import datetime
import logging
import cv2
import requests
import json
import numpy as np
from pydantic import BaseModel
from openai import OpenAI
from datetime import timedelta
from minio import Minio
import traceback
from ..db.mongodb import db


# 配置日志
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter(
    prefix="/api/ai-content-recognition",
    tags=["AI内容识别"],
    responses={404: {"description": "Not found"}},
)

# 定义模型
class QuoteInfo(BaseModel):
    regulationName: str
    provision: str
    requirement: str
    similarityScores: List[dict]
    tags: Optional[List[str]] = None
    effectiveDate: Optional[datetime] = None

class OcrRequest(BaseModel):
    base64Image: str
    fileName: Optional[str] = "未命名图片"

class OcrRecognitionResponse(BaseModel):
    success: bool
    message: str
    data: Optional[dict] = None

# async def ocr_analyze_base64(image_base64: str):
#     """直接使用base64编码的图片数据调用OCR接口"""
#     try:
       
#         ocr_url = os.getenv("OCR_PARSER_API", "http://localhost:8000/ocr/parse")
        
#         # 解码base64图片数据
#         img_data = base64.b64decode(image_base64)
#         img_array = np.frombuffer(img_data, np.uint8)
#         img_np = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        
#         if img_np is None:
#             logger.error("无法解码base64图片数据")
#             return None
            
#         h, w, c = img_np.shape
        
#         # 准备请求数据
#         request_data = {
#             "img64": image_base64,
#             "height": h,
#             "width": w,
#             "channels": c
#         }
#         logger.info(f"准备请求数据: {request_data}")
#         logger.info(f"准备请求数据: {ocr_url}")
    

#         # 发送请求到OCR服务
#         response = requests.post(ocr_url, json=request_data, timeout=60)
        
#         if response.status_code == 200:
#             result = response.json()
#             return result.get("text", "")
#         else:
#             logger.error(f"OCR接口调用失败: {response.status_code} - {response.text}")
#             return None
#     except Exception as e:
#         logger.error(f"OCR处理报错: {str(e)}")
#         return None

def analyze_content(text_content: str):
    """分析文本内容，识别潜在问题"""
    try:
        # 这里可以调用LLM或其他分析服务
        # 目前使用模拟数据
        if not text_content or len(text_content) < 10:
            return {
                "hasIssue": False,
                "assessmentTag": "内容过少",
                "analysisResult": "无法进行有效分析，文本内容过少",
                "thoughtProcess": "文本内容不足以进行深入分析",
                "suggestion": "请提供更多文本内容",
                "quoteList": []
            }
            
        # 模拟分析结果
        return {
            "hasIssue": True,
            "assessmentTag": "信息披露不充分",
            "analysisResult": "该文本未明确说明消费者的知情权和选择权",
            "thoughtProcess": "识别到消费者权益相关内容，匹配监管要求第37条",
            "suggestion": "建议补充明确消费者权益相关条款，增加退出机制说明",
            "quoteList": [
                {
                    "regulationName": "商业银行信用卡业务监督管理办法",
                    "provision": "第三十七条",
                    "requirement": "合规性要求",
                    "similarityScores": [
                        {
                            "scoreType": "text-embedding",
                            "value": 0.85,
                            "confidence": 0.92
                        }
                    ],
                    "tags": ["金融监管", "消费者权益"]
                }
            ]
        }
    except Exception as e:
        logger.error(f"内容分析失败: {str(e)}")
        return {
            "hasIssue": False,
            "assessmentTag": "分析失败",
            "analysisResult": f"分析过程中出现错误: {str(e)}",
            "thoughtProcess": "分析过程中断",
            "suggestion": "请重试或联系管理员",
            "quoteList": []
        }

def base64_to_image(base64_str: str, save_path: str = './static/temp_images'):
    """将base64字符串转换为OpenCV图像"""
    try:
        img_data = base64.b64decode(base64_str)
        nparr = np.frombuffer(img_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        # 确保保存目录存在
        if not os.path.exists(save_path):
            os.makedirs(save_path)
        
        # 生成保存文件名
        file_path = os.path.join(save_path, 'temp_image.png')
        
        # 保存图像
        cv2.imwrite(file_path, img)
        
        return file_path
    except Exception as e:
        raise HTTPException(status_code=400, detail="图片解析失败")

    
def upload_file_to_minio(file_path):
    """将解析结果打成tar包并上传至minio"""
    try:
        # MinIO客户端配置
        minio_endpoint = os.getenv("MINIO_ENDPOINT")
        minio_access_key = os.getenv("MINIO_ACCESS_KEY")
        minio_secret_key = os.getenv("MINIO_SECRET_KEY")
        minio_client = Minio(
            minio_endpoint,  # MinIO服务器地址
            access_key=minio_access_key,  
            secret_key=minio_secret_key, 
            secure=False  # 如果使用HTTP则设置为False
        )
        object_name_prefix = "multimodal_recognition/"
        bucket_name = "wiseagent"
        object_name = object_name_prefix + f"temp_image.png"

        # 检查bucket是否存在，不存在则创建
        if not minio_client.bucket_exists(bucket_name):
            minio_client.make_bucket(bucket_name)

        # 上传文件
        minio_client.fput_object(bucket_name, object_name, file_path)
        logger.info(f"Successfully uploaded {object_name} to bucket {bucket_name}.")
        # 生成预签名的下载URL，有效期为7天（604800秒）
        download_url = minio_client.presigned_get_object(bucket_name, object_name, expires=timedelta(seconds=604800))
        return download_url
        
    except Exception as e:
        print('Failed to upload file parse result: ', e)
        return None
    
@router.post("/ocr-recognition", response_model=OcrRecognitionResponse)
async def ocr_recognition(request: OcrRequest, current_user: dict = Depends(verify_token)):
    """
    OCR图片识别接口
    
    - **base64Image**: Base64编码图片
    - **fileName**: 可选的文件名
    
    返回识别结果
    
    """
    # 获取图片的base64编码
    try:
        app_info = "ocrRecognition"
        app_info_obj = await db["system_app_settings"].find_one({"app_info": app_info})
        if not app_info_obj:
            raise HTTPException(status_code=404, detail="应用信息不存在")
        app_params = app_info_obj.get('params',None)
        if not app_params:  
            raise HTTPException(status_code=404, detail="应用参数不存在")

        logger.info(f"app_params: {app_params}")


        image_base64 = request.base64Image
        if not image_base64:
            raise HTTPException(status_code=400, detail="未提供图片数据")
        request_data = {
            "image": image_base64
        }
        # ocr_url = os.getenv("OCR_PARSER_API")
        ocr_url = app_params.get("OCR_PARSER_API",None) 
        logger.info(f"ocr_url: {ocr_url}")
        if not ocr_url:
            logger.error("OCR_PARSER_API 未配置")
            raise HTTPException(status_code=500, detail="OCR服务未配置，请联系管理员")
        response = requests.post(ocr_url, json=request_data)
        logger.info(f"OCR识别结果: {response.json()}")
        
        if response.status_code == 200:
            api_result = response.json()
            logger.info(f"OCR识别结果: {api_result}")
            # print(result['data']['layout_result']['boxes'])
            # 构建完整结果
            result = {
                "ocr_result": api_result,
            }
             # {'code': 0, 'message': 'success', 'data': {'layout_result': {'boxes': [[47.45463943481445, 2.998957633972168, 993.9928588867188, 399.28924560546875]], 'scores': [0.8931268453598022], 'classes': [1], 'image': 'iVBORw0KGgoAAAANSUhEUgAAA/IAAAGSCAIAAADPa20cAAAgAElEQVR4AezBa6+lh5nn5d/vftbau06ug8tVPsaJ4xw66RHNcZDmA/CCZJxDM4JGQuIL8BX4NEgtjWjacR'}}
            return OcrRecognitionResponse(
                success=True,
                message="OCR完成",
                data=result
            )
        
        else:
            return OcrRecognitionResponse(
                success=False,
                message="OCR接口报错",
                data=None
            )
    except Exception as e:
        logger.error(f"OCR识别失败: {str(e)}")
        return OcrRecognitionResponse(
            success=False,
            message=f"图片识别失败: {str(e)}",
            data=None
        )

@router.post("/layout-recognition")
async def layout_recognition(request: OcrRequest):
    """版面识别接口"""
    try:
        app_info = "layoutRecognition"
        app_info_obj = await db["system_app_settings"].find_one({"app_info": app_info})
        if not app_info_obj:
            raise HTTPException(status_code=404, detail="应用信息不存在")
        app_params = app_info_obj.get('params',None)
        if not app_params:  
            raise HTTPException(status_code=404, detail="应用参数不存在")

        logger.info(f"app_params: {app_params}")


        # 获取图片的base64编码
        image_base64 = request.base64Image
        if not image_base64:
            raise HTTPException(status_code=400, detail="未提供图片数据")
        
        logger.info(f"接收到图片数据，准备进行版面分析")
        
        payload = {
            "image": image_base64,
            "return_text": False 
        }
        # layout_url = os.getenv("LAYOUT_PARSER_API")
        layout_url = app_params.get("LAYOUT_PARSER_API")
        if not layout_url:
            logger.error("版面分析API地址未配置")
            raise HTTPException(status_code=500, detail="版面分析API地址未配置")
        
        logger.info(f"版面分析API地址: {layout_url}")
        response = requests.post(layout_url, json=payload)
        if response.status_code == 200:
            api_result = response.json()
            # print(result['data']['layout_result']['boxes'])
            # 构建完整结果
            result = {
                "layout_result": api_result['data']['layout_result'],
            }
             # {'code': 0, 'message': 'success', 'data': {'layout_result': {'boxes': [[47.45463943481445, 2.998957633972168, 993.9928588867188, 399.28924560546875]], 'scores': [0.8931268453598022], 'classes': [1], 'image': 'iVBORw0KGgoAAAANSUhEUgAAA/IAAAGSCAIAAADPa20cAAAgAElEQVR4AezBa6+lh5nn5d/vftbau06ug8tVPsaJ4xw66RHNcZDmA/CCZJxDM4JGQuIL8BX4NEgtjWjacR'}}
            return OcrRecognitionResponse(
                success=True,
                message="版面分析完成",
                data=result
            )
        
        else:
            return OcrRecognitionResponse(
                success=False,
                message=f"版面分析报错: {str(e)}",
                data=None
            )
            
    except Exception as e:
        logger.error(f"版面分析报错: {str(e)}")
        return OcrRecognitionResponse(
            success=False,
            message=f"版面分析报错: {str(e)}",
            data=None
        )
    
def download_and_extract_files(download_url: str):
    """下载并解压文件，返回文件内容"""
    logger.info(f"下载压缩文件: {download_url}")
    # 创建临时目录用于下载和解压文件，可通过TEMP_FILE_DIR环境变量指定目录
    with tempfile.TemporaryDirectory(dir=os.getenv('TEMP_FILE_DIR','./')) as temp_dir:
        try:
            # 下载压缩包
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            # 保存压缩文件
            tar_path = os.path.join(temp_dir, "temp_image_png.tar.gz")
            with open(tar_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            logger.info(f"下载压缩文件成功: {tar_path}")
            
            # 解压文件
            with tarfile.open(tar_path, "r:gz") as tar:
                tar.extractall(path=temp_dir)
            logger.info(f"解压文件成功: {temp_dir}")
            
            # 打印解压后的文件列表
            extracted_files = [os.path.join(root, f) for root, _, files in os.walk(temp_dir) for f in files]
            logger.info(f"解压后目录内容: {extracted_files}")

            # 动态查找json和markdown文件
            json_files = [f for f in extracted_files if f.endswith('.json')]
            md_files = [f for f in extracted_files if f.endswith('.md')]

            if not json_files:
                raise FileNotFoundError("未找到JSON文件")
            if not md_files:
                raise FileNotFoundError("未找到Markdown文件")

            # 读取第一个找到的json文件
            json_path = json_files[0]
            with open(json_path, 'r', encoding='utf-8') as f:
                json_content = json.load(f)
            logger.info(f"读取json文件成功: {json_path}")
            
            # 读取第一个找到的markdown文件
            md_path = md_files[0]
            with open(md_path, 'r', encoding='utf-8') as f:
                md_content = f.read()
            logger.info(f"读取markdown文件成功: {md_path}")
            
            return {
                "file_content": json_content,
                "markdown_content": md_content
            }
            
        except Exception as e:
            traceback.print_exc()
            error_detail = f"文件处理失败: {str(e)}\n解压目录内容: {extracted_files if 'extracted_files' in locals() else '未解压成功'}"
            logger.error(error_detail)
            raise HTTPException(
                status_code=500,
                detail=error_detail
            )


@router.post("/end-to-end-recognition")
async def end_to_end_recognition(request: OcrRequest):
    """AI端到端识别接口"""
    try:
        app_info = "endToEndRecognition"
        app_info_obj = await db["system_app_settings"].find_one({"app_info": app_info})
        if not app_info_obj:
            raise HTTPException(status_code=404, detail="应用信息不存在")
        app_params = app_info_obj.get('params',None)
        if not app_params:  
            raise HTTPException(status_code=404, detail="应用参数不存在")

        logger.info(f"app_params: {app_params}")

        # 获取图片的base64编码
        image_base64 = request.base64Image
        if not image_base64:
            raise HTTPException(status_code=400, detail="未提供图片数据")
        
        logger.info(f"接收到图片数据，准备进行文档解析")
        
        # 解码图片
        temp_img_path = base64_to_image(image_base64)
        
        # 获取端到端API地址
        end_to_end_url = app_params.get("END_TO_END_API")
        logger.info(f"端到端API地址: {end_to_end_url}")
        if not end_to_end_url:
            raise HTTPException(status_code=404, detail="端到端API地址不存在")
            
        # 发送文件到端到端API
        with open(temp_img_path, 'rb') as file:
            # 构建multipart/form-data格式的请求，文件字段名为'file'
            files = {'file': (os.path.basename(temp_img_path), file, 'application/octet-stream')}
            response = requests.post(end_to_end_url, files=files, timeout=180)
            try:
                logger.info(f"端到端API响应内容==》: {response.text}")
            except Exception as e:
                logger.error(f"打印端到端API响应内容时出错: {str(e)}，原始响应对象: {response}")
            logger.info(f"端到端API响应: {response.json()}")
            
        # 检查响应状态码并处理结果
        if response.status_code == 200:
            # 下载并解析结果文件
            parsed_content = download_and_extract_files(response.json()['result'])
            
            # 合并解析结果到返回数据
            result = {
                "result_download_url": response.json()['result'],
                "file_content": parsed_content["file_content"],
                "markdown_content": parsed_content["markdown_content"]
            }
            logger.info(f"文档解析结果: {result}")
            return OcrRecognitionResponse(
                success=True,
                message="文档解析完成",
                data=result
            )
        else:
            logger.error(f"端到端API调用失败: {response.status_code} - {response.text}")
            return OcrRecognitionResponse(
                success=False,
                message=f"文档解析失败，API返回状态码: {response.status_code}",
                data=None
            )
         
    except Exception as e:
        traceback.print_exc()
        logger.error(f"文档解析报错: {str(e)}")
        return OcrRecognitionResponse(
            success=False,
            message=f"文档解析报错: {str(e)}",
            data=None
        )
    finally:
        # 清理临时文件
        if 'temp_img_path' in locals() and os.path.exists(temp_img_path):
            try:
                os.remove(temp_img_path)
            except Exception as e:
                logger.warning(f"清理临时文件失败: {str(e)}")

@router.post("/multimodal-recognition")
async def multimodal_recognition(request: OcrRequest):
    """多模态识别接口"""
    try:
        app_info = "endToEndRecognition"
        app_info_obj = await db["system_app_settings"].find_one({"app_info": app_info})
        if not app_info_obj:
            raise HTTPException(status_code=404, detail="应用信息不存在")
        app_params = app_info_obj.get('params',None)
        if not app_params:  
            raise HTTPException(status_code=404, detail="应用参数不存在")

        logger.info(f"app_params: {app_params}")
        
        # 获取图片的base64编码
        image_base64 = request.base64Image
        if not image_base64:
            raise HTTPException(status_code=400, detail="未提供图片数据")
        
        
        
        prompt = """你是一个金融领域的专家，请根据图片中的内容，给出详细的分析和解读。
要求：
1.尽量按照原始布局和内容识别信息。
2.输出识别信息后，请总结内容的信息，例如这个内容讲了什么
3.提取信息的关键点，
 - 产品类，比如：
    1. 产品名称
    2. 产品类型
    3. 产品特点
    4. 产品优势
    5. 产品风险
    6. 产品适用人群
- 服务类，比如：
    1. 服务名称
    2. 服务类型
    3. 服务特点
    4. 服务优势
    5. 服务风险
    6. 服务适用人群
- 协议、合同类，比如：
    1. 名称
    2. 类型
    3. 内容
    5. 重要信息
    6. 签字
    7. 盖章情况

- 其他类，比如：
    1. 其他信息
"""

        model = 'Qwen/Qwen2.5-VL-72B-Instruct'
        api_key = 'sk-vkmolqivosbciwqhybrjqcpgopurxdmgmbkiliwznxdgunqn'
        base_url = 'https://api.siliconflow.cn/v1/'
        
        client = OpenAI(
            base_url=base_url,
            api_key=api_key, # ModelScope Token
        )   
         # 构建请求消息
        messages = [
            {
                'role': 'user',
                'content': [
                    {
                        'type': 'text',
                        'text': prompt
                    },
                    {
                        'type': 'image_url',
                        'image_url': {
                            'url': f"data:image/jpeg;base64,{image_base64}"
                        }
                    }
                ]
            }
        ]
        
        # 发送请求
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            stream=False
        )

        logger.info(f"多模态图片解析结果: {response}")
        try:
            re_data = response.choices[0].message.content
        except (AttributeError, IndexError, KeyError) as e:
            logger.error(f"解析多模态响应时出错: {str(e)}")
            re_data = "无法解析多模态响应结果"
        
        # answer_list = []
        # for chunk in response:
        #     # print(chunk.choices[0].delta.content, end='', flush=True)
        #     answer_list.append(chunk.choices[0].delta.content)
            
        # answer = "".join(answer_list)
        # # 构建完整结果
        result = {
            "multimodal_recognition": re_data,
        }
        return OcrRecognitionResponse(
            success=True,
            message="多模态图片解析完成",
            data=result
        )
        
    except Exception as e:
        logger.error(f"多模态图片解析报错: {str(e)}")
        return OcrRecognitionResponse(
            success=False,
            message=f"多模态图片解析报错: {str(e)}",
            data=None
        )
    
