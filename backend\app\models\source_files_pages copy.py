from mongoengine import Document, StringField, DateTimeField, IntField, ObjectIdField, ListField
from datetime import datetime
from pydantic import BaseModel
from typing import Optional, List
from bson import ObjectId

class SourceFilePage(Document):
    meta = {
        'collection': 'source_files_pages'
    }
    file_id = ObjectIdField(required=True)
    content = StringField(required=True)
    page_num = IntField(required=True)
    created_at = DateTimeField(default=datetime.now)

# 对应的 Pydantic 模型
class SourceFilePageBase(BaseModel):
    file_id: str
    content: str
    page_num: int
    created_at: Optional[datetime] = None

class SourceFilePageCreate(SourceFilePageBase):
    pass

class SourceFilePageUpdate(BaseModel):
    content: Optional[str] = None
    page_num: Optional[int] = None