"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4844],{75573:function(e,n){n.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"}},40561:function(e,n,t){t.d(n,{ZP:function(){return g},Yk:function(){return h},TM:function(){return v}});var o=t(11568),r=t(63185),a=t(14747),i=t(33507),d=t(83262),l=t(83559);const c=e=>{let{treeCls:n,treeNodeCls:t,directoryNodeSelectedBg:o,directoryNodeSelectedColor:r,motionDurationMid:a,borderRadius:i,controlItemBgHover:d}=e;return{[`${n}${n}-directory ${t}`]:{[`${n}-node-content-wrapper`]:{position:"static",[`> *:not(${n}-drop-indicator)`]:{position:"relative"},"&:hover":{background:"transparent"},"&:before":{position:"absolute",inset:0,transition:`background-color ${a}`,content:'""',borderRadius:i},"&:hover:before":{background:d}},[`${n}-switcher, ${n}-checkbox, ${n}-draggable-icon`]:{zIndex:1},"&-selected":{[`${n}-switcher, ${n}-draggable-icon`]:{color:r},[`${n}-node-content-wrapper`]:{color:r,background:"transparent","&:before, &:hover:before":{background:o}}}}}},s=new o.E4("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),u=(e,n)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${n.motionDurationSlow}`}}}),f=(e,n)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:n.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${(0,o.bf)(n.lineWidthBold)} solid ${n.colorPrimary}`,borderRadius:"50%",content:'""'}}}),p=(e,n)=>{const{treeCls:t,treeNodeCls:r,treeNodePadding:i,titleHeight:d,indentSize:l,nodeSelectedBg:c,nodeHoverBg:p,colorTextQuaternary:h,controlItemBgActiveDisabled:v}=n;return{[t]:Object.assign(Object.assign({},(0,a.Wf)(n)),{background:n.colorBgContainer,borderRadius:n.borderRadius,transition:`background-color ${n.motionDurationSlow}`,"&-rtl":{direction:"rtl"},[`&${t}-rtl ${t}-switcher_close ${t}-switcher-icon svg`]:{transform:"rotate(90deg)"},[`&-focused:not(:hover):not(${t}-active-focused)`]:Object.assign({},(0,a.oN)(n)),[`${t}-list-holder-inner`]:{alignItems:"flex-start"},[`&${t}-block-node`]:{[`${t}-list-holder-inner`]:{alignItems:"stretch",[`${t}-node-content-wrapper`]:{flex:"auto"},[`${r}.dragging:after`]:{position:"absolute",inset:0,border:`1px solid ${n.colorPrimary}`,opacity:0,animationName:s,animationDuration:n.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none",borderRadius:n.borderRadius}}},[r]:{display:"flex",alignItems:"flex-start",marginBottom:i,lineHeight:(0,o.bf)(d),position:"relative","&:before":{content:'""',position:"absolute",zIndex:1,insetInlineStart:0,width:"100%",top:"100%",height:i},[`&-disabled ${t}-node-content-wrapper`]:{color:n.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}},[`${t}-checkbox-disabled + ${t}-node-selected,&${r}-disabled${r}-selected ${t}-node-content-wrapper`]:{backgroundColor:v},[`${t}-checkbox-disabled`]:{pointerEvents:"unset"},[`&:not(${r}-disabled)`]:{[`${t}-node-content-wrapper`]:{"&:hover":{color:n.nodeHoverColor}}},[`&-active ${t}-node-content-wrapper`]:{background:n.controlItemBgHover},[`&:not(${r}-disabled).filter-node ${t}-title`]:{color:n.colorPrimary,fontWeight:500},"&-draggable":{cursor:"grab",[`${t}-draggable-icon`]:{flexShrink:0,width:d,textAlign:"center",visibility:"visible",color:h},[`&${r}-disabled ${t}-draggable-icon`]:{visibility:"hidden"}}},[`${t}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:l}},[`${t}-draggable-icon`]:{visibility:"hidden"},[`${t}-switcher, ${t}-checkbox`]:{marginInlineEnd:n.calc(n.calc(d).sub(n.controlInteractiveSize)).div(2).equal()},[`${t}-switcher`]:Object.assign(Object.assign({},u(e,n)),{position:"relative",flex:"none",alignSelf:"stretch",width:d,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${n.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:d,height:d,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:n.borderRadius,transition:`all ${n.motionDurationSlow}`},[`&:not(${t}-switcher-noop):hover:before`]:{backgroundColor:n.colorBgTextHover},[`&_close ${t}-switcher-icon svg`]:{transform:"rotate(-90deg)"},"&-loading-icon":{color:n.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:n.calc(d).div(2).equal(),bottom:n.calc(i).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${n.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:n.calc(n.calc(d).div(2).equal()).mul(.8).equal(),height:n.calc(d).div(2).equal(),borderBottom:`1px solid ${n.colorBorder}`,content:'""'}}}),[`${t}-node-content-wrapper`]:Object.assign(Object.assign({position:"relative",minHeight:d,paddingBlock:0,paddingInline:n.paddingXS,background:"transparent",borderRadius:n.borderRadius,cursor:"pointer",transition:`all ${n.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`},f(e,n)),{"&:hover":{backgroundColor:p},[`&${t}-node-selected`]:{color:n.nodeSelectedColor,backgroundColor:c},[`${t}-iconEle`]:{display:"inline-block",width:d,height:d,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}}),[`${t}-unselectable ${t}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${r}.drop-container > [draggable]`]:{boxShadow:`0 0 0 2px ${n.colorPrimary}`},"&-show-line":{[`${t}-indent-unit`]:{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:n.calc(d).div(2).equal(),bottom:n.calc(i).mul(-1).equal(),borderInlineEnd:`1px solid ${n.colorBorder}`,content:'""'},"&-end:before":{display:"none"}},[`${t}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${r}-leaf-last ${t}-switcher-leaf-line:before`]:{top:"auto !important",bottom:"auto !important",height:`${(0,o.bf)(n.calc(d).div(2).equal())} !important`}})}},h=(e,n)=>{const t=`.${e}`,o=`${t}-treenode`,r=n.calc(n.paddingXS).div(2).equal(),a=(0,d.IX)(n,{treeCls:t,treeNodeCls:o,treeNodePadding:r});return[p(e,a),c(a)]},v=e=>{const{controlHeightSM:n,controlItemBgHover:t,controlItemBgActive:o}=e;return{titleHeight:n,indentSize:n,nodeHoverBg:t,nodeHoverColor:e.colorText,nodeSelectedBg:o,nodeSelectedColor:e.colorText}};var g=(0,l.I$)("Tree",((e,n)=>{let{prefixCls:t}=n;return[{[e.componentCls]:(0,r.C2)(`${t}-checkbox`,e)},h(t,e),(0,i.Z)(e)]}),(e=>{const{colorTextLightSolid:n,colorPrimary:t}=e;return Object.assign(Object.assign({},v(e)),{directoryNodeSelectedColor:n,directoryNodeSelectedBg:t})}))},77632:function(e,n,t){t.d(n,{Z:function(){return b}});var o=t(67294),r=t(87462),a={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"},i=t(93771),d=function(e,n){return o.createElement(i.Z,(0,r.Z)({},e,{ref:n,icon:a}))};var l=o.forwardRef(d),c=t(41018),s=t(19267),u={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"},f=function(e,n){return o.createElement(i.Z,(0,r.Z)({},e,{ref:n,icon:u}))};var p=o.forwardRef(f),h={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"},v=function(e,n){return o.createElement(i.Z,(0,r.Z)({},e,{ref:n,icon:h}))};var g=o.forwardRef(v),y=t(93967),k=t.n(y),m=t(96159);var b=e=>{const{prefixCls:n,switcherIcon:t,treeNodeProps:r,showLine:a,switcherLoadingIcon:i}=e,{isLeaf:d,expanded:u,loading:f}=r;if(f)return o.isValidElement(i)?i:o.createElement(s.Z,{className:`${n}-switcher-loading-icon`});let h;if(a&&"object"==typeof a&&(h=a.showLeafIcon),d){if(!a)return null;if("boolean"!=typeof h&&h){const e="function"==typeof h?h(r):h,t=`${n}-switcher-line-custom-icon`;return o.isValidElement(e)?(0,m.Tm)(e,{className:k()(e.props.className||"",t)}):e}return h?o.createElement(c.Z,{className:`${n}-switcher-line-icon`}):o.createElement("span",{className:`${n}-switcher-leaf-line`})}const v=`${n}-switcher-icon`,y="function"==typeof t?t(r):t;return o.isValidElement(y)?(0,m.Tm)(y,{className:k()(y.props.className||"",v)}):void 0!==y?y:a?u?o.createElement(p,{className:`${n}-switcher-line-icon`}):o.createElement(g,{className:`${n}-switcher-line-icon`}):o.createElement(l,{className:v})}},41018:function(e,n,t){var o=t(87462),r=t(67294),a=t(75573),i=t(93771),d=function(e,n){return r.createElement(i.Z,(0,o.Z)({},e,{ref:n,icon:a.Z}))},l=r.forwardRef(d);n.Z=l},86128:function(e,n,t){t.d(n,{Z:function(){return K}});var o=t(87462),r=t(4942),a=t(1413),i=t(97685),d=t(91),l=t(67294),c=t(93967),s=t.n(c),u=t(64217),f=t(27822),p=function(e){for(var n=e.prefixCls,t=e.level,o=e.isStart,a=e.isEnd,i="".concat(n,"-indent-unit"),d=[],c=0;c<t;c+=1)d.push(l.createElement("span",{key:c,className:s()(i,(0,r.Z)((0,r.Z)({},"".concat(i,"-start"),o[c]),"".concat(i,"-end"),a[c]))}));return l.createElement("span",{"aria-hidden":"true",className:"".concat(n,"-indent")},d)},h=l.memo(p),v=t(35381),g=t(1089),y=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],k="open",m="close",b=function(e){var n,t,c,p=e.eventKey,b=e.className,K=e.style,N=e.dragOver,x=e.dragOverGapTop,Z=e.dragOverGapBottom,E=e.isLeaf,C=e.isStart,w=e.isEnd,S=e.expanded,P=e.selected,D=e.checked,O=e.halfChecked,T=e.loading,$=e.domRef,L=e.active,M=e.data,I=e.onMouseMove,R=e.selectable,A=(0,d.Z)(e,y),B=l.useContext(f.k),H=l.useContext(f.y),F=l.useRef(null),_=l.useState(!1),j=(0,i.Z)(_,2),z=j[0],q=j[1],W=!!(B.disabled||e.disabled||null!==(n=H.nodeDisabled)&&void 0!==n&&n.call(H,M)),G=l.useMemo((function(){return!(!B.checkable||!1===e.checkable)&&B.checkable}),[B.checkable,e.checkable]),U=function(n){W||G&&!e.disableCheckbox&&B.onNodeCheck(n,(0,g.F)(e),!D)},V=l.useMemo((function(){return"boolean"==typeof R?R:B.selectable}),[R,B.selectable]),X=function(n){B.onNodeClick(n,(0,g.F)(e)),V?function(n){W||B.onNodeSelect(n,(0,g.F)(e))}(n):U(n)},Y=function(n){B.onNodeDoubleClick(n,(0,g.F)(e))},Q=function(n){B.onNodeMouseEnter(n,(0,g.F)(e))},J=function(n){B.onNodeMouseLeave(n,(0,g.F)(e))},ee=function(n){B.onNodeContextMenu(n,(0,g.F)(e))},ne=l.useMemo((function(){return!(!B.draggable||B.draggable.nodeDraggable&&!B.draggable.nodeDraggable(M))}),[B.draggable,M]),te=function(n){T||B.onNodeExpand(n,(0,g.F)(e))},oe=l.useMemo((function(){var e=((0,v.Z)(B.keyEntities,p)||{}).children;return Boolean((e||[]).length)}),[B.keyEntities,p]),re=l.useMemo((function(){return!1!==E&&(E||!B.loadData&&!oe||B.loadData&&e.loaded&&!oe)}),[E,B.loadData,oe,e.loaded]);l.useEffect((function(){T||"function"!=typeof B.loadData||!S||re||e.loaded||B.onNodeLoad((0,g.F)(e))}),[T,B.loadData,B.onNodeLoad,S,re,e]);var ae=l.useMemo((function(){var e;return null!==(e=B.draggable)&&void 0!==e&&e.icon?l.createElement("span",{className:"".concat(B.prefixCls,"-draggable-icon")},B.draggable.icon):null}),[B.draggable]),ie=function(n){var t=e.switcherIcon||B.switcherIcon;return"function"==typeof t?t((0,a.Z)((0,a.Z)({},e),{},{isLeaf:n})):t},de=l.useMemo((function(){if(!G)return null;var n="boolean"!=typeof G?G:null;return l.createElement("span",{className:s()("".concat(B.prefixCls,"-checkbox"),(0,r.Z)((0,r.Z)((0,r.Z)({},"".concat(B.prefixCls,"-checkbox-checked"),D),"".concat(B.prefixCls,"-checkbox-indeterminate"),!D&&O),"".concat(B.prefixCls,"-checkbox-disabled"),W||e.disableCheckbox)),onClick:U,role:"checkbox","aria-checked":O?"mixed":D,"aria-disabled":W||e.disableCheckbox,"aria-label":"Select ".concat("string"==typeof e.title?e.title:"tree node")},n)}),[G,D,O,W,e.disableCheckbox,e.title]),le=l.useMemo((function(){return re?null:S?k:m}),[re,S]),ce=l.useMemo((function(){return l.createElement("span",{className:s()("".concat(B.prefixCls,"-iconEle"),"".concat(B.prefixCls,"-icon__").concat(le||"docu"),(0,r.Z)({},"".concat(B.prefixCls,"-icon_loading"),T))})}),[B.prefixCls,le,T]),se=l.useMemo((function(){var n=Boolean(B.draggable);return!e.disabled&&n&&B.dragOverNodeKey===p?B.dropIndicatorRender({dropPosition:B.dropPosition,dropLevelOffset:B.dropLevelOffset,indent:B.indent,prefixCls:B.prefixCls,direction:B.direction}):null}),[B.dropPosition,B.dropLevelOffset,B.indent,B.prefixCls,B.direction,B.draggable,B.dragOverNodeKey,B.dropIndicatorRender]),ue=l.useMemo((function(){var n,t,o=e.title,a=void 0===o?"---":o,i="".concat(B.prefixCls,"-node-content-wrapper");if(B.showIcon){var d=e.icon||B.icon;n=d?l.createElement("span",{className:s()("".concat(B.prefixCls,"-iconEle"),"".concat(B.prefixCls,"-icon__customize"))},"function"==typeof d?d(e):d):ce}else B.loadData&&T&&(n=ce);return t="function"==typeof a?a(M):B.titleRender?B.titleRender(M):a,l.createElement("span",{ref:F,title:"string"==typeof a?a:"",className:s()(i,"".concat(i,"-").concat(le||"normal"),(0,r.Z)({},"".concat(B.prefixCls,"-node-selected"),!W&&(P||z))),onMouseEnter:Q,onMouseLeave:J,onContextMenu:ee,onClick:X,onDoubleClick:Y},n,l.createElement("span",{className:"".concat(B.prefixCls,"-title")},t),se)}),[B.prefixCls,B.showIcon,e,B.icon,ce,B.titleRender,M,le,Q,J,ee,X,Y]),fe=(0,u.Z)(A,{aria:!0,data:!0}),pe=((0,v.Z)(B.keyEntities,p)||{}).level,he=w[w.length-1],ve=!W&&ne,ge=B.draggingNodeKey===p,ye=void 0!==R?{"aria-selected":!!R}:void 0;return l.createElement("div",(0,o.Z)({ref:$,role:"treeitem","aria-expanded":E?void 0:S,className:s()(b,"".concat(B.prefixCls,"-treenode"),(c={},(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)(c,"".concat(B.prefixCls,"-treenode-disabled"),W),"".concat(B.prefixCls,"-treenode-switcher-").concat(S?"open":"close"),!E),"".concat(B.prefixCls,"-treenode-checkbox-checked"),D),"".concat(B.prefixCls,"-treenode-checkbox-indeterminate"),O),"".concat(B.prefixCls,"-treenode-selected"),P),"".concat(B.prefixCls,"-treenode-loading"),T),"".concat(B.prefixCls,"-treenode-active"),L),"".concat(B.prefixCls,"-treenode-leaf-last"),he),"".concat(B.prefixCls,"-treenode-draggable"),ne),"dragging",ge),(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)(c,"drop-target",B.dropTargetKey===p),"drop-container",B.dropContainerKey===p),"drag-over",!W&&N),"drag-over-gap-top",!W&&x),"drag-over-gap-bottom",!W&&Z),"filter-node",null===(t=B.filterTreeNode)||void 0===t?void 0:t.call(B,(0,g.F)(e))),"".concat(B.prefixCls,"-treenode-leaf"),re))),style:K,draggable:ve,onDragStart:ve?function(n){n.stopPropagation(),q(!0),B.onNodeDragStart(n,e);try{n.dataTransfer.setData("text/plain","")}catch(e){}}:void 0,onDragEnter:ne?function(n){n.preventDefault(),n.stopPropagation(),B.onNodeDragEnter(n,e)}:void 0,onDragOver:ne?function(n){n.preventDefault(),n.stopPropagation(),B.onNodeDragOver(n,e)}:void 0,onDragLeave:ne?function(n){n.stopPropagation(),B.onNodeDragLeave(n,e)}:void 0,onDrop:ne?function(n){n.preventDefault(),n.stopPropagation(),q(!1),B.onNodeDrop(n,e)}:void 0,onDragEnd:ne?function(n){n.stopPropagation(),q(!1),B.onNodeDragEnd(n,e)}:void 0,onMouseMove:I},ye,fe),l.createElement(h,{prefixCls:B.prefixCls,level:pe,isStart:C,isEnd:w}),ae,function(){if(re){var e=ie(!0);return!1!==e?l.createElement("span",{className:s()("".concat(B.prefixCls,"-switcher"),"".concat(B.prefixCls,"-switcher-noop"))},e):null}var n=ie(!1);return!1!==n?l.createElement("span",{onClick:te,className:s()("".concat(B.prefixCls,"-switcher"),"".concat(B.prefixCls,"-switcher_").concat(S?k:m))},n):null}(),de,ue)};b.isTreeNode=1;var K=b},27822:function(e,n,t){t.d(n,{k:function(){return r},y:function(){return a}});var o=t(67294),r=o.createContext(null),a=o.createContext({})},70593:function(e,n,t){t.d(n,{OF:function(){return w.Z},y6:function(){return m.y},ZP:function(){return U}});var o=t(87462),r=t(71002),a=t(1413),i=t(74902),d=t(15671),l=t(43144),c=t(97326),s=t(60136),u=t(29388),f=t(4942),p=t(93967),h=t.n(p),v=t(15105),g=t(64217),y=t(80334),k=t(67294),m=t(27822);var b=function(e){var n=e.dropPosition,t=e.dropLevelOffset,o=e.indent,r={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(n){case-1:r.top=0,r.left=-t*o;break;case 1:r.bottom=0,r.left=-t*o;break;case 0:r.bottom=0,r.left=o}return k.createElement("div",{style:r})};function K(e){if(null==e)throw new TypeError("Cannot destructure "+e)}var N=t(97685),x=t(91),Z=t(8410),E=t(85344),C=t(29372),w=t(86128);var S=function(e,n){var t=k.useState(!1),o=(0,N.Z)(t,2),r=o[0],a=o[1];(0,Z.Z)((function(){if(r)return e(),function(){n()}}),[r]),(0,Z.Z)((function(){return a(!0),function(){a(!1)}}),[])},P=t(1089),D=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"];var O=k.forwardRef((function(e,n){var t=e.className,r=e.style,a=e.motion,i=e.motionNodes,d=e.motionType,l=e.onMotionStart,c=e.onMotionEnd,s=e.active,u=e.treeNodeRequiredProps,f=(0,x.Z)(e,D),p=k.useState(!0),v=(0,N.Z)(p,2),g=v[0],y=v[1],b=k.useContext(m.k).prefixCls,E=i&&"hide"!==d;(0,Z.Z)((function(){i&&E!==g&&y(E)}),[i]);var O=k.useRef(!1),T=function(){i&&!O.current&&(O.current=!0,c())};S((function(){i&&l()}),T);return i?k.createElement(C.ZP,(0,o.Z)({ref:n,visible:g},a,{motionAppear:"show"===d,onVisibleChanged:function(e){E===e&&T()}}),(function(e,n){var t=e.className,r=e.style;return k.createElement("div",{ref:n,className:h()("".concat(b,"-treenode-motion"),t),style:r},i.map((function(e){var n=Object.assign({},(K(e.data),e.data)),t=e.title,r=e.key,a=e.isStart,i=e.isEnd;delete n.children;var d=(0,P.H8)(r,u);return k.createElement(w.Z,(0,o.Z)({},n,d,{title:t,active:s,data:e.data,key:r,isStart:a,isEnd:i}))})))})):k.createElement(w.Z,(0,o.Z)({domRef:n,className:t,style:r},f,{active:s}))}));function T(e,n,t){var o=e.findIndex((function(e){return e.key===t})),r=e[o+1],a=n.findIndex((function(e){return e.key===t}));if(r){var i=n.findIndex((function(e){return e.key===r.key}));return n.slice(a+1,i)}return n.slice(a+1)}var $=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","scrollWidth","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],L={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},M=function(){},I="RC_TREE_MOTION_".concat(Math.random()),R={key:I},A={key:I,level:0,index:0,pos:"0",node:R,nodes:[R]},B={parent:null,children:[],pos:A.pos,data:R,title:null,key:I,isStart:[],isEnd:[]};function H(e,n,t,o){return!1!==n&&t?e.slice(0,Math.ceil(t/o)+1):e}function F(e){var n=e.key,t=e.pos;return(0,P.km)(n,t)}var _=k.forwardRef((function(e,n){var t=e.prefixCls,r=e.data,a=(e.selectable,e.checkable,e.expandedKeys),i=e.selectedKeys,d=e.checkedKeys,l=e.loadedKeys,c=e.loadingKeys,s=e.halfCheckedKeys,u=e.keyEntities,f=e.disabled,p=e.dragging,h=e.dragOverNodeKey,v=e.dropPosition,g=e.motion,y=e.height,m=e.itemHeight,b=e.virtual,C=e.scrollWidth,w=e.focusable,S=e.activeItem,D=e.focused,R=e.tabIndex,A=e.onKeyDown,_=e.onFocus,j=e.onBlur,z=e.onActiveChange,q=e.onListChangeStart,W=e.onListChangeEnd,G=(0,x.Z)(e,$),U=k.useRef(null),V=k.useRef(null);k.useImperativeHandle(n,(function(){return{scrollTo:function(e){U.current.scrollTo(e)},getIndentWidth:function(){return V.current.offsetWidth}}}));var X=k.useState(a),Y=(0,N.Z)(X,2),Q=Y[0],J=Y[1],ee=k.useState(r),ne=(0,N.Z)(ee,2),te=ne[0],oe=ne[1],re=k.useState(r),ae=(0,N.Z)(re,2),ie=ae[0],de=ae[1],le=k.useState([]),ce=(0,N.Z)(le,2),se=ce[0],ue=ce[1],fe=k.useState(null),pe=(0,N.Z)(fe,2),he=pe[0],ve=pe[1],ge=k.useRef(r);function ye(){var e=ge.current;oe(e),de(e),ue([]),ve(null),W()}ge.current=r,(0,Z.Z)((function(){J(a);var e=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],t=e.length,o=n.length;if(1!==Math.abs(t-o))return{add:!1,key:null};function r(e,n){var t=new Map;e.forEach((function(e){t.set(e,!0)}));var o=n.filter((function(e){return!t.has(e)}));return 1===o.length?o[0]:null}return t<o?{add:!0,key:r(e,n)}:{add:!1,key:r(n,e)}}(Q,a);if(null!==e.key)if(e.add){var n=te.findIndex((function(n){return n.key===e.key})),t=H(T(te,r,e.key),b,y,m),o=te.slice();o.splice(n+1,0,B),de(o),ue(t),ve("show")}else{var i=r.findIndex((function(n){return n.key===e.key})),d=H(T(r,te,e.key),b,y,m),l=r.slice();l.splice(i+1,0,B),de(l),ue(d),ve("hide")}else te!==r&&(oe(r),de(r))}),[a,r]),k.useEffect((function(){p||ye()}),[p]);var ke=g?ie:r,me={expandedKeys:a,selectedKeys:i,loadedKeys:l,loadingKeys:c,checkedKeys:d,halfCheckedKeys:s,dragOverNodeKey:h,dropPosition:v,keyEntities:u};return k.createElement(k.Fragment,null,D&&S&&k.createElement("span",{style:L,"aria-live":"assertive"},function(e){for(var n=String(e.data.key),t=e;t.parent;)t=t.parent,n="".concat(t.data.key," > ").concat(n);return n}(S)),k.createElement("div",null,k.createElement("input",{style:L,disabled:!1===w||f,tabIndex:!1!==w?R:null,onKeyDown:A,onFocus:_,onBlur:j,value:"",onChange:M,"aria-label":"for screen reader"})),k.createElement("div",{className:"".concat(t,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},k.createElement("div",{className:"".concat(t,"-indent")},k.createElement("div",{ref:V,className:"".concat(t,"-indent-unit")}))),k.createElement(E.Z,(0,o.Z)({},G,{data:ke,itemKey:F,height:y,fullHeight:!1,virtual:b,itemHeight:m,scrollWidth:C,prefixCls:"".concat(t,"-list"),ref:U,role:"tree",onVisibleChange:function(e){e.every((function(e){return F(e)!==I}))&&ye()}}),(function(e){var n=e.pos,t=Object.assign({},(K(e.data),e.data)),r=e.title,a=e.key,i=e.isStart,d=e.isEnd,l=(0,P.km)(a,n);delete t.key,delete t.children;var c=(0,P.H8)(l,me);return k.createElement(O,(0,o.Z)({},t,c,{title:r,active:!!S&&a===S.key,pos:n,data:e.data,isStart:i,isEnd:d,motion:g,motionNodes:a===I?se:null,motionType:he,onMotionStart:q,onMotionEnd:ye,treeNodeRequiredProps:me,onMouseMove:function(){z(null)}}))})))}));var j=_,z=t(10225),q=t(17341),W=t(35381),G=function(e){(0,s.Z)(t,e);var n=(0,u.Z)(t);function t(){var e;(0,d.Z)(this,t);for(var o=arguments.length,r=new Array(o),l=0;l<o;l++)r[l]=arguments[l];return e=n.call.apply(n,[this].concat(r)),(0,f.Z)((0,c.Z)(e),"destroyed",!1),(0,f.Z)((0,c.Z)(e),"delayedDragEnterLogic",void 0),(0,f.Z)((0,c.Z)(e),"loadingRetryTimes",{}),(0,f.Z)((0,c.Z)(e),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:(0,P.w$)()}),(0,f.Z)((0,c.Z)(e),"dragStartMousePosition",null),(0,f.Z)((0,c.Z)(e),"dragNodeProps",null),(0,f.Z)((0,c.Z)(e),"currentMouseOverDroppableNodeKey",null),(0,f.Z)((0,c.Z)(e),"listRef",k.createRef()),(0,f.Z)((0,c.Z)(e),"onNodeDragStart",(function(n,t){var o=e.state,r=o.expandedKeys,a=o.keyEntities,i=e.props.onDragStart,d=t.eventKey;e.dragNodeProps=t,e.dragStartMousePosition={x:n.clientX,y:n.clientY};var l=(0,z._5)(r,d);e.setState({draggingNodeKey:d,dragChildrenKeys:(0,z.wA)(d,a),indent:e.listRef.current.getIndentWidth()}),e.setExpandedKeys(l),window.addEventListener("dragend",e.onWindowDragEnd),null==i||i({event:n,node:(0,P.F)(t)})})),(0,f.Z)((0,c.Z)(e),"onNodeDragEnter",(function(n,t){var o=e.state,r=o.expandedKeys,a=o.keyEntities,d=o.dragChildrenKeys,l=o.flattenNodes,c=o.indent,s=e.props,u=s.onDragEnter,f=s.onExpand,p=s.allowDrop,h=s.direction,v=t.pos,g=t.eventKey;if(e.currentMouseOverDroppableNodeKey!==g&&(e.currentMouseOverDroppableNodeKey=g),e.dragNodeProps){var y=(0,z.OM)(n,e.dragNodeProps,t,c,e.dragStartMousePosition,p,l,a,r,h),k=y.dropPosition,m=y.dropLevelOffset,b=y.dropTargetKey,K=y.dropContainerKey,N=y.dropTargetPos,x=y.dropAllowed,Z=y.dragOverNodeKey;!d.includes(b)&&x?(e.delayedDragEnterLogic||(e.delayedDragEnterLogic={}),Object.keys(e.delayedDragEnterLogic).forEach((function(n){clearTimeout(e.delayedDragEnterLogic[n])})),e.dragNodeProps.eventKey!==t.eventKey&&(n.persist(),e.delayedDragEnterLogic[v]=window.setTimeout((function(){if(null!==e.state.draggingNodeKey){var o=(0,i.Z)(r),d=(0,W.Z)(a,t.eventKey);d&&(d.children||[]).length&&(o=(0,z.L0)(r,t.eventKey)),e.props.hasOwnProperty("expandedKeys")||e.setExpandedKeys(o),null==f||f(o,{node:(0,P.F)(t),expanded:!0,nativeEvent:n.nativeEvent})}}),800)),e.dragNodeProps.eventKey!==b||0!==m?(e.setState({dragOverNodeKey:Z,dropPosition:k,dropLevelOffset:m,dropTargetKey:b,dropContainerKey:K,dropTargetPos:N,dropAllowed:x}),null==u||u({event:n,node:(0,P.F)(t),expandedKeys:r})):e.resetDragState()):e.resetDragState()}else e.resetDragState()})),(0,f.Z)((0,c.Z)(e),"onNodeDragOver",(function(n,t){var o=e.state,r=o.dragChildrenKeys,a=o.flattenNodes,i=o.keyEntities,d=o.expandedKeys,l=o.indent,c=e.props,s=c.onDragOver,u=c.allowDrop,f=c.direction;if(e.dragNodeProps){var p=(0,z.OM)(n,e.dragNodeProps,t,l,e.dragStartMousePosition,u,a,i,d,f),h=p.dropPosition,v=p.dropLevelOffset,g=p.dropTargetKey,y=p.dropContainerKey,k=p.dropTargetPos,m=p.dropAllowed,b=p.dragOverNodeKey;!r.includes(g)&&m&&(e.dragNodeProps.eventKey===g&&0===v?null===e.state.dropPosition&&null===e.state.dropLevelOffset&&null===e.state.dropTargetKey&&null===e.state.dropContainerKey&&null===e.state.dropTargetPos&&!1===e.state.dropAllowed&&null===e.state.dragOverNodeKey||e.resetDragState():h===e.state.dropPosition&&v===e.state.dropLevelOffset&&g===e.state.dropTargetKey&&y===e.state.dropContainerKey&&k===e.state.dropTargetPos&&m===e.state.dropAllowed&&b===e.state.dragOverNodeKey||e.setState({dropPosition:h,dropLevelOffset:v,dropTargetKey:g,dropContainerKey:y,dropTargetPos:k,dropAllowed:m,dragOverNodeKey:b}),null==s||s({event:n,node:(0,P.F)(t)}))}})),(0,f.Z)((0,c.Z)(e),"onNodeDragLeave",(function(n,t){e.currentMouseOverDroppableNodeKey!==t.eventKey||n.currentTarget.contains(n.relatedTarget)||(e.resetDragState(),e.currentMouseOverDroppableNodeKey=null);var o=e.props.onDragLeave;null==o||o({event:n,node:(0,P.F)(t)})})),(0,f.Z)((0,c.Z)(e),"onWindowDragEnd",(function(n){e.onNodeDragEnd(n,null,!0),window.removeEventListener("dragend",e.onWindowDragEnd)})),(0,f.Z)((0,c.Z)(e),"onNodeDragEnd",(function(n,t){var o=e.props.onDragEnd;e.setState({dragOverNodeKey:null}),e.cleanDragState(),null==o||o({event:n,node:(0,P.F)(t)}),e.dragNodeProps=null,window.removeEventListener("dragend",e.onWindowDragEnd)})),(0,f.Z)((0,c.Z)(e),"onNodeDrop",(function(n,t){var o,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=e.state,d=i.dragChildrenKeys,l=i.dropPosition,c=i.dropTargetKey,s=i.dropTargetPos,u=i.dropAllowed;if(u){var f=e.props.onDrop;if(e.setState({dragOverNodeKey:null}),e.cleanDragState(),null!==c){var p=(0,a.Z)((0,a.Z)({},(0,P.H8)(c,e.getTreeNodeRequiredProps())),{},{active:(null===(o=e.getActiveItem())||void 0===o?void 0:o.key)===c,data:(0,W.Z)(e.state.keyEntities,c).node}),h=d.includes(c);(0,y.ZP)(!h,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var v=(0,z.yx)(s),g={event:n,node:(0,P.F)(p),dragNode:e.dragNodeProps?(0,P.F)(e.dragNodeProps):null,dragNodesKeys:[e.dragNodeProps.eventKey].concat(d),dropToGap:0!==l,dropPosition:l+Number(v[v.length-1])};r||null==f||f(g),e.dragNodeProps=null}}})),(0,f.Z)((0,c.Z)(e),"cleanDragState",(function(){null!==e.state.draggingNodeKey&&e.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),e.dragStartMousePosition=null,e.currentMouseOverDroppableNodeKey=null})),(0,f.Z)((0,c.Z)(e),"triggerExpandActionExpand",(function(n,t){var o=e.state,r=o.expandedKeys,i=o.flattenNodes,d=t.expanded,l=t.key;if(!(t.isLeaf||n.shiftKey||n.metaKey||n.ctrlKey)){var c=i.filter((function(e){return e.key===l}))[0],s=(0,P.F)((0,a.Z)((0,a.Z)({},(0,P.H8)(l,e.getTreeNodeRequiredProps())),{},{data:c.data}));e.setExpandedKeys(d?(0,z._5)(r,l):(0,z.L0)(r,l)),e.onNodeExpand(n,s)}})),(0,f.Z)((0,c.Z)(e),"onNodeClick",(function(n,t){var o=e.props,r=o.onClick;"click"===o.expandAction&&e.triggerExpandActionExpand(n,t),null==r||r(n,t)})),(0,f.Z)((0,c.Z)(e),"onNodeDoubleClick",(function(n,t){var o=e.props,r=o.onDoubleClick;"doubleClick"===o.expandAction&&e.triggerExpandActionExpand(n,t),null==r||r(n,t)})),(0,f.Z)((0,c.Z)(e),"onNodeSelect",(function(n,t){var o=e.state.selectedKeys,r=e.state,a=r.keyEntities,i=r.fieldNames,d=e.props,l=d.onSelect,c=d.multiple,s=t.selected,u=t[i.key],f=!s,p=(o=f?c?(0,z.L0)(o,u):[u]:(0,z._5)(o,u)).map((function(e){var n=(0,W.Z)(a,e);return n?n.node:null})).filter(Boolean);e.setUncontrolledState({selectedKeys:o}),null==l||l(o,{event:"select",selected:f,node:t,selectedNodes:p,nativeEvent:n.nativeEvent})})),(0,f.Z)((0,c.Z)(e),"onNodeCheck",(function(n,t,o){var r,a=e.state,d=a.keyEntities,l=a.checkedKeys,c=a.halfCheckedKeys,s=e.props,u=s.checkStrictly,f=s.onCheck,p=t.key,h={event:"check",node:t,checked:o,nativeEvent:n.nativeEvent};if(u){var v=o?(0,z.L0)(l,p):(0,z._5)(l,p);r={checked:v,halfChecked:(0,z._5)(c,p)},h.checkedNodes=v.map((function(e){return(0,W.Z)(d,e)})).filter(Boolean).map((function(e){return e.node})),e.setUncontrolledState({checkedKeys:v})}else{var g=(0,q.S)([].concat((0,i.Z)(l),[p]),!0,d),y=g.checkedKeys,k=g.halfCheckedKeys;if(!o){var m=new Set(y);m.delete(p);var b=(0,q.S)(Array.from(m),{checked:!1,halfCheckedKeys:k},d);y=b.checkedKeys,k=b.halfCheckedKeys}r=y,h.checkedNodes=[],h.checkedNodesPositions=[],h.halfCheckedKeys=k,y.forEach((function(e){var n=(0,W.Z)(d,e);if(n){var t=n.node,o=n.pos;h.checkedNodes.push(t),h.checkedNodesPositions.push({node:t,pos:o})}})),e.setUncontrolledState({checkedKeys:y},!1,{halfCheckedKeys:k})}null==f||f(r,h)})),(0,f.Z)((0,c.Z)(e),"onNodeLoad",(function(n){var t,o=n.key,r=e.state.keyEntities,a=(0,W.Z)(r,o);if(null==a||null===(t=a.children)||void 0===t||!t.length){var i=new Promise((function(t,r){e.setState((function(a){var i=a.loadedKeys,d=void 0===i?[]:i,l=a.loadingKeys,c=void 0===l?[]:l,s=e.props,u=s.loadData,f=s.onLoad;return!u||d.includes(o)||c.includes(o)?null:(u(n).then((function(){var r=e.state.loadedKeys,a=(0,z.L0)(r,o);null==f||f(a,{event:"load",node:n}),e.setUncontrolledState({loadedKeys:a}),e.setState((function(e){return{loadingKeys:(0,z._5)(e.loadingKeys,o)}})),t()})).catch((function(n){if(e.setState((function(e){return{loadingKeys:(0,z._5)(e.loadingKeys,o)}})),e.loadingRetryTimes[o]=(e.loadingRetryTimes[o]||0)+1,e.loadingRetryTimes[o]>=10){var a=e.state.loadedKeys;(0,y.ZP)(!1,"Retry for `loadData` many times but still failed. No more retry."),e.setUncontrolledState({loadedKeys:(0,z.L0)(a,o)}),t()}r(n)})),{loadingKeys:(0,z.L0)(c,o)})}))}));return i.catch((function(){})),i}})),(0,f.Z)((0,c.Z)(e),"onNodeMouseEnter",(function(n,t){var o=e.props.onMouseEnter;null==o||o({event:n,node:t})})),(0,f.Z)((0,c.Z)(e),"onNodeMouseLeave",(function(n,t){var o=e.props.onMouseLeave;null==o||o({event:n,node:t})})),(0,f.Z)((0,c.Z)(e),"onNodeContextMenu",(function(n,t){var o=e.props.onRightClick;o&&(n.preventDefault(),o({event:n,node:t}))})),(0,f.Z)((0,c.Z)(e),"onFocus",(function(){var n=e.props.onFocus;e.setState({focused:!0});for(var t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];null==n||n.apply(void 0,o)})),(0,f.Z)((0,c.Z)(e),"onBlur",(function(){var n=e.props.onBlur;e.setState({focused:!1}),e.onActiveChange(null);for(var t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];null==n||n.apply(void 0,o)})),(0,f.Z)((0,c.Z)(e),"getTreeNodeRequiredProps",(function(){var n=e.state;return{expandedKeys:n.expandedKeys||[],selectedKeys:n.selectedKeys||[],loadedKeys:n.loadedKeys||[],loadingKeys:n.loadingKeys||[],checkedKeys:n.checkedKeys||[],halfCheckedKeys:n.halfCheckedKeys||[],dragOverNodeKey:n.dragOverNodeKey,dropPosition:n.dropPosition,keyEntities:n.keyEntities}})),(0,f.Z)((0,c.Z)(e),"setExpandedKeys",(function(n){var t=e.state,o=t.treeData,r=t.fieldNames,a=(0,P.oH)(o,n,r);e.setUncontrolledState({expandedKeys:n,flattenNodes:a},!0)})),(0,f.Z)((0,c.Z)(e),"onNodeExpand",(function(n,t){var o=e.state.expandedKeys,r=e.state,a=r.listChanging,i=r.fieldNames,d=e.props,l=d.onExpand,c=d.loadData,s=t.expanded,u=t[i.key];if(!a){var f=o.includes(u),p=!s;if((0,y.ZP)(s&&f||!s&&!f,"Expand state not sync with index check"),o=p?(0,z.L0)(o,u):(0,z._5)(o,u),e.setExpandedKeys(o),null==l||l(o,{node:t,expanded:p,nativeEvent:n.nativeEvent}),p&&c){var h=e.onNodeLoad(t);h&&h.then((function(){var n=(0,P.oH)(e.state.treeData,o,i);e.setUncontrolledState({flattenNodes:n})})).catch((function(){var n=e.state.expandedKeys,t=(0,z._5)(n,u);e.setExpandedKeys(t)}))}}})),(0,f.Z)((0,c.Z)(e),"onListChangeStart",(function(){e.setUncontrolledState({listChanging:!0})})),(0,f.Z)((0,c.Z)(e),"onListChangeEnd",(function(){setTimeout((function(){e.setUncontrolledState({listChanging:!1})}))})),(0,f.Z)((0,c.Z)(e),"onActiveChange",(function(n){var t=e.state.activeKey,o=e.props,r=o.onActiveChange,a=o.itemScrollOffset,i=void 0===a?0:a;t!==n&&(e.setState({activeKey:n}),null!==n&&e.scrollTo({key:n,offset:i}),null==r||r(n))})),(0,f.Z)((0,c.Z)(e),"getActiveItem",(function(){var n=e.state,t=n.activeKey,o=n.flattenNodes;return null===t?null:o.find((function(e){return e.key===t}))||null})),(0,f.Z)((0,c.Z)(e),"offsetActiveKey",(function(n){var t=e.state,o=t.flattenNodes,r=t.activeKey,a=o.findIndex((function(e){return e.key===r}));-1===a&&n<0&&(a=o.length);var i=o[a=(a+n+o.length)%o.length];if(i){var d=i.key;e.onActiveChange(d)}else e.onActiveChange(null)})),(0,f.Z)((0,c.Z)(e),"onKeyDown",(function(n){var t=e.state,o=t.activeKey,r=t.expandedKeys,i=t.checkedKeys,d=t.fieldNames,l=e.props,c=l.onKeyDown,s=l.checkable,u=l.selectable;switch(n.which){case v.Z.UP:e.offsetActiveKey(-1),n.preventDefault();break;case v.Z.DOWN:e.offsetActiveKey(1),n.preventDefault()}var f=e.getActiveItem();if(f&&f.data){var p=e.getTreeNodeRequiredProps(),h=!1===f.data.isLeaf||!!(f.data[d.children]||[]).length,g=(0,P.F)((0,a.Z)((0,a.Z)({},(0,P.H8)(o,p)),{},{data:f.data,active:!0}));switch(n.which){case v.Z.LEFT:h&&r.includes(o)?e.onNodeExpand({},g):f.parent&&e.onActiveChange(f.parent.key),n.preventDefault();break;case v.Z.RIGHT:h&&!r.includes(o)?e.onNodeExpand({},g):f.children&&f.children.length&&e.onActiveChange(f.children[0].key),n.preventDefault();break;case v.Z.ENTER:case v.Z.SPACE:!s||g.disabled||!1===g.checkable||g.disableCheckbox?s||!u||g.disabled||!1===g.selectable||e.onNodeSelect({},g):e.onNodeCheck({},g,!i.includes(o))}}null==c||c(n)})),(0,f.Z)((0,c.Z)(e),"setUncontrolledState",(function(n){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!e.destroyed){var r=!1,i=!0,d={};Object.keys(n).forEach((function(t){e.props.hasOwnProperty(t)?i=!1:(r=!0,d[t]=n[t])})),!r||t&&!i||e.setState((0,a.Z)((0,a.Z)({},d),o))}})),(0,f.Z)((0,c.Z)(e),"scrollTo",(function(n){e.listRef.current.scrollTo(n)})),e}return(0,l.Z)(t,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var e=this.props,n=e.activeKey,t=e.itemScrollOffset,o=void 0===t?0:t;void 0!==n&&n!==this.state.activeKey&&(this.setState({activeKey:n}),null!==n&&this.scrollTo({key:n,offset:o}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var e,n=this.state,t=n.focused,a=n.flattenNodes,i=n.keyEntities,d=n.draggingNodeKey,l=n.activeKey,c=n.dropLevelOffset,s=n.dropContainerKey,u=n.dropTargetKey,p=n.dropPosition,v=n.dragOverNodeKey,y=n.indent,b=this.props,K=b.prefixCls,N=b.className,x=b.style,Z=b.showLine,E=b.focusable,C=b.tabIndex,w=void 0===C?0:C,S=b.selectable,P=b.showIcon,D=b.icon,O=b.switcherIcon,T=b.draggable,$=b.checkable,L=b.checkStrictly,M=b.disabled,I=b.motion,R=b.loadData,A=b.filterTreeNode,B=b.height,H=b.itemHeight,F=b.scrollWidth,_=b.virtual,z=b.titleRender,q=b.dropIndicatorRender,W=b.onContextMenu,G=b.onScroll,U=b.direction,V=b.rootClassName,X=b.rootStyle,Y=(0,g.Z)(this.props,{aria:!0,data:!0});T&&(e="object"===(0,r.Z)(T)?T:"function"==typeof T?{nodeDraggable:T}:{});var Q={prefixCls:K,selectable:S,showIcon:P,icon:D,switcherIcon:O,draggable:e,draggingNodeKey:d,checkable:$,checkStrictly:L,disabled:M,keyEntities:i,dropLevelOffset:c,dropContainerKey:s,dropTargetKey:u,dropPosition:p,dragOverNodeKey:v,indent:y,direction:U,dropIndicatorRender:q,loadData:R,filterTreeNode:A,titleRender:z,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop};return k.createElement(m.k.Provider,{value:Q},k.createElement("div",{className:h()(K,N,V,(0,f.Z)((0,f.Z)((0,f.Z)({},"".concat(K,"-show-line"),Z),"".concat(K,"-focused"),t),"".concat(K,"-active-focused"),null!==l)),style:X},k.createElement(j,(0,o.Z)({ref:this.listRef,prefixCls:K,style:x,data:a,disabled:M,selectable:S,checkable:!!$,motion:I,dragging:null!==d,height:B,itemHeight:H,virtual:_,focusable:E,focused:t,tabIndex:w,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:W,onScroll:G,scrollWidth:F},this.getTreeNodeRequiredProps(),Y))))}}],[{key:"getDerivedStateFromProps",value:function(e,n){var t,o=n.prevProps,r={prevProps:e};function i(n){return!o&&e.hasOwnProperty(n)||o&&o[n]!==e[n]}var d=n.fieldNames;if(i("fieldNames")&&(d=(0,P.w$)(e.fieldNames),r.fieldNames=d),i("treeData")?t=e.treeData:i("children")&&((0,y.ZP)(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),t=(0,P.zn)(e.children)),t){r.treeData=t;var l=(0,P.I8)(t,{fieldNames:d});r.keyEntities=(0,a.Z)((0,f.Z)({},I,A),l.keyEntities)}var c,s=r.keyEntities||n.keyEntities;if(i("expandedKeys")||o&&i("autoExpandParent"))r.expandedKeys=e.autoExpandParent||!o&&e.defaultExpandParent?(0,z.r7)(e.expandedKeys,s):e.expandedKeys;else if(!o&&e.defaultExpandAll){var u=(0,a.Z)({},s);delete u[I];var p=[];Object.keys(u).forEach((function(e){var n=u[e];n.children&&n.children.length&&p.push(n.key)})),r.expandedKeys=p}else!o&&e.defaultExpandedKeys&&(r.expandedKeys=e.autoExpandParent||e.defaultExpandParent?(0,z.r7)(e.defaultExpandedKeys,s):e.defaultExpandedKeys);if(r.expandedKeys||delete r.expandedKeys,t||r.expandedKeys){var h=(0,P.oH)(t||n.treeData,r.expandedKeys||n.expandedKeys,d);r.flattenNodes=h}if((e.selectable&&(i("selectedKeys")?r.selectedKeys=(0,z.BT)(e.selectedKeys,e):!o&&e.defaultSelectedKeys&&(r.selectedKeys=(0,z.BT)(e.defaultSelectedKeys,e))),e.checkable)&&(i("checkedKeys")?c=(0,z.E6)(e.checkedKeys)||{}:!o&&e.defaultCheckedKeys?c=(0,z.E6)(e.defaultCheckedKeys)||{}:t&&(c=(0,z.E6)(e.checkedKeys)||{checkedKeys:n.checkedKeys,halfCheckedKeys:n.halfCheckedKeys}),c)){var v=c,g=v.checkedKeys,k=void 0===g?[]:g,m=v.halfCheckedKeys,b=void 0===m?[]:m;if(!e.checkStrictly){var K=(0,q.S)(k,!0,s);k=K.checkedKeys,b=K.halfCheckedKeys}r.checkedKeys=k,r.halfCheckedKeys=b}return i("loadedKeys")&&(r.loadedKeys=e.loadedKeys),r}}]),t}(k.Component);(0,f.Z)(G,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:b,allowDrop:function(){return!0},expandAction:!1}),(0,f.Z)(G,"TreeNode",w.Z);var U=G},10225:function(e,n,t){t.d(n,{BT:function(){return p},E6:function(){return h},L0:function(){return l},OM:function(){return f},_5:function(){return d},r7:function(){return v},wA:function(){return s},yx:function(){return c}});var o=t(74902),r=t(71002),a=t(80334),i=(t(67294),t(86128),t(35381));t(1089);function d(e,n){if(!e)return[];var t=e.slice(),o=t.indexOf(n);return o>=0&&t.splice(o,1),t}function l(e,n){var t=(e||[]).slice();return-1===t.indexOf(n)&&t.push(n),t}function c(e){return e.split("-")}function s(e,n){var t=[];return function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];n.forEach((function(n){var o=n.key,r=n.children;t.push(o),e(r)}))}((0,i.Z)(n,e).children),t}function u(e){if(e.parent){var n=c(e.pos);return Number(n[n.length-1])===e.parent.children.length-1}return!1}function f(e,n,t,o,r,a,d,l,s,f){var p,h=e.clientX,v=e.clientY,g=e.target.getBoundingClientRect(),y=g.top,k=g.height,m=(("rtl"===f?-1:1)*(((null==r?void 0:r.x)||0)-h)-12)/o,b=s.filter((function(e){var n;return null===(n=l[e])||void 0===n||null===(n=n.children)||void 0===n?void 0:n.length})),K=(0,i.Z)(l,t.eventKey);if(v<y+k/2){var N=d.findIndex((function(e){return e.key===K.key})),x=d[N<=0?0:N-1].key;K=(0,i.Z)(l,x)}var Z=K.key,E=K,C=K.key,w=0,S=0;if(!b.includes(Z))for(var P=0;P<m&&u(K);P+=1)K=K.parent,S+=1;var D,O=n.data,T=K.node,$=!0;return D=c(K.pos),0===Number(D[D.length-1])&&0===K.level&&v<y+k/2&&a({dragNode:O,dropNode:T,dropPosition:-1})&&K.key===t.eventKey?w=-1:(E.children||[]).length&&b.includes(C)?a({dragNode:O,dropNode:T,dropPosition:0})?w=0:$=!1:0===S?m>-1.5?a({dragNode:O,dropNode:T,dropPosition:1})?w=1:$=!1:a({dragNode:O,dropNode:T,dropPosition:0})?w=0:a({dragNode:O,dropNode:T,dropPosition:1})?w=1:$=!1:a({dragNode:O,dropNode:T,dropPosition:1})?w=1:$=!1,{dropPosition:w,dropLevelOffset:S,dropTargetKey:K.key,dropTargetPos:K.pos,dragOverNodeKey:C,dropContainerKey:0===w?null:(null===(p=K.parent)||void 0===p?void 0:p.key)||null,dropAllowed:$}}function p(e,n){if(e)return n.multiple?e.slice():e.length?[e[0]]:e}function h(e){if(!e)return null;var n;if(Array.isArray(e))n={checkedKeys:e,halfCheckedKeys:void 0};else{if("object"!==(0,r.Z)(e))return(0,a.ZP)(!1,"`checkedKeys` is not an array or an object"),null;n={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0}}return n}function v(e,n){var t=new Set;function r(e){if(!t.has(e)){var o=(0,i.Z)(n,e);if(o){t.add(e);var a=o.parent;o.node.disabled||a&&r(a.key)}}}return(e||[]).forEach((function(e){r(e)})),(0,o.Z)(t)}},17341:function(e,n,t){t.d(n,{S:function(){return d}});var o=t(80334),r=t(35381);function a(e,n){var t=new Set;return e.forEach((function(e){n.has(e)||t.add(e)})),t}function i(e){var n=e||{},t=n.disabled,o=n.disableCheckbox,r=n.checkable;return!(!t&&!o)||!1===r}function d(e,n,t,d){var l,c=[];l=d||i;var s,u=new Set(e.filter((function(e){var n=!!(0,r.Z)(t,e);return n||c.push(e),n}))),f=new Map,p=0;return Object.keys(t).forEach((function(e){var n=t[e],o=n.level,r=f.get(o);r||(r=new Set,f.set(o,r)),r.add(n),p=Math.max(p,o)})),(0,o.ZP)(!c.length,"Tree missing follow keys: ".concat(c.slice(0,100).map((function(e){return"'".concat(e,"'")})).join(", "))),s=!0===n?function(e,n,t,o){for(var r=new Set(e),i=new Set,d=0;d<=t;d+=1)(n.get(d)||new Set).forEach((function(e){var n=e.key,t=e.node,a=e.children,i=void 0===a?[]:a;r.has(n)&&!o(t)&&i.filter((function(e){return!o(e.node)})).forEach((function(e){r.add(e.key)}))}));for(var l=new Set,c=t;c>=0;c-=1)(n.get(c)||new Set).forEach((function(e){var n=e.parent,t=e.node;if(!o(t)&&e.parent&&!l.has(e.parent.key))if(o(e.parent.node))l.add(n.key);else{var a=!0,d=!1;(n.children||[]).filter((function(e){return!o(e.node)})).forEach((function(e){var n=e.key,t=r.has(n);a&&!t&&(a=!1),d||!t&&!i.has(n)||(d=!0)})),a&&r.add(n.key),d&&i.add(n.key),l.add(n.key)}}));return{checkedKeys:Array.from(r),halfCheckedKeys:Array.from(a(i,r))}}(u,f,p,l):function(e,n,t,o,r){for(var i=new Set(e),d=new Set(n),l=0;l<=o;l+=1)(t.get(l)||new Set).forEach((function(e){var n=e.key,t=e.node,o=e.children,a=void 0===o?[]:o;i.has(n)||d.has(n)||r(t)||a.filter((function(e){return!r(e.node)})).forEach((function(e){i.delete(e.key)}))}));d=new Set;for(var c=new Set,s=o;s>=0;s-=1)(t.get(s)||new Set).forEach((function(e){var n=e.parent,t=e.node;if(!r(t)&&e.parent&&!c.has(e.parent.key))if(r(e.parent.node))c.add(n.key);else{var o=!0,a=!1;(n.children||[]).filter((function(e){return!r(e.node)})).forEach((function(e){var n=e.key,t=i.has(n);o&&!t&&(o=!1),a||!t&&!d.has(n)||(a=!0)})),o||i.delete(n.key),a&&d.add(n.key),c.add(n.key)}}));return{checkedKeys:Array.from(i),halfCheckedKeys:Array.from(a(d,i))}}(u,n.halfCheckedKeys,f,p,l),s}},35381:function(e,n,t){function o(e,n){return e[n]}t.d(n,{Z:function(){return o}})},1089:function(e,n,t){t.d(n,{F:function(){return b},H8:function(){return m},I8:function(){return k},km:function(){return p},oH:function(){return g},w$:function(){return h},zn:function(){return v}});var o=t(71002),r=t(74902),a=t(1413),i=t(91),d=t(50344),l=t(98423),c=t(80334),s=t(35381),u=["children"];function f(e,n){return"".concat(e,"-").concat(n)}function p(e,n){return null!=e?e:n}function h(e){var n=e||{},t=n.title||"title";return{title:t,_title:n._title||[t],key:n.key||"key",children:n.children||"children"}}function v(e){return function e(n){return(0,d.Z)(n).map((function(n){if(!function(e){return e&&e.type&&e.type.isTreeNode}(n))return(0,c.ZP)(!n,"Tree/TreeNode can only accept TreeNode as children."),null;var t=n.key,o=n.props,r=o.children,d=(0,i.Z)(o,u),l=(0,a.Z)({key:t},d),s=e(r);return s.length&&(l.children=s),l})).filter((function(e){return e}))}(e)}function g(e,n,t){var o=h(t),a=o._title,i=o.key,d=o.children,c=new Set(!0===n?[]:n),s=[];return function e(t){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return t.map((function(u,h){for(var v,g=f(o?o.pos:"0",h),y=p(u[i],g),k=0;k<a.length;k+=1){var m=a[k];if(void 0!==u[m]){v=u[m];break}}var b=Object.assign((0,l.Z)(u,[].concat((0,r.Z)(a),[i,d])),{title:v,key:y,parent:o,pos:g,children:null,data:u,isStart:[].concat((0,r.Z)(o?o.isStart:[]),[0===h]),isEnd:[].concat((0,r.Z)(o?o.isEnd:[]),[h===t.length-1])});return s.push(b),!0===n||c.has(y)?b.children=e(u[d]||[],b):b.children=[],b}))}(e),s}function y(e,n,t){var a,i=("object"===(0,o.Z)(t)?t:{externalGetKey:t})||{},d=i.childrenPropName,l=i.externalGetKey,c=h(i.fieldNames),s=c.key,u=c.children,v=d||u;l?"string"==typeof l?a=function(e){return e[l]}:"function"==typeof l&&(a=function(e){return l(e)}):a=function(e,n){return p(e[s],n)},function t(o,i,d,l){var c=o?o[v]:e,s=o?f(d.pos,i):"0",u=o?[].concat((0,r.Z)(l),[o]):[];if(o){var p=a(o,s),h={node:o,index:i,pos:s,key:p,parentPos:d.node?d.pos:null,level:d.level+1,nodes:u};n(h)}c&&c.forEach((function(e,n){t(e,n,{node:o,pos:s,level:d?d.level+1:-1},u)}))}(null)}function k(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=n.initWrapper,o=n.processEntity,r=n.onProcessFinished,a=n.externalGetKey,i=n.childrenPropName,d=n.fieldNames,l=arguments.length>2?arguments[2]:void 0,c=a||l,s={},u={},f={posEntities:s,keyEntities:u};return t&&(f=t(f)||f),y(e,(function(e){var n=e.node,t=e.index,r=e.pos,a=e.key,i=e.parentPos,d=e.level,l={node:n,nodes:e.nodes,index:t,key:a,pos:r,level:d},c=p(a,r);s[r]=l,u[c]=l,l.parent=s[i],l.parent&&(l.parent.children=l.parent.children||[],l.parent.children.push(l)),o&&o(l,f)}),{externalGetKey:c,childrenPropName:i,fieldNames:d}),r&&r(f),f}function m(e,n){var t=n.expandedKeys,o=n.selectedKeys,r=n.loadedKeys,a=n.loadingKeys,i=n.checkedKeys,d=n.halfCheckedKeys,l=n.dragOverNodeKey,c=n.dropPosition,u=n.keyEntities,f=(0,s.Z)(u,e);return{eventKey:e,expanded:-1!==t.indexOf(e),selected:-1!==o.indexOf(e),loaded:-1!==r.indexOf(e),loading:-1!==a.indexOf(e),checked:-1!==i.indexOf(e),halfChecked:-1!==d.indexOf(e),pos:String(f?f.pos:""),dragOver:l===e&&0===c,dragOverGapTop:l===e&&-1===c,dragOverGapBottom:l===e&&1===c}}function b(e){var n=e.data,t=e.expanded,o=e.selected,r=e.checked,i=e.loaded,d=e.loading,l=e.halfChecked,s=e.dragOver,u=e.dragOverGapTop,f=e.dragOverGapBottom,p=e.pos,h=e.active,v=e.eventKey,g=(0,a.Z)((0,a.Z)({},n),{},{expanded:t,selected:o,checked:r,loaded:i,loading:d,halfChecked:l,dragOver:s,dragOverGapTop:u,dragOverGapBottom:f,pos:p,active:h,key:v});return"props"in g||Object.defineProperty(g,"props",{get:function(){return(0,c.ZP)(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),e}}),g}}}]);