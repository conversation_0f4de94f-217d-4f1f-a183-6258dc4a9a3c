"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5987],{43745:function(e,t,r){r.r(t),r.d(t,{default:function(){return ie}});var n=r(13769),s=r.n(n),a=r(15009),c=r.n(a),i=r(99289),o=r.n(i),u=r(5574),l=r.n(u),d=r(67294),p=r(11941),x=r(71471),f=r(55102),h=r(8232),m=r(2453),v=r(17788),k=r(66309),Z=r(42075),j=r(83062),y=r(83622),w=r(72269),b=r(86738),g=r(4393),_=r(67839),P=r(8751),T=r(18429),S=r(47389),C=r(21179),I=r(11475),F=r(82061),E=r(55287),N=r(51042),L=r(78158);function O(e){return V.apply(this,arguments)}function V(){return(V=o()(c()().mark((function e(t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,L.N)("/api/auditTask/audit-rules",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function z(e){return D.apply(this,arguments)}function D(){return(D=o()(c()().mark((function e(t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,L.N)("/api/auditTask/audit-task-types/".concat(t,"/rules"),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function q(e){return R.apply(this,arguments)}function R(){return(R=o()(c()().mark((function e(t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,L.N)("/api/auditTask/audit-rules",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function G(e,t){return H.apply(this,arguments)}function H(){return(H=o()(c()().mark((function e(t,r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,L.N)("/api/auditTask/audit-rules/".concat(t),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function K(e){return U.apply(this,arguments)}function U(){return(U=o()(c()().mark((function e(t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,L.N)("/api/auditTask/audit-rules/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function M(e,t){return A.apply(this,arguments)}function A(){return(A=o()(c()().mark((function e(t,r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,L.N)("/api/auditTask/audit-task-types/".concat(t,"/rules"),{method:"PUT",data:{rule_id:r}}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function B(e){return $.apply(this,arguments)}function $(){return($=o()(c()().mark((function e(t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,L.N)("/api/auditTask/audit-task-types",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function J(e){return Q.apply(this,arguments)}function Q(){return(Q=o()(c()().mark((function e(t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,L.N)("/api/auditTask/audit-task-types",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function W(e,t){return X.apply(this,arguments)}function X(){return(X=o()(c()().mark((function e(t,r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,L.N)("/api/auditTask/audit-task-types/".concat(t),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Y(e){return ee.apply(this,arguments)}function ee(){return(ee=o()(c()().mark((function e(t){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,L.N)("/api/auditTask/audit-task-types/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var te=r(85893),re=["task_type_id"],ne=p.Z.TabPane,se=x.Z.Text,ae=x.Z.Title,ce=f.Z.TextArea,ie=function(){var e,t=(0,d.useState)([]),r=l()(t,2),n=r[0],a=r[1],i=(0,d.useState)([]),u=l()(i,2),x=u[0],L=u[1],V=(0,d.useState)(!1),D=l()(V,2),R=D[0],H=D[1],U=(0,d.useState)(!1),A=l()(U,2),$=A[0],Q=A[1],X=(0,d.useState)(!1),ee=l()(X,2),ie=ee[0],oe=ee[1],ue=(0,d.useState)(null),le=l()(ue,2),de=le[0],pe=le[1],xe=(0,d.useState)(null),fe=l()(xe,2),he=fe[0],me=fe[1],ve=(0,d.useState)(null),ke=l()(ve,2),Ze=ke[0],je=ke[1],ye=h.Z.useForm(),we=l()(ye,1)[0],be=h.Z.useForm(),ge=l()(be,1)[0],_e=(0,d.useState)("1"),Pe=l()(_e,2),Te=Pe[0],Se=Pe[1],Ce=function(){var e=o()(c()().mark((function e(){var t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return H(!0),e.prev=1,e.next=4,B();case 4:(t=e.sent).success?a(t.data||[]):m.ZP.error("获取任务类型列表失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("获取任务类型失败：",e.t0),m.ZP.error("获取任务类型失败，请检查网络连接");case 12:return e.prev=12,H(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[1,8,12,15]])})));return function(){return e.apply(this,arguments)}}(),Ie=function(){var e=o()(c()().mark((function e(t){var r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(H(!0),e.prev=1,!t){e.next=8;break}return e.next=5,z(t);case 5:r=e.sent,e.next=11;break;case 8:return e.next=10,O();case 10:r=e.sent;case 11:r.success?L(r.data||[]):m.ZP.error("获取规则列表失败"),e.next=18;break;case 14:e.prev=14,e.t0=e.catch(1),console.error("获取规则失败：",e.t0),m.ZP.error("获取规则失败，请检查网络连接");case 18:return e.prev=18,H(!1),e.finish(18);case 21:case"end":return e.stop()}}),e,null,[[1,14,18,21]])})));return function(t){return e.apply(this,arguments)}}();(0,d.useEffect)((function(){Ce()}),[]),(0,d.useEffect)((function(){Ze?Ie(Ze):"2"===Te&&Ie()}),[Ze,Te]);var Fe=function(){var e=o()(c()().mark((function e(t){var r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,J(t);case 3:(r=e.sent).success?(m.ZP.success("任务类型创建成功"),Ce(),Q(!1),we.resetFields()):m.ZP.error(r.message||"创建失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("创建任务类型失败：",e.t0),m.ZP.error("创建任务类型失败，请检查网络连接");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}(),Ee=function(){var e=o()(c()().mark((function e(t,r){var n;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,W(t,r);case 3:(n=e.sent).success?(m.ZP.success("任务类型更新成功"),Ce(),Q(!1),we.resetFields()):m.ZP.error(n.message||"更新失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("更新任务类型失败：",e.t0),m.ZP.error("更新任务类型失败，请检查网络连接");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t,r){return e.apply(this,arguments)}}(),Ne=function(){var e=o()(c()().mark((function e(t){var r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Y(t);case 3:(r=e.sent).success?(m.ZP.success("任务类型删除成功"),Ce(),Ze===t&&je(null)):m.ZP.error(r.message||"删除失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("删除任务类型失败：",e.t0),m.ZP.error("删除任务类型失败，请检查网络连接");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}(),Le=function(){var e=o()(c()().mark((function e(t,r){var n;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,M(r,t);case 3:(n=e.sent).success?(m.ZP.success("规则已添加到任务类型"),Ce()):m.ZP.error(n.message||"操作失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("添加规则到任务类型失败：",e.t0),m.ZP.error("操作失败，请检查网络连接");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t,r){return e.apply(this,arguments)}}(),Oe=function(){var e=o()(c()().mark((function e(t){var r,n,a;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,r=t.task_type_id,n=s()(t,re),e.next=4,q(n);case 4:if(!(a=e.sent).success){e.next=15;break}if(m.ZP.success("规则创建成功"),!r||""===r){e.next=10;break}return e.next=10,Le(a.data.id,r);case 10:Ie(Ze||void 0),oe(!1),ge.resetFields(),e.next=16;break;case 15:m.ZP.error(a.message||"创建失败");case 16:e.next=22;break;case 18:e.prev=18,e.t0=e.catch(0),console.error("创建规则失败：",e.t0),m.ZP.error("创建规则失败，请检查网络连接");case 22:case"end":return e.stop()}}),e,null,[[0,18]])})));return function(t){return e.apply(this,arguments)}}(),Ve=function(){var e=o()(c()().mark((function e(t,r){var n;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,G(t,r);case 3:(n=e.sent).success?(m.ZP.success("规则更新成功"),Ie(Ze||void 0),oe(!1),ge.resetFields()):m.ZP.error(n.message||"更新失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("更新规则失败：",e.t0),m.ZP.error("更新规则失败，请检查网络连接");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t,r){return e.apply(this,arguments)}}(),ze=function(){var e=o()(c()().mark((function e(t){var r;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,K(t);case 3:(r=e.sent).success?(m.ZP.success("规则删除成功"),Ie(Ze||void 0)):m.ZP.error(r.message||"删除失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("删除规则失败：",e.t0),m.ZP.error("删除规则失败，请检查网络连接");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}(),De=function(){var e=o()(c()().mark((function e(t,r){var n;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,W(t,{is_active:r});case 3:(n=e.sent).success?(m.ZP.success("任务类型已".concat(r?"启用":"停用")),Ce()):m.ZP.error(n.message||"操作失败"),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("切换状态失败：",e.t0),m.ZP.error("操作失败，请检查网络连接");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t,r){return e.apply(this,arguments)}}(),qe=function(e){pe(e||null),e?we.setFieldsValue({name:e.name,code:e.code,description:e.description,is_active:e.is_active}):(we.resetFields(),we.setFieldsValue({is_active:!0})),Q(!0)},Re=function(e){me(e||null),e?ge.setFieldsValue({name:e.name,content:e.content,prompt:e.prompt,few_shot:e.few_shot?e.few_shot.join("\n"):"",task_type_id:e.task_type_id,is_active:e.is_active}):(ge.resetFields(),Ze?ge.setFieldsValue({task_type_id:Ze,is_active:!0}):ge.setFieldsValue({is_active:!0})),oe(!0)},Ge=[{title:"名称",dataIndex:"name",key:"name"},{title:"代码",dataIndex:"code",key:"code",width:60},{title:"规则数量",dataIndex:"rules_count",key:"rules_count"},{title:"状态",dataIndex:"is_active",key:"is_active",width:80,render:function(e){return e?(0,te.jsx)(k.Z,{color:"green",icon:(0,te.jsx)(P.Z,{}),children:"启用"}):(0,te.jsx)(k.Z,{color:"red",icon:(0,te.jsx)(T.Z,{}),children:"停用"})}},{title:"创建时间",dataIndex:"created_at",key:"created_at",render:function(e){return new Date(e).toLocaleString()}},{title:"操作",key:"action",render:function(e,t){return(0,te.jsxs)(Z.Z,{size:"middle",children:[(0,te.jsx)(j.Z,{title:"编辑",children:(0,te.jsx)(y.ZP,{type:"text",icon:(0,te.jsx)(S.Z,{}),onClick:function(){return qe(t)}})}),(0,te.jsx)(j.Z,{title:"查看规则",children:(0,te.jsx)(y.ZP,{type:"text",icon:(0,te.jsx)(C.Z,{}),onClick:function(){je(t.id),Se("2")}})}),(0,te.jsx)(j.Z,{title:t.is_active?"停用":"启用",children:(0,te.jsx)(w.Z,{checked:t.is_active,onChange:function(e){return De(t.id,e)}})}),(0,te.jsx)(b.Z,{title:"确定要删除该任务类型吗？",description:"删除后无法恢复，且相关规则将失去关联。",onConfirm:function(){return Ne(t.id)},okText:"确定",cancelText:"取消",icon:(0,te.jsx)(I.Z,{style:{color:"red"}}),children:(0,te.jsx)(y.ZP,{type:"text",danger:!0,icon:(0,te.jsx)(F.Z,{})})})]})}}],He=[{title:"名称",dataIndex:"name",key:"name"},{title:"内容",dataIndex:"content",key:"content",ellipsis:!0,width:"30%"},{title:"Prompt",dataIndex:"prompt",key:"prompt",ellipsis:!0,width:"20%"},{title:"创建时间",dataIndex:"created_at",key:"created_at",render:function(e){return new Date(e).toLocaleString()}},{title:"操作",key:"action",render:function(e,t){return(0,te.jsxs)(Z.Z,{size:"middle",children:[(0,te.jsx)(j.Z,{title:"查看详情",children:(0,te.jsx)(y.ZP,{type:"text",icon:(0,te.jsx)(E.Z,{}),onClick:function(){return e=t,void v.Z.info({title:"规则详情: ".concat(e.name),width:700,content:(0,te.jsxs)("div",{style:{marginTop:16},children:[(0,te.jsxs)("p",{children:[(0,te.jsx)("strong",{children:"名称:"})," ",e.name]}),e.content&&(0,te.jsxs)("div",{children:[(0,te.jsx)("p",{children:(0,te.jsx)("strong",{children:"规则内容:"})}),(0,te.jsx)("pre",{style:{backgroundColor:"#f5f5f5",padding:12,borderRadius:4,maxHeight:200,overflow:"auto"},children:e.content})]}),e.prompt&&(0,te.jsxs)("div",{children:[(0,te.jsx)("p",{children:(0,te.jsx)("strong",{children:"Prompt:"})}),(0,te.jsx)("pre",{style:{backgroundColor:"#f5f5f5",padding:12,borderRadius:4,maxHeight:200,overflow:"auto"},children:e.prompt})]}),e.few_shot&&e.few_shot.length>0&&(0,te.jsxs)("div",{children:[(0,te.jsx)("p",{children:(0,te.jsx)("strong",{children:"Few Shot 示例:"})}),(0,te.jsx)("ul",{style:{backgroundColor:"#f5f5f5",padding:12,borderRadius:4,maxHeight:200,overflow:"auto"},children:e.few_shot.map((function(e,t){return(0,te.jsx)("li",{children:e},t)}))})]}),(0,te.jsxs)("p",{children:[(0,te.jsx)("strong",{children:"创建时间:"})," ",new Date(e.created_at).toLocaleString()]})]}),okText:"关闭"});var e}})}),(0,te.jsx)(y.ZP,{type:"text",icon:(0,te.jsx)(S.Z,{}),onClick:function(){return Re(t)}}),(0,te.jsx)(j.Z,{title:"添加到任务类型",children:(0,te.jsx)(y.ZP,{type:"text",icon:(0,te.jsx)(N.Z,{}),onClick:function(){return e=t,void v.Z.confirm({title:"添加规则到任务类型",content:(0,te.jsxs)("div",{children:[(0,te.jsx)("p",{children:"选择要添加此规则的任务类型:"}),(0,te.jsx)("select",{id:"taskTypeSelector",style:{width:"100%",padding:"8px",border:"1px solid #d9d9d9",borderRadius:"2px",marginTop:"10px"},children:n.map((function(e){return(0,te.jsx)("option",{value:e.id,children:e.name},e.id)}))})]}),onOk:function(){var t=document.getElementById("taskTypeSelector");t&&t.value&&Le(e.id,t.value)},okText:"添加",cancelText:"取消"});var e}})}),(0,te.jsx)(b.Z,{title:"确定要删除该规则吗？",onConfirm:function(){return ze(t.id)},okText:"确定",cancelText:"取消",icon:(0,te.jsx)(I.Z,{style:{color:"red"}}),children:(0,te.jsx)(y.ZP,{type:"text",danger:!0,icon:(0,te.jsx)(F.Z,{})})})]})}}];return(0,te.jsxs)("div",{style:{padding:"20px"},children:[(0,te.jsx)(ae,{level:4,children:"审核任务类型与规则管理"}),(0,te.jsx)(se,{type:"secondary",children:"管理离线文件审核任务的类型与规则。任务类型决定了适用的审核规则和内容抽取方式。"}),(0,te.jsxs)(p.Z,{activeKey:Te,onChange:Se,style:{marginTop:16},children:[(0,te.jsx)(ne,{tab:"任务类型管理",children:(0,te.jsx)(g.Z,{title:"任务类型列表",extra:(0,te.jsx)(y.ZP,{type:"primary",icon:(0,te.jsx)(N.Z,{}),onClick:function(){return qe()},children:"新建任务类型"}),children:(0,te.jsx)(_.Z,{columns:Ge,dataSource:n,rowKey:"id",loading:R,pagination:{pageSize:10}})})},"1"),(0,te.jsx)(ne,{tab:"规则管理",children:(0,te.jsx)(g.Z,{title:(0,te.jsxs)(Z.Z,{children:[(0,te.jsx)("span",{children:"规则列表"}),Ze&&(0,te.jsxs)(k.Z,{color:"blue",children:[(null===(e=n.find((function(e){return e.id===Ze})))||void 0===e?void 0:e.name)||"选中类型",(0,te.jsx)(y.ZP,{type:"text",size:"small",onClick:function(){return je(null)},style:{marginLeft:4,color:"rgba(0, 0, 0, 0.45)"},children:"清除筛选"})]})]}),extra:(0,te.jsx)(y.ZP,{type:"primary",icon:(0,te.jsx)(N.Z,{}),onClick:function(){return Re()},children:"新建规则"}),children:(0,te.jsx)(_.Z,{columns:He,dataSource:x,rowKey:"id",loading:R,pagination:{pageSize:10}})})},"2")]}),(0,te.jsx)(v.Z,{title:de?"编辑任务类型":"新建任务类型",visible:$,onOk:function(){we.validateFields().then((function(e){de?Ee(de.id,e):Fe(e)}))},onCancel:function(){return Q(!1)},okText:de?"更新":"创建",cancelText:"取消",destroyOnClose:!0,children:(0,te.jsxs)(h.Z,{form:we,layout:"vertical",initialValues:{is_active:!0},children:[(0,te.jsx)(h.Z.Item,{name:"name",label:"类型名称",rules:[{required:!0,message:"请输入类型名称"}],children:(0,te.jsx)(f.Z,{placeholder:"例如：合同关键信息抽取"})}),(0,te.jsx)(h.Z.Item,{name:"code",label:"类型代码",rules:[{required:!0,message:"请输入类型代码"},{pattern:/^[a-z_]+$/,message:"代码应为小写字母和下划线"}],tooltip:{title:"用于系统识别的代码，如 contract_extraction"},children:(0,te.jsx)(f.Z,{placeholder:"例如：contract_extraction",disabled:!!de})}),(0,te.jsx)(h.Z.Item,{name:"description",label:"类型描述",children:(0,te.jsx)(ce,{rows:4,placeholder:"请描述该类型的用途和处理逻辑"})}),(0,te.jsx)(h.Z.Item,{name:"is_active",label:"启用状态",valuePropName:"checked",children:(0,te.jsx)(w.Z,{})})]})}),(0,te.jsx)(v.Z,{title:he?"编辑规则":"新建规则",visible:ie,onOk:function(){ge.validateFields().then((function(e){if(e.few_shot){var t=e.few_shot.split("\n").filter((function(e){return""!==e.trim()}));e.few_shot=t}he?Ve(he.id,e):Oe(e)}))},onCancel:function(){return oe(!1)},okText:he?"更新":"创建",cancelText:"取消",destroyOnClose:!0,width:700,children:(0,te.jsxs)(h.Z,{form:ge,layout:"vertical",initialValues:{is_active:!0},children:[(0,te.jsx)(h.Z.Item,{name:"name",label:"规则名称",rules:[{required:!0,message:"请输入规则名称"}],children:(0,te.jsx)(f.Z,{placeholder:"例如：识别合同甲方"})}),(0,te.jsx)(h.Z.Item,{name:"content",label:"规则内容",rules:[{required:!0,message:"请输入规则内容"}],children:(0,te.jsx)(ce,{rows:4,placeholder:"请输入规则的具体内容，可以是正则表达式、关键词或其他表达方式"})}),(0,te.jsx)(h.Z.Item,{name:"prompt",label:"Prompt提示词",tooltip:{title:"用于指导LLM执行规则的提示词"},children:(0,te.jsx)(ce,{rows:4,placeholder:"请输入提示词，用于指导大模型如何执行该规则"})}),(0,te.jsx)(h.Z.Item,{name:"few_shot",label:"Few Shot示例",tooltip:{title:"用于提供给LLM的少样本示例，每行一个"},children:(0,te.jsx)(ce,{rows:4,placeholder:"请输入少样本示例，每行一个"})}),(0,te.jsx)(h.Z.Item,{name:"is_active",label:"启用状态",valuePropName:"checked",children:(0,te.jsx)(w.Z,{})})]})})]})}}}]);