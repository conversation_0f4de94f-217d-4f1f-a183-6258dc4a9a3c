from fastapi import APIRouter, HTTPException, Depends, Query, File, UploadFile, Form, Body, Path as PathParam
from typing import List, Optional, Dict, Any
from ..models.consumerProtectionFile import (ConsumerProtectionFileResponse, ConsumerProtectionFileUploadRequest)
from ..models.consumerProtectionRule import (ConsumerProtectionRule, ConsumerProtectionRuleCreate, 
                                         ConsumerProtectionRuleUpdate, ConsumerProtectionRuleResponse)
from ..models.consumerProtection import (ConsumerProtection, ConsumerProtectionCreate, ConsumerProtectionUpdate, 
                                      ConsumerProtectionResponse, ChunkContent, Issue, QuoteInfo, RuleEvaluationResult)
from ..models.source_files import SourceFileBase
from ..db.mongodb import db
from ..utils.auth import verify_token
from bson import ObjectId
from datetime import datetime
from ..models.system_app_setting import SystemAppSettingModel
import os
from werkzeug.utils import secure_filename as _secure_filename
from pathlib import Path
from pydantic import BaseModel
from ..models.llm import LLMModel
from ..engines.agent import Intelligent_review
import json

from ..models.source_files import SourceFileResponse
from app.utils.logging_config import setup_logging, get_logger
from ..db.miniIO import minio
import uuid
from ..utils.config import settings
import traceback # 异常打印
from urllib.parse import quote
from app.utils.enums import FileStorageType
from app.engines.embedding.embedding_utils import get_embedding
from app.engines.retrieval.base_retriever import es_data_ingester, update_by_query
from difflib import SequenceMatcher
from ..models.consumerProtectionFile import ConsumerProtectionFileResponse
from app.engines.indexing.content_parser import parse_file_content,parse_file_content_by_image
import time

setup_logging()
logger = get_logger(__name__)
router = APIRouter(
    prefix="/api/consumer_protection",
    tags=["consumer-protection-rules"]
)




FILE_UPLOAD_PATH = os.getenv("FILE_UPLOAD_PATH")

def get_file_format(filename: str) -> str:
    """根据文件后缀返回文件格式"""
    extension = os.path.splitext(filename)[1].lower()
    format_map = {
        '.csv': 'CSV',
        '.pdf': 'PDF',
        '.docx': 'DOCX',
        '.doc': 'DOC',
        '.xls': 'Excel',
        '.xlsx': 'Excel',
        '.json': 'JSON',
        '.jsonl': 'JSONL',
    }
    return format_map.get(extension, 'Unknown')



@router.post("/upload-consumerProtection-file", response_model=List[ConsumerProtectionFileResponse])
async def upload_files(
    files: List[UploadFile] = File(...),
    current_user: dict = Depends(verify_token)
):
    uploaded_files = []
    try:            
        for file in files:                    
            content = await file.read()
            file_size = len(content)
            
            # 生成唯一的文件名，避免文件名冲突
            file_ext = os.path.splitext(file.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_ext}"

            if settings.MINIO_ENABLE:
                # 上传到MinIO
                file_url = minio.upload_bytes(
                    data=content,
                    object_name=unique_filename,
                    content_type=file.content_type
                )
            else:
                logger.info(f"MinIO 未启用，将文件上传到本地: {settings.FILE_UPLOAD_PATH}/{unique_filename}")
                file_url = f"{settings.FILE_UPLOAD_PATH}/{unique_filename}"
                with open(file_url, "wb") as f:
                    f.write(content)
            
            file_format = get_file_format(file.filename)
            
            new_file = {
                "name": file.filename,
                
                "storage_path": file_url,  # 只存储文件名
                "storage_type": FileStorageType.MINIO if settings.MINIO_ENABLE else FileStorageType.LOCAL,
                "data_type": file_format,
                "size": file_size,
                "created_at": datetime.now(),
                "user_id": current_user["id"],
                "user_name": current_user["name"],
                "file_name": file.filename,
                "created_at": datetime.now(),
            }
            logger.info(f"new_file: {new_file}")
            
            result = await db["consumer_protection_files"].insert_one(new_file)
            uploaded_files.append(ConsumerProtectionFileResponse(
                id=str(result.inserted_id),
                name=new_file["name"],
                url=file_url,
            ))

    except Exception as e:
        traceback.print_exc()
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

    return uploaded_files


# 定义解析文件请求的模型
class ParseFileRequest(BaseModel):
    file_id: str
    parser_type: Optional[str] = None


@router.post("/parse-consumerProtection-file", response_model=Dict[str, Any])
async def parse_file(
        file_id: str = Body(..., embed=True),
        parser_type: Optional[str] = Body(None, embed=True),
        current_user: dict = Depends(verify_token)
    ):
    logger.info(f"开始解析文件: {file_id}")
    logger.info(f"开始解析类型: {parser_type}")
        
    try:
        # 从数据库中查找文件
        file_record = await db["consumer_protection_files"].find_one({"_id": ObjectId(file_id)})
        if not file_record:
            raise HTTPException(status_code=404, detail="文件未找到")

        # 获取文件信息
        storage_path = file_record.get("storage_path")
        storage_type = file_record.get("storage_type", FileStorageType.LOCAL)
        file_type = file_record.get("data_type", "").lower()
        
        if not storage_path:
            raise HTTPException(status_code=400, detail="文件路径不存在")
        
        # 调用文件解析方法
        chunks = parse_file_content(
            storage_path=storage_path,
            file_type=file_type,
            parser_type=parser_type,
            storage_type=storage_type
        )
        logger.info(chunks)

        
        if not chunks:
            return {
                "success": False,
                "message": "文件解析完成，但未提取到内容",
                "chunks": []
            }
        
        # 记录chunks数量到日志
        logger.info(f"成功从文件 {file_record.get('name')} 中解析出 {len(chunks)} 个内容块")
        
        return {
            "success": True,
            "message": f"文件解析成功，共提取 {len(chunks)} 个内容块",
            "chunks": chunks
        }
    except Exception as e:
        
        logger.error(f"文件解析失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"文件解析失败: {str(e)}")


@router.post("/parse-consumerProtection-file-by-end-to-end", response_model=Dict[str, Any])
async def parse_file(
        file_id: str = Body(..., embed=True),
        parser_type: Optional[str] = Body(None, embed=True),
        current_user: dict = Depends(verify_token)
    ):
    logger.info(f"开始解析文件: {file_id}")
    logger.info(f"开始解析类型: {parser_type}")
        
    try:
        # 从数据库中查找文件
        file_record = await db["consumer_protection_files"].find_one({"_id": ObjectId(file_id)})
        if not file_record:
            raise HTTPException(status_code=404, detail="文件未找到")

        # 获取文件信息
        storage_path = file_record.get("storage_path")
        storage_type = file_record.get("storage_type", FileStorageType.LOCAL)
        file_type = file_record.get("data_type", "").lower()
        app_info = "ConsumerProtection/IntelligentReview"

        app_info_obj = await db["system_app_settings"].find_one({"app_info": app_info})
        if not app_info_obj:
            raise HTTPException(status_code=404, detail="应用信息不存在")
        app_params = app_info_obj.get('params',None)
        if not app_params:  
            raise HTTPException(status_code=404, detail="应用参数不存在")

        logger.info(f"app_params: {app_params}")
        end_to_end_url=app_params.get("END_TO_END_API")
        if not end_to_end_url:
            raise HTTPException(status_code=404, detail="端到端API地址不存在")

        if not storage_path:
            raise HTTPException(status_code=400, detail="文件路径不存在")
        
        # 调用文件解析方法
        result = parse_file_content_by_image(
            storage_path=storage_path,
            file_type=file_type,
            parser_type=parser_type,
            storage_type=storage_type,
            end_to_end_url=end_to_end_url
        )
        logger.info(result)

        
        if not result:
            return {
                "success": False,
                "message": "文件解析完成，但未提取到内容",
                "chunks": []
            }
        
        # 记录chunks数量到日志
        logger.info(f"成功从文件 {file_record.get('name')} 中解析出 {len(result.get('chunks',[]))} 个内容块")
        
        return {
            "success": True,
            "message": f"文件解析成功，共提取 {len(result.get('chunks',[]))} 个内容块",
            "chunks": result.get('chunks',[])
        }
    except Exception as e:
        
        logger.error(f"文件解析失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"文件解析失败: {str(e)}")


class OcrRequest(BaseModel):
    base64Image: str
    fileName: Optional[str] = "未命名图片"
    
# @router.post("/parse-consumerProtection-image")
# async def end_to_end_recognition(request: OcrRequest):
#     """AI端到端识别接口"""
#     try:
#         app_info = "endToEndRecognition"
#         app_info_obj = await db["system_app_settings"].find_one({"app_info": app_info})
#         if not app_info_obj:
#             raise HTTPException(status_code=404, detail="应用信息不存在")
#         app_params = app_info_obj.get('params',None)
#         if not app_params:  
#             raise HTTPException(status_code=404, detail="应用参数不存在")

#         logger.info(f"app_params: {app_params}")



#         # 获取图片的base64编码
#         image_base64 = request.base64Image
#         if not image_base64:
#             raise HTTPException(status_code=400, detail="未提供图片数据")
        
#         logger.info(f"接收到图片数据，准备进行文档解析")
        
        
#         # 解码图片
#         temp_img_path = base64_to_image(image_base64)
#         # 定义接口的URL，指定IP和端口
#         # end_to_end_url = 'http://************:8006/api/v1/file/parser'
#         # end_to_end_url = os.getenv("END_TO_END_API")
#         end_to_end_url = app_params.get("END_TO_END_API")
#         if not end_to_end_url:
#             raise HTTPException(status_code=404, detail="端到端API地址不存在")
#         with open(temp_img_path, 'rb') as file:
#             response = requests.post(end_to_end_url, files={'file': file})
            
#             # 检查响应状态码并打印结果
#             if response.status_code == 200:
#                 # 构建完整结果
#                 # 下载并解析结果文件
#                 parsed_content = download_and_extract_files(response.json()['result'])
                
#                 # 合并解析结果到返回数据
#                 result = {
#                     "result_download_url": response.json()['result'],
#                     "file_content": parsed_content["file_content"],
#                     "markdown_content": parsed_content["markdown_content"]
#                 }
#                 logger.info(f"文档解析结果: {result}")
#                 logger.info(f"文档解析结果: {parsed_content}")
#                 return OcrRecognitionResponse(
#                     success=True,
#                     message="文档解析完成",
#                     data=result
#                 )
    
#             else:
#                 logger.error("调接口报错")
#                 return OcrRecognitionResponse(
#                     success=False,
#                     message=f"文档解析报错: {str(e)}",
#                     data=None
#                 )
         
#     except Exception as e:
#         logger.error(f"文档解析报错: {str(e)}")
#         return OcrRecognitionResponse(
#             success=False,
#             message=f"文档解析报错: {str(e)}",
#             data=None
#         )




# 评估请求模型
class EvaluateRequest(BaseModel):
    file_id: str
    evaluation_type: str
    chunk_content: str
    app_info: str

# 评估响应模型
class EvaluateResponse(BaseModel):
    success: bool
    message: str
    data: Dict[str, Any] = {}

@router.post("/evaluate-chunk", response_model=EvaluateResponse)
async def evaluate_chunk(
    request: EvaluateRequest,
    current_user: dict = Depends(verify_token)
):
    """
    评估文档块内容
    
    根据指定的评估类型对文档块内容进行评估
    """
    try:
        logger.info(f"开始评估文档块: {request.file_id}")
        logger.info(f"评估应用信息: {request.app_info}")
        
        # 构建评估提示词
        prompt = f"""
        请对以下文档内容进行{request.evaluation_type}评估：
        
        文档内容：
        {request.chunk_content}
        
        请提供以下方面的评估结果：
        1. 评估标签（assessmentTag）
        2. 分析结果（analysisResult）
        3. 思考过程（thoughtProcess）
        4. 修改建议（suggestion）
        5. 引用规则列表（quoteList）
        """

        system_app_info: SystemAppSettingModel = await db["system_app_settings"].find_one({"app_info": request.app_info})
        system_app_params = system_app_info.get('params', {})
        llm_id = system_app_info.get('MODEL_ID', None)
        if not llm_id:
            raise ValueError("LLM model not found")
        else:
            llm_id = int(llm_id)

        llm: LLMModel = await db["llms"].find_one({"id": llm_id})
        if not llm:
            raise ValueError("LLM model not found")
            
        # 根据评估类型选择处理方式
        if request.evaluation_type == "合同审核":
            # 使用智能合同审核
            from app.engines.agent.Intelligent_review_contract import ContractIntelligentReview
            
            # 获取知识库ID列表
            kb_ids = system_app_params.get('knowledge_base_ids', [])
            
            # 创建合同审核代理
            contract_review = ContractIntelligentReview(
                config=system_app_params, 
                db=db,
                content=[request.chunk_content],
                llm=llm,
                knowledge_base_ids=kb_ids,
                app_info=request.app_info
            )
            
            # 执行合同审核
            review_result = await contract_review.run()
            
            if review_result.get("status") == "success":
                result_data = review_result.get("data", {})
                analysis = result_data.get("analysis", {})
                key_points = result_data.get("key_points", [])
                recommendations = result_data.get("recommendations", [])
                
                # 构建响应数据
                response_data = {
                    "assessmentTag": "合同审核结果",
                    "analysisResult": "详细分析请见思考过程",
                    "thoughtProcess": json.dumps(analysis, ensure_ascii=False),
                    "suggestion": json.dumps(recommendations, ensure_ascii=False),
                    "quoteList": []
                }
                
                # 添加引用信息
                for citation in analysis.get("citations", []):
                    quote_item = {
                        "regulationName": citation.get("context", "合同条款"),
                        "provision": citation.get("citation", ""),
                        "requirement": citation.get("finding_type", ""),
                        "similarityScores": [
                            {
                                "scoreType": "text-embedding",
                                "value": 0.95,
                                "confidence": 0.9
                            }
                        ]
                    }
                    response_data["quoteList"].append(quote_item)
                
                return {
                    "success": True,
                    "message": "评估成功",
                    "data": response_data
                }
            else:
                return {
                    "success": False,
                    "message": review_result.get("message", "评估失败"),
                    "data": None
                }
        else:
            # 其他评估类型的处理
            evaluations = await db['consumer_protection_rules'].find({"ruleType": request.evaluation_type}).to_list()
            if evaluations and len(evaluations) > 0:
                from app.engines.agent.Intelligent_review import IntelligentReview
                review = IntelligentReview(system_app_params, db, request.evaluation_type, llm)
                # 原有处理逻辑...
        
        # 调用LLM进行评估
        evaluation_result = await call_llm_api(prompt)
        
        # 解析评估结果
        parsed_result = _parse_evaluation_result(evaluation_result)
        
        return {
            "success": True,
            "message": "评估成功",
            "data": parsed_result
        }
    except Exception as e:
        traceback.print_exc()
        logger.error(f"评估失败: {str(e)}")
        return {
            "success": False,
            "message": f"评估失败: {str(e)}",
            "data": None
        }

async def call_llm_api(prompt: str) -> str:
    # 模拟LLM API调用
    # 在真实环境中，这里会调用实际的LLM API
    time.sleep(1)  # 模拟API响应时间
    return """
    {
        "assessmentTag": "不合规",
        "analysisResult": "文档中缺少必要的信息披露内容",
        "thoughtProcess": "根据规定，信息披露申请书应包含企业基本信息、财务状况等内容，但当前文档缺少这些关键信息。",
        "suggestion": "建议补充企业基本信息、完善财务状况披露、添加风险提示等内容。",
        "quoteList": [
            {
                "regulationName": "信息披露管理办法",
                "provision": "第十五条",
                "requirement": "申请书应包含完整的企业基本信息",
                "similarityScores": [
                    {
                        "scoreType": "text-embedding",
                        "value": 0.85,
                        "confidence": 0.9
                    }
                ]
            }
        ]
    }
    """

def _parse_evaluation_result(evaluation_result: str) -> Dict[str, Any]:
    """解析LLM返回的评估结果，将其转换为标准格式"""
    try:
        # 尝试解析JSON
        cleaned_text = evaluation_result.strip()
        result_data = json.loads(cleaned_text)
        
        # 确保标准字段存在
        standard_result = {
            "assessmentTag": result_data.get("assessmentTag", "待评估"),
            "analysisResult": result_data.get("analysisResult", ""),
            "thoughtProcess": result_data.get("thoughtProcess", ""),
            "suggestion": result_data.get("suggestion", ""),
            "quoteList": result_data.get("quoteList", [])
        }
        
        # 确保quoteList中的每个项目都有正确的结构
        for i, quote in enumerate(standard_result["quoteList"]):
            if "similarityScores" not in quote or not quote["similarityScores"]:
                standard_result["quoteList"][i]["similarityScores"] = [
                    {
                        "scoreType": "text-embedding",
                        "value": 0.8,
                        "confidence": 0.8
                    }
                ]
                
        return standard_result
    except json.JSONDecodeError:
        # 如果解析失败，返回默认结构
        logger.warning("评估结果解析失败，使用默认格式")
        return {
            "assessmentTag": "待评估",
            "analysisResult": evaluation_result[:200] + "...",  # 截取部分内容
            "thoughtProcess": "LLM返回了非JSON格式的结果，需要人工复核",
            "suggestion": "请人工复核评估结果",
            "quoteList": []
        }
    except Exception as e:
        logger.error(f"解析评估结果时出错: {str(e)}")
        return {
            "assessmentTag": "解析错误",
            "analysisResult": "评估结果解析失败",
            "thoughtProcess": f"错误: {str(e)}",
            "suggestion": "请重新提交评估请求",
            "quoteList": []
        }






@router.get("/get_rules")
async def get_protection_rules(
        type_id: str = "",
        current_user: dict = Depends(verify_token)
    ):
        """
        获取消费者权益保护规则接口
        参数:
        - evaluation_type: 评估类型
        - app_info: 应用信息（用于关联规则）
        - type_id: 消保类型ID，如果提供则从该类型中获取规则
        """
        try:
            # 如果提供了type_id，从消保类型中获取规则
            if type_id:
                # 获取消保类型
                protection_type = await db["consumer_protection_types"].find_one({"_id": ObjectId(type_id), "is_deleted": {"$ne": True}, "user_id": current_user.get("id")})
                if not protection_type:
                    return {
                        "success": False,
                        "message": "消保类型不存在",
                        "data": []
                    }
                
                # 获取类型关联的规则IDs
                rule_ids = protection_type.get("rules", [])
                
                # 如果有规则ID，查询这些规则
                if rule_ids:
                    # 将字符串ID转换为ObjectId
                    object_rule_ids = [ObjectId(rule_id) for rule_id in rule_ids]
                    query = {
                        "_id": {"$in": object_rule_ids}, 
                        "is_deleted": {"$ne": True}
                    }
                    
                        
                    rules = await db["consumer_protection_rules"].find(query).to_list(length=1000)
                else:
                    # 没有规则ID，返回空列表
                    return {
                        "success": True,
                        "message": "该类型下没有规则",
                        "data": []
                    }
            else:
                # 原有逻辑：根据评估类型查询
                query = {}
                # 执行查询并返回结果
                rules = await db['consumer_protection_rules'].find(query).to_list(length=1000)
            
            # 转换ObjectId为字符串
            for rule in rules:
                rule = fix_objectid(rule)
                rule["id"] = str(rule["id"]) if "id" in rule else str(rule["_id"])
                if "_id" in rule:
                    del rule["_id"]
                
            return {
                "success": True,
                "message": "规则获取成功",
                "data": rules
            }
            
        except Exception as e:
            logger.error(f"获取规则失败: {str(e)}")
            return {
                "success": False,
                "message": f"规则获取失败: {str(e)}",
                "data": []
            }
           



@router.post("/evaluate_by_rule")
async def evaluate_by_rule(
    request_data: Dict[str, Any] = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    基于特定规则评估文档内容
    参数:
    - evaluation_type: 评估类型
    - rule_id: 规则ID
    - chunk_contents: 文档内容块
    - app_info: 应用信息
    """
    try:
        evaluation_type = request_data.get("evaluation_type", "")
        rule_id = request_data.get("rule_id", "")
        app_info = request_data.get("app_info", "")
        chunk_contents = request_data.get("chunk_contents", [])
        logger.info(f"evaluation_type: {evaluation_type}")
        logger.info(f"rule_id: {rule_id}")
        logger.info(f"app_info: {app_info}")
        logger.info(f"chunk_contents: {chunk_contents}")
        
        logger.info(f"开始基于规则评估: 规则ID={rule_id}, 评估类型={evaluation_type}")
        
        if not rule_id or not chunk_contents:
            return {
                "success": False,
                "message": "缺少必要参数：规则ID或文档内容",
                "data": {"result": "", "status": "failed"}
            }
            
        # 获取系统应用设置
        logger.info(f"app_info===========>: {app_info}")
        system_app_info = await db["system_app_settings"].find_one({"app_info": app_info})
        if not system_app_info:
            return {
                "success": False,
                "message": "未找到应用配置信息",
                "data": {"result": "", "status": "failed"}
            }
        logger.info(f"system_app_info===========>: {system_app_info}")
        system_app_params = system_app_info.get('params', {})
        llm_id = system_app_params.get('MODEL_ID', None)
        if not llm_id:
            raise ValueError("LLM model not found")
        else:
            llm_id = int(llm_id)
        logger.info(f"llm_id===========>: {llm_id}")
        
        # 获取LLM配置
        llm: LLMModel = await db["llms"].find_one({"id": llm_id})
        if not llm:
            return {
                "success": False,
                "message": "未找到LLM配置",
                "data": {"result": "", "status": "failed"}
            }
        
        logger.info(f"llm===========>: {llm}")
        

        
        # 提取所有文档内容
        contents = [chunk.get("content", "") for chunk in chunk_contents]
        
        # 使用智能评估引擎
        from app.engines.agent.Intelligent_review_by_rule import IntelligentReviewByRule
        
        review_agent = IntelligentReviewByRule(
            config=system_app_params,
            db=db,
            evaluation_type=evaluation_type,
            llm=llm,
            rule_id=rule_id,
            content=contents
        )
        
        # 执行评估
        result = await review_agent.run()

        
        if result.get("status") == "success":
            logger.info(f'result: {result}')
            return {
                "success": True,
                "message": "规则评估完成",
                "data": result.get("data", {})
            }
        else:
            return {
                "success": False,
                "message": result.get("message", "规则评估失败"),
                "data": {"result": "评估失败", "status": "failed"}
            }
            
    except Exception as e:
        logger.error(f"规则评估失败: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "success": False,
            "message": f"规则评估失败: {str(e)}",
            "data": {"result": "", "status": "error"}
        }

@router.get("/protection-rules", response_model=Dict[str, Any])
async def get_rules(
    skip: int = 0, 
    limit: int = 100, 
    current_user: dict = Depends(verify_token)
):
    """
    获取所有消保规则列表
    """
    try:
        query = {"is_deleted": {"$ne": True}, "user_id": current_user["id"]}
        rules = await db["consumer_protection_rules"].find(query).skip(skip).limit(limit).to_list(limit)
        
        # 转换ObjectId为字符串，以便JSON序列化
        for rule in rules:
            rule = fix_objectid(rule)
            rule["id"] = str(rule["id"]) if "id" in rule else str(rule["_id"])
            if "_id" in rule:
                del rule["_id"]
            
        return {
            "success": True,
            "data": rules,
            "total": await db["consumer_protection_rules"].count_documents(query)
        }
    except Exception as e:
        logger.error(f"获取消保规则列表失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取规则列表失败: {str(e)}")

@router.get("/protection-rules/{rule_id}", response_model=Dict[str, Any])
async def get_rule(
    rule_id: str = PathParam(..., description="规则ID"),
    current_user: dict = Depends(verify_token)
):
    """
    根据ID获取消保规则详情
    """
    try:
        rule = await db["consumer_protection_rules"].find_one({"_id": ObjectId(rule_id), "is_deleted": {"$ne": True}})
        if not rule:
            return {"success": False, "error": "规则不存在"}
        
        rule = fix_objectid(rule)
        rule["id"] = str(rule["id"]) if "id" in rule else str(rule["_id"])
        if "_id" in rule:
            del rule["_id"]
        return {"success": True, "data": rule}
    except Exception as e:
        logger.error(f"获取消保规则详情失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取规则详情失败: {str(e)}")

@router.post("/protection-rules", response_model=Dict[str, Any])
async def create_rule(
    rule: ConsumerProtectionRuleCreate = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    创建新消保规则
    """
    try:
        # 检查规则ID是否已存在
        existing_rule = await db["consumer_protection_rules"].find_one({"ruleId": rule.ruleId, "is_deleted": {"$ne": True}})
        if existing_rule:
            return {"success": False, "error": "规则ID已存在"}
            
        rule_dict = rule.dict()
        rule_dict["user_id"] = current_user["id"]
        rule_dict["user_name"] = current_user.get("name", "")
        rule_dict["_id"] = ObjectId()
        rule_dict["created_at"] = datetime.now()
        rule_dict["updated_at"] = datetime.now()
        rule_dict["is_deleted"] = False

        logger.info(f"创建消保规则: {rule_dict}")
        
        result = await db["consumer_protection_rules"].insert_one(rule_dict)
        if result.inserted_id:
            rule_dict['id'] = str(rule_dict["_id"])
            del rule_dict["_id"]
            return {"success": True, "data": rule_dict, "message": "消保规则创建成功"}
        else:
            return {"success": False, "error": "消保规则创建失败"}
    except Exception as e:
        logger.error(f"创建消保规则失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"创建规则失败: {str(e)}")

@router.put("/protection-rules/{rule_id}", response_model=Dict[str, Any])
async def update_rule(
    rule_id: str = PathParam(..., description="规则ID"),
    rule: ConsumerProtectionRuleUpdate = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    更新消保规则
    """
    try:
        # 检查规则是否存在
        existing_rule = await db["consumer_protection_rules"].find_one({"_id": ObjectId(rule_id), "is_deleted": {"$ne": True}})
        if not existing_rule:
            return {"success": False, "error": "规则不存在"}
        
        # 更新数据
        update_data = {k: v for k, v in rule.dict(exclude_unset=True).items() if v is not None}
        if update_data:
            update_data["updated_at"] = datetime.now()
            result = await db["consumer_protection_rules"].update_one(
                {"_id": ObjectId(rule_id)},
                {"$set": update_data}
            )
            if result.modified_count:
                return {"success": True, "message": "消保规则更新成功"}
            
        return {"success": True, "message": "无数据更新"}
    except Exception as e:
        logger.error(f"更新消保规则失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"更新规则失败: {str(e)}")

@router.delete("/protection-rules/{rule_id}", response_model=Dict[str, Any])
async def delete_rule(
    rule_id: str = PathParam(..., description="规则ID"),
    current_user: dict = Depends(verify_token)
):
    """
    删除消保规则（软删除）
    """
    try:
        # 检查规则是否存在
        existing_rule = await db["consumer_protection_rules"].find_one({"_id": ObjectId(rule_id), "is_deleted": {"$ne": True}})
        if not existing_rule:
            return {"success": False, "error": "规则不存在"}
        
        # 软删除
        result = await db["consumer_protection_rules"].update_one(
            {"_id": ObjectId(rule_id)},
            {"$set": {"is_deleted": True, "updated_at": datetime.now()}}
        )
        if result.modified_count:
            return {"success": True, "message": "消保规则删除成功"}
        else:
            return {"success": False, "error": "消保规则删除失败"}
    except Exception as e:
        logger.error(f"删除消保规则失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"删除规则失败: {str(e)}")

# 添加消保类型(Type)管理相关接口
class ConsumerProtectionTypeCreate(BaseModel):
    """消保类型创建请求模型"""
    name: str
    description: Optional[str] = None
    is_active: bool = True
    rules: List[str] = []
    
class ConsumerProtectionTypeUpdate(BaseModel):
    """消保类型更新请求模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    rules: Optional[List[str]] = None

@router.get("/protection-types", response_model=Dict[str, Any])
async def get_protection_types(
    skip: int = 0, 
    limit: int = 100, 
    current_user: dict = Depends(verify_token)
):
    """
    获取所有消保类型列表
    """
    try:
        query = {"is_deleted": {"$ne": True}, "user_id": current_user["id"]}
        types = await db["consumer_protection_types"].find(query, {
            "created_at": 1,
            "name": 1,
            "rules": 1,
            "description": 1,
            "is_active": 1
        }).skip(skip).limit(limit).to_list(limit)
        
        # 转换ObjectId为字符串，添加规则数量
        for type_item in types:
            type_item["id"] = str(type_item["_id"])
            del type_item["_id"]
            type_item["rules_count"] = len(type_item.get("rules", []))
            # 格式化created_at为yyyy-MM-dd HH:mm:ss字符串
            if type_item.get("created_at"):
                type_item["created_at"] = type_item["created_at"].strftime("%Y-%m-%d %H:%M:%S")
            else:
                type_item["created_at"] = None

            
        return {
            "success": True,
            "data": types,
            "total": await db["consumer_protection_types"].count_documents(query)
        }
    except Exception as e:
        logger.error(f"获取消保类型列表失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取类型列表失败: {str(e)}")

@router.post("/protection-types", response_model=Dict[str, Any])
async def create_protection_type(
    type_data: ConsumerProtectionTypeCreate = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    创建新消保类型
    """
    try:
        type_dict = type_data.dict()
        type_dict["user_id"] = current_user["id"]
        type_dict["user_name"] = current_user.get("name", "")
        type_dict["_id"] = ObjectId()
        type_dict["created_at"] = datetime.now()
        type_dict["updated_at"] = datetime.now()
        type_dict["is_deleted"] = False
        
        result = await db["consumer_protection_types"].insert_one(type_dict)
        if result.inserted_id:
            type_dict["id"] = str(type_dict["_id"])
            del type_dict["_id"]
            return {"success": True, "data": type_dict, "message": "消保类型创建成功"}
        else:
            return {"success": False, "error": "消保类型创建失败"}
    except Exception as e:
        logger.error(f"创建消保类型失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"创建类型失败: {str(e)}")

@router.put("/protection-types/{type_id}", response_model=Dict[str, Any])
async def update_protection_type(
    type_id: str = PathParam(..., description="类型ID"),
    type_data: ConsumerProtectionTypeUpdate = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    更新消保类型
    """
    try:
        # 检查类型是否存在
        existing_type = await db["consumer_protection_types"].find_one({"_id": ObjectId(type_id), "is_deleted": {"$ne": True}})
        if not existing_type:
            return {"success": False, "error": "消保类型不存在"}
        
        # 更新数据
        update_data = {k: v for k, v in type_data.dict(exclude_unset=True).items() if v is not None}
        if update_data:
            update_data["updated_at"] = datetime.now()
            result = await db["consumer_protection_types"].update_one(
                {"_id": ObjectId(type_id)},
                {"$set": update_data}
            )
            if result.modified_count:
                return {"success": True, "message": "消保类型更新成功"}
            
        return {"success": True, "message": "无数据更新"}
    except Exception as e:
        logger.error(f"更新消保类型失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"更新类型失败: {str(e)}")

@router.delete("/protection-types/{type_id}", response_model=Dict[str, Any])
async def delete_protection_type(
    type_id: str = PathParam(..., description="类型ID"),
    current_user: dict = Depends(verify_token)
):
    """
    删除消保类型（软删除）
    """
    try:
        # 检查类型是否存在
        existing_type = await db["consumer_protection_types"].find_one({"_id": ObjectId(type_id), "is_deleted": {"$ne": True}})
        if not existing_type:
            return {"success": False, "error": "消保类型不存在"}
        
        # 软删除
        result = await db["consumer_protection_types"].update_one(
            {"_id": ObjectId(type_id)},
            {"$set": {"is_deleted": True, "updated_at": datetime.now()}}
        )
        
        if result.modified_count:
            return {"success": True, "message": "消保类型删除成功"}
        else:
            return {"success": False, "error": "消保类型删除失败"}
    except Exception as e:
        logger.error(f"删除消保类型失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"删除类型失败: {str(e)}")

@router.get("/protection-types/{type_id}/rules", response_model=Dict[str, Any])
async def get_rules_by_type(
    type_id: str = PathParam(..., description="消保类型ID"),
    current_user: dict = Depends(verify_token)
):
    """
    获取指定消保类型下的所有规则
    """
    try:
        # 先获取类型
        task_type = await db["consumer_protection_types"].find_one({"_id": ObjectId(type_id), "is_deleted": {"$ne": True}})
        if not task_type:
            return {"success": False, "error": "消保类型不存在"}
        
        # 获取类型关联的规则IDs
        rule_ids = task_type.get("rules", [])
        
        # 查询规则
        if rule_ids:
            # 将字符串ID转换为ObjectId
            object_rule_ids = [ObjectId(rule_id) for rule_id in rule_ids]
            rules = await db["consumer_protection_rules"].find({
                "_id": {"$in": object_rule_ids}, 
                "is_deleted": {"$ne": True}
            }).to_list(length=100)

            logger.info(f"获取消保类型规则: {rules}")
            
            # 转换ObjectId为字符串
            for rule in rules:
                rule = fix_objectid(rule)
                rule["id"] = str(rule["id"]) if "id" in rule else str(rule["_id"])
                if "_id" in rule:
                    del rule["_id"]
                
            return {"success": True, "data": rules}
        else:
            return {"success": True, "data": []}
    except Exception as e:
        logger.error(f"获取消保类型规则失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取类型规则失败: {str(e)}")

@router.put("/protection-types/{type_id}/rules", response_model=Dict[str, Any])
async def add_rule_to_type(
    type_id: str = PathParam(..., description="消保类型ID"),
    data: Dict[str, str] = Body(...),
    current_user: dict = Depends(verify_token)
):
    """
    将规则添加到消保类型
    """
    try:
        rule_id = data.get("rule_id")
        if not rule_id:
            return {"success": False, "error": "缺少规则ID"}
        
        # 检查规则是否存在
        rule = await db["consumer_protection_rules"].find_one({"_id": ObjectId(rule_id), "is_deleted": {"$ne": True}})
        if not rule:
            return {"success": False, "error": "规则不存在"}
        
        # 检查消保类型是否存在
        type_obj = await db["consumer_protection_types"].find_one({"_id": ObjectId(type_id), "is_deleted": {"$ne": True}})
        if not type_obj:
            return {"success": False, "error": "消保类型不存在"}
        
        # 添加规则到消保类型
        rules = type_obj.get("rules", [])
        if rule_id not in rules:
            rules.append(rule_id)
            
        # 更新消保类型
        result = await db["consumer_protection_types"].update_one(
            {"_id": ObjectId(type_id)},
            {"$set": {"rules": rules, "updated_at": datetime.now()}}
        )
        
        if result.modified_count:
            return {"success": True, "message": "规则添加成功"}
        else:
            return {"success": False, "error": "规则添加失败"}
    except Exception as e:
        logger.error(f"添加规则到消保类型失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"添加规则到类型失败: {str(e)}")

@router.delete("/protection-types/{type_id}/rules/{rule_id}", response_model=Dict[str, Any])
async def remove_rule_from_type(
    type_id: str = PathParam(..., description="消保类型ID"),
    rule_id: str = PathParam(..., description="规则ID"),
    current_user: dict = Depends(verify_token)
):
    """
    从消保类型中移除规则
    """
    try:
        # 检查消保类型是否存在
        type_obj = await db["consumer_protection_types"].find_one({"_id": ObjectId(type_id), "is_deleted": {"$ne": True}})
        if not type_obj:
            return {"success": False, "error": "消保类型不存在"}
        
        # 移除规则
        rules = type_obj.get("rules", [])
        if rule_id in rules:
            rules.remove(rule_id)
            
            # 更新消保类型
            result = await db["consumer_protection_types"].update_one(
                {"_id": ObjectId(type_id)},
                {"$set": {"rules": rules, "updated_at": datetime.now()}}
            )
            
            if result.modified_count:
                return {"success": True, "message": "规则移除成功"}
            else:
                return {"success": False, "error": "规则移除失败"}
        else:
            return {"success": True, "message": "规则不在消保类型中"}
    except Exception as e:
        logger.error(f"从消保类型中移除规则失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"从消保类型中移除规则失败: {str(e)}")

def fix_objectid(doc):
    if isinstance(doc, dict):
        for k, v in doc.items():
            if isinstance(v, ObjectId):
                doc[k] = str(v)
            elif isinstance(v, list):
                doc[k] = [fix_objectid(i) for i in v]
            elif isinstance(v, dict):
                doc[k] = fix_objectid(v)
    return doc

# 保存消保分析结果
@router.post("/save-result", response_model=Dict[str, Any])
async def save_consumer_protection_result(
    request_data: Dict[str, Any] = Body(...),
    current_user: dict = Depends(verify_token)
):
    try:

        logger.info(f"保存消保分析结果: {request_data}")
        
        # 检查文件ID是否存在
        file_id = request_data.get("file_id")
        file_record = await db["consumer_protection_files"].find_one({"_id": ObjectId(file_id)})
        if not file_record:
            return {
                "success": False,
                "message": "文件不存在"
            }
        
        # 检查是否已存在分析结果
        existing_result = await db["consumer_protection_results"].find_one({
            "file_id": file_id,
            "is_deleted": False
        })
        
        # 准备保存数据
        chunk_contents = []
        for chunk_data in request_data.get("chunk_contents", []):
            # 创建引用列表
            quote_list = []
            for quote_data in chunk_data.get("quote_list", []):
                quote_info = QuoteInfo(
                    regulation_name=quote_data.get("regulationName", ""),
                    provision=quote_data.get("provision", ""),
                    requirement=quote_data.get("requirement", ""),
                    similarity_scores=quote_data.get("similarityScores", []),
                    tags=quote_data.get("tags", []),
                    effective_date=quote_data.get("effectiveDate")
                )
                quote_list.append(quote_info)
            
            # 创建问题列表
            issues = []
            for issue_data in chunk_data.get("issues", []):
                issue = Issue(
                    rule_id=issue_data.get("rule_id", ""),
                    description=issue_data.get("description", ""),
                    location=issue_data.get("location", ""),
                    severity=issue_data.get("severity", ""),
                    suggestion=issue_data.get("suggestion", "")
                )
                issues.append(issue)
            
            # 创建内容块
            chunk_content = ChunkContent(
                file_name=chunk_data.get("file_name", ""),
                chunk_number=chunk_data.get("chunk_number", 0),
                content=chunk_data.get("content", ""),
                assessment_tag=chunk_data.get("assessment_tag", ""),
                analysis_result=chunk_data.get("analysis_result", ""),
                thought_process=chunk_data.get("thought_process", ""),
                suggestion=chunk_data.get("suggestion", ""),
                quote_list=quote_list,
                is_analyzed=chunk_data.get("is_analyzed", False),
                has_issue=chunk_data.get("has_issue", False),
                issues=issues,
                related_rules=chunk_data.get("related_rules", [])
            )
            chunk_contents.append(chunk_content)
        
        # 创建规则评估结果
        rule_results = []
        for rule_data in request_data.get("rule_results", []):
            rule_result = RuleEvaluationResult(
                rule_id=rule_data.get("rule_id", ""),
                rule_name=rule_data.get("rule_name", ""),
                description=rule_data.get("description", []),
                is_evaluated=rule_data.get("is_evaluated", False),
                evaluation_status=rule_data.get("evaluation_status", ""),
                evaluation_result=rule_data.get("evaluation_result", ""),
                analysis=rule_data.get("analysis", ""),
                overall_suggestion=rule_data.get("overall_suggestion", ""),
                affected_chunks=rule_data.get("affected_chunks", [])
            )
            rule_results.append(rule_result)
        
        # 创建消保分析结果
        consumer_protection = ConsumerProtection(
            file_id=file_id,
            file_name=request_data.get("file_name", ""),
            file_type=request_data.get("file_type", ""),
            evaluation_type=request_data.get("evaluation_type", ""),
            chunk_contents=chunk_contents,
            rule_results=rule_results,
            total_rules=request_data.get("total_rules", 0),
            evaluated_rules=request_data.get("evaluated_rules", 0),
            failed_rules=request_data.get("failed_rules", 0),
            is_completed=request_data.get("is_completed", False),
            user_id=current_user.get("id"),
            user_name=current_user.get("name"),
            created_at=datetime.now(),
            updated_at=datetime.now(),
            is_deleted=False
        )
        
        # 保存到数据库
        if existing_result:
            # 更新现有记录 - 使用to_mongo().to_dict()而不是dict()
            consumer_protection_dict = consumer_protection.to_mongo().to_dict()
            await db["consumer_protection_results"].update_one(
                {"_id": existing_result["_id"]},
                {"$set": {
                    "file_name": consumer_protection.file_name,
                    "file_type": consumer_protection.file_type,
                    "evaluation_type": consumer_protection.evaluation_type,
                    "chunk_contents": consumer_protection_dict["chunk_contents"],
                    "rule_results": consumer_protection_dict["rule_results"],
                    "total_rules": consumer_protection.total_rules,
                    "evaluated_rules": consumer_protection.evaluated_rules,
                    "failed_rules": consumer_protection.failed_rules,
                    "is_completed": consumer_protection.is_completed,
                    "updated_at": consumer_protection.updated_at
                }}
            )
            result_id = str(existing_result["_id"])
            message = "分析结果已更新"
        else:
            # 创建新记录 - 使用to_mongo().to_dict()而不是dict()
            consumer_protection_dict = consumer_protection.to_mongo().to_dict()
            result = await db["consumer_protection_results"].insert_one(consumer_protection_dict)
            result_id = str(result.inserted_id)
            message = "分析结果已保存"
        
        return {
            "success": True,
            "message": message,
            "data": {
                "id": result_id
            }
        }
    except Exception as e:
        traceback.print_exc()
        logger.error(f"保存消保分析结果失败: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "success": False,
            "message": f"保存失败: {str(e)}"
        }

# 获取消保分析历史记录
@router.get("/history", response_model=Dict[str, Any])
async def get_consumer_protection_history(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页记录数"),
    current_user: dict = Depends(verify_token)
):
    try:
        # 构建查询条件，只查询当前用户的记录
        query = {
            "user_id": current_user.get("id"),
            "is_deleted": False
        }
        
            
        # 计算总记录数
        total = await db["consumer_protection_results"].count_documents(query)
        
        # 计算跳过的记录数
        skip = (page - 1) * page_size
        
        # 查询记录并按创建时间倒序排序
        cursor = db["consumer_protection_results"].find(query).sort("created_at", -1).skip(skip).limit(page_size)
        results = await cursor.to_list(length=page_size)
        
        # 转换ObjectId为字符串
        items = []
        for result in results:
            result = fix_objectid(result)
            result["id"] = str(result.get("_id"))
            if "_id" in result:
                del result["_id"]
            
            # 统计不同evaluation_status的数量
            status_counts = {"error": 0, "warning": 0, "success": 0}
            for rule_result in result.get("rule_results", []):
                status = rule_result.get("evaluation_status", "").lower()
                if status in status_counts:
                    status_counts[status] += 1
                elif status == "failed":  # 如果状态是failed，计入error
                    status_counts["error"] += 1
            
            # 只返回必要的字段，避免返回大量内容数据
            items.append({
                "id": result["id"],
                "file_id": result["file_id"],
                "file_name": result["file_name"],
                "file_type": result["file_type"],
                "evaluation_type": result["evaluation_type"],
                "created_at": result["created_at"],
                "updated_at": result["updated_at"],
                "rule_results": result["rule_results"],
                "total_rules": result["total_rules"],
                "evaluated_rules": result["evaluated_rules"],
                "failed_rules": result["failed_rules"],
                "is_completed": result["is_completed"],
                "status_counts": status_counts  # 添加状态统计
            })
        
        return {
            "success": True,
            "message": "获取历史记录成功",
            "data": {
                "items": items,
                "total": total,
                "page": page,
                "page_size": page_size
            }
        }
    except Exception as e:
        logger.error(f"获取消保分析历史记录失败: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "success": False,
            "message": f"获取历史记录失败: {str(e)}",
            "data": {
                "items": [],
                "total": 0,
                "page": page,
                "page_size": page_size
            }
        }
