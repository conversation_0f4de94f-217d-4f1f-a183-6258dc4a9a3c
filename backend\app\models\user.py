# app/models/user.py
from pydantic import BaseModel, EmailStr
from typing import List, Optional, Dict
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import JSON

Base = declarative_base()

class Tag(BaseModel):
    key: str
    label: str

class Geographic(BaseModel):
    province: Dict[str, str]
    city: Dict[str, str]

class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False)
    avatar = Column(String, default="/avatar/default.jpeg")
    tags = Column(JSON, default=[])
    userid = Column(String, unique=True, nullable=False)
    signature = Column(String, nullable=True)
    title = Column(String, nullable=True)
    group_id = Column(Integer, ForeignKey('groups.id'), nullable=False)
    group_name = Column(String, nullable=False)
    notifyCount = Column(Integer, default=0)
    unreadCount = Column(Integer, default=0)
    country = Column(String, default="China")
    address = Column(String, nullable=True)
    phone = Column(String, unique=True, nullable=False)
    password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, nullable=False)
    role_name = Column(String, nullable=False)  # 修改字段名
    role_id = Column(Integer, nullable=False)
    auth_token = Column(String, nullable=True)
    token_expiry = Column(DateTime, nullable=True)
    currentAuthority = Column(String, default="user")

    def __repr__(self):
        return f"<User(name={self.name}, phone={self.phone})>"

class UserCreate(BaseModel):
    name: str
    phone: str
    password: str
    group_id: int
    role_id: int  # 使用 role_id 而不是 role_name

    class Config:
        from_attributes = True

class UserUpdate(BaseModel):
    name: Optional[str] = None
    phone: Optional[str] = None
    password: Optional[str] = None
    group_id: Optional[int] = None
    group_name: Optional[str] = None
    role_name: Optional[str] = None
    role_id: Optional[int] = None
    avatar: Optional[str] = None
    signature: Optional[str] = None
    title: Optional[str] = None
    is_active: Optional[bool] = None
    is_superuser: Optional[bool] = None
    auth_token: Optional[str] = None
    token_expiry: Optional[datetime] = None
    currentAuthority: Optional[str] = None

    class Config:
        from_attributes = True

class MyUserUpdate(BaseModel):
    name: Optional[str] = None
    password: Optional[str] = None
    avatar: Optional[str] = None

    class Config:
        from_attributes = True

class UserResponse(BaseModel):
    id: int
    name: str
    avatar: Optional[str] = "/avatar/default.jpeg"
    userid: str
    signature: Optional[str] = None
    title: Optional[str] = None
    group_id: int
    group_name: str
    tags: List[Tag] = []  # 使用 L
    notifyCount: Optional[int] = 0
    unreadCount: Optional[int] = 0
    phone: str
    is_active: bool
    is_superuser: bool
    role_name: str  # 修改字段名
    role_id: int
    currentAuthority: Optional[str] = "user"

    class Config:
        from_attributes = True

