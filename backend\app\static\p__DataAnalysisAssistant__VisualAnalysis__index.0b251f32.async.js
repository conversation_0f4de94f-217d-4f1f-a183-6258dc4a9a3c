(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[239],{47046:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"}},42110:function(e,t){"use strict";t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"}},82061:function(e,t,n){"use strict";var r=n(1413),a=n(67294),o=n(47046),s=n(91146),i=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o.Z}))},c=a.forwardRef(i);t.Z=c},47389:function(e,t,n){"use strict";var r=n(1413),a=n(67294),o=n(27363),s=n(91146),i=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o.Z}))},c=a.forwardRef(i);t.Z=c},45128:function(e,t,n){"use strict";var r=n(1413),a=n(67294),o=n(90102),s=n(91146),i=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o.Z}))},c=a.forwardRef(i);t.Z=c},87784:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(1413),a=n(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"},s=n(91146),i=function(e,t){return a.createElement(s.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:o}))};var c=a.forwardRef(i)},93933:function(e,t,n){"use strict";n.d(t,{$Z:function(){return Z},$o:function(){return d},Db:function(){return v},Mw:function(){return u},SJ:function(){return x},X1:function(){return b},Xw:function(){return p},bk:function(){return T},fx:function(){return N},qP:function(){return j},tn:function(){return k},zl:function(){return C}});var r=n(15009),a=n.n(r),o=n(99289),s=n.n(o),i=n(78158),c=n(10981);function u(e){return l.apply(this,arguments)}function l(){return(l=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/myConversations",{method:"GET",params:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function p(e){return f.apply(this,arguments)}function f(){return(f=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/conversations",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function d(e,t){return h.apply(this,arguments)}function h(){return(h=s()(a()().mark((function e(t,n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/conversationActive/"+t,{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function v(e){return m.apply(this,arguments)}function m(){return(m=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/clearConversation/"+t,{method:"PUT"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function x(e){return g.apply(this,arguments)}function g(){return(g=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/conversations/"+t,{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e,t){return y.apply(this,arguments)}function y(){return(y=s()(a()().mark((function e(t,n){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/conversations/".concat(t),{method:"PUT",data:n}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function k(e){return w.apply(this,arguments)}function w(){return(w=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/message",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Z(e){return _.apply(this,arguments)}function _(){return(_=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/messages/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function j(e){return S.apply(this,arguments)}function S(){return(S=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/delete_messages",{method:"DELETE",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function T(e){return E.apply(this,arguments)}function E(){return(E=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/messages/collected",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function N(e){return D.apply(this,arguments)}function D(){return(D=s()(a()().mark((function e(t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.N)("/api/app/get_knowledge_bases",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function C(e){return P.apply(this,arguments)}function P(){return(P=s()(a()().mark((function e(t){var n,r;return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=(0,c.bW)(),e.next=3,fetch("/api/app/chat/chat2kb",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(n)},body:JSON.stringify(t)});case 3:if((r=e.sent).ok){e.next=6;break}throw new Error("HTTP 错误！状态码：".concat(r.status));case 6:return e.abrupt("return",r);case 7:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},80416:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return se}});var r=n(97857),a=n.n(r),o=n(15009),s=n.n(o),i=n(64599),c=n.n(i),u=n(19632),l=n.n(u),p=n(99289),f=n.n(p),d=n(5574),h=n.n(d),v=n(93461),m=n(34114),x=n(78205),g=n(78919),b=n(4628),y=n(24495),k=n(9502),w=n(37864),Z=n(71471),_=n(2453),j=n(67294),S=n(10048),T=n(10981),E=n(78404),N=n(1832),D=n(14079),C=n(66513),P=n(93045),H=n(71255),R=n(47389),z=n(87784),A=n(82061),q=n(45128),O=n(16596),M=n(51042),F=n(42075),W=n(74330),I=n(40411),L=n(83622),G=n(83062),Y=n(17788),B=n(55102),J=n(27484),U=n.n(J),X=(0,n(24444).kc)((function(e){var t=e.token;return{layout:{width:"100%",minWidth:"1000px",height:"722px",borderRadius:t.borderRadius,display:"flex",background:t.colorBgContainer,fontFamily:"AlibabaPuHuiTi, ".concat(t.fontFamily,", sans-serif"),".ant-prompts":{color:t.colorText}},menu:{background:"".concat(t.colorBgLayout,"80"),width:"280px",height:"100%",display:"flex",flexDirection:"column"},conversations:{padding:"0 12px",flex:1,overflowY:"auto"},chat:{height:"100%",width:"100%",maxWidth:"700px",margin:"0 auto",boxSizing:"border-box",display:"flex",flexDirection:"column",padding:t.paddingLG,gap:"16px"},messages:{flex:1},placeholder:{paddingTop:"32px"},sender:{boxShadow:t.boxShadow},logo:{display:"flex",height:"72px",alignItems:"center",justifyContent:"start",padding:"0 24px",boxSizing:"border-box",img:{width:"24px",height:"24px",display:"inline-block"},span:{display:"inline-block",margin:"0 8px",fontWeight:"bold",color:t.colorText,fontSize:"16px"}},addBtn:{background:"#1677ff0f",border:"1px solid #1677ff34",width:"calc(100% - 24px)",margin:"0 12px 24px 12px"}}})),K=n(93933),$=n(85893),V=function(e,t){return(0,$.jsxs)(F.Z,{align:"start",children:[e,(0,$.jsx)("span",{children:t})]})},Q=[{key:"1",label:V((0,$.jsx)(N.Z,{style:{color:"#FF4D4F"}}),"Hot Topics"),description:"What are you interested in?",children:[{key:"1-1",description:"What's new in X?"},{key:"1-2",description:"What's AGI?"},{key:"1-3",description:"Where is the doc?"}]},{key:"2",label:V((0,$.jsx)(D.Z,{style:{color:"#1890FF"}}),"Design Guide"),description:"How to design a good product?",children:[{key:"2-1",icon:(0,$.jsx)(C.Z,{}),description:"Know the well"},{key:"2-2",icon:(0,$.jsx)(P.Z,{}),description:"Set the AI role"},{key:"2-3",icon:(0,$.jsx)(H.Z,{}),description:"Express the feeling"}]}],ee=[{key:"1",description:"Hot Topics",icon:(0,$.jsx)(N.Z,{style:{color:"#FF4D4F"}})},{key:"2",description:"Design Guide",icon:(0,$.jsx)(D.Z,{style:{color:"#1890FF"}})}],te=(0,T.bG)(),ne=(0,E.kH)(),re=(0,S.Z)({html:!0,breaks:!0}),ae=function(e){return(0,$.jsx)(Z.Z,{children:(0,$.jsx)("div",{dangerouslySetInnerHTML:{__html:re.render(e)}})})},oe="dataExploration",se=function(){var e,t=X().styles,n=(0,j.useState)(window.innerHeight),r=h()(n,1)[0],o=j.useRef(),i=j.useState(!1),u=h()(i,2),p=u[0],d=u[1],Z=j.useState(""),S=h()(Z,2),E=S[0],N=S[1],D=j.useState([]),C=h()(D,2),P=C[0],H=C[1],J=j.useState(),V=h()(J,2),re=V[0],se=V[1],ie=j.useState([]),ce=h()(ie,2),ue=ce[0],le=ce[1],pe=(0,j.useState)(!1),fe=h()(pe,2),de=fe[0],he=fe[1],ve=(0,j.useState)(!1),me=h()(ve,2),xe=me[0],ge=me[1],be=(0,j.useState)(""),ye=h()(be,2),ke=ye[0],we=ye[1],Ze=(0,j.useState)(""),_e=h()(Ze,2),je=_e[0],Se=_e[1],Te=(0,j.useState)([]),Ee=h()(Te,2),Ne=Ee[0],De=Ee[1],Ce=(0,v.Z)({request:(e=f()(s()().mark((function e(t,n){var r,a,i,u,p,f,d,h,v,m,x,g,b,y,k,w,Z,j,S,E,N,D,C,P,H,R,z,A,q,O,M,F,W,I,L;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.message,a=n.onSuccess,i=n.onUpdate,u=n.onError,e.prev=2,!de){e.next=6;break}return _.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 6:if(he(!0),console.log("message========================>",r),console.log("activeKey========================>",o.current),f=(0,T.bW)(),o.current){e.next=13;break}throw _.ZP.error("No active conversation selected"),new Error("No active conversation selected");case 13:return d=Ne.map((function(e){return{role:e.role,content:e.content}})),h={role:"user",content:r},console.log("activeKey===>",o.current),v={conversation_id:o.current,app_info:oe,user_id:null==te?void 0:te.id,user_name:null==te?void 0:te.name,extra:{},messages:[].concat(l()(d),[h])},x=!1,g="",i(m=""),e.next=23,fetch("/api/app/chat/system_app_completions",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(f)},body:JSON.stringify(v)});case 23:if((b=e.sent).ok){e.next=26;break}throw new Error("HTTP 错误！状态码：".concat(b.status));case 26:if(y=null===(p=b.body)||void 0===p?void 0:p.getReader()){e.next=29;break}throw new Error("当前浏览器不支持 ReadableStream。");case 29:k=new TextDecoder("utf-8"),w={conversation_id:o.current||"",message_id:"",meta_data:{},extra:{},role:"assistant",content:"",app_info:oe,user_id:null==te?void 0:te.id,user_name:null==te?void 0:te.name,references:[],token_count:null,price:null};case 31:if(x){e.next=89;break}return e.next=34,y.read();case 34:Z=e.sent,j=Z.value,Z.done&&(x=!0),g+=k.decode(j,{stream:!0}),S=g.split("\n\n"),g=S.pop()||"",E=c()(S),e.prev=42,E.s();case 44:if((N=E.n()).done){e.next=79;break}if(""!==(D=N.value).trim()){e.next=48;break}return e.abrupt("continue",77);case 48:C=D.split("\n"),P=null,H=null,R=c()(C);try{for(R.s();!(z=R.n()).done;)(A=z.value).startsWith("event: ")?P=A.substring(7).trim():A.startsWith("data: ")&&(H=A.substring(6))}catch(e){R.e(e)}finally{R.f()}if(!H){e.next=77;break}e.t0=P,e.next="answer"===e.t0?57:"moduleStatus"===e.t0?59:"appStreamResponse"===e.t0?61:"flowResponses"===e.t0?63:"end"===e.t0?65:"error"===e.t0?67:77;break;case 57:if("[DONE]"===H)x=!0;else try{O=JSON.parse(H),(M=(null===(q=O.choices[0])||void 0===q||null===(q=q.delta)||void 0===q?void 0:q.content)||"")&&i(m+=M)}catch(e){console.error("Error parsing answer data:",e)}return e.abrupt("break",77);case 59:try{F=JSON.parse(H),console.log("模块状态：",F)}catch(e){console.error("Error parsing moduleStatus data:",e)}return e.abrupt("break",77);case 61:try{W=JSON.parse(H),console.log("appStreamResponse",W)}catch(e){console.error("Error parsing appStreamResponse data:",e)}return e.abrupt("break",77);case 63:try{console.log("flowResponsesData",H)}catch(e){console.error("Error parsing flowResponses data:",e)}return e.abrupt("break",77);case 65:return x=!0,e.abrupt("break",77);case 67:e.prev=67,I=JSON.parse(H),i(I),e.next=76;break;case 72:throw e.prev=72,e.t1=e.catch(67),console.error("Error event received:",e.t1),e.t1;case 76:return e.abrupt("break",77);case 77:e.next=44;break;case 79:e.next=84;break;case 81:e.prev=81,e.t2=e.catch(42),E.e(e.t2);case 84:return e.prev=84,E.f(),e.finish(84);case 87:e.next=31;break;case 89:if(a(m),!m||""===m.trim()){e.next=96;break}return w.content=m,e.next=94,(0,K.tn)(w);case 94:(L=e.sent).success?(console.log("res===>",L),De([].concat(l()(Ne),[L.data]))):_.ZP.error("消息上报失败");case 96:e.next=102;break;case 98:e.prev=98,e.t3=e.catch(2),a("出现了，系统正在处理其他对话。请稍后重试"),u(e.t3 instanceof Error?e.t3:new Error("Unknown error"));case 102:return e.prev=102,he(!1),e.finish(102);case 105:case"end":return e.stop()}}),e,null,[[2,98,102,105],[42,81,84,87],[67,72]])}))),function(t,n){return e.apply(this,arguments)})}),Pe=h()(Ce,1)[0],He=(0,m.Z)({agent:Pe}),Re=He.onRequest,ze=He.messages,Ae=He.setMessages,qe=function(e){se(e),console.log("activeKey 设置",e),o.current=e},Oe=function(e){console.log("conversations==qqq=>",e);var t=e.filter((function(e){return e.pinned})).sort((function(e,t){return new Date(t.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return a()(a()({},e),{},{key:e.id||"",label:e.conversation_name||e.id,group:"置顶"})})),n=e.filter((function(e){return!e.pinned})).sort((function(e,t){return new Date(t.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return a()(a()({},e),{},{key:e.id||"",label:e.conversation_name||"",group:"对话"})}));H([].concat(l()(t),l()(n)))},Me=function(e){return 0===e.length?null:e.reduce((function(e,t){return new Date(t.active_at)>new Date(e.active_at)?t:e}))},Fe=function(){var e=f()(s()().mark((function e(t){var n,r,a;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,he(!0),console.info("获取对话信息",t),n=U()().format("YYYY-MM-DD HH:mm:ss"),e.next=6,(0,K.$o)(t,{conversation_name:null,active_at:n,pinned_at:null,pinned:null});case 6:null!=(r=e.sent)&&r.messages?(console.info("设置对话信息",r.messages),De(r.messages),a=r.messages.map((function(e){return{id:e.id||e.message_id,message:e.content,status:"assistant"===e.role?"success":"local",meta:e.meta||{avatar:"assistant"===e.role?(null==ne?void 0:ne.logo)||"/static/assets/logo.png":(null==te?void 0:te.avatar)||"/static/assets/avatar/default.jpeg"}}})),Ae(a),qe(t)):_.ZP.error("获取对话信息失败"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("切换对话时出错：",e.t0);case 13:return e.prev=13,he(!1),e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[0,10,13,16]])})));return function(t){return e.apply(this,arguments)}}(),We=function(){var e=f()(s()().mark((function e(){var t,n,r,a;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!de){e.next=3;break}return _.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(!(t=(0,T.bG)())){e.next=19;break}return e.prev=5,n=(new Date).toLocaleString(),r="对话-".concat(n),e.next=10,(0,K.Xw)({user_id:t.id,user_name:t.name,conversation_name:r,app_info:oe});case 10:a=e.sent,console.log("newConversation===>",a),Oe([].concat(l()(P),[{key:a.id||"",id:a.id||"",label:a.conversation_name||"",conversation_name:a.conversation_name||"",active_at:a.active_at||"",pinned_at:a.pinned_at,pinned:a.pinned||!1,messages:[]}])),qe(a.id||""),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(5),console.error("创建新对话时出错：",e.t0);case 19:case"end":return e.stop()}}),e,null,[[5,16]])})));return function(){return e.apply(this,arguments)}}(),Ie=function(){var e=f()(s()().mark((function e(t){var n,r,o,i,c;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=P.find((function(e){return e.key===t}))){e.next=3;break}return e.abrupt("return");case 3:return r=n.pinned,o=!r,e.prev=6,i=U()().format("YYYY-MM-DD HH:mm:ss"),e.next=10,(0,K.X1)(t,{conversation_name:null,active_at:null,pinned:o,pinned_at:i});case 10:c=P.map((function(e){return e.key===t?a()(a()({},e),{},{pinned:o}):e})),Oe(c),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),console.error("更新置顶状态时出错：",e.t0);case 17:case"end":return e.stop()}}),e,null,[[6,14]])})));return function(t){return e.apply(this,arguments)}}(),Le=function(){var e=f()(s()().mark((function e(t){var n;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,K.SJ)(t);case 3:n=P.filter((function(e){return e.key!==t})),Oe(n),o.current===t&&n.length>0&&Fe(n[0].key||""),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除对话时出错：",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(t){return e.apply(this,arguments)}}(),Ge=function(){var e=f()(s()().mark((function e(t,n){var r,o;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,P.find((function(e){return e.key===t}))){e.next=4;break}return e.abrupt("return");case 4:return r={conversation_name:n,active_at:null,pinned_at:null,pinned:null},e.next=7,(0,K.X1)(t,r);case 7:null!=(o=e.sent)&&o.success?H((function(e){return e.map((function(e){return e.key===t?a()(a()({},e),{},{label:n}):e}))})):_.ZP.error("更新对话标题失败"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("更新对话标题时出错：",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(t,n){return e.apply(this,arguments)}}();(0,j.useEffect)((function(){var e=function(){var e=f()(s()().mark((function e(){var t,n,r;return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t=(0,T.bG)())){e.next=20;break}return e.prev=2,e.next=5,(0,K.Mw)({user_id:t.id,app_info:oe});case 5:if(!(n=e.sent).success||!Array.isArray(n.data)){e.next=15;break}if(0!==n.data.length){e.next=12;break}return e.next=10,We();case 10:e.next=15;break;case 12:r=Me(n.data),Oe(n.data),Fe(r?r.id:n.data[0].id||"");case 15:e.next=20;break;case 17:e.prev=17,e.t0=e.catch(2),console.error("初始化对话时出错：",e.t0);case 20:case"end":return e.stop()}}),e,null,[[2,17]])})));return function(){return e.apply(this,arguments)}}();e()}),[oe]);var Ye=function(e){de?_.ZP.error("系统正在处理其他对话。请稍😊"):Re(e.data.description)},Be=function(){var e=f()(s()().mark((function e(t){return s()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!de){e.next=3;break}return _.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:return e.next=5,Fe(t);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Je=(0,$.jsxs)(F.Z,{direction:"vertical",size:16,className:t.placeholder,children:[(0,$.jsx)(x.Z,{variant:"borderless",icon:(0,$.jsx)("img",{src:(null==ne?void 0:ne.logo)||"/static/assets/logo.png",alt:"logo"}),title:"你好，我是数据探索助手.",description:"基于Ant Design, AGI产品接口解决方案, 创建更好的智能视觉~"}),(0,$.jsx)(g.Z,{title:"Do you want?",items:Q,styles:{list:{width:"100%"},item:{flex:1}},onItemClick:Ye})]}),Ue=ze.length>0?ze.map((function(e){var t=e.id,n=e.message,r=e.status;return console.log("拼接id==========>",o.current,t),{key:o.current+"_"+t,loadingRender:function(){return(0,$.jsxs)(F.Z,{children:[(0,$.jsx)(W.Z,{size:"small"}),"模型思考中..."]})},loading:"loading"===r&&n.length<1,messageRender:ae,content:n,shape:"corner",rols:"local"===r?"ai":"user",avatar:"local"===r?{src:(null==te?void 0:te.avatar)||"/static/assets/avatar/default.jpeg"}:{src:(null==ne?void 0:ne.logo)||"/static/assets/logo.png"},placement:"local"===r?"end":"start"}})):[{content:Je,variant:"borderless"}],Xe=(0,$.jsx)(I.Z,{dot:ue.length>0&&!p,children:(0,$.jsx)(L.ZP,{type:"text",icon:(0,$.jsx)(q.Z,{}),onClick:function(){return d(!p)}})}),Ke=(0,$.jsx)(b.Z.Header,{title:"Attachments",open:p,onOpenChange:d,styles:{content:{padding:0}},children:(0,$.jsx)(y.Z,{beforeUpload:function(){return!1},items:ue,onChange:function(e){return le(e.fileList)},placeholder:function(e){return"drop"===e?{title:"Drop file here"}:{icon:(0,$.jsx)(O.Z,{}),title:"Upload files",description:"Click or drag files to this area to upload"}}})}),$e=(0,$.jsxs)("div",{className:t.logo,children:[(0,$.jsx)("span",{children:"对话记录"}),(0,$.jsx)(G.Z,{title:"新对话",children:(0,$.jsx)(L.ZP,{type:"text",icon:(0,$.jsx)(M.Z,{}),onClick:We,style:{fontSize:"16px"}})})]}),Ve=(0,$.jsx)(Y.Z,{title:"修改对话标题",open:xe,onOk:function(){je&&ke.trim()&&(Ge(je,ke.trim()),ge(!1))},onCancel:function(){ge(!1),we(""),Se("")},children:(0,$.jsx)(B.Z,{value:ke,onChange:function(e){return we(e.target.value)},placeholder:"请输入新的对话标题"})});return(0,$.jsxs)("div",{className:t.layout,style:{height:r-56},children:[(0,$.jsxs)("div",{className:t.menu,children:[$e,(0,$.jsx)(k.Z,{items:P,className:t.conversations,activeKey:re,onActiveChange:Be,menu:function(e){return{items:[{label:"重命名",key:"edit",icon:(0,$.jsx)(R.Z,{})},{label:"置顶",key:"pin",icon:(0,$.jsx)(z.Z,{})},{label:"删除",key:"delete",icon:(0,$.jsx)(A.Z,{}),danger:!0}],onClick:function(t){switch(console.log("menuInfo","Click ".concat(e.key," - ").concat(t.key)),t.key){case"edit":Se(e.key),we(e.label),ge(!0);break;case"pin":Ie(e.key);break;case"delete":if(de)return void _.ZP.error("系统正在处理其他对话。请稍😊");Le(e.key)}}}},groupable:!0})]}),(0,$.jsxs)("div",{className:t.chat,children:[(0,$.jsx)(w.Z.List,{items:Ue,className:t.messages}),(0,$.jsx)(g.Z,{items:ee,onItemClick:Ye}),(0,$.jsx)(b.Z,{value:E,header:Ke,onSubmit:function(e){e&&(Re(e),N(""))},onChange:N,prefix:Xe,loading:Pe.isRequesting(),className:t.sender})]}),Ve]})}},64599:function(e,t,n){var r=n(96263);e.exports=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=r(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,o=function(){};return{s:o,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,i=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){c=!0,s=e},f:function(){try{i||null==n.return||n.return()}finally{if(c)throw s}}}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);