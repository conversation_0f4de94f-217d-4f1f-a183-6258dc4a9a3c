from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.feedback import FeedbackC<PERSON>, FeedbackUpdate, FeedbackResponse
from ..db.mongodb import db
from datetime import datetime
from ..utils.auth import verify_token
from pydantic import ValidationError
from bson import ObjectId
from app.utils.logging_config import setup_logging, get_logger
setup_logging()
logger = get_logger(__name__)

router = APIRouter(
    prefix="/api",
    tags=["feedbacks"]
)

# 创建新消息
@router.post("/feedbacks", response_model=Dict[str, Any])
async def create_feedback(feedback: FeedbackCreate, current_user: dict = Depends(verify_token)):
    try:
        new_feedback = feedback.dict()
        new_feedback.update({
            "_id": ObjectId(),
            "user_id": current_user["id"],
            "user_name": current_user["name"],
            "created_at": datetime.now() if new_feedback.get("created_at") is None else datetime.strptime(new_feedback.get("created_at"),'%Y-%m-%d %H:%M:%S'),
        })
        await db["feedbacks"].insert_one(new_feedback)
        logger.info(f"新消息创建成功: {new_feedback}")
        new_feedback["id"] = str(new_feedback["_id"])
        del new_feedback["_id"]
        return {
            "data": FeedbackResponse(**new_feedback),
            "success": True,
        }
    except ValidationError as ve:
        logger.error(f"验证错误: {ve.json()}")
        raise HTTPException(status_code=422, detail=ve.errors())
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"内部服务器错误: {str(e)}")

# 获取消息列表
@router.get("/feedbacks", response_model=Dict[str, Any])
async def read_feedbacks(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    content: Optional[str] = None,
    tags: Optional[str] = None,
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {"user_id": current_user["id"]}
    if tags:
        query["tags"] = {"$regex": tags, "$options": "i"}  # 
    if content:
        query["content"] = {"$regex": content, "$options": "i"} 

    messages = await db["messages"].find(query,{
        "conversation_id": 1,
        "feedback_types": 1,
        "app_info": 1,
        "content": 1,
        "created_at": 1,
        "tags": 1,
        "app_info": 1
    }).sort("created_at", -1).skip(skip).limit(pageSize).to_list(pageSize)
    total = await db["messages"].count_documents(query)

    for msg in messages:
        msg["id"] = str(msg["_id"])
        del msg["_id"]

    return {
        "data": messages,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 获取单个消息
@router.get("/feedbacks/{feedback_id}", response_model=FeedbackResponse)
async def read_feedback(feedback_id: str, current_user: dict = Depends(verify_token)):
    feedback = await db["feedbacks"].find_one({"_id": ObjectId(feedback_id)})
    if feedback is None:
        raise HTTPException(status_code=404, detail="Feedback not found")
    feedback["id"] = str(feedback["_id"])
    del feedback["_id"]
    return FeedbackResponse(**feedback)

# 更新消息
@router.put("/feedbacks/{feedback_id}", response_model=FeedbackResponse)
async def update_feedback(feedback_id: str, feedback: FeedbackUpdate, current_user: dict = Depends(verify_token)):
    update_data = feedback.dict(exclude_unset=True)
    result = await db["feedbacks"].update_one({"_id": ObjectId(feedback_id)}, {"$set": update_data})
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Feedback not found")
    
    updated_feedback = await db["feedbacks"].find_one({"_id": ObjectId(feedback_id)})
    updated_feedback["id"] = str(updated_feedback["_id"])
    del updated_feedback["_id"]
    return FeedbackResponse(**updated_feedback)

# 删除消息
@router.delete("/feedbacks/{feedback_id}", response_model=Dict[str, Any])
async def delete_feedback(feedback_id: str, current_user: dict = Depends(verify_token)):
    try:
        logger.info(f'删除 feedback_id===>{feedback_id}')
        result = await db["feedbacks"].delete_one({"feedback_id": feedback_id})
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Feedback not found")
        return {"success": True, "id": feedback_id}
    except Exception as e:
        logger.error(f"删除反馈时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除反馈失败: {str(e)}")

