{"cells": [{"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["解密后的文本: \n"]}], "source": ["from Crypto.Cipher import AES\n", "from Crypto.Hash import SHA1\n", "from Crypto.Random import get_random_bytes\n", "import base64\n", "\n", "def decrypt(encrypted_data, secret_key):\n", "    try:\n", "        if not encrypted_data:\n", "            return None\n", "\n", "        # 确保密钥为16字节（AES-128）\n", "        sha1 = SHA1.new()\n", "        sha1.update(secret_key.encode('utf-8'))\n", "        key_bytes = sha1.digest()[:16]  # 取前16字节\n", "\n", "        # 使用与Java中相同的IV值\n", "        iv = \"Jv7hVzLRlhV9gCg=\".encode('utf-8')  # Java中使用了固定的IV\n", "\n", "        # Base64解码加密数据\n", "        encrypted_bytes = base64.b64decode(encrypted_data)\n", "\n", "        # 创建AES解密器\n", "        cipher = AES.new(key_bytes, AES.MODE_CBC, iv)\n", "\n", "        # 解密数据\n", "        decrypted_bytes = cipher.decrypt(encrypted_bytes)\n", "\n", "        # 去除填充（PKCS5Padding）\n", "        padding_length = decrypted_bytes[-1]\n", "        decrypted = decrypted_bytes[:-padding_length].decode('utf-8')\n", "\n", "        return decrypted\n", "    except Exception as e:\n", "        print(\"解密错误:\", e)\n", "        return None\n", "\n", "# 示例用法\n", "encrypted_data = \"Ufhpd/1Y0MVGdayPOg8OzQ==\"  # 您的加密数据\n", "secret_key = \"F+1ZmYZzNSFDXqZdpJ3qTxCJbU2qJv7hVzLRlhV9gCg=\"  # 加密时的密钥\n", "decrypted_text = decrypt(encrypted_data, secret_key)\n", "\n", "print(\"解密后的文本:\", decrypted_text)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["解密后的文本: \n"]}], "source": ["from Crypto.Cipher import AES\n", "from Crypto.Hash import SHA1\n", "import base64\n", "\n", "def decrypt(encrypted_data, secret_key):\n", "    try:\n", "        if not encrypted_data:\n", "            return None\n", "\n", "        # 确保密钥为16字节（AES-128）\n", "        sha1 = SHA1.new()\n", "        sha1.update(secret_key.encode('utf-8'))\n", "        key_bytes = sha1.digest()[:16]  # 取前16字节\n", "\n", "        # 修正IV长度为16字节\n", "        iv = \"Jv7hVzLRlhV9gCg=\".ljust(16, '\\0').encode('utf-8')  # 填充到16字节\n", "\n", "        # Base64解码加密数据\n", "        encrypted_bytes = base64.b64decode(encrypted_data)\n", "\n", "        # 创建AES解密器\n", "        cipher = AES.new(key_bytes, AES.MODE_CBC, iv)\n", "\n", "        # 解密数据\n", "        decrypted_bytes = cipher.decrypt(encrypted_bytes)\n", "\n", "        # 去除填充（PKCS5Padding）\n", "        padding_length = decrypted_bytes[-1]\n", "        decrypted = decrypted_bytes[:-padding_length].decode('utf-8')\n", "\n", "        return decrypted\n", "    except Exception as e:\n", "        print(\"解密错误:\", e)\n", "        return None\n", "\n", "# 示例用法\n", "encrypted_data = \"Ufhpd/1Y0MVGdayPOg8OzQ==\"  # 您的加密数据\n", "secret_key = \"F+1ZmYZzNSFDXqZdpJ3qTxCJbU2qJv7hVzLRlhV9gCg=\"  # 加密时的密钥\n", "decrypted_text = decrypt(encrypted_data, secret_key)\n", "\n", "print(\"解密后的文本:\", decrypted_text)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["解密错误: Invalid padding length\n", "解密后的文本: None\n"]}], "source": ["from Crypto.Cipher import AES\n", "from Crypto.Hash import SHA1\n", "import base64\n", "\n", "def decrypt(encrypted_data, secret_key,PARAM_KEY):\n", "    try:\n", "        if not encrypted_data:\n", "            return None\n", "\n", "        # 使用SHA1生成密钥，与Java的SecureRandom逻辑一致\n", "        sha1 = SHA1.new()\n", "        sha1.update(secret_key.encode('utf-8'))\n", "        key_bytes = sha1.digest()[:16]  # 截取前16字节，作为AES-128密钥\n", "\n", "        # 将\"PARAM_KEY\"转换为IV，填充为16字节\n", "        iv = PARAM_KEY.ljust(16, '\\0').encode('utf-8')\n", "\n", "        # Base64解码加密数据\n", "        encrypted_bytes = base64.b64decode(encrypted_data)\n", "\n", "        # 创建AES解密器\n", "        cipher = AES.new(key_bytes, AES.MODE_CBC, iv)\n", "\n", "        # 解密数据\n", "        decrypted_bytes = cipher.decrypt(encrypted_bytes)\n", "\n", "        # 去除PKCS5/PKCS7填充\n", "        padding_length = decrypted_bytes[-1]\n", "        if padding_length < 1 or padding_length > 16:\n", "            raise ValueError(\"Invalid padding length\")\n", "        decrypted = decrypted_bytes[:-padding_length].decode('utf-8')\n", "\n", "        return decrypted\n", "    except Exception as e:\n", "        print(\"解密错误:\", e)\n", "        return None\n", "\n", "# 示例用法\n", "encrypted_data = \"Ufhpd/1Y0MVGdayPOg8OzQ==\"  # Java加密后的Base64字符串\n", "secret_key = \"F+1ZmYZzNSFDXqZdpJ3qTxCJbU2qJv7hVzLRlhV9gCg=\" \n", "PARAM_KEY='Jv7hVzLRlhV9gCg=' # Java加密时用的sKey\n", "decrypted_text = decrypt(encrypted_data, secret_key,PARAM_KEY)\n", "\n", "print(\"解密后的文本:\", decrypted_text)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["解密错误: Invalid padding length\n", "解密后的文本: None\n"]}], "source": ["from Crypto.Cipher import AES\n", "from Crypto.Hash import SHA1\n", "import base64\n", "\n", "def decrypt(encrypted_data, secret_key, PARAM_KEY):\n", "    try:\n", "        if not encrypted_data:\n", "            return None\n", "\n", "        # 如果 secret_key 是 Base64 编码的，先解码\n", "        try:\n", "            secret_key = base64.b64decode(secret_key).decode('utf-8')\n", "        except Exception:\n", "            pass  # 如果解码失败，认为它是普通字符串\n", "\n", "        # 使用SHA1生成密钥，与Java的SecureRandom逻辑一致\n", "        sha1 = SHA1.new()\n", "        sha1.update(secret_key.encode('utf-8'))\n", "        key_bytes = sha1.digest()[:16]  # 截取前16字节，作为AES-128密钥\n", "\n", "        # 验证 PARAM_KEY 的长度并填充到16字节\n", "        iv = PARAM_KEY.encode('utf-8')[:16]  # 截取或填充为16字节\n", "        if len(iv) < 16:\n", "            iv = iv.ljust(16, b'\\0')  # 使用空字节填充\n", "\n", "        # Base64解码加密数据\n", "        encrypted_bytes = base64.b64decode(encrypted_data)\n", "\n", "        # 创建AES解密器\n", "        cipher = AES.new(key_bytes, AES.MODE_CBC, iv)\n", "\n", "        # 解密数据\n", "        decrypted_bytes = cipher.decrypt(encrypted_bytes)\n", "\n", "        # 去除PKCS5/PKCS7填充\n", "        padding_length = decrypted_bytes[-1]\n", "        if padding_length < 1 or padding_length > 16:\n", "            raise ValueError(\"Invalid padding length\")\n", "        decrypted = decrypted_bytes[:-padding_length].decode('utf-8')\n", "\n", "        return decrypted\n", "    except Exception as e:\n", "        print(\"解密错误:\", e)\n", "        return None\n", "\n", "\n", "# 示例用法\n", "encrypted_data = \"Ufhpd/1Y0MVGdayPOg8OzQ==\"  # Java加密后的Base64字符串\n", "secret_key = \"F+1ZmYZzNSFDXqZdpJ3qTxCJbU2qJv7hVzLRlhV9gCg=\"  # Base64编码的密钥\n", "PARAM_KEY = \"Jv7hVzLRlhV9gCg=\"  # Java加密时的IV或类似固定值\n", "decrypted_text = decrypt(encrypted_data, secret_key, PARAM_KEY)\n", "\n", "print(\"解密后的文本:\", decrypted_text)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["解密错误详情: Incorrect IV length (it must be 16 bytes long)\n", "密钥长度: 16\n", "IV长度: 11\n", "加密数据长度: 16\n", "解密后的文本: None\n"]}], "source": ["from Crypto.Cipher import AES\n", "from Crypto.Hash import SHA1\n", "import base64\n", "\n", "def decrypt(encrypted_data, secret_key, PARAM_KEY):\n", "    try:\n", "        if not encrypted_data:\n", "            return None\n", "\n", "        # 使用SHA1生成密钥，与Java的SecureRandom逻辑一致\n", "        sha1 = SHA1.new()\n", "        sha1.update(secret_key.encode('utf-8'))\n", "        key_bytes = sha1.digest()[:16]  # 截取前16字节，作为AES-128密钥\n", "\n", "        # 直接使用Base64解码PARAM_KEY作为IV\n", "        iv = base64.b64decode(PARAM_KEY)\n", "\n", "        # Base64解码加密数据\n", "        encrypted_bytes = base64.b64decode(encrypted_data)\n", "\n", "        # 创建AES解密器\n", "        cipher = AES.new(key_bytes, AES.MODE_CBC, iv)\n", "\n", "        # 解密数据\n", "        decrypted_bytes = cipher.decrypt(encrypted_bytes)\n", "\n", "        # 去除PKCS5/PKCS7填充\n", "        padding_length = decrypted_bytes[-1]\n", "        if padding_length < 1 or padding_length > 16:\n", "            raise ValueError(\"Invalid padding length\")\n", "        \n", "        try:\n", "            decrypted = decrypted_bytes[:-padding_length].decode('utf-8')\n", "        except UnicodeDecodeError as e:\n", "            print(\"解码错误，解密后的字节:\", decrypted_bytes.hex())\n", "            raise e\n", "\n", "        return decrypted\n", "    except Exception as e:\n", "        print(\"解密错误详情:\", str(e))\n", "        print(\"密钥长度:\", len(key_bytes))\n", "        print(\"IV长度:\", len(iv))\n", "        print(\"加密数据长度:\", len(encrypted_bytes))\n", "        return None\n", "\n", "# 示例用法\n", "encrypted_data = \"Ufhpd/1Y0MVGdayPOg8OzQ==\"  # Java加密后的Base64字符串\n", "secret_key = \"F+1ZmYZzNSFDXqZdpJ3qTxCJbU2qJv7hVzLRlhV9gCg=\"  # Base64编码的密钥\n", "PARAM_KEY = \"Jv7hVzLRlhV9gCg=\"  # Java加密时的IV或类似固定值\n", "decrypted_text = decrypt(encrypted_data, secret_key, PARAM_KEY)\n", "\n", "print(\"解密后的文本:\", decrypted_text)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始PARAM_KEY (Base64): Jv7hVzLRlhV9gCg=\n", "解码后的IV长度: 11\n", "填充后的IV长度: 16\n", "解密错误详情: Invalid padding length\n", "密钥长度: 16\n", "IV长度: 16\n", "加密数据长度: 16\n", "解密后的文本: None\n"]}], "source": ["from Crypto.Cipher import AES\n", "from Crypto.Hash import SHA1\n", "import base64\n", "\n", "def decrypt(encrypted_data, secret_key, PARAM_KEY):\n", "    try:\n", "        if not encrypted_data:\n", "            return None\n", "\n", "        # 使用SHA1生成密钥，与Java的SecureRandom逻辑一致\n", "        sha1 = SHA1.new()\n", "        sha1.update(secret_key.encode('utf-8'))\n", "        key_bytes = sha1.digest()[:16]  # 截取前16字节，作为AES-128密钥\n", "\n", "        # 解码PARAM_KEY并确保IV长度为16字节\n", "        iv = base64.b64decode(PARAM_KEY)\n", "        if len(iv) < 16:\n", "            iv = iv + b'\\0' * (16 - len(iv))  # 用零字节填充到16字节\n", "        elif len(iv) > 16:\n", "            iv = iv[:16]  # 如果超过16字节，截取前16字节\n", "\n", "        # Base64解码加密数据\n", "        encrypted_bytes = base64.b64decode(encrypted_data)\n", "\n", "        # 创建AES解密器\n", "        cipher = AES.new(key_bytes, AES.MODE_CBC, iv)\n", "\n", "        # 解密数据\n", "        decrypted_bytes = cipher.decrypt(encrypted_bytes)\n", "\n", "        # 去除PKCS5/PKCS7填充\n", "        padding_length = decrypted_bytes[-1]\n", "        if padding_length < 1 or padding_length > 16:\n", "            raise ValueError(\"Invalid padding length\")\n", "        \n", "        try:\n", "            decrypted = decrypted_bytes[:-padding_length].decode('utf-8')\n", "        except UnicodeDecodeError as e:\n", "            print(\"解码错误，解密后的字节:\", decrypted_bytes.hex())\n", "            raise e\n", "\n", "        return decrypted\n", "    except Exception as e:\n", "        print(\"解密错误详情:\", str(e))\n", "        print(\"密钥长度:\", len(key_bytes))\n", "        print(\"IV长度:\", len(iv))\n", "        print(\"加密数据长度:\", len(encrypted_bytes))\n", "        return None\n", "\n", "# 示例用法\n", "encrypted_data = \"Ufhpd/1Y0MVGdayPOg8OzQ==\"  # Java加密后的Base64字符串\n", "secret_key = \"F+1ZmYZzNSFDXqZdpJ3qTxCJbU2qJv7hVzLRlhV9gCg=\"  # Base64编码的密钥\n", "PARAM_KEY = \"Jv7hVzLRlhV9gCg=\"  # Java加密时的IV或类似固定值\n", "\n", "# 打印调试信息\n", "print(\"原始PARAM_KEY (Base64):\", PARAM_KEY)\n", "iv = base64.b64decode(PARAM_KEY)\n", "print(\"解码后的IV长度:\", len(iv))\n", "print(\"填充后的IV长度:\", len(iv + b'\\0' * (16 - len(iv))))\n", "\n", "decrypted_text = decrypt(encrypted_data, secret_key, PARAM_KEY)\n", "print(\"解密后的文本:\", decrypted_text)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["解密后的文本: 1000417\n"]}], "source": ["from Crypto.Cipher import AES\n", "from Crypto.Hash import SHA1\n", "import base64\n", "\n", "def decrypt(encrypted_data, secret_key, PARAM_KEY):\n", "    try:\n", "        if not encrypted_data:\n", "            return None\n", "\n", "        # 使用SHA1生成密钥，与Java的SecureRandom逻辑一致\n", "        sha1 = SHA1.new()\n", "        sha1.update(secret_key.encode('utf-8'))\n", "        key_bytes = sha1.digest()[:16]  # 截取前16字节，作为AES-128密钥\n", "\n", "        # 直接使用PARAM_KEY的UTF-8字节作为IV\n", "        iv = PARAM_KEY.encode('utf-8')\n", "        if len(iv) < 16:\n", "            iv = iv + b'\\0' * (16 - len(iv))  # 用零字节填充到16字节\n", "        elif len(iv) > 16:\n", "            iv = iv[:16]  # 如果超过16字节，截取前16字节\n", "\n", "        # Base64解码加密数据\n", "        encrypted_bytes = base64.b64decode(encrypted_data)\n", "\n", "        # 创建AES解密器\n", "        cipher = AES.new(key_bytes, AES.MODE_CBC, iv)\n", "\n", "        # 解密数据\n", "        decrypted_bytes = cipher.decrypt(encrypted_bytes)\n", "\n", "        # 去除PKCS5/PKCS7填充\n", "        padding_length = decrypted_bytes[-1]\n", "        if padding_length < 1 or padding_length > 16:\n", "            raise ValueError(\"Invalid padding length\")\n", "        \n", "        try:\n", "            decrypted = decrypted_bytes[:-padding_length].decode('utf-8')\n", "        except UnicodeDecodeError as e:\n", "            print(\"解码错误，解密后的字节:\", decrypted_bytes.hex())\n", "            raise e\n", "\n", "        return decrypted\n", "    except Exception as e:\n", "        print(\"解密错误详情:\", str(e))\n", "        print(\"密钥长度:\", len(key_bytes))\n", "        print(\"IV长度:\", len(iv))\n", "        print(\"加密数据长度:\", len(encrypted_bytes))\n", "        return None\n", "\n", "# 示例用法\n", "encrypted_data = \"U8ZC48i5f5hV7qJ/b4Onag==\"  # Java加密后的Base64字符串\n", "secret_key = \"F+1ZmYZzNSFDXqZdpJ3qTxCJbU2qJv7hVzLRlhV9gCg=\"  # Base64编码的密钥\n", "PARAM_KEY = \"Jv7hVzLRlhV9gCg=\"  # Java加密时的IV或类似固定值\n", "\n", "decrypted_text = decrypt(encrypted_data, secret_key, PARAM_KEY)\n", "print(\"解密后的文本:\", decrypted_text)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["使用的密钥(hex): b56433091abb945896f527bf90e731c9\n", "使用的IV(hex): 4a763768567a4c526c6856396743673d\n", "加密数据(hex): 51f86977fd58d0c54675ac8f3a0f0ecd\n", "解密后的字节(hex): a229097015098f4752887ed8b8805c90\n", "解密错误详情: Invalid padding length\n", "密钥长度: 16\n", "IV长度: 16\n", "加密数据长度: 16\n", "解密后的文本: None\n"]}], "source": ["from Crypto.Cipher import AES\n", "from Crypto.Hash import SHA1\n", "import base64\n", "\n", "def decrypt(encrypted_data, secret_key, PARAM_KEY):\n", "    try:\n", "        if not encrypted_data:\n", "            return None\n", "\n", "        # 尝试先对secret_key进行Base64解码\n", "        try:\n", "            decoded_key = base64.b64decode(secret_key)\n", "            secret_key = decoded_key.decode('utf-8')\n", "        except:\n", "            pass  # 如果解码失败，使用原始secret_key\n", "\n", "        # 使用SHA1生成密钥，与Java的SecureRandom逻辑一致\n", "        sha1 = SHA1.new()\n", "        sha1.update(secret_key.encode('utf-8'))\n", "        key_bytes = sha1.digest()[:16]  # 截取前16字节，作为AES-128密钥\n", "\n", "        # 直接使用PARAM_KEY的UTF-8字节作为IV\n", "        iv = PARAM_KEY.encode('utf-8')\n", "        if len(iv) < 16:\n", "            iv = iv + b'\\0' * (16 - len(iv))  # 用零字节填充到16字节\n", "        elif len(iv) > 16:\n", "            iv = iv[:16]  # 如果超过16字节，截取前16字节\n", "\n", "        # Base64解码加密数据\n", "        encrypted_bytes = base64.b64decode(encrypted_data)\n", "\n", "        # 打印调试信息\n", "        print(\"使用的密钥(hex):\", key_bytes.hex())\n", "        print(\"使用的IV(hex):\", iv.hex())\n", "        print(\"加密数据(hex):\", encrypted_bytes.hex())\n", "\n", "        # 创建AES解密器\n", "        cipher = AES.new(key_bytes, AES.MODE_CBC, iv)\n", "\n", "        # 解密数据\n", "        decrypted_bytes = cipher.decrypt(encrypted_bytes)\n", "        \n", "        # 打印解密后的原始字节\n", "        print(\"解密后的字节(hex):\", decrypted_bytes.hex())\n", "\n", "        # 去除PKCS5/PKCS7填充\n", "        padding_length = decrypted_bytes[-1]\n", "        if not (1 <= padding_length <= 16):\n", "            # 如果填充长度无效，尝试不去除填充直接解码\n", "            try:\n", "                return decrypted_bytes.decode('utf-8').strip()\n", "            except:\n", "                raise ValueError(\"Invalid padding length\")\n", "        \n", "        decrypted = decrypted_bytes[:-padding_length].decode('utf-8')\n", "        return decrypted\n", "\n", "    except Exception as e:\n", "        print(\"解密错误详情:\", str(e))\n", "        print(\"密钥长度:\", len(key_bytes))\n", "        print(\"IV长度:\", len(iv))\n", "        print(\"加密数据长度:\", len(encrypted_bytes))\n", "        return None\n", "\n", "# 示例用法\n", "encrypted_data = \"Ufhpd/1Y0MVGdayPOg8OzQ==\"  # Java加密后的Base64字符串\n", "secret_key = \"F+1ZmYZzNSFDXqZdpJ3qTxCJbU2qJv7hVzLRlhV9gCg=\"  # Base64编码的密钥\n", "PARAM_KEY = \"Jv7hVzLRlhV9gCg=\"  # Java加密时使用的IV\n", "\n", "decrypted_text = decrypt(encrypted_data, secret_key, PARAM_KEY)\n", "print(\"解密后的文本:\", decrypted_text)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["from Crypto.Cipher import AES\n", "from Crypto.Hash import SHA1\n", "import base64\n", "\n", "def encrypt(src: str, sKey: str, PARAM_KEY: str) -> str:\n", "    \"\"\"\n", "    AES加密方法\n", "    \n", "    Args:\n", "        src: 要加密的原文\n", "        sKey: 密钥\n", "        PARAM_KEY: IV参数\n", "    \n", "    Returns:\n", "        加密后的Base64字符串\n", "    \"\"\"\n", "    try:\n", "        if not src:\n", "            return None\n", "            \n", "        # 使用SHA1生成密钥，与Java的SecureRandom逻辑一致\n", "        sha1 = SHA1.new()\n", "        sha1.update(sKey.encode('utf-8'))\n", "        key_bytes = sha1.digest()[:16]  # 截取前16字节，作为AES-128密钥\n", "        \n", "        # 处理IV，与Java代码保持一致\n", "        iv = PARAM_KEY.encode('utf-8')\n", "        if len(iv) < 16:\n", "            iv = iv + b'\\0' * (16 - len(iv))  # 用零字节填充到16字节\n", "        elif len(iv) > 16:\n", "            iv = iv[:16]  # 如果超过16字节，截取前16字节\n", "            \n", "        # 创建AES加密器，使用CBC模式和PKCS7填充\n", "        cipher = AES.new(key_bytes, AES.MODE_CBC, iv)\n", "        \n", "        # 对原文进行PKCS7填充\n", "        block_size = 16\n", "        padding_length = block_size - (len(src.encode('utf-8')) % block_size)\n", "        padding = bytes([padding_length] * padding_length)\n", "        \n", "        # 加密数据\n", "        encrypted_bytes = cipher.encrypt(src.encode('utf-8') + padding)\n", "        \n", "        # Base64编码\n", "        encrypted_str = base64.b64encode(encrypted_bytes).decode('utf-8')\n", "        \n", "        return encrypted_str\n", "        \n", "    except Exception as e:\n", "        print(\"加密错误:\", str(e))\n", "        return None\n", "\n", "def decrypt(encrypted_data: str, secret_key: str, PARAM_KEY: str) -> str:\n", "    \"\"\"\n", "    AES解密方法\n", "    \n", "    Args:\n", "        encrypted_data: Base64编码的加密数据\n", "        secret_key: 密钥\n", "        PARAM_KEY: IV参数\n", "    \n", "    Returns:\n", "        解密后的字符串\n", "    \"\"\"\n", "    try:\n", "        if not encrypted_data:\n", "            return None\n", "\n", "        # 使用SHA1生成密钥\n", "        sha1 = SHA1.new()\n", "        sha1.update(secret_key.encode('utf-8'))\n", "        key_bytes = sha1.digest()[:16]\n", "\n", "        # 处理IV\n", "        iv = PARAM_KEY.encode('utf-8')\n", "        if len(iv) < 16:\n", "            iv = iv + b'\\0' * (16 - len(iv))\n", "        elif len(iv) > 16:\n", "            iv = iv[:16]\n", "\n", "        # Base64解码\n", "        encrypted_bytes = base64.b64decode(encrypted_data)\n", "\n", "        # 创建AES解密器\n", "        cipher = AES.new(key_bytes, AES.MODE_CBC, iv)\n", "\n", "        # 解密数据\n", "        decrypted_bytes = cipher.decrypt(encrypted_bytes)\n", "\n", "        # 去除PKCS7填充\n", "        padding_length = decrypted_bytes[-1]\n", "        if not (1 <= padding_length <= 16):\n", "            raise ValueError(\"Invalid padding length\")\n", "        \n", "        decrypted = decrypted_bytes[:-padding_length].decode('utf-8')\n", "        return decrypted\n", "\n", "    except Exception as e:\n", "        print(\"解密错误:\", str(e))\n", "        return None\n", "\n", "# 测试代码\n", "# if __name__ == \"__main__\":\n", "    # 测试参数\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["加密后的文本: U8ZC48i5f5hV7qJ/b4Onag==\n", "解密后的文本: 1000417\n", "加解密是否成功: True\n"]}], "source": ["\n", "original_text = \"1000417\"\n", "sKey = \"F+1ZmYZzNSFDXqZdpJ3qTxCJbU2qJv7hVzLRlhV9gCg=\"\n", "PARAM_KEY = \"Jv7hVzLRlhV9gCg=\"\n", "    \n", "    # 加密\n", "encrypted = encrypt(original_text, sKey, PARAM_KEY)\n", "print(\"加密后的文本:\", encrypted)\n", "    \n", "    # 解密\n", "decrypted = decrypt(encrypted, sKey, PARAM_KEY)\n", "print(\"解密后的文本:\", decrypted)\n", "    \n", "# 验证\n", "print(\"加解密是否成功:\", original_text == decrypted)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "wisechat", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}