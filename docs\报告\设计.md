```mermaid
graph LR
    subgraph Workflows
        A[Prompt Chaining] --> B[LLM Call] --> C[Output]
        D[Parallelization] --> E[Output]
        D --> F[Output]
        G[Orchestrator-Worker] --> H[Orchestrator] --> I[Workers] --> <PERSON>[Synthesizer] --> K[Output]
        L[Evaluator-optimizer] --> M[Generator] --> N[Evaluator] --> O[Output]
        M --> N
        P[Routing] --> Q[Router] --> R[Output]
        P --> S[Router] --> T[Output]
    end
    
    subgraph Agent
        U[LLM Call] --> V[Action] --> W[Tool] --> X[Output]
        W -->|Feedback| U
    end