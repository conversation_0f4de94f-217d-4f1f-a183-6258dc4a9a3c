from pydantic import BaseModel, Field
from typing import Optional, Literal
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Enum
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class Authorization(Base):
    __tablename__ = 'authorizations'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=False)
    user_name = Column(String, nullable=False)
    m_id = Column(Integer, nullable=False) # 模型id
    m_name = Column(String, nullable=False) # 模型名称
    expiration_date = Column(DateTime, nullable=False)
    authorization_type = Column(Enum('llm', 'embedding'), nullable=False)
    created_by = Column(Integer, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<Authorization(user_name={self.user_name}, m_name={self.m_name})>"

class AuthorizationCreate(BaseModel):
    user_id: int
    user_name: str
    m_id: int
    m_name: str
    expiration_date: datetime
    authorization_type: Literal['llm', 'embedding']

    class Config:
        from_attributes = True

class AuthorizationUpdate(BaseModel):
    expiration_date: Optional[str] = None

    class Config:
        from_attributes = True

class AuthorizationResponse(BaseModel):
    id: int
    user_id: int
    user_name: str
    m_id: int
    m_name: str
    expiration_date: datetime
    authorization_type: str
    created_by: int
    created_at: datetime

    class Config:
        from_attributes = True