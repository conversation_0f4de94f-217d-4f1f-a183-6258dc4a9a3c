"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8956],{38820:function(e,n,t){t.r(n),t.d(n,{default:function(){return je}});var r=t(9783),s=t.n(r),a=t(97857),c=t.n(a),o=t(15009),i=t.n(o),l=t(64599),u=t.n(l),d=t(19632),p=t.n(d),f=t(99289),x=t.n(f),m=t(5574),h=t.n(m),g=t(84901),v=t(11488),y=t(93461),k=t(34114),b=t(78205),j=t(78919),w=t(4628),Z=t(9502),_=t(37864),C=t(42075),S=t(71471),P=t(83622),q=t(2453),D=t(17788),R=t(74330),z=t(86250),T=t(66309),F=t(83062),O=t(55102),N=t(85265),Y=t(67294),I=t(10048),M=t(10981),E=t(78404),H=t(40110),A=t(56299),L=t(20280),W=t(66073),B=t(14079),J=t(51042),G=t(82061),K=t(64029),X=t(34804),U=t(47389),$=t(87784),Q=t(25820),V=t(75750),ee=t(12906),ne=t(85175),te=t(43471),re=t(27484),se=t.n(re),ae=(0,t(24444).kc)((function(e){var n=e.token;return{reference:{background:"".concat(n.colorBgLayout,"80"),minWidth:"400px",maxWidth:"720px",width:"100%"},layout:{width:"100%",minWidth:"800px",borderRadius:n.borderRadius,display:"flex",background:n.colorBgContainer,fontFamily:"AlibabaPuHuiTi, ".concat(n.fontFamily,", sans-serif"),".ant-prompts":{color:n.colorText},border:"3px solid",borderImage:"linear-gradient(45deg, ".concat(n.colorPrimary,", ").concat(n.colorSplit,") 1")},menu:{height:"100%",display:"flex",flexDirection:"column"},conversations:{padding:"0 12px",flex:1,overflowY:"auto"},chat:{height:"100%",width:"100%",maxWidth:"900px",margin:"0 auto",boxSizing:"border-box",display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column",padding:n.paddingLG,gap:"16px"},messages:{flex:1},placeholder:{paddingTop:"32px"},sender:{boxShadow:n.boxShadow},logo:{display:"flex",height:"72px",alignItems:"center",justifyContent:"start",padding:"0 24px",boxSizing:"border-box",img:{width:"24px",height:"24px",display:"inline-block"},span:{display:"inline-block",margin:"0 8px",fontWeight:"bold",color:n.colorText,fontSize:"16px"}},addBtn:{background:"#1677ff0f",border:"1px solid #1677ff34",width:"calc(100% - 24px)",margin:"0 12px 24px 12px"},wiseAnswer:{display:"block",padding:"12px",background:"#e6f7ff",borderRadius:"8px",borderLeft:"4px solid #1890ff",margin:"8px 0"}}})),ce=t(93933),oe=t(13973),ie=t(797),le=t(85893);function ue(e){return e+"-"+Date.now()}var de=function(e,n){return(0,le.jsxs)(C.Z,{align:"start",children:[e,(0,le.jsx)("span",{children:n})]})},pe=[{key:"1",label:de((0,le.jsx)(H.Z,{style:{color:"#1890FF"}}),"查找特定投诉类型"),description:"快速定位特定类型投诉",children:[{key:"1-1",description:"如何查找金融产品误导销售类投诉？"},{key:"1-2",description:"如何筛选出理财产品收益争议投诉？"},{key:"1-3",description:"如何查询保险理赔纠纷投诉？"}]},{key:"2",label:de((0,le.jsx)(A.Z,{style:{color:"#13C2C2"}}),"投诉统计分析"),description:"投诉数据多维分析",children:[{key:"2-1",description:"近三个月投诉量最高的产品类型是什么？"},{key:"2-2",description:"不同投诉类型的解决率统计如何？"},{key:"2-3",description:"投诉处理平均时长是多少？"}]},{key:"3",label:de((0,le.jsx)(L.Z,{style:{color:"#FA8C16"}}),"被投诉产品查询"),description:"问题产品追踪分析",children:[{key:"3-1",description:"哪些理财产品投诉量最高？"},{key:"3-2",description:"哪类保险产品客户满意度最低？"},{key:"3-3",description:"哪些银行卡产品收费投诉最多？"}]},{key:"4",label:de((0,le.jsx)(W.Z,{style:{color:"#52C41A"}}),"投诉处理指引"),description:"投诉处理流程与规范",children:[{key:"4-1",description:"投诉材料提交规范指引"},{key:"4-2",description:"投诉处理进度查询方法"},{key:"4-3",description:"投诉结果异议申诉流程"}]}],fe=[{key:"historyConversation",description:"历史对话",icon:(0,le.jsx)(B.Z,{style:{color:"#FF4D4F"}})},{key:"newConversation",description:"新建对话",icon:(0,le.jsx)(J.Z,{style:{color:"#1890FF"}})},{key:"clearConversation",description:"清空对话",icon:(0,le.jsx)(G.Z,{style:{color:"#1890FF"}})}],xe=(0,M.bG)(),me=(0,E.kH)(),he=(0,I.Z)({html:!0,breaks:!0}),ge=function(e){return(0,le.jsx)(S.Z,{style:{marginBottom:0},children:(0,le.jsx)("div",{dangerouslySetInnerHTML:{__html:he.render(e)}})})},ve="ConsumerProtection",ye=function(e){var n=e.content,t=(0,Y.useState)(!1),r=h()(t,2),s=r[0],a=r[1],c=function(e){var n=[];return{processedContent:e.replace(/<think>([\s\S]*?)<\/think>/g,(function(e,t){return n.push(t.trim()),""})).trim(),thinkBlocks:n}}(n),o=c.processedContent,i=c.thinkBlocks;return(0,le.jsxs)("div",{style:{position:"relative"},children:[ge(o),i.length>0&&(0,le.jsxs)("div",{style:{marginTop:8},children:[(0,le.jsx)(P.ZP,{type:"text",size:"small",icon:s?(0,le.jsx)(K.Z,{}):(0,le.jsx)(X.Z,{}),onClick:function(){return a(!s)},style:{color:"#666",fontSize:12,padding:0,height:"auto",background:"transparent"},children:(0,le.jsxs)("span",{style:{marginLeft:4},children:[s?"收起":"展开","思考过程 (",i.length,")"]})}),(0,le.jsx)("div",{style:{maxHeight:s?"1000px":"0",overflow:"hidden",transition:"max-height 0.3s ease-out",background:"#f9f9f9",borderRadius:8,marginTop:4},children:i.map((function(e,n){return(0,le.jsx)("pre",{style:{whiteSpace:"pre-wrap",margin:0,padding:12,fontFamily:"monospace",fontSize:12},children:e},n)}))})]})]})},ke=function(e){return(0,le.jsx)(ye,{content:e})},be=function(e){var n=e.data,t=(0,Y.useRef)(null);return(0,Y.useEffect)((function(){if(t.current){var e=ie.S1(t.current),r={title:{text:Object.keys(n[0])[1]},tooltip:{},xAxis:{type:"category",axisLabel:{rotate:45},data:n.map((function(e){return e[Object.keys(e)[0]]}))},yAxis:{type:"value"},series:[{type:"bar",data:n.map((function(e){return e[Object.keys(e)[1]]}))}]};e.setOption(r)}}),[n]),(0,le.jsx)("div",{ref:t,style:{minWidth:"300px",height:"400px"}})},je=function(){var e,n=ae().styles,t=(0,Y.useState)(window.innerHeight),r=h()(t,1)[0],a=Y.useRef(),o=Y.useState(!1),l=h()(o,2),d=l[0],f=l[1],m=Y.useState(""),S=h()(m,2),I=S[0],E=S[1],H=Y.useState([]),A=h()(H,2),L=A[0],W=A[1],B=Y.useState(),K=h()(B,2),X=K[0],re=K[1],ie=(0,Y.useState)(!1),de=h()(ie,2),he=de[0],ge=de[1],ye=(0,Y.useState)(!1),je=h()(ye,2),we=je[0],Ze=je[1],_e=(0,Y.useState)(!1),Ce=h()(_e,2),Se=Ce[0],Pe=Ce[1],qe=(0,Y.useState)(""),De=h()(qe,2),Re=De[0],ze=De[1],Te=(0,Y.useState)(""),Fe=h()(Te,2),Oe=Fe[0],Ne=Fe[1],Ye=(0,Y.useState)([]),Ie=h()(Ye,2),Me=Ie[0],Ee=Ie[1],He=(0,Y.useRef)(null),Ae=(0,Y.useState)(!1),Le=h()(Ae,2),We=Le[0],Be=Le[1],Je=(0,Y.useState)(""),Ge=h()(Je,2),Ke=Ge[0],Xe=Ge[1],Ue=(0,Y.useState)([]),$e=h()(Ue,2),Qe=($e[0],$e[1]),Ve=(0,Y.useState)({}),en=h()(Ve,2),nn=en[0],tn=en[1],rn=((0,Y.useRef)(null),function(e){Xe(e),Be(!0)}),sn=function(e){var n=Me.find((function(n){return n.message_id===e}));n&&navigator.clipboard.writeText(n.content).then((function(){q.ZP.success("复制成功")})).catch((function(){q.ZP.error("复制失败")}))},an=(0,y.Z)({request:(e=x()(i()().mark((function e(n,t){var r,s,c,o,l,d,f,x,m,h,g,y,k,b,j,w,Z,_,C,S,P,D,R,z,T,F,O,N,Y,I,E,H,A,L,W,B,J,G;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.messages,s=n.message,c=t.onSuccess,o=t.onUpdate,l=t.onError,e.prev=2,!we){e.next=6;break}return q.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 6:if(Ze(!0),f=(0,M.bW)(),x=s?s.id:ue(a.current),s||c({content:"出现了异常:",role:"assistant",id:x,references:[],query:[],collected:!1}),m={conversation_id:a.current||"",message_id:x,meta_data:{},extra:{},role:s?s.role:"user",content:s?s.content:"",app_info:ve,user_id:null==xe?void 0:xe.id,user_name:null==xe?void 0:xe.name,references:[],query:[],token_count:null,price:null,collected:!1,created_at:se()().format("YYYY-MM-DD HH:mm:ss")},Ee((function(e){var n=[].concat(p()(e),[m]);return console.log("更新后的消息列表:",n),n})),a.current){e.next=15;break}throw q.ZP.error("No active conversation selected"),new Error("No active conversation selected");case 15:return console.log("activeKey===>",a.current),h={conversation_id:a.current,app_info:ve,user_id:null==xe?void 0:xe.id,user_name:null==xe?void 0:xe.name,extra:{},messages:r},g={id:ue(a.current),role:"assistant",content:"",references:[],query:[],collected:!1},y=!1,k="",b=[],j=[],o(g),e.next=25,fetch("/api/app/chat/system_app_completions",{method:"POST",headers:{Accept:"text/event-stream","Content-Type":"application/json",Authorization:"Bearer ".concat(f)},body:JSON.stringify(h)});case 25:if((w=e.sent).ok){e.next=28;break}throw new Error("HTTP 错误！状态码：".concat(w.status));case 28:if(Z=null===(d=w.body)||void 0===d?void 0:d.getReader()){e.next=31;break}throw new Error("当前浏览器不支持 ReadableStream。");case 31:_=new TextDecoder("utf-8"),C={conversation_id:a.current||"",message_id:g.id,meta_data:{},extra:{},role:"assistant",content:"",app_info:ve,user_id:null==xe?void 0:xe.id,user_name:null==xe?void 0:xe.name,references:[],query:[],token_count:null,price:null,collected:!1,created_at:se()().format("YYYY-MM-DD HH:mm:ss")};case 33:if(y){e.next=113;break}return e.next=36,Z.read();case 36:S=e.sent,P=S.value,S.done&&(y=!0),k+=_.decode(P,{stream:!0}),D=k.split("\n\n"),k=D.pop()||"",R=u()(D),e.prev=44,R.s();case 46:if((z=R.n()).done){e.next=103;break}if(""!==(T=z.value).trim()){e.next=50;break}return e.abrupt("continue",101);case 50:F=T.split("\n"),O=null,N=null,Y=u()(F);try{for(Y.s();!(I=Y.n()).done;)(E=I.value).startsWith("event: ")?O=E.substring(7).trim():E.startsWith("data: ")&&(N=E.substring(6))}catch(e){Y.e(e)}finally{Y.f()}if(!N){e.next=101;break}e.t0=O,e.next="answer"===e.t0?59:"query"===e.t0?71:"modulxeStatus"===e.t0?80:"appStreamResponse"===e.t0?82:"flowResponses"===e.t0?84:"end"===e.t0?86:"error"===e.t0?88:101;break;case 59:if("[DONE]"===N){e.next=70;break}e.prev=60,(H=JSON.parse(N)).status||(H.stream?g.content+=JSON.parse(H.stream).chat_chunk:H.flow||H.error&&(g.content+=H.error)),o(g),e.next=70;break;case 66:return e.prev=66,e.t1=e.catch(60),console.error("Error parsing answer data:",e.t1),e.abrupt("return",c({content:"出现了异常:"+N,role:"assistant",id:ue(a.current),references:[],query:[],collected:!1}));case 70:return e.abrupt("break",101);case 71:return A=JSON.parse(N),console.log("echarts图表===>",A),L=A.query.queryResults,console.log("🚀 ~ request: ~ queryResults:",L),Qe(L),j=L,console.log("🚀 ~ request: ~ queryList:",j),g.query=j,e.abrupt("break",101);case 80:try{W=JSON.parse(N),console.log("模块状态：",W)}catch(e){console.error("Error parsing moduleStatus data:",e)}return e.abrupt("break",101);case 82:try{B=JSON.parse(N),console.log("appStreamData===>",B),b=(0,v.n)(B),g.references=b}catch(e){console.error("Error parsing appStreamResponse data:",e)}return e.abrupt("break",101);case 84:try{console.log("flowResponsesData",N)}catch(e){console.error("Error parsing flowResponses data:",e)}return e.abrupt("break",101);case 86:return y=!0,e.abrupt("break",101);case 88:e.prev=88,y=!0,J=JSON.parse(N),g.content=J.message,C.role="assistant",o(g),e.next=100;break;case 96:throw e.prev=96,e.t2=e.catch(88),console.error("Error event received:",e.t2),e.t2;case 100:return e.abrupt("break",101);case 101:e.next=46;break;case 103:e.next=108;break;case 105:e.prev=105,e.t3=e.catch(44),R.e(e.t3);case 108:return e.prev=108,R.f(),e.finish(108);case 111:e.next=33;break;case 113:if(console.info(g),c(g),!g.content||""===g.content.trim()){e.next=123;break}return C.content=g.content,C.references=b,C.query=j,e.next=121,(0,ce.tn)(C);case 121:(G=e.sent).success?(C.message_id=G.data.message_id,console.log("创建消息成功，返回数据:",G.data),Ee((function(e){var n=[].concat(p()(e),[G.data]);return console.log("更新后的消息列表:",n),n}))):q.ZP.error("消息上报失败");case 123:e.next=130;break;case 125:e.prev=125,e.t4=e.catch(2),console.log("error===>",e.t4),c({content:"出现了，系统正在处理其他对话。请稍后重试",role:"assistant",id:ue(a.current),references:[],query:[],collected:!1}),l(e.t4 instanceof Error?e.t4:new Error("Unknown error"));case 130:return e.prev=130,Ze(!1),e.finish(130);case 133:case"end":return e.stop()}}),e,null,[[2,125,130,133],[44,105,108,111],[60,66],[88,96]])}))),function(n,t){return e.apply(this,arguments)})}),cn=h()(an,1)[0],on=(0,k.Z)({agent:cn}),ln=on.onRequest,un=on.messages,dn=on.setMessages,pn=function(e){re(e),console.log("activeKey 设置",e),a.current=e},fn=function(e){var n=e.filter((function(e){return e.pinned})).sort((function(e,n){return new Date(n.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return c()(c()({},e),{},{key:e.id||"",label:e.conversation_name||e.id,group:"置顶"})})),t=e.filter((function(e){return!e.pinned})).sort((function(e,n){return new Date(n.active_at||"").getTime()-new Date(e.active_at||"").getTime()})).map((function(e){return c()(c()({},e),{},{key:e.id||"",label:e.conversation_name||"",group:"对话"})}));W([].concat(p()(n),p()(t)))},xn=function(e){return 0===e.length?null:e.reduce((function(e,n){return new Date(n.active_at)>new Date(e.active_at)?n:e}))},mn=function(){var e=x()(i()().mark((function e(n){var t,r,s;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,Ze(!0),console.info("获取对话信息",n),t=se()().format("YYYY-MM-DD HH:mm:ss"),e.next=6,(0,ce.$o)(n,{conversation_name:null,active_at:t,pinned_at:null,pinned:null});case 6:null!=(r=e.sent)&&r.messages?(console.info("设置对话信息",r.messages),s=r.messages.map((function(e){return{id:e.id||e.message_id,message:{id:e.id||e.message_id,content:e.content,role:e.role,references:e.references||[],query:e.query||[],collected:e.collected||!1},status:"assistant"===e.role?"success":"local",meta:e.meta||{avatar:"assistant"===e.role?(null==me?void 0:me.logo)||"/static/logo.png":(null==xe?void 0:xe.avatar)||"/avatar/default.jpeg"}}})),Ee(r.messages),dn(s),pn(n)):q.ZP.error("获取对话信息失败"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("切换对话时出错：",e.t0);case 13:return e.prev=13,Ze(!1),re(n),e.finish(13);case 17:case"end":return e.stop()}}),e,null,[[0,10,13,17]])})));return function(n){return e.apply(this,arguments)}}(),hn=function(){var e=x()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a.current){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,(0,ce.Db)(a.current);case 4:e.sent.success?(Ee([]),dn([]),He.current&&He.current.updateReferenceList([])):q.ZP.error("清空对话失败");case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),gn=function(){var e=x()(i()().mark((function e(){var n,t,r,s;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!we){e.next=3;break}return q.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(!(n=(0,M.bG)())){e.next=19;break}return e.prev=5,t=(new Date).toLocaleString(),r="对话-".concat(t),e.next=10,(0,ce.Xw)({user_id:n.id,user_name:n.name,conversation_name:r,app_info:ve});case 10:s=e.sent,fn([].concat(p()(L),[{key:s.id||"",id:s.id||"",label:s.conversation_name||"",conversation_name:s.conversation_name||"",active_at:s.active_at||"",pinned_at:s.pinned_at,pinned:s.pinned||!1,messages:[]}])),pn(s.id||""),hn(),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(5),console.error("创建新对话时出错：",e.t0);case 19:case"end":return e.stop()}}),e,null,[[5,16]])})));return function(){return e.apply(this,arguments)}}(),vn=function(){var e=x()(i()().mark((function e(n){var t,r,s,a,o;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=L.find((function(e){return e.key===n}))){e.next=3;break}return e.abrupt("return");case 3:return r=t.pinned,s=!r,e.prev=6,a=se()().format("YYYY-MM-DD HH:mm:ss"),e.next=10,(0,ce.X1)(n,{conversation_name:null,active_at:null,pinned:s,pinned_at:a});case 10:o=L.map((function(e){return e.key===n?c()(c()({},e),{},{pinned:s}):e})),fn(o),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(6),console.error("更新置顶状态时出错：",e.t0);case 17:case"end":return e.stop()}}),e,null,[[6,14]])})));return function(n){return e.apply(this,arguments)}}(),yn=function(){var e=x()(i()().mark((function e(n){var t;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,ce.SJ)(n);case 3:t=L.filter((function(e){return e.key!==n})),fn(t),a.current===n&&t.length>0&&mn(t[0].key||""),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除对话时出错：",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(n){return e.apply(this,arguments)}}(),kn=function(){var e=x()(i()().mark((function e(n,t){var r,s;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,L.find((function(e){return e.key===n}))){e.next=4;break}return e.abrupt("return");case 4:return r={conversation_name:t,active_at:null,pinned_at:null,pinned:null},e.next=7,(0,ce.X1)(n,r);case 7:null!=(s=e.sent)&&s.success?W((function(e){return e.map((function(e){return e.key===n?c()(c()({},e),{},{label:t}):e}))})):q.ZP.error("更新对话标题失败"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("更新对话标题时出错：",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(n,t){return e.apply(this,arguments)}}(),bn=function(){var e=x()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!we){e.next=3;break}return q.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:return e.next=5,mn(n);case 5:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}();(0,Y.useEffect)((function(){var e=function(){var e=x()(i()().mark((function e(){var n,t,r;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(n=(0,M.bG)())){e.next=30;break}return e.prev=2,Ze(!0),e.next=6,(0,ce.Mw)({user_id:n.id,app_info:ve});case 6:if(!(t=e.sent).success||!Array.isArray(t.data)){e.next=22;break}if(0!==t.data.length){e.next=13;break}return e.next=11,gn();case 11:e.next=22;break;case 13:if(r=xn(t.data),fn(t.data),!r){e.next=20;break}return e.next=18,mn(r.id);case 18:e.next=22;break;case 20:return e.next=22,mn(t.data[0].id||"");case 22:e.next=27;break;case 24:e.prev=24,e.t0=e.catch(2),console.error("初始化对话时出错：",e.t0);case 27:return e.prev=27,Ze(!1),e.finish(27);case 30:case"end":return e.stop()}}),e,null,[[2,24,27,30]])})));return function(){return e.apply(this,arguments)}}();e()}),[ve]);var jn=function(){var e=x()(i()().mark((function e(n){var t,r,s,a;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("重新生成消息:",n),e.prev=1,t=Me.findIndex((function(e){return e.message_id===n})),console.log("currentIndex===>",t),-1!==t){e.next=7;break}return q.ZP.error("未找到指定消息"),e.abrupt("return");case 7:return r=Me[t],s=Me.slice(t),console.log("将要删除的消息:",s),e.next=12,(0,ce.qP)(s.map((function(e){return e.message_id})));case 12:e.sent.success||q.ZP.error("删除消息失败"),Ee((function(e){return e.slice(0,t)})),dn((function(e){return e.slice(0,t)})),"assistant"===r.role?(a=Me.slice(0,t).reverse().find((function(e){return"user"===e.role})))&&ln({id:n,role:"user",content:a.content,references:[],query:[],collected:!1}):ln({id:n,role:"user",content:r.content,references:[],query:[],collected:!1}),q.ZP.success("正在重新生成回复..."),e.next=24;break;case 20:e.prev=20,e.t0=e.catch(1),console.error("重新生成消息时出错：",e.t0),q.ZP.error("重新生成消息失败");case 24:case"end":return e.stop()}}),e,null,[[1,20]])})));return function(n){return e.apply(this,arguments)}}(),wn=function(){var e=x()(i()().mark((function e(n){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:D.Z.confirm({title:"确认删除",content:"确定要删除这条消息吗？删除后不可恢复。",okText:"确认",cancelText:"取消",onOk:function(){return x()(i()().mark((function e(){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("delete messageId===>",n),e.next=4,(0,ce.$Z)(n);case 4:e.sent.success?(Ee((function(e){return e.filter((function(e){return e.message_id!==n}))})),console.log("delete currentConversationMessages===>",Me),dn((function(e){return e.filter((function(e){return e.message.id!==n}))})),q.ZP.success("消息及相关引用已删除")):q.ZP.error("删除消息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("删除消息时出错：",e.t0),q.ZP.error("删除消息失败");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})))()}});case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Zn=function(){var e=x()(i()().mark((function e(n,t){return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("收藏状态切换:",n,t),e.next=3,(0,ce.bk)({message_id:n,collected:!t});case 3:e.sent.success?(q.ZP.success(t?"取消收藏成功":"收藏成功"),dn((function(e){return e.map((function(e){return e.id===n?c()(c()({},e),{},{message:c()(c()({},e.message),{},{collected:!t})}):e}))})),Ee((function(e){return e.map((function(e){return e.message_id===n?c()(c()({},e),{},{collected:!t}):e}))}))):q.ZP.error(t?"取消收藏失败":"收藏失败");case 5:case"end":return e.stop()}}),e)})));return function(n,t){return e.apply(this,arguments)}}(),_n=function(){var e=x()(i()().mark((function e(n){var t,r,s;return i()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!we){e.next=3;break}return q.ZP.error("系统正在处理其他对话。请稍😊"),e.abrupt("return");case 3:if(t=n.data,r=t.key,s=t.description,"historyConversation"!==r){e.next=8;break}ge(!0),e.next=19;break;case 8:if("newConversation"!==r){e.next=13;break}return e.next=11,gn();case 11:e.next=19;break;case 13:if("clearConversation"!==r){e.next=18;break}return e.next=16,hn();case 16:e.next=19;break;case 18:ln({id:ue(a.current),role:"user",content:s,references:[],query:[],collected:!1});case 19:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Cn=(0,le.jsxs)(C.Z,{direction:"vertical",size:16,className:n.placeholder,children:[(0,le.jsx)(b.Z,{variant:"borderless",icon:(0,le.jsx)("img",{src:(null==me?void 0:me.logo)||"/static/logo.png",alt:"logo"}),title:"你好，我是金融消保投诉助手,",description:"基于智能分析和监管要求，为您提供专业的投诉问题诊断、法律依据查询和处置建议"}),(0,le.jsx)(j.Z,{title:"您需要咨询哪些投诉相关问题？",items:pe,styles:{list:{width:"100%"},item:{backgroundImage:"linear-gradient(137deg, #e5f4ff 0%, #efe7ff 100%)",border:0,flex:1}},onItemClick:_n})]}),Sn=un.length>0?un.map((function(e){var n=e.id,t=e.message,r=e.status;return{key:a.current+"_"+n,loadingRender:function(){return(0,le.jsxs)(C.Z,{children:[(0,le.jsx)(R.Z,{size:"small"}),"模型思考中..."]})},loading:"loading"===r&&t.content.length<1,content:t.content,shape:"local"===r?"corner":"round",variant:"local"===r?"filled":"borderless",rols:t.role,messageRender:ke,avatar:"local"===r?{src:(null==xe?void 0:xe.avatar)||"/avatar/default.jpeg"}:{src:(null==me?void 0:me.logo)||"/static/logo.png"},placement:"local"!==r?"start":"end",footer:"local"!==r?(0,le.jsx)(z.Z,{children:(0,le.jsxs)("div",{style:{display:"flex",flexWrap:"wrap",gap:"10px"},children:[(0,le.jsxs)("div",{style:{width:"100%"},children:[t.references.length>0&&(0,le.jsxs)(T.Z,{bordered:!1,color:"blue",onClick:function(){return e=t.id,console.log("filterMessageReference===>",e),void(He.current&&(He.current.getFilterMessageId()===e?He.current.clearFilter():He.current.filterByMessageId(e)));var e},children:["引用:",t.references.length]}),(0,le.jsx)(P.ZP,{size:"small",type:"text",icon:(0,le.jsx)(G.Z,{style:{color:"#ccc"}}),onClick:function(){return wn(t.id)}}),(0,le.jsx)(P.ZP,{size:"small",type:"text",icon:t.collected?(0,le.jsx)(Q.Z,{style:{color:"#FFD700"}}):(0,le.jsx)(V.Z,{style:{color:"#ccc"}}),onClick:function(){return Zn(t.id,t.collected)}}),(0,le.jsx)(P.ZP,{size:"small",type:"text",icon:(0,le.jsx)(ee.Z,{style:{color:"#ccc"}}),onClick:function(){return rn(t.id)}}),(0,le.jsx)(P.ZP,{size:"small",type:"text",icon:(0,le.jsx)(ne.Z,{style:{color:"#ccc"}}),onClick:function(){return sn(t.id)}}),t.query&&t.query.length>0&&(0,le.jsx)(P.ZP,{size:"small",type:"text",onClick:function(){tn((function(e){return c()(c()({},e),{},s()({},n,!e[n]))}))},children:nn[n]?"隐藏图表":"显示图表"})]}),(0,le.jsx)("div",{style:{width:"100%"},children:nn[n]&&t.query&&t.query.length>0&&(0,le.jsx)("div",{style:{padding:"30px",backgroundColor:"#f9f9f9",borderRadius:"8px",width:"60vw",height:"50vh",marginTop:"30px"},children:Object.keys(t.query[0]).length<=2?(0,le.jsx)(be,{data:t.query}):(0,le.jsxs)("table",{style:{width:"100%",borderCollapse:"collapse"},children:[(0,le.jsx)("thead",{children:(0,le.jsx)("tr",{children:Object.keys(t.query[0]).map((function(e){return(0,le.jsx)("th",{style:{border:"1px solid #ccc",padding:"8px"},children:e},e)}))})}),(0,le.jsx)("tbody",{children:t.query.map((function(e){return(0,le.jsx)("tr",{children:Object.keys(e).map((function(n){return(0,le.jsx)("td",{style:{border:"1px solid #ccc",padding:"8px"},children:e[n]},"".concat(e.id,"-").concat(n))}))},e.id||e.title)}))})]})})})]})}):(0,le.jsxs)(z.Z,{children:[(0,le.jsx)(P.ZP,{size:"small",type:"text",icon:(0,le.jsx)(te.Z,{style:{color:"#ccc"}}),onClick:function(){return jn(t.id)}}),(0,le.jsx)(P.ZP,{size:"small",type:"text",icon:(0,le.jsx)(G.Z,{style:{color:"#ccc"}}),onClick:function(){return wn(t.id)}}),(0,le.jsx)(P.ZP,{size:"small",type:"text",icon:t.collected?(0,le.jsx)(Q.Z,{style:{color:"#FFD700"}}):(0,le.jsx)(V.Z,{style:{color:"#ccc"}}),onClick:function(){return Zn(t.id,t.collected)}}),(0,le.jsx)(P.ZP,{size:"small",type:"text",icon:(0,le.jsx)(ee.Z,{style:{color:"#ccc"}}),onClick:function(){return rn(t.id)}}),(0,le.jsx)(P.ZP,{size:"small",type:"text",icon:(0,le.jsx)(ne.Z,{style:{color:"#ccc"}}),onClick:function(){return sn(t.id)}})]})}})):[{content:Cn,variant:"borderless"}],Pn=(0,le.jsx)(w.Z.Header,{title:"Attachments",open:d,onOpenChange:f,styles:{content:{padding:0}}}),qn=(0,le.jsxs)("div",{className:n.logo,children:[(0,le.jsx)("span",{children:"对话记录"}),(0,le.jsx)(F.Z,{title:"新对话",children:(0,le.jsx)(P.ZP,{type:"text",icon:(0,le.jsx)(J.Z,{}),onClick:gn,style:{fontSize:"16px"}})})]}),Dn=(0,le.jsx)(D.Z,{title:"修改对话标题",open:Se,onOk:function(){Oe&&Re.trim()&&(kn(Oe,Re.trim()),Pe(!1))},onCancel:function(){Pe(!1),ze(""),Ne("")},children:(0,le.jsx)(O.Z,{value:Re,onChange:function(e){return ze(e.target.value)},placeholder:"请输入新的对话标题"})}),Rn=(0,le.jsx)(N.Z,{title:"历史对话",placement:"right",width:400,onClose:function(){return ge(!1)},open:he,children:(0,le.jsxs)("div",{className:n.menu,children:[qn,(0,le.jsx)(Z.Z,{items:L,activeKey:X,onActiveChange:bn,menu:function(e){return{items:[{label:"重命名",key:"edit",icon:(0,le.jsx)(U.Z,{})},{label:"置顶",key:"pin",icon:(0,le.jsx)($.Z,{})},{label:"删除",key:"delete",icon:(0,le.jsx)(G.Z,{}),danger:!0}],onClick:function(n){switch(console.log("menuInfo","Click ".concat(e.key," - ").concat(n.key)),n.key){case"edit":Ne(e.key),ze(e.label),Pe(!0);break;case"pin":vn(e.key);break;case"delete":if(we)return void q.ZP.error("系统正在处理其他对话。请稍😊");yn(e.key)}}}},groupable:!0})]})});return(0,Y.useEffect)((function(){console.log("currentConversationMessages 更新了:",Me)}),[Me]),(0,le.jsxs)("div",{className:n.layout,style:{height:r-56},children:[(0,le.jsxs)("div",{className:n.chat,children:[(0,le.jsx)(_.Z.List,{items:Sn,className:n.messages}),(0,le.jsx)(j.Z,{items:fe,onItemClick:_n}),(0,le.jsx)(w.Z,{value:I,header:Pn,onSubmit:function(e){console.log("nextContent===>",e),e&&(ln({id:ue(a.current),role:"user",content:e,references:[],query:[],collected:!1}),E(""))},onChange:E,loading:cn.isRequesting(),className:n.sender})]}),(0,le.jsx)("div",{className:n.reference,children:(0,le.jsx)(g.Z,{ref:He,messages:Me})}),Dn,Rn,(0,le.jsx)(oe.Z,{visible:We,messageId:Ke,conversationId:X,appInfo:ve,onClose:function(){return Be(!1)}})]})}}}]);