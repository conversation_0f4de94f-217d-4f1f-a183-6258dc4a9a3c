{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import librosa\n", "\n", "def audio_to_mel(audio_path, save_path):\n", "    # 加载音频（默认采样率22050，可调整）\n", "    y, sr = librosa.load(audio_path, sr=22050)\n", "    \n", "    # 提取梅尔频谱\n", "    mel = librosa.feature.melspectrogram(y=y, sr=sr, n_fft=1024, hop_length=256, n_mels=80)\n", "    mel_db = librosa.power_to_db(mel, ref=np.max)  # 转为对数刻度\n", "    \n", "    # 保存为.npy文件\n", "    np.save(save_path, mel_db)\n", "    print(f\"Saved Mel spectrogram to {save_path}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved <PERSON> spectrogram to 102.npy\n"]}], "source": ["# 使用示例\n", "audio_to_mel(\"102.wav\", \"102.npy\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}