from fastapi import FastAPI, HTTPException, Header
from typing import Optional
import psutil
import platform
import os
from dotenv import load_dotenv
import subprocess
from typing import List, Optional, Dict
from pydantic import BaseModel
from logger import get_logger, setup_logging
from npu_utils import NPUUtil
setup_logging()
logger = get_logger(__name__)

load_dotenv()

app = FastAPI()

API_KEY = os.getenv("API_KEY")

# GPU检测函数
def check_gpu_available() -> bool:
    """检查系统是否有可用的GPU"""
    logger.info("开始检测GPU设备...")
    
    # 方法1: 使用nvidia-smi命令
    try:
        nvidia_smi_output = subprocess.check_output(['nvidia-smi']).decode()
        logger.info("通过nvidia-smi检测到GPU设备")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        logger.warning(f"nvidia-smi检测失败: {str(e)}")
    
    # 方法2: 检查nvidia驱动目录
    nvidia_dirs = [
        '/proc/driver/nvidia/gpus',
        '/dev/nvidia0',
        '/usr/lib/nvidia'
    ]
    for dir_path in nvidia_dirs:
        if os.path.exists(dir_path):
            logger.info(f"在{dir_path}检测到NVIDIA驱动")
            return True
    
    # 方法3: 使用lspci命令
    try:
        lspci_output = subprocess.check_output(['lspci']).decode().lower()
        if 'nvidia' in lspci_output:
            logger.info("通过lspci检测到NVIDIA设备")
            return True
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        logger.warning(f"lspci检测失败: {str(e)}")
    
    # 方法4: 尝试使用GPUtil
    try:
        import GPUtil
        gpus = GPUtil.getGPUs()
        if gpus:
            logger.info("通过GPUtil检测到GPU设备")
            return True
    except ImportError:
        logger.warning("GPUtil未安装")
    except Exception as e:
        logger.warning(f"GPUtil检测失败: {str(e)}")
    
    logger.warning("未检测到任何GPU设备")
    return False

# GPU工具函数
def get_nvidia_smi_path() -> str:
    """获取nvidia-smi的完整路径"""
    possible_paths = [
        'nvidia-smi',  # 默认PATH
        '/usr/bin/nvidia-smi',
        '/usr/local/bin/nvidia-smi',
        '/usr/local/cuda/bin/nvidia-smi'
    ]
    
    for path in possible_paths:
        try:
            subprocess.check_output([path, '--version'])
            return path
        except:
            continue
    
    return 'nvidia-smi'  # 如果都失败，返回默认值

# 检查GPU可用性
HAS_GPU = False
NVIDIA_SMI_PATH = None

try:
    HAS_GPU = check_gpu_available()
    if HAS_GPU:
        NVIDIA_SMI_PATH = get_nvidia_smi_path()
        logger.info(f"GPU监控已启用，使用{NVIDIA_SMI_PATH}")
    else:
        logger.warning("GPU监控已禁用")
except Exception as e:
    logger.error(f"GPU检测过程发生错误: {str(e)}")
    HAS_GPU = False

# 检查NPU可用性
try:
    HAS_NPU = NPUUtil.get_npu_count() > 0
except Exception as e:
    logger.warning(f"NPU check failed: {str(e)}. NPU monitoring will be disabled.")
    HAS_NPU = False

# 基础响应模型
class BaseResponse(BaseModel):
    success: bool
    message: str = ""
    data: Optional[Dict] = None
    error: Optional[str] = None

def create_response(success: bool, data: Optional[Dict] = None, message: str = "", error: str = None) -> Dict:
    """创建统一的响应格式"""
    if not success:
        logger.error(f"API错误: {error}")
    else:
        logger.info(f"API响应: {message}")
    return {
        "success": success,
        "message": message,
        "data": data,
        "error": error
    }

def verify_api_key(api_key: str = Header(...)):
    if api_key != API_KEY:
        raise HTTPException(status_code=403, detail="Invalid API key")
    return api_key

def get_cuda_version() -> Optional[str]:
    """获取CUDA版本信息"""
    if not HAS_GPU:
        return None
    try:
        nvcc_output = subprocess.check_output(["nvcc", "--version"]).decode()
        for line in nvcc_output.split('\n'):
            if "release" in line:
                return line.split("release")[-1].strip().split(",")[0]
    except:
        try:
            nvidia_smi_output = subprocess.check_output(["nvidia-smi"]).decode()
            for line in nvidia_smi_output.split('\n'):
                if "CUDA Version:" in line:
                    return line.split("CUDA Version:")[-1].strip()
        except:
            pass
    return None

def get_gpu_driver_info() -> Dict:
    """获取GPU驱动信息"""
    if not HAS_GPU:
        return {}
    try:
        nvidia_smi_output = subprocess.check_output(["nvidia-smi", "-q"]).decode()
        driver_info = {}
        for line in nvidia_smi_output.split('\n'):
            line = line.strip()
            if "Driver Version" in line:
                driver_info["driver_version"] = line.split(":")[-1].strip()
            elif "CUDA Version" in line:
                driver_info["cuda_version"] = line.split(":")[-1].strip()
        return driver_info
    except:
        return {}

# GPU信息获取函数
def get_gpu_info_safe():
    """安全地获取GPU信息"""
    if not HAS_GPU:
        return []
    
    gpu_devices = []
    
    # 方法1: 使用nvidia-smi命令
    try:
        output = subprocess.check_output([
            NVIDIA_SMI_PATH, 
            '--query-gpu=index,name,memory.total,uuid,temperature.gpu,utilization.gpu,memory.used',
            '--format=csv,noheader,nounits'
        ]).decode()
        
        for line in output.strip().split('\n'):
            try:
                idx, name, total_mem, uuid, temp, util, used_mem = line.split(',')
                gpu_devices.append({
                    "id": int(idx),
                    "name": name.strip(),
                    "memory_total": float(total_mem.strip()),
                    "uuid": uuid.strip(),
                    "temperature": float(temp.strip()),
                    "utilization": float(util.strip()),
                    "memory_used": float(used_mem.strip())
                })
                logger.debug(f"通过nvidia-smi获取到GPU {idx} 信息")
            except Exception as e:
                logger.warning(f"解析GPU信息失败: {str(e)}")
                continue
                
    except Exception as e:
        logger.warning(f"nvidia-smi获取信息失败: {str(e)}")
        # 方法2: 尝试使用GPUtil
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()
            for gpu in gpus:
                gpu_devices.append({
                    "id": gpu.id,
                    "name": gpu.name,
                    "memory_total": gpu.memoryTotal,
                    "uuid": gpu.uuid,
                    "temperature": gpu.temperature,
                    "utilization": gpu.load * 100,
                    "memory_used": gpu.memoryUsed
                })
                logger.debug(f"通过GPUtil获取到GPU {gpu.id} 信息")
        except Exception as e:
            logger.warning(f"GPUtil获取信息失败: {str(e)}")
    
    return gpu_devices

def print_system_info():
    """打印系统信息"""
    logger.info("="*50)
    logger.info("系统信息")
    logger.info("="*50)
    
    # 基本系统信息
    logger.info(f"操作系统: {platform.system()} {platform.version()}")
    logger.info(f"系统架构: {platform.machine()}")
    logger.info(f"CPU核心数: {psutil.cpu_count()} (物理核心: {psutil.cpu_count(logical=False)})")
    memory = psutil.virtual_memory()
    logger.info(f"内存大小: {memory.total / (1024**3):.1f}GB")
    
    # 磁盘信息
    logger.info("\n磁盘信息:")
    for partition in psutil.disk_partitions():
        try:
            usage = psutil.disk_usage(partition.mountpoint)
            logger.info(f"  {partition.mountpoint}: "
                       f"总容量 {usage.total / (1024**3):.1f}GB, "
                       f"已用 {usage.used / (1024**3):.1f}GB, "
                       f"可用 {usage.free / (1024**3):.1f}GB")
        except:
            continue

def print_gpu_info():
    """打印GPU信息"""
    logger.info("\n" + "="*50)
    logger.info("GPU信息")
    logger.info("="*50)
    
    if not HAS_GPU:
        logger.info("未检测到GPU设备")
        return
    
    try:
        gpu_devices = get_gpu_info_safe()
        if not gpu_devices:
            logger.info("无法获取GPU信息")
            return
        
        cuda_version = get_cuda_version()
        driver_info = get_gpu_driver_info()
        
        logger.info(f"CUDA版本: {cuda_version or '未知'}")
        logger.info(f"驱动版本: {driver_info.get('driver_version', '未知')}")
        
        logger.info("\nGPU设备列表:")
        for gpu in gpu_devices:
            logger.info(f"\n  设备 {gpu['id']}:")
            logger.info(f"    名称: {gpu['name']}")
            logger.info(f"    UUID: {gpu['uuid']}")
            logger.info(f"    显存: {gpu['memory_total']}MB")
            logger.info(f"    温度: {gpu.get('temperature', '未知')}°C")
            logger.info(f"    使用率: {gpu.get('utilization', '未知')}%")
            logger.info(f"    显存使用: {gpu.get('memory_used', '未知')}MB")
    except Exception as e:
        logger.error(f"打印GPU信息时发生错误: {str(e)}")

def print_npu_info():
    """打印NPU信息"""
    logger.info("\n" + "="*50)
    logger.info("NPU信息")
    logger.info("="*50)
    
    if not HAS_NPU:
        logger.info("未检测到NPU设备")
        return
    
    try:
        npu_devices = NPUUtil.get_npu_info()
        driver_version = NPUUtil.get_driver_version()
        
        logger.info(f"驱动版本: {driver_version or '未知'}")
        
        logger.info("\nNPU设备列表:")
        for npu in npu_devices:
            logger.info(f"\n  设备 {npu['id']}:")
            logger.info(f"    名称: {npu['name']}")
            logger.info(f"    内存: {npu['memory_total']}MB")
    except Exception as e:
        logger.error(f"打印NPU信息时发生错误: {str(e)}")

# 在应用启动时打印系统信息
@app.on_event("startup")
async def startup_event():
    """应用启动时执行"""
    logger.info("\n" + "="*50)
    logger.info("监控服务启动")
    logger.info("="*50)
    
    print_system_info()
    print_gpu_info()
    print_npu_info()
    
    logger.info("\n" + "="*50)
    logger.info("服务就绪")
    logger.info("="*50)

@app.get("/system/info", response_model=BaseResponse)
async def get_system_info(api_key: str = Header(...)):
    try:
        logger.info("开始获取系统信息")
        verify_api_key(api_key)
        
        # 获取基本系统信息
        system_info = {
            "os": platform.system(),
            "os_version": platform.version(),
            "architecture": platform.machine(),
            "cpu_count": psutil.cpu_count(),
            "cpu_physical_count": psutil.cpu_count(logical=False),
            "memory_total": psutil.virtual_memory().total,
            "disk_partitions": [p.mountpoint for p in psutil.disk_partitions()]
        }
        logger.debug(f"系统信息: {system_info}")
        
        return create_response(True, data=system_info, message="获取系统信息成功")
    except Exception as e:
        logger.exception("获取系统信息时发生错误")
        return create_response(False, error=f"系统错误: {str(e)}")

@app.get("/system/metrics", response_model=BaseResponse)
async def get_system_metrics(api_key: str = Header(...)):
    """获取基本系统指标"""
    try:
        verify_api_key(api_key)
        
        metrics = {
            "cpu": {
                "percent": psutil.cpu_percent(interval=1),
                "per_cpu": psutil.cpu_percent(interval=1, percpu=True),
                "freq": {
                    "current": psutil.cpu_freq().current if hasattr(psutil.cpu_freq(), 'current') else None,
                    "min": psutil.cpu_freq().min if hasattr(psutil.cpu_freq(), 'min') else None,
                    "max": psutil.cpu_freq().max if hasattr(psutil.cpu_freq(), 'max') else None
                }
            },
            "memory": dict(psutil.virtual_memory()._asdict()),
            "disk": {p.mountpoint: psutil.disk_usage(p.mountpoint)._asdict() 
                     for p in psutil.disk_partitions()},
            "network": dict(psutil.net_io_counters()._asdict())
        }
        
        return create_response(True, data=metrics, message="获取系统指标成功")
    except Exception as e:
        logger.exception("获取系统指标时发生错误")
        return create_response(False, error=f"系统错误: {str(e)}")

@app.get("/gpu/metrics", response_model=BaseResponse)
async def get_gpu_metrics(api_key: str = Header(...)):
    """获取GPU监控指标"""
    try:
        verify_api_key(api_key)
        
        if not HAS_GPU:
            return create_response(True, data={
                "available": False,
                "devices": []
            }, message="系统未安装GPU")

        try:
            gpu_devices = get_gpu_info_safe()
            if not gpu_devices:
                return create_response(True, data={
                    "available": False,
                    "devices": []
                }, message="无法获取GPU指标")

            return create_response(True, data={
                "available": True,
                "devices": gpu_devices
            }, message="获取GPU指标成功")
        except Exception as e:
            logger.error(f"获取GPU指标失败: {str(e)}")
            return create_response(False, error=f"获取GPU指标失败: {str(e)}")
            
    except Exception as e:
        return create_response(False, error=f"系统错误: {str(e)}")

@app.get("/npu/metrics", response_model=BaseResponse)
async def get_npu_metrics(api_key: str = Header(...)):
    """获取NPU监控指标"""
    try:
        verify_api_key(api_key)
        
        if not HAS_NPU:
            return create_response(True, data={
                "available": False,
                "devices": []
            }, message="系统未安装NPU")

        try:
            npu_metrics = NPUUtil.get_npu_metrics()
            return create_response(True, data={
                "available": True,
                "devices": npu_metrics
            }, message="获取NPU指标成功")
        except Exception as e:
            logger.error(f"获取NPU指标失败: {str(e)}")
            return create_response(False, error=f"获取NPU指标失败: {str(e)}")
            
    except Exception as e:
        return create_response(False, error=f"系统错误: {str(e)}")

@app.get("/gpu/info", response_model=BaseResponse)
async def get_gpu_info(api_key: str = Header(...)):
    """获取GPU信息"""
    try:
        verify_api_key(api_key)
        
        if not HAS_GPU:
            return create_response(True, data={
                "available": False,
                "devices": [],
                "cuda_version": None,
                "driver_info": {}
            }, message="系统未安装GPU")

        try:
            gpu_devices = get_gpu_info_safe()
            if not gpu_devices:
                return create_response(True, data={
                    "available": False,
                    "devices": [],
                    "cuda_version": None,
                    "driver_info": {}
                }, message="无法获取GPU信息")

            gpu_info = {
                "available": True,
                "devices": gpu_devices,
                "cuda_version": get_cuda_version(),
                "driver_info": get_gpu_driver_info()
            }
            
            return create_response(True, data=gpu_info, message="获取GPU信息成功")
        except Exception as e:
            logger.error(f"获取GPU信息失败: {str(e)}")
            return create_response(False, error=f"获取GPU信息失败: {str(e)}")
            
    except Exception as e:
        return create_response(False, error=f"系统错误: {str(e)}")

@app.get("/npu/info", response_model=BaseResponse)
async def get_npu_info(api_key: str = Header(...)):
    """获取NPU信息"""
    try:
        verify_api_key(api_key)
        
        if not HAS_NPU:
            return create_response(True, data={
                "available": False,
                "devices": [],
                "driver_version": None
            }, message="系统未安装NPU")

        try:
            npu_info = {
                "available": True,
                "devices": NPUUtil.get_npu_info(),
                "driver_version": NPUUtil.get_driver_version()
            }
            
            return create_response(True, data=npu_info, message="获取NPU信息成功")
        except Exception as e:
            logger.error(f"获取NPU信息失败: {str(e)}")
            return create_response(False, error=f"获取NPU信息失败: {str(e)}")
            
    except Exception as e:
        return create_response(False, error=f"系统错误: {str(e)}") 