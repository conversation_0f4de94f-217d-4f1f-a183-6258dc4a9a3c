"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8636],{47422:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M682 455V311l-76 76v68c-.1 50.7-42 92.1-94 92a95.8 95.8 0 01-52-15l-54 55c29.1 22.4 65.9 36 106 36 93.8 0 170-75.1 170-168z"}},{tag:"path",attrs:{d:"M833 446h-60c-4.4 0-8 3.6-8 8 0 140.3-113.7 254-254 254-63 0-120.7-23-165-61l-54 54a334.01 334.01 0 00179 81v102H326c-13.9 0-24.9 14.3-25 32v36c.1 4.4 2.9 8 6 8h408c3.2 0 6-3.6 6-8v-36c0-17.7-11-32-25-32H547V782c165.3-17.9 294-157.9 294-328 0-4.4-3.6-8-8-8zm13.1-377.7l-43.5-41.9a8 8 0 00-11.2.1l-129 129C634.3 101.2 577 64 511 64c-93.9 0-170 75.3-170 168v224c0 6.7.4 13.3 1.2 19.8l-68 68A252.33 252.33 0 01258 454c-.2-4.4-3.8-8-8-8h-60c-4.4 0-8 3.6-8 8 0 53 12.5 103 34.6 147.4l-137 137a8.03 8.03 0 000 11.3l42.7 42.7c3.1 3.1 8.2 3.1 11.3 0L846.2 79.8l.1-.1c3.1-3.2 3-8.3-.2-11.4zM417 401V232c0-50.6 41.9-92 94-92 46 0 84.1 32.3 92.3 74.7L417 401z"}}]},name:"audio-muted",theme:"outlined"}},62497:function(e,t){t.Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M842 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 140.3-113.7 254-254 254S258 594.3 258 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 168.7 126.6 307.9 290 327.6V884H326.7c-13.7 0-24.7 14.3-24.7 32v36c0 4.4 2.8 8 6.2 8h407.6c3.4 0 6.2-3.6 6.2-8v-36c0-17.7-11-32-24.7-32H548V782.1c165.3-18 294-158 294-328.1zM512 624c93.9 0 170-75.2 170-168V232c0-92.8-76.1-168-170-168s-170 75.2-170 168v224c0 92.8 76.1 168 170 168zm-94-392c0-50.6 41.9-92 94-92s94 41.4 94 92v224c0 50.6-41.9 92-94 92s-94-41.4-94-92V232z"}}]},name:"audio",theme:"outlined"}},52838:function(e,t,n){n.r(t),n.d(t,{default:function(){return D}});var r=n(12444),a=n.n(r),c=n(72004),i=n.n(c),o=n(9783),u=n.n(o),s=n(5574),l=n.n(s),h=n(67294),f=n(71471),d=n(2453),v=n(4393),p=n(42075),g=n(83622),m=n(55102),w=n(72269),y=n(1413),x=n(47422),S=n(91146),Z=function(e,t){return h.createElement(S.Z,(0,y.Z)((0,y.Z)({},e),{},{ref:t,icon:x.Z}))};var b=h.forwardRef(Z),k=n(62497),j=function(e,t){return h.createElement(S.Z,(0,y.Z)((0,y.Z)({},e),{},{ref:t,icon:k.Z}))};var B=h.forwardRef(j),C=n(97131),M=n(85893),z=f.Z.Text,R=function(){function e(t){var n=this;a()(this,e),u()(this,"sampleBits",16),u()(this,"inputSampleRate",48e3),u()(this,"outputSampleRate",16e3),u()(this,"channelCount",1),u()(this,"context",void 0),u()(this,"audioInput",void 0),u()(this,"recorder",void 0),u()(this,"audioData",void 0),this.context=new AudioContext,this.audioInput=this.context.createMediaStreamSource(t),this.recorder=this.context.createScriptProcessor(4096,this.channelCount,this.channelCount),this.audioData={size:0,buffer:[],inputSampleRate:this.inputSampleRate,inputSampleBits:this.sampleBits,clear:function(){this.buffer=[],this.size=0},input:function(e){this.buffer.push(new Float32Array(e)),this.size+=e.length},encodePCM:function(){for(var e=new Float32Array(this.size),t=0,n=0;n<this.buffer.length;n++)e.set(this.buffer[n],t),t+=this.buffer[n].length;var r=e.length*(this.inputSampleBits/8),a=new ArrayBuffer(r),c=new DataView(a);t=0;for(var i=0;i<e.length;i++,t+=2){var o=Math.max(-1,Math.min(1,e[i]));c.setInt16(t,o<0?32768*o:32767*o,!0)}return new Blob([c],{type:"audio/pcm"})}},this.recorder.onaudioprocess=function(e){var t=n.downsampleBuffer(e.inputBuffer.getChannelData(0),n.inputSampleRate,n.outputSampleRate);n.audioData.input(t)}}return i()(e,[{key:"start",value:function(){this.audioInput.connect(this.recorder),this.recorder.connect(this.context.destination)}},{key:"stop",value:function(){this.recorder.disconnect(),this.audioInput.disconnect()}},{key:"getBlob",value:function(){return this.audioData.encodePCM()}},{key:"clear",value:function(){this.audioData.clear()}},{key:"downsampleBuffer",value:function(e,t,n){if(n===t)return e;for(var r=t/n,a=Math.round(e.length/r),c=new Float32Array(a),i=0,o=0;i<c.length;){for(var u=Math.round((i+1)*r),s=0,l=0,h=o;h<u&&h<e.length;h++)s+=e[h],l++;c[i]=s/l,i++,o=u}return c}}]),e}(),D=function(){var e=(0,h.useState)(!1),t=l()(e,2),n=t[0],r=t[1],a=(0,h.useState)(""),c=l()(a,2),i=c[0],o=c[1],u=(0,h.useState)("auto"),s=l()(u,2),f=s[0],y=s[1],x=(0,h.useState)(!1),S=l()(x,2),Z=S[0],k=S[1],j=(0,h.useRef)(null),D=(0,h.useRef)(null),V=(0,h.useRef)(null);(0,h.useEffect)((function(){return navigator.mediaDevices&&navigator.mediaDevices.getUserMedia?navigator.mediaDevices.getUserMedia({audio:!0}).then((function(e){I(e)})).catch((function(e){d.ZP.error("无法访问麦克风"),console.error(e)})):d.ZP.error("您的浏览器不支持音频输入"),function(){P()}}),[]);var I=function(e){D.current=new R(e)},P=function(){var e;j.current&&(j.current.close(),null===(e=D.current)||void 0===e||e.stop(),V.current&&clearInterval(V.current));r(!1)};return(0,M.jsx)(C._z,{children:(0,M.jsx)(v.Z,{title:"实时语音识别",style:{margin:24},children:(0,M.jsxs)(p.Z,{direction:"vertical",style:{width:"100%"},size:"large",children:[(0,M.jsxs)(p.Z,{children:[(0,M.jsx)(g.ZP,{type:"primary",icon:n?(0,M.jsx)(b,{}):(0,M.jsx)(B,{}),onClick:function(){return n?P():function(){var e=[];f&&e.push("lang=".concat(f)),Z&&e.push("sv=1");var t=e.length>0?"?".concat(e.join("&")):"";j.current=new WebSocket("ws://124.225.78.123:27000/ws/transcribe".concat(t)),j.current.binaryType="arraybuffer",j.current.onopen=function(){D.current.start(),V.current=setInterval((function(){var e;if(1===(null===(e=j.current)||void 0===e?void 0:e.readyState)){var t=D.current.getBlob();j.current.send(t),D.current.clear()}}),500)},j.current.onmessage=function(e){try{var t=JSON.parse(e.data);0===t.code&&o((function(e){return e+"\n"+(t.data||"未识别到语音")}))}catch(t){console.error("解析响应数据失败",t),o((function(t){return t+"\n"+e.data}))}},j.current.onclose=function(){console.log("WebSocket 连接已关闭")},j.current.onerror=function(e){d.ZP.error("WebSocket 错误"),console.error("WebSocket error:",e)},r(!0)}()},danger:n,children:n?"停止录音":"开始录音"}),(0,M.jsx)(m.Z,{addonBefore:"语言",style:{width:200},value:f,onChange:function(e){return y(e.target.value)},placeholder:"auto"}),(0,M.jsxs)(p.Z,{children:[(0,M.jsx)(z,{children:"说话人识别："}),(0,M.jsx)(w.Z,{checked:Z,onChange:function(e){return k(e)}})]})]}),(0,M.jsx)(v.Z,{type:"inner",title:"识别结果",style:{backgroundColor:"#f5f5f5"},children:(0,M.jsx)("pre",{style:{whiteSpace:"pre-wrap",margin:0},children:i||"等待识别..."})})]})})})}}}]);