from dataclasses import dataclass

from langgraph.graph.state import CompiledStateGraph

from .schema.schema import AgentInfo
# from ..agents.adaptive_rag import rag_assistant
from .chatbot import chatbot

DEFAULT_AGENT = "chatbot"
CHAT_AGENT = "chatbot"


@dataclass
class Agent:
    description: str
    graph: CompiledStateGraph


agents: dict[str, Agent] = {
    # "rag-assistant": Agent(
    #     description="A research assistant with web search and calculator.", graph=rag_assistant
    # )
     "chatbot": Agent(description="一个简单的聊天机器人.", graph=chatbot),
    #  "baseRAG": Agent(description="强化检索增强.", graph=adaptiveRAG),
}


def get_agent(agent_type=None):
    """
    获取对应类型的智能体
    """
    if agent_type == CHAT_AGENT:
        return chatbot
    else:
        return chatbot


def get_agent_output_filter(agent_type=None):
    """
    获取对应类型的智能体输出过滤器
    """
   
    return None
def get_all_agent_info() -> list[AgentInfo]:
    return [
        AgentInfo(key=agent_id, description=agent.description) for agent_id, agent in agents.items()
    ]



def get_all_agent_info() -> list[AgentInfo]:
    return [
        AgentInfo(key=agent_id, description=agent.description) for agent_id, agent in agents.items()
    ]


