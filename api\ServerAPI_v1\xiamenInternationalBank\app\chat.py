# ... existing imports ...
from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import StreamingResponse
import asyncio
from .utils import ChatResponse
from .database import get_db  # 修改这里：使用正确的 get_db 导入
from .langgraphchat import FlowRAG
from datetime import datetime
from typing import Dict, Any, List, Optional
from .logging_config import get_logger
from .models import ChatDialogueRequest, Chat, AppInfo
from .llmapiClient import chat_model_api
import json
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel
from sqlalchemy import text
from uuid import UUID

logger = get_logger(__name__)

router = APIRouter(
    prefix="/api/v1/chat",  # 更新前缀
    tags=["chat"]
)

# 定义请求模型
class CreateChatRequest(BaseModel):
    appId: str

# 创建新聊天
@router.post("/createChat", response_model=Dict[str, Any])
async def create_chat(
    request: CreateChatRequest,
    db: AsyncSession = Depends(get_db)
):
    try:
        if not request.appId:
            raise ValueError("应用ID不能为空")
            
        logger.info(f"正在创建新的聊天记录，appId: {request.appId}")
        
        # 创建新的聊天记录
        new_chat = Chat(
            app_id=request.appId,
            created_at=datetime.now(),
            stream=False,
            detail=False,
            variables={},  # 直接使用字典
            messages=[]    # 直接使用列表
        )
        
        db.add(new_chat)
        try:
            await db.commit()  # 直接使用 commit
            await db.refresh(new_chat)
            
            logger.info(f"聊天创建成功: {str(new_chat.id)}")
            return {"chatId": str(new_chat.id), "message": "聊天创建成功"}
            
        except Exception as e:
            logger.error(f"创建聊天失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"创建聊天失败: {str(e)}")
            
    except Exception as e:
        logger.error(f"创建聊天失败，详细错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建聊天失败: {str(e)}")

# 聊天对话
@router.post("/completions")
async def chat_dialogue(
    conversation: ChatDialogueRequest, 
    db: AsyncSession = Depends(get_db)
):
    try:
        # 验证请求参数
        if not conversation.chatId:
            raise ValueError("聊天ID不能为空")
        if not conversation.messages:
            raise ValueError("消息内容不能为空")
        if not conversation.datasetId:
            raise ValueError("知识库ID不能为空")

        # 获取聊天记录
        stmt = select(Chat).where(Chat.id == conversation.chatId)
        result = await db.execute(stmt)
        chat = result.scalar_one_or_none()
        
        if not chat:
            logger.error(f"聊天记录未找到: {conversation.chatId}")
            raise HTTPException(status_code=404, detail="聊天记录未找到")

        # 提取最后一条消息
        last_message = conversation.messages[-1]
        logger.info(f'最新消息: {last_message}')

        # 更新聊天记录
        chat.messages = conversation.messages
        chat.variables = conversation.variables
        chat.stream = conversation.stream
        chat.detail = conversation.detail
        await db.commit()

        async def stream_response():
            try:
                ai_message_content = ""
                async for chunk in chat_model_api(
                    stream=conversation.stream,
                    detail=conversation.detail,
                    chat_id=conversation.chatId,
                    knowledgebase_ids=conversation.datasetId,
                    messages=conversation.messages,
                ):
                    if not chunk:
                        continue
                    ai_message_content += chunk
                    logger.debug(f"Stream chunk: {chunk}")
                    yield chunk
                    await asyncio.sleep(0)

                yield "event: end\ndata: Stream has ended\n\n"

                # 更新最终的AI回复到数据库
                if ai_message_content:
                    try:
                        conversation.messages.append({
                            "role": "assistant",
                            "content": ai_message_content
                        })
                        chat.messages = json.dumps(conversation.messages)
                        await db.commit()
                    except Exception as e:
                        logger.error(f"保存AI回复失败: {str(e)}")

            except asyncio.CancelledError:
                logger.warning(f"Stream was cancelled by client: {conversation.chatId}")
                raise
            except Exception as e:
                logger.error(f"Stream处理错误: {str(e)}")
                yield f"event: error\ndata: {str(e)}\n\n"

        return StreamingResponse(
            stream_response(),
            media_type='text/event-stream'
        )

    except ValueError as e:
        logger.error(f"参数验证错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理聊天请求失败: {str(e)}")
        raise HTTPException(status_code=500, detail="处理聊天请求失败")



