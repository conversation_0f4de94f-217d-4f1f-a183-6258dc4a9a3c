from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from ..models.embedding import EmbeddingModel, EmbeddingCreate, EmbeddingUpdate, EmbeddingResponse
from ..db.mongodb import db
from datetime import datetime
from ..utils.auth import verify_token

router = APIRouter()

# 获取所有 Embedding 模型，支持分页
@router.get("/api/embeddings", response_model=Dict[str, Any])
async def get_embeddings(
    current: int = Query(1, description="Current page"),
    pageSize: int = Query(10, description="Page size"),
    m_name: Optional[str] = None,  # 支持按模型名称检索
    provider: Optional[str] = None,  # 支持按提供商检索
    current_user: dict = Depends(verify_token)
):
    skip = (current - 1) * pageSize
    query = {}
    if m_name:
        query["m_name"] = {"$regex": m_name, "$options": "i"}
    if provider:
        query["provider"] = {"$regex": provider, "$options": "i"}

    embeddings = await db["embeddings"].find(query, {
        "_id": 0,
        "id": 1,
        "api_key": 1,
        "service_url": 1,
        "name": 1,
        "embedding_name": 1,
        "reRank_model": 1,
        "provider": 1,
        "is_active": 1,
        "created_at": 1
    }).skip(skip).limit(pageSize).to_list(pageSize)
    total = await db["embeddings"].count_documents(query)
    return {
        "data": embeddings,
        "total": total,
        "success": True,
        "pageSize": pageSize,
        "current": current
    }

# 添加新 Embedding 模型
@router.post("/api/embeddings", response_model=EmbeddingResponse)
async def add_embedding(embedding: EmbeddingCreate, current_user: dict = Depends(verify_token)):
    last_embedding = await db["embeddings"].find_one(sort=[("id", -1)])
    new_id = (last_embedding["id"] + 1) if last_embedding else 1

    new_embedding = embedding.dict()
    new_embedding.update({
        "id": new_id,
        "created_at": datetime.now(),
        "created_by": current_user["id"],  # 确保设置 created_by
        "is_active": True,
        "api_key": embedding.api_key,
        "embedding_name": embedding.embedding_name,
        "provider": embedding.provider,
        "service_url": embedding.service_url,
        "vector_size": embedding.vector_size  # 确保设置 vector_size
    })

    await db["embeddings"].insert_one(new_embedding)
    return EmbeddingResponse(**new_embedding)

# 更新 Embedding 模型
@router.put("/api/embeddings/{embedding_id}", response_model=EmbeddingResponse)
async def update_embedding(embedding_id: int, embedding: EmbeddingUpdate, current_user: dict = Depends(verify_token)):
    result = await db["embeddings"].update_one({"id": embedding_id}, {"$set": embedding.dict(exclude_unset=True)})
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Embedding not found")
    updated_embedding = await db["embeddings"].find_one({"id": embedding_id})
    return EmbeddingResponse(**updated_embedding)

# 删除 Embedding 模型
@router.delete("/api/embeddings/{embedding_id}", response_model=Dict[str, int])
async def delete_embedding(embedding_id: int, current_user: dict = Depends(verify_token)):
    embedding = await db["embeddings"].find_one({"id": embedding_id})
    if not embedding:
        raise HTTPException(status_code=404, detail="Embedding not found")
    result = await db["embeddings"].delete_one({"id": embedding_id})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Embedding not found")
    return {"id": embedding_id}

@router.get("/api/embeddingsList", response_model=Dict[str, Any])
async def get_embeddings(
    current_user: dict = Depends(verify_token)
):
    embeddings = await db["embeddings"].find({"is_active": True}, {
        "_id": 0,
        "id": 1,
        "name": 1,
        "embedding_name": 1,
    }).to_list()
    return {
        "data": embeddings,
        "success": True,
    }