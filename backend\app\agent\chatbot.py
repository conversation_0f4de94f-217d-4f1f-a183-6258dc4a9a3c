from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_core.prompts import SystemMessagePromptTemplate
from langchain_core.runnables import (RunnableConfig, RunnableLambda,
                                      RunnableSerializable)
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, MessagesState, StateGraph

from .core.llm import get_model
from app.utils.logging_config import get_logger, setup_logging

setup_logging()
logger = get_logger(__name__)

class AgentState(MessagesState, total=False):
    """`total=False` is PEP589 specs.

    documentation: https://typing.readthedocs.io/en/latest/spec/typeddict.html#totality
    """


def wrap_model(model: BaseChatModel) -> RunnableSerializable[AgentState, AIMessage]:
    preprocessor = RunnableLambda(
        lambda state: state["messages"],
        name="StateModifier",
    )
    return preprocessor | model


async def acall_model(state: AgentState, config: RunnableConfig) -> AgentState:
    # 获取线程ID，用于跟踪特定对话
    logger.info('===========>acall_model')
    thread_id = config.get("configurable", {}).get("thread_id", "default")
    # 获取模型ID，应从 llm_id 参数获取
    llm_id = config.get("configurable", {}).get("llm_id", 1)  # 默认值为1
    logger.info(f"使用模型ID: {llm_id}")

    # 获取模型，添加 await 关键字
    m = await get_model(llm_id, stream=True)
    model_runnable = wrap_model(m)

    # 添加系统提示词
    system_prompt = "你是一个简洁高效的助手，你的名字是清清。请用简短的语言回答用户问题，尽量不超过三句话。使用中文回答，保持回答友好、准确且直接。"
    
    # 检查消息列表中是否已有系统消息
    if not any(isinstance(msg, SystemMessage) for msg in state.get("messages", [])):
        # 创建系统消息
        system_msg = SystemMessage(content=system_prompt)
        state["messages"] = [system_msg] + state.get("messages", [])

    # 调用模型并获取响应
    response = await model_runnable.ainvoke(state, config)
    logger.info(f"模型响应: {response}")

    # 记录响应
    logger.info(f"Thread {thread_id} response: {response}")

    # 更新状态，保留原有消息并添加新的AI回复
    updated_messages = state.get("messages", []) + [response]
    
    # 返回更新后的状态
    return {"messages": updated_messages}


# 定义图
agent = StateGraph(AgentState)
agent.add_node("model", acall_model)
agent.set_entry_point("model")

# 总是在模型响应后结束
agent.add_edge("model", END)

# 编译图，使用内存保存器来保存状态
chatbot = agent.compile(
    checkpointer=MemorySaver(),
)
