"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5340],{45393:function(e,r,n){n.r(r);var t=n(97857),o=n.n(t),a=n(15009),c=n.n(a),l=n(19632),i=n.n(l),s=n(99289),u=n.n(s),d=n(5574),p=n.n(d),f=n(67294),v=n(97131),g=n(12453),m=n(8232),h=n(2453),b=n(66309),x=n(83622),y=n(17788),C=n(55102),k=n(34041),j=n(51042),Z=n(69044),$=n(23544),w=n(85893);r.default=function(){var e=(0,f.useState)(!1),r=p()(e,2),n=r[0],t=r[1],a=(0,f.useState)(!1),l=p()(a,2),s=l[0],d=l[1],S=(0,f.useState)(),P=p()(S,2),O=P[0],I=P[1],T=(0,f.useRef)(),E=m.Z.useForm(),_=p()(E,1)[0],B=(0,f.useState)([{id:999,name:"全部"}]),z=p()(B,2),N=z[0],A=z[1],q=(0,f.useState)([{id:999,name:"全部"}]),F=p()(q,2),H=F[0],R=F[1],W=function(){var e=u()(c()().mark((function e(){var r,n,t,o;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.prev=1,e.next=4,(0,Z.F3)();case 4:r=e.sent,(n=r)&&Array.isArray(n)?A([{id:999,name:"全部"}].concat(i()(n))):n&&n.data&&Array.isArray(n.data)&&A([{id:999,name:"全部"}].concat(i()(n.data))),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(1),console.error("获取角色失败:",e.t0);case 12:return e.prev=12,e.next=15,(0,Z.jA)();case 15:t=e.sent,(o=t)&&Array.isArray(o)?R([{id:999,name:"全部"}].concat(i()(o))):o&&o.data&&Array.isArray(o.data)&&R([{id:999,name:"全部"}].concat(i()(o.data))),e.next=23;break;case 20:e.prev=20,e.t1=e.catch(12),console.error("获取组织失败:",e.t1);case 23:e.next=28;break;case 25:e.prev=25,e.t2=e.catch(0),h.ZP.error("获取角色或组织失败");case 28:case"end":return e.stop()}}),e,null,[[0,25],[1,9],[12,20]])})));return function(){return e.apply(this,arguments)}}();(0,f.useEffect)((function(){W()}),[]);var L=function(){var e=u()(c()().mark((function e(r){var n,a;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=h.ZP.loading("正在添加"),e.prev=1,e.next=4,(0,Z.cn)(o()({},r));case 4:return n(),h.ZP.success("添加成功"),t(!1),null===(a=T.current)||void 0===a||a.reload(),_.resetFields(),e.abrupt("return",!0);case 12:return e.prev=12,e.t0=e.catch(1),n(),h.ZP.error("添加失败，请重试"),e.abrupt("return",!1);case 17:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(r){return e.apply(this,arguments)}}(),M=function(){var e=u()(c()().mark((function e(r){var n,t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=h.ZP.loading("正在更新"),e.prev=1,e.next=4,(0,Z.Nq)(r);case 4:return n(),h.ZP.success("更新成功"),d(!1),I(void 0),null===(t=T.current)||void 0===t||t.reload(),_.resetFields(),e.abrupt("return",!0);case 13:return e.prev=13,e.t0=e.catch(1),n(),h.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 18:case"end":return e.stop()}}),e,null,[[1,13]])})));return function(r){return e.apply(this,arguments)}}(),X=function(){var e=u()(c()().mark((function e(r){var n,t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=h.ZP.loading("正在删除"),e.prev=1,e.next=4,(0,Z.h8)(r.id);case 4:return n(),h.ZP.success("删除成功"),null===(t=T.current)||void 0===t||t.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),n(),h.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(r){return e.apply(this,arguments)}}(),D=function(){var e=u()(c()().mark((function e(r,n){var t;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,Z.az)(r,n?1:0);case 3:h.ZP.success("用户已".concat(n?"启用":"禁用")),null===(t=T.current)||void 0===t||t.reload(),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),h.ZP.error("操作失败");case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(r,n){return e.apply(this,arguments)}}(),V=[{title:"姓名",dataIndex:"name",valueType:"text"},{title:"电话",dataIndex:"phone",valueType:"text"},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"组织",dataIndex:"group_name",valueType:"select",valueEnum:H.reduce((function(e,r){return e[r.id]={text:r.name},e}),{})},{title:"权限",dataIndex:"role_name",valueType:"select",valueEnum:N.reduce((function(e,r){return e[r.id]={text:r.name},e}),{})},{title:"是否激活",dataIndex:"is_active",valueType:"switch",render:function(e,r){var n=Boolean(r.is_active);return(0,w.jsx)(b.Z,{color:n?"green":"red",children:n?"激活":"禁用"})},search:!1},{title:"操作",dataIndex:"option",valueType:"option",render:function(e,r){return[(0,w.jsx)(x.ZP,{type:"link",onClick:function(){d(!0),I(r)},children:"编辑"},"edit-".concat(r.id)),(0,w.jsx)(x.ZP,{type:"link",danger:!0,onClick:function(){return X(r)},children:"删除"},"delete-".concat(r.id)),(0,w.jsx)(x.ZP,{type:"link",onClick:function(){return D(r.id,!r.is_active)},children:r.is_active?"禁用":"启用"},"status-".concat(r.id))]}}];return(0,w.jsxs)(v._z,{children:[(0,w.jsx)(g.Z,{headerTitle:"用户管理",actionRef:T,rowKey:"id",search:{labelWidth:120,defaultCollapsed:!1},form:{initialValues:{group_id:999,role_id:999}},toolBarRender:function(){return[(0,w.jsxs)(x.ZP,{type:"primary",onClick:function(){t(!0)},children:[(0,w.jsx)(j.Z,{})," 新建"]},"primary")]},request:function(){var e=u()(c()().mark((function e(r){var n;return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,Z.Rf)(o()({current:r.current||1,pageSize:r.pageSize||10},r));case 2:return n=e.sent,e.abrupt("return",{data:n.data,success:n.success,total:n.total});case 4:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),columns:V}),(0,w.jsx)(y.Z,{visible:n,title:"新建用户",onCancel:function(){return t(!1)},onOk:function(){return _.submit()},children:(0,w.jsxs)(m.Z,{form:_,layout:"vertical",onFinish:L,children:[(0,w.jsx)(m.Z.Item,{name:"name",label:"姓名",rules:[{required:!0,message:"请输入姓名"}],children:(0,w.jsx)(C.Z,{})}),(0,w.jsx)(m.Z.Item,{name:"phone",label:"手机号",rules:[{required:!0,message:"请输入手机号"}],children:(0,w.jsx)(C.Z,{})}),(0,w.jsx)(m.Z.Item,{name:"password",label:"初始密码",rules:[{required:!0,message:"请输入初始密码"}],children:(0,w.jsx)(C.Z.Password,{})}),(0,w.jsx)(m.Z.Item,{name:"role_id",label:"选择权限",rules:[{required:!0,message:"请选择权限"}],children:(0,w.jsx)(k.default,{children:N.filter((function(e){return 999!==e.id})).map((function(e){return(0,w.jsx)(k.default.Option,{value:e.id,children:e.name},e.id)}))})}),(0,w.jsx)(m.Z.Item,{name:"group_id",label:"选择组织",rules:[{required:!0,message:"请选择组织"}],children:(0,w.jsx)(k.default,{children:H.filter((function(e){return 999!==e.id})).map((function(e){return(0,w.jsx)(k.default.Option,{value:e.id,children:e.name},e.id)}))})})]})}),O&&(0,w.jsx)($.Z,{onSubmit:function(){var e=u()(c()().mark((function e(r){return c()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,M(o()(o()({},O),r));case 2:e.sent&&(d(!1),I(void 0));case 4:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),onCancel:function(){d(!1),I(void 0)},modalVisible:s,values:O})]})}},66309:function(e,r,n){n.d(r,{Z:function(){return I}});var t=n(67294),o=n(93967),a=n.n(o),c=n(98423),l=n(98787),i=n(69760),s=n(96159),u=n(45353),d=n(53124),p=n(11568),f=n(15063),v=n(14747),g=n(83262),m=n(83559);const h=e=>{const{lineWidth:r,fontSizeIcon:n,calc:t}=e,o=e.fontSizeSM;return(0,g.IX)(e,{tagFontSize:o,tagLineHeight:(0,p.bf)(t(e.lineHeightSM).mul(o).equal()),tagIconSize:t(n).sub(t(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},b=e=>({defaultBg:new f.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var x=(0,m.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:n,tagPaddingHorizontal:t,componentCls:o,calc:a}=e,c=a(t).sub(n).equal(),l=a(r).sub(n).equal();return{[o]:Object.assign(Object.assign({},(0,v.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:c}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(h(e))),b),y=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(n[t[o]]=e[t[o]])}return n};const C=t.forwardRef(((e,r)=>{const{prefixCls:n,style:o,className:c,checked:l,onChange:i,onClick:s}=e,u=y(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:f}=t.useContext(d.E_),v=p("tag",n),[g,m,h]=x(v),b=a()(v,`${v}-checkable`,{[`${v}-checkable-checked`]:l},null==f?void 0:f.className,c,m,h);return g(t.createElement("span",Object.assign({},u,{ref:r,style:Object.assign(Object.assign({},o),null==f?void 0:f.style),className:b,onClick:e=>{null==i||i(!l),null==s||s(e)}})))}));var k=C,j=n(98719);var Z=(0,m.bk)(["Tag","preset"],(e=>(e=>(0,j.Z)(e,((r,n)=>{let{textColor:t,lightBorderColor:o,lightColor:a,darkColor:c}=n;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:t,background:a,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(h(e))),b);const $=(e,r,n)=>{const t="string"!=typeof(o=n)?o:o.charAt(0).toUpperCase()+o.slice(1);var o;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${n}`],background:e[`color${t}Bg`],borderColor:e[`color${t}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var w=(0,m.bk)(["Tag","status"],(e=>{const r=h(e);return[$(r,"success","Success"),$(r,"processing","Info"),$(r,"error","Error"),$(r,"warning","Warning")]}),b),S=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(n[t[o]]=e[t[o]])}return n};const P=t.forwardRef(((e,r)=>{const{prefixCls:n,className:o,rootClassName:p,style:f,children:v,icon:g,color:m,onClose:h,bordered:b=!0,visible:y}=e,C=S(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:k,direction:j,tag:$}=t.useContext(d.E_),[P,O]=t.useState(!0),I=(0,c.Z)(C,["closeIcon","closable"]);t.useEffect((()=>{void 0!==y&&O(y)}),[y]);const T=(0,l.o2)(m),E=(0,l.yT)(m),_=T||E,B=Object.assign(Object.assign({backgroundColor:m&&!_?m:void 0},null==$?void 0:$.style),f),z=k("tag",n),[N,A,q]=x(z),F=a()(z,null==$?void 0:$.className,{[`${z}-${m}`]:_,[`${z}-has-color`]:m&&!_,[`${z}-hidden`]:!P,[`${z}-rtl`]:"rtl"===j,[`${z}-borderless`]:!b},o,p,A,q),H=e=>{e.stopPropagation(),null==h||h(e),e.defaultPrevented||O(!1)},[,R]=(0,i.Z)((0,i.w)(e),(0,i.w)($),{closable:!1,closeIconRender:e=>{const r=t.createElement("span",{className:`${z}-close-icon`,onClick:H},e);return(0,s.wm)(e,r,(e=>({onClick:r=>{var n;null===(n=null==e?void 0:e.onClick)||void 0===n||n.call(e,r),H(r)},className:a()(null==e?void 0:e.className,`${z}-close-icon`)})))}}),W="function"==typeof C.onClick||v&&"a"===v.type,L=g||null,M=L?t.createElement(t.Fragment,null,L,v&&t.createElement("span",null,v)):v,X=t.createElement("span",Object.assign({},I,{ref:r,className:F,style:B}),M,R,T&&t.createElement(Z,{key:"preset",prefixCls:z}),E&&t.createElement(w,{key:"status",prefixCls:z}));return N(W?t.createElement(u.Z,{component:"Tag"},X):X)})),O=P;O.CheckableTag=k;var I=O}}]);