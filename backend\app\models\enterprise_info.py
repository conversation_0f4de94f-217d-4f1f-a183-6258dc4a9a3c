from mongoengine import Document, StringField, DateTimeField, IntField, ObjectIdField, ListField, Dict<PERSON><PERSON>
from datetime import datetime
from pydantic import BaseModel
from typing import Optional, List, Dict
from bson import ObjectId

class DueDiligence(Document):
    meta = {
        'collection': 'enterprise_info'
    }
    name = StringField(required=True)                    # 企业名称
    credit_code = StringField(required=True)             # 社会信用代码
    tags = ListField(StringField(), required=True)       # 尽调标签列表
    legal_compliance = StringField()                     # 法律合规性审查
    financial_status = StringField()                     # 财务状况分析
    operation_analysis = StringField()                   # 经营情况分析
    credit_history = StringField()                       # 信用记录分析
    market_position = StringField()                      # 市场地位分析
    risk_assessment = StringField()                      # 风险评估
    management_team = StringField()                      # 管理团队评估
    industry_analysis = StringField()                    # 行业分析
    file_id = ObjectIdField(required=True)               # 关联文件ID
    content = StringField(required=True)                 # 内容
    created_at = DateTimeField(default=datetime.now)    # 创建时间

# 对应的 Pydantic 模型
class DueDiligenceBase(BaseModel):
    name: str
    credit_code: str
    tags: List[str]
    legal_compliance: Optional[str] = None
    financial_status: Optional[str] = None
    operation_analysis: Optional[str] = None
    credit_history: Optional[str] = None
    market_position: Optional[str] = None
    risk_assessment: Optional[str] = None
    management_team: Optional[str] = None
    industry_analysis: Optional[str] = None
    file_id: str
    content: str  
    page_num: int
    created_at: Optional[datetime] = None

class DueDiligenceCreate(DueDiligenceBase):
    pass

class DueDiligenceUpdate(BaseModel):
    name: Optional[str] = None
    credit_code: Optional[str] = None
    tags: Optional[List[str]] = None
    legal_compliance: Optional[str] = None
    financial_status: Optional[str] = None
    operation_analysis: Optional[str] = None
    credit_history: Optional[str] = None
    market_position: Optional[str] = None
    risk_assessment: Optional[str] = None
    management_team: Optional[str] = None
    industry_analysis: Optional[str] = None
    content: Optional[str] = None
    page_num: Optional[int] = None