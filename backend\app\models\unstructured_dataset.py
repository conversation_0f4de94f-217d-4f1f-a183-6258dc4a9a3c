from mongoengine import Document, <PERSON>Field, DateTimeField, IntField, ListField, BooleanField, ObjectIdField
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List
from bson import ObjectId

# MongoEngine 模型
class UnstructuredDataset(Document):
    meta = {
        'collection': 'unstructured_datasets'
    }
    _id = ObjectIdField(primary_key=True, default=ObjectId)
    name = StringField(required=True)
    description = StringField()
    created_at = DateTimeField(default=datetime.now)
    last_updated = DateTimeField(default=datetime.now)
    user_id = IntField(required=True)
    user_name = StringField()
    file_count = IntField(default=0)
    is_active = BooleanField(default=True)
    tags = ListField(StringField(), default=list)  # 新增的标签字段


# Pydantic 模型
class UnstructuredDatasetBase(BaseModel):
    name: str
    description: Optional[str] = None
    user_id: int
    user_name: Optional[str] = None
    file_count: int = 0
    is_active: bool = True
    tags: List[str] = []  # 新增的标签字段


class UnstructuredDatasetCreate(UnstructuredDatasetBase):
    pass

class UnstructuredDatasetUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    file_count: Optional[int] = None
    is_active: Optional[bool] = None
    tags: Optional[List[str]] = None  # 新增的标签字段

class UnstructuredDatasetResponse(UnstructuredDatasetBase):
    id: str
    created_at: datetime
    last_updated: datetime

    class Config:
        from_attributes = True
