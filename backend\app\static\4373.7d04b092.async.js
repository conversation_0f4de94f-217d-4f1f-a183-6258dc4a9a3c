"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4373],{44373:function(t,r,e){e.d(r,{BQ:function(){return h},EN:function(){return O},LO:function(){return v},UU:function(){return k},XB:function(){return i},ZX:function(){return b},do:function(){return G},iK:function(){return l},jF:function(){return T},k_:function(){return g},n1:function(){return y}});var n=e(97857),a=e.n(n),u=e(15009),c=e.n(u),s=e(99289),o=e.n(s),p=e(78158);function i(t){return f.apply(this,arguments)}function f(){return(f=o()(c()().mark((function t(r){return c()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,p.N)("/api/structured-datasets",{method:"GET",params:r});case 3:return t.abrupt("return",t.sent);case 6:throw t.prev=6,t.t0=t.catch(0),console.error("获取结构化数据集列表失败:",t.t0),t.t0;case 10:case"end":return t.stop()}}),t,null,[[0,6]])})))).apply(this,arguments)}function h(t){return d.apply(this,arguments)}function d(){return(d=o()(c()().mark((function t(r){return c()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",(0,p.N)("/api/structured-datasets/".concat(r),{method:"GET"}));case 1:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function l(t){return w.apply(this,arguments)}function w(){return(w=o()(c()().mark((function t(r){return c()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,p.N)("/api/structured-datasets",{method:"POST",data:r});case 3:return t.abrupt("return",t.sent);case 6:throw t.prev=6,t.t0=t.catch(0),console.error("创建结构化数据集失败:",t.t0),t.t0;case 10:case"end":return t.stop()}}),t,null,[[0,6]])})))).apply(this,arguments)}function v(t,r){return m.apply(this,arguments)}function m(){return(m=o()(c()().mark((function t(r,e){return c()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,p.N)("/api/structured-datasets/".concat(r),{method:"PUT",data:e});case 3:return t.abrupt("return",t.sent);case 6:throw t.prev=6,t.t0=t.catch(0),console.error("更新结构化数据集失败:",t.t0),t.t0;case 10:case"end":return t.stop()}}),t,null,[[0,6]])})))).apply(this,arguments)}function y(t){return x.apply(this,arguments)}function x(){return(x=o()(c()().mark((function t(r){return c()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,p.N)("/api/structured-datasets/".concat(r),{method:"DELETE"});case 3:return t.abrupt("return",t.sent);case 6:throw t.prev=6,t.t0=t.catch(0),console.error("删除结构化数据集失败:",t.t0),t.t0;case 10:case"end":return t.stop()}}),t,null,[[0,6]])})))).apply(this,arguments)}function k(t,r){return E.apply(this,arguments)}function E(){return(E=o()(c()().mark((function t(r,e){return c()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,p.N)("/api/structured-data-records",{method:"GET",params:a()({dataset_id:r},e)});case 3:return t.abrupt("return",t.sent);case 6:throw t.prev=6,t.t0=t.catch(0),console.error("获取结构化数据记录列表失败:",t.t0),t.t0;case 10:case"end":return t.stop()}}),t,null,[[0,6]])})))).apply(this,arguments)}function b(t){return N.apply(this,arguments)}function N(){return(N=o()(c()().mark((function t(r){return c()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,p.N)("/api/structured-data-records/".concat(r),{method:"DELETE"});case 3:return t.abrupt("return",t.sent);case 6:throw t.prev=6,t.t0=t.catch(0),console.error("删除结构化数据记录失败:",t.t0),t.t0;case 10:case"end":return t.stop()}}),t,null,[[0,6]])})))).apply(this,arguments)}function T(t,r){return _.apply(this,arguments)}function _(){return(_=o()(c()().mark((function t(r,e){return c()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,p.N)("/api/dataset-files",{method:"GET",params:a()({dataset_id:r},e)});case 3:return t.abrupt("return",t.sent);case 6:throw t.prev=6,t.t0=t.catch(0),console.error("获取数据集文件列表失败:",t.t0),t.t0;case 10:case"end":return t.stop()}}),t,null,[[0,6]])})))).apply(this,arguments)}function g(t,r){return D.apply(this,arguments)}function D(){return(D=o()(c()().mark((function t(r,e){return c()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,p.N)("/api/dataset-files/".concat(r),{method:"PUT",data:e});case 3:return t.abrupt("return",t.sent);case 6:throw t.prev=6,t.t0=t.catch(0),console.error("更新数据集文件失败:",t.t0),t.t0;case 10:case"end":return t.stop()}}),t,null,[[0,6]])})))).apply(this,arguments)}function G(t){return L.apply(this,arguments)}function L(){return(L=o()(c()().mark((function t(r){return c()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,p.N)("/api/dataset-files/".concat(r),{method:"DELETE"});case 3:return t.abrupt("return",t.sent);case 6:throw t.prev=6,t.t0=t.catch(0),console.error("删除数据集文件失败:",t.t0),t.t0;case 10:case"end":return t.stop()}}),t,null,[[0,6]])})))).apply(this,arguments)}function O(t,r,e,n){return P.apply(this,arguments)}function P(){return(P=o()(c()().mark((function t(r,e,n,a){var u,s;return c()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return(u=new FormData).append("dataset_id",r),u.append("dataset_type",e),u.append("tags",JSON.stringify(n)),a.forEach((function(t){u.append("files",t)})),t.prev=5,t.next=8,(0,p.N)("/api/dataset-files/upload",{method:"POST",data:u,requestType:"form"});case 8:return t.abrupt("return",t.sent);case 11:if(t.prev=11,t.t0=t.catch(5),404!==(null===(s=t.t0.response)||void 0===s?void 0:s.status)){t.next=16;break}throw console.error("上传端点不存在 (404):",t.t0),new Error("上传服务不可用，请联系管理员");case 16:throw console.error("文件上传失败:",t.t0),t.t0;case 18:case"end":return t.stop()}}),t,null,[[5,11]])})))).apply(this,arguments)}}}]);