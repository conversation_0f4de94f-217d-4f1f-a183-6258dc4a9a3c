import subprocess
import json
import asyncio
from typing import Optional

class MarkItDownClient:
    def __init__(self, use_sse: bool = False, host: str = "127.0.0.1", port: int = 3001):
        """
        初始化 MarkItDown 客户端
        
        Args:
            use_sse (bool): 是否使用 SSE 模式
            host (str): SSE 服务器主机地址
            port (int): SSE 服务器端口
        """
        self.use_sse = use_sse
        self.host = host
        self.port = port
        self._process = None
        
    async def start_server(self) -> None:
        """启动 markitdown-mcp 服务器"""
        if self.use_sse:
            cmd = ["markitdown-mcp", "--sse", "--host", self.host, "--port", str(self.port)]
        else:
            cmd = ["markitdown-mcp"]
            
        self._process = await asyncio.create_subprocess_exec(
            *cmd,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        # 等待服务器启动
        await asyncio.sleep(2)
        
    async def stop_server(self) -> None:
        """停止 markitdown-mcp 服务器"""
        if self._process:
            self._process.terminate()
            await self._process.wait()
            
    async def convert_to_markdown(self, uri: str) -> str:
        """
        将指定 URI 的内容转换为 Markdown 格式
        
        Args:
            uri (str): 要转换的 URI，可以是 http、https、file 或 data URI
            
        Returns:
            str: 转换后的 Markdown 内容
        """
        try:
            # 准备输入数据
            input_data = {
                "tool": "convert_to_markdown",
                "parameters": {
                    "uri": uri
                }
            }
            
            if self.use_sse:
                # 使用 SSE 模式
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"http://{self.host}:{self.port}/sse",
                        json=input_data
                    ) as response:
                        result = await response.json()
                        return result.get("result", "")
            else:
                # 使用 STDIO 模式
                if not self._process:
                    await self.start_server()
                    
                # 发送请求
                self._process.stdin.write(json.dumps(input_data).encode() + b"\n")
                await self._process.stdin.drain()
                
                # 读取响应
                line = await self._process.stdout.readline()
                response = json.loads(line.decode().strip())
                
                if "error" in response:
                    raise Exception(f"转换错误: {response['error']}")
                    
                return response.get("result", "")
                
        except Exception as e:
            raise Exception(f"执行转换时出错: {str(e)}")

async def main():
    # 示例 1: 使用 STDIO 模式
    print("示例 1: 使用 STDIO 模式")
    client1 = MarkItDownClient(use_sse=False)
    try:
        result = await client1.convert_to_markdown("https://example.com")
        print("转换结果:", result)
    finally:
        await client1.stop_server()
        
    # 示例 2: 使用 SSE 模式
    print("\n示例 2: 使用 SSE 模式")
    client2 = MarkItDownClient(use_sse=True)
    try:
        await client2.start_server()
        result = await client2.convert_to_markdown("file:///path/to/your/file.html")
        print("转换结果:", result)
    finally:
        await client2.stop_server()
        
    # 示例 3: 转换本地文件
    print("\n示例 3: 转换本地文件")
    client3 = MarkItDownClient(use_sse=True)
    try:
        await client3.start_server()
        result = await client3.convert_to_markdown("file:///path/to/local/file.html")
        print("转换结果:", result)
    finally:
        await client3.stop_server()

if __name__ == "__main__":
    asyncio.run(main()) 