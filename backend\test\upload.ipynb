{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import requests\n", "def test_file_upload(url, file_path, token=None):\n", "    \"\"\"测试文件上传接口。\n", "\n", "    Args:\n", "        url (str): 要上传文件到的接口URL。\n", "        file_path (str): 要上传的文件的路径。\n", "        token (str, optional): 如果接口需要认证，提供认证token。\n", "\n", "    Returns:\n", "        response: 请求的响应对象。\n", "    \"\"\"\n", "    # 准备文件和头信息\n", "    files = {'file': open(file_path, 'rb')}\n", "    headers = {}\n", "    if token:\n", "        headers['Authorization'] = f'Bearer {token}'\n", "\n", "    # 发送POST请求上传文件\n", "    response = requests.post(url, files=files, headers=headers)\n", "\n", "    # 关闭文件\n", "    files['file'].close()\n", "\n", "    # 返回响应对象\n", "    return response"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'code': 0, 'msg': 'SUCCESS', 'content': None, 'success': True, 'failure': False}\n"]}], "source": ["url = 'http://*************:8012/fileUpload'\n", "file_path = '/Users/<USER>/Downloads/清华大学研究生登记表填写说明_20240918.pdf'\n", "response = test_file_upload(url, file_path)\n", "print(response.json())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "wisechat", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}