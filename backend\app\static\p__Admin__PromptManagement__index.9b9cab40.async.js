"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6297],{8751:function(e,r,t){t.d(r,{Z:function(){return l}});var n=t(1413),a=t(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"},s=t(91146),c=function(e,r){return a.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:o}))};var l=a.forwardRef(c)},85175:function(e,r,t){var n=t(1413),a=t(67294),o=t(48820),s=t(91146),c=function(e,r){return a.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:o.Z}))},l=a.forwardRef(c);r.Z=l},82061:function(e,r,t){var n=t(1413),a=t(67294),o=t(47046),s=t(91146),c=function(e,r){return a.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:o.Z}))},l=a.forwardRef(c);r.Z=l},47389:function(e,r,t){var n=t(1413),a=t(67294),o=t(27363),s=t(91146),c=function(e,r){return a.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:o.Z}))},l=a.forwardRef(c);r.Z=l},51042:function(e,r,t){var n=t(1413),a=t(67294),o=t(42110),s=t(91146),c=function(e,r){return a.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:o.Z}))},l=a.forwardRef(c);r.Z=l},87784:function(e,r,t){t.d(r,{Z:function(){return l}});var n=t(1413),a=t(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"},s=t(91146),c=function(e,r){return a.createElement(s.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:o}))};var l=a.forwardRef(c)},33367:function(e,r,t){t.r(r);var n=t(19632),a=t.n(n),o=t(13769),s=t.n(o),c=t(9783),l=t.n(c),i=t(97857),u=t.n(i),d=t(15009),p=t.n(d),f=t(99289),h=t.n(f),x=t(5574),m=t.n(x),g=t(67294),y=t(97131),v=t(12453),b=t(71471),k=t(55102),Z=t(17788),j=t(8232),C=t(2453),w=t(42075),S=t(66309),P=t(83622),_=t(67839),T=t(34041),z=t(72269),E=t(96074),$=t(47389),I=t(85175),N=t(87784),O=t(8751),B=t(82061),L=t(51042),F=t(77880),R=t(85893),H=["current","pageSize","is_system"],q=b.Z.Title,M=b.Z.Text,A=b.Z.Paragraph,W=k.Z.TextArea,D=Z.Z.confirm;r.default=function(){var e=(0,g.useRef)(),r=j.Z.useForm(),t=m()(r,1)[0],n=(0,g.useState)(!1),o=m()(n,2),c=o[0],i=o[1],d=(0,g.useState)(null),f=m()(d,2),x=f[0],b=f[1],V=(0,g.useState)(!1),U=m()(V,2),G=U[0],X=U[1],J=(0,g.useState)(null),Y=m()(J,2),Q=Y[0],K=Y[1],ee=(0,g.useState)([]),re=m()(ee,2),te=re[0],ne=re[1],ae=(0,g.useState)(!1),oe=m()(ae,2),se=oe[0],ce=oe[1],le=[{label:"全部",desc:"所有模型",key:"all"},{label:"GPT-4",desc:"LLM",key:"gpt4"},{label:"Claude 2",desc:"LLM",key:"claude2"},{label:"LLaMa 2",desc:"LLM",key:"llama2"},{label:"FinBERT",desc:"金融NLP",key:"finbert"}];(0,g.useEffect)((function(){var e=function(){var e=h()(p()().mark((function e(){var r;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,F.qB)();case 3:(r=e.sent).success&&r.data&&ne(r.data),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取分类失败",e.t0),C.ZP.error("获取分类列表失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var ie=function(e){b(e||null),e?t.setFieldsValue(u()(u()({},e),{},{title:e.title,content:e.content,category:e.category,positions:e.positions,models:e.models,language:e.language||"zh-CN",is_system:e.is_system,is_active:e.is_active})):(t.resetFields(),t.setFieldsValue({models:["gpt4"],category:[],positions:["analyst"],language:"zh-CN",is_system:!1,is_active:!0})),i(!0)},ue=function(){var e=h()(p()().mark((function e(r){var t;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,F.WJ)(r.id);case 3:(t=e.sent).success&&t.data&&(K(t.data),X(!0)),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("获取提示词详情失败",e.t0),C.ZP.error("获取提示词详情失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(r){return e.apply(this,arguments)}}(),de=function(){var r=h()(p()().mark((function r(){var n,a,o,s;return p()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,t.validateFields();case 3:if(n=r.sent,ce(!0),a=u()({},n),!x){r.next=12;break}return r.next=9,(0,F.Fu)(x.id,a);case 9:o=r.sent,r.next=15;break;case 12:return r.next=14,(0,F.Fh)(a);case 14:o=r.sent;case 15:o.success?(C.ZP.success("".concat(x?"更新":"创建","提示词成功")),i(!1),t.resetFields(),null===(s=e.current)||void 0===s||s.reload()):C.ZP.error(o.message||"".concat(x?"更新":"创建","提示词失败")),r.next=22;break;case 18:r.prev=18,r.t0=r.catch(0),console.error("表单验证或提交失败:",r.t0),C.ZP.error("".concat(x?"更新":"创建","提示词失败"));case 22:return r.prev=22,ce(!1),r.finish(22);case 25:case"end":return r.stop()}}),r,null,[[0,18,22,25]])})));return function(){return r.apply(this,arguments)}}(),pe=function(){var r=h()(p()().mark((function r(t){var n,a;return p()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,ce(!0),r.next=4,(0,F.Yb)(t);case 4:(n=r.sent).success?(C.ZP.success("复制提示词成功"),null===(a=e.current)||void 0===a||a.reload()):C.ZP.error(n.message||"复制提示词失败"),r.next=12;break;case 8:r.prev=8,r.t0=r.catch(0),console.error("复制失败:",r.t0),C.ZP.error("复制提示词失败");case 12:return r.prev=12,ce(!1),r.finish(12);case 15:case"end":return r.stop()}}),r,null,[[0,8,12,15]])})));return function(e){return r.apply(this,arguments)}}(),fe=function(){var r=h()(p()().mark((function r(t,n){var a,o;return p()().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,ce(!0),r.next=4,(0,F.Fu)(t,{is_active:!n});case 4:(a=r.sent).success?(C.ZP.success("".concat(n?"禁用":"启用","提示词成功")),null===(o=e.current)||void 0===o||o.reload()):C.ZP.error(a.message||"".concat(n?"禁用":"启用","提示词失败")),r.next=12;break;case 8:r.prev=8,r.t0=r.catch(0),console.error("状态更新失败:",r.t0),C.ZP.error("".concat(n?"禁用":"启用","提示词失败"));case 12:return r.prev=12,ce(!1),r.finish(12);case 15:case"end":return r.stop()}}),r,null,[[0,8,12,15]])})));return function(e,t){return r.apply(this,arguments)}}(),he=[{title:"提示词名称",dataIndex:"title",valueType:"text",render:function(e,r){return(0,R.jsx)("a",{onClick:function(){return ue(r)},children:r.title})}},{title:"分类",dataIndex:"category",valueType:"text",render:function(e,r){return(0,R.jsx)(w.Z,{children:Array.isArray(r.category)?r.category.map((function(e){return(0,R.jsx)(S.Z,{color:"blue",children:e},e)})):r.category&&(0,R.jsx)(S.Z,{color:"blue",children:r.category})})},valueEnum:te.reduce((function(e,r){return u()(u()({},e),{},l()({},r,{text:r}))}),{})},{title:"适用模型",dataIndex:"models",valueType:"text",width:120,render:function(e,r){return(0,R.jsx)(w.Z,{children:r.models&&r.models.map((function(e){var r;return(0,R.jsx)(S.Z,{color:"green",children:(null===(r=le.find((function(r){return r.key===e})))||void 0===r?void 0:r.label)||e},e)}))})},valueEnum:le.reduce((function(e,r){return u()(u()({},e),{},l()({},r.key,{text:r.label}))}),{})},{title:"语言",dataIndex:"language",valueType:"select",width:80,render:function(e,r){return(0,R.jsx)(S.Z,{color:"purple",children:"zh-CN"===r.language?"中文":"en-US"===r.language?"英文":r.language})},valueEnum:{"zh-CN":{text:"中文"},"en-US":{text:"英文"}}},{title:"类型",dataIndex:"is_system",valueType:"select",width:100,render:function(e,r){return(0,R.jsx)(S.Z,{color:r.is_system?"success":"default",children:r.is_system?"系统提示词":"个人提示词"})},valueEnum:{true:{text:"系统提示词",status:"Success"},false:{text:"个人提示词",status:"Default"}}},{title:"创建者",dataIndex:"user",valueType:"text",width:100,fieldProps:{placeholder:"请输入创建者"}},{title:"状态",dataIndex:"is_active",valueType:"select",width:80,render:function(e,r){return(0,R.jsx)(S.Z,{color:r.is_active?"success":"error",children:r.is_active?"已启用":"已禁用"})},valueEnum:{true:{text:"已启用",status:"Success"},false:{text:"已禁用",status:"Error"}}},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",sorter:!0,width:160,hideInSearch:!0},{title:"操作",dataIndex:"option",valueType:"option",width:240,render:function(r,t){return[(0,R.jsx)(P.ZP,{type:"link",icon:(0,R.jsx)($.Z,{}),onClick:function(){return ie(t)},children:"编辑"},"edit"),(0,R.jsx)(P.ZP,{type:"link",icon:(0,R.jsx)(I.Z,{}),onClick:function(){return pe(t.id)},children:"复制"},"copy"),(0,R.jsx)(P.ZP,{type:"link",icon:t.is_active?(0,R.jsx)(N.Z,{}):(0,R.jsx)(O.Z,{}),danger:t.is_active,onClick:function(){return fe(t.id,t.is_active)},children:t.is_active?"禁用":"启用"},"status"),(0,R.jsx)(P.ZP,{type:"link",danger:!0,icon:(0,R.jsx)(B.Z,{}),onClick:function(){return r=t.id,void D({title:"确认删除提示词",content:"您确定要删除这个提示词吗？此操作无法撤销。",okText:"确定删除",cancelText:"取消",okButtonProps:{danger:!0},onOk:(n=h()(p()().mark((function t(){var n,a;return p()().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,ce(!0),t.next=4,(0,F.$j)(r);case 4:(n=t.sent).success?(C.ZP.success("删除提示词成功"),null===(a=e.current)||void 0===a||a.reload()):C.ZP.error(n.message||"删除提示词失败"),t.next=12;break;case 8:t.prev=8,t.t0=t.catch(0),console.error("删除失败:",t.t0),C.ZP.error("删除提示词失败");case 12:return t.prev=12,ce(!1),t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[0,8,12,15]])}))),function(){return n.apply(this,arguments)})});var r,n},children:"删除"},"delete")]}}];return(0,R.jsxs)(y._z,{children:[(0,R.jsx)(v.Z,{headerTitle:"提示词管理",actionRef:e,rowKey:"id",search:{labelWidth:"auto",defaultCollapsed:!1},toolBarRender:function(){return[(0,R.jsx)(P.ZP,{type:"primary",onClick:function(){return ie()},icon:(0,R.jsx)(L.Z,{}),children:"新建提示词"},"create")]},request:h()(p()().mark((function e(){var r,t,n,o,c,l,i,d,f=arguments;return p()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=f.length>0&&void 0!==f[0]?f[0]:{},t=r.current,n=r.pageSize,o=r.is_system,c=s()(r,H),e.prev=2,void 0!==o){e.next=13;break}return e.next=6,(0,F.V7)(u()({current:t,pageSize:n},c));case 6:return i=e.sent,e.next=9,(0,F.He)(u()({current:t,pageSize:n},c));case 9:d=e.sent,l={data:[].concat(a()(i.data||[]),a()(d.data||[])),success:i.success&&d.success,total:(i.total||0)+(d.total||0)},e.next=22;break;case 13:if("true"!==o){e.next=19;break}return e.next=16,(0,F.V7)(u()({current:t,pageSize:n},c));case 16:l=e.sent,e.next=22;break;case 19:return e.next=21,(0,F.He)(u()({current:t,pageSize:n},c));case 21:l=e.sent;case 22:return e.abrupt("return",{data:l.data||[],success:l.success,total:l.total||0});case 25:return e.prev=25,e.t0=e.catch(2),console.error("获取提示词失败:",e.t0),C.ZP.error("获取提示词列表失败"),e.abrupt("return",{data:[],success:!1,total:0});case 30:case"end":return e.stop()}}),e,null,[[2,25]])}))),columns:he,rowSelection:{selections:[_.Z.SELECTION_ALL,_.Z.SELECTION_INVERT]},pagination:{showQuickJumper:!0,showSizeChanger:!0}}),(0,R.jsx)(Z.Z,{title:x?"编辑提示词":"新建提示词",open:c,onCancel:function(){return i(!1)},destroyOnClose:!0,footer:[(0,R.jsx)(P.ZP,{onClick:function(){return i(!1)},children:"取消"},"cancel"),(0,R.jsx)(P.ZP,{type:"primary",onClick:de,loading:se,children:"确定"},"submit")],width:600,children:(0,R.jsxs)(j.Z,{form:t,layout:"vertical",initialValues:{models:["gpt4"],category:[],positions:["analyst"],language:"zh-CN",is_system:!1,is_active:!0},children:[(0,R.jsx)(j.Z.Item,{name:"title",label:"提示词名称",rules:[{required:!0,message:"请输入提示词名称"}],children:(0,R.jsx)(k.Z,{placeholder:"请输入提示词名称"})}),(0,R.jsx)(j.Z.Item,{name:"content",label:"提示词内容",rules:[{required:!0,message:"请输入提示词内容"}],children:(0,R.jsx)(W,{placeholder:"请输入提示词内容",rows:6,style:{resize:"none"}})}),(0,R.jsx)(j.Z.Item,{name:"category",label:"分类",rules:[{required:!0,message:"请选择分类"}],children:(0,R.jsx)(T.default,{mode:"tags",options:te.map((function(e){return{label:e,value:e}})),placeholder:"请选择分类"})}),(0,R.jsx)(j.Z.Item,{name:"positions",label:"适用岗位",rules:[{required:!0,message:"请选择适用岗位"}],children:(0,R.jsx)(T.default,{mode:"multiple",options:[{label:"金融分析师",value:"analyst"},{label:"风险管理师",value:"risk_manager"},{label:"投资经理",value:"investment_manager"}],placeholder:"请选择适用岗位"})}),(0,R.jsx)(j.Z.Item,{name:"models",label:"使用模型",rules:[{required:!0,message:"请选择使用模型"}],children:(0,R.jsx)(T.default,{mode:"multiple",options:le.filter((function(e){return"all"!==e.key})).map((function(e){return{label:e.label,value:e.key}})),placeholder:"请选择使用模型"})}),(0,R.jsx)(j.Z.Item,{name:"language",label:"语言",rules:[{required:!0,message:"请选择语言"}],children:(0,R.jsx)(T.default,{options:[{label:"中文",value:"zh-CN"},{label:"英文",value:"en-US"}],placeholder:"请选择语言"})}),(0,R.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,R.jsx)(j.Z.Item,{name:"is_system",label:"是否为系统提示词",valuePropName:"checked",children:(0,R.jsx)(z.Z,{checkedChildren:"是",unCheckedChildren:"否"})}),(0,R.jsx)(j.Z.Item,{name:"is_active",label:"是否启用",valuePropName:"checked",children:(0,R.jsx)(z.Z,{checkedChildren:"启用",unCheckedChildren:"禁用",defaultChecked:!0})})]})]})}),Q&&(0,R.jsxs)(Z.Z,{title:(0,R.jsx)(q,{level:5,style:{margin:0},children:Q.title}),open:G,onCancel:function(){X(!1),K(null)},width:700,footer:[(0,R.jsx)(P.ZP,{icon:(0,R.jsx)(I.Z,{}),onClick:function(){navigator.clipboard.writeText(Q.content),C.ZP.success("提示词内容已复制到剪贴板")},children:"复制内容"},"copy"),(0,R.jsx)(P.ZP,{type:"primary",onClick:function(){X(!1),K(null)},children:"关闭"},"close")],children:[(0,R.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,R.jsxs)("div",{style:{display:"flex",flexWrap:"wrap",gap:"16px",marginBottom:"16px"},children:[(0,R.jsxs)("div",{children:[(0,R.jsx)(M,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"分类"}),(0,R.jsx)("div",{children:Array.isArray(Q.category)?Q.category.map((function(e){return(0,R.jsx)(S.Z,{color:"blue",children:e},e)})):Q.category&&(0,R.jsx)(S.Z,{color:"blue",children:Q.category})})]}),(0,R.jsxs)("div",{children:[(0,R.jsx)(M,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"适用模型"}),(0,R.jsx)("div",{children:Q.models&&Q.models.map((function(e){var r;return(0,R.jsx)(S.Z,{color:"green",children:(null===(r=le.find((function(r){return r.key===e})))||void 0===r?void 0:r.label)||e},e)}))})]}),(0,R.jsxs)("div",{children:[(0,R.jsx)(M,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"适用岗位"}),(0,R.jsx)("div",{children:Q.positions&&Q.positions.map((function(e){return(0,R.jsx)(S.Z,{color:"gold",children:"analyst"===e?"金融分析师":"risk_manager"===e?"风险管理师":"investment_manager"===e?"投资经理":e},e)}))})]}),(0,R.jsxs)("div",{children:[(0,R.jsx)(M,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"语言"}),(0,R.jsx)("div",{children:(0,R.jsx)(S.Z,{color:"purple",children:"zh-CN"===Q.language?"中文":"en-US"===Q.language?"英文":Q.language})})]})]}),(0,R.jsxs)("div",{style:{display:"flex",gap:"16px"},children:[(0,R.jsxs)("div",{children:[(0,R.jsx)(M,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"类型"}),(0,R.jsx)(S.Z,{color:Q.is_system?"success":"default",children:Q.is_system?"系统提示词":"个人提示词"})]}),(0,R.jsxs)("div",{children:[(0,R.jsx)(M,{type:"secondary",style:{fontSize:"13px",display:"block",marginBottom:"4px"},children:"状态"}),(0,R.jsx)(S.Z,{color:Q.is_active?"success":"error",children:Q.is_active?"已启用":"已禁用"})]})]})]}),(0,R.jsx)(E.Z,{orientation:"left",children:"提示词内容"}),(0,R.jsx)(A,{style:{whiteSpace:"pre-wrap",maxHeight:"40vh",overflowY:"auto",fontSize:"14px",lineHeight:"1.8",padding:"16px",backgroundColor:"#f9f9f9",borderRadius:"8px",border:"1px solid #f0f0f0"},children:Q.content}),(0,R.jsxs)("div",{style:{marginTop:20,paddingTop:16,borderTop:"1px solid #f0f0f0",display:"flex",justifyContent:"space-between"},children:[(0,R.jsxs)("div",{children:[(0,R.jsxs)(M,{type:"secondary",style:{fontSize:"12px"},children:["创建时间: ",new Date(Q.created_at).toLocaleString()]}),Q.updated_at&&Q.updated_at!==Q.created_at&&(0,R.jsxs)(M,{type:"secondary",style:{fontSize:"12px",marginLeft:"16px"},children:["更新时间: ",new Date(Q.updated_at).toLocaleString()]})]}),(0,R.jsxs)(M,{type:"secondary",style:{fontSize:"12px"},children:["创建者: ",Q.user]})]})]})]})}},77880:function(e,r,t){t.d(r,{$j:function(){return y},Fh:function(){return h},Fu:function(){return m},He:function(){return l},V7:function(){return u},WJ:function(){return p},Yb:function(){return b},qB:function(){return Z}});var n=t(15009),a=t.n(n),o=t(99289),s=t.n(o),c=t(78158);function l(e){return i.apply(this,arguments)}function i(){return(i=s()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/user-prompts",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function u(e){return d.apply(this,arguments)}function d(){return(d=s()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/system-prompts",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function p(e){return f.apply(this,arguments)}function f(){return(f=s()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts/".concat(r),{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function h(e){return x.apply(this,arguments)}function x(){return(x=s()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function m(e,r){return g.apply(this,arguments)}function g(){return(g=s()(a()().mark((function e(r,t){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts/".concat(r),{method:"PUT",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function y(e){return v.apply(this,arguments)}function v(){return(v=s()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function b(e){return k.apply(this,arguments)}function k(){return(k=s()(a()().mark((function e(r){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompts/".concat(r,"/copy"),{method:"POST"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Z(){return j.apply(this,arguments)}function j(){return(j=s()(a()().mark((function e(){return a()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,c.N)("/api/prompt_categories",{method:"GET"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},66309:function(e,r,t){t.d(r,{Z:function(){return z}});var n=t(67294),a=t(93967),o=t.n(a),s=t(98423),c=t(98787),l=t(69760),i=t(96159),u=t(45353),d=t(53124),p=t(11568),f=t(15063),h=t(14747),x=t(83262),m=t(83559);const g=e=>{const{lineWidth:r,fontSizeIcon:t,calc:n}=e,a=e.fontSizeSM;return(0,x.IX)(e,{tagFontSize:a,tagLineHeight:(0,p.bf)(n(e.lineHeightSM).mul(a).equal()),tagIconSize:n(t).sub(n(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},y=e=>({defaultBg:new f.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var v=(0,m.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:t,tagPaddingHorizontal:n,componentCls:a,calc:o}=e,s=o(n).sub(t).equal(),c=o(r).sub(t).equal();return{[a]:Object.assign(Object.assign({},(0,h.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:s,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${a}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${a}-close-icon`]:{marginInlineStart:c,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${a}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${a}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:s}}),[`${a}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(g(e))),y),b=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)r.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(t[n[a]]=e[n[a]])}return t};const k=n.forwardRef(((e,r)=>{const{prefixCls:t,style:a,className:s,checked:c,onChange:l,onClick:i}=e,u=b(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:f}=n.useContext(d.E_),h=p("tag",t),[x,m,g]=v(h),y=o()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:c},null==f?void 0:f.className,s,m,g);return x(n.createElement("span",Object.assign({},u,{ref:r,style:Object.assign(Object.assign({},a),null==f?void 0:f.style),className:y,onClick:e=>{null==l||l(!c),null==i||i(e)}})))}));var Z=k,j=t(98719);var C=(0,m.bk)(["Tag","preset"],(e=>(e=>(0,j.Z)(e,((r,t)=>{let{textColor:n,lightBorderColor:a,lightColor:o,darkColor:s}=t;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:n,background:o,borderColor:a,"&-inverse":{color:e.colorTextLightSolid,background:s,borderColor:s},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(g(e))),y);const w=(e,r,t)=>{const n="string"!=typeof(a=t)?a:a.charAt(0).toUpperCase()+a.slice(1);var a;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${t}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var S=(0,m.bk)(["Tag","status"],(e=>{const r=g(e);return[w(r,"success","Success"),w(r,"processing","Info"),w(r,"error","Error"),w(r,"warning","Warning")]}),y),P=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)r.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(t[n[a]]=e[n[a]])}return t};const _=n.forwardRef(((e,r)=>{const{prefixCls:t,className:a,rootClassName:p,style:f,children:h,icon:x,color:m,onClose:g,bordered:y=!0,visible:b}=e,k=P(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:Z,direction:j,tag:w}=n.useContext(d.E_),[_,T]=n.useState(!0),z=(0,s.Z)(k,["closeIcon","closable"]);n.useEffect((()=>{void 0!==b&&T(b)}),[b]);const E=(0,c.o2)(m),$=(0,c.yT)(m),I=E||$,N=Object.assign(Object.assign({backgroundColor:m&&!I?m:void 0},null==w?void 0:w.style),f),O=Z("tag",t),[B,L,F]=v(O),R=o()(O,null==w?void 0:w.className,{[`${O}-${m}`]:I,[`${O}-has-color`]:m&&!I,[`${O}-hidden`]:!_,[`${O}-rtl`]:"rtl"===j,[`${O}-borderless`]:!y},a,p,L,F),H=e=>{e.stopPropagation(),null==g||g(e),e.defaultPrevented||T(!1)},[,q]=(0,l.Z)((0,l.w)(e),(0,l.w)(w),{closable:!1,closeIconRender:e=>{const r=n.createElement("span",{className:`${O}-close-icon`,onClick:H},e);return(0,i.wm)(e,r,(e=>({onClick:r=>{var t;null===(t=null==e?void 0:e.onClick)||void 0===t||t.call(e,r),H(r)},className:o()(null==e?void 0:e.className,`${O}-close-icon`)})))}}),M="function"==typeof k.onClick||h&&"a"===h.type,A=x||null,W=A?n.createElement(n.Fragment,null,A,h&&n.createElement("span",null,h)):h,D=n.createElement("span",Object.assign({},z,{ref:r,className:R,style:N}),W,q,E&&n.createElement(C,{key:"preset",prefixCls:O}),$&&n.createElement(S,{key:"status",prefixCls:O}));return B(M?n.createElement(u.Z,{component:"Tag"},D):D)})),T=_;T.CheckableTag=Z;var z=T}}]);