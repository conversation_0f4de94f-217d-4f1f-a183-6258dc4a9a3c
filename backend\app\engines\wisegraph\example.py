"""
WiseGraph 使用示例
演示知识图谱构建和纯图结构检索
"""
import asyncio
from datetime import datetime
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient
from tqdm.asyncio import tqdm
from typing import Dict, Any
from .graph_builder import GraphBuilder
from .graph_retriever import GraphRetriever
from .config import get_mongo_config
from urllib.parse import quote_plus
from loguru import logger

# 测试用的knowledge_base_id
TEST_KNOWLEDGE_BASE_ID = "68463f1626512dbb437fd482"
# MongoDB配置 - 复用LightRAG demo中的配置
# MongoDB配置
mongo_config = get_mongo_config()
MONGO_URI = mongo_config["uri"]
MONGO_DATABASE = mongo_config["database"]

async def fetch_chunks_from_mongodb(knowledge_base_id: str, only_pending: bool = True) -> dict:
    """
    从MongoDB的chunks集合中获取数据

    Args:
        knowledge_base_id: 知识库ID
        only_pending: 是否只获取未构建图谱的数据

    Returns:
        字典格式的chunks数据，格式为 {chunk_id: answer_content}
    """
    print(f"🔍 正在从MongoDB获取数据，knowledge_base_id: {knowledge_base_id}")

    client = None
    try:
        # 连接MongoDB
        client = AsyncIOMotorClient(MONGO_URI)
        db = client[MONGO_DATABASE]
        chunks_collection = db["chunks"]

        # 查询指定knowledge_base_id的chunks (转换为ObjectId)
        try:
            kb_object_id = ObjectId(knowledge_base_id)
        except Exception as e:
            print(f"❌ 无效的knowledge_base_id格式: {e}")
            return {}

        query = {"knowledge_base_id": kb_object_id}

        if only_pending:
            # 只获取未构建图谱的chunks
            query["graph_build_status"] = {"$ne": "completed"}

        projection = {"_id": 1, "answer": 1, "graph_build_status": 1}  # 获取_id、answer和状态字段

        cursor = chunks_collection.find(query, projection)
        chunks_data = {}

        count = 0
        async for doc in cursor:
            chunk_id = str(doc["_id"])  # 使用_id作为chunk_id
            answer = doc.get("answer", "")  # 获取answer字段

            if answer and answer.strip():  # 确保answer不为空
                chunks_data[chunk_id] = answer.strip()
                count += 1

        print(f"✅ 成功获取 {count} 个有效chunks数据")

        if count == 0:
            print("❌ 未找到任何数据")
            return {}

        # 显示前3个chunks的预览
        print("📄 数据预览:")
        for i, (chunk_id, content) in enumerate(list(chunks_data.items())[:3]):
            preview = content[:100] + "..." if len(content) > 100 else content
            print(f"  {chunk_id}: {preview}")

        return chunks_data

    except Exception as e:
        logger.error(f"MongoDB查询失败: {e}")
        print(f"❌ MongoDB查询失败: {e}")
        return {}

    finally:
        if client:
            client.close()


async def update_chunk_graph_status(chunk_id: str, status: str, error: str = None, silent: bool = True):
    """更新chunk的图构建状态"""
    client = None
    try:
        client = AsyncIOMotorClient(MONGO_URI)
        db = client[MONGO_DATABASE]
        chunks_collection = db["chunks"]

        update_data = {
            "graph_build_status": status,
            "graph_build_time": datetime.now() if status == "completed" else None
        }

        if error:
            update_data["graph_build_error"] = error

        result = await chunks_collection.update_one(
            {"_id": ObjectId(chunk_id)},
            {"$set": update_data}
        )

        # 静默模式下不打印，避免干扰进度条
        if not silent:
            if result.modified_count > 0:
                print(f"✅ 更新chunk {chunk_id} 状态为: {status}")
            else:
                print(f"⚠️ 未找到chunk {chunk_id}")

    except Exception as e:
        if not silent:
            print(f"❌ 更新chunk状态失败: {str(e)}")
        logger.error(f"更新chunk状态失败: {e}")
    finally:
        if client:
            client.close()


async def demo_graph_building(knowledge_base_id: str, incremental: bool = True):
    """演示图构建过程（支持增量构建）"""
    print("🔧 开始演示知识图谱构建...")

    # 从MongoDB获取真实数据（只获取未构建的chunks）
    chunks_data = await fetch_chunks_from_mongodb(knowledge_base_id, only_pending=incremental)

    if not chunks_data:
        print("✅ 该知识库所有chunks已完成图谱构建")
        return True

    print(f"📊 发现 {len(chunks_data)} 个待构建图谱的chunks")

    builder = GraphBuilder()

    try:
        # 初始化
        await builder.initialize()
        print("✅ GraphBuilder初始化成功\n")

        # 创建进度条
        total_chunks = len(chunks_data)
        with tqdm(
            total=total_chunks,
            desc=f"🔨 构建知识库 {knowledge_base_id[:8]}... 图谱",
            unit="chunks",
            ncols=120,
            bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]"
        ) as pbar:

            processed_chunks = 0
            failed_chunks = 0

            for chunk_id, content in chunks_data.items():
                try:
                    # 更新进度条描述
                    pbar.set_postfix({
                        'current': chunk_id[:8],
                        'status': '处理中',
                        'success': processed_chunks,
                        'failed': failed_chunks
                    })

                    # 更新状态为处理中
                    await update_chunk_graph_status(chunk_id, "processing")

                    # 构建单个chunk的图谱（传递知识库ID）
                    await builder.build_graph_from_single_text(content, chunk_id, knowledge_base_id)

                    # 更新状态为完成
                    await update_chunk_graph_status(chunk_id, "completed")
                    processed_chunks += 1

                    # 更新进度条
                    pbar.set_postfix({
                        'current': chunk_id[:8],
                        'status': '✅完成',
                        'success': processed_chunks,
                        'failed': failed_chunks
                    })
                    pbar.update(1)

                except Exception as e:
                    # 处理失败
                    await update_chunk_graph_status(chunk_id, "failed", str(e))
                    failed_chunks += 1

                    # 更新进度条
                    pbar.set_postfix({
                        'current': chunk_id[:8],
                        'status': '❌失败',
                        'success': processed_chunks,
                        'failed': failed_chunks
                    })
                    pbar.update(1)
                    continue

        # 获取最终统计
        final_stats = await builder.neo4j_client.get_database_stats()

        result = {
            "processed_chunks": processed_chunks,
            "failed_chunks": failed_chunks,
            "success": processed_chunks > 0,
            "final_database_stats": final_stats
        }
        
        print(f"\n✅ 增量图构建完成!")
        print(f"   - 成功处理: {result['processed_chunks']}/{total_chunks}")
        print(f"   - 失败数量: {result['failed_chunks']}")
        print(f"   - 最终统计: {result['final_database_stats']}")

        # 获取图谱摘要
        if result['processed_chunks'] > 0:
            print(f"\n📈 图谱摘要:")
            summary = await builder.get_graph_summary()

            print(f"   基本统计: {summary['basic_stats']}")
            print(f"   实体类型分布:")
            for entity_type in summary['entity_types']:
                print(f"     - {entity_type['type']}: {entity_type['count']}")

            print(f"   关系类型分布:")
            for relation_type in summary['relation_types']:
                print(f"     - {relation_type['type']}: {relation_type['count']}")

        return result['success']
        
    except Exception as e:
        logger.error(f"图构建演示失败: {e}")
        return False

async def graph_retrieval_for_wiseagent(retriever: GraphRetriever, query: str, knowledge_base_id: str = None) -> Dict[str, Any]:
    """
    为wiseagent项目提供图检索功能（无需初始化，直接使用已初始化的retriever）

    Args:
        retriever: 已初始化的GraphRetriever实例
        query: 用户查询
        knowledge_base_id: 知识库ID

    Returns:
        Dict包含:
        - success: 是否成功
        - context: 格式化的三元组上下文
        - triplets: 三元组列表
        - entities_found: 找到的实体列表
        - chunk_ids: 相关文档块ID
        - stats: 统计信息
    """
    logger.info(f"[WiseAgent图检索] 开始检索 - query: {query}, kb_id: {knowledge_base_id}")

    try:
        # 调用专门的wiseagent检索方法
        result = await retriever.retrieve_for_wiseagent(query, knowledge_base_id)

        if result["success"]:
            logger.info(f"[WiseAgent图检索] 检索成功 - 三元组: {result['stats']['triplets_count']}, 实体: {result['stats']['entities_count']}")

            # 打印上下文用于调试（可选）
            if result["context"]:
                logger.debug(f"[WiseAgent图检索] 生成的上下文:\n{result['context']}")
            else:
                logger.warning(f"[WiseAgent图检索] 未生成上下文内容")
        else:
            logger.warning(f"[WiseAgent图检索] 检索失败或未找到相关内容")

        return result

    except Exception as e:
        logger.error(f"[WiseAgent图检索] 检索过程异常: {e}")
        return {
            "success": False,
            "context": "",
            "triplets": [],
            "entities_found": [],
            "chunk_ids": [],
            "stats": {
                "triplets_count": 0,
                "entities_count": 0,
                "depth": 0
            }
        }

def graph_retrieval_for_wiseagent_sync(retriever: GraphRetriever,query: str, knowledge_base_id: str = None) -> Dict[str, Any]:
    """
    为wiseagent项目提供图检索功能的同步版本
    直接使用已初始化的全局neo4j连接，无需重新初始化

    Args:
        query: 用户查询
        knowledge_base_id: 知识库ID

    Returns:
        Dict包含:
        - success: 是否成功
        - context: 格式化的三元组上下文
        - triplets: 三元组列表
        - entities_found: 找到的实体列表
        - chunk_ids: 相关文档块ID
        - stats: 统计信息
    """

    logger.info(f"[WiseAgent图检索-同步] 开始检索 - query: {query}, kb_id: {knowledge_base_id}")

    try:
        # 创建GraphRetriever实例，使用已初始化的全局连接
        # retriever = GraphRetriever()
        # 注意：不调用initialize()，因为全局neo4j连接已经在main.py中初始化

        # 在同步函数中运行异步方法
        async def _run_retrieval():
            # 直接调用检索方法，不需要初始化
            return await retriever.retrieve_for_wiseagent(query, knowledge_base_id)

        # 获取当前事件循环并运行
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果在事件循环中，使用线程池
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, _run_retrieval())
                    result = future.result()
            else:
                result = loop.run_until_complete(_run_retrieval())
        except RuntimeError:
            # 没有事件循环，创建新的
            result = asyncio.run(_run_retrieval())

        if result["success"]:
            logger.info(f"[WiseAgent图检索-同步] 检索成功 - 三元组: {result['stats']['triplets_count']}, 实体: {result['stats']['entities_count']}")
        else:
            logger.warning(f"[WiseAgent图检索-同步] 检索失败或未找到相关内容")

        return result

    except Exception as e:
        logger.error(f"[WiseAgent图检索-同步] 检索过程异常: {e}")
        return {
            "success": False,
            "context": "",
            "triplets": [],
            "entities_found": [],
            "chunk_ids": [],
            "stats": {
                "triplets_count": 0,
                "entities_count": 0,
                "depth": 0
            }
        }

async def demo_graph_retrieval(query: str, knowledge_base_id: str):
    """演示图检索过程（保留原有的演示功能）"""
    print("\n🔍 开始演示纯图结构检索...")

    retriever = GraphRetriever()

    try:
        # 初始化
        await retriever.initialize()
        print("✅ GraphRetriever初始化成功")

        # 使用新的wiseagent检索方法
        # result = await graph_retrieval_for_wiseagent(retriever, query, knowledge_base_id)
        result = graph_retrieval_for_wiseagent_sync(retriever,query, knowledge_base_id)

        if result["success"]:
            print(f"✅ 检索成功")
            print(f"找到实体: {result['entities_found']}")
            print(f"三元组数量: {result['stats']['triplets_count']}")
            print(f"相关文档块: {len(result['chunk_ids'])} 个")

            if result["context"]:
                print(f"\n生成的上下文:")
                print(result["context"])
            else:
                print("❌ 未生成上下文")
        else:
            print("❌ 检索失败")

        return result

    except Exception as e:
        logger.error(f"图检索演示失败: {e}")
        return False
    finally:
        await retriever.close()

async def main():
    """主函数"""
    print("🚀 WiseGraph 知识图谱系统演示")
    print("=" * 50)
    
    try:
        # 演示图构建
        knowledge_base_id = "684fc4f9486ca9c9e5381b3c"
        query = "支付机构范围？"
        topk = 5
        # build_success = await demo_graph_building(knowledge_base_id)
        
        # if build_success:
            # 演示图检索
        await demo_graph_retrieval(query,knowledge_base_id)       
    except Exception as e:
        logger.error(f"演示过程中出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
