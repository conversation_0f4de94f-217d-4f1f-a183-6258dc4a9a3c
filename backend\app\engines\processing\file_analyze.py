from ultralytics import YOL<PERSON>
from pdf2image import convert_from_path
import pdf2image
import shutil
import os
from tqdm import tqdm
from glob import glob
import cv2
import numpy as np
import paddle_ocr_local
from paddleocr import PPStructure
from markdownify import markdownify
from img2table.document import Image as table_img
from img2table.ocr import TesseractOCR
import argparse
import subprocess
import re
import markdown_split
import mini_io
from config import *
import upload_json
import layout_sorting
import process_xlsx_files
import traceback
from doclayout_yolo import YOLOv10
import torchvision
import torch

from layout_detection import LayoutDetectionYOLO

# from img2table.ocr import EasyOCR

# !export TESSDATA_PREFIX=/usr/share/tesseract-ocr/4.00/tessdata
# !export YOLO_VERBOSE=False

# Model output index dictionary
report_names = {
    0: 'Text',
    1: 'Title',
    2: 'Header',
    3: 'Footer',
    4: 'Figure',
    5: 'Table',
    6: 'Toc',
    7: 'Figure caption',
    8: 'Table caption'
}
paper_names = {
    0: 'Text', 
    1: 'Title', 
    2: 'Figure', 
    3: 'Figure caption', 
    4: 'Table', 
    5: 'Table caption', 
    6: 'Header', 
    7: 'Footer', 
    8: 'Reference', 
    9: 'Equation'
}

doclayout_names = {
    0: 'title', 
    1: 'plain text', 
    2: 'abandon', 
    3: 'figure', 
    4: 'figure_caption', 
    5: 'table', 
    6: 'table_caption', 
    7: 'table_footnote', 
    8: 'isolate_formula', 
    9: 'formula_caption'
}
##############################################################################################################################

# Check heading, decide heading level
def check_chinese_heading(in_string, thres, level_index):

    # Regular expression for chinese titles
    patterns = [
        r'^第[一二三四五六七八九十壹贰叁肆伍陆柒捌玖拾]+[一二三四五六七八九十零壹贰叁肆伍陆柒捌玖拾佰]*章',
        r'^第[一二三四五六七八九十壹贰叁肆伍陆柒捌玖拾]+[一二三四五六七八九十零壹贰叁肆伍陆柒捌玖拾佰]*条',
        r'^第[一二三四五六七八九十壹贰叁肆伍陆柒捌玖拾]+[一二三四五六七八九十零壹贰叁肆伍陆柒捌玖拾佰]*则',
        r'^第[一二三四五六七八九十壹贰叁肆伍陆柒捌玖拾]+[一二三四五六七八九十零壹贰叁肆伍陆柒捌玖拾佰]*节',
        r'^[一二三四五六七八九十壹贰叁肆伍陆柒捌玖拾]+[一二三四五六七八九十零壹贰叁肆伍陆柒捌玖拾佰]*[\、\.．]',
        r'^[（\(][一二三四五六七八九十壹贰叁肆伍陆柒捌玖拾]+[一二三四五六七八九十零壹贰叁肆伍陆柒捌玖拾佰]*[）\)]',
        r'^第[1-9]+[0-9]*章',
        r'^第[1-9]+[0-9]*条',
        r'^第[1-9]+[0-9]*则',
        r'^第[1-9]+[0-9]*节',
        r'^[1-9]+[0-9]*[\.．][1-9]+[0-9]*[\.．][1-9]+[0-9]*',
        r'^[1-9]+[0-9]*[\.．][1-9]+[0-9]*',
        r'^[1-9]+[0-9]*[\、\.．]',
        r'^[（\(][1-9]+[0-9]*[）\)]',
        r'^[1-9]+[0-9]*[）\)][\、\.．]?'
    ]
    
    # Check if title matches regular expressions
    # If so, find/record its heading level
    for i, pattern in enumerate(patterns):
        if re.match(pattern, in_string):
            # if (len(in_string) > thres):
            #     if i not in level_index:
            #         level_index.append(i)
            #     return -2
            if i in level_index:
                return level_index.index(i)
            else:
                level_index.append(i)
                return len(level_index) - 1
    
    # Prevent mistake recognition from the model: returns -2, indicates definately NOT title
    if (len(in_string) > thres):
        return -2
    if in_string.endswith((",", "，", "。","；",";")):
        return -2
    
    # If not, return -1, indicates MAY be a title
    return -1


# Convert docx and ppt to pdf 老版本
# def convert_to_pdf(input_file, output_dir):
#     file_extension = ((input_file.split('/')[-1]).split('.')[-1]).lower()
#     file_name = (input_file.split('/')[-1]).rsplit('.', 1)[0]
#     to_convert = ['doc', 'ppt', 'docx', 'pptx']

#     if file_extension == 'pdf':
#         shutil.copyfile(input_file, output_dir + file_name + '.pdf')
#     else:
#         for extension_choice in to_convert:
#             if file_extension == extension_choice:
#                 result = subprocess.run(['libreoffice', '--headless', '--convert-to', 'pdf', '--outdir', output_dir, input_file],
#                                         stdout=subprocess.PIPE, stderr=subprocess.PIPE)
#                 if result.returncode != 0:
#                     raise Exception(f"Error converting file: {result.stderr.decode('utf-8')}")
#                 print(f"File converted successfully to {output_dir}")
#                 return
#         raise ValueError("Unsupported file type: {}".format(file_extension))
    

def convert_to_pdf_v2(file_path, output_dir):
    """Convert docx and ppt to pdf"""
    try:
        result = subprocess.run(
            ['libreoffice', '--headless', '--convert-to', 'pdf', '--outdir', output_dir, file_path],
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE
            )
        if result.returncode == 0:
            return True
        else:
            return False
    except Exception as e:
        traceback.print_exc()
        return False



# If save folder does not exist, create
# If save folder exists, delete, then create new
def clean_and_create_directory(path):
    if not os.path.exists(path):
        os.makedirs(path)
    else:
        shutil.rmtree(path)
        os.makedirs(path)


# Clean 360 model's output path
def clean_360_output():
    old_folders = glob(os.path.join('./runs/detect', 'predict*'))
    for old_folder_path in old_folders:
        shutil.rmtree(old_folder_path)


# Output string to text file
def write_str_to_txt(str, path):
    with open(path, "w") as file:
        file.write(str)


# Move model output to designated folder, delete the original folder
def relocate_360_output(index, target_folder_path):
    predict_folder_path = './runs/detect/predict'
    # 如果目录不存在，则创建
    if not os.path.exists(predict_folder_path):
        os.makedirs(predict_folder_path)
        
    for file_name in os.listdir(predict_folder_path):
        if file_name.endswith('.png'):
            source_file_path = os.path.join(predict_folder_path, file_name)
            target_name = file_name
            folder = target_folder_path + '/model_output_images'
            if not os.path.exists(folder):
                os.makedirs(folder)
            target_file_path = os.path.join(folder, target_name)
            shutil.copy(source_file_path, target_file_path)
    shutil.rmtree(predict_folder_path)


# Delete all temp images/pdfs under current directory
def delete_temp_images():
    jpg_files = glob("*.jpg")
    png_files = glob("*.png")
    pdf_files = glob("*.pdf")
    xlsx_files = glob("*.xlsx")
    for file in jpg_files:
        os.remove(file)
        print(f"Deleted: {file}")
    for file in png_files:
        os.remove(file)
        print(f"Deleted: {file}")
    for file in pdf_files:
        os.remove(file)
        print(f"Deleted: {file}")
    for file in xlsx_files:
        os.remove(file)
        print(f"Deleted: {file}")


# Sort model recognition output vertically by their bounding box position
def sort_box_index_vertically(box):
    sorted_idx = []
    positions = box.xyxy
    for i, coords in enumerate(positions):
        top_left_x, top_left_y, bottom_right_x, bottom_right_y = coords
        if len(sorted_idx) == 0:
            sorted_idx.append(i)
        else:
            put = False
            for j, index in enumerate(sorted_idx):
                if positions[index][1] > top_left_y:
                    sorted_idx.insert(j, i)
                    put = True
                    break
            if not put:
                sorted_idx.append(i)
    return sorted_idx


# If there are duplicate blocks inside the file, remove until one remains
def remove_duplicate_lines(file_path):
    content = ''
    with open(file_path, 'r') as file:
        content = file.read()
    if (not content == ''):
        content = content.replace('（', '(')
        content = content.replace('）', ')')
        content = content.replace('．', '.')
        blocks = content.split('\n\n')
        unique_blocks = [blocks[0]]
        for block in blocks[1:]:
            if block != unique_blocks[-1]:
                unique_blocks.append(block)

        with open(file_path, 'w') as file:
            file.write('\n\n'.join(unique_blocks))

# Helper function for combine_tables function
# Find the maximum number of columns among a list of tables
def find_max_columns(tables):
    max_columns = 0
    for table in tables:
        rows = table.strip().split('\n')
        for row in rows:
            columns = row.split('|')
            max_columns = max(max_columns, len(columns))
    return max_columns

# Helper function for combine_tables function
# Pad all rows to fit the maximum columns
def pad_table(table, max_columns):
    rows = table.strip().split('\n')
    padded_rows = []
    for row in rows:
        columns = row.split('|')
        while len(columns) < max_columns:
            columns.insert(-1, ' ')
        padded_rows.append('|'.join(columns))
    return '\n'.join(padded_rows)

# Combine Markdown tables and pad columns to maximum
def combine_tables(file_path):
    content = ''
    with open(file_path, 'r') as file:
        content = file.read()

    if (not content.lstrip() == ''):
        blocks = content.split('\n\n')
        tables_and_content = []
        table_pattern = re.compile(r'(\|(?s).*\|)')
        
        for block in blocks:
            # print(block.strip())
            if table_pattern.fullmatch(block.strip()):
                # print('table===============')
                tables_and_content.append(('table', block.strip()))
            else:
                tables_and_content.append(('content', block.strip()))
            #     print('content=============')
            # print('\n\n\n\n')
        
        combined_content = []
        i = 0
        while i < len(tables_and_content):
            if tables_and_content[i][0] == 'table':
                consecutive_tables = []
                while i < len(tables_and_content) and tables_and_content[i][0] == 'table':
                    consecutive_tables.append(tables_and_content[i][1])
                    i += 1

                max_columns = find_max_columns(consecutive_tables)
                combined_table = ''
                for table in consecutive_tables:
                    padded_table = pad_table(table, max_columns)
                    combined_table += padded_table + '\n'
                combined_content.append(combined_table.strip())
            else:
                combined_content.append(tables_and_content[i][1])
                i += 1
        
        with open(file_path, 'w') as file:
            file.write('\n\n'.join(combined_content))

# If there's a heading with same or lower level right before current heading, change previous heading to text
def remove_unlikely_heading(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()

    if content.lstrip() == '':
        return

    lines = content.split('\n\n')
    modified_lines = []
    prev_heading_level = None

    for line in lines:
        if line.startswith('#'):
            heading_level = len(re.match(r'#+', line).group(0))

            if prev_heading_level is None:
                prev_heading_level = heading_level
                modified_lines.append(line)
            else:
                if heading_level == 1:
                    # Keep the first level 1 heading and change others level 1 to text
                    if prev_heading_level == 1:
                        modified_lines.append(line)
                        modified_lines[-1] = re.sub(r'#+ ', '', modified_lines[-1])
                    else:
                        prev_heading_level = heading_level
                        modified_lines.append(line)
                else:
                    # If the previous heading has the same or lower level, change that one to text
                    if prev_heading_level >= heading_level:
                        modified_lines[-1] = re.sub(r'#+ ', '', modified_lines[-1])
                    prev_heading_level = heading_level
                    modified_lines.append(line)
        else:
            prev_heading_level = None
            modified_lines.append(line)

    if modified_lines[-2].startswith('#'):
        modified_lines[-2] = re.sub(r'#+ ', '', modified_lines[-2])

    # If title too long, then not title
    for i, line in enumerate(modified_lines):
        if line.startswith('#') and len((line.lstrip('#')).lstrip()) > title_length_thres:
            modified_lines[i] = re.sub(r'#+ ', '', line)

    processed_content = '\n\n'.join(modified_lines)
    
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(processed_content)


# upload image to minIO
def upload_img(image_path, file_name, img_name):
    object_name = object_name_prefix + file_name + "/" + img_name + ".jpg"
    return mini_io.upload_image_to_minio(image_path, 
                                         minio_endpoint, 
                                         minio_access_key, 
                                         minio_secret_key, 
                                         image_bucket_name,
                                         object_name)


# Use OpenCV to process recognition output to md file and folders
def opencv_image_process(
    result_folder_path,  
    convert_image_path, 
    result_box, 
    image_idx, 
    md_file, 
    title_index, 
    big_file_name):
    
    # Use cv2 to read the image, create folder for results
    # 使用cv2读取暂时保存的图像
    recognized_img = cv2.imread(convert_image_path)

    # layout_xy排序算法
    sorted_box_index = layout_sorting.layout_sort_xy(result_box.xyxy)


    # Handle boxes in sorted order
    # 处理已排序的识别区块
    for i, j in enumerate(tqdm(sorted_box_index, desc=f"Processing Page {image_idx + 1}: ", leave=False)):
        # Get its type ID and corresponding name
        # 获取该区块识别出的标签id和名字
        type_id = int(result_box.cls[j])
        
        name = doclayout_names[type_id]
        

        # Handle Header / Footer: ignore
        # 若为页眉/页脚，直接忽略
        if name == 'abandon':
            continue

        # Get its bounding box position and round to int
        # Give extra box area so that OCR tool can perform better
        # 获取边界框的位置并且取整
        # 比识别出的框大一点以避免ocr漏/误识别
        top_left_x, top_left_y, bottom_right_x, bottom_right_y = result_box.xyxy[j]
        top_left_x = int(np.floor(top_left_x.cpu()))
        top_left_y = int(np.floor(top_left_y.cpu()))
        bottom_right_x = int(np.ceil(bottom_right_x.cpu()))
        bottom_right_y = int(np.ceil(bottom_right_y.cpu()))
        if (name != 'table' and name != 'figure'):
            top_left_x = max(0, top_left_x - ocr_bounding_box_x_dimension_widen)
            top_left_y = max(0, top_left_y - ocr_bounding_box_y_dimension_widen)
            bottom_right_x = min(recognized_img.shape[1], bottom_right_x + ocr_bounding_box_x_dimension_widen)
            bottom_right_y = min(recognized_img.shape[0], bottom_right_y + ocr_bounding_box_y_dimension_widen)

        # Crop image using adjusted bounding box positions
        # 裁剪调整完毕的边界框
        cropped_image = recognized_img[top_left_y:bottom_right_y, top_left_x:bottom_right_x]

        # Handle Figures: save them directly
        # 若为图像，直接保存至输出文件夹，并将路径写入Markdown文件
        if (name == 'figure'):
            figures_path = result_folder_path + '/figures/page_' + str(image_idx)
            if not os.path.exists(figures_path):
                os.makedirs(figures_path)
            figure_path = figures_path + '/' + name + '_' + str(i+1) + '.jpg'
            cv2.imwrite(figure_path, cropped_image)

            if upload_to_minio:
                image_path = figure_path
                image_name = "page_" + str(image_idx) + '_' + name + '_' + str(i+1)
                # image_path = os.path.abspath(image_path)
                url = upload_img(image_path,big_file_name,image_name)
                md_file.write("!["+ name +"](" + str(url) + ")\n\n")
            else:
                image_path = "./figures/page_" + str(image_idx) + '/' + name + '_' + str(i+1) + ".jpg"
                md_file.write("!["+ name +"](" + image_path + ")\n\n")
        
        # Handle Tables: input to paddleocr and record the output table
        # 若为表格，则先使用tesseractOCR识别，若未识别出表格则再使用paddleOCR识别，然后导入到Markdown文件
        elif (name == 'table'):
            ############################### EasyOCR ###############################
            # output_cropped_path = './temp_ocr.jpg'
            # cv2.imwrite(output_cropped_path, cropped_image)
            # img = table_img(output_cropped_path, detect_rotation=False)
            # ocr = EasyOCR(lang=["ch_sim"])
            # extracted_tables = img.extract_tables(ocr=ocr,
            #                           implicit_rows=False,
            #                           borderless_tables=False,
            #                           min_confidence=50)
            # md_file.write(markdownify((extracted_tables[0]).html))
            # os.remove(output_cropped_path)

            ######################## Tesseract + PaddleOCR ########################
            tables_path = result_folder_path + '/tables/page_' + str(image_idx)
            if not os.path.exists(tables_path):
                os.makedirs(tables_path)
            table_path = tables_path + '/' + name + '_' + str(i+1) + '.jpg' 
            cv2.imwrite(table_path, cropped_image)
            img = table_img(table_path, detect_rotation=False)
            extracted_tables = img.extract_tables(ocr=TesseractOCR(lang="chi_sim"),
                                      implicit_rows=False,
                                      borderless_tables=True,
                                      min_confidence=50)
            if len(extracted_tables) > 0:
                md_file.write(markdownify((extracted_tables[0]).html).lstrip('\n'))
            else:
                table_engine = PPStructure(layout=False, ocr=False, show_log=False)
                table_result = table_engine(cropped_image)
                if len(table_result) > 0:
                    md_file.write(markdownify(table_result[0]['res']['html']).lstrip('\n'))

        # Handle Texts: input to ocr models and record the output text
        # (will be recognizing heading and corresponding heading levels)
        # 若为其他类型，则导入至ocr工具识别并记录输出文本
        else:
            # output_cropped_path = './temp_ocr.jpg'
            # cv2.imwrite(output_cropped_path, cropped_image)

            # 调用服务器ocr工具
            text = paddle_ocr_local.get_ocr(np.ascontiguousarray(cropped_image))
            text = text.replace("\n", "")
            text = text.lstrip()
            if (text == ''):
                # os.remove(output_cropped_path)
                continue
            
            if name == 'title':
                text = "#" + " " + text
                
            md_file.write(text + "\n\n")
            # os.remove(output_cropped_path)

##############################################################################################################################

def pdf_parser(file_path, file_actual_name, result_folder_path):
    markdown_output_path = result_folder_path + "/" +  file_actual_name + ".md"
    # convert pdf to images
    images = convert_from_path(file_path)
    
    # Initiate layout model
    # model_path = './model/doclayout_yolo_ft.pt'
    model_path = '/data/jinxu/file_analyze_v2/model/doclayout_yolo_docstructbench_imgsz1024.pt'
    # model = YOLOv10(model_path)
    layout_detector = LayoutDetectionYOLO(model_path)
    # device = 'cuda' if torch.cuda.is_available() else 'cpu'
    device = 'cpu'
    
    # Initiate title level index
    title_indexs = []

    # Image Processing Phase
    for image_idx, image in enumerate(tqdm(images, desc="Processing file:")):
        # Save image to temporary path
        convert_images_path = result_folder_path + "/convert_images"
        if not os.path.exists(convert_images_path):
            os.makedirs(convert_images_path)
        convert_image_path = convert_images_path + "/" + file_actual_name + "_" + str(image_idx+1) + ".png"
        image.save(convert_image_path, 'PNG')
        
        # layout model predict
        # layout_result = model.predict(convert_image_path, imgsz=1024, conf=0.2, device="cpu")[0]
        layout_result = layout_detector.detect(convert_image_path, imgsz=1024, conf=0.2, device=device)[0]
        # bounding box，置信度，类别序号
        boxes = layout_result.__dict__['boxes'].xyxy
        scores = layout_result.__dict__['boxes'].conf
        classes = layout_result.__dict__['boxes'].cls
        # NMS后处理-去除重叠框
        iou_threshold = 0
        boxes, scores, classes = layout_detector.layout_nms(boxes, scores, classes, iou_threshold)
        # save layout model output
        layout_images_path = result_folder_path + "/layout_images"
        if not os.path.exists(layout_images_path):
            os.makedirs(layout_images_path)
        layout_image_path = layout_images_path + "/" + file_actual_name + "_" + str(image_idx+1) + ".png"
        layout_detector.save_layout_result(convert_image_path, layout_image_path, boxes, scores, classes)
        
        # Use opencv/ocr to cut, process data, and save them to markdown file
        # 用opencv裁剪并根据区块标签进行处理，完毕后保存至Markdown文件中
        with open(markdown_output_path, "a") as file:    
            if len(classes) > 0:
                opencv_image_process(result_folder_path, convert_image_path, layout_result.boxes, image_idx, file, title_indexs, file_actual_name)
    
    # 移除重复识别的区块
    remove_duplicate_lines(markdown_output_path)
    # 合并跨页表格
    # combine_tables(markdown_output_path)
    
    return markdown_output_path  


def main(file_path):
    # 解析文件名
    file_name = file_path.split('/')[-1]  # 文件名，带扩展名
    file_actual_name = file_name.rsplit('.', 1)[0]  # 文件名，不带扩展名
    file_type = (file_name.rsplit('.', 1)[1]).lower()  # 文件扩展名，不带.
    # 定义输出文件夹路径
    result_folder_path = "./results/" + file_actual_name + "_" + file_type
    # 重新创建输出文件夹，每次重新解析同一文件时保证输出结果为最新
    clean_and_create_directory(result_folder_path)
    
    # 开始解析...   txt、markdown、word、pdf、excel、ppt、html
    # 5.解析pdf文件
    if file_type == 'pdf':
        markdown_output_path = pdf_parser(file_path, file_actual_name, result_folder_path)
        
    # 6.解析word、ppt文件，将word、ppt转为pdf处理
    elif file_type in ['doc', 'ppt', 'docx', 'pptx']:
        convert_result = convert_to_pdf_v2(file_path, result_folder_path)
        if convert_result:  
            # 'doc', 'ppt', 'docx', 'pptx'转为'pdf'
            file_path = result_folder_path + "/" + file_actual_name + ".pdf"
            pdf_parser(file_path, file_actual_name, result_folder_path)
        else:
            print('Failed to convert pdf!')
    else:
        print("Unsupported file type: {}".format(file_type))
    
    # 生成并上传文本切块的json文件
    flag, split_json_path = markdown_split.file_chunking(markdown_output_path)
    if flag and upload_to_database:
        upload_json.upload_json_from_path(split_json_path, file_name, file_path)
    
    print("文件处理完成：", file_name)


# Normal use case
if __name__ == "__main__":
    # parser = argparse.ArgumentParser(description='Process some inputs.')
    # parser.add_argument('file_path', type=str, help='Path to the file')
    #
    # args = parser.parse_args()
    #
    # main(args.file_path)

    # directory = "./data/天翼租赁知识库v2/财务类资料"
    # directory = "./data/天翼租赁知识库v2/业务类资料"
    # directory = "./data/天翼租赁知识库v2/制度类资料"
    # directory = "./data/天翼租赁知识库v2/风控类资料"
    # directory = "./data/天翼租赁知识库v2/ppt"
    # directory = "./data/处罚信息抽取"
    # for root, dirs, files in os.walk(directory):
    #     for file in files:
    #         try:
    #             file_path = os.path.join(root, file)
    #             if not file_path.endswith(".DS_Store") and not file_path.endswith(".pptx") and not file_path.endswith(
    #                     ".xlsx"):
    #                 print("当前处理文件：", file_path)
    #                 main(file_path)
    #         except Exception as e:
    #             traceback.print_exc()
    #             continue
    
    
    # file_path = "/data/jinxu/file_analyze_v2/data/2019新湘教版高中地理必修2.pdf"
    # file_path = "/data/jinxu/file_analyze_v2/data/TYZL-PA02002-2022A天翼融资租赁有限公司党支部委员会会议制度.pdf"
    # file_path = "/data/jinxu/file_analyze_v2/data/总公司制度合集/2-工会类-综合部/TYZL-LU02001-2022A天翼融资租赁有限公司工会经费管理办法.pdf"
    # file_path = "/data/jinxu/file_analyze_v2/data/总公司制度合集/1-党务类-综合部/TYZL-PA02001-2024A天翼融资租赁有限公司党支部委员会研究决策重大事项清单（2024年版）.pdf"
    # file_path = "/data/jinxu/file_analyze_v2/data/总公司制度合集/4-业务类-业务部/TYZL-CB02001-2024A天翼融资租赁有限公司租赁业务管理办法（2024修订）.pdf"
    # file_path = "/data/jinxu/file_analyze_v2/data/总公司制度合集/1-党务类-综合部/TYZL-PA02002-2022A天翼融资租赁有限公司党支部委员会会议制度.pdf"
    # main_v2(file_path)
    
    
    # model_path = '/data/jinxu/file_analyze_v2/model/doclayout_yolo_docstructbench_imgsz1024.pt'
    # layout_detector = LayoutDetectionYOLO(model_path)
    # convert_image_path = "/data/jinxu/file_analyze_v2/data/images/Snipaste_2024-11-16_10-31-51.jpg"
    # layout_result = layout_detector.detect(convert_image_path, imgsz=1024, conf=0.2, device='cpu')[0]
    # print(layout_result)

    
    # directory = "/data/jinxu/file_analyze_v2/data/总公司制度合集/1-党务类-综合部"
    # directory = "/data/jinxu/file_analyze_v2/data/总公司制度合集/2-工会类-综合部"
    # directory = "/data/jinxu/file_analyze_v2/data/总公司制度合集/3-公司治理类-风险部、综合部"
    # directory = "/data/jinxu/file_analyze_v2/data/总公司制度合集/4-业务类-业务部"
    # directory = "/data/jinxu/file_analyze_v2/data/总公司制度合集/5-融资类-融资部"
    # directory = "/data/jinxu/file_analyze_v2/data/总公司制度合集/6-风险类-风险部"
    directory = "/data/jinxu/file_analyze_v2/data/总公司制度合集/7-综合类-综合部"
    print(directory)
    for root, dirs, files in os.walk(directory):
        print(root)
        for file in files:
            try:
                file_path = os.path.join(root, file)
                if not file_path.endswith(".DS_Store") and not file_path.endswith(".pptx") and not file_path.endswith(".xlsx"):
                    print("当前处理文件：", file_path)
                    main(file_path)
            except Exception as e:
                traceback.print_exc()
                continue