"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4766],{51042:function(e,t,n){var r=n(1413),u=n(67294),a=n(42110),i=n(91146),c=function(e,t){return u.createElement(i.Z,(0,r.Z)((0,r.Z)({},e),{},{ref:t,icon:a.Z}))},s=u.forwardRef(c);t.Z=s},57916:function(e,t,n){n.r(t),n.d(t,{default:function(){return L}});n(9783);var r=n(15009),u=n.n(r),a=n(97857),i=n.n(a),c=n(99289),s=n.n(c),o=n(5574),l=n.n(o),p=n(51042),d=n(97131),f=n(12453),h=n(17788),v=n(8232),m=n(2453),x=n(83622),Z=n(55102),w=n(26412),b=n(67294),y=n(78158);function k(e){return j.apply(this,arguments)}function j(){return(j=s()(u()().mark((function e(t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,y.N)("/api/llms",{method:"POST",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function g(e){return P.apply(this,arguments)}function P(){return(P=s()(u()().mark((function e(t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,y.N)("/api/llms/".concat(t.id),{method:"PUT",data:t}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function C(e){return I.apply(this,arguments)}function I(){return(I=s()(u()().mark((function e(t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,y.N)("/api/llms/".concat(t),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var T=n(85893),F=h.Z.confirm,_=[{id:"log1",content:"用户登录成功",event:"Login",user:"admin",timestamp:"2024-01-15 10:30:00"},{id:"log2",content:"创建了新的公告: 公告A",event:"Create Announcement",user:"editor1",timestamp:"2024-01-15 11:05:21"},{id:"log3",content:"删除了模型: GPT-3.5",event:"Delete LLM",user:"admin",timestamp:"2024-01-15 14:15:10"},{id:"log4",content:"用户尝试登录失败 (密码错误)",event:"Login Attempt Failed",user:"unknown_user",timestamp:"2024-01-16 09:00:05"}],L=function(){var e=(0,b.useState)(!1),t=l()(e,2),n=t[0],r=t[1],a=(0,b.useState)(!1),c=l()(a,2),o=c[0],y=c[1],j=(0,b.useState)(void 0),P=l()(j,2),I=P[0],L=P[1],R=(0,b.useRef)(),S=v.Z.useForm(),q=l()(S,1)[0],A=(0,b.useState)({}),E=l()(A,2),O=(E[0],E[1],function(){var e=s()(u()().mark((function e(t){var n,a;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=m.ZP.loading("正在添加"),e.prev=1,e.next=4,k(i()({},t));case 4:return n(),m.ZP.success("添加模型成功"),r(!1),null===(a=R.current)||void 0===a||a.reload(),q.resetFields(),e.abrupt("return",!0);case 12:return e.prev=12,e.t0=e.catch(1),n(),m.ZP.error("添加失败，请重试"),e.abrupt("return",!1);case 17:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(t){return e.apply(this,arguments)}}()),D=function(){var e=s()(u()().mark((function e(t){var n,a,c;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=null==I?void 0:I.id){e.next=4;break}return m.ZP.error("更新失败，缺少模型 ID"),e.abrupt("return",!1);case 4:return a=m.ZP.loading("正在更新"),e.prev=5,e.next=8,g(i()(i()({},t),{},{id:n}));case 8:return a(),m.ZP.success("更新成功"),r(!1),L(void 0),null===(c=R.current)||void 0===c||c.reload(),q.resetFields(),e.abrupt("return",!0);case 17:return e.prev=17,e.t0=e.catch(5),a(),m.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 22:case"end":return e.stop()}}),e,null,[[5,17]])})));return function(t){return e.apply(this,arguments)}}(),N=function(){var e=s()(u()().mark((function e(t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!I){e.next=4;break}return e.abrupt("return",D(t));case 4:return e.abrupt("return",O(t));case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),z=function(){var e=s()(u()().mark((function e(t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:F({title:"确认删除",content:"你确定要删除这个日志吗？",onOk:function(){var e=s()(u()().mark((function e(){var n,r;return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=m.ZP.loading("正在删除"),e.prev=1,e.next=4,C(t.id);case 4:return n(),m.ZP.success("删除成功"),null===(r=R.current)||void 0===r||r.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),n(),m.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}(),onCancel:function(){console.log("取消删除")}});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),B=[{title:"日志内容",dataIndex:"content",valueType:"text"},{title:"事件",dataIndex:"event",valueType:"text"},{title:"用户",dataIndex:"user",valueType:"text",search:!1},{title:"操作",dataIndex:"option",valueType:"option",width:60,render:function(e,t){return[(0,T.jsx)(x.ZP,{type:"link",style:{width:"50px"},onClick:function(){L(t),q.setFieldsValue(t),r(!0)},children:"编辑"},"edit-".concat(t.id)),(0,T.jsx)(x.ZP,{type:"link",style:{width:"50px"},onClick:function(){L(t),y(!0)},children:"查看"},"view-".concat(t.id)),(0,T.jsx)(x.ZP,{type:"link",danger:!0,style:{width:"50px"},onClick:function(){return z(t)},children:"删除"},"delete-".concat(t.id))]}}];return(0,T.jsxs)(d._z,{children:[(0,T.jsx)(f.Z,{headerTitle:"日志管理",actionRef:R,rowKey:"id",search:{labelWidth:120,defaultCollapsed:!0},options:!1,toolBarRender:function(){return[(0,T.jsxs)(x.ZP,{type:"primary",onClick:function(){L(void 0),q.resetFields(),r(!0)},children:[(0,T.jsx)(p.Z,{})," 新建"]},"primary")]},request:function(){var e=s()(u()().mark((function e(t){return u()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",{data:_,success:!0,total:_.length});case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),columns:B}),(0,T.jsx)(h.Z,{title:I?"编辑日志":"新建日志",visible:n,onCancel:function(){r(!1),L(void 0),q.resetFields()},onOk:function(){return q.submit()},destroyOnClose:!0,forceRender:!0,children:(0,T.jsxs)(v.Z,{form:q,layout:"horizontal",onFinish:N,labelCol:{span:6},wrapperCol:{span:18},children:[(0,T.jsx)(v.Z.Item,{name:"content",label:"日志内容",rules:[{required:!0,message:"请输入日志内容"}],children:(0,T.jsx)(Z.Z.TextArea,{rows:4,placeholder:"请输入日志内容"})}),(0,T.jsx)(v.Z.Item,{name:"event",label:"事件",rules:[{required:!0,message:"请输入事件"}],children:(0,T.jsx)(Z.Z,{placeholder:"请输入事件名称"})}),(0,T.jsx)(v.Z.Item,{name:"user",label:"用户",rules:[{required:!0,message:"请输入用户名"}],children:(0,T.jsx)(Z.Z,{placeholder:"请输入用户名"})})]})}),I&&(0,T.jsx)(h.Z,{title:"公告详情",visible:o,onCancel:function(){return y(!1)},footer:null,children:(0,T.jsxs)(w.Z,{column:1,children:[(0,T.jsx)(w.Z.Item,{label:"日志内容",children:I.content}),(0,T.jsx)(w.Z.Item,{label:"事件",children:I.event}),(0,T.jsx)(w.Z.Item,{label:"用户",children:I.user})]})})]})}}}]);