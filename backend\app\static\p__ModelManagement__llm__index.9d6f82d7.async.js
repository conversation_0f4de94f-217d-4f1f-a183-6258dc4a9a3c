"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1614],{51042:function(e,r,t){var n=t(1413),a=t(67294),l=t(42110),o=t(91146),s=function(e,r){return a.createElement(o.Z,(0,n.Z)((0,n.Z)({},e),{},{ref:r,icon:l.Z}))},i=a.forwardRef(s);r.Z=i},45216:function(e,r,t){t.r(r),t.d(r,{default:function(){return D}});var n=t(9783),a=t.n(n),l=t(15009),o=t.n(l),s=t(97857),i=t.n(s),c=t(99289),u=t.n(c),d=t(5574),p=t.n(d),m=t(51042),h=t(97131),f=t(12453),x=t(17788),b=t(8232),v=t(2453),g=t(42075),j=t(66309),y=t(83622),Z=t(74330),k=t(55102),C=t(34041),I=t(26412),O=t(67294),P=t(78158);function w(e){return E.apply(this,arguments)}function E(){return(E=u()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,P.N)("/api/llms",{method:"GET",params:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function S(e){return _.apply(this,arguments)}function _(){return(_=u()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,P.N)("/api/llms",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function $(e){return A.apply(this,arguments)}function A(){return(A=u()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,P.N)("/api/llms/test",{method:"POST",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function T(e){return M.apply(this,arguments)}function M(){return(M=u()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,P.N)("/api/llms/".concat(r.id),{method:"PUT",data:r}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function L(e){return N.apply(this,arguments)}function N(){return(N=u()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,P.N)("/api/llms/".concat(r),{method:"DELETE"}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var q=t(77885),B=t(85893),z=x.Z.confirm,D=function(){var e=(0,O.useState)(!1),r=p()(e,2),t=r[0],n=r[1],l=(0,O.useState)(!1),s=p()(l,2),c=s[0],d=s[1],P=(0,O.useState)(!1),E=p()(P,2),_=E[0],A=E[1],M=(0,O.useState)(void 0),N=p()(M,2),D=N[0],F=N[1],R=(0,O.useRef)(),U=b.Z.useForm(),K=p()(U,1)[0],H=(0,O.useState)({}),W=p()(H,2),J=W[0],V=W[1],X=function(){var e=u()(o()().mark((function e(r){var t,a;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=v.ZP.loading("正在添加"),e.prev=1,e.next=4,S(i()({},r));case 4:return t(),v.ZP.success("添加模型成功"),n(!1),null===(a=R.current)||void 0===a||a.reload(),K.resetFields(),e.abrupt("return",!0);case 12:return e.prev=12,e.t0=e.catch(1),t(),v.ZP.error("添加失败，请重试"),e.abrupt("return",!1);case 17:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(r){return e.apply(this,arguments)}}(),G=function(){var e=u()(o()().mark((function e(r){var t,n;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.info(r),r.id){e.next=4;break}return v.ZP.error("更新失败，缺少模型 ID"),e.abrupt("return",!1);case 4:return t=v.ZP.loading("正在更新"),e.prev=5,e.next=8,T(r);case 8:return t(),v.ZP.success("更新成功"),d(!1),F(void 0),null===(n=R.current)||void 0===n||n.reload(),K.resetFields(),e.abrupt("return",!0);case 17:return e.prev=17,e.t0=e.catch(5),t(),v.ZP.error("更新失败，请重试"),e.abrupt("return",!1);case 22:case"end":return e.stop()}}),e,null,[[5,17]])})));return function(r){return e.apply(this,arguments)}}(),Q=function(){var e=u()(o()().mark((function e(r){return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:z({title:"确认删除",content:"你确定要删除这个模型吗？",onOk:function(){var e=u()(o()().mark((function e(){var t,n;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=v.ZP.loading("正在删除"),e.prev=1,e.next=4,L(r.id);case 4:return t(),v.ZP.success("删除成功"),null===(n=R.current)||void 0===n||n.reload(),e.abrupt("return",!0);case 10:return e.prev=10,e.t0=e.catch(1),t(),v.ZP.error("删除失败，请重试"),e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(){return e.apply(this,arguments)}}(),onCancel:function(){console.log("取消删除")}});case 1:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),Y=function(){var e=u()(o()().mark((function e(r){var t,n;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return V((function(e){return i()(i()({},e),{},a()({},r.id,!0))})),t=v.ZP.loading("开始测试"),e.prev=2,e.next=5,$({id:r.id});case 5:"success"===(null==(n=e.sent)?void 0:n.status)?(t(),v.ZP.success("测试成功: ".concat(n.message))):(t(),v.ZP.error("测试失败: ".concat(n.message))),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(2),t(),v.ZP.error("测试失败，请重试");case 13:return e.prev=13,V((function(e){return i()(i()({},e),{},a()({},r.id,!1))})),e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[2,9,13,16]])})));return function(r){return e.apply(this,arguments)}}(),ee=[{title:"ID",dataIndex:"id",valueType:"digit"},{title:"名称",dataIndex:"name",valueType:"text",render:function(e,r){return(0,B.jsxs)(g.Z,{children:[r.name,(0,B.jsx)(j.Z,{color:r.is_active?"success":"default",children:r.is_active?"上线":"离线"}),(0,B.jsx)(j.Z,{color:"blue",children:"local"===r.provider?"本地化":r.provider})]})}},{title:"模型名称",dataIndex:"m_name",valueType:"text"},{title:"最大令牌数",dataIndex:"max_tokens",valueType:"digit",search:!1},{title:"创建时间",dataIndex:"created_at",valueType:"dateTime",search:!1},{title:"操作",dataIndex:"option",valueType:"option",width:60,render:function(e,r){return[(0,B.jsx)(y.ZP,{type:"link",style:{width:"50px"},onClick:function(){F(r),d(!0),K.resetFields(),K.setFieldsValue(r)},children:"编辑"},"edit-".concat(r.id)),(0,B.jsx)(y.ZP,{type:"link",style:{width:"50px"},onClick:function(){var e;z({title:"确认".concat(r.is_active?"下线":"上线"),content:"确定要".concat(r.is_active?"下线":"上线","该模型吗？"),onOk:(e=u()(o()().mark((function e(){var t,n;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=v.ZP.loading("正在更新状态"),e.prev=1,e.next=4,T({id:r.id,is_active:!r.is_active});case 4:t(),v.ZP.success("状态更新成功"),null===(n=R.current)||void 0===n||n.reload(),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),t(),v.ZP.error("状态更新失败，请重试");case 13:case"end":return e.stop()}}),e,null,[[1,9]])}))),function(){return e.apply(this,arguments)})})},children:r.is_active?"下线":"上线"},"toggle-".concat(r.id)),(0,B.jsx)(y.ZP,{type:"link",style:{width:"50px"},onClick:function(){F(r),A(!0)},children:"查看"},"view-".concat(r.id)),(0,B.jsx)(y.ZP,{type:"link",danger:!0,style:{width:"50px"},onClick:function(){return Q(r)},children:"删除"},"delete-".concat(r.id)),(0,B.jsxs)(y.ZP,{type:"link",style:{width:"50px"},onClick:function(){return Y(r)},disabled:J[r.id],children:["测试",J[r.id]&&(0,B.jsx)(Z.Z,{size:"small",style:{marginLeft:8}})]},"test-".concat(r.id))]}}];return(0,B.jsxs)(h._z,{children:[(0,B.jsx)(f.Z,{headerTitle:"大语言模型管理",actionRef:R,rowKey:"id",search:{labelWidth:120,defaultCollapsed:!0},toolBarRender:function(){return[(0,B.jsxs)(y.ZP,{type:"primary",onClick:function(){n(!0)},children:[(0,B.jsx)(m.Z,{})," 新建"]},"primary")]},request:function(){var e=u()(o()().mark((function e(r){var t;return o()().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,w(i()({current:r.current,pageSize:r.pageSize},r));case 2:return t=e.sent,e.abrupt("return",{data:t.data,success:t.success,total:t.total});case 4:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}(),columns:ee}),(0,B.jsx)(x.Z,{visible:t,title:"新建模型",onCancel:function(){return n(!1)},onOk:function(){return K.submit()},children:(0,B.jsxs)(b.Z,{form:K,layout:"horizontal",onFinish:X,initialValues:{temperature:.7,max_tokens:4096,top_p:2,provider:q.M.OPENAI,vector_size:300},labelCol:{span:6},wrapperCol:{span:18},children:[(0,B.jsx)(b.Z.Item,{name:"name",label:"名称",rules:[{required:!0,message:"请输入名称"}],children:(0,B.jsx)(k.Z,{})}),(0,B.jsx)(b.Z.Item,{name:"description",label:"描述",rules:[{required:!0,message:"请输入模型描述"}],children:(0,B.jsx)(k.Z,{})}),(0,B.jsx)(b.Z.Item,{name:"m_name",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:(0,B.jsx)(k.Z,{})}),(0,B.jsx)(b.Z.Item,{name:"provider",label:"接口标准",rules:[{required:!0,message:"请选择接口调用标准"}],children:(0,B.jsxs)(C.default,{onChange:function(e){var r="";switch(e){case q.M.DEEPSEEK:r="https://api.deepseek.com";break;case q.M.DOUBAO:r="https://ark.cn-beijing.volces.com/api/v3";break;case q.M.OPENAI:r="https://api.openai.com";break;case q.M.LOACL:case q.M.OLLAMA:case q.M.JIUTIAN:case q.M.TELEAI:r="http://"}K.setFieldsValue({service_url:r})},children:[(0,B.jsx)(C.default.Option,{value:q.M.LOACL,children:"本地化"}),(0,B.jsx)(C.default.Option,{value:q.M.OLLAMA,children:"Ollama"}),(0,B.jsx)(C.default.Option,{value:q.M.JIUTIAN,children:"移动九天"}),(0,B.jsx)(C.default.Option,{value:q.M.DEEPSEEK,children:"DeepSeek"}),(0,B.jsx)(C.default.Option,{value:q.M.DOUBAO,children:"豆包"}),(0,B.jsx)(C.default.Option,{value:q.M.TELEAI,children:"电信星辰"}),(0,B.jsx)(C.default.Option,{value:q.M.OPENAI,children:"OpenAI"})]})}),(0,B.jsx)(b.Z.Item,{name:"service_url",label:"服务地址",extra:"服务地址必须以 /v1 结尾",rules:[{required:!0,message:"请输入服务地址"},{pattern:/^(http|https):\/\/[^ "]+$/,message:"请输入有效的 URL，必须以 http 或 https 开头"},{pattern:/\/v1$/,message:"服务地址必须以 /v1 结尾"}],children:(0,B.jsx)(k.Z,{})}),(0,B.jsx)(b.Z.Item,{name:"api_key",label:"API Key",rules:[{required:!0,message:"请输入 API Key"}],children:(0,B.jsx)(k.Z,{})}),(0,B.jsx)(b.Z.Item,{name:"temperature",label:"默认温度",rules:[{required:!0,message:"请输入温度"}],children:(0,B.jsx)(k.Z,{type:"number"})}),(0,B.jsx)(b.Z.Item,{name:"max_tokens",label:"最大令牌数",rules:[{required:!0,message:"请输入最大令牌数"}],children:(0,B.jsx)(k.Z,{type:"number"})}),(0,B.jsx)(b.Z.Item,{name:"top_p",label:"Top P",rules:[{required:!0,message:"请输入 Top P"}],children:(0,B.jsx)(k.Z,{type:"number"})})]})}),D&&(0,B.jsx)(x.Z,{visible:c,title:"更新模型",onCancel:function(){d(!1),K.resetFields()},onOk:function(){return K.submit()},destroyOnClose:!0,forceRender:!0,children:(0,B.jsxs)(b.Z,{form:K,layout:"horizontal",initialValues:D,onFinish:G,labelCol:{span:6},wrapperCol:{span:18},children:[(0,B.jsx)(b.Z.Item,{name:"id",hidden:!0,children:(0,B.jsx)(k.Z,{})}),(0,B.jsx)(b.Z.Item,{name:"name",label:"名称",rules:[{required:!0,message:"请输入名称"}],children:(0,B.jsx)(k.Z,{})}),(0,B.jsx)(b.Z.Item,{name:"description",label:"描述",rules:[{required:!1,message:"请输入模型描述"}],children:(0,B.jsx)(k.Z,{})}),(0,B.jsx)(b.Z.Item,{name:"m_name",label:"模型名称",rules:[{required:!0,message:"请输入模型名称"}],children:(0,B.jsx)(k.Z,{})}),(0,B.jsx)(b.Z.Item,{name:"provider",label:"接口标准",rules:[{required:!0,message:"请选择接口调用标准"}],children:(0,B.jsxs)(C.default,{children:[(0,B.jsx)(C.default.Option,{value:q.M.LOACL,children:"本地化"}),(0,B.jsx)(C.default.Option,{value:q.M.OLLAMA,children:"Ollama"}),(0,B.jsx)(C.default.Option,{value:q.M.JIUTIAN,children:"移动九天"}),(0,B.jsx)(C.default.Option,{value:q.M.DEEPSEEK,children:"DeepSeek"}),(0,B.jsx)(C.default.Option,{value:q.M.DOUBAO,children:"豆包"}),(0,B.jsx)(C.default.Option,{value:q.M.OPENAI,children:"OpenAI"}),(0,B.jsx)(C.default.Option,{value:q.M.TELEAI,children:"电信星辰"})]})}),(0,B.jsx)(b.Z.Item,{name:"service_url",label:"服务地址",extra:"服务地址必须以 /v1 结尾",rules:[{required:!0,message:"请输入服务地址"},{pattern:/^(http|https):\/\/[^ "]+$/,message:"请输入有效的 URL，必须以 http 或 https 开头"},{pattern:/\/v1$/,message:"服务地址必须以 /v1 结尾"}],children:(0,B.jsx)(k.Z,{})}),(0,B.jsx)(b.Z.Item,{name:"api_key",label:"API Key",rules:[{required:!0,message:"请输入 API Key"}],children:(0,B.jsx)(k.Z,{})}),(0,B.jsx)(b.Z.Item,{name:"temperature",label:"默认温度",rules:[{required:!0,message:"请输入温度"}],children:(0,B.jsx)(k.Z,{type:"number"})}),(0,B.jsx)(b.Z.Item,{name:"max_tokens",label:"最大令牌数",rules:[{required:!0,message:"请输入最大令牌数"}],children:(0,B.jsx)(k.Z,{type:"number"})}),(0,B.jsx)(b.Z.Item,{name:"top_p",label:"Top P",rules:[{required:!0,message:"请输入 Top P"}],children:(0,B.jsx)(k.Z,{type:"number"})})]},D.id)}),D&&(0,B.jsx)(x.Z,{visible:_,title:"模型信息",width:800,onCancel:function(){return A(!1)},footer:null,children:(0,B.jsxs)(I.Z,{bordered:!0,column:1,children:[(0,B.jsx)(I.Z.Item,{label:"名称",children:D.name}),(0,B.jsx)(I.Z.Item,{label:"模型名称",children:D.m_name}),(0,B.jsx)(I.Z.Item,{label:"描述",children:D.description}),(0,B.jsx)(I.Z.Item,{label:"服务地址",children:D.service_url}),(0,B.jsx)(I.Z.Item,{label:"接口标准",children:D.provider}),(0,B.jsx)(I.Z.Item,{label:"状态",children:D.is_active?"上线":"离线"}),(0,B.jsx)(I.Z.Item,{label:"最大令牌数",children:D.max_tokens}),(0,B.jsx)(I.Z.Item,{label:"创建时间",children:D.created_at}),(0,B.jsx)(I.Z.Item,{label:"API Key",children:D.api_key.slice(0,4)+"****"+D.api_key.slice(-4)}),(0,B.jsx)(I.Z.Item,{label:"默认温度",children:D.temperature}),(0,B.jsx)(I.Z.Item,{label:"Top P",children:D.top_p}),(0,B.jsx)(I.Z.Item,{label:"频率惩罚",children:D.frequency_penalty}),(0,B.jsx)(I.Z.Item,{label:"存在惩罚",children:D.presence_penalty}),(0,B.jsx)(I.Z.Item,{label:"价格",children:D.price})]})})]})}},77885:function(e,r,t){t.d(r,{M:function(){return n}});var n={LOACL:"local",OPENAI:"openai",DEEPSEEK:"deepseek",DOUBAO:"doubao",TELEAI:"teleai",JIUTIAN:"jiutian",OLLAMA:"ollama"}},66309:function(e,r,t){t.d(r,{Z:function(){return S}});var n=t(67294),a=t(93967),l=t.n(a),o=t(98423),s=t(98787),i=t(69760),c=t(96159),u=t(45353),d=t(53124),p=t(11568),m=t(15063),h=t(14747),f=t(83262),x=t(83559);const b=e=>{const{lineWidth:r,fontSizeIcon:t,calc:n}=e,a=e.fontSizeSM;return(0,f.IX)(e,{tagFontSize:a,tagLineHeight:(0,p.bf)(n(e.lineHeightSM).mul(a).equal()),tagIconSize:n(t).sub(n(r).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new m.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var g=(0,x.I$)("Tag",(e=>(e=>{const{paddingXXS:r,lineWidth:t,tagPaddingHorizontal:n,componentCls:a,calc:l}=e,o=l(n).sub(t).equal(),s=l(r).sub(t).equal();return{[a]:Object.assign(Object.assign({},(0,h.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:o,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${a}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${a}-close-icon`]:{marginInlineStart:s,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${a}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${a}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:o}}),[`${a}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(b(e))),v),j=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)r.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(t[n[a]]=e[n[a]])}return t};const y=n.forwardRef(((e,r)=>{const{prefixCls:t,style:a,className:o,checked:s,onChange:i,onClick:c}=e,u=j(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:m}=n.useContext(d.E_),h=p("tag",t),[f,x,b]=g(h),v=l()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:s},null==m?void 0:m.className,o,x,b);return f(n.createElement("span",Object.assign({},u,{ref:r,style:Object.assign(Object.assign({},a),null==m?void 0:m.style),className:v,onClick:e=>{null==i||i(!s),null==c||c(e)}})))}));var Z=y,k=t(98719);var C=(0,x.bk)(["Tag","preset"],(e=>(e=>(0,k.Z)(e,((r,t)=>{let{textColor:n,lightBorderColor:a,lightColor:l,darkColor:o}=t;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:n,background:l,borderColor:a,"&-inverse":{color:e.colorTextLightSolid,background:o,borderColor:o},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}})))(b(e))),v);const I=(e,r,t)=>{const n="string"!=typeof(a=t)?a:a.charAt(0).toUpperCase()+a.slice(1);var a;return{[`${e.componentCls}${e.componentCls}-${r}`]:{color:e[`color${t}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var O=(0,x.bk)(["Tag","status"],(e=>{const r=b(e);return[I(r,"success","Success"),I(r,"processing","Info"),I(r,"error","Error"),I(r,"warning","Warning")]}),v),P=function(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)r.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(t[n[a]]=e[n[a]])}return t};const w=n.forwardRef(((e,r)=>{const{prefixCls:t,className:a,rootClassName:p,style:m,children:h,icon:f,color:x,onClose:b,bordered:v=!0,visible:j}=e,y=P(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:Z,direction:k,tag:I}=n.useContext(d.E_),[w,E]=n.useState(!0),S=(0,o.Z)(y,["closeIcon","closable"]);n.useEffect((()=>{void 0!==j&&E(j)}),[j]);const _=(0,s.o2)(x),$=(0,s.yT)(x),A=_||$,T=Object.assign(Object.assign({backgroundColor:x&&!A?x:void 0},null==I?void 0:I.style),m),M=Z("tag",t),[L,N,q]=g(M),B=l()(M,null==I?void 0:I.className,{[`${M}-${x}`]:A,[`${M}-has-color`]:x&&!A,[`${M}-hidden`]:!w,[`${M}-rtl`]:"rtl"===k,[`${M}-borderless`]:!v},a,p,N,q),z=e=>{e.stopPropagation(),null==b||b(e),e.defaultPrevented||E(!1)},[,D]=(0,i.Z)((0,i.w)(e),(0,i.w)(I),{closable:!1,closeIconRender:e=>{const r=n.createElement("span",{className:`${M}-close-icon`,onClick:z},e);return(0,c.wm)(e,r,(e=>({onClick:r=>{var t;null===(t=null==e?void 0:e.onClick)||void 0===t||t.call(e,r),z(r)},className:l()(null==e?void 0:e.className,`${M}-close-icon`)})))}}),F="function"==typeof y.onClick||h&&"a"===h.type,R=f||null,U=R?n.createElement(n.Fragment,null,R,h&&n.createElement("span",null,h)):h,K=n.createElement("span",Object.assign({},S,{ref:r,className:B,style:T}),U,D,_&&n.createElement(C,{key:"preset",prefixCls:M}),$&&n.createElement(O,{key:"status",prefixCls:M}));return L(F?n.createElement(u.Z,{component:"Tag"},K):K)})),E=w;E.CheckableTag=Z;var S=E}}]);