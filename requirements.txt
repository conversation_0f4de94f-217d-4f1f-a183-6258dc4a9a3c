# Web Framework
fastapi==0.104.1
uvicorn==0.24.0

# Database
motor==3.3.1
mongoengine==0.27.0
pymongo==4.6.0

# Authentication & Security
python-jose==3.3.0
passlib==1.7.4
python-multipart==0.0.6
bcrypt==4.0.1

# Data Processing
pydantic==2.4.2
python-dotenv==1.0.0

# Utilities
aiofiles==23.2.1
requests==2.31.0
aiohttp==3.9.1

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.1

# Optional Dependencies
pandas==2.1.2  # 如果需要数据处理
numpy==1.26.1  # 如果需要数学计算 

# MCP and Markdown
lmnr-flow==0.1.4
werkzeug
sqlalchemy
minio
langgraph
elasticsearch==7.13.1
markitdown-mcp>=0.1.0
modelcontextprotocol>=0.1.0 