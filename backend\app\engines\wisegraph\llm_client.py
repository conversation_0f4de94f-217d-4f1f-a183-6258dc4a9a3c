"""
LLM客户端 - 复用LightRAG demo中的OpenAI实现
"""

import asyncio
import json
from typing import List, Dict, Any, Optional
from openai import AsyncOpenAI
from .settings import wisegraph_settings

from loguru import logger

class LLMClient:
    """LLM客户端，用于实体关系抽取和查询处理"""
    
    def __init__(self):
        self.openai_config = wisegraph_settings.openai_config
        self.client = AsyncOpenAI(
            api_key=self.openai_config["api_key"],
            base_url=self.openai_config["base_url"]
        )
    
    async def extract_entities_and_relations(self, text: str, prompt_template: str) -> Dict[str, Any]:
        """
        从文本中提取实体和关系
        
        Args:
            text: 输入文本
            prompt_template: 提示词模板
            
        Returns:
            包含实体和关系的字典
        """
        try:
            prompt = prompt_template.format(text=text)
            
            response = await self.client.chat.completions.create(
                model=self.openai_config["model"],
                messages=[
                    {"role": "system", "content": "你是一个专业的知识图谱构建专家，擅长从文本中提取实体和关系。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.openai_config["temperature"],
                max_tokens=self.openai_config["max_tokens"]
            )
            
            result_text = response.choices[0].message.content.strip()
            
            # 尝试解析JSON
            try:
                result = json.loads(result_text)
                return result
            except json.JSONDecodeError:
                # 如果JSON解析失败，尝试提取JSON部分
                import re
                json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                    return result
                else:
                    logger.error(f"无法解析LLM返回的JSON: {result_text}")
                    return {"entities": [], "relationships": []}
                    
        except Exception as e:
            logger.error(f"LLM调用失败: {e}")
            return {"entities": [], "relationships": []}
    
    async def extract_query_entities(self, query: str, prompt_template: str) -> Dict[str, Any]:
        """
        从查询中提取实体和关系类型
        
        Args:
            query: 用户查询
            prompt_template: 提示词模板
            
        Returns:
            包含查询实体和关系类型的字典
        """
        try:
            prompt = prompt_template.format(query=query)
            
            response = await self.client.chat.completions.create(
                model=self.openai_config["model"],
                messages=[
                    {"role": "system", "content": "你是一个专业的查询分析专家，擅长从用户问题中提取关键信息用于图数据库查询。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.openai_config["temperature"],
                max_tokens=2000
            )
            
            result_text = response.choices[0].message.content.strip()
            
            # 尝试解析JSON
            try:
                result = json.loads(result_text)
                return result
            except json.JSONDecodeError:
                # 如果JSON解析失败，尝试提取JSON部分
                import re
                json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                    return result
                else:
                    logger.error(f"无法解析查询分析结果: {result_text}")
                    return {"entities": [], "relation_types": [], "keywords": []}
                    
        except Exception as e:
            logger.error(f"查询分析失败: {e}")
            return {"entities": [], "relation_types": [], "keywords": []}
    
    async def batch_extract_entities_and_relations(self, texts: List[str], prompt_template: str) -> List[Dict[str, Any]]:
        """
        批量提取实体和关系
        
        Args:
            texts: 文本列表
            prompt_template: 提示词模板
            
        Returns:
            实体关系提取结果列表
        """
        tasks = []
        for text in texts:
            task = self.extract_entities_and_relations(text, prompt_template)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"批量提取中的错误: {result}")
                processed_results.append({"entities": [], "relationships": []})
            else:
                processed_results.append(result)
        
        return processed_results
