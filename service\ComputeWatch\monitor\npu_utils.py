from typing import List, Dict, Optional
import subprocess
import json

class NPUUtil:
    @staticmethod
    def get_npu_count() -> int:
        """获取NPU设备数量"""
        try:
            output = subprocess.check_output(['npu-smi info'], shell=True).decode()
            # 解析输出获取NPU数量
            return len([line for line in output.split('\n') if 'NPU-' in line])
        except:
            return 0

    @staticmethod
    def get_npu_info() -> List[Dict]:
        """获取NPU基本信息"""
        try:
            output = subprocess.check_output(['npu-smi info -l'], shell=True).decode()
            npus = []
            current_npu = {}
            
            for line in output.split('\n'):
                line = line.strip()
                if line.startswith('NPU-'):
                    if current_npu:
                        npus.append(current_npu)
                    current_npu = {'id': line.split()[0]}
                elif ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip().lower().replace(' ', '_')
                    value = value.strip()
                    current_npu[key] = value
            
            if current_npu:
                npus.append(current_npu)
            
            return npus
        except:
            return []

    @staticmethod
    def get_npu_metrics() -> List[Dict]:
        """获取NPU使用指标"""
        try:
            output = subprocess.check_output(['npu-smi info'], shell=True).decode()
            metrics = []
            
            for line in output.split('\n'):
                if 'NPU-' in line:
                    parts = line.split()
                    metrics.append({
                        'id': parts[0],
                        'power': float(parts[3].replace('W', '')),
                        'temperature': float(parts[4].replace('C', '')),
                        'memory_used': int(parts[5].replace('MB', '')),
                        'memory_total': int(parts[6].replace('MB', '')),
                        'utilization': float(parts[7].replace('%', ''))
                    })
            
            return metrics
        except Exception as e:
            print(f"Error getting NPU metrics: {str(e)}")
            return []

    @staticmethod
    def get_driver_version() -> Optional[str]:
        """获取NPU驱动版本"""
        try:
            output = subprocess.check_output(['npu-smi version'], shell=True).decode()
            for line in output.split('\n'):
                if 'Driver Version' in line:
                    return line.split(':')[1].strip()
            return None
        except:
            return None 