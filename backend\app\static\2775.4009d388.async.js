"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2775],{51042:function(e,t,n){var s=n(1413),r=n(67294),a=n(42110),o=n(91146),i=function(e,t){return r.createElement(o.Z,(0,s.Z)((0,s.Z)({},e),{},{ref:t,icon:a.Z}))},c=r.forwardRef(i);t.Z=c},14079:function(e,t,n){n.d(t,{Z:function(){return c}});var s=n(1413),r=n(67294),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 161H699.2c-49.1 0-97.1 14.1-138.4 40.7L512 233l-48.8-31.3A255.2 255.2 0 00324.8 161H96c-17.7 0-32 14.3-32 32v568c0 17.7 14.3 32 32 32h228.8c49.1 0 97.1 14.1 138.4 40.7l44.4 28.6c1.3.8 2.8 1.3 4.3 1.3s3-.4 4.3-1.3l44.4-28.6C602 807.1 650.1 793 699.2 793H928c17.7 0 32-14.3 32-32V193c0-17.7-14.3-32-32-32zM324.8 721H136V233h188.8c35.4 0 69.8 10.1 99.5 29.2l48.8 31.3 6.9 4.5v462c-47.6-25.6-100.8-39-155.2-39zm563.2 0H699.2c-54.4 0-107.6 13.4-155.2 39V298l6.9-4.5 48.8-31.3c29.7-19.1 64.1-29.2 99.5-29.2H888v488zM396.9 361H211.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c.1-4.1-3.1-7.5-7-7.5zm223.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c0-4.1-3.2-7.5-7.1-7.5H627.1c-3.9 0-7.1 3.4-7.1 7.5zM396.9 501H211.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c.1-4.1-3.1-7.5-7-7.5zm416 0H627.1c-3.9 0-7.1 3.4-7.1 7.5v45c0 4.1 3.2 7.5 7.1 7.5h185.7c3.9 0 7.1-3.4 7.1-7.5v-45c.1-4.1-3.1-7.5-7-7.5z"}}]},name:"read",theme:"outlined"},o=n(91146),i=function(e,t){return r.createElement(o.Z,(0,s.Z)((0,s.Z)({},e),{},{ref:t,icon:a}))};var c=r.forwardRef(i)},9502:function(e,t,n){n.d(t,{Z:function(){return Z}});var s=n(87462),r=n(93967),a=n.n(r),o=n(67294),i=n(71471);const c=o.createContext(null);var l=({children:e})=>{const{prefixCls:t}=o.useContext(c);return o.createElement("div",{className:a()(`${t}-group-title`)},e&&o.createElement(i.Z.Text,null,e))},u=n(29245),d=n(51398),m=function(e,t){return o.createElement(d.Z,(0,s.Z)({},e,{ref:t,icon:u.Z}))};var f=o.forwardRef(m),p=n(83062),g=n(85418),h=n(64217);const y=e=>{e.stopPropagation()};var v=e=>{const{prefixCls:t,info:n,className:r,direction:c,onClick:l,active:u,menu:d,...m}=e,v=(0,h.Z)(m,{aria:!0,data:!0,attr:!0}),{disabled:b}=n,[w,$]=o.useState(!1),[E,R]=o.useState(!1),S=a()(r,`${t}-item`,{[`${t}-item-active`]:u&&!b},{[`${t}-item-disabled`]:b});return o.createElement(p.Z,{title:n.label,open:w&&E,onOpenChange:R,placement:"rtl"===c?"left":"right"},o.createElement("li",(0,s.Z)({},v,{className:S,onClick:()=>{!b&&l&&l(n)}}),n.icon&&o.createElement("div",{className:`${t}-icon`},n.icon),o.createElement(i.Z.Text,{className:`${t}-label`,ellipsis:{onEllipsis:$}},n.label),d&&!b&&o.createElement(g.Z,{menu:d,placement:"rtl"===c?"bottomLeft":"bottomRight",trigger:["click"],disabled:b,onOpenChange:e=>{e&&R(!e)}},o.createElement(f,{onClick:y,disabled:b,className:`${t}-menu-icon`}))))},b=n(21770),w=n(21450),$=n(36158);const E="__ungrouped";var R=(e,t=[])=>{const[n,s,r]=o.useMemo((()=>{if(!e)return[!1,void 0,void 0];let t={sort:void 0,title:void 0};return"object"==typeof e&&(t={...t,...e}),[!0,t.sort,t.title]}),[e]);return o.useMemo((()=>{if(!n){return[[{name:E,data:t,title:void 0}],n]}const e=t.reduce(((e,t)=>{const n=t.group||E;return e[n]||(e[n]=[]),e[n].push(t),e}),{});return[(s?Object.keys(e).sort(s):Object.keys(e)).map((t=>({name:t===E?void 0:t,title:r,data:e[t]}))),n]}),[t,e])},S=n(11568),x=n(83262),k=n(43495);var H=(0,k.I$)("Conversations",(e=>(e=>{const{componentCls:t}=e;return{[t]:{display:"flex",flexDirection:"column",gap:e.paddingXXS,overflowY:"auto",padding:e.paddingSM,[`&${t}-rtl`]:{direction:"rtl"},[`& ${t}-list`]:{display:"flex",gap:e.paddingXXS,flexDirection:"column",[`& ${t}-item`]:{paddingInlineStart:e.paddingXL},[`& ${t}-label`]:{color:e.colorTextDescription}},[`& ${t}-item`]:{display:"flex",height:e.controlHeightLG,minHeight:e.controlHeightLG,gap:e.paddingXS,padding:`0 ${(0,S.bf)(e.paddingXS)}`,alignItems:"center",borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,"&:hover":{backgroundColor:e.colorBgTextHover},"&-active":{backgroundColor:e.colorBgTextHover,[`& ${t}-label, ${t}-menu-icon`]:{color:e.colorText}},"&-disabled":{cursor:"not-allowed",[`& ${t}-label`]:{color:e.colorTextDisabled}},"&:hover, &-active":{[`& ${t}-menu-icon`]:{opacity:1}}},[`& ${t}-label`]:{flex:1,color:e.colorText},[`& ${t}-menu-icon`]:{opacity:0,fontSize:e.fontSizeXL},[`& ${t}-group-title`]:{display:"flex",alignItems:"center",height:e.controlHeightLG,minHeight:e.controlHeightLG,padding:`0 ${(0,S.bf)(e.paddingXS)}`}}}})((0,x.IX)(e,{}))),(()=>({})));var Z=e=>{const{prefixCls:t,rootClassName:n,items:r,activeKey:i,defaultActiveKey:u,onActiveChange:d,menu:m,styles:f={},classNames:p={},groupable:g,className:y,style:E,...S}=e,x=(0,h.Z)(S,{attr:!0,aria:!0,data:!0}),[k,Z]=(0,b.Z)(u,{value:i}),[C,N]=R(g,r),{getPrefixCls:T,direction:q}=(0,$.Z)(),M=T("conversations",t),L=(0,w.Z)("conversations"),[U,X,z]=H(M),O=a()(M,L.className,y,n,X,z,{[`${M}-rtl`]:"rtl"===q}),A=e=>{Z(e.key),d&&d(e.key)};return U(o.createElement("ul",(0,s.Z)({},x,{style:{...L.style,...E},className:O}),C.map(((e,t)=>{const n=e.data.map(((e,t)=>o.createElement(v,{key:e.key||`key-${t}`,info:e,prefixCls:M,direction:q,className:a()(p.item,L.classNames.item),style:{...L.styles.item,...f.item},menu:"function"==typeof m?m(e):m,active:k===e.key,onClick:A})));return N?o.createElement("li",{key:e.name||`key-${t}`},o.createElement(c.Provider,{value:{prefixCls:M}},e.title?.(e.name,{components:{GroupTitle:l}})||o.createElement(l,{key:e.name},e.name)),o.createElement("ul",{className:`${M}-list`},n)):n}))))}},93461:function(e,t,n){n.d(t,{Z:function(){return d}});var s=n(67294);const r=e=>""!==(e??"").trim();var a=function(e){const{readableStream:t,transformStream:n}=e;if(!(t instanceof ReadableStream))throw new Error("The options.readableStream must be an instance of ReadableStream.");const s=new TextDecoderStream,a=n?t.pipeThrough(s).pipeThrough(n):t.pipeThrough(s).pipeThrough(function(){let e="";return new TransformStream({transform(t,n){e+=t;const s=e.split("\n\n");s.slice(0,-1).forEach((e=>{r(e)&&n.enqueue(e)})),e=s[s.length-1]},flush(t){r(e)&&t.enqueue(e)}})}()).pipeThrough(new TransformStream({transform(e,t){const n=e.split("\n").reduce(((e,t)=>{const n=t.indexOf(":");if(-1===n)throw new Error('The key-value separator ":" is not found in the sse line chunk!');const s=t.slice(0,n);if(!r(s))return e;const a=t.slice(n+1);return{...e,[s]:a}}),{});0!==Object.keys(n).length&&t.enqueue(n)}}));return a[Symbol.asyncIterator]=async function*(){const e=this.getReader();for(;;){const{done:t,value:n}=await e.read();if(t)break;n&&(yield n)}},a};var o=async(e,t={})=>{const{fetch:n=globalThis.fetch,middlewares:s={},...r}=t;if("function"!=typeof n)throw new Error("The options.fetch must be a typeof fetch function!");let a=[e,r];if("function"==typeof s.onRequest){a=await s.onRequest(...a)}let o=await n(...a);if("function"==typeof s.onResponse){const e=await s.onResponse(o);if(!(e instanceof Response))throw new Error("The options.onResponse must return a Response instance!");o=e}if(!o.ok)throw new Error(`Fetch failed with status ${o.status}`);if(!o.body)throw new Error("The response body is empty.");return o};class i{baseURL;model;defaultHeaders;customOptions;static instanceBuffer=new Map;constructor(e){const{baseURL:t,model:n,dangerouslyApiKey:s,...r}=e;this.baseURL=e.baseURL,this.model=e.model,this.defaultHeaders={"Content-Type":"application/json",...e.dangerouslyApiKey&&{Authorization:e.dangerouslyApiKey}},this.customOptions=r}static init(e){if(!e.baseURL||"string"!=typeof e.baseURL)throw new Error("The baseURL is not valid!");const t=e.fetch||e.baseURL;return i.instanceBuffer.has(t)||i.instanceBuffer.set(t,new i(e)),i.instanceBuffer.get(t)}create=async(e,t,n)=>{const s={method:"POST",body:JSON.stringify({model:this.model,...e}),headers:this.defaultHeaders};try{const e=await o(this.baseURL,{fetch:this.customOptions.fetch,...s});if(n)return void await this.customResponseHandler(e,t,n);const r=e.headers.get("content-type")||"";switch(r.split(";")[0].trim()){case"text/event-stream":await this.sseResponseHandler(e,t);break;case"application/json":await this.jsonResponseHandler(e,t);break;default:throw new Error(`The response content-type: ${r} is not support!`)}}catch(e){const n=e instanceof Error?e:new Error("Unknown error!");throw t?.onError?.(n),n}};customResponseHandler=async(e,t,n)=>{const s=[];for await(const r of a({readableStream:e.body,transformStream:n}))s.push(r),t?.onUpdate?.(r);t?.onSuccess?.(s)};sseResponseHandler=async(e,t)=>{const n=[];for await(const s of a({readableStream:e.body}))n.push(s),t?.onUpdate?.(s);t?.onSuccess?.(n)};jsonResponseHandler=async(e,t)=>{const n=await e.json();t?.onUpdate?.(n),t?.onSuccess?.([n])}}var c=i.init;let l=0;class u{config;requestingMap={};constructor(e){this.config=e}finishRequest(e){delete this.requestingMap[e]}request=(e,t)=>{const{request:n}=this.config,{onUpdate:s,onSuccess:r,onError:a}=t,o=l;l+=1,this.requestingMap[o]=!0,n?.(e,{onUpdate:e=>{this.requestingMap[o]&&s(e)},onSuccess:e=>{this.requestingMap[o]&&(r(e),this.finishRequest(o))},onError:e=>{this.requestingMap[o]&&(a(e),this.finishRequest(o))}})};isRequesting(){return Object.keys(this.requestingMap).length>0}}function d(e){const{request:t,...n}=e;return s.useMemo((()=>[new u({request:t||c({baseURL:n.baseURL,model:n.model,dangerouslyApiKey:n.dangerouslyApiKey}).create,...n})]),[])}},34114:function(e,t,n){n.d(t,{Z:function(){return a}});var s=n(56790),r=n(67294);function a(e){const{defaultMessages:t,agent:n,requestFallback:a,requestPlaceholder:o,parser:i}=e,c=r.useRef(0),[l,u,d]=function(e){const[,t]=r.useState(0),n=r.useRef("function"==typeof e?e():e),s=r.useCallback((e=>{n.current="function"==typeof e?e(n.current):e,t((e=>e+1))}),[]),a=r.useCallback((()=>n.current),[]);return[n.current,s,a]}((()=>(t||[]).map(((e,t)=>({id:`default_${t}`,status:"local",...e}))))),m=(e,t)=>{const n={id:`msg_${c.current}`,message:e,status:t};return c.current+=1,n},f=r.useMemo((()=>{const e=[];return l.forEach((t=>{const n=i?i(t.message):t.message,s=(r=n,Array.isArray(r)?r:[r]);var r;s.forEach(((n,r)=>{let a=t.id;s.length>1&&(a=`${a}_${r}`),e.push({id:a,message:n,status:t.status})}))})),e}),[l]),p=e=>e.filter((e=>"loading"!==e.status&&"error"!==e.status)).map((e=>e.message)),g=()=>p(d());return{onRequest:(0,s.zX)((e=>{if(!n)throw new Error("The agent parameter is required when using the onRequest method in an agent generated by useXAgent.");let t=null;u((n=>{let s=[...n,m(e,"local")];if(o){let n;n="function"==typeof o?o(e,{messages:p(s)}):o;const r=m(n,"loading");t=r.id,s=[...s,r]}return s}));let s=null;const r=(e,n)=>{let r=d().find((e=>e.id===s));return r?u((t=>t.map((t=>t.id===s?{...t,message:e,status:n}:t)))):(r=m(e,n),u((e=>[...e.filter((e=>e.id!==t)),r])),s=r.id),r};n.request({message:e,messages:g()},{onUpdate:e=>{r(e,"loading")},onSuccess:e=>{r(e,"success")},onError:async n=>{if(a){let r;r="function"==typeof a?await a(e,{error:n,messages:g()}):a,u((e=>[...e.filter((e=>e.id!==t&&e.id!==s)),m(r,"error")]))}else u((e=>e.filter((e=>e.id!==t&&e.id!==s))))}})})),messages:l,parsedMessages:f,setMessages:u}}},78205:function(e,t,n){n.d(t,{Z:function(){return h}});var s=n(71471),r=n(86250),a=n(93967),o=n.n(a),i=n(67294),c=n(21450),l=n(36158),u=n(83262),d=n(43495);const m=e=>{const{componentCls:t,calc:n}=e,s=n(e.fontSizeHeading3).mul(e.lineHeightHeading3).equal(),r=n(e.fontSize).mul(e.lineHeight).equal();return{[t]:{gap:e.padding,[`${t}-icon`]:{height:n(s).add(r).add(e.paddingXXS).equal(),display:"flex",img:{height:"100%"}},[`${t}-content-wrapper`]:{gap:e.paddingXS,flex:"auto",minWidth:0,[`${t}-title-wrapper`]:{gap:e.paddingXS},[`${t}-title`]:{margin:0},[`${t}-extra`]:{marginInlineStart:"auto"}}}}},f=e=>{const{componentCls:t}=e;return{[t]:{"&-filled":{paddingInline:e.padding,paddingBlock:e.paddingSM,background:e.colorFillContent,borderRadius:e.borderRadiusLG},"&-borderless":{[`${t}-title`]:{fontSize:e.fontSizeHeading3,lineHeight:e.lineHeightHeading3}}}}};var p=(0,d.I$)("Welcome",(e=>{const t=(0,u.IX)(e,{});return[m(t),f(t)]}),(()=>({})));function g(e,t){const{prefixCls:n,rootClassName:a,className:u,style:d,variant:m="filled",classNames:f={},styles:g={},icon:h,title:y,description:v,extra:b}=e,{direction:w,getPrefixCls:$}=(0,l.Z)(),E=$("welcome",n),R=(0,c.Z)("welcome"),[S,x,k]=p(E),H=i.useMemo((()=>{if(!h)return null;let e=h;return"string"==typeof h&&h.startsWith("http")&&(e=i.createElement("img",{src:h,alt:"icon"})),i.createElement("div",{className:o()(`${E}-icon`,R.classNames.icon,f.icon),style:g.icon},e)}),[h]),Z=i.useMemo((()=>y?i.createElement(s.Z.Title,{level:4,className:o()(`${E}-title`,R.classNames.title,f.title),style:g.title},y):null),[y]),C=i.useMemo((()=>b?i.createElement("div",{className:o()(`${E}-extra`,R.classNames.extra,f.extra),style:g.extra},b):null),[b]);return S(i.createElement(r.Z,{ref:t,className:o()(E,R.className,u,a,x,k,`${E}-${m}`,{[`${E}-rtl`]:"rtl"===w}),style:d},H,i.createElement(r.Z,{vertical:!0,className:`${E}-content-wrapper`},b?i.createElement(r.Z,{align:"flex-start",className:`${E}-title-wrapper`},Z,C):Z,v&&i.createElement(s.Z.Text,{className:o()(`${E}-description`,R.classNames.description,f.description),style:g.description},v))))}var h=i.forwardRef(g)}}]);