import sys
import os
# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
import json
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
import asyncio  # 导入 asyncio 模块
from typing import Dict, Any, List
from datetime import datetime
from bson import ObjectId
from app.utils.logging_config import setup_logging, get_logger
from app.models.chat import getContextFromLangflowRequest
from app.db.mongodb import db
from app.utils.auth import verify_token
from app.models.system_app_setting import KnowledgeQAParams
from app.utils.llmClient import stream_model_api
from app.models.message import Message
from app.utils.wiseflow_auth import auth
from app.engines.rag.flow_langgraph import FlowRAGforgeContext
setup_logging()
logger = get_logger(__name__)

router = APIRouter(
    prefix="/api",
    tags=["getContextFromLangflow"]
)


# 获取上下文
@router.post("/app/getContextFromLangflow", response_model=Dict[str, Any])
async def getContextFromLangflow(conversation: getContextFromLangflowRequest):
    
    # 打印conversation
    logger.info(f"conversation==》{conversation}")
    # 验签
    kb_ids = json.dumps(conversation.params)
    # auth = AuthUtil(secret_key="8d9f62e621f0b22d18db0a71b68ea09d21818566d40712758b8c0c4d476b8bdd")
    result = auth.verify_signature(kb_ids,conversation.client_signature)
    
    if not result:
        logger.error(f"验签失败")
        raise HTTPException(status_code=403, detail="验签失败")
    else:
        logger.info(f"验签成功")
    # 构造config
    print("result==》",result)
    config = {
        "knowledge_base_ids": conversation.params,
        "query": conversation.query,
        "rerank_id": 1,
        "config": {
            "top_k": 5,
            "final_top_k": 5
        }
    }
    # 调用langflow
    kg_qa_flow_rag = FlowRAGforgeContext(config=KnowledgeQAParams(**config))
    result = await kg_qa_flow_rag.run(conversation.query)
    return result


    
# if __name__ == "__main__":
    ## 运行测试
    # dict_user = {
    #     "user_id": 10
    # }
    # conversation = KnowledgeBaseRequest(**dict_user)
    # print(asyncio.run(get_knowledge_bases(conversation)))
    #测试 chat2kb
    # kb_id = ["123124","99999"]
    # conversation_dict = {
    #     "conversation_id": "6758f09b8a57feb909f00b81",
    #     "user_id": 1,
    #     "user_name": "超级管理员",
    #     "app_info": "chat2kb",
    #     "messages": [{"id": "6758f09b8a57feb909f00b81", "role": "user", "content": "1"}],
    #     "extra": {"prompt": "请根据以上参考内容，用中文简洁明了地回答：{{question}}"},
    #     "kb_id": kb_id
    # }
    # # 将字典转换为 ChatResponse 对象
    # conversation = ChatResponse(**conversation_dict)
    # print(asyncio.run(chat2kb(conversation=conversation)))

    